<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.5.xsd">

    <bean id="dataSource" class="com.alibaba.druid.pool.DruidDataSource" destroy-method="close">
        <property name="driverClassName" value="com.mysql.jdbc.Driver"/><!--mysql驱动类名 -->
        <property name="initialSize" value="${datasource.initialSize}"/><!-- 连接池启动时的初始值 -->
        <property name="maxActive" value="${datasource.maxActive}"/><!-- 允许最大空闲值 -->
        <property name="minIdle" value="${datasource.minIdle}"/><!-- 允许最小空闲值 -->
        <property name="maxWait" value="${datasource.maxWait}"/>
        <property name="timeBetweenEvictionRunsMillis" value="${datasource.timeBetweenEvictionRunsMillis}"/><!--失效检查线程运行时间间隔，要小于MySQL的'wait_timeout'时间 -->
        <property name="testOnBorrow" value="${datasource.testOnBorrow}"/>
        <property name="testWhileIdle" value="${datasource.testWhileIdle}"/>
        <property name="validationQuery" value="${datasource.validationQuery}"/><!-- 检查连接有效性的SQL语句 -->
        <property name="username" value="${web.datasource.username}"></property>
        <property name="password" value="${web.datasource.password}"></property>
        <property name="url" value="${web.datasource.url}"></property>
    </bean>
    <bean id="jdbcTemplate" class="cn.daxiang.framework.database.BaseJdbcTemplate">
        <property name="dataSource" ref="dataSource"></property>
    </bean>
    <bean id="dbQueue" class="cn.daxiang.framework.database.BaseDBQueueImpl">
    </bean>
    <bean id="jdbc.entity_scan_package" class="java.lang.String">
        <constructor-arg value="cn.daxiang.hbtd.webserver.core.database"/>
    </bean>
</beans>
