package cn.daxiang.hbtd.webserver.module.chargeReturn.dao;

import cn.daxiang.framework.result.TResult;
import cn.daxiang.hbtd.webserver.core.database.table.ChargeReturn;

public interface ChargeReturnDao {

    /**
     * 获取充值返还充值信息
     *
     * @param uid
     * @return
     */
    TResult<ChargeReturn> getChargeReturn(long uid);

    /**
     * 领取充值返还奖励
     *
     * @param uid
     * @param serverId
     * @param actorId
     * @return
     */
    public TResult<ChargeReturn> receive(long uid, int serverId, long actorId);

}
