package cn.daxiang.hbtd.webserver.module.platform.gaoreyh;

import cn.daxiang.framework.netty.SocketConstant;
import cn.daxiang.framework.result.MapResult;
import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.framework.utils.HttpUtils;
import cn.daxiang.framework.utils.PropertiesUtils;
import cn.daxiang.framework.utils.SecurityUtils;
import cn.daxiang.framework.utils.StringUtils;
import cn.daxiang.framework.utils.TimeConstant;
import cn.daxiang.framework.utils.TimeUtils;
import cn.daxiang.hbtd.webserver.core.database.table.BuyOrder;
import cn.daxiang.hbtd.webserver.core.database.table.User;
import cn.daxiang.hbtd.webserver.module.admin.dao.DisableIpDao;
import cn.daxiang.hbtd.webserver.module.platform.AbstractPlatformInvoke;
import cn.daxiang.hbtd.webserver.module.server.Server;
import cn.daxiang.hbtd.webserver.module.server.ServerHelper;
import cn.daxiang.hbtd.webserver.module.user.dao.UserDao;
import cn.daxiang.hbtd.webserver.module.user.model.HistoryServerEntity;
import cn.daxiang.shared.module.user.PlatformId;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.sql.Timestamp;
import java.util.Collection;
import java.util.Map;
import java.util.Properties;
import java.util.TreeMap;

/**
 * 高热-硬核渠道
 */
@Component
public class GaoReYhPlatformImpl extends AbstractPlatformInvoke {

    /**
     * 禁言
     */
    private static final String GAME_DISABLE_CHAT_URL = "/admin/disableChat";
    /**
     * 封号
     */
    private static final String GAME_DISABLE_LOGIN_URL = "/admin/disableLogin";
    /**
     * 查询角色
     */
    private static final String GAME_USER_QUERY_URL = "/query/attributes";
    private static String VERIFY_TOKEN_URL;
    private static String PAY_KEY;
    private static String GAME_SIGN;
    private static String LOGIN_KEY;
    private static String CHAT_G_KEY;
    private static String CHAT_T_KEY;
    private static String CHAT_KEY;

    @Autowired
    private UserDao userDao;
    @Autowired
    private DisableIpDao disableIpDao;
    @Value("${server_type}")
    private int serverType;

    @Override
    protected void register() {
        Properties p = PropertiesUtils.read("platform/gaoreyh.properties");
        VERIFY_TOKEN_URL = PropertiesUtils.getString(p, "VERIFY_TOKEN_URL");

        GAME_SIGN = PropertiesUtils.getString(p, "GAME_SIGN");
        LOGIN_KEY = PropertiesUtils.getString(p, "LOGIN_KEY");
        PAY_KEY = PropertiesUtils.getString(p, "PAY_KEY");

        CHAT_G_KEY = PropertiesUtils.getString(p, "CHAT_G_KEY");
        CHAT_T_KEY = PropertiesUtils.getString(p, "CHAT_T_KEY");
        CHAT_KEY = PropertiesUtils.getString(p, "CHAT_KEY");

        context.register(PlatformId.GAO_RE_YH, this);
    }

    @Override
    public MapResult<String, Object> login(JSONObject params) {
        try {
            String userId = params.getString("userId");
            String token = params.getString("token");
            String gameId = params.getString("gameId");
            if (StringUtils.isBlank(userId) || StringUtils.isBlank(token) || StringUtils.isBlank(gameId)) {
                LOGGER.error("userId || token || gameId is null params:{}", params);
                return MapResult.fail();
            }

            int nowTime = TimeUtils.getNow();
            JSONObject request = new JSONObject();
            request.put("userID", userId);
            request.put("game_sign", GAME_SIGN);
            request.put("game_id", gameId);
            request.put("token", token);
            request.put("time", TimeUtils.getNow());
            String sign = loginSign(userId, gameId, token, nowTime);
            request.put("sign", sign);
            String response = HttpUtils.sendPost(VERIFY_TOKEN_URL, request.getInnerMap());
            JSONObject responseJson = JSONObject.parseObject(response);
            if (responseJson.getIntValue("state") == 1) {
                JSONObject data = responseJson.getJSONObject("data");
                if (data == null) {
                    LOGGER.error("login error:1 params:{},response:{},requestJson:{}", params, response, request.getInnerMap());
                    return MapResult.fail();
                }
                return MapResult.valueOf(TOKEN, data.getString("userID"));
            } else {
                LOGGER.error("login error:2 params:{},response:{},requestJson:{},responseJson:{}", params, response, request.getInnerMap(), responseJson);
            }
        } catch (Exception e) {
            LOGGER.error("{}", e);
        }
        LOGGER.error("login error params:{}", params);
        return MapResult.fail();
    }

    @Override
    public TResult<Object> deliver(JSONObject params) {
        try {
            String originSign = params.getString("sign");
            if (originSign == null) {
                return TResult.fail();
            }
            params.remove("sign");
            // 签名校验
            String signedString = this.sign(params);
            if (!originSign.equalsIgnoreCase(signedString)) {
                return TResult.fail();
            }
            //serverId,actorId,chargeId,cpOrderId
            String ext = params.getString("ext");
            String[] strings = ext.split(",");
            if (strings.length != 4) {
                LOGGER.error("doDeliver ext error, extend_info :{}", ext);
                return TResult.fail();
            }

            int serverId;
            long actorId;
            int chargeId;
            String orderId = params.getString("orderid");
            String uid = params.getString("uid");
            Double amount = params.getDouble("money") * 100;
            BuyOrder buyOrder = null;
            //线下支付：xxzf,serverId,actorId,chargeId
            String label = strings[0];
            if ("xxzf".equals(label)) {
                serverId = Integer.valueOf(strings[1]);
                actorId = Long.valueOf(strings[2]);
                chargeId = Integer.parseInt(strings[3]);
                boolean userExist = userDao.isUserExist(PlatformId.GAO_RE_YH, uid);
                if (!userExist) {
                    LOGGER.error("user not exist platformId:{} token:{}", PlatformId.GAO_RE_YH, uid);
                    return TResult.fail();
                }
                User user = userDao.getUser(PlatformId.GAO_RE_YH, uid);
                buyOrder = buyOrderFacade.getBuyOrder(actorId, "xxzf" + orderId);
                if (buyOrder == null) {
                    buyOrder = buyOrderFacade.createBuyOrder(actorId, serverType, serverId, user.getUid(), PlatformId.GAO_RE_YH, "xxzf", orderId, chargeId, chargeId, 1,
                        amount.longValue());
                }
            } else {
                actorId = Long.valueOf(strings[1]);
                chargeId = Integer.parseInt(strings[2]);
                String cpOrderId = strings[3];
                buyOrder = buyOrderFacade.getBuyOrder(actorId, cpOrderId);
            }

            if (buyOrder == null || !amount.equals(buyOrder.getAmount())) {
                LOGGER.error("orderId:{}, {}!={}", buyOrder.getOrderId(), amount, buyOrder.getAmount());
                return TResult.fail();
            }
            buyOrder.setChargeId(chargeId);
            buyOrder.setThirdOrderId(orderId);
            buyOrder.setSuccessTimestamp(new Timestamp(System.currentTimeMillis()));
            dbQueue.updateQueue(buyOrder);
            Result result = buyOrderFacade.doDeliver(buyOrder);
            if (result.isFail()) {
                return TResult.fail();
            }
            LOGGER.info("orderId:{}, payment:{}", buyOrder.getOrderId(), params.getString("payment"));
            return TResult.sucess(1);
        } catch (Exception e) {
            LOGGER.error("{}", e);
            return TResult.fail();
        }
    }

    public String loginSign(String userId, String gameId, String token, long time) {
        return SecurityUtils.md5(userId + gameId + GAME_SIGN + token + time + LOGIN_KEY);
    }

    public String sign(JSONObject data) throws UnsupportedEncodingException {
        TreeMap<String, Object> treeMap = Maps.newTreeMap();
        treeMap.putAll(data);
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, Object> entry : treeMap.entrySet()) {
            if (entry.getValue() == null) {
                continue;
            }
            sb.append(entry.getKey()).append("=").append(URLDecoder.decode(entry.getValue().toString(), SocketConstant.CHARSET.name())).append("&");
        }
        sb.deleteCharAt(sb.length() - 1);
        sb.append(PAY_KEY);
        return SecurityUtils.md5(sb.toString());
    }

    /**
     * 封（解封）帐号接口
     * 1 － 封号或解封成功
     * -1 －当传递参数type为1时接口返回-1表示该帐号已经被封号，当传递参数type为2时接口返回-1表示该帐号未被封
     * -2 － 其它错误
     *
     * @param params
     * @return
     */
    public int disableLogin(JSONObject params) {
        String sid = params.getString("sid");
        String uid = params.getString("uid");
        String time = params.getString("time");
        String sign = params.getString("sign");
        int type = params.getIntValue("type");
        int banduration = params.getIntValue("banduration");

        String securitySign = SecurityUtils.md5(CHAT_G_KEY + sid + uid + time + CHAT_KEY);
        if (!securitySign.equals(sign)) {
            LOGGER.error("disableLogin sign error {}!={}", securitySign, sign);
            return -2;
        }

        long beginTime = System.currentTimeMillis();
        long endTime = System.currentTimeMillis();
        if (type < 1 || type > 2) {
            LOGGER.error("disableLogin type error {}}", type);
            return -2;
        }
        if (type == 1) {
            if (banduration <= 0) {
                endTime = endTime + TimeConstant.ONE_DAY_MILLISECOND * 3600;
            } else if (banduration > 0) {
                endTime = endTime + TimeConstant.ONE_MINUTE_MILLISECOND * banduration;
            }
        }
        int serverId = Integer.parseInt(sid);
        User user = userDao.getUser(PlatformId.GAO_RE_YH, uid);
        Server server = ServerHelper.get(serverId);
        if (server == null) {
            LOGGER.error("disableLogin server error serverId:{} ,params:{}}", serverId, params);
            return -2;
        }
        JSONObject jsonObject = new JSONObject();
        HistoryServerEntity historyServerEntity = user.getHistoryServers().get(serverId);
        jsonObject.put("actorId", historyServerEntity.getId());
        jsonObject.put("beginTime", beginTime);
        jsonObject.put("endTime", endTime);
        String url = "http://" + server.getServerIp() + ":" + server.getMaintainPort() + GAME_DISABLE_LOGIN_URL;
        String respnse = HttpUtils.sendPost(url, jsonObject);
        JSONObject result = JSONObject.parseObject(respnse);
        if (result.getIntValue("result") == 0) {
            return 1;
        }
        LOGGER.error("disableLogin result error {}}", result);
        return -2;
    }

    /**
     * 封禁IP地址
     *
     * @param params
     * @return
     */
    public int disableIp(JSONObject params) {
        String sid = params.getString("sid");
        String uid = params.getString("uid");
        String time = params.getString("time");
        String sign = params.getString("sign");
        int type = params.getIntValue("type");
        String uname = params.getString("uname");
        String ip = params.getString("ip");

        String securitySign = SecurityUtils.md5(CHAT_G_KEY + sid + uid + time + CHAT_KEY);
        if (!securitySign.equals(sign)) {
            LOGGER.error("disableIp sign error {}!={}", securitySign, sign);
            return -2;
        }

        if (ip.length() > 15) {
            LOGGER.error("disableIp ip error {}}", ip);
            return -2;
        }

        long endTime = 0;
        if (type < 1 || type > 2) {
            LOGGER.error("disableIp type error {}}", type);
            return -2;
        }
        if (type == 1) {
            endTime = System.currentTimeMillis() + TimeConstant.ONE_DAY_MILLISECOND * 100;
        }

        JSONObject desc = new JSONObject();
        desc.put("gaoReUid", uid);
        desc.put("actorName", uname);
        Result result = disableIpDao.updateDisableIp(ip, endTime, desc.toJSONString());
        if (result.isOk()) {
            return 1;
        }
        LOGGER.error("disableIp result error {}}", result);
        return -2;
    }

    /**
     * 禁言
     *
     * @param params
     * @return
     */
    public int disableChat(JSONObject params) {
        String sid = params.getString("sid");
        String uid = params.getString("uid");
        String time = params.getString("time");
        String sign = params.getString("sign");
        long actorId = params.getLongValue("roleid");
        //	type为1时表示禁言，type为2时表示解禁
        int type = params.getIntValue("type");
        int banduration = params.getIntValue("banduration");

        int serverId = Integer.parseInt(sid);

        String securitySign = SecurityUtils.md5(CHAT_G_KEY + sid + uid + time + CHAT_KEY);
        if (!securitySign.equals(sign)) {
            LOGGER.error("disableChat sign error {}!={}", securitySign, sign);
            return -2;
        }

        if (type < 1 || type > 2) {
            LOGGER.error("disableChat type error {}}", type);
            return -2;
        }
        long disableTime = 0;
        if (type == 1) {
            if (banduration <= 0) {
                disableTime += TimeConstant.ONE_DAY_MILLISECOND * 3600;
            } else if (banduration > 0) {
                disableTime += TimeConstant.ONE_MINUTE_MILLISECOND * banduration;
            }
        }

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("actorId", actorId);
        jsonObject.put("disableTime", disableTime);
        Server server = ServerHelper.get(serverId);
        if (server == null) {
            LOGGER.error("disableChat server error serverId:{}}", serverId);
            return -2;
        }
        String url = "http://" + server.getServerIp() + ":" + server.getMaintainPort() + GAME_DISABLE_CHAT_URL;
        String respnse = HttpUtils.sendPost(url, jsonObject);
        JSONObject result = JSONObject.parseObject(respnse);
        if (result.getIntValue("result") == 0) {
            return 1;
        }
        LOGGER.error("disableChat result error {}}", result);
        return -2;
    }

    /**
     * 查询角色
     *
     * @param params
     * @return
     */
    public JSONObject userQuery(JSONObject params) {
        JSONObject result = new JSONObject();
        result.put("code", "-2");
        result.put("msg", "params error");

        String uid = params.getString("uid");
        int serverId = params.getIntValue("serverId");
        String timestamp = params.getString("timestamp");
        String sign = params.getString("sign");

        String securitySign = SecurityUtils.md5(serverId + uid + timestamp + LOGIN_KEY);
        if (!securitySign.equals(sign)) {
            LOGGER.error("userQuery sign error {}!={}", securitySign, sign);
            result.put("msg", "sign error");
            return result;
        }
        Collection<Integer> serverIdList = Lists.newArrayList();

        if (!userDao.isUserExist(PlatformId.GAO_RE_YH, uid)) {
            LOGGER.debug("User not found platformId:{} token:{}", PlatformId.GAO_RE_YH, uid);
            result.put("code", "-1");
            result.put("msg", "user not found");
            return result;
        }

        User user = userDao.getUser(PlatformId.GAO_RE_YH, uid);
        if (serverId == 0) {
            serverIdList.addAll(user.getHistoryServers().keySet());
        } else {
            if (!user.getHistoryServers().keySet().contains(serverId)) {
                LOGGER.error("userQuery server error serverId:{} ,params:{}}", serverId, params);
                result.put("msg", "server not found");
                return result;
            }
            serverIdList.add(serverId);
        }

        if (serverIdList.isEmpty()) {
            result.put("code", "-1");
            result.put("msg", "server not found");
            return result;
        }

        Collection<JSONObject> resultInfoList = Lists.newArrayList();
        for (int id : serverIdList) {
            Server server = ServerHelper.get(id);
            if (server == null) {
                LOGGER.error("userQuery server error serverId:{} ,params:{}}", serverId, params);
                continue;
            }
            JSONObject request = new JSONObject();
            HistoryServerEntity historyServerEntity = user.getHistoryServers().get(id);
            request.put("actorId", historyServerEntity.getId());
            String url = "http://" + server.getServerIp() + ":" + server.getMaintainPort() + GAME_USER_QUERY_URL;
            String response = HttpUtils.sendPost(url, request);
            if ("".equals(response)) {
                LOGGER.error("response is null url:{}, serverId:{}, request:{}, params:{}}", url, serverId, request, params);
                continue;
            }
            JSONObject resultInfo = new JSONObject();
            JSONObject actorInfo = JSONObject.parseObject(response);
            resultInfo.put("roleId", actorInfo.getLongValue("5"));
            resultInfo.put("name", actorInfo.getString("6"));
            resultInfo.put("server", actorInfo.getIntValue("4"));
            resultInfo.put("serverName", actorInfo.getIntValue("4") + "服");
            resultInfo.put("level", actorInfo.getIntValue("8"));
            resultInfo.put("vipLevel", actorInfo.getIntValue("12"));
            resultInfo.put("createTime", actorInfo.getLongValue("26"));
            resultInfo.put("roleLevelUpdateTime", actorInfo.getLongValue("100"));
            resultInfo.put("power", actorInfo.getLongValue("17"));
            resultInfo.put("profession", "无");
            resultInfo.put("zhuansheng_lv", 0);
            resultInfo.put("zhuanshengName", "");
            resultInfo.put("gold", actorInfo.getIntValue("10"));
            resultInfoList.add(resultInfo);
        }
        if (resultInfoList.isEmpty()) {
            LOGGER.error("userQuery server error serverId:{} ,params:{}}", serverId, params);
            result.put("code", "-8");
            result.put("msg", "actor not found");
            return result;
        }
        result.put("code", "0");
        result.put("msg", "成功");
        JSONObject dataObject = new JSONObject();
        dataObject.put("userinfo", resultInfoList);
        result.put("data", dataObject);
        return result;
    }
}
