package cn.daxiang.hbtd.webserver.module.client;

import cn.daxiang.dto.result.JsonResultProtocol;
import cn.daxiang.dto.result.ResultCode;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/client")
public class ClientController {

    private static final Logger REPORT_LOGGER = LoggerFactory.getLogger("client-report");
    private static final Logger STATISTICS_LOGGER = LoggerFactory.getLogger("client-statistics");

    @RequestMapping(value = "/report", method = RequestMethod.POST)
    public @ResponseBody JsonResultProtocol report(@RequestBody JSONObject object) {
        JsonResultProtocol resultProtocol = new JsonResultProtocol();
        if (object.containsKey("message")) {
            REPORT_LOGGER.debug("{}", JSONObject.toJSONString(object.getJSONObject("message"), SerializerFeature.QuoteFieldNames.getMask()));
        }
        resultProtocol.setup(ResultCode.SUCCESS);
        return resultProtocol;
    }

    @RequestMapping(value = "/statistics", method = RequestMethod.POST)
    public @ResponseBody JsonResultProtocol statistics(@RequestBody JSONObject object) {
        JsonResultProtocol resultProtocol = new JsonResultProtocol();
        if (object.containsKey("message")) {
            STATISTICS_LOGGER.debug("{}", JSONObject.toJSONString(object.getJSONObject("message"), SerializerFeature.QuoteFieldNames.getMask()));
        }
        resultProtocol.setup(ResultCode.SUCCESS);
        return resultProtocol;
    }
}
