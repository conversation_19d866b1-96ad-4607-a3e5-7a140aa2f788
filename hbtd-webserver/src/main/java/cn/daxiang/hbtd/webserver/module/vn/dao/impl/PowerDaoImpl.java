package cn.daxiang.hbtd.webserver.module.vn.dao.impl;

import cn.daxiang.hbtd.webserver.core.redis.RedisDao;
import cn.daxiang.hbtd.webserver.module.vn.dao.PowerDao;
import com.alibaba.fastjson.JSONObject;
import org.springframework.stereotype.Component;

import java.util.Collection;

/**
 * <AUTHOR>
 * @date 2024/7/10
 */
@Component
public class PowerDaoImpl extends RedisDao implements PowerDao {
    /**
     * 战力排行
     */
    private static final String VN_POWER_RANK_CACHE_KEY = "VN_POWER_RANK";
    /**
     * 战力刷新时间
     */
    private static final String VN_POWER_RANK_REFRESH_TIME_CACHE_KEY = "VN_POWER_RANK_REFRESH_TIME";

    @Override
    public JSONObject getVnPowerRank() {
        JSONObject response = new JSONObject();
        Collection<JSONObject> rankList = getListObject(VN_POWER_RANK_CACHE_KEY, JSONObject.class);
        if (rankList.isEmpty()) {
            return response;
        }
        Integer time = get(Integer.class, VN_POWER_RANK_REFRESH_TIME_CACHE_KEY);
        if (time == null) {
            time = 0;
        }
        response.put("time", time);
        response.put("data", rankList);
        return response;
    }
}
