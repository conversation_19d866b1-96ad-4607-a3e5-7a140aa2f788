package cn.daxiang.hbtd.webserver.module.platform.gaoreyh;

import cn.daxiang.framework.result.TResult;
import cn.daxiang.hbtd.webserver.core.ServletUtils;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 高热-硬核渠道
 */
@Controller
@RequestMapping(value = "/gaoreyh")
public class GaoReYhController {
    private static final Logger LOGGER = LoggerFactory.getLogger(GaoReYhController.class);
    @Autowired
    private GaoReYhPlatformImpl platformImpl;

    @RequestMapping(value = "/deliver", method = {RequestMethod.POST, RequestMethod.GET})
    public void deliver(HttpServletRequest request, HttpServletResponse response) throws IOException {
        LOGGER.info("deliver request  ip:{},queryString: {},parameter:{}", ServletUtils.getRemoteAddress(request), request.getQueryString(), request.getParameterMap());
        try {
            JSONObject params = new JSONObject();
            params.put("uid", request.getParameter("uid"));
            params.put("money", request.getParameter("money"));
            params.put("time", request.getParameter("time"));
            params.put("sid", request.getParameter("sid"));
            params.put("orderid", request.getParameter("orderid"));
            params.put("ext", request.getParameter("ext"));
            params.put("flag", request.getParameter("flag"));
            params.put("payment", request.getParameter("payment"));
            params.put("sign", request.getParameter("sign"));
            TResult<Object> deliverResult = platformImpl.deliver(params);
            if (deliverResult.isOk()) {
                response.getWriter().write("1");
                return;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        response.getWriter().write("-1");
        return;
    }

    @RequestMapping(value = "/disableLogin", method = {RequestMethod.POST, RequestMethod.GET})
    public void disableLogin(HttpServletRequest request, HttpServletResponse response) {
        LOGGER.info("disableLogin request  ip:{},queryString: {},parameter:{}", ServletUtils.getRemoteAddress(request), request.getQueryString(), request.getParameterMap());
        try {
            JSONObject params = new JSONObject();
            params.put("gkey", request.getParameter("gkey"));
            params.put("tkey", request.getParameter("tkey"));
            params.put("sid", request.getParameter("sid"));
            params.put("uid", request.getParameter("uid"));
            params.put("type", request.getParameter("type"));
            params.put("uname", request.getParameter("uname"));
            params.put("time", request.getParameter("time"));
            params.put("banduration", request.getParameter("banduration"));
            params.put("sign", request.getParameter("sign"));
            int code = platformImpl.disableLogin(params);
            response.getWriter().write(code + "");
            return;
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    @RequestMapping(value = "/disableIp", method = {RequestMethod.POST, RequestMethod.GET})
    public void disableIp(HttpServletRequest request, HttpServletResponse response) {
        LOGGER.info("disableIp request  ip:{},queryString: {},parameter:{}", ServletUtils.getRemoteAddress(request), request.getQueryString(), request.getParameterMap());
        try {
            JSONObject params = new JSONObject();
            params.put("gkey", request.getParameter("gkey"));
            params.put("tkey", request.getParameter("tkey"));
            params.put("sid", request.getParameter("sid"));
            params.put("uid", request.getParameter("uid"));
            params.put("ip", request.getParameter("ip"));
            params.put("uname", request.getParameter("uname"));
            params.put("type", request.getParameter("type"));
            params.put("time", request.getParameter("time"));
            params.put("sign", request.getParameter("sign"));
            int code = platformImpl.disableIp(params);
            response.getWriter().write(code + "");
            return;
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    @RequestMapping(value = "/disableChat", method = {RequestMethod.POST, RequestMethod.GET})
    public void disableChat(HttpServletRequest request, HttpServletResponse response) {
        LOGGER.info("disableChat request  ip:{},queryString: {},parameter:{}", ServletUtils.getRemoteAddress(request), request.getQueryString(), request.getParameterMap());
        try {
            JSONObject params = new JSONObject();
            params.put("gkey", request.getParameter("gkey"));
            params.put("tkey", request.getParameter("tkey"));
            params.put("uid", request.getParameter("uid"));
            params.put("sid", request.getParameter("sid"));
            params.put("uname", request.getParameter("uname"));
            params.put("roleid", request.getParameter("roleid"));
            params.put("type", request.getParameter("type"));
            params.put("time", request.getParameter("time"));
            params.put("banduration", request.getParameter("banduration"));
            params.put("sign", request.getParameter("sign"));
            int code = platformImpl.disableChat(params);
            response.getWriter().write(code + "");
            return;
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    @RequestMapping(value = "/query", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public JSONObject query(HttpServletRequest request, HttpServletResponse response) throws IOException {
        JSONObject params = new JSONObject();
        params.put("uid", request.getParameter("uid"));
        params.put("serverId", request.getParameter("serverId"));
        params.put("timestamp", request.getParameter("timestamp"));
        params.put("sign", request.getParameter("sign"));
        return platformImpl.userQuery(params);
    }
}