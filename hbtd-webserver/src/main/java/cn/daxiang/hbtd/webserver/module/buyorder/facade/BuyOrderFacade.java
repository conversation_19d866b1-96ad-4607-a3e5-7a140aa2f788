package cn.daxiang.hbtd.webserver.module.buyorder.facade;

import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.hbtd.webserver.core.database.table.BuyOrder;
import cn.daxiang.hbtd.webserver.module.buyorder.model.ShopItem;

import java.util.Map;

public interface BuyOrderFacade {

    /**
     * 创建订单
     *
     * @param actorId
     * @param serverType
     * @param serverId
     * @param userId
     * @param platformId
     * @param channel
     * @param thirdId
     * @param shopItemId
     * @param chargeId
     * @param count
     * @param amount
     * @return
     */
    BuyOrder createBuyOrder(long actorId, int serverType, int serverId, long userId, int platformId, String channel, String thirdId, int shopItemId, int chargeId, int count,
        long amount);

    /**
     * 获取订单
     *
     * @param actorId
     * @param orderId
     * @return
     */
    BuyOrder getBuyOrder(long actorId, String orderId);

    /**
     * 批价,申请购买
     *
     * @param userId
     * @param serverType
     * @param serverId
     * @param actorId
     * @param shopItem
     * @param ext
     * @return
     */
    TResult<Map<String, Object>> doInquiry(long userId, int serverType, int serverId, long actorId, ShopItem shopItem, Map<String, Object> ext);

    /**
     * 发货，购买成功
     *
     * @param buyOrder
     * @return
     */
    Result doDeliver(BuyOrder buyOrder);

    /**
     * 登陆检查是否有漏单
     *
     * @param actorId
     * @param platformId
     * @return
     */
    Result loginCheck(long actorId, int platformId);

    /**
     * 是否存在第三方订单
     *
     * @param actorId
     * @param thirdOrderId
     * @return
     */
    boolean isExist(long actorId, String thirdOrderId);

    /**
     * 获取订单
     * @param thirdOrderId
     * @return
     */
    BuyOrder getBuyOrder(String thirdOrderId);
}
