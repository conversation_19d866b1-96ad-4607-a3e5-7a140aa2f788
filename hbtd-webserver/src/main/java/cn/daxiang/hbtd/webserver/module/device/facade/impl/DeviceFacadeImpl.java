package cn.daxiang.hbtd.webserver.module.device.facade.impl;

import cn.daxiang.framework.result.Result;
import cn.daxiang.hbtd.webserver.module.device.dao.DeviceDao;
import cn.daxiang.hbtd.webserver.module.device.facade.DeviceFacade;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class DeviceFacadeImpl implements DeviceFacade {

    @Autowired
    private DeviceDao deviceDao;

    @Override
    public Result record(String udid, String imei, String brand, String model, String resolution, String os, String version, String lang, int type, String channel, String ip) {
        deviceDao.record(udid, imei, brand, model, resolution, os, version, lang, type, channel, ip);
        return Result.valueOf();
    }

}
