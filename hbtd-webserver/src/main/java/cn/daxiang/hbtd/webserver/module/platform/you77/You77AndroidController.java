package cn.daxiang.hbtd.webserver.module.platform.you77;

import cn.daxiang.framework.result.TResult;
import cn.daxiang.hbtd.webserver.core.ServletUtils;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@Controller
@RequestMapping(value = "/77youandroid")
public class You77AndroidController {
    private static final Logger LOGGER = LoggerFactory.getLogger(You77AndroidController.class);
    @Autowired
    private You77AndroidPlatformImpl platformImpl;

    @RequestMapping(value = "/deliver", method = {RequestMethod.POST, RequestMethod.GET})
    public void deliver(HttpServletRequest request, HttpServletResponse response) throws IOException {
        LOGGER.error("deliver request  ip:{},queryString: {},parameter:{}", ServletUtils.getRemoteAddress(request), request.getQueryString(), request.getParameterMap());
        try {
            JSONObject params = new JSONObject(true);
            params.put("qqes_order", request.getParameter("qqes_order"));
            params.put("cp_order", request.getParameter("cp_order"));
            params.put("gameid", request.getParameter("gameid"));
            params.put("fee", request.getParameter("fee"));
            params.put("currency", request.getParameter("currency"));
            params.put("timestamp", request.getParameter("timestamp"));
            params.put("ext", request.getParameter("ext"));
            params.put("sign", request.getParameter("sign"));
            TResult<Object> result = platformImpl.deliver(params);
            if (result.isOk()) {
                response.getWriter().write("success");
                return;
            }
        } catch (Exception e) {
            LOGGER.error("", e);
        }
        response.getWriter().write("failure");
    }

    @RequestMapping(value = "/applyOrder", method = {RequestMethod.POST, RequestMethod.GET})
    public void applyOrder(HttpServletRequest request, HttpServletResponse response) throws IOException {
        LOGGER.error("applyOrder request  ip:{},queryString: {},parameter:{}", ServletUtils.getRemoteAddress(request), request.getQueryString(), request.getParameterMap());
        JSONObject params = new JSONObject(true);
        params.put("game_id", request.getParameter("game_id"));
        params.put("uid", request.getParameter("uid"));
        params.put("server_id", request.getParameter("server_id"));
        params.put("role_id", request.getParameter("role_id"));
        params.put("fee", request.getParameter("fee"));
        params.put("currency", request.getParameter("currency"));
        params.put("rate", request.getParameter("rate"));
        params.put("ext", request.getParameter("ext"));
        params.put("timestamp", request.getParameter("timestamp"));
        params.put("sign", request.getParameter("sign"));
        JSONObject result = platformImpl.applyOrder(params);
        response.getWriter().write(result.toString());
    }

    @RequestMapping(value = "/sign")
    @ResponseBody
    public TResult<String> sign(@RequestBody JSONObject object, HttpServletRequest request) {
        try {
            return TResult.sucess(platformImpl.sign(object));
        } catch (Exception e) {
            LOGGER.error("", e);
        }
        return TResult.fail();
    }

    @RequestMapping(value = "/queryProduct", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public JSONObject productQuery(HttpServletRequest request, HttpServletResponse response) throws IOException {
        JSONObject data = new JSONObject();
        data.put("game_id", request.getParameter("game_id"));
        data.put("uid", request.getParameter("uid"));
        data.put("server_id", request.getParameter("server_id"));
        data.put("role_id", request.getParameter("role_id"));
        data.put("product_id", request.getParameter("product_id"));
        data.put("sign", request.getParameter("sign"));
        return platformImpl.productQuery(data);
    }
}
