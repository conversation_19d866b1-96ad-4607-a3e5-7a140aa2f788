package cn.daxiang.hbtd.webserver.module.server;

import cn.daxiang.framework.result.Result;
import cn.daxiang.hbtd.webserver.module.server.dao.ServerDao;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Collections;
import java.util.Map;
import java.util.TimeZone;

@Controller
@RequestMapping("/server")
public class ServerController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ServerController.class);
    @Autowired
    private ServerLoadBalance serverLoadBalance;
    @Autowired
    private ServerDao serverDao;

    @RequestMapping(value = "/activityException", method = RequestMethod.POST)
    public void report(@RequestBody JSONObject object) {
        int serverId = object.getIntValue("serverId");
        serverLoadBalance.activityException(serverId);
    }

    /**
     * 获取开服时间
     *
     * @return
     */
    @RequestMapping(value = "/getOpenTime", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public Map<Integer, Long> getOpenTime() {
        try {
            return serverDao.getServerOpenTime();
        } catch (Exception e) {
            LOGGER.error("{}", e);
        }
        return Collections.emptyMap();
    }

    @RequestMapping(value = "/open", method = RequestMethod.POST)
    @ResponseBody
    public Result open(@RequestBody JSONObject object) {
        int serverType = object.getIntValue("serverType");
        int serverId = object.getIntValue("serverId");
        Server server = ServerHelper.get(serverId);
        if (server == null) {
            LOGGER.error("Server not found, serverType:{}, serverId:{}", serverType, serverId);
            return Result.fail();
        }
        String timeZone = object.getString("timeZone");
        if (!TimeZone.getDefault().getID().equals(timeZone)) {
            serverLoadBalance.timeZoneException(serverId, timeZone);
            return Result.fail();
        }
        serverLoadBalance.sendOpenServerMessage(server);
        return Result.valueOf();
    }
}