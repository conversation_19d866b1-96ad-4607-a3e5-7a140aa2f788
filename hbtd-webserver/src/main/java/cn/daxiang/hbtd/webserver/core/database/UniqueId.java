package cn.daxiang.hbtd.webserver.core.database;

public class UniqueId {

    public static long baseActorId(int serverId, int serverType) {
        long flag = Long.valueOf(serverType);
        long sid = Long.valueOf(serverId);
        // flag 左移动16位 serverId 左移24
        long flagResult = flag << 40;
        long result = sid << 24;
        return result + flagResult;
    }

    public static long otherId(int serverId, int serverType) {
        long flag = Long.valueOf(serverType);
        long sid = Long.valueOf(serverId);
        // flag 左移动16位 serverId 左移40
        long flagresult = flag << 56;
        long result = sid << 40;
        return result + flagresult;
    }
}
