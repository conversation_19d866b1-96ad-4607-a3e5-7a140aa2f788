package cn.daxiang.hbtd.webserver.core.database.table;

import cn.daxiang.framework.database.SingleEntity;
import cn.daxiang.framework.database.annotation.Column;
import cn.daxiang.framework.database.annotation.DBQueueType;
import cn.daxiang.framework.database.annotation.Table;
import cn.daxiang.framework.identity.IdentiyKey;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/25
 */
@Table(name = "disable_ip", type = DBQueueType.IMPORTANT)
public class DisableIp extends SingleEntity<Long> {
    /**
     * 封禁IP地址
     */
    @Column(pk = true)
    private String ip;
    /**
     * 封禁IP结束时间
     */
    @Column
    private long endTime;
    /**
     * 封禁执行时间
     */
    @Column
    private long disabledTime;

    @Column
    private String desc;

    public static DisableIp valueOf(String ip, long endTime, String desc) {
        DisableIp disableIp = new DisableIp();
        disableIp.setIp(ip);
        disableIp.setEndTime(endTime);
        disableIp.setDisabledTime(System.currentTimeMillis());
        disableIp.setDesc(desc);
        return disableIp;
    }

    @Override
    public IdentiyKey findPkId() {
        return IdentiyKey.build(ip);
    }

    @Override
    public void setPkId(IdentiyKey pk) {

    }

    @Override
    public List<IdentiyKey> keyLists() {
        return null;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public long getEndTime() {
        return endTime;
    }

    public void setEndTime(long endTime) {
        this.endTime = endTime;
    }

    public long getDisabledTime() {
        return disabledTime;
    }

    public void setDisabledTime(long disabledTime) {
        this.disabledTime = disabledTime;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
