package cn.daxiang.hbtd.webserver.module.user.response;

import cn.daxiang.hbtd.webserver.module.user.model.HistoryServerEntity;
import com.google.common.collect.Lists;

import java.util.Collection;

/**
 * 历史服务器响应
 *
 * <AUTHOR>
 */
public class HistoryServerResponse {
    /**
     * 历史服务器列表，按照登录顺序
     * {@code HistoryServerEntity}
     */
    private Collection<HistoryServerEntity> histories;

    public static HistoryServerResponse valueOf(Collection<HistoryServerEntity> histories) {
        HistoryServerResponse response = new HistoryServerResponse();
        response.histories = Lists.newArrayList(histories);
        return response;
    }

    public Collection<HistoryServerEntity> getHistories() {
        return histories;
    }
}
