package cn.daxiang.hbtd.webserver.module.user.model;

public class AuthTokenResponse {
    /**
     * 用户ID
     */
    private long uid;
    /**
     * 登陆时间
     */
    private long loginTime;
    /**
     * 服务器验证token
     */
    private String token;
    /**
     * 是否是新用户
     */
    private boolean newUser;
    /**
     * 是否是老手(其他区有号并且满足某种条件)
     */
    private boolean veteran;
    /**
     * 平台附带信息(详见各平台ID)
     */
    private Object platform;

    public static AuthTokenResponse valueOf(long uid, String token, long loginTime, boolean newUser, boolean veteran, Object platform) {
        AuthTokenResponse response = new AuthTokenResponse();
        response.uid = uid;
        response.token = token;
        response.loginTime = loginTime;
        response.newUser = newUser;
        response.veteran = veteran;
        response.platform = platform;
        return response;
    }

    public long getUid() {
        return uid;
    }

    public long getLoginTime() {
        return loginTime;
    }

    public String getToken() {
        return token;
    }

    public boolean isNewUser() {
        return newUser;
    }

    public boolean isVeteran() {
        return veteran;
    }

    public Object getPlatform() {
        return platform;
    }
}
