package cn.daxiang.shared.reward;

/**
 * 奖励类型
 *
 * <AUTHOR>
 */
public enum RewardType {

    /**
     * 10.资源类型（金币、钻石）
     * {@code ResourceId}
     */
    RESOURCE(10),

    /**
     * 11.物品
     */
    GOODS(11),
    /**
     * 12.英雄
     */
    HERO(12),
    /**
     * 13.装备
     */
    EQUIPMENT(13),
    /**
     * 14.符文
     */
    RUNE(14),
    /**
     * 15.特权
     */
    PREROGATIVE(15),
    /**
     * 17.头像框
     */
    AVATAR_FRAME(17),
    /**
     * 18.头像
     */
    AVATAR(18),
    /**
     * 19.形象
     */
    SHOW(18),
    /**
     * 20.宝物
     */
    TREASURE(20),
    /**
     * 0.NONE
     */
    NONE(0);
    private int id;

    private RewardType(int id) {
        this.id = id;
    }

    public static RewardType getType(int id) {
        for (RewardType type : RewardType.values()) {
            if (type.id == id) {
                return type;
            }
        }
        return NONE;
    }

    public int getId() {
        return id;
    }

    @Override
    public String toString() {
        return String.valueOf(id);
    }
}
