package cn.daxiang.shared.module.crossCup;

import cn.daxiang.shared.module.lineup.LineupAttribute;
import com.google.common.collect.Maps;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

/**
 * 巅峰竞技场参赛选手信息
 *
 * <AUTHOR>
 * @date 2020/2/7
 */
public class CrossCupPlayer {
    /**
     * 服务器类型
     */
    private int serverType;
    /**
     * 服务器ID
     */
    private int serverId;
    /**
     * 角色ID
     */
    private long actorId;
    /**
     * 角色属性
     */
    private Map<Byte, Object> attributes;
    /**
     * 阵容信息
     * key:CrossCupBattleType,value:{positionId,value:LineupAttribute}
     */
    private Map<Integer, Map<Integer, LineupAttribute>> lineupMap;
    /**
     * 锁定阵容
     */
    private boolean lockLineup;
    /**
     * 巅峰竞技场战区ID crossCupRegionId
     */
    private int zoneId;
    /**
     * 组ID
     */
    private int groupId;
    /**
     * 组内OrderId
     */
    private int orderId;
    /**
     * 对手ID
     */
    private long oppositeId;
    /**
     * 淘汰状态
     * {@code CrossCupState}
     */
    private int eliminatedState;
    /**
     * 淘汰战斗ID
     * {@code CrossCupBattle.id}
     */
    private long eliminatedBattleId;
    /**
     * 下注信息
     * key:serverType,value:{key:serverId,value:{key:actorId,value:count}}
     */
    private Map<Integer, Map<Integer, Map<Long, Integer>>> betMap = new HashMap<>();
    /**
     * 夺冠次数
     */
    private int overlordsTimes;
    /**
     * 系统技能
     */
    private Map<Integer, Collection<Integer>> systemSkillMap = Maps.newHashMap();

    public static CrossCupPlayer valueOf(int serverType, int serverId, int crossCupRegionId, long actorId, Map<Byte, Object> attributes,
        Map<Integer, Map<Integer, LineupAttribute>> lineupMap, int overlordsTimes, Map<Integer, Collection<Integer>> systemSkillMap) {
        CrossCupPlayer player = new CrossCupPlayer();
        player.serverType = serverType;
        player.serverId = serverId;
        player.zoneId = crossCupRegionId;
        player.actorId = actorId;
        player.attributes = attributes;
        player.lineupMap = lineupMap;
        player.overlordsTimes = overlordsTimes;
        player.systemSkillMap = systemSkillMap;
        return player;
    }

    public int getServerType() {
        return serverType;
    }

    public void setServerType(int serverType) {
        this.serverType = serverType;
    }

    public int getServerId() {
        return serverId;
    }

    public void setServerId(int serverId) {
        this.serverId = serverId;
    }

    public long getActorId() {
        return actorId;
    }

    public void setActorId(long actorId) {
        this.actorId = actorId;
    }

    public Map<Byte, Object> getAttributes() {
        return attributes;
    }

    public void setAttributes(Map<Byte, Object> attributes) {
        this.attributes = attributes;
    }

    public Map<Integer, Map<Integer, LineupAttribute>> getLineupMap() {
        return lineupMap;
    }

    public void setLineupMap(Map<Integer, Map<Integer, LineupAttribute>> lineupMap) {
        this.lineupMap = lineupMap;
    }

    public boolean isLockLineup() {
        return lockLineup;
    }

    public void setLockLineup(boolean lockLineup) {
        this.lockLineup = lockLineup;
    }

    public int getZoneId() {
        return zoneId;
    }

    public void setZoneId(int zoneId) {
        this.zoneId = zoneId;
    }

    public int getGroupId() {
        return groupId;
    }

    public void setGroupId(int groupId) {
        this.groupId = groupId;
    }

    public int getOrderId() {
        return orderId;
    }

    public void setOrderId(int orderId) {
        this.orderId = orderId;
    }

    public long getOppositeId() {
        return oppositeId;
    }

    public void setOppositeId(long oppositeId) {
        this.oppositeId = oppositeId;
    }

    public int getEliminatedState() {
        return eliminatedState;
    }

    public void setEliminatedState(int eliminatedState) {
        this.eliminatedState = eliminatedState;
    }

    public long getEliminatedBattleId() {
        return eliminatedBattleId;
    }

    public void setEliminatedBattleId(long eliminatedBattleId) {
        this.eliminatedBattleId = eliminatedBattleId;
    }

    public Map<Integer, Map<Integer, Map<Long, Integer>>> getBetMap() {
        return betMap;
    }

    public void setBetMap(Map<Integer, Map<Integer, Map<Long, Integer>>> betMap) {
        this.betMap = betMap;
    }

    public int getOverlordsTimes() {
        return overlordsTimes;
    }

    public void setOverlordsTimes(int overlordsTimes) {
        this.overlordsTimes = overlordsTimes;
    }

    public Map<Integer, Collection<Integer>> getSystemSkillMap() {
        return systemSkillMap;
    }

    public void setSystemSkillMap(Map<Integer, Collection<Integer>> systemSkillMap) {
        this.systemSkillMap = systemSkillMap;
    }
}
