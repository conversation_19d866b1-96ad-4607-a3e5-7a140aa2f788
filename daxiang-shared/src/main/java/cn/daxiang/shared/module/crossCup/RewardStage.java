package cn.daxiang.shared.module.crossCup;

/**
 * <AUTHOR>
 * @date 2025/3/25
 */
public class RewardStage {
    /**
     * 阶段
     * 1阶段：维持战区配置；
     * 2阶段：维持战区配置，3队6人 变更为 3队18人；
     * 3阶段：提升战区，保持3队18人；
     */
    private int rewardStage;
    /**
     * 二、三阶段额外技能列表
     * CrossCupFeaturesConfig.id
     */
    private int featuresId;
    /**
     * 2、3阶段开启的模块 CrossCupUpGroupConfig.id
     */
    private int upGroupId;

    public static RewardStage valueOf(int rewardStage, int featuresId, int upGroupId) {
        RewardStage stage = new RewardStage();
        stage.setRewardStage(rewardStage);
        stage.setFeaturesId(featuresId);
        stage.setUpGroupId(upGroupId);
        return stage;
    }

    public int getRewardStage() {
        return rewardStage;
    }

    public void setRewardStage(int rewardStage) {
        this.rewardStage = rewardStage;
    }

    public int getFeaturesId() {
        return featuresId;
    }

    public void setFeaturesId(int featuresId) {
        this.featuresId = featuresId;
    }

    public int getUpGroupId() {
        return upGroupId;
    }

    public void setUpGroupId(int upGroupId) {
        this.upGroupId = upGroupId;
    }
}
