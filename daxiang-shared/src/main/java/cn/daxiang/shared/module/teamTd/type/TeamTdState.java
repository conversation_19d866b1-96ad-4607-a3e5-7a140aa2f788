package cn.daxiang.shared.module.teamTd.type;

/**
 * <AUTHOR>
 * @date 2020/12/23
 */
public enum TeamTdState {
    /**
     * 1.空闲
     */
    IDLE(1),
    /**
     * 2.战斗
     */
    BATTLE(2),
    /**
     * 0.NONE
     */
    NONE(0);

    private int id;

    private TeamTdState(int id) {
        this.id = id;
    }

    public static TeamTdState getType(int id) {
        for (TeamTdState type : TeamTdState.values()) {
            if (type.id == id) {
                return type;
            }
        }
        return NONE;
    }

    public int getId() {
        return id;
    }

    @Override
    public String toString() {
        return String.valueOf(id);
    }
}
