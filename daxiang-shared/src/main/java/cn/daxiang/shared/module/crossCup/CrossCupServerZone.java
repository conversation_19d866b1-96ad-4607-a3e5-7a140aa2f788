package cn.daxiang.shared.module.crossCup;

import java.util.Collection;

/**
 * 巅峰竞技场服务器赛区
 *
 * <AUTHOR>
 * @date 2020/2/10
 */
public class CrossCupServerZone {
    /**
     * 当前赛区ID
     * 如果赛区ID为0表示服务器本轮未参与
     */
    private int zoneId;
    /**
     * 赛区列表
     */
    private Collection<Integer> zoneList;

    public static CrossCupServerZone valueOf(int zoneId, Collection<Integer> zoneList) {
        CrossCupServerZone response = new CrossCupServerZone();
        response.zoneId = zoneId;
        response.zoneList = zoneList;
        return response;
    }

    public int getZoneId() {
        return zoneId;
    }

    public Collection<Integer> getZoneList() {
        return zoneList;
    }
}
