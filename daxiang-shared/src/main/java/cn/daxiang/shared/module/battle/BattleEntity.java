package cn.daxiang.shared.module.battle;

import cn.daxiang.shared.module.lineup.LineupAttribute;

import java.util.Collection;
import java.util.Map;

/**
 * 攻击挑战实体
 *
 * <AUTHOR>
 */

public class BattleEntity {
    /**
     * 玩家属性
     */
    private Map<Integer, LineupAttribute> lineupAttributeMap;

    /**
     * 角色属性
     */
    private Map<Byte, Object> attributes;

    /**
     * 系统技能
     */
    private Map<Integer, Collection<Integer>> sysSkillMaps;

    public static BattleEntity valueOf(Map<Integer, LineupAttribute> lineupAttributeMap, Map<Byte, Object> attributes, Map<Integer, Collection<Integer>> sysSkillMaps) {
        BattleEntity entity = new BattleEntity();
        entity.attributes = attributes;
        entity.lineupAttributeMap = lineupAttributeMap;
        entity.sysSkillMaps = sysSkillMaps;
        return entity;
    }

    public Map<Integer, LineupAttribute> getLineupAttributeMap() {
        return lineupAttributeMap;
    }

    public void setLineupAttributeMap(Map<Integer, LineupAttribute> lineupAttributeMap) {
        this.lineupAttributeMap = lineupAttributeMap;
    }

    public Map<Byte, Object> getAttributes() {
        return attributes;
    }

    public void setAttributes(Map<Byte, Object> attributes) {
        this.attributes = attributes;
    }

    public Map<Integer, Collection<Integer>> getSysSkillMaps() {
        return sysSkillMaps;
    }

    public void setSysSkillMaps(Map<Integer, Collection<Integer>> sysSkillMaps) {
        this.sysSkillMaps = sysSkillMaps;
    }
}
