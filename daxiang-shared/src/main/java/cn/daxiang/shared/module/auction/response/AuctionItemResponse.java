package cn.daxiang.shared.module.auction.response;

import cn.daxiang.shared.module.auction.model.AuctionItem;
import cn.daxiang.shared.module.auction.type.AuctionType;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/12/7
 */
public class AuctionItemResponse {
    /**
     * key:AuctionType,value:Collection<AuctionItem>
     * {@code AuctionType}
     * {@code AuctionItem}
     */
    private Map<AuctionType, Collection<AuctionItem>> items = new HashMap<>();

    public static AuctionItemResponse valueOf(Map<AuctionType, Collection<AuctionItem>> items) {
        AuctionItemResponse response = new AuctionItemResponse();
        response.items = items;
        return response;
    }

    public static AuctionItemResponse valueOf(AuctionType type, Collection<AuctionItem> items) {
        Map<AuctionType, Collection<AuctionItem>> itemMap = new HashMap();
        itemMap.put(type, items);
        return valueOf(itemMap);
    }

    public Map<AuctionType, Collection<AuctionItem>> getItems() {
        return items;
    }
}
