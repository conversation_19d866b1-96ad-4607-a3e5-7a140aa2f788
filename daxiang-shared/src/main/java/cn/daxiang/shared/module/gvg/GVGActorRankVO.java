package cn.daxiang.shared.module.gvg;

import java.util.Map;

/**
 * @author: <PERSON>
 * @date: 2022/12/29 15:53
 * @Description:
 */
public class GVGActorRankVO {
    /**
     * 角色信息
     */
    private Map<Byte, Object> attributes;
    /**
     * 积分
     */
    private long score;
    /**
     * 排名
     */
    private long rank;
    /**
     * 基础积分
     */
    private long baseScore;

    public static GVGActorRankVO valueOf(Map<Byte, Object> attributes, long score, long rank) {
        GVGActorRankVO model = new GVGActorRankVO();
        model.attributes = attributes;
        model.score = score;
        model.rank = rank;
        return model;
    }

    public static GVGActorRankVO valueOf(Map<Byte, Object> attributes, long score, long rank, long baseScore) {
        GVGActorRankVO model = new GVGActorRankVO();
        model.attributes = attributes;
        model.score = score;
        model.rank = rank;
        model.baseScore = baseScore;
        return model;
    }

    public Map<Byte, Object> getAttributes() {
        return attributes;
    }

    public void setAttributes(Map<Byte, Object> attributes) {
        this.attributes = attributes;
    }

    public long getScore() {
        return score;
    }

    public void setScore(long score) {
        this.score = score;
    }

    public long getRank() {
        return rank;
    }

    public void setRank(long rank) {
        this.rank = rank;
    }

    public long getBaseScore() {
        return baseScore;
    }

    public void setBaseScore(long baseScore) {
        this.baseScore = baseScore;
    }
}
