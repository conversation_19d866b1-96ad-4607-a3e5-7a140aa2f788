package cn.daxiang.shared.module.nation;

import java.util.Map;

/**
 * 军团副本军团成员关卡伤害排名显示对象
 */
public class NationDungeonActorDamageRankVO {
    /**
     * 角色信息
     */
    private Map<Byte, Object> actorAttributes;
    /**
     * 伤害
     */
    private long damage;
    /**
     * 排名
     */
    private long rank;

    public static NationDungeonActorDamageRankVO valueOf(Map<Byte, Object> actorAttributes, long damage, long rank) {
        NationDungeonActorDamageRankVO vo = new NationDungeonActorDamageRankVO();
        vo.actorAttributes = actorAttributes;
        vo.damage = damage;
        vo.rank = rank;
        return vo;
    }

    public Map<Byte, Object> getActorAttributes() {
        return actorAttributes;
    }

    public void setActorAttributes(Map<Byte, Object> actorAttributes) {
        this.actorAttributes = actorAttributes;
    }

    public long getDamage() {
        return damage;
    }

    public void setDamage(long damage) {
        this.damage = damage;
    }

    public long getRank() {
        return rank;
    }

    public void setRank(long rank) {
        this.rank = rank;
    }
}
