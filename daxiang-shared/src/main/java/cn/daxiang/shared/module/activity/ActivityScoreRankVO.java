package cn.daxiang.shared.module.activity;

import java.util.HashMap;
import java.util.Map;

/**
 * 活动积分排名
 *
 * <AUTHOR>
 * @date 2020/3/4
 */
public class ActivityScoreRankVO {
    /**
     * 角色属性Map
     * key:{@code ActorFieldType},value:Value
     */
    private Map<Byte, Object> attributes = new HashMap<>();
    /**
     * 积分
     */
    private int score;
    /**
     * 排名
     */
    private long rank;

    public static ActivityScoreRankVO valueOf(Map<Byte, Object> attributes, int score, long rank) {
        ActivityScoreRankVO vo = new ActivityScoreRankVO();
        vo.attributes = attributes;
        vo.score = score;
        vo.rank = rank;
        return vo;
    }

    public Map<Byte, Object> getAttributes() {
        return attributes;
    }

    public int getScore() {
        return score;
    }

    public long getRank() {
        return rank;
    }
}
