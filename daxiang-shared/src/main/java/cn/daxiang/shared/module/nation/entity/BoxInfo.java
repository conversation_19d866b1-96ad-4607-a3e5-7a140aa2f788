package cn.daxiang.shared.module.nation.entity;

import cn.daxiang.shared.reward.RewardObject;

import java.util.HashMap;
import java.util.Map;

/**
 * 宝箱信息
 */
public class BoxInfo {
    /**
     * 领取者信息
     */
    private Map<Byte, Object> actorAttributes = new HashMap();
    /**
     * 领取到的奖励
     */
    private RewardObject rewardObject;

    public static BoxInfo valueOf(Map<Byte, Object> actorAttributes, RewardObject rewardObject) {
        BoxInfo boxInfo = new BoxInfo();
        boxInfo.actorAttributes = actorAttributes;
        boxInfo.rewardObject = rewardObject;
        return boxInfo;
    }

    public Map<Byte, Object> getActorAttributes() {
        return actorAttributes;
    }

    public void setActorAttributes(Map<Byte, Object> actorAttributes) {
        this.actorAttributes = actorAttributes;
    }

    public RewardObject getRewardObject() {
        return rewardObject;
    }

    public void setRewardObject(RewardObject rewardObject) {
        this.rewardObject = rewardObject;
    }
}
