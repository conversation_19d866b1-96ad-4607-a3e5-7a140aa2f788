package cn.daxiang.shared.module.activity;

import java.util.Map;

/**
 * 鹊桥会玩家实体
 */
public class BirdBridgeActorEntity {
    /**
     * 角色属性
     */
    private Map<Byte, Object> attributes;
    /**
     * 当前仙缘值
     */
    private long fateScore;
    /**
     * 仙缘值到达时间
     */
    private long fateArriveTime;
    /**
     * 当前魅力值
     */
    private long flowerScore;
    /**
     * 魅力值到达时间
     */
    private long flowerArriveTime;

    public static BirdBridgeActorEntity valueOf(Map<Byte, Object> attributes) {
        BirdBridgeActorEntity entity = new BirdBridgeActorEntity();
        entity.attributes = attributes;
        entity.fateArriveTime = System.currentTimeMillis();
        entity.flowerArriveTime = System.currentTimeMillis();
        return entity;
    }

    public Map<Byte, Object> getAttributes() {
        return attributes;
    }

    public void setAttributes(Map<Byte, Object> attributes) {
        this.attributes = attributes;
    }

    public long getFateScore() {
        return fateScore;
    }

    public void setFateScore(long fateScore) {
        this.fateScore = fateScore;
    }

    public long getFlowerScore() {
        return flowerScore;
    }

    public void setFlowerScore(long flowerScore) {
        this.flowerScore = flowerScore;
    }

    public long entityActorId() {
        return Long.parseLong(attributes.get((byte) 5).toString());
    }

    public int entityServer() {
        return (int) attributes.get((byte) 4);
    }

    public long getFateArriveTime() {
        return fateArriveTime;
    }

    public void setFateArriveTime(long fateArriveTime) {
        this.fateArriveTime = fateArriveTime;
    }

    public long getFlowerArriveTime() {
        return flowerArriveTime;
    }

    public void setFlowerArriveTime(long flowerArriveTime) {
        this.flowerArriveTime = flowerArriveTime;
    }

    public void addFateScore(int score) {
        this.fateScore += score;
        this.fateArriveTime = System.currentTimeMillis();
    }

    public void addFlowerScore(int score) {
        this.flowerScore += score;
        this.flowerArriveTime = System.currentTimeMillis();
    }
}
