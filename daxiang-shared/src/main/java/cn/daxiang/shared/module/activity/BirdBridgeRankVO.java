package cn.daxiang.shared.module.activity;

import java.util.HashMap;
import java.util.Map;

/**
 * 活动积分排名
 *
 * <AUTHOR>
 * @date 2020/3/4
 */
public class BirdBridgeRankVO {
    /**
     * 角色属性Map
     * key:{@code ActorFieldType},value:Value
     */
    private Map<Byte, Object> attributes = new HashMap<>();
    /**
     * 仙缘值/魅力值
     */
    private long score;
    /**
     * 排名
     */
    private long rank;

    public static BirdBridgeRankVO valueOf(Map<Byte, Object> attributes, long score, long rank) {
        BirdBridgeRankVO vo = new BirdBridgeRankVO();
        vo.attributes = attributes;
        vo.score = score;
        vo.rank = rank;
        return vo;
    }

    public Map<Byte, Object> getAttributes() {
        return attributes;
    }

    public long getScore() {
        return score;
    }

    public long getRank() {
        return rank;
    }
}
