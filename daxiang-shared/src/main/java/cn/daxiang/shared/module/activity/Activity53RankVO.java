package cn.daxiang.shared.module.activity;

import cn.daxiang.protocol.game.TypeProtocol;

import java.util.Map;

/**
 * 星布棋局排行榜展示信息
 */
public class Activity53RankVO {
    /**
     * 角色属性Map
     * key:{@code ActorKey},value:Value
     */
    private Map<Byte, Object> attributes;
    /**
     * 排名
     */
    private long rank;
    /**
     * 次数
     */
    private int times;

    public static Activity53RankVO valueOf(Map<Byte, Object> attributes, long rank, int times) {
        Activity53RankVO rankVO = new Activity53RankVO();
        rankVO.attributes = attributes;
        rankVO.rank = rank;
        rankVO.times = times;
        return rankVO;
    }

    public Map<Byte, Object> getAttributes() {
        return attributes;
    }

    public long getRank() {
        return rank;
    }

    public int getTimes() {
        return times;
    }

    public long getActorId() {
        return Long.parseLong(this.attributes.get((byte) TypeProtocol.ActorFieldType.ACTOR_ID_VALUE).toString());
    }
}
