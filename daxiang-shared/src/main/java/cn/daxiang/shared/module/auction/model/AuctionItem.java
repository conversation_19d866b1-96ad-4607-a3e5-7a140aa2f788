package cn.daxiang.shared.module.auction.model;

import com.alibaba.fastjson.annotation.JSONField;

import java.util.*;

/**
 * 拍卖品
 *
 * <AUTHOR>
 * @date 2020/9/7
 */
public class AuctionItem {
    /**
     * 唯一ID
     */
    private long uid;
    /**
     *
     */
    private int id;
    /**
     * 当前价格
     */
    private int price;
    /**
     * 玩家属性
     */
    private Map<Byte, Object> attributes = new HashMap<>();
    /**
     * 竞拍玩家ID列表
     */
    private Collection<Long> bidActorIds = new HashSet<>();
    /**
     * 开始时间
     */
    private long beginTime;
    /**
     * 结束时间
     */
    private long endTime;
    /**
     * 关注人数
     */
    private int careNum;
    /**
     * 上一次竞拍时间
     */
    private long bidTime;
    /**
     * 上一次竞价最高者
     */
    private long lastBidActor;

    public static AuctionItem valueOf(long uid, int id, int price, long beginTime, long endTime) {
        AuctionItem auctionItem = new AuctionItem();
        auctionItem.uid = uid;
        auctionItem.id = id;
        auctionItem.price = price;
        auctionItem.beginTime = beginTime;
        auctionItem.endTime = endTime;
        return auctionItem;
    }

    public long getUid() {
        return uid;
    }

    public void setUid(long uid) {
        this.uid = uid;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getPrice() {
        return price;
    }

    public void setPrice(int price) {
        this.price = price;
    }

    public Collection<Long> getBidActorIds() {
        return bidActorIds;
    }

    public void setBidActorIds(Set<Long> bidActorIds) {
        this.bidActorIds = bidActorIds;
    }

    public long getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(long beginTime) {
        this.beginTime = beginTime;
    }

    public long getEndTime() {
        return endTime;
    }

    public void setEndTime(long endTime) {
        this.endTime = endTime;
    }

    public Map<Byte, Object> getAttributes() {
        return attributes;
    }

    public void setAttributes(Map<Byte, Object> attributes) {
        this.attributes = attributes;
    }

    public int getCareNum() {
        return careNum;
    }

    public void setCareNum(int careNum) {
        this.careNum = careNum;
    }

    public long getBidTime() {
        return bidTime;
    }

    public void setBidTime(long bidTime) {
        this.bidTime = bidTime;
    }

    public long getLastBidActor() {
        return lastBidActor;
    }

    public void setLastBidActor(long lastBidActor) {
        this.lastBidActor = lastBidActor;
    }

    @JSONField(serialize = false)
    public int getServerId() {
        return (int) this.getAttributes().getOrDefault((byte) 4, 0);
    }

    @JSONField(serialize = false)
    public long getWinner() {
        return (long) this.getAttributes().getOrDefault((byte) 5, 0L);
    }
}
