package cn.daxiang.shared.module.teamTd.type;

/**
 * <AUTHOR>
 * @date 2020/12/28
 */
public enum TeamTdParameterKey {
    /**
     * 1.开始时间(long)
     */
    BATTLE_START_TIME(1),
    /**
     * 2.成员属性列表
     */
    MEMBER_ATTRIBUTES(2),
    /**
     * 3.波数
     */
    BATTLE_WAVE(3),
    /**
     * 4.最后一波耗时（ms）
     */
    LAST_WAVE_TIME(4),
    /**
     * 5.是否作弊
     */
    IS_CHEAT(5),
    /**
     * 0.NONE
     */
    NONE(0);

    private int id;

    private TeamTdParameterKey(int id) {
        this.id = id;
    }

    public static TeamTdParameterKey getKey(int id) {
        for (TeamTdParameterKey key : TeamTdParameterKey.values()) {
            if (key.id == id) {
                return key;
            }
        }
        return NONE;
    }

    public int getId() {
        return id;
    }

    @Override
    public String toString() {
        return String.valueOf(id);
    }
}
