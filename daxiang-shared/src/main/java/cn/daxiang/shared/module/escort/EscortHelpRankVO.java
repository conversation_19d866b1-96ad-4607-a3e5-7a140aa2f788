package cn.daxiang.shared.module.escort;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: Cary
 * @date: 2022/02/10 10:32
 * @Description:
 */
public class EscortHelpRankVO {
    /**
     * 角色属性Map
     * key:{@code ActorKey},value:Value
     */
    private Map<Byte, Object> attributes = new HashMap<>();
    /**
     * 排名
     */
    private long rank;
    /**
     * 护镖次数
     */
    private int times;

    public static EscortHelpRankVO valueOf(long rank, Map<Byte, Object> attributes, int times) {
        EscortHelpRankVO vo = new EscortHelpRankVO();
        vo.rank = rank;
        vo.attributes = attributes;
        vo.times = times;
        return vo;
    }

    public Map<Byte, Object> getAttributes() {
        return attributes;
    }

    public void setAttributes(Map<Byte, Object> attributes) {
        this.attributes = attributes;
    }

    public long getRank() {
        return rank;
    }

    public void setRank(long rank) {
        this.rank = rank;
    }

    public int getTimes() {
        return times;
    }

    public void setTimes(int times) {
        this.times = times;
    }
}
