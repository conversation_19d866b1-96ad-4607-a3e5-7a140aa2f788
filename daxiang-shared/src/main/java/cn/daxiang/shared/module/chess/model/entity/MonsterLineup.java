package cn.daxiang.shared.module.chess.model.entity;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.Collection;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/4/25
 */
public class MonsterLineup {
    /**
     * key:位置ID，value:怪物数值
     */
    private Map<Integer, ChessMonster> chessMonsterMap = Maps.newHashMap();
    /**
     * 怪物特性Id
     */
    private Collection<Integer> featureIdList = Lists.newArrayList();
    /**
     * 阵容配置Id
     */
    private int formationId;

    public static MonsterLineup valueOf(Map<Integer, ChessMonster> chessMonsterMap, Collection<Integer> featureIdList, int formationId) {
        MonsterLineup monsterLineup = new MonsterLineup();
        monsterLineup.chessMonsterMap.putAll(chessMonsterMap);
        monsterLineup.featureIdList.addAll(featureIdList);
        monsterLineup.formationId = formationId;
        return monsterLineup;
    }

    public Map<Integer, ChessMonster> getChessMonsterMap() {
        return chessMonsterMap;
    }

    public void setChessMonsterMap(Map<Integer, ChessMonster> chessMonsterMap) {
        this.chessMonsterMap = chessMonsterMap;
    }

    public Collection<Integer> getFeatureIdList() {
        return featureIdList;
    }

    public void setFeatureIdList(Collection<Integer> featureIdList) {
        this.featureIdList = featureIdList;
    }

    public int getFormationId() {
        return formationId;
    }

    public void setFormationId(int formationId) {
        this.formationId = formationId;
    }
}
