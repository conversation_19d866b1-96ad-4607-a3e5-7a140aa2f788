package cn.daxiang.shared.module.nation.entity;

import cn.daxiang.protocol.game.TypeProtocol;
import cn.daxiang.shared.type.NationMemberType;

import java.util.HashMap;
import java.util.Map;

/**
 * 国家成员实体
 *
 * <AUTHOR>
 */
public class NationMemberEntity {
    /**
     * 角色属性Map
     * key:{@code ActorKey},value:Value
     */
    private Map<Byte, Object> attributes = new HashMap<>();
    /**
     * 成员类型
     * {@code NationMemberType}
     */
    private NationMemberType memberType;
    /**
     * 工会建设值
     */
    private long buildScore;
    /**
     * 工会总建设值
     */
    private long buildTotalScore;

    public static NationMemberEntity valueOf(Map<Byte, Object> attributes) {
        NationMemberEntity entity = new NationMemberEntity();
        entity.attributes = attributes;
        entity.memberType = NationMemberType.MEMBER;
        return entity;
    }

    public Map<Byte, Object> getAttributes() {
        return attributes;
    }

    public void setAttributes(Map<Byte, Object> attributes) {
        this.attributes = attributes;
    }

    public NationMemberType getMemberType() {
        return memberType;
    }

    public void setMemberType(String memberType) {
        this.memberType = NationMemberType.getType(Integer.valueOf(memberType));
    }

    public long entityActorId() {
        return Long.parseLong( attributes.get((byte) 5).toString());
    }

    public int entityLevel() {
        return (int) attributes.get((byte) 8);
    }

    public void setType(NationMemberType memberType) {
        this.memberType = memberType;
    }

    public int entityServer() {
        return (int) attributes.getOrDefault((byte) 4, 0);
    }

    public long entityPower() {
        return Long.valueOf(attributes.getOrDefault((byte) 17, 0).toString());
    }

    public long entityLogoutTime() {
        return Long.valueOf(attributes.get((byte) 103).toString());
    }

    public long getBuildScore() {
        return buildScore;
    }

    public void setBuildScore(long buildScore) {
        this.buildScore = buildScore;
    }

    public long getBuildTotalScore() {
        return buildTotalScore;
    }

    public void setBuildTotalScore(long buildTotalScore) {
        this.buildTotalScore = buildTotalScore;
    }

    public void refreshBuildScore(boolean reset, long exp) {
        if (reset) {
            this.buildScore = 0;
        } else {
            this.buildScore += exp;
            this.buildTotalScore += exp;
        }
    }

    public long getActorId() {
        return Long.parseLong(this.attributes.get((byte) TypeProtocol.ActorFieldType.ACTOR_ID_VALUE).toString());
    }

}
