package cn.daxiang.shared.module.teamTd.model;

import cn.daxiang.shared.module.teamTd.type.TeamTdMemberType;
import com.alibaba.fastjson.annotation.JSONField;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/12/23
 */
public class TeamTdMember {
    /**
     * 角色属性Map
     * key:{@code ActorKey},value:Value
     */
    private Map<Byte, Object> attributeMap;
    /**
     * 成员类型
     * {@code TeamMemberType}
     */
    private TeamTdMemberType type;
    /**
     * 是否准备
     */
    private boolean ready;
    /**
     * 运行环境分数
     */
    private int reScore;

    public static TeamTdMember valueOf(Map<Byte, Object> attributeMap, TeamTdMemberType type) {
        TeamTdMember member = new TeamTdMember();
        member.attributeMap = attributeMap;
        member.type = type;
        return member;
    }

    public Map<Byte, Object> getAttributeMap() {
        return attributeMap;
    }

    public void setAttributeMap(Map<Byte, Object> attributeMap) {
        this.attributeMap = attributeMap;
    }

    public TeamTdMemberType getType() {
        return type;
    }

    public void setType(TeamTdMemberType type) {
        this.type = type;
    }

    public boolean isReady() {
        return ready;
    }

    public void setReady(boolean ready) {
        this.ready = ready;
    }

    @JSONField(serialize = false)
    public long getActorId() {
        return Long.parseLong(attributeMap.get((byte) 5).toString());
    }

    @JSONField(serialize = false)
    public int getServerId() {
        return (int) attributeMap.get((byte) 4);
    }

    @JSONField(serialize = false)
    public int getCurrentServerId() {
        return (int) attributeMap.get((byte) 40);
    }

    public int getReScore() {
        return reScore;
    }

    public void setReScore(int reScore) {
        this.reScore = reScore;
    }
}
