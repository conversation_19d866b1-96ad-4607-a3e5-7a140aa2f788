package cn.daxiang.shared.module.activity.type;

public enum Activity57RankType {
    /**
     * 1. 仙缘值排行榜
     */
    FATE_RANK(1),
    /**
     * 2. 魅力值排行榜
     */
    FLOWER_RANK(2),
    /**
     * 0.NONE
     */
    NONE(0);
    /**
     * ID
     */
    private int id;

    private Activity57RankType(int id) {
        this.id = id;
    }

    public static Activity57RankType getType(int id) {
        for (Activity57RankType type : Activity57RankType.values()) {
            if (type.id == id) {
                return type;
            }
        }
        return NONE;
    }

    public int getId() {
        return id;
    }

    @Override
    public String toString() {
        return String.valueOf(id);
    }
}
