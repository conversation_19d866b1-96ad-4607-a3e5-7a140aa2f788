package cn.daxiang.shared.module.common;

import cn.daxiang.shared.reward.RewardObject;
import com.google.common.collect.Lists;

import java.util.Collection;
import java.util.Map;

/**
 * 中奖记录实体
 *
 * @author: <PERSON>
 * @date: 2023/6/1 11:15
 * @Description:
 */
public class RecordEntity {
    /**
     * 角色信息
     */
    private Map<Byte, Object> attributes;

    /**
     * 奖励列表
     */
    private Collection<RewardObject> rewards = Lists.newArrayList();

    /**
     * 描述文本
     */
    private String des;

    public static RecordEntity valueOf(Map<Byte, Object> attributes, Collection<RewardObject> rewards, String des) {
        RecordEntity model = new RecordEntity();
        model.attributes = attributes;
        model.rewards = rewards;
        model.des = des;
        return model;
    }

    public Map<Byte, Object> getAttributes() {
        return attributes;
    }

    public void setAttributes(Map<Byte, Object> attributes) {
        this.attributes = attributes;
    }

    public Collection<RewardObject> getRewards() {
        return rewards;
    }

    public void setRewards(Collection<RewardObject> rewards) {
        this.rewards = rewards;
    }

    public String getDes() {
        return des;
    }

    public void setDes(String des) {
        this.des = des;
    }
}
