package cn.daxiang.shared.module.crossCup;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 巅峰竞技场赛区显示对象
 *
 * <AUTHOR>
 * @date 2020/2/10
 */
public class CrossCupZoneVO {
    /**
     * 状态
     * {@code CrossCupState}
     */
    private int state;
    /**
     * 子状态
     * {@code CrossCupSubState}
     */
    private int subState;
    /**
     * 开始时间
     */
    private long startTime;
    /**
     * 结束时间
     */
    private long endTime;
    /**
     * 战区ID
     */
    private int zoneId;
    /**
     * 小组信息
     * key:groupId,value:CrossCupGroup
     * groupId为0的自己.
     */
    private Map<Integer, CrossCupGroup> groupMap = new HashMap<>();
    /**
     * 生成的押注场次随机ID
     */
    private Map<Integer, List<Integer>> allowBetMap = new HashMap<>();

    public static CrossCupZoneVO valueOf(int state, int subState, long startTime, long endTime, int zoneId, Map<Integer, CrossCupGroup> groupMap,
        Map<Integer, List<Integer>> allowBetMap) {
        CrossCupZoneVO vo = new CrossCupZoneVO();
        vo.state = state;
        vo.subState = subState;
        vo.startTime = startTime;
        vo.endTime = endTime;
        vo.zoneId = zoneId;
        vo.groupMap = groupMap;
        vo.allowBetMap = allowBetMap;
        return vo;
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public int getSubState() {
        return subState;
    }

    public void setSubState(int subState) {
        this.subState = subState;
    }

    public long getStartTime() {
        return startTime;
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }

    public long getEndTime() {
        return endTime;
    }

    public void setEndTime(long endTime) {
        this.endTime = endTime;
    }

    public int getZoneId() {
        return zoneId;
    }

    public void setZoneId(int zoneId) {
        this.zoneId = zoneId;
    }

    public Map<Integer, CrossCupGroup> getGroupMap() {
        return groupMap;
    }

    public void setGroupMap(Map<Integer, CrossCupGroup> groupMap) {
        this.groupMap = groupMap;
    }

    public Map<Integer, List<Integer>> getAllowBetMap() {
        return allowBetMap;
    }

    public void setAllowBetMap(Map<Integer, List<Integer>> allowBetMap) {
        this.allowBetMap = allowBetMap;
    }

}
