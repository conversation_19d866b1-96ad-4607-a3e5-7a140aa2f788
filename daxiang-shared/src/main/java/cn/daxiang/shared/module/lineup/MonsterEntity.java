package cn.daxiang.shared.module.lineup;

/**
 * 怪物实体
 *
 * <AUTHOR>
 */
public class MonsterEntity {
    /**
     * 位置ID
     */
    private int positionId;
    /**
     * 怪物ID
     */
    private int monsterId;
    /**
     * 剩余生命
     */
    private volatile long leftHP;

    public static MonsterEntity valueOf(int positionId, int monsterId, long leftHP) {
        MonsterEntity monster = new MonsterEntity();
        monster.positionId = positionId;
        monster.monsterId = monsterId;
        monster.leftHP = leftHP;
        return monster;
    }

    public int getPositionId() {
        return positionId;
    }

    public void setPositionId(int positionId) {
        this.positionId = positionId;
    }

    public int getMonsterId() {
        return monsterId;
    }

    public void setMonsterId(int monsterId) {
        this.monsterId = monsterId;
    }

    public long getLeftHP() {
        return leftHP;
    }

    public void setLeftHP(long leftHP) {
        this.leftHP = leftHP;
    }
}