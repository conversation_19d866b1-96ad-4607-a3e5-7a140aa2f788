package cn.daxiang.shared.module.gvg;

/**
 * @author: <PERSON>
 * @date: 2022/12/29 16:16
 * @Description:
 */
public class GVGNationRankVo {
    /**
     * 军团Id
     */
    private long nationId;
    /**
     * 军团名称
     */
    private String nationName;
    /**
     * 军团旗帜
     */
    private int flagId;
    /**
     * 团长区服Id
     */
    private int serverId;
    /**
     * 排名
     */
    private long rank;
    /**
     * 值
     */
    private long score;

    public static GVGNationRankVo valueOf(long nationId, String nationName, int flagId, int serverId, long rank, long score) {
        GVGNationRankVo model = new GVGNationRankVo();
        model.nationId = nationId;
        model.nationName = nationName;
        model.flagId = flagId;
        model.serverId = serverId;
        model.rank = rank;
        model.score = score;
        return model;
    }

    public long getNationId() {
        return nationId;
    }

    public void setNationId(long nationId) {
        this.nationId = nationId;
    }

    public String getNationName() {
        return nationName;
    }

    public void setNationName(String nationName) {
        this.nationName = nationName;
    }

    public int getFlagId() {
        return flagId;
    }

    public void setFlagId(int flagId) {
        this.flagId = flagId;
    }

    public int getServerId() {
        return serverId;
    }

    public void setServerId(int serverId) {
        this.serverId = serverId;
    }

    public long getRank() {
        return rank;
    }

    public void setRank(long rank) {
        this.rank = rank;
    }

    public long getScore() {
        return score;
    }

    public void setScore(long score) {
        this.score = score;
    }
}
