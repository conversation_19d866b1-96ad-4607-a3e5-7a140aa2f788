package cn.daxiang.shared.module.sanctuary;

/**
 * 活动结束邮件发奖信息
 *
 * <AUTHOR>
 * @date 2023/8/30
 */
public class EndSendReward {
    /**
     * 角色ID
     */
    private long actorId;
    /**
     * 进度排行（注意发奖必须通关的才发奖）
     */
    private long rank;

    public static EndSendReward valueOf(long actorId, long rank) {
        EndSendReward endSendReward = new EndSendReward();
        endSendReward.actorId = actorId;
        endSendReward.rank = rank;
        return endSendReward;
    }

    public long getActorId() {
        return actorId;
    }

    public void setActorId(long actorId) {
        this.actorId = actorId;
    }

    public long getRank() {
        return rank;
    }

    public void setRank(long rank) {
        this.rank = rank;
    }
}
