package cn.daxiang.shared.module.teamTd.type;

/**
 * 队伍成员类型
 *
 * <AUTHOR>
 * @date 2020/12/23
 */
public enum TeamTdMemberType {
    /**
     * 1.队长
     */
    CAPTAIN(1),
    /**
     * 2.成员
     */
    MEMBER(2),
    /**
     * 0.NONE
     */
    NONE(0);

    private int id;

    private TeamTdMemberType(int id) {
        this.id = id;
    }

    public static TeamTdMemberType getType(int id) {
        for (TeamTdMemberType type : TeamTdMemberType.values()) {
            if (type.id == id) {
                return type;
            }
        }
        return NONE;
    }

    public int getId() {
        return id;
    }

    @Override
    public String toString() {
        return String.valueOf(id);
    }
}
