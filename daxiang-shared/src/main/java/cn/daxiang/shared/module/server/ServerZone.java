package cn.daxiang.shared.module.server;

import cn.daxiang.protocol.game.SystemProtocol;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.Collection;
import java.util.Map;
import java.util.OptionalDouble;
import java.util.Random;
import java.util.TreeMap;

/**
 * 服务器战区信息
 *
 * <AUTHOR>
 * @date 2022/7/1
 */
public class ServerZone {
    /**
     * 服务器类型
     */
    private int type;
    /**
     * 战区ID
     */
    private int zoneId;
    /**
     * 服务器列表
     */
    private Collection<Integer> serverIds = Lists.newArrayList();
    /**
     * 服务器属性信息
     * {@link cn.daxiang.protocol.game.SystemProtocol.ServerAttributeKey}
     * {key:serverId,value:{key:attributeKey,value:attributeValue}}
     * 一定注意这个属性会在每周23点清空并重新填充,不清的话会有被合并的服污染数据
     */
    private Map<Integer, Map<SystemProtocol.ServerAttributeKey, Object>> serverAttributes = Maps.newHashMap();
    /**
     * 首次开启时间
     */
    private long firstOpenTime;

    public static ServerZone valueOf(int type, int zoneId, Collection<Integer> serverIds) {
        ServerZone serverZone = new ServerZone();
        serverZone.type = type;
        serverZone.zoneId = zoneId;
        serverZone.serverIds = serverIds;
        return serverZone;
    }

    public static void main(String[] args) {
        JSON.DEFAULT_GENERATE_FEATURE |= SerializerFeature.DisableCircularReferenceDetect.getMask();
        JSON.DEFAULT_GENERATE_FEATURE |= SerializerFeature.PrettyFormat.getMask();
        JSON.DEFAULT_GENERATE_FEATURE |= SerializerFeature.WriteNonStringKeyAsString.getMask();
        JSON.DEFAULT_GENERATE_FEATURE |= SerializerFeature.WriteEnumUsingToString.getMask();
        Map<Integer, Map<SystemProtocol.ServerAttributeKey, Object>> serverAttributes = Maps.newHashMap();
        for (int i = 0; i < 10; i++) {
            Map<SystemProtocol.ServerAttributeKey, Object> attributes = Maps.newHashMap();
            if (i % 2 == 0) {
                attributes.put(SystemProtocol.ServerAttributeKey.SERVER_ATTRIBUTE_RANK_10_POWER, i);
            } else {
                TreeMap<Integer, Long> map = Maps.newTreeMap();
                for (int j = 0; j < 10; j++) {
                    int level = new Random().nextInt(50);
                    long count = new Random().nextInt(1000);
                    map.put(level, count);
                }
                System.err.println(map);
                attributes.put(SystemProtocol.ServerAttributeKey.SERVER_ATTRIBUTE_ACTIVE_COUNT, JSONObject.toJSON(map));
            }
            serverAttributes.put(i, attributes);
        }
        System.err.println(serverAttributes.values().stream().filter(e -> e.containsKey(SystemProtocol.ServerAttributeKey.SERVER_ATTRIBUTE_RANK_10_POWER))
            .mapToLong(e -> Long.parseLong(e.get(SystemProtocol.ServerAttributeKey.SERVER_ATTRIBUTE_RANK_10_POWER).toString())).sum());
        OptionalDouble average = serverAttributes.values().stream().filter(e -> e.containsKey(SystemProtocol.ServerAttributeKey.SERVER_ATTRIBUTE_RANK_10_POWER))
            .mapToLong(e -> Long.parseLong(e.get(SystemProtocol.ServerAttributeKey.SERVER_ATTRIBUTE_RANK_10_POWER).toString())).average();
        System.err.println(average.getAsDouble());

        Map<Integer, Long> attributeMergeMap = Maps.newHashMap();
        for (Map<SystemProtocol.ServerAttributeKey, Object> attributeKeyObjectMap : serverAttributes.values()) {
            if (attributeKeyObjectMap.containsKey(SystemProtocol.ServerAttributeKey.SERVER_ATTRIBUTE_ACTIVE_COUNT)) {
                Map<Object, Object> attributeMap =
                    JSONObject.parseObject(attributeKeyObjectMap.get(SystemProtocol.ServerAttributeKey.SERVER_ATTRIBUTE_ACTIVE_COUNT).toString(), Map.class);
                attributeMap.forEach((k, v) -> attributeMergeMap.merge(Integer.parseInt(k.toString()), Long.parseLong(v.toString()), Long::sum));
            }
        }
        System.err.println(attributeMergeMap);
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getZoneId() {
        return zoneId;
    }

    public void setZoneId(int zoneId) {
        this.zoneId = zoneId;
    }

    public Collection<Integer> getServerIds() {
        return serverIds;
    }

    public void setServerIds(Collection<Integer> serverIds) {
        this.serverIds = serverIds;
    }

    public Map<Integer, Map<SystemProtocol.ServerAttributeKey, Object>> getServerAttributes() {
        return serverAttributes;
    }

    public void setServerAttributes(Map<Integer, Map<SystemProtocol.ServerAttributeKey, Object>> serverAttributes) {
        this.serverAttributes = serverAttributes;
    }

    public void refreshAttributes(int serverId, Map<SystemProtocol.ServerAttributeKey, Object> attributes) {
        Map<SystemProtocol.ServerAttributeKey, Object> serverZoneMap = this.serverAttributes.computeIfAbsent(serverId, k -> Maps.newHashMap());
        attributes.forEach((k, v) -> serverZoneMap.put(k, JSONObject.toJSON(v)));
    }

    public long getAverageAttribute(SystemProtocol.ServerAttributeKey attributeKey) {
        OptionalDouble average =
            this.serverAttributes.values().stream().filter(e -> e.containsKey(attributeKey)).mapToLong(e -> Long.parseLong(e.get(attributeKey).toString())).average();
        if (average.isPresent()) {
            return (long) average.getAsDouble();
        }
        return 0L;
    }

    public Map<Integer, Long> getMapMergeAttribute(SystemProtocol.ServerAttributeKey attributeKey) {
        Map<Integer, Long> attributeMergeMap = Maps.newHashMap();
        for (Map<SystemProtocol.ServerAttributeKey, Object> attributeKeyObjectMap : this.serverAttributes.values()) {
            if (attributeKeyObjectMap.containsKey(attributeKey)) {
                Map<Object, Object> attributeMap = JSONObject.parseObject(attributeKeyObjectMap.get(attributeKey).toString(), Map.class);
                attributeMap.forEach((k, v) -> attributeMergeMap.merge(Integer.parseInt(k.toString()), Long.parseLong(v.toString()), Long::sum));
            }
        }
        return attributeMergeMap;
    }

    public void refresh(Collection<Integer> serverIds) {
        this.serverIds = serverIds;
        this.serverAttributes.clear();
    }

    public long getFirstOpenTime() {
        return firstOpenTime;
    }

    public void setFirstOpenTime(long firstOpenTime) {
        this.firstOpenTime = firstOpenTime;
    }

    public boolean refreshFirstOpenTime(long firstOpenTime) {
        if (this.firstOpenTime != 0 && this.firstOpenTime < firstOpenTime) {
            return false;
        }
        this.firstOpenTime = firstOpenTime;
        return true;
    }
}
