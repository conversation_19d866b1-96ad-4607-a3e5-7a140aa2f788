package cn.daxiang.shared.module.lineup;

import com.google.common.collect.Lists;

import java.util.Collection;
import java.util.List;
import java.util.Map;

public enum SpriteAttributeType {
    /**
     * 1.攻击力
     */
    ATK(1),
    /**
     * 2.生命值
     */
    HP(2),
    /**
     * 3.物理护甲
     */
    PHYSICAL_ARMOR(3),
    /**
     * 4.魔法防御
     */
    SPELL_ARMOR(4),
    /**
     * 5.暴击
     */
    CRIT(5),
    /**
     * 6.韧性(抗暴)
     */
    TOUGHNESS(6),
    /**
     * 7.命中
     */
    ACCURACY(7),
    /**
     * 8.闪避
     */
    EVADE(8),
    /**
     * 9.伤害加成万分比
     */
    DAMAGE_BONUS(9),
    /**
     * 10.伤害减免万分比
     */
    DAMAGE_REDUCTION(10),
    /**
     * 11.回合战-PVP伤害加成万分比
     */
    PVP_DAMAGE_BONUS(11),
    /**
     * 12.回合战-PVP伤害减免万分比
     */
    PVP_DAMAGE_REDUCTION(12),
    /**
     * 13.回合战-PVE伤害加成万分比
     */
    PVE_DAMAGE_BONUS(13),
    /**
     * 14.回合战-PVE伤害减免万分比
     */
    PVE_DAMAGE_REDUCTION(14),
    /**
     * 15.塔防攻击速度(只投放万分比)
     */
    TOWER_ATTACK_SPEED(15),
    /**
     * 16.塔防攻击范围
     */
    TOWER_ATTACK_RANGE(16),
    /**
     * 17.塔防伤害加成百分比
     */
    TOWER_DAMAGE_BONUS(17),
    /**
     * 18.塔防伤害加成固定值
     */
    TOWER_DAMAGE_INCREASE(18),
    /**
     * 19.塔防技能概率
     */
    TOWER_SKILL_RATE(19),
    /**
     * 20.回合战-初始怒气
     */
    RAGE(20),
    /**
     * 21.回合战-每回合怒气回复
     */
    RAGE_RECOVER(21),
    /**
     * 22.最终伤害加成万分比
     */
    FINAL_DAMAGE_BONUS(22),
    /**
     * 23.回合战-被击回复怒气值
     */
    RAGE_PASSIVE(23),
    /**
     * 24.回合战-攻击回复怒气值
     */
    RAGE_ATTACK(24),
    /**
     * 25.回合战-敌方阵亡回复怒气值
     */
    ENEMY_DIED_RAGE(25),
    /**
     * 26.回合战-己方阵亡回复怒气值
     */
    TEAMMATE_DIED_ARAGE(26),
    /**
     * 27.格挡
     */
    BLOCK(27),
    /**
     * 28.破击
     */
    SMASH(28),
    /**
     * 29.塔暴击值
     */
    TOWER_CRIT_INCREASE(29),
    /**
     * 30.塔威力
     */
    TOWER_POWER(30),
    /**
     * 31.塔防PVE增伤
     */
    TOWER_PVE_DAMAGE_BONUS(31),
    /**
     * 32.塔的初始能量
     */
    TOWER_INITIAL_ENERGY(32),
    /**
     * 33.每秒回复能量
     */
    TOWER_ENERGY_RECOVERY_PER_SECOND(33),
    /**
     * 34.击杀回复能量
     */
    TOWER_KILL_ENERGY_RECOVERY(34),
    /**
     * 35.回合战-击杀回复怒气值
     */
    RAGE_STRUCK(35),
    /**
     * 36.灭魏伤害加成万分比
     */
    RESTRAINT_WEI_DAMAGE_BONUS(36),
    /**
     * 37.灭蜀伤害加成万分比
     */
    RESTRAINT_SHU_DAMAGE_BONUS(37),
    /**
     * 38.灭吴伤害加成万分比
     */
    RESTRAINT_WU_DAMAGE_BONUS(38),
    /**
     * 39.灭群伤害加成万分比
     */
    RESTRAINT_QUN_DAMAGE_BONUS(39),
    /**
     * 40.抗魏伤害减免万分比
     */
    INDULGE_WEI_DAMAGE_REDUCTION(40),
    /**
     * 41.抗蜀伤害减免万分比
     */
    INDULGE_SHU_DAMAGE_REDUCTION(41),
    /**
     * 42.抗吴伤害减免万分比
     */
    INDULGE_WU_DAMAGE_REDUCTION(42),
    /**
     * 43.抗群伤害减免万分比
     */
    INDULGE_QUN_DAMAGE_REDUCTION(43),
    /**
     * 44.克防特伤害加成万分比
     */
    RESTRAINT_DEFENSIVE_DAMAGE_BONUS(44),
    /**
     * 45.克技特伤害加成万分比
     */
    RESTRAINT_SKILL_DAMAGE_BONUS(45),
    /**
     * 46.克攻特伤害加成万分比
     */
    RESTRAINT_ATTACK_DAMAGE_BONUS(46),
    /**
     * 47.抗防特伤害减免万分比
     */
    INDULGE_DEFENSIVE_DAMAGE_REDUCTION(47),
    /**
     * 48.抗技特伤害减免万分比
     */
    INDULGE_SKILL_DAMAGE_REDUCTION(48),
    /**
     * 49.抗攻特伤害减免万分比
     */
    INDULGE_ATTACK_DAMAGE_REDUCTION(49),
    /**
     * 吸血比率
     */
    VAMPIRE_RATIO(50),
    /**
     * 反伤比率
     */
    REBOUND_RATIO(51),
    /**
     * 忽视防御比率
     */
    IGNORE_DEFENSE(52),
    /**
     * 暴击伤害
     */
    CRITICAL_DAMAGE(53),
    /**
     * 怒技伤害万分比
     */
    RAGE_SKILL_DAMAGE_BONUS(54),
    /**
     * 怒技抗性万分比
     */
    RAGE_SKILL_DAMAGE_REDUCTION(55),
    /**
     * 治疗比率万分比
     */
    HEAL_BONUS(56),
    /**
     * 控制几率
     */
    CONTROL_PROBABILITY(57),
    /**
     * 免控几率
     */
    EXEMPTION_PROBABILITY(58),
    //todo 59~63 神兽属性计算伤害是对应到英雄属性里执行公式
    /**
     * 神兽战力转换系数
     */
    BEAST_POWER_COEFFICIENT(59),
    /**
     * 神兽基础攻击(1.攻击力)
     */
    BEAST_ATK(60),
    /**
     * 神兽暴击率 对应到英雄暴击率(5.暴击)
     */
    BEAST_CRIT(61),
    /**
     * 神兽爆伤 对应到英雄暴击伤害(53.暴击伤害)
     */
    BEAST_CRIT_DAMAGE(62),
    /**
     * 神兽加伤 对应到英雄伤害加成万分比(9.伤害加成万分比)
     */
    BEAST_DAMAGE_BONUS(63),
    //todo 59~63 神兽属性计算伤害是对应到英雄属性里执行公式
    /**
     * 神兽伤害减免
     */
    BEAST_DAMAGE_REDUCTION(64),
    /**
     * 神兽抗暴
     */
    BEAST_TOUGHNESS(65),
    /**
     * 神兽爆伤减免
     */
    BEAST_CRIT_DAMAGE_REDUCTION(66),
    /**
     * 神兽风免
     */
    BEAST_WIND_DAMAGE_REDUCTION(67),
    /**
     * 神兽雷免
     */
    BEAST_RAY_DAMAGE_REDUCTION(68),
    /**
     * 神兽火免
     */
    BEAST_FIRE_DAMAGE_REDUCTION(69),
    /**
     * 神兽水免
     */
    BEAST_WATER_DAMAGE_REDUCTION(70),
    /**
     * 神兽塔威力
     */
    BEAST_TOWER_POWER(71),
    /**
     * 神兽塔防暴击率
     */
    BEAST_TOWER_CRIT(72),
    /**
     * 神兽塔防爆伤
     */
    BEAST_TOWER_CRIT_DAMAGE(73),
    /**
     * 74.抗减怒
     */
    RAGE_REDUCE_RESISTANCE(74),
    /**
     * 75.普攻增伤
     */
    NORMAL_ATTACK_DAMAGE_BONUS(75),
    /**
     * 76.初始灵力值
     */
    SPIRITUAL(76),
    /**
     * 77.攻击回复灵力值
     */
    SPIRITUAL_ATTACK(77),
    /**
     * 78.受击回复灵力值
     */
    SPIRITUAL_PASSIVE(78),
    /**
     * 79.回合战-每回合灵力回复
     */
    SPIRITUAL_RECOVER(79),
    /**
     * 80.灵气值回复率万分比
     */
    SPIRITUAL_BONUS(80),
    /**
     * 81.最最终伤害加成万分比
     */
    DAMAGE_FINAL_BONUS(81),
    /**
     * 82.最最终伤害减免万分比
     */
    DAMAGE_FINAL_REDUCTION(82),
    /**
     * 83.最终攻击力加成
     */
    FINAL_ATTACK_BONUS(83),
    /**
     * 84.最终血量加成
     */
    FINAL_HP_BONUS(84),
    /**
     * 85.最终物防加成
     */
    FINAL_PHYSICAL_ARMOR_BONUS(85),
    /**
     * 86.最终法防加成
     */
    FINAL_SPELL_ARMOR_BONUS(86),
    /**
     * 87.无双一击攻击力
     */
    UNPARALLELED_ATK(87),
    /**
     * 88.回合无双一击概率
     */
    PVP_UNPARALLELED_PROBABILITY(88),
    /**
     * 89.塔防无双一击概率
     */
    PVE_UNPARALLELED_PROBABILITY(89),
    /**
     * 90.无双一击伤害加成
     */
    UNPARALLELED_DAMAGE_BONUS(90),
    /**
     * 91.彩金装备压制万分比(最最最最终伤害减免,即所有伤害计算完后(目前))
     */
    EQUIPMENT_QUALITY_SUPPRESS_REDUCTION(91),
    /**
     * 92.会心一击概率
     */
    CRITICAL(92),
    /**
     * 93.会心一击防率
     */
    CRITICAL_REDUCTION(93),
    /**
     * 95.速度
     */
    SPEED(95),

    /**
     * 99.全属性(特指四个基础属性{ATK, HP, PHYSICAL_ARMOR, SPELL_ARMOR};)
     */
    ALL_SPRITE_ATTRIBUTE(99),
    //---------------------------------------------------------------------
    //          以下枚举程序不生效，只是方便策划配表的时候好对照着配置
    //---------------------------------------------------------------------
    /**
     * 101.攻击力乘算万分比
     */
    T_ATK(101),
    /**
     * 102.生命值乘算万分比
     */
    T_HP(102),
    /**
     * 103.物理护甲乘算万分比
     */
    T_PHYSICAL_ARMOR(103),
    /**
     * 104.魔法防御乘算万分比
     */
    T_SPELL_ARMOR(104),
    /**
     * 121.回合怒气回复乘算万分比
     */
    T_RAGE_RECOVER(121),
    /**
     * 124.回合攻击回复怒气值乘算万分比
     */
    T_ATTACK_RAGE(124),
    /**
     * 123.回合被击回复怒气值乘算万分比
     */
    T_PASSIVE_RAGE(123),
    /**
     * 125.回合敌方阵亡回复怒气值乘算万分比
     */
    T_ENEMY_DIED_RAGE(125),
    /**
     * 126.回合己方阵亡回复怒气值乘算万分比
     */
    T_TEAMMATE_DIED_ARAGE(126),
    /**
     * 132.塔的初始能量乘算万分比
     */
    T_TOWER_INITIAL_ENERGY(132),
    /**
     * 133.每秒回复能量乘算万分比
     */
    T_TOWER_ENERGY_RECOVERY_PER_SECOND(133),
    /**
     * 134.击杀回复能量乘算万分比
     */
    T_TOWER_KILL_ENERGY_RECOVERY(134),
    /**
     * 199.全属性乘算万分比
     */
    T_ALL_SPRITE_ATTRIBUTE(199),

    /**
     * 999.初始血量最大值
     */
    HP_INIT_MAX(999),
    /**
     * 0.NONE
     */
    NONE(0);
    public static Collection<SpriteAttributeType> ALL_SPRITE_ATTRIBUTE_TYPES = Lists.newArrayList(ATK, HP, PHYSICAL_ARMOR, SPELL_ARMOR);
    /**
     * 神兽属性
     */
    public static Collection<SpriteAttributeType> ALL_BEAST_ATTRIBUTE_TYPES =
        Lists.newArrayList(BEAST_POWER_COEFFICIENT, BEAST_ATK, BEAST_CRIT, BEAST_CRIT_DAMAGE, BEAST_DAMAGE_BONUS);
    /**
     * 最最终四维属性加成
     */
    public static Collection<SpriteAttributeType> FINAL_BASE_ATTRIBUTE_TYPE =
        Lists.newArrayList(FINAL_ATTACK_BONUS, FINAL_HP_BONUS, FINAL_PHYSICAL_ARMOR_BONUS, FINAL_SPELL_ARMOR_BONUS);
    /**
     * 属性ID
     */
    private short id;
    /**
     * 用于计算战力的id
     */
    private byte calcPowerId;

    private SpriteAttributeType(int id) {
        this.id = (short) id;
    }

    public static SpriteAttributeType getType(int id) {
        for (SpriteAttributeType type : SpriteAttributeType.values()) {
            if (type.id == id) {
                return type;
            }
        }
        return NONE;
    }

    public static boolean isBeastAttribute(SpriteAttributeType type) {
        return ALL_BEAST_ATTRIBUTE_TYPES.contains(type);
    }

    public static Collection<Long> getAttributeList(Map<SpriteAttributeType, Long> attributeMap) {
        List<Long> attributeList = Lists.newLinkedList();
        for (SpriteAttributeType attributeType : SpriteAttributeType.values()) {
            if (attributeType.getId() >= 99) {
                break;
            }
            attributeList.add(attributeMap.getOrDefault(attributeType, 0L));
        }
        return attributeList;
    }

    public short getId() {
        return id;
    }

    /**
     * 是否最最终属性加成
     *
     * @param type
     * @return
     */
    public static boolean isFinalBaseAttribute(SpriteAttributeType type) {
        return FINAL_BASE_ATTRIBUTE_TYPE.contains(type);
    }

    @Override
    public String toString() {
        return String.valueOf(id);
    }

    public boolean isTType() {
        return this.id > 100;
    }

    public SpriteAttributeType convertType() {
        return getType(this.id - 100);
    }
}
