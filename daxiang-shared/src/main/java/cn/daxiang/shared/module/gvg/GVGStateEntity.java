package cn.daxiang.shared.module.gvg;

/**
 * GVG活动状态
 *
 * @author: <PERSON>
 * @date: 2022/12/22 19:10
 * @Description:
 */
public class GVGStateEntity {
    /**
     * 当前状态
     */
    private int type;
    /**
     * 开启时间
     */
    private long openTime;
    /**
     * 结束时间
     */
    private long closeTime;
    /**
     * 王城乱斗开启时间
     */
    private long brawlingOpenTime;
    /**
     * 王城乱斗解锁条件
     */
    private int serverZoneOpenDayLimit;

    public static GVGStateEntity valueOf(int type, long openTime, long closeTime) {
        GVGStateEntity model = new GVGStateEntity();
        model.type = type;
        model.openTime = openTime;
        model.closeTime = closeTime;
        return model;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public long getOpenTime() {
        return openTime;
    }

    public void setOpenTime(long openTime) {
        this.openTime = openTime;
    }

    public long getCloseTime() {
        return closeTime;
    }

    public void setCloseTime(long closeTime) {
        this.closeTime = closeTime;
    }

    public long getBrawlingOpenTime() {
        return brawlingOpenTime;
    }

    public void setBrawlingOpenTime(long brawlingOpenTime) {
        this.brawlingOpenTime = brawlingOpenTime;
    }

    public int getServerZoneOpenDayLimit() {
        return serverZoneOpenDayLimit;
    }

    public void setServerZoneOpenDayLimit(int serverZoneOpenDayLimit) {
        this.serverZoneOpenDayLimit = serverZoneOpenDayLimit;
    }
}
