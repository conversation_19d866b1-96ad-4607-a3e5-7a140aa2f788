package cn.daxiang.shared.module.activity;

import cn.daxiang.protocol.game.TypeProtocol;

import java.util.Map;

/**
 * 摘星揽月排行榜展示信息
 *
 * @Author: Gary
 * @Date 2022-08-12 17:12
 * @Description:
 */
public class Activity31RankVO {
    /**
     * 角色属性Map
     * key:{@code ActorKey},value:Value
     */
    private Map<Byte, Object> attributes;
    /**
     * 排名
     */
    private long rank;
    /**
     * 层数
     */
    private int level;

    public static Activity31RankVO valueOf(Map<Byte, Object> attributes, long rank, int level) {
        Activity31RankVO rankVO = new Activity31RankVO();
        rankVO.attributes = attributes;
        rankVO.rank = rank;
        rankVO.level = level;
        return rankVO;
    }

    public Map<Byte, Object> getAttributes() {
        return attributes;
    }

    public long getRank() {
        return rank;
    }

    public int getLevel() {
        return level;
    }

    public long getActorId() {
        return Long.parseLong(this.attributes.get((byte) TypeProtocol.ActorFieldType.ACTOR_ID_VALUE).toString());
    }
}
