package cn.daxiang.shared.type;

/**
 * 国家限制类型
 *
 * <AUTHOR>
 */
public enum NationLimitType {
    /**
     * 1.自动加入
     */
    AUTO_IN(1),
    /**
     * 2.角色等级限制
     * value:level(int)
     */
    ACTOR_LEVEL(2),
    /**
     * 3.禁止加入
     */
    REFUSE_IN(3),
    /**
     * 0.NONE
     */
    NONE(0);

    private int id;

    private NationLimitType(int id) {
        this.id = id;
    }

    public static NationLimitType getType(int id) {
        for (NationLimitType type : NationLimitType.values()) {
            if (type.id == id) {
                return type;
            }
        }
        return NONE;
    }

    public int getId() {
        return id;
    }

    @Override
    public String toString() {
        return String.valueOf(id);
    }
}
