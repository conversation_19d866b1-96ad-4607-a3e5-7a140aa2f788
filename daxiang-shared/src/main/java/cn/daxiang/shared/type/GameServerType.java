package cn.daxiang.shared.type;

/**
 * 游戏服务器类型
 *
 * <AUTHOR>
 */
public enum GameServerType {
    /**
     * 1.内网测试服务器
     */
    INTRANET_TEST(1),
    /**
     * 2.PLAY800
     */
    PLAY800(2),
    /**
     * 3.爱微游
     */
    AWY(3),
    /**
     * 4.阅文游戏
     */
    GAME_BOOK_QQ(4),
    /**
     * 5.P8安卓
     */
    P8_ANDROID(5),
    /**
     * 6.后浪互娱
     */
    HOU_LANG(6), SPARE7(7),
    /**
     * 8.畅想微信小游戏
     */
    CX_GAME(8),
    /**
     * 9.QQ大厅
     */
    QQ_GAME(9),
    /**
     * 10.p8微信
     */
    P8_WX(10),
    /**
     * 11.畅想专服
     */
    CX_GAME1(11),
    /**
     * 12.畅想-上古封神
     */
    CX_GAME2(12),
    /**
     * 13.畅想-神将争霸
     */
    CX_GAME3(13), SPARE13(13), SPARE14(14), SPARE15(15), SPARE16(16), SPARE17(17),
    /**
     * 20.繁体
     */
    FLY_FUN(20),

    /**
     * 30.摩羯手Q
     */
    MJSQ(30),
    /**
     * 验证服服务器类型
     */
    VERIFY_SERVER_TYPE(7777),
    /**
     * 战斗服服务器类型
     */
    BATTLE_SERVER_TYPE(9999),

    /**
     * 0.NONE
     */
    NONE(0);
    private int id;

    private GameServerType(int id) {
        this.id = id;
    }

    public static GameServerType getType(int id) {
        for (GameServerType type : GameServerType.values()) {
            if (type.id == id) {
                return type;
            }
        }
        return NONE;
    }

    public int getId() {
        return id;
    }

    @Override
    public String toString() {
        return String.valueOf(id);
    }
}
