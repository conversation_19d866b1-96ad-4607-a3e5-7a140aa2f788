package cn.daxiang.shared.type;

/**
 * 组队类型
 */
public enum TeamType {
    /**
     * 1.藏兵阁
     */
    ARSENAL(1),
    /**
     * 2.勇闯魔窟-组队魔将
     */
    CAVE_NORMAL(2),
    /**
     * 3.勇闯魔窟-组队天魔
     */
    CAVE_HARD(3),
    /**
     * 0.NONE
     */
    NONE(0);
    private int id;

    private TeamType(int id) {
        this.id = id;
    }

    public static TeamType getType(int id) {
        for (TeamType type : TeamType.values()) {
            if (type.id == id) {
                return type;
            }
        }
        return NONE;
    }

    public int getId() {
        return id;
    }

    @Override
    public String toString() {
        return String.valueOf(id);
    }
}
