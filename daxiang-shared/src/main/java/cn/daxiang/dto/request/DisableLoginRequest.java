package cn.daxiang.dto.request;

public class DisableLoginRequest {
    private long actorId;
    private long beginTime;
    private long endTime;

    public static DisableLoginRequest valueOf(long actorId, long beginTime, long endTime) {
        DisableLoginRequest request = new DisableLoginRequest();
        request.actorId = actorId;
        request.beginTime = beginTime;
        request.endTime = endTime;
        return request;
    }

    public long getActorId() {
        return actorId;
    }

    public void setActorId(long actorId) {
        this.actorId = actorId;
    }

    public long getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(long beginTime) {
        this.beginTime = beginTime;
    }

    public long getEndTime() {
        return endTime;
    }

    public void setEndTime(long endTime) {
        this.endTime = endTime;
    }

}
