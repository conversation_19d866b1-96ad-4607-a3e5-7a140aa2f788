package cn.daxiang.dto.request;

import java.util.Date;

public class ActorRequest extends Request {

    /**
     * 查询类型 1.角色ID 2.角色名 3.帐号 4.其他
     */
    private int queryType;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 显示角色Id
     */
    private long showId;

    /**
     * 角色名
     */
    private String actorName;

    /**
     * 开始角色创建时间
     */
    private Date startDate;
    /**
     * 结束角色创建时间
     */
    private Date endDate;

    /**
     * 最小角色等级
     */
    private int minLevel;
    /**
     * 最大角色等级
     */
    private int maxLevel;
    /**
     * 最小vip等级
     */
    private int minVipLevel;
    /**
     * 最大vip等级
     */
    private int maxVipLevel;
    /**
     * 开始登陆时间
     */
    private Date startLoginDate;
    /**
     * 结束登陆时间
     */
    private Date endLoginDate;

    public ActorRequest() {
    }

    public ActorRequest(long actorId) {
        super(actorId);
    }

    /**
     * 角色名称
     *
     * @param gamerName
     */
    public ActorRequest(String actorName) {
        this.actorName = actorName;
    }

    /**
     * 其他多条件查询
     *
     * @param startDate
     * @param endDate
     * @param minLevel
     * @param maxLevel
     * @param minVipLevel
     * @param maxVipLevel
     * @param startLoginTime
     * @param endLoginTime
     */
    public ActorRequest(Date startDate, Date endDate, int minLevel, int maxLevel, int minVipLevel, int maxVipLevel, Date startLoginDate, Date endLoginDate) {
        this.startDate = startDate;
        this.endDate = endDate;
        this.minLevel = minLevel;
        this.maxLevel = maxLevel;
        this.minVipLevel = minVipLevel;
        this.maxVipLevel = maxVipLevel;
        this.startLoginDate = startLoginDate;
        this.endLoginDate = endLoginDate;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public int getQueryType() {
        return queryType;
    }

    public void setQueryType(int queryType) {
        this.queryType = queryType;
    }

    public long getShowId() {
        return showId;
    }

    public void setShowId(long showId) {
        this.showId = showId;
    }

    public String getActorName() {
        return actorName;
    }

    public void setActorName(String actorName) {
        this.actorName = actorName;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public int getMinLevel() {
        return minLevel;
    }

    public void setMinLevel(int minLevel) {
        this.minLevel = minLevel;
    }

    public int getMaxLevel() {
        return maxLevel;
    }

    public void setMaxLevel(int maxLevel) {
        this.maxLevel = maxLevel;
    }

    public int getMinVipLevel() {
        return minVipLevel;
    }

    public void setMinVipLevel(int minVipLevel) {
        this.minVipLevel = minVipLevel;
    }

    public int getMaxVipLevel() {
        return maxVipLevel;
    }

    public void setMaxVipLevel(int maxVipLevel) {
        this.maxVipLevel = maxVipLevel;
    }

    public Date getStartLoginDate() {
        return startLoginDate;
    }

    public void setStartLoginDate(Date startLoginDate) {
        this.startLoginDate = startLoginDate;
    }

    public Date getEndLoginDate() {
        return endLoginDate;
    }

    public void setEndLoginDate(Date endLoginDate) {
        this.endLoginDate = endLoginDate;
    }

}