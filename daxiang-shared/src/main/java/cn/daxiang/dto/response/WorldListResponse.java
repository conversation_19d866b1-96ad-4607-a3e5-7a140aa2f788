package cn.daxiang.dto.response;

import java.util.ArrayList;
import java.util.List;

public class WorldListResponse {
    private long mailWorldId;
    private String fromName;
    private String title;
    private String content;
    private String attachments;
    private long addTime;
    private long endTime;
    private long actorCreateLimitTime;
    private int actorLevelDownLimit;
    private int actorLevelTopLimit;

    public static WorldListResponse valueOf(long mailWorldId, String fromName, String title, String content, String attachments, long addTime, long endTime,
        long actorCreateLimitTime, int actorLevelDownLimit, int actorLevelTopLimit) {
        WorldListResponse response = new WorldListResponse();
        response.mailWorldId = mailWorldId;
        response.fromName = fromName;
        response.title = title;
        response.content = content;
        response.attachments = attachments;
        response.addTime = addTime;
        response.endTime = endTime;
        response.actorCreateLimitTime = actorCreateLimitTime;
        response.actorLevelDownLimit = actorLevelDownLimit;
        response.actorLevelTopLimit = actorLevelTopLimit;
        return response;
    }

    public static void main(String[] args) {
        String arg = "12345";
        char[] byteList = arg.toCharArray();
        int len = byteList.length % 2 == 0 ? byteList.length / 2 : (byteList.length / 2) + 1;
        List<List<Integer>> total = new ArrayList<>();
        for (int i = 0; i < len; i++) {
            List<Integer> targetList = new ArrayList<>();
            System.err.println(byteList[i]);
            char num = byteList[i];
            targetList.add(num - 1);
            targetList.add(num + 1);
            total.add(targetList);
        }
        System.err.println(total);
    }

    public long getMailWorldId() {
        return mailWorldId;
    }

    public void setMailWorldId(long mailWorldId) {
        this.mailWorldId = mailWorldId;
    }

    public String getFromName() {
        return fromName;
    }

    public void setFromName(String fromName) {
        this.fromName = fromName;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getAttachments() {
        return attachments;
    }

    public void setAttachments(String attachments) {
        this.attachments = attachments;
    }

    public long getAddTime() {
        return addTime;
    }

    public void setAddTime(long addTime) {
        this.addTime = addTime;
    }

    public long getEndTime() {
        return endTime;
    }

    public void setEndTime(long endTime) {
        this.endTime = endTime;
    }

    public long getActorCreateLimitTime() {
        return actorCreateLimitTime;
    }

    public void setActorCreateLimitTime(long actorCreateLimitTime) {
        this.actorCreateLimitTime = actorCreateLimitTime;
    }

    public int getActorLevelDownLimit() {
        return actorLevelDownLimit;
    }

    public void setActorLevelDownLimit(int actorLevelDownLimit) {
        this.actorLevelDownLimit = actorLevelDownLimit;
    }

    public int getActorLevelTopLimit() {
        return actorLevelTopLimit;
    }

    public void setActorLevelTopLimit(int actorLevelTopLimit) {
        this.actorLevelTopLimit = actorLevelTopLimit;
    }

}
