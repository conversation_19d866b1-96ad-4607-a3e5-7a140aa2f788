package cn.daxiang.dto.response;

import cn.daxiang.dto.model.EquipmentInfo;

import java.util.ArrayList;
import java.util.List;

/**
 * 装备
 *
 * <AUTHOR>
 */
public class EquipmentResponse {
    private List<EquipmentInfo> EquipmentInfoList;

    public EquipmentResponse() {
        EquipmentInfoList = new ArrayList<EquipmentInfo>();
    }

    public List<EquipmentInfo> getEquipmentInfoList() {
        return EquipmentInfoList;
    }

    public void addEquipmentInfoList(EquipmentInfo equipmentInfo) {
        this.EquipmentInfoList.add(equipmentInfo);
    }
}
