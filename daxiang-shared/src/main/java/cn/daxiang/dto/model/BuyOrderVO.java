package cn.daxiang.dto.model;

import java.sql.Timestamp;

public class BuyOrderVO {
    /**
     * 订单ID
     */
    private String orderId;
    /**
     * 第三方订单ID
     */
    private String thirdOrderId;
    /**
     * 商品ID
     */
    private int shopItemId;
    /**
     * 充值ID
     */
    private int chargeId;
    /**
     * 数量
     */
    private int count;
    /**
     * 金额(单位:元)
     */
    private double amount;
    /**
     * 创建时间
     */
    private Timestamp createTimestamp;
    /**
     * 成功时间
     */
    private Timestamp successTimestamp;
    /**
     * 发货时间
     */
    private Timestamp deliverTimestamp;

    public static BuyOrderVO valueOf(String orderId, String thirdOrderId, int shopItemId, int chargeId, int count, double amount, Timestamp createTimestamp,
        Timestamp successTimestamp, Timestamp deliverTimestamp) {
        BuyOrderVO vo = new BuyOrderVO();
        vo.orderId = orderId;
        vo.thirdOrderId = thirdOrderId;
        vo.shopItemId = shopItemId;
        vo.chargeId = chargeId;
        vo.count = count;
        vo.amount = amount;
        vo.createTimestamp = createTimestamp;
        vo.successTimestamp = successTimestamp;
        vo.deliverTimestamp = deliverTimestamp;
        return vo;
    }

    public String getOrderId() {
        return orderId;
    }

    public String getThirdOrderId() {
        return thirdOrderId;
    }

    public int getShopItemId() {
        return shopItemId;
    }

    public int getChargeId() {
        return chargeId;
    }

    public int getCount() {
        return count;
    }

    public double getAmount() {
        return amount;
    }

    public Timestamp getCreateTimestamp() {
        return createTimestamp;
    }

    public Timestamp getSuccessTimestamp() {
        return successTimestamp;
    }

    public Timestamp getDeliverTimestamp() {
        return deliverTimestamp;
    }
}
