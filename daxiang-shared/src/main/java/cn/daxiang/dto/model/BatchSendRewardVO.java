package cn.daxiang.dto.model;

import cn.daxiang.shared.reward.RewardObject;

import java.util.List;

/**
 * 批量发送奖励对象
 *
 * <AUTHOR>
 */
public class BatchSendRewardVO {

    private long actorId;

    private List<RewardObject> rewardList;

    public static BatchSendRewardVO valueOf(long actorId, List<RewardObject> rewardList) {
        BatchSendRewardVO vo = new BatchSendRewardVO();
        vo.actorId = actorId;
        vo.rewardList = rewardList;
        return vo;
    }

    public long getActorId() {
        return actorId;
    }

    public void setActorId(long actorId) {
        this.actorId = actorId;
    }

    public List<RewardObject> getRewardList() {
        return rewardList;
    }

    public void setRewardList(List<RewardObject> rewardList) {
        this.rewardList = rewardList;
    }

}
