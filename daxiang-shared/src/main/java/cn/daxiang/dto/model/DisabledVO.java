package cn.daxiang.dto.model;

/**
 * 封号实体
 *
 * <AUTHOR>
 */
public class DisabledVO {
    /**
     * 玩家ID
     */
    private long actorId;
    /**
     * 玩家名称
     */
    private String actorName;
    /**
     * 渠道
     */
    private String channel;
    /**
     * 游戏服ID
     */
    private int serverId;
    /**
     * 封号开始时间
     */
    private long beginTime;
    /**
     * 封号结束时间
     */
    private long endTime;
    /**
     * 封号时间
     */
    private long disabeldTime;

    public static DisabledVO valueOf(long actorId, String actorName, String channel, int serverId, long beginTime, long endTime, long disabeldTime) {
        DisabledVO vo = new DisabledVO();
        vo.actorId = actorId;
        vo.actorName = actorName;
        vo.channel = channel;
        vo.serverId = serverId;
        vo.beginTime = beginTime;
        vo.endTime = endTime;
        vo.disabeldTime = disabeldTime;
        return vo;
    }

    public long getActorId() {
        return actorId;
    }

    public void setActorId(long actorId) {
        this.actorId = actorId;
    }

    public String getActorName() {
        return actorName;
    }

    public void setActorName(String actorName) {
        this.actorName = actorName;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public int getServerId() {
        return serverId;
    }

    public void setServerId(int serverId) {
        this.serverId = serverId;
    }

    public long getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(long beginTime) {
        this.beginTime = beginTime;
    }

    public long getEndTime() {
        return endTime;
    }

    public void setEndTime(long endTime) {
        this.endTime = endTime;
    }

    public long getDisabeldTime() {
        return disabeldTime;
    }

    public void setDisabeldTime(long disabeldTime) {
        this.disabeldTime = disabeldTime;
    }
}
