package cn.daxiang.dto.result;

import com.alibaba.fastjson.annotation.JSONField;

public class JsonResult {
    protected int result;
    protected Object message;
    @JSONField(serialize = false)
    private String serializedName = "message";

    public int getResult() {
        return result;
    }

    public void setResult(int result) {
        this.result = result;
    }

    public Object getMessage() {
        return message;
    }

    public void setMessage(Object message) {
        this.message = message;
    }

    public String getSerializedName() {
        return serializedName;
    }

    public void setSerializedName(String serializedName) {
        this.serializedName = serializedName;
    }

    public void setup(int code) {
        this.result = code;
    }

    public JsonResult setup(ResultCode resultCode) {
        this.result = resultCode.getCode();
        this.message = resultCode.getMessage();
        return this;
    }

    public JsonResult setup(ResultCode resultCode, String serializedName) {
        this.result = resultCode.getCode();
        this.message = resultCode.getMessage();
        this.serializedName = serializedName;
        return this;
    }

    public JsonResult setup(int result, Object message) {
        this.result = result;
        this.message = message;
        return this;
    }

    public void setup(int result, Object message, String serializedName) {
        this.result = result;
        this.message = message;
        this.serializedName = serializedName;
    }

    public JsonResult setupAsException(Throwable e) {
        this.result = ResultCode.SERVER_ERROR.getCode();
        this.message = e.getMessage();
        return this;
    }
}
