package cn.daxiang.dto.result;

public class ResultCode {

    public static final ResultCode SUCCESS = new ResultCode(0, "Success");
    public static final ResultCode INVALID_PARAM = new ResultCode(1, "Invalid parameter(s)");
    public static final ResultCode INVALID_SESSION = new ResultCode(100, "Invalid session");
    public static final ResultCode SERVER_ERROR = new ResultCode(500, "Server internal error");
    public static final ResultCode INVALID_TOKEN = new ResultCode(1000, "Auth token failed");
    public static final ResultCode LOGIN_FAILED = new ResultCode(1001, "Login failed");
    public static final ResultCode INVALID_IP = new ResultCode(1002, "Invalid ip address");
    private final int code;
    private final String message;

    protected ResultCode(int code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public boolean equals(Object obj) {
        return obj != null && obj instanceof ResultCode && this.code == ((ResultCode) obj).getCode();
    }

    public boolean equals(int code) {
        return this.code == code;
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
