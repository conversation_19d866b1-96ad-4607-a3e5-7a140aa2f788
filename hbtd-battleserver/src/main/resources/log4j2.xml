<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="warn" monitorInterval="1"
               shutdownHook="disable">
    <Properties>
        <Property name="LOG_HOME">logs</Property>
    </Properties>
    <Appenders>
        <Console name="Console" target="SYSTEM_OUT">
            <ThresholdFilter level="info" onMatch="ACCEPT"
                             onMismatch="DENY"/>
            <PatternLayout pattern="%d %-5level [%t] [%F:%L] - %msg%n"/>
        </Console>
        <RollingFile name="FILE" fileName="${LOG_HOME}/info.log"
                     filePattern="${LOG_HOME}/info.%d{yyyy-MM-dd}.log">
            <!--控制台只输出level及以上级别的信息（onMatch），其他的直接拒绝（onMismatch） -->
            <ThresholdFilter level="info" onMatch="ACCEPT"
                             onMismatch="DENY"/>
            <PatternLayout pattern="%d %-5level [%t] [%F:%L] - %msg%n"/>
            <Policies>
                <TimeBasedTriggeringPolicy modulate="true"
                                           interval="1"/>
            </Policies>
        </RollingFile>
        <RollingFile name="FILE_ERROR" fileName="${LOG_HOME}/error.log"
                     filePattern="${LOG_HOME}/error.%d{yyyy-MM-dd}.log">
            <ThresholdFilter level="warn" onMatch="ACCEPT"
                             onMismatch="DENY"/>
            <PatternLayout pattern="%d %-5level [%t] [%F:%L] - %msg%n"/>
            <Policies>
                <TimeBasedTriggeringPolicy modulate="true"
                                           interval="1"/>
            </Policies>
        </RollingFile>
    </Appenders>
    <Loggers>
        <logger name="com.alibaba.nacos.client.logging" level="ERROR">
        </logger>
        <logger name="org.springframework" level="ERROR">
        </logger>
        <logger name="cn.daxiang.framework" level="INFO">
        </logger>
        <logger name="cn.daxiang.hbtd.battleserver" level="DEBUG">
        </logger>
        <logger name="com.alibaba.druid.support.http.stat.WebSessionStat"
                level="OFF">
        </logger>
        <Root level="INFO">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="FILE"/>
            <AppenderRef ref="FILE_ERROR"/>
        </Root>
    </Loggers>
</Configuration>
