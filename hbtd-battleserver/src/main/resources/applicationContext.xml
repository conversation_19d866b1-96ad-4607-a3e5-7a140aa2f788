<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context" xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
           http://www.springframework.org/schema/beans/spring-beans.xsd
           http://www.springframework.org/schema/context
           http://www.springframework.org/schema/context/spring-context-4.0.xsd">

    <context:annotation-config/>
    <!-- 启动包扫描功能，以便注册带有@Controller、@Service、@repository、@Component等注解的类成为spring的bean -->
    <context:component-scan base-package="cn.daxiang.framework,cn.daxiang.hbtd.battleserver"/>
    <context:property-placeholder location="classpath*:*.properties"
                                  ignore-unresolvable="true"/>
    <bean id="thread-pool-num" class="java.lang.Integer">
        <constructor-arg value="20"></constructor-arg>
    </bean>
    <bean id="controller.package_scan" class="java.lang.String">
        <constructor-arg value="cn.daxiang.hbtd.battleserver.controller"/>
    </bean>
    <bean id="dataconfig.package_scan" class="java.lang.String">
        <constructor-arg value="cn.daxiang.hbtd.battleserver.core.dataconfig.model"/>
    </bean>

    <import resource="schedule.xml"/>
</beans>