<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:task="http://www.springframework.org/schema/task"
       xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.2.xsd


			http://www.springframework.org/schema/task
			http://www.springframework.org/schema/task/spring-task-3.2.xsd">


    <!-- Enables the Spring Task @Scheduled programming model -->
    <task:executor id="executor" pool-size="${task.executor.pool.size}"/>
    <task:scheduler id="scheduler" pool-size="${task.scheduler.pool.size}"/>

    <!-- task注解方式 -->
    <task:annotation-driven executor="executor" scheduler="scheduler"/>
</beans>