package cn.daxiang.hbtd.battleserver.core.dataconfig.convert;

import cn.daxiang.hbtd.battleserver.core.dataconfig.convert.callback.ConvertStringToMapCallback;
import com.google.common.collect.Maps;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

public abstract class HashMapConfig<K, V> implements IConfigable {

    protected HashMap<K, V> cache = Maps.newHashMapWithExpectedSize(8);

    @Override
    public void buildObject(final String config) {

        Map<K, V> map = StringConvert.convertToObjectHashMap(new ConvertStringToMapCallback<K, V>() {

            @Override
            public String getTokenizerString() {
                return config;
            }

            @Override
            public int getSize() {
                return fromSize();
            }

            @Override
            public void fromArray(Map<K, V> map, String[] array) {
                map.put(fromKey(array), fromValue(array));
            }
        });
        this.cache.putAll(map);
    }

    public Map<K, V> getMap() {
        return this.cache;
    }

    public V find(K k) {
        return cache.get(k);
    }

    public Set<K> keys() {
        return cache.keySet();
    }

    protected abstract int fromSize();

    protected abstract K fromKey(String[] array);

    protected abstract V fromValue(String[] array);
}
