package cn.daxiang.hbtd.battleserver.module.team.parser;

import cn.daxiang.shared.module.teamTd.type.TeamTdType;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/1/9
 */
@Component
public class TeamContext {
    /**
     * key:SkillSeekType,value:SkillSeekParser
     */
    private Map<Integer, TeamParser> parerMap = Maps.newHashMap();

    /**
     * 注册解析器
     *
     * @param type
     * @param parser
     */
    public void register(TeamTdType type, TeamParser parser) {
        parerMap.put(type.getId(), parser);
    }

    /**
     * 获取解析器
     *
     * @param type
     * @return
     */
    public TeamParser getParser(TeamTdType type) {
        return parerMap.get(type.getId());
    }
}