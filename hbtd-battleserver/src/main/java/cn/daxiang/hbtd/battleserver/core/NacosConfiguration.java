package cn.daxiang.hbtd.battleserver.core;

import cn.daxiang.framework.utils.ObjectReference;
import com.alibaba.nacos.api.annotation.NacosInjected;
import com.alibaba.nacos.api.common.Constants;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.spring.context.annotation.config.EnableNacosConfig;
import com.alibaba.nacos.spring.context.annotation.config.NacosPropertySource;
import com.alibaba.nacos.spring.context.annotation.config.NacosPropertySources;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

import javax.annotation.PostConstruct;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2021/12/2
 */
@Configuration
@PropertySource("classpath:nacos.properties")
@EnableNacosConfig
@NacosPropertySources({@NacosPropertySource(name = "global", dataId = "${nacos.global.dataId}"),
    @NacosPropertySource(name = "private", dataId = "${nacos.private.dataId}", groupId = "${server_type}", first = true),})
public class NacosConfiguration {
    private static final Logger LOGGER = LoggerFactory.getLogger(NacosConfiguration.class);
    @Value("${nacos.timeoutMs}")
    private static int timeoutMs;
    private static ObjectReference<NacosConfiguration> ref = new ObjectReference<NacosConfiguration>();
    @NacosInjected
    private ConfigService configService;

    public static Optional<String> getConfig(String group, String dataId) {
        try {
            return Optional.ofNullable(ref.get().configService.getConfig(dataId, group, timeoutMs));
        } catch (Exception e) {
            LOGGER.error("{}", e);
        }
        return Optional.empty();
    }

    public static Optional<String> getConfig(String dataId) {
        return getConfig(Constants.DEFAULT_GROUP, dataId);
    }

    @PostConstruct
    public void initialize() {
        ref.set(this);
    }
}
