package cn.daxiang.hbtd.battleserver.core.netty.handler;

import cn.daxiang.framework.protocal.DataPacket;
import cn.daxiang.framework.router.RouterHandler;
import cn.daxiang.framework.router.annotation.Cmd;
import cn.daxiang.framework.router.type.HandlerType;
import cn.daxiang.hbtd.battleserver.core.dispatch.DispatchHelper;
import cn.daxiang.hbtd.battleserver.core.netty.channel.PlayerChannel;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;

import static cn.daxiang.shared.BattleModuleStatusCodeConstant.TOKEN_VALIDATE_ERROR;

public class GatewayHandler extends SimpleChannelInboundHandler<DataPacket> {

    private static final Logger LOGGER = LoggerFactory.getLogger(GatewayHandler.class);

    @Override
    protected void channelRead0(ChannelHandlerContext ctx, DataPacket message) throws Exception {
        if (message == null) {
            LOGGER.error("message type error, packet is droped.");
            return;
        }
        DataPacket request = (DataPacket) message;
        RouterHandler routerHandler = DispatchHelper.getRouterHandler(HandlerType.GATEWAY, request.getModule());
        if (routerHandler == null) {
            LOGGER.error("router handler not found. {}", request);
            return;
        }

        // 增加 根据cmd标注判断 帐号是否登陆，角色是否登陆.
        Cmd annotation = routerHandler.getCmd(request.getCmd());
        if (annotation == null) {
            LOGGER.error("handler cmd not found. {}", request);
            return;
        }
        if (annotation.CheckActorLogin()) {
            if (!PlayerChannel.isOnline(ctx.channel())) {
                ctx.channel().writeAndFlush(DataPacket.valueOf(request.getModule(), request.getCmd(), TOKEN_VALIDATE_ERROR));
                return;
            }
        }
        long uniqueId = PlayerChannel.getActorId(ctx.channel());
        if (LOGGER.isDebugEnabled()) {
            long startTime = System.currentTimeMillis();
            routerHandler.invoke(request.getCmd(), ctx.channel(), uniqueId, request);
            long endTime = System.currentTimeMillis();
            LOGGER.debug("[messageReceived] channel:[{}] uniqueId:[{}] module:[{}] cmd:[{}] data:[{}] request time:[{}]ms", ctx.channel().id(), uniqueId, request.getModule(),
                request.getCmd(), Arrays.toString(request.getBytes()), endTime - startTime);
        } else {
            routerHandler.invoke(request.getCmd(), ctx.channel(), uniqueId, request);
        }
    }
}
