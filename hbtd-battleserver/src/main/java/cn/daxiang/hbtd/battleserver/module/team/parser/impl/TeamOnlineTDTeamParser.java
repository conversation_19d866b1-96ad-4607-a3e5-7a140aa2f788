package cn.daxiang.hbtd.battleserver.module.team.parser.impl;

import cn.daxiang.framework.result.Result;
import cn.daxiang.hbtd.battleserver.core.dataconfig.model.TeamTdBattleConfig;
import cn.daxiang.hbtd.battleserver.core.dataconfig.service.TOTConfigService;
import cn.daxiang.hbtd.battleserver.module.team.parser.AbstractTeamParser;
import cn.daxiang.hbtd.battleserver.module.teamOnlineTD.model.BattleRoom;
import cn.daxiang.shared.module.teamTd.type.TeamTdType;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;

import static cn.daxiang.shared.GameModuleStatusCodeConstant.CONFIG_NOT_FOUND;

/**
 * <AUTHOR>
 * @date 2021/1/9
 */
@Component
public class TeamOnlineTDTeamParser extends AbstractTeamParser {
    @Override
    protected TeamTdType getType() {
        return TeamTdType.ONLINE_TD;
    }

    @Override
    public Result createBattleRoom(long teamId, int value, Collection<Map<Byte, Object>> attributes) {
        TeamTdBattleConfig totBattleConfig = TOTConfigService.getTOTBattleConfig(value);
        if (totBattleConfig == null) {
            LOGGER.error("TOTBattleConfig not found, id:{}", value);
            return Result.valueOf(CONFIG_NOT_FOUND);
        }
        BattleRoom battleRoom = BattleRoom.valueOf(getType(), value, teamId, totBattleConfig, attributes);
        return teamFacade.createBattleRoom(battleRoom);
    }
}
