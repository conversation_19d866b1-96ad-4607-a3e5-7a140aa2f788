package cn.daxiang.hbtd.battleserver.module.teamOnlineTD.parser.process.impl;

import cn.daxiang.framework.extend.KeyValue;
import cn.daxiang.hbtd.battleserver.core.dataconfig.model.TeamTdBattleConfig;
import cn.daxiang.hbtd.battleserver.core.dataconfig.model.TeamTdProduceMonsterConfig;
import cn.daxiang.hbtd.battleserver.core.dataconfig.service.GlobalConfigService;
import cn.daxiang.hbtd.battleserver.core.dataconfig.service.TOTConfigService;
import cn.daxiang.hbtd.battleserver.module.teamOnlineTD.helper.TeamOnlineTDHelper;
import cn.daxiang.hbtd.battleserver.module.teamOnlineTD.helper.TeamOnlineTDPushHelper;
import cn.daxiang.hbtd.battleserver.module.teamOnlineTD.model.BattleMember;
import cn.daxiang.hbtd.battleserver.module.teamOnlineTD.model.BattleResource;
import cn.daxiang.hbtd.battleserver.module.teamOnlineTD.model.BattleRoom;
import cn.daxiang.hbtd.battleserver.module.teamOnlineTD.parser.process.AbstractBattleProcessFlowParser;
import cn.daxiang.hbtd.battleserver.module.teamOnlineTD.type.BattleCamp;
import cn.daxiang.hbtd.battleserver.module.teamOnlineTD.type.BattleMonsterType;
import cn.daxiang.hbtd.battleserver.module.teamOnlineTD.type.BattleProcessFlow;
import cn.daxiang.hbtd.battleserver.module.teamOnlineTD.type.BattleRewardType;
import cn.daxiang.shared.GlobalConfigKey;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/1/11
 */
@Component
public class WaveBattleProcessFlowParser extends AbstractBattleProcessFlowParser {
    @Override
    protected void processFlow(BattleRoom battleRoom) {
        if (battleRoom.getTick() < battleRoom.getNextWaveTick()) {
            return;
        }
        TeamTdBattleConfig battleConfig = TOTConfigService.getTOTBattleConfig(battleRoom.getId());
        if (TOTConfigService.isLastWave(battleConfig.getMonsterGroupId(), battleRoom.getWave())) {
            return;
        }
        KeyValue<BattleRewardType, Integer> waveReward = battleConfig.getWaveReward(battleRoom.getWave());
        if (waveReward != null) {
            for (BattleMember battleMember : battleRoom.getMembers().values()) {
                BattleResource battleResource = battleMember.getResource();
                battleResource.addResource(waveReward.getKey(), waveReward.getValue());
                TeamOnlineTDPushHelper.pushBattleResource(battleRoom, battleMember.getActorId(), battleResource);
            }
        }
        int wave = battleRoom.getWave() + 1;
        Integer monsterCount = TOTConfigService.getTOTProductMonsterCount(battleConfig.getMonsterGroupId(), wave);
        if (monsterCount == null) {
            LOGGER.error("TOT WAVE MONSTER not found,id:{},wave:{}", battleRoom.getId(), wave);
            return;
        }
        KeyValue<BattleRewardType, Integer> monsterReward = battleConfig.getMonsterReward(wave);
        if (monsterReward == null) {
            LOGGER.error("TOT MONSTER REWARD not found,id:{},wave:{}", battleRoom.getId(), wave);
            return;
        }
        BattleMonsterType monsterType;
        int waveTick = battleRoom.getTick();
        int waveIntervalTick;
        if (TOTConfigService.isLastWave(battleConfig.getMonsterGroupId(), wave)) {
            waveIntervalTick = GlobalConfigService.findGlobalConfig(GlobalConfigKey.TEAMTD_LAST_WAVE_TICK).findInt();
            monsterType = BattleMonsterType.BOSS;
        } else {
            waveIntervalTick = GlobalConfigService.findGlobalConfig(GlobalConfigKey.TEAMTD_WAVE_INTERVAL_TICK).findInt();
            monsterType = BattleMonsterType.WAVE;
        }
        battleRoom.refreshWave(wave, waveTick + waveIntervalTick);
        Map<BattleCamp, Collection<TeamTdProduceMonsterConfig>> produceMonsterConfigMap = TOTConfigService.getTOTProduceMonsterConfigList(battleConfig.getMonsterGroupId(), wave);
        TeamOnlineTDHelper.productMonster(battleRoom, wave, waveTick, battleRoom.getNextWaveTick(), produceMonsterConfigMap, monsterType, monsterCount, monsterReward.getKey(),
            monsterReward.getValue());
    }

    @Override
    public BattleProcessFlow getProcessFlow() {
        return BattleProcessFlow.WAVE;
    }
}
