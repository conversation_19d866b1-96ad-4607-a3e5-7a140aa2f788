package cn.daxiang.hbtd.battleserver.core.dataconfig.convert;

import java.util.Map.Entry;

public class ConfigPairKey<K, V> implements Entry<K, V>, IConfigKeyable<String> {
    private K key;
    private V value;

    public ConfigPairKey(K k, V v) {
        this.key = k;
        this.value = v;
    }

    @Override
    public K getKey() {
        return key;
    }

    @Override
    public V getValue() {
        return value;
    }

    @Override
    public V setValue(V v) {
        V oldValue = value;
        value = v;
        return oldValue;
    }

    @Override
    public final String toString() {
        return key + "=" + value;
    }

    @Override
    public String getCacheKey() {
        return toString();
    }

}
