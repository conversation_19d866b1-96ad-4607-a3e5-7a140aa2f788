package cn.daxiang.hbtd.battleserver.module.teamOnlineTD.parser.buffVerify.impl;

import cn.daxiang.hbtd.battleserver.module.teamOnlineTD.model.BattleHero;
import cn.daxiang.hbtd.battleserver.module.teamOnlineTD.model.BattleMember;
import cn.daxiang.hbtd.battleserver.module.teamOnlineTD.model.BattleRoom;
import cn.daxiang.hbtd.battleserver.module.teamOnlineTD.parser.buffVerify.AbstractBuffVerifyParser;
import cn.daxiang.hbtd.battleserver.module.teamOnlineTD.type.BattleCamp;
import cn.daxiang.hbtd.battleserver.module.teamOnlineTD.type.BuffVerifyType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2021/6/1
 */
@Component
public class CampBuffVerifyParser extends AbstractBuffVerifyParser {
    @Override
    protected BuffVerifyType getType() {
        return BuffVerifyType.CAMP;
    }

    @Override
    public boolean verify(<PERSON>Room battleRoom, BattleHero battleHero, BattleCamp battleCamp, int heroId) {
        BattleMember battleMember = battleRoom.getBattleMember(battleHero.getActorId());
        return battleMember.getDamages().containsKey(heroId);
    }
}
