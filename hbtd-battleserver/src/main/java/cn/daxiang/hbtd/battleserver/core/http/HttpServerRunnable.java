package cn.daxiang.hbtd.battleserver.core.http;

import cn.daxiang.framework.utils.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import io.netty.channel.Channel;
import io.netty.handler.codec.http.FullHttpRequest;
import io.netty.handler.codec.http.HttpHeaderNames;
import io.netty.handler.codec.http.HttpMethod;
import io.netty.handler.codec.http.HttpResponseStatus;
import io.netty.handler.codec.http.QueryStringDecoder;
import org.apache.commons.codec.CharEncoding;
import org.apache.commons.codec.Charsets;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Method;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

public class HttpServerRunnable implements Runnable {

    private static final String FAVICON_ICO = "favicon.ico";

    private static final Logger LOGGER = LoggerFactory.getLogger(HttpServerRunnable.class);

    private Channel channel;

    private FullHttpRequest request;

    private HttpResult httpResult;

    private JSONObject parameters;

    public HttpServerRunnable(Channel channel, FullHttpRequest request) {
        this.channel = channel;
        this.request = request;
        this.httpResult = HttpResult.valueOf(request.protocolVersion(), HttpResponseStatus.OK);
        this.initParameters();
    }

    @Override
    public void run() {
        try {
            if (request.uri().contains(FAVICON_ICO)) {
                httpResult.setStatus(HttpResponseStatus.NOT_FOUND);
                return;
            }
            if (this.parameters == null) {
                httpResult.setStatus(HttpResponseStatus.BAD_REQUEST);
                return;
            }
            String[] paths = request.uri().split("/");
            if (paths == null || paths.length < 1) {
                executeAction(ControllerContext.DEFAULT_CONTROLLER, ControllerContext.DEFAULT_ACTION);
                return;
            }
            String controllerName = paths[1];
            if (StringUtils.isBlank(controllerName)) {
                controllerName = ControllerContext.DEFAULT_CONTROLLER;
            }
            String actionName = ControllerContext.DEFAULT_ACTION;
            if (paths.length > 2) {
                actionName = paths[2];
            }
            executeAction(controllerName, actionName);
        } catch (Exception e) {
            LOGGER.error("{}", e);
        } finally {
            channel.writeAndFlush(httpResult);
        }
    }

    private void executeAction(String controllerName, String actionName) {
        Class<Controller> controller = ControllerContext.getController(controllerName);
        if (controller == null) {
            httpResult.setStatus(HttpResponseStatus.NOT_FOUND);
            return;
        }
        Method method = ControllerContext.getAction(controllerName, actionName);
        if (method == null) {
            httpResult.setStatus(HttpResponseStatus.NOT_FOUND);
            return;
        }
        try {
            Controller instance = controller.newInstance();
            instance.init(channel, request, parameters, httpResult);
            method.invoke(instance);
        } catch (Exception ex) {
            LOGGER.warn("{}", ex);
        }
    }

    public void initParameters() {
        HttpMethod method = request.method();
        if (method.equals(HttpMethod.GET)) {
            parameters = new JSONObject();
            QueryStringDecoder queryDecoder = new QueryStringDecoder(request.uri(), Charsets.toCharset(CharEncoding.UTF_8));
            Map<String, List<String>> uriAttributes = queryDecoder.parameters();
            for (Entry<String, List<String>> entry : uriAttributes.entrySet()) {
                for (String attrValue : entry.getValue()) {
                    parameters.put(entry.getKey(), attrValue);
                }
            }
        } else if (method.equals(HttpMethod.POST)) {
            if (!request.headers().contains(HttpHeaderNames.CONTENT_TYPE)) {
                return;
            }
            String contentType = request.headers().get(HttpHeaderNames.CONTENT_TYPE).split(";")[0];
            if (contentType.equals("application/json")) {
                String jsonStr = request.content().toString(Charsets.toCharset(CharEncoding.UTF_8));
                parameters = JSON.parseObject(jsonStr);
                return;
            } else if (contentType.equals("application/x-www-form-urlencoded")) {
                parameters = new JSONObject();
                String jsonStr = request.content().toString(Charsets.toCharset(CharEncoding.UTF_8));
                QueryStringDecoder queryDecoder = new QueryStringDecoder(jsonStr, false);
                Map<String, List<String>> uriAttributes = queryDecoder.parameters();
                for (Entry<String, List<String>> entry : uriAttributes.entrySet()) {
                    for (String attrValue : entry.getValue()) {
                        parameters.put(entry.getKey(), attrValue);
                    }
                }
            }
        }
    }
}
