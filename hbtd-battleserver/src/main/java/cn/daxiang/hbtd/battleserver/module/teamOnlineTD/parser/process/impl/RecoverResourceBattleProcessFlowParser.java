package cn.daxiang.hbtd.battleserver.module.teamOnlineTD.parser.process.impl;

import cn.daxiang.hbtd.battleserver.core.dataconfig.service.GlobalConfigService;
import cn.daxiang.hbtd.battleserver.module.teamOnlineTD.helper.TeamOnlineTDPushHelper;
import cn.daxiang.hbtd.battleserver.module.teamOnlineTD.model.BattleMember;
import cn.daxiang.hbtd.battleserver.module.teamOnlineTD.model.BattleRoom;
import cn.daxiang.hbtd.battleserver.module.teamOnlineTD.parser.process.AbstractBattleProcessFlowParser;
import cn.daxiang.hbtd.battleserver.module.teamOnlineTD.type.BattleProcessFlow;
import cn.daxiang.shared.GlobalConfigKey;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2021/1/11
 */
@Component
public class RecoverResourceBattleProcessFlowParser extends AbstractBattleProcessFlowParser {

    @Override
    protected void processFlow(BattleRoom battleRoom) {
        for (BattleMember member : battleRoom.getMembers().values()) {
            if (battleRoom.getTick() < member.getNextDemonBreatheTick()) {
                continue;
            }
            int recoverTick = GlobalConfigService.findGlobalConfig(GlobalConfigKey.TEAMTD_DEMON_BREATHE_RECOVER_TICK).findInt();
            member.recoverDemonBreathe(battleRoom.getTick() + recoverTick);
            TeamOnlineTDPushHelper.pushBattleResource(battleRoom, member.getActorId(), member.getResource());
        }
    }

    @Override
    public BattleProcessFlow getProcessFlow() {
        return BattleProcessFlow.RECOVER_RESOURCE;
    }
}
