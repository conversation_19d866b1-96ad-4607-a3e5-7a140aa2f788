package cn.daxiang.hbtd.battleserver.module.teamOnlineTD.model;

import com.alibaba.fastjson.annotation.JSONField;

import java.util.List;

/**
 * 战场商店信息
 *
 * <AUTHOR>
 * @date 2021/1/7
 */
public class BattleStore {
    /**
     * 英雄ID列表
     */
    private List<Integer> heroes;
    /**
     * 正在分享英雄Index(-1表示无,索引从0开始)
     */
    private int sharing;
    /**
     * 被分享英雄配置ID(0表示无)
     */
    private int shared;

    public static BattleStore valueOf(List<Integer> heroIds) {
        BattleStore store = new BattleStore();
        store.heroes = heroIds;
        store.sharing = -1;
        return store;
    }

    public List<Integer> getHeroes() {
        return heroes;
    }

    public void setHeroes(List<Integer> heroes) {
        this.heroes = heroes;
    }

    public int getSharing() {
        return sharing;
    }

    public void setSharing(int sharing) {
        this.sharing = sharing;
    }

    public void refresh(List<Integer> heroes) {
        this.heroes = heroes;
        this.sharing = -1;
    }

    public int getHeroIdByIndex(int index) {
        if (index < 0) {
            return this.shared;
        }
        return this.heroes.get(index);
    }

    @JSONField(serialize = false)
    public int getSharingHeroId() {
        if (this.heroes.isEmpty() || this.sharing < 0) {
            return 0;
        }
        return this.heroes.get(this.sharing);
    }

    public int getShared() {
        return shared;
    }

    public void setShared(int shared) {
        this.shared = shared;
    }

}
