package cn.daxiang.hbtd.battleserver.core.dataconfig.convert;

import cn.daxiang.hbtd.battleserver.core.dataconfig.convert.callback.ConvertStringToMapCallback;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

public abstract class LinkedHashMapConfig<K extends Comparable<K>, V> implements IConfigable {

    protected LinkedHashMap<K, V> cache = Maps.newLinkedHashMap();

    protected List<K> keyList = Lists.newArrayList();

    @Override
    public void buildObject(final String config) {
        Map<K, V> map = StringConvert.convertToObjectLinkedMap(new ConvertStringToMapCallback<K, V>() {

            @Override
            public String getTokenizerString() {
                return config;
            }

            @Override
            public int getSize() {
                return fromSize();
            }

            @Override
            public void fromArray(Map<K, V> map, String[] array) {
                map.put(fromKey(array), fromValue(array));
            }
        });
        this.cache.putAll(map);
        this.keyList.addAll(map.keySet());
    }

    protected int fromSize() {
        return 2;
    }

    public LinkedHashMap<K, V> getCache() {
        return this.cache;
    }

    public List<K> getKeyList() {
        return this.keyList;
    }

    protected abstract K fromKey(String[] array);

    protected abstract V fromValue(String[] array);
}
