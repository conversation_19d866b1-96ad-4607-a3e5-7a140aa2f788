package cn.daxiang.hbtd.verifyserver.module.pve.parser.impl;

import cn.daxiang.framework.utils.FormulaUtils;
import cn.daxiang.hbtd.verifyserver.core.dataconfig.model.BattlefieldSkillEffectConfig;
import cn.daxiang.hbtd.verifyserver.core.dataconfig.model.BeastConfig;
import cn.daxiang.hbtd.verifyserver.core.dataconfig.model.HeroConfig;
import cn.daxiang.hbtd.verifyserver.core.dataconfig.model.PveSkillAttackEffectConfig;
import cn.daxiang.hbtd.verifyserver.core.dataconfig.service.GlobalConfigService;
import cn.daxiang.hbtd.verifyserver.core.dataconfig.service.PVEBattleConfigService;
import cn.daxiang.hbtd.verifyserver.module.pve.parser.AbstractPVEVerifyParser;
import cn.daxiang.protocol.game.BattlefielddrillProtocol;
import cn.daxiang.shared.GlobalConfigKey;
import cn.daxiang.shared.type.PVEVerifyParameterKey;
import cn.daxiang.shared.type.PVEVerifyType;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.protobuf.InvalidProtocolBufferException;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2022/4/27
 */
@Component
public class BattlefieldDrillBattleVerifyParser extends AbstractPVEVerifyParser {

    @Override
    protected PVEVerifyType getType() {
        return PVEVerifyType.BATTLEFIELDDRILL;
    }

    @Override
    public String getBattleRequest(byte[] data) throws InvalidProtocolBufferException {
        BattlefielddrillProtocol.BattlefieldDrillChallengeRequest request = BattlefielddrillProtocol.BattlefieldDrillChallengeRequest.parseFrom(data);
        return request.toString();
    }

    @Override
    public boolean verify(int serverType, int serverId, long actorId, Map<PVEVerifyParameterKey, Object> parameter) throws InvalidProtocolBufferException {
        BattlefielddrillProtocol.BattlefieldDrillChallengeRequest request =
            BattlefielddrillProtocol.BattlefieldDrillChallengeRequest.parseFrom((byte[]) parameter.get(PVEVerifyParameterKey.BATTLE_VERIFY_REQUEST));

        List<Integer> tokenSkillList = (List) parameter.get(PVEVerifyParameterKey.TOKEN_SKILL_ID_LIST);

        JSONArray data = JSON.parseArray(request.getCheck());
        int waveId = (int) parameter.get(PVEVerifyParameterKey.WAVE_ID);
        long time = (long) parameter.get(PVEVerifyParameterKey.TIME);
        Map<Integer, Long> serverPowerMap = (Map<Integer, Long>) parameter.get(PVEVerifyParameterKey.POWER);
        Map<Integer, Long> serverBeastPowerMap = (Map<Integer, Long>) parameter.get(PVEVerifyParameterKey.BEAST_POWER);
        //[{上阵英雄ID:战力,上阵英雄ID:战力},[[英雄ID,技能ID,伤害,是否暴击(0:false.1:true)],[英雄ID,技能ID,伤害,是否暴击(0:false.1:true)]]]
        Map<Integer, Long> clientPowerMap = data.getJSONObject(0).toJavaObject(TYPE_REFERENCE);
        Collection<JSONArray> damageList = data.getJSONArray(1).toJavaList(JSONArray.class);
        if (this.verifyPower(serverType, serverId, actorId, serverPowerMap, serverBeastPowerMap, clientPowerMap)) {
            LOGGER.error("PVE Verify Power error. serverType:{},serverId:{},actorId:{},serverPowerMap:{},serverBeastPowerMap:{},clientPowerMap:{}", serverType, serverId, actorId,
                serverPowerMap, serverBeastPowerMap, clientPowerMap);
            return true;
        }
        if (this.verifyDamage(serverType, serverId, actorId, damageList, serverPowerMap, serverBeastPowerMap, tokenSkillList)) {
            LOGGER.error("PVE Verify Damage error. serverType:{},serverId:{},actorId:{},damageList:{},serverPowerMap:{},serverBeastPowerMap:{},tokenSkillList:{}", serverType,
                serverId, actorId, damageList, serverPowerMap, serverBeastPowerMap, tokenSkillList);
            return true;
        }
        Optional<Long> timeOptional = PVEBattleConfigService.getTime(waveId);
        if (!timeOptional.isPresent()) {
            LOGGER.error("PVE Verify Time error. serverType:{},serverId:{},actorId:{},waveId:{}", serverType, serverId, actorId, waveId);
            return false;
        }
        if (this.verifyTime(timeOptional.get(), time)) {
            LOGGER.error("PVE Verify Time. serverType:{},serverId:{},actorId:{},waveId:{},serverTime:{},ClientTime:{}", serverType, serverId, actorId, waveId, timeOptional.get(),
                time);
            return true;
        }
        return false;

    }

    private boolean verifyDamage(int serverType, int serverId, long actorId, Collection<JSONArray> damageList, Map<Integer, Long> heroPowerMap, Map<Integer, Long> beastPowerMap,
        List<Integer> tokenSkillList) {
        String damageExpr = GlobalConfigService.get(GlobalConfigKey.BATTLEFIELD_CHECK_DAMAGE_EXPR);
        int cheatTimes = 0;
        //[英雄ID,技能ID,伤害,是否暴击(0:false.1:true)]
        for (JSONArray data : damageList) {
            Collection<Object> parameters = this.getVerifyDamageParameter(serverType, serverId, actorId, data, heroPowerMap, beastPowerMap, tokenSkillList);
            if (parameters.isEmpty()) {
                LOGGER.error("PVE Verify Damage error, parameters is empty. serverType:{},serverId:{},actorId:{},data:{},heroPower:{},beastPower:{},tokenSkillList:{}", serverType,
                    serverId, actorId, damageList, heroPowerMap, beastPowerMap, tokenSkillList);
                return false;
            }
            if (FormulaUtils.executeBool(damageExpr, parameters.toArray())) {
                LOGGER.error("PVE Verify Damage. serverType:{},serverId:{},actorId:{},expr:{},parameters:{}", serverType, serverId, actorId, damageExpr, parameters);
                cheatTimes++;
            }
        }
        String timesExpr = GlobalConfigService.get(GlobalConfigKey.PVE_CHECK_DAMAGE_TIMES_EXPR);
        return FormulaUtils.executeBool(timesExpr, cheatTimes, damageList.size());
    }

    private Collection<Object> getVerifyDamageParameter(int serverType, int serverId, long actorId, JSONArray data, Map<Integer, Long> heroPowerMap,
        Map<Integer, Long> beastPowerMap, List<Integer> tokenSkillIdList) {

        long power = 0L;
        int towerPower = 0;
        int towerBonusPercent = 0;
        int towerPveBonus = 0;
        int towerBonusValue = 0;
        int heroId = data.getIntValue(0);

        boolean isExist = false;

        Optional<HeroConfig> heroConfigOptional = PVEBattleConfigService.getHeroConfig(heroId);
        if (heroConfigOptional.isPresent()) {
            HeroConfig heroConfig = heroConfigOptional.get();
            towerPower = heroConfig.getTowerPower();
            towerBonusPercent = heroConfig.getTowerBonusPercent();
            towerPveBonus = heroConfig.getTowerPveBonus();
            towerBonusValue = heroConfig.getTowerBonusValue();
            power = heroPowerMap.getOrDefault(heroId, 0L);
            // 共鸣取最低英雄战力
            if (!heroPowerMap.isEmpty() && power == 0) {
                power = Collections.min(heroPowerMap.values());
            }
            isExist = true;
        }

        Optional<BeastConfig> beastConfigOptional = PVEBattleConfigService.getBeastConfig(heroId);
        if (beastConfigOptional.isPresent()) {
            BeastConfig beastConfig = beastConfigOptional.get();
            towerPower = beastConfig.getTowerPower();
            towerBonusPercent = beastConfig.getTowerBonusPercent();
            towerPveBonus = beastConfig.getTowerPveBonus();
            towerBonusValue = beastConfig.getTowerBonusValue();
            power = beastPowerMap.getOrDefault(heroId, 0L);
            // 共鸣取最低神兽战力
            if (!beastPowerMap.isEmpty() && power == 0) {
                power = Collections.min(beastPowerMap.values());
            }
            isExist = true;
        }

        if (!isExist) {
            LOGGER.error("HeroConfig or BeastConfig not found, serverType:{},serverId:{},actorId:{},heroId:{}", serverType, serverId, actorId, heroId);
            return Collections.emptyList();
        }

        int skillId = data.getIntValue(1);
        Optional<PveSkillAttackEffectConfig> attackEffectConfigOptional = PVEBattleConfigService.getPveSkillAttackEffectConfig(skillId);
        if (!attackEffectConfigOptional.isPresent()) {
            LOGGER.error("PveSkillAttackEffectConfig not found, serverType:{},serverId:{},actorId:{},skillId:{}", serverType, serverId, actorId, skillId);
            return Collections.emptyList();
        }
        long damage = data.getLongValue(2);
        int isCrit = data.getIntValue(3);

        PveSkillAttackEffectConfig attackEffectConfig = attackEffectConfigOptional.get();
        Collection<Object> parameters = Lists.newArrayList();

        Map<Integer, Integer> tokenSkillMap = Maps.newHashMap();
        for (Integer tokenSkillId : tokenSkillIdList) {
            BattlefieldSkillEffectConfig config = GlobalConfigService.findConfig(tokenSkillId, BattlefieldSkillEffectConfig.class);
            if (config == null || config.getParamList().isEmpty()) {
                continue;
            }
            tokenSkillMap.merge(config.getType(), config.getParamList().get(0), Integer::sum);
        }

        parameters.add(power);
        parameters.add(towerPower);
        parameters.add(attackEffectConfig.getPhysicAtkScale());
        parameters.add(attackEffectConfig.getMagicAtkScale());
        parameters.add(attackEffectConfig.getPhysicAtkAdd());
        parameters.add(attackEffectConfig.getMagicAtkAdd());
        parameters.add(attackEffectConfig.getPhysicDmgAdd());
        parameters.add(attackEffectConfig.getMagicDmgAdd());
        parameters.add(towerBonusPercent);
        parameters.add(towerPveBonus);
        parameters.add(towerBonusValue);
        parameters.add(isCrit);
        parameters.add(damage);
        parameters.add(tokenSkillMap.getOrDefault(3, 0));
        parameters.add(tokenSkillMap.getOrDefault(8, 0));
        parameters.add(tokenSkillMap.getOrDefault(9, 0));
        parameters.add(tokenSkillMap.getOrDefault(10, 0));
        return parameters;
    }
}
