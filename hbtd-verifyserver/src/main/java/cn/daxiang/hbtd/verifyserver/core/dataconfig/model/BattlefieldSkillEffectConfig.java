package cn.daxiang.hbtd.verifyserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.dataconfig.annotation.FieldIgnore;
import cn.daxiang.framework.identity.IdentiyKey;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * 无尽试炼技能效果表
 *
 * <AUTHOR>
 * @date 2022/11/11
 */
@DataFile(fileName = "battlefield_skill_effect_config")
public class BattlefieldSkillEffectConfig implements ModelAdapter {
    /**
     * 技能ID
     */
    private int skillId;
    /**
     * 目标
     */
    private int target;
    /**
     * 类型
     * 1-移速百分比
     * 2-射程百分比
     * 3-攻击力百分比
     * 4-击杀额外获得能量百分比
     * 5-眩晕
     * 6-剑雨
     * 7-击杀
     * 8-最终伤害百分比
     * 9-物理伤害加成
     * 10-魔法伤害加成
     * 11-释放觉醒技概率回能量
     */
    private int type;
    /**
     * 概率
     * 现在针对type=12的使用
     */
    private String param;

    @FieldIgnore
    private List<Integer> paramList = Lists.newArrayList();

    @Override
    public void initialize() {
        JSONArray array = JSONArray.parseArray(param);
        for (Object value : array) {
            this.paramList.add(Integer.valueOf(value.toString()));
        }
    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(skillId);
    }

    public int getSkillId() {
        return skillId;
    }

    public int getTarget() {
        return target;
    }

    public int getType() {
        return type;
    }

    public List<Integer> getParamList() {
        return paramList;
    }
}
