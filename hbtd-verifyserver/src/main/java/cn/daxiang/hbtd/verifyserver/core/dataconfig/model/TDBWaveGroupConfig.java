package cn.daxiang.hbtd.verifyserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.identity.IdentiyKey;

/**
 * PVE波次表
 *
 * <AUTHOR>
 * @date 2022/4/27
 */
@DataFile(fileName = "TDBWaveGroupConfig")
public class TDBWaveGroupConfig implements ModelAdapter {
    /**
     * 分组ID
     */
    private int groupId;
    /**
     * 波次ID
     */
    private int waveId;
    /**
     * 怪物ID
     */
    private int monsterId;
    /**
     * 延时（ms）
     */
    private int delay;
    /**
     * 间隔（ms）
     */
    private int interval;
    /**
     * 数量
     */
    private int number;

    @Override
    public void initialize() {

    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(waveId, groupId);
    }

    public int getGroupId() {
        return groupId;
    }

    public int getWaveId() {
        return waveId;
    }

    public int getMonsterId() {
        return monsterId;
    }

    public int getDelay() {
        return delay;
    }

    public int getInterval() {
        return interval;
    }

    public int getNumber() {
        return number;
    }
}
