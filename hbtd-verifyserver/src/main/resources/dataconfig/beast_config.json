[{"beastAttributes": [[61, 1500], [62, 5000], [59, 500], [63, 0], [60, 600000]], "towerPower": 1000, "towerCrit": 1000, "manualAttributes": [[9, 50], [10, 50], [3, 10000], [4, 10000]], "initNumber": 80, "towerPower2": 0, "type": 3, "towerBonusPercent": 0, "quality": 4, "towerBonusValue": 0, "fragmentId": 381401, "scoreExpr": "Math.floor(((4*x2*x2+342.8*x3)+(4.1952*x3*x3*x3+55.349*x3*x3+16509.4*x3+4820.7))*4*10/400+(x1/400))", "id": 90401, "fragmentCount": 80, "towerPveBonus": 0, "cirtHurtMulti": 5000}, {"beastAttributes": [[61, 1500], [62, 5000], [59, 600], [63, 0], [60, 800000]], "towerPower": 1000, "towerCrit": 1000, "manualAttributes": [[9, 50], [10, 50], [3, 10000], [4, 10000]], "initNumber": 80, "towerPower2": 0, "type": 1, "towerBonusPercent": 0, "quality": 4, "towerBonusValue": 0, "fragmentId": 381402, "scoreExpr": "Math.floor(((4*x2*x2+342.8*x3)+(4.1952*x3*x3*x3+55.349*x3*x3+16509.4*x3+4820.7))*4*10/400+(x1/400))", "id": 90402, "fragmentCount": 80, "towerPveBonus": 0, "cirtHurtMulti": 5000}, {"beastAttributes": [[61, 1500], [62, 5000], [59, 600], [63, 0], [60, 800000]], "towerPower": 1000, "towerCrit": 1000, "manualAttributes": [[9, 50], [10, 50], [3, 10000], [4, 10000]], "initNumber": 80, "towerPower2": 0, "type": 4, "towerBonusPercent": 0, "quality": 4, "towerBonusValue": 0, "fragmentId": 381403, "scoreExpr": "Math.floor(((4*x2*x2+342.8*x3)+(4.1952*x3*x3*x3+55.349*x3*x3+16509.4*x3+4820.7))*4*10/400+(x1/400))", "id": 90403, "fragmentCount": 80, "towerPveBonus": 0, "cirtHurtMulti": 5000}, {"beastAttributes": [[61, 1500], [62, 5000], [59, 600], [63, 0], [60, 800000]], "towerPower": 1000, "towerCrit": 1000, "manualAttributes": [[9, 50], [10, 50], [3, 10000], [4, 10000]], "initNumber": 80, "towerPower2": 0, "type": 2, "towerBonusPercent": 0, "quality": 4, "towerBonusValue": 0, "fragmentId": 381404, "scoreExpr": "Math.floor(((4*x2*x2+342.8*x3)+(4.1952*x3*x3*x3+55.349*x3*x3+16509.4*x3+4820.7))*4*10/400+(x1/400))", "id": 90404, "fragmentCount": 80, "towerPveBonus": 0, "cirtHurtMulti": 5000}, {"beastAttributes": [[61, 2000], [62, 5000], [59, 700], [63, 0], [60, 1000000]], "towerPower": 1000, "towerCrit": 1000, "manualAttributes": [[9, 100], [10, 100], [3, 12500], [4, 12500]], "initNumber": 120, "towerPower2": 0, "type": 3, "towerBonusPercent": 0, "quality": 5, "towerBonusValue": 0, "fragmentId": 381501, "scoreExpr": "Math.floor((1.2*(4*x2*x2+342.8*x3)+3*(4.1952*x3*x3*x3+55.349*x3*x3+16509.4*x3+4820.7)*4*10/400)+(x1/400))", "id": 90501, "fragmentCount": 120, "towerPveBonus": 0, "cirtHurtMulti": 5000}, {"beastAttributes": [[61, 2000], [62, 5000], [59, 700], [63, 0], [60, 1000000]], "towerPower": 1000, "towerCrit": 1000, "manualAttributes": [[9, 100], [10, 100], [3, 12500], [4, 12500]], "initNumber": 120, "towerPower2": 0, "type": 1, "towerBonusPercent": 0, "quality": 5, "towerBonusValue": 0, "fragmentId": 381502, "scoreExpr": "Math.floor((1.2*(4*x2*x2+342.8*x3)+3*(4.1952*x3*x3*x3+55.349*x3*x3+16509.4*x3+4820.7)*4*10/400)+(x1/400))", "id": 90502, "fragmentCount": 120, "towerPveBonus": 0, "cirtHurtMulti": 5000}, {"beastAttributes": [[61, 2000], [62, 5000], [59, 700], [63, 0], [60, 1000000]], "towerPower": 1000, "towerCrit": 1000, "manualAttributes": [[9, 100], [10, 100], [3, 12500], [4, 12500]], "initNumber": 120, "towerPower2": 0, "type": 4, "towerBonusPercent": 0, "quality": 5, "towerBonusValue": 0, "fragmentId": 381503, "scoreExpr": "Math.floor((1.2*(4*x2*x2+342.8*x3)+3*(4.1952*x3*x3*x3+55.349*x3*x3+16509.4*x3+4820.7)*4*10/400)+(x1/400))", "id": 90503, "fragmentCount": 120, "towerPveBonus": 0, "cirtHurtMulti": 5000}, {"beastAttributes": [[61, 2000], [62, 5000], [59, 700], [63, 0], [60, 1000000]], "towerPower": 1000, "towerCrit": 1000, "manualAttributes": [[9, 100], [10, 100], [3, 12500], [4, 12500]], "initNumber": 120, "towerPower2": 0, "type": 2, "towerBonusPercent": 0, "quality": 5, "towerBonusValue": 0, "fragmentId": 381504, "scoreExpr": "Math.floor((1.2*(4*x2*x2+342.8*x3)+3*(4.1952*x3*x3*x3+55.349*x3*x3+16509.4*x3+4820.7)*4*10/400)+(x1/400))", "id": 90504, "fragmentCount": 120, "towerPveBonus": 0, "cirtHurtMulti": 5000}]