[{"attributeModulus": [35000, 60000, 10000, 10000], "formationId": 1001, "playerFormation": [1, 2, 3, 4, 5, 6], "formation": [[2, 100]], "type": 3}, {"attributeModulus": [35000, 60000, 10000, 10000], "formationId": 1002, "playerFormation": [1, 2, 3, 4, 5, 6], "formation": [[2, 101]], "type": 3}, {"attributeModulus": [35000, 60000, 10000, 10000], "formationId": 1003, "playerFormation": [1, 2, 3, 4, 5, 6], "formation": [[2, 102]], "type": 3}, {"attributeModulus": [35000, 60000, 10000, 10000], "formationId": 1004, "playerFormation": [1, 2, 3, 4, 5, 6], "formation": [[2, 103]], "type": 3}, {"attributeModulus": [35000, 60000, 10000, 10000], "formationId": 1005, "playerFormation": [1, 2, 3, 4, 5, 6], "formation": [[2, 107]], "type": 3}, {"attributeModulus": [35000, 60000, 10000, 10000], "formationId": 1006, "playerFormation": [1, 2, 3, 4, 5, 6], "formation": [[2, 108]], "type": 3}, {"attributeModulus": [9500, 10000, 10000, 10000], "formationId": 3001, "playerFormation": [1, 2, 3], "formation": [[1, 107], [2, 107], [3, 107]], "type": 1}, {"attributeModulus": [9500, 10000, 10000, 10000], "formationId": 3002, "playerFormation": [1, 2, 3], "formation": [[1, 108], [2, 108], [3, 108]], "type": 1}, {"attributeModulus": [9500, 10000, 10000, 10000], "formationId": 3003, "playerFormation": [1, 2, 3], "formation": [[1, 107], [2, 107], [3, 107]], "type": 2}, {"attributeModulus": [9500, 10000, 10000, 10000], "formationId": 3004, "playerFormation": [1, 2, 3], "formation": [[1, 107], [2, 108], [3, 107]], "type": 2}, {"attributeModulus": [9500, 10000, 10000, 10000], "formationId": 3005, "playerFormation": [1, 2, 3], "formation": [[1, 108], [2, 108], [3, 108]], "type": 2}, {"attributeModulus": [15000, 20000, 10000, 10000], "formationId": 3006, "playerFormation": [1, 2, 3, 4, 5, 6], "formation": [[1, 107], [2, 107], [3, 107]], "type": 1}, {"attributeModulus": [15000, 20000, 10000, 10000], "formationId": 3007, "playerFormation": [1, 2, 3, 4, 5, 6], "formation": [[1, 108], [2, 108], [3, 108]], "type": 1}, {"attributeModulus": [15000, 20000, 10000, 10000], "formationId": 3008, "playerFormation": [1, 2, 3, 4, 5, 6], "formation": [[1, 107], [2, 107], [3, 107]], "type": 2}, {"attributeModulus": [15000, 20000, 10000, 10000], "formationId": 3009, "playerFormation": [1, 2, 3, 4, 5, 6], "formation": [[1, 107], [2, 108], [3, 107]], "type": 2}, {"attributeModulus": [15000, 20000, 10000, 10000], "formationId": 3100, "playerFormation": [1, 2, 3, 4, 5, 6], "formation": [[1, 108], [2, 108], [3, 108]], "type": 2}, {"attributeModulus": [10000, 10000, 10000, 10000], "formationId": 4001, "playerFormation": [1, 2, 3, 5], "formation": [[1, 107], [2, 107], [3, 107], [5, 107]], "type": 1}, {"attributeModulus": [10000, 10000, 10000, 10000], "formationId": 4002, "playerFormation": [1, 2, 3, 5], "formation": [[1, 108], [2, 108], [3, 108], [5, 108]], "type": 1}, {"attributeModulus": [10000, 10000, 10000, 10000], "formationId": 4003, "playerFormation": [1, 2, 3, 5], "formation": [[1, 107], [2, 107], [3, 107], [5, 107]], "type": 2}, {"attributeModulus": [10000, 10000, 10000, 10000], "formationId": 4004, "playerFormation": [1, 2, 3, 5], "formation": [[1, 107], [2, 107], [3, 107], [5, 108]], "type": 2}, {"attributeModulus": [10000, 10000, 10000, 10000], "formationId": 4005, "playerFormation": [1, 2, 3, 5], "formation": [[1, 107], [2, 108], [3, 107], [5, 108]], "type": 2}, {"attributeModulus": [10000, 10000, 10000, 10000], "formationId": 4006, "playerFormation": [1, 2, 3, 5], "formation": [[1, 108], [2, 108], [3, 108], [5, 108]], "type": 2}, {"attributeModulus": [10000, 10000, 10000, 10000], "formationId": 5001, "playerFormation": [1, 2, 3, 4, 6], "formation": [[1, 107], [2, 107], [3, 107], [4, 107], [6, 107]], "type": 1}, {"attributeModulus": [10000, 10000, 10000, 10000], "formationId": 5002, "playerFormation": [1, 2, 3, 4, 6], "formation": [[1, 108], [2, 108], [3, 108], [4, 108], [6, 108]], "type": 1}, {"attributeModulus": [10000, 10000, 10000, 10000], "formationId": 5003, "playerFormation": [1, 2, 3, 4, 6], "formation": [[1, 107], [2, 107], [3, 107], [4, 107], [6, 107]], "type": 2}, {"attributeModulus": [10000, 10000, 10000, 10000], "formationId": 5004, "playerFormation": [1, 2, 3, 4, 6], "formation": [[1, 107], [2, 108], [3, 107], [4, 107], [6, 107]], "type": 2}, {"attributeModulus": [10000, 10000, 10000, 10000], "formationId": 5005, "playerFormation": [1, 2, 3, 4, 6], "formation": [[1, 108], [2, 108], [3, 108], [4, 107], [6, 107]], "type": 2}, {"attributeModulus": [10000, 10000, 10000, 10000], "formationId": 6001, "playerFormation": [1, 2, 3, 4, 5, 6], "formation": [[1, 107], [2, 107], [3, 107], [4, 107], [5, 107], [6, 107]], "type": 1}, {"attributeModulus": [10000, 10000, 10000, 10000], "formationId": 6002, "playerFormation": [1, 2, 3, 4, 5, 6], "formation": [[1, 108], [2, 108], [3, 108], [4, 108], [5, 108], [6, 108]], "type": 1}, {"attributeModulus": [10000, 10000, 10000, 10000], "formationId": 6003, "playerFormation": [1, 2, 3, 4, 5, 6], "formation": [[1, 107], [2, 107], [3, 107], [4, 107], [5, 107], [6, 107]], "type": 2}, {"attributeModulus": [10000, 10000, 10000, 10000], "formationId": 6004, "playerFormation": [1, 2, 3, 4, 5, 6], "formation": [[1, 107], [2, 108], [3, 107], [4, 107], [5, 107], [6, 107]], "type": 2}, {"attributeModulus": [10000, 10000, 10000, 10000], "formationId": 6005, "playerFormation": [1, 2, 3, 4, 5, 6], "formation": [[1, 108], [2, 108], [3, 108], [4, 107], [5, 107], [6, 107]], "type": 2}, {"attributeModulus": [10000, 10000, 10000, 10000], "formationId": 6006, "playerFormation": [1, 2, 3, 4, 5, 6], "formation": [[1, 108], [2, 108], [3, 108], [4, 108], [5, 108], [6, 108]], "type": 2}, {"attributeModulus": [35000, 60000, 10000, 10000], "formationId": 11001, "playerFormation": [1, 2, 3, 4, 5, 6], "formation": [[2, 111]], "type": 3}, {"attributeModulus": [35000, 60000, 10000, 10000], "formationId": 11002, "playerFormation": [1, 2, 3, 4, 5, 6], "formation": [[2, 112]], "type": 3}, {"attributeModulus": [35000, 60000, 10000, 10000], "formationId": 11003, "playerFormation": [1, 2, 3, 4, 5, 6], "formation": [[2, 110]], "type": 3}, {"attributeModulus": [15000, 20000, 10000, 10000], "formationId": 13001, "playerFormation": [1, 2, 3, 4, 5, 6], "formation": [[1, 114], [2, 114], [3, 114]], "type": 1}, {"attributeModulus": [15000, 20000, 10000, 10000], "formationId": 13002, "playerFormation": [1, 2, 3, 4, 5, 6], "formation": [[1, 111], [2, 111], [3, 111]], "type": 2}, {"attributeModulus": [15000, 20000, 10000, 10000], "formationId": 13003, "playerFormation": [1, 2, 3, 4, 5, 6], "formation": [[1, 108], [2, 108], [3, 108]], "type": 1}, {"attributeModulus": [9500, 10000, 10000, 10000], "formationId": 13004, "playerFormation": [1, 2, 3], "formation": [[1, 114], [2, 114], [3, 114]], "type": 1}, {"attributeModulus": [9500, 10000, 10000, 10000], "formationId": 13005, "playerFormation": [1, 2, 3], "formation": [[1, 111], [2, 111], [3, 111]], "type": 2}, {"attributeModulus": [9500, 10000, 10000, 10000], "formationId": 13006, "playerFormation": [1, 2, 3], "formation": [[1, 111], [2, 111], [3, 111]], "type": 1}, {"attributeModulus": [10000, 10000, 10000, 10000], "formationId": 14001, "playerFormation": [1, 2, 3, 5], "formation": [[1, 114], [2, 114], [3, 114], [5, 114]], "type": 1}, {"attributeModulus": [10000, 10000, 10000, 10000], "formationId": 14002, "playerFormation": [1, 2, 3, 5], "formation": [[1, 111], [2, 111], [3, 111], [5, 111]], "type": 2}, {"attributeModulus": [10000, 10000, 10000, 10000], "formationId": 14003, "playerFormation": [1, 2, 3, 5], "formation": [[1, 108], [2, 108], [3, 108], [5, 108]], "type": 2}, {"attributeModulus": [10000, 10000, 10000, 10000], "formationId": 15001, "playerFormation": [1, 2, 3, 4, 6], "formation": [[1, 114], [2, 114], [3, 114], [4, 114], [6, 114]], "type": 1}, {"attributeModulus": [10000, 10000, 10000, 10000], "formationId": 15002, "playerFormation": [1, 2, 3, 4, 6], "formation": [[1, 111], [2, 111], [3, 111], [4, 111], [6, 111]], "type": 2}, {"attributeModulus": [10000, 10000, 10000, 10000], "formationId": 15003, "playerFormation": [1, 2, 3, 4, 6], "formation": [[1, 108], [2, 108], [3, 108], [4, 108], [6, 108]], "type": 2}, {"attributeModulus": [10000, 10000, 10000, 10000], "formationId": 16001, "playerFormation": [1, 2, 3, 4, 5, 6], "formation": [[1, 107], [2, 107], [3, 107], [4, 107], [5, 107], [6, 107]], "type": 2}, {"attributeModulus": [10000, 10000, 10000, 10000], "formationId": 16002, "playerFormation": [1, 2, 3, 4, 5, 6], "formation": [[1, 109], [2, 110], [3, 109], [4, 110], [5, 109], [6, 110]], "type": 2}, {"attributeModulus": [10000, 10000, 10000, 10000], "formationId": 16003, "playerFormation": [1, 2, 3, 4, 5, 6], "formation": [[1, 114], [2, 114], [3, 114], [4, 114], [5, 114], [6, 114]], "type": 1}, {"attributeModulus": [10000, 10000, 10000, 10000], "formationId": 16004, "playerFormation": [1, 2, 3, 4, 5, 6], "formation": [[1, 109], [2, 111], [3, 109], [4, 111], [5, 109], [6, 111]], "type": 2}, {"attributeModulus": [10000, 10000, 10000, 10000], "formationId": 16005, "playerFormation": [1, 2, 3, 4, 5, 6], "formation": [[1, 111], [2, 111], [3, 111], [4, 111], [5, 111], [6, 111]], "type": 2}, {"attributeModulus": [10000, 10000, 10000, 10000], "formationId": 16006, "playerFormation": [1, 2, 3, 4, 5, 6], "formation": [[1, 111], [2, 112], [3, 111], [4, 111], [5, 112], [6, 111]], "type": 2}, {"attributeModulus": [10000, 10000, 10000, 10000], "formationId": 16007, "playerFormation": [1, 2, 3, 4, 5, 6], "formation": [[1, 112], [2, 111], [3, 112], [4, 111], [5, 112], [6, 111]], "type": 2}, {"attributeModulus": [10000, 10000, 10000, 10000], "formationId": 16008, "playerFormation": [1, 2, 3, 4, 5, 6], "formation": [[1, 111], [2, 111], [3, 111], [4, 111], [5, 112], [6, 111]], "type": 2}, {"attributeModulus": [10000, 10000, 10000, 10000], "formationId": 16009, "playerFormation": [1, 2, 3, 4, 5, 6], "formation": [[1, 111], [2, 112], [3, 112], [4, 112], [5, 112], [6, 111]], "type": 2}]