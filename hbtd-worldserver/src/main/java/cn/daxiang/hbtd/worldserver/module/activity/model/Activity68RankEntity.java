package cn.daxiang.hbtd.worldserver.module.activity.model;

import cn.daxiang.protocol.game.TypeProtocol;

import java.util.HashMap;
import java.util.Map;

/**
 * 心愿招募-招募排行榜
 * @author: keven
 * @date: 2024/5/17
 */
public class Activity68RankEntity{
    /**
     * 角色属性Map
     * key:{@code ActorKey}
     */
    private Map<Byte, Object> attributes = new HashMap<>();
    /**
     * 招募次数
     */
    private int times;
    /**
     * 达成时间
     */
    private long achieveTime;

    public static Activity68RankEntity valueOf( Map<Byte, Object> attributes, int times, long achieveTime) {
        Activity68RankEntity model = new Activity68RankEntity();
        model.attributes = attributes;
        model.times = times;
        model.achieveTime = achieveTime;
        return model;
    }

    public Map<Byte, Object> getAttributes() {
        return attributes;
    }

    public void setAttributes(Map<Byte, Object> attributes) {
        this.attributes = attributes;
    }

    public int getTimes() {
        return times;
    }

    public void setTimes(int times) {
        this.times = times;
    }

    public long getAchieveTime() {
        return achieveTime;
    }

    public void setAchieveTime(long achieveTime) {
        this.achieveTime = achieveTime;
    }

    public long getActorId() {
        return Long.parseLong(this.attributes.get((byte) TypeProtocol.ActorFieldType.ACTOR_ID_VALUE).toString());
    }
}
