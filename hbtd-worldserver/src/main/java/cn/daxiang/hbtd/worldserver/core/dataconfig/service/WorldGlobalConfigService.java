package cn.daxiang.hbtd.worldserver.core.dataconfig.service;

import cn.daxiang.framework.dataconfig.DataConfig;
import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.hbtd.worldserver.core.dataconfig.convert.IConfigable;
import cn.daxiang.hbtd.worldserver.core.dataconfig.model.GlobalConfig;
import cn.daxiang.shared.GlobalConfigKey;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;

@Component
public class WorldGlobalConfigService {
    private static final Logger logger = LoggerFactory.getLogger(WorldGlobalConfigService.class);

    @Autowired
    private DataConfig dataConfig;

    public <T extends ModelAdapter> T findConfig(IdentiyKey key, Class<T> clazz) {
        return dataConfig.getConfig(key, clazz);
    }

    public <T extends ModelAdapter> T findConfig(Object key, Class<T> clazz) {
        return dataConfig.getConfig(IdentiyKey.build(key), clazz);
    }

    public <T extends IConfigable> T findGlobalObject(GlobalConfigKey key, Class<T> clazz) {
        GlobalConfig load = this.load(key);
        return load.findObject(clazz);
    }

    public GlobalConfig load(GlobalConfigKey key) {
        return load(key.toString());
    }

    public GlobalConfig load(String key) {
        GlobalConfig config = dataConfig.getConfig(IdentiyKey.build(key), GlobalConfig.class);
        if (config == null) {
            logger.error("GlobalConfig not found, key:{}", key);
        }
        return config;
    }

    public GlobalConfig findGlobalConfig(GlobalConfigKey key) {
        return load(key.toString());
    }

    public <T extends ModelAdapter> Collection<T> getList(Class<T> modelClass) {
        return dataConfig.getList(modelClass);
    }
}
