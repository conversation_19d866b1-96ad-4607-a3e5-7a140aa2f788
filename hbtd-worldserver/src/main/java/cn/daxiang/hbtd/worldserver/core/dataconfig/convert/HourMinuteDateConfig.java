package cn.daxiang.hbtd.worldserver.core.dataconfig.convert;

import cn.daxiang.framework.utils.DateUtils;
import cn.daxiang.hbtd.worldserver.core.dataconfig.convert.callback.ConvertStringToObjectCallback;

import java.util.Calendar;
import java.util.List;

/**
 * 小时分钟配置
 *
 * <AUTHOR>
 */
public class HourMinuteDateConfig implements IConfigable {
    /**
     * 开始
     */
    private String beginTime;
    /**
     * 结束
     */
    private String endTime;

    @Override
    public void buildObject(String config) {
        List<String> list = StringConvert.convertToObjectList(new ConvertStringToObjectCallback<String>() {
            @Override
            public String getTokenizerString() {
                return config;
            }

            @Override
            public int getSize() {
                return 1;
            }

            @Override
            public String fromArray(String[] array) {
                return array[0];
            }
        });
        int begin = Integer.valueOf(list.get(0));
        this.beginTime = begin / 100 + ":" + begin % 100;
        int end = Integer.valueOf(list.get(1));
        this.endTime = end / 100 + ":" + end % 100;
    }

    public String getBeginTime() {
        return beginTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public long getTime(int weekDays, String hourMinute) {
        long time = DateUtils.getTimeWihtWeekDayHHmm(weekDays, hourMinute);
        if (System.currentTimeMillis() > time) {
            Calendar c = Calendar.getInstance();
            c.setTimeInMillis(time);
            c.add(Calendar.WEEK_OF_YEAR, 1);
            time = c.getTimeInMillis();
        }
        return time;
    }

    public long getNextWeekTime(int weekDays, String hourMinute) {
        Calendar c = DateUtils.getCalendarWihtWeekDayHHmm(weekDays, hourMinute);
        c.add(Calendar.WEEK_OF_YEAR, 1);
        return c.getTimeInMillis();
    }
}
