package cn.daxiang.hbtd.worldserver.module.crossCup.model;

import cn.daxiang.shared.module.crossCup.CrossCupGroup;
import com.google.common.collect.Maps;

import java.util.TreeMap;

/**
 * 跨服杯赛赛区信息
 *
 * <AUTHOR>
 * @date 2020/2/7
 */
public class CrossCupZone {
    /**
     * crossCupRegionId
     */
    private int zoneId;
    /**
     * 小组信息
     * key:groupId,value:CrossCupGroup
     */
    private TreeMap<Integer, CrossCupGroup> groupMap = Maps.newTreeMap();

    public static CrossCupZone valueOf(int crossCupRegionId) {
        CrossCupZone zone = new CrossCupZone();
        zone.zoneId = crossCupRegionId;
        return zone;
    }

    public int getZoneId() {
        return zoneId;
    }

    public void setZoneId(int zoneId) {
        this.zoneId = zoneId;
    }

    public TreeMap<Integer, CrossCupGroup> getGroupMap() {
        return groupMap;
    }

    public void setGroupMap(TreeMap<Integer, CrossCupGroup> groupMap) {
        this.groupMap = groupMap;
    }
}
