package cn.daxiang.hbtd.worldserver.core.dataconfig.convert;

import cn.daxiang.hbtd.worldserver.core.dataconfig.convert.callback.ConvertStringToDynamicObjectWithSplitCallback;
import cn.daxiang.hbtd.worldserver.core.dataconfig.convert.callback.ConvertStringToMapCallback;
import cn.daxiang.hbtd.worldserver.core.dataconfig.convert.callback.ConvertStringToObjectCallback;
import cn.daxiang.hbtd.worldserver.core.dataconfig.convert.callback.ConvertStringToObjectCallbackWithDelimiter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.StringTokenizer;
import java.util.TreeMap;

public class StringConvert {

    public static String ALL_DELIMITER = "[,]";

    public static <K, V> Map<K, V> convertToObjectHashMap(ConvertStringToMapCallback<K, V> callback) {
        Map<K, V> map = Maps.newHashMapWithExpectedSize(8);
        StringTokenizer tokenizer = new StringTokenizer(callback.getTokenizerString(), ALL_DELIMITER);
        String[] array = new String[callback.getSize()];
        int count = 0;
        while (tokenizer.hasMoreTokens()) {
            array[count] = tokenizer.nextToken();
            count++;
            if (count >= callback.getSize()) {
                callback.fromArray(map, array);
                count = 0;
            }
        }
        return map;
    }

    @SuppressWarnings("unchecked")
    public static <K, V> TreeMap<K, V> convertToObjecTreeMap(ConvertStringToMapCallback<K, V> callback) {
        TreeMap<K, V> map = (TreeMap<K, V>) Maps.newTreeMap();
        StringTokenizer tokenizer = new StringTokenizer(callback.getTokenizerString(), ALL_DELIMITER);
        String[] array = new String[callback.getSize()];
        int count = 0;
        while (tokenizer.hasMoreTokens()) {
            array[count] = tokenizer.nextToken();
            count++;
            if (count >= callback.getSize()) {
                callback.fromArray(map, array);
                count = 0;
            }
        }
        return map;
    }

    public static <K, V> LinkedHashMap<K, V> convertToObjectLinkedMap(ConvertStringToMapCallback<K, V> callback) {
        LinkedHashMap<K, V> map = new LinkedHashMap<>(8);
        StringTokenizer tokenizer = new StringTokenizer(callback.getTokenizerString(), ALL_DELIMITER);
        String[] array = new String[callback.getSize()];
        int count = 0;
        while (tokenizer.hasMoreTokens()) {
            array[count] = tokenizer.nextToken();
            count++;
            if (count >= callback.getSize()) {
                callback.fromArray(map, array);
                count = 0;
            }
        }
        return map;
    }

    public static <V> List<V> convertToObjectList(ConvertStringToObjectCallback<V> callback) {
        List<V> list = Lists.newArrayList();
        StringTokenizer tokenizer = new StringTokenizer(callback.getTokenizerString(), ALL_DELIMITER);
        String[] array = new String[callback.getSize()];
        int count = 0;
        while (tokenizer.hasMoreTokens()) {
            array[count] = tokenizer.nextToken();
            count++;
            if (count >= callback.getSize()) {
                V v = callback.fromArray(array);
                if (v != null) {
                    list.add(v);
                }
                count = 0;
            }
        }
        return list;
    }

    public static <V> List<V> convertToObjectList(ConvertStringToObjectCallbackWithDelimiter<V> callback) {
        List<V> list = Lists.newArrayList();
        StringTokenizer tokenizer = new StringTokenizer(callback.getTokenizerString(), callback.getDelimiter());
        String[] array = new String[callback.getSize()];
        int count = 0;
        while (tokenizer.hasMoreTokens()) {
            array[count] = tokenizer.nextToken();
            count++;
            if (count >= callback.getSize()) {
                V v = callback.fromArray(array);
                if (v != null) {
                    list.add(v);
                }
                count = 0;
            }
        }
        return list;
    }

    public static <V> LinkedList<V> convertToObjectLinkedList(ConvertStringToObjectCallback<V> callback) {
        LinkedList<V> list = new LinkedList<>();
        StringTokenizer tokenizer = new StringTokenizer(callback.getTokenizerString(), ALL_DELIMITER);
        String[] array = new String[callback.getSize()];
        int count = 0;
        while (tokenizer.hasMoreTokens()) {
            array[count] = tokenizer.nextToken();
            count++;
            if (count >= callback.getSize()) {
                V v = callback.fromArray(array);
                if (v != null) {
                    list.add(v);
                }
                count = 0;
            }
        }
        return list;
    }

    public static List<Integer> converttoIntegerList(final String values) {
        return StringConvert.convertToObjectList(new ConvertStringToObjectCallback<Integer>() {

            @Override
            public String getTokenizerString() {
                return values;
            }

            @Override
            public int getSize() {
                return 1;
            }

            @Override
            public Integer fromArray(String[] array) {
                return Integer.parseInt(array[0]);
            }
        });
    }

    public static List<Long> converttoLongList(final String values) {
        return StringConvert.convertToObjectList(new ConvertStringToObjectCallback<Long>() {

            @Override
            public String getTokenizerString() {
                return values;
            }

            @Override
            public int getSize() {
                return 1;
            }

            @Override
            public Long fromArray(String[] array) {
                return Long.parseLong(array[0]);
            }
        });
    }

    public static String convertListToString(Collection<? extends Number> es) {
        StringBuilder builder = new StringBuilder();
        for (Number re : es) {
            builder.append(re).append(",");
        }
        return builder.toString();
    }

    public static <V extends Dynamicable<T>, T> List<V> convertToObject(ConvertStringToDynamicObjectWithSplitCallback<V, T> callback) {
        List<V> list = new ArrayList<>(callback.findCapacity());
        List<String> strings = Lists.newArrayList();
        int start = 0;
        int end = 0;
        int index = 0;
        String str = callback.getString();
        for (int i = 0; i < str.length(); i++) {
            char c = str.charAt(i);
            if (c == callback.getStartSplit()) {
                start++;
            }
            if (c == callback.getEndSplit()) {
                end++;
            }
            if (start != 0 && start == end) {
                strings.add(str.substring(index, i + 1));
                index = i;
                index++;
                index++;
                start = 0;
                end = 0;
            }
        }
        for (String string : strings) {
            List<T> dynamic = Lists.newArrayList();
            int count = 0;
            int dynamicCount = 0;
            StringTokenizer stringTokenizer = new StringTokenizer(string, callback.getDelimiter());
            String[] array = new String[stringTokenizer.countTokens()];
            String[] dynamicArray = new String[callback.getDynamicSize()];
            while (stringTokenizer.hasMoreTokens()) {
                String temp = stringTokenizer.nextToken();
                array[count] = temp;
                count++;
                if (count >= callback.getDynamicStart()) {
                    dynamicArray[dynamicCount] = temp;
                    dynamicCount++;
                    if (dynamicCount >= callback.getDynamicSize()) {
                        T t = callback.fromDynamic(dynamicArray);
                        if (t != null) {
                            dynamic.add(t);
                        }
                        dynamicCount = 0;
                    }
                }
            }
            V v = callback.fromArray(array);
            if (v != null) {
                v.fromDynamic(dynamic);
                list.add(v);
            }
        }
        return list;
    }
    // public static <K,V> HashMap<K, V>
    // convertToHashMap(ConvertStringToMapWithSplitCallback<K,V> callback) {
    // HashMap<K, V> map = new HashMap<>(callback.findCapacity());
    // List<String> strings = Lists.newArrayList();
    // int start = 0;
    // int end = 0;
    // int index = 0;
    // String str = callback.getString();
    // for(int i=0;i<str.length();i++){
    // char c = str.charAt(i);
    // if(c == callback.getStartSplit()){
    // start ++;
    // }
    // if(c == callback.getEndSplit()){
    // end ++;
    // }
    // if(start != 0 && start == end){
    // strings.add(str.substring(index, i +1));
    // index = i;
    // index ++;
    // index ++;
    // start = 0;
    // end = 0;
    // }
    // }
    // for(String string : strings){
    // int count = 0;
    // StringTokenizer stringTokenizer = new
    // StringTokenizer(string,callback.getDelimiter());
    // String[] array = new String[stringTokenizer.countTokens()];
    // while (stringTokenizer.hasMoreTokens()){
    // array[count] = stringTokenizer.nextToken();
    // count ++;
    // if(count >= callback.getSize()){
    // callback.fromArray(map,array);
    // count = 0;
    // }
    // }
    // }
    // return map;
    // }

    // public static <V> List<V>
    // convertToObject(ConvertStringToObjectWithSplitCallback<V> callback) {
    // List<V> vs = new ArrayList<>(callback.findCapacity());
    // List<String> strings = Lists.newArrayList();
    // int start = 0;
    // int end = 0;
    // int index = 0;
    // String str = callback.getString();
    // for(int i=0;i<str.length();i++){
    // char c = str.charAt(i);
    // if(c == callback.getStartSplit()){
    // start ++;
    // }
    // if(c == callback.getEndSplit()){
    // end ++;
    // }
    // if(start != 0 && start == end){
    // strings.add(str.substring(index, i +1));
    // index = i;
    // index ++;
    // index ++;
    // start = 0;
    // end = 0;
    // }
    // }
    // for(String string : strings){
    // int count = 0;
    // StringTokenizer stringTokenizer = new
    // StringTokenizer(string,callback.getDelimiter());
    // String[] array = new String[stringTokenizer.countTokens()];
    // while (stringTokenizer.hasMoreTokens()){
    // array[count] = stringTokenizer.nextToken();
    // count ++;
    // if(count >= callback.getSize()){
    // V v = callback.fromArray(array);
    // vs.add(v);
    // count = 0;
    // }
    // }
    // }
    // return vs;
    // }

}
