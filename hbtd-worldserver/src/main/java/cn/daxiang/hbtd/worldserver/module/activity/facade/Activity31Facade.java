package cn.daxiang.hbtd.worldserver.module.activity.facade;

import cn.daxiang.shared.module.activity.Activity31RankVO;

import java.util.Collection;
import java.util.Map;

/**
 * @Author: Gary
 * @Date 2022-08-15 15:23
 * @Description:
 */
public interface Activity31Facade {
    /**
     * 获取摘星揽月排行榜
     *
     * @param serverType
     * @param activityId
     * @param data
     * @param openDate
     * @return
     */
    Collection<Activity31RankVO> getActivity31RankList(int serverType, int activityId, int data, String openDate, long rankLimit);

    /**
     * 刷新摘星揽月排行榜
     *
     * @param serverType
     * @param activityId
     * @param data
     * @param openDate
     * @param rankVO
     */
    void refreshActivity31Rank(int serverType, int activityId, int data, String openDate, Activity31RankVO rankVO);

    /**
     * 摘星揽月活动开启初始化
     *
     * @param serverType
     * @param activityId
     * @param data
     * @param openDate
     */
    void activityOpen(int serverType, int activityId, int data, String openDate, long rankLimit);

    /**
     * 根据区间获取摘星揽月排行榜
     *
     * @param serverType
     * @param activityId
     * @param data
     * @param openDate
     * @param rankLimit
     * @param attributes
     */
    Collection<Activity31RankVO> getActivity31RankListByRankingInterval(int serverType, int activityId, int data, String openDate, long rankLimit, int begin, int end,
        Map<Byte, Object> attributes);
}
