package cn.daxiang.hbtd.worldserver.core.dataconfig.service;

import cn.daxiang.framework.dataconfig.ConfigServiceAdapter;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.hbtd.worldserver.core.dataconfig.model.NationChapterConfig;
import cn.daxiang.hbtd.worldserver.core.dataconfig.model.NationLevelConfig;
import cn.daxiang.hbtd.worldserver.core.dataconfig.model.NationStoryConfig;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

import static cn.daxiang.shared.GameModuleStatusCodeConstant.CONFIG_NOT_FOUND;

@Component
public class NationConfigService extends ConfigServiceAdapter {
    /**
     * 军团副本关卡配置Map
     * key:storyId,value:NationStoryConfig
     */
    private static TreeMap<Integer, NationStoryConfig> NATION_STORY_CONFIG_MAP = Maps.newTreeMap();
    /**
     * 军团副本章节配置Map
     * key:chapterId,value:NationChapterConfig
     */
    private static TreeMap<Integer, NationChapterConfig> NATION_CHAPTER_CONFIG_MAP = Maps.newTreeMap();
    /**
     * 军团副本关卡章节配置Map
     * key:chapterId,value:Collection <NationStoryConfig><
     */
    private static Map<Integer, Collection<NationStoryConfig>> NATION_CHAPTER_STORYS_CONFIG_MAP = Maps.newHashMap();

    private static Map<Integer, NationLevelConfig> NATION_LEVEL_CONFIG_MAP = new HashMap<>();

    public static NationStoryConfig getNationStoryConfig(int storyId) {
        return NATION_STORY_CONFIG_MAP.get(storyId);
    }

    public static NationChapterConfig getNationChapterConfig(int chapterId) {
        return NATION_CHAPTER_CONFIG_MAP.get(chapterId);
    }

    public static int getInitChapterId() {
        return NATION_CHAPTER_CONFIG_MAP.firstKey();
    }

    /**
     * 获取军团副本全部的关卡配置
     *
     * @return
     */
    public static Collection<NationStoryConfig> getNationStoryConfigList() {
        return NATION_STORY_CONFIG_MAP.values();
    }

    /**
     * 获取军团副本的所有的storyId
     *
     * @return
     */
    public static Collection<Integer> getNationStoryConfigIdList() {
        return NATION_STORY_CONFIG_MAP.keySet();
    }

    /**
     * 获取军团副本的所有的storyId
     *
     * @return
     */
    public static Collection<Integer> getNationStoryConfigIdList(int chapterId) {
        Collection<Integer> storyIdList = Lists.newArrayList();
        Collection<NationStoryConfig> nationStoryConfigs = NATION_CHAPTER_STORYS_CONFIG_MAP.get(chapterId);
        if (nationStoryConfigs != null) {
            for (NationStoryConfig config : nationStoryConfigs) {
                storyIdList.add(config.getStoryId());
            }
        }
        return storyIdList;
    }

    /**
     * 根据章节Id
     *
     * @param chapterId
     * @return
     */
    public static Collection<NationStoryConfig> getStoryConfigsByChapterId(int chapterId) {
        return NATION_CHAPTER_STORYS_CONFIG_MAP.get(chapterId);
    }

    public static TResult<NationLevelConfig> getNationLevelConfig(int level) {
        NationLevelConfig levelConfig = NATION_LEVEL_CONFIG_MAP.get(level);
        if (levelConfig == null) {
            LOGGER.error("NationLevelConfig not found, level:{}", level);
            return TResult.valueOf(CONFIG_NOT_FOUND);
        }
        return TResult.sucess(levelConfig);
    }

    public static boolean isMaxLevel(int level, long exp) {
        TResult<NationLevelConfig> result = getNationLevelConfig(level);
        if (result.isFail()) {
            return true;
        }
        NationLevelConfig levelConfig = result.item;
        if (levelConfig.getNextLevel() != 0) {
            return false;
        }
        return levelConfig.getNeedExp() <= exp;
    }

    public static Map<Integer, Number> addExp(int level, long exp, long addExp) {
        Map<Integer, Number> attributeMaps = Maps.newHashMap();
        while (addExp > 0) {
            TResult<NationLevelConfig> result = getNationLevelConfig(level);
            if (result.isFail()) {
                break;
            }
            NationLevelConfig levelConfig = result.item;
            if (levelConfig.getNeedExp() <= exp + addExp) {
                if (levelConfig.getNextLevel() == 0) {
                    exp = levelConfig.getNeedExp();
                    addExp = 0;
                } else {
                    addExp -= levelConfig.getNeedExp() - exp;
                    level++;
                    exp = 0;
                }
            } else {
                exp += addExp;
                addExp = 0;
            }
        }
        attributeMaps.put(1, level);
        attributeMaps.put(2, exp);
        return attributeMaps;
    }

    @Override
    protected void initialize() {
        Collection<NationStoryConfig> nationStoryConfigs = dataConfig.listAll(this, NationStoryConfig.class);
        for (NationStoryConfig storyConfig : nationStoryConfigs) {
            NATION_STORY_CONFIG_MAP.put(storyConfig.getStoryId(), storyConfig);
            Collection<NationStoryConfig> NationStoryConfigList = NATION_CHAPTER_STORYS_CONFIG_MAP.get(storyConfig.getChapterId());
            if (NationStoryConfigList == null) {
                NationStoryConfigList = new ArrayList<NationStoryConfig>();
                NATION_CHAPTER_STORYS_CONFIG_MAP.put(storyConfig.getChapterId(), NationStoryConfigList);
            }
            NationStoryConfigList.add(storyConfig);
        }
        Collection<NationChapterConfig> nationChapterConfigs = dataConfig.listAll(this, NationChapterConfig.class);
        for (NationChapterConfig config : nationChapterConfigs) {
            NATION_CHAPTER_CONFIG_MAP.put(config.getChapterId(), config);
        }
        Collection<NationLevelConfig> levelConfigList = dataConfig.listAll(this, NationLevelConfig.class);
        for (NationLevelConfig config : levelConfigList) {
            NATION_LEVEL_CONFIG_MAP.put(config.getLevel(), config);
        }
    }

    @Override
    protected void clean() {
        NATION_STORY_CONFIG_MAP.clear();
        NATION_CHAPTER_CONFIG_MAP.clear();
        NATION_LEVEL_CONFIG_MAP.clear();
        NATION_CHAPTER_STORYS_CONFIG_MAP.clear();
    }
}
