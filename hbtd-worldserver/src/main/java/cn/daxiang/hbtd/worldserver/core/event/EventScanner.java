package cn.daxiang.hbtd.worldserver.core.event;

import cn.daxiang.framework.event.EventContext;
import cn.daxiang.framework.event.OnEventListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.List;

@Component
public class EventScanner {

    private static final Logger LOGGER = LoggerFactory.getLogger(EventScanner.class);

    @Autowired
    private EventContext eventContext;

    /**
     * 扫描获取事件集合
     *
     * @param beanList
     */
    public void initEvent(List<Object> beanList) {
        // 注册事件监听方法
        for (Object bean : beanList) {
            Class<?> clazz = bean.getClass();
            // 注册事件监听器
            if (OnEventListener.class.isAssignableFrom(clazz)) {
                OnEventListener listener = (OnEventListener) bean;
                eventContext.registeEventListener(listener);
            }
            Method[] methods = clazz.getMethods();
            for (Method m : methods) {
                eventContext.register(bean, clazz, m);
            }
        }
        LOGGER.info("event init complete!");
    }
}
