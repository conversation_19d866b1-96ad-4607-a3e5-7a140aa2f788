package cn.daxiang.hbtd.worldserver.module.team;

import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.shared.module.lineup.LineupAttribute;
import cn.daxiang.shared.module.team.Team;
import cn.daxiang.shared.type.TeamType;

import java.util.Collection;
import java.util.Map;

public interface TeamFacade {

    /**
     * 获取队伍信息
     *
     * @param serverType
     * @param serverId
     * @param actorId
     * @return
     */
    TResult<Team> getTeam(int serverType, int serverId, long actorId);

    /**
     * 挑战，队伍设置为战斗状态
     *
     * @param serverType
     * @param serverId
     * @param actorId
     * @return
     */
    TResult<Team> challenge(int serverType, int serverId, long actorId);

    /**
     * 一键邀请
     *
     * @param serverType
     * @param serverId
     * @param actorId
     * @return
     */
    TResult<Team> oneClickInvitation(int serverType, int serverId, long actorId);

    /**
     * 组队列表
     *
     * @param serverType
     * @param serverId
     * @param actorId
     * @param teamType
     * @return
     */
    Collection<Team> getList(int serverType, int serverId, long actorId, TeamType teamType);

    /**
     * 创建队伍
     *
     * @param serverType
     * @param serverId
     * @param actorId
     * @param attribute
     * @param lineupAttribute
     * @param systemSkillMaps
     * @param teamType
     * @param ext
     * @param numberLimit
     * @return
     */
    TResult<Team> create(int serverType, int serverId, long actorId, Map<Byte, Object> attribute, Map<Integer, LineupAttribute> lineupAttribute,
        Map<Integer, Collection<Integer>> systemSkillMaps, TeamType teamType, Object ext, int numberLimit);

    /**
     * 设置密码
     *
     * @param serverType
     * @param serverId
     * @param actorId
     * @param password
     * @return
     */
    TResult<Team> setPassword(int serverType, int serverId, long actorId, String password);

    /**
     * 加入队伍
     *
     * @return
     */
    Result join(int serverType, int serverId, Map<Byte, Object> attribute, Map<Integer, LineupAttribute> lineupAttribute, Map<Integer, Collection<Integer>> systemSkillMaps,
        long targetId, String password, TeamType teamType);

    /**
     * 踢出队伍
     *
     * @param serverType
     * @param serverId
     * @param actorId
     * @param targetActorId
     * @return
     */
    Result kickOut(int serverType, int serverId, long actorId, long targetActorId);

    /**
     * 退出队伍
     *
     * @param serverType
     * @param serverId
     * @param actorId
     * @return
     */
    Result quit(int serverType, int serverId, long actorId);

    /**
     * 邀请队员
     *
     * @param serverType
     * @param serverId
     * @param actorId
     * @param leaderActorId
     * @param targetServerId
     * @param targetActorId
     * @return
     */
    Result invite(int serverType, int serverId, long actorId, long leaderActorId, int targetServerId, long targetActorId);

    /**
     * 销毁房间
     *
     * @param team
     */
    void destroyTeam(Team team);

    /**
     * 角色退出
     */
    void actorLogout(int serverType, int serverId, long actorId);

    /**
     * 跳过战斗
     *
     * @param serverType
     * @param serverId
     * @param actorId
     * @return
     */
    void skipBattle(int serverType, int serverId, long actorId);

    /**
     * 队员位置调整
     *
     * @param serverType
     * @param serverId
     * @param actorId
     * @param currentPosition
     * @param targetPosition
     */
    void swapPosition(int serverType, int serverId, long actorId, int currentPosition, int targetPosition);

}
