package cn.daxiang.hbtd.worldserver.module.immortalsTurntable;

import cn.daxiang.framework.rpc.world.WorldImmortalsTurntableRpc;
import cn.daxiang.hbtd.worldserver.module.immortalsTurntable.facade.WorldImmortalsTurntableFacade;
import cn.daxiang.shared.module.immortalsTurntable.ImmortalsTurntableRecordEntity;
import cn.daxiang.shared.reward.RewardObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * @author: Gary
 * @date: 2023/2/16 18:17
 * @Description:
 */
@Component
public class WorldImmortalsTurntableRpcImpl implements WorldImmortalsTurntableRpc {
    @Autowired
    private WorldImmortalsTurntableFacade worldImmortalsTurntableFacade;

    @Override
    public List<ImmortalsTurntableRecordEntity> getImmortalsTurntableRewardRecord(int serverType, int serverId) {
        return worldImmortalsTurntableFacade.getImmortalsTurntableRewardRecord(serverType, serverId);
    }

    @Override
    public void refreshImmortalsTurntableRewardRecord(int serverType, int serverId, Map<Byte, Object> attributes, Collection<RewardObject> rewards, String des, int limit) {
        worldImmortalsTurntableFacade.refreshImmortalsTurntableRewardRecord(serverType, serverId, attributes, rewards, des, limit);
    }
}
