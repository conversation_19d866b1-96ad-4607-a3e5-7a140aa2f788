package cn.daxiang.hbtd.worldserver.module.reputation;

import cn.daxiang.framework.result.CollectionResult;
import cn.daxiang.framework.rpc.world.WorldReputationRpc;
import cn.daxiang.hbtd.worldserver.module.reputation.facade.WorldReputationFacade;
import cn.daxiang.shared.module.reputation.ReputationTurntableRecordEntity;
import cn.daxiang.shared.module.reputation.ReputationZoneRankEntity;
import cn.daxiang.shared.reward.RewardObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 平定天下
 *
 * @author: Gary
 * @date: 2023/3/9 10:59
 * @Description:
 */
@Component
public class WorldReputationRpcImpl implements WorldReputationRpc {
    @Autowired
    private WorldReputationFacade worldReputationFacade;

    @Override
    public List<ReputationTurntableRecordEntity> getReputationTurntableRewardRecord(int serverType, int serverId) {
        return worldReputationFacade.getReputationTurntableRewardRecord(serverType, serverId);
    }

    @Override
    public void refreshReputationTurntableRewardRecord(int serverType, int serverId, Map<Byte, Object> attributes, Collection<RewardObject> rewards, String des, int limit) {
        worldReputationFacade.refreshReputationTurntableRewardRecord(serverType, serverId, attributes, rewards, des, limit);
    }

    @Override
    public void refreshReputationZoneRank(int serverType, int serverId, ReputationZoneRankEntity entity) {
        worldReputationFacade.refreshReputationZoneRank(serverType, serverId, entity);
    }

    @Override
    public CollectionResult<ReputationZoneRankEntity> getReputationZoneRankList(int serverType, int serverId) {
        return worldReputationFacade.getReputationZoneRankList(serverType, serverId);
    }
}
