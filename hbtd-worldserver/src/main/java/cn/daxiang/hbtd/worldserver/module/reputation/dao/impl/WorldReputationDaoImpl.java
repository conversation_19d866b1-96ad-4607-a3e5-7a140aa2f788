package cn.daxiang.hbtd.worldserver.module.reputation.dao.impl;

import cn.daxiang.framework.result.CollectionResult;
import cn.daxiang.framework.utils.rank.cache.ConsistentLadderRankCache;
import cn.daxiang.hbtd.worldserver.core.context.ApplicationServerZoneCompleteEvent;
import cn.daxiang.hbtd.worldserver.core.redis.RedisDao;
import cn.daxiang.hbtd.worldserver.module.reputation.dao.WorldReputationDao;
import cn.daxiang.hbtd.worldserver.module.reputation.module.ReputationZoneRank;
import cn.daxiang.hbtd.worldserver.module.server.ServerZoneHelper;
import cn.daxiang.shared.module.reputation.ReputationTurntableRecordEntity;
import cn.daxiang.shared.module.reputation.ReputationZoneRankEntity;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import static cn.daxiang.shared.GameModuleStatusCodeConstant.INVALID_PARAM;

/**
 * @author: Gary
 * @date: 2023/3/13 15:52
 * @Description:
 */
@Component
public class WorldReputationDaoImpl extends RedisDao implements WorldReputationDao, ApplicationListener<ApplicationServerZoneCompleteEvent> {
    /**
     * 平定天下战区排行榜
     * key:servetType,value:{key:zoneId,value:ConsistentLadderRankCache}
     */
    private static final Map<Integer, Map<Integer, ConsistentLadderRankCache<Long, ReputationZoneRank>>> REPUTATION_ZONE_RANK_MAP = Maps.newConcurrentMap();
    /**
     * 皇家宝库中奖记录
     * key:serverType,value:{key:zoneId,value:Collection<>}
     */
    private static final Map<Integer, Map<Integer, List<ReputationTurntableRecordEntity>>> REPUTATION_TURNTABLE_RECORD_MAP = Maps.newConcurrentMap();
    /**
     * 皇家宝库中奖记录缓存key
     * serverType_zoneId
     */
    private static final String REPUTATION_TURNTABLE_RECORD_CACHE_KEY = "REPUTATION_TURNTABLE_RECORD_CACHE_KEY_%s_%s";
    /**
     * 平定天下战区排行榜缓存Key
     * serverType_zoneId
     */
    private static final String REPUTATION_ZONE_RANK_CACHE_KEY = "REPUTATION_ZONE_RANK_CACHE_KEY_%s_%s";

    /**
     * 排行榜限制条数
     */
    private static int RANK_LIMIT = 100;

    @Override
    public List<ReputationTurntableRecordEntity> getReputationTurntableRewardRecord(int serverType, int zoneId) {
        Map<Integer, List<ReputationTurntableRecordEntity>> map = REPUTATION_TURNTABLE_RECORD_MAP.get(serverType);
        return map.get(zoneId);
    }

    @Override
    public void refreshReputationTurntableRewardRecord(int serverType, int zoneId, ReputationTurntableRecordEntity entity, int limit) {
        Map<Integer, List<ReputationTurntableRecordEntity>> map = REPUTATION_TURNTABLE_RECORD_MAP.get(serverType);
        List<ReputationTurntableRecordEntity> entities = map.get(zoneId);
        entities.add(entity);
        if (entities.size() > limit) {
            entities.remove(0);
        }
        String cacheKey = getReputationTurntableRecordCacheKey(serverType, zoneId);
        clearSortSet(cacheKey);
        addListObject(cacheKey, entities);
    }

    @Override
    public void refreshReputationZoneRank(int serverType, int zoneId, ReputationZoneRankEntity entity) {
        Map<Integer, ConsistentLadderRankCache<Long, ReputationZoneRank>> rankCacheMap = REPUTATION_ZONE_RANK_MAP.get(serverType);
        if (rankCacheMap == null) {
            LOGGER.error("ConsistentLadderRankCache is null,serverType:{}", serverType);
            return;
        }
        ConsistentLadderRankCache<Long, ReputationZoneRank> rankCache = rankCacheMap.get(zoneId);
        if (rankCache == null) {
            LOGGER.error("ReputationZoneRank is null,zoneId:{}", zoneId);
            return;
        }
        long actorId = entity.getActorId();
        ReputationZoneRank rank = rankCache.find(actorId);
        if (rank == null) {
            rank = ReputationZoneRank.valueOf(entity.getAttributes(), entity.getLevel(), entity.getReputationVale(), entity.isBuyPrivilege(), entity.getAchieveTime());
        } else {
            rank.update(entity.getAttributes(), entity.getLevel(), entity.getReputationVale(), entity.getAchieveTime(), entity.isBuyPrivilege());
        }
        rankCache.achieve(rank);
        if (rankCache.find(actorId) != null) {
            Collection<ReputationZoneRank> rankList = rankCache.findAll();
            String cacheKey = getReputationZoneRankCacheKey(serverType, zoneId);
            clearSortSet(cacheKey);
            addListObject(cacheKey, rankList);
        }
    }

    @Override
    public CollectionResult<ReputationZoneRank> getReputationZoneRankList(int serverType, int zoneId) {
        Map<Integer, ConsistentLadderRankCache<Long, ReputationZoneRank>> rankCacheMap = REPUTATION_ZONE_RANK_MAP.get(serverType);
        if (rankCacheMap == null) {
            LOGGER.error("ConsistentLadderRankCache is null,serverType:{}", serverType);
            return CollectionResult.valueOf(INVALID_PARAM);
        }
        ConsistentLadderRankCache<Long, ReputationZoneRank> rankCache = rankCacheMap.get(zoneId);
        if (rankCache == null) {
            LOGGER.error("ReputationZoneRank is null,zoneId:{}", zoneId);
            return CollectionResult.valueOf(INVALID_PARAM);
        }
        return CollectionResult.collection(rankCache.findAll());
    }

    @Override
    public void onApplicationEvent(ApplicationServerZoneCompleteEvent event) {
        for (Map.Entry<Integer, Map<Integer, List<Integer>>> entry : ServerZoneHelper.getAllZoneConfig().entrySet()) {
            int serverType = entry.getKey();
            //皇家宝库中奖记录
            Map<Integer, List<ReputationTurntableRecordEntity>> zoneMap = REPUTATION_TURNTABLE_RECORD_MAP.computeIfAbsent(serverType, x -> Maps.newHashMap());
            //平定天下排行榜
            Map<Integer, ConsistentLadderRankCache<Long, ReputationZoneRank>> rankCacheMap = REPUTATION_ZONE_RANK_MAP.computeIfAbsent(serverType, x -> Maps.newHashMap());
            for (Integer zoneId : entry.getValue().keySet()) {
                List<ReputationTurntableRecordEntity> entityList = zoneMap.computeIfAbsent(zoneId, x -> Lists.newArrayList());
                String cacheKey = getReputationTurntableRecordCacheKey(serverType, zoneId);
                entityList.addAll(getListObject(cacheKey, ReputationTurntableRecordEntity.class));
                //平定天下排行榜数据加载
                ConsistentLadderRankCache<Long, ReputationZoneRank> rankCache =
                    rankCacheMap.computeIfAbsent(zoneId, x -> new ConsistentLadderRankCache<>(RANK_LIMIT, Short.MAX_VALUE));
                List<ReputationZoneRank> zoneRanks = getListObject(getReputationZoneRankCacheKey(serverType, zoneId), ReputationZoneRank.class);
                for (ReputationZoneRank rank : zoneRanks) {
                    rankCache.achieve(rank);
                }
            }
        }
    }

    private String getReputationTurntableRecordCacheKey(Object serverType, Object zoneId) {
        return String.format(REPUTATION_TURNTABLE_RECORD_CACHE_KEY, serverType, zoneId);
    }

    private String getReputationZoneRankCacheKey(Object serverType, Object zoneId) {
        return String.format(REPUTATION_ZONE_RANK_CACHE_KEY, serverType, zoneId);
    }
}
