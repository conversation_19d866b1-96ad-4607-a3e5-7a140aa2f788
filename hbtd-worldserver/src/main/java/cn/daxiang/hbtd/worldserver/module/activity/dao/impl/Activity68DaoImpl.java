package cn.daxiang.hbtd.worldserver.module.activity.dao.impl;

import cn.daxiang.framework.utils.rank.cache.ConsistentLadderRankCache;
import cn.daxiang.hbtd.worldserver.core.context.ApplicationServerZoneCompleteEvent;
import cn.daxiang.hbtd.worldserver.core.redis.RedisDao;
import cn.daxiang.hbtd.worldserver.module.activity.dao.Activity68Dao;
import cn.daxiang.hbtd.worldserver.module.activity.model.Activity68Rank;
import cn.daxiang.hbtd.worldserver.module.activity.model.Activity68RankEntity;
import cn.daxiang.hbtd.worldserver.module.server.ServerZoneHelper;
import cn.daxiang.protocol.game.TypeProtocol;
import cn.daxiang.shared.module.activity.Activity68RankVO;
import cn.daxiang.shared.module.activity.Activity68ReportVO;
import cn.daxiang.shared.reward.RewardObject;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * @author: keven
 * @date: 2024/5/20 17:18
 */
@Component
public class Activity68DaoImpl extends RedisDao implements Activity68Dao, ApplicationListener<ApplicationServerZoneCompleteEvent>{
    private final static int RANK_LIMIT = 100;
    private final static int REPORT_LIMIT = 30;
    private final static int TOP_RANK = 3;

    /**
     * key:serverType,
     * value:{
     * key:activityType_data_openDate,value:{
     * key:zoneId,
     * value:ConsistentLadderRankCache
     * }
     * }
     */
    public static final Map<Integer, Map<String, ConsistentLadderRankCache<Long, Activity68Rank>>> ACTIVITY_68_RANK = Maps.newConcurrentMap();

    public static final Map<String, CopyOnWriteArrayList<Activity68ReportVO>> ACTIVITY_68_REPORT_CACHE = Maps.newConcurrentMap();

    /**
     * 排行榜Key
     * serverType
     * activityType_data_openDate
     */
    private static final String ACTIVITY_68_RANK_CACHE_KEY = "ACTIVITY_68_RANK_CACHE_KEY_%s_%s";


    /**
     * 中奖播报Key
     * serverType
     * activityType_data_openDate
     */
    private static final String ACTIVITY_68_REPORT_CACHE_KEY = "ACTIVITY_68_REPORT_CACHE_KEY_%s_%s";

    /**
     * activityType, data, openDate
     */
    private static final String KEY_FORMAT = "%s_%s_%s";

    @Override
    public void activity68Open(int serverType, int activityType, int data, String openDate) {
        String activityKey = String.format(KEY_FORMAT, activityType, data, openDate);
        Map<String, ConsistentLadderRankCache<Long, Activity68Rank>> serverTypeMap = ACTIVITY_68_RANK.computeIfAbsent(serverType, x -> Maps.newHashMap());
        ConsistentLadderRankCache<Long, Activity68Rank> rankCache =
            serverTypeMap.computeIfAbsent(activityKey, k -> new ConsistentLadderRankCache<>(RANK_LIMIT, Short.MAX_VALUE));
        Collection<Activity68Rank> rankList = rankCache.findAll();
        String cacheKey = this.getRankCacheKey(serverType, activityKey);
        clearSortSet(cacheKey);
        for (Activity68Rank activity68Rank : rankList) {
            Activity68RankEntity activity68RankEntity = Activity68RankEntity.valueOf(activity68Rank.getAttributes(), activity68Rank.getTimes(), activity68Rank.getAchieveTime());
            putHashObject(cacheKey, JSONObject.toJSONString(activity68Rank.getKey()), activity68RankEntity);
        }
    }

    @Override
    public void refreshActivity68Rank(int serverType, int activityType, int data, String openDate, Map<Byte, Object> attribute, int times) {
        Map<String, ConsistentLadderRankCache<Long, Activity68Rank>> serverTypeMap = ACTIVITY_68_RANK.computeIfAbsent(serverType, x -> Maps.newHashMap());
        String activityKey = String.format(KEY_FORMAT, activityType, data, openDate);
        ConsistentLadderRankCache<Long, Activity68Rank> rankCache = serverTypeMap.computeIfAbsent(activityKey, k -> new ConsistentLadderRankCache<>(RANK_LIMIT, Short.MAX_VALUE));
        long actorId = Long.parseLong(attribute.get((byte) TypeProtocol.ActorFieldType.ACTOR_ID_VALUE).toString());
        Activity68Rank rank = rankCache.find(actorId);
        if (rank == null) {
            rank = Activity68Rank.valueOf(actorId, attribute, times, System.currentTimeMillis());
        } else {
            rank.refresh(attribute, times);
        }
        rankCache.achieve(rank);
        if (rankCache.find(actorId) != null) {
            String cacheKey = this.getRankCacheKey(serverType, activityKey);
            Activity68RankEntity activity68RankEntity = Activity68RankEntity.valueOf(rank.getAttributes(), rank.getTimes(), rank.getAchieveTime());
            putHashObject(cacheKey, JSONObject.toJSONString(actorId), activity68RankEntity);
        }
    }


    @Override
    public Collection<Activity68RankVO> getActivity68RankList(int serverType, int activityType, int data, String openDate, long rankLimit, int rankTimesLimit) {
        String activityKey = String.format(KEY_FORMAT, activityType, data, openDate);
        Map<String, ConsistentLadderRankCache<Long, Activity68Rank>> serverTypeMap = ACTIVITY_68_RANK.computeIfAbsent(serverType, x -> Maps.newHashMap());
        ConsistentLadderRankCache<Long, Activity68Rank> rankCache = serverTypeMap.get(activityKey);
        Collection<Activity68RankVO> ranks = Lists.newArrayList();
        if (rankCache == null) {
            return ranks;
        }
        int top3num = 0;
        List<Activity68Rank> settleRanks = rankCache.findSettleRanks(3);
        for (Activity68Rank settleRank : settleRanks) {
            if (settleRank.getTimes() >= rankTimesLimit) {
                top3num++;
            }
        }
        long size = Math.min(rankCache.findAll().size(), rankLimit);
        for (long i = 1; i <= size; i++) {
            Activity68Rank rank = rankCache.findByRank(i);
            if(top3num == TOP_RANK){
                ranks.add(Activity68RankVO.valueOf(rank.getAttributes(), rank.getTimes(), rank.getRank()));
            }else {
                if (rank.getTimes() >= rankTimesLimit) {
                    ranks.add(Activity68RankVO.valueOf(rank.getAttributes(), rank.getTimes(), rank.getRank()));
                }else {
                    ranks.add(Activity68RankVO.valueOf(rank.getAttributes(), rank.getTimes(), rank.getRank() + TOP_RANK - top3num));
                }
            }
        }
        return ranks;
    }

    @Override
    public void activity68ReportRecord(int serverType, int activityType, int data, String openDate, Map<Byte, Object> actorAttributeMap,
        Collection<RewardObject> rewardList, String des) {
        Activity68ReportVO activity68ReportVO = Activity68ReportVO.valueOf(actorAttributeMap, rewardList, des);
        String activityKey = String.format(KEY_FORMAT, activityType, data, openDate);
        CopyOnWriteArrayList<Activity68ReportVO> activity68ReportVOS =
            ACTIVITY_68_REPORT_CACHE.computeIfAbsent(activityKey, x -> Lists.newCopyOnWriteArrayList());
        if (activity68ReportVOS.size() >= REPORT_LIMIT) {
            activity68ReportVOS.remove(0);
        }
        activity68ReportVOS.add(activity68ReportVO);
        String key = getReportCacheKey(serverType, activityKey);
        delete(key);
        addListObject(key, activity68ReportVOS);
    }

    @Override
    public Collection<Activity68ReportVO> getActivity68ReportRecord(int serverType, int activityType, int data, String activityOpenDate) {
        Collection<Activity68ReportVO> activity68ReportVOs = Lists.newArrayList();
        CopyOnWriteArrayList<Activity68ReportVO> reportVOS =
            ACTIVITY_68_REPORT_CACHE.getOrDefault(String.format(KEY_FORMAT, activityType, data, activityOpenDate), new CopyOnWriteArrayList<>());
        activity68ReportVOs.addAll(reportVOS);
        return activity68ReportVOs;

    }

    private String getRankCacheKey(int serverType, String key) {
        return String.format(ACTIVITY_68_RANK_CACHE_KEY, serverType, key);
    }

    private String getReportCacheKey(int serverType, String key) {
        return String.format(ACTIVITY_68_REPORT_CACHE_KEY, serverType, key);
    }

    @Override
    public void onApplicationEvent(ApplicationServerZoneCompleteEvent applicationServerZoneCompleteEvent) {
        try {
            Map<Integer, Map<Integer, List<Integer>>> serverRegionMap = ServerZoneHelper.getAllRegionConfig();
            for (int serverType : serverRegionMap.keySet()) {
                // 初始化排行榜缓存
                String worldKey = String.format(ACTIVITY_68_RANK_CACHE_KEY, serverType, "*");
                Collection<String> keys = getKeys(worldKey);
                for (String key : keys) {
                    Collection<Activity68RankEntity> ranks = getHashValues(key, Activity68RankEntity.class);
                    Map<String, ConsistentLadderRankCache<Long, Activity68Rank>> serverTypeRankMap =
                        ACTIVITY_68_RANK.computeIfAbsent(serverType, x -> Maps.newHashMap());
                    String activityKey = getActivityKey(key);
                    ConsistentLadderRankCache<Long, Activity68Rank> rankCache =
                        serverTypeRankMap.computeIfAbsent(activityKey, k -> new ConsistentLadderRankCache<>(RANK_LIMIT, Short.MAX_VALUE));
                    for (Activity68RankEntity rank : ranks) {
                        rankCache.achieve(Activity68Rank.valueOf(rank.getActorId(), rank.getAttributes(), rank .getTimes(), rank.getAchieveTime()));
                    }
                }
                // 初始化中奖记录缓存
                String reportKey = String.format(ACTIVITY_68_REPORT_CACHE_KEY, serverType, "*");
                Collection<String> reportKeys = getKeys(reportKey);
                for (String key : reportKeys) {
                    Collection<Activity68ReportVO> reportVOs = getListObject(key,  Activity68ReportVO.class);
                    if (reportVOs == null) {
                        continue;
                    }
                    String activityKey = getActivityKey(key);
                    CopyOnWriteArrayList<Activity68ReportVO> activity68ReportVOS = ACTIVITY_68_REPORT_CACHE.computeIfAbsent(activityKey, x -> Lists.newCopyOnWriteArrayList());
                    activity68ReportVOS.addAll(reportVOs);
                }
            }
        } catch (Exception e) {
            LOGGER.error("{}", e);
        }
    }

    private String getActivityKey(String key){
        String[] cacheKeys = key.split("_");
        String openDate = cacheKeys[cacheKeys.length - 1];
        String data = cacheKeys[cacheKeys.length - 2];
        String activityType = cacheKeys[cacheKeys.length - 3];
        return String.format(KEY_FORMAT, activityType, data, openDate);
    }
}
