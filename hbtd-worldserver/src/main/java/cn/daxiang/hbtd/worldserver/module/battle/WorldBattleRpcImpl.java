package cn.daxiang.hbtd.worldserver.module.battle;

import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.rpc.event.BattlePVEVerifyEvent;
import cn.daxiang.framework.rpc.event.BattlePVEVerifyResultEvent;
import cn.daxiang.framework.rpc.event.CrossBattleEndEvent;
import cn.daxiang.framework.rpc.event.CrossBattleEvent;
import cn.daxiang.framework.rpc.world.WorldBattleRpc;
import cn.daxiang.hbtd.worldserver.core.rpc.RpcClientChannel;
import cn.daxiang.shared.module.battle.BattleEntity;
import cn.daxiang.shared.module.battle.BattleParameterKey;
import cn.daxiang.shared.module.battle.BattleType;
import cn.daxiang.shared.type.GameServerType;
import cn.daxiang.shared.type.PVEVerifyParameterKey;
import cn.daxiang.shared.type.PVEVerifyType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Map;

import static cn.daxiang.shared.GameModuleStatusCodeConstant.SERVER_STATE_ERROR;

/**
 * <AUTHOR>
 * @date 2020/6/3
 */
@Component
public class WorldBattleRpcImpl implements WorldBattleRpc {
    private final Logger LOGGER = LoggerFactory.getLogger(WorldBattleRpcImpl.class);

    @Override
    public Result compete(int serverType, BattleEntity battleEntity, int targetServerId, long targetId) {
        if (RpcClientChannel.getChannel(serverType, targetServerId) == null) {
            return Result.valueOf(SERVER_STATE_ERROR);
        }
        RpcClientChannel.pushNodeEvent(serverType, targetServerId, new CrossBattleEvent(targetId, BattleType.COMPETE, battleEntity));
        return Result.valueOf();
    }

    @Override
    public void pushCompeteResult(int serverType, int serverId, long actorId, boolean isWin, byte[] responseBytes, Map<BattleParameterKey, Object> parameterMap) {
        RpcClientChannel.pushNodeEvent(serverType, serverId, new CrossBattleEndEvent(actorId, responseBytes, isWin, parameterMap, BattleType.COMPETE));
    }

    @Override
    public void checkPVE(int serverType, int serverId, long actorId, PVEVerifyType type, Map<PVEVerifyParameterKey, Object> parameter) {
        if (!RpcClientChannel.checkCommonServer(GameServerType.VERIFY_SERVER_TYPE)) {
            parameter.put(PVEVerifyParameterKey.VERIFY_RESULT, false);
            RpcClientChannel.pushNodeEvent(serverType, serverId, new BattlePVEVerifyResultEvent(actorId, type, parameter));
        } else {
            RpcClientChannel.pushCommonServerEvent(GameServerType.VERIFY_SERVER_TYPE, new BattlePVEVerifyEvent(serverType, serverId, actorId, type, parameter));
        }
    }

    @Override
    public void checkPVEResult(int serverType, int serverId, long actorId, PVEVerifyType type, Map<PVEVerifyParameterKey, Object> parameter) {
        RpcClientChannel.pushNodeEvent(serverType, serverId, new BattlePVEVerifyResultEvent(actorId, type, parameter));
    }
}
