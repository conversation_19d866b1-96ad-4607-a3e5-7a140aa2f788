package cn.daxiang.hbtd.worldserver.module.chess.model.entity;

import cn.daxiang.shared.module.chess.model.entity.MonsterLineup;

import java.util.Map;

/**
 * 珍珑棋局信息
 *
 * <AUTHOR>
 * @date 2023/5/13
 */
public class ChessInfo {
    /**
     * 珍珑棋局循环ID
     */
    private int circulate;
    /**
     * 珍珑棋局怪物阵容模板
     */
    private Map<Integer, MonsterLineup> monsterLineupMap;
    /**
     * 下一次重置时间
     */
    private long nextRefreshTime;

    public static ChessInfo valueOf(int circulate, Map<Integer, MonsterLineup> monsterLineupMap, long nextRefreshTime) {
        ChessInfo chessInfo = new ChessInfo();
        chessInfo.circulate = circulate;
        chessInfo.monsterLineupMap = monsterLineupMap;
        chessInfo.nextRefreshTime = nextRefreshTime;
        return chessInfo;
    }

    public int getCirculate() {
        return circulate;
    }

    public void setCirculate(int circulate) {
        this.circulate = circulate;
    }

    public Map<Integer, MonsterLineup> getMonsterLineupMap() {
        return monsterLineupMap;
    }

    public void setMonsterLineupMap(Map<Integer, MonsterLineup> monsterLineupMap) {
        this.monsterLineupMap = monsterLineupMap;
    }

    public long getNextRefreshTime() {
        return nextRefreshTime;
    }

    public void setNextRefreshTime(long nextRefreshTime) {
        this.nextRefreshTime = nextRefreshTime;
    }
}
