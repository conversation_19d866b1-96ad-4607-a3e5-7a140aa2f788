package cn.daxiang.hbtd.worldserver.module.capture.dao;

import cn.daxiang.shared.module.capture.Capture;
import cn.daxiang.shared.module.capture.CaptureCity;

import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2022/7/5
 */
public interface CaptureDao {
    /**
     * 获取攻城略地信息
     *
     * @return
     */
    Capture getCapture();

    /**
     * 刷新攻城略地信息
     *
     * @param capture
     */
    void refreshCapture(Capture capture);

    /**
     * 获取所有攻城略地城池信息
     *
     * @return
     */
    Map<Integer, Map<Integer, Map<Integer, CaptureCity>>> getCaptureCityMap();

    /**
     * 获取区域攻城略地城池信息
     *
     * @param serverType
     * @param areaId
     * @return
     */
    Map<Integer, CaptureCity> getAreaCaptureCityMap(int serverType, int areaId);

    /**
     * 获取攻城略地诚信信息
     *
     * @param serverType
     * @param areaId
     * @param cityId
     * @return
     */
    Optional<CaptureCity> getCaptureCity(int serverType, int areaId, int cityId);

    /**
     * 刷新攻城略地城池信息
     *
     * @param serverType
     * @param areaId
     * @param captureCity
     */
    void refreshCaptureCity(int serverType, int areaId, CaptureCity captureCity);

    /**
     * 清空攻城略地城池信息
     */
    void resetCaptureCity();
}
