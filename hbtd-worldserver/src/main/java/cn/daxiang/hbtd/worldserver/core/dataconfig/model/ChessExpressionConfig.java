package cn.daxiang.hbtd.worldserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.identity.IdentiyKey;

/**
 * 珍珑棋局武将抽卡
 *
 * <AUTHOR>
 * @date 2023/4/26
 */
@DataFile(fileName = "chess_expression_config")
public class ChessExpressionConfig implements ModelAdapter {
    /**
     * 配置Id
     */
    private int id;
    /**
     * 抽取规则表达式
     * x1：武将ID-roleId
     * x2：阵营-camp（1蜀 2魏 3吴 4群雄 5穿越
     * ）"
     * x3：回合职业-features（1.防御型 2.技能型 3.攻击型）
     * x4：品质-quality（1蓝2紫3橙4红5金6赤金）
     * x5：资质-aptitude
     * x6：是否稀有-aptitude（0不是稀有，1是红色稀有，2是金色稀有 ）
     * x7：性别-sex（0女 1男）
     */
    private String expression;

    @Override
    public void initialize() {

    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(id);
    }

    public int getId() {
        return id;
    }

    public String getExpression() {
        return expression;
    }
}
