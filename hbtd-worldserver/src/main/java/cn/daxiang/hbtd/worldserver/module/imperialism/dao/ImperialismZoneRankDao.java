package cn.daxiang.hbtd.worldserver.module.imperialism.dao;

import cn.daxiang.framework.utils.rank.cache.ILineRankToProvider;
import cn.daxiang.hbtd.worldserver.module.imperialism.model.ImperialismZoneRank;
import cn.daxiang.protocol.game.ImperialismProtocol.ImperialismRankType;

import java.util.Collection;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/11/13
 */
public interface ImperialismZoneRankDao extends ILineRankToProvider<Long, ImperialismZoneRank> {
    /**
     * 获取决战皇城小战区排名
     *
     * @param actorId
     * @return
     */
    ImperialismZoneRank getImperialismZoneRank(long actorId);

    /**
     * 刷新决战皇城小战区排名
     *
     * @param serverType
     * @param rank
     */
    void refreshImperialismZoneRank(int serverType, ImperialismZoneRank rank);

    /**
     * 获取决战皇城排名
     *
     * @param serverZone
     * @param rank
     * @return
     */
    ImperialismZoneRank getImperialismZoneRankByRank(int serverZone, long rank);

    /**
     * 处理战斗结果
     *
     * @param zoneId
     * @param challengeAttributes
     * @param type
     * @param rank
     * @param opponentAttributes
     * @param isWin
     * @param battleData
     */
    void battleReport(int serverType, int zoneId, Map<Byte, Object> challengeAttributes, ImperialismRankType type, long rank, Map<Byte, Object> opponentAttributes, boolean isWin,
        byte[] battleData);

    /**
     * 记录决战皇城本服与战区战报
     *
     * @param serverType
     * @param zoneId
     * @param actorId
     * @param battleReport
     */
    void recordBattleReport(int serverType, int zoneId, long actorId, byte[] battleReport);

    /**
     * 获取战报列表
     *
     * @param serverType
     * @param zoneId
     * @param actorId
     * @return
     */
    Collection<byte[]> getBattleReport(int serverType, int zoneId, long actorId);
}
