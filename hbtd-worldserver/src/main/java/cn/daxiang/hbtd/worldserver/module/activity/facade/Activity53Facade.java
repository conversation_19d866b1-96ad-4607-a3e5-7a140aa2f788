package cn.daxiang.hbtd.worldserver.module.activity.facade;

import cn.daxiang.shared.module.activity.Activity53RankVO;

import java.util.Collection;

/**
 * <AUTHOR>
 * @date 2023/12/28
 */
public interface Activity53Facade {
    /**
     * 天赐神将排行榜初始化
     *
     * @param serverType
     * @param serverId
     * @param activityType
     * @param data
     * @param openDate
     * @param rankLimit
     */
    void activity53Open(int serverType, int serverId, int activityType, int data, String openDate, long rankLimit);

    /**
     * 刷新天赐神将排行榜
     *
     * @param serverType
     * @param serverId
     * @param activityType
     * @param data
     * @param openDate
     * @param rankVO
     */
    void refreshActivity53Rank(int serverType, int serverId, int activityType, int data, String openDate, Activity53RankVO rankVO);

    /**
     * 获取天赐神将排行榜
     *
     * @param serverType
     * @param serverId
     * @param activityType
     * @param data
     * @param openDate
     * @param rankLimit
     * @return
     */
    Collection<Activity53RankVO> getActivity53RankList(int serverType, int serverId, int activityType, int data, String openDate, long rankLimit);
}
