package cn.daxiang.hbtd.worldserver.module.gvg.dao;

import cn.daxiang.hbtd.worldserver.module.gvg.model.GVGActorRank;
import cn.daxiang.hbtd.worldserver.module.gvg.model.GVGCityInfo;
import cn.daxiang.hbtd.worldserver.module.gvg.model.GVGNationInfo;
import cn.daxiang.hbtd.worldserver.module.gvg.model.GVGNationRank;
import cn.daxiang.shared.module.gvg.GVGActorInfo;
import cn.daxiang.shared.module.gvg.GVGBrawlingChallengeReportEntity;
import cn.daxiang.shared.module.gvg.GVGStateEntity;

import java.util.Collection;
import java.util.Map;

/**
 * @author: Gary
 * @date: 2022/12/22 16:43
 * @Description:
 */
public interface WorldGVGDao {
    /**
     * 获取城池状态
     *
     * @return
     */
    GVGStateEntity getGVGStateEntity();

    /**
     * 获取城池信息
     *
     * @param serverType
     * @param zoneId
     * @return
     */
    Collection<GVGCityInfo> getCityInfos(int serverType, int zoneId);

    /**
     * 获取单个城池
     *
     * @param serverType
     * @param zoneId
     * @param cityId
     * @return
     */
    GVGCityInfo getCityInfoByCityId(int serverType, int zoneId, int cityId);

    /**
     * 刷新城池信息
     *
     * @param serverType
     * @param zoneId
     * @param cityInfo
     */
    void refreshCityInfo(int serverType, int zoneId, GVGCityInfo cityInfo);

    /**
     * 获取单个玩家信息
     *
     * @param sererType
     * @param zoneId
     * @param actorId
     * @return
     */
    GVGActorInfo getActorInfo(int sererType, int zoneId, long actorId);

    /**
     * 玩家信息更新
     *
     * @param serverType
     * @param zoneId
     * @param infos
     */
    void updateActorInfo(int serverType, int zoneId, Collection<GVGActorInfo> infos);

    /**
     * 获取军团排行榜
     *
     * @param serverType
     * @param zoneId
     * @return
     */
    Collection<GVGNationRank> getNationRankList(int serverType, int zoneId);

    /**
     * 刷新排行榜
     *
     * @param serverType
     * @param zoneId
     * @param attributes
     * @param addScore
     */
    void achieveRank(int serverType, int zoneId, Map<Byte, Object> attributes, long addScore);

    /**
     * 刷新王城乱斗战神排行榜
     *
     * @param serverType
     * @param zoneId
     * @param attributes
     * @param addScore
     */
    void achieveBrawlingRank(int serverType, int zoneId, Map<Byte, Object> attributes, long addScore);

    /**
     * 获取玩家排行榜
     *
     * @param serverType
     * @param zoneId
     * @return
     */
    Collection<GVGActorRank> getActorRankList(int serverType, int zoneId);

    /**
     * 获取玩家战神排行榜
     *
     * @param serverType
     * @param zoneId
     * @return
     */
    Collection<GVGActorRank> getBrawlingActorRankList(int serverType, int zoneId);

    /**
     * 根据城池Id获取玩家信息
     *
     * @param serverType
     * @param zoneId
     * @param cityId
     * @return
     */
    Collection<GVGActorInfo> getActorInfoListByCityId(int serverType, int zoneId, int cityId);

    /**
     * @param serverType
     * @param zoneId
     * @param nationId
     * @return
     */
    GVGNationInfo getGVGNationInfo(int serverType, int zoneId, long nationId);

    /**
     * 获取上一赛季军团排名名
     *
     * @param serverType
     * @param zoneId
     * @param nationId
     * @return
     */
    long getLastSeasonNationRank(int serverType, int zoneId, long nationId);

    /**
     * 根据排名获取玩家排行榜信息
     *
     * @param serverType
     * @param zoneId
     * @param rank
     * @return
     */
    GVGActorRank getActorRankByRank(int serverType, int zoneId, long rank);

    /**
     * 根据排名获取玩家战神排行榜信息
     *
     * @param serverType
     * @param zoneId
     * @param rank
     * @return
     */
    GVGActorRank getBrawlingActorRankByRank(int serverType, int zoneId, long rank);

    /**
     * 根据玩家Id获取玩家排行信息
     *
     * @param serverType
     * @param zoneId
     * @param actorId
     * @return
     */
    GVGActorRank getActorRankById(int serverType, int zoneId, long actorId);

    /**
     * 根据玩家Id获取玩家战神排行信息
     *
     * @param serverType
     * @param zoneId
     * @param actorId
     * @return
     */
    GVGActorRank getBrawlingActorRankById(int serverType, int zoneId, long actorId);

    /**
     * 王城乱斗是否开启
     */
    boolean brawlingIsOpen();

    /**
     * 战报更新
     */
    void updateBrawlingBattleRecordEntity(int serverType, int zoneId, int type, Map<Byte, Object> attackAttributes, Map<Byte, Object> targetAttributes, long score, int multiple);

    /**
     * 获取王城乱斗战报
     *
     * @param serverType
     * @param zoneId
     * @return
     */
    Collection<GVGBrawlingChallengeReportEntity> getGVGBrawlingReportList(int serverType, int zoneId);

    /**
     * 获取王城之争上一赛季军团排行榜信息
     *
     * @param serverType
     * @param zoneId
     * @return
     */
    Collection<GVGNationRank> getLastSeasonGVGNationRanks(int serverType, int zoneId);

    /**
     * 获取上个赛季王城乱斗战神排行榜
     *
     * @param serverType
     * @param zoneId
     * @return
     */

    Collection<GVGActorRank> getLastSeasonGVGBrawlingActorRanks(int serverType, int zoneId);

    /**
     * 刷新王城之争玩法解锁条件
     *
     * @param zoneOpenDayLimit
     */
    void refreshZoneOpenDayLimit(int zoneOpenDayLimit);
}
