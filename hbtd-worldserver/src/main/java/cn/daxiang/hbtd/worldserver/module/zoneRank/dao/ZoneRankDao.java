package cn.daxiang.hbtd.worldserver.module.zoneRank.dao;

import cn.daxiang.shared.module.zoneRank.ZoneRankVO;

import java.util.Collection;

/**
 * <AUTHOR>
 * @date 2023/7/4
 */
public interface ZoneRankDao {
    /**
     * 获得战区排行榜
     *
     * @param serverType
     * @param zoneId
     * @param type
     * @param rankVO
     * @return
     */
    Collection<ZoneRankVO> getZoneRankList(int serverType, int zoneId, int type, ZoneRankVO rankVO);

    /**
     * 获得玩家战力战区排行榜
     *
     * @param serverType
     * @param zoneId
     * @param rankVO
     * @return
     */
    Collection<ZoneRankVO> getPowerZoneRankList(int serverType, int zoneId, ZoneRankVO rankVO);

    /**
     * 点赞玩家战力战区排行榜
     *
     * @param serverType
     * @param zoneId
     * @param rank
     */
    void praisePowerZoneRank(int serverType, int zoneId, long rank);

    /**
     * 刷新战区排行榜
     *
     * @param serverType
     * @param zoneId
     * @param type
     * @param rankVO
     */
    void refreshZoneRank(int serverType, int zoneId, int type, ZoneRankVO rankVO);

    /**
     * 推送排行榜
     *
     * @param serverType
     * @param zoneId
     * @param type
     * @param zoneRankList
     */
    void pushRanks(int serverType, int zoneId, int type, Collection<ZoneRankVO> zoneRankList);

}
