package cn.daxiang.hbtd.worldserver.module.recruit.dao.impl;

import cn.daxiang.dto.model.RecruitRecordVO;
import cn.daxiang.framework.event.annotation.Event;
import cn.daxiang.framework.utils.StopWatch;
import cn.daxiang.hbtd.worldserver.core.context.ApplicationServerZoneCompleteEvent;
import cn.daxiang.hbtd.worldserver.core.dataconfig.service.WorldGlobalConfigService;
import cn.daxiang.hbtd.worldserver.core.event.impl.WorldServerZoneMergeEvent;
import cn.daxiang.hbtd.worldserver.core.event.impl.WorldServerZoneRefreshEvent;
import cn.daxiang.hbtd.worldserver.core.redis.RedisDao;
import cn.daxiang.hbtd.worldserver.module.recruit.dao.RecruitDao;
import cn.daxiang.hbtd.worldserver.module.recruit.model.entity.RecruitRecord;
import cn.daxiang.hbtd.worldserver.module.server.ServerZoneHelper;
import cn.daxiang.shared.GlobalConfigKey;
import cn.daxiang.shared.event.EventKey;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/8/8
 */
@Component
public class RecruitDaoImpl extends RedisDao implements RecruitDao, ApplicationListener<ApplicationServerZoneCompleteEvent> {
    /**
     * redis缓存key
     */
    private static final String REDIS_CACHE_KEY = "RECRUIT_RECORD_%s_%s";
    /**
     * key:serverType,value:{key:zoneId,value:{Collection<RecruitRecord>}}
     */
    private static Map<Integer, Map<Integer, List<RecruitRecord>>> RECRUIT_RECORD_CACHE_MAP = Maps.newConcurrentMap();
    @Autowired
    private WorldGlobalConfigService globalConfigService;

    @Override
    public void onApplicationEvent(ApplicationServerZoneCompleteEvent event) {
        StopWatch sw = new StopWatch(true);
        this.initialize();
        this.loadData();
        sw.stop();
        LOGGER.info("fund loading complete!  time:{}ms", sw.runTime());
    }

    private void initialize() {
        for (Map.Entry<Integer, Map<Integer, List<Integer>>> mapEntry : ServerZoneHelper.getAllZoneConfig().entrySet()) {
            int serverType = mapEntry.getKey();
            Map<Integer, List<Integer>> zoneServerIdMap = mapEntry.getValue();
            Map<Integer, List<RecruitRecord>> serverTypeMap = RECRUIT_RECORD_CACHE_MAP.get(serverType);
            if (serverTypeMap == null) {
                serverTypeMap = Maps.newHashMap();
                RECRUIT_RECORD_CACHE_MAP.put(serverType, serverTypeMap);
            }
            for (Integer zoneId : zoneServerIdMap.keySet()) {
                List<RecruitRecord> recordCache = serverTypeMap.get(zoneId);
                if (recordCache == null) {
                    recordCache = Lists.newCopyOnWriteArrayList();
                    serverTypeMap.put(zoneId, recordCache);
                }
            }
        }
    }

    private void loadData() {
        Collection<String> keys = getKeys(REDIS_CACHE_KEY.substring(0, REDIS_CACHE_KEY.indexOf('%')) + "*");
        if (!keys.isEmpty()) {
            for (String key : keys) {
                List<RecruitRecord> zoneNameList = getListObject(key, RecruitRecord.class);
                int serverType = Integer.valueOf(key.split("_")[2]);
                int zoneId = Integer.valueOf(key.split("_")[3]);
                List<RecruitRecord> recordCache = RECRUIT_RECORD_CACHE_MAP.get(serverType).get(zoneId);
                recordCache.addAll(zoneNameList);
            }
        }
    }

    @Event(name = EventKey.WORLD_SERVER_ZONE_REFRESH_EVENT)
    public void onWorldServerZoneRefreshEvent(WorldServerZoneRefreshEvent event) {
        this.initialize();
    }

    @Event(name = EventKey.WORLD_SERVER_ZONE_MERGE_EVENT)
    public void onWorldServerZoneMergeEvent(WorldServerZoneMergeEvent event) {
        this.initialize();
        for (Map.Entry<Integer, Map<Integer, Integer>> serverTypeEntry : event.getRefreshZoneMap().entrySet()) {
            int serverType = serverTypeEntry.getKey();
            Map<Integer, List<RecruitRecord>> zoneFundTypeBuyTimesMap = RECRUIT_RECORD_CACHE_MAP.get(serverType);
            for (Map.Entry<Integer, Integer> zoneEntry : serverTypeEntry.getValue().entrySet()) {
                int oldZoneId = zoneEntry.getKey();
                zoneFundTypeBuyTimesMap.remove(oldZoneId);
                clearSortSet(getRedisCacheKey(serverType, oldZoneId));
            }
        }

    }

    @Override
    public Collection<RecruitRecordVO> getRecruitRecord(int serverType, int zoneId) {
        Map<Integer, List<RecruitRecord>> zoneIdRecruitRecordMap = RECRUIT_RECORD_CACHE_MAP.get(serverType);
        if (zoneIdRecruitRecordMap == null) {
            return Collections.emptyList();
        }
        List<RecruitRecord> recruitRecordList = zoneIdRecruitRecordMap.get(zoneId);
        if (recruitRecordList == null) {
            return Collections.emptyList();
        }
        Collection<RecruitRecordVO> voList = Lists.newLinkedList();
        for (RecruitRecord record : recruitRecordList) {
            voList.add(RecruitRecordVO.valueOf(record.getServerId(), record.getName(), record.getRewardList(), record.getDes()));
        }
        return voList;
    }

    @Override
    public void setRecruitRecord(int serverType, int zoneId, Collection<RecruitRecordVO> voList) {
        Map<Integer, List<RecruitRecord>> zoneIdRecruitRecordMap = RECRUIT_RECORD_CACHE_MAP.get(serverType);
        if (zoneIdRecruitRecordMap == null) {
            return;
        }
        List<RecruitRecord> recruitRecordList = zoneIdRecruitRecordMap.get(zoneId);
        if (recruitRecordList == null) {
            return;
        }
        int limit = globalConfigService.findGlobalConfig(GlobalConfigKey.RECRUIT_RECORD_MAX).findInt();
        String cacheKey = getRedisCacheKey(serverType, zoneId);
        for (RecruitRecordVO recordVO : voList) {
            RecruitRecord record = RecruitRecord.valueOf(recordVO);
            if (recruitRecordList.size() >= limit) {
                recruitRecordList.remove(0);
            }
            recruitRecordList.add(record);
        }
        //以下代码目的为当配置改小的时候，能删除多余记录
        if (recruitRecordList.size() > limit) {
            int times = recruitRecordList.size() - limit;
            for (int i = 0; i < times; i++) {
                recruitRecordList.remove(0);
            }
        }
        clearSortSet(cacheKey);
        addListObject(cacheKey, recruitRecordList);
    }

    private String getRedisCacheKey(int serverType, int zoneId) {
        return String.format(REDIS_CACHE_KEY, serverType, zoneId);
    }

}
