package cn.daxiang.hbtd.worldserver.core.dataconfig.convert.impl;

import cn.daxiang.hbtd.worldserver.core.dataconfig.convert.TreeMapConfig;

import java.util.Collection;
import java.util.Map.Entry;
import java.util.TreeMap;

public class LongIntTreeMapConfig extends TreeMapConfig<Long, Integer> {

    @Override
    protected int fromSize() {
        return 2;
    }

    @Override
    protected Long fromKey(String[] array) {
        return Long.valueOf(array[0]);
    }

    @Override
    protected Integer fromValue(String[] array) {
        return Integer.valueOf(array[1]);
    }

    public int getCeilingValue(long key) {
        if (key > cache.lastKey()) {
            key = cache.lastKey();
        }
        return cache.ceilingEntry(key).getValue();
    }

    public Collection<Long> getKeySet() {
        return cache.keySet();
    }

    public int getFloorValue(long key) {
        if (key < cache.firstKey()) {
            key = cache.firstKey();
        }
        return cache.floorEntry(key).getValue();
    }

    public long getKey(int value) {
        for (Entry<Long, Integer> entry : cache.entrySet()) {
            if (entry.getValue() == value) {
                return entry.getKey();
            }
        }
        return cache.firstKey();
    }

    public int getNextValue(long damage) {
        for (Entry<Long, Integer> entry : cache.entrySet()) {
            if (entry.getKey() > damage) {
                return entry.getValue();
            }
        }
        return cache.lastEntry().getValue();
    }

    public int getPreValue(long damage) {
        TreeMap<Long, Integer> descMap = new TreeMap<>((o1, o2) -> o2.compareTo(o1));
        descMap.putAll(cache);
        for (Entry<Long, Integer> entry : descMap.entrySet()) {
            if (entry.getKey() < damage) {
                return entry.getValue();
            }
        }
        return descMap.firstEntry().getValue();
    }

    public long getLastKey() {
        return cache.lastKey();
    }

}
