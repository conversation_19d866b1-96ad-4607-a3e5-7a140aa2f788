package cn.daxiang.hbtd.worldserver.module.imperialism.facade;

import cn.daxiang.framework.result.CollectionResult;
import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.protocol.game.ImperialismProtocol.ImperialismRankType;
import cn.daxiang.shared.module.battle.BattleEntity;
import cn.daxiang.shared.reward.RewardObject;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/11/15
 */
public interface ImperialismFacade {
    /**
     * 获取公共信息
     *
     * @param serverId
     * @param actorId
     * @return
     */
    TResult<byte[]> getGlobal(int serverId, long actorId);

    /**
     * 获取决战皇城宫殿信息
     *
     * @param serverId
     * @param actorId
     * @param id
     * @return
     */
    TResult<byte[]> getImperialismPalace(int serverId, long actorId, long id);

    /**
     * 获取决战皇城宫殿个人信息
     *
     * @param serverId
     * @param actorId
     * @param attributes
     * @param prerogative
     * @return
     */
    TResult<byte[]> getImperialismPalaceActorResponse(int serverId, long actorId, Map<Byte, Object> attributes, boolean prerogative);

    /**
     * 刷新小战区目标
     *
     * @param serverId
     * @param actorId
     * @param lastRank
     * @return
     */
    TResult<byte[]> refreshZoneTarget(int serverId, long actorId, long lastRank);

    /**
     * 挑战
     *
     * @param serverId
     * @param actorId
     * @param battleEntity
     * @param type
     * @param rank
     * @return
     */
    Result challenge(int serverId, long actorId, BattleEntity battleEntity, ImperialismRankType type, long rank);

    /**
     * 战斗上报
     *
     * @param serverId
     * @param challengeAttributes
     * @param type
     * @param rank
     * @param opponentAttributes
     * @param isWin
     * @param battleData
     */
    void battleReport(int serverId, Map<Byte, Object> challengeAttributes, ImperialismRankType type, long rank, Map<Byte, Object> opponentAttributes, boolean isWin,
        byte[] battleData);

    /**
     * 上报本服站战报
     *
     * @param serverId
     * @param type
     * @param challenge
     * @param opponent
     * @param data
     */
    void reportBattleReport(int serverId, ImperialismRankType type, long challenge, long opponent, byte[] data);

    /**
     * 上报特权购买信息
     *
     * @param serverId
     * @param actorId
     * @param attributes
     */
    void reportPrerogative(int serverId, long actorId, Map<Byte, Object> attributes);

    /**
     * 追随
     *
     * @param serverId
     * @param actorId
     * @param id
     * @return
     */
    Result follow(int serverId, long actorId, long id);

    /**
     * 申请护佑
     *
     * @param serverId
     * @param actorId
     * @param level
     * @param id
     * @return
     */
    Result apply(int serverId, long actorId, int level, long id);

    /**
     * 殿主-申请通过
     *
     * @param serverId
     * @param actorId
     * @param targetId
     * @return
     */
    Result approve(int serverId, long actorId, long targetId);

    /**
     * 殿主-申请拒绝
     *
     * @param serverId
     * @param actorId
     * @param targetId
     * @return
     */
    Result reject(int serverId, long actorId, long targetId);

    /**
     * 殿主-设置等级限制
     *
     * @param serverId
     * @param actorId
     * @param level
     * @return
     */
    Result levelLimit(int serverId, long actorId, int level);

    /**
     * 殿主-设置军团免审
     *
     * @param serverId
     * @param actorId
     * @param audit
     * @return
     */
    Result audit(int serverId, long actorId, boolean audit);
    /**
     * 殿主-设置全员免审
     *
     * @param serverId
     * @param actorId
     * @param allAudit
     * @return
     */
    Result allAudit(int serverId, long actorId, boolean allAudit);

    /**
     * 殿主-踢出
     *
     * @param serverId
     * @param actorId
     * @param targetId
     * @return
     */
    Result kickOff(int serverId, long actorId, long targetId);

    /**
     * 殿主-领取分成奖励
     *
     * @param serverId
     * @param actorId
     * @return
     */
    CollectionResult<RewardObject> receiveSharedReward(int serverId, long actorId);

    /**
     * 护佑/跟随结算
     *
     * @param serverId
     * @param actorId
     * @return
     */
    CollectionResult<RewardObject> settlement(int serverId, long actorId);

    /**
     * 获取排名
     *
     * @param serverId
     * @param actorId
     * @param attributes
     * @param type
     * @param begin
     * @param end
     * @param count
     * @return
     */
    TResult<byte[]> getRanks(int serverId, long actorId, Map<Byte, Object> attributes, ImperialismRankType type, long begin, long end, int count);

    /**
     * 获取战报
     *
     * @param serverId
     * @param actorId
     * @param type
     * @return
     */
    TResult<byte[]> getBattleReport(int serverId, long actorId, ImperialismRankType type);

    /**
     * 点赞
     *
     * @param serverId
     * @param rank
     * @param count
     */
    Result praise(int serverId, long rank, long count);
}
