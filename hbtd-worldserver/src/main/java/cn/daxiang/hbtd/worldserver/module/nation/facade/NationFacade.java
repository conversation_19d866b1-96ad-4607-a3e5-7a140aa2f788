package cn.daxiang.hbtd.worldserver.module.nation.facade;

import cn.daxiang.dto.response.NationMemberInfoResponse;
import cn.daxiang.framework.extend.KeyValue;
import cn.daxiang.framework.result.CollectionResult;
import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.shared.module.nation.Nation;
import cn.daxiang.shared.module.nation.NationPowerRankVO;
import cn.daxiang.shared.module.nation.entity.NationDungeonActorDamageRankResponse;
import cn.daxiang.shared.module.nation.entity.NationDungeonNationDamageRankResponse;
import cn.daxiang.shared.module.nation.entity.NationLevelRankResponse;
import cn.daxiang.shared.module.nation.entity.NationListEntity;
import cn.daxiang.shared.module.nation.entity.NationMemberListEntity;
import cn.daxiang.shared.module.nation.entity.NationPowerRankResponse;
import cn.daxiang.shared.reward.RewardObject;
import cn.daxiang.shared.type.NationLimitType;
import cn.daxiang.shared.type.NationMemberType;

import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface NationFacade {

    /**
     * 同步国家信息
     *
     * @param serverType
     * @param serverId
     * @param actorId
     * @return
     */
    KeyValue<Long, Integer> syncNation(int serverType, int serverId, long actorId);

    /**
     * 获取国家信息
     *
     * @param serverType
     * @param zoneId
     * @param nationId
     * @return
     */
    Nation getNation(int serverType, int zoneId, long nationId);

    /**
     * 解散军团
     *
     * @param serverType
     * @param serverId
     * @param actorId
     * @param nationId
     * @return
     */
    Result dissolution(int serverType, int serverId, long actorId, long nationId);

    /**
     * 获取国家列表
     *
     * @param serverType
     * @param serverId
     * @param nationId
     * @param page
     * @return
     */
    NationListEntity getNationList(int serverType, int serverId, long nationId, int page);

    /**
     * 创建国家
     *
     * @param serverType
     * @param serverId
     * @param attribute
     * @param name
     * @param flagId
     * @param applyLevelLimit
     * @return
     */
    TResult<Nation> createNation(int serverType, int serverId, Map<Byte, Object> attribute, String name, int flagId, int applyLevelLimit, Collection<Long> applyList);

    /**
     * 申请加入国家
     *
     * @param serverType
     * @param serverId
     * @param attribute
     * @param nationId
     * @return
     */
    Result apply(int serverType, int serverId, Map<Byte, Object> attribute, long nationId);

    /**
     * 批准加入国家
     *
     * @param serverType
     * @param serverId
     * @param nationId
     * @param actorId
     * @param targets
     * @param isApproved
     * @return
     */
    Result approve(int serverType, int serverId, long nationId, long actorId, Collection<Long> targets, boolean isApproved);

    /**
     * 任命
     *
     * @param serverType
     * @param serverId
     * @param nationId
     * @param actorId
     * @param targeterId
     * @param memberType
     * @return
     */
    Result appoint(int serverType, int serverId, long nationId, long actorId, long targeterId, NationMemberType memberType);

    /**
     * 弹劾
     *
     * @param serverType
     * @param serverId
     * @param nationId
     * @param actorId
     * @return
     */
    Result impeach(int serverType, int serverId, long nationId, long actorId, long kingId);

    /**
     * 转让
     *
     * @param serverType
     * @param serverId
     * @param nationId
     * @param actorId
     * @param targeterId
     * @param isAuto
     * @return
     */
    Result transfer(int serverType, int serverId, long nationId, long actorId, long targeterId, boolean isAuto);

    /**
     * 提出
     *
     * @param serverType
     * @param serverId
     * @param nationId
     * @param actorId
     * @param targeterId
     * @return
     */
    Result kickOff(int serverType, int serverId, long nationId, long actorId, long targeterId);

    /**
     * 修改限制
     *
     * @param serverType
     * @param serverId
     * @param nationId
     * @param actorId
     * @param type
     * @param value
     * @return
     */
    Result changeLimit(int serverType, int serverId, long nationId, long actorId, NationLimitType type, Object value, String declaration);

    /**
     * 修改军团招募宣言(后台使用)
     *
     * @param serverType
     * @param serverId
     * @param nationId
     * @param declaration
     * @return
     */
    Result changeDeclaration(int serverType, int serverId, long nationId, String declaration);

    /**
     * 退出国家
     *
     * @param serverType
     * @param serverId
     * @param nationId
     * @param actorId
     * @param isSelfExit
     * @return
     */
    Result exit(int serverType, int serverId, long nationId, long actorId, boolean isSelfExit);

    /**
     * 修改公告
     *
     * @param serverType
     * @param serverId
     * @param nationId
     * @param actorId
     * @param notice
     * @return
     */
    Result notice(int serverType, int serverId, long nationId, long actorId, String notice);

    /**
     * 发送国家邮件
     *
     * @param serverType
     * @param serverId
     * @param nationId
     * @param actorId
     * @param message
     * @return
     */
    Result mail(int serverType, int serverId, long nationId, long actorId, String message);

    /**
     * 更改国家名称
     *
     * @param serverType
     * @param serverId
     * @param nationId
     * @param actorId
     * @param name
     * @return
     */
    Result rename(int serverType, int serverId, long nationId, long actorId, String name);

    /**
     * 更改国家旗帜
     *
     * @param serverType
     * @param serverId
     * @param nationId
     * @param actorId
     * @param flag
     * @return
     */
    Result reflag(int serverType, int serverId, long nationId, long actorId, int flag);

    /**
     * 增加经验
     *
     * @param serverType
     * @param serverId
     * @param nationId
     * @param exp
     * @return
     */
    Result addExp(int serverType, int serverId, long actorId, long nationId, long exp);

    /**
     * 捐献
     *
     * @param serverType
     * @param serverId
     * @param nationId
     * @param actorId
     * @param id
     * @param nationExp
     * @param progress
     * @return
     */
    TResult<Long> donate(int serverType, int serverId, long nationId, long actorId, int id, long nationExp, int progress);

    /**
     * 获取国家战力
     *
     * @param serverType
     * @param serverId
     * @param nationId
     * @return
     */
    long getNationPower(int serverType, int serverId, long nationId);

    /**
     * 获取国家成员
     *
     * @param serverType
     * @param serverId
     * @param nationId
     * @return
     */
    NationMemberListEntity getNationMember(int serverType, int serverId, long nationId);

    /**
     * 根据国家名称获取所有工会成员信息
     *
     * @param serverType
     * @param serverId
     * @param nationName
     * @return
     */
    NationMemberInfoResponse getNationMemberByName(int serverType, int serverId, String nationName);

    /**
     * 获取所有公会名称
     *
     * @param serverType
     * @param zoneId
     * @return
     */
    Map<Long, String> getAllNationNames(int serverType, int zoneId);

    /**
     * 获取所有公会Id
     *
     * @param serverType
     * @param zoneId
     * @return
     */
    Collection<Long> getNationIds(int serverType, int zoneId);

    /**
     * 获取国家等级排行榜
     *
     * @param serverType
     * @param serverId
     * @param nationId
     * @param page
     * @return
     */
    NationLevelRankResponse getNationLevelRankResponse(int serverType, int serverId, long nationId, int page);

    /**
     * 获取国家战斗力排行榜
     *
     * @param serverType
     * @param serverId
     * @param nationId
     * @param page
     * @return
     */
    NationPowerRankResponse getNationPowerRankResponse(int serverType, int serverId, long nationId, int page);

    /**
     * 获取国家战力排行
     *
     * @param serverType
     * @param serverId
     * @param nationId
     * @return
     */
    long getNationPowerRank(int serverType, int serverId, long nationId);

    /**
     * 获取国家战力排行
     *
     * @param serverType
     * @param serverId
     * @param page
     * @return
     */
    Collection<NationPowerRankVO> getNationPowerRanks(int serverType, int serverId, int page);

    /**
     * 军团副本军团伤害排行榜
     *
     * @param serverType
     * @param serverId
     * @param nationId
     * @param actorId
     * @param page
     * @return
     */
    NationDungeonNationDamageRankResponse getNationDungeonNationDamageRankResponse(int serverType, int serverId, long nationId, long actorId, int page);

    /**
     * 军团副本军团成员副本关卡伤害排行榜
     *
     * @param serverType
     * @param serverId
     * @param nationId
     * @param actorId
     * @param storyId
     * @return
     */
    NationDungeonActorDamageRankResponse getNationDungeonStoryDamageRankResponse(int serverType, int serverId, long nationId, long actorId, int storyId);

    /**
     * 更新玩家信息
     *
     * @param serverType
     * @param serverId
     * @param attribute
     */
    void updateNationMember(int serverType, int serverId, long nationId, Map<Byte, Object> attribute, long powerRank);

    /**
     * 获取仙盟成员服务器分布
     *
     * @param nation
     * @return
     */
    Map<Integer, Collection<Long>> findNationMemberServer(Nation nation);

    /**
     * 清理申请列表
     *
     * @param serverType
     * @param serverId
     * @param actorId
     * @param nationId
     */
    void clearApply(int serverType, int serverId, long actorId, Collection<Long> nationId);

    /**
     * 合服清理
     *
     * @param serverType
     * @param serverIds
     * @param actorIds
     */
    void serverComposeClear(int serverType, Collection<Integer> serverIds, Collection<Long> actorIds);

    /**
     * 献花
     *
     * @param serverType
     * @param serverId
     * @param flowerCount
     */
    void flower(int serverType, int serverId, long actorId, int targetServerId, long targetId, int type, int flowerCount);

    /**
     * 更新献花
     *
     * @param serverType
     * @param flowerCount
     */
    void updateFlower(int serverType, int serverId, long actorId, int pushServer, long pushActor, int type, long flowerCount);

    /**
     * 军团副本关卡宝箱领取
     *
     * @param serverType
     * @param serverId
     * @param actorId
     * @param storyId
     * @param location
     */
    CollectionResult<RewardObject> nationStoryReceive(int serverType, int serverId, long nationId, long actorId, int storyId, int location);

    /**
     * 军团副本关卡宝箱一键领取
     *
     * @param serverType
     * @param serverId
     * @param actorId
     */
    TResult<KeyValue<Collection<Integer>, Collection<RewardObject>>> nationStoryQuickReceive(int serverType, int serverId, long nationId, long actorId,
        Collection<Integer> hadReceivedList);

    /**
     * 军团百工作坊-道具回收
     *
     * @param serverType
     * @param serverId
     * @param nationId
     * @param recycleValue
     * @param actorId
     */
    Result nationRecycle(int serverType, int serverId, long nationId, int recycleValue, long actorId);

    /**
     * 获取仙盟列表
     *
     * @param serverType
     * @param zoneId
     * @return
     */
    List<Nation> getNationList(int serverType, int zoneId);
}
