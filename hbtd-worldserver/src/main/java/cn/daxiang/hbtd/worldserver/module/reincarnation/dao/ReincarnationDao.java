package cn.daxiang.hbtd.worldserver.module.reincarnation.dao;

import cn.daxiang.hbtd.worldserver.module.reincarnation.model.ReincarnationActor;
import cn.daxiang.hbtd.worldserver.module.reincarnation.model.ReincarnationBattlefield;
import cn.daxiang.hbtd.worldserver.module.reincarnation.model.ReincarnationRank;
import cn.daxiang.shared.module.reincarnation.ReincarnationInfo;

import java.util.Collection;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/12/23
 */
public interface ReincarnationDao {
    /**
     * 获取轮回战场信息
     *
     * @return
     */
    ReincarnationInfo getReincarnationInfo();

    /**
     * 刷新轮回战场信息
     *
     * @param reincarnationInfo
     */
    void refreshReincarnationInfo(ReincarnationInfo reincarnationInfo);

    /**
     * 获取所有战场
     *
     * @return
     */
    Collection<ReincarnationBattlefield> getReincarnationBattlefieldList();

    /**
     * 获取战场信息
     *
     * @param battlefieldId
     * @return
     */
    Optional<ReincarnationBattlefield> getReincarnationBattlefield(int battlefieldId);

    /**
     * 加入战场
     *
     * @param floor
     * @return
     */
    void enterBattlefield(Map<Byte, Object> attributes, int layer, int floor);

    /**
     * 退出战场
     */
    void exitBattlefield(ReincarnationBattlefield battlefield, long actorId);

    /**
     * 获取轮回战场玩家信息
     *
     * @param actorId
     * @return
     */
    Optional<ReincarnationActor> getReincarnationActor(long actorId);

    /**
     * 刷新轮回战场玩家信息
     *
     * @param reincarnationActor
     */
    void updateReincarnationActor(ReincarnationActor reincarnationActor);

    /**
     * 获取轮回战场排名
     *
     * @return
     */
    Collection<ReincarnationRank> getReincarnationRankList();

    /**
     * 刷新轮回战场排名
     *
     * @param reincarnationActor
     */
    void refreshReincarnationRank(ReincarnationActor reincarnationActor);

    /**
     * 清除玩家数据和战场信息
     */
    void clearReincarnation();

    /**
     * 处理合服信息
     *
     * @param serverId
     * @param composes
     */
    void serverCompose(int serverId, Collection<Integer> composes);

    /**
     * 获取空闲战场信息
     * @return
     */
    Map<Integer, Collection<Integer>> getIdleBattlefield();
}
