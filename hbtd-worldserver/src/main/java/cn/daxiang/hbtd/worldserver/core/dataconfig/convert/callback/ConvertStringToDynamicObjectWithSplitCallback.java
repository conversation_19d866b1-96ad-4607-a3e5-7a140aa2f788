package cn.daxiang.hbtd.worldserver.core.dataconfig.convert.callback;

import cn.daxiang.hbtd.worldserver.core.dataconfig.convert.CapacityResultable;
import cn.daxiang.hbtd.worldserver.core.dataconfig.convert.Dynamicable;

public interface ConvertStringToDynamicObjectWithSplitCallback<V extends Dynamicable<T>, T> extends CapacityResultable {

    public String getString();

    public V fromArray(String[] array);

    public char getStartSplit();

    public char getEndSplit();

    public String getDelimiter();

    public int getDynamicStart();

    public int getDynamicSize();

    public T fromDynamic(String[] array);

}