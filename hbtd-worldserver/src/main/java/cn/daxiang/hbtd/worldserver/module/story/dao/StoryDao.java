package cn.daxiang.hbtd.worldserver.module.story.dao;

import cn.daxiang.shared.module.story.StoryWorldRankVO;

import java.util.Collection;

/**
 * <AUTHOR>
 * @date 2023/7/5
 */
public interface StoryDao {
    /**
     * 获取主线关卡战区排行榜
     *
     * @param serverType
     * @param zoneId
     * @param rankVO
     * @return
     */
    Collection<StoryWorldRankVO> getStoryWorldRankList(int serverType, int zoneId, StoryWorldRankVO rankVO);

    /**
     * 刷新主线关卡战区排行榜
     *
     * @param serverType
     * @param zoneId
     * @param rankVO
     */
    void refreshStoryWorldRank(int serverType, int zoneId, StoryWorldRankVO rankVO);

    /**
     * 游戏服的排行榜推送到战区排行榜
     *
     * @param serverType
     * @param zoneId
     * @param rankVOList
     */
    void pushStoryRanks(int serverType, int zoneId, Collection<StoryWorldRankVO> rankVOList);
}
