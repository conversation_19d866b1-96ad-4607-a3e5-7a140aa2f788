package cn.daxiang.hbtd.worldserver.core;

import cn.daxiang.framework.utils.ObjectReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@Component
public class WorldConfig {
    private static ObjectReference<WorldConfig> ref = new ObjectReference<WorldConfig>();
    /**
     * 端口号
     */
    @Value("#{new Integer('${world_server}'.split(':')[1])}")
    private int port = 9123;
    /**
     * 服务器类型
     * {@code GameServerType}
     */
    @Value("${server_type}")
    private int serverType = 1;

    @Value("${debug_mode:false}")
    private boolean debugMode = false;

    /**
     * 运营微信接口
     */
    @Value("${operations_fs_url}")
    private String operationsWXURL;

    /**
     * 飞书警告消息
     */
    @Value("${server.fs.warning.key}")
    private String fsWarningKey;

    public static int getPort() {
        return ref.get().port;
    }

    public static int getServerType() {
        return ref.get().serverType;
    }

    /**
     * 是否处于debug模式
     *
     * @return
     */
    public static boolean isDebug() {
        return ref.get().debugMode;
    }

    public static String getOperationsWXURL() {
        return ref.get().operationsWXURL;
    }

    public static String getFsWarningKey() {
        return ref.get().fsWarningKey;
    }

    @PostConstruct
    public void initialize() {
        ref.set(this);
    }

}
