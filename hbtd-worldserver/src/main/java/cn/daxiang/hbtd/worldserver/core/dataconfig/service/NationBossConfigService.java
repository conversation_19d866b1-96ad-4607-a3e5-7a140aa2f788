package cn.daxiang.hbtd.worldserver.core.dataconfig.service;

import cn.daxiang.framework.dataconfig.ConfigServiceAdapter;
import cn.daxiang.hbtd.worldserver.core.dataconfig.model.HellRewardPoolConfig;
import cn.daxiang.hbtd.worldserver.core.dataconfig.model.HellScoreRewardConfig;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;
import java.util.TreeMap;

/**
 * @Author: Gary
 * @Date 2022-12-07 17:03
 * @Description:
 */
@Component
public class NationBossConfigService extends ConfigServiceAdapter {

    /**
     * 奖励配置
     * key:openDay,value:{key:rank, value:{key:count, value:HellScoreRewardConfig}}
     */
    private static TreeMap<Integer, TreeMap<Long, TreeMap<Integer, HellScoreRewardConfig>>> HELL_NATION_RANK_REWARD_MAP = Maps.newTreeMap();
    /**
     * 奖池配置
     * key:openDay,value:{key:rank, value:{key:count, value:HellScoreRewardConfig}}
     */
    private static Map<Integer, HellRewardPoolConfig> HELL_REWARD_POOL_MAP = Maps.newHashMap();

    /**
     * 获取奖励配置
     *
     * @param day
     * @param rank
     * @param count
     * @return
     */
    public static Collection<Integer> getHellRewardIdList(int day, long rank, int count) {
        Collection<Integer> list = Lists.newArrayList();
        Integer dayKey = HELL_NATION_RANK_REWARD_MAP.floorKey(day);
        if (dayKey == null) {
            dayKey = HELL_NATION_RANK_REWARD_MAP.firstKey();
        }
        TreeMap<Long, TreeMap<Integer, HellScoreRewardConfig>> rankRewardMap = HELL_NATION_RANK_REWARD_MAP.get(dayKey);
        Long rankKey = rankRewardMap.ceilingKey(rank);
        if (rankKey == null) {
            rankKey = rankRewardMap.lastKey();
        }
        TreeMap<Integer, HellScoreRewardConfig> countRewardMap = rankRewardMap.get(rankKey);
        Integer countKey = countRewardMap.floorKey(count);
        if (countKey == null) {
            countKey = countRewardMap.firstKey();
        }
        for (Integer poolId : countRewardMap.get(countKey).getPoolIds()) {
            HellRewardPoolConfig hellRewardPoolConfig = HELL_REWARD_POOL_MAP.get(poolId);
            if (hellRewardPoolConfig == null) {
                continue;
            }
            list.addAll(hellRewardPoolConfig.getHellRankReward());
        }
        return list;
    }

    @Override
    protected void initialize() {
        Collection<HellScoreRewardConfig> rewardConfigList = dataConfig.listAll(this, HellScoreRewardConfig.class);
        for (HellScoreRewardConfig config : rewardConfigList) {
            TreeMap<Long, TreeMap<Integer, HellScoreRewardConfig>> rankRewardMap = HELL_NATION_RANK_REWARD_MAP.get(config.getDay());
            if (rankRewardMap == null) {
                rankRewardMap = Maps.newTreeMap();
                HELL_NATION_RANK_REWARD_MAP.put(config.getDay(), rankRewardMap);
            }
            TreeMap<Integer, HellScoreRewardConfig> countRewardMap = rankRewardMap.get(config.getRank());
            if (countRewardMap == null) {
                countRewardMap = Maps.newTreeMap();
                rankRewardMap.put(config.getRank(), countRewardMap);
            }
            countRewardMap.put(config.getMemberCount(), config);
        }
        Collection<HellRewardPoolConfig> poolConfigList = dataConfig.listAll(this, HellRewardPoolConfig.class);
        for (HellRewardPoolConfig config : poolConfigList) {
            HELL_REWARD_POOL_MAP.put(config.getPoolId(), config);
        }
    }

    @Override
    protected void clean() {
        HELL_NATION_RANK_REWARD_MAP.clear();
        HELL_REWARD_POOL_MAP.clear();
    }
}
