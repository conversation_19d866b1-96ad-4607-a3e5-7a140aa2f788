package cn.daxiang.hbtd.worldserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.dataconfig.annotation.FieldIgnore;
import cn.daxiang.framework.identity.IdentiyKey;
import com.alibaba.fastjson.JSONArray;

import java.util.Collection;

/**
 * 攻城略地活跃值表
 *
 * <AUTHOR>
 * @date 2022/7/4
 */
@DataFile(fileName = "capture_active_config")
public class CaptureActiveConfig implements ModelAdapter {
    /**
     * 州ID
     */
    private int stateId;
    /**
     * 最小等级（包含此等级）
     */
    private int minLevel;
    /**
     * 最大等级（包含此等级）
     */
    private int maxLevel;
    /**
     * 活跃值,向下取
     * 1,100000,200000
     * 1-99999=1
     * 100000-199999=100000
     * 200000-max=200000
     */
    private long active;
    /**
     * 城池序号列表，用以随机不同州的哪些城池
     * [1,2,3,4,5,6,7,8]
     */
    private String orders;

    @FieldIgnore
    private Collection<Integer> orderList;

    @Override
    public void initialize() {
        orderList = JSONArray.parseArray(orders, Integer.class);
    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(stateId, active);
    }

    public int getStateId() {
        return stateId;
    }

    public int getMinLevel() {
        return minLevel;
    }

    public int getMaxLevel() {
        return maxLevel;
    }

    public long getActive() {
        return active;
    }

    public Collection<Integer> getOrderList() {
        return orderList;
    }
}
