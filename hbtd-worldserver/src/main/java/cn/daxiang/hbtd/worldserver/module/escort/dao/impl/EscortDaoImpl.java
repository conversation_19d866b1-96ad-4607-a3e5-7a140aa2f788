package cn.daxiang.hbtd.worldserver.module.escort.dao.impl;

import cn.daxiang.framework.result.CollectionResult;
import cn.daxiang.framework.rpc.event.EscortEndRewardEvent;
import cn.daxiang.framework.rpc.event.EscortStateRefreshEvent;
import cn.daxiang.framework.utils.DateUtils;
import cn.daxiang.framework.utils.NumberUtils;
import cn.daxiang.framework.utils.RandomUtils;
import cn.daxiang.framework.utils.TimeUtils;
import cn.daxiang.framework.utils.rank.cache.ConsistentLadderRankCache;
import cn.daxiang.framework.utils.schedule.Schedule;
import cn.daxiang.hbtd.worldserver.core.context.ApplicationServerZoneCompleteEvent;
import cn.daxiang.hbtd.worldserver.core.dataconfig.convert.impl.IntListConfig;
import cn.daxiang.hbtd.worldserver.core.dataconfig.convert.impl.IntTreeMapConfig;
import cn.daxiang.hbtd.worldserver.core.dataconfig.model.EscortCarriageConfig;
import cn.daxiang.hbtd.worldserver.core.dataconfig.model.EscortStateConfig;
import cn.daxiang.hbtd.worldserver.core.dataconfig.service.EscortConfigService;
import cn.daxiang.hbtd.worldserver.core.dataconfig.service.WorldGlobalConfigService;
import cn.daxiang.hbtd.worldserver.core.redis.RedisDao;
import cn.daxiang.hbtd.worldserver.core.rpc.RpcClientChannel;
import cn.daxiang.hbtd.worldserver.module.escort.dao.EscortDao;
import cn.daxiang.hbtd.worldserver.module.escort.model.EscortActor;
import cn.daxiang.hbtd.worldserver.module.escort.model.EscortHelpRank;
import cn.daxiang.hbtd.worldserver.module.escort.model.EscortRank;
import cn.daxiang.hbtd.worldserver.module.escort.model.EscortRobbingRank;
import cn.daxiang.hbtd.worldserver.module.server.ServerZoneHelper;
import cn.daxiang.protocol.battle.BattleTypeProtocol;
import cn.daxiang.protocol.game.EscortProtocol;
import cn.daxiang.protocol.game.TypeProtocol;
import cn.daxiang.shared.GlobalConfigKey;
import cn.daxiang.shared.module.escort.Escort;
import cn.daxiang.shared.module.escort.EscortBattle;
import cn.daxiang.shared.module.escort.EscortCart;
import cn.daxiang.shared.module.escort.EscortState;
import cn.daxiang.shared.reward.RewardObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/1/31
 */
@Component
public class EscortDaoImpl extends RedisDao implements EscortDao, ApplicationListener<ApplicationServerZoneCompleteEvent> {
    @Autowired
    private WorldGlobalConfigService globalConfigService;
    @Autowired
    private Schedule schedule;
    /**
     * 押镖RedisKey
     */
    private static final String ESCORT_CACHE_KEY = "ESCORT";
    /**
     * 押镖信息
     */
    private static Escort ESCORT;
    /**
     * 押镖城池RedisKey
     * ESCORT_ACTOR__{serverType}_{areaId}
     */
    private static final String ESCORT_ACTOR_CACHE_KEY = "ESCORT_ACTOR_%s_%s";
    /**
     * 押镖城池RedisKey
     * ESCORT_CART_{serverType}_{areaId}
     */
    private static final String ESCORT_CART_CACHE_KEY = "ESCORT_CART_%s_%s";
    /**
     * 帮助押镖次数
     */
    private static final String ESCORT_ESCORTED_CACHE_KEY = "ESCORT_ESCORTED";

    /**
     * 押镖角色信息
     * {k:serverType,v:{k:areaId,v:{k:id,v:EscortActor}}}
     */
    private static final Map<Integer, Map<Integer, Map<Long, EscortActor>>> ESCORT_ACTOR_MAP = Maps.newConcurrentMap();
    /**
     * 押镖镖车信息
     * {k:serverType,v:{k:areaId,v:{k:id,v:EscortCart}}}
     */
    private static final Map<Integer, Map<Integer, Map<Long, EscortCart>>> ESCORT_CART_MAP = Maps.newConcurrentMap();

    /**
     * 押镖排行榜
     * key:{serverType,value:{key:zoneId,value:{key:actorId,value:EscortRank}}}
     */
    private static Map<Integer, Map<Integer, ConsistentLadderRankCache<Long, EscortRank>>> ESCORT_RANK_MAP = Maps.newHashMap();
    /**
     * 押镖排行榜
     * key:{serverType,value:{key:zoneId,value:{key:actorId,value:EscortRobbingRank}}}
     */
    private static Map<Integer, Map<Integer, ConsistentLadderRankCache<Long, EscortRobbingRank>>> ESCORT_ROBBING_RANK_MAP = Maps.newHashMap();
    /**
     * 押镖排行榜
     * key:{serverType,value:{key:zoneId,value:{key:actorId,value:EscortHelpRank}}}
     */
    private static Map<Integer, Map<Integer, ConsistentLadderRankCache<Long, EscortHelpRank>>> ESCORT_HELP_RANK_MAP = Maps.newHashMap();

    /**
     * 押镖战斗录像信息redis缓存key
     */
    private static final String ESCORT_BATTLE_REDIS_CACHE_KEY = "ESCORT_BATTLE";

    private static final int RANK_LIMIT = 10;

    @Override
    public Escort getEscort() {
        return get(Escort.class, ESCORT_CACHE_KEY);
    }

    @Override
    public void refreshEscort(Escort escort) {
        update(escort, ESCORT_CACHE_KEY);
    }

    @Override
    public void onApplicationEvent(ApplicationServerZoneCompleteEvent event) {
        initData();
        schedule.addEveryHour(() -> {
            refreshEscort();
        });
        resetEscortCart();
    }

    private void initData() {
        this.initialize();
        refreshEscort();
        Collection<String> escortActorKeys = getKeys(ESCORT_ACTOR_CACHE_KEY.substring(0, ESCORT_ACTOR_CACHE_KEY.indexOf('%')) + "*");
        for (String escortActorKey : escortActorKeys) {
            String[] data = escortActorKey.split("_");
            int serverType = Integer.parseInt(data[2]);
            int areaId = Integer.parseInt(data[3]);
            Map<Integer, Map<Long, EscortActor>> serverTypeMap = ESCORT_ACTOR_MAP.get(serverType);
            if (serverTypeMap == null) {
                LOGGER.error("ESCORT_ACTOR_CACHE_KEY serverType not found, serverType:{}", serverType);
                continue;
            }
            Map<Long, EscortActor> actorMap = serverTypeMap.get(areaId);
            if (actorMap == null) {
                LOGGER.error("ESCORT_ACTOR_CACHE_KEY areaId not found, serverType:{}, areaId:{}", serverType, areaId);
                continue;
            }
            Map<Long, EscortActor> escortActorMap = this.getHashMap(escortActorKey, Long.class, EscortActor.class);
            actorMap.putAll(escortActorMap);
        }

        Collection<String> escortCartKeys = getKeys(ESCORT_CART_CACHE_KEY.substring(0, ESCORT_CART_CACHE_KEY.indexOf('%')) + "*");
        for (String escortCartKey : escortCartKeys) {
            String[] data = escortCartKey.split("_");
            int serverType = Integer.parseInt(data[2]);
            int areaId = Integer.parseInt(data[3]);
            Map<Integer, Map<Long, EscortCart>> serverTypeMap = ESCORT_CART_MAP.get(serverType);
            if (serverTypeMap == null) {
                LOGGER.error("ESCORT_CART_CACHE_KEY serverType not found, serverType:{}", serverType);
                continue;
            }
            Map<Long, EscortCart> areaMap = serverTypeMap.get(areaId);
            if (areaMap == null) {
                LOGGER.error("ESCORT_CART_CACHE_KEY areaId not found, serverType:{}, areaId:{}", serverType, areaId);
                continue;
            }
            Map<Long, EscortCart> escortCartMap = this.getHashMap(escortCartKey, Long.class, EscortCart.class);
            areaMap.putAll(escortCartMap);
        }

        // 初始化排行
        for (Map.Entry<Integer, Map<Integer, Map<Long, EscortActor>>> serverTypeEntry : ESCORT_ACTOR_MAP.entrySet()) {
            int serverType = serverTypeEntry.getKey();
            for (Map.Entry<Integer, Map<Long, EscortActor>> zoneEntry : serverTypeEntry.getValue().entrySet()) {
                int zoneId = zoneEntry.getKey();
                for (Map.Entry<Long, EscortActor> entry : zoneEntry.getValue().entrySet()) {
                    achieveEscortRank(serverType, zoneId, entry.getValue());
                    achieveEscortRobbingRank(serverType, zoneId, entry.getValue());
                    achieveHelpRank(serverType, zoneId, entry.getValue());
                }
            }
        }
    }

    private void refreshEscort() {
        int week = DateUtils.getChineseDayOfWeek();
        int hour = TimeUtils.getHour();
        long now = System.currentTimeMillis();
        IntTreeMapConfig config = globalConfigService.findGlobalObject(GlobalConfigKey.ESCORT_OPEN_TIME, IntTreeMapConfig.class);
        List<Integer> sleepTimeConfig = globalConfigService.findGlobalObject(GlobalConfigKey.ESCORT_SLEEP_TIME, IntListConfig.class).getVs();
        long stopTime = DateUtils.getTimeWithChineseWeekDayHHmm(sleepTimeConfig.get(0), sleepTimeConfig.get(1), 0);

        boolean isPush = false;

        if (ESCORT == null || ESCORT.getEscortType() == EscortProtocol.EscortType.ESCORT_TYPE_CLOSE) {
            Escort escort = getEscort();
            if (week >= config.getFirstKey() && hour >= config.getFirstEntry().getValue()) {
                long startTime = DateUtils.getTimeWithChineseWeekDayHHmm(config.getFirstKey(), config.getFirstEntry().getValue(), 0);
                long endTime = DateUtils.getTimeWithChineseWeekDayHHmm(config.getLastKey(), config.getLastEntry().getValue(), 0);
                if (escort == null) {
                    escort = Escort.valueOf(EscortProtocol.EscortType.ESCORT_TYPE_OPEN, startTime, endTime);
                } else {
                    escort.refresh(EscortProtocol.EscortType.ESCORT_TYPE_OPEN, startTime, endTime);
                    isPush = true;
                }
            }
            if (now > stopTime) {
                escort.setEscortType(EscortProtocol.EscortType.ESCORT_TYPE_SLEEP);
            }
            if (now >= escort.getEndTime()) {
                escort.setEscortType(EscortProtocol.EscortType.ESCORT_TYPE_CLOSE);
            }
            ESCORT = escort;
            this.refreshEscort(ESCORT);
        }

        if (ESCORT != null && ESCORT.getEscortType() != EscortProtocol.EscortType.ESCORT_TYPE_CLOSE) {
            if (now > stopTime && ESCORT.getEscortType() != EscortProtocol.EscortType.ESCORT_TYPE_SLEEP) {
                ESCORT.setEscortType(EscortProtocol.EscortType.ESCORT_TYPE_SLEEP);
                this.refreshEscort(ESCORT);
                isPush = true;
            }
            if (now >= ESCORT.getEndTime()) {
                ESCORT.setEscortType(EscortProtocol.EscortType.ESCORT_TYPE_CLOSE);
                this.refreshEscort(ESCORT);
                escortEndSendMail();
                //删除数据
                clearData();
                isPush = true;
            }
        }

        if (isPush) {
            for (Integer serverType : ServerZoneHelper.getAllZoneConfig().keySet()) {
                RpcClientChannel.pushToAllNode(serverType, new EscortStateRefreshEvent(ESCORT));
            }
        }
    }

    private void initialize() {
        for (Map.Entry<Integer, Map<Integer, List<Integer>>> serverTypeEntry : ServerZoneHelper.getAllZoneConfig().entrySet()) {
            int serverType = serverTypeEntry.getKey();
            Map<Integer, Map<Long, EscortActor>> actorServerTypeMap = ESCORT_ACTOR_MAP.computeIfAbsent(serverType, k -> Maps.newHashMap());
            Map<Integer, Map<Long, EscortCart>> cartServerTypeMap = ESCORT_CART_MAP.computeIfAbsent(serverType, k -> Maps.newConcurrentMap());

            Map<Integer, ConsistentLadderRankCache<Long, EscortRank>> zoneEscortRankMap = ESCORT_RANK_MAP.computeIfAbsent(serverType, x -> Maps.newHashMap());
            Map<Integer, ConsistentLadderRankCache<Long, EscortRobbingRank>> zoneEscortRobbingRankMap = ESCORT_ROBBING_RANK_MAP.computeIfAbsent(serverType, x -> Maps.newHashMap());
            Map<Integer, ConsistentLadderRankCache<Long, EscortHelpRank>> zoneEscortHelpRankMap = ESCORT_HELP_RANK_MAP.computeIfAbsent(serverType, x -> Maps.newHashMap());

            for (int serverZone : serverTypeEntry.getValue().keySet()) {
                actorServerTypeMap.computeIfAbsent(serverZone, k -> Maps.newHashMap());
                cartServerTypeMap.computeIfAbsent(serverZone, k -> Maps.newConcurrentMap());
                zoneEscortRankMap.computeIfAbsent(serverZone, x -> new ConsistentLadderRankCache<>(RANK_LIMIT, Short.MAX_VALUE));
                zoneEscortRobbingRankMap.computeIfAbsent(serverZone, x -> new ConsistentLadderRankCache<>(RANK_LIMIT, Short.MAX_VALUE));
                zoneEscortHelpRankMap.computeIfAbsent(serverZone, x -> new ConsistentLadderRankCache<>(RANK_LIMIT, Short.MAX_VALUE));
            }
        }
    }

    @Override
    public EscortBattle getEscortBattle(long battleId) {
        return getHashObject(ESCORT_BATTLE_REDIS_CACHE_KEY, String.valueOf(battleId), EscortBattle.class);
    }

    @Override
    public void saveEscortBattle(EscortBattle escortBattle) {
        putHashObject(ESCORT_BATTLE_REDIS_CACHE_KEY, String.valueOf(escortBattle.getId()), escortBattle);
    }

    @Override
    public void cleanEscortBattle() {
        this.delete(ESCORT_BATTLE_REDIS_CACHE_KEY);
    }

    @Override
    public void deleteEscortBattle(Collection<Long> battleIdList) {
        for (Long battleId : battleIdList) {
            deleteHashObject(ESCORT_BATTLE_REDIS_CACHE_KEY, String.valueOf(battleId));
        }
    }

    @Override
    public Collection<EscortState> getEscortState(int serverType, int zoneId, long actorId, long nationId) {
        Collection<EscortState> list = Lists.newArrayList();
        Map<Integer, Map<Long, EscortCart>> zoneMap = ESCORT_CART_MAP.get(serverType);
        if (zoneMap == null) {
            return list;
        }
        Map<Long, EscortCart> gvgActorInfoMap = zoneMap.get(zoneId);
        if (gvgActorInfoMap == null) {
            return list;
        }
        EscortCarriageConfig systemEscortCarriageConfig = EscortConfigService.getSystemEscortCarriageConfig();
        for (EscortStateConfig config : EscortConfigService.getEscortStateConfigMap().values()) {
            Long escortCartTimes = 0L;
            if (nationId > 0) {
                escortCartTimes = gvgActorInfoMap.values().stream().filter(
                    x -> x.getEndTime() > System.currentTimeMillis() && x.getBeRobbingTimes() > 0 && x.getStateId() == config.getStateId() && x.getActorId() != actorId
                        && Long.parseLong(x.getEscortActor().get((byte) 41).toString()) != nationId && !x.getRobbingActorIdList().contains(actorId)
                        && x.getGuardActorId() != actorId).collect(Collectors.counting());
            } else {
                escortCartTimes = gvgActorInfoMap.values().stream().filter(
                    x -> x.getEndTime() > System.currentTimeMillis() && x.getBeRobbingTimes() > 0 && x.getStateId() == config.getStateId() && x.getActorId() != actorId
                        && !x.getRobbingActorIdList().contains(actorId) && x.getGuardActorId() != actorId).collect(Collectors.counting());
            }
            if (escortCartTimes == null) {
                escortCartTimes = 0L;
            }

            if (escortCartTimes < 6 && systemEscortCarriageConfig.getStateIdList().contains(config.getStateId())) {
                escortCartTimes++;
            }

            list.add(EscortState.valueOf(config.getStateId(), escortCartTimes.intValue()));
        }
        return list;
    }

    @Override
    public void refreshEscortState(int serverType, int areaId, EscortState escortState) {
        putHashObject(String.format(ESCORT_ACTOR_CACHE_KEY, serverType, areaId), String.valueOf(escortState.getId()), escortState);
    }

    @Override
    public EscortCart getEscortCart(int sererType, int zoneId, long actorId) {
        Map<Integer, Map<Long, EscortCart>> zoneMap = ESCORT_CART_MAP.get(sererType);
        if (zoneMap == null) {
            return null;
        }
        Map<Long, EscortCart> escortCartMap = zoneMap.get(zoneId);
        if (escortCartMap == null) {
            return null;
        }

        EscortCart escortCart = escortCartMap.get(actorId);
        if (escortCart != null && escortCart.isRewardCompletion()) {
            if (!DateUtils.isToday(escortCart.getStartTime())) {
                deleteEscortCart(sererType, zoneId, Lists.newArrayList(escortCart));
                return null;
            }
        }

        return escortCart;
    }

    @Override
    public Collection<EscortCart> getEscortCartList(int serverType, int zoneId, long actorId, int stateId, long nationId, Collection<Long> actorIdList) {
        int cartTimes = 6;
        if (stateId == 0) {
            cartTimes = globalConfigService.findGlobalConfig(GlobalConfigKey.ESCORT_ROB_SHOW_LIMIT).findInt();
        }

        List<EscortCart> list = Lists.newArrayList();
        Map<Integer, Map<Long, EscortCart>> zoneMap = ESCORT_CART_MAP.get(serverType);
        if (zoneMap == null) {
            return list;
        }
        Map<Long, EscortCart> escortCartMap = zoneMap.get(zoneId);
        if (escortCartMap == null) {
            return list;
        }

        if (stateId == 0) {
            if (nationId > 0) {
                list = escortCartMap.values().stream().filter(x -> x.getEndTime() > System.currentTimeMillis() && x.getBeRobbingTimes() > 0 && x.getActorId() != actorId
                        && Long.parseLong(x.getEscortActor().get((byte) 41).toString()) != nationId && !x.getRobbingActorIdList().contains(actorId) && x.getGuardActorId() != actorId)
                    .collect(Collectors.toList());
            } else {
                list = escortCartMap.values().stream().filter(
                    x -> x.getEndTime() > System.currentTimeMillis() && x.getBeRobbingTimes() > 0 && x.getActorId() != actorId && !x.getRobbingActorIdList().contains(actorId)
                        && x.getGuardActorId() != actorId).collect(Collectors.toList());
            }
        } else {
            if (nationId > 0) {
                list = escortCartMap.values().stream().filter(
                    x -> x.getEndTime() > System.currentTimeMillis() && x.getBeRobbingTimes() > 0 && x.getStateId() == stateId && x.getActorId() != actorId
                        && Long.parseLong(x.getEscortActor().get((byte) 41).toString()) != nationId && !x.getRobbingActorIdList().contains(actorId)
                        && x.getGuardActorId() != actorId).collect(Collectors.toList());
            } else {
                list = escortCartMap.values().stream().filter(
                    x -> x.getEndTime() > System.currentTimeMillis() && x.getBeRobbingTimes() > 0 && x.getStateId() == stateId && x.getActorId() != actorId
                        && !x.getRobbingActorIdList().contains(actorId) && x.getGuardActorId() != actorId).collect(Collectors.toList());
            }
        }
        Collections.shuffle(list);

        List<EscortCart> cartList = Lists.newArrayList();
        // 上次刷新的镖车
        List<EscortCart> repeatCartList = Lists.newArrayList();

        for (EscortCart escortCart : list) {
            if (actorIdList.contains(escortCart.getActorId())) {
                repeatCartList.add(escortCart);
            } else {
                cartList.add(escortCart);
                if (cartList.size() >= cartTimes) {
                    break;
                }
            }
        }
        // 镖车不够时填充上次刷新的镖车
        if (cartList.size() < cartTimes && repeatCartList.size() > 0) {
            for (EscortCart escortCart : repeatCartList) {
                cartList.add(escortCart);
                if (cartList.size() >= cartTimes) {
                    break;
                }
            }
        }
        // 增加系统镖车
        if (cartList.size() < cartTimes) {
            EscortCarriageConfig systemEscortCarriageConfig = EscortConfigService.getSystemEscortCarriageConfig();
            if (systemEscortCarriageConfig.getStateIdList().contains(stateId)) {
                cartList.add(EscortCart.valueOf(0, stateId, systemEscortCarriageConfig.getId(), systemEscortCarriageConfig.getRobTimes()));
            } else if (stateId == 0) {
                cartList.add(EscortCart.valueOf(0, systemEscortCarriageConfig.getStateIdList().iterator().next().intValue(), systemEscortCarriageConfig.getId(),
                    systemEscortCarriageConfig.getRobTimes()));
            }
        }

        return cartList;
    }

    @Override
    public Map<Long, EscortCart> getEscortCartMap(int serverType, int zoneId) {
        List<EscortCart> list = Lists.newArrayList();
        Map<Integer, Map<Long, EscortCart>> zoneMap = ESCORT_CART_MAP.get(serverType);
        if (zoneMap == null) {
            return Maps.newHashMap();
        }
        Map<Long, EscortCart> escortCartMap = zoneMap.get(zoneId);
        if (escortCartMap == null) {
            return Maps.newHashMap();
        }

        return escortCartMap;
    }

    @Override
    public void updateEscortCart(int serverType, int zoneId, EscortCart escortCart) {
        Map<Integer, Map<Long, EscortCart>> zoneMap = ESCORT_CART_MAP.get(serverType);
        if (zoneMap == null) {
            return;
        }
        Map<Long, EscortCart> escortCartMap = zoneMap.get(zoneId);
        if (escortCartMap == null) {
            return;
        }
        escortCartMap.put(escortCart.getActorId(), escortCart);
        putHashObject(String.format(ESCORT_CART_CACHE_KEY, serverType, zoneId), String.valueOf(escortCart.getActorId()), escortCart);
    }

    @Override
    public void deleteEscortCart(int serverType, int zoneId, Collection<EscortCart> cartList) {
        for (EscortCart cart : cartList) {
            Map<Integer, Map<Long, EscortCart>> zoneMap = ESCORT_CART_MAP.get(serverType);
            if (zoneMap == null) {
                continue;
            }
            Map<Long, EscortCart> escortCartMap = zoneMap.get(zoneId);
            if (escortCartMap == null) {
                continue;
            }
            escortCartMap.remove(cart.getActorId());
            deleteHashObject(String.format(ESCORT_CART_CACHE_KEY, serverType, zoneId), String.valueOf(cart.getActorId()));
        }
    }

    @Override
    public void addEscortEscortedTimes(long actorId) {
        Integer times = getHashObject(ESCORT_ESCORTED_CACHE_KEY, String.valueOf(actorId), Integer.class);
        if (times == null) {
            times = 0;
        }
        putHashObject(ESCORT_ESCORTED_CACHE_KEY, String.valueOf(actorId), times + 1);
    }

    @Override
    public int getEscortEscortedTimes(long actorId) {
        Integer times = getHashObject(ESCORT_ESCORTED_CACHE_KEY, String.valueOf(actorId), Integer.class);
        if (times == null) {
            return 0;
        }
        return times;
    }

    @Override
    public void resetEveryDay() {
        // 清理帮助护镖人记录
        delete(String.format(ESCORT_ESCORTED_CACHE_KEY));
        // 清理昨天已完成镖车
        resetEscortCart();
    }

    @Override
    public EscortActor getEscortActor(int sererType, int zoneId, long actorId) {
        Map<Integer, Map<Long, EscortActor>> zoneMap = ESCORT_ACTOR_MAP.get(sererType);
        if (zoneMap == null) {
            return null;
        }
        Map<Long, EscortActor> escortActorMap = zoneMap.get(zoneId);
        if (escortActorMap == null) {
            return null;
        }
        EscortActor escortActor = escortActorMap.get(actorId);
        return escortActor;
    }

    @Override
    public void updateEscortActor(int serverType, int zoneId, EscortActor escortActor) {
        Map<Integer, Map<Long, EscortActor>> zoneMap = ESCORT_ACTOR_MAP.get(serverType);
        if (zoneMap == null) {
            return;
        }
        Map<Long, EscortActor> escortActorMap = zoneMap.get(zoneId);
        if (escortActorMap == null) {
            return;
        }
        long actorId = (long) escortActor.getAttributes().get((byte) TypeProtocol.ActorFieldType.ACTOR_ID_VALUE);
        escortActorMap.put(actorId, escortActor);
        putHashObject(String.format(ESCORT_ACTOR_CACHE_KEY, serverType, zoneId), String.valueOf(actorId), escortActor);
    }

    @Override
    public void achieveEscortRank(int serverType, int zoneId, EscortActor escortActor) {
        if (escortActor.getEscortCoin() == 0) {
            return;
        }
        Map<Integer, ConsistentLadderRankCache<Long, EscortRank>> rankCacheMap = ESCORT_RANK_MAP.get(serverType);
        ConsistentLadderRankCache<Long, EscortRank> rankCache = rankCacheMap.get(zoneId);
        EscortRank actorRank = EscortRank.valueOf(escortActor.getAttributes(), escortActor.getEscortCoin(), escortActor.getEscortTimes(), escortActor.getEscortAchieveTime());
        rankCache.achieve(actorRank);
    }

    @Override
    public void achieveEscortRobbingRank(int serverType, int zoneId, EscortActor escortActor) {
        if (escortActor.getRobbingEscortCoin() == 0) {
            return;
        }
        Map<Integer, ConsistentLadderRankCache<Long, EscortRobbingRank>> rankCacheMap = ESCORT_ROBBING_RANK_MAP.get(serverType);
        ConsistentLadderRankCache<Long, EscortRobbingRank> rankCache = rankCacheMap.get(zoneId);
        EscortRobbingRank actorRank =
            EscortRobbingRank.valueOf(escortActor.getAttributes(), escortActor.getRobbingEscortCoin(), escortActor.getRobbingSuccessTimes(), escortActor.getRobbingTimes(),
                escortActor.getRobbingAchieveTime());
        rankCache.achieve(actorRank);
    }

    @Override
    public void achieveHelpRank(int serverType, int zoneId, EscortActor escortActor) {
        if (escortActor.getHelpTimes() == 0) {
            return;
        }
        Map<Integer, ConsistentLadderRankCache<Long, EscortHelpRank>> rankCacheMap = ESCORT_HELP_RANK_MAP.get(serverType);
        ConsistentLadderRankCache<Long, EscortHelpRank> rankCache = rankCacheMap.get(zoneId);
        EscortHelpRank actorRank = EscortHelpRank.valueOf(escortActor.getAttributes(), escortActor.getHelpTimes(), escortActor.getHelpAchieveTime());
        rankCache.achieve(actorRank);
    }

    @Override
    public CollectionResult<EscortRank> getEscortRank(int serverType, int zoneId, long actorId, Map<Byte, Object> attributes) {
        Map<Integer, ConsistentLadderRankCache<Long, EscortRank>> rankCacheMap = ESCORT_RANK_MAP.get(serverType);
        ConsistentLadderRankCache<Long, EscortRank> rankCache = rankCacheMap.get(zoneId);
        int rankLimit = globalConfigService.findGlobalConfig(GlobalConfigKey.ESCORT_RANKLIST_SHOW_LIMIT).findInt();
        Collection<EscortRank> ranks = Lists.newArrayList();
        int size = rankCache.findAll().size();
        for (long i = 1; i <= rankLimit && i <= size; i++) {
            EscortRank escortRank = rankCache.findByRank(i);
            ranks.add(escortRank);
        }

        EscortRank escortRank = rankCache.find(actorId);
        if (escortRank == null) {
            EscortActor escortActor = getEscortActor(serverType, zoneId, actorId);
            if (escortActor != null) {
                escortRank = EscortRank.valueOf(escortActor.getAttributes(), escortActor.getEscortCoin(), escortActor.getEscortTimes(), escortActor.getEscortAchieveTime());
            } else {
                escortRank = EscortRank.valueOf(attributes, 0, 0, 0);
            }
        }
        ranks.add(escortRank);

        return CollectionResult.collection(ranks);
    }

    @Override
    public CollectionResult<EscortRobbingRank> getEscortRobbingRank(int serverType, int zoneId, long actorId, Map<Byte, Object> attributes) {
        Map<Integer, ConsistentLadderRankCache<Long, EscortRobbingRank>> rankCacheMap = ESCORT_ROBBING_RANK_MAP.get(serverType);
        ConsistentLadderRankCache<Long, EscortRobbingRank> rankCache = rankCacheMap.get(zoneId);
        int rankLimit = globalConfigService.findGlobalConfig(GlobalConfigKey.ESCORT_RANKLIST_SHOW_LIMIT).findInt();
        Collection<EscortRobbingRank> ranks = Lists.newArrayList();
        int size = rankCache.findAll().size();
        for (long i = 1; i <= rankLimit && i <= size; i++) {
            EscortRobbingRank escortRobbingRank = rankCache.findByRank(i);
            ranks.add(escortRobbingRank);
        }

        EscortRobbingRank escortRobbingRank = rankCache.find(actorId);
        if (escortRobbingRank == null) {
            EscortActor escortActor = getEscortActor(serverType, zoneId, actorId);
            if (escortActor != null) {
                escortRobbingRank =
                    EscortRobbingRank.valueOf(escortActor.getAttributes(), escortActor.getRobbingEscortCoin(), escortActor.getRobbingSuccessTimes(), escortActor.getRobbingTimes(),
                        escortActor.getRobbingAchieveTime());
            } else {
                escortRobbingRank = EscortRobbingRank.valueOf(attributes, 0, 0, 0, 0);
            }
        }
        ranks.add(escortRobbingRank);

        return CollectionResult.collection(ranks);
    }

    @Override
    public CollectionResult<EscortHelpRank> getEscortHelpRank(int serverType, int zoneId, long actorId, Map<Byte, Object> attributes) {
        Map<Integer, ConsistentLadderRankCache<Long, EscortHelpRank>> rankCacheMap = ESCORT_HELP_RANK_MAP.get(serverType);
        ConsistentLadderRankCache<Long, EscortHelpRank> rankCache = rankCacheMap.get(zoneId);
        int rankLimit = globalConfigService.findGlobalConfig(GlobalConfigKey.ESCORT_RANKLIST_SHOW_LIMIT).findInt();
        Collection<EscortHelpRank> ranks = Lists.newArrayList();
        int size = rankCache.findAll().size();
        for (long i = 1; i <= rankLimit && i <= size; i++) {
            EscortHelpRank escortHelpRank = rankCache.findByRank(i);
            ranks.add(escortHelpRank);
        }

        EscortHelpRank escortHelpRank = rankCache.find(actorId);
        if (escortHelpRank == null) {
            EscortActor escortActor = getEscortActor(serverType, zoneId, actorId);
            if (escortActor != null) {
                escortHelpRank = EscortHelpRank.valueOf(escortActor.getAttributes(), escortActor.getHelpTimes(), escortActor.getHelpAchieveTime());
            } else {
                escortHelpRank = EscortHelpRank.valueOf(attributes, 0, 0);
            }
        }
        ranks.add(escortHelpRank);

        return CollectionResult.collection(ranks);
    }

    private void resetEscortCart() {
        // 清理昨天已完成镖车
        for (Map.Entry<Integer, Map<Integer, Map<Long, EscortCart>>> serverTypeEntry : ESCORT_CART_MAP.entrySet()) {
            int serverType = serverTypeEntry.getKey();
            for (Map.Entry<Integer, Map<Long, EscortCart>> entry : serverTypeEntry.getValue().entrySet()) {
                int zoneId = entry.getKey();
                List<EscortCart> list = entry.getValue().values().stream().filter(x -> x.isRewardCompletion() && !DateUtils.isToday(x.getStartTime())).collect(Collectors.toList());
                if (!list.isEmpty()) {
                    deleteEscortCart(serverType, zoneId, list);
                }
            }
        }
    }

    /**
     * 每星期结束给未领取押镖奖励的用户发邮件
     */
    private void escortEndSendMail() {
        // 清理昨天已完成镖车
        for (Map.Entry<Integer, Map<Integer, Map<Long, EscortCart>>> serverTypeEntry : ESCORT_CART_MAP.entrySet()) {
            int serverType = serverTypeEntry.getKey();
            for (Map.Entry<Integer, Map<Long, EscortCart>> entry : serverTypeEntry.getValue().entrySet()) {
                List<EscortCart> list = entry.getValue().values().stream().filter(x -> x.getEndTime() > 0 && !x.isRewardCompletion()).collect(Collectors.toList());
                if (!list.isEmpty()) {
                    for (EscortCart escortCart : list) {
                        try {
                            int serverId = (int) escortCart.getEscortActor().get((byte) BattleTypeProtocol.ActorFieldType.SERVER_ID_VALUE);
                            long actorId = Long.parseLong(escortCart.getEscortActor().get((byte) BattleTypeProtocol.ActorFieldType.ACTOR_ID_VALUE).toString());
                            EscortCarriageConfig escortCarriageConfig = EscortConfigService.getEscortCarriageConfig(escortCart.getConfigId());
                            int beRobbingTimes = Math.max(0, escortCarriageConfig.getRobTimes() - escortCart.getBeRobbingTimes());
                            // 抢劫保留比率
                            int lost = RandomUtils.TEN_THOUSAND - escortCarriageConfig.getLostRewards() * beRobbingTimes;
                            // 加成
                            int add = RandomUtils.TEN_THOUSAND + escortCart.getGvgAdd() + escortCart.getTimeAdd();
                            Collection<RewardObject> rewardList = Lists.newArrayList();
                            for (RewardObject reward : escortCarriageConfig.getBaseRewardList()) {
                                long num = NumberUtils.getPercentLongValue(NumberUtils.getPercentLongValue(reward.getCount(), add), lost);
                                rewardList.add(RewardObject.valueOf(reward.getType(), reward.getId(), num));
                            }
                            rewardList.addAll(escortCarriageConfig.getSpecialRewardList());
                            RpcClientChannel.pushNodeEvent(serverType, serverId, new EscortEndRewardEvent(actorId, rewardList));
                        } catch (Exception e) {
                            LOGGER.error(e.getMessage(), e);
                        }
                    }
                }
            }
        }
    }

    /**
     * 清除数据
     */
    private void clearData() {
        ESCORT_RANK_MAP.clear();
        ESCORT_ROBBING_RANK_MAP.clear();
        ESCORT_HELP_RANK_MAP.clear();
        ESCORT_ACTOR_MAP.clear();
        ESCORT_CART_MAP.clear();
        delete(String.format(ESCORT_ACTOR_CACHE_KEY, "*", "*"));
        delete(String.format(ESCORT_CART_CACHE_KEY, "*", "*"));
        delete(String.format(ESCORT_ESCORTED_CACHE_KEY));
        delete(String.format(ESCORT_BATTLE_REDIS_CACHE_KEY));
        this.initialize();
    }
}
