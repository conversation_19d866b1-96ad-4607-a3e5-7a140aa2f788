package cn.daxiang.hbtd.worldserver.module.zoneRank.dao.impl;

import cn.daxiang.framework.event.annotation.Event;
import cn.daxiang.framework.utils.StopWatch;
import cn.daxiang.framework.utils.rank.cache.ConsistentLadderRankCache;
import cn.daxiang.framework.utils.schedule.Schedule;
import cn.daxiang.hbtd.worldserver.core.context.ApplicationServerZoneCompleteEvent;
import cn.daxiang.hbtd.worldserver.core.event.impl.WorldServerZoneMergeEvent;
import cn.daxiang.hbtd.worldserver.core.event.impl.WorldServerZoneRefreshEvent;
import cn.daxiang.hbtd.worldserver.core.redis.RedisDao;
import cn.daxiang.hbtd.worldserver.module.server.ServerZoneHelper;
import cn.daxiang.hbtd.worldserver.module.zoneRank.dao.ZoneRankDao;
import cn.daxiang.hbtd.worldserver.module.zoneRank.model.ZoneRank;
import cn.daxiang.protocol.game.UserProtocol;
import cn.daxiang.shared.event.EventKey;
import cn.daxiang.shared.module.zoneRank.ZoneRankVO;
import cn.daxiang.shared.type.ZoneRankType;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentMap;

/**
 * <AUTHOR>
 * @date 2023/7/4
 */
@Component
public class ZoneRankDaoImpl extends RedisDao implements ZoneRankDao, ApplicationListener<ApplicationServerZoneCompleteEvent> {
    /**
     * 战区排行榜缓存Key
     * serverType_zoneId_rankType
     */
    private static final String ZONE_RANK_CACHE_KEY = "ZONE_RANK_%s_%s_%s";
    /**
     * 战力排行榜缓存Key
     * zoneId
     */
    private static final String POWER_PRAISE_CACHE_KEY = "ZONE_POWER_PRAISE_%s";
    /**
     * key:serverType,value:{key:zoneId,value:{key:rankType,value:rankCache}}
     */
    private static Map<Integer, Map<Integer, Map<Integer, ConsistentLadderRankCache<Long, ZoneRank>>>> ZONE_RANK_CACHE_MAP = Maps.newHashMap();
    /**
     * key:zoneId,value:{key:actorId,value:praiseNum}
     */
    private static Map<Integer, ConcurrentMap<Long, Long>> ACTOR_PRAISE_CACHE_MAP = Maps.newHashMap();
    @Autowired
    protected Schedule schedule;

    @Override
    public void onApplicationEvent(ApplicationServerZoneCompleteEvent event) {
        StopWatch sw = new StopWatch(true);
        this.initialize();
        this.loadData();
        sw.stop();
        LOGGER.info("zoneRank loading complete!  time:{}ms", sw.runTime());
    }

    @Event(name = EventKey.WORLD_SERVER_ZONE_REFRESH_EVENT)
    public void onWorldServerZoneRefreshEvent(WorldServerZoneRefreshEvent event) {
        this.initialize();
    }

    private void initialize() {
        for (Map.Entry<Integer, Map<Integer, List<Integer>>> mapEntry : ServerZoneHelper.getAllZoneConfig().entrySet()) {
            int serverType = mapEntry.getKey();
            Map<Integer, List<Integer>> zoneServerIdMap = mapEntry.getValue();
            Map<Integer, Map<Integer, ConsistentLadderRankCache<Long, ZoneRank>>> zoneIdRankTypeMap = ZONE_RANK_CACHE_MAP.get(serverType);
            if (zoneIdRankTypeMap == null) {
                zoneIdRankTypeMap = Maps.newHashMap();
                ZONE_RANK_CACHE_MAP.put(serverType, zoneIdRankTypeMap);
            }
            for (Integer zoneId : zoneServerIdMap.keySet()) {
                Map<Integer, ConsistentLadderRankCache<Long, ZoneRank>> rankTypeMap = zoneIdRankTypeMap.get(zoneId);
                if (rankTypeMap == null) {
                    rankTypeMap = Maps.newHashMap();
                    zoneIdRankTypeMap.put(zoneId, rankTypeMap);
                }
                for (ZoneRankType zoneRankType : ZoneRankType.values()) {
                    if (zoneRankType == ZoneRankType.NONE) {
                        continue;
                    }
                    ConsistentLadderRankCache<Long, ZoneRank> rankCache = rankTypeMap.get(zoneRankType.getId());
                    if (rankCache == null) {
                        rankCache = new ConsistentLadderRankCache<>(100, 100);
                        rankTypeMap.put(zoneRankType.getId(), rankCache);
                    }
                }
                ACTOR_PRAISE_CACHE_MAP.computeIfAbsent(zoneId, x -> Maps.newConcurrentMap());
            }
        }
    }

    private void loadData() {
        for (Map.Entry<Integer, Map<Integer, Map<Integer, ConsistentLadderRankCache<Long, ZoneRank>>>> serverTypeEntry : ZONE_RANK_CACHE_MAP.entrySet()) {
            int serverType = serverTypeEntry.getKey();
            for (Map.Entry<Integer, Map<Integer, ConsistentLadderRankCache<Long, ZoneRank>>> zoneTypeEntry : serverTypeEntry.getValue().entrySet()) {
                int zoneId = zoneTypeEntry.getKey();
                Map<Integer, ConsistentLadderRankCache<Long, ZoneRank>> typeRankCache = zoneTypeEntry.getValue();
                for (Map.Entry<Integer, ConsistentLadderRankCache<Long, ZoneRank>> rankCacheEntry : typeRankCache.entrySet()) {
                    Collection<ZoneRank> rankList = getListObject(getRedisCacheKey(serverType, zoneId, rankCacheEntry.getKey()), ZoneRank.class);
                    ConsistentLadderRankCache<Long, ZoneRank> rankCache = rankCacheEntry.getValue();
                    for (ZoneRank rank : rankList) {
                        rankCache.achieve(rank);
                    }
                }
            }
        }

        for (Map.Entry<Integer, ConcurrentMap<Long, Long>> zoneIdEntry : ACTOR_PRAISE_CACHE_MAP.entrySet()) {
            Integer zoneId = zoneIdEntry.getKey();
            Map<Long, Long> hashMap = getHashMap(getPowerPraiseCacheKey(zoneId), Long.class, Long.class);
            zoneIdEntry.getValue().putAll(hashMap);
        }
    }

    private String getRedisCacheKey(int serverType, int zoneId, int type) {
        return String.format(ZONE_RANK_CACHE_KEY, serverType, zoneId, type);
    }

    private String getPowerPraiseCacheKey(int zoneId) {
        return String.format(POWER_PRAISE_CACHE_KEY, zoneId);
    }

    @Event(name = EventKey.WORLD_SERVER_ZONE_MERGE_EVENT)
    public void onWorldServerZoneMergeEvent(WorldServerZoneMergeEvent event) {
        for (Map.Entry<Integer, Map<Integer, Integer>> serverTypeEntry : event.getRefreshZoneMap().entrySet()) {
            int serverType = serverTypeEntry.getKey();
            Map<Integer, Map<Integer, ConsistentLadderRankCache<Long, ZoneRank>>> zoneTypeRankMap = ZONE_RANK_CACHE_MAP.get(serverType);
            for (Map.Entry<Integer, Integer> zoneEntry : serverTypeEntry.getValue().entrySet()) {
                int oldZoneId = zoneEntry.getKey();
                int newZoneId = zoneEntry.getValue();

                Map<Integer, ConsistentLadderRankCache<Long, ZoneRank>> oldTypeRankCache = zoneTypeRankMap.get(oldZoneId);
                Map<Integer, ConsistentLadderRankCache<Long, ZoneRank>> newTypeRankCache = zoneTypeRankMap.get(newZoneId);
                for (Map.Entry<Integer, ConsistentLadderRankCache<Long, ZoneRank>> rankCacheEntry : oldTypeRankCache.entrySet()) {
                    Integer rankType = rankCacheEntry.getKey();
                    for (ZoneRank rank : oldTypeRankCache.get(rankType).findAll()) {
                        rank.reset();
                        newTypeRankCache.get(rankType).achieve(rank);
                    }
                    //删除旧的
                    oldTypeRankCache.get(rankType).cleanRank();
                    clearSortSet(getRedisCacheKey(serverType, oldZoneId, rankType));
                    //更新新的
                    String cacheKey = getRedisCacheKey(serverType, newZoneId, rankType);
                    clearSortSet(cacheKey);
                    addListObject(cacheKey, newTypeRankCache.get(rankType).findAll());
                }

                ConcurrentMap<Long, Long> oldPraiseMap = ACTOR_PRAISE_CACHE_MAP.get(oldZoneId);
                ConcurrentMap<Long, Long> newPraiseMap = ACTOR_PRAISE_CACHE_MAP.get(newZoneId);
                newPraiseMap.putAll(oldPraiseMap);
                ACTOR_PRAISE_CACHE_MAP.remove(oldZoneId);
                //删除老战区数据
                delete(getPowerPraiseCacheKey(oldZoneId));
                //将老战区的点赞数据存到新战区
                String newPowerPraiseCacheKey = getPowerPraiseCacheKey(newZoneId);
                for (Map.Entry<Long, Long> oldPraiseEntry : oldPraiseMap.entrySet()) {
                    putHashObject(newPowerPraiseCacheKey, oldPraiseEntry.getKey().toString(), oldPraiseEntry.getValue());
                }
            }
        }
    }

    @Override
    public Collection<ZoneRankVO> getZoneRankList(int serverType, int zoneId, int type, ZoneRankVO rankVO) {
        Map<Integer, Map<Integer, ConsistentLadderRankCache<Long, ZoneRank>>> zoneTypeMap = ZONE_RANK_CACHE_MAP.get(serverType);
        if (zoneTypeMap == null) {
            LOGGER.error("this serverType:{} is not found in ZONE_RANK_CACHE_MAP", serverType);
            return Collections.emptyList();
        }
        Map<Integer, ConsistentLadderRankCache<Long, ZoneRank>> typeRankCache = zoneTypeMap.get(zoneId);
        if (typeRankCache == null) {
            LOGGER.error("this zoneId:{} is not found in zoneTypeMap", zoneId);
            return Collections.emptyList();
        }
        ConsistentLadderRankCache<Long, ZoneRank> rankCache = typeRankCache.get(type);
        if (rankCache == null) {
            LOGGER.error("this type:{} is not found in typeRankCache", type);
            return Collections.emptyList();
        }
        Collection<ZoneRank> ranks = rankCache.findRanks(1);
        long actorId = (long) rankVO.getAttributes().get((byte) 5);
        ZoneRank zoneRank = rankCache.find(actorId);
        Collection<ZoneRankVO> rankVOList = Lists.newArrayList();
        if (zoneRank != null) {
            ranks.add(zoneRank);
        }
        for (ZoneRank rank : ranks) {
            rankVOList.add(ZoneRankVO.valueOf(rank.getAttributes(), rank.getRank(), rank.getValue()));
        }
        if (zoneRank == null) {
            rankVOList.add(rankVO);
        }
        return rankVOList;
    }

    @Override
    public Collection<ZoneRankVO> getPowerZoneRankList(int serverType, int zoneId, ZoneRankVO rankVO) {
        Map<Integer, Map<Integer, ConsistentLadderRankCache<Long, ZoneRank>>> zoneTypeMap = ZONE_RANK_CACHE_MAP.get(serverType);
        if (zoneTypeMap == null) {
            LOGGER.error("this serverType:{} is not found in ZONE_RANK_CACHE_MAP", serverType);
            return Collections.emptyList();
        }
        Map<Integer, ConsistentLadderRankCache<Long, ZoneRank>> typeRankCache = zoneTypeMap.get(zoneId);
        if (typeRankCache == null) {
            LOGGER.error("this zoneId:{} is not found in zoneTypeMap", zoneId);
            return Collections.emptyList();
        }
        ConsistentLadderRankCache<Long, ZoneRank> rankCache = typeRankCache.get(UserProtocol.RankType.POWER_RANK_VALUE);
        if (rankCache == null) {
            LOGGER.error("type:POWER_RANK is not found in typeRankCache");
            return Collections.emptyList();
        }
        Collection<ZoneRank> ranks = rankCache.findRanks(1);
        long actorId = (long) rankVO.getAttributes().get((byte) 5);
        ZoneRank zoneRank = rankCache.find(actorId);
        Collection<ZoneRankVO> rankVOList = Lists.newArrayList();
        if (zoneRank != null) {
            ranks.add(zoneRank);
        }
        ConcurrentMap<Long, Long> praiseNumMap = ACTOR_PRAISE_CACHE_MAP.get(zoneId);
        for (ZoneRank rank : ranks) {
            long praiseNum = praiseNumMap == null ? 0L : praiseNumMap.getOrDefault(rank.getKey(), 0L);
            rankVOList.add(ZoneRankVO.valueOf(rank.getRank(), rank.getValue(), praiseNum, rank.getAttributes()));
        }
        if (zoneRank == null) {
            rankVOList.add(rankVO);
        }
        return rankVOList;
    }

    @Override
    public void praisePowerZoneRank(int serverType, int zoneId, long rank) {
        ConcurrentMap<Long, Long> praiseNumMap = ACTOR_PRAISE_CACHE_MAP.get(zoneId);
        if (praiseNumMap == null) {
            LOGGER.error("this zoneId:{} is not found in ACTOR_PRAISE_CACHE_MAP", zoneId);
            return;
        }
        Map<Integer, Map<Integer, ConsistentLadderRankCache<Long, ZoneRank>>> zoneTypeMap = ZONE_RANK_CACHE_MAP.get(serverType);
        if (zoneTypeMap == null) {
            LOGGER.error("this serverType:{} is not found in ZONE_RANK_CACHE_MAP", serverType);
            return;
        }
        Map<Integer, ConsistentLadderRankCache<Long, ZoneRank>> typeRankCache = zoneTypeMap.get(zoneId);
        if (typeRankCache == null) {
            LOGGER.error("this zoneId:{} is not found in zoneTypeMap", zoneId);
            return;
        }
        ConsistentLadderRankCache<Long, ZoneRank> rankCache = typeRankCache.get(UserProtocol.RankType.POWER_RANK_VALUE);
        if (rankCache == null) {
            LOGGER.error("this type:POWER_RANK is not found in typeRankCache");
            return;
        }
        ZoneRank zoneRank = rankCache.findByRank(rank);
        if (zoneRank == null) {
            LOGGER.info("get POWER_RANK by rank is err! POWER_RANK size:{},rank:{}", rankCache.findSize(), rank);
            return;
        }
        Long value = praiseNumMap.merge(zoneRank.getKey(), 1L, Long::sum);
        putHashObject(getPowerPraiseCacheKey(zoneId), zoneRank.getKey().toString(), value);
    }

    @Override
    public void refreshZoneRank(int serverType, int zoneId, int type, ZoneRankVO rankVO) {
        Map<Integer, Map<Integer, ConsistentLadderRankCache<Long, ZoneRank>>> zoneTypeMap = ZONE_RANK_CACHE_MAP.get(serverType);
        if (zoneTypeMap == null) {
            LOGGER.error("this serverType:{} is not found in ZONE_RANK_CACHE_MAP", serverType);
            return;
        }
        Map<Integer, ConsistentLadderRankCache<Long, ZoneRank>> typeRankCache = zoneTypeMap.get(zoneId);
        if (typeRankCache == null) {
            LOGGER.error("this zoneId:{} is not found in zoneTypeMap", zoneId);
            return;
        }
        ConsistentLadderRankCache<Long, ZoneRank> rankCache = typeRankCache.get(type);
        if (rankCache == null) {
            LOGGER.error("this type:{} is not found in typeRankCache", type);
            return;
        }
        long actorId = (long) rankVO.getAttributes().get((byte) 5);
        ZoneRank zoneRank = rankCache.find(actorId);
        if (zoneRank == null) {
            zoneRank = ZoneRank.valueOf(rankVO.getAttributes(), rankVO.getValue());
        } else {
            zoneRank.refresh(rankVO.getAttributes(), rankVO.getValue());
        }
        rankCache.achieve(zoneRank);
        if (rankCache.find(actorId) != null) {
            Collection<ZoneRank> rankList = rankCache.findAll();
            String cacheKey = getRedisCacheKey(serverType, zoneId, type);
            clearSortSet(cacheKey);
            addListObject(cacheKey, rankList);
        }
    }

    @Override
    public void pushRanks(int serverType, int zoneId, int type, Collection<ZoneRankVO> zoneRankList) {
        Map<Integer, Map<Integer, ConsistentLadderRankCache<Long, ZoneRank>>> zoneTypeMap = ZONE_RANK_CACHE_MAP.get(serverType);
        if (zoneTypeMap == null) {
            LOGGER.error("this serverType:{} is not found in ZONE_RANK_CACHE_MAP", serverType);
            return;
        }
        Map<Integer, ConsistentLadderRankCache<Long, ZoneRank>> typeRankCache = zoneTypeMap.get(zoneId);
        if (typeRankCache == null) {
            LOGGER.error("this zoneId:{} is not found in zoneTypeMap", zoneId);
            return;
        }
        ConsistentLadderRankCache<Long, ZoneRank> rankCache = typeRankCache.get(type);
        if (rankCache == null) {
            LOGGER.error("this type:{} is not found in typeRankCache", type);
            return;
        }
        ConcurrentMap<Long, Long> praiseNumMap = ACTOR_PRAISE_CACHE_MAP.get(zoneId);
        if (praiseNumMap == null) {
            LOGGER.error("ACTOR_PRAISE_CACHE_MAP is not found this zoneId:{}", zoneId);
        }
        for (ZoneRankVO rankVO : zoneRankList) {
            long actorId = (long) rankVO.getAttributes().get((byte) 5);
            ZoneRank zoneRank = rankCache.find(actorId);
            if (zoneRank == null) {
                zoneRank = ZoneRank.valueOf(rankVO.getAttributes(), rankVO.getValue(), rankVO.getTime());
            } else {
                zoneRank.refresh(rankVO.getAttributes(), rankVO.getValue());
            }
            rankCache.achieve(zoneRank);
            if (type == UserProtocol.RankType.POWER_RANK_VALUE && praiseNumMap != null) {
                praiseNumMap.put(actorId, rankVO.getPraiseNum());
                putHashObject(getPowerPraiseCacheKey(zoneId), String.valueOf(actorId), rankVO.getPraiseNum());
            }
        }
        Collection<ZoneRank> rankList = rankCache.findAll();
        String cacheKey = getRedisCacheKey(serverType, zoneId, type);
        clearSortSet(cacheKey);
        addListObject(cacheKey, rankList);
    }
}
