package cn.daxiang.hbtd.worldserver.module.imperialism.dao;

import cn.daxiang.hbtd.worldserver.module.imperialism.model.ImperialismGlobal;
import cn.daxiang.hbtd.worldserver.module.imperialism.model.ImperialismPalace;
import cn.daxiang.hbtd.worldserver.module.imperialism.model.ImperialismPalaceActor;
import cn.daxiang.hbtd.worldserver.module.imperialism.model.ImperialismRegionRank;

import java.util.Collection;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/11/13
 */
public interface ImperialismDao {

    /**
     * 获取决战皇城公共信息
     *
     * @return
     */
    ImperialismGlobal getImperialismGlobal();

    /**
     * 刷新决战皇城公共信息
     *
     * @param imperialismGlobal
     */
    void refreshImperialismGlobal(ImperialismGlobal imperialismGlobal);

    /**
     * 获取宫殿信息
     *
     * @param serverType
     * @param imperialismRegionId
     * @param id
     * @return
     */
    Optional<ImperialismPalace> getImperialismPalace(int serverType, int imperialismRegionId, long id);

    /**
     * 获取宫殿信息列表
     *
     * @param serverType
     * @param imperialismRegionId
     * @return
     */
    Collection<ImperialismPalace> getImperialismPalaces(int serverType, int imperialismRegionId);

    /**
     * 获取宫殿信息
     *
     * @param serverType
     * @param imperialismRegionId
     * @param actorId
     * @return
     */
    Optional<ImperialismPalace> getImperialismPalaceByActorId(int serverType, int imperialismRegionId, long actorId);

    /**
     * 创建殿主
     *
     * @param serverType
     * @param imperialismRegionRank
     */
    ImperialismPalace createImperialismPalace(int serverType, ImperialismRegionRank imperialismRegionRank);

    /**
     * 重置宫殿
     *
     * @param serverType
     * @param imperialismRegionId
     */
    void resetImperialismPalace(int serverType, int imperialismRegionId);
    /**
     * 刷新殿主
     *
     * @param serverType
     * @param imperialismPalace
     */
    void updateImperialismPalace(int serverType, ImperialismPalace imperialismPalace);

    /**
     * 获取宫殿角色信息
     *
     * @param serverType
     * @param imperialismRegionId
     * @param actorId
     * @return
     */
    Optional<ImperialismPalaceActor> getImperialismPalaceActor(int serverType, int imperialismRegionId, long actorId);

    /**
     * 创建宫殿角色信息
     *
     * @param serverType
     * @param imperialismRegionId
     * @param actorId
     * @param attributes
     * @param prerogative
     * @return
     */
    ImperialismPalaceActor createImperialismPalaceActor(int serverType, int imperialismRegionId, long actorId, Map<Byte, Object> attributes, boolean prerogative);

    /**
     * 刷新宫殿角色信息
     *
     * @param serverType
     * @param imperialismPalaceActor
     * @return
     */
    void updateImperialismPalaceActor(int serverType, ImperialismPalaceActor imperialismPalaceActor);
}
