package cn.daxiang.hbtd.worldserver.module.heroTag;

import cn.daxiang.framework.result.CollectionResult;
import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.rpc.world.WorldHeroTagRpc;
import cn.daxiang.hbtd.worldserver.module.heroTag.dao.HeroTagDao;
import cn.daxiang.shared.module.heroTag.HeroTagEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;

/**
 * <AUTHOR>
 * @date 2023/12/20
 */
@Component
public class WorldHeroTagRpcImpl implements WorldHeroTagRpc {

    @Autowired
    private HeroTagDao heroTagDao;

    @Override
    public CollectionResult<HeroTagEntity> getHeroTagList() {
        return heroTagDao.getHeroTagList();
    }

    @Override
    public Result pickHeroTag(int heroId, Collection<Integer> tagIds) {
        return heroTagDao.pickHeroTag(heroId, tagIds);
    }
}
