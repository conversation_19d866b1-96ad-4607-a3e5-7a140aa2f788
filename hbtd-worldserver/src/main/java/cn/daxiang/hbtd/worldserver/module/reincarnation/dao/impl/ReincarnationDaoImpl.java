package cn.daxiang.hbtd.worldserver.module.reincarnation.dao.impl;

import cn.daxiang.framework.utils.RandomUtils;
import cn.daxiang.framework.utils.TimeConstant;
import cn.daxiang.framework.utils.lock.ChainLock;
import cn.daxiang.framework.utils.lock.LockUtils;
import cn.daxiang.framework.utils.rank.cache.ConsistentLadderRankCache;
import cn.daxiang.hbtd.worldserver.core.context.ApplicationServerZoneCompleteEvent;
import cn.daxiang.hbtd.worldserver.core.dataconfig.model.ReincarnationRobotConfig;
import cn.daxiang.hbtd.worldserver.core.dataconfig.service.ReincarnationConfigService;
import cn.daxiang.hbtd.worldserver.core.dataconfig.service.WorldGlobalConfigService;
import cn.daxiang.hbtd.worldserver.core.redis.RedisDao;
import cn.daxiang.hbtd.worldserver.module.reincarnation.dao.ReincarnationDao;
import cn.daxiang.hbtd.worldserver.module.reincarnation.helper.ReincarnationHelper;
import cn.daxiang.hbtd.worldserver.module.reincarnation.model.ReincarnationActor;
import cn.daxiang.hbtd.worldserver.module.reincarnation.model.ReincarnationBattlefield;
import cn.daxiang.hbtd.worldserver.module.reincarnation.model.ReincarnationBattlefieldRank;
import cn.daxiang.hbtd.worldserver.module.reincarnation.model.ReincarnationRank;
import cn.daxiang.protocol.game.ReincarnationProtocol.ReincarnationState;
import cn.daxiang.protocol.game.TypeProtocol.ActorFieldType;
import cn.daxiang.shared.GlobalConfigKey;
import cn.daxiang.shared.module.reincarnation.ReincarnationInfo;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Comparator;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @date 2023/12/23
 */
@Component
public class ReincarnationDaoImpl extends RedisDao implements ReincarnationDao, ApplicationListener<ApplicationServerZoneCompleteEvent> {
    /**
     * 轮回战场信息Key
     */
    private static final String REINCARNATION_KEY = "REINCARNATION";
    /**
     * 轮回战场玩家Key
     */
    private static final String REINCARNATION_ACTOR_KEY = "REINCARNATION_ACTOR";
    /**
     * 轮回战场排名Key
     */
    private static final String REINCARNATION_RANK_KEY = "REINCARNATION_RANK";
    /**
     * 轮回战场玩家信息
     * {key:actorId,value:ReincarnationActor}
     */
    private static final Map<Long, ReincarnationActor> REINCARNATION_ACTOR_MAP = Maps.newConcurrentMap();
    /**
     * 轮回战场战场信息
     * {key:battlefieldId,value:ReincarnationBattlefield}
     */
    private static final Map<Integer, ReincarnationBattlefield> REINCARNATION_BATTLEFIELD_MAP = Maps.newConcurrentMap();
    /**
     * 轮回战场空闲ID
     * {key:floor,value:Collection<battlefieldId>}
     */
    private static final Map<Integer, Collection<Integer>> REINCARNATION_IDLE_BATTLEFIELD_ID_MAP = Maps.newConcurrentMap();
    /**
     * 战场ID
     */
    private static final AtomicInteger BATTLEFIELD_ID = new AtomicInteger(TimeConstant.ONE_SECOND_MILLISECOND);

    /**
     * 战场排名
     */
    private static final ConsistentLadderRankCache<Long, ReincarnationRank> REINCARNATION_RANK_CACHE = new ConsistentLadderRankCache<>(100, 100);
    @Autowired
    private WorldGlobalConfigService globalConfigService;

    @Override
    public void onApplicationEvent(ApplicationServerZoneCompleteEvent event) {
        Collection<ReincarnationRank> reincarnationRankList = getListObject(REINCARNATION_RANK_KEY, ReincarnationRank.class);
        for (ReincarnationRank reincarnationRank : reincarnationRankList) {
            REINCARNATION_RANK_CACHE.achieve(reincarnationRank);
        }
        LOGGER.info("Reincarnation Rank initialize completed. count:{}", REINCARNATION_RANK_CACHE.findSize());
        Collection<ReincarnationActor> reincarnationActors = getHashValues(REINCARNATION_ACTOR_KEY, ReincarnationActor.class);
        for (ReincarnationActor reincarnationActor : reincarnationActors) {
            REINCARNATION_ACTOR_MAP.put(reincarnationActor.getActorId(), reincarnationActor);
            ReincarnationBattlefield reincarnationBattlefield = REINCARNATION_BATTLEFIELD_MAP.get(reincarnationActor.getBattlefieldId());
            if (reincarnationBattlefield == null) {
                reincarnationBattlefield = ReincarnationBattlefield.valueOf(reincarnationActor.getBattlefieldId(), reincarnationActor.getFloor());
                REINCARNATION_BATTLEFIELD_MAP.put(reincarnationBattlefield.getBattlefieldId(), reincarnationBattlefield);
            }
            ReincarnationBattlefieldRank rank =
                ReincarnationBattlefieldRank.valueOf(reincarnationActor.getActorId(), reincarnationActor.getDefeats().size(), reincarnationActor.getRefreshDefeatTime());
            reincarnationBattlefield.achieve(rank);
        }
        LOGGER.info("Reincarnation Actor initialize completed. count:{}", REINCARNATION_ACTOR_MAP.size());
        for (Integer floor : ReincarnationConfigService.getFloorList()) {
            REINCARNATION_IDLE_BATTLEFIELD_ID_MAP.put(floor, Sets.newConcurrentHashSet());
        }
        int battlefieldLimit = globalConfigService.findGlobalConfig(GlobalConfigKey.REINCARNATION_BATTLEFIELD_LIMIT).findInt();
        int battlefieldPlayerLimit = globalConfigService.findGlobalConfig(GlobalConfigKey.REINCARNATION_BATTLEFIELD_PLAYER_LIMIT).findInt();
        for (ReincarnationBattlefield battlefield : REINCARNATION_BATTLEFIELD_MAP.values()) {
            int count = battlefield.getCount();
            for (int i = count + 1; i <= battlefieldLimit; i++) {
                Optional<ReincarnationRobotConfig> optional = ReincarnationConfigService.getReincarnationRobotConfig(battlefield.getFloor(), i);
                optional.ifPresent(monsterGroupConfig -> battlefield.achieve(
                    ReincarnationBattlefieldRank.valueOf((long) monsterGroupConfig.getAttributes().get((byte) ActorFieldType.ACTOR_ID.getNumber()))));
            }
            if (count < battlefieldPlayerLimit) {
                Collection<Integer> idleList = REINCARNATION_IDLE_BATTLEFIELD_ID_MAP.get(battlefield.getFloor());
                idleList.add(battlefield.getBattlefieldId());
            }
        }
        if (!REINCARNATION_BATTLEFIELD_MAP.isEmpty()) {
            BATTLEFIELD_ID.set(REINCARNATION_BATTLEFIELD_MAP.keySet().stream().max(Comparator.naturalOrder()).get());
        }
        LOGGER.info("Reincarnation Battlefield initialize completed. count:{}", REINCARNATION_BATTLEFIELD_MAP.size());
    }

    @Override
    public ReincarnationInfo getReincarnationInfo() {
        ReincarnationInfo reincarnationInfo = this.get(ReincarnationInfo.class, REINCARNATION_KEY);
        if (reincarnationInfo == null) {
            reincarnationInfo = ReincarnationInfo.valueOf(ReincarnationState.REINCARNATION_STATE_BATTLE, ReincarnationHelper.getSleepTime());
            this.refreshReincarnationInfo(reincarnationInfo);
        }
        return reincarnationInfo;
    }

    @Override
    public void refreshReincarnationInfo(ReincarnationInfo reincarnationInfo) {
        this.update(reincarnationInfo, REINCARNATION_KEY);
    }

    @Override
    public Collection<ReincarnationBattlefield> getReincarnationBattlefieldList() {
        return REINCARNATION_BATTLEFIELD_MAP.values();
    }

    @Override
    public Optional<ReincarnationBattlefield> getReincarnationBattlefield(int battlefieldId) {
        return Optional.ofNullable(REINCARNATION_BATTLEFIELD_MAP.get(battlefieldId));
    }

    @Override
    public void enterBattlefield(Map<Byte, Object> attributes, int layer, int floor) {
        int battlefieldLimit = globalConfigService.findGlobalConfig(GlobalConfigKey.REINCARNATION_BATTLEFIELD_LIMIT).findInt();
        int battlefieldPlayerLimit = globalConfigService.findGlobalConfig(GlobalConfigKey.REINCARNATION_BATTLEFIELD_PLAYER_LIMIT).findInt();
        Collection<Integer> idleList = REINCARNATION_IDLE_BATTLEFIELD_ID_MAP.get(floor);
        ChainLock lock = LockUtils.getLock(idleList);
        try {
            lock.lock();
            Integer battlefieldId = RandomUtils.randomHit(idleList);
            if (battlefieldId == null) {
                battlefieldId = BATTLEFIELD_ID.incrementAndGet();
                idleList.add(battlefieldId);
                REINCARNATION_BATTLEFIELD_MAP.put(battlefieldId, ReincarnationBattlefield.valueOf(battlefieldId, floor));
            }
            ReincarnationBattlefield battlefield = REINCARNATION_BATTLEFIELD_MAP.get(battlefieldId);
            ChainLock battlefieldLock = LockUtils.getLock(battlefield);
            try {
                battlefieldLock.lock();
                if (battlefield.getRanks().isEmpty()) {
                    for (int i = 2; i <= battlefieldLimit; i++) {
                        Optional<ReincarnationRobotConfig> optional = ReincarnationConfigService.getReincarnationRobotConfig(battlefield.getFloor(), i);
                        optional.ifPresent(
                            e -> battlefield.achieve(ReincarnationBattlefieldRank.valueOf((long) e.getAttributes().get((byte) ActorFieldType.ACTOR_ID.getNumber()))));
                    }
                } else {
                    Optional<ReincarnationRobotConfig> optional = ReincarnationConfigService.getReincarnationRobotConfig(battlefield.getFloor(), battlefield.getPlayerCount() + 1);
                    optional.ifPresent(e -> this.exitBattlefield(idleList, battlefield, (long) e.getAttributes().get((byte) ActorFieldType.ACTOR_ID.getNumber())));
                }
                ReincarnationActor reincarnationActor = ReincarnationActor.valueOf(attributes, layer, floor, battlefieldId);
                this.updateReincarnationActor(reincarnationActor);
                this.refreshReincarnationRank(reincarnationActor);
                battlefield.achieve(ReincarnationBattlefieldRank.valueOf(reincarnationActor.getActorId(), 0, System.currentTimeMillis()));
                if (battlefield.getPlayerCount() >= battlefieldPlayerLimit) {
                    idleList.remove(battlefieldId);
                }
            } finally {
                battlefieldLock.unlock();
            }
        } finally {
            lock.unlock();
        }
    }

    @Override
    public void exitBattlefield(ReincarnationBattlefield battlefield, long actorId) {
        Collection<Integer> idleList = REINCARNATION_IDLE_BATTLEFIELD_ID_MAP.get(battlefield.getFloor());
        ChainLock lock = LockUtils.getLock(idleList);
        try {
            lock.lock();
            this.exitBattlefield(idleList, battlefield, actorId);
        } finally {
            lock.unlock();
        }
    }

    private void exitBattlefield(Collection<Integer> idleList, ReincarnationBattlefield battlefield, long actorId) {
        Collection<ReincarnationBattlefieldRank> ranks = battlefield.getRanks();
        for (ReincarnationBattlefieldRank rank : ranks) {
            if (rank.getKey() <= 0 || rank.getKey() == actorId) {
                continue;
            }
            Optional<ReincarnationActor> optional = this.getReincarnationActor(rank.getKey());
            if (optional.isPresent()) {
                ReincarnationActor reincarnationActor = optional.get();
                if (reincarnationActor.removeDefeat(actorId)) {
                    ReincarnationBattlefieldRank battlefieldRank = battlefield.getRank(reincarnationActor.getActorId());
                    battlefieldRank.refresh(reincarnationActor.getDefeats().size());
                    battlefield.achieve(battlefieldRank);
                    this.updateReincarnationActor(reincarnationActor);
                }
            }
        }
        battlefield.remove(actorId);
        REINCARNATION_ACTOR_MAP.remove(actorId);
        this.deleteHashObject(REINCARNATION_ACTOR_KEY, String.valueOf(actorId));
        // 如果是玩家的话，判断战场里玩家为0则解散战场，大于0添加机器人
        if (actorId > 0) {
            if (battlefield.getPlayerCount() <= 0) {
                idleList.remove(battlefield.getBattlefieldId());
                REINCARNATION_BATTLEFIELD_MAP.remove(battlefield.getBattlefieldId());
            } else {
                idleList.add(battlefield.getBattlefieldId());
                Optional<ReincarnationRobotConfig> optional = ReincarnationConfigService.getReincarnationRobotConfig(battlefield.getFloor(), battlefield.getPlayerCount() + 1);
                optional.ifPresent(e -> battlefield.achieve(ReincarnationBattlefieldRank.valueOf((long) e.getAttributes().get((byte) ActorFieldType.ACTOR_ID.getNumber()))));
            }
        }
    }

    @Override
    public Optional<ReincarnationActor> getReincarnationActor(long actorId) {
        return Optional.ofNullable(REINCARNATION_ACTOR_MAP.get(actorId));
    }

    @Override
    public void updateReincarnationActor(ReincarnationActor reincarnationActor) {
        REINCARNATION_ACTOR_MAP.put(reincarnationActor.getActorId(), reincarnationActor);
        this.putHashObject(REINCARNATION_ACTOR_KEY, String.valueOf(reincarnationActor.getActorId()), reincarnationActor);
    }

    @Override
    public Collection<ReincarnationRank> getReincarnationRankList() {
        return REINCARNATION_RANK_CACHE.findAll();
    }

    @Override
    public void refreshReincarnationRank(ReincarnationActor reincarnationActor) {
        ReincarnationRank rank = REINCARNATION_RANK_CACHE.find(reincarnationActor.getActorId());
        if (rank == null) {
            rank = ReincarnationRank.valueOf(reincarnationActor.getActorId(), reincarnationActor.getAttributes(), reincarnationActor.getLayer(), reincarnationActor.getFloor(),
                System.currentTimeMillis());
        } else {
            if (ReincarnationConfigService.isLast(rank.getLayer(), rank.getFloor())) {
                return;
            }
            rank.refresh(reincarnationActor.getAttributes(), reincarnationActor.getLayer(), reincarnationActor.getFloor());
        }
        REINCARNATION_RANK_CACHE.achieve(rank);
        rank = REINCARNATION_RANK_CACHE.find(reincarnationActor.getActorId());
        if (rank != null) {
            Collection<ReincarnationRank> rankList = REINCARNATION_RANK_CACHE.findAll();
            clearSortSet(REINCARNATION_RANK_KEY);
            addListObject(REINCARNATION_RANK_KEY, rankList);
        }
    }

    @Override
    public void clearReincarnation() {
        REINCARNATION_ACTOR_MAP.clear();
        delete(REINCARNATION_ACTOR_KEY);
        REINCARNATION_BATTLEFIELD_MAP.clear();
        for (Integer floor : ReincarnationConfigService.getFloorList()) {
            REINCARNATION_IDLE_BATTLEFIELD_ID_MAP.put(floor, Sets.newConcurrentHashSet());
        }
        BATTLEFIELD_ID.set(TimeConstant.ONE_SECOND_MILLISECOND);
    }

    @Override
    public void serverCompose(int serverId, Collection<Integer> composes) {
        REINCARNATION_ACTOR_MAP.values().stream().filter(e -> composes.contains(e.getServer())).forEach(e -> {
            e.getAttributes().put(((byte) ActorFieldType.CURRENT_SERVER_ID.getNumber()), serverId);
            this.updateReincarnationActor(e);
        });
    }

    @Override
    public Map<Integer, Collection<Integer>> getIdleBattlefield() {
        return REINCARNATION_IDLE_BATTLEFIELD_ID_MAP;
    }
}
