package cn.daxiang.hbtd.worldserver.module.chess;

import cn.daxiang.framework.result.TResult;
import cn.daxiang.framework.rpc.world.WorldChessRpc;
import cn.daxiang.hbtd.worldserver.module.chess.facade.ChessFacade;
import cn.daxiang.shared.module.chess.model.ChessBattleRecordVO;
import cn.daxiang.shared.module.chess.model.ChessWorldRankVO;
import cn.daxiang.shared.module.chess.model.entity.ChessStoryRecord;
import cn.daxiang.shared.module.chess.model.entity.ChessTurntableRecordEntity;
import cn.daxiang.shared.module.chess.model.entity.MonsterLineup;
import cn.daxiang.shared.reward.RewardObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/5/5
 */
@Component
public class WorldChessRpcImpl implements WorldChessRpc {
    @Autowired
    ChessFacade chessFacade;

    @Override
    public int getCirculate() {
        return chessFacade.getCirculate();
    }

    @Override
    public Map<Integer, MonsterLineup> getMonsterLineupMap() {
        return chessFacade.getMonsterLineupMap();
    }

    @Override
    public Collection<ChessWorldRankVO> getChessZoneRank(int serverType, int serverId) {
        return chessFacade.getChessZoneRank(serverType, serverId);
    }

    @Override
    public void refreshChessRank(int serverType, int serverId, ChessWorldRankVO rankVO) {
        chessFacade.refreshChessRank(serverType, serverId, rankVO);
    }

    @Override
    public Collection<ChessStoryRecord> getChessStoryRecords(int serverType, int serverId, int storyId) {
        return chessFacade.getChessStoryRecords(serverType, serverId, storyId);
    }

    @Override
    public void recordBattle(int serverType, int serverId, ChessBattleRecordVO recordVO) {
        chessFacade.recordBattle(serverType, serverId, recordVO.getAttributes(), recordVO.getHeroMap(), recordVO.getStoryId(), recordVO.getBattleReplay());
    }

    @Override
    public TResult<byte[]> getBattleReplay(int serverType, int serverId, int storyId, long battleId) {
        return chessFacade.getBattleReplay(serverType, serverId, storyId, battleId);
    }

    @Override
    public List<ChessTurntableRecordEntity> getChessTurntableRewardRecord(int serverType, int serverId) {
        return chessFacade.getChessTurntableRewardRecord(serverType, serverId);
    }

    @Override
    public void refreshChessTurntableRewardRecord(int serverType, int serverId, Map<Byte, Object> attributes, Collection<RewardObject> rewards, String des, int limit) {
        chessFacade.refreshChessTurntableRewardRecord(serverType, serverId, attributes, rewards, des, limit);
    }
}
