package cn.daxiang.hbtd.worldserver.core.dataconfig.convert.impl;

import cn.daxiang.hbtd.worldserver.core.dataconfig.convert.IConfigable;
import cn.daxiang.shared.reward.RewardObject;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Maps;

import java.util.TreeMap;

public class RewardObjectMapConfig implements IConfigable {
    /**
     * [[key,[rewardType,id,num]],[key,[rewardType,id,num]]]
     */
    private TreeMap<Integer, RewardObject> cache = Maps.newTreeMap();

    public static void main(String[] args) {
        String s = "[[1,[]],[2,[21,22,23]]]";
        RewardObjectMapConfig config = new RewardObjectMapConfig();
        config.buildObject(s);
        System.err.println(config.cache);
    }

    @Override
    public void buildObject(String config) {
        JSONArray array = JSONArray.parseArray(config);
        for (Object item : array) {
            JSONArray valueArray = JSONArray.parseArray(item.toString());
            JSONArray rewardArray = JSONArray.parseArray(valueArray.getString(1));
            RewardObject rewardObject;
            if (!rewardArray.isEmpty()) {
                rewardObject = RewardObject.valueOf(rewardArray);
            } else {
                rewardObject = new RewardObject();
            }
            cache.put(valueArray.getInteger(0), rewardObject);
        }
    }

    public RewardObject getRewardObject(int key) {
        return cache.floorEntry(key).getValue();
    }

    public boolean contains(int key) {
        return cache.containsKey(key);
    }

    public RewardObject getRewardObjectByKey(int key) {
        return cache.get(key);
    }

    public int getLastKey() {
        return cache.lastKey();
    }

    public int getFirstKey() {
        return cache.firstKey();
    }

}
