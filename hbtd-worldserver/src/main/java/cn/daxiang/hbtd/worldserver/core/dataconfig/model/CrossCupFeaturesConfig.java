package cn.daxiang.hbtd.worldserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.dataconfig.annotation.FieldIgnore;
import cn.daxiang.framework.identity.IdentiyKey;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;

import java.util.Collection;

/**
 * 巅峰竞技场特性生成表
 *
 * <AUTHOR>
 * @date 2025/3/25
 */
@DataFile(fileName = "cross_cup_features_config")
public class CrossCupFeaturesConfig implements ModelAdapter {
    /**
     * 唯一ID
     */
    private int id;
    /**
     * 技能id列表
     */
    private String skillId;
    /**
     * 权重
     */
    private int weight;

    @FieldIgnore
    public Collection<Integer> skillIdList = Lists.newArrayList();

    @Override
    public void initialize() {
        JSONArray array = JSONArray.parseArray(skillId);
        skillIdList.addAll(array.toJavaList(Integer.class));
    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(id);
    }

    public int getId() {
        return id;
    }

    public int getWeight() {
        return weight;
    }

    public Collection<Integer> getSkillIdList() {
        return skillIdList;
    }
}
