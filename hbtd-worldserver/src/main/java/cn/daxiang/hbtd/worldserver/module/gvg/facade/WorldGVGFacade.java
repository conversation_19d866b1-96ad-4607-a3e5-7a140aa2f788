package cn.daxiang.hbtd.worldserver.module.gvg.facade;

import cn.daxiang.framework.result.CollectionResult;
import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.shared.module.battle.BattleEntity;
import cn.daxiang.shared.module.gvg.GVGActorInfo;
import cn.daxiang.shared.module.gvg.GVGActorRankVO;
import cn.daxiang.shared.module.gvg.GVGBattleReportVO;
import cn.daxiang.shared.module.gvg.GVGBrawlingChallengeReportEntity;
import cn.daxiang.shared.module.gvg.GVGBrawlingTargetListVO;
import cn.daxiang.shared.module.gvg.GVGCityInfoVO;
import cn.daxiang.shared.module.gvg.GVGNationRankVo;
import cn.daxiang.shared.module.gvg.GVGStateEntity;
import cn.daxiang.shared.module.gvg.GVGTargetInfoVO;
import com.google.protobuf.ByteString;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * @author: Gary
 * @date: 2022/12/24 12:12
 * @Description:
 */
public interface WorldGVGFacade {
    /**
     * 获取活动状态
     *
     * @return
     */
    TResult<GVGStateEntity> getGVGStateEntity();

    /**
     * 获取城池列表
     *
     * @param serverType
     * @param serverId
     * @param actorId
     * @param attributes
     * @return
     */
    CollectionResult<GVGCityInfoVO> getCityInfoList(int serverType, int serverId, long actorId, Map<Byte, Object> attributes);

    /**
     * 加入城池
     *
     * @param serverType
     * @param serverId
     * @param attributes
     * @param cityId
     * @return
     */
    Result joinCity(int serverType, int serverId, Map<Byte, Object> attributes, int cityId);

    /**
     * 获取城池信息
     *
     * @param serverType
     * @param serverId
     * @param cityId
     * @param actorId
     * @param attributes
     * @param type
     * @param targetIds
     * @return
     */
    CollectionResult<GVGTargetInfoVO> getCityInfoByCityId(int serverType, int serverId, int cityId, long actorId, Map<Byte, Object> attributes, int type, List<Long> targetIds);

    /**
     * 获取玩家信息
     *
     * @param serverType
     * @param serverId
     * @param actorId
     * @param attributes
     * @return
     */
    TResult<GVGActorInfo> getGVGActorInfo(int serverType, int serverId, long actorId, Map<Byte, Object> attributes);

    /**
     * 挑战
     *
     * @param serverType
     * @param serverId
     * @param actorId
     * @param attributes
     * @param targetId
     * @return
     */
    TResult<GVGBattleReportVO> challenge(int serverType, int serverId, long actorId, Map<Byte, Object> attributes, long targetId);

    /**
     * 领取收益
     *
     * @param serverType
     * @param serverId
     * @param actorId
     * @param actorAttributeMap
     */
    TResult<GVGActorInfo> receiveReward(int serverType, int serverId, long actorId, Map<Byte, Object> actorAttributeMap);

    /**
     * 兑换兵力
     *
     * @param serverType
     * @param serverId
     * @param actorId
     * @param actorAttributeMap
     * @param soldiers
     * @return
     */
    TResult<GVGActorInfo> exchangeSoldiers(int serverType, int serverId, long actorId, Map<Byte, Object> actorAttributeMap, long soldiers);

    /**
     * 王城乱斗战斗结果
     *
     * @param serverType
     * @param attributes
     * @param targetAttributes
     * @param type
     * @param battle
     * @param killCount
     * @param isWin
     */
    void brawlingChallengeRecord(int serverType, Map<Byte, Object> attributes, Map<Byte, Object> targetAttributes, int type, ByteString battle, int killCount, boolean isWin);

    /**
     * 获取王城乱斗战报
     *
     * @param serverType
     * @param serverId
     * @return
     */
    Collection<GVGBrawlingChallengeReportEntity> getGVGBrawlingReportList(int serverType, int serverId);

    /**
     * 获取玩家排行榜
     *
     * @param serverType
     * @param serverId
     * @param actorId
     * @return
     */
    CollectionResult<GVGActorRankVO> getGVGActorRank(int serverType, int serverId, long actorId);

    /**
     * 获取军团排行榜
     *
     * @param serverType
     * @param serverId
     * @param nationId
     * @return
     */
    CollectionResult<GVGNationRankVo> getGVGNationRank(int serverType, int serverId, long nationId);

    /**
     * 获取目标玩家信息
     *
     * @param serverType
     * @param serverId
     * @param cityId
     * @param actorId
     * @param type
     * @param actorIdsList
     * @return
     */
    CollectionResult<GVGTargetInfoVO> getTargetInfo(int serverType, int serverId, int cityId, long actorId, int type, Collection<Long> actorIdsList);

    /**
     * 获取战报
     *
     * @param serverType
     * @param serverId
     * @param actorId
     * @return
     */
    CollectionResult<GVGBattleReportVO> getBattleReportInfo(int serverType, int serverId, long actorId);

    /**
     * 获取王城乱斗挑战列表
     *
     * @param serverType
     * @param serverId
     * @param actorId
     * @return
     */
    TResult<GVGBrawlingTargetListVO> getGVGBrawlingList(int serverType, int serverId, long actorId);

    /**
     * 王城乱斗挑战
     *
     * @param serverType
     * @param serverId
     * @param type
     * @param battleEntity
     * @param targetId
     * @return
     */
    Result brawlingChallenge(int serverType, int serverId, int type, BattleEntity battleEntity, long targetId);

    /**
     * 获取王城之争上一赛季军团排行榜信息
     *
     * @param serverType
     * @param serverId
     * @return
     */
    CollectionResult<GVGNationRankVo> getGVGLastSeasonNationRank(int serverType, int serverId);

    /**
     * 获取王城乱斗战神榜信息
     *
     * @param serverType
     * @param serverId
     * @param actorId
     * @return
     */
    CollectionResult<GVGActorRankVO> getBrawlingActorRankList(int serverType, int serverId, long actorId);

    /**
     * 获取王城乱斗上个赛季战神排行榜
     *
     * @param serverType
     * @param serverId
     * @param actorId
     * @return
     */
    CollectionResult<GVGActorRankVO> getLastGVGBrawlingActorRank(int serverType, int serverId, long actorId);

    /**
     * 刷新王城之争玩法解锁条件
     *
     * @param zoneOpenDayLimit
     */
    void refreshZoneOpenDayLimit(int zoneOpenDayLimit);
}
