package cn.daxiang.hbtd.worldserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.dataconfig.annotation.FieldIgnore;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.hbtd.worldserver.module.reward.RewardHelper;
import cn.daxiang.shared.reward.RewardObject;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;

import java.util.Collection;

/**
 * 攻城略地城池表
 *
 * <AUTHOR>
 * @date 2022/7/4
 */
@DataFile(fileName = "capture_city_config")
public class CaptureCityConfig implements ModelAdapter {
    /**
     * 城池ID
     */
    private int id;
    /**
     * 州ID
     */
    private int stateId;
    /**
     * 序号
     */
    private int order;
    /**
     * 难度
     */
    private int difficulty;
    /**
     * 每分钟挂机收益
     * [[rewardType,id,num],[rewardType,id,num]]
     * {@code RewardType}
     */
    private String fixedRewards;
    /**
     * 占领时间上限
     * 单位：分钟
     */
    private int timeLimit;
    /**
     * 攻占保底奖励倍数
     */
    private int captureMinimum;
    /**
     * 攻占保底奖励倍数
     */
    private int plunderMinimum;
    @FieldIgnore
    private Collection<RewardObject> fixedRewardList = Lists.newArrayList();
    @FieldIgnore
    private Collection<RewardObject> captureMinimumRewardList = Lists.newArrayList();
    @FieldIgnore
    private Collection<RewardObject> plunderMinimumRewardList = Lists.newArrayList();

    @FieldIgnore
    private Collection<RewardObject> garrisonMaximumRewardList = Lists.newArrayList();

    @Override
    public void initialize() {
        JSONArray fixedRewardArray = JSONArray.parseArray(fixedRewards);
        for (Object rewardItem : fixedRewardArray) {
            JSONArray array = JSONArray.parseArray(rewardItem.toString());
            RewardObject rewardObject = RewardObject.valueOf(array);
            fixedRewardList.add(rewardObject);
        }
        captureMinimumRewardList = RewardHelper.multipleRewardList(fixedRewardList, captureMinimum);
        plunderMinimumRewardList = RewardHelper.multipleRewardList(fixedRewardList, plunderMinimum);
        garrisonMaximumRewardList = RewardHelper.multipleRewardList(fixedRewardList, timeLimit);

    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(id);
    }

    public int getId() {
        return id;
    }

    public int getStateId() {
        return stateId;
    }

    public int getOrder() {
        return order;
    }

    public int getDifficulty() {
        return difficulty;
    }

    public int getTimeLimit() {
        return timeLimit;
    }

    public Collection<RewardObject> getFixedRewardList() {
        return fixedRewardList;
    }

    public Collection<RewardObject> getCaptureMinimumRewardList() {
        return captureMinimumRewardList;
    }

    public Collection<RewardObject> getPlunderMinimumRewardList() {
        return plunderMinimumRewardList;
    }

    public Collection<RewardObject> getGarrisonMaximumRewardList() {
        return garrisonMaximumRewardList;
    }
}

