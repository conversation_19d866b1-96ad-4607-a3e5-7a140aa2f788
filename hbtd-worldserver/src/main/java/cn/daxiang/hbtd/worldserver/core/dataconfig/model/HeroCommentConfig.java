package cn.daxiang.hbtd.worldserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.identity.IdentiyKey;

/**
 * <AUTHOR>
 * @since 2021/11/9 11:10
 */
@DataFile(fileName = "hero_comment_config")
public class HeroCommentConfig implements ModelAdapter {

    /**
     * 英雄配置Id
     */
    private int heroConfigId;

    /**
     * 昵称
     */
    private String name;

    /**
     * 区服ID
     */
    private int serverId;

    /**
     * 头像ID
     */
    private int avatarId;

    /**
     * VIP 等级
     */
    private int vipLevel;

    /**
     * 内容
     */
    private String content;

    /**
     * 点赞数量
     */
    private int likes;

    /**
     * 英雄点赞量
     *
     * @return
     */
    private int heroLikes;

    public int getHeroConfigId() {
        return heroConfigId;
    }

    public void setHeroConfigId(int heroConfigId) {
        this.heroConfigId = heroConfigId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getServerId() {
        return serverId;
    }

    public void setServerId(int serverId) {
        this.serverId = serverId;
    }

    public int getAvatarId() {
        return avatarId;
    }

    public void setAvatarId(int avatarId) {
        this.avatarId = avatarId;
    }

    public int getVipLevel() {
        return vipLevel;
    }

    public void setVipLevel(int vipLevel) {
        this.vipLevel = vipLevel;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public int getLikes() {
        return likes;
    }

    public void setLikes(int likes) {
        this.likes = likes;
    }

    public int getHeroLikes() {
        return heroLikes;
    }

    @Override
    public void initialize() {
    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(heroConfigId, name);
    }
}
