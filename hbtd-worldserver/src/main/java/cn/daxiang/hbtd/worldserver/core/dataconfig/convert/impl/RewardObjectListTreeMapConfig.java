package cn.daxiang.hbtd.worldserver.core.dataconfig.convert.impl;

import cn.daxiang.hbtd.worldserver.core.dataconfig.convert.IConfigable;
import cn.daxiang.shared.reward.RewardObject;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.Collection;
import java.util.TreeMap;

/**
 * 奖励列表TreeMap
 *
 * <AUTHOR>
 */
public class RewardObjectListTreeMapConfig implements IConfigable {

    /**
     * [[key,[[rewardType,id,num],[rewardType,id,num]]],[key,[[rewardType,id,num],[rewardType,id,num]]]]
     */
    private TreeMap<Integer, Collection<RewardObject>> cache = Maps.newTreeMap();

    public static void main(String[] args) {
        TreeMap<Integer, Collection<RewardObject>> rewardMap = Maps.newTreeMap();
        String config = "[[1,[[11,1,1],[12,2,2]]],[2,[[13,3,3],[14,4,4]]]]";
        JSONArray array = JSONArray.parseArray(config);
        for (Object item : array) {
            JSONArray valueArray = JSONArray.parseArray(item.toString());
            JSONArray rewardListArray = JSONArray.parseArray(valueArray.getString(1));
            Collection<RewardObject> rewardList = Lists.newArrayListWithCapacity(rewardListArray.size());
            for (Object rewardListItem : rewardListArray) {
                JSONArray rewardArray = JSONArray.parseArray(rewardListItem.toString());
                RewardObject rewardObject = RewardObject.valueOf(rewardArray);
                rewardList.add(rewardObject);
            }
            rewardMap.put(valueArray.getInteger(0), rewardList);
        }
        System.err.println(rewardMap);
    }

    public Collection<RewardObject> getRewardListByFloorKey(int key) {
        return cache.floorEntry(key).getValue();
    }

    public boolean contains(int key) {
        return cache.containsKey(key);
    }

    public Collection<RewardObject> getRewardListByKey(int key) {
        return cache.get(key);
    }

    public int getLastKey() {
        return cache.lastKey();
    }

    public Collection<Integer> getKeys() {
        return cache.keySet();
    }

    @Override
    public void buildObject(String config) {
        JSONArray array = JSONArray.parseArray(config);
        for (Object item : array) {
            JSONArray valueArray = JSONArray.parseArray(item.toString());
            JSONArray rewardListArray = JSONArray.parseArray(valueArray.getString(1));
            Collection<RewardObject> rewardList = Lists.newArrayListWithCapacity(rewardListArray.size());
            for (Object rewardListItem : rewardListArray) {
                JSONArray rewardArray = JSONArray.parseArray(rewardListItem.toString());
                RewardObject rewardObject = RewardObject.valueOf(rewardArray);
                rewardList.add(rewardObject);
            }
            cache.put(valueArray.getInteger(0), rewardList);
        }
    }
}
