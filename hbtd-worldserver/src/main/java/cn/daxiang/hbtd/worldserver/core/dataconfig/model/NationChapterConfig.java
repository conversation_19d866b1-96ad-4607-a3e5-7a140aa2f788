package cn.daxiang.hbtd.worldserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.dataconfig.annotation.FieldIgnore;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.shared.reward.RewardObject;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * 军团副本章节表
 *
 * <AUTHOR>
 */
@DataFile(fileName = "nation_chapter_config")
public class NationChapterConfig implements ModelAdapter {
    /**
     * 章节Id
     */
    private int chapterId;
    /**
     * 下一章节的Id
     */
    private int nextChapterId;
    /**
     * 通关章节奖励
     */
    private String passedReward;
    @FieldIgnore
    private List<RewardObject> passedRewardList = Lists.newArrayList();

    @Override
    public void initialize() {
        JSONArray passedRewardArray = JSONArray.parseArray(passedReward);
        for (Object rewardItem : passedRewardArray) {
            JSONArray ItemArray = JSONArray.parseArray(rewardItem.toString());
            RewardObject rewardObject = RewardObject.valueOf(ItemArray);
            passedRewardList.add(rewardObject);
        }
    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(chapterId);
    }

    public int getChapterId() {
        return chapterId;
    }

    public int getNextChapterId() {
        return nextChapterId;
    }

    public List<RewardObject> getPassedRewardList() {
        return passedRewardList;
    }
}
