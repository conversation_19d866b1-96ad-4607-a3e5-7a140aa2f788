package cn.daxiang.hbtd.worldserver.module.activity.dao;

import cn.daxiang.shared.module.activity.Activity55RankVO;

import java.util.Collection;

/**
 * <AUTHOR>
 * @date 2021/6/24
 */
public interface Activity55Dao {
    /**
     * 合战锦标赛活动开始
     *
     * @param serverType
     * @param activityType
     * @param data
     * @param openDate
     * @param limit
     */
    void activityOpen(int serverType, int activityType, int data, String openDate, long limit);

    /**
     * 刷新合战锦标赛排名
     *
     * @param serverType
     * @param activityType
     * @param data
     * @param openDate
     * @param rankVO
     */
    void refreshActivity55Rank(int serverType, int activityType, int data, String openDate, Activity55RankVO rankVO);

    /**
     * 获取锦标赛总排名
     *
     * @param serverType
     * @param activityType
     * @param data
     * @param openDate
     * @param limit
     * @return
     */
    Collection<Activity55RankVO> getActivity55RankList(int serverType, int activityType, int data, String openDate, long limit);
}
