package cn.daxiang.hbtd.worldserver.core.redis.parser;

import cn.daxiang.hbtd.worldserver.core.redis.Identifiable;
import cn.daxiang.hbtd.worldserver.core.redis.KeyGenerator;

public class StringKeyGenerator implements KeyGenerator<String> {

    @Override
    public String generate(Identifiable identifiable) {
        StringBuffer sb = new StringBuffer();
        sb.append(identifiable.getClass().getName());
        sb.append("-");
        Object[] objs = identifiable.getIdentify();
        for (int i = 0; i < objs.length; i++) {
            sb.append(objs[i].toString());
            sb.append("-");
        }
        sb.deleteCharAt(sb.length() - 1);
        return sb.toString();
    }

    @Override
    public String generate(Class<?> clz, Object... identify) {
        StringBuffer sb = new StringBuffer();
        sb.append(clz.getName());
        sb.append("-");
        for (int i = 0; i < identify.length; i++) {
            sb.append(identify[i].toString());
            sb.append("-");
        }
        sb.deleteCharAt(sb.length() - 1);
        return sb.toString();
    }

}
