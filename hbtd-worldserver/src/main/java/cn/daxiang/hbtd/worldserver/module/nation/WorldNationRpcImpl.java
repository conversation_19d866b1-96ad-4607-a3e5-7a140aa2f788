package cn.daxiang.hbtd.worldserver.module.nation;

import cn.daxiang.dto.response.NationMemberInfoResponse;
import cn.daxiang.framework.extend.KeyValue;
import cn.daxiang.framework.result.CollectionResult;
import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.framework.rpc.world.WorldNationRpc;
import cn.daxiang.hbtd.worldserver.module.nation.facade.NationFacade;
import cn.daxiang.hbtd.worldserver.module.server.ServerZoneHelper;
import cn.daxiang.shared.module.nation.Nation;
import cn.daxiang.shared.module.nation.NationPowerRankVO;
import cn.daxiang.shared.module.nation.entity.NationDungeonActorDamageRankResponse;
import cn.daxiang.shared.module.nation.entity.NationDungeonNationDamageRankResponse;
import cn.daxiang.shared.module.nation.entity.NationLevelRankResponse;
import cn.daxiang.shared.module.nation.entity.NationListEntity;
import cn.daxiang.shared.module.nation.entity.NationMemberListEntity;
import cn.daxiang.shared.module.nation.entity.NationPowerRankResponse;
import cn.daxiang.shared.reward.RewardObject;
import cn.daxiang.shared.type.NationLimitType;
import cn.daxiang.shared.type.NationMemberType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;

@Component
public class WorldNationRpcImpl implements WorldNationRpc {

    @Autowired
    private NationFacade nationFacade;

    @Override
    public KeyValue<Long, Integer> syncNation(int serverType, int serverId, long actorId) {
        return nationFacade.syncNation(serverType, serverId, actorId);
    }

    @Override
    public TResult<Nation> getNation(int serverType, int serverId, long nationId) {
        int zoneId = ServerZoneHelper.getZoneByServerId(serverType, serverId);
        Nation nation = nationFacade.getNation(serverType, zoneId, nationId);
        if (nation == null) {
            return TResult.fail();
        }
        return TResult.sucess(nation);
    }

    @Override
    public Result dissolution(int serverType, int serverId, long actorId, long nationId) {
        return nationFacade.dissolution(serverType, serverId, actorId, nationId);
    }

    @Override
    public NationListEntity getNationList(int serverType, int serverId, long nationId, int page) {
        return nationFacade.getNationList(serverType, serverId, nationId, page);
    }

    @Override
    public TResult<Nation> createNation(int serverType, int serverId, Map<Byte, Object> attribute, String name, int flagId, int applyLevelLimit, Collection<Long> applyList) {
        return nationFacade.createNation(serverType, serverId, attribute, name, flagId, applyLevelLimit, applyList);
    }

    @Override
    public Result apply(int serverType, int serverId, Map<Byte, Object> attribute, long nationId) {
        return nationFacade.apply(serverType, serverId, attribute, nationId);
    }

    @Override
    public Result approve(int serverType, int serverId, long nationId, long actorId, Collection<Long> targets, boolean isApproved) {
        return nationFacade.approve(serverType, serverId, nationId, actorId, targets, isApproved);
    }

    @Override
    public Result appoint(int serverType, int serverId, long nationId, long actorId, long target, NationMemberType memberType) {
        return nationFacade.appoint(serverType, serverId, nationId, actorId, target, memberType);
    }

    @Override
    public Result impeach(int serverType, int serverId, long nationId, long actorId, long kingId) {
        return nationFacade.impeach(serverType, serverId, nationId, actorId, kingId);
    }

    @Override
    public Result transfer(int serverType, int serverId, long nationId, long actorId, long targeterId, boolean isAuto) {
        return nationFacade.transfer(serverType, serverId, nationId, actorId, targeterId, isAuto);
    }

    @Override
    public Result kickOff(int serverType, int serverId, long nationId, long actorId, long targeterId) {
        return nationFacade.kickOff(serverType, serverId, nationId, actorId, targeterId);
    }

    @Override
    public Result changeLimit(int serverType, int serverId, long nationId, long actorId, NationLimitType type, Object value, String declaration) {
        return nationFacade.changeLimit(serverType, serverId, nationId, actorId, type, value, declaration);
    }

    @Override
    public Result changeDeclaration(int serverType, int serverId, long nationId, String declaration) {
        return nationFacade.changeDeclaration(serverType, serverId, nationId, declaration);
    }

    @Override
    public Result exit(int serverType, int serverId, long nationId, long actorId, boolean isSelfExit) {
        return nationFacade.exit(serverType, serverId, nationId, actorId, isSelfExit);
    }

    @Override
    public Result notice(int serverType, int serverId, long nationId, long actorId, String notice) {
        return nationFacade.notice(serverType, serverId, nationId, actorId, notice);
    }

    @Override
    public Result mail(int serverType, int serverId, long nationId, long actorId, String message) {
        return nationFacade.mail(serverType, serverId, nationId, actorId, message);
    }

    @Override
    public Result rename(int serverType, int serverId, long nationId, long actorId, String name) {
        return nationFacade.rename(serverType, serverId, nationId, actorId, name);
    }

    @Override
    public Result reflag(int serverType, int serverId, long nationId, long actorId, int flag) {
        return nationFacade.reflag(serverType, serverId, nationId, actorId, flag);
    }

    @Override
    public Result addExp(int serverType, int serverId, long actorId, long nationId, long exp) {
        return nationFacade.addExp(serverType, serverId, actorId, nationId, exp);
    }

    @Override
    public TResult<Long> donate(int serverType, int serverId, long nationId, long actorId, int id, long nationExp, int progress) {
        return nationFacade.donate(serverType, serverId, nationId, actorId, id, nationExp, progress);
    }

    @Override
    public long getNationPower(int serverType, int serverId, long nationId) {
        return nationFacade.getNationPower(serverType, serverId, nationId);
    }

    @Override
    public NationMemberListEntity getNationMember(int serverType, int serverId, long nationId) {
        return nationFacade.getNationMember(serverType, serverId, nationId);
    }

    @Override
    public NationMemberInfoResponse getNationMemberByName(int serverType, int serverId, String nationName) {
        return nationFacade.getNationMemberByName(serverType, serverId, nationName);
    }

    @Override
    public Map<Long, String> getAllNationNames(int serverType, int serverId) {
        int zoneId = ServerZoneHelper.getZoneByServerId(serverType, serverId);
        return nationFacade.getAllNationNames(serverType, zoneId);
    }

    @Override
    public NationLevelRankResponse getNationLevelRankResponse(int serverType, int serverId, long nationId, int page) {
        return nationFacade.getNationLevelRankResponse(serverType, serverId, nationId, page);
    }

    @Override
    public NationPowerRankResponse getNationPowerRankResponse(int serverType, int serverId, long nationId, int page) {
        return nationFacade.getNationPowerRankResponse(serverType, serverId, nationId, page);
    }

    @Override
    public long getNationPowerRank(int serverType, int serverId, long nationId) {
        return nationFacade.getNationPowerRank(serverType, serverId, nationId);
    }

    @Override
    public Collection<NationPowerRankVO> getNationPowerRanks(int serverType, int serverId, int page) {
        return nationFacade.getNationPowerRanks(serverType, serverId, page);
    }

    @Override
    public NationDungeonNationDamageRankResponse getNationDungeonDamageRankResponse(int serverType, int serverId, long nationId, long actorId, int page) {
        return nationFacade.getNationDungeonNationDamageRankResponse(serverType, serverId, nationId, actorId, page);
    }

    @Override
    public NationDungeonActorDamageRankResponse getNationStoryDamageRankResponse(int serverType, int serverId, long nationId, long actorId, int storyId) {
        return nationFacade.getNationDungeonStoryDamageRankResponse(serverType, serverId, nationId, actorId, storyId);
    }

    @Override
    public void updateNationMember(int serverType, int serverId, long nationId, Map<Byte, Object> attribute, long powerRank) {
        nationFacade.updateNationMember(serverType, serverId, nationId, attribute, powerRank);
    }

    @Override
    public void clearApply(int serverType, int serverId, long actorId, Collection<Long> nationId) {
        nationFacade.clearApply(serverType, serverId, actorId, nationId);
    }

    @Override
    public void serverComposeClear(int serverType, Collection<Integer> serverIds, Collection<Long> actorIds) {
        nationFacade.serverComposeClear(serverType, serverIds, actorIds);
    }

    @Override
    public void flower(int serverType, int serverId, long actorId, int targetServerId, long targetId, int type, int flowerCount) {
        nationFacade.flower(serverType, serverId, actorId, targetServerId, targetId, type, flowerCount);
    }

    @Override
    public void updateFlower(int serverType, int serverId, long actorId, int pushSever, long pushActor, int type, long flowerCount) {
        nationFacade.updateFlower(serverType, serverId, actorId, pushSever, pushActor, type, flowerCount);
    }

    @Override
    public CollectionResult<RewardObject> nationStoryReceive(int serverType, int serverId, long nationId, long actorId, int storyId, int location) {
        return nationFacade.nationStoryReceive(serverType, serverId, nationId, actorId, storyId, location);
    }

    @Override
    public TResult<KeyValue<Collection<Integer>, Collection<RewardObject>>> nationStoryQuickReceive(int serverType, int serverId, long nationId, long actorId,
        Collection<Integer> hadReceivedList) {
        return nationFacade.nationStoryQuickReceive(serverType, serverId, nationId, actorId, hadReceivedList);
    }

    @Override
    public Result nationRecycle(int serverType, int serverId, long nationId, int recycleValue, long actorId) {
        return nationFacade.nationRecycle(serverType, serverId, nationId, recycleValue, actorId);
    }
}
