package cn.daxiang.framework.rpc.event;

import cn.daxiang.framework.event.ActorEvent;
import cn.daxiang.framework.event.NodeEvent;
import cn.daxiang.shared.event.EventKey;
import cn.daxiang.shared.reward.RewardObject;

import java.util.Collection;
import java.util.Map;

/**
 * 护镖奖励事件
 */
public class EscortHelpRewardEvent extends ActorEvent {
    /**
     * 奖励对象
     */
    public Collection<RewardObject> rewardList;

    public EscortHelpRewardEvent(long actorId, Collection<RewardObject> rewardList) {
        super(EventKey.ESCORT_HELP_REWARD_EVENT, actorId);
        this.rewardList = rewardList;
    }
}
