package cn.daxiang.framework.rpc.event;

import cn.daxiang.framework.event.ActorEvent;
import cn.daxiang.shared.event.EventKey;

/**
 * 请求护镖
 *
 * <AUTHOR>
 * @date 2023/2/6
 */
public class EscortBegEscortEvent extends ActorEvent {
    /**
     * 邀请者serverId
     */
    public int inviterServerId;
    /**
     * 邀请者角色ID
     */
    public long inviterActorId;

    public EscortBegEscortEvent(long actorId, int inviterServerId, long inviterActorId) {
        super(EventKey.ESCORT_BEG_ESCORT_EVENT, actorId);
        this.inviterServerId = inviterServerId;
        this.inviterActorId = inviterActorId;
    }
}
