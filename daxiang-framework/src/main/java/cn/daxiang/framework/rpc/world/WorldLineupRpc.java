package cn.daxiang.framework.rpc.world;

import cn.daxiang.framework.dispatch.DispatchType;
import cn.daxiang.framework.result.CollectionResult;
import cn.daxiang.framework.rpc.RpcInterface;
import cn.daxiang.framework.rpc.annotation.Rpc;
import cn.daxiang.shared.module.lineup.HeroWorldRankVO;

import java.util.Collection;

/**
 * 阵容信息跨服
 *
 * <AUTHOR>
 * @date 2019/10/26
 */
public interface WorldLineupRpc extends RpcInterface {
    /**
     * 查询阵容信息
     *
     * @param actorId
     * @param serverType
     * @param serverId
     * @param targetServerId
     * @param targeterId
     */
    @Rpc(dispatchType = DispatchType.ACTOR)
    void queryLineup(long actorId, int serverType, int serverId, int targetServerId, long targeterId);

    /**
     * 推送阵容信息
     *
     * @param actorId
     * @param serverType
     * @param serverId
     * @param targetServerId
     * @param targeterId
     * @param statusCode
     * @param result
     */
    @Rpc(dispatchType = DispatchType.ACTOR)
    void pushLineup(long actorId, int serverType, int serverId, int targetServerId, long targeterId, short statusCode, byte[] result);

    /**
     * 查询阵容信息
     *
     * @param actorId
     * @param serverType
     * @param serverId
     * @param targetServerId
     * @param targeterId
     */
    @Rpc(dispatchType = DispatchType.ACTOR)
    void querySimpleLineup(long actorId, int serverType, int serverId, int targetServerId, long targeterId);

    /**
     * 推送阵容信息
     *
     * @param actorId
     * @param serverType
     * @param serverId
     * @param targetServerId
     * @param targeterId
     * @param statusCode
     * @param result
     */
    @Rpc(dispatchType = DispatchType.ACTOR)
    void pushSimpleLineup(long actorId, int serverType, int serverId, int targetServerId, long targeterId, short statusCode, byte[] result);

    /**
     * 获取英雄战力排行榜
     *
     * @param serverType
     * @param serverId
     * @param rankVO
     * @return
     */
    @Rpc(dispatchType = DispatchType.ACTOR)
    CollectionResult<HeroWorldRankVO> getHeroRankList(int serverType, int serverId, HeroWorldRankVO rankVO);

    /**
     * 刷新英雄战力排名
     *
     * @param serverType
     * @param serverId
     * @param rankVO
     */
    @Rpc(dispatchType = DispatchType.ACTOR)
    void refreshHeroRank(int serverType, int serverId, HeroWorldRankVO rankVO);

    /**
     * 最强英雄战区排行榜推送到世界服
     *
     * @param serverType
     * @param serverId
     * @param rankVOList
     */
    @Rpc(dispatchType = DispatchType.ACTOR)
    void pushHeroRanks(int serverType, int serverId, Collection<HeroWorldRankVO> rankVOList);
}
