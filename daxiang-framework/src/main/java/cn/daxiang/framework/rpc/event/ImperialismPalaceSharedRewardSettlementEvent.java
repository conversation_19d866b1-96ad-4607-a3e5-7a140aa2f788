package cn.daxiang.framework.rpc.event;

import cn.daxiang.framework.event.ActorEvent;
import cn.daxiang.shared.event.EventKey;
import cn.daxiang.shared.reward.RewardObject;

import java.util.Collection;

/**
 * 决战皇城赛季结束分成奖励事件
 *
 * <AUTHOR>
 * @date 2023/11/20
 */
public class ImperialismPalaceSharedRewardSettlementEvent extends ActorEvent {

    /**
     * 奖励列表
     */
    public Collection<RewardObject> rewardList;

    public ImperialismPalaceSharedRewardSettlementEvent(long actorId, Collection<RewardObject> rewardList) {
        super(EventKey.IMPERIALISM_PALACE_SHARED_REWARD_SETTLEMENT_EVENT, actorId);
        this.rewardList = rewardList;
    }
}