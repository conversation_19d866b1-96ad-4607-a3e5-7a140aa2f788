package cn.daxiang.framework.rpc.event;

import cn.daxiang.framework.event.ActorEvent;
import cn.daxiang.shared.event.EventKey;
import cn.daxiang.shared.module.gvg.GVGActorInfo;
import com.google.protobuf.ByteString;

/**
 * 王城乱斗战斗结果事件
 *
 * @author: <PERSON>
 * @date: 2023/11/27 11:35
 * @Description:
 */
public class GVGBrawlingBattleEndEvent extends ActorEvent {
    /**
     * 挑战类型类型
     */
    public int type;
    /**
     * 战报结果
     */
    public ByteString battle;
    /**
     * 获得的总积分
     */
    public long score;
    /**
     * 扣除对方的积分
     */
    public long deductScore;
    /**
     * 被攻击方死亡人数
     */
    public int killCount;
    /**
     * 倍率
     */
    public int multiple;
    /**
     * 玩家世界服信息
     */
    public GVGActorInfo actorInfo;

    public GVGBrawlingBattleEndEvent(long actorId, int type, ByteString battle, long deductScore, long score, int killCount, int multiple, GVGActorInfo attackEntity) {
        super(EventKey.GVG_BRAWLING_BATTLE_END_EVENT, actorId);
        this.type = type;
        this.battle = battle;
        this.score = score;
        this.deductScore = deductScore;
        this.killCount = killCount;
        this.multiple = multiple;
        this.actorInfo = attackEntity;
    }
}
