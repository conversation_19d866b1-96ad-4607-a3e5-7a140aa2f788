package cn.daxiang.framework.rpc.event;

import cn.daxiang.framework.event.ActorEvent;
import cn.daxiang.shared.event.EventKey;
import cn.daxiang.shared.module.teamTd.type.TeamTdJoinLimitType;
import cn.daxiang.shared.module.teamTd.type.TeamTdParameterKey;
import cn.daxiang.shared.module.teamTd.type.TeamTdType;

import java.util.Map;

/**
 * 队伍战斗结果事件
 *
 * <AUTHOR>
 * @date 2020/12/28
 */
public class TeamTdBattleResultEvent extends ActorEvent {

    /**
     * 队伍类型
     * {@code TeamType}
     */
    public TeamTdType type;
    /**
     * 队伍类型值
     */
    public int value;
    /**
     * 加入类型
     * {@code TeamJoinType}
     */
    public TeamTdJoinLimitType limitType;
    /**
     * 额外参数Map
     * key:TeamParameterKey,value:TeamParameterValue
     */
    public Map<TeamTdParameterKey, Object> parameterMap;

    public TeamTdBattleResultEvent(long actorId, TeamTdType type, int value, TeamTdJoinLimitType limitType, Map<TeamTdParameterKey, Object> parameterMap) {
        super(EventKey.TEAM_TD_BATTLE_RESULT_EVENT, actorId);
        this.type = type;
        this.value = value;
        this.limitType = limitType;
        this.parameterMap = parameterMap;
    }
}
