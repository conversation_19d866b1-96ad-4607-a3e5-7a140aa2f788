package cn.daxiang.framework.rpc.event;

import cn.daxiang.framework.event.NodeEvent;
import cn.daxiang.shared.event.EventKey;

import java.util.Map;

/**
 * 决战皇城皇城排名奖励事件
 *
 * <AUTHOR>
 * @date 2023/11/18
 */
public class ImperialismRegionRankRewardEvent extends NodeEvent {

    /**
     * 排名信息
     * {rank:actorId}
     */
    public Map<Long, Long> rankMap;

    public ImperialismRegionRankRewardEvent(int serverId, Map<Long, Long> rankMap) {
        super(EventKey.IMPERIALISM_REGION_RANK_REWARD_EVENT, serverId);
        this.rankMap = rankMap;
    }
}
