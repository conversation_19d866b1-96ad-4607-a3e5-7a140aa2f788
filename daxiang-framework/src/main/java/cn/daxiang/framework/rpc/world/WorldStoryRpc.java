package cn.daxiang.framework.rpc.world;

import cn.daxiang.framework.dispatch.DispatchType;
import cn.daxiang.framework.result.CollectionResult;
import cn.daxiang.framework.rpc.RpcInterface;
import cn.daxiang.framework.rpc.annotation.Rpc;
import cn.daxiang.shared.module.story.StoryWorldRankVO;

import java.util.Collection;

/**
 * <AUTHOR>
 * @date 2023/7/5
 */
public interface WorldStoryRpc extends RpcInterface {
    /**
     * 获取主线关卡战区排行榜
     *
     * @param serverType
     * @param serverId
     * @param rankVO
     * @return
     */
    @Rpc(dispatchType = DispatchType.ACTOR)
    CollectionResult<StoryWorldRankVO> getStoryRankList(int serverType, int serverId, StoryWorldRankVO rankVO);

    /**
     * 刷新主线关卡战区排行榜
     *
     * @param serverType
     * @param serverId
     * @param rankVO
     */
    @Rpc(dispatchType = DispatchType.ACTOR)
    void refreshStoryRank(int serverType, int serverId, StoryWorldRankVO rankVO);

    /**
     * 游戏服的排行榜推送到战区排行榜
     *
     * @param serverType
     * @param serverId
     * @param rankVOList
     */
    @Rpc(dispatchType = DispatchType.ACTOR)
    void pushStoryRanks(int serverType, int serverId, Collection<StoryWorldRankVO> rankVOList);
}
