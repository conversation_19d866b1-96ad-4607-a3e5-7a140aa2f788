package cn.daxiang.framework.rpc.world;

import cn.daxiang.framework.dispatch.DispatchType;
import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.framework.rpc.RpcInterface;
import cn.daxiang.framework.rpc.annotation.Rpc;
import cn.daxiang.framework.rpc.event.TeamTdBattleStartEvent;
import cn.daxiang.shared.module.teamTd.model.TeamTd;
import cn.daxiang.shared.module.teamTd.type.TeamTdJoinLimitType;
import cn.daxiang.shared.module.teamTd.type.TeamTdType;

import java.util.Collection;
import java.util.Map;

public interface WorldTeamTdRpc extends RpcInterface {

    /**
     * 获取队伍
     *
     * @param serverType
     * @param serverId
     * @param actorId
     * @return
     */
    @Rpc(dispatchType = DispatchType.ACTOR)
    TResult<TeamTd> getTeamByActorId(int serverType, int serverId, long actorId);

    /**
     * 获取队伍列表
     *
     * @param serverType
     * @param serverId
     * @param actorId
     * @param type
     * @param typeValue
     * @return
     */
    @Rpc(dispatchType = DispatchType.ACTOR)
    Collection<TeamTd> getTeamList(int serverType, int serverId, long actorId, TeamTdType type, int typeValue);

    /**
     * 离线
     *
     * @param serverType
     * @param serverId
     * @param actorId
     * @return
     */
    @Rpc(dispatchType = DispatchType.ACTOR)
    Result offline(int serverType, int serverId, long actorId);

    /**
     * 取消监听
     *
     * @param serverType
     * @param serverId
     * @param actorId
     * @return
     */
    @Rpc(dispatchType = DispatchType.ACTOR)
    void cancelListen(int serverType, int serverId, long actorId);

    /**
     * 创建口令
     *
     * @param serverType
     * @param serverId
     * @param actorId
     * @param type
     * @return
     */
    @Rpc(dispatchType = DispatchType.SETTING)
    TResult<Integer> createCode(int serverType, int serverId, long actorId, TeamTdType type);

    /**
     * 取消口令
     *
     * @param serverType
     * @param serverId
     * @param actorId
     * @return
     */
    @Rpc(dispatchType = DispatchType.SETTING)
    void cancelCode(int serverType, int serverId, long actorId);

    /**
     * 创建队伍
     *
     * @param serverType
     * @param serverId
     * @param attributes
     * @param type
     * @param typeValue
     * @param limitType
     * @param limitValue
     * @param spectate
     * @return
     */
    @Rpc(dispatchType = DispatchType.ACTOR)
    Result createTeam(int serverType, int serverId, Map<Byte, Object> attributes, TeamTdType type, int typeValue, TeamTdJoinLimitType limitType, long limitValue,
        boolean spectate);

    /**
     * 口令队伍
     *
     * @param serverType
     * @param serverId
     * @param teamId
     * @return
     */
    @Rpc(dispatchType = DispatchType.ACTOR)
    TResult<TeamTd> getTeam(int serverType, int serverId, long teamId);

    /**
     * 口令队伍
     *
     * @param serverType
     * @param type
     * @param serverId
     * @param code
     * @return
     */
    @Rpc(dispatchType = DispatchType.ACTOR)
    TResult<TeamTd> getCodeTeam(int serverType, int serverId, TeamTdType type, int code);

    /**
     * 加入队伍
     *
     * @param serverType
     * @param serverId
     * @param attributes
     * @param teamId
     * @return
     */
    @Rpc(dispatchType = DispatchType.ACTOR)
    Result joinTeam(int serverType, int serverId, Map<Byte, Object> attributes, long teamId);

    /**
     * 退出队伍
     *
     * @param serverType
     * @param serverId
     * @param actorId
     * @return
     */
    @Rpc(dispatchType = DispatchType.ACTOR)
    Result exitTeam(int serverType, int serverId, long actorId);

    /**
     * 踢出队伍
     *
     * @param serverType
     * @param serverId
     * @param actorId
     * @return
     */
    @Rpc(dispatchType = DispatchType.ACTOR)
    Result kickTeam(int serverType, int serverId, long actorId);

    /**
     * 准备
     *
     * @param serverType
     * @param serverId
     * @param actorId
     * @param ready
     * @return
     */
    @Rpc(dispatchType = DispatchType.ACTOR)
    Result readyTeam(int serverType, int serverId, long actorId, boolean ready);

    /**
     * 刷新运行环境分数
     *
     * @param serverType
     * @param serverId
     * @param actorId
     * @param score
     * @return
     */
    @Rpc(dispatchType = DispatchType.ACTOR)
    Result refreshREScore(int serverType, int serverId, long actorId, int score);

    /**
     * 开始战斗
     *
     * @param serverType
     * @param serverId
     * @param actorId
     * @return
     */
    @Rpc(dispatchType = DispatchType.ACTOR)
    Result startBattle(int serverType, int serverId, long actorId);

    /**
     * 战斗开始
     *
     * @param bsId
     * @param teamId
     * @return
     */
    @Rpc(dispatchType = DispatchType.ACTOR)
    Result battleStart(int bsId, long teamId, long roomId, String token, String battleHost, int battlePort);

    /**
     * 战斗结束
     *
     * @param teamId
     * @param memberAttributes
     * @param battleStartTime
     * @param wave
     * @param lastWaveTime
     * @param isCheat
     * @param isExit
     * @return
     */
    @Rpc(dispatchType = DispatchType.ACTOR)
    Result battleEnd(long teamId, Collection<Map<Byte, Object>> memberAttributes, long battleStartTime, int wave, int lastWaveTime, boolean isCheat, boolean isExit);

    /**
     * 观战
     *
     * @param serverType
     * @param serverId
     * @param actorId
     * @param teamId
     * @return
     */
    @Rpc(dispatchType = DispatchType.ACTOR)
    TResult<TeamTdBattleStartEvent> spectate(int serverType, int serverId, long actorId, long teamId);
}
