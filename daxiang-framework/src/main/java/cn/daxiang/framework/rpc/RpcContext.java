package cn.daxiang.framework.rpc;

import com.google.common.collect.Maps;

import java.util.Map;

public class RpcContext {
    private static final ThreadLocal<RpcContext> LOCAL_CONTEXT = new ThreadLocal<RpcContext>() {
        protected RpcContext initialValue() {
            return new RpcContext();
        }
    };
    private Map<Byte, Object> attributes = Maps.newHashMap();

    public static RpcContext getContext() {
        return LOCAL_CONTEXT.get();
    }

    public static void destory() {
        LOCAL_CONTEXT.remove();
    }

    public Map<Byte, Object> getAttributes() {
        return attributes;
    }

    public void setAttribute(Byte key, Object value) {
        attributes.put(key, value);
    }

    public boolean contains(Byte key) {
        return attributes.containsKey(key);
    }

    @SuppressWarnings("unchecked")
    public <T> T getAttribute(Byte key) {
        return (T) attributes.get(key);
    }

    public void removeAttribute(Byte key) {
        attributes.remove(key);
    }
}
