package cn.daxiang.framework.rpc;

public class RpcConstants {
    /**
     * 节点类型
     */
    public static final byte NODE_TYPE = 1;
    /**
     * 节点ID
     */
    public static final byte NODE_ID = 2;
    /**
     * RPC_ID
     */
    public static final byte RPC_ID = 3;
    /**
     * 唯一ID
     */
    public static final byte UNIQUE_ID = 4;
    /**
     * 事件名称
     */
    public static final byte EVENT_NAME = 5;
    /**
     * 节点注册
     */
    public static final byte NODE_REDISTER = 6;
    /**
     * 节点心跳
     */
    public static final byte NODE_HEART_BEAT = 7;
    /**
     * 异步
     */
    public static final byte ASYNC = 8;

    /**
     * 回调
     */
    public static final byte CALLBACK = 9;

}
