package cn.daxiang.framework.rpc.event;

import cn.daxiang.framework.event.NodeEvent;
import cn.daxiang.shared.event.EventKey;
import cn.daxiang.shared.module.capture.CaptureCity;

import java.util.Collection;

/**
 * 攻城略地城池刷新事件
 *
 * <AUTHOR>
 * @date 2022/7/8
 */
public class CaptureCityRefreshEvent extends NodeEvent {
    /**
     * 攻城略地城池信息列表
     */
    public Collection<CaptureCity> captureCityList;

    public CaptureCityRefreshEvent(Collection<CaptureCity> captureCityList) {
        super(EventKey.CAPTURE_CITY_REFRESH_EVENT, 0);
        this.captureCityList = captureCityList;
    }
}
