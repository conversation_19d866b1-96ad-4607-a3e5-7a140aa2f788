package cn.daxiang.framework.rpc.world;

import cn.daxiang.dto.model.RecruitRecordVO;
import cn.daxiang.framework.dispatch.DispatchType;
import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.rpc.RpcInterface;
import cn.daxiang.framework.rpc.annotation.Rpc;

import java.util.Collection;

/**
 * <AUTHOR>
 * @date 2022/8/9
 */
public interface WorldRecruitRpc extends RpcInterface {
    /**
     * 获得名将招募上榜记录
     *
     * @param serverType
     * @param serverId
     * @return
     */
    @Rpc(dispatchType = DispatchType.ACTOR)
    Collection<RecruitRecordVO> getRecruitRecord(int serverType, int serverId);

    /**
     * 记录名将招募上榜
     *
     * @param serverType
     * @param serverId
     * @param voList
     */
    @Rpc(dispatchType = DispatchType.ACTOR)
    Result setRecruitRecord(int serverType, int serverId, Collection<RecruitRecordVO> voList);
}
