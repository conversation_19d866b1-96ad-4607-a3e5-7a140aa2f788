package cn.daxiang.framework.rpc.event;

import cn.daxiang.framework.event.ActorEvent;
import cn.daxiang.shared.event.EventKey;

/**
 * 攻城略地领取占领奖励事件
 *
 * <AUTHOR>
 * @date 2022/7/8
 */
public class CaptureReceiveOccupyRewardEvent extends ActorEvent {
    /**
     * 驻守分钟
     */
    public long minute;

    public CaptureReceiveOccupyRewardEvent(long actorId, long minute) {
        super(EventKey.CAPTURE_RECEIVE_OCCUPY_REWARD_EVENT, actorId);
        this.minute = minute;
    }
}
