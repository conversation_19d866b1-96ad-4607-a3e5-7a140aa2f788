package cn.daxiang.framework.rpc.event;

import cn.daxiang.framework.event.ActorEvent;
import cn.daxiang.shared.event.EventKey;
import cn.daxiang.shared.type.TeamType;

/**
 * 队伍解散事件
 */
public class TeamDisbandEvent extends ActorEvent {

    public TeamType teamType;

    public TeamDisbandEvent(long actorId, TeamType teamType) {
        super(EventKey.TEAM_DISBAND_EVENT, actorId);
        this.teamType = teamType;
    }
}
