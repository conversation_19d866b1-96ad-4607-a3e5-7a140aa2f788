package cn.daxiang.framework.rpc.event;

import cn.daxiang.framework.event.ActorEvent;
import cn.daxiang.shared.event.EventKey;

/**
 * 决战皇城宫殿信息刷新事件
 *
 * <AUTHOR>
 * @date 2023/11/20
 */
public class ImperialismPalaceRefreshEvent extends ActorEvent {

    /**
     * 宫殿角色信息
     */
    public byte[] data;

    public ImperialismPalaceRefreshEvent(long actorId, byte[] data) {
        super(EventKey.IMPERIALISM_PALACE_REFRESH_EVENT, actorId);
        this.data = data;
    }
}
