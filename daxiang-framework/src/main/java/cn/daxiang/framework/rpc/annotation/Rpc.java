package cn.daxiang.framework.rpc.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 描述服务接口信息，rpc服务器部份使用
 *
 * <AUTHOR>
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD})
public @interface Rpc {
    /**
     * 消息派发类型 DispatchType
     *
     * @return
     */
    public int dispatchType();
}
