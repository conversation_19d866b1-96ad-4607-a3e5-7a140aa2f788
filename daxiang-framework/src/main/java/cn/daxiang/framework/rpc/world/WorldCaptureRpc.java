package cn.daxiang.framework.rpc.world;

import cn.daxiang.framework.dispatch.DispatchType;
import cn.daxiang.framework.result.CollectionResult;
import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.framework.rpc.RpcInterface;
import cn.daxiang.framework.rpc.annotation.Rpc;
import cn.daxiang.protocol.game.CaptureProtocol;
import cn.daxiang.shared.module.capture.Capture;
import cn.daxiang.shared.module.capture.CaptureBattleReport;
import cn.daxiang.shared.module.capture.CaptureCity;
import cn.daxiang.shared.module.lineup.LineupAttribute;
import cn.daxiang.shared.reward.RewardObject;

import java.util.Collection;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/7/8
 */
public interface WorldCaptureRpc extends RpcInterface {
    /**
     * 获取攻城略地信息
     *
     * @return
     */
    @Rpc(dispatchType = DispatchType.ACTOR)
    Capture getCapture();

    /**
     * 获取攻城略地城池信息
     *
     * @param serverType
     * @param serverId
     * @param cityId
     * @return
     */
    @Rpc(dispatchType = DispatchType.ACTOR)
    Collection<CaptureCity> getCaptureCity(int serverType, int serverId, int cityId);

    /**
     * 战斗
     *
     * @param serverType
     * @param serverId
     * @param actorId
     * @param captureCityId
     * @param garrisonCityId
     * @param type
     * @param lineup
     * @param lineupHeroSkin
     * @return
     */
    @Rpc(dispatchType = DispatchType.ACTOR)
    Result battle(int serverType, int serverId, long actorId, int captureCityId, int garrisonCityId, CaptureProtocol.CaptureBattleType type, Map<Integer, Integer> lineup,
        Map<Integer, Integer> lineupHeroSkin);

    /**
     * 领取奖励
     *
     * @param serverType
     * @param serverId
     * @param actorId
     * @param cityId
     * @param multipleExpireTime
     * @return
     */
    @Rpc(dispatchType = DispatchType.ACTOR)
    TResult<Map.Entry<Long, Collection<RewardObject>>> receiveReward(int serverType, int serverId, long actorId, int cityId, long multipleExpireTime);

    /**
     * 战斗结果
     *
     * @param serverType
     * @param serverId
     * @param actorId
     * @param attributes
     * @param captureCityId
     * @param garrisonCityId
     * @param lineupAttributeMap
     * @param garrisonLineupMap
     * @param isReward
     * @param captureBattleReport
     * @param multipleExpireTime
     * @param systemSkillMaps
     * @return
     */
    @Rpc(dispatchType = DispatchType.ACTOR)
    CollectionResult<RewardObject> battleResult(int serverType, int serverId, long actorId, Map<Byte, Object> attributes, int captureCityId, int garrisonCityId,
        Map<Integer, LineupAttribute> lineupAttributeMap, Map<Integer, Long> garrisonLineupMap, boolean isReward, CaptureBattleReport captureBattleReport, long multipleExpireTime,
        Map<Integer, Collection<Integer>> systemSkillMaps);

    /**
     * 刷新多倍奖励时间
     *
     * @param serverType
     * @param serverId
     * @param actorId
     * @param cityId
     * @param multipleExpireTime
     * @return
     */
    @Rpc(dispatchType = DispatchType.ACTOR)
    TResult<CaptureCity> refreshMultipleExpireTime(int serverType, int serverId, long actorId, int cityId, long multipleExpireTime);

    /**
     * 弃城
     *
     * @param serverType
     * @param serverId
     * @param actorId
     * @param cityId
     * @param multipleExpireTime
     * @return
     */
    @Rpc(dispatchType = DispatchType.ACTOR)
    Result giveUp(int serverType, int serverId, long actorId, int cityId, long multipleExpireTime);
}
