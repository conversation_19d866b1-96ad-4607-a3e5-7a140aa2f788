package cn.daxiang.framework.rpc.event;

import cn.daxiang.framework.event.NodeEvent;
import cn.daxiang.shared.event.EventKey;

import java.util.Map;

/**
 * 巅峰竞技场奖励事件
 *
 * <AUTHOR>
 * @date 2020/02/11
 */
public class CrossCupRewardEvent extends NodeEvent {

    /**
     * 期数
     */
    public int cycle;

    /**
     * key:actorId,value:CrossCupState
     */
    public Map<Long, Integer> data;

    /**
     * 奖励类型
     */
    public int rewardStage;

    public CrossCupRewardEvent(int cycle, Map<Long, Integer> data, int rewardStage) {
        super(EventKey.CROSS_CUP_REWARD_EVENT, 0);
        this.cycle = cycle;
        this.data = data;
        this.rewardStage = rewardStage;
    }
}
