package cn.daxiang.framework.rpc.event;

import cn.daxiang.framework.event.NodeEvent;
import cn.daxiang.shared.event.EventKey;

import java.util.Collection;

/**
 * 军团副本伤害奖励事件
 */
public class NationDungeonDamageRewardEvent extends NodeEvent {
    /**
     * 军团长ID
     */
    public long kingId;
    /**
     * 仙盟名称
     */
    public String nationName;
    /**
     * 发送对象
     */
    public Collection<Long> actorIds;
    /**
     * 排行
     */
    public long rank;

    /**
     * 积分
     */
    public long damage;

    public NationDungeonDamageRewardEvent(long kingId, String nationName, Collection<Long> actorIds, long rank, long score) {
        super(EventKey.NATION_DUNGEON_DAMAGE_REWARD_EVENT, 0);
        this.kingId = kingId;
        this.nationName = nationName;
        this.actorIds = actorIds;
        this.rank = rank;
        this.damage = score;
    }
}
