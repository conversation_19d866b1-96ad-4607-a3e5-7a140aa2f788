package cn.daxiang.framework.rpc.event;

import cn.daxiang.framework.event.NodeEvent;
import cn.daxiang.shared.event.EventKey;
import cn.daxiang.shared.module.sanctuary.SanctuaryRegionState;

/**
 * 归葬秘境状态
 */
public class SanctuaryPushStateEvent extends NodeEvent {
    /**
     * 归葬秘境状态信息
     */
    public SanctuaryRegionState sanctuaryRegionState;

    public SanctuaryPushStateEvent(SanctuaryRegionState sanctuaryRegionState) {
        super(EventKey.SANCTUARY_REGION_STATE_EVENT, 0);
        this.sanctuaryRegionState = sanctuaryRegionState;
    }
}
