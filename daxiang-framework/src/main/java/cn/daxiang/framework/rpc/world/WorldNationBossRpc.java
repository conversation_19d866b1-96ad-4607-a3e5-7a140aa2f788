package cn.daxiang.framework.rpc.world;

import cn.daxiang.framework.dispatch.DispatchType;
import cn.daxiang.framework.result.CollectionResult;
import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.framework.rpc.RpcInterface;
import cn.daxiang.framework.rpc.annotation.Rpc;
import cn.daxiang.protocol.game.NationbossProtocol;
import cn.daxiang.shared.module.battle.BattleEntity;
import cn.daxiang.shared.module.worldNationBoss.NationBossActorEntityVO;
import cn.daxiang.shared.module.worldNationBoss.NationBossActorRankVO;
import cn.daxiang.shared.module.worldNationBoss.NationBossChallengeTargetListVO;
import cn.daxiang.shared.module.worldNationBoss.NationBossNationRankVO;
import cn.daxiang.shared.module.worldNationBoss.NationBossReportEntity;
import cn.daxiang.shared.module.worldNationBoss.NationBossStateEntity;
import com.google.protobuf.ByteString;

import java.util.Map;

/**
 * @Author: Gary
 * @Date 2022-12-01 11:30
 * @Description:
 */
public interface WorldNationBossRpc extends RpcInterface {

    /**
     * 获取军团boss阶段信息
     *
     * @return
     */
    @Rpc(dispatchType = DispatchType.ACTOR)
    NationBossStateEntity getNationBossState();

    /**
     * 获取玩家信息
     *
     * @param serverType
     * @param serverId
     * @param actorId
     * @return
     */
    @Rpc(dispatchType = DispatchType.ACTOR)
    TResult<NationBossActorEntityVO> getNationBossActorInfo(int serverType, int serverId, long actorId, long nationId);

    /**
     * 获取军团排行榜
     *
     * @param serverType
     * @param serverId
     * @param nationId
     * @param limit
     * @return
     */
    @Rpc(dispatchType = DispatchType.ACTOR)
    CollectionResult<NationBossNationRankVO> getNationRank(int serverType, int serverId, long nationId, int limit);

    /**
     * 获取玩家排行榜
     *
     * @param serverType
     * @param serverId
     * @param attributes
     * @param limit
     * @return
     */
    @Rpc(dispatchType = DispatchType.ACTOR)
    CollectionResult<NationBossActorRankVO> getActorRank(int serverType, int serverId, Map<Byte, Object> attributes, int limit);

    /**
     * 获取玩家挑战列表
     *
     * @param serverType
     * @param serverId
     * @param actorId
     * @param nationId
     * @return
     */
    @Rpc(dispatchType = DispatchType.ACTOR)
    TResult<NationBossChallengeTargetListVO> getChallengeTargetList(int serverType, int serverId, long actorId, long nationId);

    /**
     * 军团boss挑战玩家
     *
     * @param serverType
     * @param serverId
     * @param battleEntity
     * @param targetId
     * @param type
     * @return
     */
    @Rpc(dispatchType = DispatchType.ACTOR)
    Result challengeActor(int serverType, int serverId, BattleEntity battleEntity, long targetId, int type);

    /**
     * 军团boss挑战玩家战斗结束记录
     *
     * @param serverType
     * @param attributes
     * @param targetAttributes
     * @param challengeType
     * @param battle
     * @param deadCount
     * @param isWin
     * @param damageValue
     */
    @Rpc(dispatchType = DispatchType.ACTOR)
    void challengeActorRecord(int serverType, Map<Byte, Object> attributes, Map<Byte, Object> targetAttributes, NationbossProtocol.ChallengeType challengeType, ByteString battle,
        int deadCount, boolean isWin, long damageValue);

    /**
     * 获取军团boss战斗记录
     *
     * @param serverType
     * @param serverId
     * @return
     */
    @Rpc(dispatchType = DispatchType.ACTOR)
    CollectionResult<NationBossReportEntity> getChallengeRecord(int serverType, int serverId);

    /**
     * 军团boss挑战boss战斗结束记录
     *
     * @param serverType
     * @param attributes
     * @param score
     * @param damageValue
     * @param addPublicScore
     * @return
     */
    @Rpc(dispatchType = DispatchType.ACTOR)
    Result challengeBossRecord(int serverType, Map<Byte, Object> attributes, int score, long damageValue, int addPublicScore);

    /**
     * 获取军团鼓舞次数
     *
     * @param serverType
     * @param serverId
     * @param actorId
     * @param nationId
     * @return
     */
    @Rpc(dispatchType = DispatchType.ACTOR)
    TResult<Integer> getInspireTimes(int serverType, int serverId, long actorId, long nationId);

    /**
     * 军团boss鼓舞
     *
     * @param serverType
     * @param serverId
     * @param actorId
     * @param nationId
     * @param name
     * @return
     */
    @Rpc(dispatchType = DispatchType.ACTOR)
    Result inspire(int serverType, int serverId, long actorId, long nationId, String name);
}
