package cn.daxiang.framework.rpc.event;

import cn.daxiang.framework.event.ActorEvent;
import cn.daxiang.shared.event.EventKey;
import cn.daxiang.shared.module.team.Team;

import java.util.List;

public class ArsenalBattleEndEvent extends ActorEvent {
    /**
     * 战报
     */
    public List<byte[]> response;

    /**
     * 输赢
     */
    public boolean win;

    /**
     * 组队信息
     */
    public Team team;

    public ArsenalBattleEndEvent(long actorId, boolean win, Team team, List<byte[]> response) {
        super(EventKey.ARSENAL_BATTLE_END_EVENT, actorId);
        this.response = response;
        this.win = win;
        this.team = team;
    }
}
