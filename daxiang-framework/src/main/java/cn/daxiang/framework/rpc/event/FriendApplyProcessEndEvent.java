package cn.daxiang.framework.rpc.event;

import cn.daxiang.framework.event.NodeEvent;
import cn.daxiang.framework.result.Result;
import cn.daxiang.shared.event.EventKey;

/**
 * 好友申请处理结束事件
 *
 * <AUTHOR>
 */
public class FriendApplyProcessEndEvent extends NodeEvent {
    /**
     * 申请人
     */
    public long actorId;
    /**
     * 申请结果
     */
    public Result result;

    public FriendApplyProcessEndEvent(long actorId, Result result) {
        super(EventKey.FRIEND_APPLY_PROCESS_END_EVENT, 0);
        this.actorId = actorId;
        this.result = result;
    }
}
