package cn.daxiang.framework.rpc.world;

import cn.daxiang.framework.dispatch.DispatchType;
import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.framework.rpc.RpcInterface;
import cn.daxiang.framework.rpc.annotation.Rpc;
import cn.daxiang.shared.module.lineup.LineupAttribute;
import cn.daxiang.shared.module.team.Team;
import cn.daxiang.shared.type.TeamType;

import java.util.Collection;
import java.util.Map;

/**
 * 组队
 */
public interface WorldTeamRpc extends RpcInterface {

    /**
     * 获取队伍信息
     *
     * @param serverType
     * @param serverId
     * @param actorId
     * @return
     */
    @Rpc(dispatchType = DispatchType.ACTOR)
    TResult<Team> getTeam(int serverType, int serverId, long actorId);

    /**
     * 挑战，队伍设置为战斗状态
     *
     * @param serverType
     * @param serverId
     * @param actorId
     * @return
     */
    @Rpc(dispatchType = DispatchType.ACTOR)
    TResult<Team> challenge(int serverType, int serverId, long actorId);

    /**
     * 一键邀请，设置为已邀请
     *
     * @param serverType
     * @param serverId
     * @param actorId
     * @return
     */
    @Rpc(dispatchType = DispatchType.ACTOR)
    TResult<Team> oneClickInvitation(int serverType, int serverId, long actorId);

    /**
     * 组队列表
     *
     * @param serverType
     * @param serverId
     * @param actorId
     * @return
     */
    @Rpc(dispatchType = DispatchType.ACTOR)
    Collection<Team> getList(int serverType, int serverId, long actorId, TeamType teamType);

    /**
     * 创建队伍
     *
     * @param serverType
     * @param serverId
     * @param actorId
     * @param attribute
     * @param lineupAttribute
     * @param systemSkillMaps
     * @param teamType
     * @param ext
     * @return
     */
    @Rpc(dispatchType = DispatchType.ACTOR)
    TResult<Team> create(int serverType, int serverId, long actorId, Map<Byte, Object> attribute, Map<Integer, LineupAttribute> lineupAttribute,
        Map<Integer, Collection<Integer>> systemSkillMaps, TeamType teamType, Object ext,
        int numberLimit);

    /**
     * 设置密码
     *
     * @param serverType
     * @param serverId
     * @param actorId
     * @param password
     * @return
     */
    @Rpc(dispatchType = DispatchType.ACTOR)
    TResult<Team> setPassword(int serverType, int serverId, long actorId, String password);

    /**
     * 加入队伍
     *
     * @param serverType
     * @param serverId
     * @param attribute
     * @param lineupAttribute
     * @param systemSkillMaps
     * @param targetId
     * @param password
     * @return
     */
    @Rpc(dispatchType = DispatchType.ACTOR)
    Result join(int serverType, int serverId, Map<Byte, Object> attribute, Map<Integer, LineupAttribute> lineupAttribute, Map<Integer, Collection<Integer>> systemSkillMaps,
        long targetId, String password, TeamType teamType);

    /**
     * 踢出队伍
     *
     * @param serverType
     * @param serverId
     * @param actorId
     * @param targetActorId
     * @return
     */
    @Rpc(dispatchType = DispatchType.ACTOR)
    Result kickOut(int serverType, int serverId, long actorId, long targetActorId);

    /**
     * 退出队伍
     *
     * @param serverType
     * @param serverId
     * @param actorId
     * @return
     */
    @Rpc(dispatchType = DispatchType.ACTOR)
    Result quit(int serverType, int serverId, long actorId);

    /**
     * 邀请队员
     *
     * @param serverType
     * @param serverId
     * @param actorId
     * @param leaderActorId
     * @param targetServerId
     * @param targetActorId
     * @return
     */
    @Rpc(dispatchType = DispatchType.ACTOR)
    Result invite(int serverType, int serverId, long actorId, long leaderActorId, int targetServerId, long targetActorId);

    /**
     * 队员位置调整
     *
     * @param serverType
     * @param serverId
     * @param actorId
     * @param currentPosition
     * @param targetPosition
     */
    @Rpc(dispatchType = DispatchType.ACTOR)
    void swapPosition(int serverType, int serverId, long actorId, int currentPosition, int targetPosition);

    /**
     * 角色退出
     *
     * @param serverType
     * @param serverId
     * @param actorId
     */
    @Rpc(dispatchType = DispatchType.ACTOR)
    void actorLogout(int serverType, int serverId, long actorId);

    /**
     * 跳过战斗
     *
     * @param serverType
     * @param serverId
     * @param actorId
     * @return
     */
    @Rpc(dispatchType = DispatchType.ACTOR)
    void skipBattle(int serverType, int serverId, long actorId);

}
