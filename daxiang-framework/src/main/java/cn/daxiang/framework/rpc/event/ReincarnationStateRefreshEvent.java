package cn.daxiang.framework.rpc.event;

import cn.daxiang.framework.event.NodeEvent;
import cn.daxiang.shared.event.EventKey;
import cn.daxiang.shared.module.reincarnation.ReincarnationInfo;

/**
 * 轮回战场重数层数刷新事件
 *
 * <AUTHOR>
 * @date 2023/12/23
 */
public class ReincarnationStateRefreshEvent extends NodeEvent {
    /**
     * 轮回战场信息
     */
    public ReincarnationInfo reincarnationInfo;

    public ReincarnationStateRefreshEvent(ReincarnationInfo reincarnationInfo) {
        super(EventKey.REINCARNATION_STATE_REFRESH_EVENT, 0);
        this.reincarnationInfo = reincarnationInfo;
    }
}
