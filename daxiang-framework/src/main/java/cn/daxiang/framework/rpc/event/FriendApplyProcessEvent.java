package cn.daxiang.framework.rpc.event;

import cn.daxiang.framework.event.NodeEvent;
import cn.daxiang.shared.event.EventKey;

import java.util.Map;

/**
 * 好友申请处理事件
 *
 * <AUTHOR>
 */
public class FriendApplyProcessEvent extends NodeEvent {
    /**
     * 被申请人
     */
    public long actorId;
    /**
     * 申请人信息
     */
    public Map<Byte, Object> attribute;

    public FriendApplyProcessEvent(long actorId, Map<Byte, Object> attribute) {
        super(EventKey.FRIEND_APPLY_PROCESS_EVENT, 0);
        this.actorId = actorId;
        this.attribute = attribute;
    }
}
