package cn.daxiang.framework.rpc.world;

import cn.daxiang.framework.dispatch.DispatchType;
import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.framework.rpc.RpcInterface;
import cn.daxiang.framework.rpc.annotation.Rpc;
import cn.daxiang.shared.module.nationboss.NationDungeon;
import cn.daxiang.shared.module.nationboss.NationDungeonEntity;

import java.util.Map;

public interface WorldNationDungeonRpc extends RpcInterface {
    /**
     * 获取军团副本所有章节关卡信息（本周初始章节到当前所在章节信息）
     *
     * @return
     */
    @Rpc(dispatchType = DispatchType.ACTOR)
    TResult<NationDungeonEntity> getNationDungeonEntity(int serverType, int serverId, long nationId);

    /**
     * 获取军团副本单关卡信息
     *
     * @param nationId
     * @param storyId
     * @return
     */
    @Rpc(dispatchType = DispatchType.ACTOR)
    TResult<NationDungeon> getNationDungeon(int serverType, int serverId, long nationId, int storyId);

    /**
     * 挑战
     *
     * @param actorId
     * @param id
     * @return
     */
    @Rpc(dispatchType = DispatchType.ACTOR)
    Result challenge(int serverType, int serverId, long nationId, long actorId, int id);

    /**
     * 挑战结果
     *
     * @param serverType
     * @param serverId
     * @param nationId
     * @param actorId
     * @param id
     * @param damage
     * @param monsterHPMap
     * @param responseBytes
     * @return
     */
    @Rpc(dispatchType = DispatchType.ACTOR)
    Result challengeResult(int serverType, int serverId, long nationId, long actorId, int id, long damage, Map<Integer, Long> monsterHPMap, byte[] responseBytes);

}
