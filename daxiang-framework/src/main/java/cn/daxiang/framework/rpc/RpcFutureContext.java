package cn.daxiang.framework.rpc;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class RpcFutureContext {
    private static Map<Long, RpcCallback> CALL_BACK_MAPS = new ConcurrentHashMap<>();
    private static Map<Long, RpcFuture<RpcResponse>> FUTURE_MAPS = new ConcurrentHashMap<>();

    public static void setCallback(long rpcId, RpcCallback callback) {
        CALL_BACK_MAPS.put(rpcId, callback);
    }

    public static RpcCallback getCallback(long rpcId) {
        return CALL_BACK_MAPS.remove(rpcId);
    }

    public static void remove(long rpcId) {
        CALL_BACK_MAPS.remove(rpcId);
    }

    public static void setRpcFuture(long rpcId, RpcFuture<RpcResponse> future) {
        FUTURE_MAPS.put(rpcId, future);
    }

    public static void notifyRpcMessage(RpcResponse response) {
        long rpcId = response.getAttachment(RpcConstants.RPC_ID);
        RpcFuture<?> future = FUTURE_MAPS.remove(rpcId);
        if (future == null)
            return;
        future.onSuccess(response);
    }
}
