package cn.daxiang.framework.rpc.event;

import cn.daxiang.framework.event.ActorEvent;
import cn.daxiang.shared.event.EventKey;

/**
 * 跨服查询阵容事件
 *
 * <AUTHOR>
 */
public class CrossQueryLineupEvent extends ActorEvent {
    /**
     * 目标服务器ID
     */
    public int targetServerId;
    /**
     * 目标ID
     */
    public long targeterId;

    public CrossQueryLineupEvent(long actorId, int targetServerId, long targeterId) {
        super(EventKey.CROSS_QUERY_LINE_UP_EVENT, actorId);
        this.targetServerId = targetServerId;
        this.targeterId = targeterId;
    }

}
