package cn.daxiang.framework.rpc.event;

import cn.daxiang.framework.event.ActorEvent;
import cn.daxiang.shared.event.EventKey;
import cn.daxiang.shared.module.nation.Nation;

/**
 * 加入国家事件
 *
 * <AUTHOR>
 */
public class NationJoinEvent extends ActorEvent {
    /**
     * 服务器类型
     */
    public int serverType;
    /**
     * 服务器赛区ID
     */
    public int zoneId;
    /**
     * 国家
     */
    public Nation nation;

    public NationJoinEvent(int serverType, int zoneId, long actorId, Nation nation) {
        super(EventKey.NATION_JOIN_EVENT, actorId);
        this.serverType = serverType;
        this.zoneId = zoneId;
        this.nation = nation;
    }
}
