package cn.daxiang.framework.rpc.event;

import cn.daxiang.framework.event.ActorEvent;
import cn.daxiang.shared.event.EventKey;
import cn.daxiang.shared.module.team.Team;

/**
 * 房间改变事件
 */
public class TeamChangeEvent extends ActorEvent {

    public Team team;

    public TeamChangeEvent(long actorId, Team team) {
        super(EventKey.TEAM_CHANGE_EVENT, actorId);
        this.team = team;
    }

}
