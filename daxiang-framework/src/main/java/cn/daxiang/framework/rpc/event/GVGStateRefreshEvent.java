package cn.daxiang.framework.rpc.event;

import cn.daxiang.framework.event.NodeEvent;
import cn.daxiang.shared.event.EventKey;
import cn.daxiang.shared.module.gvg.GVGStateEntity;

/**
 * @author: <PERSON>
 * @date: 2022/12/31 9:26
 * @Description:
 */
public class GVGStateRefreshEvent extends NodeEvent {
    /**
     * 状态信息
     */
    public GVGStateEntity stateEntity;

    public GVGStateRefreshEvent(GVGStateEntity stateEntity) {
        super(EventKey.GVG_STATE_REFRESH_EVENT, 0);
        this.stateEntity = stateEntity;
    }
}
