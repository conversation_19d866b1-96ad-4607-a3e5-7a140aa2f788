package cn.daxiang.framework.rpc.event;

import cn.daxiang.framework.event.ActorEvent;
import cn.daxiang.shared.event.EventKey;
import cn.daxiang.shared.reward.RewardObject;

import java.util.Collection;

/**
 * 星期结束后未领取奖励镖车发奖
 */
public class EscortEndRewardEvent extends ActorEvent {
    /**
     * 奖励对象
     */
    public Collection<RewardObject> rewardList;

    public EscortEndRewardEvent(long actorId, Collection<RewardObject> rewardList) {
        super(EventKey.ESCORT_END_REWARD_EVENT, actorId);
        this.rewardList = rewardList;
    }
}
