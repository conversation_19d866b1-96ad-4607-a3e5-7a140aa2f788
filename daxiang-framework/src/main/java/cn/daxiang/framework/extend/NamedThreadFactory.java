package cn.daxiang.framework.extend;

import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 扩展有名字的线程工厂
 *
 * <AUTHOR>
 */
public class NamedThreadFactory implements ThreadFactory {
    private final ThreadGroup group;
    private final AtomicInteger threadNumber = new AtomicInteger(1);
    private final String namePrefix;

    public NamedThreadFactory(ThreadGroup group, String name) {
        this.group = group;
        this.namePrefix = (group.getName() + ":" + name);
    }

    public NamedThreadFactory(String name) {
        this.group = new ThreadGroup(name);
        this.namePrefix = (group.getName() + "-");
    }

    public Thread newThread(Runnable r) {
        return new Thread(this.group, r, this.namePrefix + this.threadNumber.getAndIncrement(), 0L);
    }

    public Thread newThread(Runnable r, String title) {
        return new Thread(this.group, r, this.namePrefix + this.threadNumber.getAndIncrement() + title, 0L);
    }

    public void start(Runnable r, String title) {
        Thread thread = new Thread(this.group, r, this.namePrefix + this.threadNumber.getAndIncrement() + title, 0L);
        thread.setDaemon(true);
        thread.start();
    }
}
