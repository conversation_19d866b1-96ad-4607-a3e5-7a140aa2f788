package cn.daxiang.framework.extend;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 阻塞队列线程池执行器封装类
 *
 * <AUTHOR>
 */
public class BlockingQueueThreadExecutor extends ThreadPoolExecutor {

    /**
     * 线程管理工厂
     */
    private NamedThreadFactory threadFactory;

    public BlockingQueueThreadExecutor(String name, int corePoolSize, int maximumPoolSize, long keepAliveTime, TimeUnit unit) {
        super(corePoolSize, maximumPoolSize, keepAliveTime, unit, new LinkedBlockingQueue<Runnable>());
        threadFactory = new NamedThreadFactory(name);
        super.setThreadFactory(threadFactory);
    }

    /**
     * 创建新的线程
     *
     * @param task
     * @return
     */
    public Thread newThread(Runnable task) {
        return threadFactory.newThread(task);
    }

    /**
     * 创建新的线程
     *
     * @param task
     * @param isDaemon 是否后台执行
     * @param isStart  是否启动
     * @return
     */
    public Thread newThread(Runnable task, boolean isDaemon, boolean isStart) {
        Thread thread = threadFactory.newThread(task);
        thread.setDaemon(isDaemon);
        if (isStart) {
            thread.start();
        }
        return thread;
    }

    public Thread newThread(Runnable task, boolean isDaemon, boolean isStart, String title) {
        Thread thread = threadFactory.newThread(task, title);
        thread.setDaemon(isDaemon);
        if (isStart) {
            thread.start();
        }
        return thread;
    }

}
