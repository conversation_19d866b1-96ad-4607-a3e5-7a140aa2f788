package cn.daxiang.framework.event;

import cn.daxiang.framework.dispatch.DispatchType;

public class NodeEvent extends GameEvent {

    private int nodeId;

    public NodeEvent(String name, int nodeId) {
        super(name, DispatchType.NODE);
        this.nodeId = nodeId;
    }

    public int getNodeId() {
        return nodeId;
    }

    public void setNodeId(int nodeId) {
        this.nodeId = nodeId;
    }

    @Override
    public long getUniqueId() {
        return nodeId;
    }

}
