package cn.daxiang.framework.event;

/**
 * 事件
 *
 * <AUTHOR>
 */
public abstract class GameEvent {

    /**
     * 派发类型
     */
    public int dispatchType;

    /**
     * 事件的key {@code EventKey}
     */
    public String name;

    public GameEvent() {
    }

    /**
     * @param name    事件名称
     * @param actorId 当前角色
     */
    public GameEvent(String name) {
        this.name = name;
    }

    public GameEvent(String name, int dispatchType) {
        this.name = name;
        this.dispatchType = dispatchType;
    }

    public int getDispatchType() {
        return dispatchType;
    }

    public String getName() {
        return this.name;
    }

    /**
     * 唯一id
     */
    public abstract long getUniqueId();

    @SuppressWarnings("unchecked")
    public <T> T convert() {
        return (T) this;
    }

    @Override
    public String toString() {
        return "Event [name=" + name + ", UniqueId=" + getUniqueId() + "]";
    }

}
