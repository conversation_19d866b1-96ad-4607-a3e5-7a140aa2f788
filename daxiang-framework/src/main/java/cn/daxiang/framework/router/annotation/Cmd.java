package cn.daxiang.framework.router.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 命令注解，用于标识接收消息的方法
 * 适用于在RouterHandler实现类中加入该注解即可接收事件消息
 * {@see ModuleHandler}
 *
 * <AUTHOR>
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD})
public @interface Cmd {

    /**
     * 命令id(该模块内唯一)
     *
     * @return
     */
    public int Id();

    /**
     * 验证角色是否登陆.默认true
     *
     * @return
     */
    public boolean CheckActorLogin() default true;

    /**
     * 消息派发类型 DispatchType
     *
     * @return
     */
    public int dispatchType();

}
