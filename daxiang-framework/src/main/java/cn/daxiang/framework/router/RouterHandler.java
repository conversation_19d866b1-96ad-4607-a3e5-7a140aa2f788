package cn.daxiang.framework.router;

import cn.daxiang.framework.asm.ASMMethod;
import cn.daxiang.framework.protocal.DataPacket;
import cn.daxiang.framework.router.annotation.Cmd;
import cn.daxiang.framework.router.type.HandlerType;
import com.esotericsoftware.reflectasm.MethodAccess;
import com.google.protobuf.GeneratedMessageV3;
import io.netty.channel.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

/**
 * 模块处理句柄接口
 *
 * <AUTHOR>
 */
public abstract class RouterHandler implements InitializingBean {
    protected final Logger LOGGER = LoggerFactory.getLogger(getClass());

    @Autowired
    protected RouterContext routerContext;

    /**
     * 方法映射列表 key:cmdId,value:ASMMethod
     */
    private Map<Integer, ASMMethod> cmdMethodMaps = new HashMap<>();

    /**
     * 命令映射列表
     */
    private Map<Integer, Cmd> cmdMaps = new HashMap<>();

    /**
     * get module
     *
     * @return
     */
    public abstract int getModule();

    public abstract HandlerType getHandlerType();

    @Override
    public void afterPropertiesSet() throws Exception {
        Method[] mList = this.getClass().getDeclaredMethods();
        MethodAccess methodAccess = MethodAccess.get(this.getClass());
        for (Method m : mList) {
            m.setAccessible(true);
            Cmd c = m.getAnnotation(Cmd.class);
            if (c != null) {
                if (cmdMethodMaps.containsKey(c.Id())) {
                    LOGGER.error("module:[{}] cmd:[{}] duplicated key!!!", getModule(), c.Id());
                    continue;
                }
                cmdMethodMaps.put(c.Id(), ASMMethod.valueOf(m, methodAccess, this));
                cmdMaps.put(c.Id(), c);
            }
        }
        routerContext.register(this);
    }

    public Cmd getCmd(int cmd) {
        return cmdMaps.get(cmd);
    }

    public boolean containsMethod(int cmd) {
        return cmdMethodMaps.containsKey(cmd);
    }

    /**
     * 调用方法
     *
     * @param cmd  cmd id
     * @param args 方法的参数
     * @return
     */
    public Object invoke(int cmd, Object... args) {
        ASMMethod method = cmdMethodMaps.get(cmd);
        if (method == null) {
            if (LOGGER.isInfoEnabled()) {
                LOGGER.info("[invokeMethod] module:{} cmd:{} args:{} ", getModule(), cmd, args);
            }
            return null;
        }

        try {
            return method.invoke(args);
        } catch (Exception ex) {
            LOGGER.error("[invokeMethod] {}", ex);
            return null;
        }
    }

    public void channelWrite(Channel channel, DataPacket packet, GeneratedMessageV3 value) {
        packet.setValue(value);
        channel.writeAndFlush(packet);
    }

    /**
     * 发送状态码
     *
     * @param channel
     * @param packet
     * @param statusCode
     */
    public void channelWrite(Channel channel, DataPacket packet, short statusCode) {
        packet.setStatusCode(statusCode);
        channel.writeAndFlush(packet);
    }
}
