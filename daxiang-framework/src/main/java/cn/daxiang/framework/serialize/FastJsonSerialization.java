package cn.daxiang.framework.serialize;

import cn.daxiang.framework.netty.SocketConstant;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.JSONSerializer;
import com.alibaba.fastjson.serializer.SerializeWriter;
import com.alibaba.fastjson.serializer.SerializerFeature;

import java.io.IOException;

public class FastJsonSerialization implements Serialization {

    @Override
    public <T> byte[] encode(T data) throws IOException {
        SerializeWriter out = new SerializeWriter();
        JSONSerializer serializer = new JSONSerializer(out);
        serializer.config(SerializerFeature.WriteEnumUsingToString, true);
        serializer.config(SerializerFeature.WriteClassName, true);
        serializer.write(data);
        return out.toBytes(SocketConstant.CHARSET);
    }

    @Override
    public <T> T decode(byte[] bytes, Class<T> clazz) throws IOException {
        return JSON.parseObject(new String(bytes), clazz);
    }
}
