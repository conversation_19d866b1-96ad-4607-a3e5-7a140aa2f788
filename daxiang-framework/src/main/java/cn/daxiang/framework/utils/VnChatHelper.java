package cn.daxiang.framework.utils;

import com.google.common.collect.Lists;

import java.util.Collection;

/**
 * 越南聊天帮助类
 */
public class VnChatHelper {
    private static final Collection<String> validNameText =
        Lists.newArrayList("a", "á", "à", "ả", "ã", "ạ", "ă", "ắ", "ằ", "ẳ", "ẵ", "ặ", "â", "ấ", "ầ", "ẩ", "ẫ", "ậ", "b", "c", "d", "đ", "e", "é", "è", "ẻ", "ẽ", "ẹ", "ê", "ế",
            "ề", "ể", "ễ", "ệ", "f", "g", "h", "i", "í", "ì", "ỉ", "ĩ", "ị", "j", "k", "l", "m", "n", "o", "ó", "ò", "ỏ", "õ", "ọ", "ô", "ố", "ồ", "ổ", "ỗ", "ộ", "ơ", "ớ", "ờ",
            "ở", "ỡ", "ợ", "p", "q", "r", "s", "t", "u", "ú", "ù", "ủ", "ũ", "ụ", "ư", "ứ", "ừ", "ử", "ữ", "ự", "v", "w", "x", "y", "ý", "ỳ", "ỷ", "ỹ", "ỵ", "z", "A", "Á", "À",
            "Ả", "Ã", "Ạ", "Ă", "Ắ", "Ằ", "Ẳ", "Ẵ", "Ặ", "Â", "Ấ", "Ầ", "Ẩ", "Ẫ", "Ậ", "B", "C", "D", "Đ", "E", "É", "È", "Ẻ", "Ẽ", "Ẹ", "Ê", "Ế", "Ề", "Ể", "Ễ", "Ệ", "F", "G",
            "H", "I", "Í", "Ì", "Ỉ", "Ĩ", "Ị", "J", "K", "L", "M", "N", "O", "Ó", "Ò", "Ỏ", "Õ", "Ọ", "Ô", "Ố", "Ồ", "Ổ", "Ỗ", "Ộ", "Ơ", "Ớ", "Ờ", "Ở", "Ỡ", "Ợ", "P", "Q", "R",
            "S", "T", "U", "Ú", "Ù", "Ủ", "Ũ", "Ụ", "Ư", "Ứ", "Ừ", "Ử", "Ữ", "Ự", "V", "W", "X", "Y", "Ý", "Ỳ", "Ỷ", "Ỹ", "Ỵ", "Z", "1", "2", "3", "4", "5", "6", "7", "8", "9",
            "0", " ");

    private static final Collection<String> validText =
        Lists.newArrayList("a", "á", "à", "ả", "ã", "ạ", "ă", "ắ", "ằ", "ẳ", "ẵ", "ặ", "â", "ấ", "ầ", "ẩ", "ẫ", "ậ", "b", "c", "d", "đ", "e", "é", "è", "ẻ", "ẽ", "ẹ", "ê", "ế",
            "ề", "ể", "ễ", "ệ", "f", "g", "h", "i", "í", "ì", "ỉ", "ĩ", "ị", "j", "k", "l", "m", "n", "o", "ó", "ò", "ỏ", "õ", "ọ", "ô", "ố", "ồ", "ổ", "ỗ", "ộ", "ơ", "ớ", "ờ",
            "ở", "ỡ", "ợ", "p", "q", "r", "s", "t", "u", "ú", "ù", "ủ", "ũ", "ụ", "ư", "ứ", "ừ", "ử", "ữ", "ự", "v", "w", "x", "y", "ý", "ỳ", "ỷ", "ỹ", "ỵ", "z", "A", "Á", "À",
            "Ả", "Ã", "Ạ", "Ă", "Ắ", "Ằ", "Ẳ", "Ẵ", "Ặ", "Â", "Ấ", "Ầ", "Ẩ", "Ẫ", "Ậ", "B", "C", "D", "Đ", "E", "É", "È", "Ẻ", "Ẽ", "Ẹ", "Ê", "Ế", "Ề", "Ể", "Ễ", "Ệ", "F", "G",
            "H", "I", "Í", "Ì", "Ỉ", "Ĩ", "Ị", "J", "K", "L", "M", "N", "O", "Ó", "Ò", "Ỏ", "Õ", "Ọ", "Ô", "Ố", "Ồ", "Ổ", "Ỗ", "Ộ", "Ơ", "Ớ", "Ờ", "Ở", "Ỡ", "Ợ", "P", "Q", "R",
            "S", "T", "U", "Ú", "Ù", "Ủ", "Ũ", "Ụ", "Ư", "Ứ", "Ừ", "Ử", "Ữ", "Ự", "V", "W", "X", "Y", "Ý", "Ỳ", "Ỷ", "Ỹ", "Ỵ", "Z", "1", "2", "3", "4", "5", "6", "7", "8", "9",
            "0", "'", "“", "”", ";", ":", "!", "(", ")", "?", ".", ",", "%", "[", "]", "{", "}", "$", "×", "÷", "~", "*", "_", "-", "+", " ");

    /**
     * 判断是否是有效
     *
     * @param input
     * @return true:有效 false:无效
     */
    public static boolean isValidInput(String input) {
        for (char c : input.toCharArray()) {
            String charAsString = String.valueOf(c);
            if (!validNameText.contains(charAsString)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 替换非越南符号为*
     *
     * @param input
     * @return
     */
    public static String replace(String input) {
        if (input == null || input.isEmpty()) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        for (char c : input.toCharArray()) {
            String charAsString = String.valueOf(c);
            if (!validText.contains(charAsString)) {
                sb.append("*");
            } else {
                sb.append(charAsString);
            }
        }
        return sb.toString();
    }

    public static void main(String[] args) {
        String sampleText = "Ò+1";
        System.out.println("Is \"" + sampleText + "\" valid? " + isValidInput(sampleText));

        String invalidText = "Xin chào Ò a啊啊";
        System.out.println("Is \"" + invalidText + "\" valid? " + replace(invalidText));
    }
}
