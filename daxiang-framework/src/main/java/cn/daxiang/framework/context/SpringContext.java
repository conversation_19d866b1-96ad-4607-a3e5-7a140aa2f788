package cn.daxiang.framework.context;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.context.support.AbstractApplicationContext;
import org.springframework.context.support.ClassPathXmlApplicationContext;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

public class SpringContext {
    private static final Logger LOGGER = LoggerFactory.getLogger(SpringContext.class);
    private static SpringContext CONTEXT;
    private AbstractApplicationContext appContext;

    private SpringContext() {
        appContext = new ClassPathXmlApplicationContext("**/*applicationContext*.xml");
    }

    private static SpringContext getSpringContext() {
        if (CONTEXT == null) {
            CONTEXT = new SpringContext();
        }
        return CONTEXT;
    }

    public static AbstractApplicationContext getContext() {
        return getSpringContext().appContext;
    }

    public static Object getBean(String name) {
        return getContext().getBean(name);
    }

    @SuppressWarnings("unchecked")
    public static <T> T getBean(Class<T> beanClazz) {
        String[] names = getContext().getBeanNamesForType(beanClazz);
        if ((names != null) && (names.length > 0)) {
            if (names.length == 1) {
                return (T) getContext().getBean(names[0]);
            }
            LOGGER.error("[{}] interface class must be only!", beanClazz);
        } else {
            LOGGER.error("[{}] class not found!", beanClazz);
        }
        return null;
    }

    public static List<Object> getBeanList(ApplicationContext context) {
        List<Object> list = new ArrayList<>();
        String[] names = context.getBeanDefinitionNames();
        for (String n : names) {
            list.add(context.getBean(n));
        }
        return list;
    }

    public static <T> Collection<T> getBeanList(Class<T> beanClazz) {
        Map<String, T> beanMap = getContext().getBeansOfType(beanClazz);
        return beanMap.values();
    }

    public static String getProperty(String key) {
        return getContext().getEnvironment().getProperty(key);
    }
}
