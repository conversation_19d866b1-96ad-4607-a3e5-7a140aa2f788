package cn.daxiang.framework.result;

import cn.daxiang.shared.StatusCode;
import com.alibaba.fastjson.annotation.JSONField;

/**
 * 最简单的结果类,返回一个状态啥的用这个
 *
 * <AUTHOR>
 */
public class Result {

    public short statusCode = 0;

    public Result() {
    }

    public Result(short statusCode) {
        this.statusCode = statusCode;
    }

    public static Result valueOf() {
        return new Result();
    }

    public static Result fail() {
        return new Result(StatusCode.NO_RESULTS);
    }

    public static Result valueOf(short statusCode) {
        return new Result(statusCode);
    }

    @JSONField(serialize = false)
    public boolean isFail() {
        return statusCode != StatusCode.SUCCESS;
    }

    @JSONField(serialize = false)
    public boolean isOk() {
        return statusCode == StatusCode.SUCCESS;
    }

    @Override
    public String toString() {
        return "Result [statusCode=" + statusCode + "]";
    }

}
