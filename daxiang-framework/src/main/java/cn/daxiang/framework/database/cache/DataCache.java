package cn.daxiang.framework.database.cache;

import java.util.Collection;

/**
 * 缓存接口
 *
 * <AUTHOR>
 */
public interface DataCache extends CacheListener {
    /**
     * 从缓存取
     *
     * @param key
     * @return
     */
    Object getFromCache(Object key);

    /**
     * 放入缓存
     *
     * @param key
     * @param value
     */
    void setToCache(Object key, Object value);

    /**
     * 获取缓存数量
     *
     * @return
     */
    long size();

    /**
     * 是否存在缓存中
     *
     * @param key
     * @return
     */
    boolean exsit(Object key);

    /**
     * 获取所有缓存
     *
     * @return
     */
    Collection<?> all();

    /**
     * 清理
     */
    void clear();
}
