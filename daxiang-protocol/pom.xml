<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>cn.daxiang.protocol</groupId>
	<artifactId>daxiang-protocol</artifactId>
	<version>4.0.1-SNAPSHOT</version>
	<packaging>jar</packaging>

	<name>daxiang-protocol</name>
	<url>http://maven.apache.org</url>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<jdk-version>1.8</jdk-version>
		<protoc.version>3.7.0</protoc.version>
	</properties>

	<dependencies>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<version>3.8.1</version>
			<scope>test</scope>
		</dependency>
        <dependency>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java</artifactId>
            <version>3.5.1</version>
        </dependency>
	</dependencies>
	<build>
		<extensions>
			<extension>
				<groupId>kr.motd.maven</groupId>
				<artifactId>os-maven-plugin</artifactId>
				<version>1.7.0</version>
			</extension>
		</extensions>
		<plugins>
<!--			<plugin>-->
<!--				<groupId>org.xolstice.maven.plugins</groupId>-->
<!--				<artifactId>protobuf-maven-plugin</artifactId>-->
<!--				<version>0.6.1</version>-->
<!--				<extensions>true</extensions>-->
<!--				<executions>-->
<!--					<execution>-->
<!--						<goals>-->
<!--							<goal>compile</goal>-->
<!--							<goal>test-compile</goal>-->
<!--						</goals>-->
<!--					</execution>-->
<!--				</executions>-->
<!--				<configuration>-->
<!--					<includes>-->
<!--						<include>**/*.proto</include>-->
<!--					</includes>-->
<!--&lt;!&ndash;					<additionalProtoPathElements>&ndash;&gt;-->
<!--&lt;!&ndash;						<additionalProtoPathElement>${project.basedir}/src/main/proto</additionalProtoPathElement>&ndash;&gt;-->
<!--&lt;!&ndash;					</additionalProtoPathElements>&ndash;&gt;-->
<!--					<protoSourceRoot>${project.basedir}/src/main/proto</protoSourceRoot>-->
<!--					<outputDirectory>${project.basedir}/src/main/java</outputDirectory>-->
<!--					<clearOutputDirectory>false</clearOutputDirectory>-->
<!--					<protocArtifact>com.google.protobuf:protoc:${protoc.version}:exe:${os.detected.classifier}</protocArtifact>-->
<!--				</configuration>-->
<!--			</plugin>-->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.1</version>
				<configuration>
					<source>${jdk-version}</source>
					<target>${jdk-version}</target>
					<encoding>UTF-8</encoding>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<version>2.22.2</version>
				<configuration>
					<skip>true</skip>
				</configuration>
			</plugin>
		</plugins>
	</build>
</project>
