// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: battle/battleCommonProtocol.proto

package cn.daxiang.protocol.battle;

public final class BattleCommonProtocol {
  private BattleCommonProtocol() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface RequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Request)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code Request}
   */
  public  static final class Request extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Request)
      RequestOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Request.newBuilder() to construct.
    private Request(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Request() {
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Request(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.daxiang.protocol.battle.BattleCommonProtocol.internal_static_Request_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.daxiang.protocol.battle.BattleCommonProtocol.internal_static_Request_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.daxiang.protocol.battle.BattleCommonProtocol.Request.class, cn.daxiang.protocol.battle.BattleCommonProtocol.Request.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof cn.daxiang.protocol.battle.BattleCommonProtocol.Request)) {
        return super.equals(obj);
      }
      cn.daxiang.protocol.battle.BattleCommonProtocol.Request other = (cn.daxiang.protocol.battle.BattleCommonProtocol.Request) obj;

      boolean result = true;
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static cn.daxiang.protocol.battle.BattleCommonProtocol.Request parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.battle.BattleCommonProtocol.Request parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.battle.BattleCommonProtocol.Request parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.battle.BattleCommonProtocol.Request parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.battle.BattleCommonProtocol.Request parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.battle.BattleCommonProtocol.Request parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.battle.BattleCommonProtocol.Request parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.battle.BattleCommonProtocol.Request parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.battle.BattleCommonProtocol.Request parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.battle.BattleCommonProtocol.Request parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.battle.BattleCommonProtocol.Request parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.battle.BattleCommonProtocol.Request parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(cn.daxiang.protocol.battle.BattleCommonProtocol.Request prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code Request}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Request)
        cn.daxiang.protocol.battle.BattleCommonProtocol.RequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.daxiang.protocol.battle.BattleCommonProtocol.internal_static_Request_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.daxiang.protocol.battle.BattleCommonProtocol.internal_static_Request_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.daxiang.protocol.battle.BattleCommonProtocol.Request.class, cn.daxiang.protocol.battle.BattleCommonProtocol.Request.Builder.class);
      }

      // Construct using cn.daxiang.protocol.battle.BattleCommonProtocol.Request.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return cn.daxiang.protocol.battle.BattleCommonProtocol.internal_static_Request_descriptor;
      }

      public cn.daxiang.protocol.battle.BattleCommonProtocol.Request getDefaultInstanceForType() {
        return cn.daxiang.protocol.battle.BattleCommonProtocol.Request.getDefaultInstance();
      }

      public cn.daxiang.protocol.battle.BattleCommonProtocol.Request build() {
        cn.daxiang.protocol.battle.BattleCommonProtocol.Request result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public cn.daxiang.protocol.battle.BattleCommonProtocol.Request buildPartial() {
        cn.daxiang.protocol.battle.BattleCommonProtocol.Request result = new cn.daxiang.protocol.battle.BattleCommonProtocol.Request(this);
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof cn.daxiang.protocol.battle.BattleCommonProtocol.Request) {
          return mergeFrom((cn.daxiang.protocol.battle.BattleCommonProtocol.Request)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(cn.daxiang.protocol.battle.BattleCommonProtocol.Request other) {
        if (other == cn.daxiang.protocol.battle.BattleCommonProtocol.Request.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        cn.daxiang.protocol.battle.BattleCommonProtocol.Request parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (cn.daxiang.protocol.battle.BattleCommonProtocol.Request) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Request)
    }

    // @@protoc_insertion_point(class_scope:Request)
    private static final cn.daxiang.protocol.battle.BattleCommonProtocol.Request DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new cn.daxiang.protocol.battle.BattleCommonProtocol.Request();
    }

    public static cn.daxiang.protocol.battle.BattleCommonProtocol.Request getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<Request>
        PARSER = new com.google.protobuf.AbstractParser<Request>() {
      public Request parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Request(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Request> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Request> getParserForType() {
      return PARSER;
    }

    public cn.daxiang.protocol.battle.BattleCommonProtocol.Request getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Response)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code Response}
   */
  public  static final class Response extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Response)
      ResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Response.newBuilder() to construct.
    private Response(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Response() {
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Response(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.daxiang.protocol.battle.BattleCommonProtocol.internal_static_Response_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.daxiang.protocol.battle.BattleCommonProtocol.internal_static_Response_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.daxiang.protocol.battle.BattleCommonProtocol.Response.class, cn.daxiang.protocol.battle.BattleCommonProtocol.Response.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof cn.daxiang.protocol.battle.BattleCommonProtocol.Response)) {
        return super.equals(obj);
      }
      cn.daxiang.protocol.battle.BattleCommonProtocol.Response other = (cn.daxiang.protocol.battle.BattleCommonProtocol.Response) obj;

      boolean result = true;
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static cn.daxiang.protocol.battle.BattleCommonProtocol.Response parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.battle.BattleCommonProtocol.Response parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.battle.BattleCommonProtocol.Response parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.battle.BattleCommonProtocol.Response parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.battle.BattleCommonProtocol.Response parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.battle.BattleCommonProtocol.Response parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.battle.BattleCommonProtocol.Response parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.battle.BattleCommonProtocol.Response parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.battle.BattleCommonProtocol.Response parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.battle.BattleCommonProtocol.Response parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.battle.BattleCommonProtocol.Response parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.battle.BattleCommonProtocol.Response parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(cn.daxiang.protocol.battle.BattleCommonProtocol.Response prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code Response}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Response)
        cn.daxiang.protocol.battle.BattleCommonProtocol.ResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.daxiang.protocol.battle.BattleCommonProtocol.internal_static_Response_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.daxiang.protocol.battle.BattleCommonProtocol.internal_static_Response_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.daxiang.protocol.battle.BattleCommonProtocol.Response.class, cn.daxiang.protocol.battle.BattleCommonProtocol.Response.Builder.class);
      }

      // Construct using cn.daxiang.protocol.battle.BattleCommonProtocol.Response.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return cn.daxiang.protocol.battle.BattleCommonProtocol.internal_static_Response_descriptor;
      }

      public cn.daxiang.protocol.battle.BattleCommonProtocol.Response getDefaultInstanceForType() {
        return cn.daxiang.protocol.battle.BattleCommonProtocol.Response.getDefaultInstance();
      }

      public cn.daxiang.protocol.battle.BattleCommonProtocol.Response build() {
        cn.daxiang.protocol.battle.BattleCommonProtocol.Response result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public cn.daxiang.protocol.battle.BattleCommonProtocol.Response buildPartial() {
        cn.daxiang.protocol.battle.BattleCommonProtocol.Response result = new cn.daxiang.protocol.battle.BattleCommonProtocol.Response(this);
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof cn.daxiang.protocol.battle.BattleCommonProtocol.Response) {
          return mergeFrom((cn.daxiang.protocol.battle.BattleCommonProtocol.Response)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(cn.daxiang.protocol.battle.BattleCommonProtocol.Response other) {
        if (other == cn.daxiang.protocol.battle.BattleCommonProtocol.Response.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        cn.daxiang.protocol.battle.BattleCommonProtocol.Response parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (cn.daxiang.protocol.battle.BattleCommonProtocol.Response) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Response)
    }

    // @@protoc_insertion_point(class_scope:Response)
    private static final cn.daxiang.protocol.battle.BattleCommonProtocol.Response DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new cn.daxiang.protocol.battle.BattleCommonProtocol.Response();
    }

    public static cn.daxiang.protocol.battle.BattleCommonProtocol.Response getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<Response>
        PARSER = new com.google.protobuf.AbstractParser<Response>() {
      public Response parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Response(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Response> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Response> getParserForType() {
      return PARSER;
    }

    public cn.daxiang.protocol.battle.BattleCommonProtocol.Response getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface IntPacketOrBuilder extends
      // @@protoc_insertion_point(interface_extends:IntPacket)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 value = 1;</code>
     */
    int getValue();
  }
  /**
   * Protobuf type {@code IntPacket}
   */
  public  static final class IntPacket extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:IntPacket)
      IntPacketOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use IntPacket.newBuilder() to construct.
    private IntPacket(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private IntPacket() {
      value_ = 0;
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private IntPacket(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {

              value_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.daxiang.protocol.battle.BattleCommonProtocol.internal_static_IntPacket_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.daxiang.protocol.battle.BattleCommonProtocol.internal_static_IntPacket_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.daxiang.protocol.battle.BattleCommonProtocol.IntPacket.class, cn.daxiang.protocol.battle.BattleCommonProtocol.IntPacket.Builder.class);
    }

    public static final int VALUE_FIELD_NUMBER = 1;
    private int value_;
    /**
     * <code>int32 value = 1;</code>
     */
    public int getValue() {
      return value_;
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (value_ != 0) {
        output.writeInt32(1, value_);
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (value_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, value_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof cn.daxiang.protocol.battle.BattleCommonProtocol.IntPacket)) {
        return super.equals(obj);
      }
      cn.daxiang.protocol.battle.BattleCommonProtocol.IntPacket other = (cn.daxiang.protocol.battle.BattleCommonProtocol.IntPacket) obj;

      boolean result = true;
      result = result && (getValue()
          == other.getValue());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + VALUE_FIELD_NUMBER;
      hash = (53 * hash) + getValue();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static cn.daxiang.protocol.battle.BattleCommonProtocol.IntPacket parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.battle.BattleCommonProtocol.IntPacket parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.battle.BattleCommonProtocol.IntPacket parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.battle.BattleCommonProtocol.IntPacket parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.battle.BattleCommonProtocol.IntPacket parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.battle.BattleCommonProtocol.IntPacket parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.battle.BattleCommonProtocol.IntPacket parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.battle.BattleCommonProtocol.IntPacket parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.battle.BattleCommonProtocol.IntPacket parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.battle.BattleCommonProtocol.IntPacket parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.battle.BattleCommonProtocol.IntPacket parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.battle.BattleCommonProtocol.IntPacket parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(cn.daxiang.protocol.battle.BattleCommonProtocol.IntPacket prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code IntPacket}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:IntPacket)
        cn.daxiang.protocol.battle.BattleCommonProtocol.IntPacketOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.daxiang.protocol.battle.BattleCommonProtocol.internal_static_IntPacket_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.daxiang.protocol.battle.BattleCommonProtocol.internal_static_IntPacket_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.daxiang.protocol.battle.BattleCommonProtocol.IntPacket.class, cn.daxiang.protocol.battle.BattleCommonProtocol.IntPacket.Builder.class);
      }

      // Construct using cn.daxiang.protocol.battle.BattleCommonProtocol.IntPacket.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        value_ = 0;

        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return cn.daxiang.protocol.battle.BattleCommonProtocol.internal_static_IntPacket_descriptor;
      }

      public cn.daxiang.protocol.battle.BattleCommonProtocol.IntPacket getDefaultInstanceForType() {
        return cn.daxiang.protocol.battle.BattleCommonProtocol.IntPacket.getDefaultInstance();
      }

      public cn.daxiang.protocol.battle.BattleCommonProtocol.IntPacket build() {
        cn.daxiang.protocol.battle.BattleCommonProtocol.IntPacket result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public cn.daxiang.protocol.battle.BattleCommonProtocol.IntPacket buildPartial() {
        cn.daxiang.protocol.battle.BattleCommonProtocol.IntPacket result = new cn.daxiang.protocol.battle.BattleCommonProtocol.IntPacket(this);
        result.value_ = value_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof cn.daxiang.protocol.battle.BattleCommonProtocol.IntPacket) {
          return mergeFrom((cn.daxiang.protocol.battle.BattleCommonProtocol.IntPacket)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(cn.daxiang.protocol.battle.BattleCommonProtocol.IntPacket other) {
        if (other == cn.daxiang.protocol.battle.BattleCommonProtocol.IntPacket.getDefaultInstance()) return this;
        if (other.getValue() != 0) {
          setValue(other.getValue());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        cn.daxiang.protocol.battle.BattleCommonProtocol.IntPacket parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (cn.daxiang.protocol.battle.BattleCommonProtocol.IntPacket) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int value_ ;
      /**
       * <code>int32 value = 1;</code>
       */
      public int getValue() {
        return value_;
      }
      /**
       * <code>int32 value = 1;</code>
       */
      public Builder setValue(int value) {
        
        value_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 value = 1;</code>
       */
      public Builder clearValue() {
        
        value_ = 0;
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:IntPacket)
    }

    // @@protoc_insertion_point(class_scope:IntPacket)
    private static final cn.daxiang.protocol.battle.BattleCommonProtocol.IntPacket DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new cn.daxiang.protocol.battle.BattleCommonProtocol.IntPacket();
    }

    public static cn.daxiang.protocol.battle.BattleCommonProtocol.IntPacket getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<IntPacket>
        PARSER = new com.google.protobuf.AbstractParser<IntPacket>() {
      public IntPacket parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new IntPacket(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<IntPacket> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<IntPacket> getParserForType() {
      return PARSER;
    }

    public cn.daxiang.protocol.battle.BattleCommonProtocol.IntPacket getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ActorProfileOrBuilder extends
      // @@protoc_insertion_point(interface_extends:ActorProfile)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     ** 服务器ID 
     * </pre>
     *
     * <code>int32 serverId = 1;</code>
     */
    int getServerId();

    /**
     * <pre>
     ** 角色ID 
     * </pre>
     *
     * <code>int64 actorId = 2;</code>
     */
    long getActorId();

    /**
     * <pre>
     ** 角色名 
     * </pre>
     *
     * <code>string actorName = 3;</code>
     */
    java.lang.String getActorName();
    /**
     * <pre>
     ** 角色名 
     * </pre>
     *
     * <code>string actorName = 3;</code>
     */
    com.google.protobuf.ByteString
        getActorNameBytes();

    /**
     * <pre>
     ** 性别 
     * </pre>
     *
     * <code>bool sex = 4;</code>
     */
    boolean getSex();

    /**
     * <pre>
     ** 等级 
     * </pre>
     *
     * <code>int32 level = 5;</code>
     */
    int getLevel();

    /**
     * <pre>
     ** vip等级 
     * </pre>
     *
     * <code>int32 vipLevel = 6;</code>
     */
    int getVipLevel();

    /**
     * <pre>
     ** 头像ID 
     * </pre>
     *
     * <code>int32 avatarId = 7;</code>
     */
    int getAvatarId();

    /**
     * <pre>
     ** 战力 
     * </pre>
     *
     * <code>int64 power = 8;</code>
     */
    long getPower();

    /**
     * <pre>
     ** 国家名称 
     * </pre>
     *
     * <code>string nationName = 9;</code>
     */
    java.lang.String getNationName();
    /**
     * <pre>
     ** 国家名称 
     * </pre>
     *
     * <code>string nationName = 9;</code>
     */
    com.google.protobuf.ByteString
        getNationNameBytes();

    /**
     * <pre>
     ** 阵容显示英雄ID 
     * </pre>
     *
     * <code>int32 showId = 10;</code>
     */
    int getShowId();

    /**
     * <pre>
     ** 当前服务器ID 
     * </pre>
     *
     * <code>int32 currentServerId = 11;</code>
     */
    int getCurrentServerId();

    /**
     * <pre>
     **在线状态
     * </pre>
     *
     * <code>bool onlineState = 12;</code>
     */
    boolean getOnlineState();

    /**
     * <pre>
     **最后一次登录时间
     * </pre>
     *
     * <code>int64 lastLogoutTime = 13;</code>
     */
    long getLastLogoutTime();

    /**
     * <pre>
     ** 头像框ID 
     * </pre>
     *
     * <code>int32 avatarFrameId = 14;</code>
     */
    int getAvatarFrameId();

    /**
     * <pre>
     ** 魔窟解锁状态列表
     * </pre>
     *
     * <code>repeated int32 caveSingleChallengeList = 15;</code>
     */
    java.util.List<java.lang.Integer> getCaveSingleChallengeListList();
    /**
     * <pre>
     ** 魔窟解锁状态列表
     * </pre>
     *
     * <code>repeated int32 caveSingleChallengeList = 15;</code>
     */
    int getCaveSingleChallengeListCount();
    /**
     * <pre>
     ** 魔窟解锁状态列表
     * </pre>
     *
     * <code>repeated int32 caveSingleChallengeList = 15;</code>
     */
    int getCaveSingleChallengeList(int index);

    /**
     * <pre>
     ** 国家ID 
     * </pre>
     *
     * <code>int64 nationId = 16;</code>
     */
    long getNationId();

    /**
     * <pre>
     ** 官职 
     * </pre>
     *
     * <code>int32 official = 17;</code>
     */
    int getOfficial();

    /**
     * <pre>
     ** 已激活专武皮肤Id 
     * </pre>
     *
     * <code>repeated int32 immortalsSkinIds = 18;</code>
     */
    java.util.List<java.lang.Integer> getImmortalsSkinIdsList();
    /**
     * <pre>
     ** 已激活专武皮肤Id 
     * </pre>
     *
     * <code>repeated int32 immortalsSkinIds = 18;</code>
     */
    int getImmortalsSkinIdsCount();
    /**
     * <pre>
     ** 已激活专武皮肤Id 
     * </pre>
     *
     * <code>repeated int32 immortalsSkinIds = 18;</code>
     */
    int getImmortalsSkinIds(int index);

    /**
     * <pre>
     ** 称号 
     * </pre>
     *
     * <code>int32 titleId = 19;</code>
     */
    int getTitleId();

    /**
     * <pre>
     ** 第三方渠道用户ID 
     * </pre>
     *
     * <code>string sdkUserId = 20;</code>
     */
    java.lang.String getSdkUserId();
    /**
     * <pre>
     ** 第三方渠道用户ID 
     * </pre>
     *
     * <code>string sdkUserId = 20;</code>
     */
    com.google.protobuf.ByteString
        getSdkUserIdBytes();

    /**
     * <pre>
     ** 阵容显示英雄是否有专武 
     * </pre>
     *
     * <code>bool showImmortals = 21;</code>
     */
    boolean getShowImmortals();

    /**
     * <pre>
     ** 阵容显示英雄星级（小星级） 
     * </pre>
     *
     * <code>int32 showHeroStar = 22;</code>
     */
    int getShowHeroStar();

    /**
     * <pre>
     ** 被点赞数量 {rankType:praisedCount}
     * </pre>
     *
     * <code>map&lt;int32, int64&gt; praisedCountMap = 23;</code>
     */
    int getPraisedCountMapCount();
    /**
     * <pre>
     ** 被点赞数量 {rankType:praisedCount}
     * </pre>
     *
     * <code>map&lt;int32, int64&gt; praisedCountMap = 23;</code>
     */
    boolean containsPraisedCountMap(
        int key);
    /**
     * Use {@link #getPraisedCountMapMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Integer, java.lang.Long>
    getPraisedCountMap();
    /**
     * <pre>
     ** 被点赞数量 {rankType:praisedCount}
     * </pre>
     *
     * <code>map&lt;int32, int64&gt; praisedCountMap = 23;</code>
     */
    java.util.Map<java.lang.Integer, java.lang.Long>
    getPraisedCountMapMap();
    /**
     * <pre>
     ** 被点赞数量 {rankType:praisedCount}
     * </pre>
     *
     * <code>map&lt;int32, int64&gt; praisedCountMap = 23;</code>
     */

    long getPraisedCountMapOrDefault(
        int key,
        long defaultValue);
    /**
     * <pre>
     ** 被点赞数量 {rankType:praisedCount}
     * </pre>
     *
     * <code>map&lt;int32, int64&gt; praisedCountMap = 23;</code>
     */

    long getPraisedCountMapOrThrow(
        int key);
  }
  /**
   * <pre>
   ** 角色简介信息 
   * </pre>
   *
   * Protobuf type {@code ActorProfile}
   */
  public  static final class ActorProfile extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:ActorProfile)
      ActorProfileOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ActorProfile.newBuilder() to construct.
    private ActorProfile(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ActorProfile() {
      serverId_ = 0;
      actorId_ = 0L;
      actorName_ = "";
      sex_ = false;
      level_ = 0;
      vipLevel_ = 0;
      avatarId_ = 0;
      power_ = 0L;
      nationName_ = "";
      showId_ = 0;
      currentServerId_ = 0;
      onlineState_ = false;
      lastLogoutTime_ = 0L;
      avatarFrameId_ = 0;
      caveSingleChallengeList_ = java.util.Collections.emptyList();
      nationId_ = 0L;
      official_ = 0;
      immortalsSkinIds_ = java.util.Collections.emptyList();
      titleId_ = 0;
      sdkUserId_ = "";
      showImmortals_ = false;
      showHeroStar_ = 0;
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ActorProfile(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {

              serverId_ = input.readInt32();
              break;
            }
            case 16: {

              actorId_ = input.readInt64();
              break;
            }
            case 26: {
              java.lang.String s = input.readStringRequireUtf8();

              actorName_ = s;
              break;
            }
            case 32: {

              sex_ = input.readBool();
              break;
            }
            case 40: {

              level_ = input.readInt32();
              break;
            }
            case 48: {

              vipLevel_ = input.readInt32();
              break;
            }
            case 56: {

              avatarId_ = input.readInt32();
              break;
            }
            case 64: {

              power_ = input.readInt64();
              break;
            }
            case 74: {
              java.lang.String s = input.readStringRequireUtf8();

              nationName_ = s;
              break;
            }
            case 80: {

              showId_ = input.readInt32();
              break;
            }
            case 88: {

              currentServerId_ = input.readInt32();
              break;
            }
            case 96: {

              onlineState_ = input.readBool();
              break;
            }
            case 104: {

              lastLogoutTime_ = input.readInt64();
              break;
            }
            case 112: {

              avatarFrameId_ = input.readInt32();
              break;
            }
            case 120: {
              if (!((mutable_bitField0_ & 0x00004000) == 0x00004000)) {
                caveSingleChallengeList_ = new java.util.ArrayList<java.lang.Integer>();
                mutable_bitField0_ |= 0x00004000;
              }
              caveSingleChallengeList_.add(input.readInt32());
              break;
            }
            case 122: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00004000) == 0x00004000) && input.getBytesUntilLimit() > 0) {
                caveSingleChallengeList_ = new java.util.ArrayList<java.lang.Integer>();
                mutable_bitField0_ |= 0x00004000;
              }
              while (input.getBytesUntilLimit() > 0) {
                caveSingleChallengeList_.add(input.readInt32());
              }
              input.popLimit(limit);
              break;
            }
            case 128: {

              nationId_ = input.readInt64();
              break;
            }
            case 136: {

              official_ = input.readInt32();
              break;
            }
            case 144: {
              if (!((mutable_bitField0_ & 0x00020000) == 0x00020000)) {
                immortalsSkinIds_ = new java.util.ArrayList<java.lang.Integer>();
                mutable_bitField0_ |= 0x00020000;
              }
              immortalsSkinIds_.add(input.readInt32());
              break;
            }
            case 146: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00020000) == 0x00020000) && input.getBytesUntilLimit() > 0) {
                immortalsSkinIds_ = new java.util.ArrayList<java.lang.Integer>();
                mutable_bitField0_ |= 0x00020000;
              }
              while (input.getBytesUntilLimit() > 0) {
                immortalsSkinIds_.add(input.readInt32());
              }
              input.popLimit(limit);
              break;
            }
            case 152: {

              titleId_ = input.readInt32();
              break;
            }
            case 162: {
              java.lang.String s = input.readStringRequireUtf8();

              sdkUserId_ = s;
              break;
            }
            case 168: {

              showImmortals_ = input.readBool();
              break;
            }
            case 176: {

              showHeroStar_ = input.readInt32();
              break;
            }
            case 186: {
              if (!((mutable_bitField0_ & 0x00400000) == 0x00400000)) {
                praisedCountMap_ = com.google.protobuf.MapField.newMapField(
                    PraisedCountMapDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00400000;
              }
              com.google.protobuf.MapEntry<java.lang.Integer, java.lang.Long>
              praisedCountMap__ = input.readMessage(
                  PraisedCountMapDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              praisedCountMap_.getMutableMap().put(
                  praisedCountMap__.getKey(), praisedCountMap__.getValue());
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00004000) == 0x00004000)) {
          caveSingleChallengeList_ = java.util.Collections.unmodifiableList(caveSingleChallengeList_);
        }
        if (((mutable_bitField0_ & 0x00020000) == 0x00020000)) {
          immortalsSkinIds_ = java.util.Collections.unmodifiableList(immortalsSkinIds_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.daxiang.protocol.battle.BattleCommonProtocol.internal_static_ActorProfile_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 23:
          return internalGetPraisedCountMap();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.daxiang.protocol.battle.BattleCommonProtocol.internal_static_ActorProfile_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.daxiang.protocol.battle.BattleCommonProtocol.ActorProfile.class, cn.daxiang.protocol.battle.BattleCommonProtocol.ActorProfile.Builder.class);
    }

    private int bitField0_;
    public static final int SERVERID_FIELD_NUMBER = 1;
    private int serverId_;
    /**
     * <pre>
     ** 服务器ID 
     * </pre>
     *
     * <code>int32 serverId = 1;</code>
     */
    public int getServerId() {
      return serverId_;
    }

    public static final int ACTORID_FIELD_NUMBER = 2;
    private long actorId_;
    /**
     * <pre>
     ** 角色ID 
     * </pre>
     *
     * <code>int64 actorId = 2;</code>
     */
    public long getActorId() {
      return actorId_;
    }

    public static final int ACTORNAME_FIELD_NUMBER = 3;
    private volatile java.lang.Object actorName_;
    /**
     * <pre>
     ** 角色名 
     * </pre>
     *
     * <code>string actorName = 3;</code>
     */
    public java.lang.String getActorName() {
      java.lang.Object ref = actorName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        actorName_ = s;
        return s;
      }
    }
    /**
     * <pre>
     ** 角色名 
     * </pre>
     *
     * <code>string actorName = 3;</code>
     */
    public com.google.protobuf.ByteString
        getActorNameBytes() {
      java.lang.Object ref = actorName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        actorName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int SEX_FIELD_NUMBER = 4;
    private boolean sex_;
    /**
     * <pre>
     ** 性别 
     * </pre>
     *
     * <code>bool sex = 4;</code>
     */
    public boolean getSex() {
      return sex_;
    }

    public static final int LEVEL_FIELD_NUMBER = 5;
    private int level_;
    /**
     * <pre>
     ** 等级 
     * </pre>
     *
     * <code>int32 level = 5;</code>
     */
    public int getLevel() {
      return level_;
    }

    public static final int VIPLEVEL_FIELD_NUMBER = 6;
    private int vipLevel_;
    /**
     * <pre>
     ** vip等级 
     * </pre>
     *
     * <code>int32 vipLevel = 6;</code>
     */
    public int getVipLevel() {
      return vipLevel_;
    }

    public static final int AVATARID_FIELD_NUMBER = 7;
    private int avatarId_;
    /**
     * <pre>
     ** 头像ID 
     * </pre>
     *
     * <code>int32 avatarId = 7;</code>
     */
    public int getAvatarId() {
      return avatarId_;
    }

    public static final int POWER_FIELD_NUMBER = 8;
    private long power_;
    /**
     * <pre>
     ** 战力 
     * </pre>
     *
     * <code>int64 power = 8;</code>
     */
    public long getPower() {
      return power_;
    }

    public static final int NATIONNAME_FIELD_NUMBER = 9;
    private volatile java.lang.Object nationName_;
    /**
     * <pre>
     ** 国家名称 
     * </pre>
     *
     * <code>string nationName = 9;</code>
     */
    public java.lang.String getNationName() {
      java.lang.Object ref = nationName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        nationName_ = s;
        return s;
      }
    }
    /**
     * <pre>
     ** 国家名称 
     * </pre>
     *
     * <code>string nationName = 9;</code>
     */
    public com.google.protobuf.ByteString
        getNationNameBytes() {
      java.lang.Object ref = nationName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        nationName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int SHOWID_FIELD_NUMBER = 10;
    private int showId_;
    /**
     * <pre>
     ** 阵容显示英雄ID 
     * </pre>
     *
     * <code>int32 showId = 10;</code>
     */
    public int getShowId() {
      return showId_;
    }

    public static final int CURRENTSERVERID_FIELD_NUMBER = 11;
    private int currentServerId_;
    /**
     * <pre>
     ** 当前服务器ID 
     * </pre>
     *
     * <code>int32 currentServerId = 11;</code>
     */
    public int getCurrentServerId() {
      return currentServerId_;
    }

    public static final int ONLINESTATE_FIELD_NUMBER = 12;
    private boolean onlineState_;
    /**
     * <pre>
     **在线状态
     * </pre>
     *
     * <code>bool onlineState = 12;</code>
     */
    public boolean getOnlineState() {
      return onlineState_;
    }

    public static final int LASTLOGOUTTIME_FIELD_NUMBER = 13;
    private long lastLogoutTime_;
    /**
     * <pre>
     **最后一次登录时间
     * </pre>
     *
     * <code>int64 lastLogoutTime = 13;</code>
     */
    public long getLastLogoutTime() {
      return lastLogoutTime_;
    }

    public static final int AVATARFRAMEID_FIELD_NUMBER = 14;
    private int avatarFrameId_;
    /**
     * <pre>
     ** 头像框ID 
     * </pre>
     *
     * <code>int32 avatarFrameId = 14;</code>
     */
    public int getAvatarFrameId() {
      return avatarFrameId_;
    }

    public static final int CAVESINGLECHALLENGELIST_FIELD_NUMBER = 15;
    private java.util.List<java.lang.Integer> caveSingleChallengeList_;
    /**
     * <pre>
     ** 魔窟解锁状态列表
     * </pre>
     *
     * <code>repeated int32 caveSingleChallengeList = 15;</code>
     */
    public java.util.List<java.lang.Integer>
        getCaveSingleChallengeListList() {
      return caveSingleChallengeList_;
    }
    /**
     * <pre>
     ** 魔窟解锁状态列表
     * </pre>
     *
     * <code>repeated int32 caveSingleChallengeList = 15;</code>
     */
    public int getCaveSingleChallengeListCount() {
      return caveSingleChallengeList_.size();
    }
    /**
     * <pre>
     ** 魔窟解锁状态列表
     * </pre>
     *
     * <code>repeated int32 caveSingleChallengeList = 15;</code>
     */
    public int getCaveSingleChallengeList(int index) {
      return caveSingleChallengeList_.get(index);
    }
    private int caveSingleChallengeListMemoizedSerializedSize = -1;

    public static final int NATIONID_FIELD_NUMBER = 16;
    private long nationId_;
    /**
     * <pre>
     ** 国家ID 
     * </pre>
     *
     * <code>int64 nationId = 16;</code>
     */
    public long getNationId() {
      return nationId_;
    }

    public static final int OFFICIAL_FIELD_NUMBER = 17;
    private int official_;
    /**
     * <pre>
     ** 官职 
     * </pre>
     *
     * <code>int32 official = 17;</code>
     */
    public int getOfficial() {
      return official_;
    }

    public static final int IMMORTALSSKINIDS_FIELD_NUMBER = 18;
    private java.util.List<java.lang.Integer> immortalsSkinIds_;
    /**
     * <pre>
     ** 已激活专武皮肤Id 
     * </pre>
     *
     * <code>repeated int32 immortalsSkinIds = 18;</code>
     */
    public java.util.List<java.lang.Integer>
        getImmortalsSkinIdsList() {
      return immortalsSkinIds_;
    }
    /**
     * <pre>
     ** 已激活专武皮肤Id 
     * </pre>
     *
     * <code>repeated int32 immortalsSkinIds = 18;</code>
     */
    public int getImmortalsSkinIdsCount() {
      return immortalsSkinIds_.size();
    }
    /**
     * <pre>
     ** 已激活专武皮肤Id 
     * </pre>
     *
     * <code>repeated int32 immortalsSkinIds = 18;</code>
     */
    public int getImmortalsSkinIds(int index) {
      return immortalsSkinIds_.get(index);
    }
    private int immortalsSkinIdsMemoizedSerializedSize = -1;

    public static final int TITLEID_FIELD_NUMBER = 19;
    private int titleId_;
    /**
     * <pre>
     ** 称号 
     * </pre>
     *
     * <code>int32 titleId = 19;</code>
     */
    public int getTitleId() {
      return titleId_;
    }

    public static final int SDKUSERID_FIELD_NUMBER = 20;
    private volatile java.lang.Object sdkUserId_;
    /**
     * <pre>
     ** 第三方渠道用户ID 
     * </pre>
     *
     * <code>string sdkUserId = 20;</code>
     */
    public java.lang.String getSdkUserId() {
      java.lang.Object ref = sdkUserId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        sdkUserId_ = s;
        return s;
      }
    }
    /**
     * <pre>
     ** 第三方渠道用户ID 
     * </pre>
     *
     * <code>string sdkUserId = 20;</code>
     */
    public com.google.protobuf.ByteString
        getSdkUserIdBytes() {
      java.lang.Object ref = sdkUserId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        sdkUserId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int SHOWIMMORTALS_FIELD_NUMBER = 21;
    private boolean showImmortals_;
    /**
     * <pre>
     ** 阵容显示英雄是否有专武 
     * </pre>
     *
     * <code>bool showImmortals = 21;</code>
     */
    public boolean getShowImmortals() {
      return showImmortals_;
    }

    public static final int SHOWHEROSTAR_FIELD_NUMBER = 22;
    private int showHeroStar_;
    /**
     * <pre>
     ** 阵容显示英雄星级（小星级） 
     * </pre>
     *
     * <code>int32 showHeroStar = 22;</code>
     */
    public int getShowHeroStar() {
      return showHeroStar_;
    }

    public static final int PRAISEDCOUNTMAP_FIELD_NUMBER = 23;
    private static final class PraisedCountMapDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Integer, java.lang.Long> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Integer, java.lang.Long>newDefaultInstance(
                  cn.daxiang.protocol.battle.BattleCommonProtocol.internal_static_ActorProfile_PraisedCountMapEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0,
                  com.google.protobuf.WireFormat.FieldType.INT64,
                  0L);
    }
    private com.google.protobuf.MapField<
        java.lang.Integer, java.lang.Long> praisedCountMap_;
    private com.google.protobuf.MapField<java.lang.Integer, java.lang.Long>
    internalGetPraisedCountMap() {
      if (praisedCountMap_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            PraisedCountMapDefaultEntryHolder.defaultEntry);
      }
      return praisedCountMap_;
    }

    public int getPraisedCountMapCount() {
      return internalGetPraisedCountMap().getMap().size();
    }
    /**
     * <pre>
     ** 被点赞数量 {rankType:praisedCount}
     * </pre>
     *
     * <code>map&lt;int32, int64&gt; praisedCountMap = 23;</code>
     */

    public boolean containsPraisedCountMap(
        int key) {
      
      return internalGetPraisedCountMap().getMap().containsKey(key);
    }
    /**
     * Use {@link #getPraisedCountMapMap()} instead.
     */
    @java.lang.Deprecated
    public java.util.Map<java.lang.Integer, java.lang.Long> getPraisedCountMap() {
      return getPraisedCountMapMap();
    }
    /**
     * <pre>
     ** 被点赞数量 {rankType:praisedCount}
     * </pre>
     *
     * <code>map&lt;int32, int64&gt; praisedCountMap = 23;</code>
     */

    public java.util.Map<java.lang.Integer, java.lang.Long> getPraisedCountMapMap() {
      return internalGetPraisedCountMap().getMap();
    }
    /**
     * <pre>
     ** 被点赞数量 {rankType:praisedCount}
     * </pre>
     *
     * <code>map&lt;int32, int64&gt; praisedCountMap = 23;</code>
     */

    public long getPraisedCountMapOrDefault(
        int key,
        long defaultValue) {
      
      java.util.Map<java.lang.Integer, java.lang.Long> map =
          internalGetPraisedCountMap().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <pre>
     ** 被点赞数量 {rankType:praisedCount}
     * </pre>
     *
     * <code>map&lt;int32, int64&gt; praisedCountMap = 23;</code>
     */

    public long getPraisedCountMapOrThrow(
        int key) {
      
      java.util.Map<java.lang.Integer, java.lang.Long> map =
          internalGetPraisedCountMap().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (serverId_ != 0) {
        output.writeInt32(1, serverId_);
      }
      if (actorId_ != 0L) {
        output.writeInt64(2, actorId_);
      }
      if (!getActorNameBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, actorName_);
      }
      if (sex_ != false) {
        output.writeBool(4, sex_);
      }
      if (level_ != 0) {
        output.writeInt32(5, level_);
      }
      if (vipLevel_ != 0) {
        output.writeInt32(6, vipLevel_);
      }
      if (avatarId_ != 0) {
        output.writeInt32(7, avatarId_);
      }
      if (power_ != 0L) {
        output.writeInt64(8, power_);
      }
      if (!getNationNameBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 9, nationName_);
      }
      if (showId_ != 0) {
        output.writeInt32(10, showId_);
      }
      if (currentServerId_ != 0) {
        output.writeInt32(11, currentServerId_);
      }
      if (onlineState_ != false) {
        output.writeBool(12, onlineState_);
      }
      if (lastLogoutTime_ != 0L) {
        output.writeInt64(13, lastLogoutTime_);
      }
      if (avatarFrameId_ != 0) {
        output.writeInt32(14, avatarFrameId_);
      }
      if (getCaveSingleChallengeListList().size() > 0) {
        output.writeUInt32NoTag(122);
        output.writeUInt32NoTag(caveSingleChallengeListMemoizedSerializedSize);
      }
      for (int i = 0; i < caveSingleChallengeList_.size(); i++) {
        output.writeInt32NoTag(caveSingleChallengeList_.get(i));
      }
      if (nationId_ != 0L) {
        output.writeInt64(16, nationId_);
      }
      if (official_ != 0) {
        output.writeInt32(17, official_);
      }
      if (getImmortalsSkinIdsList().size() > 0) {
        output.writeUInt32NoTag(146);
        output.writeUInt32NoTag(immortalsSkinIdsMemoizedSerializedSize);
      }
      for (int i = 0; i < immortalsSkinIds_.size(); i++) {
        output.writeInt32NoTag(immortalsSkinIds_.get(i));
      }
      if (titleId_ != 0) {
        output.writeInt32(19, titleId_);
      }
      if (!getSdkUserIdBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 20, sdkUserId_);
      }
      if (showImmortals_ != false) {
        output.writeBool(21, showImmortals_);
      }
      if (showHeroStar_ != 0) {
        output.writeInt32(22, showHeroStar_);
      }
      com.google.protobuf.GeneratedMessageV3
        .serializeIntegerMapTo(
          output,
          internalGetPraisedCountMap(),
          PraisedCountMapDefaultEntryHolder.defaultEntry,
          23);
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (serverId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, serverId_);
      }
      if (actorId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, actorId_);
      }
      if (!getActorNameBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, actorName_);
      }
      if (sex_ != false) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(4, sex_);
      }
      if (level_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(5, level_);
      }
      if (vipLevel_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(6, vipLevel_);
      }
      if (avatarId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(7, avatarId_);
      }
      if (power_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(8, power_);
      }
      if (!getNationNameBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(9, nationName_);
      }
      if (showId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(10, showId_);
      }
      if (currentServerId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(11, currentServerId_);
      }
      if (onlineState_ != false) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(12, onlineState_);
      }
      if (lastLogoutTime_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(13, lastLogoutTime_);
      }
      if (avatarFrameId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(14, avatarFrameId_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < caveSingleChallengeList_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt32SizeNoTag(caveSingleChallengeList_.get(i));
        }
        size += dataSize;
        if (!getCaveSingleChallengeListList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        caveSingleChallengeListMemoizedSerializedSize = dataSize;
      }
      if (nationId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(16, nationId_);
      }
      if (official_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(17, official_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < immortalsSkinIds_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt32SizeNoTag(immortalsSkinIds_.get(i));
        }
        size += dataSize;
        if (!getImmortalsSkinIdsList().isEmpty()) {
          size += 2;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        immortalsSkinIdsMemoizedSerializedSize = dataSize;
      }
      if (titleId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(19, titleId_);
      }
      if (!getSdkUserIdBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(20, sdkUserId_);
      }
      if (showImmortals_ != false) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(21, showImmortals_);
      }
      if (showHeroStar_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(22, showHeroStar_);
      }
      for (java.util.Map.Entry<java.lang.Integer, java.lang.Long> entry
           : internalGetPraisedCountMap().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Integer, java.lang.Long>
        praisedCountMap__ = PraisedCountMapDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(23, praisedCountMap__);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof cn.daxiang.protocol.battle.BattleCommonProtocol.ActorProfile)) {
        return super.equals(obj);
      }
      cn.daxiang.protocol.battle.BattleCommonProtocol.ActorProfile other = (cn.daxiang.protocol.battle.BattleCommonProtocol.ActorProfile) obj;

      boolean result = true;
      result = result && (getServerId()
          == other.getServerId());
      result = result && (getActorId()
          == other.getActorId());
      result = result && getActorName()
          .equals(other.getActorName());
      result = result && (getSex()
          == other.getSex());
      result = result && (getLevel()
          == other.getLevel());
      result = result && (getVipLevel()
          == other.getVipLevel());
      result = result && (getAvatarId()
          == other.getAvatarId());
      result = result && (getPower()
          == other.getPower());
      result = result && getNationName()
          .equals(other.getNationName());
      result = result && (getShowId()
          == other.getShowId());
      result = result && (getCurrentServerId()
          == other.getCurrentServerId());
      result = result && (getOnlineState()
          == other.getOnlineState());
      result = result && (getLastLogoutTime()
          == other.getLastLogoutTime());
      result = result && (getAvatarFrameId()
          == other.getAvatarFrameId());
      result = result && getCaveSingleChallengeListList()
          .equals(other.getCaveSingleChallengeListList());
      result = result && (getNationId()
          == other.getNationId());
      result = result && (getOfficial()
          == other.getOfficial());
      result = result && getImmortalsSkinIdsList()
          .equals(other.getImmortalsSkinIdsList());
      result = result && (getTitleId()
          == other.getTitleId());
      result = result && getSdkUserId()
          .equals(other.getSdkUserId());
      result = result && (getShowImmortals()
          == other.getShowImmortals());
      result = result && (getShowHeroStar()
          == other.getShowHeroStar());
      result = result && internalGetPraisedCountMap().equals(
          other.internalGetPraisedCountMap());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + SERVERID_FIELD_NUMBER;
      hash = (53 * hash) + getServerId();
      hash = (37 * hash) + ACTORID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getActorId());
      hash = (37 * hash) + ACTORNAME_FIELD_NUMBER;
      hash = (53 * hash) + getActorName().hashCode();
      hash = (37 * hash) + SEX_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getSex());
      hash = (37 * hash) + LEVEL_FIELD_NUMBER;
      hash = (53 * hash) + getLevel();
      hash = (37 * hash) + VIPLEVEL_FIELD_NUMBER;
      hash = (53 * hash) + getVipLevel();
      hash = (37 * hash) + AVATARID_FIELD_NUMBER;
      hash = (53 * hash) + getAvatarId();
      hash = (37 * hash) + POWER_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getPower());
      hash = (37 * hash) + NATIONNAME_FIELD_NUMBER;
      hash = (53 * hash) + getNationName().hashCode();
      hash = (37 * hash) + SHOWID_FIELD_NUMBER;
      hash = (53 * hash) + getShowId();
      hash = (37 * hash) + CURRENTSERVERID_FIELD_NUMBER;
      hash = (53 * hash) + getCurrentServerId();
      hash = (37 * hash) + ONLINESTATE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getOnlineState());
      hash = (37 * hash) + LASTLOGOUTTIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getLastLogoutTime());
      hash = (37 * hash) + AVATARFRAMEID_FIELD_NUMBER;
      hash = (53 * hash) + getAvatarFrameId();
      if (getCaveSingleChallengeListCount() > 0) {
        hash = (37 * hash) + CAVESINGLECHALLENGELIST_FIELD_NUMBER;
        hash = (53 * hash) + getCaveSingleChallengeListList().hashCode();
      }
      hash = (37 * hash) + NATIONID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getNationId());
      hash = (37 * hash) + OFFICIAL_FIELD_NUMBER;
      hash = (53 * hash) + getOfficial();
      if (getImmortalsSkinIdsCount() > 0) {
        hash = (37 * hash) + IMMORTALSSKINIDS_FIELD_NUMBER;
        hash = (53 * hash) + getImmortalsSkinIdsList().hashCode();
      }
      hash = (37 * hash) + TITLEID_FIELD_NUMBER;
      hash = (53 * hash) + getTitleId();
      hash = (37 * hash) + SDKUSERID_FIELD_NUMBER;
      hash = (53 * hash) + getSdkUserId().hashCode();
      hash = (37 * hash) + SHOWIMMORTALS_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getShowImmortals());
      hash = (37 * hash) + SHOWHEROSTAR_FIELD_NUMBER;
      hash = (53 * hash) + getShowHeroStar();
      if (!internalGetPraisedCountMap().getMap().isEmpty()) {
        hash = (37 * hash) + PRAISEDCOUNTMAP_FIELD_NUMBER;
        hash = (53 * hash) + internalGetPraisedCountMap().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static cn.daxiang.protocol.battle.BattleCommonProtocol.ActorProfile parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.battle.BattleCommonProtocol.ActorProfile parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.battle.BattleCommonProtocol.ActorProfile parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.battle.BattleCommonProtocol.ActorProfile parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.battle.BattleCommonProtocol.ActorProfile parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.battle.BattleCommonProtocol.ActorProfile parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.battle.BattleCommonProtocol.ActorProfile parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.battle.BattleCommonProtocol.ActorProfile parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.battle.BattleCommonProtocol.ActorProfile parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.battle.BattleCommonProtocol.ActorProfile parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.battle.BattleCommonProtocol.ActorProfile parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.battle.BattleCommonProtocol.ActorProfile parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(cn.daxiang.protocol.battle.BattleCommonProtocol.ActorProfile prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** 角色简介信息 
     * </pre>
     *
     * Protobuf type {@code ActorProfile}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:ActorProfile)
        cn.daxiang.protocol.battle.BattleCommonProtocol.ActorProfileOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.daxiang.protocol.battle.BattleCommonProtocol.internal_static_ActorProfile_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 23:
            return internalGetPraisedCountMap();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 23:
            return internalGetMutablePraisedCountMap();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.daxiang.protocol.battle.BattleCommonProtocol.internal_static_ActorProfile_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.daxiang.protocol.battle.BattleCommonProtocol.ActorProfile.class, cn.daxiang.protocol.battle.BattleCommonProtocol.ActorProfile.Builder.class);
      }

      // Construct using cn.daxiang.protocol.battle.BattleCommonProtocol.ActorProfile.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        serverId_ = 0;

        actorId_ = 0L;

        actorName_ = "";

        sex_ = false;

        level_ = 0;

        vipLevel_ = 0;

        avatarId_ = 0;

        power_ = 0L;

        nationName_ = "";

        showId_ = 0;

        currentServerId_ = 0;

        onlineState_ = false;

        lastLogoutTime_ = 0L;

        avatarFrameId_ = 0;

        caveSingleChallengeList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00004000);
        nationId_ = 0L;

        official_ = 0;

        immortalsSkinIds_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00020000);
        titleId_ = 0;

        sdkUserId_ = "";

        showImmortals_ = false;

        showHeroStar_ = 0;

        internalGetMutablePraisedCountMap().clear();
        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return cn.daxiang.protocol.battle.BattleCommonProtocol.internal_static_ActorProfile_descriptor;
      }

      public cn.daxiang.protocol.battle.BattleCommonProtocol.ActorProfile getDefaultInstanceForType() {
        return cn.daxiang.protocol.battle.BattleCommonProtocol.ActorProfile.getDefaultInstance();
      }

      public cn.daxiang.protocol.battle.BattleCommonProtocol.ActorProfile build() {
        cn.daxiang.protocol.battle.BattleCommonProtocol.ActorProfile result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public cn.daxiang.protocol.battle.BattleCommonProtocol.ActorProfile buildPartial() {
        cn.daxiang.protocol.battle.BattleCommonProtocol.ActorProfile result = new cn.daxiang.protocol.battle.BattleCommonProtocol.ActorProfile(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        result.serverId_ = serverId_;
        result.actorId_ = actorId_;
        result.actorName_ = actorName_;
        result.sex_ = sex_;
        result.level_ = level_;
        result.vipLevel_ = vipLevel_;
        result.avatarId_ = avatarId_;
        result.power_ = power_;
        result.nationName_ = nationName_;
        result.showId_ = showId_;
        result.currentServerId_ = currentServerId_;
        result.onlineState_ = onlineState_;
        result.lastLogoutTime_ = lastLogoutTime_;
        result.avatarFrameId_ = avatarFrameId_;
        if (((bitField0_ & 0x00004000) == 0x00004000)) {
          caveSingleChallengeList_ = java.util.Collections.unmodifiableList(caveSingleChallengeList_);
          bitField0_ = (bitField0_ & ~0x00004000);
        }
        result.caveSingleChallengeList_ = caveSingleChallengeList_;
        result.nationId_ = nationId_;
        result.official_ = official_;
        if (((bitField0_ & 0x00020000) == 0x00020000)) {
          immortalsSkinIds_ = java.util.Collections.unmodifiableList(immortalsSkinIds_);
          bitField0_ = (bitField0_ & ~0x00020000);
        }
        result.immortalsSkinIds_ = immortalsSkinIds_;
        result.titleId_ = titleId_;
        result.sdkUserId_ = sdkUserId_;
        result.showImmortals_ = showImmortals_;
        result.showHeroStar_ = showHeroStar_;
        result.praisedCountMap_ = internalGetPraisedCountMap();
        result.praisedCountMap_.makeImmutable();
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof cn.daxiang.protocol.battle.BattleCommonProtocol.ActorProfile) {
          return mergeFrom((cn.daxiang.protocol.battle.BattleCommonProtocol.ActorProfile)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(cn.daxiang.protocol.battle.BattleCommonProtocol.ActorProfile other) {
        if (other == cn.daxiang.protocol.battle.BattleCommonProtocol.ActorProfile.getDefaultInstance()) return this;
        if (other.getServerId() != 0) {
          setServerId(other.getServerId());
        }
        if (other.getActorId() != 0L) {
          setActorId(other.getActorId());
        }
        if (!other.getActorName().isEmpty()) {
          actorName_ = other.actorName_;
          onChanged();
        }
        if (other.getSex() != false) {
          setSex(other.getSex());
        }
        if (other.getLevel() != 0) {
          setLevel(other.getLevel());
        }
        if (other.getVipLevel() != 0) {
          setVipLevel(other.getVipLevel());
        }
        if (other.getAvatarId() != 0) {
          setAvatarId(other.getAvatarId());
        }
        if (other.getPower() != 0L) {
          setPower(other.getPower());
        }
        if (!other.getNationName().isEmpty()) {
          nationName_ = other.nationName_;
          onChanged();
        }
        if (other.getShowId() != 0) {
          setShowId(other.getShowId());
        }
        if (other.getCurrentServerId() != 0) {
          setCurrentServerId(other.getCurrentServerId());
        }
        if (other.getOnlineState() != false) {
          setOnlineState(other.getOnlineState());
        }
        if (other.getLastLogoutTime() != 0L) {
          setLastLogoutTime(other.getLastLogoutTime());
        }
        if (other.getAvatarFrameId() != 0) {
          setAvatarFrameId(other.getAvatarFrameId());
        }
        if (!other.caveSingleChallengeList_.isEmpty()) {
          if (caveSingleChallengeList_.isEmpty()) {
            caveSingleChallengeList_ = other.caveSingleChallengeList_;
            bitField0_ = (bitField0_ & ~0x00004000);
          } else {
            ensureCaveSingleChallengeListIsMutable();
            caveSingleChallengeList_.addAll(other.caveSingleChallengeList_);
          }
          onChanged();
        }
        if (other.getNationId() != 0L) {
          setNationId(other.getNationId());
        }
        if (other.getOfficial() != 0) {
          setOfficial(other.getOfficial());
        }
        if (!other.immortalsSkinIds_.isEmpty()) {
          if (immortalsSkinIds_.isEmpty()) {
            immortalsSkinIds_ = other.immortalsSkinIds_;
            bitField0_ = (bitField0_ & ~0x00020000);
          } else {
            ensureImmortalsSkinIdsIsMutable();
            immortalsSkinIds_.addAll(other.immortalsSkinIds_);
          }
          onChanged();
        }
        if (other.getTitleId() != 0) {
          setTitleId(other.getTitleId());
        }
        if (!other.getSdkUserId().isEmpty()) {
          sdkUserId_ = other.sdkUserId_;
          onChanged();
        }
        if (other.getShowImmortals() != false) {
          setShowImmortals(other.getShowImmortals());
        }
        if (other.getShowHeroStar() != 0) {
          setShowHeroStar(other.getShowHeroStar());
        }
        internalGetMutablePraisedCountMap().mergeFrom(
            other.internalGetPraisedCountMap());
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        cn.daxiang.protocol.battle.BattleCommonProtocol.ActorProfile parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (cn.daxiang.protocol.battle.BattleCommonProtocol.ActorProfile) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int serverId_ ;
      /**
       * <pre>
       ** 服务器ID 
       * </pre>
       *
       * <code>int32 serverId = 1;</code>
       */
      public int getServerId() {
        return serverId_;
      }
      /**
       * <pre>
       ** 服务器ID 
       * </pre>
       *
       * <code>int32 serverId = 1;</code>
       */
      public Builder setServerId(int value) {
        
        serverId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 服务器ID 
       * </pre>
       *
       * <code>int32 serverId = 1;</code>
       */
      public Builder clearServerId() {
        
        serverId_ = 0;
        onChanged();
        return this;
      }

      private long actorId_ ;
      /**
       * <pre>
       ** 角色ID 
       * </pre>
       *
       * <code>int64 actorId = 2;</code>
       */
      public long getActorId() {
        return actorId_;
      }
      /**
       * <pre>
       ** 角色ID 
       * </pre>
       *
       * <code>int64 actorId = 2;</code>
       */
      public Builder setActorId(long value) {
        
        actorId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 角色ID 
       * </pre>
       *
       * <code>int64 actorId = 2;</code>
       */
      public Builder clearActorId() {
        
        actorId_ = 0L;
        onChanged();
        return this;
      }

      private java.lang.Object actorName_ = "";
      /**
       * <pre>
       ** 角色名 
       * </pre>
       *
       * <code>string actorName = 3;</code>
       */
      public java.lang.String getActorName() {
        java.lang.Object ref = actorName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          actorName_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       ** 角色名 
       * </pre>
       *
       * <code>string actorName = 3;</code>
       */
      public com.google.protobuf.ByteString
          getActorNameBytes() {
        java.lang.Object ref = actorName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          actorName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       ** 角色名 
       * </pre>
       *
       * <code>string actorName = 3;</code>
       */
      public Builder setActorName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        actorName_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 角色名 
       * </pre>
       *
       * <code>string actorName = 3;</code>
       */
      public Builder clearActorName() {
        
        actorName_ = getDefaultInstance().getActorName();
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 角色名 
       * </pre>
       *
       * <code>string actorName = 3;</code>
       */
      public Builder setActorNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        actorName_ = value;
        onChanged();
        return this;
      }

      private boolean sex_ ;
      /**
       * <pre>
       ** 性别 
       * </pre>
       *
       * <code>bool sex = 4;</code>
       */
      public boolean getSex() {
        return sex_;
      }
      /**
       * <pre>
       ** 性别 
       * </pre>
       *
       * <code>bool sex = 4;</code>
       */
      public Builder setSex(boolean value) {
        
        sex_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 性别 
       * </pre>
       *
       * <code>bool sex = 4;</code>
       */
      public Builder clearSex() {
        
        sex_ = false;
        onChanged();
        return this;
      }

      private int level_ ;
      /**
       * <pre>
       ** 等级 
       * </pre>
       *
       * <code>int32 level = 5;</code>
       */
      public int getLevel() {
        return level_;
      }
      /**
       * <pre>
       ** 等级 
       * </pre>
       *
       * <code>int32 level = 5;</code>
       */
      public Builder setLevel(int value) {
        
        level_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 等级 
       * </pre>
       *
       * <code>int32 level = 5;</code>
       */
      public Builder clearLevel() {
        
        level_ = 0;
        onChanged();
        return this;
      }

      private int vipLevel_ ;
      /**
       * <pre>
       ** vip等级 
       * </pre>
       *
       * <code>int32 vipLevel = 6;</code>
       */
      public int getVipLevel() {
        return vipLevel_;
      }
      /**
       * <pre>
       ** vip等级 
       * </pre>
       *
       * <code>int32 vipLevel = 6;</code>
       */
      public Builder setVipLevel(int value) {
        
        vipLevel_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** vip等级 
       * </pre>
       *
       * <code>int32 vipLevel = 6;</code>
       */
      public Builder clearVipLevel() {
        
        vipLevel_ = 0;
        onChanged();
        return this;
      }

      private int avatarId_ ;
      /**
       * <pre>
       ** 头像ID 
       * </pre>
       *
       * <code>int32 avatarId = 7;</code>
       */
      public int getAvatarId() {
        return avatarId_;
      }
      /**
       * <pre>
       ** 头像ID 
       * </pre>
       *
       * <code>int32 avatarId = 7;</code>
       */
      public Builder setAvatarId(int value) {
        
        avatarId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 头像ID 
       * </pre>
       *
       * <code>int32 avatarId = 7;</code>
       */
      public Builder clearAvatarId() {
        
        avatarId_ = 0;
        onChanged();
        return this;
      }

      private long power_ ;
      /**
       * <pre>
       ** 战力 
       * </pre>
       *
       * <code>int64 power = 8;</code>
       */
      public long getPower() {
        return power_;
      }
      /**
       * <pre>
       ** 战力 
       * </pre>
       *
       * <code>int64 power = 8;</code>
       */
      public Builder setPower(long value) {
        
        power_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 战力 
       * </pre>
       *
       * <code>int64 power = 8;</code>
       */
      public Builder clearPower() {
        
        power_ = 0L;
        onChanged();
        return this;
      }

      private java.lang.Object nationName_ = "";
      /**
       * <pre>
       ** 国家名称 
       * </pre>
       *
       * <code>string nationName = 9;</code>
       */
      public java.lang.String getNationName() {
        java.lang.Object ref = nationName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          nationName_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       ** 国家名称 
       * </pre>
       *
       * <code>string nationName = 9;</code>
       */
      public com.google.protobuf.ByteString
          getNationNameBytes() {
        java.lang.Object ref = nationName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          nationName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       ** 国家名称 
       * </pre>
       *
       * <code>string nationName = 9;</code>
       */
      public Builder setNationName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        nationName_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 国家名称 
       * </pre>
       *
       * <code>string nationName = 9;</code>
       */
      public Builder clearNationName() {
        
        nationName_ = getDefaultInstance().getNationName();
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 国家名称 
       * </pre>
       *
       * <code>string nationName = 9;</code>
       */
      public Builder setNationNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        nationName_ = value;
        onChanged();
        return this;
      }

      private int showId_ ;
      /**
       * <pre>
       ** 阵容显示英雄ID 
       * </pre>
       *
       * <code>int32 showId = 10;</code>
       */
      public int getShowId() {
        return showId_;
      }
      /**
       * <pre>
       ** 阵容显示英雄ID 
       * </pre>
       *
       * <code>int32 showId = 10;</code>
       */
      public Builder setShowId(int value) {
        
        showId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 阵容显示英雄ID 
       * </pre>
       *
       * <code>int32 showId = 10;</code>
       */
      public Builder clearShowId() {
        
        showId_ = 0;
        onChanged();
        return this;
      }

      private int currentServerId_ ;
      /**
       * <pre>
       ** 当前服务器ID 
       * </pre>
       *
       * <code>int32 currentServerId = 11;</code>
       */
      public int getCurrentServerId() {
        return currentServerId_;
      }
      /**
       * <pre>
       ** 当前服务器ID 
       * </pre>
       *
       * <code>int32 currentServerId = 11;</code>
       */
      public Builder setCurrentServerId(int value) {
        
        currentServerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 当前服务器ID 
       * </pre>
       *
       * <code>int32 currentServerId = 11;</code>
       */
      public Builder clearCurrentServerId() {
        
        currentServerId_ = 0;
        onChanged();
        return this;
      }

      private boolean onlineState_ ;
      /**
       * <pre>
       **在线状态
       * </pre>
       *
       * <code>bool onlineState = 12;</code>
       */
      public boolean getOnlineState() {
        return onlineState_;
      }
      /**
       * <pre>
       **在线状态
       * </pre>
       *
       * <code>bool onlineState = 12;</code>
       */
      public Builder setOnlineState(boolean value) {
        
        onlineState_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       **在线状态
       * </pre>
       *
       * <code>bool onlineState = 12;</code>
       */
      public Builder clearOnlineState() {
        
        onlineState_ = false;
        onChanged();
        return this;
      }

      private long lastLogoutTime_ ;
      /**
       * <pre>
       **最后一次登录时间
       * </pre>
       *
       * <code>int64 lastLogoutTime = 13;</code>
       */
      public long getLastLogoutTime() {
        return lastLogoutTime_;
      }
      /**
       * <pre>
       **最后一次登录时间
       * </pre>
       *
       * <code>int64 lastLogoutTime = 13;</code>
       */
      public Builder setLastLogoutTime(long value) {
        
        lastLogoutTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       **最后一次登录时间
       * </pre>
       *
       * <code>int64 lastLogoutTime = 13;</code>
       */
      public Builder clearLastLogoutTime() {
        
        lastLogoutTime_ = 0L;
        onChanged();
        return this;
      }

      private int avatarFrameId_ ;
      /**
       * <pre>
       ** 头像框ID 
       * </pre>
       *
       * <code>int32 avatarFrameId = 14;</code>
       */
      public int getAvatarFrameId() {
        return avatarFrameId_;
      }
      /**
       * <pre>
       ** 头像框ID 
       * </pre>
       *
       * <code>int32 avatarFrameId = 14;</code>
       */
      public Builder setAvatarFrameId(int value) {
        
        avatarFrameId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 头像框ID 
       * </pre>
       *
       * <code>int32 avatarFrameId = 14;</code>
       */
      public Builder clearAvatarFrameId() {
        
        avatarFrameId_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<java.lang.Integer> caveSingleChallengeList_ = java.util.Collections.emptyList();
      private void ensureCaveSingleChallengeListIsMutable() {
        if (!((bitField0_ & 0x00004000) == 0x00004000)) {
          caveSingleChallengeList_ = new java.util.ArrayList<java.lang.Integer>(caveSingleChallengeList_);
          bitField0_ |= 0x00004000;
         }
      }
      /**
       * <pre>
       ** 魔窟解锁状态列表
       * </pre>
       *
       * <code>repeated int32 caveSingleChallengeList = 15;</code>
       */
      public java.util.List<java.lang.Integer>
          getCaveSingleChallengeListList() {
        return java.util.Collections.unmodifiableList(caveSingleChallengeList_);
      }
      /**
       * <pre>
       ** 魔窟解锁状态列表
       * </pre>
       *
       * <code>repeated int32 caveSingleChallengeList = 15;</code>
       */
      public int getCaveSingleChallengeListCount() {
        return caveSingleChallengeList_.size();
      }
      /**
       * <pre>
       ** 魔窟解锁状态列表
       * </pre>
       *
       * <code>repeated int32 caveSingleChallengeList = 15;</code>
       */
      public int getCaveSingleChallengeList(int index) {
        return caveSingleChallengeList_.get(index);
      }
      /**
       * <pre>
       ** 魔窟解锁状态列表
       * </pre>
       *
       * <code>repeated int32 caveSingleChallengeList = 15;</code>
       */
      public Builder setCaveSingleChallengeList(
          int index, int value) {
        ensureCaveSingleChallengeListIsMutable();
        caveSingleChallengeList_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 魔窟解锁状态列表
       * </pre>
       *
       * <code>repeated int32 caveSingleChallengeList = 15;</code>
       */
      public Builder addCaveSingleChallengeList(int value) {
        ensureCaveSingleChallengeListIsMutable();
        caveSingleChallengeList_.add(value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 魔窟解锁状态列表
       * </pre>
       *
       * <code>repeated int32 caveSingleChallengeList = 15;</code>
       */
      public Builder addAllCaveSingleChallengeList(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureCaveSingleChallengeListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, caveSingleChallengeList_);
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 魔窟解锁状态列表
       * </pre>
       *
       * <code>repeated int32 caveSingleChallengeList = 15;</code>
       */
      public Builder clearCaveSingleChallengeList() {
        caveSingleChallengeList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00004000);
        onChanged();
        return this;
      }

      private long nationId_ ;
      /**
       * <pre>
       ** 国家ID 
       * </pre>
       *
       * <code>int64 nationId = 16;</code>
       */
      public long getNationId() {
        return nationId_;
      }
      /**
       * <pre>
       ** 国家ID 
       * </pre>
       *
       * <code>int64 nationId = 16;</code>
       */
      public Builder setNationId(long value) {
        
        nationId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 国家ID 
       * </pre>
       *
       * <code>int64 nationId = 16;</code>
       */
      public Builder clearNationId() {
        
        nationId_ = 0L;
        onChanged();
        return this;
      }

      private int official_ ;
      /**
       * <pre>
       ** 官职 
       * </pre>
       *
       * <code>int32 official = 17;</code>
       */
      public int getOfficial() {
        return official_;
      }
      /**
       * <pre>
       ** 官职 
       * </pre>
       *
       * <code>int32 official = 17;</code>
       */
      public Builder setOfficial(int value) {
        
        official_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 官职 
       * </pre>
       *
       * <code>int32 official = 17;</code>
       */
      public Builder clearOfficial() {
        
        official_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<java.lang.Integer> immortalsSkinIds_ = java.util.Collections.emptyList();
      private void ensureImmortalsSkinIdsIsMutable() {
        if (!((bitField0_ & 0x00020000) == 0x00020000)) {
          immortalsSkinIds_ = new java.util.ArrayList<java.lang.Integer>(immortalsSkinIds_);
          bitField0_ |= 0x00020000;
         }
      }
      /**
       * <pre>
       ** 已激活专武皮肤Id 
       * </pre>
       *
       * <code>repeated int32 immortalsSkinIds = 18;</code>
       */
      public java.util.List<java.lang.Integer>
          getImmortalsSkinIdsList() {
        return java.util.Collections.unmodifiableList(immortalsSkinIds_);
      }
      /**
       * <pre>
       ** 已激活专武皮肤Id 
       * </pre>
       *
       * <code>repeated int32 immortalsSkinIds = 18;</code>
       */
      public int getImmortalsSkinIdsCount() {
        return immortalsSkinIds_.size();
      }
      /**
       * <pre>
       ** 已激活专武皮肤Id 
       * </pre>
       *
       * <code>repeated int32 immortalsSkinIds = 18;</code>
       */
      public int getImmortalsSkinIds(int index) {
        return immortalsSkinIds_.get(index);
      }
      /**
       * <pre>
       ** 已激活专武皮肤Id 
       * </pre>
       *
       * <code>repeated int32 immortalsSkinIds = 18;</code>
       */
      public Builder setImmortalsSkinIds(
          int index, int value) {
        ensureImmortalsSkinIdsIsMutable();
        immortalsSkinIds_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 已激活专武皮肤Id 
       * </pre>
       *
       * <code>repeated int32 immortalsSkinIds = 18;</code>
       */
      public Builder addImmortalsSkinIds(int value) {
        ensureImmortalsSkinIdsIsMutable();
        immortalsSkinIds_.add(value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 已激活专武皮肤Id 
       * </pre>
       *
       * <code>repeated int32 immortalsSkinIds = 18;</code>
       */
      public Builder addAllImmortalsSkinIds(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureImmortalsSkinIdsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, immortalsSkinIds_);
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 已激活专武皮肤Id 
       * </pre>
       *
       * <code>repeated int32 immortalsSkinIds = 18;</code>
       */
      public Builder clearImmortalsSkinIds() {
        immortalsSkinIds_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00020000);
        onChanged();
        return this;
      }

      private int titleId_ ;
      /**
       * <pre>
       ** 称号 
       * </pre>
       *
       * <code>int32 titleId = 19;</code>
       */
      public int getTitleId() {
        return titleId_;
      }
      /**
       * <pre>
       ** 称号 
       * </pre>
       *
       * <code>int32 titleId = 19;</code>
       */
      public Builder setTitleId(int value) {
        
        titleId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 称号 
       * </pre>
       *
       * <code>int32 titleId = 19;</code>
       */
      public Builder clearTitleId() {
        
        titleId_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object sdkUserId_ = "";
      /**
       * <pre>
       ** 第三方渠道用户ID 
       * </pre>
       *
       * <code>string sdkUserId = 20;</code>
       */
      public java.lang.String getSdkUserId() {
        java.lang.Object ref = sdkUserId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          sdkUserId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       ** 第三方渠道用户ID 
       * </pre>
       *
       * <code>string sdkUserId = 20;</code>
       */
      public com.google.protobuf.ByteString
          getSdkUserIdBytes() {
        java.lang.Object ref = sdkUserId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          sdkUserId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       ** 第三方渠道用户ID 
       * </pre>
       *
       * <code>string sdkUserId = 20;</code>
       */
      public Builder setSdkUserId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        sdkUserId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 第三方渠道用户ID 
       * </pre>
       *
       * <code>string sdkUserId = 20;</code>
       */
      public Builder clearSdkUserId() {
        
        sdkUserId_ = getDefaultInstance().getSdkUserId();
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 第三方渠道用户ID 
       * </pre>
       *
       * <code>string sdkUserId = 20;</code>
       */
      public Builder setSdkUserIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        sdkUserId_ = value;
        onChanged();
        return this;
      }

      private boolean showImmortals_ ;
      /**
       * <pre>
       ** 阵容显示英雄是否有专武 
       * </pre>
       *
       * <code>bool showImmortals = 21;</code>
       */
      public boolean getShowImmortals() {
        return showImmortals_;
      }
      /**
       * <pre>
       ** 阵容显示英雄是否有专武 
       * </pre>
       *
       * <code>bool showImmortals = 21;</code>
       */
      public Builder setShowImmortals(boolean value) {
        
        showImmortals_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 阵容显示英雄是否有专武 
       * </pre>
       *
       * <code>bool showImmortals = 21;</code>
       */
      public Builder clearShowImmortals() {
        
        showImmortals_ = false;
        onChanged();
        return this;
      }

      private int showHeroStar_ ;
      /**
       * <pre>
       ** 阵容显示英雄星级（小星级） 
       * </pre>
       *
       * <code>int32 showHeroStar = 22;</code>
       */
      public int getShowHeroStar() {
        return showHeroStar_;
      }
      /**
       * <pre>
       ** 阵容显示英雄星级（小星级） 
       * </pre>
       *
       * <code>int32 showHeroStar = 22;</code>
       */
      public Builder setShowHeroStar(int value) {
        
        showHeroStar_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 阵容显示英雄星级（小星级） 
       * </pre>
       *
       * <code>int32 showHeroStar = 22;</code>
       */
      public Builder clearShowHeroStar() {
        
        showHeroStar_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.MapField<
          java.lang.Integer, java.lang.Long> praisedCountMap_;
      private com.google.protobuf.MapField<java.lang.Integer, java.lang.Long>
      internalGetPraisedCountMap() {
        if (praisedCountMap_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              PraisedCountMapDefaultEntryHolder.defaultEntry);
        }
        return praisedCountMap_;
      }
      private com.google.protobuf.MapField<java.lang.Integer, java.lang.Long>
      internalGetMutablePraisedCountMap() {
        onChanged();;
        if (praisedCountMap_ == null) {
          praisedCountMap_ = com.google.protobuf.MapField.newMapField(
              PraisedCountMapDefaultEntryHolder.defaultEntry);
        }
        if (!praisedCountMap_.isMutable()) {
          praisedCountMap_ = praisedCountMap_.copy();
        }
        return praisedCountMap_;
      }

      public int getPraisedCountMapCount() {
        return internalGetPraisedCountMap().getMap().size();
      }
      /**
       * <pre>
       ** 被点赞数量 {rankType:praisedCount}
       * </pre>
       *
       * <code>map&lt;int32, int64&gt; praisedCountMap = 23;</code>
       */

      public boolean containsPraisedCountMap(
          int key) {
        
        return internalGetPraisedCountMap().getMap().containsKey(key);
      }
      /**
       * Use {@link #getPraisedCountMapMap()} instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, java.lang.Long> getPraisedCountMap() {
        return getPraisedCountMapMap();
      }
      /**
       * <pre>
       ** 被点赞数量 {rankType:praisedCount}
       * </pre>
       *
       * <code>map&lt;int32, int64&gt; praisedCountMap = 23;</code>
       */

      public java.util.Map<java.lang.Integer, java.lang.Long> getPraisedCountMapMap() {
        return internalGetPraisedCountMap().getMap();
      }
      /**
       * <pre>
       ** 被点赞数量 {rankType:praisedCount}
       * </pre>
       *
       * <code>map&lt;int32, int64&gt; praisedCountMap = 23;</code>
       */

      public long getPraisedCountMapOrDefault(
          int key,
          long defaultValue) {
        
        java.util.Map<java.lang.Integer, java.lang.Long> map =
            internalGetPraisedCountMap().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <pre>
       ** 被点赞数量 {rankType:praisedCount}
       * </pre>
       *
       * <code>map&lt;int32, int64&gt; praisedCountMap = 23;</code>
       */

      public long getPraisedCountMapOrThrow(
          int key) {
        
        java.util.Map<java.lang.Integer, java.lang.Long> map =
            internalGetPraisedCountMap().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearPraisedCountMap() {
        internalGetMutablePraisedCountMap().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <pre>
       ** 被点赞数量 {rankType:praisedCount}
       * </pre>
       *
       * <code>map&lt;int32, int64&gt; praisedCountMap = 23;</code>
       */

      public Builder removePraisedCountMap(
          int key) {
        
        internalGetMutablePraisedCountMap().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, java.lang.Long>
      getMutablePraisedCountMap() {
        return internalGetMutablePraisedCountMap().getMutableMap();
      }
      /**
       * <pre>
       ** 被点赞数量 {rankType:praisedCount}
       * </pre>
       *
       * <code>map&lt;int32, int64&gt; praisedCountMap = 23;</code>
       */
      public Builder putPraisedCountMap(
          int key,
          long value) {
        
        
        internalGetMutablePraisedCountMap().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <pre>
       ** 被点赞数量 {rankType:praisedCount}
       * </pre>
       *
       * <code>map&lt;int32, int64&gt; praisedCountMap = 23;</code>
       */

      public Builder putAllPraisedCountMap(
          java.util.Map<java.lang.Integer, java.lang.Long> values) {
        internalGetMutablePraisedCountMap().getMutableMap()
            .putAll(values);
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:ActorProfile)
    }

    // @@protoc_insertion_point(class_scope:ActorProfile)
    private static final cn.daxiang.protocol.battle.BattleCommonProtocol.ActorProfile DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new cn.daxiang.protocol.battle.BattleCommonProtocol.ActorProfile();
    }

    public static cn.daxiang.protocol.battle.BattleCommonProtocol.ActorProfile getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ActorProfile>
        PARSER = new com.google.protobuf.AbstractParser<ActorProfile>() {
      public ActorProfile parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ActorProfile(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ActorProfile> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ActorProfile> getParserForType() {
      return PARSER;
    }

    public cn.daxiang.protocol.battle.BattleCommonProtocol.ActorProfile getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface MsgOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Msg)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     ** message名字 
     * </pre>
     *
     * <code>string name = 1;</code>
     */
    java.lang.String getName();
    /**
     * <pre>
     ** message名字 
     * </pre>
     *
     * <code>string name = 1;</code>
     */
    com.google.protobuf.ByteString
        getNameBytes();

    /**
     * <pre>
     ** 值 
     * </pre>
     *
     * <code>bytes value = 2;</code>
     */
    com.google.protobuf.ByteString getValue();
  }
  /**
   * <pre>
   ** 自定义对象 
   * </pre>
   *
   * Protobuf type {@code Msg}
   */
  public  static final class Msg extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Msg)
      MsgOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Msg.newBuilder() to construct.
    private Msg(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Msg() {
      name_ = "";
      value_ = com.google.protobuf.ByteString.EMPTY;
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Msg(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              java.lang.String s = input.readStringRequireUtf8();

              name_ = s;
              break;
            }
            case 18: {

              value_ = input.readBytes();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.daxiang.protocol.battle.BattleCommonProtocol.internal_static_Msg_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.daxiang.protocol.battle.BattleCommonProtocol.internal_static_Msg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.daxiang.protocol.battle.BattleCommonProtocol.Msg.class, cn.daxiang.protocol.battle.BattleCommonProtocol.Msg.Builder.class);
    }

    public static final int NAME_FIELD_NUMBER = 1;
    private volatile java.lang.Object name_;
    /**
     * <pre>
     ** message名字 
     * </pre>
     *
     * <code>string name = 1;</code>
     */
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        name_ = s;
        return s;
      }
    }
    /**
     * <pre>
     ** message名字 
     * </pre>
     *
     * <code>string name = 1;</code>
     */
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int VALUE_FIELD_NUMBER = 2;
    private com.google.protobuf.ByteString value_;
    /**
     * <pre>
     ** 值 
     * </pre>
     *
     * <code>bytes value = 2;</code>
     */
    public com.google.protobuf.ByteString getValue() {
      return value_;
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!getNameBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, name_);
      }
      if (!value_.isEmpty()) {
        output.writeBytes(2, value_);
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!getNameBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, name_);
      }
      if (!value_.isEmpty()) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(2, value_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof cn.daxiang.protocol.battle.BattleCommonProtocol.Msg)) {
        return super.equals(obj);
      }
      cn.daxiang.protocol.battle.BattleCommonProtocol.Msg other = (cn.daxiang.protocol.battle.BattleCommonProtocol.Msg) obj;

      boolean result = true;
      result = result && getName()
          .equals(other.getName());
      result = result && getValue()
          .equals(other.getValue());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + NAME_FIELD_NUMBER;
      hash = (53 * hash) + getName().hashCode();
      hash = (37 * hash) + VALUE_FIELD_NUMBER;
      hash = (53 * hash) + getValue().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static cn.daxiang.protocol.battle.BattleCommonProtocol.Msg parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.battle.BattleCommonProtocol.Msg parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.battle.BattleCommonProtocol.Msg parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.battle.BattleCommonProtocol.Msg parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.battle.BattleCommonProtocol.Msg parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.battle.BattleCommonProtocol.Msg parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.battle.BattleCommonProtocol.Msg parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.battle.BattleCommonProtocol.Msg parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.battle.BattleCommonProtocol.Msg parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.battle.BattleCommonProtocol.Msg parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.battle.BattleCommonProtocol.Msg parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.battle.BattleCommonProtocol.Msg parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(cn.daxiang.protocol.battle.BattleCommonProtocol.Msg prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** 自定义对象 
     * </pre>
     *
     * Protobuf type {@code Msg}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Msg)
        cn.daxiang.protocol.battle.BattleCommonProtocol.MsgOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.daxiang.protocol.battle.BattleCommonProtocol.internal_static_Msg_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.daxiang.protocol.battle.BattleCommonProtocol.internal_static_Msg_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.daxiang.protocol.battle.BattleCommonProtocol.Msg.class, cn.daxiang.protocol.battle.BattleCommonProtocol.Msg.Builder.class);
      }

      // Construct using cn.daxiang.protocol.battle.BattleCommonProtocol.Msg.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        name_ = "";

        value_ = com.google.protobuf.ByteString.EMPTY;

        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return cn.daxiang.protocol.battle.BattleCommonProtocol.internal_static_Msg_descriptor;
      }

      public cn.daxiang.protocol.battle.BattleCommonProtocol.Msg getDefaultInstanceForType() {
        return cn.daxiang.protocol.battle.BattleCommonProtocol.Msg.getDefaultInstance();
      }

      public cn.daxiang.protocol.battle.BattleCommonProtocol.Msg build() {
        cn.daxiang.protocol.battle.BattleCommonProtocol.Msg result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public cn.daxiang.protocol.battle.BattleCommonProtocol.Msg buildPartial() {
        cn.daxiang.protocol.battle.BattleCommonProtocol.Msg result = new cn.daxiang.protocol.battle.BattleCommonProtocol.Msg(this);
        result.name_ = name_;
        result.value_ = value_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof cn.daxiang.protocol.battle.BattleCommonProtocol.Msg) {
          return mergeFrom((cn.daxiang.protocol.battle.BattleCommonProtocol.Msg)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(cn.daxiang.protocol.battle.BattleCommonProtocol.Msg other) {
        if (other == cn.daxiang.protocol.battle.BattleCommonProtocol.Msg.getDefaultInstance()) return this;
        if (!other.getName().isEmpty()) {
          name_ = other.name_;
          onChanged();
        }
        if (other.getValue() != com.google.protobuf.ByteString.EMPTY) {
          setValue(other.getValue());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        cn.daxiang.protocol.battle.BattleCommonProtocol.Msg parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (cn.daxiang.protocol.battle.BattleCommonProtocol.Msg) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private java.lang.Object name_ = "";
      /**
       * <pre>
       ** message名字 
       * </pre>
       *
       * <code>string name = 1;</code>
       */
      public java.lang.String getName() {
        java.lang.Object ref = name_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          name_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       ** message名字 
       * </pre>
       *
       * <code>string name = 1;</code>
       */
      public com.google.protobuf.ByteString
          getNameBytes() {
        java.lang.Object ref = name_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          name_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       ** message名字 
       * </pre>
       *
       * <code>string name = 1;</code>
       */
      public Builder setName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        name_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** message名字 
       * </pre>
       *
       * <code>string name = 1;</code>
       */
      public Builder clearName() {
        
        name_ = getDefaultInstance().getName();
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** message名字 
       * </pre>
       *
       * <code>string name = 1;</code>
       */
      public Builder setNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        name_ = value;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString value_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <pre>
       ** 值 
       * </pre>
       *
       * <code>bytes value = 2;</code>
       */
      public com.google.protobuf.ByteString getValue() {
        return value_;
      }
      /**
       * <pre>
       ** 值 
       * </pre>
       *
       * <code>bytes value = 2;</code>
       */
      public Builder setValue(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        value_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 值 
       * </pre>
       *
       * <code>bytes value = 2;</code>
       */
      public Builder clearValue() {
        
        value_ = getDefaultInstance().getValue();
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Msg)
    }

    // @@protoc_insertion_point(class_scope:Msg)
    private static final cn.daxiang.protocol.battle.BattleCommonProtocol.Msg DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new cn.daxiang.protocol.battle.BattleCommonProtocol.Msg();
    }

    public static cn.daxiang.protocol.battle.BattleCommonProtocol.Msg getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<Msg>
        PARSER = new com.google.protobuf.AbstractParser<Msg>() {
      public Msg parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Msg(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Msg> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Msg> getParserForType() {
      return PARSER;
    }

    public cn.daxiang.protocol.battle.BattleCommonProtocol.Msg getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Request_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Request_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Response_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Response_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_IntPacket_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_IntPacket_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ActorProfile_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_ActorProfile_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ActorProfile_PraisedCountMapEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_ActorProfile_PraisedCountMapEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Msg_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Msg_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n!battle/battleCommonProtocol.proto\"\t\n\007R" +
      "equest\"\n\n\010Response\"\032\n\tIntPacket\022\r\n\005value" +
      "\030\001 \001(\005\"\271\004\n\014ActorProfile\022\020\n\010serverId\030\001 \001(" +
      "\005\022\017\n\007actorId\030\002 \001(\003\022\021\n\tactorName\030\003 \001(\t\022\013\n" +
      "\003sex\030\004 \001(\010\022\r\n\005level\030\005 \001(\005\022\020\n\010vipLevel\030\006 " +
      "\001(\005\022\020\n\010avatarId\030\007 \001(\005\022\r\n\005power\030\010 \001(\003\022\022\n\n" +
      "nationName\030\t \001(\t\022\016\n\006showId\030\n \001(\005\022\027\n\017curr" +
      "entServerId\030\013 \001(\005\022\023\n\013onlineState\030\014 \001(\010\022\026" +
      "\n\016lastLogoutTime\030\r \001(\003\022\025\n\ravatarFrameId\030" +
      "\016 \001(\005\022\037\n\027caveSingleChallengeList\030\017 \003(\005\022\020" +
      "\n\010nationId\030\020 \001(\003\022\020\n\010official\030\021 \001(\005\022\030\n\020im" +
      "mortalsSkinIds\030\022 \003(\005\022\017\n\007titleId\030\023 \001(\005\022\021\n" +
      "\tsdkUserId\030\024 \001(\t\022\025\n\rshowImmortals\030\025 \001(\010\022" +
      "\024\n\014showHeroStar\030\026 \001(\005\022;\n\017praisedCountMap" +
      "\030\027 \003(\0132\".ActorProfile.PraisedCountMapEnt" +
      "ry\0326\n\024PraisedCountMapEntry\022\013\n\003key\030\001 \001(\005\022" +
      "\r\n\005value\030\002 \001(\003:\0028\001\"\"\n\003Msg\022\014\n\004name\030\001 \001(\t\022" +
      "\r\n\005value\030\002 \001(\014B\036\n\032cn.daxiang.protocol.ba" +
      "ttleH\001b\006proto3"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
        new com.google.protobuf.Descriptors.FileDescriptor.    InternalDescriptorAssigner() {
          public com.google.protobuf.ExtensionRegistry assignDescriptors(
              com.google.protobuf.Descriptors.FileDescriptor root) {
            descriptor = root;
            return null;
          }
        };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        }, assigner);
    internal_static_Request_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_Request_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Request_descriptor,
        new java.lang.String[] { });
    internal_static_Response_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_Response_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Response_descriptor,
        new java.lang.String[] { });
    internal_static_IntPacket_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_IntPacket_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_IntPacket_descriptor,
        new java.lang.String[] { "Value", });
    internal_static_ActorProfile_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_ActorProfile_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_ActorProfile_descriptor,
        new java.lang.String[] { "ServerId", "ActorId", "ActorName", "Sex", "Level", "VipLevel", "AvatarId", "Power", "NationName", "ShowId", "CurrentServerId", "OnlineState", "LastLogoutTime", "AvatarFrameId", "CaveSingleChallengeList", "NationId", "Official", "ImmortalsSkinIds", "TitleId", "SdkUserId", "ShowImmortals", "ShowHeroStar", "PraisedCountMap", });
    internal_static_ActorProfile_PraisedCountMapEntry_descriptor =
      internal_static_ActorProfile_descriptor.getNestedTypes().get(0);
    internal_static_ActorProfile_PraisedCountMapEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_ActorProfile_PraisedCountMapEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_Msg_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_Msg_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Msg_descriptor,
        new java.lang.String[] { "Name", "Value", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
