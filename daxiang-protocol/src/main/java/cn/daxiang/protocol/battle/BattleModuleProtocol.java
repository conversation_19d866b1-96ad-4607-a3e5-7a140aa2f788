// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: battle/battleModuleProtocol.proto

package cn.daxiang.protocol.battle;

public final class BattleModuleProtocol {
  private BattleModuleProtocol() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  /**
   * <pre>
   **
   * 战斗服服模块分类
   * &lt;pre&gt;
   * 00 - 10 用途：用于维护、控制台、测试等
   * 11 - 99 用途：逻辑模块
   * &lt;/pre&gt;
   * </pre>
   *
   * Protobuf enum {@code BattleModule}
   */
  public enum BattleModule
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>MODULE_NONE = 0;</code>
     */
    MODULE_NONE(0),
    /**
     * <pre>
     ** 基础模块 
     * </pre>
     *
     * <code>BASE = 1;</code>
     */
    BASE(1),
    /**
     * <pre>
     ** 登录 
     * </pre>
     *
     * <code>LOGIN = 11;</code>
     */
    LOGIN(11),
    /**
     * <pre>
     ** 组队在线TD 
     * </pre>
     *
     * <code>TEAMONLINETD = 12;</code>
     */
    TEAMONLINETD(12),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>MODULE_NONE = 0;</code>
     */
    public static final int MODULE_NONE_VALUE = 0;
    /**
     * <pre>
     ** 基础模块 
     * </pre>
     *
     * <code>BASE = 1;</code>
     */
    public static final int BASE_VALUE = 1;
    /**
     * <pre>
     ** 登录 
     * </pre>
     *
     * <code>LOGIN = 11;</code>
     */
    public static final int LOGIN_VALUE = 11;
    /**
     * <pre>
     ** 组队在线TD 
     * </pre>
     *
     * <code>TEAMONLINETD = 12;</code>
     */
    public static final int TEAMONLINETD_VALUE = 12;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static BattleModule valueOf(int value) {
      return forNumber(value);
    }

    public static BattleModule forNumber(int value) {
      switch (value) {
        case 0: return MODULE_NONE;
        case 1: return BASE;
        case 11: return LOGIN;
        case 12: return TEAMONLINETD;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<BattleModule>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        BattleModule> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<BattleModule>() {
            public BattleModule findValueByNumber(int number) {
              return BattleModule.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return cn.daxiang.protocol.battle.BattleModuleProtocol.getDescriptor().getEnumTypes().get(0);
    }

    private static final BattleModule[] VALUES = values();

    public static BattleModule valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private BattleModule(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:BattleModule)
  }

  public interface MsgRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:MsgRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     ** 头标记（约定值） 
     * </pre>
     *
     * <code>int32 headerFlag = 1;</code>
     */
    int getHeaderFlag();

    /**
     * <pre>
     ** 消息具体数据长度 
     * </pre>
     *
     * <code>int32 packageLen = 2;</code>
     */
    int getPackageLen();

    /**
     * <pre>
     ** 模块id 
     * </pre>
     *
     * <code>.BattleModule module = 3;</code>
     */
    int getModuleValue();
    /**
     * <pre>
     ** 模块id 
     * </pre>
     *
     * <code>.BattleModule module = 3;</code>
     */
    cn.daxiang.protocol.battle.BattleModuleProtocol.BattleModule getModule();

    /**
     * <pre>
     ** 命令id 
     * </pre>
     *
     * <code>int32 cmd = 4;</code>
     */
    int getCmd();

    /**
     * <pre>
     ** 消息具体数据hash验证 
     * </pre>
     *
     * <code>int32 hashCode = 5;</code>
     */
    int getHashCode();

    /**
     * <pre>
     ** 消息具体数据 
     * </pre>
     *
     * <code>bytes value = 6;</code>
     */
    com.google.protobuf.ByteString getValue();
  }
  /**
   * Protobuf type {@code MsgRequest}
   */
  public  static final class MsgRequest extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:MsgRequest)
      MsgRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use MsgRequest.newBuilder() to construct.
    private MsgRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private MsgRequest() {
      headerFlag_ = 0;
      packageLen_ = 0;
      module_ = 0;
      cmd_ = 0;
      hashCode_ = 0;
      value_ = com.google.protobuf.ByteString.EMPTY;
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private MsgRequest(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {

              headerFlag_ = input.readInt32();
              break;
            }
            case 16: {

              packageLen_ = input.readInt32();
              break;
            }
            case 24: {
              int rawValue = input.readEnum();

              module_ = rawValue;
              break;
            }
            case 32: {

              cmd_ = input.readInt32();
              break;
            }
            case 40: {

              hashCode_ = input.readInt32();
              break;
            }
            case 50: {

              value_ = input.readBytes();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.daxiang.protocol.battle.BattleModuleProtocol.internal_static_MsgRequest_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.daxiang.protocol.battle.BattleModuleProtocol.internal_static_MsgRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.daxiang.protocol.battle.BattleModuleProtocol.MsgRequest.class, cn.daxiang.protocol.battle.BattleModuleProtocol.MsgRequest.Builder.class);
    }

    public static final int HEADERFLAG_FIELD_NUMBER = 1;
    private int headerFlag_;
    /**
     * <pre>
     ** 头标记（约定值） 
     * </pre>
     *
     * <code>int32 headerFlag = 1;</code>
     */
    public int getHeaderFlag() {
      return headerFlag_;
    }

    public static final int PACKAGELEN_FIELD_NUMBER = 2;
    private int packageLen_;
    /**
     * <pre>
     ** 消息具体数据长度 
     * </pre>
     *
     * <code>int32 packageLen = 2;</code>
     */
    public int getPackageLen() {
      return packageLen_;
    }

    public static final int MODULE_FIELD_NUMBER = 3;
    private int module_;
    /**
     * <pre>
     ** 模块id 
     * </pre>
     *
     * <code>.BattleModule module = 3;</code>
     */
    public int getModuleValue() {
      return module_;
    }
    /**
     * <pre>
     ** 模块id 
     * </pre>
     *
     * <code>.BattleModule module = 3;</code>
     */
    public cn.daxiang.protocol.battle.BattleModuleProtocol.BattleModule getModule() {
      cn.daxiang.protocol.battle.BattleModuleProtocol.BattleModule result = cn.daxiang.protocol.battle.BattleModuleProtocol.BattleModule.valueOf(module_);
      return result == null ? cn.daxiang.protocol.battle.BattleModuleProtocol.BattleModule.UNRECOGNIZED : result;
    }

    public static final int CMD_FIELD_NUMBER = 4;
    private int cmd_;
    /**
     * <pre>
     ** 命令id 
     * </pre>
     *
     * <code>int32 cmd = 4;</code>
     */
    public int getCmd() {
      return cmd_;
    }

    public static final int HASHCODE_FIELD_NUMBER = 5;
    private int hashCode_;
    /**
     * <pre>
     ** 消息具体数据hash验证 
     * </pre>
     *
     * <code>int32 hashCode = 5;</code>
     */
    public int getHashCode() {
      return hashCode_;
    }

    public static final int VALUE_FIELD_NUMBER = 6;
    private com.google.protobuf.ByteString value_;
    /**
     * <pre>
     ** 消息具体数据 
     * </pre>
     *
     * <code>bytes value = 6;</code>
     */
    public com.google.protobuf.ByteString getValue() {
      return value_;
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (headerFlag_ != 0) {
        output.writeInt32(1, headerFlag_);
      }
      if (packageLen_ != 0) {
        output.writeInt32(2, packageLen_);
      }
      if (module_ != cn.daxiang.protocol.battle.BattleModuleProtocol.BattleModule.MODULE_NONE.getNumber()) {
        output.writeEnum(3, module_);
      }
      if (cmd_ != 0) {
        output.writeInt32(4, cmd_);
      }
      if (hashCode_ != 0) {
        output.writeInt32(5, hashCode_);
      }
      if (!value_.isEmpty()) {
        output.writeBytes(6, value_);
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (headerFlag_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, headerFlag_);
      }
      if (packageLen_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, packageLen_);
      }
      if (module_ != cn.daxiang.protocol.battle.BattleModuleProtocol.BattleModule.MODULE_NONE.getNumber()) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(3, module_);
      }
      if (cmd_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, cmd_);
      }
      if (hashCode_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(5, hashCode_);
      }
      if (!value_.isEmpty()) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(6, value_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof cn.daxiang.protocol.battle.BattleModuleProtocol.MsgRequest)) {
        return super.equals(obj);
      }
      cn.daxiang.protocol.battle.BattleModuleProtocol.MsgRequest other = (cn.daxiang.protocol.battle.BattleModuleProtocol.MsgRequest) obj;

      boolean result = true;
      result = result && (getHeaderFlag()
          == other.getHeaderFlag());
      result = result && (getPackageLen()
          == other.getPackageLen());
      result = result && module_ == other.module_;
      result = result && (getCmd()
          == other.getCmd());
      result = result && (getHashCode()
          == other.getHashCode());
      result = result && getValue()
          .equals(other.getValue());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + HEADERFLAG_FIELD_NUMBER;
      hash = (53 * hash) + getHeaderFlag();
      hash = (37 * hash) + PACKAGELEN_FIELD_NUMBER;
      hash = (53 * hash) + getPackageLen();
      hash = (37 * hash) + MODULE_FIELD_NUMBER;
      hash = (53 * hash) + module_;
      hash = (37 * hash) + CMD_FIELD_NUMBER;
      hash = (53 * hash) + getCmd();
      hash = (37 * hash) + HASHCODE_FIELD_NUMBER;
      hash = (53 * hash) + getHashCode();
      hash = (37 * hash) + VALUE_FIELD_NUMBER;
      hash = (53 * hash) + getValue().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static cn.daxiang.protocol.battle.BattleModuleProtocol.MsgRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.battle.BattleModuleProtocol.MsgRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.battle.BattleModuleProtocol.MsgRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.battle.BattleModuleProtocol.MsgRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.battle.BattleModuleProtocol.MsgRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.battle.BattleModuleProtocol.MsgRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.battle.BattleModuleProtocol.MsgRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.battle.BattleModuleProtocol.MsgRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.battle.BattleModuleProtocol.MsgRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.battle.BattleModuleProtocol.MsgRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.battle.BattleModuleProtocol.MsgRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.battle.BattleModuleProtocol.MsgRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(cn.daxiang.protocol.battle.BattleModuleProtocol.MsgRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code MsgRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:MsgRequest)
        cn.daxiang.protocol.battle.BattleModuleProtocol.MsgRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.daxiang.protocol.battle.BattleModuleProtocol.internal_static_MsgRequest_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.daxiang.protocol.battle.BattleModuleProtocol.internal_static_MsgRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.daxiang.protocol.battle.BattleModuleProtocol.MsgRequest.class, cn.daxiang.protocol.battle.BattleModuleProtocol.MsgRequest.Builder.class);
      }

      // Construct using cn.daxiang.protocol.battle.BattleModuleProtocol.MsgRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        headerFlag_ = 0;

        packageLen_ = 0;

        module_ = 0;

        cmd_ = 0;

        hashCode_ = 0;

        value_ = com.google.protobuf.ByteString.EMPTY;

        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return cn.daxiang.protocol.battle.BattleModuleProtocol.internal_static_MsgRequest_descriptor;
      }

      public cn.daxiang.protocol.battle.BattleModuleProtocol.MsgRequest getDefaultInstanceForType() {
        return cn.daxiang.protocol.battle.BattleModuleProtocol.MsgRequest.getDefaultInstance();
      }

      public cn.daxiang.protocol.battle.BattleModuleProtocol.MsgRequest build() {
        cn.daxiang.protocol.battle.BattleModuleProtocol.MsgRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public cn.daxiang.protocol.battle.BattleModuleProtocol.MsgRequest buildPartial() {
        cn.daxiang.protocol.battle.BattleModuleProtocol.MsgRequest result = new cn.daxiang.protocol.battle.BattleModuleProtocol.MsgRequest(this);
        result.headerFlag_ = headerFlag_;
        result.packageLen_ = packageLen_;
        result.module_ = module_;
        result.cmd_ = cmd_;
        result.hashCode_ = hashCode_;
        result.value_ = value_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof cn.daxiang.protocol.battle.BattleModuleProtocol.MsgRequest) {
          return mergeFrom((cn.daxiang.protocol.battle.BattleModuleProtocol.MsgRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(cn.daxiang.protocol.battle.BattleModuleProtocol.MsgRequest other) {
        if (other == cn.daxiang.protocol.battle.BattleModuleProtocol.MsgRequest.getDefaultInstance()) return this;
        if (other.getHeaderFlag() != 0) {
          setHeaderFlag(other.getHeaderFlag());
        }
        if (other.getPackageLen() != 0) {
          setPackageLen(other.getPackageLen());
        }
        if (other.module_ != 0) {
          setModuleValue(other.getModuleValue());
        }
        if (other.getCmd() != 0) {
          setCmd(other.getCmd());
        }
        if (other.getHashCode() != 0) {
          setHashCode(other.getHashCode());
        }
        if (other.getValue() != com.google.protobuf.ByteString.EMPTY) {
          setValue(other.getValue());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        cn.daxiang.protocol.battle.BattleModuleProtocol.MsgRequest parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (cn.daxiang.protocol.battle.BattleModuleProtocol.MsgRequest) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int headerFlag_ ;
      /**
       * <pre>
       ** 头标记（约定值） 
       * </pre>
       *
       * <code>int32 headerFlag = 1;</code>
       */
      public int getHeaderFlag() {
        return headerFlag_;
      }
      /**
       * <pre>
       ** 头标记（约定值） 
       * </pre>
       *
       * <code>int32 headerFlag = 1;</code>
       */
      public Builder setHeaderFlag(int value) {
        
        headerFlag_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 头标记（约定值） 
       * </pre>
       *
       * <code>int32 headerFlag = 1;</code>
       */
      public Builder clearHeaderFlag() {
        
        headerFlag_ = 0;
        onChanged();
        return this;
      }

      private int packageLen_ ;
      /**
       * <pre>
       ** 消息具体数据长度 
       * </pre>
       *
       * <code>int32 packageLen = 2;</code>
       */
      public int getPackageLen() {
        return packageLen_;
      }
      /**
       * <pre>
       ** 消息具体数据长度 
       * </pre>
       *
       * <code>int32 packageLen = 2;</code>
       */
      public Builder setPackageLen(int value) {
        
        packageLen_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 消息具体数据长度 
       * </pre>
       *
       * <code>int32 packageLen = 2;</code>
       */
      public Builder clearPackageLen() {
        
        packageLen_ = 0;
        onChanged();
        return this;
      }

      private int module_ = 0;
      /**
       * <pre>
       ** 模块id 
       * </pre>
       *
       * <code>.BattleModule module = 3;</code>
       */
      public int getModuleValue() {
        return module_;
      }
      /**
       * <pre>
       ** 模块id 
       * </pre>
       *
       * <code>.BattleModule module = 3;</code>
       */
      public Builder setModuleValue(int value) {
        module_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 模块id 
       * </pre>
       *
       * <code>.BattleModule module = 3;</code>
       */
      public cn.daxiang.protocol.battle.BattleModuleProtocol.BattleModule getModule() {
        cn.daxiang.protocol.battle.BattleModuleProtocol.BattleModule result = cn.daxiang.protocol.battle.BattleModuleProtocol.BattleModule.valueOf(module_);
        return result == null ? cn.daxiang.protocol.battle.BattleModuleProtocol.BattleModule.UNRECOGNIZED : result;
      }
      /**
       * <pre>
       ** 模块id 
       * </pre>
       *
       * <code>.BattleModule module = 3;</code>
       */
      public Builder setModule(cn.daxiang.protocol.battle.BattleModuleProtocol.BattleModule value) {
        if (value == null) {
          throw new NullPointerException();
        }
        
        module_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 模块id 
       * </pre>
       *
       * <code>.BattleModule module = 3;</code>
       */
      public Builder clearModule() {
        
        module_ = 0;
        onChanged();
        return this;
      }

      private int cmd_ ;
      /**
       * <pre>
       ** 命令id 
       * </pre>
       *
       * <code>int32 cmd = 4;</code>
       */
      public int getCmd() {
        return cmd_;
      }
      /**
       * <pre>
       ** 命令id 
       * </pre>
       *
       * <code>int32 cmd = 4;</code>
       */
      public Builder setCmd(int value) {
        
        cmd_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 命令id 
       * </pre>
       *
       * <code>int32 cmd = 4;</code>
       */
      public Builder clearCmd() {
        
        cmd_ = 0;
        onChanged();
        return this;
      }

      private int hashCode_ ;
      /**
       * <pre>
       ** 消息具体数据hash验证 
       * </pre>
       *
       * <code>int32 hashCode = 5;</code>
       */
      public int getHashCode() {
        return hashCode_;
      }
      /**
       * <pre>
       ** 消息具体数据hash验证 
       * </pre>
       *
       * <code>int32 hashCode = 5;</code>
       */
      public Builder setHashCode(int value) {
        
        hashCode_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 消息具体数据hash验证 
       * </pre>
       *
       * <code>int32 hashCode = 5;</code>
       */
      public Builder clearHashCode() {
        
        hashCode_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString value_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <pre>
       ** 消息具体数据 
       * </pre>
       *
       * <code>bytes value = 6;</code>
       */
      public com.google.protobuf.ByteString getValue() {
        return value_;
      }
      /**
       * <pre>
       ** 消息具体数据 
       * </pre>
       *
       * <code>bytes value = 6;</code>
       */
      public Builder setValue(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        value_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 消息具体数据 
       * </pre>
       *
       * <code>bytes value = 6;</code>
       */
      public Builder clearValue() {
        
        value_ = getDefaultInstance().getValue();
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:MsgRequest)
    }

    // @@protoc_insertion_point(class_scope:MsgRequest)
    private static final cn.daxiang.protocol.battle.BattleModuleProtocol.MsgRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new cn.daxiang.protocol.battle.BattleModuleProtocol.MsgRequest();
    }

    public static cn.daxiang.protocol.battle.BattleModuleProtocol.MsgRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<MsgRequest>
        PARSER = new com.google.protobuf.AbstractParser<MsgRequest>() {
      public MsgRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new MsgRequest(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<MsgRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MsgRequest> getParserForType() {
      return PARSER;
    }

    public cn.daxiang.protocol.battle.BattleModuleProtocol.MsgRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface MsgResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:MsgResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     ** 头标记（约定值）
     * </pre>
     *
     * <code>int32 headerFlag = 1;</code>
     */
    int getHeaderFlag();

    /**
     * <pre>
     ** 消息具体数据长度 
     * </pre>
     *
     * <code>int32 packageLen = 2;</code>
     */
    int getPackageLen();

    /**
     * <pre>
     ** 模块id 
     * </pre>
     *
     * <code>.BattleModule module = 3;</code>
     */
    int getModuleValue();
    /**
     * <pre>
     ** 模块id 
     * </pre>
     *
     * <code>.BattleModule module = 3;</code>
     */
    cn.daxiang.protocol.battle.BattleModuleProtocol.BattleModule getModule();

    /**
     * <pre>
     ** 命令id 
     * </pre>
     *
     * <code>int32 cmd = 4;</code>
     */
    int getCmd();

    /**
     * <pre>
     ** 返回状态码 0 正常 
     * </pre>
     *
     * <code>int32 statusCode = 5;</code>
     */
    int getStatusCode();

    /**
     * <pre>
     ** 消息具体数据 
     * </pre>
     *
     * <code>bytes value = 6;</code>
     */
    com.google.protobuf.ByteString getValue();
  }
  /**
   * Protobuf type {@code MsgResponse}
   */
  public  static final class MsgResponse extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:MsgResponse)
      MsgResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use MsgResponse.newBuilder() to construct.
    private MsgResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private MsgResponse() {
      headerFlag_ = 0;
      packageLen_ = 0;
      module_ = 0;
      cmd_ = 0;
      statusCode_ = 0;
      value_ = com.google.protobuf.ByteString.EMPTY;
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private MsgResponse(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {

              headerFlag_ = input.readInt32();
              break;
            }
            case 16: {

              packageLen_ = input.readInt32();
              break;
            }
            case 24: {
              int rawValue = input.readEnum();

              module_ = rawValue;
              break;
            }
            case 32: {

              cmd_ = input.readInt32();
              break;
            }
            case 40: {

              statusCode_ = input.readInt32();
              break;
            }
            case 50: {

              value_ = input.readBytes();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.daxiang.protocol.battle.BattleModuleProtocol.internal_static_MsgResponse_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.daxiang.protocol.battle.BattleModuleProtocol.internal_static_MsgResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.daxiang.protocol.battle.BattleModuleProtocol.MsgResponse.class, cn.daxiang.protocol.battle.BattleModuleProtocol.MsgResponse.Builder.class);
    }

    public static final int HEADERFLAG_FIELD_NUMBER = 1;
    private int headerFlag_;
    /**
     * <pre>
     ** 头标记（约定值）
     * </pre>
     *
     * <code>int32 headerFlag = 1;</code>
     */
    public int getHeaderFlag() {
      return headerFlag_;
    }

    public static final int PACKAGELEN_FIELD_NUMBER = 2;
    private int packageLen_;
    /**
     * <pre>
     ** 消息具体数据长度 
     * </pre>
     *
     * <code>int32 packageLen = 2;</code>
     */
    public int getPackageLen() {
      return packageLen_;
    }

    public static final int MODULE_FIELD_NUMBER = 3;
    private int module_;
    /**
     * <pre>
     ** 模块id 
     * </pre>
     *
     * <code>.BattleModule module = 3;</code>
     */
    public int getModuleValue() {
      return module_;
    }
    /**
     * <pre>
     ** 模块id 
     * </pre>
     *
     * <code>.BattleModule module = 3;</code>
     */
    public cn.daxiang.protocol.battle.BattleModuleProtocol.BattleModule getModule() {
      cn.daxiang.protocol.battle.BattleModuleProtocol.BattleModule result = cn.daxiang.protocol.battle.BattleModuleProtocol.BattleModule.valueOf(module_);
      return result == null ? cn.daxiang.protocol.battle.BattleModuleProtocol.BattleModule.UNRECOGNIZED : result;
    }

    public static final int CMD_FIELD_NUMBER = 4;
    private int cmd_;
    /**
     * <pre>
     ** 命令id 
     * </pre>
     *
     * <code>int32 cmd = 4;</code>
     */
    public int getCmd() {
      return cmd_;
    }

    public static final int STATUSCODE_FIELD_NUMBER = 5;
    private int statusCode_;
    /**
     * <pre>
     ** 返回状态码 0 正常 
     * </pre>
     *
     * <code>int32 statusCode = 5;</code>
     */
    public int getStatusCode() {
      return statusCode_;
    }

    public static final int VALUE_FIELD_NUMBER = 6;
    private com.google.protobuf.ByteString value_;
    /**
     * <pre>
     ** 消息具体数据 
     * </pre>
     *
     * <code>bytes value = 6;</code>
     */
    public com.google.protobuf.ByteString getValue() {
      return value_;
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (headerFlag_ != 0) {
        output.writeInt32(1, headerFlag_);
      }
      if (packageLen_ != 0) {
        output.writeInt32(2, packageLen_);
      }
      if (module_ != cn.daxiang.protocol.battle.BattleModuleProtocol.BattleModule.MODULE_NONE.getNumber()) {
        output.writeEnum(3, module_);
      }
      if (cmd_ != 0) {
        output.writeInt32(4, cmd_);
      }
      if (statusCode_ != 0) {
        output.writeInt32(5, statusCode_);
      }
      if (!value_.isEmpty()) {
        output.writeBytes(6, value_);
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (headerFlag_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, headerFlag_);
      }
      if (packageLen_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, packageLen_);
      }
      if (module_ != cn.daxiang.protocol.battle.BattleModuleProtocol.BattleModule.MODULE_NONE.getNumber()) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(3, module_);
      }
      if (cmd_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, cmd_);
      }
      if (statusCode_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(5, statusCode_);
      }
      if (!value_.isEmpty()) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(6, value_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof cn.daxiang.protocol.battle.BattleModuleProtocol.MsgResponse)) {
        return super.equals(obj);
      }
      cn.daxiang.protocol.battle.BattleModuleProtocol.MsgResponse other = (cn.daxiang.protocol.battle.BattleModuleProtocol.MsgResponse) obj;

      boolean result = true;
      result = result && (getHeaderFlag()
          == other.getHeaderFlag());
      result = result && (getPackageLen()
          == other.getPackageLen());
      result = result && module_ == other.module_;
      result = result && (getCmd()
          == other.getCmd());
      result = result && (getStatusCode()
          == other.getStatusCode());
      result = result && getValue()
          .equals(other.getValue());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + HEADERFLAG_FIELD_NUMBER;
      hash = (53 * hash) + getHeaderFlag();
      hash = (37 * hash) + PACKAGELEN_FIELD_NUMBER;
      hash = (53 * hash) + getPackageLen();
      hash = (37 * hash) + MODULE_FIELD_NUMBER;
      hash = (53 * hash) + module_;
      hash = (37 * hash) + CMD_FIELD_NUMBER;
      hash = (53 * hash) + getCmd();
      hash = (37 * hash) + STATUSCODE_FIELD_NUMBER;
      hash = (53 * hash) + getStatusCode();
      hash = (37 * hash) + VALUE_FIELD_NUMBER;
      hash = (53 * hash) + getValue().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static cn.daxiang.protocol.battle.BattleModuleProtocol.MsgResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.battle.BattleModuleProtocol.MsgResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.battle.BattleModuleProtocol.MsgResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.battle.BattleModuleProtocol.MsgResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.battle.BattleModuleProtocol.MsgResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.battle.BattleModuleProtocol.MsgResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.battle.BattleModuleProtocol.MsgResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.battle.BattleModuleProtocol.MsgResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.battle.BattleModuleProtocol.MsgResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.battle.BattleModuleProtocol.MsgResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.battle.BattleModuleProtocol.MsgResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.battle.BattleModuleProtocol.MsgResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(cn.daxiang.protocol.battle.BattleModuleProtocol.MsgResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code MsgResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:MsgResponse)
        cn.daxiang.protocol.battle.BattleModuleProtocol.MsgResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.daxiang.protocol.battle.BattleModuleProtocol.internal_static_MsgResponse_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.daxiang.protocol.battle.BattleModuleProtocol.internal_static_MsgResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.daxiang.protocol.battle.BattleModuleProtocol.MsgResponse.class, cn.daxiang.protocol.battle.BattleModuleProtocol.MsgResponse.Builder.class);
      }

      // Construct using cn.daxiang.protocol.battle.BattleModuleProtocol.MsgResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        headerFlag_ = 0;

        packageLen_ = 0;

        module_ = 0;

        cmd_ = 0;

        statusCode_ = 0;

        value_ = com.google.protobuf.ByteString.EMPTY;

        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return cn.daxiang.protocol.battle.BattleModuleProtocol.internal_static_MsgResponse_descriptor;
      }

      public cn.daxiang.protocol.battle.BattleModuleProtocol.MsgResponse getDefaultInstanceForType() {
        return cn.daxiang.protocol.battle.BattleModuleProtocol.MsgResponse.getDefaultInstance();
      }

      public cn.daxiang.protocol.battle.BattleModuleProtocol.MsgResponse build() {
        cn.daxiang.protocol.battle.BattleModuleProtocol.MsgResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public cn.daxiang.protocol.battle.BattleModuleProtocol.MsgResponse buildPartial() {
        cn.daxiang.protocol.battle.BattleModuleProtocol.MsgResponse result = new cn.daxiang.protocol.battle.BattleModuleProtocol.MsgResponse(this);
        result.headerFlag_ = headerFlag_;
        result.packageLen_ = packageLen_;
        result.module_ = module_;
        result.cmd_ = cmd_;
        result.statusCode_ = statusCode_;
        result.value_ = value_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof cn.daxiang.protocol.battle.BattleModuleProtocol.MsgResponse) {
          return mergeFrom((cn.daxiang.protocol.battle.BattleModuleProtocol.MsgResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(cn.daxiang.protocol.battle.BattleModuleProtocol.MsgResponse other) {
        if (other == cn.daxiang.protocol.battle.BattleModuleProtocol.MsgResponse.getDefaultInstance()) return this;
        if (other.getHeaderFlag() != 0) {
          setHeaderFlag(other.getHeaderFlag());
        }
        if (other.getPackageLen() != 0) {
          setPackageLen(other.getPackageLen());
        }
        if (other.module_ != 0) {
          setModuleValue(other.getModuleValue());
        }
        if (other.getCmd() != 0) {
          setCmd(other.getCmd());
        }
        if (other.getStatusCode() != 0) {
          setStatusCode(other.getStatusCode());
        }
        if (other.getValue() != com.google.protobuf.ByteString.EMPTY) {
          setValue(other.getValue());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        cn.daxiang.protocol.battle.BattleModuleProtocol.MsgResponse parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (cn.daxiang.protocol.battle.BattleModuleProtocol.MsgResponse) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int headerFlag_ ;
      /**
       * <pre>
       ** 头标记（约定值）
       * </pre>
       *
       * <code>int32 headerFlag = 1;</code>
       */
      public int getHeaderFlag() {
        return headerFlag_;
      }
      /**
       * <pre>
       ** 头标记（约定值）
       * </pre>
       *
       * <code>int32 headerFlag = 1;</code>
       */
      public Builder setHeaderFlag(int value) {
        
        headerFlag_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 头标记（约定值）
       * </pre>
       *
       * <code>int32 headerFlag = 1;</code>
       */
      public Builder clearHeaderFlag() {
        
        headerFlag_ = 0;
        onChanged();
        return this;
      }

      private int packageLen_ ;
      /**
       * <pre>
       ** 消息具体数据长度 
       * </pre>
       *
       * <code>int32 packageLen = 2;</code>
       */
      public int getPackageLen() {
        return packageLen_;
      }
      /**
       * <pre>
       ** 消息具体数据长度 
       * </pre>
       *
       * <code>int32 packageLen = 2;</code>
       */
      public Builder setPackageLen(int value) {
        
        packageLen_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 消息具体数据长度 
       * </pre>
       *
       * <code>int32 packageLen = 2;</code>
       */
      public Builder clearPackageLen() {
        
        packageLen_ = 0;
        onChanged();
        return this;
      }

      private int module_ = 0;
      /**
       * <pre>
       ** 模块id 
       * </pre>
       *
       * <code>.BattleModule module = 3;</code>
       */
      public int getModuleValue() {
        return module_;
      }
      /**
       * <pre>
       ** 模块id 
       * </pre>
       *
       * <code>.BattleModule module = 3;</code>
       */
      public Builder setModuleValue(int value) {
        module_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 模块id 
       * </pre>
       *
       * <code>.BattleModule module = 3;</code>
       */
      public cn.daxiang.protocol.battle.BattleModuleProtocol.BattleModule getModule() {
        cn.daxiang.protocol.battle.BattleModuleProtocol.BattleModule result = cn.daxiang.protocol.battle.BattleModuleProtocol.BattleModule.valueOf(module_);
        return result == null ? cn.daxiang.protocol.battle.BattleModuleProtocol.BattleModule.UNRECOGNIZED : result;
      }
      /**
       * <pre>
       ** 模块id 
       * </pre>
       *
       * <code>.BattleModule module = 3;</code>
       */
      public Builder setModule(cn.daxiang.protocol.battle.BattleModuleProtocol.BattleModule value) {
        if (value == null) {
          throw new NullPointerException();
        }
        
        module_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 模块id 
       * </pre>
       *
       * <code>.BattleModule module = 3;</code>
       */
      public Builder clearModule() {
        
        module_ = 0;
        onChanged();
        return this;
      }

      private int cmd_ ;
      /**
       * <pre>
       ** 命令id 
       * </pre>
       *
       * <code>int32 cmd = 4;</code>
       */
      public int getCmd() {
        return cmd_;
      }
      /**
       * <pre>
       ** 命令id 
       * </pre>
       *
       * <code>int32 cmd = 4;</code>
       */
      public Builder setCmd(int value) {
        
        cmd_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 命令id 
       * </pre>
       *
       * <code>int32 cmd = 4;</code>
       */
      public Builder clearCmd() {
        
        cmd_ = 0;
        onChanged();
        return this;
      }

      private int statusCode_ ;
      /**
       * <pre>
       ** 返回状态码 0 正常 
       * </pre>
       *
       * <code>int32 statusCode = 5;</code>
       */
      public int getStatusCode() {
        return statusCode_;
      }
      /**
       * <pre>
       ** 返回状态码 0 正常 
       * </pre>
       *
       * <code>int32 statusCode = 5;</code>
       */
      public Builder setStatusCode(int value) {
        
        statusCode_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 返回状态码 0 正常 
       * </pre>
       *
       * <code>int32 statusCode = 5;</code>
       */
      public Builder clearStatusCode() {
        
        statusCode_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString value_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <pre>
       ** 消息具体数据 
       * </pre>
       *
       * <code>bytes value = 6;</code>
       */
      public com.google.protobuf.ByteString getValue() {
        return value_;
      }
      /**
       * <pre>
       ** 消息具体数据 
       * </pre>
       *
       * <code>bytes value = 6;</code>
       */
      public Builder setValue(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        value_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 消息具体数据 
       * </pre>
       *
       * <code>bytes value = 6;</code>
       */
      public Builder clearValue() {
        
        value_ = getDefaultInstance().getValue();
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:MsgResponse)
    }

    // @@protoc_insertion_point(class_scope:MsgResponse)
    private static final cn.daxiang.protocol.battle.BattleModuleProtocol.MsgResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new cn.daxiang.protocol.battle.BattleModuleProtocol.MsgResponse();
    }

    public static cn.daxiang.protocol.battle.BattleModuleProtocol.MsgResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<MsgResponse>
        PARSER = new com.google.protobuf.AbstractParser<MsgResponse>() {
      public MsgResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new MsgResponse(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<MsgResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MsgResponse> getParserForType() {
      return PARSER;
    }

    public cn.daxiang.protocol.battle.BattleModuleProtocol.MsgResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_MsgRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_MsgRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_MsgResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_MsgResponse_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n!battle/battleModuleProtocol.proto\"\201\001\n\n" +
      "MsgRequest\022\022\n\nheaderFlag\030\001 \001(\005\022\022\n\npackag" +
      "eLen\030\002 \001(\005\022\035\n\006module\030\003 \001(\0162\r.BattleModul" +
      "e\022\013\n\003cmd\030\004 \001(\005\022\020\n\010hashCode\030\005 \001(\005\022\r\n\005valu" +
      "e\030\006 \001(\014\"\204\001\n\013MsgResponse\022\022\n\nheaderFlag\030\001 " +
      "\001(\005\022\022\n\npackageLen\030\002 \001(\005\022\035\n\006module\030\003 \001(\0162" +
      "\r.BattleModule\022\013\n\003cmd\030\004 \001(\005\022\022\n\nstatusCod" +
      "e\030\005 \001(\005\022\r\n\005value\030\006 \001(\014*F\n\014BattleModule\022\017" +
      "\n\013MODULE_NONE\020\000\022\010\n\004BASE\020\001\022\t\n\005LOGIN\020\013\022\020\n\014" +
      "TEAMONLINETD\020\014B\036\n\032cn.daxiang.protocol.ba" +
      "ttleH\001b\006proto3"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
        new com.google.protobuf.Descriptors.FileDescriptor.    InternalDescriptorAssigner() {
          public com.google.protobuf.ExtensionRegistry assignDescriptors(
              com.google.protobuf.Descriptors.FileDescriptor root) {
            descriptor = root;
            return null;
          }
        };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        }, assigner);
    internal_static_MsgRequest_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_MsgRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_MsgRequest_descriptor,
        new java.lang.String[] { "HeaderFlag", "PackageLen", "Module", "Cmd", "HashCode", "Value", });
    internal_static_MsgResponse_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_MsgResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_MsgResponse_descriptor,
        new java.lang.String[] { "HeaderFlag", "PackageLen", "Module", "Cmd", "StatusCode", "Value", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
