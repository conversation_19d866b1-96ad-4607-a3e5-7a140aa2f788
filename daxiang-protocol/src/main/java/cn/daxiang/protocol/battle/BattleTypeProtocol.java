// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: battle/battleTypeProtocol.proto

package cn.daxiang.protocol.battle;

public final class BattleTypeProtocol {
  private BattleTypeProtocol() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  /**
   * Protobuf enum {@code ActorFieldType}
   */
  public enum ActorFieldType
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>ACTOR_FIELD_TYPE_NONE = 0;</code>
     */
    ACTOR_FIELD_TYPE_NONE(0),
    /**
     * <pre>
     ** 用户ID 
     * </pre>
     *
     * <code>UID = 1;</code>
     */
    UID(1),
    /**
     * <pre>
     ** 平台ID 
     * </pre>
     *
     * <code>PLATFORM_ID = 2;</code>
     */
    PLATFORM_ID(2),
    /**
     * <pre>
     ** 渠道ID 
     * </pre>
     *
     * <code>CHANNEL = 3;</code>
     */
    CHANNEL(3),
    /**
     * <pre>
     ** 服务器ID 
     * </pre>
     *
     * <code>SERVER_ID = 4;</code>
     */
    SERVER_ID(4),
    /**
     * <pre>
     ** 角色ID 
     * </pre>
     *
     * <code>ACTOR_ID = 5;</code>
     */
    ACTOR_ID(5),
    /**
     * <pre>
     ** 角色名称 
     * </pre>
     *
     * <code>ACTOR_NAME = 6;</code>
     */
    ACTOR_NAME(6),
    /**
     * <pre>
     ** 性别 
     * </pre>
     *
     * <code>SEX = 7;</code>
     */
    SEX(7),
    /**
     * <pre>
     ** 角色等级 
     * </pre>
     *
     * <code>ACTOR_LEVEL = 8;</code>
     */
    ACTOR_LEVEL(8),
    /**
     * <pre>
     ** 角色经验 
     * </pre>
     *
     * <code>ACTOR_EXP = 9;</code>
     */
    ACTOR_EXP(9),
    /**
     * <pre>
     ** 金币 
     * </pre>
     *
     * <code>ACTOR_GOLD = 10;</code>
     */
    ACTOR_GOLD(10),
    /**
     * <pre>
     ** 钻石 
     * </pre>
     *
     * <code>ACTOR_DIAMOND = 11;</code>
     */
    ACTOR_DIAMOND(11),
    /**
     * <pre>
     ** vip等级 
     * </pre>
     *
     * <code>VIP_LEVEL = 12;</code>
     */
    VIP_LEVEL(12),
    /**
     * <pre>
     ** 头像ID 
     * </pre>
     *
     * <code>AVATAR_ID = 13;</code>
     */
    AVATAR_ID(13),
    /**
     * <pre>
     ** 新手引导ID列表 
     * </pre>
     *
     * <code>GUIDE_ID_LIST = 14;</code>
     */
    GUIDE_ID_LIST(14),
    /**
     * <pre>
     ** 充值数 
     * </pre>
     *
     * <code>RECHARGE = 15;</code>
     */
    RECHARGE(15),
    /**
     * <pre>
     ** 最后一次改名时间 
     * </pre>
     *
     * <code>LAST_RENAME_TIME = 16;</code>
     */
    LAST_RENAME_TIME(16),
    /**
     * <pre>
     ** 战力 
     * </pre>
     *
     * <code>ACTOR_POWER = 17;</code>
     */
    ACTOR_POWER(17),
    /**
     * <pre>
     ** 赠送的充值数 
     * </pre>
     *
     * <code>PRESENT = 18;</code>
     */
    PRESENT(18),
    /**
     * <pre>
     ** VIP卡信息 
     * </pre>
     *
     * <code>VIP_CARDS = 19;</code>
     */
    VIP_CARDS(19),
    /**
     * <pre>
     ** 精力 
     * </pre>
     *
     * <code>ACTOR_VIGOUR = 20;</code>
     */
    ACTOR_VIGOUR(20),
    /**
     * <pre>
     ** 体力 
     * </pre>
     *
     * <code>ACTOR_ENERGY = 21;</code>
     */
    ACTOR_ENERGY(21),
    /**
     * <pre>
     ** 头像列表 
     * </pre>
     *
     * <code>AVATAR_LIST = 22;</code>
     */
    AVATAR_LIST(22),
    /**
     * <pre>
     ** 形象列表 
     * </pre>
     *
     * <code>SHOW_LIST = 23;</code>
     */
    SHOW_LIST(23),
    /**
     * <pre>
     ** 最后一次恢复精力时间 
     * </pre>
     *
     * <code>LAST_REFRESH_VIGOUR_TIME = 24;</code>
     */
    LAST_REFRESH_VIGOUR_TIME(24),
    /**
     * <pre>
     ** 最后一次恢复体力 
     * </pre>
     *
     * <code>LAST_REFRESH_ENERGY_TIME = 25;</code>
     */
    LAST_REFRESH_ENERGY_TIME(25),
    /**
     * <pre>
     ** 用户创建时间 
     * </pre>
     *
     * <code>ACTOR_CREATE_TIME = 26;</code>
     */
    ACTOR_CREATE_TIME(26),
    /**
     * <pre>
     ** 累计登陆第几天 
     * </pre>
     *
     * <code>LOGIN_TOTAL_DAY = 27;</code>
     */
    LOGIN_TOTAL_DAY(27),
    /**
     * <pre>
     ** 形象ID 
     * </pre>
     *
     * <code>SHOW_ID = 28;</code>
     */
    SHOW_ID(28),
    /**
     * <pre>
     ** 竞技场排名(历史最高) 
     * </pre>
     *
     * <code>ARENA_MAX_RANK = 29;</code>
     */
    ARENA_MAX_RANK(29),
    /**
     * <pre>
     ** 关卡ID 
     * </pre>
     *
     * <code>STORY_ID = 30;</code>
     */
    STORY_ID(30),
    /**
     * <pre>
     ** 历史最高战力 
     * </pre>
     *
     * <code>MAX_POWER = 31;</code>
     */
    MAX_POWER(31),
    /**
     * <pre>
     ** 地宫币
     * </pre>
     *
     * <code>ACTOR_CHESS_COIN = 32;</code>
     */
    ACTOR_CHESS_COIN(32),
    /**
     * <pre>
     ** 竞技币
     * </pre>
     *
     * <code>ACTOR_ARENA_COIN = 33;</code>
     */
    ACTOR_ARENA_COIN(33),
    /**
     * <pre>
     **点赞状态
     * </pre>
     *
     * <code>PRAISE_STATE = 34;</code>
     */
    PRAISE_STATE(34),
    /**
     * <pre>
     **国家名称
     * </pre>
     *
     * <code>NATION_NAME = 35;</code>
     */
    NATION_NAME(35),
    /**
     * <pre>
     **在线状态
     * </pre>
     *
     * <code>ONLINE_STATE = 36;</code>
     */
    ONLINE_STATE(36),
    /**
     * <pre>
     **最后一次退出时间
     * </pre>
     *
     * <code>LAST_LOGOUT_TIME = 37;</code>
     */
    LAST_LOGOUT_TIME(37),
    /**
     * <pre>
     **国家旗帜
     * </pre>
     *
     * <code>NATION_FLAG = 38;</code>
     */
    NATION_FLAG(38),
    /**
     * <pre>
     **国家旗帜id
     * </pre>
     *
     * <code>NATION_FLAGID = 39;</code>
     */
    NATION_FLAGID(39),
    /**
     * <pre>
     **当前服务器ID
     * </pre>
     *
     * <code>CURRENT_SERVER_ID = 40;</code>
     */
    CURRENT_SERVER_ID(40),
    /**
     * <pre>
     **国家ID
     * </pre>
     *
     * <code>NATION_ID = 41;</code>
     */
    NATION_ID(41),
    /**
     * <pre>
     **国家等级
     * </pre>
     *
     * <code>NATION_LEVEL = 42;</code>
     */
    NATION_LEVEL(42),
    /**
     * <pre>
     ** 专武币（藏兵阁掉落）
     * </pre>
     *
     * <code>ACTOR_WEAPON_COIN = 43;</code>
     */
    ACTOR_WEAPON_COIN(43),
    /**
     * <pre>
     ** 在线组队TD币（赏金） 
     * </pre>
     *
     * <code>ACTOR_TEAM_ONLINE_TD_COIN = 44;</code>
     */
    ACTOR_TEAM_ONLINE_TD_COIN(44),
    /**
     * <pre>
     ** 个人签名 
     * </pre>
     *
     * <code>AUTOGRAPH = 45;</code>
     */
    AUTOGRAPH(45),
    /**
     * <pre>
     ** 头像框ID 
     * </pre>
     *
     * <code>AVATAR_FRAME_ID = 46;</code>
     */
    AVATAR_FRAME_ID(46),
    /**
     * <pre>
     ** 头像框列表 
     * </pre>
     *
     * <code>AVATAR_FRAME_LIST = 47;</code>
     */
    AVATAR_FRAME_LIST(47),
    /**
     * <pre>
     ** 聊天气泡ID 
     * </pre>
     *
     * <code>CHAT_BUBBLE_ID = 48;</code>
     */
    CHAT_BUBBLE_ID(48),
    /**
     * <pre>
     ** 聊天气泡框列表 
     * </pre>
     *
     * <code>CHAT_BUBBLE_LIST = 49;</code>
     */
    CHAT_BUBBLE_LIST(49),
    /**
     * <pre>
     ** 友情点
     * </pre>
     *
     * <code>ACTOR_FRIEND_POINT = 50;</code>
     */
    ACTOR_FRIEND_POINT(50),
    /**
     * <pre>
     ** 军团币 
     * </pre>
     *
     * <code>ACTOR_NATION_COIN = 51;</code>
     */
    ACTOR_NATION_COIN(51),
    /**
     * <pre>
     ** 军团经验
     * </pre>
     *
     * <code>ACTOR_NATION_EXP = 52;</code>
     */
    ACTOR_NATION_EXP(52),
    /**
     * <pre>
     ** 军团贡献
     * </pre>
     *
     * <code>ACTOR_NATION_CONTRIBUTION = 53;</code>
     */
    ACTOR_NATION_CONTRIBUTION(53),
    /**
     * <pre>
     ** VIP经验 
     * </pre>
     *
     * <code>ACTOR_VIP_EXP = 54;</code>
     */
    ACTOR_VIP_EXP(54),
    /**
     * <pre>
     **名将币（名将塔掉落）
     * </pre>
     *
     * <code>ACTOR_TOWER_COIN = 55;</code>
     */
    ACTOR_TOWER_COIN(55),
    /**
     * <pre>
     **无尽币（无尽试炼掉落）
     * </pre>
     *
     * <code>ACTOR_ENDLESS_COIN = 56;</code>
     */
    ACTOR_ENDLESS_COIN(56),
    /**
     * <pre>
     ** 剧情ID列表 
     * </pre>
     *
     * <code>PLOT_ID_LIST = 57;</code>
     */
    PLOT_ID_LIST(57),
    /**
     * <pre>
     ** 魔窟单人解锁状态 
     * </pre>
     *
     * <code>CAVE_SINGLE_CHALLENGE_LIST = 58;</code>
     */
    CAVE_SINGLE_CHALLENGE_LIST(58),
    /**
     * <pre>
     ** 战魂玉(组队魔窟掉落) 
     * </pre>
     *
     * <code>ACTOR_CAVE_COIN = 59;</code>
     */
    ACTOR_CAVE_COIN(59),
    /**
     * <pre>
     ** 官职 
     * </pre>
     *
     * <code>ACTOR_OFFICIAL = 60;</code>
     */
    ACTOR_OFFICIAL(60),
    /**
     * <pre>
     ** 攻城略地积分  
     * </pre>
     *
     * <code>ACTOR_CAPTURE_COIN = 61;</code>
     */
    ACTOR_CAPTURE_COIN(61),
    /**
     * <pre>
     ** 称号 
     * </pre>
     *
     * <code>TITLE_ID = 63;</code>
     */
    TITLE_ID(63),
    /**
     * <pre>
     ** 巅峰竞技场货币 
     * </pre>
     *
     * <code>ACTOR_CROSS_CUP_COIN = 64;</code>
     */
    ACTOR_CROSS_CUP_COIN(64),
    /**
     * <pre>
     ** 第三方帐号ID 
     * </pre>
     *
     * <code>SDK_USER_ID = 65;</code>
     */
    SDK_USER_ID(65),
    /**
     * <pre>
     ** 成长任务 
     * </pre>
     *
     * <code>GROWTH_TASK = 66;</code>
     */
    GROWTH_TASK(66),
    /**
     * <pre>
     ** 群雄币 
     * </pre>
     *
     * <code>ACTOR_HEGEMONY_COIN = 67;</code>
     */
    ACTOR_HEGEMONY_COIN(67),
    /**
     * <pre>
     ** 形象专武状态 
     * </pre>
     *
     * <code>SHOW_IMMORTALS = 68;</code>
     */
    SHOW_IMMORTALS(68),
    /**
     * <pre>
     ** 形象英雄星级（小星级） 
     * </pre>
     *
     * <code>SHOW_HERO_STAR = 69;</code>
     */
    SHOW_HERO_STAR(69),
    /**
     * <pre>
     ** 王城币 
     * </pre>
     *
     * <code>ACTOR_GVG_COIN = 70;</code>
     */
    ACTOR_GVG_COIN(70),
    /**
     * <pre>
     ** 活动31任务代币 
     * </pre>
     *
     * <code>ACTOR_ACTIVITY_31_COIN = 71;</code>
     */
    ACTOR_ACTIVITY_31_COIN(71),
    /**
     * <pre>
     ** 镖银 
     * </pre>
     *
     * <code>ACTOR_ESCORT_COIN = 72;</code>
     */
    ACTOR_ESCORT_COIN(72),
    /**
     * <pre>
     ** 棋魂 
     * </pre>
     *
     * <code>ACTOR_CHESS_2_COIN = 73;</code>
     */
    ACTOR_CHESS_2_COIN(73),
    /**
     * <pre>
     ** 战魂币 
     * </pre>
     *
     * <code>ACTOR_QINRACE_COIN = 74;</code>
     */
    ACTOR_QINRACE_COIN(74),
    /**
     * <pre>
     ** 被点赞数量 {rankType:praisedCount}
     * </pre>
     *
     * <code>PRAISED_COUNT_MAP = 75;</code>
     */
    PRAISED_COUNT_MAP(75),
    /**
     * <pre>
     ** 英雄幻彩重生次数 
     * </pre>
     *
     * <code>HERO_COLOR_REBIRTH_TIMES = 76;</code>
     */
    HERO_COLOR_REBIRTH_TIMES(76),
    /**
     * <pre>
     ** 专武幻彩重生次数 
     * </pre>
     *
     * <code>IMMORTALS_COLOR_REBIRTH_TIMES = 77;</code>
     */
    IMMORTALS_COLOR_REBIRTH_TIMES(77),
    /**
     * <pre>
     ** showId幻彩等级 
     * </pre>
     *
     * <code>SHOW_HERO_COLOR_LEVEL = 78;</code>
     */
    SHOW_HERO_COLOR_LEVEL(78),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>ACTOR_FIELD_TYPE_NONE = 0;</code>
     */
    public static final int ACTOR_FIELD_TYPE_NONE_VALUE = 0;
    /**
     * <pre>
     ** 用户ID 
     * </pre>
     *
     * <code>UID = 1;</code>
     */
    public static final int UID_VALUE = 1;
    /**
     * <pre>
     ** 平台ID 
     * </pre>
     *
     * <code>PLATFORM_ID = 2;</code>
     */
    public static final int PLATFORM_ID_VALUE = 2;
    /**
     * <pre>
     ** 渠道ID 
     * </pre>
     *
     * <code>CHANNEL = 3;</code>
     */
    public static final int CHANNEL_VALUE = 3;
    /**
     * <pre>
     ** 服务器ID 
     * </pre>
     *
     * <code>SERVER_ID = 4;</code>
     */
    public static final int SERVER_ID_VALUE = 4;
    /**
     * <pre>
     ** 角色ID 
     * </pre>
     *
     * <code>ACTOR_ID = 5;</code>
     */
    public static final int ACTOR_ID_VALUE = 5;
    /**
     * <pre>
     ** 角色名称 
     * </pre>
     *
     * <code>ACTOR_NAME = 6;</code>
     */
    public static final int ACTOR_NAME_VALUE = 6;
    /**
     * <pre>
     ** 性别 
     * </pre>
     *
     * <code>SEX = 7;</code>
     */
    public static final int SEX_VALUE = 7;
    /**
     * <pre>
     ** 角色等级 
     * </pre>
     *
     * <code>ACTOR_LEVEL = 8;</code>
     */
    public static final int ACTOR_LEVEL_VALUE = 8;
    /**
     * <pre>
     ** 角色经验 
     * </pre>
     *
     * <code>ACTOR_EXP = 9;</code>
     */
    public static final int ACTOR_EXP_VALUE = 9;
    /**
     * <pre>
     ** 金币 
     * </pre>
     *
     * <code>ACTOR_GOLD = 10;</code>
     */
    public static final int ACTOR_GOLD_VALUE = 10;
    /**
     * <pre>
     ** 钻石 
     * </pre>
     *
     * <code>ACTOR_DIAMOND = 11;</code>
     */
    public static final int ACTOR_DIAMOND_VALUE = 11;
    /**
     * <pre>
     ** vip等级 
     * </pre>
     *
     * <code>VIP_LEVEL = 12;</code>
     */
    public static final int VIP_LEVEL_VALUE = 12;
    /**
     * <pre>
     ** 头像ID 
     * </pre>
     *
     * <code>AVATAR_ID = 13;</code>
     */
    public static final int AVATAR_ID_VALUE = 13;
    /**
     * <pre>
     ** 新手引导ID列表 
     * </pre>
     *
     * <code>GUIDE_ID_LIST = 14;</code>
     */
    public static final int GUIDE_ID_LIST_VALUE = 14;
    /**
     * <pre>
     ** 充值数 
     * </pre>
     *
     * <code>RECHARGE = 15;</code>
     */
    public static final int RECHARGE_VALUE = 15;
    /**
     * <pre>
     ** 最后一次改名时间 
     * </pre>
     *
     * <code>LAST_RENAME_TIME = 16;</code>
     */
    public static final int LAST_RENAME_TIME_VALUE = 16;
    /**
     * <pre>
     ** 战力 
     * </pre>
     *
     * <code>ACTOR_POWER = 17;</code>
     */
    public static final int ACTOR_POWER_VALUE = 17;
    /**
     * <pre>
     ** 赠送的充值数 
     * </pre>
     *
     * <code>PRESENT = 18;</code>
     */
    public static final int PRESENT_VALUE = 18;
    /**
     * <pre>
     ** VIP卡信息 
     * </pre>
     *
     * <code>VIP_CARDS = 19;</code>
     */
    public static final int VIP_CARDS_VALUE = 19;
    /**
     * <pre>
     ** 精力 
     * </pre>
     *
     * <code>ACTOR_VIGOUR = 20;</code>
     */
    public static final int ACTOR_VIGOUR_VALUE = 20;
    /**
     * <pre>
     ** 体力 
     * </pre>
     *
     * <code>ACTOR_ENERGY = 21;</code>
     */
    public static final int ACTOR_ENERGY_VALUE = 21;
    /**
     * <pre>
     ** 头像列表 
     * </pre>
     *
     * <code>AVATAR_LIST = 22;</code>
     */
    public static final int AVATAR_LIST_VALUE = 22;
    /**
     * <pre>
     ** 形象列表 
     * </pre>
     *
     * <code>SHOW_LIST = 23;</code>
     */
    public static final int SHOW_LIST_VALUE = 23;
    /**
     * <pre>
     ** 最后一次恢复精力时间 
     * </pre>
     *
     * <code>LAST_REFRESH_VIGOUR_TIME = 24;</code>
     */
    public static final int LAST_REFRESH_VIGOUR_TIME_VALUE = 24;
    /**
     * <pre>
     ** 最后一次恢复体力 
     * </pre>
     *
     * <code>LAST_REFRESH_ENERGY_TIME = 25;</code>
     */
    public static final int LAST_REFRESH_ENERGY_TIME_VALUE = 25;
    /**
     * <pre>
     ** 用户创建时间 
     * </pre>
     *
     * <code>ACTOR_CREATE_TIME = 26;</code>
     */
    public static final int ACTOR_CREATE_TIME_VALUE = 26;
    /**
     * <pre>
     ** 累计登陆第几天 
     * </pre>
     *
     * <code>LOGIN_TOTAL_DAY = 27;</code>
     */
    public static final int LOGIN_TOTAL_DAY_VALUE = 27;
    /**
     * <pre>
     ** 形象ID 
     * </pre>
     *
     * <code>SHOW_ID = 28;</code>
     */
    public static final int SHOW_ID_VALUE = 28;
    /**
     * <pre>
     ** 竞技场排名(历史最高) 
     * </pre>
     *
     * <code>ARENA_MAX_RANK = 29;</code>
     */
    public static final int ARENA_MAX_RANK_VALUE = 29;
    /**
     * <pre>
     ** 关卡ID 
     * </pre>
     *
     * <code>STORY_ID = 30;</code>
     */
    public static final int STORY_ID_VALUE = 30;
    /**
     * <pre>
     ** 历史最高战力 
     * </pre>
     *
     * <code>MAX_POWER = 31;</code>
     */
    public static final int MAX_POWER_VALUE = 31;
    /**
     * <pre>
     ** 地宫币
     * </pre>
     *
     * <code>ACTOR_CHESS_COIN = 32;</code>
     */
    public static final int ACTOR_CHESS_COIN_VALUE = 32;
    /**
     * <pre>
     ** 竞技币
     * </pre>
     *
     * <code>ACTOR_ARENA_COIN = 33;</code>
     */
    public static final int ACTOR_ARENA_COIN_VALUE = 33;
    /**
     * <pre>
     **点赞状态
     * </pre>
     *
     * <code>PRAISE_STATE = 34;</code>
     */
    public static final int PRAISE_STATE_VALUE = 34;
    /**
     * <pre>
     **国家名称
     * </pre>
     *
     * <code>NATION_NAME = 35;</code>
     */
    public static final int NATION_NAME_VALUE = 35;
    /**
     * <pre>
     **在线状态
     * </pre>
     *
     * <code>ONLINE_STATE = 36;</code>
     */
    public static final int ONLINE_STATE_VALUE = 36;
    /**
     * <pre>
     **最后一次退出时间
     * </pre>
     *
     * <code>LAST_LOGOUT_TIME = 37;</code>
     */
    public static final int LAST_LOGOUT_TIME_VALUE = 37;
    /**
     * <pre>
     **国家旗帜
     * </pre>
     *
     * <code>NATION_FLAG = 38;</code>
     */
    public static final int NATION_FLAG_VALUE = 38;
    /**
     * <pre>
     **国家旗帜id
     * </pre>
     *
     * <code>NATION_FLAGID = 39;</code>
     */
    public static final int NATION_FLAGID_VALUE = 39;
    /**
     * <pre>
     **当前服务器ID
     * </pre>
     *
     * <code>CURRENT_SERVER_ID = 40;</code>
     */
    public static final int CURRENT_SERVER_ID_VALUE = 40;
    /**
     * <pre>
     **国家ID
     * </pre>
     *
     * <code>NATION_ID = 41;</code>
     */
    public static final int NATION_ID_VALUE = 41;
    /**
     * <pre>
     **国家等级
     * </pre>
     *
     * <code>NATION_LEVEL = 42;</code>
     */
    public static final int NATION_LEVEL_VALUE = 42;
    /**
     * <pre>
     ** 专武币（藏兵阁掉落）
     * </pre>
     *
     * <code>ACTOR_WEAPON_COIN = 43;</code>
     */
    public static final int ACTOR_WEAPON_COIN_VALUE = 43;
    /**
     * <pre>
     ** 在线组队TD币（赏金） 
     * </pre>
     *
     * <code>ACTOR_TEAM_ONLINE_TD_COIN = 44;</code>
     */
    public static final int ACTOR_TEAM_ONLINE_TD_COIN_VALUE = 44;
    /**
     * <pre>
     ** 个人签名 
     * </pre>
     *
     * <code>AUTOGRAPH = 45;</code>
     */
    public static final int AUTOGRAPH_VALUE = 45;
    /**
     * <pre>
     ** 头像框ID 
     * </pre>
     *
     * <code>AVATAR_FRAME_ID = 46;</code>
     */
    public static final int AVATAR_FRAME_ID_VALUE = 46;
    /**
     * <pre>
     ** 头像框列表 
     * </pre>
     *
     * <code>AVATAR_FRAME_LIST = 47;</code>
     */
    public static final int AVATAR_FRAME_LIST_VALUE = 47;
    /**
     * <pre>
     ** 聊天气泡ID 
     * </pre>
     *
     * <code>CHAT_BUBBLE_ID = 48;</code>
     */
    public static final int CHAT_BUBBLE_ID_VALUE = 48;
    /**
     * <pre>
     ** 聊天气泡框列表 
     * </pre>
     *
     * <code>CHAT_BUBBLE_LIST = 49;</code>
     */
    public static final int CHAT_BUBBLE_LIST_VALUE = 49;
    /**
     * <pre>
     ** 友情点
     * </pre>
     *
     * <code>ACTOR_FRIEND_POINT = 50;</code>
     */
    public static final int ACTOR_FRIEND_POINT_VALUE = 50;
    /**
     * <pre>
     ** 军团币 
     * </pre>
     *
     * <code>ACTOR_NATION_COIN = 51;</code>
     */
    public static final int ACTOR_NATION_COIN_VALUE = 51;
    /**
     * <pre>
     ** 军团经验
     * </pre>
     *
     * <code>ACTOR_NATION_EXP = 52;</code>
     */
    public static final int ACTOR_NATION_EXP_VALUE = 52;
    /**
     * <pre>
     ** 军团贡献
     * </pre>
     *
     * <code>ACTOR_NATION_CONTRIBUTION = 53;</code>
     */
    public static final int ACTOR_NATION_CONTRIBUTION_VALUE = 53;
    /**
     * <pre>
     ** VIP经验 
     * </pre>
     *
     * <code>ACTOR_VIP_EXP = 54;</code>
     */
    public static final int ACTOR_VIP_EXP_VALUE = 54;
    /**
     * <pre>
     **名将币（名将塔掉落）
     * </pre>
     *
     * <code>ACTOR_TOWER_COIN = 55;</code>
     */
    public static final int ACTOR_TOWER_COIN_VALUE = 55;
    /**
     * <pre>
     **无尽币（无尽试炼掉落）
     * </pre>
     *
     * <code>ACTOR_ENDLESS_COIN = 56;</code>
     */
    public static final int ACTOR_ENDLESS_COIN_VALUE = 56;
    /**
     * <pre>
     ** 剧情ID列表 
     * </pre>
     *
     * <code>PLOT_ID_LIST = 57;</code>
     */
    public static final int PLOT_ID_LIST_VALUE = 57;
    /**
     * <pre>
     ** 魔窟单人解锁状态 
     * </pre>
     *
     * <code>CAVE_SINGLE_CHALLENGE_LIST = 58;</code>
     */
    public static final int CAVE_SINGLE_CHALLENGE_LIST_VALUE = 58;
    /**
     * <pre>
     ** 战魂玉(组队魔窟掉落) 
     * </pre>
     *
     * <code>ACTOR_CAVE_COIN = 59;</code>
     */
    public static final int ACTOR_CAVE_COIN_VALUE = 59;
    /**
     * <pre>
     ** 官职 
     * </pre>
     *
     * <code>ACTOR_OFFICIAL = 60;</code>
     */
    public static final int ACTOR_OFFICIAL_VALUE = 60;
    /**
     * <pre>
     ** 攻城略地积分  
     * </pre>
     *
     * <code>ACTOR_CAPTURE_COIN = 61;</code>
     */
    public static final int ACTOR_CAPTURE_COIN_VALUE = 61;
    /**
     * <pre>
     ** 称号 
     * </pre>
     *
     * <code>TITLE_ID = 63;</code>
     */
    public static final int TITLE_ID_VALUE = 63;
    /**
     * <pre>
     ** 巅峰竞技场货币 
     * </pre>
     *
     * <code>ACTOR_CROSS_CUP_COIN = 64;</code>
     */
    public static final int ACTOR_CROSS_CUP_COIN_VALUE = 64;
    /**
     * <pre>
     ** 第三方帐号ID 
     * </pre>
     *
     * <code>SDK_USER_ID = 65;</code>
     */
    public static final int SDK_USER_ID_VALUE = 65;
    /**
     * <pre>
     ** 成长任务 
     * </pre>
     *
     * <code>GROWTH_TASK = 66;</code>
     */
    public static final int GROWTH_TASK_VALUE = 66;
    /**
     * <pre>
     ** 群雄币 
     * </pre>
     *
     * <code>ACTOR_HEGEMONY_COIN = 67;</code>
     */
    public static final int ACTOR_HEGEMONY_COIN_VALUE = 67;
    /**
     * <pre>
     ** 形象专武状态 
     * </pre>
     *
     * <code>SHOW_IMMORTALS = 68;</code>
     */
    public static final int SHOW_IMMORTALS_VALUE = 68;
    /**
     * <pre>
     ** 形象英雄星级（小星级） 
     * </pre>
     *
     * <code>SHOW_HERO_STAR = 69;</code>
     */
    public static final int SHOW_HERO_STAR_VALUE = 69;
    /**
     * <pre>
     ** 王城币 
     * </pre>
     *
     * <code>ACTOR_GVG_COIN = 70;</code>
     */
    public static final int ACTOR_GVG_COIN_VALUE = 70;
    /**
     * <pre>
     ** 活动31任务代币 
     * </pre>
     *
     * <code>ACTOR_ACTIVITY_31_COIN = 71;</code>
     */
    public static final int ACTOR_ACTIVITY_31_COIN_VALUE = 71;
    /**
     * <pre>
     ** 镖银 
     * </pre>
     *
     * <code>ACTOR_ESCORT_COIN = 72;</code>
     */
    public static final int ACTOR_ESCORT_COIN_VALUE = 72;
    /**
     * <pre>
     ** 棋魂 
     * </pre>
     *
     * <code>ACTOR_CHESS_2_COIN = 73;</code>
     */
    public static final int ACTOR_CHESS_2_COIN_VALUE = 73;
    /**
     * <pre>
     ** 战魂币 
     * </pre>
     *
     * <code>ACTOR_QINRACE_COIN = 74;</code>
     */
    public static final int ACTOR_QINRACE_COIN_VALUE = 74;
    /**
     * <pre>
     ** 被点赞数量 {rankType:praisedCount}
     * </pre>
     *
     * <code>PRAISED_COUNT_MAP = 75;</code>
     */
    public static final int PRAISED_COUNT_MAP_VALUE = 75;
    /**
     * <pre>
     ** 英雄幻彩重生次数 
     * </pre>
     *
     * <code>HERO_COLOR_REBIRTH_TIMES = 76;</code>
     */
    public static final int HERO_COLOR_REBIRTH_TIMES_VALUE = 76;
    /**
     * <pre>
     ** 专武幻彩重生次数 
     * </pre>
     *
     * <code>IMMORTALS_COLOR_REBIRTH_TIMES = 77;</code>
     */
    public static final int IMMORTALS_COLOR_REBIRTH_TIMES_VALUE = 77;
    /**
     * <pre>
     ** showId幻彩等级 
     * </pre>
     *
     * <code>SHOW_HERO_COLOR_LEVEL = 78;</code>
     */
    public static final int SHOW_HERO_COLOR_LEVEL_VALUE = 78;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static ActorFieldType valueOf(int value) {
      return forNumber(value);
    }

    public static ActorFieldType forNumber(int value) {
      switch (value) {
        case 0: return ACTOR_FIELD_TYPE_NONE;
        case 1: return UID;
        case 2: return PLATFORM_ID;
        case 3: return CHANNEL;
        case 4: return SERVER_ID;
        case 5: return ACTOR_ID;
        case 6: return ACTOR_NAME;
        case 7: return SEX;
        case 8: return ACTOR_LEVEL;
        case 9: return ACTOR_EXP;
        case 10: return ACTOR_GOLD;
        case 11: return ACTOR_DIAMOND;
        case 12: return VIP_LEVEL;
        case 13: return AVATAR_ID;
        case 14: return GUIDE_ID_LIST;
        case 15: return RECHARGE;
        case 16: return LAST_RENAME_TIME;
        case 17: return ACTOR_POWER;
        case 18: return PRESENT;
        case 19: return VIP_CARDS;
        case 20: return ACTOR_VIGOUR;
        case 21: return ACTOR_ENERGY;
        case 22: return AVATAR_LIST;
        case 23: return SHOW_LIST;
        case 24: return LAST_REFRESH_VIGOUR_TIME;
        case 25: return LAST_REFRESH_ENERGY_TIME;
        case 26: return ACTOR_CREATE_TIME;
        case 27: return LOGIN_TOTAL_DAY;
        case 28: return SHOW_ID;
        case 29: return ARENA_MAX_RANK;
        case 30: return STORY_ID;
        case 31: return MAX_POWER;
        case 32: return ACTOR_CHESS_COIN;
        case 33: return ACTOR_ARENA_COIN;
        case 34: return PRAISE_STATE;
        case 35: return NATION_NAME;
        case 36: return ONLINE_STATE;
        case 37: return LAST_LOGOUT_TIME;
        case 38: return NATION_FLAG;
        case 39: return NATION_FLAGID;
        case 40: return CURRENT_SERVER_ID;
        case 41: return NATION_ID;
        case 42: return NATION_LEVEL;
        case 43: return ACTOR_WEAPON_COIN;
        case 44: return ACTOR_TEAM_ONLINE_TD_COIN;
        case 45: return AUTOGRAPH;
        case 46: return AVATAR_FRAME_ID;
        case 47: return AVATAR_FRAME_LIST;
        case 48: return CHAT_BUBBLE_ID;
        case 49: return CHAT_BUBBLE_LIST;
        case 50: return ACTOR_FRIEND_POINT;
        case 51: return ACTOR_NATION_COIN;
        case 52: return ACTOR_NATION_EXP;
        case 53: return ACTOR_NATION_CONTRIBUTION;
        case 54: return ACTOR_VIP_EXP;
        case 55: return ACTOR_TOWER_COIN;
        case 56: return ACTOR_ENDLESS_COIN;
        case 57: return PLOT_ID_LIST;
        case 58: return CAVE_SINGLE_CHALLENGE_LIST;
        case 59: return ACTOR_CAVE_COIN;
        case 60: return ACTOR_OFFICIAL;
        case 61: return ACTOR_CAPTURE_COIN;
        case 63: return TITLE_ID;
        case 64: return ACTOR_CROSS_CUP_COIN;
        case 65: return SDK_USER_ID;
        case 66: return GROWTH_TASK;
        case 67: return ACTOR_HEGEMONY_COIN;
        case 68: return SHOW_IMMORTALS;
        case 69: return SHOW_HERO_STAR;
        case 70: return ACTOR_GVG_COIN;
        case 71: return ACTOR_ACTIVITY_31_COIN;
        case 72: return ACTOR_ESCORT_COIN;
        case 73: return ACTOR_CHESS_2_COIN;
        case 74: return ACTOR_QINRACE_COIN;
        case 75: return PRAISED_COUNT_MAP;
        case 76: return HERO_COLOR_REBIRTH_TIMES;
        case 77: return IMMORTALS_COLOR_REBIRTH_TIMES;
        case 78: return SHOW_HERO_COLOR_LEVEL;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<ActorFieldType>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        ActorFieldType> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<ActorFieldType>() {
            public ActorFieldType findValueByNumber(int number) {
              return ActorFieldType.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return cn.daxiang.protocol.battle.BattleTypeProtocol.getDescriptor().getEnumTypes().get(0);
    }

    private static final ActorFieldType[] VALUES = values();

    public static ActorFieldType valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private ActorFieldType(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:ActorFieldType)
  }

  /**
   * <pre>
   **
   * 塔防类型
   * </pre>
   *
   * Protobuf enum {@code TeamTdType}
   */
  public enum TeamTdType
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>TEAM_TD_TYPE_NONE = 0;</code>
     */
    TEAM_TD_TYPE_NONE(0),
    /**
     * <pre>
     **
     * 1.在线TD
     * value:在线TD关卡ID
     * </pre>
     *
     * <code>ONLINE_TD = 1;</code>
     */
    ONLINE_TD(1),
    /**
     * <pre>
     **
     * 2.双人塔防锦标赛
     * value:难度
     * </pre>
     *
     * <code>ACTIVITY_55 = 2;</code>
     */
    ACTIVITY_55(2),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>TEAM_TD_TYPE_NONE = 0;</code>
     */
    public static final int TEAM_TD_TYPE_NONE_VALUE = 0;
    /**
     * <pre>
     **
     * 1.在线TD
     * value:在线TD关卡ID
     * </pre>
     *
     * <code>ONLINE_TD = 1;</code>
     */
    public static final int ONLINE_TD_VALUE = 1;
    /**
     * <pre>
     **
     * 2.双人塔防锦标赛
     * value:难度
     * </pre>
     *
     * <code>ACTIVITY_55 = 2;</code>
     */
    public static final int ACTIVITY_55_VALUE = 2;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static TeamTdType valueOf(int value) {
      return forNumber(value);
    }

    public static TeamTdType forNumber(int value) {
      switch (value) {
        case 0: return TEAM_TD_TYPE_NONE;
        case 1: return ONLINE_TD;
        case 2: return ACTIVITY_55;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<TeamTdType>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        TeamTdType> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<TeamTdType>() {
            public TeamTdType findValueByNumber(int number) {
              return TeamTdType.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return cn.daxiang.protocol.battle.BattleTypeProtocol.getDescriptor().getEnumTypes().get(1);
    }

    private static final TeamTdType[] VALUES = values();

    public static TeamTdType valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private TeamTdType(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:TeamTdType)
  }

  /**
   * <pre>
   **
   * 塔防组队加入类型
   * </pre>
   *
   * Protobuf enum {@code TeamTdJoinLimitType}
   */
  public enum TeamTdJoinLimitType
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>TEAM_TD_JOIN_LIMIT_TYPE_NONE = 0;</code>
     */
    TEAM_TD_JOIN_LIMIT_TYPE_NONE(0),
    /**
     * <pre>
     **
     * 1.口令
     * </pre>
     *
     * <code>CODE = 1;</code>
     */
    CODE(1),
    /**
     * <pre>
     **
     * 2.战力
     * </pre>
     *
     * <code>POWER = 2;</code>
     */
    POWER(2),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>TEAM_TD_JOIN_LIMIT_TYPE_NONE = 0;</code>
     */
    public static final int TEAM_TD_JOIN_LIMIT_TYPE_NONE_VALUE = 0;
    /**
     * <pre>
     **
     * 1.口令
     * </pre>
     *
     * <code>CODE = 1;</code>
     */
    public static final int CODE_VALUE = 1;
    /**
     * <pre>
     **
     * 2.战力
     * </pre>
     *
     * <code>POWER = 2;</code>
     */
    public static final int POWER_VALUE = 2;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static TeamTdJoinLimitType valueOf(int value) {
      return forNumber(value);
    }

    public static TeamTdJoinLimitType forNumber(int value) {
      switch (value) {
        case 0: return TEAM_TD_JOIN_LIMIT_TYPE_NONE;
        case 1: return CODE;
        case 2: return POWER;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<TeamTdJoinLimitType>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        TeamTdJoinLimitType> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<TeamTdJoinLimitType>() {
            public TeamTdJoinLimitType findValueByNumber(int number) {
              return TeamTdJoinLimitType.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return cn.daxiang.protocol.battle.BattleTypeProtocol.getDescriptor().getEnumTypes().get(2);
    }

    private static final TeamTdJoinLimitType[] VALUES = values();

    public static TeamTdJoinLimitType valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private TeamTdJoinLimitType(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:TeamTdJoinLimitType)
  }

  /**
   * <pre>
   **
   * 塔防组队队伍状态
   * </pre>
   *
   * Protobuf enum {@code TeamTdState}
   */
  public enum TeamTdState
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>TEAM_TD_STATE_NONE = 0;</code>
     */
    TEAM_TD_STATE_NONE(0),
    /**
     * <pre>
     **
     * 1.空闲
     * </pre>
     *
     * <code>IDLE = 1;</code>
     */
    IDLE(1),
    /**
     * <pre>
     **
     * 2.战斗
     * </pre>
     *
     * <code>BATTLE = 2;</code>
     */
    BATTLE(2),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>TEAM_TD_STATE_NONE = 0;</code>
     */
    public static final int TEAM_TD_STATE_NONE_VALUE = 0;
    /**
     * <pre>
     **
     * 1.空闲
     * </pre>
     *
     * <code>IDLE = 1;</code>
     */
    public static final int IDLE_VALUE = 1;
    /**
     * <pre>
     **
     * 2.战斗
     * </pre>
     *
     * <code>BATTLE = 2;</code>
     */
    public static final int BATTLE_VALUE = 2;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static TeamTdState valueOf(int value) {
      return forNumber(value);
    }

    public static TeamTdState forNumber(int value) {
      switch (value) {
        case 0: return TEAM_TD_STATE_NONE;
        case 1: return IDLE;
        case 2: return BATTLE;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<TeamTdState>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        TeamTdState> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<TeamTdState>() {
            public TeamTdState findValueByNumber(int number) {
              return TeamTdState.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return cn.daxiang.protocol.battle.BattleTypeProtocol.getDescriptor().getEnumTypes().get(3);
    }

    private static final TeamTdState[] VALUES = values();

    public static TeamTdState valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private TeamTdState(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:TeamTdState)
  }

  /**
   * <pre>
   **
   * 塔防组队成员类型
   * </pre>
   *
   * Protobuf enum {@code TeamTdMemberType}
   */
  public enum TeamTdMemberType
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>TEAM_TD_MEMBER_TYPE_NONE = 0;</code>
     */
    TEAM_TD_MEMBER_TYPE_NONE(0),
    /**
     * <pre>
     **
     * 1.队长
     * </pre>
     *
     * <code>CAPTAIN = 1;</code>
     */
    CAPTAIN(1),
    /**
     * <pre>
     **
     * 2.成员
     * </pre>
     *
     * <code>MEMBER = 2;</code>
     */
    MEMBER(2),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>TEAM_TD_MEMBER_TYPE_NONE = 0;</code>
     */
    public static final int TEAM_TD_MEMBER_TYPE_NONE_VALUE = 0;
    /**
     * <pre>
     **
     * 1.队长
     * </pre>
     *
     * <code>CAPTAIN = 1;</code>
     */
    public static final int CAPTAIN_VALUE = 1;
    /**
     * <pre>
     **
     * 2.成员
     * </pre>
     *
     * <code>MEMBER = 2;</code>
     */
    public static final int MEMBER_VALUE = 2;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static TeamTdMemberType valueOf(int value) {
      return forNumber(value);
    }

    public static TeamTdMemberType forNumber(int value) {
      switch (value) {
        case 0: return TEAM_TD_MEMBER_TYPE_NONE;
        case 1: return CAPTAIN;
        case 2: return MEMBER;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<TeamTdMemberType>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        TeamTdMemberType> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<TeamTdMemberType>() {
            public TeamTdMemberType findValueByNumber(int number) {
              return TeamTdMemberType.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return cn.daxiang.protocol.battle.BattleTypeProtocol.getDescriptor().getEnumTypes().get(4);
    }

    private static final TeamTdMemberType[] VALUES = values();

    public static TeamTdMemberType valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private TeamTdMemberType(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:TeamTdMemberType)
  }

  /**
   * <pre>
   ** 战斗科技类型 
   * </pre>
   *
   * Protobuf enum {@code BattleTechnologyType}
   */
  public enum BattleTechnologyType
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>BATTLE_TECHNOLOGY_TYPE_NONE = 0;</code>
     */
    BATTLE_TECHNOLOGY_TYPE_NONE(0),
    /**
     * <pre>
     **
     * 1.人口
     * </pre>
     *
     * <code>FOOD = 1;</code>
     */
    FOOD(1),
    /**
     * <pre>
     **
     * 2.武魂(妖气)增长速度
     * </pre>
     *
     * <code>DEMON_BREATHE_SPEED = 2;</code>
     */
    DEMON_BREATHE_SPEED(2),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>BATTLE_TECHNOLOGY_TYPE_NONE = 0;</code>
     */
    public static final int BATTLE_TECHNOLOGY_TYPE_NONE_VALUE = 0;
    /**
     * <pre>
     **
     * 1.人口
     * </pre>
     *
     * <code>FOOD = 1;</code>
     */
    public static final int FOOD_VALUE = 1;
    /**
     * <pre>
     **
     * 2.武魂(妖气)增长速度
     * </pre>
     *
     * <code>DEMON_BREATHE_SPEED = 2;</code>
     */
    public static final int DEMON_BREATHE_SPEED_VALUE = 2;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static BattleTechnologyType valueOf(int value) {
      return forNumber(value);
    }

    public static BattleTechnologyType forNumber(int value) {
      switch (value) {
        case 0: return BATTLE_TECHNOLOGY_TYPE_NONE;
        case 1: return FOOD;
        case 2: return DEMON_BREATHE_SPEED;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<BattleTechnologyType>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        BattleTechnologyType> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<BattleTechnologyType>() {
            public BattleTechnologyType findValueByNumber(int number) {
              return BattleTechnologyType.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return cn.daxiang.protocol.battle.BattleTypeProtocol.getDescriptor().getEnumTypes().get(5);
    }

    private static final BattleTechnologyType[] VALUES = values();

    public static BattleTechnologyType valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private BattleTechnologyType(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:BattleTechnologyType)
  }

  /**
   * <pre>
   ** 战斗奖励类型 
   * </pre>
   *
   * Protobuf enum {@code BattleRewardType}
   */
  public enum BattleRewardType
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>BATTLE_REWARD_TYPE_NONE = 0;</code>
     */
    BATTLE_REWARD_TYPE_NONE(0),
    /**
     * <pre>
     **
     * 1.将令（灵丹）
     * </pre>
     *
     * <code>PANACEA = 1;</code>
     */
    PANACEA(1),
    /**
     * <pre>
     **
     * 2.武魂（妖气）
     * </pre>
     *
     * <code>DEMON_BREATHE = 2;</code>
     */
    DEMON_BREATHE(2),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>BATTLE_REWARD_TYPE_NONE = 0;</code>
     */
    public static final int BATTLE_REWARD_TYPE_NONE_VALUE = 0;
    /**
     * <pre>
     **
     * 1.将令（灵丹）
     * </pre>
     *
     * <code>PANACEA = 1;</code>
     */
    public static final int PANACEA_VALUE = 1;
    /**
     * <pre>
     **
     * 2.武魂（妖气）
     * </pre>
     *
     * <code>DEMON_BREATHE = 2;</code>
     */
    public static final int DEMON_BREATHE_VALUE = 2;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static BattleRewardType valueOf(int value) {
      return forNumber(value);
    }

    public static BattleRewardType forNumber(int value) {
      switch (value) {
        case 0: return BATTLE_REWARD_TYPE_NONE;
        case 1: return PANACEA;
        case 2: return DEMON_BREATHE;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<BattleRewardType>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        BattleRewardType> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<BattleRewardType>() {
            public BattleRewardType findValueByNumber(int number) {
              return BattleRewardType.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return cn.daxiang.protocol.battle.BattleTypeProtocol.getDescriptor().getEnumTypes().get(6);
    }

    private static final BattleRewardType[] VALUES = values();

    public static BattleRewardType valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private BattleRewardType(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:BattleRewardType)
  }

  /**
   * <pre>
   ** 怪物类型 
   * </pre>
   *
   * Protobuf enum {@code BattleMonsterType}
   */
  public enum BattleMonsterType
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>BATTLE_MONSTER_TYPE_NONE = 0;</code>
     */
    BATTLE_MONSTER_TYPE_NONE(0),
    /**
     * <pre>
     **
     * 1.波次
     * </pre>
     *
     * <code>WAVE = 1;</code>
     */
    WAVE(1),
    /**
     * <pre>
     **
     * 2.挑战
     * </pre>
     *
     * <code>CHALLENGE = 2;</code>
     */
    CHALLENGE(2),
    /**
     * <pre>
     **
     * 3.BOSS
     * </pre>
     *
     * <code>BOSS = 3;</code>
     */
    BOSS(3),
    /**
     * <pre>
     **
     * 4.怪物召唤
     * </pre>
     *
     * <code>SUMMON = 4;</code>
     */
    SUMMON(4),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>BATTLE_MONSTER_TYPE_NONE = 0;</code>
     */
    public static final int BATTLE_MONSTER_TYPE_NONE_VALUE = 0;
    /**
     * <pre>
     **
     * 1.波次
     * </pre>
     *
     * <code>WAVE = 1;</code>
     */
    public static final int WAVE_VALUE = 1;
    /**
     * <pre>
     **
     * 2.挑战
     * </pre>
     *
     * <code>CHALLENGE = 2;</code>
     */
    public static final int CHALLENGE_VALUE = 2;
    /**
     * <pre>
     **
     * 3.BOSS
     * </pre>
     *
     * <code>BOSS = 3;</code>
     */
    public static final int BOSS_VALUE = 3;
    /**
     * <pre>
     **
     * 4.怪物召唤
     * </pre>
     *
     * <code>SUMMON = 4;</code>
     */
    public static final int SUMMON_VALUE = 4;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static BattleMonsterType valueOf(int value) {
      return forNumber(value);
    }

    public static BattleMonsterType forNumber(int value) {
      switch (value) {
        case 0: return BATTLE_MONSTER_TYPE_NONE;
        case 1: return WAVE;
        case 2: return CHALLENGE;
        case 3: return BOSS;
        case 4: return SUMMON;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<BattleMonsterType>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        BattleMonsterType> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<BattleMonsterType>() {
            public BattleMonsterType findValueByNumber(int number) {
              return BattleMonsterType.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return cn.daxiang.protocol.battle.BattleTypeProtocol.getDescriptor().getEnumTypes().get(7);
    }

    private static final BattleMonsterType[] VALUES = values();

    public static BattleMonsterType valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private BattleMonsterType(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:BattleMonsterType)
  }


  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\037battle/battleTypeProtocol.proto*\262\014\n\016Ac" +
      "torFieldType\022\031\n\025ACTOR_FIELD_TYPE_NONE\020\000\022" +
      "\007\n\003UID\020\001\022\017\n\013PLATFORM_ID\020\002\022\013\n\007CHANNEL\020\003\022\r" +
      "\n\tSERVER_ID\020\004\022\014\n\010ACTOR_ID\020\005\022\016\n\nACTOR_NAM" +
      "E\020\006\022\007\n\003SEX\020\007\022\017\n\013ACTOR_LEVEL\020\010\022\r\n\tACTOR_E" +
      "XP\020\t\022\016\n\nACTOR_GOLD\020\n\022\021\n\rACTOR_DIAMOND\020\013\022" +
      "\r\n\tVIP_LEVEL\020\014\022\r\n\tAVATAR_ID\020\r\022\021\n\rGUIDE_I" +
      "D_LIST\020\016\022\014\n\010RECHARGE\020\017\022\024\n\020LAST_RENAME_TI" +
      "ME\020\020\022\017\n\013ACTOR_POWER\020\021\022\013\n\007PRESENT\020\022\022\r\n\tVI" +
      "P_CARDS\020\023\022\020\n\014ACTOR_VIGOUR\020\024\022\020\n\014ACTOR_ENE" +
      "RGY\020\025\022\017\n\013AVATAR_LIST\020\026\022\r\n\tSHOW_LIST\020\027\022\034\n" +
      "\030LAST_REFRESH_VIGOUR_TIME\020\030\022\034\n\030LAST_REFR" +
      "ESH_ENERGY_TIME\020\031\022\025\n\021ACTOR_CREATE_TIME\020\032" +
      "\022\023\n\017LOGIN_TOTAL_DAY\020\033\022\013\n\007SHOW_ID\020\034\022\022\n\016AR" +
      "ENA_MAX_RANK\020\035\022\014\n\010STORY_ID\020\036\022\r\n\tMAX_POWE" +
      "R\020\037\022\024\n\020ACTOR_CHESS_COIN\020 \022\024\n\020ACTOR_ARENA" +
      "_COIN\020!\022\020\n\014PRAISE_STATE\020\"\022\017\n\013NATION_NAME" +
      "\020#\022\020\n\014ONLINE_STATE\020$\022\024\n\020LAST_LOGOUT_TIME" +
      "\020%\022\017\n\013NATION_FLAG\020&\022\021\n\rNATION_FLAGID\020\'\022\025" +
      "\n\021CURRENT_SERVER_ID\020(\022\r\n\tNATION_ID\020)\022\020\n\014" +
      "NATION_LEVEL\020*\022\025\n\021ACTOR_WEAPON_COIN\020+\022\035\n" +
      "\031ACTOR_TEAM_ONLINE_TD_COIN\020,\022\r\n\tAUTOGRAP" +
      "H\020-\022\023\n\017AVATAR_FRAME_ID\020.\022\025\n\021AVATAR_FRAME" +
      "_LIST\020/\022\022\n\016CHAT_BUBBLE_ID\0200\022\024\n\020CHAT_BUBB" +
      "LE_LIST\0201\022\026\n\022ACTOR_FRIEND_POINT\0202\022\025\n\021ACT" +
      "OR_NATION_COIN\0203\022\024\n\020ACTOR_NATION_EXP\0204\022\035" +
      "\n\031ACTOR_NATION_CONTRIBUTION\0205\022\021\n\rACTOR_V" +
      "IP_EXP\0206\022\024\n\020ACTOR_TOWER_COIN\0207\022\026\n\022ACTOR_" +
      "ENDLESS_COIN\0208\022\020\n\014PLOT_ID_LIST\0209\022\036\n\032CAVE" +
      "_SINGLE_CHALLENGE_LIST\020:\022\023\n\017ACTOR_CAVE_C" +
      "OIN\020;\022\022\n\016ACTOR_OFFICIAL\020<\022\026\n\022ACTOR_CAPTU" +
      "RE_COIN\020=\022\014\n\010TITLE_ID\020?\022\030\n\024ACTOR_CROSS_C" +
      "UP_COIN\020@\022\017\n\013SDK_USER_ID\020A\022\017\n\013GROWTH_TAS" +
      "K\020B\022\027\n\023ACTOR_HEGEMONY_COIN\020C\022\022\n\016SHOW_IMM" +
      "ORTALS\020D\022\022\n\016SHOW_HERO_STAR\020E\022\022\n\016ACTOR_GV" +
      "G_COIN\020F\022\032\n\026ACTOR_ACTIVITY_31_COIN\020G\022\025\n\021" +
      "ACTOR_ESCORT_COIN\020H\022\026\n\022ACTOR_CHESS_2_COI" +
      "N\020I\022\026\n\022ACTOR_QINRACE_COIN\020J\022\025\n\021PRAISED_C" +
      "OUNT_MAP\020K\022\034\n\030HERO_COLOR_REBIRTH_TIMES\020L" +
      "\022!\n\035IMMORTALS_COLOR_REBIRTH_TIMES\020M\022\031\n\025S" +
      "HOW_HERO_COLOR_LEVEL\020N*C\n\nTeamTdType\022\025\n\021" +
      "TEAM_TD_TYPE_NONE\020\000\022\r\n\tONLINE_TD\020\001\022\017\n\013AC" +
      "TIVITY_55\020\002*L\n\023TeamTdJoinLimitType\022 \n\034TE" +
      "AM_TD_JOIN_LIMIT_TYPE_NONE\020\000\022\010\n\004CODE\020\001\022\t" +
      "\n\005POWER\020\002*;\n\013TeamTdState\022\026\n\022TEAM_TD_STAT" +
      "E_NONE\020\000\022\010\n\004IDLE\020\001\022\n\n\006BATTLE\020\002*I\n\020TeamTd" +
      "MemberType\022\034\n\030TEAM_TD_MEMBER_TYPE_NONE\020\000" +
      "\022\013\n\007CAPTAIN\020\001\022\n\n\006MEMBER\020\002*Z\n\024BattleTechn" +
      "ologyType\022\037\n\033BATTLE_TECHNOLOGY_TYPE_NONE" +
      "\020\000\022\010\n\004FOOD\020\001\022\027\n\023DEMON_BREATHE_SPEED\020\002*O\n" +
      "\020BattleRewardType\022\033\n\027BATTLE_REWARD_TYPE_" +
      "NONE\020\000\022\013\n\007PANACEA\020\001\022\021\n\rDEMON_BREATHE\020\002*`" +
      "\n\021BattleMonsterType\022\034\n\030BATTLE_MONSTER_TY" +
      "PE_NONE\020\000\022\010\n\004WAVE\020\001\022\r\n\tCHALLENGE\020\002\022\010\n\004BO" +
      "SS\020\003\022\n\n\006SUMMON\020\004B\036\n\032cn.daxiang.protocol." +
      "battleH\001b\006proto3"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
        new com.google.protobuf.Descriptors.FileDescriptor.    InternalDescriptorAssigner() {
          public com.google.protobuf.ExtensionRegistry assignDescriptors(
              com.google.protobuf.Descriptors.FileDescriptor root) {
            descriptor = root;
            return null;
          }
        };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        }, assigner);
  }

  // @@protoc_insertion_point(outer_class_scope)
}
