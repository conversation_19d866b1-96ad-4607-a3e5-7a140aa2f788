// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: game/battleProtocol.proto

package cn.daxiang.protocol.game;

public final class BattleProtocol {
  private BattleProtocol() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  /**
   * <pre>
   ** 战斗 
   * </pre>
   *
   * Protobuf enum {@code BattleCmd}
   */
  public enum BattleCmd
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>BATTLE_CMD_NONE = 0;</code>
     */
    BATTLE_CMD_NONE(0),
    /**
     * <pre>
     **
     * 战斗测试
     * 请求:{&#64;code LongPacket} 目标角色ID
     * 响应:{&#64;code Response}
     * </pre>
     *
     * <code>BATTLE_TEST = 1;</code>
     */
    BATTLE_TEST(1),
    /**
     * <pre>
     **
     * 2.切磋比试
     * 请求:{&#64;code BattleCompeteRequest}
     * 响应:{&#64;code Response}
     * </pre>
     *
     * <code>BATTLE_COMPETE = 2;</code>
     */
    BATTLE_COMPETE(2),
    /**
     * <pre>
     **
     * 获取战斗回放
     * 请求:{&#64;code GetBattleReplayRequest}
     * 响应:{&#64;code BattleResultResponse}
     * </pre>
     *
     * <code>GET_BATTLE_REPLAY = 3;</code>
     */
    GET_BATTLE_REPLAY(3),
    /**
     * <pre>
     **
     * 4.测试技能
     * &lt;pre&gt;
     * 请求:{&#64;code StringPacket}
     * 响应:{&#64;code Response}
     * &lt;/pre&gt;
     * </pre>
     *
     * <code>BATTLE_SKILL_TEST = 4;</code>
     */
    BATTLE_SKILL_TEST(4),
    /**
     * <pre>
     **
     * 推送战斗结果
     * 推送: {&#64;code BattleResultResponse}
     * </pre>
     *
     * <code>PUSH_BATTLE_RESULT = 100;</code>
     */
    PUSH_BATTLE_RESULT(100),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>BATTLE_CMD_NONE = 0;</code>
     */
    public static final int BATTLE_CMD_NONE_VALUE = 0;
    /**
     * <pre>
     **
     * 战斗测试
     * 请求:{&#64;code LongPacket} 目标角色ID
     * 响应:{&#64;code Response}
     * </pre>
     *
     * <code>BATTLE_TEST = 1;</code>
     */
    public static final int BATTLE_TEST_VALUE = 1;
    /**
     * <pre>
     **
     * 2.切磋比试
     * 请求:{&#64;code BattleCompeteRequest}
     * 响应:{&#64;code Response}
     * </pre>
     *
     * <code>BATTLE_COMPETE = 2;</code>
     */
    public static final int BATTLE_COMPETE_VALUE = 2;
    /**
     * <pre>
     **
     * 获取战斗回放
     * 请求:{&#64;code GetBattleReplayRequest}
     * 响应:{&#64;code BattleResultResponse}
     * </pre>
     *
     * <code>GET_BATTLE_REPLAY = 3;</code>
     */
    public static final int GET_BATTLE_REPLAY_VALUE = 3;
    /**
     * <pre>
     **
     * 4.测试技能
     * &lt;pre&gt;
     * 请求:{&#64;code StringPacket}
     * 响应:{&#64;code Response}
     * &lt;/pre&gt;
     * </pre>
     *
     * <code>BATTLE_SKILL_TEST = 4;</code>
     */
    public static final int BATTLE_SKILL_TEST_VALUE = 4;
    /**
     * <pre>
     **
     * 推送战斗结果
     * 推送: {&#64;code BattleResultResponse}
     * </pre>
     *
     * <code>PUSH_BATTLE_RESULT = 100;</code>
     */
    public static final int PUSH_BATTLE_RESULT_VALUE = 100;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static BattleCmd valueOf(int value) {
      return forNumber(value);
    }

    public static BattleCmd forNumber(int value) {
      switch (value) {
        case 0: return BATTLE_CMD_NONE;
        case 1: return BATTLE_TEST;
        case 2: return BATTLE_COMPETE;
        case 3: return GET_BATTLE_REPLAY;
        case 4: return BATTLE_SKILL_TEST;
        case 100: return PUSH_BATTLE_RESULT;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<BattleCmd>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        BattleCmd> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<BattleCmd>() {
            public BattleCmd findValueByNumber(int number) {
              return BattleCmd.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.BattleProtocol.getDescriptor().getEnumTypes().get(0);
    }

    private static final BattleCmd[] VALUES = values();

    public static BattleCmd valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private BattleCmd(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:BattleCmd)
  }

  /**
   * <pre>
   ** 战斗阵营 
   * </pre>
   *
   * Protobuf enum {@code BattleCamp}
   */
  public enum BattleCamp
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>BATTLE_CAMP_NONE = 0;</code>
     */
    BATTLE_CAMP_NONE(0),
    /**
     * <pre>
     ** 1.左边阵营 
     * </pre>
     *
     * <code>LEFT_CAMP = 1;</code>
     */
    LEFT_CAMP(1),
    /**
     * <pre>
     ** 2.右边阵营 
     * </pre>
     *
     * <code>RIGHT_CAMP = 2;</code>
     */
    RIGHT_CAMP(2),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>BATTLE_CAMP_NONE = 0;</code>
     */
    public static final int BATTLE_CAMP_NONE_VALUE = 0;
    /**
     * <pre>
     ** 1.左边阵营 
     * </pre>
     *
     * <code>LEFT_CAMP = 1;</code>
     */
    public static final int LEFT_CAMP_VALUE = 1;
    /**
     * <pre>
     ** 2.右边阵营 
     * </pre>
     *
     * <code>RIGHT_CAMP = 2;</code>
     */
    public static final int RIGHT_CAMP_VALUE = 2;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static BattleCamp valueOf(int value) {
      return forNumber(value);
    }

    public static BattleCamp forNumber(int value) {
      switch (value) {
        case 0: return BATTLE_CAMP_NONE;
        case 1: return LEFT_CAMP;
        case 2: return RIGHT_CAMP;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<BattleCamp>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        BattleCamp> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<BattleCamp>() {
            public BattleCamp findValueByNumber(int number) {
              return BattleCamp.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.BattleProtocol.getDescriptor().getEnumTypes().get(1);
    }

    private static final BattleCamp[] VALUES = values();

    public static BattleCamp valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private BattleCamp(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:BattleCamp)
  }

  /**
   * <pre>
   ** 精灵阵营 
   * </pre>
   *
   * Protobuf enum {@code SpriteCampType}
   */
  public enum SpriteCampType
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>SPRITE_CAMP_TYPE_NONE = 0;</code>
     */
    SPRITE_CAMP_TYPE_NONE(0),
    /**
     * <pre>
     ** 1.天神 
     * </pre>
     *
     * <code>ANGEL = 1;</code>
     */
    ANGEL(1),
    /**
     * <pre>
     ** 2.恶魔 
     * </pre>
     *
     * <code>DEVIL = 2;</code>
     */
    DEVIL(2),
    /**
     * <pre>
     ** 3.幻灵 
     * </pre>
     *
     * <code>ELVES = 3;</code>
     */
    ELVES(3),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>SPRITE_CAMP_TYPE_NONE = 0;</code>
     */
    public static final int SPRITE_CAMP_TYPE_NONE_VALUE = 0;
    /**
     * <pre>
     ** 1.天神 
     * </pre>
     *
     * <code>ANGEL = 1;</code>
     */
    public static final int ANGEL_VALUE = 1;
    /**
     * <pre>
     ** 2.恶魔 
     * </pre>
     *
     * <code>DEVIL = 2;</code>
     */
    public static final int DEVIL_VALUE = 2;
    /**
     * <pre>
     ** 3.幻灵 
     * </pre>
     *
     * <code>ELVES = 3;</code>
     */
    public static final int ELVES_VALUE = 3;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static SpriteCampType valueOf(int value) {
      return forNumber(value);
    }

    public static SpriteCampType forNumber(int value) {
      switch (value) {
        case 0: return SPRITE_CAMP_TYPE_NONE;
        case 1: return ANGEL;
        case 2: return DEVIL;
        case 3: return ELVES;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<SpriteCampType>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        SpriteCampType> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<SpriteCampType>() {
            public SpriteCampType findValueByNumber(int number) {
              return SpriteCampType.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.BattleProtocol.getDescriptor().getEnumTypes().get(2);
    }

    private static final SpriteCampType[] VALUES = values();

    public static SpriteCampType valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private SpriteCampType(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:SpriteCampType)
  }

  /**
   * <pre>
   ** 精灵类型 
   * </pre>
   *
   * Protobuf enum {@code SpriteType}
   */
  public enum SpriteType
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>SPRITE_TYPE_NONE = 0;</code>
     */
    SPRITE_TYPE_NONE(0),
    /**
     * <pre>
     ** 1.英雄 
     * </pre>
     *
     * <code>SPRITE_HERO = 1;</code>
     */
    SPRITE_HERO(1),
    /**
     * <pre>
     ** 2.怪物 
     * </pre>
     *
     * <code>SPRITE_MONSTER = 2;</code>
     */
    SPRITE_MONSTER(2),
    /**
     * <pre>
     ** 3.神兽 
     * </pre>
     *
     * <code>SPRITE_BEAST = 3;</code>
     */
    SPRITE_BEAST(3),
    /**
     * <pre>
     ** 4.系统 
     * </pre>
     *
     * <code>SYSTEM = 4;</code>
     */
    SYSTEM(4),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>SPRITE_TYPE_NONE = 0;</code>
     */
    public static final int SPRITE_TYPE_NONE_VALUE = 0;
    /**
     * <pre>
     ** 1.英雄 
     * </pre>
     *
     * <code>SPRITE_HERO = 1;</code>
     */
    public static final int SPRITE_HERO_VALUE = 1;
    /**
     * <pre>
     ** 2.怪物 
     * </pre>
     *
     * <code>SPRITE_MONSTER = 2;</code>
     */
    public static final int SPRITE_MONSTER_VALUE = 2;
    /**
     * <pre>
     ** 3.神兽 
     * </pre>
     *
     * <code>SPRITE_BEAST = 3;</code>
     */
    public static final int SPRITE_BEAST_VALUE = 3;
    /**
     * <pre>
     ** 4.系统 
     * </pre>
     *
     * <code>SYSTEM = 4;</code>
     */
    public static final int SYSTEM_VALUE = 4;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static SpriteType valueOf(int value) {
      return forNumber(value);
    }

    public static SpriteType forNumber(int value) {
      switch (value) {
        case 0: return SPRITE_TYPE_NONE;
        case 1: return SPRITE_HERO;
        case 2: return SPRITE_MONSTER;
        case 3: return SPRITE_BEAST;
        case 4: return SYSTEM;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<SpriteType>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        SpriteType> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<SpriteType>() {
            public SpriteType findValueByNumber(int number) {
              return SpriteType.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.BattleProtocol.getDescriptor().getEnumTypes().get(3);
    }

    private static final SpriteType[] VALUES = values();

    public static SpriteType valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private SpriteType(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:SpriteType)
  }

  /**
   * <pre>
   ** 战斗类型 
   * </pre>
   *
   * Protobuf enum {@code BattleType}
   */
  public enum BattleType
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>BATTLE_TYPE_NONE = 0;</code>
     */
    BATTLE_TYPE_NONE(0),
    /**
     * <pre>
     ** 1.竞技场 
     * </pre>
     *
     * <code>BATTLE_ARENA = 1;</code>
     */
    BATTLE_ARENA(1),
    /**
     * <pre>
     ** 2.爬塔挑战 
     * </pre>
     *
     * <code>BATTLE_TOWER = 2;</code>
     */
    BATTLE_TOWER(2),
    /**
     * <pre>
     ** 3.藏兵阁 
     * </pre>
     *
     * <code>BATTLE_ARSENAL = 3;</code>
     */
    BATTLE_ARSENAL(3),
    /**
     * <pre>
     ** 4.勇闯魔窟单人挑战
     * </pre>
     *
     * <code>CAVE_SINGLE = 4;</code>
     */
    CAVE_SINGLE(4),
    /**
     * <pre>
     ** 5.勇闯魔窟组队魔将挑战
     * </pre>
     *
     * <code>CAVE_GROUP_NORMAL = 5;</code>
     */
    CAVE_GROUP_NORMAL(5),
    /**
     * <pre>
     ** 6.勇闯魔窟组队天魔挑战
     * </pre>
     *
     * <code>CAVE_GROUP_HARD = 6;</code>
     */
    CAVE_GROUP_HARD(6),
    /**
     * <pre>
     ** 7.比试切磋 
     * </pre>
     *
     * <code>COMPETE = 7;</code>
     */
    COMPETE(7),
    /**
     * <pre>
     ** 8.军团副本挑战 
     * </pre>
     *
     * <code>NATION_DUNGEON = 8;</code>
     */
    NATION_DUNGEON(8),
    /**
     * <pre>
     ** 9.攻城略地 
     * </pre>
     *
     * <code>CAPTURE = 9;</code>
     */
    CAPTURE(9),
    /**
     * <pre>
     ** 10.巅峰竞技场 
     * </pre>
     *
     * <code>CROSS_CUP = 10;</code>
     */
    CROSS_CUP(10),
    /**
     * <pre>
     ** 11.群雄争霸
     * </pre>
     *
     * <code>HEGEMONY = 11;</code>
     */
    HEGEMONY(11),
    /**
     * <pre>
     ** 12.军团BOSS挑战Boss 
     * </pre>
     *
     * <code>NATION_BOSS = 12;</code>
     */
    NATION_BOSS(12),
    /**
     * <pre>
     ** 13.军团BOSS挑战玩家 
     * </pre>
     *
     * <code>NATION_BOSS_ACTOR = 13;</code>
     */
    NATION_BOSS_ACTOR(13),
    /**
     * <pre>
     ** 14.押镖 
     * </pre>
     *
     * <code>ESCORT = 14;</code>
     */
    ESCORT(14),
    /**
     * <pre>
     ** 15.珍珑棋局
     * </pre>
     *
     * <code>CHESS = 15;</code>
     */
    CHESS(15),
    /**
     * <pre>
     ** 16.归葬秘境
     * </pre>
     *
     * <code>SANCTUARY = 16;</code>
     */
    SANCTUARY(16),
    /**
     * <pre>
     ** 17.归藏秘境Boss
     * </pre>
     *
     * <code>SANCTUARY_BOSS = 17;</code>
     */
    SANCTUARY_BOSS(17),
    /**
     * <pre>
     ** 18.决战皇城
     * </pre>
     *
     * <code>IMPERIALISM = 18;</code>
     */
    IMPERIALISM(18),
    /**
     * <pre>
     ** 19.王城乱斗 
     * </pre>
     *
     * <code>GVG_BRAWLING = 19;</code>
     */
    GVG_BRAWLING(19),
    /**
     * <pre>
     ** 20.轮回战场 
     * </pre>
     *
     * <code>REINCARNATION = 20;</code>
     */
    REINCARNATION(20),
    /**
     * <pre>
     ** 1000.技能测试
     * </pre>
     *
     * <code>SKILL_TEST = 1000;</code>
     */
    SKILL_TEST(1000),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>BATTLE_TYPE_NONE = 0;</code>
     */
    public static final int BATTLE_TYPE_NONE_VALUE = 0;
    /**
     * <pre>
     ** 1.竞技场 
     * </pre>
     *
     * <code>BATTLE_ARENA = 1;</code>
     */
    public static final int BATTLE_ARENA_VALUE = 1;
    /**
     * <pre>
     ** 2.爬塔挑战 
     * </pre>
     *
     * <code>BATTLE_TOWER = 2;</code>
     */
    public static final int BATTLE_TOWER_VALUE = 2;
    /**
     * <pre>
     ** 3.藏兵阁 
     * </pre>
     *
     * <code>BATTLE_ARSENAL = 3;</code>
     */
    public static final int BATTLE_ARSENAL_VALUE = 3;
    /**
     * <pre>
     ** 4.勇闯魔窟单人挑战
     * </pre>
     *
     * <code>CAVE_SINGLE = 4;</code>
     */
    public static final int CAVE_SINGLE_VALUE = 4;
    /**
     * <pre>
     ** 5.勇闯魔窟组队魔将挑战
     * </pre>
     *
     * <code>CAVE_GROUP_NORMAL = 5;</code>
     */
    public static final int CAVE_GROUP_NORMAL_VALUE = 5;
    /**
     * <pre>
     ** 6.勇闯魔窟组队天魔挑战
     * </pre>
     *
     * <code>CAVE_GROUP_HARD = 6;</code>
     */
    public static final int CAVE_GROUP_HARD_VALUE = 6;
    /**
     * <pre>
     ** 7.比试切磋 
     * </pre>
     *
     * <code>COMPETE = 7;</code>
     */
    public static final int COMPETE_VALUE = 7;
    /**
     * <pre>
     ** 8.军团副本挑战 
     * </pre>
     *
     * <code>NATION_DUNGEON = 8;</code>
     */
    public static final int NATION_DUNGEON_VALUE = 8;
    /**
     * <pre>
     ** 9.攻城略地 
     * </pre>
     *
     * <code>CAPTURE = 9;</code>
     */
    public static final int CAPTURE_VALUE = 9;
    /**
     * <pre>
     ** 10.巅峰竞技场 
     * </pre>
     *
     * <code>CROSS_CUP = 10;</code>
     */
    public static final int CROSS_CUP_VALUE = 10;
    /**
     * <pre>
     ** 11.群雄争霸
     * </pre>
     *
     * <code>HEGEMONY = 11;</code>
     */
    public static final int HEGEMONY_VALUE = 11;
    /**
     * <pre>
     ** 12.军团BOSS挑战Boss 
     * </pre>
     *
     * <code>NATION_BOSS = 12;</code>
     */
    public static final int NATION_BOSS_VALUE = 12;
    /**
     * <pre>
     ** 13.军团BOSS挑战玩家 
     * </pre>
     *
     * <code>NATION_BOSS_ACTOR = 13;</code>
     */
    public static final int NATION_BOSS_ACTOR_VALUE = 13;
    /**
     * <pre>
     ** 14.押镖 
     * </pre>
     *
     * <code>ESCORT = 14;</code>
     */
    public static final int ESCORT_VALUE = 14;
    /**
     * <pre>
     ** 15.珍珑棋局
     * </pre>
     *
     * <code>CHESS = 15;</code>
     */
    public static final int CHESS_VALUE = 15;
    /**
     * <pre>
     ** 16.归葬秘境
     * </pre>
     *
     * <code>SANCTUARY = 16;</code>
     */
    public static final int SANCTUARY_VALUE = 16;
    /**
     * <pre>
     ** 17.归藏秘境Boss
     * </pre>
     *
     * <code>SANCTUARY_BOSS = 17;</code>
     */
    public static final int SANCTUARY_BOSS_VALUE = 17;
    /**
     * <pre>
     ** 18.决战皇城
     * </pre>
     *
     * <code>IMPERIALISM = 18;</code>
     */
    public static final int IMPERIALISM_VALUE = 18;
    /**
     * <pre>
     ** 19.王城乱斗 
     * </pre>
     *
     * <code>GVG_BRAWLING = 19;</code>
     */
    public static final int GVG_BRAWLING_VALUE = 19;
    /**
     * <pre>
     ** 20.轮回战场 
     * </pre>
     *
     * <code>REINCARNATION = 20;</code>
     */
    public static final int REINCARNATION_VALUE = 20;
    /**
     * <pre>
     ** 1000.技能测试
     * </pre>
     *
     * <code>SKILL_TEST = 1000;</code>
     */
    public static final int SKILL_TEST_VALUE = 1000;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static BattleType valueOf(int value) {
      return forNumber(value);
    }

    public static BattleType forNumber(int value) {
      switch (value) {
        case 0: return BATTLE_TYPE_NONE;
        case 1: return BATTLE_ARENA;
        case 2: return BATTLE_TOWER;
        case 3: return BATTLE_ARSENAL;
        case 4: return CAVE_SINGLE;
        case 5: return CAVE_GROUP_NORMAL;
        case 6: return CAVE_GROUP_HARD;
        case 7: return COMPETE;
        case 8: return NATION_DUNGEON;
        case 9: return CAPTURE;
        case 10: return CROSS_CUP;
        case 11: return HEGEMONY;
        case 12: return NATION_BOSS;
        case 13: return NATION_BOSS_ACTOR;
        case 14: return ESCORT;
        case 15: return CHESS;
        case 16: return SANCTUARY;
        case 17: return SANCTUARY_BOSS;
        case 18: return IMPERIALISM;
        case 19: return GVG_BRAWLING;
        case 20: return REINCARNATION;
        case 1000: return SKILL_TEST;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<BattleType>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        BattleType> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<BattleType>() {
            public BattleType findValueByNumber(int number) {
              return BattleType.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.BattleProtocol.getDescriptor().getEnumTypes().get(4);
    }

    private static final BattleType[] VALUES = values();

    public static BattleType valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private BattleType(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:BattleType)
  }

  /**
   * <pre>
   ** 伤害类型 
   * </pre>
   *
   * Protobuf enum {@code DamageType}
   */
  public enum DamageType
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>DAMAGE_TYPE_NONE = 0;</code>
     */
    DAMAGE_TYPE_NONE(0),
    /**
     * <pre>
     ** 1.物理伤害 
     * </pre>
     *
     * <code>PHYSICAL = 1;</code>
     */
    PHYSICAL(1),
    /**
     * <pre>
     ** 2.法术伤害 
     * </pre>
     *
     * <code>SPELL = 2;</code>
     */
    SPELL(2),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>DAMAGE_TYPE_NONE = 0;</code>
     */
    public static final int DAMAGE_TYPE_NONE_VALUE = 0;
    /**
     * <pre>
     ** 1.物理伤害 
     * </pre>
     *
     * <code>PHYSICAL = 1;</code>
     */
    public static final int PHYSICAL_VALUE = 1;
    /**
     * <pre>
     ** 2.法术伤害 
     * </pre>
     *
     * <code>SPELL = 2;</code>
     */
    public static final int SPELL_VALUE = 2;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static DamageType valueOf(int value) {
      return forNumber(value);
    }

    public static DamageType forNumber(int value) {
      switch (value) {
        case 0: return DAMAGE_TYPE_NONE;
        case 1: return PHYSICAL;
        case 2: return SPELL;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<DamageType>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        DamageType> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<DamageType>() {
            public DamageType findValueByNumber(int number) {
              return DamageType.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.BattleProtocol.getDescriptor().getEnumTypes().get(5);
    }

    private static final DamageType[] VALUES = values();

    public static DamageType valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private DamageType(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:DamageType)
  }

  /**
   * <pre>
   ** 命中状态 
   * </pre>
   *
   * Protobuf enum {@code HitState}
   */
  public enum HitState
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>HIT_STATE_NONE = 0;</code>
     */
    HIT_STATE_NONE(0),
    /**
     * <pre>
     ** 1.命中 
     * </pre>
     *
     * <code>HIT = 1;</code>
     */
    HIT(1),
    /**
     * <pre>
     ** 2.未命中 
     * </pre>
     *
     * <code>MISS = 2;</code>
     */
    MISS(2),
    /**
     * <pre>
     ** 3.免疫 
     * </pre>
     *
     * <code>IMMUNE = 3;</code>
     */
    IMMUNE(3),
    /**
     * <pre>
     ** 4.移除Buff 
     * </pre>
     *
     * <code>REMOVE_BUFF = 4;</code>
     */
    REMOVE_BUFF(4),
    /**
     * <pre>
     ** 5.禁疗 
     * </pre>
     *
     * <code>FORBID_HEAL = 5;</code>
     */
    FORBID_HEAL(5),
    /**
     * <pre>
     ** 6.Buff效果 
     * </pre>
     *
     * <code>BUFF = 6;</code>
     */
    BUFF(6),
    /**
     * <pre>
     ** 7.分摊伤害 effectId:buff的效果Id 
     * </pre>
     *
     * <code>SHARING_DAMAGE = 7;</code>
     */
    SHARING_DAMAGE(7),
    /**
     * <pre>
     ** 8.被替身抵挡 
     * </pre>
     *
     * <code>SUBSTITUTE_DAMAGE = 8;</code>
     */
    SUBSTITUTE_DAMAGE(8),
    /**
     * <pre>
     ** 9.伤害限制 
     * </pre>
     *
     * <code>DAMAGE_LIMIT = 9;</code>
     */
    DAMAGE_LIMIT(9),
    /**
     * <pre>
     **  10.伤害减免 
     * </pre>
     *
     * <code>DAMAGE_REDUCTION = 10;</code>
     */
    DAMAGE_REDUCTION(10),
    /**
     * <pre>
     ** 11.豁免 
     * </pre>
     *
     * <code>DAMAGE_EXEMPT = 11;</code>
     */
    DAMAGE_EXEMPT(11),
    /**
     * <pre>
     ** 12.致命一击 
     * </pre>
     *
     * <code>FATAL_STRIKE = 12;</code>
     */
    FATAL_STRIKE(12),
    /**
     * <pre>
     ** 13.神圣一击 
     * </pre>
     *
     * <code>HOLY_STRIKE = 13;</code>
     */
    HOLY_STRIKE(13),
    /**
     * <pre>
     ** 14.无双一击 
     * </pre>
     *
     * <code>UNPARALLELED = 14;</code>
     */
    UNPARALLELED(14),
    /**
     * <pre>
     ** 15.业火 
     * </pre>
     *
     * <code>CONFLAGRATION_BUFF = 15;</code>
     */
    CONFLAGRATION_BUFF(15),
    /**
     * <pre>
     ** 16.反震 
     * </pre>
     *
     * <code>COUNTER_SHOCK_BUFF = 16;</code>
     */
    COUNTER_SHOCK_BUFF(16),
    /**
     * <pre>
     ** 17.混乱 
     * </pre>
     *
     * <code>CONFUSION = 17;</code>
     */
    CONFUSION(17),
    /**
     * <pre>
     ** 18.会心一击 
     * </pre>
     *
     * <code>CRITICAL_ONE = 18;</code>
     */
    CRITICAL_ONE(18),
    /**
     * <pre>
     ** 19.转移伤害 
     * </pre>
     *
     * <code>TRANSFER_DAMAGE = 19;</code>
     */
    TRANSFER_DAMAGE(19),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>HIT_STATE_NONE = 0;</code>
     */
    public static final int HIT_STATE_NONE_VALUE = 0;
    /**
     * <pre>
     ** 1.命中 
     * </pre>
     *
     * <code>HIT = 1;</code>
     */
    public static final int HIT_VALUE = 1;
    /**
     * <pre>
     ** 2.未命中 
     * </pre>
     *
     * <code>MISS = 2;</code>
     */
    public static final int MISS_VALUE = 2;
    /**
     * <pre>
     ** 3.免疫 
     * </pre>
     *
     * <code>IMMUNE = 3;</code>
     */
    public static final int IMMUNE_VALUE = 3;
    /**
     * <pre>
     ** 4.移除Buff 
     * </pre>
     *
     * <code>REMOVE_BUFF = 4;</code>
     */
    public static final int REMOVE_BUFF_VALUE = 4;
    /**
     * <pre>
     ** 5.禁疗 
     * </pre>
     *
     * <code>FORBID_HEAL = 5;</code>
     */
    public static final int FORBID_HEAL_VALUE = 5;
    /**
     * <pre>
     ** 6.Buff效果 
     * </pre>
     *
     * <code>BUFF = 6;</code>
     */
    public static final int BUFF_VALUE = 6;
    /**
     * <pre>
     ** 7.分摊伤害 effectId:buff的效果Id 
     * </pre>
     *
     * <code>SHARING_DAMAGE = 7;</code>
     */
    public static final int SHARING_DAMAGE_VALUE = 7;
    /**
     * <pre>
     ** 8.被替身抵挡 
     * </pre>
     *
     * <code>SUBSTITUTE_DAMAGE = 8;</code>
     */
    public static final int SUBSTITUTE_DAMAGE_VALUE = 8;
    /**
     * <pre>
     ** 9.伤害限制 
     * </pre>
     *
     * <code>DAMAGE_LIMIT = 9;</code>
     */
    public static final int DAMAGE_LIMIT_VALUE = 9;
    /**
     * <pre>
     **  10.伤害减免 
     * </pre>
     *
     * <code>DAMAGE_REDUCTION = 10;</code>
     */
    public static final int DAMAGE_REDUCTION_VALUE = 10;
    /**
     * <pre>
     ** 11.豁免 
     * </pre>
     *
     * <code>DAMAGE_EXEMPT = 11;</code>
     */
    public static final int DAMAGE_EXEMPT_VALUE = 11;
    /**
     * <pre>
     ** 12.致命一击 
     * </pre>
     *
     * <code>FATAL_STRIKE = 12;</code>
     */
    public static final int FATAL_STRIKE_VALUE = 12;
    /**
     * <pre>
     ** 13.神圣一击 
     * </pre>
     *
     * <code>HOLY_STRIKE = 13;</code>
     */
    public static final int HOLY_STRIKE_VALUE = 13;
    /**
     * <pre>
     ** 14.无双一击 
     * </pre>
     *
     * <code>UNPARALLELED = 14;</code>
     */
    public static final int UNPARALLELED_VALUE = 14;
    /**
     * <pre>
     ** 15.业火 
     * </pre>
     *
     * <code>CONFLAGRATION_BUFF = 15;</code>
     */
    public static final int CONFLAGRATION_BUFF_VALUE = 15;
    /**
     * <pre>
     ** 16.反震 
     * </pre>
     *
     * <code>COUNTER_SHOCK_BUFF = 16;</code>
     */
    public static final int COUNTER_SHOCK_BUFF_VALUE = 16;
    /**
     * <pre>
     ** 17.混乱 
     * </pre>
     *
     * <code>CONFUSION = 17;</code>
     */
    public static final int CONFUSION_VALUE = 17;
    /**
     * <pre>
     ** 18.会心一击 
     * </pre>
     *
     * <code>CRITICAL_ONE = 18;</code>
     */
    public static final int CRITICAL_ONE_VALUE = 18;
    /**
     * <pre>
     ** 19.转移伤害 
     * </pre>
     *
     * <code>TRANSFER_DAMAGE = 19;</code>
     */
    public static final int TRANSFER_DAMAGE_VALUE = 19;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static HitState valueOf(int value) {
      return forNumber(value);
    }

    public static HitState forNumber(int value) {
      switch (value) {
        case 0: return HIT_STATE_NONE;
        case 1: return HIT;
        case 2: return MISS;
        case 3: return IMMUNE;
        case 4: return REMOVE_BUFF;
        case 5: return FORBID_HEAL;
        case 6: return BUFF;
        case 7: return SHARING_DAMAGE;
        case 8: return SUBSTITUTE_DAMAGE;
        case 9: return DAMAGE_LIMIT;
        case 10: return DAMAGE_REDUCTION;
        case 11: return DAMAGE_EXEMPT;
        case 12: return FATAL_STRIKE;
        case 13: return HOLY_STRIKE;
        case 14: return UNPARALLELED;
        case 15: return CONFLAGRATION_BUFF;
        case 16: return COUNTER_SHOCK_BUFF;
        case 17: return CONFUSION;
        case 18: return CRITICAL_ONE;
        case 19: return TRANSFER_DAMAGE;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<HitState>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        HitState> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<HitState>() {
            public HitState findValueByNumber(int number) {
              return HitState.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.BattleProtocol.getDescriptor().getEnumTypes().get(6);
    }

    private static final HitState[] VALUES = values();

    public static HitState valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private HitState(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:HitState)
  }

  /**
   * Protobuf enum {@code SpriteKey}
   */
  public enum SpriteKey
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>SPRITE_KEY_NONE = 0;</code>
     */
    SPRITE_KEY_NONE(0),
    /**
     * <pre>
     ** 1.精灵ID 
     * </pre>
     *
     * <code>SPRITE_UID = 1;</code>
     */
    SPRITE_UID(1),
    /**
     * <pre>
     ** 2.角色ID 
     * </pre>
     *
     * <code>SPRITE_ACTOR_ID = 2;</code>
     */
    SPRITE_ACTOR_ID(2),
    /**
     * <pre>
     ** 3.配置ID 
     * </pre>
     *
     * <code>SPRITE_CONFIG_ID = 3;</code>
     */
    SPRITE_CONFIG_ID(3),
    /**
     * <pre>
     ** 4.阵营 
     * </pre>
     *
     * <code>BATTLE_CAMP_ID = 4;</code>
     */
    BATTLE_CAMP_ID(4),
    /**
     * <pre>
     ** 5.位置 
     * </pre>
     *
     * <code>POSITION = 5;</code>
     */
    POSITION(5),
    /**
     * <pre>
     ** 6.精灵类型 {&#64;code SpriteType} 
     * </pre>
     *
     * <code>SPRITE_TYPE = 6;</code>
     */
    SPRITE_TYPE(6),
    /**
     * <pre>
     ** 30.生命值 
     * </pre>
     *
     * <code>HP = 30;</code>
     */
    HP(30),
    /**
     * <pre>
     ** 32.生命最大值 
     * </pre>
     *
     * <code>HP_MAX = 32;</code>
     */
    HP_MAX(32),
    /**
     * <pre>
     ** 35.怒气值 
     * </pre>
     *
     * <code>RAGE = 35;</code>
     */
    RAGE(35),
    /**
     * <pre>
     ** 36.怒气最大值 
     * </pre>
     *
     * <code>RAGE_MAX = 36;</code>
     */
    RAGE_MAX(36),
    /**
     * <pre>
     ** 100.护盾 
     * </pre>
     *
     * <code>SHIELD = 100;</code>
     */
    SHIELD(100),
    /**
     * <pre>
     ** 101.最终血量 
     * </pre>
     *
     * <code>FINAL_HP = 101;</code>
     */
    FINAL_HP(101),
    /**
     * <pre>
     ** 102.英雄阵营 
     * </pre>
     *
     * <code>HERO_CAMP = 102;</code>
     */
    HERO_CAMP(102),
    /**
     * <pre>
     ** 103.已穿戴的英雄专武皮肤ID 
     * </pre>
     *
     * <code>IMMORTALS_SKIN_ID = 103;</code>
     */
    IMMORTALS_SKIN_ID(103),
    /**
     * <pre>
     ** 104.星级 
     * </pre>
     *
     * <code>STAR = 104;</code>
     */
    STAR(104),
    /**
     * <pre>
     ** 105.时空红颜配置Id 
     * </pre>
     *
     * <code>SPACETIME_BEAUTY_ID = 105;</code>
     */
    SPACETIME_BEAUTY_ID(105),
    /**
     * <pre>
     ** 106.英雄皮肤ID 
     * </pre>
     *
     * <code>HERO_SKIN_ID = 106;</code>
     */
    HERO_SKIN_ID(106),
    /**
     * <pre>
     ** 107.英雄幻彩等级 
     * </pre>
     *
     * <code>HERO_COLOR_LEVEL = 107;</code>
     */
    HERO_COLOR_LEVEL(107),
    /**
     * <pre>
     ** 108.专武幻彩等级 
     * </pre>
     *
     * <code>IMMORTALS_COLOR_LEVEL = 108;</code>
     */
    IMMORTALS_COLOR_LEVEL(108),
    /**
     * <pre>
     ** 109.坐骑ID 
     * </pre>
     *
     * <code>MOUNT_CID = 109;</code>
     */
    MOUNT_CID(109),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>SPRITE_KEY_NONE = 0;</code>
     */
    public static final int SPRITE_KEY_NONE_VALUE = 0;
    /**
     * <pre>
     ** 1.精灵ID 
     * </pre>
     *
     * <code>SPRITE_UID = 1;</code>
     */
    public static final int SPRITE_UID_VALUE = 1;
    /**
     * <pre>
     ** 2.角色ID 
     * </pre>
     *
     * <code>SPRITE_ACTOR_ID = 2;</code>
     */
    public static final int SPRITE_ACTOR_ID_VALUE = 2;
    /**
     * <pre>
     ** 3.配置ID 
     * </pre>
     *
     * <code>SPRITE_CONFIG_ID = 3;</code>
     */
    public static final int SPRITE_CONFIG_ID_VALUE = 3;
    /**
     * <pre>
     ** 4.阵营 
     * </pre>
     *
     * <code>BATTLE_CAMP_ID = 4;</code>
     */
    public static final int BATTLE_CAMP_ID_VALUE = 4;
    /**
     * <pre>
     ** 5.位置 
     * </pre>
     *
     * <code>POSITION = 5;</code>
     */
    public static final int POSITION_VALUE = 5;
    /**
     * <pre>
     ** 6.精灵类型 {&#64;code SpriteType} 
     * </pre>
     *
     * <code>SPRITE_TYPE = 6;</code>
     */
    public static final int SPRITE_TYPE_VALUE = 6;
    /**
     * <pre>
     ** 30.生命值 
     * </pre>
     *
     * <code>HP = 30;</code>
     */
    public static final int HP_VALUE = 30;
    /**
     * <pre>
     ** 32.生命最大值 
     * </pre>
     *
     * <code>HP_MAX = 32;</code>
     */
    public static final int HP_MAX_VALUE = 32;
    /**
     * <pre>
     ** 35.怒气值 
     * </pre>
     *
     * <code>RAGE = 35;</code>
     */
    public static final int RAGE_VALUE = 35;
    /**
     * <pre>
     ** 36.怒气最大值 
     * </pre>
     *
     * <code>RAGE_MAX = 36;</code>
     */
    public static final int RAGE_MAX_VALUE = 36;
    /**
     * <pre>
     ** 100.护盾 
     * </pre>
     *
     * <code>SHIELD = 100;</code>
     */
    public static final int SHIELD_VALUE = 100;
    /**
     * <pre>
     ** 101.最终血量 
     * </pre>
     *
     * <code>FINAL_HP = 101;</code>
     */
    public static final int FINAL_HP_VALUE = 101;
    /**
     * <pre>
     ** 102.英雄阵营 
     * </pre>
     *
     * <code>HERO_CAMP = 102;</code>
     */
    public static final int HERO_CAMP_VALUE = 102;
    /**
     * <pre>
     ** 103.已穿戴的英雄专武皮肤ID 
     * </pre>
     *
     * <code>IMMORTALS_SKIN_ID = 103;</code>
     */
    public static final int IMMORTALS_SKIN_ID_VALUE = 103;
    /**
     * <pre>
     ** 104.星级 
     * </pre>
     *
     * <code>STAR = 104;</code>
     */
    public static final int STAR_VALUE = 104;
    /**
     * <pre>
     ** 105.时空红颜配置Id 
     * </pre>
     *
     * <code>SPACETIME_BEAUTY_ID = 105;</code>
     */
    public static final int SPACETIME_BEAUTY_ID_VALUE = 105;
    /**
     * <pre>
     ** 106.英雄皮肤ID 
     * </pre>
     *
     * <code>HERO_SKIN_ID = 106;</code>
     */
    public static final int HERO_SKIN_ID_VALUE = 106;
    /**
     * <pre>
     ** 107.英雄幻彩等级 
     * </pre>
     *
     * <code>HERO_COLOR_LEVEL = 107;</code>
     */
    public static final int HERO_COLOR_LEVEL_VALUE = 107;
    /**
     * <pre>
     ** 108.专武幻彩等级 
     * </pre>
     *
     * <code>IMMORTALS_COLOR_LEVEL = 108;</code>
     */
    public static final int IMMORTALS_COLOR_LEVEL_VALUE = 108;
    /**
     * <pre>
     ** 109.坐骑ID 
     * </pre>
     *
     * <code>MOUNT_CID = 109;</code>
     */
    public static final int MOUNT_CID_VALUE = 109;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static SpriteKey valueOf(int value) {
      return forNumber(value);
    }

    public static SpriteKey forNumber(int value) {
      switch (value) {
        case 0: return SPRITE_KEY_NONE;
        case 1: return SPRITE_UID;
        case 2: return SPRITE_ACTOR_ID;
        case 3: return SPRITE_CONFIG_ID;
        case 4: return BATTLE_CAMP_ID;
        case 5: return POSITION;
        case 6: return SPRITE_TYPE;
        case 30: return HP;
        case 32: return HP_MAX;
        case 35: return RAGE;
        case 36: return RAGE_MAX;
        case 100: return SHIELD;
        case 101: return FINAL_HP;
        case 102: return HERO_CAMP;
        case 103: return IMMORTALS_SKIN_ID;
        case 104: return STAR;
        case 105: return SPACETIME_BEAUTY_ID;
        case 106: return HERO_SKIN_ID;
        case 107: return HERO_COLOR_LEVEL;
        case 108: return IMMORTALS_COLOR_LEVEL;
        case 109: return MOUNT_CID;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<SpriteKey>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        SpriteKey> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<SpriteKey>() {
            public SpriteKey findValueByNumber(int number) {
              return SpriteKey.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.BattleProtocol.getDescriptor().getEnumTypes().get(7);
    }

    private static final SpriteKey[] VALUES = values();

    public static SpriteKey valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private SpriteKey(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:SpriteKey)
  }

  public interface BattleCompeteRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:BattleCompeteRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     ** 服务器ID(与查询一样是currentServerId) 
     * </pre>
     *
     * <code>int32 serverId = 1;</code>
     */
    int getServerId();

    /**
     * <pre>
     ** 角色ID 
     * </pre>
     *
     * <code>int64 actorId = 2;</code>
     */
    long getActorId();
  }
  /**
   * <pre>
   ** 比试切磋请求 
   * </pre>
   *
   * Protobuf type {@code BattleCompeteRequest}
   */
  public  static final class BattleCompeteRequest extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:BattleCompeteRequest)
      BattleCompeteRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use BattleCompeteRequest.newBuilder() to construct.
    private BattleCompeteRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private BattleCompeteRequest() {
      serverId_ = 0;
      actorId_ = 0L;
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private BattleCompeteRequest(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {

              serverId_ = input.readInt32();
              break;
            }
            case 16: {

              actorId_ = input.readInt64();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.BattleProtocol.internal_static_BattleCompeteRequest_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.daxiang.protocol.game.BattleProtocol.internal_static_BattleCompeteRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.daxiang.protocol.game.BattleProtocol.BattleCompeteRequest.class, cn.daxiang.protocol.game.BattleProtocol.BattleCompeteRequest.Builder.class);
    }

    public static final int SERVERID_FIELD_NUMBER = 1;
    private int serverId_;
    /**
     * <pre>
     ** 服务器ID(与查询一样是currentServerId) 
     * </pre>
     *
     * <code>int32 serverId = 1;</code>
     */
    public int getServerId() {
      return serverId_;
    }

    public static final int ACTORID_FIELD_NUMBER = 2;
    private long actorId_;
    /**
     * <pre>
     ** 角色ID 
     * </pre>
     *
     * <code>int64 actorId = 2;</code>
     */
    public long getActorId() {
      return actorId_;
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (serverId_ != 0) {
        output.writeInt32(1, serverId_);
      }
      if (actorId_ != 0L) {
        output.writeInt64(2, actorId_);
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (serverId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, serverId_);
      }
      if (actorId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, actorId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof cn.daxiang.protocol.game.BattleProtocol.BattleCompeteRequest)) {
        return super.equals(obj);
      }
      cn.daxiang.protocol.game.BattleProtocol.BattleCompeteRequest other = (cn.daxiang.protocol.game.BattleProtocol.BattleCompeteRequest) obj;

      boolean result = true;
      result = result && (getServerId()
          == other.getServerId());
      result = result && (getActorId()
          == other.getActorId());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + SERVERID_FIELD_NUMBER;
      hash = (53 * hash) + getServerId();
      hash = (37 * hash) + ACTORID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getActorId());
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static cn.daxiang.protocol.game.BattleProtocol.BattleCompeteRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.BattleCompeteRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.BattleCompeteRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.BattleCompeteRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.BattleCompeteRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.BattleCompeteRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.BattleCompeteRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.BattleCompeteRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.BattleCompeteRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.BattleCompeteRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.BattleCompeteRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.BattleCompeteRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(cn.daxiang.protocol.game.BattleProtocol.BattleCompeteRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** 比试切磋请求 
     * </pre>
     *
     * Protobuf type {@code BattleCompeteRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:BattleCompeteRequest)
        cn.daxiang.protocol.game.BattleProtocol.BattleCompeteRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.daxiang.protocol.game.BattleProtocol.internal_static_BattleCompeteRequest_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.daxiang.protocol.game.BattleProtocol.internal_static_BattleCompeteRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.daxiang.protocol.game.BattleProtocol.BattleCompeteRequest.class, cn.daxiang.protocol.game.BattleProtocol.BattleCompeteRequest.Builder.class);
      }

      // Construct using cn.daxiang.protocol.game.BattleProtocol.BattleCompeteRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        serverId_ = 0;

        actorId_ = 0L;

        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return cn.daxiang.protocol.game.BattleProtocol.internal_static_BattleCompeteRequest_descriptor;
      }

      public cn.daxiang.protocol.game.BattleProtocol.BattleCompeteRequest getDefaultInstanceForType() {
        return cn.daxiang.protocol.game.BattleProtocol.BattleCompeteRequest.getDefaultInstance();
      }

      public cn.daxiang.protocol.game.BattleProtocol.BattleCompeteRequest build() {
        cn.daxiang.protocol.game.BattleProtocol.BattleCompeteRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public cn.daxiang.protocol.game.BattleProtocol.BattleCompeteRequest buildPartial() {
        cn.daxiang.protocol.game.BattleProtocol.BattleCompeteRequest result = new cn.daxiang.protocol.game.BattleProtocol.BattleCompeteRequest(this);
        result.serverId_ = serverId_;
        result.actorId_ = actorId_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof cn.daxiang.protocol.game.BattleProtocol.BattleCompeteRequest) {
          return mergeFrom((cn.daxiang.protocol.game.BattleProtocol.BattleCompeteRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(cn.daxiang.protocol.game.BattleProtocol.BattleCompeteRequest other) {
        if (other == cn.daxiang.protocol.game.BattleProtocol.BattleCompeteRequest.getDefaultInstance()) return this;
        if (other.getServerId() != 0) {
          setServerId(other.getServerId());
        }
        if (other.getActorId() != 0L) {
          setActorId(other.getActorId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        cn.daxiang.protocol.game.BattleProtocol.BattleCompeteRequest parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (cn.daxiang.protocol.game.BattleProtocol.BattleCompeteRequest) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int serverId_ ;
      /**
       * <pre>
       ** 服务器ID(与查询一样是currentServerId) 
       * </pre>
       *
       * <code>int32 serverId = 1;</code>
       */
      public int getServerId() {
        return serverId_;
      }
      /**
       * <pre>
       ** 服务器ID(与查询一样是currentServerId) 
       * </pre>
       *
       * <code>int32 serverId = 1;</code>
       */
      public Builder setServerId(int value) {
        
        serverId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 服务器ID(与查询一样是currentServerId) 
       * </pre>
       *
       * <code>int32 serverId = 1;</code>
       */
      public Builder clearServerId() {
        
        serverId_ = 0;
        onChanged();
        return this;
      }

      private long actorId_ ;
      /**
       * <pre>
       ** 角色ID 
       * </pre>
       *
       * <code>int64 actorId = 2;</code>
       */
      public long getActorId() {
        return actorId_;
      }
      /**
       * <pre>
       ** 角色ID 
       * </pre>
       *
       * <code>int64 actorId = 2;</code>
       */
      public Builder setActorId(long value) {
        
        actorId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 角色ID 
       * </pre>
       *
       * <code>int64 actorId = 2;</code>
       */
      public Builder clearActorId() {
        
        actorId_ = 0L;
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:BattleCompeteRequest)
    }

    // @@protoc_insertion_point(class_scope:BattleCompeteRequest)
    private static final cn.daxiang.protocol.game.BattleProtocol.BattleCompeteRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new cn.daxiang.protocol.game.BattleProtocol.BattleCompeteRequest();
    }

    public static cn.daxiang.protocol.game.BattleProtocol.BattleCompeteRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<BattleCompeteRequest>
        PARSER = new com.google.protobuf.AbstractParser<BattleCompeteRequest>() {
      public BattleCompeteRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new BattleCompeteRequest(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<BattleCompeteRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<BattleCompeteRequest> getParserForType() {
      return PARSER;
    }

    public cn.daxiang.protocol.game.BattleProtocol.BattleCompeteRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface GetBattleReplayRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:GetBattleReplayRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     ** 战斗回放Id
     * </pre>
     *
     * <code>int64 battleReplayId = 1;</code>
     */
    long getBattleReplayId();
  }
  /**
   * <pre>
   **竞技场战斗回放请求
   * </pre>
   *
   * Protobuf type {@code GetBattleReplayRequest}
   */
  public  static final class GetBattleReplayRequest extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:GetBattleReplayRequest)
      GetBattleReplayRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use GetBattleReplayRequest.newBuilder() to construct.
    private GetBattleReplayRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private GetBattleReplayRequest() {
      battleReplayId_ = 0L;
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private GetBattleReplayRequest(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {

              battleReplayId_ = input.readInt64();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.BattleProtocol.internal_static_GetBattleReplayRequest_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.daxiang.protocol.game.BattleProtocol.internal_static_GetBattleReplayRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.daxiang.protocol.game.BattleProtocol.GetBattleReplayRequest.class, cn.daxiang.protocol.game.BattleProtocol.GetBattleReplayRequest.Builder.class);
    }

    public static final int BATTLEREPLAYID_FIELD_NUMBER = 1;
    private long battleReplayId_;
    /**
     * <pre>
     ** 战斗回放Id
     * </pre>
     *
     * <code>int64 battleReplayId = 1;</code>
     */
    public long getBattleReplayId() {
      return battleReplayId_;
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (battleReplayId_ != 0L) {
        output.writeInt64(1, battleReplayId_);
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (battleReplayId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, battleReplayId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof cn.daxiang.protocol.game.BattleProtocol.GetBattleReplayRequest)) {
        return super.equals(obj);
      }
      cn.daxiang.protocol.game.BattleProtocol.GetBattleReplayRequest other = (cn.daxiang.protocol.game.BattleProtocol.GetBattleReplayRequest) obj;

      boolean result = true;
      result = result && (getBattleReplayId()
          == other.getBattleReplayId());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + BATTLEREPLAYID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getBattleReplayId());
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static cn.daxiang.protocol.game.BattleProtocol.GetBattleReplayRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.GetBattleReplayRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.GetBattleReplayRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.GetBattleReplayRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.GetBattleReplayRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.GetBattleReplayRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.GetBattleReplayRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.GetBattleReplayRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.GetBattleReplayRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.GetBattleReplayRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.GetBattleReplayRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.GetBattleReplayRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(cn.daxiang.protocol.game.BattleProtocol.GetBattleReplayRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     **竞技场战斗回放请求
     * </pre>
     *
     * Protobuf type {@code GetBattleReplayRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:GetBattleReplayRequest)
        cn.daxiang.protocol.game.BattleProtocol.GetBattleReplayRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.daxiang.protocol.game.BattleProtocol.internal_static_GetBattleReplayRequest_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.daxiang.protocol.game.BattleProtocol.internal_static_GetBattleReplayRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.daxiang.protocol.game.BattleProtocol.GetBattleReplayRequest.class, cn.daxiang.protocol.game.BattleProtocol.GetBattleReplayRequest.Builder.class);
      }

      // Construct using cn.daxiang.protocol.game.BattleProtocol.GetBattleReplayRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        battleReplayId_ = 0L;

        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return cn.daxiang.protocol.game.BattleProtocol.internal_static_GetBattleReplayRequest_descriptor;
      }

      public cn.daxiang.protocol.game.BattleProtocol.GetBattleReplayRequest getDefaultInstanceForType() {
        return cn.daxiang.protocol.game.BattleProtocol.GetBattleReplayRequest.getDefaultInstance();
      }

      public cn.daxiang.protocol.game.BattleProtocol.GetBattleReplayRequest build() {
        cn.daxiang.protocol.game.BattleProtocol.GetBattleReplayRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public cn.daxiang.protocol.game.BattleProtocol.GetBattleReplayRequest buildPartial() {
        cn.daxiang.protocol.game.BattleProtocol.GetBattleReplayRequest result = new cn.daxiang.protocol.game.BattleProtocol.GetBattleReplayRequest(this);
        result.battleReplayId_ = battleReplayId_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof cn.daxiang.protocol.game.BattleProtocol.GetBattleReplayRequest) {
          return mergeFrom((cn.daxiang.protocol.game.BattleProtocol.GetBattleReplayRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(cn.daxiang.protocol.game.BattleProtocol.GetBattleReplayRequest other) {
        if (other == cn.daxiang.protocol.game.BattleProtocol.GetBattleReplayRequest.getDefaultInstance()) return this;
        if (other.getBattleReplayId() != 0L) {
          setBattleReplayId(other.getBattleReplayId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        cn.daxiang.protocol.game.BattleProtocol.GetBattleReplayRequest parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (cn.daxiang.protocol.game.BattleProtocol.GetBattleReplayRequest) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private long battleReplayId_ ;
      /**
       * <pre>
       ** 战斗回放Id
       * </pre>
       *
       * <code>int64 battleReplayId = 1;</code>
       */
      public long getBattleReplayId() {
        return battleReplayId_;
      }
      /**
       * <pre>
       ** 战斗回放Id
       * </pre>
       *
       * <code>int64 battleReplayId = 1;</code>
       */
      public Builder setBattleReplayId(long value) {
        
        battleReplayId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 战斗回放Id
       * </pre>
       *
       * <code>int64 battleReplayId = 1;</code>
       */
      public Builder clearBattleReplayId() {
        
        battleReplayId_ = 0L;
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:GetBattleReplayRequest)
    }

    // @@protoc_insertion_point(class_scope:GetBattleReplayRequest)
    private static final cn.daxiang.protocol.game.BattleProtocol.GetBattleReplayRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new cn.daxiang.protocol.game.BattleProtocol.GetBattleReplayRequest();
    }

    public static cn.daxiang.protocol.game.BattleProtocol.GetBattleReplayRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<GetBattleReplayRequest>
        PARSER = new com.google.protobuf.AbstractParser<GetBattleReplayRequest>() {
      public GetBattleReplayRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new GetBattleReplayRequest(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<GetBattleReplayRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<GetBattleReplayRequest> getParserForType() {
      return PARSER;
    }

    public cn.daxiang.protocol.game.BattleProtocol.GetBattleReplayRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface BattleResultResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:BattleResultResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     ** 战斗类型 
     * </pre>
     *
     * <code>.BattleType type = 1;</code>
     */
    int getTypeValue();
    /**
     * <pre>
     ** 战斗类型 
     * </pre>
     *
     * <code>.BattleType type = 1;</code>
     */
    cn.daxiang.protocol.game.BattleProtocol.BattleType getType();

    /**
     * <pre>
     ** 成员列表 
     * </pre>
     *
     * <code>repeated .BattleMember members = 2;</code>
     */
    java.util.List<cn.daxiang.protocol.game.BattleProtocol.BattleMember> 
        getMembersList();
    /**
     * <pre>
     ** 成员列表 
     * </pre>
     *
     * <code>repeated .BattleMember members = 2;</code>
     */
    cn.daxiang.protocol.game.BattleProtocol.BattleMember getMembers(int index);
    /**
     * <pre>
     ** 成员列表 
     * </pre>
     *
     * <code>repeated .BattleMember members = 2;</code>
     */
    int getMembersCount();
    /**
     * <pre>
     ** 成员列表 
     * </pre>
     *
     * <code>repeated .BattleMember members = 2;</code>
     */
    java.util.List<? extends cn.daxiang.protocol.game.BattleProtocol.BattleMemberOrBuilder> 
        getMembersOrBuilderList();
    /**
     * <pre>
     ** 成员列表 
     * </pre>
     *
     * <code>repeated .BattleMember members = 2;</code>
     */
    cn.daxiang.protocol.game.BattleProtocol.BattleMemberOrBuilder getMembersOrBuilder(
        int index);

    /**
     * <pre>
     ** 战斗报表 
     * </pre>
     *
     * <code>map&lt;int32, .FightReportList&gt; reports = 3;</code>
     */
    int getReportsCount();
    /**
     * <pre>
     ** 战斗报表 
     * </pre>
     *
     * <code>map&lt;int32, .FightReportList&gt; reports = 3;</code>
     */
    boolean containsReports(
        int key);
    /**
     * Use {@link #getReportsMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Integer, cn.daxiang.protocol.game.BattleProtocol.FightReportList>
    getReports();
    /**
     * <pre>
     ** 战斗报表 
     * </pre>
     *
     * <code>map&lt;int32, .FightReportList&gt; reports = 3;</code>
     */
    java.util.Map<java.lang.Integer, cn.daxiang.protocol.game.BattleProtocol.FightReportList>
    getReportsMap();
    /**
     * <pre>
     ** 战斗报表 
     * </pre>
     *
     * <code>map&lt;int32, .FightReportList&gt; reports = 3;</code>
     */

    cn.daxiang.protocol.game.BattleProtocol.FightReportList getReportsOrDefault(
        int key,
        cn.daxiang.protocol.game.BattleProtocol.FightReportList defaultValue);
    /**
     * <pre>
     ** 战斗报表 
     * </pre>
     *
     * <code>map&lt;int32, .FightReportList&gt; reports = 3;</code>
     */

    cn.daxiang.protocol.game.BattleProtocol.FightReportList getReportsOrThrow(
        int key);

    /**
     * <pre>
     ** 战斗奖励 
     * </pre>
     *
     * <code>.RewardResult reward = 4;</code>
     */
    boolean hasReward();
    /**
     * <pre>
     ** 战斗奖励 
     * </pre>
     *
     * <code>.RewardResult reward = 4;</code>
     */
    cn.daxiang.protocol.game.CommonProtocol.RewardResult getReward();
    /**
     * <pre>
     ** 战斗奖励 
     * </pre>
     *
     * <code>.RewardResult reward = 4;</code>
     */
    cn.daxiang.protocol.game.CommonProtocol.RewardResultOrBuilder getRewardOrBuilder();

    /**
     * <pre>
     ** 战斗统计 
     * </pre>
     *
     * <code>.BattleStats battleStats = 5;</code>
     */
    boolean hasBattleStats();
    /**
     * <pre>
     ** 战斗统计 
     * </pre>
     *
     * <code>.BattleStats battleStats = 5;</code>
     */
    cn.daxiang.protocol.game.BattleProtocol.BattleStats getBattleStats();
    /**
     * <pre>
     ** 战斗统计 
     * </pre>
     *
     * <code>.BattleStats battleStats = 5;</code>
     */
    cn.daxiang.protocol.game.BattleProtocol.BattleStatsOrBuilder getBattleStatsOrBuilder();

    /**
     * <pre>
     ** 右边阵营历史战斗精灵 
     * </pre>
     *
     * <code>repeated .BattleSprite rightHistorySprite = 6;</code>
     */
    java.util.List<cn.daxiang.protocol.game.BattleProtocol.BattleSprite> 
        getRightHistorySpriteList();
    /**
     * <pre>
     ** 右边阵营历史战斗精灵 
     * </pre>
     *
     * <code>repeated .BattleSprite rightHistorySprite = 6;</code>
     */
    cn.daxiang.protocol.game.BattleProtocol.BattleSprite getRightHistorySprite(int index);
    /**
     * <pre>
     ** 右边阵营历史战斗精灵 
     * </pre>
     *
     * <code>repeated .BattleSprite rightHistorySprite = 6;</code>
     */
    int getRightHistorySpriteCount();
    /**
     * <pre>
     ** 右边阵营历史战斗精灵 
     * </pre>
     *
     * <code>repeated .BattleSprite rightHistorySprite = 6;</code>
     */
    java.util.List<? extends cn.daxiang.protocol.game.BattleProtocol.BattleSpriteOrBuilder> 
        getRightHistorySpriteOrBuilderList();
    /**
     * <pre>
     ** 右边阵营历史战斗精灵 
     * </pre>
     *
     * <code>repeated .BattleSprite rightHistorySprite = 6;</code>
     */
    cn.daxiang.protocol.game.BattleProtocol.BattleSpriteOrBuilder getRightHistorySpriteOrBuilder(
        int index);

    /**
     * <pre>
     ** 竞技场历史排名上升奖励列表 
     * </pre>
     *
     * <code>.RewardResult extraReward = 7;</code>
     */
    boolean hasExtraReward();
    /**
     * <pre>
     ** 竞技场历史排名上升奖励列表 
     * </pre>
     *
     * <code>.RewardResult extraReward = 7;</code>
     */
    cn.daxiang.protocol.game.CommonProtocol.RewardResult getExtraReward();
    /**
     * <pre>
     ** 竞技场历史排名上升奖励列表 
     * </pre>
     *
     * <code>.RewardResult extraReward = 7;</code>
     */
    cn.daxiang.protocol.game.CommonProtocol.RewardResultOrBuilder getExtraRewardOrBuilder();
  }
  /**
   * <pre>
   ** 战斗结果响应 
   * </pre>
   *
   * Protobuf type {@code BattleResultResponse}
   */
  public  static final class BattleResultResponse extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:BattleResultResponse)
      BattleResultResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use BattleResultResponse.newBuilder() to construct.
    private BattleResultResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private BattleResultResponse() {
      type_ = 0;
      members_ = java.util.Collections.emptyList();
      rightHistorySprite_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private BattleResultResponse(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              int rawValue = input.readEnum();

              type_ = rawValue;
              break;
            }
            case 18: {
              if (!((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
                members_ = new java.util.ArrayList<cn.daxiang.protocol.game.BattleProtocol.BattleMember>();
                mutable_bitField0_ |= 0x00000002;
              }
              members_.add(
                  input.readMessage(cn.daxiang.protocol.game.BattleProtocol.BattleMember.parser(), extensionRegistry));
              break;
            }
            case 26: {
              if (!((mutable_bitField0_ & 0x00000004) == 0x00000004)) {
                reports_ = com.google.protobuf.MapField.newMapField(
                    ReportsDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000004;
              }
              com.google.protobuf.MapEntry<java.lang.Integer, cn.daxiang.protocol.game.BattleProtocol.FightReportList>
              reports__ = input.readMessage(
                  ReportsDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              reports_.getMutableMap().put(
                  reports__.getKey(), reports__.getValue());
              break;
            }
            case 34: {
              cn.daxiang.protocol.game.CommonProtocol.RewardResult.Builder subBuilder = null;
              if (reward_ != null) {
                subBuilder = reward_.toBuilder();
              }
              reward_ = input.readMessage(cn.daxiang.protocol.game.CommonProtocol.RewardResult.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(reward_);
                reward_ = subBuilder.buildPartial();
              }

              break;
            }
            case 42: {
              cn.daxiang.protocol.game.BattleProtocol.BattleStats.Builder subBuilder = null;
              if (battleStats_ != null) {
                subBuilder = battleStats_.toBuilder();
              }
              battleStats_ = input.readMessage(cn.daxiang.protocol.game.BattleProtocol.BattleStats.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(battleStats_);
                battleStats_ = subBuilder.buildPartial();
              }

              break;
            }
            case 50: {
              if (!((mutable_bitField0_ & 0x00000020) == 0x00000020)) {
                rightHistorySprite_ = new java.util.ArrayList<cn.daxiang.protocol.game.BattleProtocol.BattleSprite>();
                mutable_bitField0_ |= 0x00000020;
              }
              rightHistorySprite_.add(
                  input.readMessage(cn.daxiang.protocol.game.BattleProtocol.BattleSprite.parser(), extensionRegistry));
              break;
            }
            case 58: {
              cn.daxiang.protocol.game.CommonProtocol.RewardResult.Builder subBuilder = null;
              if (extraReward_ != null) {
                subBuilder = extraReward_.toBuilder();
              }
              extraReward_ = input.readMessage(cn.daxiang.protocol.game.CommonProtocol.RewardResult.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(extraReward_);
                extraReward_ = subBuilder.buildPartial();
              }

              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
          members_ = java.util.Collections.unmodifiableList(members_);
        }
        if (((mutable_bitField0_ & 0x00000020) == 0x00000020)) {
          rightHistorySprite_ = java.util.Collections.unmodifiableList(rightHistorySprite_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.BattleProtocol.internal_static_BattleResultResponse_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 3:
          return internalGetReports();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.daxiang.protocol.game.BattleProtocol.internal_static_BattleResultResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.daxiang.protocol.game.BattleProtocol.BattleResultResponse.class, cn.daxiang.protocol.game.BattleProtocol.BattleResultResponse.Builder.class);
    }

    private int bitField0_;
    public static final int TYPE_FIELD_NUMBER = 1;
    private int type_;
    /**
     * <pre>
     ** 战斗类型 
     * </pre>
     *
     * <code>.BattleType type = 1;</code>
     */
    public int getTypeValue() {
      return type_;
    }
    /**
     * <pre>
     ** 战斗类型 
     * </pre>
     *
     * <code>.BattleType type = 1;</code>
     */
    public cn.daxiang.protocol.game.BattleProtocol.BattleType getType() {
      cn.daxiang.protocol.game.BattleProtocol.BattleType result = cn.daxiang.protocol.game.BattleProtocol.BattleType.valueOf(type_);
      return result == null ? cn.daxiang.protocol.game.BattleProtocol.BattleType.UNRECOGNIZED : result;
    }

    public static final int MEMBERS_FIELD_NUMBER = 2;
    private java.util.List<cn.daxiang.protocol.game.BattleProtocol.BattleMember> members_;
    /**
     * <pre>
     ** 成员列表 
     * </pre>
     *
     * <code>repeated .BattleMember members = 2;</code>
     */
    public java.util.List<cn.daxiang.protocol.game.BattleProtocol.BattleMember> getMembersList() {
      return members_;
    }
    /**
     * <pre>
     ** 成员列表 
     * </pre>
     *
     * <code>repeated .BattleMember members = 2;</code>
     */
    public java.util.List<? extends cn.daxiang.protocol.game.BattleProtocol.BattleMemberOrBuilder> 
        getMembersOrBuilderList() {
      return members_;
    }
    /**
     * <pre>
     ** 成员列表 
     * </pre>
     *
     * <code>repeated .BattleMember members = 2;</code>
     */
    public int getMembersCount() {
      return members_.size();
    }
    /**
     * <pre>
     ** 成员列表 
     * </pre>
     *
     * <code>repeated .BattleMember members = 2;</code>
     */
    public cn.daxiang.protocol.game.BattleProtocol.BattleMember getMembers(int index) {
      return members_.get(index);
    }
    /**
     * <pre>
     ** 成员列表 
     * </pre>
     *
     * <code>repeated .BattleMember members = 2;</code>
     */
    public cn.daxiang.protocol.game.BattleProtocol.BattleMemberOrBuilder getMembersOrBuilder(
        int index) {
      return members_.get(index);
    }

    public static final int REPORTS_FIELD_NUMBER = 3;
    private static final class ReportsDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Integer, cn.daxiang.protocol.game.BattleProtocol.FightReportList> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Integer, cn.daxiang.protocol.game.BattleProtocol.FightReportList>newDefaultInstance(
                  cn.daxiang.protocol.game.BattleProtocol.internal_static_BattleResultResponse_ReportsEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0,
                  com.google.protobuf.WireFormat.FieldType.MESSAGE,
                  cn.daxiang.protocol.game.BattleProtocol.FightReportList.getDefaultInstance());
    }
    private com.google.protobuf.MapField<
        java.lang.Integer, cn.daxiang.protocol.game.BattleProtocol.FightReportList> reports_;
    private com.google.protobuf.MapField<java.lang.Integer, cn.daxiang.protocol.game.BattleProtocol.FightReportList>
    internalGetReports() {
      if (reports_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            ReportsDefaultEntryHolder.defaultEntry);
      }
      return reports_;
    }

    public int getReportsCount() {
      return internalGetReports().getMap().size();
    }
    /**
     * <pre>
     ** 战斗报表 
     * </pre>
     *
     * <code>map&lt;int32, .FightReportList&gt; reports = 3;</code>
     */

    public boolean containsReports(
        int key) {
      
      return internalGetReports().getMap().containsKey(key);
    }
    /**
     * Use {@link #getReportsMap()} instead.
     */
    @java.lang.Deprecated
    public java.util.Map<java.lang.Integer, cn.daxiang.protocol.game.BattleProtocol.FightReportList> getReports() {
      return getReportsMap();
    }
    /**
     * <pre>
     ** 战斗报表 
     * </pre>
     *
     * <code>map&lt;int32, .FightReportList&gt; reports = 3;</code>
     */

    public java.util.Map<java.lang.Integer, cn.daxiang.protocol.game.BattleProtocol.FightReportList> getReportsMap() {
      return internalGetReports().getMap();
    }
    /**
     * <pre>
     ** 战斗报表 
     * </pre>
     *
     * <code>map&lt;int32, .FightReportList&gt; reports = 3;</code>
     */

    public cn.daxiang.protocol.game.BattleProtocol.FightReportList getReportsOrDefault(
        int key,
        cn.daxiang.protocol.game.BattleProtocol.FightReportList defaultValue) {
      
      java.util.Map<java.lang.Integer, cn.daxiang.protocol.game.BattleProtocol.FightReportList> map =
          internalGetReports().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <pre>
     ** 战斗报表 
     * </pre>
     *
     * <code>map&lt;int32, .FightReportList&gt; reports = 3;</code>
     */

    public cn.daxiang.protocol.game.BattleProtocol.FightReportList getReportsOrThrow(
        int key) {
      
      java.util.Map<java.lang.Integer, cn.daxiang.protocol.game.BattleProtocol.FightReportList> map =
          internalGetReports().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    public static final int REWARD_FIELD_NUMBER = 4;
    private cn.daxiang.protocol.game.CommonProtocol.RewardResult reward_;
    /**
     * <pre>
     ** 战斗奖励 
     * </pre>
     *
     * <code>.RewardResult reward = 4;</code>
     */
    public boolean hasReward() {
      return reward_ != null;
    }
    /**
     * <pre>
     ** 战斗奖励 
     * </pre>
     *
     * <code>.RewardResult reward = 4;</code>
     */
    public cn.daxiang.protocol.game.CommonProtocol.RewardResult getReward() {
      return reward_ == null ? cn.daxiang.protocol.game.CommonProtocol.RewardResult.getDefaultInstance() : reward_;
    }
    /**
     * <pre>
     ** 战斗奖励 
     * </pre>
     *
     * <code>.RewardResult reward = 4;</code>
     */
    public cn.daxiang.protocol.game.CommonProtocol.RewardResultOrBuilder getRewardOrBuilder() {
      return getReward();
    }

    public static final int BATTLESTATS_FIELD_NUMBER = 5;
    private cn.daxiang.protocol.game.BattleProtocol.BattleStats battleStats_;
    /**
     * <pre>
     ** 战斗统计 
     * </pre>
     *
     * <code>.BattleStats battleStats = 5;</code>
     */
    public boolean hasBattleStats() {
      return battleStats_ != null;
    }
    /**
     * <pre>
     ** 战斗统计 
     * </pre>
     *
     * <code>.BattleStats battleStats = 5;</code>
     */
    public cn.daxiang.protocol.game.BattleProtocol.BattleStats getBattleStats() {
      return battleStats_ == null ? cn.daxiang.protocol.game.BattleProtocol.BattleStats.getDefaultInstance() : battleStats_;
    }
    /**
     * <pre>
     ** 战斗统计 
     * </pre>
     *
     * <code>.BattleStats battleStats = 5;</code>
     */
    public cn.daxiang.protocol.game.BattleProtocol.BattleStatsOrBuilder getBattleStatsOrBuilder() {
      return getBattleStats();
    }

    public static final int RIGHTHISTORYSPRITE_FIELD_NUMBER = 6;
    private java.util.List<cn.daxiang.protocol.game.BattleProtocol.BattleSprite> rightHistorySprite_;
    /**
     * <pre>
     ** 右边阵营历史战斗精灵 
     * </pre>
     *
     * <code>repeated .BattleSprite rightHistorySprite = 6;</code>
     */
    public java.util.List<cn.daxiang.protocol.game.BattleProtocol.BattleSprite> getRightHistorySpriteList() {
      return rightHistorySprite_;
    }
    /**
     * <pre>
     ** 右边阵营历史战斗精灵 
     * </pre>
     *
     * <code>repeated .BattleSprite rightHistorySprite = 6;</code>
     */
    public java.util.List<? extends cn.daxiang.protocol.game.BattleProtocol.BattleSpriteOrBuilder> 
        getRightHistorySpriteOrBuilderList() {
      return rightHistorySprite_;
    }
    /**
     * <pre>
     ** 右边阵营历史战斗精灵 
     * </pre>
     *
     * <code>repeated .BattleSprite rightHistorySprite = 6;</code>
     */
    public int getRightHistorySpriteCount() {
      return rightHistorySprite_.size();
    }
    /**
     * <pre>
     ** 右边阵营历史战斗精灵 
     * </pre>
     *
     * <code>repeated .BattleSprite rightHistorySprite = 6;</code>
     */
    public cn.daxiang.protocol.game.BattleProtocol.BattleSprite getRightHistorySprite(int index) {
      return rightHistorySprite_.get(index);
    }
    /**
     * <pre>
     ** 右边阵营历史战斗精灵 
     * </pre>
     *
     * <code>repeated .BattleSprite rightHistorySprite = 6;</code>
     */
    public cn.daxiang.protocol.game.BattleProtocol.BattleSpriteOrBuilder getRightHistorySpriteOrBuilder(
        int index) {
      return rightHistorySprite_.get(index);
    }

    public static final int EXTRAREWARD_FIELD_NUMBER = 7;
    private cn.daxiang.protocol.game.CommonProtocol.RewardResult extraReward_;
    /**
     * <pre>
     ** 竞技场历史排名上升奖励列表 
     * </pre>
     *
     * <code>.RewardResult extraReward = 7;</code>
     */
    public boolean hasExtraReward() {
      return extraReward_ != null;
    }
    /**
     * <pre>
     ** 竞技场历史排名上升奖励列表 
     * </pre>
     *
     * <code>.RewardResult extraReward = 7;</code>
     */
    public cn.daxiang.protocol.game.CommonProtocol.RewardResult getExtraReward() {
      return extraReward_ == null ? cn.daxiang.protocol.game.CommonProtocol.RewardResult.getDefaultInstance() : extraReward_;
    }
    /**
     * <pre>
     ** 竞技场历史排名上升奖励列表 
     * </pre>
     *
     * <code>.RewardResult extraReward = 7;</code>
     */
    public cn.daxiang.protocol.game.CommonProtocol.RewardResultOrBuilder getExtraRewardOrBuilder() {
      return getExtraReward();
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (type_ != cn.daxiang.protocol.game.BattleProtocol.BattleType.BATTLE_TYPE_NONE.getNumber()) {
        output.writeEnum(1, type_);
      }
      for (int i = 0; i < members_.size(); i++) {
        output.writeMessage(2, members_.get(i));
      }
      com.google.protobuf.GeneratedMessageV3
        .serializeIntegerMapTo(
          output,
          internalGetReports(),
          ReportsDefaultEntryHolder.defaultEntry,
          3);
      if (reward_ != null) {
        output.writeMessage(4, getReward());
      }
      if (battleStats_ != null) {
        output.writeMessage(5, getBattleStats());
      }
      for (int i = 0; i < rightHistorySprite_.size(); i++) {
        output.writeMessage(6, rightHistorySprite_.get(i));
      }
      if (extraReward_ != null) {
        output.writeMessage(7, getExtraReward());
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (type_ != cn.daxiang.protocol.game.BattleProtocol.BattleType.BATTLE_TYPE_NONE.getNumber()) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, type_);
      }
      for (int i = 0; i < members_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, members_.get(i));
      }
      for (java.util.Map.Entry<java.lang.Integer, cn.daxiang.protocol.game.BattleProtocol.FightReportList> entry
           : internalGetReports().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Integer, cn.daxiang.protocol.game.BattleProtocol.FightReportList>
        reports__ = ReportsDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(3, reports__);
      }
      if (reward_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, getReward());
      }
      if (battleStats_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(5, getBattleStats());
      }
      for (int i = 0; i < rightHistorySprite_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(6, rightHistorySprite_.get(i));
      }
      if (extraReward_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(7, getExtraReward());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof cn.daxiang.protocol.game.BattleProtocol.BattleResultResponse)) {
        return super.equals(obj);
      }
      cn.daxiang.protocol.game.BattleProtocol.BattleResultResponse other = (cn.daxiang.protocol.game.BattleProtocol.BattleResultResponse) obj;

      boolean result = true;
      result = result && type_ == other.type_;
      result = result && getMembersList()
          .equals(other.getMembersList());
      result = result && internalGetReports().equals(
          other.internalGetReports());
      result = result && (hasReward() == other.hasReward());
      if (hasReward()) {
        result = result && getReward()
            .equals(other.getReward());
      }
      result = result && (hasBattleStats() == other.hasBattleStats());
      if (hasBattleStats()) {
        result = result && getBattleStats()
            .equals(other.getBattleStats());
      }
      result = result && getRightHistorySpriteList()
          .equals(other.getRightHistorySpriteList());
      result = result && (hasExtraReward() == other.hasExtraReward());
      if (hasExtraReward()) {
        result = result && getExtraReward()
            .equals(other.getExtraReward());
      }
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + type_;
      if (getMembersCount() > 0) {
        hash = (37 * hash) + MEMBERS_FIELD_NUMBER;
        hash = (53 * hash) + getMembersList().hashCode();
      }
      if (!internalGetReports().getMap().isEmpty()) {
        hash = (37 * hash) + REPORTS_FIELD_NUMBER;
        hash = (53 * hash) + internalGetReports().hashCode();
      }
      if (hasReward()) {
        hash = (37 * hash) + REWARD_FIELD_NUMBER;
        hash = (53 * hash) + getReward().hashCode();
      }
      if (hasBattleStats()) {
        hash = (37 * hash) + BATTLESTATS_FIELD_NUMBER;
        hash = (53 * hash) + getBattleStats().hashCode();
      }
      if (getRightHistorySpriteCount() > 0) {
        hash = (37 * hash) + RIGHTHISTORYSPRITE_FIELD_NUMBER;
        hash = (53 * hash) + getRightHistorySpriteList().hashCode();
      }
      if (hasExtraReward()) {
        hash = (37 * hash) + EXTRAREWARD_FIELD_NUMBER;
        hash = (53 * hash) + getExtraReward().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static cn.daxiang.protocol.game.BattleProtocol.BattleResultResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.BattleResultResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.BattleResultResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.BattleResultResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.BattleResultResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.BattleResultResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.BattleResultResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.BattleResultResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.BattleResultResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.BattleResultResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.BattleResultResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.BattleResultResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(cn.daxiang.protocol.game.BattleProtocol.BattleResultResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** 战斗结果响应 
     * </pre>
     *
     * Protobuf type {@code BattleResultResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:BattleResultResponse)
        cn.daxiang.protocol.game.BattleProtocol.BattleResultResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.daxiang.protocol.game.BattleProtocol.internal_static_BattleResultResponse_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 3:
            return internalGetReports();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 3:
            return internalGetMutableReports();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.daxiang.protocol.game.BattleProtocol.internal_static_BattleResultResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.daxiang.protocol.game.BattleProtocol.BattleResultResponse.class, cn.daxiang.protocol.game.BattleProtocol.BattleResultResponse.Builder.class);
      }

      // Construct using cn.daxiang.protocol.game.BattleProtocol.BattleResultResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getMembersFieldBuilder();
          getRightHistorySpriteFieldBuilder();
        }
      }
      public Builder clear() {
        super.clear();
        type_ = 0;

        if (membersBuilder_ == null) {
          members_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
        } else {
          membersBuilder_.clear();
        }
        internalGetMutableReports().clear();
        if (rewardBuilder_ == null) {
          reward_ = null;
        } else {
          reward_ = null;
          rewardBuilder_ = null;
        }
        if (battleStatsBuilder_ == null) {
          battleStats_ = null;
        } else {
          battleStats_ = null;
          battleStatsBuilder_ = null;
        }
        if (rightHistorySpriteBuilder_ == null) {
          rightHistorySprite_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000020);
        } else {
          rightHistorySpriteBuilder_.clear();
        }
        if (extraRewardBuilder_ == null) {
          extraReward_ = null;
        } else {
          extraReward_ = null;
          extraRewardBuilder_ = null;
        }
        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return cn.daxiang.protocol.game.BattleProtocol.internal_static_BattleResultResponse_descriptor;
      }

      public cn.daxiang.protocol.game.BattleProtocol.BattleResultResponse getDefaultInstanceForType() {
        return cn.daxiang.protocol.game.BattleProtocol.BattleResultResponse.getDefaultInstance();
      }

      public cn.daxiang.protocol.game.BattleProtocol.BattleResultResponse build() {
        cn.daxiang.protocol.game.BattleProtocol.BattleResultResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public cn.daxiang.protocol.game.BattleProtocol.BattleResultResponse buildPartial() {
        cn.daxiang.protocol.game.BattleProtocol.BattleResultResponse result = new cn.daxiang.protocol.game.BattleProtocol.BattleResultResponse(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        result.type_ = type_;
        if (membersBuilder_ == null) {
          if (((bitField0_ & 0x00000002) == 0x00000002)) {
            members_ = java.util.Collections.unmodifiableList(members_);
            bitField0_ = (bitField0_ & ~0x00000002);
          }
          result.members_ = members_;
        } else {
          result.members_ = membersBuilder_.build();
        }
        result.reports_ = internalGetReports();
        result.reports_.makeImmutable();
        if (rewardBuilder_ == null) {
          result.reward_ = reward_;
        } else {
          result.reward_ = rewardBuilder_.build();
        }
        if (battleStatsBuilder_ == null) {
          result.battleStats_ = battleStats_;
        } else {
          result.battleStats_ = battleStatsBuilder_.build();
        }
        if (rightHistorySpriteBuilder_ == null) {
          if (((bitField0_ & 0x00000020) == 0x00000020)) {
            rightHistorySprite_ = java.util.Collections.unmodifiableList(rightHistorySprite_);
            bitField0_ = (bitField0_ & ~0x00000020);
          }
          result.rightHistorySprite_ = rightHistorySprite_;
        } else {
          result.rightHistorySprite_ = rightHistorySpriteBuilder_.build();
        }
        if (extraRewardBuilder_ == null) {
          result.extraReward_ = extraReward_;
        } else {
          result.extraReward_ = extraRewardBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof cn.daxiang.protocol.game.BattleProtocol.BattleResultResponse) {
          return mergeFrom((cn.daxiang.protocol.game.BattleProtocol.BattleResultResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(cn.daxiang.protocol.game.BattleProtocol.BattleResultResponse other) {
        if (other == cn.daxiang.protocol.game.BattleProtocol.BattleResultResponse.getDefaultInstance()) return this;
        if (other.type_ != 0) {
          setTypeValue(other.getTypeValue());
        }
        if (membersBuilder_ == null) {
          if (!other.members_.isEmpty()) {
            if (members_.isEmpty()) {
              members_ = other.members_;
              bitField0_ = (bitField0_ & ~0x00000002);
            } else {
              ensureMembersIsMutable();
              members_.addAll(other.members_);
            }
            onChanged();
          }
        } else {
          if (!other.members_.isEmpty()) {
            if (membersBuilder_.isEmpty()) {
              membersBuilder_.dispose();
              membersBuilder_ = null;
              members_ = other.members_;
              bitField0_ = (bitField0_ & ~0x00000002);
              membersBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getMembersFieldBuilder() : null;
            } else {
              membersBuilder_.addAllMessages(other.members_);
            }
          }
        }
        internalGetMutableReports().mergeFrom(
            other.internalGetReports());
        if (other.hasReward()) {
          mergeReward(other.getReward());
        }
        if (other.hasBattleStats()) {
          mergeBattleStats(other.getBattleStats());
        }
        if (rightHistorySpriteBuilder_ == null) {
          if (!other.rightHistorySprite_.isEmpty()) {
            if (rightHistorySprite_.isEmpty()) {
              rightHistorySprite_ = other.rightHistorySprite_;
              bitField0_ = (bitField0_ & ~0x00000020);
            } else {
              ensureRightHistorySpriteIsMutable();
              rightHistorySprite_.addAll(other.rightHistorySprite_);
            }
            onChanged();
          }
        } else {
          if (!other.rightHistorySprite_.isEmpty()) {
            if (rightHistorySpriteBuilder_.isEmpty()) {
              rightHistorySpriteBuilder_.dispose();
              rightHistorySpriteBuilder_ = null;
              rightHistorySprite_ = other.rightHistorySprite_;
              bitField0_ = (bitField0_ & ~0x00000020);
              rightHistorySpriteBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getRightHistorySpriteFieldBuilder() : null;
            } else {
              rightHistorySpriteBuilder_.addAllMessages(other.rightHistorySprite_);
            }
          }
        }
        if (other.hasExtraReward()) {
          mergeExtraReward(other.getExtraReward());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        cn.daxiang.protocol.game.BattleProtocol.BattleResultResponse parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (cn.daxiang.protocol.game.BattleProtocol.BattleResultResponse) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int type_ = 0;
      /**
       * <pre>
       ** 战斗类型 
       * </pre>
       *
       * <code>.BattleType type = 1;</code>
       */
      public int getTypeValue() {
        return type_;
      }
      /**
       * <pre>
       ** 战斗类型 
       * </pre>
       *
       * <code>.BattleType type = 1;</code>
       */
      public Builder setTypeValue(int value) {
        type_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 战斗类型 
       * </pre>
       *
       * <code>.BattleType type = 1;</code>
       */
      public cn.daxiang.protocol.game.BattleProtocol.BattleType getType() {
        cn.daxiang.protocol.game.BattleProtocol.BattleType result = cn.daxiang.protocol.game.BattleProtocol.BattleType.valueOf(type_);
        return result == null ? cn.daxiang.protocol.game.BattleProtocol.BattleType.UNRECOGNIZED : result;
      }
      /**
       * <pre>
       ** 战斗类型 
       * </pre>
       *
       * <code>.BattleType type = 1;</code>
       */
      public Builder setType(cn.daxiang.protocol.game.BattleProtocol.BattleType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        
        type_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 战斗类型 
       * </pre>
       *
       * <code>.BattleType type = 1;</code>
       */
      public Builder clearType() {
        
        type_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<cn.daxiang.protocol.game.BattleProtocol.BattleMember> members_ =
        java.util.Collections.emptyList();
      private void ensureMembersIsMutable() {
        if (!((bitField0_ & 0x00000002) == 0x00000002)) {
          members_ = new java.util.ArrayList<cn.daxiang.protocol.game.BattleProtocol.BattleMember>(members_);
          bitField0_ |= 0x00000002;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          cn.daxiang.protocol.game.BattleProtocol.BattleMember, cn.daxiang.protocol.game.BattleProtocol.BattleMember.Builder, cn.daxiang.protocol.game.BattleProtocol.BattleMemberOrBuilder> membersBuilder_;

      /**
       * <pre>
       ** 成员列表 
       * </pre>
       *
       * <code>repeated .BattleMember members = 2;</code>
       */
      public java.util.List<cn.daxiang.protocol.game.BattleProtocol.BattleMember> getMembersList() {
        if (membersBuilder_ == null) {
          return java.util.Collections.unmodifiableList(members_);
        } else {
          return membersBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       ** 成员列表 
       * </pre>
       *
       * <code>repeated .BattleMember members = 2;</code>
       */
      public int getMembersCount() {
        if (membersBuilder_ == null) {
          return members_.size();
        } else {
          return membersBuilder_.getCount();
        }
      }
      /**
       * <pre>
       ** 成员列表 
       * </pre>
       *
       * <code>repeated .BattleMember members = 2;</code>
       */
      public cn.daxiang.protocol.game.BattleProtocol.BattleMember getMembers(int index) {
        if (membersBuilder_ == null) {
          return members_.get(index);
        } else {
          return membersBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       ** 成员列表 
       * </pre>
       *
       * <code>repeated .BattleMember members = 2;</code>
       */
      public Builder setMembers(
          int index, cn.daxiang.protocol.game.BattleProtocol.BattleMember value) {
        if (membersBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureMembersIsMutable();
          members_.set(index, value);
          onChanged();
        } else {
          membersBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       ** 成员列表 
       * </pre>
       *
       * <code>repeated .BattleMember members = 2;</code>
       */
      public Builder setMembers(
          int index, cn.daxiang.protocol.game.BattleProtocol.BattleMember.Builder builderForValue) {
        if (membersBuilder_ == null) {
          ensureMembersIsMutable();
          members_.set(index, builderForValue.build());
          onChanged();
        } else {
          membersBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       ** 成员列表 
       * </pre>
       *
       * <code>repeated .BattleMember members = 2;</code>
       */
      public Builder addMembers(cn.daxiang.protocol.game.BattleProtocol.BattleMember value) {
        if (membersBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureMembersIsMutable();
          members_.add(value);
          onChanged();
        } else {
          membersBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       ** 成员列表 
       * </pre>
       *
       * <code>repeated .BattleMember members = 2;</code>
       */
      public Builder addMembers(
          int index, cn.daxiang.protocol.game.BattleProtocol.BattleMember value) {
        if (membersBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureMembersIsMutable();
          members_.add(index, value);
          onChanged();
        } else {
          membersBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       ** 成员列表 
       * </pre>
       *
       * <code>repeated .BattleMember members = 2;</code>
       */
      public Builder addMembers(
          cn.daxiang.protocol.game.BattleProtocol.BattleMember.Builder builderForValue) {
        if (membersBuilder_ == null) {
          ensureMembersIsMutable();
          members_.add(builderForValue.build());
          onChanged();
        } else {
          membersBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       ** 成员列表 
       * </pre>
       *
       * <code>repeated .BattleMember members = 2;</code>
       */
      public Builder addMembers(
          int index, cn.daxiang.protocol.game.BattleProtocol.BattleMember.Builder builderForValue) {
        if (membersBuilder_ == null) {
          ensureMembersIsMutable();
          members_.add(index, builderForValue.build());
          onChanged();
        } else {
          membersBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       ** 成员列表 
       * </pre>
       *
       * <code>repeated .BattleMember members = 2;</code>
       */
      public Builder addAllMembers(
          java.lang.Iterable<? extends cn.daxiang.protocol.game.BattleProtocol.BattleMember> values) {
        if (membersBuilder_ == null) {
          ensureMembersIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, members_);
          onChanged();
        } else {
          membersBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       ** 成员列表 
       * </pre>
       *
       * <code>repeated .BattleMember members = 2;</code>
       */
      public Builder clearMembers() {
        if (membersBuilder_ == null) {
          members_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
        } else {
          membersBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       ** 成员列表 
       * </pre>
       *
       * <code>repeated .BattleMember members = 2;</code>
       */
      public Builder removeMembers(int index) {
        if (membersBuilder_ == null) {
          ensureMembersIsMutable();
          members_.remove(index);
          onChanged();
        } else {
          membersBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       ** 成员列表 
       * </pre>
       *
       * <code>repeated .BattleMember members = 2;</code>
       */
      public cn.daxiang.protocol.game.BattleProtocol.BattleMember.Builder getMembersBuilder(
          int index) {
        return getMembersFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       ** 成员列表 
       * </pre>
       *
       * <code>repeated .BattleMember members = 2;</code>
       */
      public cn.daxiang.protocol.game.BattleProtocol.BattleMemberOrBuilder getMembersOrBuilder(
          int index) {
        if (membersBuilder_ == null) {
          return members_.get(index);  } else {
          return membersBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       ** 成员列表 
       * </pre>
       *
       * <code>repeated .BattleMember members = 2;</code>
       */
      public java.util.List<? extends cn.daxiang.protocol.game.BattleProtocol.BattleMemberOrBuilder> 
           getMembersOrBuilderList() {
        if (membersBuilder_ != null) {
          return membersBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(members_);
        }
      }
      /**
       * <pre>
       ** 成员列表 
       * </pre>
       *
       * <code>repeated .BattleMember members = 2;</code>
       */
      public cn.daxiang.protocol.game.BattleProtocol.BattleMember.Builder addMembersBuilder() {
        return getMembersFieldBuilder().addBuilder(
            cn.daxiang.protocol.game.BattleProtocol.BattleMember.getDefaultInstance());
      }
      /**
       * <pre>
       ** 成员列表 
       * </pre>
       *
       * <code>repeated .BattleMember members = 2;</code>
       */
      public cn.daxiang.protocol.game.BattleProtocol.BattleMember.Builder addMembersBuilder(
          int index) {
        return getMembersFieldBuilder().addBuilder(
            index, cn.daxiang.protocol.game.BattleProtocol.BattleMember.getDefaultInstance());
      }
      /**
       * <pre>
       ** 成员列表 
       * </pre>
       *
       * <code>repeated .BattleMember members = 2;</code>
       */
      public java.util.List<cn.daxiang.protocol.game.BattleProtocol.BattleMember.Builder> 
           getMembersBuilderList() {
        return getMembersFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          cn.daxiang.protocol.game.BattleProtocol.BattleMember, cn.daxiang.protocol.game.BattleProtocol.BattleMember.Builder, cn.daxiang.protocol.game.BattleProtocol.BattleMemberOrBuilder> 
          getMembersFieldBuilder() {
        if (membersBuilder_ == null) {
          membersBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              cn.daxiang.protocol.game.BattleProtocol.BattleMember, cn.daxiang.protocol.game.BattleProtocol.BattleMember.Builder, cn.daxiang.protocol.game.BattleProtocol.BattleMemberOrBuilder>(
                  members_,
                  ((bitField0_ & 0x00000002) == 0x00000002),
                  getParentForChildren(),
                  isClean());
          members_ = null;
        }
        return membersBuilder_;
      }

      private com.google.protobuf.MapField<
          java.lang.Integer, cn.daxiang.protocol.game.BattleProtocol.FightReportList> reports_;
      private com.google.protobuf.MapField<java.lang.Integer, cn.daxiang.protocol.game.BattleProtocol.FightReportList>
      internalGetReports() {
        if (reports_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              ReportsDefaultEntryHolder.defaultEntry);
        }
        return reports_;
      }
      private com.google.protobuf.MapField<java.lang.Integer, cn.daxiang.protocol.game.BattleProtocol.FightReportList>
      internalGetMutableReports() {
        onChanged();;
        if (reports_ == null) {
          reports_ = com.google.protobuf.MapField.newMapField(
              ReportsDefaultEntryHolder.defaultEntry);
        }
        if (!reports_.isMutable()) {
          reports_ = reports_.copy();
        }
        return reports_;
      }

      public int getReportsCount() {
        return internalGetReports().getMap().size();
      }
      /**
       * <pre>
       ** 战斗报表 
       * </pre>
       *
       * <code>map&lt;int32, .FightReportList&gt; reports = 3;</code>
       */

      public boolean containsReports(
          int key) {
        
        return internalGetReports().getMap().containsKey(key);
      }
      /**
       * Use {@link #getReportsMap()} instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, cn.daxiang.protocol.game.BattleProtocol.FightReportList> getReports() {
        return getReportsMap();
      }
      /**
       * <pre>
       ** 战斗报表 
       * </pre>
       *
       * <code>map&lt;int32, .FightReportList&gt; reports = 3;</code>
       */

      public java.util.Map<java.lang.Integer, cn.daxiang.protocol.game.BattleProtocol.FightReportList> getReportsMap() {
        return internalGetReports().getMap();
      }
      /**
       * <pre>
       ** 战斗报表 
       * </pre>
       *
       * <code>map&lt;int32, .FightReportList&gt; reports = 3;</code>
       */

      public cn.daxiang.protocol.game.BattleProtocol.FightReportList getReportsOrDefault(
          int key,
          cn.daxiang.protocol.game.BattleProtocol.FightReportList defaultValue) {
        
        java.util.Map<java.lang.Integer, cn.daxiang.protocol.game.BattleProtocol.FightReportList> map =
            internalGetReports().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <pre>
       ** 战斗报表 
       * </pre>
       *
       * <code>map&lt;int32, .FightReportList&gt; reports = 3;</code>
       */

      public cn.daxiang.protocol.game.BattleProtocol.FightReportList getReportsOrThrow(
          int key) {
        
        java.util.Map<java.lang.Integer, cn.daxiang.protocol.game.BattleProtocol.FightReportList> map =
            internalGetReports().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearReports() {
        internalGetMutableReports().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <pre>
       ** 战斗报表 
       * </pre>
       *
       * <code>map&lt;int32, .FightReportList&gt; reports = 3;</code>
       */

      public Builder removeReports(
          int key) {
        
        internalGetMutableReports().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, cn.daxiang.protocol.game.BattleProtocol.FightReportList>
      getMutableReports() {
        return internalGetMutableReports().getMutableMap();
      }
      /**
       * <pre>
       ** 战斗报表 
       * </pre>
       *
       * <code>map&lt;int32, .FightReportList&gt; reports = 3;</code>
       */
      public Builder putReports(
          int key,
          cn.daxiang.protocol.game.BattleProtocol.FightReportList value) {
        
        if (value == null) { throw new java.lang.NullPointerException(); }
        internalGetMutableReports().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <pre>
       ** 战斗报表 
       * </pre>
       *
       * <code>map&lt;int32, .FightReportList&gt; reports = 3;</code>
       */

      public Builder putAllReports(
          java.util.Map<java.lang.Integer, cn.daxiang.protocol.game.BattleProtocol.FightReportList> values) {
        internalGetMutableReports().getMutableMap()
            .putAll(values);
        return this;
      }

      private cn.daxiang.protocol.game.CommonProtocol.RewardResult reward_ = null;
      private com.google.protobuf.SingleFieldBuilderV3<
          cn.daxiang.protocol.game.CommonProtocol.RewardResult, cn.daxiang.protocol.game.CommonProtocol.RewardResult.Builder, cn.daxiang.protocol.game.CommonProtocol.RewardResultOrBuilder> rewardBuilder_;
      /**
       * <pre>
       ** 战斗奖励 
       * </pre>
       *
       * <code>.RewardResult reward = 4;</code>
       */
      public boolean hasReward() {
        return rewardBuilder_ != null || reward_ != null;
      }
      /**
       * <pre>
       ** 战斗奖励 
       * </pre>
       *
       * <code>.RewardResult reward = 4;</code>
       */
      public cn.daxiang.protocol.game.CommonProtocol.RewardResult getReward() {
        if (rewardBuilder_ == null) {
          return reward_ == null ? cn.daxiang.protocol.game.CommonProtocol.RewardResult.getDefaultInstance() : reward_;
        } else {
          return rewardBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       ** 战斗奖励 
       * </pre>
       *
       * <code>.RewardResult reward = 4;</code>
       */
      public Builder setReward(cn.daxiang.protocol.game.CommonProtocol.RewardResult value) {
        if (rewardBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          reward_ = value;
          onChanged();
        } else {
          rewardBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <pre>
       ** 战斗奖励 
       * </pre>
       *
       * <code>.RewardResult reward = 4;</code>
       */
      public Builder setReward(
          cn.daxiang.protocol.game.CommonProtocol.RewardResult.Builder builderForValue) {
        if (rewardBuilder_ == null) {
          reward_ = builderForValue.build();
          onChanged();
        } else {
          rewardBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <pre>
       ** 战斗奖励 
       * </pre>
       *
       * <code>.RewardResult reward = 4;</code>
       */
      public Builder mergeReward(cn.daxiang.protocol.game.CommonProtocol.RewardResult value) {
        if (rewardBuilder_ == null) {
          if (reward_ != null) {
            reward_ =
              cn.daxiang.protocol.game.CommonProtocol.RewardResult.newBuilder(reward_).mergeFrom(value).buildPartial();
          } else {
            reward_ = value;
          }
          onChanged();
        } else {
          rewardBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <pre>
       ** 战斗奖励 
       * </pre>
       *
       * <code>.RewardResult reward = 4;</code>
       */
      public Builder clearReward() {
        if (rewardBuilder_ == null) {
          reward_ = null;
          onChanged();
        } else {
          reward_ = null;
          rewardBuilder_ = null;
        }

        return this;
      }
      /**
       * <pre>
       ** 战斗奖励 
       * </pre>
       *
       * <code>.RewardResult reward = 4;</code>
       */
      public cn.daxiang.protocol.game.CommonProtocol.RewardResult.Builder getRewardBuilder() {
        
        onChanged();
        return getRewardFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       ** 战斗奖励 
       * </pre>
       *
       * <code>.RewardResult reward = 4;</code>
       */
      public cn.daxiang.protocol.game.CommonProtocol.RewardResultOrBuilder getRewardOrBuilder() {
        if (rewardBuilder_ != null) {
          return rewardBuilder_.getMessageOrBuilder();
        } else {
          return reward_ == null ?
              cn.daxiang.protocol.game.CommonProtocol.RewardResult.getDefaultInstance() : reward_;
        }
      }
      /**
       * <pre>
       ** 战斗奖励 
       * </pre>
       *
       * <code>.RewardResult reward = 4;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          cn.daxiang.protocol.game.CommonProtocol.RewardResult, cn.daxiang.protocol.game.CommonProtocol.RewardResult.Builder, cn.daxiang.protocol.game.CommonProtocol.RewardResultOrBuilder> 
          getRewardFieldBuilder() {
        if (rewardBuilder_ == null) {
          rewardBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              cn.daxiang.protocol.game.CommonProtocol.RewardResult, cn.daxiang.protocol.game.CommonProtocol.RewardResult.Builder, cn.daxiang.protocol.game.CommonProtocol.RewardResultOrBuilder>(
                  getReward(),
                  getParentForChildren(),
                  isClean());
          reward_ = null;
        }
        return rewardBuilder_;
      }

      private cn.daxiang.protocol.game.BattleProtocol.BattleStats battleStats_ = null;
      private com.google.protobuf.SingleFieldBuilderV3<
          cn.daxiang.protocol.game.BattleProtocol.BattleStats, cn.daxiang.protocol.game.BattleProtocol.BattleStats.Builder, cn.daxiang.protocol.game.BattleProtocol.BattleStatsOrBuilder> battleStatsBuilder_;
      /**
       * <pre>
       ** 战斗统计 
       * </pre>
       *
       * <code>.BattleStats battleStats = 5;</code>
       */
      public boolean hasBattleStats() {
        return battleStatsBuilder_ != null || battleStats_ != null;
      }
      /**
       * <pre>
       ** 战斗统计 
       * </pre>
       *
       * <code>.BattleStats battleStats = 5;</code>
       */
      public cn.daxiang.protocol.game.BattleProtocol.BattleStats getBattleStats() {
        if (battleStatsBuilder_ == null) {
          return battleStats_ == null ? cn.daxiang.protocol.game.BattleProtocol.BattleStats.getDefaultInstance() : battleStats_;
        } else {
          return battleStatsBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       ** 战斗统计 
       * </pre>
       *
       * <code>.BattleStats battleStats = 5;</code>
       */
      public Builder setBattleStats(cn.daxiang.protocol.game.BattleProtocol.BattleStats value) {
        if (battleStatsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          battleStats_ = value;
          onChanged();
        } else {
          battleStatsBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <pre>
       ** 战斗统计 
       * </pre>
       *
       * <code>.BattleStats battleStats = 5;</code>
       */
      public Builder setBattleStats(
          cn.daxiang.protocol.game.BattleProtocol.BattleStats.Builder builderForValue) {
        if (battleStatsBuilder_ == null) {
          battleStats_ = builderForValue.build();
          onChanged();
        } else {
          battleStatsBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <pre>
       ** 战斗统计 
       * </pre>
       *
       * <code>.BattleStats battleStats = 5;</code>
       */
      public Builder mergeBattleStats(cn.daxiang.protocol.game.BattleProtocol.BattleStats value) {
        if (battleStatsBuilder_ == null) {
          if (battleStats_ != null) {
            battleStats_ =
              cn.daxiang.protocol.game.BattleProtocol.BattleStats.newBuilder(battleStats_).mergeFrom(value).buildPartial();
          } else {
            battleStats_ = value;
          }
          onChanged();
        } else {
          battleStatsBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <pre>
       ** 战斗统计 
       * </pre>
       *
       * <code>.BattleStats battleStats = 5;</code>
       */
      public Builder clearBattleStats() {
        if (battleStatsBuilder_ == null) {
          battleStats_ = null;
          onChanged();
        } else {
          battleStats_ = null;
          battleStatsBuilder_ = null;
        }

        return this;
      }
      /**
       * <pre>
       ** 战斗统计 
       * </pre>
       *
       * <code>.BattleStats battleStats = 5;</code>
       */
      public cn.daxiang.protocol.game.BattleProtocol.BattleStats.Builder getBattleStatsBuilder() {
        
        onChanged();
        return getBattleStatsFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       ** 战斗统计 
       * </pre>
       *
       * <code>.BattleStats battleStats = 5;</code>
       */
      public cn.daxiang.protocol.game.BattleProtocol.BattleStatsOrBuilder getBattleStatsOrBuilder() {
        if (battleStatsBuilder_ != null) {
          return battleStatsBuilder_.getMessageOrBuilder();
        } else {
          return battleStats_ == null ?
              cn.daxiang.protocol.game.BattleProtocol.BattleStats.getDefaultInstance() : battleStats_;
        }
      }
      /**
       * <pre>
       ** 战斗统计 
       * </pre>
       *
       * <code>.BattleStats battleStats = 5;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          cn.daxiang.protocol.game.BattleProtocol.BattleStats, cn.daxiang.protocol.game.BattleProtocol.BattleStats.Builder, cn.daxiang.protocol.game.BattleProtocol.BattleStatsOrBuilder> 
          getBattleStatsFieldBuilder() {
        if (battleStatsBuilder_ == null) {
          battleStatsBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              cn.daxiang.protocol.game.BattleProtocol.BattleStats, cn.daxiang.protocol.game.BattleProtocol.BattleStats.Builder, cn.daxiang.protocol.game.BattleProtocol.BattleStatsOrBuilder>(
                  getBattleStats(),
                  getParentForChildren(),
                  isClean());
          battleStats_ = null;
        }
        return battleStatsBuilder_;
      }

      private java.util.List<cn.daxiang.protocol.game.BattleProtocol.BattleSprite> rightHistorySprite_ =
        java.util.Collections.emptyList();
      private void ensureRightHistorySpriteIsMutable() {
        if (!((bitField0_ & 0x00000020) == 0x00000020)) {
          rightHistorySprite_ = new java.util.ArrayList<cn.daxiang.protocol.game.BattleProtocol.BattleSprite>(rightHistorySprite_);
          bitField0_ |= 0x00000020;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          cn.daxiang.protocol.game.BattleProtocol.BattleSprite, cn.daxiang.protocol.game.BattleProtocol.BattleSprite.Builder, cn.daxiang.protocol.game.BattleProtocol.BattleSpriteOrBuilder> rightHistorySpriteBuilder_;

      /**
       * <pre>
       ** 右边阵营历史战斗精灵 
       * </pre>
       *
       * <code>repeated .BattleSprite rightHistorySprite = 6;</code>
       */
      public java.util.List<cn.daxiang.protocol.game.BattleProtocol.BattleSprite> getRightHistorySpriteList() {
        if (rightHistorySpriteBuilder_ == null) {
          return java.util.Collections.unmodifiableList(rightHistorySprite_);
        } else {
          return rightHistorySpriteBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       ** 右边阵营历史战斗精灵 
       * </pre>
       *
       * <code>repeated .BattleSprite rightHistorySprite = 6;</code>
       */
      public int getRightHistorySpriteCount() {
        if (rightHistorySpriteBuilder_ == null) {
          return rightHistorySprite_.size();
        } else {
          return rightHistorySpriteBuilder_.getCount();
        }
      }
      /**
       * <pre>
       ** 右边阵营历史战斗精灵 
       * </pre>
       *
       * <code>repeated .BattleSprite rightHistorySprite = 6;</code>
       */
      public cn.daxiang.protocol.game.BattleProtocol.BattleSprite getRightHistorySprite(int index) {
        if (rightHistorySpriteBuilder_ == null) {
          return rightHistorySprite_.get(index);
        } else {
          return rightHistorySpriteBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       ** 右边阵营历史战斗精灵 
       * </pre>
       *
       * <code>repeated .BattleSprite rightHistorySprite = 6;</code>
       */
      public Builder setRightHistorySprite(
          int index, cn.daxiang.protocol.game.BattleProtocol.BattleSprite value) {
        if (rightHistorySpriteBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRightHistorySpriteIsMutable();
          rightHistorySprite_.set(index, value);
          onChanged();
        } else {
          rightHistorySpriteBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       ** 右边阵营历史战斗精灵 
       * </pre>
       *
       * <code>repeated .BattleSprite rightHistorySprite = 6;</code>
       */
      public Builder setRightHistorySprite(
          int index, cn.daxiang.protocol.game.BattleProtocol.BattleSprite.Builder builderForValue) {
        if (rightHistorySpriteBuilder_ == null) {
          ensureRightHistorySpriteIsMutable();
          rightHistorySprite_.set(index, builderForValue.build());
          onChanged();
        } else {
          rightHistorySpriteBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       ** 右边阵营历史战斗精灵 
       * </pre>
       *
       * <code>repeated .BattleSprite rightHistorySprite = 6;</code>
       */
      public Builder addRightHistorySprite(cn.daxiang.protocol.game.BattleProtocol.BattleSprite value) {
        if (rightHistorySpriteBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRightHistorySpriteIsMutable();
          rightHistorySprite_.add(value);
          onChanged();
        } else {
          rightHistorySpriteBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       ** 右边阵营历史战斗精灵 
       * </pre>
       *
       * <code>repeated .BattleSprite rightHistorySprite = 6;</code>
       */
      public Builder addRightHistorySprite(
          int index, cn.daxiang.protocol.game.BattleProtocol.BattleSprite value) {
        if (rightHistorySpriteBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRightHistorySpriteIsMutable();
          rightHistorySprite_.add(index, value);
          onChanged();
        } else {
          rightHistorySpriteBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       ** 右边阵营历史战斗精灵 
       * </pre>
       *
       * <code>repeated .BattleSprite rightHistorySprite = 6;</code>
       */
      public Builder addRightHistorySprite(
          cn.daxiang.protocol.game.BattleProtocol.BattleSprite.Builder builderForValue) {
        if (rightHistorySpriteBuilder_ == null) {
          ensureRightHistorySpriteIsMutable();
          rightHistorySprite_.add(builderForValue.build());
          onChanged();
        } else {
          rightHistorySpriteBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       ** 右边阵营历史战斗精灵 
       * </pre>
       *
       * <code>repeated .BattleSprite rightHistorySprite = 6;</code>
       */
      public Builder addRightHistorySprite(
          int index, cn.daxiang.protocol.game.BattleProtocol.BattleSprite.Builder builderForValue) {
        if (rightHistorySpriteBuilder_ == null) {
          ensureRightHistorySpriteIsMutable();
          rightHistorySprite_.add(index, builderForValue.build());
          onChanged();
        } else {
          rightHistorySpriteBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       ** 右边阵营历史战斗精灵 
       * </pre>
       *
       * <code>repeated .BattleSprite rightHistorySprite = 6;</code>
       */
      public Builder addAllRightHistorySprite(
          java.lang.Iterable<? extends cn.daxiang.protocol.game.BattleProtocol.BattleSprite> values) {
        if (rightHistorySpriteBuilder_ == null) {
          ensureRightHistorySpriteIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, rightHistorySprite_);
          onChanged();
        } else {
          rightHistorySpriteBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       ** 右边阵营历史战斗精灵 
       * </pre>
       *
       * <code>repeated .BattleSprite rightHistorySprite = 6;</code>
       */
      public Builder clearRightHistorySprite() {
        if (rightHistorySpriteBuilder_ == null) {
          rightHistorySprite_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000020);
          onChanged();
        } else {
          rightHistorySpriteBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       ** 右边阵营历史战斗精灵 
       * </pre>
       *
       * <code>repeated .BattleSprite rightHistorySprite = 6;</code>
       */
      public Builder removeRightHistorySprite(int index) {
        if (rightHistorySpriteBuilder_ == null) {
          ensureRightHistorySpriteIsMutable();
          rightHistorySprite_.remove(index);
          onChanged();
        } else {
          rightHistorySpriteBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       ** 右边阵营历史战斗精灵 
       * </pre>
       *
       * <code>repeated .BattleSprite rightHistorySprite = 6;</code>
       */
      public cn.daxiang.protocol.game.BattleProtocol.BattleSprite.Builder getRightHistorySpriteBuilder(
          int index) {
        return getRightHistorySpriteFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       ** 右边阵营历史战斗精灵 
       * </pre>
       *
       * <code>repeated .BattleSprite rightHistorySprite = 6;</code>
       */
      public cn.daxiang.protocol.game.BattleProtocol.BattleSpriteOrBuilder getRightHistorySpriteOrBuilder(
          int index) {
        if (rightHistorySpriteBuilder_ == null) {
          return rightHistorySprite_.get(index);  } else {
          return rightHistorySpriteBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       ** 右边阵营历史战斗精灵 
       * </pre>
       *
       * <code>repeated .BattleSprite rightHistorySprite = 6;</code>
       */
      public java.util.List<? extends cn.daxiang.protocol.game.BattleProtocol.BattleSpriteOrBuilder> 
           getRightHistorySpriteOrBuilderList() {
        if (rightHistorySpriteBuilder_ != null) {
          return rightHistorySpriteBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(rightHistorySprite_);
        }
      }
      /**
       * <pre>
       ** 右边阵营历史战斗精灵 
       * </pre>
       *
       * <code>repeated .BattleSprite rightHistorySprite = 6;</code>
       */
      public cn.daxiang.protocol.game.BattleProtocol.BattleSprite.Builder addRightHistorySpriteBuilder() {
        return getRightHistorySpriteFieldBuilder().addBuilder(
            cn.daxiang.protocol.game.BattleProtocol.BattleSprite.getDefaultInstance());
      }
      /**
       * <pre>
       ** 右边阵营历史战斗精灵 
       * </pre>
       *
       * <code>repeated .BattleSprite rightHistorySprite = 6;</code>
       */
      public cn.daxiang.protocol.game.BattleProtocol.BattleSprite.Builder addRightHistorySpriteBuilder(
          int index) {
        return getRightHistorySpriteFieldBuilder().addBuilder(
            index, cn.daxiang.protocol.game.BattleProtocol.BattleSprite.getDefaultInstance());
      }
      /**
       * <pre>
       ** 右边阵营历史战斗精灵 
       * </pre>
       *
       * <code>repeated .BattleSprite rightHistorySprite = 6;</code>
       */
      public java.util.List<cn.daxiang.protocol.game.BattleProtocol.BattleSprite.Builder> 
           getRightHistorySpriteBuilderList() {
        return getRightHistorySpriteFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          cn.daxiang.protocol.game.BattleProtocol.BattleSprite, cn.daxiang.protocol.game.BattleProtocol.BattleSprite.Builder, cn.daxiang.protocol.game.BattleProtocol.BattleSpriteOrBuilder> 
          getRightHistorySpriteFieldBuilder() {
        if (rightHistorySpriteBuilder_ == null) {
          rightHistorySpriteBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              cn.daxiang.protocol.game.BattleProtocol.BattleSprite, cn.daxiang.protocol.game.BattleProtocol.BattleSprite.Builder, cn.daxiang.protocol.game.BattleProtocol.BattleSpriteOrBuilder>(
                  rightHistorySprite_,
                  ((bitField0_ & 0x00000020) == 0x00000020),
                  getParentForChildren(),
                  isClean());
          rightHistorySprite_ = null;
        }
        return rightHistorySpriteBuilder_;
      }

      private cn.daxiang.protocol.game.CommonProtocol.RewardResult extraReward_ = null;
      private com.google.protobuf.SingleFieldBuilderV3<
          cn.daxiang.protocol.game.CommonProtocol.RewardResult, cn.daxiang.protocol.game.CommonProtocol.RewardResult.Builder, cn.daxiang.protocol.game.CommonProtocol.RewardResultOrBuilder> extraRewardBuilder_;
      /**
       * <pre>
       ** 竞技场历史排名上升奖励列表 
       * </pre>
       *
       * <code>.RewardResult extraReward = 7;</code>
       */
      public boolean hasExtraReward() {
        return extraRewardBuilder_ != null || extraReward_ != null;
      }
      /**
       * <pre>
       ** 竞技场历史排名上升奖励列表 
       * </pre>
       *
       * <code>.RewardResult extraReward = 7;</code>
       */
      public cn.daxiang.protocol.game.CommonProtocol.RewardResult getExtraReward() {
        if (extraRewardBuilder_ == null) {
          return extraReward_ == null ? cn.daxiang.protocol.game.CommonProtocol.RewardResult.getDefaultInstance() : extraReward_;
        } else {
          return extraRewardBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       ** 竞技场历史排名上升奖励列表 
       * </pre>
       *
       * <code>.RewardResult extraReward = 7;</code>
       */
      public Builder setExtraReward(cn.daxiang.protocol.game.CommonProtocol.RewardResult value) {
        if (extraRewardBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          extraReward_ = value;
          onChanged();
        } else {
          extraRewardBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <pre>
       ** 竞技场历史排名上升奖励列表 
       * </pre>
       *
       * <code>.RewardResult extraReward = 7;</code>
       */
      public Builder setExtraReward(
          cn.daxiang.protocol.game.CommonProtocol.RewardResult.Builder builderForValue) {
        if (extraRewardBuilder_ == null) {
          extraReward_ = builderForValue.build();
          onChanged();
        } else {
          extraRewardBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <pre>
       ** 竞技场历史排名上升奖励列表 
       * </pre>
       *
       * <code>.RewardResult extraReward = 7;</code>
       */
      public Builder mergeExtraReward(cn.daxiang.protocol.game.CommonProtocol.RewardResult value) {
        if (extraRewardBuilder_ == null) {
          if (extraReward_ != null) {
            extraReward_ =
              cn.daxiang.protocol.game.CommonProtocol.RewardResult.newBuilder(extraReward_).mergeFrom(value).buildPartial();
          } else {
            extraReward_ = value;
          }
          onChanged();
        } else {
          extraRewardBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <pre>
       ** 竞技场历史排名上升奖励列表 
       * </pre>
       *
       * <code>.RewardResult extraReward = 7;</code>
       */
      public Builder clearExtraReward() {
        if (extraRewardBuilder_ == null) {
          extraReward_ = null;
          onChanged();
        } else {
          extraReward_ = null;
          extraRewardBuilder_ = null;
        }

        return this;
      }
      /**
       * <pre>
       ** 竞技场历史排名上升奖励列表 
       * </pre>
       *
       * <code>.RewardResult extraReward = 7;</code>
       */
      public cn.daxiang.protocol.game.CommonProtocol.RewardResult.Builder getExtraRewardBuilder() {
        
        onChanged();
        return getExtraRewardFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       ** 竞技场历史排名上升奖励列表 
       * </pre>
       *
       * <code>.RewardResult extraReward = 7;</code>
       */
      public cn.daxiang.protocol.game.CommonProtocol.RewardResultOrBuilder getExtraRewardOrBuilder() {
        if (extraRewardBuilder_ != null) {
          return extraRewardBuilder_.getMessageOrBuilder();
        } else {
          return extraReward_ == null ?
              cn.daxiang.protocol.game.CommonProtocol.RewardResult.getDefaultInstance() : extraReward_;
        }
      }
      /**
       * <pre>
       ** 竞技场历史排名上升奖励列表 
       * </pre>
       *
       * <code>.RewardResult extraReward = 7;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          cn.daxiang.protocol.game.CommonProtocol.RewardResult, cn.daxiang.protocol.game.CommonProtocol.RewardResult.Builder, cn.daxiang.protocol.game.CommonProtocol.RewardResultOrBuilder> 
          getExtraRewardFieldBuilder() {
        if (extraRewardBuilder_ == null) {
          extraRewardBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              cn.daxiang.protocol.game.CommonProtocol.RewardResult, cn.daxiang.protocol.game.CommonProtocol.RewardResult.Builder, cn.daxiang.protocol.game.CommonProtocol.RewardResultOrBuilder>(
                  getExtraReward(),
                  getParentForChildren(),
                  isClean());
          extraReward_ = null;
        }
        return extraRewardBuilder_;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:BattleResultResponse)
    }

    // @@protoc_insertion_point(class_scope:BattleResultResponse)
    private static final cn.daxiang.protocol.game.BattleProtocol.BattleResultResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new cn.daxiang.protocol.game.BattleProtocol.BattleResultResponse();
    }

    public static cn.daxiang.protocol.game.BattleProtocol.BattleResultResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<BattleResultResponse>
        PARSER = new com.google.protobuf.AbstractParser<BattleResultResponse>() {
      public BattleResultResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new BattleResultResponse(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<BattleResultResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<BattleResultResponse> getParserForType() {
      return PARSER;
    }

    public cn.daxiang.protocol.game.BattleProtocol.BattleResultResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface FightReportListOrBuilder extends
      // @@protoc_insertion_point(interface_extends:FightReportList)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .FightReport fightReport = 1;</code>
     */
    java.util.List<cn.daxiang.protocol.game.BattleProtocol.FightReport> 
        getFightReportList();
    /**
     * <code>repeated .FightReport fightReport = 1;</code>
     */
    cn.daxiang.protocol.game.BattleProtocol.FightReport getFightReport(int index);
    /**
     * <code>repeated .FightReport fightReport = 1;</code>
     */
    int getFightReportCount();
    /**
     * <code>repeated .FightReport fightReport = 1;</code>
     */
    java.util.List<? extends cn.daxiang.protocol.game.BattleProtocol.FightReportOrBuilder> 
        getFightReportOrBuilderList();
    /**
     * <code>repeated .FightReport fightReport = 1;</code>
     */
    cn.daxiang.protocol.game.BattleProtocol.FightReportOrBuilder getFightReportOrBuilder(
        int index);
  }
  /**
   * <pre>
   ** 战斗报表列表 
   * </pre>
   *
   * Protobuf type {@code FightReportList}
   */
  public  static final class FightReportList extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:FightReportList)
      FightReportListOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FightReportList.newBuilder() to construct.
    private FightReportList(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FightReportList() {
      fightReport_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private FightReportList(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
                fightReport_ = new java.util.ArrayList<cn.daxiang.protocol.game.BattleProtocol.FightReport>();
                mutable_bitField0_ |= 0x00000001;
              }
              fightReport_.add(
                  input.readMessage(cn.daxiang.protocol.game.BattleProtocol.FightReport.parser(), extensionRegistry));
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
          fightReport_ = java.util.Collections.unmodifiableList(fightReport_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.BattleProtocol.internal_static_FightReportList_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.daxiang.protocol.game.BattleProtocol.internal_static_FightReportList_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.daxiang.protocol.game.BattleProtocol.FightReportList.class, cn.daxiang.protocol.game.BattleProtocol.FightReportList.Builder.class);
    }

    public static final int FIGHTREPORT_FIELD_NUMBER = 1;
    private java.util.List<cn.daxiang.protocol.game.BattleProtocol.FightReport> fightReport_;
    /**
     * <code>repeated .FightReport fightReport = 1;</code>
     */
    public java.util.List<cn.daxiang.protocol.game.BattleProtocol.FightReport> getFightReportList() {
      return fightReport_;
    }
    /**
     * <code>repeated .FightReport fightReport = 1;</code>
     */
    public java.util.List<? extends cn.daxiang.protocol.game.BattleProtocol.FightReportOrBuilder> 
        getFightReportOrBuilderList() {
      return fightReport_;
    }
    /**
     * <code>repeated .FightReport fightReport = 1;</code>
     */
    public int getFightReportCount() {
      return fightReport_.size();
    }
    /**
     * <code>repeated .FightReport fightReport = 1;</code>
     */
    public cn.daxiang.protocol.game.BattleProtocol.FightReport getFightReport(int index) {
      return fightReport_.get(index);
    }
    /**
     * <code>repeated .FightReport fightReport = 1;</code>
     */
    public cn.daxiang.protocol.game.BattleProtocol.FightReportOrBuilder getFightReportOrBuilder(
        int index) {
      return fightReport_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < fightReport_.size(); i++) {
        output.writeMessage(1, fightReport_.get(i));
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < fightReport_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, fightReport_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof cn.daxiang.protocol.game.BattleProtocol.FightReportList)) {
        return super.equals(obj);
      }
      cn.daxiang.protocol.game.BattleProtocol.FightReportList other = (cn.daxiang.protocol.game.BattleProtocol.FightReportList) obj;

      boolean result = true;
      result = result && getFightReportList()
          .equals(other.getFightReportList());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getFightReportCount() > 0) {
        hash = (37 * hash) + FIGHTREPORT_FIELD_NUMBER;
        hash = (53 * hash) + getFightReportList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static cn.daxiang.protocol.game.BattleProtocol.FightReportList parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.FightReportList parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.FightReportList parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.FightReportList parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.FightReportList parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.FightReportList parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.FightReportList parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.FightReportList parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.FightReportList parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.FightReportList parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.FightReportList parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.FightReportList parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(cn.daxiang.protocol.game.BattleProtocol.FightReportList prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** 战斗报表列表 
     * </pre>
     *
     * Protobuf type {@code FightReportList}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:FightReportList)
        cn.daxiang.protocol.game.BattleProtocol.FightReportListOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.daxiang.protocol.game.BattleProtocol.internal_static_FightReportList_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.daxiang.protocol.game.BattleProtocol.internal_static_FightReportList_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.daxiang.protocol.game.BattleProtocol.FightReportList.class, cn.daxiang.protocol.game.BattleProtocol.FightReportList.Builder.class);
      }

      // Construct using cn.daxiang.protocol.game.BattleProtocol.FightReportList.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getFightReportFieldBuilder();
        }
      }
      public Builder clear() {
        super.clear();
        if (fightReportBuilder_ == null) {
          fightReport_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          fightReportBuilder_.clear();
        }
        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return cn.daxiang.protocol.game.BattleProtocol.internal_static_FightReportList_descriptor;
      }

      public cn.daxiang.protocol.game.BattleProtocol.FightReportList getDefaultInstanceForType() {
        return cn.daxiang.protocol.game.BattleProtocol.FightReportList.getDefaultInstance();
      }

      public cn.daxiang.protocol.game.BattleProtocol.FightReportList build() {
        cn.daxiang.protocol.game.BattleProtocol.FightReportList result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public cn.daxiang.protocol.game.BattleProtocol.FightReportList buildPartial() {
        cn.daxiang.protocol.game.BattleProtocol.FightReportList result = new cn.daxiang.protocol.game.BattleProtocol.FightReportList(this);
        int from_bitField0_ = bitField0_;
        if (fightReportBuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001)) {
            fightReport_ = java.util.Collections.unmodifiableList(fightReport_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.fightReport_ = fightReport_;
        } else {
          result.fightReport_ = fightReportBuilder_.build();
        }
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof cn.daxiang.protocol.game.BattleProtocol.FightReportList) {
          return mergeFrom((cn.daxiang.protocol.game.BattleProtocol.FightReportList)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(cn.daxiang.protocol.game.BattleProtocol.FightReportList other) {
        if (other == cn.daxiang.protocol.game.BattleProtocol.FightReportList.getDefaultInstance()) return this;
        if (fightReportBuilder_ == null) {
          if (!other.fightReport_.isEmpty()) {
            if (fightReport_.isEmpty()) {
              fightReport_ = other.fightReport_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureFightReportIsMutable();
              fightReport_.addAll(other.fightReport_);
            }
            onChanged();
          }
        } else {
          if (!other.fightReport_.isEmpty()) {
            if (fightReportBuilder_.isEmpty()) {
              fightReportBuilder_.dispose();
              fightReportBuilder_ = null;
              fightReport_ = other.fightReport_;
              bitField0_ = (bitField0_ & ~0x00000001);
              fightReportBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getFightReportFieldBuilder() : null;
            } else {
              fightReportBuilder_.addAllMessages(other.fightReport_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        cn.daxiang.protocol.game.BattleProtocol.FightReportList parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (cn.daxiang.protocol.game.BattleProtocol.FightReportList) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<cn.daxiang.protocol.game.BattleProtocol.FightReport> fightReport_ =
        java.util.Collections.emptyList();
      private void ensureFightReportIsMutable() {
        if (!((bitField0_ & 0x00000001) == 0x00000001)) {
          fightReport_ = new java.util.ArrayList<cn.daxiang.protocol.game.BattleProtocol.FightReport>(fightReport_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          cn.daxiang.protocol.game.BattleProtocol.FightReport, cn.daxiang.protocol.game.BattleProtocol.FightReport.Builder, cn.daxiang.protocol.game.BattleProtocol.FightReportOrBuilder> fightReportBuilder_;

      /**
       * <code>repeated .FightReport fightReport = 1;</code>
       */
      public java.util.List<cn.daxiang.protocol.game.BattleProtocol.FightReport> getFightReportList() {
        if (fightReportBuilder_ == null) {
          return java.util.Collections.unmodifiableList(fightReport_);
        } else {
          return fightReportBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .FightReport fightReport = 1;</code>
       */
      public int getFightReportCount() {
        if (fightReportBuilder_ == null) {
          return fightReport_.size();
        } else {
          return fightReportBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .FightReport fightReport = 1;</code>
       */
      public cn.daxiang.protocol.game.BattleProtocol.FightReport getFightReport(int index) {
        if (fightReportBuilder_ == null) {
          return fightReport_.get(index);
        } else {
          return fightReportBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .FightReport fightReport = 1;</code>
       */
      public Builder setFightReport(
          int index, cn.daxiang.protocol.game.BattleProtocol.FightReport value) {
        if (fightReportBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureFightReportIsMutable();
          fightReport_.set(index, value);
          onChanged();
        } else {
          fightReportBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .FightReport fightReport = 1;</code>
       */
      public Builder setFightReport(
          int index, cn.daxiang.protocol.game.BattleProtocol.FightReport.Builder builderForValue) {
        if (fightReportBuilder_ == null) {
          ensureFightReportIsMutable();
          fightReport_.set(index, builderForValue.build());
          onChanged();
        } else {
          fightReportBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .FightReport fightReport = 1;</code>
       */
      public Builder addFightReport(cn.daxiang.protocol.game.BattleProtocol.FightReport value) {
        if (fightReportBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureFightReportIsMutable();
          fightReport_.add(value);
          onChanged();
        } else {
          fightReportBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .FightReport fightReport = 1;</code>
       */
      public Builder addFightReport(
          int index, cn.daxiang.protocol.game.BattleProtocol.FightReport value) {
        if (fightReportBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureFightReportIsMutable();
          fightReport_.add(index, value);
          onChanged();
        } else {
          fightReportBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .FightReport fightReport = 1;</code>
       */
      public Builder addFightReport(
          cn.daxiang.protocol.game.BattleProtocol.FightReport.Builder builderForValue) {
        if (fightReportBuilder_ == null) {
          ensureFightReportIsMutable();
          fightReport_.add(builderForValue.build());
          onChanged();
        } else {
          fightReportBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .FightReport fightReport = 1;</code>
       */
      public Builder addFightReport(
          int index, cn.daxiang.protocol.game.BattleProtocol.FightReport.Builder builderForValue) {
        if (fightReportBuilder_ == null) {
          ensureFightReportIsMutable();
          fightReport_.add(index, builderForValue.build());
          onChanged();
        } else {
          fightReportBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .FightReport fightReport = 1;</code>
       */
      public Builder addAllFightReport(
          java.lang.Iterable<? extends cn.daxiang.protocol.game.BattleProtocol.FightReport> values) {
        if (fightReportBuilder_ == null) {
          ensureFightReportIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, fightReport_);
          onChanged();
        } else {
          fightReportBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .FightReport fightReport = 1;</code>
       */
      public Builder clearFightReport() {
        if (fightReportBuilder_ == null) {
          fightReport_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          fightReportBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .FightReport fightReport = 1;</code>
       */
      public Builder removeFightReport(int index) {
        if (fightReportBuilder_ == null) {
          ensureFightReportIsMutable();
          fightReport_.remove(index);
          onChanged();
        } else {
          fightReportBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .FightReport fightReport = 1;</code>
       */
      public cn.daxiang.protocol.game.BattleProtocol.FightReport.Builder getFightReportBuilder(
          int index) {
        return getFightReportFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .FightReport fightReport = 1;</code>
       */
      public cn.daxiang.protocol.game.BattleProtocol.FightReportOrBuilder getFightReportOrBuilder(
          int index) {
        if (fightReportBuilder_ == null) {
          return fightReport_.get(index);  } else {
          return fightReportBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .FightReport fightReport = 1;</code>
       */
      public java.util.List<? extends cn.daxiang.protocol.game.BattleProtocol.FightReportOrBuilder> 
           getFightReportOrBuilderList() {
        if (fightReportBuilder_ != null) {
          return fightReportBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(fightReport_);
        }
      }
      /**
       * <code>repeated .FightReport fightReport = 1;</code>
       */
      public cn.daxiang.protocol.game.BattleProtocol.FightReport.Builder addFightReportBuilder() {
        return getFightReportFieldBuilder().addBuilder(
            cn.daxiang.protocol.game.BattleProtocol.FightReport.getDefaultInstance());
      }
      /**
       * <code>repeated .FightReport fightReport = 1;</code>
       */
      public cn.daxiang.protocol.game.BattleProtocol.FightReport.Builder addFightReportBuilder(
          int index) {
        return getFightReportFieldBuilder().addBuilder(
            index, cn.daxiang.protocol.game.BattleProtocol.FightReport.getDefaultInstance());
      }
      /**
       * <code>repeated .FightReport fightReport = 1;</code>
       */
      public java.util.List<cn.daxiang.protocol.game.BattleProtocol.FightReport.Builder> 
           getFightReportBuilderList() {
        return getFightReportFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          cn.daxiang.protocol.game.BattleProtocol.FightReport, cn.daxiang.protocol.game.BattleProtocol.FightReport.Builder, cn.daxiang.protocol.game.BattleProtocol.FightReportOrBuilder> 
          getFightReportFieldBuilder() {
        if (fightReportBuilder_ == null) {
          fightReportBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              cn.daxiang.protocol.game.BattleProtocol.FightReport, cn.daxiang.protocol.game.BattleProtocol.FightReport.Builder, cn.daxiang.protocol.game.BattleProtocol.FightReportOrBuilder>(
                  fightReport_,
                  ((bitField0_ & 0x00000001) == 0x00000001),
                  getParentForChildren(),
                  isClean());
          fightReport_ = null;
        }
        return fightReportBuilder_;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:FightReportList)
    }

    // @@protoc_insertion_point(class_scope:FightReportList)
    private static final cn.daxiang.protocol.game.BattleProtocol.FightReportList DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new cn.daxiang.protocol.game.BattleProtocol.FightReportList();
    }

    public static cn.daxiang.protocol.game.BattleProtocol.FightReportList getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<FightReportList>
        PARSER = new com.google.protobuf.AbstractParser<FightReportList>() {
      public FightReportList parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new FightReportList(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<FightReportList> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FightReportList> getParserForType() {
      return PARSER;
    }

    public cn.daxiang.protocol.game.BattleProtocol.FightReportList getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface BattleSpriteOrBuilder extends
      // @@protoc_insertion_point(interface_extends:BattleSprite)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     ** 角色属性Map key:SpriteKey value:String 
     * </pre>
     *
     * <code>map&lt;int32, string&gt; attribute = 1;</code>
     */
    int getAttributeCount();
    /**
     * <pre>
     ** 角色属性Map key:SpriteKey value:String 
     * </pre>
     *
     * <code>map&lt;int32, string&gt; attribute = 1;</code>
     */
    boolean containsAttribute(
        int key);
    /**
     * Use {@link #getAttributeMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Integer, java.lang.String>
    getAttribute();
    /**
     * <pre>
     ** 角色属性Map key:SpriteKey value:String 
     * </pre>
     *
     * <code>map&lt;int32, string&gt; attribute = 1;</code>
     */
    java.util.Map<java.lang.Integer, java.lang.String>
    getAttributeMap();
    /**
     * <pre>
     ** 角色属性Map key:SpriteKey value:String 
     * </pre>
     *
     * <code>map&lt;int32, string&gt; attribute = 1;</code>
     */

    java.lang.String getAttributeOrDefault(
        int key,
        java.lang.String defaultValue);
    /**
     * <pre>
     ** 角色属性Map key:SpriteKey value:String 
     * </pre>
     *
     * <code>map&lt;int32, string&gt; attribute = 1;</code>
     */

    java.lang.String getAttributeOrThrow(
        int key);
  }
  /**
   * <pre>
   ** 战斗精灵 
   * </pre>
   *
   * Protobuf type {@code BattleSprite}
   */
  public  static final class BattleSprite extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:BattleSprite)
      BattleSpriteOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use BattleSprite.newBuilder() to construct.
    private BattleSprite(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private BattleSprite() {
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private BattleSprite(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
                attribute_ = com.google.protobuf.MapField.newMapField(
                    AttributeDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000001;
              }
              com.google.protobuf.MapEntry<java.lang.Integer, java.lang.String>
              attribute__ = input.readMessage(
                  AttributeDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              attribute_.getMutableMap().put(
                  attribute__.getKey(), attribute__.getValue());
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.BattleProtocol.internal_static_BattleSprite_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 1:
          return internalGetAttribute();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.daxiang.protocol.game.BattleProtocol.internal_static_BattleSprite_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.daxiang.protocol.game.BattleProtocol.BattleSprite.class, cn.daxiang.protocol.game.BattleProtocol.BattleSprite.Builder.class);
    }

    public static final int ATTRIBUTE_FIELD_NUMBER = 1;
    private static final class AttributeDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Integer, java.lang.String> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Integer, java.lang.String>newDefaultInstance(
                  cn.daxiang.protocol.game.BattleProtocol.internal_static_BattleSprite_AttributeEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0,
                  com.google.protobuf.WireFormat.FieldType.STRING,
                  "");
    }
    private com.google.protobuf.MapField<
        java.lang.Integer, java.lang.String> attribute_;
    private com.google.protobuf.MapField<java.lang.Integer, java.lang.String>
    internalGetAttribute() {
      if (attribute_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            AttributeDefaultEntryHolder.defaultEntry);
      }
      return attribute_;
    }

    public int getAttributeCount() {
      return internalGetAttribute().getMap().size();
    }
    /**
     * <pre>
     ** 角色属性Map key:SpriteKey value:String 
     * </pre>
     *
     * <code>map&lt;int32, string&gt; attribute = 1;</code>
     */

    public boolean containsAttribute(
        int key) {
      
      return internalGetAttribute().getMap().containsKey(key);
    }
    /**
     * Use {@link #getAttributeMap()} instead.
     */
    @java.lang.Deprecated
    public java.util.Map<java.lang.Integer, java.lang.String> getAttribute() {
      return getAttributeMap();
    }
    /**
     * <pre>
     ** 角色属性Map key:SpriteKey value:String 
     * </pre>
     *
     * <code>map&lt;int32, string&gt; attribute = 1;</code>
     */

    public java.util.Map<java.lang.Integer, java.lang.String> getAttributeMap() {
      return internalGetAttribute().getMap();
    }
    /**
     * <pre>
     ** 角色属性Map key:SpriteKey value:String 
     * </pre>
     *
     * <code>map&lt;int32, string&gt; attribute = 1;</code>
     */

    public java.lang.String getAttributeOrDefault(
        int key,
        java.lang.String defaultValue) {
      
      java.util.Map<java.lang.Integer, java.lang.String> map =
          internalGetAttribute().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <pre>
     ** 角色属性Map key:SpriteKey value:String 
     * </pre>
     *
     * <code>map&lt;int32, string&gt; attribute = 1;</code>
     */

    public java.lang.String getAttributeOrThrow(
        int key) {
      
      java.util.Map<java.lang.Integer, java.lang.String> map =
          internalGetAttribute().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      com.google.protobuf.GeneratedMessageV3
        .serializeIntegerMapTo(
          output,
          internalGetAttribute(),
          AttributeDefaultEntryHolder.defaultEntry,
          1);
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (java.util.Map.Entry<java.lang.Integer, java.lang.String> entry
           : internalGetAttribute().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Integer, java.lang.String>
        attribute__ = AttributeDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(1, attribute__);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof cn.daxiang.protocol.game.BattleProtocol.BattleSprite)) {
        return super.equals(obj);
      }
      cn.daxiang.protocol.game.BattleProtocol.BattleSprite other = (cn.daxiang.protocol.game.BattleProtocol.BattleSprite) obj;

      boolean result = true;
      result = result && internalGetAttribute().equals(
          other.internalGetAttribute());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (!internalGetAttribute().getMap().isEmpty()) {
        hash = (37 * hash) + ATTRIBUTE_FIELD_NUMBER;
        hash = (53 * hash) + internalGetAttribute().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static cn.daxiang.protocol.game.BattleProtocol.BattleSprite parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.BattleSprite parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.BattleSprite parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.BattleSprite parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.BattleSprite parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.BattleSprite parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.BattleSprite parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.BattleSprite parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.BattleSprite parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.BattleSprite parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.BattleSprite parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.BattleSprite parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(cn.daxiang.protocol.game.BattleProtocol.BattleSprite prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** 战斗精灵 
     * </pre>
     *
     * Protobuf type {@code BattleSprite}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:BattleSprite)
        cn.daxiang.protocol.game.BattleProtocol.BattleSpriteOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.daxiang.protocol.game.BattleProtocol.internal_static_BattleSprite_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetAttribute();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetMutableAttribute();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.daxiang.protocol.game.BattleProtocol.internal_static_BattleSprite_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.daxiang.protocol.game.BattleProtocol.BattleSprite.class, cn.daxiang.protocol.game.BattleProtocol.BattleSprite.Builder.class);
      }

      // Construct using cn.daxiang.protocol.game.BattleProtocol.BattleSprite.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        internalGetMutableAttribute().clear();
        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return cn.daxiang.protocol.game.BattleProtocol.internal_static_BattleSprite_descriptor;
      }

      public cn.daxiang.protocol.game.BattleProtocol.BattleSprite getDefaultInstanceForType() {
        return cn.daxiang.protocol.game.BattleProtocol.BattleSprite.getDefaultInstance();
      }

      public cn.daxiang.protocol.game.BattleProtocol.BattleSprite build() {
        cn.daxiang.protocol.game.BattleProtocol.BattleSprite result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public cn.daxiang.protocol.game.BattleProtocol.BattleSprite buildPartial() {
        cn.daxiang.protocol.game.BattleProtocol.BattleSprite result = new cn.daxiang.protocol.game.BattleProtocol.BattleSprite(this);
        int from_bitField0_ = bitField0_;
        result.attribute_ = internalGetAttribute();
        result.attribute_.makeImmutable();
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof cn.daxiang.protocol.game.BattleProtocol.BattleSprite) {
          return mergeFrom((cn.daxiang.protocol.game.BattleProtocol.BattleSprite)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(cn.daxiang.protocol.game.BattleProtocol.BattleSprite other) {
        if (other == cn.daxiang.protocol.game.BattleProtocol.BattleSprite.getDefaultInstance()) return this;
        internalGetMutableAttribute().mergeFrom(
            other.internalGetAttribute());
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        cn.daxiang.protocol.game.BattleProtocol.BattleSprite parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (cn.daxiang.protocol.game.BattleProtocol.BattleSprite) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.MapField<
          java.lang.Integer, java.lang.String> attribute_;
      private com.google.protobuf.MapField<java.lang.Integer, java.lang.String>
      internalGetAttribute() {
        if (attribute_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              AttributeDefaultEntryHolder.defaultEntry);
        }
        return attribute_;
      }
      private com.google.protobuf.MapField<java.lang.Integer, java.lang.String>
      internalGetMutableAttribute() {
        onChanged();;
        if (attribute_ == null) {
          attribute_ = com.google.protobuf.MapField.newMapField(
              AttributeDefaultEntryHolder.defaultEntry);
        }
        if (!attribute_.isMutable()) {
          attribute_ = attribute_.copy();
        }
        return attribute_;
      }

      public int getAttributeCount() {
        return internalGetAttribute().getMap().size();
      }
      /**
       * <pre>
       ** 角色属性Map key:SpriteKey value:String 
       * </pre>
       *
       * <code>map&lt;int32, string&gt; attribute = 1;</code>
       */

      public boolean containsAttribute(
          int key) {
        
        return internalGetAttribute().getMap().containsKey(key);
      }
      /**
       * Use {@link #getAttributeMap()} instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, java.lang.String> getAttribute() {
        return getAttributeMap();
      }
      /**
       * <pre>
       ** 角色属性Map key:SpriteKey value:String 
       * </pre>
       *
       * <code>map&lt;int32, string&gt; attribute = 1;</code>
       */

      public java.util.Map<java.lang.Integer, java.lang.String> getAttributeMap() {
        return internalGetAttribute().getMap();
      }
      /**
       * <pre>
       ** 角色属性Map key:SpriteKey value:String 
       * </pre>
       *
       * <code>map&lt;int32, string&gt; attribute = 1;</code>
       */

      public java.lang.String getAttributeOrDefault(
          int key,
          java.lang.String defaultValue) {
        
        java.util.Map<java.lang.Integer, java.lang.String> map =
            internalGetAttribute().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <pre>
       ** 角色属性Map key:SpriteKey value:String 
       * </pre>
       *
       * <code>map&lt;int32, string&gt; attribute = 1;</code>
       */

      public java.lang.String getAttributeOrThrow(
          int key) {
        
        java.util.Map<java.lang.Integer, java.lang.String> map =
            internalGetAttribute().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearAttribute() {
        internalGetMutableAttribute().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <pre>
       ** 角色属性Map key:SpriteKey value:String 
       * </pre>
       *
       * <code>map&lt;int32, string&gt; attribute = 1;</code>
       */

      public Builder removeAttribute(
          int key) {
        
        internalGetMutableAttribute().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, java.lang.String>
      getMutableAttribute() {
        return internalGetMutableAttribute().getMutableMap();
      }
      /**
       * <pre>
       ** 角色属性Map key:SpriteKey value:String 
       * </pre>
       *
       * <code>map&lt;int32, string&gt; attribute = 1;</code>
       */
      public Builder putAttribute(
          int key,
          java.lang.String value) {
        
        if (value == null) { throw new java.lang.NullPointerException(); }
        internalGetMutableAttribute().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <pre>
       ** 角色属性Map key:SpriteKey value:String 
       * </pre>
       *
       * <code>map&lt;int32, string&gt; attribute = 1;</code>
       */

      public Builder putAllAttribute(
          java.util.Map<java.lang.Integer, java.lang.String> values) {
        internalGetMutableAttribute().getMutableMap()
            .putAll(values);
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:BattleSprite)
    }

    // @@protoc_insertion_point(class_scope:BattleSprite)
    private static final cn.daxiang.protocol.game.BattleProtocol.BattleSprite DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new cn.daxiang.protocol.game.BattleProtocol.BattleSprite();
    }

    public static cn.daxiang.protocol.game.BattleProtocol.BattleSprite getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<BattleSprite>
        PARSER = new com.google.protobuf.AbstractParser<BattleSprite>() {
      public BattleSprite parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new BattleSprite(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<BattleSprite> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<BattleSprite> getParserForType() {
      return PARSER;
    }

    public cn.daxiang.protocol.game.BattleProtocol.BattleSprite getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface BattleMemberOrBuilder extends
      // @@protoc_insertion_point(interface_extends:BattleMember)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     ** 角色属性 
     * </pre>
     *
     * <code>.ActorProfile actorProfile = 1;</code>
     */
    boolean hasActorProfile();
    /**
     * <pre>
     ** 角色属性 
     * </pre>
     *
     * <code>.ActorProfile actorProfile = 1;</code>
     */
    cn.daxiang.protocol.game.CommonProtocol.ActorProfile getActorProfile();
    /**
     * <pre>
     ** 角色属性 
     * </pre>
     *
     * <code>.ActorProfile actorProfile = 1;</code>
     */
    cn.daxiang.protocol.game.CommonProtocol.ActorProfileOrBuilder getActorProfileOrBuilder();

    /**
     * <pre>
     ** 战斗精灵列表 
     * </pre>
     *
     * <code>repeated .BattleSprite sprites = 2;</code>
     */
    java.util.List<cn.daxiang.protocol.game.BattleProtocol.BattleSprite> 
        getSpritesList();
    /**
     * <pre>
     ** 战斗精灵列表 
     * </pre>
     *
     * <code>repeated .BattleSprite sprites = 2;</code>
     */
    cn.daxiang.protocol.game.BattleProtocol.BattleSprite getSprites(int index);
    /**
     * <pre>
     ** 战斗精灵列表 
     * </pre>
     *
     * <code>repeated .BattleSprite sprites = 2;</code>
     */
    int getSpritesCount();
    /**
     * <pre>
     ** 战斗精灵列表 
     * </pre>
     *
     * <code>repeated .BattleSprite sprites = 2;</code>
     */
    java.util.List<? extends cn.daxiang.protocol.game.BattleProtocol.BattleSpriteOrBuilder> 
        getSpritesOrBuilderList();
    /**
     * <pre>
     ** 战斗精灵列表 
     * </pre>
     *
     * <code>repeated .BattleSprite sprites = 2;</code>
     */
    cn.daxiang.protocol.game.BattleProtocol.BattleSpriteOrBuilder getSpritesOrBuilder(
        int index);

    /**
     * <pre>
     ** 战斗阵营 
     * </pre>
     *
     * <code>.BattleCamp camp = 3;</code>
     */
    int getCampValue();
    /**
     * <pre>
     ** 战斗阵营 
     * </pre>
     *
     * <code>.BattleCamp camp = 3;</code>
     */
    cn.daxiang.protocol.game.BattleProtocol.BattleCamp getCamp();
  }
  /**
   * <pre>
   ** 战斗成员显示对象 
   * </pre>
   *
   * Protobuf type {@code BattleMember}
   */
  public  static final class BattleMember extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:BattleMember)
      BattleMemberOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use BattleMember.newBuilder() to construct.
    private BattleMember(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private BattleMember() {
      sprites_ = java.util.Collections.emptyList();
      camp_ = 0;
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private BattleMember(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              cn.daxiang.protocol.game.CommonProtocol.ActorProfile.Builder subBuilder = null;
              if (actorProfile_ != null) {
                subBuilder = actorProfile_.toBuilder();
              }
              actorProfile_ = input.readMessage(cn.daxiang.protocol.game.CommonProtocol.ActorProfile.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(actorProfile_);
                actorProfile_ = subBuilder.buildPartial();
              }

              break;
            }
            case 18: {
              if (!((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
                sprites_ = new java.util.ArrayList<cn.daxiang.protocol.game.BattleProtocol.BattleSprite>();
                mutable_bitField0_ |= 0x00000002;
              }
              sprites_.add(
                  input.readMessage(cn.daxiang.protocol.game.BattleProtocol.BattleSprite.parser(), extensionRegistry));
              break;
            }
            case 24: {
              int rawValue = input.readEnum();

              camp_ = rawValue;
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
          sprites_ = java.util.Collections.unmodifiableList(sprites_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.BattleProtocol.internal_static_BattleMember_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.daxiang.protocol.game.BattleProtocol.internal_static_BattleMember_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.daxiang.protocol.game.BattleProtocol.BattleMember.class, cn.daxiang.protocol.game.BattleProtocol.BattleMember.Builder.class);
    }

    private int bitField0_;
    public static final int ACTORPROFILE_FIELD_NUMBER = 1;
    private cn.daxiang.protocol.game.CommonProtocol.ActorProfile actorProfile_;
    /**
     * <pre>
     ** 角色属性 
     * </pre>
     *
     * <code>.ActorProfile actorProfile = 1;</code>
     */
    public boolean hasActorProfile() {
      return actorProfile_ != null;
    }
    /**
     * <pre>
     ** 角色属性 
     * </pre>
     *
     * <code>.ActorProfile actorProfile = 1;</code>
     */
    public cn.daxiang.protocol.game.CommonProtocol.ActorProfile getActorProfile() {
      return actorProfile_ == null ? cn.daxiang.protocol.game.CommonProtocol.ActorProfile.getDefaultInstance() : actorProfile_;
    }
    /**
     * <pre>
     ** 角色属性 
     * </pre>
     *
     * <code>.ActorProfile actorProfile = 1;</code>
     */
    public cn.daxiang.protocol.game.CommonProtocol.ActorProfileOrBuilder getActorProfileOrBuilder() {
      return getActorProfile();
    }

    public static final int SPRITES_FIELD_NUMBER = 2;
    private java.util.List<cn.daxiang.protocol.game.BattleProtocol.BattleSprite> sprites_;
    /**
     * <pre>
     ** 战斗精灵列表 
     * </pre>
     *
     * <code>repeated .BattleSprite sprites = 2;</code>
     */
    public java.util.List<cn.daxiang.protocol.game.BattleProtocol.BattleSprite> getSpritesList() {
      return sprites_;
    }
    /**
     * <pre>
     ** 战斗精灵列表 
     * </pre>
     *
     * <code>repeated .BattleSprite sprites = 2;</code>
     */
    public java.util.List<? extends cn.daxiang.protocol.game.BattleProtocol.BattleSpriteOrBuilder> 
        getSpritesOrBuilderList() {
      return sprites_;
    }
    /**
     * <pre>
     ** 战斗精灵列表 
     * </pre>
     *
     * <code>repeated .BattleSprite sprites = 2;</code>
     */
    public int getSpritesCount() {
      return sprites_.size();
    }
    /**
     * <pre>
     ** 战斗精灵列表 
     * </pre>
     *
     * <code>repeated .BattleSprite sprites = 2;</code>
     */
    public cn.daxiang.protocol.game.BattleProtocol.BattleSprite getSprites(int index) {
      return sprites_.get(index);
    }
    /**
     * <pre>
     ** 战斗精灵列表 
     * </pre>
     *
     * <code>repeated .BattleSprite sprites = 2;</code>
     */
    public cn.daxiang.protocol.game.BattleProtocol.BattleSpriteOrBuilder getSpritesOrBuilder(
        int index) {
      return sprites_.get(index);
    }

    public static final int CAMP_FIELD_NUMBER = 3;
    private int camp_;
    /**
     * <pre>
     ** 战斗阵营 
     * </pre>
     *
     * <code>.BattleCamp camp = 3;</code>
     */
    public int getCampValue() {
      return camp_;
    }
    /**
     * <pre>
     ** 战斗阵营 
     * </pre>
     *
     * <code>.BattleCamp camp = 3;</code>
     */
    public cn.daxiang.protocol.game.BattleProtocol.BattleCamp getCamp() {
      cn.daxiang.protocol.game.BattleProtocol.BattleCamp result = cn.daxiang.protocol.game.BattleProtocol.BattleCamp.valueOf(camp_);
      return result == null ? cn.daxiang.protocol.game.BattleProtocol.BattleCamp.UNRECOGNIZED : result;
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (actorProfile_ != null) {
        output.writeMessage(1, getActorProfile());
      }
      for (int i = 0; i < sprites_.size(); i++) {
        output.writeMessage(2, sprites_.get(i));
      }
      if (camp_ != cn.daxiang.protocol.game.BattleProtocol.BattleCamp.BATTLE_CAMP_NONE.getNumber()) {
        output.writeEnum(3, camp_);
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (actorProfile_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getActorProfile());
      }
      for (int i = 0; i < sprites_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, sprites_.get(i));
      }
      if (camp_ != cn.daxiang.protocol.game.BattleProtocol.BattleCamp.BATTLE_CAMP_NONE.getNumber()) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(3, camp_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof cn.daxiang.protocol.game.BattleProtocol.BattleMember)) {
        return super.equals(obj);
      }
      cn.daxiang.protocol.game.BattleProtocol.BattleMember other = (cn.daxiang.protocol.game.BattleProtocol.BattleMember) obj;

      boolean result = true;
      result = result && (hasActorProfile() == other.hasActorProfile());
      if (hasActorProfile()) {
        result = result && getActorProfile()
            .equals(other.getActorProfile());
      }
      result = result && getSpritesList()
          .equals(other.getSpritesList());
      result = result && camp_ == other.camp_;
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasActorProfile()) {
        hash = (37 * hash) + ACTORPROFILE_FIELD_NUMBER;
        hash = (53 * hash) + getActorProfile().hashCode();
      }
      if (getSpritesCount() > 0) {
        hash = (37 * hash) + SPRITES_FIELD_NUMBER;
        hash = (53 * hash) + getSpritesList().hashCode();
      }
      hash = (37 * hash) + CAMP_FIELD_NUMBER;
      hash = (53 * hash) + camp_;
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static cn.daxiang.protocol.game.BattleProtocol.BattleMember parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.BattleMember parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.BattleMember parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.BattleMember parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.BattleMember parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.BattleMember parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.BattleMember parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.BattleMember parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.BattleMember parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.BattleMember parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.BattleMember parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.BattleMember parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(cn.daxiang.protocol.game.BattleProtocol.BattleMember prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** 战斗成员显示对象 
     * </pre>
     *
     * Protobuf type {@code BattleMember}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:BattleMember)
        cn.daxiang.protocol.game.BattleProtocol.BattleMemberOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.daxiang.protocol.game.BattleProtocol.internal_static_BattleMember_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.daxiang.protocol.game.BattleProtocol.internal_static_BattleMember_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.daxiang.protocol.game.BattleProtocol.BattleMember.class, cn.daxiang.protocol.game.BattleProtocol.BattleMember.Builder.class);
      }

      // Construct using cn.daxiang.protocol.game.BattleProtocol.BattleMember.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getSpritesFieldBuilder();
        }
      }
      public Builder clear() {
        super.clear();
        if (actorProfileBuilder_ == null) {
          actorProfile_ = null;
        } else {
          actorProfile_ = null;
          actorProfileBuilder_ = null;
        }
        if (spritesBuilder_ == null) {
          sprites_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
        } else {
          spritesBuilder_.clear();
        }
        camp_ = 0;

        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return cn.daxiang.protocol.game.BattleProtocol.internal_static_BattleMember_descriptor;
      }

      public cn.daxiang.protocol.game.BattleProtocol.BattleMember getDefaultInstanceForType() {
        return cn.daxiang.protocol.game.BattleProtocol.BattleMember.getDefaultInstance();
      }

      public cn.daxiang.protocol.game.BattleProtocol.BattleMember build() {
        cn.daxiang.protocol.game.BattleProtocol.BattleMember result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public cn.daxiang.protocol.game.BattleProtocol.BattleMember buildPartial() {
        cn.daxiang.protocol.game.BattleProtocol.BattleMember result = new cn.daxiang.protocol.game.BattleProtocol.BattleMember(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (actorProfileBuilder_ == null) {
          result.actorProfile_ = actorProfile_;
        } else {
          result.actorProfile_ = actorProfileBuilder_.build();
        }
        if (spritesBuilder_ == null) {
          if (((bitField0_ & 0x00000002) == 0x00000002)) {
            sprites_ = java.util.Collections.unmodifiableList(sprites_);
            bitField0_ = (bitField0_ & ~0x00000002);
          }
          result.sprites_ = sprites_;
        } else {
          result.sprites_ = spritesBuilder_.build();
        }
        result.camp_ = camp_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof cn.daxiang.protocol.game.BattleProtocol.BattleMember) {
          return mergeFrom((cn.daxiang.protocol.game.BattleProtocol.BattleMember)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(cn.daxiang.protocol.game.BattleProtocol.BattleMember other) {
        if (other == cn.daxiang.protocol.game.BattleProtocol.BattleMember.getDefaultInstance()) return this;
        if (other.hasActorProfile()) {
          mergeActorProfile(other.getActorProfile());
        }
        if (spritesBuilder_ == null) {
          if (!other.sprites_.isEmpty()) {
            if (sprites_.isEmpty()) {
              sprites_ = other.sprites_;
              bitField0_ = (bitField0_ & ~0x00000002);
            } else {
              ensureSpritesIsMutable();
              sprites_.addAll(other.sprites_);
            }
            onChanged();
          }
        } else {
          if (!other.sprites_.isEmpty()) {
            if (spritesBuilder_.isEmpty()) {
              spritesBuilder_.dispose();
              spritesBuilder_ = null;
              sprites_ = other.sprites_;
              bitField0_ = (bitField0_ & ~0x00000002);
              spritesBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getSpritesFieldBuilder() : null;
            } else {
              spritesBuilder_.addAllMessages(other.sprites_);
            }
          }
        }
        if (other.camp_ != 0) {
          setCampValue(other.getCampValue());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        cn.daxiang.protocol.game.BattleProtocol.BattleMember parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (cn.daxiang.protocol.game.BattleProtocol.BattleMember) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private cn.daxiang.protocol.game.CommonProtocol.ActorProfile actorProfile_ = null;
      private com.google.protobuf.SingleFieldBuilderV3<
          cn.daxiang.protocol.game.CommonProtocol.ActorProfile, cn.daxiang.protocol.game.CommonProtocol.ActorProfile.Builder, cn.daxiang.protocol.game.CommonProtocol.ActorProfileOrBuilder> actorProfileBuilder_;
      /**
       * <pre>
       ** 角色属性 
       * </pre>
       *
       * <code>.ActorProfile actorProfile = 1;</code>
       */
      public boolean hasActorProfile() {
        return actorProfileBuilder_ != null || actorProfile_ != null;
      }
      /**
       * <pre>
       ** 角色属性 
       * </pre>
       *
       * <code>.ActorProfile actorProfile = 1;</code>
       */
      public cn.daxiang.protocol.game.CommonProtocol.ActorProfile getActorProfile() {
        if (actorProfileBuilder_ == null) {
          return actorProfile_ == null ? cn.daxiang.protocol.game.CommonProtocol.ActorProfile.getDefaultInstance() : actorProfile_;
        } else {
          return actorProfileBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       ** 角色属性 
       * </pre>
       *
       * <code>.ActorProfile actorProfile = 1;</code>
       */
      public Builder setActorProfile(cn.daxiang.protocol.game.CommonProtocol.ActorProfile value) {
        if (actorProfileBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          actorProfile_ = value;
          onChanged();
        } else {
          actorProfileBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <pre>
       ** 角色属性 
       * </pre>
       *
       * <code>.ActorProfile actorProfile = 1;</code>
       */
      public Builder setActorProfile(
          cn.daxiang.protocol.game.CommonProtocol.ActorProfile.Builder builderForValue) {
        if (actorProfileBuilder_ == null) {
          actorProfile_ = builderForValue.build();
          onChanged();
        } else {
          actorProfileBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <pre>
       ** 角色属性 
       * </pre>
       *
       * <code>.ActorProfile actorProfile = 1;</code>
       */
      public Builder mergeActorProfile(cn.daxiang.protocol.game.CommonProtocol.ActorProfile value) {
        if (actorProfileBuilder_ == null) {
          if (actorProfile_ != null) {
            actorProfile_ =
              cn.daxiang.protocol.game.CommonProtocol.ActorProfile.newBuilder(actorProfile_).mergeFrom(value).buildPartial();
          } else {
            actorProfile_ = value;
          }
          onChanged();
        } else {
          actorProfileBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <pre>
       ** 角色属性 
       * </pre>
       *
       * <code>.ActorProfile actorProfile = 1;</code>
       */
      public Builder clearActorProfile() {
        if (actorProfileBuilder_ == null) {
          actorProfile_ = null;
          onChanged();
        } else {
          actorProfile_ = null;
          actorProfileBuilder_ = null;
        }

        return this;
      }
      /**
       * <pre>
       ** 角色属性 
       * </pre>
       *
       * <code>.ActorProfile actorProfile = 1;</code>
       */
      public cn.daxiang.protocol.game.CommonProtocol.ActorProfile.Builder getActorProfileBuilder() {
        
        onChanged();
        return getActorProfileFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       ** 角色属性 
       * </pre>
       *
       * <code>.ActorProfile actorProfile = 1;</code>
       */
      public cn.daxiang.protocol.game.CommonProtocol.ActorProfileOrBuilder getActorProfileOrBuilder() {
        if (actorProfileBuilder_ != null) {
          return actorProfileBuilder_.getMessageOrBuilder();
        } else {
          return actorProfile_ == null ?
              cn.daxiang.protocol.game.CommonProtocol.ActorProfile.getDefaultInstance() : actorProfile_;
        }
      }
      /**
       * <pre>
       ** 角色属性 
       * </pre>
       *
       * <code>.ActorProfile actorProfile = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          cn.daxiang.protocol.game.CommonProtocol.ActorProfile, cn.daxiang.protocol.game.CommonProtocol.ActorProfile.Builder, cn.daxiang.protocol.game.CommonProtocol.ActorProfileOrBuilder> 
          getActorProfileFieldBuilder() {
        if (actorProfileBuilder_ == null) {
          actorProfileBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              cn.daxiang.protocol.game.CommonProtocol.ActorProfile, cn.daxiang.protocol.game.CommonProtocol.ActorProfile.Builder, cn.daxiang.protocol.game.CommonProtocol.ActorProfileOrBuilder>(
                  getActorProfile(),
                  getParentForChildren(),
                  isClean());
          actorProfile_ = null;
        }
        return actorProfileBuilder_;
      }

      private java.util.List<cn.daxiang.protocol.game.BattleProtocol.BattleSprite> sprites_ =
        java.util.Collections.emptyList();
      private void ensureSpritesIsMutable() {
        if (!((bitField0_ & 0x00000002) == 0x00000002)) {
          sprites_ = new java.util.ArrayList<cn.daxiang.protocol.game.BattleProtocol.BattleSprite>(sprites_);
          bitField0_ |= 0x00000002;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          cn.daxiang.protocol.game.BattleProtocol.BattleSprite, cn.daxiang.protocol.game.BattleProtocol.BattleSprite.Builder, cn.daxiang.protocol.game.BattleProtocol.BattleSpriteOrBuilder> spritesBuilder_;

      /**
       * <pre>
       ** 战斗精灵列表 
       * </pre>
       *
       * <code>repeated .BattleSprite sprites = 2;</code>
       */
      public java.util.List<cn.daxiang.protocol.game.BattleProtocol.BattleSprite> getSpritesList() {
        if (spritesBuilder_ == null) {
          return java.util.Collections.unmodifiableList(sprites_);
        } else {
          return spritesBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       ** 战斗精灵列表 
       * </pre>
       *
       * <code>repeated .BattleSprite sprites = 2;</code>
       */
      public int getSpritesCount() {
        if (spritesBuilder_ == null) {
          return sprites_.size();
        } else {
          return spritesBuilder_.getCount();
        }
      }
      /**
       * <pre>
       ** 战斗精灵列表 
       * </pre>
       *
       * <code>repeated .BattleSprite sprites = 2;</code>
       */
      public cn.daxiang.protocol.game.BattleProtocol.BattleSprite getSprites(int index) {
        if (spritesBuilder_ == null) {
          return sprites_.get(index);
        } else {
          return spritesBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       ** 战斗精灵列表 
       * </pre>
       *
       * <code>repeated .BattleSprite sprites = 2;</code>
       */
      public Builder setSprites(
          int index, cn.daxiang.protocol.game.BattleProtocol.BattleSprite value) {
        if (spritesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureSpritesIsMutable();
          sprites_.set(index, value);
          onChanged();
        } else {
          spritesBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       ** 战斗精灵列表 
       * </pre>
       *
       * <code>repeated .BattleSprite sprites = 2;</code>
       */
      public Builder setSprites(
          int index, cn.daxiang.protocol.game.BattleProtocol.BattleSprite.Builder builderForValue) {
        if (spritesBuilder_ == null) {
          ensureSpritesIsMutable();
          sprites_.set(index, builderForValue.build());
          onChanged();
        } else {
          spritesBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       ** 战斗精灵列表 
       * </pre>
       *
       * <code>repeated .BattleSprite sprites = 2;</code>
       */
      public Builder addSprites(cn.daxiang.protocol.game.BattleProtocol.BattleSprite value) {
        if (spritesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureSpritesIsMutable();
          sprites_.add(value);
          onChanged();
        } else {
          spritesBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       ** 战斗精灵列表 
       * </pre>
       *
       * <code>repeated .BattleSprite sprites = 2;</code>
       */
      public Builder addSprites(
          int index, cn.daxiang.protocol.game.BattleProtocol.BattleSprite value) {
        if (spritesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureSpritesIsMutable();
          sprites_.add(index, value);
          onChanged();
        } else {
          spritesBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       ** 战斗精灵列表 
       * </pre>
       *
       * <code>repeated .BattleSprite sprites = 2;</code>
       */
      public Builder addSprites(
          cn.daxiang.protocol.game.BattleProtocol.BattleSprite.Builder builderForValue) {
        if (spritesBuilder_ == null) {
          ensureSpritesIsMutable();
          sprites_.add(builderForValue.build());
          onChanged();
        } else {
          spritesBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       ** 战斗精灵列表 
       * </pre>
       *
       * <code>repeated .BattleSprite sprites = 2;</code>
       */
      public Builder addSprites(
          int index, cn.daxiang.protocol.game.BattleProtocol.BattleSprite.Builder builderForValue) {
        if (spritesBuilder_ == null) {
          ensureSpritesIsMutable();
          sprites_.add(index, builderForValue.build());
          onChanged();
        } else {
          spritesBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       ** 战斗精灵列表 
       * </pre>
       *
       * <code>repeated .BattleSprite sprites = 2;</code>
       */
      public Builder addAllSprites(
          java.lang.Iterable<? extends cn.daxiang.protocol.game.BattleProtocol.BattleSprite> values) {
        if (spritesBuilder_ == null) {
          ensureSpritesIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, sprites_);
          onChanged();
        } else {
          spritesBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       ** 战斗精灵列表 
       * </pre>
       *
       * <code>repeated .BattleSprite sprites = 2;</code>
       */
      public Builder clearSprites() {
        if (spritesBuilder_ == null) {
          sprites_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
        } else {
          spritesBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       ** 战斗精灵列表 
       * </pre>
       *
       * <code>repeated .BattleSprite sprites = 2;</code>
       */
      public Builder removeSprites(int index) {
        if (spritesBuilder_ == null) {
          ensureSpritesIsMutable();
          sprites_.remove(index);
          onChanged();
        } else {
          spritesBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       ** 战斗精灵列表 
       * </pre>
       *
       * <code>repeated .BattleSprite sprites = 2;</code>
       */
      public cn.daxiang.protocol.game.BattleProtocol.BattleSprite.Builder getSpritesBuilder(
          int index) {
        return getSpritesFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       ** 战斗精灵列表 
       * </pre>
       *
       * <code>repeated .BattleSprite sprites = 2;</code>
       */
      public cn.daxiang.protocol.game.BattleProtocol.BattleSpriteOrBuilder getSpritesOrBuilder(
          int index) {
        if (spritesBuilder_ == null) {
          return sprites_.get(index);  } else {
          return spritesBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       ** 战斗精灵列表 
       * </pre>
       *
       * <code>repeated .BattleSprite sprites = 2;</code>
       */
      public java.util.List<? extends cn.daxiang.protocol.game.BattleProtocol.BattleSpriteOrBuilder> 
           getSpritesOrBuilderList() {
        if (spritesBuilder_ != null) {
          return spritesBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(sprites_);
        }
      }
      /**
       * <pre>
       ** 战斗精灵列表 
       * </pre>
       *
       * <code>repeated .BattleSprite sprites = 2;</code>
       */
      public cn.daxiang.protocol.game.BattleProtocol.BattleSprite.Builder addSpritesBuilder() {
        return getSpritesFieldBuilder().addBuilder(
            cn.daxiang.protocol.game.BattleProtocol.BattleSprite.getDefaultInstance());
      }
      /**
       * <pre>
       ** 战斗精灵列表 
       * </pre>
       *
       * <code>repeated .BattleSprite sprites = 2;</code>
       */
      public cn.daxiang.protocol.game.BattleProtocol.BattleSprite.Builder addSpritesBuilder(
          int index) {
        return getSpritesFieldBuilder().addBuilder(
            index, cn.daxiang.protocol.game.BattleProtocol.BattleSprite.getDefaultInstance());
      }
      /**
       * <pre>
       ** 战斗精灵列表 
       * </pre>
       *
       * <code>repeated .BattleSprite sprites = 2;</code>
       */
      public java.util.List<cn.daxiang.protocol.game.BattleProtocol.BattleSprite.Builder> 
           getSpritesBuilderList() {
        return getSpritesFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          cn.daxiang.protocol.game.BattleProtocol.BattleSprite, cn.daxiang.protocol.game.BattleProtocol.BattleSprite.Builder, cn.daxiang.protocol.game.BattleProtocol.BattleSpriteOrBuilder> 
          getSpritesFieldBuilder() {
        if (spritesBuilder_ == null) {
          spritesBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              cn.daxiang.protocol.game.BattleProtocol.BattleSprite, cn.daxiang.protocol.game.BattleProtocol.BattleSprite.Builder, cn.daxiang.protocol.game.BattleProtocol.BattleSpriteOrBuilder>(
                  sprites_,
                  ((bitField0_ & 0x00000002) == 0x00000002),
                  getParentForChildren(),
                  isClean());
          sprites_ = null;
        }
        return spritesBuilder_;
      }

      private int camp_ = 0;
      /**
       * <pre>
       ** 战斗阵营 
       * </pre>
       *
       * <code>.BattleCamp camp = 3;</code>
       */
      public int getCampValue() {
        return camp_;
      }
      /**
       * <pre>
       ** 战斗阵营 
       * </pre>
       *
       * <code>.BattleCamp camp = 3;</code>
       */
      public Builder setCampValue(int value) {
        camp_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 战斗阵营 
       * </pre>
       *
       * <code>.BattleCamp camp = 3;</code>
       */
      public cn.daxiang.protocol.game.BattleProtocol.BattleCamp getCamp() {
        cn.daxiang.protocol.game.BattleProtocol.BattleCamp result = cn.daxiang.protocol.game.BattleProtocol.BattleCamp.valueOf(camp_);
        return result == null ? cn.daxiang.protocol.game.BattleProtocol.BattleCamp.UNRECOGNIZED : result;
      }
      /**
       * <pre>
       ** 战斗阵营 
       * </pre>
       *
       * <code>.BattleCamp camp = 3;</code>
       */
      public Builder setCamp(cn.daxiang.protocol.game.BattleProtocol.BattleCamp value) {
        if (value == null) {
          throw new NullPointerException();
        }
        
        camp_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 战斗阵营 
       * </pre>
       *
       * <code>.BattleCamp camp = 3;</code>
       */
      public Builder clearCamp() {
        
        camp_ = 0;
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:BattleMember)
    }

    // @@protoc_insertion_point(class_scope:BattleMember)
    private static final cn.daxiang.protocol.game.BattleProtocol.BattleMember DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new cn.daxiang.protocol.game.BattleProtocol.BattleMember();
    }

    public static cn.daxiang.protocol.game.BattleProtocol.BattleMember getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<BattleMember>
        PARSER = new com.google.protobuf.AbstractParser<BattleMember>() {
      public BattleMember parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new BattleMember(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<BattleMember> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<BattleMember> getParserForType() {
      return PARSER;
    }

    public cn.daxiang.protocol.game.BattleProtocol.BattleMember getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface FightReportOrBuilder extends
      // @@protoc_insertion_point(interface_extends:FightReport)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     ** 发起技能的精灵单元 
     * </pre>
     *
     * <code>int64 attackerUid = 1;</code>
     */
    long getAttackerUid();

    /**
     * <pre>
     ** 技能配置id 
     * </pre>
     *
     * <code>int32 skillId = 2;</code>
     */
    int getSkillId();

    /**
     * <pre>
     ** 伤害类型 
     * </pre>
     *
     * <code>.DamageType damageType = 3;</code>
     */
    int getDamageTypeValue();
    /**
     * <pre>
     ** 伤害类型 
     * </pre>
     *
     * <code>.DamageType damageType = 3;</code>
     */
    cn.daxiang.protocol.game.BattleProtocol.DamageType getDamageType();

    /**
     * <pre>
     ** 战斗效果列表 
     * </pre>
     *
     * <code>repeated .FightEffect effectList = 4;</code>
     */
    java.util.List<cn.daxiang.protocol.game.BattleProtocol.FightEffect> 
        getEffectListList();
    /**
     * <pre>
     ** 战斗效果列表 
     * </pre>
     *
     * <code>repeated .FightEffect effectList = 4;</code>
     */
    cn.daxiang.protocol.game.BattleProtocol.FightEffect getEffectList(int index);
    /**
     * <pre>
     ** 战斗效果列表 
     * </pre>
     *
     * <code>repeated .FightEffect effectList = 4;</code>
     */
    int getEffectListCount();
    /**
     * <pre>
     ** 战斗效果列表 
     * </pre>
     *
     * <code>repeated .FightEffect effectList = 4;</code>
     */
    java.util.List<? extends cn.daxiang.protocol.game.BattleProtocol.FightEffectOrBuilder> 
        getEffectListOrBuilderList();
    /**
     * <pre>
     ** 战斗效果列表 
     * </pre>
     *
     * <code>repeated .FightEffect effectList = 4;</code>
     */
    cn.daxiang.protocol.game.BattleProtocol.FightEffectOrBuilder getEffectListOrBuilder(
        int index);
  }
  /**
   * <pre>
   ** 战报 
   * </pre>
   *
   * Protobuf type {@code FightReport}
   */
  public  static final class FightReport extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:FightReport)
      FightReportOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FightReport.newBuilder() to construct.
    private FightReport(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FightReport() {
      attackerUid_ = 0L;
      skillId_ = 0;
      damageType_ = 0;
      effectList_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private FightReport(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {

              attackerUid_ = input.readInt64();
              break;
            }
            case 16: {

              skillId_ = input.readInt32();
              break;
            }
            case 24: {
              int rawValue = input.readEnum();

              damageType_ = rawValue;
              break;
            }
            case 34: {
              if (!((mutable_bitField0_ & 0x00000008) == 0x00000008)) {
                effectList_ = new java.util.ArrayList<cn.daxiang.protocol.game.BattleProtocol.FightEffect>();
                mutable_bitField0_ |= 0x00000008;
              }
              effectList_.add(
                  input.readMessage(cn.daxiang.protocol.game.BattleProtocol.FightEffect.parser(), extensionRegistry));
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000008) == 0x00000008)) {
          effectList_ = java.util.Collections.unmodifiableList(effectList_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.BattleProtocol.internal_static_FightReport_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.daxiang.protocol.game.BattleProtocol.internal_static_FightReport_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.daxiang.protocol.game.BattleProtocol.FightReport.class, cn.daxiang.protocol.game.BattleProtocol.FightReport.Builder.class);
    }

    private int bitField0_;
    public static final int ATTACKERUID_FIELD_NUMBER = 1;
    private long attackerUid_;
    /**
     * <pre>
     ** 发起技能的精灵单元 
     * </pre>
     *
     * <code>int64 attackerUid = 1;</code>
     */
    public long getAttackerUid() {
      return attackerUid_;
    }

    public static final int SKILLID_FIELD_NUMBER = 2;
    private int skillId_;
    /**
     * <pre>
     ** 技能配置id 
     * </pre>
     *
     * <code>int32 skillId = 2;</code>
     */
    public int getSkillId() {
      return skillId_;
    }

    public static final int DAMAGETYPE_FIELD_NUMBER = 3;
    private int damageType_;
    /**
     * <pre>
     ** 伤害类型 
     * </pre>
     *
     * <code>.DamageType damageType = 3;</code>
     */
    public int getDamageTypeValue() {
      return damageType_;
    }
    /**
     * <pre>
     ** 伤害类型 
     * </pre>
     *
     * <code>.DamageType damageType = 3;</code>
     */
    public cn.daxiang.protocol.game.BattleProtocol.DamageType getDamageType() {
      cn.daxiang.protocol.game.BattleProtocol.DamageType result = cn.daxiang.protocol.game.BattleProtocol.DamageType.valueOf(damageType_);
      return result == null ? cn.daxiang.protocol.game.BattleProtocol.DamageType.UNRECOGNIZED : result;
    }

    public static final int EFFECTLIST_FIELD_NUMBER = 4;
    private java.util.List<cn.daxiang.protocol.game.BattleProtocol.FightEffect> effectList_;
    /**
     * <pre>
     ** 战斗效果列表 
     * </pre>
     *
     * <code>repeated .FightEffect effectList = 4;</code>
     */
    public java.util.List<cn.daxiang.protocol.game.BattleProtocol.FightEffect> getEffectListList() {
      return effectList_;
    }
    /**
     * <pre>
     ** 战斗效果列表 
     * </pre>
     *
     * <code>repeated .FightEffect effectList = 4;</code>
     */
    public java.util.List<? extends cn.daxiang.protocol.game.BattleProtocol.FightEffectOrBuilder> 
        getEffectListOrBuilderList() {
      return effectList_;
    }
    /**
     * <pre>
     ** 战斗效果列表 
     * </pre>
     *
     * <code>repeated .FightEffect effectList = 4;</code>
     */
    public int getEffectListCount() {
      return effectList_.size();
    }
    /**
     * <pre>
     ** 战斗效果列表 
     * </pre>
     *
     * <code>repeated .FightEffect effectList = 4;</code>
     */
    public cn.daxiang.protocol.game.BattleProtocol.FightEffect getEffectList(int index) {
      return effectList_.get(index);
    }
    /**
     * <pre>
     ** 战斗效果列表 
     * </pre>
     *
     * <code>repeated .FightEffect effectList = 4;</code>
     */
    public cn.daxiang.protocol.game.BattleProtocol.FightEffectOrBuilder getEffectListOrBuilder(
        int index) {
      return effectList_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (attackerUid_ != 0L) {
        output.writeInt64(1, attackerUid_);
      }
      if (skillId_ != 0) {
        output.writeInt32(2, skillId_);
      }
      if (damageType_ != cn.daxiang.protocol.game.BattleProtocol.DamageType.DAMAGE_TYPE_NONE.getNumber()) {
        output.writeEnum(3, damageType_);
      }
      for (int i = 0; i < effectList_.size(); i++) {
        output.writeMessage(4, effectList_.get(i));
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (attackerUid_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, attackerUid_);
      }
      if (skillId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, skillId_);
      }
      if (damageType_ != cn.daxiang.protocol.game.BattleProtocol.DamageType.DAMAGE_TYPE_NONE.getNumber()) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(3, damageType_);
      }
      for (int i = 0; i < effectList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, effectList_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof cn.daxiang.protocol.game.BattleProtocol.FightReport)) {
        return super.equals(obj);
      }
      cn.daxiang.protocol.game.BattleProtocol.FightReport other = (cn.daxiang.protocol.game.BattleProtocol.FightReport) obj;

      boolean result = true;
      result = result && (getAttackerUid()
          == other.getAttackerUid());
      result = result && (getSkillId()
          == other.getSkillId());
      result = result && damageType_ == other.damageType_;
      result = result && getEffectListList()
          .equals(other.getEffectListList());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + ATTACKERUID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getAttackerUid());
      hash = (37 * hash) + SKILLID_FIELD_NUMBER;
      hash = (53 * hash) + getSkillId();
      hash = (37 * hash) + DAMAGETYPE_FIELD_NUMBER;
      hash = (53 * hash) + damageType_;
      if (getEffectListCount() > 0) {
        hash = (37 * hash) + EFFECTLIST_FIELD_NUMBER;
        hash = (53 * hash) + getEffectListList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static cn.daxiang.protocol.game.BattleProtocol.FightReport parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.FightReport parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.FightReport parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.FightReport parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.FightReport parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.FightReport parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.FightReport parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.FightReport parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.FightReport parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.FightReport parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.FightReport parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.FightReport parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(cn.daxiang.protocol.game.BattleProtocol.FightReport prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** 战报 
     * </pre>
     *
     * Protobuf type {@code FightReport}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:FightReport)
        cn.daxiang.protocol.game.BattleProtocol.FightReportOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.daxiang.protocol.game.BattleProtocol.internal_static_FightReport_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.daxiang.protocol.game.BattleProtocol.internal_static_FightReport_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.daxiang.protocol.game.BattleProtocol.FightReport.class, cn.daxiang.protocol.game.BattleProtocol.FightReport.Builder.class);
      }

      // Construct using cn.daxiang.protocol.game.BattleProtocol.FightReport.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getEffectListFieldBuilder();
        }
      }
      public Builder clear() {
        super.clear();
        attackerUid_ = 0L;

        skillId_ = 0;

        damageType_ = 0;

        if (effectListBuilder_ == null) {
          effectList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000008);
        } else {
          effectListBuilder_.clear();
        }
        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return cn.daxiang.protocol.game.BattleProtocol.internal_static_FightReport_descriptor;
      }

      public cn.daxiang.protocol.game.BattleProtocol.FightReport getDefaultInstanceForType() {
        return cn.daxiang.protocol.game.BattleProtocol.FightReport.getDefaultInstance();
      }

      public cn.daxiang.protocol.game.BattleProtocol.FightReport build() {
        cn.daxiang.protocol.game.BattleProtocol.FightReport result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public cn.daxiang.protocol.game.BattleProtocol.FightReport buildPartial() {
        cn.daxiang.protocol.game.BattleProtocol.FightReport result = new cn.daxiang.protocol.game.BattleProtocol.FightReport(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        result.attackerUid_ = attackerUid_;
        result.skillId_ = skillId_;
        result.damageType_ = damageType_;
        if (effectListBuilder_ == null) {
          if (((bitField0_ & 0x00000008) == 0x00000008)) {
            effectList_ = java.util.Collections.unmodifiableList(effectList_);
            bitField0_ = (bitField0_ & ~0x00000008);
          }
          result.effectList_ = effectList_;
        } else {
          result.effectList_ = effectListBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof cn.daxiang.protocol.game.BattleProtocol.FightReport) {
          return mergeFrom((cn.daxiang.protocol.game.BattleProtocol.FightReport)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(cn.daxiang.protocol.game.BattleProtocol.FightReport other) {
        if (other == cn.daxiang.protocol.game.BattleProtocol.FightReport.getDefaultInstance()) return this;
        if (other.getAttackerUid() != 0L) {
          setAttackerUid(other.getAttackerUid());
        }
        if (other.getSkillId() != 0) {
          setSkillId(other.getSkillId());
        }
        if (other.damageType_ != 0) {
          setDamageTypeValue(other.getDamageTypeValue());
        }
        if (effectListBuilder_ == null) {
          if (!other.effectList_.isEmpty()) {
            if (effectList_.isEmpty()) {
              effectList_ = other.effectList_;
              bitField0_ = (bitField0_ & ~0x00000008);
            } else {
              ensureEffectListIsMutable();
              effectList_.addAll(other.effectList_);
            }
            onChanged();
          }
        } else {
          if (!other.effectList_.isEmpty()) {
            if (effectListBuilder_.isEmpty()) {
              effectListBuilder_.dispose();
              effectListBuilder_ = null;
              effectList_ = other.effectList_;
              bitField0_ = (bitField0_ & ~0x00000008);
              effectListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getEffectListFieldBuilder() : null;
            } else {
              effectListBuilder_.addAllMessages(other.effectList_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        cn.daxiang.protocol.game.BattleProtocol.FightReport parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (cn.daxiang.protocol.game.BattleProtocol.FightReport) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long attackerUid_ ;
      /**
       * <pre>
       ** 发起技能的精灵单元 
       * </pre>
       *
       * <code>int64 attackerUid = 1;</code>
       */
      public long getAttackerUid() {
        return attackerUid_;
      }
      /**
       * <pre>
       ** 发起技能的精灵单元 
       * </pre>
       *
       * <code>int64 attackerUid = 1;</code>
       */
      public Builder setAttackerUid(long value) {
        
        attackerUid_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 发起技能的精灵单元 
       * </pre>
       *
       * <code>int64 attackerUid = 1;</code>
       */
      public Builder clearAttackerUid() {
        
        attackerUid_ = 0L;
        onChanged();
        return this;
      }

      private int skillId_ ;
      /**
       * <pre>
       ** 技能配置id 
       * </pre>
       *
       * <code>int32 skillId = 2;</code>
       */
      public int getSkillId() {
        return skillId_;
      }
      /**
       * <pre>
       ** 技能配置id 
       * </pre>
       *
       * <code>int32 skillId = 2;</code>
       */
      public Builder setSkillId(int value) {
        
        skillId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 技能配置id 
       * </pre>
       *
       * <code>int32 skillId = 2;</code>
       */
      public Builder clearSkillId() {
        
        skillId_ = 0;
        onChanged();
        return this;
      }

      private int damageType_ = 0;
      /**
       * <pre>
       ** 伤害类型 
       * </pre>
       *
       * <code>.DamageType damageType = 3;</code>
       */
      public int getDamageTypeValue() {
        return damageType_;
      }
      /**
       * <pre>
       ** 伤害类型 
       * </pre>
       *
       * <code>.DamageType damageType = 3;</code>
       */
      public Builder setDamageTypeValue(int value) {
        damageType_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 伤害类型 
       * </pre>
       *
       * <code>.DamageType damageType = 3;</code>
       */
      public cn.daxiang.protocol.game.BattleProtocol.DamageType getDamageType() {
        cn.daxiang.protocol.game.BattleProtocol.DamageType result = cn.daxiang.protocol.game.BattleProtocol.DamageType.valueOf(damageType_);
        return result == null ? cn.daxiang.protocol.game.BattleProtocol.DamageType.UNRECOGNIZED : result;
      }
      /**
       * <pre>
       ** 伤害类型 
       * </pre>
       *
       * <code>.DamageType damageType = 3;</code>
       */
      public Builder setDamageType(cn.daxiang.protocol.game.BattleProtocol.DamageType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        
        damageType_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 伤害类型 
       * </pre>
       *
       * <code>.DamageType damageType = 3;</code>
       */
      public Builder clearDamageType() {
        
        damageType_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<cn.daxiang.protocol.game.BattleProtocol.FightEffect> effectList_ =
        java.util.Collections.emptyList();
      private void ensureEffectListIsMutable() {
        if (!((bitField0_ & 0x00000008) == 0x00000008)) {
          effectList_ = new java.util.ArrayList<cn.daxiang.protocol.game.BattleProtocol.FightEffect>(effectList_);
          bitField0_ |= 0x00000008;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          cn.daxiang.protocol.game.BattleProtocol.FightEffect, cn.daxiang.protocol.game.BattleProtocol.FightEffect.Builder, cn.daxiang.protocol.game.BattleProtocol.FightEffectOrBuilder> effectListBuilder_;

      /**
       * <pre>
       ** 战斗效果列表 
       * </pre>
       *
       * <code>repeated .FightEffect effectList = 4;</code>
       */
      public java.util.List<cn.daxiang.protocol.game.BattleProtocol.FightEffect> getEffectListList() {
        if (effectListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(effectList_);
        } else {
          return effectListBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       ** 战斗效果列表 
       * </pre>
       *
       * <code>repeated .FightEffect effectList = 4;</code>
       */
      public int getEffectListCount() {
        if (effectListBuilder_ == null) {
          return effectList_.size();
        } else {
          return effectListBuilder_.getCount();
        }
      }
      /**
       * <pre>
       ** 战斗效果列表 
       * </pre>
       *
       * <code>repeated .FightEffect effectList = 4;</code>
       */
      public cn.daxiang.protocol.game.BattleProtocol.FightEffect getEffectList(int index) {
        if (effectListBuilder_ == null) {
          return effectList_.get(index);
        } else {
          return effectListBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       ** 战斗效果列表 
       * </pre>
       *
       * <code>repeated .FightEffect effectList = 4;</code>
       */
      public Builder setEffectList(
          int index, cn.daxiang.protocol.game.BattleProtocol.FightEffect value) {
        if (effectListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureEffectListIsMutable();
          effectList_.set(index, value);
          onChanged();
        } else {
          effectListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       ** 战斗效果列表 
       * </pre>
       *
       * <code>repeated .FightEffect effectList = 4;</code>
       */
      public Builder setEffectList(
          int index, cn.daxiang.protocol.game.BattleProtocol.FightEffect.Builder builderForValue) {
        if (effectListBuilder_ == null) {
          ensureEffectListIsMutable();
          effectList_.set(index, builderForValue.build());
          onChanged();
        } else {
          effectListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       ** 战斗效果列表 
       * </pre>
       *
       * <code>repeated .FightEffect effectList = 4;</code>
       */
      public Builder addEffectList(cn.daxiang.protocol.game.BattleProtocol.FightEffect value) {
        if (effectListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureEffectListIsMutable();
          effectList_.add(value);
          onChanged();
        } else {
          effectListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       ** 战斗效果列表 
       * </pre>
       *
       * <code>repeated .FightEffect effectList = 4;</code>
       */
      public Builder addEffectList(
          int index, cn.daxiang.protocol.game.BattleProtocol.FightEffect value) {
        if (effectListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureEffectListIsMutable();
          effectList_.add(index, value);
          onChanged();
        } else {
          effectListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       ** 战斗效果列表 
       * </pre>
       *
       * <code>repeated .FightEffect effectList = 4;</code>
       */
      public Builder addEffectList(
          cn.daxiang.protocol.game.BattleProtocol.FightEffect.Builder builderForValue) {
        if (effectListBuilder_ == null) {
          ensureEffectListIsMutable();
          effectList_.add(builderForValue.build());
          onChanged();
        } else {
          effectListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       ** 战斗效果列表 
       * </pre>
       *
       * <code>repeated .FightEffect effectList = 4;</code>
       */
      public Builder addEffectList(
          int index, cn.daxiang.protocol.game.BattleProtocol.FightEffect.Builder builderForValue) {
        if (effectListBuilder_ == null) {
          ensureEffectListIsMutable();
          effectList_.add(index, builderForValue.build());
          onChanged();
        } else {
          effectListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       ** 战斗效果列表 
       * </pre>
       *
       * <code>repeated .FightEffect effectList = 4;</code>
       */
      public Builder addAllEffectList(
          java.lang.Iterable<? extends cn.daxiang.protocol.game.BattleProtocol.FightEffect> values) {
        if (effectListBuilder_ == null) {
          ensureEffectListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, effectList_);
          onChanged();
        } else {
          effectListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       ** 战斗效果列表 
       * </pre>
       *
       * <code>repeated .FightEffect effectList = 4;</code>
       */
      public Builder clearEffectList() {
        if (effectListBuilder_ == null) {
          effectList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000008);
          onChanged();
        } else {
          effectListBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       ** 战斗效果列表 
       * </pre>
       *
       * <code>repeated .FightEffect effectList = 4;</code>
       */
      public Builder removeEffectList(int index) {
        if (effectListBuilder_ == null) {
          ensureEffectListIsMutable();
          effectList_.remove(index);
          onChanged();
        } else {
          effectListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       ** 战斗效果列表 
       * </pre>
       *
       * <code>repeated .FightEffect effectList = 4;</code>
       */
      public cn.daxiang.protocol.game.BattleProtocol.FightEffect.Builder getEffectListBuilder(
          int index) {
        return getEffectListFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       ** 战斗效果列表 
       * </pre>
       *
       * <code>repeated .FightEffect effectList = 4;</code>
       */
      public cn.daxiang.protocol.game.BattleProtocol.FightEffectOrBuilder getEffectListOrBuilder(
          int index) {
        if (effectListBuilder_ == null) {
          return effectList_.get(index);  } else {
          return effectListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       ** 战斗效果列表 
       * </pre>
       *
       * <code>repeated .FightEffect effectList = 4;</code>
       */
      public java.util.List<? extends cn.daxiang.protocol.game.BattleProtocol.FightEffectOrBuilder> 
           getEffectListOrBuilderList() {
        if (effectListBuilder_ != null) {
          return effectListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(effectList_);
        }
      }
      /**
       * <pre>
       ** 战斗效果列表 
       * </pre>
       *
       * <code>repeated .FightEffect effectList = 4;</code>
       */
      public cn.daxiang.protocol.game.BattleProtocol.FightEffect.Builder addEffectListBuilder() {
        return getEffectListFieldBuilder().addBuilder(
            cn.daxiang.protocol.game.BattleProtocol.FightEffect.getDefaultInstance());
      }
      /**
       * <pre>
       ** 战斗效果列表 
       * </pre>
       *
       * <code>repeated .FightEffect effectList = 4;</code>
       */
      public cn.daxiang.protocol.game.BattleProtocol.FightEffect.Builder addEffectListBuilder(
          int index) {
        return getEffectListFieldBuilder().addBuilder(
            index, cn.daxiang.protocol.game.BattleProtocol.FightEffect.getDefaultInstance());
      }
      /**
       * <pre>
       ** 战斗效果列表 
       * </pre>
       *
       * <code>repeated .FightEffect effectList = 4;</code>
       */
      public java.util.List<cn.daxiang.protocol.game.BattleProtocol.FightEffect.Builder> 
           getEffectListBuilderList() {
        return getEffectListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          cn.daxiang.protocol.game.BattleProtocol.FightEffect, cn.daxiang.protocol.game.BattleProtocol.FightEffect.Builder, cn.daxiang.protocol.game.BattleProtocol.FightEffectOrBuilder> 
          getEffectListFieldBuilder() {
        if (effectListBuilder_ == null) {
          effectListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              cn.daxiang.protocol.game.BattleProtocol.FightEffect, cn.daxiang.protocol.game.BattleProtocol.FightEffect.Builder, cn.daxiang.protocol.game.BattleProtocol.FightEffectOrBuilder>(
                  effectList_,
                  ((bitField0_ & 0x00000008) == 0x00000008),
                  getParentForChildren(),
                  isClean());
          effectList_ = null;
        }
        return effectListBuilder_;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:FightReport)
    }

    // @@protoc_insertion_point(class_scope:FightReport)
    private static final cn.daxiang.protocol.game.BattleProtocol.FightReport DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new cn.daxiang.protocol.game.BattleProtocol.FightReport();
    }

    public static cn.daxiang.protocol.game.BattleProtocol.FightReport getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<FightReport>
        PARSER = new com.google.protobuf.AbstractParser<FightReport>() {
      public FightReport parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new FightReport(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<FightReport> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FightReport> getParserForType() {
      return PARSER;
    }

    public cn.daxiang.protocol.game.BattleProtocol.FightReport getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface FightEffectOrBuilder extends
      // @@protoc_insertion_point(interface_extends:FightEffect)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     **  该技能属性的承受者Uid 
     * </pre>
     *
     * <code>int64 targeterUid = 1;</code>
     */
    long getTargeterUid();

    /**
     * <pre>
     ** 技能效果id 
     * </pre>
     *
     * <code>int32 effectId = 2;</code>
     */
    int getEffectId();

    /**
     * <pre>
     *** 技能触发后精灵属性变化的属性Map key:{&#64;code SpriteKey},value:attributesValue
     * </pre>
     *
     * <code>.BattleSprite battleSprite = 3;</code>
     */
    boolean hasBattleSprite();
    /**
     * <pre>
     *** 技能触发后精灵属性变化的属性Map key:{&#64;code SpriteKey},value:attributesValue
     * </pre>
     *
     * <code>.BattleSprite battleSprite = 3;</code>
     */
    cn.daxiang.protocol.game.BattleProtocol.BattleSprite getBattleSprite();
    /**
     * <pre>
     *** 技能触发后精灵属性变化的属性Map key:{&#64;code SpriteKey},value:attributesValue
     * </pre>
     *
     * <code>.BattleSprite battleSprite = 3;</code>
     */
    cn.daxiang.protocol.game.BattleProtocol.BattleSpriteOrBuilder getBattleSpriteOrBuilder();

    /**
     * <pre>
     ** 是否暴击 
     * </pre>
     *
     * <code>bool critical = 4;</code>
     */
    boolean getCritical();

    /**
     * <pre>
     **  命中状态 
     * </pre>
     *
     * <code>.HitState hitState = 5;</code>
     */
    int getHitStateValue();
    /**
     * <pre>
     **  命中状态 
     * </pre>
     *
     * <code>.HitState hitState = 5;</code>
     */
    cn.daxiang.protocol.game.BattleProtocol.HitState getHitState();

    /**
     * <pre>
     ** 产生的buff 
     * </pre>
     *
     * <code>.BattleBuff buff = 6;</code>
     */
    boolean hasBuff();
    /**
     * <pre>
     ** 产生的buff 
     * </pre>
     *
     * <code>.BattleBuff buff = 6;</code>
     */
    cn.daxiang.protocol.game.BattleProtocol.BattleBuff getBuff();
    /**
     * <pre>
     ** 产生的buff 
     * </pre>
     *
     * <code>.BattleBuff buff = 6;</code>
     */
    cn.daxiang.protocol.game.BattleProtocol.BattleBuffOrBuilder getBuffOrBuilder();

    /**
     * <pre>
     ** 是否格挡 
     * </pre>
     *
     * <code>bool block = 7;</code>
     */
    boolean getBlock();
  }
  /**
   * <pre>
   ** 战斗效果 
   * </pre>
   *
   * Protobuf type {@code FightEffect}
   */
  public  static final class FightEffect extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:FightEffect)
      FightEffectOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FightEffect.newBuilder() to construct.
    private FightEffect(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FightEffect() {
      targeterUid_ = 0L;
      effectId_ = 0;
      critical_ = false;
      hitState_ = 0;
      block_ = false;
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private FightEffect(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {

              targeterUid_ = input.readInt64();
              break;
            }
            case 16: {

              effectId_ = input.readInt32();
              break;
            }
            case 26: {
              cn.daxiang.protocol.game.BattleProtocol.BattleSprite.Builder subBuilder = null;
              if (battleSprite_ != null) {
                subBuilder = battleSprite_.toBuilder();
              }
              battleSprite_ = input.readMessage(cn.daxiang.protocol.game.BattleProtocol.BattleSprite.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(battleSprite_);
                battleSprite_ = subBuilder.buildPartial();
              }

              break;
            }
            case 32: {

              critical_ = input.readBool();
              break;
            }
            case 40: {
              int rawValue = input.readEnum();

              hitState_ = rawValue;
              break;
            }
            case 50: {
              cn.daxiang.protocol.game.BattleProtocol.BattleBuff.Builder subBuilder = null;
              if (buff_ != null) {
                subBuilder = buff_.toBuilder();
              }
              buff_ = input.readMessage(cn.daxiang.protocol.game.BattleProtocol.BattleBuff.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(buff_);
                buff_ = subBuilder.buildPartial();
              }

              break;
            }
            case 56: {

              block_ = input.readBool();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.BattleProtocol.internal_static_FightEffect_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.daxiang.protocol.game.BattleProtocol.internal_static_FightEffect_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.daxiang.protocol.game.BattleProtocol.FightEffect.class, cn.daxiang.protocol.game.BattleProtocol.FightEffect.Builder.class);
    }

    public static final int TARGETERUID_FIELD_NUMBER = 1;
    private long targeterUid_;
    /**
     * <pre>
     **  该技能属性的承受者Uid 
     * </pre>
     *
     * <code>int64 targeterUid = 1;</code>
     */
    public long getTargeterUid() {
      return targeterUid_;
    }

    public static final int EFFECTID_FIELD_NUMBER = 2;
    private int effectId_;
    /**
     * <pre>
     ** 技能效果id 
     * </pre>
     *
     * <code>int32 effectId = 2;</code>
     */
    public int getEffectId() {
      return effectId_;
    }

    public static final int BATTLESPRITE_FIELD_NUMBER = 3;
    private cn.daxiang.protocol.game.BattleProtocol.BattleSprite battleSprite_;
    /**
     * <pre>
     *** 技能触发后精灵属性变化的属性Map key:{&#64;code SpriteKey},value:attributesValue
     * </pre>
     *
     * <code>.BattleSprite battleSprite = 3;</code>
     */
    public boolean hasBattleSprite() {
      return battleSprite_ != null;
    }
    /**
     * <pre>
     *** 技能触发后精灵属性变化的属性Map key:{&#64;code SpriteKey},value:attributesValue
     * </pre>
     *
     * <code>.BattleSprite battleSprite = 3;</code>
     */
    public cn.daxiang.protocol.game.BattleProtocol.BattleSprite getBattleSprite() {
      return battleSprite_ == null ? cn.daxiang.protocol.game.BattleProtocol.BattleSprite.getDefaultInstance() : battleSprite_;
    }
    /**
     * <pre>
     *** 技能触发后精灵属性变化的属性Map key:{&#64;code SpriteKey},value:attributesValue
     * </pre>
     *
     * <code>.BattleSprite battleSprite = 3;</code>
     */
    public cn.daxiang.protocol.game.BattleProtocol.BattleSpriteOrBuilder getBattleSpriteOrBuilder() {
      return getBattleSprite();
    }

    public static final int CRITICAL_FIELD_NUMBER = 4;
    private boolean critical_;
    /**
     * <pre>
     ** 是否暴击 
     * </pre>
     *
     * <code>bool critical = 4;</code>
     */
    public boolean getCritical() {
      return critical_;
    }

    public static final int HITSTATE_FIELD_NUMBER = 5;
    private int hitState_;
    /**
     * <pre>
     **  命中状态 
     * </pre>
     *
     * <code>.HitState hitState = 5;</code>
     */
    public int getHitStateValue() {
      return hitState_;
    }
    /**
     * <pre>
     **  命中状态 
     * </pre>
     *
     * <code>.HitState hitState = 5;</code>
     */
    public cn.daxiang.protocol.game.BattleProtocol.HitState getHitState() {
      cn.daxiang.protocol.game.BattleProtocol.HitState result = cn.daxiang.protocol.game.BattleProtocol.HitState.valueOf(hitState_);
      return result == null ? cn.daxiang.protocol.game.BattleProtocol.HitState.UNRECOGNIZED : result;
    }

    public static final int BUFF_FIELD_NUMBER = 6;
    private cn.daxiang.protocol.game.BattleProtocol.BattleBuff buff_;
    /**
     * <pre>
     ** 产生的buff 
     * </pre>
     *
     * <code>.BattleBuff buff = 6;</code>
     */
    public boolean hasBuff() {
      return buff_ != null;
    }
    /**
     * <pre>
     ** 产生的buff 
     * </pre>
     *
     * <code>.BattleBuff buff = 6;</code>
     */
    public cn.daxiang.protocol.game.BattleProtocol.BattleBuff getBuff() {
      return buff_ == null ? cn.daxiang.protocol.game.BattleProtocol.BattleBuff.getDefaultInstance() : buff_;
    }
    /**
     * <pre>
     ** 产生的buff 
     * </pre>
     *
     * <code>.BattleBuff buff = 6;</code>
     */
    public cn.daxiang.protocol.game.BattleProtocol.BattleBuffOrBuilder getBuffOrBuilder() {
      return getBuff();
    }

    public static final int BLOCK_FIELD_NUMBER = 7;
    private boolean block_;
    /**
     * <pre>
     ** 是否格挡 
     * </pre>
     *
     * <code>bool block = 7;</code>
     */
    public boolean getBlock() {
      return block_;
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (targeterUid_ != 0L) {
        output.writeInt64(1, targeterUid_);
      }
      if (effectId_ != 0) {
        output.writeInt32(2, effectId_);
      }
      if (battleSprite_ != null) {
        output.writeMessage(3, getBattleSprite());
      }
      if (critical_ != false) {
        output.writeBool(4, critical_);
      }
      if (hitState_ != cn.daxiang.protocol.game.BattleProtocol.HitState.HIT_STATE_NONE.getNumber()) {
        output.writeEnum(5, hitState_);
      }
      if (buff_ != null) {
        output.writeMessage(6, getBuff());
      }
      if (block_ != false) {
        output.writeBool(7, block_);
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (targeterUid_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, targeterUid_);
      }
      if (effectId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, effectId_);
      }
      if (battleSprite_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, getBattleSprite());
      }
      if (critical_ != false) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(4, critical_);
      }
      if (hitState_ != cn.daxiang.protocol.game.BattleProtocol.HitState.HIT_STATE_NONE.getNumber()) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(5, hitState_);
      }
      if (buff_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(6, getBuff());
      }
      if (block_ != false) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(7, block_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof cn.daxiang.protocol.game.BattleProtocol.FightEffect)) {
        return super.equals(obj);
      }
      cn.daxiang.protocol.game.BattleProtocol.FightEffect other = (cn.daxiang.protocol.game.BattleProtocol.FightEffect) obj;

      boolean result = true;
      result = result && (getTargeterUid()
          == other.getTargeterUid());
      result = result && (getEffectId()
          == other.getEffectId());
      result = result && (hasBattleSprite() == other.hasBattleSprite());
      if (hasBattleSprite()) {
        result = result && getBattleSprite()
            .equals(other.getBattleSprite());
      }
      result = result && (getCritical()
          == other.getCritical());
      result = result && hitState_ == other.hitState_;
      result = result && (hasBuff() == other.hasBuff());
      if (hasBuff()) {
        result = result && getBuff()
            .equals(other.getBuff());
      }
      result = result && (getBlock()
          == other.getBlock());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TARGETERUID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTargeterUid());
      hash = (37 * hash) + EFFECTID_FIELD_NUMBER;
      hash = (53 * hash) + getEffectId();
      if (hasBattleSprite()) {
        hash = (37 * hash) + BATTLESPRITE_FIELD_NUMBER;
        hash = (53 * hash) + getBattleSprite().hashCode();
      }
      hash = (37 * hash) + CRITICAL_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getCritical());
      hash = (37 * hash) + HITSTATE_FIELD_NUMBER;
      hash = (53 * hash) + hitState_;
      if (hasBuff()) {
        hash = (37 * hash) + BUFF_FIELD_NUMBER;
        hash = (53 * hash) + getBuff().hashCode();
      }
      hash = (37 * hash) + BLOCK_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getBlock());
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static cn.daxiang.protocol.game.BattleProtocol.FightEffect parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.FightEffect parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.FightEffect parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.FightEffect parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.FightEffect parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.FightEffect parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.FightEffect parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.FightEffect parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.FightEffect parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.FightEffect parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.FightEffect parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.FightEffect parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(cn.daxiang.protocol.game.BattleProtocol.FightEffect prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** 战斗效果 
     * </pre>
     *
     * Protobuf type {@code FightEffect}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:FightEffect)
        cn.daxiang.protocol.game.BattleProtocol.FightEffectOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.daxiang.protocol.game.BattleProtocol.internal_static_FightEffect_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.daxiang.protocol.game.BattleProtocol.internal_static_FightEffect_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.daxiang.protocol.game.BattleProtocol.FightEffect.class, cn.daxiang.protocol.game.BattleProtocol.FightEffect.Builder.class);
      }

      // Construct using cn.daxiang.protocol.game.BattleProtocol.FightEffect.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        targeterUid_ = 0L;

        effectId_ = 0;

        if (battleSpriteBuilder_ == null) {
          battleSprite_ = null;
        } else {
          battleSprite_ = null;
          battleSpriteBuilder_ = null;
        }
        critical_ = false;

        hitState_ = 0;

        if (buffBuilder_ == null) {
          buff_ = null;
        } else {
          buff_ = null;
          buffBuilder_ = null;
        }
        block_ = false;

        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return cn.daxiang.protocol.game.BattleProtocol.internal_static_FightEffect_descriptor;
      }

      public cn.daxiang.protocol.game.BattleProtocol.FightEffect getDefaultInstanceForType() {
        return cn.daxiang.protocol.game.BattleProtocol.FightEffect.getDefaultInstance();
      }

      public cn.daxiang.protocol.game.BattleProtocol.FightEffect build() {
        cn.daxiang.protocol.game.BattleProtocol.FightEffect result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public cn.daxiang.protocol.game.BattleProtocol.FightEffect buildPartial() {
        cn.daxiang.protocol.game.BattleProtocol.FightEffect result = new cn.daxiang.protocol.game.BattleProtocol.FightEffect(this);
        result.targeterUid_ = targeterUid_;
        result.effectId_ = effectId_;
        if (battleSpriteBuilder_ == null) {
          result.battleSprite_ = battleSprite_;
        } else {
          result.battleSprite_ = battleSpriteBuilder_.build();
        }
        result.critical_ = critical_;
        result.hitState_ = hitState_;
        if (buffBuilder_ == null) {
          result.buff_ = buff_;
        } else {
          result.buff_ = buffBuilder_.build();
        }
        result.block_ = block_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof cn.daxiang.protocol.game.BattleProtocol.FightEffect) {
          return mergeFrom((cn.daxiang.protocol.game.BattleProtocol.FightEffect)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(cn.daxiang.protocol.game.BattleProtocol.FightEffect other) {
        if (other == cn.daxiang.protocol.game.BattleProtocol.FightEffect.getDefaultInstance()) return this;
        if (other.getTargeterUid() != 0L) {
          setTargeterUid(other.getTargeterUid());
        }
        if (other.getEffectId() != 0) {
          setEffectId(other.getEffectId());
        }
        if (other.hasBattleSprite()) {
          mergeBattleSprite(other.getBattleSprite());
        }
        if (other.getCritical() != false) {
          setCritical(other.getCritical());
        }
        if (other.hitState_ != 0) {
          setHitStateValue(other.getHitStateValue());
        }
        if (other.hasBuff()) {
          mergeBuff(other.getBuff());
        }
        if (other.getBlock() != false) {
          setBlock(other.getBlock());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        cn.daxiang.protocol.game.BattleProtocol.FightEffect parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (cn.daxiang.protocol.game.BattleProtocol.FightEffect) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private long targeterUid_ ;
      /**
       * <pre>
       **  该技能属性的承受者Uid 
       * </pre>
       *
       * <code>int64 targeterUid = 1;</code>
       */
      public long getTargeterUid() {
        return targeterUid_;
      }
      /**
       * <pre>
       **  该技能属性的承受者Uid 
       * </pre>
       *
       * <code>int64 targeterUid = 1;</code>
       */
      public Builder setTargeterUid(long value) {
        
        targeterUid_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       **  该技能属性的承受者Uid 
       * </pre>
       *
       * <code>int64 targeterUid = 1;</code>
       */
      public Builder clearTargeterUid() {
        
        targeterUid_ = 0L;
        onChanged();
        return this;
      }

      private int effectId_ ;
      /**
       * <pre>
       ** 技能效果id 
       * </pre>
       *
       * <code>int32 effectId = 2;</code>
       */
      public int getEffectId() {
        return effectId_;
      }
      /**
       * <pre>
       ** 技能效果id 
       * </pre>
       *
       * <code>int32 effectId = 2;</code>
       */
      public Builder setEffectId(int value) {
        
        effectId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 技能效果id 
       * </pre>
       *
       * <code>int32 effectId = 2;</code>
       */
      public Builder clearEffectId() {
        
        effectId_ = 0;
        onChanged();
        return this;
      }

      private cn.daxiang.protocol.game.BattleProtocol.BattleSprite battleSprite_ = null;
      private com.google.protobuf.SingleFieldBuilderV3<
          cn.daxiang.protocol.game.BattleProtocol.BattleSprite, cn.daxiang.protocol.game.BattleProtocol.BattleSprite.Builder, cn.daxiang.protocol.game.BattleProtocol.BattleSpriteOrBuilder> battleSpriteBuilder_;
      /**
       * <pre>
       *** 技能触发后精灵属性变化的属性Map key:{&#64;code SpriteKey},value:attributesValue
       * </pre>
       *
       * <code>.BattleSprite battleSprite = 3;</code>
       */
      public boolean hasBattleSprite() {
        return battleSpriteBuilder_ != null || battleSprite_ != null;
      }
      /**
       * <pre>
       *** 技能触发后精灵属性变化的属性Map key:{&#64;code SpriteKey},value:attributesValue
       * </pre>
       *
       * <code>.BattleSprite battleSprite = 3;</code>
       */
      public cn.daxiang.protocol.game.BattleProtocol.BattleSprite getBattleSprite() {
        if (battleSpriteBuilder_ == null) {
          return battleSprite_ == null ? cn.daxiang.protocol.game.BattleProtocol.BattleSprite.getDefaultInstance() : battleSprite_;
        } else {
          return battleSpriteBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       *** 技能触发后精灵属性变化的属性Map key:{&#64;code SpriteKey},value:attributesValue
       * </pre>
       *
       * <code>.BattleSprite battleSprite = 3;</code>
       */
      public Builder setBattleSprite(cn.daxiang.protocol.game.BattleProtocol.BattleSprite value) {
        if (battleSpriteBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          battleSprite_ = value;
          onChanged();
        } else {
          battleSpriteBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <pre>
       *** 技能触发后精灵属性变化的属性Map key:{&#64;code SpriteKey},value:attributesValue
       * </pre>
       *
       * <code>.BattleSprite battleSprite = 3;</code>
       */
      public Builder setBattleSprite(
          cn.daxiang.protocol.game.BattleProtocol.BattleSprite.Builder builderForValue) {
        if (battleSpriteBuilder_ == null) {
          battleSprite_ = builderForValue.build();
          onChanged();
        } else {
          battleSpriteBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <pre>
       *** 技能触发后精灵属性变化的属性Map key:{&#64;code SpriteKey},value:attributesValue
       * </pre>
       *
       * <code>.BattleSprite battleSprite = 3;</code>
       */
      public Builder mergeBattleSprite(cn.daxiang.protocol.game.BattleProtocol.BattleSprite value) {
        if (battleSpriteBuilder_ == null) {
          if (battleSprite_ != null) {
            battleSprite_ =
              cn.daxiang.protocol.game.BattleProtocol.BattleSprite.newBuilder(battleSprite_).mergeFrom(value).buildPartial();
          } else {
            battleSprite_ = value;
          }
          onChanged();
        } else {
          battleSpriteBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <pre>
       *** 技能触发后精灵属性变化的属性Map key:{&#64;code SpriteKey},value:attributesValue
       * </pre>
       *
       * <code>.BattleSprite battleSprite = 3;</code>
       */
      public Builder clearBattleSprite() {
        if (battleSpriteBuilder_ == null) {
          battleSprite_ = null;
          onChanged();
        } else {
          battleSprite_ = null;
          battleSpriteBuilder_ = null;
        }

        return this;
      }
      /**
       * <pre>
       *** 技能触发后精灵属性变化的属性Map key:{&#64;code SpriteKey},value:attributesValue
       * </pre>
       *
       * <code>.BattleSprite battleSprite = 3;</code>
       */
      public cn.daxiang.protocol.game.BattleProtocol.BattleSprite.Builder getBattleSpriteBuilder() {
        
        onChanged();
        return getBattleSpriteFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       *** 技能触发后精灵属性变化的属性Map key:{&#64;code SpriteKey},value:attributesValue
       * </pre>
       *
       * <code>.BattleSprite battleSprite = 3;</code>
       */
      public cn.daxiang.protocol.game.BattleProtocol.BattleSpriteOrBuilder getBattleSpriteOrBuilder() {
        if (battleSpriteBuilder_ != null) {
          return battleSpriteBuilder_.getMessageOrBuilder();
        } else {
          return battleSprite_ == null ?
              cn.daxiang.protocol.game.BattleProtocol.BattleSprite.getDefaultInstance() : battleSprite_;
        }
      }
      /**
       * <pre>
       *** 技能触发后精灵属性变化的属性Map key:{&#64;code SpriteKey},value:attributesValue
       * </pre>
       *
       * <code>.BattleSprite battleSprite = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          cn.daxiang.protocol.game.BattleProtocol.BattleSprite, cn.daxiang.protocol.game.BattleProtocol.BattleSprite.Builder, cn.daxiang.protocol.game.BattleProtocol.BattleSpriteOrBuilder> 
          getBattleSpriteFieldBuilder() {
        if (battleSpriteBuilder_ == null) {
          battleSpriteBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              cn.daxiang.protocol.game.BattleProtocol.BattleSprite, cn.daxiang.protocol.game.BattleProtocol.BattleSprite.Builder, cn.daxiang.protocol.game.BattleProtocol.BattleSpriteOrBuilder>(
                  getBattleSprite(),
                  getParentForChildren(),
                  isClean());
          battleSprite_ = null;
        }
        return battleSpriteBuilder_;
      }

      private boolean critical_ ;
      /**
       * <pre>
       ** 是否暴击 
       * </pre>
       *
       * <code>bool critical = 4;</code>
       */
      public boolean getCritical() {
        return critical_;
      }
      /**
       * <pre>
       ** 是否暴击 
       * </pre>
       *
       * <code>bool critical = 4;</code>
       */
      public Builder setCritical(boolean value) {
        
        critical_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 是否暴击 
       * </pre>
       *
       * <code>bool critical = 4;</code>
       */
      public Builder clearCritical() {
        
        critical_ = false;
        onChanged();
        return this;
      }

      private int hitState_ = 0;
      /**
       * <pre>
       **  命中状态 
       * </pre>
       *
       * <code>.HitState hitState = 5;</code>
       */
      public int getHitStateValue() {
        return hitState_;
      }
      /**
       * <pre>
       **  命中状态 
       * </pre>
       *
       * <code>.HitState hitState = 5;</code>
       */
      public Builder setHitStateValue(int value) {
        hitState_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       **  命中状态 
       * </pre>
       *
       * <code>.HitState hitState = 5;</code>
       */
      public cn.daxiang.protocol.game.BattleProtocol.HitState getHitState() {
        cn.daxiang.protocol.game.BattleProtocol.HitState result = cn.daxiang.protocol.game.BattleProtocol.HitState.valueOf(hitState_);
        return result == null ? cn.daxiang.protocol.game.BattleProtocol.HitState.UNRECOGNIZED : result;
      }
      /**
       * <pre>
       **  命中状态 
       * </pre>
       *
       * <code>.HitState hitState = 5;</code>
       */
      public Builder setHitState(cn.daxiang.protocol.game.BattleProtocol.HitState value) {
        if (value == null) {
          throw new NullPointerException();
        }
        
        hitState_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       **  命中状态 
       * </pre>
       *
       * <code>.HitState hitState = 5;</code>
       */
      public Builder clearHitState() {
        
        hitState_ = 0;
        onChanged();
        return this;
      }

      private cn.daxiang.protocol.game.BattleProtocol.BattleBuff buff_ = null;
      private com.google.protobuf.SingleFieldBuilderV3<
          cn.daxiang.protocol.game.BattleProtocol.BattleBuff, cn.daxiang.protocol.game.BattleProtocol.BattleBuff.Builder, cn.daxiang.protocol.game.BattleProtocol.BattleBuffOrBuilder> buffBuilder_;
      /**
       * <pre>
       ** 产生的buff 
       * </pre>
       *
       * <code>.BattleBuff buff = 6;</code>
       */
      public boolean hasBuff() {
        return buffBuilder_ != null || buff_ != null;
      }
      /**
       * <pre>
       ** 产生的buff 
       * </pre>
       *
       * <code>.BattleBuff buff = 6;</code>
       */
      public cn.daxiang.protocol.game.BattleProtocol.BattleBuff getBuff() {
        if (buffBuilder_ == null) {
          return buff_ == null ? cn.daxiang.protocol.game.BattleProtocol.BattleBuff.getDefaultInstance() : buff_;
        } else {
          return buffBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       ** 产生的buff 
       * </pre>
       *
       * <code>.BattleBuff buff = 6;</code>
       */
      public Builder setBuff(cn.daxiang.protocol.game.BattleProtocol.BattleBuff value) {
        if (buffBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          buff_ = value;
          onChanged();
        } else {
          buffBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <pre>
       ** 产生的buff 
       * </pre>
       *
       * <code>.BattleBuff buff = 6;</code>
       */
      public Builder setBuff(
          cn.daxiang.protocol.game.BattleProtocol.BattleBuff.Builder builderForValue) {
        if (buffBuilder_ == null) {
          buff_ = builderForValue.build();
          onChanged();
        } else {
          buffBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <pre>
       ** 产生的buff 
       * </pre>
       *
       * <code>.BattleBuff buff = 6;</code>
       */
      public Builder mergeBuff(cn.daxiang.protocol.game.BattleProtocol.BattleBuff value) {
        if (buffBuilder_ == null) {
          if (buff_ != null) {
            buff_ =
              cn.daxiang.protocol.game.BattleProtocol.BattleBuff.newBuilder(buff_).mergeFrom(value).buildPartial();
          } else {
            buff_ = value;
          }
          onChanged();
        } else {
          buffBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <pre>
       ** 产生的buff 
       * </pre>
       *
       * <code>.BattleBuff buff = 6;</code>
       */
      public Builder clearBuff() {
        if (buffBuilder_ == null) {
          buff_ = null;
          onChanged();
        } else {
          buff_ = null;
          buffBuilder_ = null;
        }

        return this;
      }
      /**
       * <pre>
       ** 产生的buff 
       * </pre>
       *
       * <code>.BattleBuff buff = 6;</code>
       */
      public cn.daxiang.protocol.game.BattleProtocol.BattleBuff.Builder getBuffBuilder() {
        
        onChanged();
        return getBuffFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       ** 产生的buff 
       * </pre>
       *
       * <code>.BattleBuff buff = 6;</code>
       */
      public cn.daxiang.protocol.game.BattleProtocol.BattleBuffOrBuilder getBuffOrBuilder() {
        if (buffBuilder_ != null) {
          return buffBuilder_.getMessageOrBuilder();
        } else {
          return buff_ == null ?
              cn.daxiang.protocol.game.BattleProtocol.BattleBuff.getDefaultInstance() : buff_;
        }
      }
      /**
       * <pre>
       ** 产生的buff 
       * </pre>
       *
       * <code>.BattleBuff buff = 6;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          cn.daxiang.protocol.game.BattleProtocol.BattleBuff, cn.daxiang.protocol.game.BattleProtocol.BattleBuff.Builder, cn.daxiang.protocol.game.BattleProtocol.BattleBuffOrBuilder> 
          getBuffFieldBuilder() {
        if (buffBuilder_ == null) {
          buffBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              cn.daxiang.protocol.game.BattleProtocol.BattleBuff, cn.daxiang.protocol.game.BattleProtocol.BattleBuff.Builder, cn.daxiang.protocol.game.BattleProtocol.BattleBuffOrBuilder>(
                  getBuff(),
                  getParentForChildren(),
                  isClean());
          buff_ = null;
        }
        return buffBuilder_;
      }

      private boolean block_ ;
      /**
       * <pre>
       ** 是否格挡 
       * </pre>
       *
       * <code>bool block = 7;</code>
       */
      public boolean getBlock() {
        return block_;
      }
      /**
       * <pre>
       ** 是否格挡 
       * </pre>
       *
       * <code>bool block = 7;</code>
       */
      public Builder setBlock(boolean value) {
        
        block_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 是否格挡 
       * </pre>
       *
       * <code>bool block = 7;</code>
       */
      public Builder clearBlock() {
        
        block_ = false;
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:FightEffect)
    }

    // @@protoc_insertion_point(class_scope:FightEffect)
    private static final cn.daxiang.protocol.game.BattleProtocol.FightEffect DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new cn.daxiang.protocol.game.BattleProtocol.FightEffect();
    }

    public static cn.daxiang.protocol.game.BattleProtocol.FightEffect getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<FightEffect>
        PARSER = new com.google.protobuf.AbstractParser<FightEffect>() {
      public FightEffect parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new FightEffect(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<FightEffect> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FightEffect> getParserForType() {
      return PARSER;
    }

    public cn.daxiang.protocol.game.BattleProtocol.FightEffect getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface BattleBuffOrBuilder extends
      // @@protoc_insertion_point(interface_extends:BattleBuff)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     ** 唯一KEY 
     * </pre>
     *
     * <code>string key = 1;</code>
     */
    java.lang.String getKey();
    /**
     * <pre>
     ** 唯一KEY 
     * </pre>
     *
     * <code>string key = 1;</code>
     */
    com.google.protobuf.ByteString
        getKeyBytes();

    /**
     * <pre>
     ** 效果ID 
     * </pre>
     *
     * <code>int32 effectId = 2;</code>
     */
    int getEffectId();

    /**
     * <pre>
     ** buff值 
     * </pre>
     *
     * <code>int64 value = 3;</code>
     */
    long getValue();

    /**
     * <pre>
     ** 层数 
     * </pre>
     *
     * <code>int32 layers = 4;</code>
     */
    int getLayers();
  }
  /**
   * <pre>
   ** 战斗Buffer 
   * </pre>
   *
   * Protobuf type {@code BattleBuff}
   */
  public  static final class BattleBuff extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:BattleBuff)
      BattleBuffOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use BattleBuff.newBuilder() to construct.
    private BattleBuff(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private BattleBuff() {
      key_ = "";
      effectId_ = 0;
      value_ = 0L;
      layers_ = 0;
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private BattleBuff(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              java.lang.String s = input.readStringRequireUtf8();

              key_ = s;
              break;
            }
            case 16: {

              effectId_ = input.readInt32();
              break;
            }
            case 24: {

              value_ = input.readInt64();
              break;
            }
            case 32: {

              layers_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.BattleProtocol.internal_static_BattleBuff_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.daxiang.protocol.game.BattleProtocol.internal_static_BattleBuff_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.daxiang.protocol.game.BattleProtocol.BattleBuff.class, cn.daxiang.protocol.game.BattleProtocol.BattleBuff.Builder.class);
    }

    public static final int KEY_FIELD_NUMBER = 1;
    private volatile java.lang.Object key_;
    /**
     * <pre>
     ** 唯一KEY 
     * </pre>
     *
     * <code>string key = 1;</code>
     */
    public java.lang.String getKey() {
      java.lang.Object ref = key_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        key_ = s;
        return s;
      }
    }
    /**
     * <pre>
     ** 唯一KEY 
     * </pre>
     *
     * <code>string key = 1;</code>
     */
    public com.google.protobuf.ByteString
        getKeyBytes() {
      java.lang.Object ref = key_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        key_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int EFFECTID_FIELD_NUMBER = 2;
    private int effectId_;
    /**
     * <pre>
     ** 效果ID 
     * </pre>
     *
     * <code>int32 effectId = 2;</code>
     */
    public int getEffectId() {
      return effectId_;
    }

    public static final int VALUE_FIELD_NUMBER = 3;
    private long value_;
    /**
     * <pre>
     ** buff值 
     * </pre>
     *
     * <code>int64 value = 3;</code>
     */
    public long getValue() {
      return value_;
    }

    public static final int LAYERS_FIELD_NUMBER = 4;
    private int layers_;
    /**
     * <pre>
     ** 层数 
     * </pre>
     *
     * <code>int32 layers = 4;</code>
     */
    public int getLayers() {
      return layers_;
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!getKeyBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, key_);
      }
      if (effectId_ != 0) {
        output.writeInt32(2, effectId_);
      }
      if (value_ != 0L) {
        output.writeInt64(3, value_);
      }
      if (layers_ != 0) {
        output.writeInt32(4, layers_);
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!getKeyBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, key_);
      }
      if (effectId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, effectId_);
      }
      if (value_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, value_);
      }
      if (layers_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, layers_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof cn.daxiang.protocol.game.BattleProtocol.BattleBuff)) {
        return super.equals(obj);
      }
      cn.daxiang.protocol.game.BattleProtocol.BattleBuff other = (cn.daxiang.protocol.game.BattleProtocol.BattleBuff) obj;

      boolean result = true;
      result = result && getKey()
          .equals(other.getKey());
      result = result && (getEffectId()
          == other.getEffectId());
      result = result && (getValue()
          == other.getValue());
      result = result && (getLayers()
          == other.getLayers());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + KEY_FIELD_NUMBER;
      hash = (53 * hash) + getKey().hashCode();
      hash = (37 * hash) + EFFECTID_FIELD_NUMBER;
      hash = (53 * hash) + getEffectId();
      hash = (37 * hash) + VALUE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getValue());
      hash = (37 * hash) + LAYERS_FIELD_NUMBER;
      hash = (53 * hash) + getLayers();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static cn.daxiang.protocol.game.BattleProtocol.BattleBuff parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.BattleBuff parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.BattleBuff parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.BattleBuff parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.BattleBuff parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.BattleBuff parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.BattleBuff parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.BattleBuff parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.BattleBuff parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.BattleBuff parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.BattleBuff parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.BattleBuff parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(cn.daxiang.protocol.game.BattleProtocol.BattleBuff prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** 战斗Buffer 
     * </pre>
     *
     * Protobuf type {@code BattleBuff}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:BattleBuff)
        cn.daxiang.protocol.game.BattleProtocol.BattleBuffOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.daxiang.protocol.game.BattleProtocol.internal_static_BattleBuff_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.daxiang.protocol.game.BattleProtocol.internal_static_BattleBuff_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.daxiang.protocol.game.BattleProtocol.BattleBuff.class, cn.daxiang.protocol.game.BattleProtocol.BattleBuff.Builder.class);
      }

      // Construct using cn.daxiang.protocol.game.BattleProtocol.BattleBuff.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        key_ = "";

        effectId_ = 0;

        value_ = 0L;

        layers_ = 0;

        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return cn.daxiang.protocol.game.BattleProtocol.internal_static_BattleBuff_descriptor;
      }

      public cn.daxiang.protocol.game.BattleProtocol.BattleBuff getDefaultInstanceForType() {
        return cn.daxiang.protocol.game.BattleProtocol.BattleBuff.getDefaultInstance();
      }

      public cn.daxiang.protocol.game.BattleProtocol.BattleBuff build() {
        cn.daxiang.protocol.game.BattleProtocol.BattleBuff result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public cn.daxiang.protocol.game.BattleProtocol.BattleBuff buildPartial() {
        cn.daxiang.protocol.game.BattleProtocol.BattleBuff result = new cn.daxiang.protocol.game.BattleProtocol.BattleBuff(this);
        result.key_ = key_;
        result.effectId_ = effectId_;
        result.value_ = value_;
        result.layers_ = layers_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof cn.daxiang.protocol.game.BattleProtocol.BattleBuff) {
          return mergeFrom((cn.daxiang.protocol.game.BattleProtocol.BattleBuff)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(cn.daxiang.protocol.game.BattleProtocol.BattleBuff other) {
        if (other == cn.daxiang.protocol.game.BattleProtocol.BattleBuff.getDefaultInstance()) return this;
        if (!other.getKey().isEmpty()) {
          key_ = other.key_;
          onChanged();
        }
        if (other.getEffectId() != 0) {
          setEffectId(other.getEffectId());
        }
        if (other.getValue() != 0L) {
          setValue(other.getValue());
        }
        if (other.getLayers() != 0) {
          setLayers(other.getLayers());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        cn.daxiang.protocol.game.BattleProtocol.BattleBuff parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (cn.daxiang.protocol.game.BattleProtocol.BattleBuff) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private java.lang.Object key_ = "";
      /**
       * <pre>
       ** 唯一KEY 
       * </pre>
       *
       * <code>string key = 1;</code>
       */
      public java.lang.String getKey() {
        java.lang.Object ref = key_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          key_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       ** 唯一KEY 
       * </pre>
       *
       * <code>string key = 1;</code>
       */
      public com.google.protobuf.ByteString
          getKeyBytes() {
        java.lang.Object ref = key_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          key_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       ** 唯一KEY 
       * </pre>
       *
       * <code>string key = 1;</code>
       */
      public Builder setKey(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        key_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 唯一KEY 
       * </pre>
       *
       * <code>string key = 1;</code>
       */
      public Builder clearKey() {
        
        key_ = getDefaultInstance().getKey();
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 唯一KEY 
       * </pre>
       *
       * <code>string key = 1;</code>
       */
      public Builder setKeyBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        key_ = value;
        onChanged();
        return this;
      }

      private int effectId_ ;
      /**
       * <pre>
       ** 效果ID 
       * </pre>
       *
       * <code>int32 effectId = 2;</code>
       */
      public int getEffectId() {
        return effectId_;
      }
      /**
       * <pre>
       ** 效果ID 
       * </pre>
       *
       * <code>int32 effectId = 2;</code>
       */
      public Builder setEffectId(int value) {
        
        effectId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 效果ID 
       * </pre>
       *
       * <code>int32 effectId = 2;</code>
       */
      public Builder clearEffectId() {
        
        effectId_ = 0;
        onChanged();
        return this;
      }

      private long value_ ;
      /**
       * <pre>
       ** buff值 
       * </pre>
       *
       * <code>int64 value = 3;</code>
       */
      public long getValue() {
        return value_;
      }
      /**
       * <pre>
       ** buff值 
       * </pre>
       *
       * <code>int64 value = 3;</code>
       */
      public Builder setValue(long value) {
        
        value_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** buff值 
       * </pre>
       *
       * <code>int64 value = 3;</code>
       */
      public Builder clearValue() {
        
        value_ = 0L;
        onChanged();
        return this;
      }

      private int layers_ ;
      /**
       * <pre>
       ** 层数 
       * </pre>
       *
       * <code>int32 layers = 4;</code>
       */
      public int getLayers() {
        return layers_;
      }
      /**
       * <pre>
       ** 层数 
       * </pre>
       *
       * <code>int32 layers = 4;</code>
       */
      public Builder setLayers(int value) {
        
        layers_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 层数 
       * </pre>
       *
       * <code>int32 layers = 4;</code>
       */
      public Builder clearLayers() {
        
        layers_ = 0;
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:BattleBuff)
    }

    // @@protoc_insertion_point(class_scope:BattleBuff)
    private static final cn.daxiang.protocol.game.BattleProtocol.BattleBuff DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new cn.daxiang.protocol.game.BattleProtocol.BattleBuff();
    }

    public static cn.daxiang.protocol.game.BattleProtocol.BattleBuff getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<BattleBuff>
        PARSER = new com.google.protobuf.AbstractParser<BattleBuff>() {
      public BattleBuff parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new BattleBuff(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<BattleBuff> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<BattleBuff> getParserForType() {
      return PARSER;
    }

    public cn.daxiang.protocol.game.BattleProtocol.BattleBuff getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface BattleStatsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:BattleStats)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     ** 胜利阵营 
     * </pre>
     *
     * <code>.BattleCamp winCamp = 1;</code>
     */
    int getWinCampValue();
    /**
     * <pre>
     ** 胜利阵营 
     * </pre>
     *
     * <code>.BattleCamp winCamp = 1;</code>
     */
    cn.daxiang.protocol.game.BattleProtocol.BattleCamp getWinCamp();

    /**
     * <pre>
     ** 星数 
     * </pre>
     *
     * <code>int32 star = 2;</code>
     */
    int getStar();

    /**
     * <pre>
     ** 阵营伤害 key:BattleCamp,value:damage 
     * </pre>
     *
     * <code>map&lt;int32, int64&gt; campDamages = 3;</code>
     */
    int getCampDamagesCount();
    /**
     * <pre>
     ** 阵营伤害 key:BattleCamp,value:damage 
     * </pre>
     *
     * <code>map&lt;int32, int64&gt; campDamages = 3;</code>
     */
    boolean containsCampDamages(
        int key);
    /**
     * Use {@link #getCampDamagesMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Integer, java.lang.Long>
    getCampDamages();
    /**
     * <pre>
     ** 阵营伤害 key:BattleCamp,value:damage 
     * </pre>
     *
     * <code>map&lt;int32, int64&gt; campDamages = 3;</code>
     */
    java.util.Map<java.lang.Integer, java.lang.Long>
    getCampDamagesMap();
    /**
     * <pre>
     ** 阵营伤害 key:BattleCamp,value:damage 
     * </pre>
     *
     * <code>map&lt;int32, int64&gt; campDamages = 3;</code>
     */

    long getCampDamagesOrDefault(
        int key,
        long defaultValue);
    /**
     * <pre>
     ** 阵营伤害 key:BattleCamp,value:damage 
     * </pre>
     *
     * <code>map&lt;int32, int64&gt; campDamages = 3;</code>
     */

    long getCampDamagesOrThrow(
        int key);

    /**
     * <pre>
     ** 精灵伤害 key:spriteId,value:damage
     * </pre>
     *
     * <code>map&lt;int64, int64&gt; spriteDamages = 4;</code>
     */
    int getSpriteDamagesCount();
    /**
     * <pre>
     ** 精灵伤害 key:spriteId,value:damage
     * </pre>
     *
     * <code>map&lt;int64, int64&gt; spriteDamages = 4;</code>
     */
    boolean containsSpriteDamages(
        long key);
    /**
     * Use {@link #getSpriteDamagesMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Long, java.lang.Long>
    getSpriteDamages();
    /**
     * <pre>
     ** 精灵伤害 key:spriteId,value:damage
     * </pre>
     *
     * <code>map&lt;int64, int64&gt; spriteDamages = 4;</code>
     */
    java.util.Map<java.lang.Long, java.lang.Long>
    getSpriteDamagesMap();
    /**
     * <pre>
     ** 精灵伤害 key:spriteId,value:damage
     * </pre>
     *
     * <code>map&lt;int64, int64&gt; spriteDamages = 4;</code>
     */

    long getSpriteDamagesOrDefault(
        long key,
        long defaultValue);
    /**
     * <pre>
     ** 精灵伤害 key:spriteId,value:damage
     * </pre>
     *
     * <code>map&lt;int64, int64&gt; spriteDamages = 4;</code>
     */

    long getSpriteDamagesOrThrow(
        long key);

    /**
     * <pre>
     ** 精灵受到伤害 key:spriteId,value:damage
     * </pre>
     *
     * <code>map&lt;int64, int64&gt; spriteWithstandDamages = 5;</code>
     */
    int getSpriteWithstandDamagesCount();
    /**
     * <pre>
     ** 精灵受到伤害 key:spriteId,value:damage
     * </pre>
     *
     * <code>map&lt;int64, int64&gt; spriteWithstandDamages = 5;</code>
     */
    boolean containsSpriteWithstandDamages(
        long key);
    /**
     * Use {@link #getSpriteWithstandDamagesMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Long, java.lang.Long>
    getSpriteWithstandDamages();
    /**
     * <pre>
     ** 精灵受到伤害 key:spriteId,value:damage
     * </pre>
     *
     * <code>map&lt;int64, int64&gt; spriteWithstandDamages = 5;</code>
     */
    java.util.Map<java.lang.Long, java.lang.Long>
    getSpriteWithstandDamagesMap();
    /**
     * <pre>
     ** 精灵受到伤害 key:spriteId,value:damage
     * </pre>
     *
     * <code>map&lt;int64, int64&gt; spriteWithstandDamages = 5;</code>
     */

    long getSpriteWithstandDamagesOrDefault(
        long key,
        long defaultValue);
    /**
     * <pre>
     ** 精灵受到伤害 key:spriteId,value:damage
     * </pre>
     *
     * <code>map&lt;int64, int64&gt; spriteWithstandDamages = 5;</code>
     */

    long getSpriteWithstandDamagesOrThrow(
        long key);

    /**
     * <pre>
     ** 精灵护盾和治疗 key:spriteId,value:value
     * </pre>
     *
     * <code>map&lt;int64, int64&gt; spriteShieldHeal = 6;</code>
     */
    int getSpriteShieldHealCount();
    /**
     * <pre>
     ** 精灵护盾和治疗 key:spriteId,value:value
     * </pre>
     *
     * <code>map&lt;int64, int64&gt; spriteShieldHeal = 6;</code>
     */
    boolean containsSpriteShieldHeal(
        long key);
    /**
     * Use {@link #getSpriteShieldHealMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Long, java.lang.Long>
    getSpriteShieldHeal();
    /**
     * <pre>
     ** 精灵护盾和治疗 key:spriteId,value:value
     * </pre>
     *
     * <code>map&lt;int64, int64&gt; spriteShieldHeal = 6;</code>
     */
    java.util.Map<java.lang.Long, java.lang.Long>
    getSpriteShieldHealMap();
    /**
     * <pre>
     ** 精灵护盾和治疗 key:spriteId,value:value
     * </pre>
     *
     * <code>map&lt;int64, int64&gt; spriteShieldHeal = 6;</code>
     */

    long getSpriteShieldHealOrDefault(
        long key,
        long defaultValue);
    /**
     * <pre>
     ** 精灵护盾和治疗 key:spriteId,value:value
     * </pre>
     *
     * <code>map&lt;int64, int64&gt; spriteShieldHeal = 6;</code>
     */

    long getSpriteShieldHealOrThrow(
        long key);
  }
  /**
   * <pre>
   ** 战斗统计 
   * </pre>
   *
   * Protobuf type {@code BattleStats}
   */
  public  static final class BattleStats extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:BattleStats)
      BattleStatsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use BattleStats.newBuilder() to construct.
    private BattleStats(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private BattleStats() {
      winCamp_ = 0;
      star_ = 0;
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private BattleStats(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              int rawValue = input.readEnum();

              winCamp_ = rawValue;
              break;
            }
            case 16: {

              star_ = input.readInt32();
              break;
            }
            case 26: {
              if (!((mutable_bitField0_ & 0x00000004) == 0x00000004)) {
                campDamages_ = com.google.protobuf.MapField.newMapField(
                    CampDamagesDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000004;
              }
              com.google.protobuf.MapEntry<java.lang.Integer, java.lang.Long>
              campDamages__ = input.readMessage(
                  CampDamagesDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              campDamages_.getMutableMap().put(
                  campDamages__.getKey(), campDamages__.getValue());
              break;
            }
            case 34: {
              if (!((mutable_bitField0_ & 0x00000008) == 0x00000008)) {
                spriteDamages_ = com.google.protobuf.MapField.newMapField(
                    SpriteDamagesDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000008;
              }
              com.google.protobuf.MapEntry<java.lang.Long, java.lang.Long>
              spriteDamages__ = input.readMessage(
                  SpriteDamagesDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              spriteDamages_.getMutableMap().put(
                  spriteDamages__.getKey(), spriteDamages__.getValue());
              break;
            }
            case 42: {
              if (!((mutable_bitField0_ & 0x00000010) == 0x00000010)) {
                spriteWithstandDamages_ = com.google.protobuf.MapField.newMapField(
                    SpriteWithstandDamagesDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000010;
              }
              com.google.protobuf.MapEntry<java.lang.Long, java.lang.Long>
              spriteWithstandDamages__ = input.readMessage(
                  SpriteWithstandDamagesDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              spriteWithstandDamages_.getMutableMap().put(
                  spriteWithstandDamages__.getKey(), spriteWithstandDamages__.getValue());
              break;
            }
            case 50: {
              if (!((mutable_bitField0_ & 0x00000020) == 0x00000020)) {
                spriteShieldHeal_ = com.google.protobuf.MapField.newMapField(
                    SpriteShieldHealDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000020;
              }
              com.google.protobuf.MapEntry<java.lang.Long, java.lang.Long>
              spriteShieldHeal__ = input.readMessage(
                  SpriteShieldHealDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              spriteShieldHeal_.getMutableMap().put(
                  spriteShieldHeal__.getKey(), spriteShieldHeal__.getValue());
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.BattleProtocol.internal_static_BattleStats_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 3:
          return internalGetCampDamages();
        case 4:
          return internalGetSpriteDamages();
        case 5:
          return internalGetSpriteWithstandDamages();
        case 6:
          return internalGetSpriteShieldHeal();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.daxiang.protocol.game.BattleProtocol.internal_static_BattleStats_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.daxiang.protocol.game.BattleProtocol.BattleStats.class, cn.daxiang.protocol.game.BattleProtocol.BattleStats.Builder.class);
    }

    private int bitField0_;
    public static final int WINCAMP_FIELD_NUMBER = 1;
    private int winCamp_;
    /**
     * <pre>
     ** 胜利阵营 
     * </pre>
     *
     * <code>.BattleCamp winCamp = 1;</code>
     */
    public int getWinCampValue() {
      return winCamp_;
    }
    /**
     * <pre>
     ** 胜利阵营 
     * </pre>
     *
     * <code>.BattleCamp winCamp = 1;</code>
     */
    public cn.daxiang.protocol.game.BattleProtocol.BattleCamp getWinCamp() {
      cn.daxiang.protocol.game.BattleProtocol.BattleCamp result = cn.daxiang.protocol.game.BattleProtocol.BattleCamp.valueOf(winCamp_);
      return result == null ? cn.daxiang.protocol.game.BattleProtocol.BattleCamp.UNRECOGNIZED : result;
    }

    public static final int STAR_FIELD_NUMBER = 2;
    private int star_;
    /**
     * <pre>
     ** 星数 
     * </pre>
     *
     * <code>int32 star = 2;</code>
     */
    public int getStar() {
      return star_;
    }

    public static final int CAMPDAMAGES_FIELD_NUMBER = 3;
    private static final class CampDamagesDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Integer, java.lang.Long> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Integer, java.lang.Long>newDefaultInstance(
                  cn.daxiang.protocol.game.BattleProtocol.internal_static_BattleStats_CampDamagesEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0,
                  com.google.protobuf.WireFormat.FieldType.INT64,
                  0L);
    }
    private com.google.protobuf.MapField<
        java.lang.Integer, java.lang.Long> campDamages_;
    private com.google.protobuf.MapField<java.lang.Integer, java.lang.Long>
    internalGetCampDamages() {
      if (campDamages_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            CampDamagesDefaultEntryHolder.defaultEntry);
      }
      return campDamages_;
    }

    public int getCampDamagesCount() {
      return internalGetCampDamages().getMap().size();
    }
    /**
     * <pre>
     ** 阵营伤害 key:BattleCamp,value:damage 
     * </pre>
     *
     * <code>map&lt;int32, int64&gt; campDamages = 3;</code>
     */

    public boolean containsCampDamages(
        int key) {
      
      return internalGetCampDamages().getMap().containsKey(key);
    }
    /**
     * Use {@link #getCampDamagesMap()} instead.
     */
    @java.lang.Deprecated
    public java.util.Map<java.lang.Integer, java.lang.Long> getCampDamages() {
      return getCampDamagesMap();
    }
    /**
     * <pre>
     ** 阵营伤害 key:BattleCamp,value:damage 
     * </pre>
     *
     * <code>map&lt;int32, int64&gt; campDamages = 3;</code>
     */

    public java.util.Map<java.lang.Integer, java.lang.Long> getCampDamagesMap() {
      return internalGetCampDamages().getMap();
    }
    /**
     * <pre>
     ** 阵营伤害 key:BattleCamp,value:damage 
     * </pre>
     *
     * <code>map&lt;int32, int64&gt; campDamages = 3;</code>
     */

    public long getCampDamagesOrDefault(
        int key,
        long defaultValue) {
      
      java.util.Map<java.lang.Integer, java.lang.Long> map =
          internalGetCampDamages().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <pre>
     ** 阵营伤害 key:BattleCamp,value:damage 
     * </pre>
     *
     * <code>map&lt;int32, int64&gt; campDamages = 3;</code>
     */

    public long getCampDamagesOrThrow(
        int key) {
      
      java.util.Map<java.lang.Integer, java.lang.Long> map =
          internalGetCampDamages().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    public static final int SPRITEDAMAGES_FIELD_NUMBER = 4;
    private static final class SpriteDamagesDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Long, java.lang.Long> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Long, java.lang.Long>newDefaultInstance(
                  cn.daxiang.protocol.game.BattleProtocol.internal_static_BattleStats_SpriteDamagesEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.INT64,
                  0L,
                  com.google.protobuf.WireFormat.FieldType.INT64,
                  0L);
    }
    private com.google.protobuf.MapField<
        java.lang.Long, java.lang.Long> spriteDamages_;
    private com.google.protobuf.MapField<java.lang.Long, java.lang.Long>
    internalGetSpriteDamages() {
      if (spriteDamages_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            SpriteDamagesDefaultEntryHolder.defaultEntry);
      }
      return spriteDamages_;
    }

    public int getSpriteDamagesCount() {
      return internalGetSpriteDamages().getMap().size();
    }
    /**
     * <pre>
     ** 精灵伤害 key:spriteId,value:damage
     * </pre>
     *
     * <code>map&lt;int64, int64&gt; spriteDamages = 4;</code>
     */

    public boolean containsSpriteDamages(
        long key) {
      
      return internalGetSpriteDamages().getMap().containsKey(key);
    }
    /**
     * Use {@link #getSpriteDamagesMap()} instead.
     */
    @java.lang.Deprecated
    public java.util.Map<java.lang.Long, java.lang.Long> getSpriteDamages() {
      return getSpriteDamagesMap();
    }
    /**
     * <pre>
     ** 精灵伤害 key:spriteId,value:damage
     * </pre>
     *
     * <code>map&lt;int64, int64&gt; spriteDamages = 4;</code>
     */

    public java.util.Map<java.lang.Long, java.lang.Long> getSpriteDamagesMap() {
      return internalGetSpriteDamages().getMap();
    }
    /**
     * <pre>
     ** 精灵伤害 key:spriteId,value:damage
     * </pre>
     *
     * <code>map&lt;int64, int64&gt; spriteDamages = 4;</code>
     */

    public long getSpriteDamagesOrDefault(
        long key,
        long defaultValue) {
      
      java.util.Map<java.lang.Long, java.lang.Long> map =
          internalGetSpriteDamages().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <pre>
     ** 精灵伤害 key:spriteId,value:damage
     * </pre>
     *
     * <code>map&lt;int64, int64&gt; spriteDamages = 4;</code>
     */

    public long getSpriteDamagesOrThrow(
        long key) {
      
      java.util.Map<java.lang.Long, java.lang.Long> map =
          internalGetSpriteDamages().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    public static final int SPRITEWITHSTANDDAMAGES_FIELD_NUMBER = 5;
    private static final class SpriteWithstandDamagesDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Long, java.lang.Long> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Long, java.lang.Long>newDefaultInstance(
                  cn.daxiang.protocol.game.BattleProtocol.internal_static_BattleStats_SpriteWithstandDamagesEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.INT64,
                  0L,
                  com.google.protobuf.WireFormat.FieldType.INT64,
                  0L);
    }
    private com.google.protobuf.MapField<
        java.lang.Long, java.lang.Long> spriteWithstandDamages_;
    private com.google.protobuf.MapField<java.lang.Long, java.lang.Long>
    internalGetSpriteWithstandDamages() {
      if (spriteWithstandDamages_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            SpriteWithstandDamagesDefaultEntryHolder.defaultEntry);
      }
      return spriteWithstandDamages_;
    }

    public int getSpriteWithstandDamagesCount() {
      return internalGetSpriteWithstandDamages().getMap().size();
    }
    /**
     * <pre>
     ** 精灵受到伤害 key:spriteId,value:damage
     * </pre>
     *
     * <code>map&lt;int64, int64&gt; spriteWithstandDamages = 5;</code>
     */

    public boolean containsSpriteWithstandDamages(
        long key) {
      
      return internalGetSpriteWithstandDamages().getMap().containsKey(key);
    }
    /**
     * Use {@link #getSpriteWithstandDamagesMap()} instead.
     */
    @java.lang.Deprecated
    public java.util.Map<java.lang.Long, java.lang.Long> getSpriteWithstandDamages() {
      return getSpriteWithstandDamagesMap();
    }
    /**
     * <pre>
     ** 精灵受到伤害 key:spriteId,value:damage
     * </pre>
     *
     * <code>map&lt;int64, int64&gt; spriteWithstandDamages = 5;</code>
     */

    public java.util.Map<java.lang.Long, java.lang.Long> getSpriteWithstandDamagesMap() {
      return internalGetSpriteWithstandDamages().getMap();
    }
    /**
     * <pre>
     ** 精灵受到伤害 key:spriteId,value:damage
     * </pre>
     *
     * <code>map&lt;int64, int64&gt; spriteWithstandDamages = 5;</code>
     */

    public long getSpriteWithstandDamagesOrDefault(
        long key,
        long defaultValue) {
      
      java.util.Map<java.lang.Long, java.lang.Long> map =
          internalGetSpriteWithstandDamages().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <pre>
     ** 精灵受到伤害 key:spriteId,value:damage
     * </pre>
     *
     * <code>map&lt;int64, int64&gt; spriteWithstandDamages = 5;</code>
     */

    public long getSpriteWithstandDamagesOrThrow(
        long key) {
      
      java.util.Map<java.lang.Long, java.lang.Long> map =
          internalGetSpriteWithstandDamages().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    public static final int SPRITESHIELDHEAL_FIELD_NUMBER = 6;
    private static final class SpriteShieldHealDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Long, java.lang.Long> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Long, java.lang.Long>newDefaultInstance(
                  cn.daxiang.protocol.game.BattleProtocol.internal_static_BattleStats_SpriteShieldHealEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.INT64,
                  0L,
                  com.google.protobuf.WireFormat.FieldType.INT64,
                  0L);
    }
    private com.google.protobuf.MapField<
        java.lang.Long, java.lang.Long> spriteShieldHeal_;
    private com.google.protobuf.MapField<java.lang.Long, java.lang.Long>
    internalGetSpriteShieldHeal() {
      if (spriteShieldHeal_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            SpriteShieldHealDefaultEntryHolder.defaultEntry);
      }
      return spriteShieldHeal_;
    }

    public int getSpriteShieldHealCount() {
      return internalGetSpriteShieldHeal().getMap().size();
    }
    /**
     * <pre>
     ** 精灵护盾和治疗 key:spriteId,value:value
     * </pre>
     *
     * <code>map&lt;int64, int64&gt; spriteShieldHeal = 6;</code>
     */

    public boolean containsSpriteShieldHeal(
        long key) {
      
      return internalGetSpriteShieldHeal().getMap().containsKey(key);
    }
    /**
     * Use {@link #getSpriteShieldHealMap()} instead.
     */
    @java.lang.Deprecated
    public java.util.Map<java.lang.Long, java.lang.Long> getSpriteShieldHeal() {
      return getSpriteShieldHealMap();
    }
    /**
     * <pre>
     ** 精灵护盾和治疗 key:spriteId,value:value
     * </pre>
     *
     * <code>map&lt;int64, int64&gt; spriteShieldHeal = 6;</code>
     */

    public java.util.Map<java.lang.Long, java.lang.Long> getSpriteShieldHealMap() {
      return internalGetSpriteShieldHeal().getMap();
    }
    /**
     * <pre>
     ** 精灵护盾和治疗 key:spriteId,value:value
     * </pre>
     *
     * <code>map&lt;int64, int64&gt; spriteShieldHeal = 6;</code>
     */

    public long getSpriteShieldHealOrDefault(
        long key,
        long defaultValue) {
      
      java.util.Map<java.lang.Long, java.lang.Long> map =
          internalGetSpriteShieldHeal().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <pre>
     ** 精灵护盾和治疗 key:spriteId,value:value
     * </pre>
     *
     * <code>map&lt;int64, int64&gt; spriteShieldHeal = 6;</code>
     */

    public long getSpriteShieldHealOrThrow(
        long key) {
      
      java.util.Map<java.lang.Long, java.lang.Long> map =
          internalGetSpriteShieldHeal().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (winCamp_ != cn.daxiang.protocol.game.BattleProtocol.BattleCamp.BATTLE_CAMP_NONE.getNumber()) {
        output.writeEnum(1, winCamp_);
      }
      if (star_ != 0) {
        output.writeInt32(2, star_);
      }
      com.google.protobuf.GeneratedMessageV3
        .serializeIntegerMapTo(
          output,
          internalGetCampDamages(),
          CampDamagesDefaultEntryHolder.defaultEntry,
          3);
      com.google.protobuf.GeneratedMessageV3
        .serializeLongMapTo(
          output,
          internalGetSpriteDamages(),
          SpriteDamagesDefaultEntryHolder.defaultEntry,
          4);
      com.google.protobuf.GeneratedMessageV3
        .serializeLongMapTo(
          output,
          internalGetSpriteWithstandDamages(),
          SpriteWithstandDamagesDefaultEntryHolder.defaultEntry,
          5);
      com.google.protobuf.GeneratedMessageV3
        .serializeLongMapTo(
          output,
          internalGetSpriteShieldHeal(),
          SpriteShieldHealDefaultEntryHolder.defaultEntry,
          6);
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (winCamp_ != cn.daxiang.protocol.game.BattleProtocol.BattleCamp.BATTLE_CAMP_NONE.getNumber()) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, winCamp_);
      }
      if (star_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, star_);
      }
      for (java.util.Map.Entry<java.lang.Integer, java.lang.Long> entry
           : internalGetCampDamages().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Integer, java.lang.Long>
        campDamages__ = CampDamagesDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(3, campDamages__);
      }
      for (java.util.Map.Entry<java.lang.Long, java.lang.Long> entry
           : internalGetSpriteDamages().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Long, java.lang.Long>
        spriteDamages__ = SpriteDamagesDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(4, spriteDamages__);
      }
      for (java.util.Map.Entry<java.lang.Long, java.lang.Long> entry
           : internalGetSpriteWithstandDamages().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Long, java.lang.Long>
        spriteWithstandDamages__ = SpriteWithstandDamagesDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(5, spriteWithstandDamages__);
      }
      for (java.util.Map.Entry<java.lang.Long, java.lang.Long> entry
           : internalGetSpriteShieldHeal().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Long, java.lang.Long>
        spriteShieldHeal__ = SpriteShieldHealDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(6, spriteShieldHeal__);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof cn.daxiang.protocol.game.BattleProtocol.BattleStats)) {
        return super.equals(obj);
      }
      cn.daxiang.protocol.game.BattleProtocol.BattleStats other = (cn.daxiang.protocol.game.BattleProtocol.BattleStats) obj;

      boolean result = true;
      result = result && winCamp_ == other.winCamp_;
      result = result && (getStar()
          == other.getStar());
      result = result && internalGetCampDamages().equals(
          other.internalGetCampDamages());
      result = result && internalGetSpriteDamages().equals(
          other.internalGetSpriteDamages());
      result = result && internalGetSpriteWithstandDamages().equals(
          other.internalGetSpriteWithstandDamages());
      result = result && internalGetSpriteShieldHeal().equals(
          other.internalGetSpriteShieldHeal());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + WINCAMP_FIELD_NUMBER;
      hash = (53 * hash) + winCamp_;
      hash = (37 * hash) + STAR_FIELD_NUMBER;
      hash = (53 * hash) + getStar();
      if (!internalGetCampDamages().getMap().isEmpty()) {
        hash = (37 * hash) + CAMPDAMAGES_FIELD_NUMBER;
        hash = (53 * hash) + internalGetCampDamages().hashCode();
      }
      if (!internalGetSpriteDamages().getMap().isEmpty()) {
        hash = (37 * hash) + SPRITEDAMAGES_FIELD_NUMBER;
        hash = (53 * hash) + internalGetSpriteDamages().hashCode();
      }
      if (!internalGetSpriteWithstandDamages().getMap().isEmpty()) {
        hash = (37 * hash) + SPRITEWITHSTANDDAMAGES_FIELD_NUMBER;
        hash = (53 * hash) + internalGetSpriteWithstandDamages().hashCode();
      }
      if (!internalGetSpriteShieldHeal().getMap().isEmpty()) {
        hash = (37 * hash) + SPRITESHIELDHEAL_FIELD_NUMBER;
        hash = (53 * hash) + internalGetSpriteShieldHeal().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static cn.daxiang.protocol.game.BattleProtocol.BattleStats parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.BattleStats parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.BattleStats parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.BattleStats parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.BattleStats parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.BattleStats parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.BattleStats parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.BattleStats parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.BattleStats parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.BattleStats parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.BattleStats parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BattleProtocol.BattleStats parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(cn.daxiang.protocol.game.BattleProtocol.BattleStats prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** 战斗统计 
     * </pre>
     *
     * Protobuf type {@code BattleStats}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:BattleStats)
        cn.daxiang.protocol.game.BattleProtocol.BattleStatsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.daxiang.protocol.game.BattleProtocol.internal_static_BattleStats_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 3:
            return internalGetCampDamages();
          case 4:
            return internalGetSpriteDamages();
          case 5:
            return internalGetSpriteWithstandDamages();
          case 6:
            return internalGetSpriteShieldHeal();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 3:
            return internalGetMutableCampDamages();
          case 4:
            return internalGetMutableSpriteDamages();
          case 5:
            return internalGetMutableSpriteWithstandDamages();
          case 6:
            return internalGetMutableSpriteShieldHeal();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.daxiang.protocol.game.BattleProtocol.internal_static_BattleStats_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.daxiang.protocol.game.BattleProtocol.BattleStats.class, cn.daxiang.protocol.game.BattleProtocol.BattleStats.Builder.class);
      }

      // Construct using cn.daxiang.protocol.game.BattleProtocol.BattleStats.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        winCamp_ = 0;

        star_ = 0;

        internalGetMutableCampDamages().clear();
        internalGetMutableSpriteDamages().clear();
        internalGetMutableSpriteWithstandDamages().clear();
        internalGetMutableSpriteShieldHeal().clear();
        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return cn.daxiang.protocol.game.BattleProtocol.internal_static_BattleStats_descriptor;
      }

      public cn.daxiang.protocol.game.BattleProtocol.BattleStats getDefaultInstanceForType() {
        return cn.daxiang.protocol.game.BattleProtocol.BattleStats.getDefaultInstance();
      }

      public cn.daxiang.protocol.game.BattleProtocol.BattleStats build() {
        cn.daxiang.protocol.game.BattleProtocol.BattleStats result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public cn.daxiang.protocol.game.BattleProtocol.BattleStats buildPartial() {
        cn.daxiang.protocol.game.BattleProtocol.BattleStats result = new cn.daxiang.protocol.game.BattleProtocol.BattleStats(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        result.winCamp_ = winCamp_;
        result.star_ = star_;
        result.campDamages_ = internalGetCampDamages();
        result.campDamages_.makeImmutable();
        result.spriteDamages_ = internalGetSpriteDamages();
        result.spriteDamages_.makeImmutable();
        result.spriteWithstandDamages_ = internalGetSpriteWithstandDamages();
        result.spriteWithstandDamages_.makeImmutable();
        result.spriteShieldHeal_ = internalGetSpriteShieldHeal();
        result.spriteShieldHeal_.makeImmutable();
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof cn.daxiang.protocol.game.BattleProtocol.BattleStats) {
          return mergeFrom((cn.daxiang.protocol.game.BattleProtocol.BattleStats)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(cn.daxiang.protocol.game.BattleProtocol.BattleStats other) {
        if (other == cn.daxiang.protocol.game.BattleProtocol.BattleStats.getDefaultInstance()) return this;
        if (other.winCamp_ != 0) {
          setWinCampValue(other.getWinCampValue());
        }
        if (other.getStar() != 0) {
          setStar(other.getStar());
        }
        internalGetMutableCampDamages().mergeFrom(
            other.internalGetCampDamages());
        internalGetMutableSpriteDamages().mergeFrom(
            other.internalGetSpriteDamages());
        internalGetMutableSpriteWithstandDamages().mergeFrom(
            other.internalGetSpriteWithstandDamages());
        internalGetMutableSpriteShieldHeal().mergeFrom(
            other.internalGetSpriteShieldHeal());
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        cn.daxiang.protocol.game.BattleProtocol.BattleStats parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (cn.daxiang.protocol.game.BattleProtocol.BattleStats) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int winCamp_ = 0;
      /**
       * <pre>
       ** 胜利阵营 
       * </pre>
       *
       * <code>.BattleCamp winCamp = 1;</code>
       */
      public int getWinCampValue() {
        return winCamp_;
      }
      /**
       * <pre>
       ** 胜利阵营 
       * </pre>
       *
       * <code>.BattleCamp winCamp = 1;</code>
       */
      public Builder setWinCampValue(int value) {
        winCamp_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 胜利阵营 
       * </pre>
       *
       * <code>.BattleCamp winCamp = 1;</code>
       */
      public cn.daxiang.protocol.game.BattleProtocol.BattleCamp getWinCamp() {
        cn.daxiang.protocol.game.BattleProtocol.BattleCamp result = cn.daxiang.protocol.game.BattleProtocol.BattleCamp.valueOf(winCamp_);
        return result == null ? cn.daxiang.protocol.game.BattleProtocol.BattleCamp.UNRECOGNIZED : result;
      }
      /**
       * <pre>
       ** 胜利阵营 
       * </pre>
       *
       * <code>.BattleCamp winCamp = 1;</code>
       */
      public Builder setWinCamp(cn.daxiang.protocol.game.BattleProtocol.BattleCamp value) {
        if (value == null) {
          throw new NullPointerException();
        }
        
        winCamp_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 胜利阵营 
       * </pre>
       *
       * <code>.BattleCamp winCamp = 1;</code>
       */
      public Builder clearWinCamp() {
        
        winCamp_ = 0;
        onChanged();
        return this;
      }

      private int star_ ;
      /**
       * <pre>
       ** 星数 
       * </pre>
       *
       * <code>int32 star = 2;</code>
       */
      public int getStar() {
        return star_;
      }
      /**
       * <pre>
       ** 星数 
       * </pre>
       *
       * <code>int32 star = 2;</code>
       */
      public Builder setStar(int value) {
        
        star_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 星数 
       * </pre>
       *
       * <code>int32 star = 2;</code>
       */
      public Builder clearStar() {
        
        star_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.MapField<
          java.lang.Integer, java.lang.Long> campDamages_;
      private com.google.protobuf.MapField<java.lang.Integer, java.lang.Long>
      internalGetCampDamages() {
        if (campDamages_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              CampDamagesDefaultEntryHolder.defaultEntry);
        }
        return campDamages_;
      }
      private com.google.protobuf.MapField<java.lang.Integer, java.lang.Long>
      internalGetMutableCampDamages() {
        onChanged();;
        if (campDamages_ == null) {
          campDamages_ = com.google.protobuf.MapField.newMapField(
              CampDamagesDefaultEntryHolder.defaultEntry);
        }
        if (!campDamages_.isMutable()) {
          campDamages_ = campDamages_.copy();
        }
        return campDamages_;
      }

      public int getCampDamagesCount() {
        return internalGetCampDamages().getMap().size();
      }
      /**
       * <pre>
       ** 阵营伤害 key:BattleCamp,value:damage 
       * </pre>
       *
       * <code>map&lt;int32, int64&gt; campDamages = 3;</code>
       */

      public boolean containsCampDamages(
          int key) {
        
        return internalGetCampDamages().getMap().containsKey(key);
      }
      /**
       * Use {@link #getCampDamagesMap()} instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, java.lang.Long> getCampDamages() {
        return getCampDamagesMap();
      }
      /**
       * <pre>
       ** 阵营伤害 key:BattleCamp,value:damage 
       * </pre>
       *
       * <code>map&lt;int32, int64&gt; campDamages = 3;</code>
       */

      public java.util.Map<java.lang.Integer, java.lang.Long> getCampDamagesMap() {
        return internalGetCampDamages().getMap();
      }
      /**
       * <pre>
       ** 阵营伤害 key:BattleCamp,value:damage 
       * </pre>
       *
       * <code>map&lt;int32, int64&gt; campDamages = 3;</code>
       */

      public long getCampDamagesOrDefault(
          int key,
          long defaultValue) {
        
        java.util.Map<java.lang.Integer, java.lang.Long> map =
            internalGetCampDamages().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <pre>
       ** 阵营伤害 key:BattleCamp,value:damage 
       * </pre>
       *
       * <code>map&lt;int32, int64&gt; campDamages = 3;</code>
       */

      public long getCampDamagesOrThrow(
          int key) {
        
        java.util.Map<java.lang.Integer, java.lang.Long> map =
            internalGetCampDamages().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearCampDamages() {
        internalGetMutableCampDamages().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <pre>
       ** 阵营伤害 key:BattleCamp,value:damage 
       * </pre>
       *
       * <code>map&lt;int32, int64&gt; campDamages = 3;</code>
       */

      public Builder removeCampDamages(
          int key) {
        
        internalGetMutableCampDamages().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, java.lang.Long>
      getMutableCampDamages() {
        return internalGetMutableCampDamages().getMutableMap();
      }
      /**
       * <pre>
       ** 阵营伤害 key:BattleCamp,value:damage 
       * </pre>
       *
       * <code>map&lt;int32, int64&gt; campDamages = 3;</code>
       */
      public Builder putCampDamages(
          int key,
          long value) {
        
        
        internalGetMutableCampDamages().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <pre>
       ** 阵营伤害 key:BattleCamp,value:damage 
       * </pre>
       *
       * <code>map&lt;int32, int64&gt; campDamages = 3;</code>
       */

      public Builder putAllCampDamages(
          java.util.Map<java.lang.Integer, java.lang.Long> values) {
        internalGetMutableCampDamages().getMutableMap()
            .putAll(values);
        return this;
      }

      private com.google.protobuf.MapField<
          java.lang.Long, java.lang.Long> spriteDamages_;
      private com.google.protobuf.MapField<java.lang.Long, java.lang.Long>
      internalGetSpriteDamages() {
        if (spriteDamages_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              SpriteDamagesDefaultEntryHolder.defaultEntry);
        }
        return spriteDamages_;
      }
      private com.google.protobuf.MapField<java.lang.Long, java.lang.Long>
      internalGetMutableSpriteDamages() {
        onChanged();;
        if (spriteDamages_ == null) {
          spriteDamages_ = com.google.protobuf.MapField.newMapField(
              SpriteDamagesDefaultEntryHolder.defaultEntry);
        }
        if (!spriteDamages_.isMutable()) {
          spriteDamages_ = spriteDamages_.copy();
        }
        return spriteDamages_;
      }

      public int getSpriteDamagesCount() {
        return internalGetSpriteDamages().getMap().size();
      }
      /**
       * <pre>
       ** 精灵伤害 key:spriteId,value:damage
       * </pre>
       *
       * <code>map&lt;int64, int64&gt; spriteDamages = 4;</code>
       */

      public boolean containsSpriteDamages(
          long key) {
        
        return internalGetSpriteDamages().getMap().containsKey(key);
      }
      /**
       * Use {@link #getSpriteDamagesMap()} instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Long, java.lang.Long> getSpriteDamages() {
        return getSpriteDamagesMap();
      }
      /**
       * <pre>
       ** 精灵伤害 key:spriteId,value:damage
       * </pre>
       *
       * <code>map&lt;int64, int64&gt; spriteDamages = 4;</code>
       */

      public java.util.Map<java.lang.Long, java.lang.Long> getSpriteDamagesMap() {
        return internalGetSpriteDamages().getMap();
      }
      /**
       * <pre>
       ** 精灵伤害 key:spriteId,value:damage
       * </pre>
       *
       * <code>map&lt;int64, int64&gt; spriteDamages = 4;</code>
       */

      public long getSpriteDamagesOrDefault(
          long key,
          long defaultValue) {
        
        java.util.Map<java.lang.Long, java.lang.Long> map =
            internalGetSpriteDamages().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <pre>
       ** 精灵伤害 key:spriteId,value:damage
       * </pre>
       *
       * <code>map&lt;int64, int64&gt; spriteDamages = 4;</code>
       */

      public long getSpriteDamagesOrThrow(
          long key) {
        
        java.util.Map<java.lang.Long, java.lang.Long> map =
            internalGetSpriteDamages().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearSpriteDamages() {
        internalGetMutableSpriteDamages().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <pre>
       ** 精灵伤害 key:spriteId,value:damage
       * </pre>
       *
       * <code>map&lt;int64, int64&gt; spriteDamages = 4;</code>
       */

      public Builder removeSpriteDamages(
          long key) {
        
        internalGetMutableSpriteDamages().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Long, java.lang.Long>
      getMutableSpriteDamages() {
        return internalGetMutableSpriteDamages().getMutableMap();
      }
      /**
       * <pre>
       ** 精灵伤害 key:spriteId,value:damage
       * </pre>
       *
       * <code>map&lt;int64, int64&gt; spriteDamages = 4;</code>
       */
      public Builder putSpriteDamages(
          long key,
          long value) {
        
        
        internalGetMutableSpriteDamages().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <pre>
       ** 精灵伤害 key:spriteId,value:damage
       * </pre>
       *
       * <code>map&lt;int64, int64&gt; spriteDamages = 4;</code>
       */

      public Builder putAllSpriteDamages(
          java.util.Map<java.lang.Long, java.lang.Long> values) {
        internalGetMutableSpriteDamages().getMutableMap()
            .putAll(values);
        return this;
      }

      private com.google.protobuf.MapField<
          java.lang.Long, java.lang.Long> spriteWithstandDamages_;
      private com.google.protobuf.MapField<java.lang.Long, java.lang.Long>
      internalGetSpriteWithstandDamages() {
        if (spriteWithstandDamages_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              SpriteWithstandDamagesDefaultEntryHolder.defaultEntry);
        }
        return spriteWithstandDamages_;
      }
      private com.google.protobuf.MapField<java.lang.Long, java.lang.Long>
      internalGetMutableSpriteWithstandDamages() {
        onChanged();;
        if (spriteWithstandDamages_ == null) {
          spriteWithstandDamages_ = com.google.protobuf.MapField.newMapField(
              SpriteWithstandDamagesDefaultEntryHolder.defaultEntry);
        }
        if (!spriteWithstandDamages_.isMutable()) {
          spriteWithstandDamages_ = spriteWithstandDamages_.copy();
        }
        return spriteWithstandDamages_;
      }

      public int getSpriteWithstandDamagesCount() {
        return internalGetSpriteWithstandDamages().getMap().size();
      }
      /**
       * <pre>
       ** 精灵受到伤害 key:spriteId,value:damage
       * </pre>
       *
       * <code>map&lt;int64, int64&gt; spriteWithstandDamages = 5;</code>
       */

      public boolean containsSpriteWithstandDamages(
          long key) {
        
        return internalGetSpriteWithstandDamages().getMap().containsKey(key);
      }
      /**
       * Use {@link #getSpriteWithstandDamagesMap()} instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Long, java.lang.Long> getSpriteWithstandDamages() {
        return getSpriteWithstandDamagesMap();
      }
      /**
       * <pre>
       ** 精灵受到伤害 key:spriteId,value:damage
       * </pre>
       *
       * <code>map&lt;int64, int64&gt; spriteWithstandDamages = 5;</code>
       */

      public java.util.Map<java.lang.Long, java.lang.Long> getSpriteWithstandDamagesMap() {
        return internalGetSpriteWithstandDamages().getMap();
      }
      /**
       * <pre>
       ** 精灵受到伤害 key:spriteId,value:damage
       * </pre>
       *
       * <code>map&lt;int64, int64&gt; spriteWithstandDamages = 5;</code>
       */

      public long getSpriteWithstandDamagesOrDefault(
          long key,
          long defaultValue) {
        
        java.util.Map<java.lang.Long, java.lang.Long> map =
            internalGetSpriteWithstandDamages().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <pre>
       ** 精灵受到伤害 key:spriteId,value:damage
       * </pre>
       *
       * <code>map&lt;int64, int64&gt; spriteWithstandDamages = 5;</code>
       */

      public long getSpriteWithstandDamagesOrThrow(
          long key) {
        
        java.util.Map<java.lang.Long, java.lang.Long> map =
            internalGetSpriteWithstandDamages().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearSpriteWithstandDamages() {
        internalGetMutableSpriteWithstandDamages().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <pre>
       ** 精灵受到伤害 key:spriteId,value:damage
       * </pre>
       *
       * <code>map&lt;int64, int64&gt; spriteWithstandDamages = 5;</code>
       */

      public Builder removeSpriteWithstandDamages(
          long key) {
        
        internalGetMutableSpriteWithstandDamages().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Long, java.lang.Long>
      getMutableSpriteWithstandDamages() {
        return internalGetMutableSpriteWithstandDamages().getMutableMap();
      }
      /**
       * <pre>
       ** 精灵受到伤害 key:spriteId,value:damage
       * </pre>
       *
       * <code>map&lt;int64, int64&gt; spriteWithstandDamages = 5;</code>
       */
      public Builder putSpriteWithstandDamages(
          long key,
          long value) {
        
        
        internalGetMutableSpriteWithstandDamages().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <pre>
       ** 精灵受到伤害 key:spriteId,value:damage
       * </pre>
       *
       * <code>map&lt;int64, int64&gt; spriteWithstandDamages = 5;</code>
       */

      public Builder putAllSpriteWithstandDamages(
          java.util.Map<java.lang.Long, java.lang.Long> values) {
        internalGetMutableSpriteWithstandDamages().getMutableMap()
            .putAll(values);
        return this;
      }

      private com.google.protobuf.MapField<
          java.lang.Long, java.lang.Long> spriteShieldHeal_;
      private com.google.protobuf.MapField<java.lang.Long, java.lang.Long>
      internalGetSpriteShieldHeal() {
        if (spriteShieldHeal_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              SpriteShieldHealDefaultEntryHolder.defaultEntry);
        }
        return spriteShieldHeal_;
      }
      private com.google.protobuf.MapField<java.lang.Long, java.lang.Long>
      internalGetMutableSpriteShieldHeal() {
        onChanged();;
        if (spriteShieldHeal_ == null) {
          spriteShieldHeal_ = com.google.protobuf.MapField.newMapField(
              SpriteShieldHealDefaultEntryHolder.defaultEntry);
        }
        if (!spriteShieldHeal_.isMutable()) {
          spriteShieldHeal_ = spriteShieldHeal_.copy();
        }
        return spriteShieldHeal_;
      }

      public int getSpriteShieldHealCount() {
        return internalGetSpriteShieldHeal().getMap().size();
      }
      /**
       * <pre>
       ** 精灵护盾和治疗 key:spriteId,value:value
       * </pre>
       *
       * <code>map&lt;int64, int64&gt; spriteShieldHeal = 6;</code>
       */

      public boolean containsSpriteShieldHeal(
          long key) {
        
        return internalGetSpriteShieldHeal().getMap().containsKey(key);
      }
      /**
       * Use {@link #getSpriteShieldHealMap()} instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Long, java.lang.Long> getSpriteShieldHeal() {
        return getSpriteShieldHealMap();
      }
      /**
       * <pre>
       ** 精灵护盾和治疗 key:spriteId,value:value
       * </pre>
       *
       * <code>map&lt;int64, int64&gt; spriteShieldHeal = 6;</code>
       */

      public java.util.Map<java.lang.Long, java.lang.Long> getSpriteShieldHealMap() {
        return internalGetSpriteShieldHeal().getMap();
      }
      /**
       * <pre>
       ** 精灵护盾和治疗 key:spriteId,value:value
       * </pre>
       *
       * <code>map&lt;int64, int64&gt; spriteShieldHeal = 6;</code>
       */

      public long getSpriteShieldHealOrDefault(
          long key,
          long defaultValue) {
        
        java.util.Map<java.lang.Long, java.lang.Long> map =
            internalGetSpriteShieldHeal().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <pre>
       ** 精灵护盾和治疗 key:spriteId,value:value
       * </pre>
       *
       * <code>map&lt;int64, int64&gt; spriteShieldHeal = 6;</code>
       */

      public long getSpriteShieldHealOrThrow(
          long key) {
        
        java.util.Map<java.lang.Long, java.lang.Long> map =
            internalGetSpriteShieldHeal().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearSpriteShieldHeal() {
        internalGetMutableSpriteShieldHeal().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <pre>
       ** 精灵护盾和治疗 key:spriteId,value:value
       * </pre>
       *
       * <code>map&lt;int64, int64&gt; spriteShieldHeal = 6;</code>
       */

      public Builder removeSpriteShieldHeal(
          long key) {
        
        internalGetMutableSpriteShieldHeal().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Long, java.lang.Long>
      getMutableSpriteShieldHeal() {
        return internalGetMutableSpriteShieldHeal().getMutableMap();
      }
      /**
       * <pre>
       ** 精灵护盾和治疗 key:spriteId,value:value
       * </pre>
       *
       * <code>map&lt;int64, int64&gt; spriteShieldHeal = 6;</code>
       */
      public Builder putSpriteShieldHeal(
          long key,
          long value) {
        
        
        internalGetMutableSpriteShieldHeal().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <pre>
       ** 精灵护盾和治疗 key:spriteId,value:value
       * </pre>
       *
       * <code>map&lt;int64, int64&gt; spriteShieldHeal = 6;</code>
       */

      public Builder putAllSpriteShieldHeal(
          java.util.Map<java.lang.Long, java.lang.Long> values) {
        internalGetMutableSpriteShieldHeal().getMutableMap()
            .putAll(values);
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:BattleStats)
    }

    // @@protoc_insertion_point(class_scope:BattleStats)
    private static final cn.daxiang.protocol.game.BattleProtocol.BattleStats DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new cn.daxiang.protocol.game.BattleProtocol.BattleStats();
    }

    public static cn.daxiang.protocol.game.BattleProtocol.BattleStats getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<BattleStats>
        PARSER = new com.google.protobuf.AbstractParser<BattleStats>() {
      public BattleStats parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new BattleStats(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<BattleStats> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<BattleStats> getParserForType() {
      return PARSER;
    }

    public cn.daxiang.protocol.game.BattleProtocol.BattleStats getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_BattleCompeteRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_BattleCompeteRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_GetBattleReplayRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_GetBattleReplayRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_BattleResultResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_BattleResultResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_BattleResultResponse_ReportsEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_BattleResultResponse_ReportsEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_FightReportList_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_FightReportList_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_BattleSprite_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_BattleSprite_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_BattleSprite_AttributeEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_BattleSprite_AttributeEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_BattleMember_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_BattleMember_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_FightReport_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_FightReport_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_FightEffect_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_FightEffect_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_BattleBuff_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_BattleBuff_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_BattleStats_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_BattleStats_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_BattleStats_CampDamagesEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_BattleStats_CampDamagesEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_BattleStats_SpriteDamagesEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_BattleStats_SpriteDamagesEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_BattleStats_SpriteWithstandDamagesEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_BattleStats_SpriteWithstandDamagesEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_BattleStats_SpriteShieldHealEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_BattleStats_SpriteShieldHealEntry_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\031game/battleProtocol.proto\032\031game/common" +
      "Protocol.proto\"9\n\024BattleCompeteRequest\022\020" +
      "\n\010serverId\030\001 \001(\005\022\017\n\007actorId\030\002 \001(\003\"0\n\026Get" +
      "BattleReplayRequest\022\026\n\016battleReplayId\030\001 " +
      "\001(\003\"\331\002\n\024BattleResultResponse\022\031\n\004type\030\001 \001" +
      "(\0162\013.BattleType\022\036\n\007members\030\002 \003(\0132\r.Battl" +
      "eMember\0223\n\007reports\030\003 \003(\0132\".BattleResultR" +
      "esponse.ReportsEntry\022\035\n\006reward\030\004 \001(\0132\r.R" +
      "ewardResult\022!\n\013battleStats\030\005 \001(\0132\014.Battl" +
      "eStats\022)\n\022rightHistorySprite\030\006 \003(\0132\r.Bat" +
      "tleSprite\022\"\n\013extraReward\030\007 \001(\0132\r.RewardR" +
      "esult\032@\n\014ReportsEntry\022\013\n\003key\030\001 \001(\005\022\037\n\005va" +
      "lue\030\002 \001(\0132\020.FightReportList:\0028\001\"4\n\017Fight" +
      "ReportList\022!\n\013fightReport\030\001 \003(\0132\014.FightR" +
      "eport\"q\n\014BattleSprite\022/\n\tattribute\030\001 \003(\013" +
      "2\034.BattleSprite.AttributeEntry\0320\n\016Attrib" +
      "uteEntry\022\013\n\003key\030\001 \001(\005\022\r\n\005value\030\002 \001(\t:\0028\001" +
      "\"n\n\014BattleMember\022#\n\014actorProfile\030\001 \001(\0132\r" +
      ".ActorProfile\022\036\n\007sprites\030\002 \003(\0132\r.BattleS" +
      "prite\022\031\n\004camp\030\003 \001(\0162\013.BattleCamp\"v\n\013Figh" +
      "tReport\022\023\n\013attackerUid\030\001 \001(\003\022\017\n\007skillId\030" +
      "\002 \001(\005\022\037\n\ndamageType\030\003 \001(\0162\013.DamageType\022 " +
      "\n\neffectList\030\004 \003(\0132\014.FightEffect\"\262\001\n\013Fig" +
      "htEffect\022\023\n\013targeterUid\030\001 \001(\003\022\020\n\010effectI" +
      "d\030\002 \001(\005\022#\n\014battleSprite\030\003 \001(\0132\r.BattleSp" +
      "rite\022\020\n\010critical\030\004 \001(\010\022\033\n\010hitState\030\005 \001(\016" +
      "2\t.HitState\022\031\n\004buff\030\006 \001(\0132\013.BattleBuff\022\r" +
      "\n\005block\030\007 \001(\010\"J\n\nBattleBuff\022\013\n\003key\030\001 \001(\t" +
      "\022\020\n\010effectId\030\002 \001(\005\022\r\n\005value\030\003 \001(\003\022\016\n\006lay" +
      "ers\030\004 \001(\005\"\217\004\n\013BattleStats\022\034\n\007winCamp\030\001 \001" +
      "(\0162\013.BattleCamp\022\014\n\004star\030\002 \001(\005\0222\n\013campDam" +
      "ages\030\003 \003(\0132\035.BattleStats.CampDamagesEntr" +
      "y\0226\n\rspriteDamages\030\004 \003(\0132\037.BattleStats.S" +
      "priteDamagesEntry\022H\n\026spriteWithstandDama" +
      "ges\030\005 \003(\0132(.BattleStats.SpriteWithstandD" +
      "amagesEntry\022<\n\020spriteShieldHeal\030\006 \003(\0132\"." +
      "BattleStats.SpriteShieldHealEntry\0322\n\020Cam" +
      "pDamagesEntry\022\013\n\003key\030\001 \001(\005\022\r\n\005value\030\002 \001(" +
      "\003:\0028\001\0324\n\022SpriteDamagesEntry\022\013\n\003key\030\001 \001(\003" +
      "\022\r\n\005value\030\002 \001(\003:\0028\001\032=\n\033SpriteWithstandDa" +
      "magesEntry\022\013\n\003key\030\001 \001(\003\022\r\n\005value\030\002 \001(\003:\002" +
      "8\001\0327\n\025SpriteShieldHealEntry\022\013\n\003key\030\001 \001(\003" +
      "\022\r\n\005value\030\002 \001(\003:\0028\001*\213\001\n\tBattleCmd\022\023\n\017BAT" +
      "TLE_CMD_NONE\020\000\022\017\n\013BATTLE_TEST\020\001\022\022\n\016BATTL" +
      "E_COMPETE\020\002\022\025\n\021GET_BATTLE_REPLAY\020\003\022\025\n\021BA" +
      "TTLE_SKILL_TEST\020\004\022\026\n\022PUSH_BATTLE_RESULT\020" +
      "d*A\n\nBattleCamp\022\024\n\020BATTLE_CAMP_NONE\020\000\022\r\n" +
      "\tLEFT_CAMP\020\001\022\016\n\nRIGHT_CAMP\020\002*L\n\016SpriteCa" +
      "mpType\022\031\n\025SPRITE_CAMP_TYPE_NONE\020\000\022\t\n\005ANG" +
      "EL\020\001\022\t\n\005DEVIL\020\002\022\t\n\005ELVES\020\003*e\n\nSpriteType" +
      "\022\024\n\020SPRITE_TYPE_NONE\020\000\022\017\n\013SPRITE_HERO\020\001\022" +
      "\022\n\016SPRITE_MONSTER\020\002\022\020\n\014SPRITE_BEAST\020\003\022\n\n" +
      "\006SYSTEM\020\004*\213\003\n\nBattleType\022\024\n\020BATTLE_TYPE_" +
      "NONE\020\000\022\020\n\014BATTLE_ARENA\020\001\022\020\n\014BATTLE_TOWER" +
      "\020\002\022\022\n\016BATTLE_ARSENAL\020\003\022\017\n\013CAVE_SINGLE\020\004\022" +
      "\025\n\021CAVE_GROUP_NORMAL\020\005\022\023\n\017CAVE_GROUP_HAR" +
      "D\020\006\022\013\n\007COMPETE\020\007\022\022\n\016NATION_DUNGEON\020\010\022\013\n\007" +
      "CAPTURE\020\t\022\r\n\tCROSS_CUP\020\n\022\014\n\010HEGEMONY\020\013\022\017" +
      "\n\013NATION_BOSS\020\014\022\025\n\021NATION_BOSS_ACTOR\020\r\022\n" +
      "\n\006ESCORT\020\016\022\t\n\005CHESS\020\017\022\r\n\tSANCTUARY\020\020\022\022\n\016" +
      "SANCTUARY_BOSS\020\021\022\017\n\013IMPERIALISM\020\022\022\020\n\014GVG" +
      "_BRAWLING\020\023\022\021\n\rREINCARNATION\020\024\022\017\n\nSKILL_" +
      "TEST\020\350\007*;\n\nDamageType\022\024\n\020DAMAGE_TYPE_NON" +
      "E\020\000\022\014\n\010PHYSICAL\020\001\022\t\n\005SPELL\020\002*\352\002\n\010HitStat" +
      "e\022\022\n\016HIT_STATE_NONE\020\000\022\007\n\003HIT\020\001\022\010\n\004MISS\020\002" +
      "\022\n\n\006IMMUNE\020\003\022\017\n\013REMOVE_BUFF\020\004\022\017\n\013FORBID_" +
      "HEAL\020\005\022\010\n\004BUFF\020\006\022\022\n\016SHARING_DAMAGE\020\007\022\025\n\021" +
      "SUBSTITUTE_DAMAGE\020\010\022\020\n\014DAMAGE_LIMIT\020\t\022\024\n" +
      "\020DAMAGE_REDUCTION\020\n\022\021\n\rDAMAGE_EXEMPT\020\013\022\020" +
      "\n\014FATAL_STRIKE\020\014\022\017\n\013HOLY_STRIKE\020\r\022\020\n\014UNP" +
      "ARALLELED\020\016\022\026\n\022CONFLAGRATION_BUFF\020\017\022\026\n\022C" +
      "OUNTER_SHOCK_BUFF\020\020\022\r\n\tCONFUSION\020\021\022\020\n\014CR" +
      "ITICAL_ONE\020\022\022\023\n\017TRANSFER_DAMAGE\020\023*\357\002\n\tSp" +
      "riteKey\022\023\n\017SPRITE_KEY_NONE\020\000\022\016\n\nSPRITE_U" +
      "ID\020\001\022\023\n\017SPRITE_ACTOR_ID\020\002\022\024\n\020SPRITE_CONF" +
      "IG_ID\020\003\022\022\n\016BATTLE_CAMP_ID\020\004\022\014\n\010POSITION\020" +
      "\005\022\017\n\013SPRITE_TYPE\020\006\022\006\n\002HP\020\036\022\n\n\006HP_MAX\020 \022\010" +
      "\n\004RAGE\020#\022\014\n\010RAGE_MAX\020$\022\n\n\006SHIELD\020d\022\014\n\010FI" +
      "NAL_HP\020e\022\r\n\tHERO_CAMP\020f\022\025\n\021IMMORTALS_SKI" +
      "N_ID\020g\022\010\n\004STAR\020h\022\027\n\023SPACETIME_BEAUTY_ID\020" +
      "i\022\020\n\014HERO_SKIN_ID\020j\022\024\n\020HERO_COLOR_LEVEL\020" +
      "k\022\031\n\025IMMORTALS_COLOR_LEVEL\020l\022\r\n\tMOUNT_CI" +
      "D\020mB\034\n\030cn.daxiang.protocol.gameH\001b\006proto" +
      "3"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
        new com.google.protobuf.Descriptors.FileDescriptor.    InternalDescriptorAssigner() {
          public com.google.protobuf.ExtensionRegistry assignDescriptors(
              com.google.protobuf.Descriptors.FileDescriptor root) {
            descriptor = root;
            return null;
          }
        };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          cn.daxiang.protocol.game.CommonProtocol.getDescriptor(),
        }, assigner);
    internal_static_BattleCompeteRequest_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_BattleCompeteRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_BattleCompeteRequest_descriptor,
        new java.lang.String[] { "ServerId", "ActorId", });
    internal_static_GetBattleReplayRequest_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_GetBattleReplayRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_GetBattleReplayRequest_descriptor,
        new java.lang.String[] { "BattleReplayId", });
    internal_static_BattleResultResponse_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_BattleResultResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_BattleResultResponse_descriptor,
        new java.lang.String[] { "Type", "Members", "Reports", "Reward", "BattleStats", "RightHistorySprite", "ExtraReward", });
    internal_static_BattleResultResponse_ReportsEntry_descriptor =
      internal_static_BattleResultResponse_descriptor.getNestedTypes().get(0);
    internal_static_BattleResultResponse_ReportsEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_BattleResultResponse_ReportsEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_FightReportList_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_FightReportList_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_FightReportList_descriptor,
        new java.lang.String[] { "FightReport", });
    internal_static_BattleSprite_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_BattleSprite_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_BattleSprite_descriptor,
        new java.lang.String[] { "Attribute", });
    internal_static_BattleSprite_AttributeEntry_descriptor =
      internal_static_BattleSprite_descriptor.getNestedTypes().get(0);
    internal_static_BattleSprite_AttributeEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_BattleSprite_AttributeEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_BattleMember_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_BattleMember_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_BattleMember_descriptor,
        new java.lang.String[] { "ActorProfile", "Sprites", "Camp", });
    internal_static_FightReport_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_FightReport_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_FightReport_descriptor,
        new java.lang.String[] { "AttackerUid", "SkillId", "DamageType", "EffectList", });
    internal_static_FightEffect_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_FightEffect_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_FightEffect_descriptor,
        new java.lang.String[] { "TargeterUid", "EffectId", "BattleSprite", "Critical", "HitState", "Buff", "Block", });
    internal_static_BattleBuff_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_BattleBuff_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_BattleBuff_descriptor,
        new java.lang.String[] { "Key", "EffectId", "Value", "Layers", });
    internal_static_BattleStats_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_BattleStats_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_BattleStats_descriptor,
        new java.lang.String[] { "WinCamp", "Star", "CampDamages", "SpriteDamages", "SpriteWithstandDamages", "SpriteShieldHeal", });
    internal_static_BattleStats_CampDamagesEntry_descriptor =
      internal_static_BattleStats_descriptor.getNestedTypes().get(0);
    internal_static_BattleStats_CampDamagesEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_BattleStats_CampDamagesEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_BattleStats_SpriteDamagesEntry_descriptor =
      internal_static_BattleStats_descriptor.getNestedTypes().get(1);
    internal_static_BattleStats_SpriteDamagesEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_BattleStats_SpriteDamagesEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_BattleStats_SpriteWithstandDamagesEntry_descriptor =
      internal_static_BattleStats_descriptor.getNestedTypes().get(2);
    internal_static_BattleStats_SpriteWithstandDamagesEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_BattleStats_SpriteWithstandDamagesEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_BattleStats_SpriteShieldHealEntry_descriptor =
      internal_static_BattleStats_descriptor.getNestedTypes().get(3);
    internal_static_BattleStats_SpriteShieldHealEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_BattleStats_SpriteShieldHealEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    cn.daxiang.protocol.game.CommonProtocol.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
