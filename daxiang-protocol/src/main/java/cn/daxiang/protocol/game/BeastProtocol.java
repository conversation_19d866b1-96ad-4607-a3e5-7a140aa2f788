// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: game/beastProtocol.proto

package cn.daxiang.protocol.game;

public final class BeastProtocol {
  private BeastProtocol() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  /**
   * <pre>
   ** 神兽 
   * </pre>
   *
   * Protobuf enum {@code BeastCmd}
   */
  public enum BeastCmd
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>BEAST_CMD_NONE = 0;</code>
     */
    BEAST_CMD_NONE(0),
    /**
     * <pre>
     **
     * 获取神兽背包信息
     * 请求:{&#64;code Request}
     * 响应:{&#64;code BeastBagInfoResponse}
     * </pre>
     *
     * <code>GET_BEAST_BAG_INFO = 1;</code>
     */
    GET_BEAST_BAG_INFO(1),
    /**
     * <pre>
     **
     * 神兽合成
     * 请求:{&#64;code IntPacket} -神兽配置Id
     * 响应:{&#64;code RewardResult}
     * 推送:{&#64;code BeastBagInfoResponse}
     * </pre>
     *
     * <code>BEAST_ACTIVATE = 2;</code>
     */
    BEAST_ACTIVATE(2),
    /**
     * <pre>
     **
     * 神兽升级
     * 请求:{&#64;code BeastAddExpRequest}
     * 响应:{&#64;code BeastBagInfoResponse}
     * </pre>
     *
     * <code>BEAST_LEVE_UP = 3;</code>
     */
    BEAST_LEVE_UP(3),
    /**
     * <pre>
     **
     * 神兽升星
     * 请求:{&#64;code IntPacket} -神兽配置Id
     * 响应:{&#64;code BeastBagInfoResponse}
     * </pre>
     *
     * <code>BEAST_STAR_UP = 4;</code>
     */
    BEAST_STAR_UP(4),
    /**
     * <pre>
     **
     * 获取神兽图鉴
     * 请求:{&#64;code Request}
     * 响应:{&#64;code BeastManualResponse}
     * </pre>
     *
     * <code>GET_BEAST_MANUAL_INFO = 5;</code>
     */
    GET_BEAST_MANUAL_INFO(5),
    /**
     * <pre>
     **
     * 神兽图鉴激活
     * 请求:{&#64;code IntPacket} -神兽配置Id
     * 响应:{&#64;code BeastManualResponse}
     * </pre>
     *
     * <code>BEAST_MANUAL_ACTIVATE = 6;</code>
     */
    BEAST_MANUAL_ACTIVATE(6),
    /**
     * <pre>
     **
     * 神兽图鉴阶段奖励领取
     * 请求:{&#64;code IntPacket} -配置Id
     * 响应:{&#64;code RewardResult}
     * 推送:{&#64;code BeastManualResponse}
     * </pre>
     *
     * <code>BEAST_MANUAL_RECEIVE_STAGE_REWARD = 7;</code>
     */
    BEAST_MANUAL_RECEIVE_STAGE_REWARD(7),
    /**
     * <pre>
     **
     * 神兽图鉴共鸣奖励领取
     * 请求:{&#64;code IntPacket} -配置Id
     * 响应:{&#64;code RewardResult}
     * 推送:{&#64;code BeastManualResponse}
     * </pre>
     *
     * <code>BEAST_MANUAL_RECEIVE_RESONANCE_REWARD = 8;</code>
     */
    BEAST_MANUAL_RECEIVE_RESONANCE_REWARD(8),
    /**
     * <pre>
     **
     * 获取神兽召唤信息
     * 请求:{&#64;code Request}
     * 响应:{&#64;code BeastGachaInfoResponse}
     * </pre>
     *
     * <code>GET_BEAST_GACHA_INFO = 9;</code>
     */
    GET_BEAST_GACHA_INFO(9),
    /**
     * <pre>
     **
     * 神兽召唤奖池更换
     * 请求:{&#64;code IntPacket} -配置Id
     * 响应:{&#64;code BeastGachaInfoResponse}
     * </pre>
     *
     * <code>BEAST_GACHA_CHANGE_REWARD_POOL = 10;</code>
     */
    BEAST_GACHA_CHANGE_REWARD_POOL(10),
    /**
     * <pre>
     **
     * 神兽召唤
     * 请求:{&#64;code IntPacket} -次数
     * 响应:{&#64;code BeastGachaResultResponse}
     * 推送:{&#64;code BeastGachaInfoResponse}
     * </pre>
     *
     * <code>BEAST_GACHA = 11;</code>
     */
    BEAST_GACHA(11),
    /**
     * <pre>
     **
     * 获取神兽召唤中奖播报
     * 请求:{&#64;code Request}
     * 响应:{&#64;code BeastGachaReportResponse}
     * </pre>
     *
     * <code>GET_BEAST_GACHA_REPORT_INFO = 12;</code>
     */
    GET_BEAST_GACHA_REPORT_INFO(12),
    /**
     * <pre>
     **
     * 领取召唤回馈奖励
     * 请求:{&#64;code IntPacket} -配置Id
     * 响应:{&#64;code RewardResult}
     * 推送:{&#64;code BeastGachaInfoResponse}
     * </pre>
     *
     * <code>RECEIVE_FEEDBACK_REWARD = 13;</code>
     */
    RECEIVE_FEEDBACK_REWARD(13),
    /**
     * <pre>
     **
     * 领取特权奖励
     * 请求:{&#64;code IntPacket} -特权类型
     * 响应:{&#64;code RewardResult}
     * 推送:{&#64;code BeastGachaInfoResponse}
     * </pre>
     *
     * <code>RECEIVE_BEAST_GACHA_PRIVILEGE_REWARD = 14;</code>
     */
    RECEIVE_BEAST_GACHA_PRIVILEGE_REWARD(14),
    /**
     * <pre>
     **
     * 领取充值礼包
     * 请求:{&#64;code IntPacket} -配置Id
     * 响应:{&#64;code RewardResult}
     * 推送:{&#64;code BeastGachaInfoResponse}
     * </pre>
     *
     * <code>RECEIVE_BEAST_GACHA_CHARGE_REWARD = 15;</code>
     */
    RECEIVE_BEAST_GACHA_CHARGE_REWARD(15),
    /**
     * <pre>
     **
     * 获取神兽图腾信息
     * 请求:{&#64;code Request}
     * 响应:{&#64;code BeastTotemInfoResponse}
     * </pre>
     *
     * <code>GET_BEAST_TOTEM_INFO = 16;</code>
     */
    GET_BEAST_TOTEM_INFO(16),
    /**
     * <pre>
     **
     * 激活/升级神兽图腾部位
     * 请求:{&#64;code KeyValuePacket} -key:图腾类型 value:部位
     * 响应:{&#64;code BeastTotemInfoResponse}
     * </pre>
     *
     * <code>BEAST_TOTEM_LEVEL_UP = 17;</code>
     */
    BEAST_TOTEM_LEVEL_UP(17),
    /**
     * <pre>
     **
     * 神兽图腾进阶
     * 请求:{&#64;code IntPacket} -图腾类型
     * 响应:{&#64;code BeastTotemInfoResponse}
     * </pre>
     *
     * <code>BEAST_TOTEM_ADVANCED = 18;</code>
     */
    BEAST_TOTEM_ADVANCED(18),
    /**
     * <pre>
     **
     * 一键升级神兽图腾
     * 请求:{&#64;code IntPacket} -图腾类型
     * 响应:{&#64;code BeastTotemInfoResponse}
     * </pre>
     *
     * <code>BEAST_TOTEM_QUICKLY_LEVEL_UP = 19;</code>
     */
    BEAST_TOTEM_QUICKLY_LEVEL_UP(19),
    /**
     * <pre>
     **
     * 推送神兽背包信息
     * 推送:{&#64;code BeastBagInfoResponse}
     * </pre>
     *
     * <code>PUSH_BEAST_BAG_INFO = 101;</code>
     */
    PUSH_BEAST_BAG_INFO(101),
    /**
     * <pre>
     **
     * 推送神兽图鉴信息
     * 推送:{&#64;code BeastManualResponse}
     * </pre>
     *
     * <code>PUSH_BEAST_MANUAL_INFO = 102;</code>
     */
    PUSH_BEAST_MANUAL_INFO(102),
    /**
     * <pre>
     **
     * 推送神兽召唤信息
     * 推送:{&#64;code BeastGachaInfoResponse}
     * </pre>
     *
     * <code>PUSH_BEAST_GACHA_INFO = 103;</code>
     */
    PUSH_BEAST_GACHA_INFO(103),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>BEAST_CMD_NONE = 0;</code>
     */
    public static final int BEAST_CMD_NONE_VALUE = 0;
    /**
     * <pre>
     **
     * 获取神兽背包信息
     * 请求:{&#64;code Request}
     * 响应:{&#64;code BeastBagInfoResponse}
     * </pre>
     *
     * <code>GET_BEAST_BAG_INFO = 1;</code>
     */
    public static final int GET_BEAST_BAG_INFO_VALUE = 1;
    /**
     * <pre>
     **
     * 神兽合成
     * 请求:{&#64;code IntPacket} -神兽配置Id
     * 响应:{&#64;code RewardResult}
     * 推送:{&#64;code BeastBagInfoResponse}
     * </pre>
     *
     * <code>BEAST_ACTIVATE = 2;</code>
     */
    public static final int BEAST_ACTIVATE_VALUE = 2;
    /**
     * <pre>
     **
     * 神兽升级
     * 请求:{&#64;code BeastAddExpRequest}
     * 响应:{&#64;code BeastBagInfoResponse}
     * </pre>
     *
     * <code>BEAST_LEVE_UP = 3;</code>
     */
    public static final int BEAST_LEVE_UP_VALUE = 3;
    /**
     * <pre>
     **
     * 神兽升星
     * 请求:{&#64;code IntPacket} -神兽配置Id
     * 响应:{&#64;code BeastBagInfoResponse}
     * </pre>
     *
     * <code>BEAST_STAR_UP = 4;</code>
     */
    public static final int BEAST_STAR_UP_VALUE = 4;
    /**
     * <pre>
     **
     * 获取神兽图鉴
     * 请求:{&#64;code Request}
     * 响应:{&#64;code BeastManualResponse}
     * </pre>
     *
     * <code>GET_BEAST_MANUAL_INFO = 5;</code>
     */
    public static final int GET_BEAST_MANUAL_INFO_VALUE = 5;
    /**
     * <pre>
     **
     * 神兽图鉴激活
     * 请求:{&#64;code IntPacket} -神兽配置Id
     * 响应:{&#64;code BeastManualResponse}
     * </pre>
     *
     * <code>BEAST_MANUAL_ACTIVATE = 6;</code>
     */
    public static final int BEAST_MANUAL_ACTIVATE_VALUE = 6;
    /**
     * <pre>
     **
     * 神兽图鉴阶段奖励领取
     * 请求:{&#64;code IntPacket} -配置Id
     * 响应:{&#64;code RewardResult}
     * 推送:{&#64;code BeastManualResponse}
     * </pre>
     *
     * <code>BEAST_MANUAL_RECEIVE_STAGE_REWARD = 7;</code>
     */
    public static final int BEAST_MANUAL_RECEIVE_STAGE_REWARD_VALUE = 7;
    /**
     * <pre>
     **
     * 神兽图鉴共鸣奖励领取
     * 请求:{&#64;code IntPacket} -配置Id
     * 响应:{&#64;code RewardResult}
     * 推送:{&#64;code BeastManualResponse}
     * </pre>
     *
     * <code>BEAST_MANUAL_RECEIVE_RESONANCE_REWARD = 8;</code>
     */
    public static final int BEAST_MANUAL_RECEIVE_RESONANCE_REWARD_VALUE = 8;
    /**
     * <pre>
     **
     * 获取神兽召唤信息
     * 请求:{&#64;code Request}
     * 响应:{&#64;code BeastGachaInfoResponse}
     * </pre>
     *
     * <code>GET_BEAST_GACHA_INFO = 9;</code>
     */
    public static final int GET_BEAST_GACHA_INFO_VALUE = 9;
    /**
     * <pre>
     **
     * 神兽召唤奖池更换
     * 请求:{&#64;code IntPacket} -配置Id
     * 响应:{&#64;code BeastGachaInfoResponse}
     * </pre>
     *
     * <code>BEAST_GACHA_CHANGE_REWARD_POOL = 10;</code>
     */
    public static final int BEAST_GACHA_CHANGE_REWARD_POOL_VALUE = 10;
    /**
     * <pre>
     **
     * 神兽召唤
     * 请求:{&#64;code IntPacket} -次数
     * 响应:{&#64;code BeastGachaResultResponse}
     * 推送:{&#64;code BeastGachaInfoResponse}
     * </pre>
     *
     * <code>BEAST_GACHA = 11;</code>
     */
    public static final int BEAST_GACHA_VALUE = 11;
    /**
     * <pre>
     **
     * 获取神兽召唤中奖播报
     * 请求:{&#64;code Request}
     * 响应:{&#64;code BeastGachaReportResponse}
     * </pre>
     *
     * <code>GET_BEAST_GACHA_REPORT_INFO = 12;</code>
     */
    public static final int GET_BEAST_GACHA_REPORT_INFO_VALUE = 12;
    /**
     * <pre>
     **
     * 领取召唤回馈奖励
     * 请求:{&#64;code IntPacket} -配置Id
     * 响应:{&#64;code RewardResult}
     * 推送:{&#64;code BeastGachaInfoResponse}
     * </pre>
     *
     * <code>RECEIVE_FEEDBACK_REWARD = 13;</code>
     */
    public static final int RECEIVE_FEEDBACK_REWARD_VALUE = 13;
    /**
     * <pre>
     **
     * 领取特权奖励
     * 请求:{&#64;code IntPacket} -特权类型
     * 响应:{&#64;code RewardResult}
     * 推送:{&#64;code BeastGachaInfoResponse}
     * </pre>
     *
     * <code>RECEIVE_BEAST_GACHA_PRIVILEGE_REWARD = 14;</code>
     */
    public static final int RECEIVE_BEAST_GACHA_PRIVILEGE_REWARD_VALUE = 14;
    /**
     * <pre>
     **
     * 领取充值礼包
     * 请求:{&#64;code IntPacket} -配置Id
     * 响应:{&#64;code RewardResult}
     * 推送:{&#64;code BeastGachaInfoResponse}
     * </pre>
     *
     * <code>RECEIVE_BEAST_GACHA_CHARGE_REWARD = 15;</code>
     */
    public static final int RECEIVE_BEAST_GACHA_CHARGE_REWARD_VALUE = 15;
    /**
     * <pre>
     **
     * 获取神兽图腾信息
     * 请求:{&#64;code Request}
     * 响应:{&#64;code BeastTotemInfoResponse}
     * </pre>
     *
     * <code>GET_BEAST_TOTEM_INFO = 16;</code>
     */
    public static final int GET_BEAST_TOTEM_INFO_VALUE = 16;
    /**
     * <pre>
     **
     * 激活/升级神兽图腾部位
     * 请求:{&#64;code KeyValuePacket} -key:图腾类型 value:部位
     * 响应:{&#64;code BeastTotemInfoResponse}
     * </pre>
     *
     * <code>BEAST_TOTEM_LEVEL_UP = 17;</code>
     */
    public static final int BEAST_TOTEM_LEVEL_UP_VALUE = 17;
    /**
     * <pre>
     **
     * 神兽图腾进阶
     * 请求:{&#64;code IntPacket} -图腾类型
     * 响应:{&#64;code BeastTotemInfoResponse}
     * </pre>
     *
     * <code>BEAST_TOTEM_ADVANCED = 18;</code>
     */
    public static final int BEAST_TOTEM_ADVANCED_VALUE = 18;
    /**
     * <pre>
     **
     * 一键升级神兽图腾
     * 请求:{&#64;code IntPacket} -图腾类型
     * 响应:{&#64;code BeastTotemInfoResponse}
     * </pre>
     *
     * <code>BEAST_TOTEM_QUICKLY_LEVEL_UP = 19;</code>
     */
    public static final int BEAST_TOTEM_QUICKLY_LEVEL_UP_VALUE = 19;
    /**
     * <pre>
     **
     * 推送神兽背包信息
     * 推送:{&#64;code BeastBagInfoResponse}
     * </pre>
     *
     * <code>PUSH_BEAST_BAG_INFO = 101;</code>
     */
    public static final int PUSH_BEAST_BAG_INFO_VALUE = 101;
    /**
     * <pre>
     **
     * 推送神兽图鉴信息
     * 推送:{&#64;code BeastManualResponse}
     * </pre>
     *
     * <code>PUSH_BEAST_MANUAL_INFO = 102;</code>
     */
    public static final int PUSH_BEAST_MANUAL_INFO_VALUE = 102;
    /**
     * <pre>
     **
     * 推送神兽召唤信息
     * 推送:{&#64;code BeastGachaInfoResponse}
     * </pre>
     *
     * <code>PUSH_BEAST_GACHA_INFO = 103;</code>
     */
    public static final int PUSH_BEAST_GACHA_INFO_VALUE = 103;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static BeastCmd valueOf(int value) {
      return forNumber(value);
    }

    public static BeastCmd forNumber(int value) {
      switch (value) {
        case 0: return BEAST_CMD_NONE;
        case 1: return GET_BEAST_BAG_INFO;
        case 2: return BEAST_ACTIVATE;
        case 3: return BEAST_LEVE_UP;
        case 4: return BEAST_STAR_UP;
        case 5: return GET_BEAST_MANUAL_INFO;
        case 6: return BEAST_MANUAL_ACTIVATE;
        case 7: return BEAST_MANUAL_RECEIVE_STAGE_REWARD;
        case 8: return BEAST_MANUAL_RECEIVE_RESONANCE_REWARD;
        case 9: return GET_BEAST_GACHA_INFO;
        case 10: return BEAST_GACHA_CHANGE_REWARD_POOL;
        case 11: return BEAST_GACHA;
        case 12: return GET_BEAST_GACHA_REPORT_INFO;
        case 13: return RECEIVE_FEEDBACK_REWARD;
        case 14: return RECEIVE_BEAST_GACHA_PRIVILEGE_REWARD;
        case 15: return RECEIVE_BEAST_GACHA_CHARGE_REWARD;
        case 16: return GET_BEAST_TOTEM_INFO;
        case 17: return BEAST_TOTEM_LEVEL_UP;
        case 18: return BEAST_TOTEM_ADVANCED;
        case 19: return BEAST_TOTEM_QUICKLY_LEVEL_UP;
        case 101: return PUSH_BEAST_BAG_INFO;
        case 102: return PUSH_BEAST_MANUAL_INFO;
        case 103: return PUSH_BEAST_GACHA_INFO;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<BeastCmd>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        BeastCmd> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<BeastCmd>() {
            public BeastCmd findValueByNumber(int number) {
              return BeastCmd.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.BeastProtocol.getDescriptor().getEnumTypes().get(0);
    }

    private static final BeastCmd[] VALUES = values();

    public static BeastCmd valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private BeastCmd(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:BeastCmd)
  }

  public interface BeastsInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:BeastsInfo)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     ** 神兽配置Id 
     * </pre>
     *
     * <code>int32 configId = 1;</code>
     */
    int getConfigId();

    /**
     * <pre>
     ** 神兽等级 
     * </pre>
     *
     * <code>int32 level = 2;</code>
     */
    int getLevel();

    /**
     * <pre>
     ** 神兽星级 
     * </pre>
     *
     * <code>int32 star = 3;</code>
     */
    int getStar();

    /**
     * <pre>
     ** 神兽等级经验 当前值
     * </pre>
     *
     * <code>int32 exp = 4;</code>
     */
    int getExp();
  }
  /**
   * <pre>
   ** 神兽信息 
   * </pre>
   *
   * Protobuf type {@code BeastsInfo}
   */
  public  static final class BeastsInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:BeastsInfo)
      BeastsInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use BeastsInfo.newBuilder() to construct.
    private BeastsInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private BeastsInfo() {
      configId_ = 0;
      level_ = 0;
      star_ = 0;
      exp_ = 0;
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private BeastsInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {

              configId_ = input.readInt32();
              break;
            }
            case 16: {

              level_ = input.readInt32();
              break;
            }
            case 24: {

              star_ = input.readInt32();
              break;
            }
            case 32: {

              exp_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.BeastProtocol.internal_static_BeastsInfo_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.daxiang.protocol.game.BeastProtocol.internal_static_BeastsInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.daxiang.protocol.game.BeastProtocol.BeastsInfo.class, cn.daxiang.protocol.game.BeastProtocol.BeastsInfo.Builder.class);
    }

    public static final int CONFIGID_FIELD_NUMBER = 1;
    private int configId_;
    /**
     * <pre>
     ** 神兽配置Id 
     * </pre>
     *
     * <code>int32 configId = 1;</code>
     */
    public int getConfigId() {
      return configId_;
    }

    public static final int LEVEL_FIELD_NUMBER = 2;
    private int level_;
    /**
     * <pre>
     ** 神兽等级 
     * </pre>
     *
     * <code>int32 level = 2;</code>
     */
    public int getLevel() {
      return level_;
    }

    public static final int STAR_FIELD_NUMBER = 3;
    private int star_;
    /**
     * <pre>
     ** 神兽星级 
     * </pre>
     *
     * <code>int32 star = 3;</code>
     */
    public int getStar() {
      return star_;
    }

    public static final int EXP_FIELD_NUMBER = 4;
    private int exp_;
    /**
     * <pre>
     ** 神兽等级经验 当前值
     * </pre>
     *
     * <code>int32 exp = 4;</code>
     */
    public int getExp() {
      return exp_;
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (configId_ != 0) {
        output.writeInt32(1, configId_);
      }
      if (level_ != 0) {
        output.writeInt32(2, level_);
      }
      if (star_ != 0) {
        output.writeInt32(3, star_);
      }
      if (exp_ != 0) {
        output.writeInt32(4, exp_);
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (configId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, configId_);
      }
      if (level_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, level_);
      }
      if (star_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, star_);
      }
      if (exp_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, exp_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof cn.daxiang.protocol.game.BeastProtocol.BeastsInfo)) {
        return super.equals(obj);
      }
      cn.daxiang.protocol.game.BeastProtocol.BeastsInfo other = (cn.daxiang.protocol.game.BeastProtocol.BeastsInfo) obj;

      boolean result = true;
      result = result && (getConfigId()
          == other.getConfigId());
      result = result && (getLevel()
          == other.getLevel());
      result = result && (getStar()
          == other.getStar());
      result = result && (getExp()
          == other.getExp());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CONFIGID_FIELD_NUMBER;
      hash = (53 * hash) + getConfigId();
      hash = (37 * hash) + LEVEL_FIELD_NUMBER;
      hash = (53 * hash) + getLevel();
      hash = (37 * hash) + STAR_FIELD_NUMBER;
      hash = (53 * hash) + getStar();
      hash = (37 * hash) + EXP_FIELD_NUMBER;
      hash = (53 * hash) + getExp();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static cn.daxiang.protocol.game.BeastProtocol.BeastsInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastsInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastsInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastsInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastsInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastsInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastsInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastsInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastsInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastsInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastsInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastsInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(cn.daxiang.protocol.game.BeastProtocol.BeastsInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** 神兽信息 
     * </pre>
     *
     * Protobuf type {@code BeastsInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:BeastsInfo)
        cn.daxiang.protocol.game.BeastProtocol.BeastsInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.daxiang.protocol.game.BeastProtocol.internal_static_BeastsInfo_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.daxiang.protocol.game.BeastProtocol.internal_static_BeastsInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.daxiang.protocol.game.BeastProtocol.BeastsInfo.class, cn.daxiang.protocol.game.BeastProtocol.BeastsInfo.Builder.class);
      }

      // Construct using cn.daxiang.protocol.game.BeastProtocol.BeastsInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        configId_ = 0;

        level_ = 0;

        star_ = 0;

        exp_ = 0;

        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return cn.daxiang.protocol.game.BeastProtocol.internal_static_BeastsInfo_descriptor;
      }

      public cn.daxiang.protocol.game.BeastProtocol.BeastsInfo getDefaultInstanceForType() {
        return cn.daxiang.protocol.game.BeastProtocol.BeastsInfo.getDefaultInstance();
      }

      public cn.daxiang.protocol.game.BeastProtocol.BeastsInfo build() {
        cn.daxiang.protocol.game.BeastProtocol.BeastsInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public cn.daxiang.protocol.game.BeastProtocol.BeastsInfo buildPartial() {
        cn.daxiang.protocol.game.BeastProtocol.BeastsInfo result = new cn.daxiang.protocol.game.BeastProtocol.BeastsInfo(this);
        result.configId_ = configId_;
        result.level_ = level_;
        result.star_ = star_;
        result.exp_ = exp_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof cn.daxiang.protocol.game.BeastProtocol.BeastsInfo) {
          return mergeFrom((cn.daxiang.protocol.game.BeastProtocol.BeastsInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(cn.daxiang.protocol.game.BeastProtocol.BeastsInfo other) {
        if (other == cn.daxiang.protocol.game.BeastProtocol.BeastsInfo.getDefaultInstance()) return this;
        if (other.getConfigId() != 0) {
          setConfigId(other.getConfigId());
        }
        if (other.getLevel() != 0) {
          setLevel(other.getLevel());
        }
        if (other.getStar() != 0) {
          setStar(other.getStar());
        }
        if (other.getExp() != 0) {
          setExp(other.getExp());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        cn.daxiang.protocol.game.BeastProtocol.BeastsInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (cn.daxiang.protocol.game.BeastProtocol.BeastsInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int configId_ ;
      /**
       * <pre>
       ** 神兽配置Id 
       * </pre>
       *
       * <code>int32 configId = 1;</code>
       */
      public int getConfigId() {
        return configId_;
      }
      /**
       * <pre>
       ** 神兽配置Id 
       * </pre>
       *
       * <code>int32 configId = 1;</code>
       */
      public Builder setConfigId(int value) {
        
        configId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 神兽配置Id 
       * </pre>
       *
       * <code>int32 configId = 1;</code>
       */
      public Builder clearConfigId() {
        
        configId_ = 0;
        onChanged();
        return this;
      }

      private int level_ ;
      /**
       * <pre>
       ** 神兽等级 
       * </pre>
       *
       * <code>int32 level = 2;</code>
       */
      public int getLevel() {
        return level_;
      }
      /**
       * <pre>
       ** 神兽等级 
       * </pre>
       *
       * <code>int32 level = 2;</code>
       */
      public Builder setLevel(int value) {
        
        level_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 神兽等级 
       * </pre>
       *
       * <code>int32 level = 2;</code>
       */
      public Builder clearLevel() {
        
        level_ = 0;
        onChanged();
        return this;
      }

      private int star_ ;
      /**
       * <pre>
       ** 神兽星级 
       * </pre>
       *
       * <code>int32 star = 3;</code>
       */
      public int getStar() {
        return star_;
      }
      /**
       * <pre>
       ** 神兽星级 
       * </pre>
       *
       * <code>int32 star = 3;</code>
       */
      public Builder setStar(int value) {
        
        star_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 神兽星级 
       * </pre>
       *
       * <code>int32 star = 3;</code>
       */
      public Builder clearStar() {
        
        star_ = 0;
        onChanged();
        return this;
      }

      private int exp_ ;
      /**
       * <pre>
       ** 神兽等级经验 当前值
       * </pre>
       *
       * <code>int32 exp = 4;</code>
       */
      public int getExp() {
        return exp_;
      }
      /**
       * <pre>
       ** 神兽等级经验 当前值
       * </pre>
       *
       * <code>int32 exp = 4;</code>
       */
      public Builder setExp(int value) {
        
        exp_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 神兽等级经验 当前值
       * </pre>
       *
       * <code>int32 exp = 4;</code>
       */
      public Builder clearExp() {
        
        exp_ = 0;
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:BeastsInfo)
    }

    // @@protoc_insertion_point(class_scope:BeastsInfo)
    private static final cn.daxiang.protocol.game.BeastProtocol.BeastsInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new cn.daxiang.protocol.game.BeastProtocol.BeastsInfo();
    }

    public static cn.daxiang.protocol.game.BeastProtocol.BeastsInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<BeastsInfo>
        PARSER = new com.google.protobuf.AbstractParser<BeastsInfo>() {
      public BeastsInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new BeastsInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<BeastsInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<BeastsInfo> getParserForType() {
      return PARSER;
    }

    public cn.daxiang.protocol.game.BeastProtocol.BeastsInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface BeastBagInfoResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:BeastBagInfoResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .BeastsInfo infos = 1;</code>
     */
    java.util.List<cn.daxiang.protocol.game.BeastProtocol.BeastsInfo> 
        getInfosList();
    /**
     * <code>repeated .BeastsInfo infos = 1;</code>
     */
    cn.daxiang.protocol.game.BeastProtocol.BeastsInfo getInfos(int index);
    /**
     * <code>repeated .BeastsInfo infos = 1;</code>
     */
    int getInfosCount();
    /**
     * <code>repeated .BeastsInfo infos = 1;</code>
     */
    java.util.List<? extends cn.daxiang.protocol.game.BeastProtocol.BeastsInfoOrBuilder> 
        getInfosOrBuilderList();
    /**
     * <code>repeated .BeastsInfo infos = 1;</code>
     */
    cn.daxiang.protocol.game.BeastProtocol.BeastsInfoOrBuilder getInfosOrBuilder(
        int index);
  }
  /**
   * <pre>
   ** 神兽背包信息响应 
   * </pre>
   *
   * Protobuf type {@code BeastBagInfoResponse}
   */
  public  static final class BeastBagInfoResponse extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:BeastBagInfoResponse)
      BeastBagInfoResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use BeastBagInfoResponse.newBuilder() to construct.
    private BeastBagInfoResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private BeastBagInfoResponse() {
      infos_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private BeastBagInfoResponse(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
                infos_ = new java.util.ArrayList<cn.daxiang.protocol.game.BeastProtocol.BeastsInfo>();
                mutable_bitField0_ |= 0x00000001;
              }
              infos_.add(
                  input.readMessage(cn.daxiang.protocol.game.BeastProtocol.BeastsInfo.parser(), extensionRegistry));
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
          infos_ = java.util.Collections.unmodifiableList(infos_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.BeastProtocol.internal_static_BeastBagInfoResponse_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.daxiang.protocol.game.BeastProtocol.internal_static_BeastBagInfoResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.daxiang.protocol.game.BeastProtocol.BeastBagInfoResponse.class, cn.daxiang.protocol.game.BeastProtocol.BeastBagInfoResponse.Builder.class);
    }

    public static final int INFOS_FIELD_NUMBER = 1;
    private java.util.List<cn.daxiang.protocol.game.BeastProtocol.BeastsInfo> infos_;
    /**
     * <code>repeated .BeastsInfo infos = 1;</code>
     */
    public java.util.List<cn.daxiang.protocol.game.BeastProtocol.BeastsInfo> getInfosList() {
      return infos_;
    }
    /**
     * <code>repeated .BeastsInfo infos = 1;</code>
     */
    public java.util.List<? extends cn.daxiang.protocol.game.BeastProtocol.BeastsInfoOrBuilder> 
        getInfosOrBuilderList() {
      return infos_;
    }
    /**
     * <code>repeated .BeastsInfo infos = 1;</code>
     */
    public int getInfosCount() {
      return infos_.size();
    }
    /**
     * <code>repeated .BeastsInfo infos = 1;</code>
     */
    public cn.daxiang.protocol.game.BeastProtocol.BeastsInfo getInfos(int index) {
      return infos_.get(index);
    }
    /**
     * <code>repeated .BeastsInfo infos = 1;</code>
     */
    public cn.daxiang.protocol.game.BeastProtocol.BeastsInfoOrBuilder getInfosOrBuilder(
        int index) {
      return infos_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < infos_.size(); i++) {
        output.writeMessage(1, infos_.get(i));
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < infos_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, infos_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof cn.daxiang.protocol.game.BeastProtocol.BeastBagInfoResponse)) {
        return super.equals(obj);
      }
      cn.daxiang.protocol.game.BeastProtocol.BeastBagInfoResponse other = (cn.daxiang.protocol.game.BeastProtocol.BeastBagInfoResponse) obj;

      boolean result = true;
      result = result && getInfosList()
          .equals(other.getInfosList());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getInfosCount() > 0) {
        hash = (37 * hash) + INFOS_FIELD_NUMBER;
        hash = (53 * hash) + getInfosList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static cn.daxiang.protocol.game.BeastProtocol.BeastBagInfoResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastBagInfoResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastBagInfoResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastBagInfoResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastBagInfoResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastBagInfoResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastBagInfoResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastBagInfoResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastBagInfoResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastBagInfoResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastBagInfoResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastBagInfoResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(cn.daxiang.protocol.game.BeastProtocol.BeastBagInfoResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** 神兽背包信息响应 
     * </pre>
     *
     * Protobuf type {@code BeastBagInfoResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:BeastBagInfoResponse)
        cn.daxiang.protocol.game.BeastProtocol.BeastBagInfoResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.daxiang.protocol.game.BeastProtocol.internal_static_BeastBagInfoResponse_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.daxiang.protocol.game.BeastProtocol.internal_static_BeastBagInfoResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.daxiang.protocol.game.BeastProtocol.BeastBagInfoResponse.class, cn.daxiang.protocol.game.BeastProtocol.BeastBagInfoResponse.Builder.class);
      }

      // Construct using cn.daxiang.protocol.game.BeastProtocol.BeastBagInfoResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getInfosFieldBuilder();
        }
      }
      public Builder clear() {
        super.clear();
        if (infosBuilder_ == null) {
          infos_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          infosBuilder_.clear();
        }
        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return cn.daxiang.protocol.game.BeastProtocol.internal_static_BeastBagInfoResponse_descriptor;
      }

      public cn.daxiang.protocol.game.BeastProtocol.BeastBagInfoResponse getDefaultInstanceForType() {
        return cn.daxiang.protocol.game.BeastProtocol.BeastBagInfoResponse.getDefaultInstance();
      }

      public cn.daxiang.protocol.game.BeastProtocol.BeastBagInfoResponse build() {
        cn.daxiang.protocol.game.BeastProtocol.BeastBagInfoResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public cn.daxiang.protocol.game.BeastProtocol.BeastBagInfoResponse buildPartial() {
        cn.daxiang.protocol.game.BeastProtocol.BeastBagInfoResponse result = new cn.daxiang.protocol.game.BeastProtocol.BeastBagInfoResponse(this);
        int from_bitField0_ = bitField0_;
        if (infosBuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001)) {
            infos_ = java.util.Collections.unmodifiableList(infos_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.infos_ = infos_;
        } else {
          result.infos_ = infosBuilder_.build();
        }
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof cn.daxiang.protocol.game.BeastProtocol.BeastBagInfoResponse) {
          return mergeFrom((cn.daxiang.protocol.game.BeastProtocol.BeastBagInfoResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(cn.daxiang.protocol.game.BeastProtocol.BeastBagInfoResponse other) {
        if (other == cn.daxiang.protocol.game.BeastProtocol.BeastBagInfoResponse.getDefaultInstance()) return this;
        if (infosBuilder_ == null) {
          if (!other.infos_.isEmpty()) {
            if (infos_.isEmpty()) {
              infos_ = other.infos_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureInfosIsMutable();
              infos_.addAll(other.infos_);
            }
            onChanged();
          }
        } else {
          if (!other.infos_.isEmpty()) {
            if (infosBuilder_.isEmpty()) {
              infosBuilder_.dispose();
              infosBuilder_ = null;
              infos_ = other.infos_;
              bitField0_ = (bitField0_ & ~0x00000001);
              infosBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getInfosFieldBuilder() : null;
            } else {
              infosBuilder_.addAllMessages(other.infos_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        cn.daxiang.protocol.game.BeastProtocol.BeastBagInfoResponse parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (cn.daxiang.protocol.game.BeastProtocol.BeastBagInfoResponse) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<cn.daxiang.protocol.game.BeastProtocol.BeastsInfo> infos_ =
        java.util.Collections.emptyList();
      private void ensureInfosIsMutable() {
        if (!((bitField0_ & 0x00000001) == 0x00000001)) {
          infos_ = new java.util.ArrayList<cn.daxiang.protocol.game.BeastProtocol.BeastsInfo>(infos_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          cn.daxiang.protocol.game.BeastProtocol.BeastsInfo, cn.daxiang.protocol.game.BeastProtocol.BeastsInfo.Builder, cn.daxiang.protocol.game.BeastProtocol.BeastsInfoOrBuilder> infosBuilder_;

      /**
       * <code>repeated .BeastsInfo infos = 1;</code>
       */
      public java.util.List<cn.daxiang.protocol.game.BeastProtocol.BeastsInfo> getInfosList() {
        if (infosBuilder_ == null) {
          return java.util.Collections.unmodifiableList(infos_);
        } else {
          return infosBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .BeastsInfo infos = 1;</code>
       */
      public int getInfosCount() {
        if (infosBuilder_ == null) {
          return infos_.size();
        } else {
          return infosBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .BeastsInfo infos = 1;</code>
       */
      public cn.daxiang.protocol.game.BeastProtocol.BeastsInfo getInfos(int index) {
        if (infosBuilder_ == null) {
          return infos_.get(index);
        } else {
          return infosBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .BeastsInfo infos = 1;</code>
       */
      public Builder setInfos(
          int index, cn.daxiang.protocol.game.BeastProtocol.BeastsInfo value) {
        if (infosBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureInfosIsMutable();
          infos_.set(index, value);
          onChanged();
        } else {
          infosBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .BeastsInfo infos = 1;</code>
       */
      public Builder setInfos(
          int index, cn.daxiang.protocol.game.BeastProtocol.BeastsInfo.Builder builderForValue) {
        if (infosBuilder_ == null) {
          ensureInfosIsMutable();
          infos_.set(index, builderForValue.build());
          onChanged();
        } else {
          infosBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .BeastsInfo infos = 1;</code>
       */
      public Builder addInfos(cn.daxiang.protocol.game.BeastProtocol.BeastsInfo value) {
        if (infosBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureInfosIsMutable();
          infos_.add(value);
          onChanged();
        } else {
          infosBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .BeastsInfo infos = 1;</code>
       */
      public Builder addInfos(
          int index, cn.daxiang.protocol.game.BeastProtocol.BeastsInfo value) {
        if (infosBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureInfosIsMutable();
          infos_.add(index, value);
          onChanged();
        } else {
          infosBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .BeastsInfo infos = 1;</code>
       */
      public Builder addInfos(
          cn.daxiang.protocol.game.BeastProtocol.BeastsInfo.Builder builderForValue) {
        if (infosBuilder_ == null) {
          ensureInfosIsMutable();
          infos_.add(builderForValue.build());
          onChanged();
        } else {
          infosBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .BeastsInfo infos = 1;</code>
       */
      public Builder addInfos(
          int index, cn.daxiang.protocol.game.BeastProtocol.BeastsInfo.Builder builderForValue) {
        if (infosBuilder_ == null) {
          ensureInfosIsMutable();
          infos_.add(index, builderForValue.build());
          onChanged();
        } else {
          infosBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .BeastsInfo infos = 1;</code>
       */
      public Builder addAllInfos(
          java.lang.Iterable<? extends cn.daxiang.protocol.game.BeastProtocol.BeastsInfo> values) {
        if (infosBuilder_ == null) {
          ensureInfosIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, infos_);
          onChanged();
        } else {
          infosBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .BeastsInfo infos = 1;</code>
       */
      public Builder clearInfos() {
        if (infosBuilder_ == null) {
          infos_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          infosBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .BeastsInfo infos = 1;</code>
       */
      public Builder removeInfos(int index) {
        if (infosBuilder_ == null) {
          ensureInfosIsMutable();
          infos_.remove(index);
          onChanged();
        } else {
          infosBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .BeastsInfo infos = 1;</code>
       */
      public cn.daxiang.protocol.game.BeastProtocol.BeastsInfo.Builder getInfosBuilder(
          int index) {
        return getInfosFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .BeastsInfo infos = 1;</code>
       */
      public cn.daxiang.protocol.game.BeastProtocol.BeastsInfoOrBuilder getInfosOrBuilder(
          int index) {
        if (infosBuilder_ == null) {
          return infos_.get(index);  } else {
          return infosBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .BeastsInfo infos = 1;</code>
       */
      public java.util.List<? extends cn.daxiang.protocol.game.BeastProtocol.BeastsInfoOrBuilder> 
           getInfosOrBuilderList() {
        if (infosBuilder_ != null) {
          return infosBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(infos_);
        }
      }
      /**
       * <code>repeated .BeastsInfo infos = 1;</code>
       */
      public cn.daxiang.protocol.game.BeastProtocol.BeastsInfo.Builder addInfosBuilder() {
        return getInfosFieldBuilder().addBuilder(
            cn.daxiang.protocol.game.BeastProtocol.BeastsInfo.getDefaultInstance());
      }
      /**
       * <code>repeated .BeastsInfo infos = 1;</code>
       */
      public cn.daxiang.protocol.game.BeastProtocol.BeastsInfo.Builder addInfosBuilder(
          int index) {
        return getInfosFieldBuilder().addBuilder(
            index, cn.daxiang.protocol.game.BeastProtocol.BeastsInfo.getDefaultInstance());
      }
      /**
       * <code>repeated .BeastsInfo infos = 1;</code>
       */
      public java.util.List<cn.daxiang.protocol.game.BeastProtocol.BeastsInfo.Builder> 
           getInfosBuilderList() {
        return getInfosFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          cn.daxiang.protocol.game.BeastProtocol.BeastsInfo, cn.daxiang.protocol.game.BeastProtocol.BeastsInfo.Builder, cn.daxiang.protocol.game.BeastProtocol.BeastsInfoOrBuilder> 
          getInfosFieldBuilder() {
        if (infosBuilder_ == null) {
          infosBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              cn.daxiang.protocol.game.BeastProtocol.BeastsInfo, cn.daxiang.protocol.game.BeastProtocol.BeastsInfo.Builder, cn.daxiang.protocol.game.BeastProtocol.BeastsInfoOrBuilder>(
                  infos_,
                  ((bitField0_ & 0x00000001) == 0x00000001),
                  getParentForChildren(),
                  isClean());
          infos_ = null;
        }
        return infosBuilder_;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:BeastBagInfoResponse)
    }

    // @@protoc_insertion_point(class_scope:BeastBagInfoResponse)
    private static final cn.daxiang.protocol.game.BeastProtocol.BeastBagInfoResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new cn.daxiang.protocol.game.BeastProtocol.BeastBagInfoResponse();
    }

    public static cn.daxiang.protocol.game.BeastProtocol.BeastBagInfoResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<BeastBagInfoResponse>
        PARSER = new com.google.protobuf.AbstractParser<BeastBagInfoResponse>() {
      public BeastBagInfoResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new BeastBagInfoResponse(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<BeastBagInfoResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<BeastBagInfoResponse> getParserForType() {
      return PARSER;
    }

    public cn.daxiang.protocol.game.BeastProtocol.BeastBagInfoResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface BeastManualResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:BeastManualResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     ** 图鉴值 key-类型 value-图鉴值
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; manualValueMap = 1;</code>
     */
    int getManualValueMapCount();
    /**
     * <pre>
     ** 图鉴值 key-类型 value-图鉴值
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; manualValueMap = 1;</code>
     */
    boolean containsManualValueMap(
        int key);
    /**
     * Use {@link #getManualValueMapMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Integer, java.lang.Integer>
    getManualValueMap();
    /**
     * <pre>
     ** 图鉴值 key-类型 value-图鉴值
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; manualValueMap = 1;</code>
     */
    java.util.Map<java.lang.Integer, java.lang.Integer>
    getManualValueMapMap();
    /**
     * <pre>
     ** 图鉴值 key-类型 value-图鉴值
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; manualValueMap = 1;</code>
     */

    int getManualValueMapOrDefault(
        int key,
        int defaultValue);
    /**
     * <pre>
     ** 图鉴值 key-类型 value-图鉴值
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; manualValueMap = 1;</code>
     */

    int getManualValueMapOrThrow(
        int key);

    /**
     * <pre>
     ** 已激活的神兽Id列表 
     * </pre>
     *
     * <code>repeated int32 beastIds = 2;</code>
     */
    java.util.List<java.lang.Integer> getBeastIdsList();
    /**
     * <pre>
     ** 已激活的神兽Id列表 
     * </pre>
     *
     * <code>repeated int32 beastIds = 2;</code>
     */
    int getBeastIdsCount();
    /**
     * <pre>
     ** 已激活的神兽Id列表 
     * </pre>
     *
     * <code>repeated int32 beastIds = 2;</code>
     */
    int getBeastIds(int index);

    /**
     * <pre>
     ** 已领取的图鉴奖励 
     * </pre>
     *
     * <code>repeated int32 receiveStageIds = 3;</code>
     */
    java.util.List<java.lang.Integer> getReceiveStageIdsList();
    /**
     * <pre>
     ** 已领取的图鉴奖励 
     * </pre>
     *
     * <code>repeated int32 receiveStageIds = 3;</code>
     */
    int getReceiveStageIdsCount();
    /**
     * <pre>
     ** 已领取的图鉴奖励 
     * </pre>
     *
     * <code>repeated int32 receiveStageIds = 3;</code>
     */
    int getReceiveStageIds(int index);

    /**
     * <pre>
     ** 已领取的共鸣奖励 
     * </pre>
     *
     * <code>repeated int32 receiveResonanceIds = 4;</code>
     */
    java.util.List<java.lang.Integer> getReceiveResonanceIdsList();
    /**
     * <pre>
     ** 已领取的共鸣奖励 
     * </pre>
     *
     * <code>repeated int32 receiveResonanceIds = 4;</code>
     */
    int getReceiveResonanceIdsCount();
    /**
     * <pre>
     ** 已领取的共鸣奖励 
     * </pre>
     *
     * <code>repeated int32 receiveResonanceIds = 4;</code>
     */
    int getReceiveResonanceIds(int index);
  }
  /**
   * <pre>
   ** 神兽图鉴列表 
   * </pre>
   *
   * Protobuf type {@code BeastManualResponse}
   */
  public  static final class BeastManualResponse extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:BeastManualResponse)
      BeastManualResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use BeastManualResponse.newBuilder() to construct.
    private BeastManualResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private BeastManualResponse() {
      beastIds_ = java.util.Collections.emptyList();
      receiveStageIds_ = java.util.Collections.emptyList();
      receiveResonanceIds_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private BeastManualResponse(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
                manualValueMap_ = com.google.protobuf.MapField.newMapField(
                    ManualValueMapDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000001;
              }
              com.google.protobuf.MapEntry<java.lang.Integer, java.lang.Integer>
              manualValueMap__ = input.readMessage(
                  ManualValueMapDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              manualValueMap_.getMutableMap().put(
                  manualValueMap__.getKey(), manualValueMap__.getValue());
              break;
            }
            case 16: {
              if (!((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
                beastIds_ = new java.util.ArrayList<java.lang.Integer>();
                mutable_bitField0_ |= 0x00000002;
              }
              beastIds_.add(input.readInt32());
              break;
            }
            case 18: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000002) == 0x00000002) && input.getBytesUntilLimit() > 0) {
                beastIds_ = new java.util.ArrayList<java.lang.Integer>();
                mutable_bitField0_ |= 0x00000002;
              }
              while (input.getBytesUntilLimit() > 0) {
                beastIds_.add(input.readInt32());
              }
              input.popLimit(limit);
              break;
            }
            case 24: {
              if (!((mutable_bitField0_ & 0x00000004) == 0x00000004)) {
                receiveStageIds_ = new java.util.ArrayList<java.lang.Integer>();
                mutable_bitField0_ |= 0x00000004;
              }
              receiveStageIds_.add(input.readInt32());
              break;
            }
            case 26: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000004) == 0x00000004) && input.getBytesUntilLimit() > 0) {
                receiveStageIds_ = new java.util.ArrayList<java.lang.Integer>();
                mutable_bitField0_ |= 0x00000004;
              }
              while (input.getBytesUntilLimit() > 0) {
                receiveStageIds_.add(input.readInt32());
              }
              input.popLimit(limit);
              break;
            }
            case 32: {
              if (!((mutable_bitField0_ & 0x00000008) == 0x00000008)) {
                receiveResonanceIds_ = new java.util.ArrayList<java.lang.Integer>();
                mutable_bitField0_ |= 0x00000008;
              }
              receiveResonanceIds_.add(input.readInt32());
              break;
            }
            case 34: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000008) == 0x00000008) && input.getBytesUntilLimit() > 0) {
                receiveResonanceIds_ = new java.util.ArrayList<java.lang.Integer>();
                mutable_bitField0_ |= 0x00000008;
              }
              while (input.getBytesUntilLimit() > 0) {
                receiveResonanceIds_.add(input.readInt32());
              }
              input.popLimit(limit);
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
          beastIds_ = java.util.Collections.unmodifiableList(beastIds_);
        }
        if (((mutable_bitField0_ & 0x00000004) == 0x00000004)) {
          receiveStageIds_ = java.util.Collections.unmodifiableList(receiveStageIds_);
        }
        if (((mutable_bitField0_ & 0x00000008) == 0x00000008)) {
          receiveResonanceIds_ = java.util.Collections.unmodifiableList(receiveResonanceIds_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.BeastProtocol.internal_static_BeastManualResponse_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 1:
          return internalGetManualValueMap();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.daxiang.protocol.game.BeastProtocol.internal_static_BeastManualResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.daxiang.protocol.game.BeastProtocol.BeastManualResponse.class, cn.daxiang.protocol.game.BeastProtocol.BeastManualResponse.Builder.class);
    }

    public static final int MANUALVALUEMAP_FIELD_NUMBER = 1;
    private static final class ManualValueMapDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Integer, java.lang.Integer> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Integer, java.lang.Integer>newDefaultInstance(
                  cn.daxiang.protocol.game.BeastProtocol.internal_static_BeastManualResponse_ManualValueMapEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0,
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0);
    }
    private com.google.protobuf.MapField<
        java.lang.Integer, java.lang.Integer> manualValueMap_;
    private com.google.protobuf.MapField<java.lang.Integer, java.lang.Integer>
    internalGetManualValueMap() {
      if (manualValueMap_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            ManualValueMapDefaultEntryHolder.defaultEntry);
      }
      return manualValueMap_;
    }

    public int getManualValueMapCount() {
      return internalGetManualValueMap().getMap().size();
    }
    /**
     * <pre>
     ** 图鉴值 key-类型 value-图鉴值
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; manualValueMap = 1;</code>
     */

    public boolean containsManualValueMap(
        int key) {
      
      return internalGetManualValueMap().getMap().containsKey(key);
    }
    /**
     * Use {@link #getManualValueMapMap()} instead.
     */
    @java.lang.Deprecated
    public java.util.Map<java.lang.Integer, java.lang.Integer> getManualValueMap() {
      return getManualValueMapMap();
    }
    /**
     * <pre>
     ** 图鉴值 key-类型 value-图鉴值
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; manualValueMap = 1;</code>
     */

    public java.util.Map<java.lang.Integer, java.lang.Integer> getManualValueMapMap() {
      return internalGetManualValueMap().getMap();
    }
    /**
     * <pre>
     ** 图鉴值 key-类型 value-图鉴值
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; manualValueMap = 1;</code>
     */

    public int getManualValueMapOrDefault(
        int key,
        int defaultValue) {
      
      java.util.Map<java.lang.Integer, java.lang.Integer> map =
          internalGetManualValueMap().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <pre>
     ** 图鉴值 key-类型 value-图鉴值
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; manualValueMap = 1;</code>
     */

    public int getManualValueMapOrThrow(
        int key) {
      
      java.util.Map<java.lang.Integer, java.lang.Integer> map =
          internalGetManualValueMap().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    public static final int BEASTIDS_FIELD_NUMBER = 2;
    private java.util.List<java.lang.Integer> beastIds_;
    /**
     * <pre>
     ** 已激活的神兽Id列表 
     * </pre>
     *
     * <code>repeated int32 beastIds = 2;</code>
     */
    public java.util.List<java.lang.Integer>
        getBeastIdsList() {
      return beastIds_;
    }
    /**
     * <pre>
     ** 已激活的神兽Id列表 
     * </pre>
     *
     * <code>repeated int32 beastIds = 2;</code>
     */
    public int getBeastIdsCount() {
      return beastIds_.size();
    }
    /**
     * <pre>
     ** 已激活的神兽Id列表 
     * </pre>
     *
     * <code>repeated int32 beastIds = 2;</code>
     */
    public int getBeastIds(int index) {
      return beastIds_.get(index);
    }
    private int beastIdsMemoizedSerializedSize = -1;

    public static final int RECEIVESTAGEIDS_FIELD_NUMBER = 3;
    private java.util.List<java.lang.Integer> receiveStageIds_;
    /**
     * <pre>
     ** 已领取的图鉴奖励 
     * </pre>
     *
     * <code>repeated int32 receiveStageIds = 3;</code>
     */
    public java.util.List<java.lang.Integer>
        getReceiveStageIdsList() {
      return receiveStageIds_;
    }
    /**
     * <pre>
     ** 已领取的图鉴奖励 
     * </pre>
     *
     * <code>repeated int32 receiveStageIds = 3;</code>
     */
    public int getReceiveStageIdsCount() {
      return receiveStageIds_.size();
    }
    /**
     * <pre>
     ** 已领取的图鉴奖励 
     * </pre>
     *
     * <code>repeated int32 receiveStageIds = 3;</code>
     */
    public int getReceiveStageIds(int index) {
      return receiveStageIds_.get(index);
    }
    private int receiveStageIdsMemoizedSerializedSize = -1;

    public static final int RECEIVERESONANCEIDS_FIELD_NUMBER = 4;
    private java.util.List<java.lang.Integer> receiveResonanceIds_;
    /**
     * <pre>
     ** 已领取的共鸣奖励 
     * </pre>
     *
     * <code>repeated int32 receiveResonanceIds = 4;</code>
     */
    public java.util.List<java.lang.Integer>
        getReceiveResonanceIdsList() {
      return receiveResonanceIds_;
    }
    /**
     * <pre>
     ** 已领取的共鸣奖励 
     * </pre>
     *
     * <code>repeated int32 receiveResonanceIds = 4;</code>
     */
    public int getReceiveResonanceIdsCount() {
      return receiveResonanceIds_.size();
    }
    /**
     * <pre>
     ** 已领取的共鸣奖励 
     * </pre>
     *
     * <code>repeated int32 receiveResonanceIds = 4;</code>
     */
    public int getReceiveResonanceIds(int index) {
      return receiveResonanceIds_.get(index);
    }
    private int receiveResonanceIdsMemoizedSerializedSize = -1;

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      com.google.protobuf.GeneratedMessageV3
        .serializeIntegerMapTo(
          output,
          internalGetManualValueMap(),
          ManualValueMapDefaultEntryHolder.defaultEntry,
          1);
      if (getBeastIdsList().size() > 0) {
        output.writeUInt32NoTag(18);
        output.writeUInt32NoTag(beastIdsMemoizedSerializedSize);
      }
      for (int i = 0; i < beastIds_.size(); i++) {
        output.writeInt32NoTag(beastIds_.get(i));
      }
      if (getReceiveStageIdsList().size() > 0) {
        output.writeUInt32NoTag(26);
        output.writeUInt32NoTag(receiveStageIdsMemoizedSerializedSize);
      }
      for (int i = 0; i < receiveStageIds_.size(); i++) {
        output.writeInt32NoTag(receiveStageIds_.get(i));
      }
      if (getReceiveResonanceIdsList().size() > 0) {
        output.writeUInt32NoTag(34);
        output.writeUInt32NoTag(receiveResonanceIdsMemoizedSerializedSize);
      }
      for (int i = 0; i < receiveResonanceIds_.size(); i++) {
        output.writeInt32NoTag(receiveResonanceIds_.get(i));
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (java.util.Map.Entry<java.lang.Integer, java.lang.Integer> entry
           : internalGetManualValueMap().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Integer, java.lang.Integer>
        manualValueMap__ = ManualValueMapDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(1, manualValueMap__);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < beastIds_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt32SizeNoTag(beastIds_.get(i));
        }
        size += dataSize;
        if (!getBeastIdsList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        beastIdsMemoizedSerializedSize = dataSize;
      }
      {
        int dataSize = 0;
        for (int i = 0; i < receiveStageIds_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt32SizeNoTag(receiveStageIds_.get(i));
        }
        size += dataSize;
        if (!getReceiveStageIdsList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        receiveStageIdsMemoizedSerializedSize = dataSize;
      }
      {
        int dataSize = 0;
        for (int i = 0; i < receiveResonanceIds_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt32SizeNoTag(receiveResonanceIds_.get(i));
        }
        size += dataSize;
        if (!getReceiveResonanceIdsList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        receiveResonanceIdsMemoizedSerializedSize = dataSize;
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof cn.daxiang.protocol.game.BeastProtocol.BeastManualResponse)) {
        return super.equals(obj);
      }
      cn.daxiang.protocol.game.BeastProtocol.BeastManualResponse other = (cn.daxiang.protocol.game.BeastProtocol.BeastManualResponse) obj;

      boolean result = true;
      result = result && internalGetManualValueMap().equals(
          other.internalGetManualValueMap());
      result = result && getBeastIdsList()
          .equals(other.getBeastIdsList());
      result = result && getReceiveStageIdsList()
          .equals(other.getReceiveStageIdsList());
      result = result && getReceiveResonanceIdsList()
          .equals(other.getReceiveResonanceIdsList());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (!internalGetManualValueMap().getMap().isEmpty()) {
        hash = (37 * hash) + MANUALVALUEMAP_FIELD_NUMBER;
        hash = (53 * hash) + internalGetManualValueMap().hashCode();
      }
      if (getBeastIdsCount() > 0) {
        hash = (37 * hash) + BEASTIDS_FIELD_NUMBER;
        hash = (53 * hash) + getBeastIdsList().hashCode();
      }
      if (getReceiveStageIdsCount() > 0) {
        hash = (37 * hash) + RECEIVESTAGEIDS_FIELD_NUMBER;
        hash = (53 * hash) + getReceiveStageIdsList().hashCode();
      }
      if (getReceiveResonanceIdsCount() > 0) {
        hash = (37 * hash) + RECEIVERESONANCEIDS_FIELD_NUMBER;
        hash = (53 * hash) + getReceiveResonanceIdsList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static cn.daxiang.protocol.game.BeastProtocol.BeastManualResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastManualResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastManualResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastManualResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastManualResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastManualResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastManualResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastManualResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastManualResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastManualResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastManualResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastManualResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(cn.daxiang.protocol.game.BeastProtocol.BeastManualResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** 神兽图鉴列表 
     * </pre>
     *
     * Protobuf type {@code BeastManualResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:BeastManualResponse)
        cn.daxiang.protocol.game.BeastProtocol.BeastManualResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.daxiang.protocol.game.BeastProtocol.internal_static_BeastManualResponse_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetManualValueMap();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetMutableManualValueMap();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.daxiang.protocol.game.BeastProtocol.internal_static_BeastManualResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.daxiang.protocol.game.BeastProtocol.BeastManualResponse.class, cn.daxiang.protocol.game.BeastProtocol.BeastManualResponse.Builder.class);
      }

      // Construct using cn.daxiang.protocol.game.BeastProtocol.BeastManualResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        internalGetMutableManualValueMap().clear();
        beastIds_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        receiveStageIds_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);
        receiveResonanceIds_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return cn.daxiang.protocol.game.BeastProtocol.internal_static_BeastManualResponse_descriptor;
      }

      public cn.daxiang.protocol.game.BeastProtocol.BeastManualResponse getDefaultInstanceForType() {
        return cn.daxiang.protocol.game.BeastProtocol.BeastManualResponse.getDefaultInstance();
      }

      public cn.daxiang.protocol.game.BeastProtocol.BeastManualResponse build() {
        cn.daxiang.protocol.game.BeastProtocol.BeastManualResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public cn.daxiang.protocol.game.BeastProtocol.BeastManualResponse buildPartial() {
        cn.daxiang.protocol.game.BeastProtocol.BeastManualResponse result = new cn.daxiang.protocol.game.BeastProtocol.BeastManualResponse(this);
        int from_bitField0_ = bitField0_;
        result.manualValueMap_ = internalGetManualValueMap();
        result.manualValueMap_.makeImmutable();
        if (((bitField0_ & 0x00000002) == 0x00000002)) {
          beastIds_ = java.util.Collections.unmodifiableList(beastIds_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.beastIds_ = beastIds_;
        if (((bitField0_ & 0x00000004) == 0x00000004)) {
          receiveStageIds_ = java.util.Collections.unmodifiableList(receiveStageIds_);
          bitField0_ = (bitField0_ & ~0x00000004);
        }
        result.receiveStageIds_ = receiveStageIds_;
        if (((bitField0_ & 0x00000008) == 0x00000008)) {
          receiveResonanceIds_ = java.util.Collections.unmodifiableList(receiveResonanceIds_);
          bitField0_ = (bitField0_ & ~0x00000008);
        }
        result.receiveResonanceIds_ = receiveResonanceIds_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof cn.daxiang.protocol.game.BeastProtocol.BeastManualResponse) {
          return mergeFrom((cn.daxiang.protocol.game.BeastProtocol.BeastManualResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(cn.daxiang.protocol.game.BeastProtocol.BeastManualResponse other) {
        if (other == cn.daxiang.protocol.game.BeastProtocol.BeastManualResponse.getDefaultInstance()) return this;
        internalGetMutableManualValueMap().mergeFrom(
            other.internalGetManualValueMap());
        if (!other.beastIds_.isEmpty()) {
          if (beastIds_.isEmpty()) {
            beastIds_ = other.beastIds_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureBeastIdsIsMutable();
            beastIds_.addAll(other.beastIds_);
          }
          onChanged();
        }
        if (!other.receiveStageIds_.isEmpty()) {
          if (receiveStageIds_.isEmpty()) {
            receiveStageIds_ = other.receiveStageIds_;
            bitField0_ = (bitField0_ & ~0x00000004);
          } else {
            ensureReceiveStageIdsIsMutable();
            receiveStageIds_.addAll(other.receiveStageIds_);
          }
          onChanged();
        }
        if (!other.receiveResonanceIds_.isEmpty()) {
          if (receiveResonanceIds_.isEmpty()) {
            receiveResonanceIds_ = other.receiveResonanceIds_;
            bitField0_ = (bitField0_ & ~0x00000008);
          } else {
            ensureReceiveResonanceIdsIsMutable();
            receiveResonanceIds_.addAll(other.receiveResonanceIds_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        cn.daxiang.protocol.game.BeastProtocol.BeastManualResponse parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (cn.daxiang.protocol.game.BeastProtocol.BeastManualResponse) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.MapField<
          java.lang.Integer, java.lang.Integer> manualValueMap_;
      private com.google.protobuf.MapField<java.lang.Integer, java.lang.Integer>
      internalGetManualValueMap() {
        if (manualValueMap_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              ManualValueMapDefaultEntryHolder.defaultEntry);
        }
        return manualValueMap_;
      }
      private com.google.protobuf.MapField<java.lang.Integer, java.lang.Integer>
      internalGetMutableManualValueMap() {
        onChanged();;
        if (manualValueMap_ == null) {
          manualValueMap_ = com.google.protobuf.MapField.newMapField(
              ManualValueMapDefaultEntryHolder.defaultEntry);
        }
        if (!manualValueMap_.isMutable()) {
          manualValueMap_ = manualValueMap_.copy();
        }
        return manualValueMap_;
      }

      public int getManualValueMapCount() {
        return internalGetManualValueMap().getMap().size();
      }
      /**
       * <pre>
       ** 图鉴值 key-类型 value-图鉴值
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; manualValueMap = 1;</code>
       */

      public boolean containsManualValueMap(
          int key) {
        
        return internalGetManualValueMap().getMap().containsKey(key);
      }
      /**
       * Use {@link #getManualValueMapMap()} instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, java.lang.Integer> getManualValueMap() {
        return getManualValueMapMap();
      }
      /**
       * <pre>
       ** 图鉴值 key-类型 value-图鉴值
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; manualValueMap = 1;</code>
       */

      public java.util.Map<java.lang.Integer, java.lang.Integer> getManualValueMapMap() {
        return internalGetManualValueMap().getMap();
      }
      /**
       * <pre>
       ** 图鉴值 key-类型 value-图鉴值
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; manualValueMap = 1;</code>
       */

      public int getManualValueMapOrDefault(
          int key,
          int defaultValue) {
        
        java.util.Map<java.lang.Integer, java.lang.Integer> map =
            internalGetManualValueMap().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <pre>
       ** 图鉴值 key-类型 value-图鉴值
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; manualValueMap = 1;</code>
       */

      public int getManualValueMapOrThrow(
          int key) {
        
        java.util.Map<java.lang.Integer, java.lang.Integer> map =
            internalGetManualValueMap().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearManualValueMap() {
        internalGetMutableManualValueMap().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <pre>
       ** 图鉴值 key-类型 value-图鉴值
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; manualValueMap = 1;</code>
       */

      public Builder removeManualValueMap(
          int key) {
        
        internalGetMutableManualValueMap().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, java.lang.Integer>
      getMutableManualValueMap() {
        return internalGetMutableManualValueMap().getMutableMap();
      }
      /**
       * <pre>
       ** 图鉴值 key-类型 value-图鉴值
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; manualValueMap = 1;</code>
       */
      public Builder putManualValueMap(
          int key,
          int value) {
        
        
        internalGetMutableManualValueMap().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <pre>
       ** 图鉴值 key-类型 value-图鉴值
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; manualValueMap = 1;</code>
       */

      public Builder putAllManualValueMap(
          java.util.Map<java.lang.Integer, java.lang.Integer> values) {
        internalGetMutableManualValueMap().getMutableMap()
            .putAll(values);
        return this;
      }

      private java.util.List<java.lang.Integer> beastIds_ = java.util.Collections.emptyList();
      private void ensureBeastIdsIsMutable() {
        if (!((bitField0_ & 0x00000002) == 0x00000002)) {
          beastIds_ = new java.util.ArrayList<java.lang.Integer>(beastIds_);
          bitField0_ |= 0x00000002;
         }
      }
      /**
       * <pre>
       ** 已激活的神兽Id列表 
       * </pre>
       *
       * <code>repeated int32 beastIds = 2;</code>
       */
      public java.util.List<java.lang.Integer>
          getBeastIdsList() {
        return java.util.Collections.unmodifiableList(beastIds_);
      }
      /**
       * <pre>
       ** 已激活的神兽Id列表 
       * </pre>
       *
       * <code>repeated int32 beastIds = 2;</code>
       */
      public int getBeastIdsCount() {
        return beastIds_.size();
      }
      /**
       * <pre>
       ** 已激活的神兽Id列表 
       * </pre>
       *
       * <code>repeated int32 beastIds = 2;</code>
       */
      public int getBeastIds(int index) {
        return beastIds_.get(index);
      }
      /**
       * <pre>
       ** 已激活的神兽Id列表 
       * </pre>
       *
       * <code>repeated int32 beastIds = 2;</code>
       */
      public Builder setBeastIds(
          int index, int value) {
        ensureBeastIdsIsMutable();
        beastIds_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 已激活的神兽Id列表 
       * </pre>
       *
       * <code>repeated int32 beastIds = 2;</code>
       */
      public Builder addBeastIds(int value) {
        ensureBeastIdsIsMutable();
        beastIds_.add(value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 已激活的神兽Id列表 
       * </pre>
       *
       * <code>repeated int32 beastIds = 2;</code>
       */
      public Builder addAllBeastIds(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureBeastIdsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, beastIds_);
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 已激活的神兽Id列表 
       * </pre>
       *
       * <code>repeated int32 beastIds = 2;</code>
       */
      public Builder clearBeastIds() {
        beastIds_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }

      private java.util.List<java.lang.Integer> receiveStageIds_ = java.util.Collections.emptyList();
      private void ensureReceiveStageIdsIsMutable() {
        if (!((bitField0_ & 0x00000004) == 0x00000004)) {
          receiveStageIds_ = new java.util.ArrayList<java.lang.Integer>(receiveStageIds_);
          bitField0_ |= 0x00000004;
         }
      }
      /**
       * <pre>
       ** 已领取的图鉴奖励 
       * </pre>
       *
       * <code>repeated int32 receiveStageIds = 3;</code>
       */
      public java.util.List<java.lang.Integer>
          getReceiveStageIdsList() {
        return java.util.Collections.unmodifiableList(receiveStageIds_);
      }
      /**
       * <pre>
       ** 已领取的图鉴奖励 
       * </pre>
       *
       * <code>repeated int32 receiveStageIds = 3;</code>
       */
      public int getReceiveStageIdsCount() {
        return receiveStageIds_.size();
      }
      /**
       * <pre>
       ** 已领取的图鉴奖励 
       * </pre>
       *
       * <code>repeated int32 receiveStageIds = 3;</code>
       */
      public int getReceiveStageIds(int index) {
        return receiveStageIds_.get(index);
      }
      /**
       * <pre>
       ** 已领取的图鉴奖励 
       * </pre>
       *
       * <code>repeated int32 receiveStageIds = 3;</code>
       */
      public Builder setReceiveStageIds(
          int index, int value) {
        ensureReceiveStageIdsIsMutable();
        receiveStageIds_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 已领取的图鉴奖励 
       * </pre>
       *
       * <code>repeated int32 receiveStageIds = 3;</code>
       */
      public Builder addReceiveStageIds(int value) {
        ensureReceiveStageIdsIsMutable();
        receiveStageIds_.add(value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 已领取的图鉴奖励 
       * </pre>
       *
       * <code>repeated int32 receiveStageIds = 3;</code>
       */
      public Builder addAllReceiveStageIds(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureReceiveStageIdsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, receiveStageIds_);
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 已领取的图鉴奖励 
       * </pre>
       *
       * <code>repeated int32 receiveStageIds = 3;</code>
       */
      public Builder clearReceiveStageIds() {
        receiveStageIds_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
        return this;
      }

      private java.util.List<java.lang.Integer> receiveResonanceIds_ = java.util.Collections.emptyList();
      private void ensureReceiveResonanceIdsIsMutable() {
        if (!((bitField0_ & 0x00000008) == 0x00000008)) {
          receiveResonanceIds_ = new java.util.ArrayList<java.lang.Integer>(receiveResonanceIds_);
          bitField0_ |= 0x00000008;
         }
      }
      /**
       * <pre>
       ** 已领取的共鸣奖励 
       * </pre>
       *
       * <code>repeated int32 receiveResonanceIds = 4;</code>
       */
      public java.util.List<java.lang.Integer>
          getReceiveResonanceIdsList() {
        return java.util.Collections.unmodifiableList(receiveResonanceIds_);
      }
      /**
       * <pre>
       ** 已领取的共鸣奖励 
       * </pre>
       *
       * <code>repeated int32 receiveResonanceIds = 4;</code>
       */
      public int getReceiveResonanceIdsCount() {
        return receiveResonanceIds_.size();
      }
      /**
       * <pre>
       ** 已领取的共鸣奖励 
       * </pre>
       *
       * <code>repeated int32 receiveResonanceIds = 4;</code>
       */
      public int getReceiveResonanceIds(int index) {
        return receiveResonanceIds_.get(index);
      }
      /**
       * <pre>
       ** 已领取的共鸣奖励 
       * </pre>
       *
       * <code>repeated int32 receiveResonanceIds = 4;</code>
       */
      public Builder setReceiveResonanceIds(
          int index, int value) {
        ensureReceiveResonanceIdsIsMutable();
        receiveResonanceIds_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 已领取的共鸣奖励 
       * </pre>
       *
       * <code>repeated int32 receiveResonanceIds = 4;</code>
       */
      public Builder addReceiveResonanceIds(int value) {
        ensureReceiveResonanceIdsIsMutable();
        receiveResonanceIds_.add(value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 已领取的共鸣奖励 
       * </pre>
       *
       * <code>repeated int32 receiveResonanceIds = 4;</code>
       */
      public Builder addAllReceiveResonanceIds(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureReceiveResonanceIdsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, receiveResonanceIds_);
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 已领取的共鸣奖励 
       * </pre>
       *
       * <code>repeated int32 receiveResonanceIds = 4;</code>
       */
      public Builder clearReceiveResonanceIds() {
        receiveResonanceIds_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000008);
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:BeastManualResponse)
    }

    // @@protoc_insertion_point(class_scope:BeastManualResponse)
    private static final cn.daxiang.protocol.game.BeastProtocol.BeastManualResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new cn.daxiang.protocol.game.BeastProtocol.BeastManualResponse();
    }

    public static cn.daxiang.protocol.game.BeastProtocol.BeastManualResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<BeastManualResponse>
        PARSER = new com.google.protobuf.AbstractParser<BeastManualResponse>() {
      public BeastManualResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new BeastManualResponse(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<BeastManualResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<BeastManualResponse> getParserForType() {
      return PARSER;
    }

    public cn.daxiang.protocol.game.BeastProtocol.BeastManualResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface BeastAddExpRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:BeastAddExpRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     ** 神兽ID 
     * </pre>
     *
     * <code>int32 configId = 1;</code>
     */
    int getConfigId();

    /**
     * <pre>
     ** 神兽等级 
     * </pre>
     *
     * <code>int32 level = 2;</code>
     */
    int getLevel();
  }
  /**
   * <pre>
   ** 神兽增加经验请求 
   * </pre>
   *
   * Protobuf type {@code BeastAddExpRequest}
   */
  public  static final class BeastAddExpRequest extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:BeastAddExpRequest)
      BeastAddExpRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use BeastAddExpRequest.newBuilder() to construct.
    private BeastAddExpRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private BeastAddExpRequest() {
      configId_ = 0;
      level_ = 0;
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private BeastAddExpRequest(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {

              configId_ = input.readInt32();
              break;
            }
            case 16: {

              level_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.BeastProtocol.internal_static_BeastAddExpRequest_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.daxiang.protocol.game.BeastProtocol.internal_static_BeastAddExpRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.daxiang.protocol.game.BeastProtocol.BeastAddExpRequest.class, cn.daxiang.protocol.game.BeastProtocol.BeastAddExpRequest.Builder.class);
    }

    public static final int CONFIGID_FIELD_NUMBER = 1;
    private int configId_;
    /**
     * <pre>
     ** 神兽ID 
     * </pre>
     *
     * <code>int32 configId = 1;</code>
     */
    public int getConfigId() {
      return configId_;
    }

    public static final int LEVEL_FIELD_NUMBER = 2;
    private int level_;
    /**
     * <pre>
     ** 神兽等级 
     * </pre>
     *
     * <code>int32 level = 2;</code>
     */
    public int getLevel() {
      return level_;
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (configId_ != 0) {
        output.writeInt32(1, configId_);
      }
      if (level_ != 0) {
        output.writeInt32(2, level_);
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (configId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, configId_);
      }
      if (level_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, level_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof cn.daxiang.protocol.game.BeastProtocol.BeastAddExpRequest)) {
        return super.equals(obj);
      }
      cn.daxiang.protocol.game.BeastProtocol.BeastAddExpRequest other = (cn.daxiang.protocol.game.BeastProtocol.BeastAddExpRequest) obj;

      boolean result = true;
      result = result && (getConfigId()
          == other.getConfigId());
      result = result && (getLevel()
          == other.getLevel());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CONFIGID_FIELD_NUMBER;
      hash = (53 * hash) + getConfigId();
      hash = (37 * hash) + LEVEL_FIELD_NUMBER;
      hash = (53 * hash) + getLevel();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static cn.daxiang.protocol.game.BeastProtocol.BeastAddExpRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastAddExpRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastAddExpRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastAddExpRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastAddExpRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastAddExpRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastAddExpRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastAddExpRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastAddExpRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastAddExpRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastAddExpRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastAddExpRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(cn.daxiang.protocol.game.BeastProtocol.BeastAddExpRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** 神兽增加经验请求 
     * </pre>
     *
     * Protobuf type {@code BeastAddExpRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:BeastAddExpRequest)
        cn.daxiang.protocol.game.BeastProtocol.BeastAddExpRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.daxiang.protocol.game.BeastProtocol.internal_static_BeastAddExpRequest_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.daxiang.protocol.game.BeastProtocol.internal_static_BeastAddExpRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.daxiang.protocol.game.BeastProtocol.BeastAddExpRequest.class, cn.daxiang.protocol.game.BeastProtocol.BeastAddExpRequest.Builder.class);
      }

      // Construct using cn.daxiang.protocol.game.BeastProtocol.BeastAddExpRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        configId_ = 0;

        level_ = 0;

        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return cn.daxiang.protocol.game.BeastProtocol.internal_static_BeastAddExpRequest_descriptor;
      }

      public cn.daxiang.protocol.game.BeastProtocol.BeastAddExpRequest getDefaultInstanceForType() {
        return cn.daxiang.protocol.game.BeastProtocol.BeastAddExpRequest.getDefaultInstance();
      }

      public cn.daxiang.protocol.game.BeastProtocol.BeastAddExpRequest build() {
        cn.daxiang.protocol.game.BeastProtocol.BeastAddExpRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public cn.daxiang.protocol.game.BeastProtocol.BeastAddExpRequest buildPartial() {
        cn.daxiang.protocol.game.BeastProtocol.BeastAddExpRequest result = new cn.daxiang.protocol.game.BeastProtocol.BeastAddExpRequest(this);
        result.configId_ = configId_;
        result.level_ = level_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof cn.daxiang.protocol.game.BeastProtocol.BeastAddExpRequest) {
          return mergeFrom((cn.daxiang.protocol.game.BeastProtocol.BeastAddExpRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(cn.daxiang.protocol.game.BeastProtocol.BeastAddExpRequest other) {
        if (other == cn.daxiang.protocol.game.BeastProtocol.BeastAddExpRequest.getDefaultInstance()) return this;
        if (other.getConfigId() != 0) {
          setConfigId(other.getConfigId());
        }
        if (other.getLevel() != 0) {
          setLevel(other.getLevel());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        cn.daxiang.protocol.game.BeastProtocol.BeastAddExpRequest parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (cn.daxiang.protocol.game.BeastProtocol.BeastAddExpRequest) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int configId_ ;
      /**
       * <pre>
       ** 神兽ID 
       * </pre>
       *
       * <code>int32 configId = 1;</code>
       */
      public int getConfigId() {
        return configId_;
      }
      /**
       * <pre>
       ** 神兽ID 
       * </pre>
       *
       * <code>int32 configId = 1;</code>
       */
      public Builder setConfigId(int value) {
        
        configId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 神兽ID 
       * </pre>
       *
       * <code>int32 configId = 1;</code>
       */
      public Builder clearConfigId() {
        
        configId_ = 0;
        onChanged();
        return this;
      }

      private int level_ ;
      /**
       * <pre>
       ** 神兽等级 
       * </pre>
       *
       * <code>int32 level = 2;</code>
       */
      public int getLevel() {
        return level_;
      }
      /**
       * <pre>
       ** 神兽等级 
       * </pre>
       *
       * <code>int32 level = 2;</code>
       */
      public Builder setLevel(int value) {
        
        level_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 神兽等级 
       * </pre>
       *
       * <code>int32 level = 2;</code>
       */
      public Builder clearLevel() {
        
        level_ = 0;
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:BeastAddExpRequest)
    }

    // @@protoc_insertion_point(class_scope:BeastAddExpRequest)
    private static final cn.daxiang.protocol.game.BeastProtocol.BeastAddExpRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new cn.daxiang.protocol.game.BeastProtocol.BeastAddExpRequest();
    }

    public static cn.daxiang.protocol.game.BeastProtocol.BeastAddExpRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<BeastAddExpRequest>
        PARSER = new com.google.protobuf.AbstractParser<BeastAddExpRequest>() {
      public BeastAddExpRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new BeastAddExpRequest(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<BeastAddExpRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<BeastAddExpRequest> getParserForType() {
      return PARSER;
    }

    public cn.daxiang.protocol.game.BeastProtocol.BeastAddExpRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface BeastGachaInfoResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:BeastGachaInfoResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     ** 当前所选奖池 
     * </pre>
     *
     * <code>int32 configId = 1;</code>
     */
    int getConfigId();

    /**
     * <pre>
     ** 所有特权 
     * </pre>
     *
     * <code>repeated .BeastPrivilegeEntityVO privileges = 2;</code>
     */
    java.util.List<cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO> 
        getPrivilegesList();
    /**
     * <pre>
     ** 所有特权 
     * </pre>
     *
     * <code>repeated .BeastPrivilegeEntityVO privileges = 2;</code>
     */
    cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO getPrivileges(int index);
    /**
     * <pre>
     ** 所有特权 
     * </pre>
     *
     * <code>repeated .BeastPrivilegeEntityVO privileges = 2;</code>
     */
    int getPrivilegesCount();
    /**
     * <pre>
     ** 所有特权 
     * </pre>
     *
     * <code>repeated .BeastPrivilegeEntityVO privileges = 2;</code>
     */
    java.util.List<? extends cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVOOrBuilder> 
        getPrivilegesOrBuilderList();
    /**
     * <pre>
     ** 所有特权 
     * </pre>
     *
     * <code>repeated .BeastPrivilegeEntityVO privileges = 2;</code>
     */
    cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVOOrBuilder getPrivilegesOrBuilder(
        int index);

    /**
     * <pre>
     ** 已抽奖次数 
     * </pre>
     *
     * <code>int32 gachaTimes = 3;</code>
     */
    int getGachaTimes();

    /**
     * <pre>
     ** 积分 
     * </pre>
     *
     * <code>int32 score = 4;</code>
     */
    int getScore();

    /**
     * <pre>
     ** 历史最低幸运值 -1为未初始化过 
     * </pre>
     *
     * <code>int32 lowestLucky = 5;</code>
     */
    int getLowestLucky();

    /**
     * <pre>
     ** 历史最高幸运值 
     * </pre>
     *
     * <code>int32 highestLucky = 6;</code>
     */
    int getHighestLucky();

    /**
     * <pre>
     ** 厄运值 
     * </pre>
     *
     * <code>int32 badLucky = 7;</code>
     */
    int getBadLucky();

    /**
     * <pre>
     ** 已领回馈奖励
     * </pre>
     *
     * <code>repeated int32 receiveFeedbackIds = 8;</code>
     */
    java.util.List<java.lang.Integer> getReceiveFeedbackIdsList();
    /**
     * <pre>
     ** 已领回馈奖励
     * </pre>
     *
     * <code>repeated int32 receiveFeedbackIds = 8;</code>
     */
    int getReceiveFeedbackIdsCount();
    /**
     * <pre>
     ** 已领回馈奖励
     * </pre>
     *
     * <code>repeated int32 receiveFeedbackIds = 8;</code>
     */
    int getReceiveFeedbackIds(int index);

    /**
     * <pre>
     ** key:id,value:当天购买数量 
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; dayBuyTimes = 9;</code>
     */
    int getDayBuyTimesCount();
    /**
     * <pre>
     ** key:id,value:当天购买数量 
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; dayBuyTimes = 9;</code>
     */
    boolean containsDayBuyTimes(
        int key);
    /**
     * Use {@link #getDayBuyTimesMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Integer, java.lang.Integer>
    getDayBuyTimes();
    /**
     * <pre>
     ** key:id,value:当天购买数量 
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; dayBuyTimes = 9;</code>
     */
    java.util.Map<java.lang.Integer, java.lang.Integer>
    getDayBuyTimesMap();
    /**
     * <pre>
     ** key:id,value:当天购买数量 
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; dayBuyTimes = 9;</code>
     */

    int getDayBuyTimesOrDefault(
        int key,
        int defaultValue);
    /**
     * <pre>
     ** key:id,value:当天购买数量 
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; dayBuyTimes = 9;</code>
     */

    int getDayBuyTimesOrThrow(
        int key);

    /**
     * <pre>
     ** key:id,value:累计购买数量 
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; buyTimes = 10;</code>
     */
    int getBuyTimesCount();
    /**
     * <pre>
     ** key:id,value:累计购买数量 
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; buyTimes = 10;</code>
     */
    boolean containsBuyTimes(
        int key);
    /**
     * Use {@link #getBuyTimesMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Integer, java.lang.Integer>
    getBuyTimes();
    /**
     * <pre>
     ** key:id,value:累计购买数量 
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; buyTimes = 10;</code>
     */
    java.util.Map<java.lang.Integer, java.lang.Integer>
    getBuyTimesMap();
    /**
     * <pre>
     ** key:id,value:累计购买数量 
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; buyTimes = 10;</code>
     */

    int getBuyTimesOrDefault(
        int key,
        int defaultValue);
    /**
     * <pre>
     ** key:id,value:累计购买数量 
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; buyTimes = 10;</code>
     */

    int getBuyTimesOrThrow(
        int key);

    /**
     * <pre>
     ** key:id,value:累计已领奖励数量 
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; receives = 11;</code>
     */
    int getReceivesCount();
    /**
     * <pre>
     ** key:id,value:累计已领奖励数量 
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; receives = 11;</code>
     */
    boolean containsReceives(
        int key);
    /**
     * Use {@link #getReceivesMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Integer, java.lang.Integer>
    getReceives();
    /**
     * <pre>
     ** key:id,value:累计已领奖励数量 
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; receives = 11;</code>
     */
    java.util.Map<java.lang.Integer, java.lang.Integer>
    getReceivesMap();
    /**
     * <pre>
     ** key:id,value:累计已领奖励数量 
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; receives = 11;</code>
     */

    int getReceivesOrDefault(
        int key,
        int defaultValue);
    /**
     * <pre>
     ** key:id,value:累计已领奖励数量 
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; receives = 11;</code>
     */

    int getReceivesOrThrow(
        int key);
  }
  /**
   * <pre>
   ** 神兽召唤信息 
   * </pre>
   *
   * Protobuf type {@code BeastGachaInfoResponse}
   */
  public  static final class BeastGachaInfoResponse extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:BeastGachaInfoResponse)
      BeastGachaInfoResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use BeastGachaInfoResponse.newBuilder() to construct.
    private BeastGachaInfoResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private BeastGachaInfoResponse() {
      configId_ = 0;
      privileges_ = java.util.Collections.emptyList();
      gachaTimes_ = 0;
      score_ = 0;
      lowestLucky_ = 0;
      highestLucky_ = 0;
      badLucky_ = 0;
      receiveFeedbackIds_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private BeastGachaInfoResponse(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {

              configId_ = input.readInt32();
              break;
            }
            case 18: {
              if (!((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
                privileges_ = new java.util.ArrayList<cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO>();
                mutable_bitField0_ |= 0x00000002;
              }
              privileges_.add(
                  input.readMessage(cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO.parser(), extensionRegistry));
              break;
            }
            case 24: {

              gachaTimes_ = input.readInt32();
              break;
            }
            case 32: {

              score_ = input.readInt32();
              break;
            }
            case 40: {

              lowestLucky_ = input.readInt32();
              break;
            }
            case 48: {

              highestLucky_ = input.readInt32();
              break;
            }
            case 56: {

              badLucky_ = input.readInt32();
              break;
            }
            case 64: {
              if (!((mutable_bitField0_ & 0x00000080) == 0x00000080)) {
                receiveFeedbackIds_ = new java.util.ArrayList<java.lang.Integer>();
                mutable_bitField0_ |= 0x00000080;
              }
              receiveFeedbackIds_.add(input.readInt32());
              break;
            }
            case 66: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000080) == 0x00000080) && input.getBytesUntilLimit() > 0) {
                receiveFeedbackIds_ = new java.util.ArrayList<java.lang.Integer>();
                mutable_bitField0_ |= 0x00000080;
              }
              while (input.getBytesUntilLimit() > 0) {
                receiveFeedbackIds_.add(input.readInt32());
              }
              input.popLimit(limit);
              break;
            }
            case 74: {
              if (!((mutable_bitField0_ & 0x00000100) == 0x00000100)) {
                dayBuyTimes_ = com.google.protobuf.MapField.newMapField(
                    DayBuyTimesDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000100;
              }
              com.google.protobuf.MapEntry<java.lang.Integer, java.lang.Integer>
              dayBuyTimes__ = input.readMessage(
                  DayBuyTimesDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              dayBuyTimes_.getMutableMap().put(
                  dayBuyTimes__.getKey(), dayBuyTimes__.getValue());
              break;
            }
            case 82: {
              if (!((mutable_bitField0_ & 0x00000200) == 0x00000200)) {
                buyTimes_ = com.google.protobuf.MapField.newMapField(
                    BuyTimesDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000200;
              }
              com.google.protobuf.MapEntry<java.lang.Integer, java.lang.Integer>
              buyTimes__ = input.readMessage(
                  BuyTimesDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              buyTimes_.getMutableMap().put(
                  buyTimes__.getKey(), buyTimes__.getValue());
              break;
            }
            case 90: {
              if (!((mutable_bitField0_ & 0x00000400) == 0x00000400)) {
                receives_ = com.google.protobuf.MapField.newMapField(
                    ReceivesDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000400;
              }
              com.google.protobuf.MapEntry<java.lang.Integer, java.lang.Integer>
              receives__ = input.readMessage(
                  ReceivesDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              receives_.getMutableMap().put(
                  receives__.getKey(), receives__.getValue());
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
          privileges_ = java.util.Collections.unmodifiableList(privileges_);
        }
        if (((mutable_bitField0_ & 0x00000080) == 0x00000080)) {
          receiveFeedbackIds_ = java.util.Collections.unmodifiableList(receiveFeedbackIds_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.BeastProtocol.internal_static_BeastGachaInfoResponse_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 9:
          return internalGetDayBuyTimes();
        case 10:
          return internalGetBuyTimes();
        case 11:
          return internalGetReceives();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.daxiang.protocol.game.BeastProtocol.internal_static_BeastGachaInfoResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.daxiang.protocol.game.BeastProtocol.BeastGachaInfoResponse.class, cn.daxiang.protocol.game.BeastProtocol.BeastGachaInfoResponse.Builder.class);
    }

    private int bitField0_;
    public static final int CONFIGID_FIELD_NUMBER = 1;
    private int configId_;
    /**
     * <pre>
     ** 当前所选奖池 
     * </pre>
     *
     * <code>int32 configId = 1;</code>
     */
    public int getConfigId() {
      return configId_;
    }

    public static final int PRIVILEGES_FIELD_NUMBER = 2;
    private java.util.List<cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO> privileges_;
    /**
     * <pre>
     ** 所有特权 
     * </pre>
     *
     * <code>repeated .BeastPrivilegeEntityVO privileges = 2;</code>
     */
    public java.util.List<cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO> getPrivilegesList() {
      return privileges_;
    }
    /**
     * <pre>
     ** 所有特权 
     * </pre>
     *
     * <code>repeated .BeastPrivilegeEntityVO privileges = 2;</code>
     */
    public java.util.List<? extends cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVOOrBuilder> 
        getPrivilegesOrBuilderList() {
      return privileges_;
    }
    /**
     * <pre>
     ** 所有特权 
     * </pre>
     *
     * <code>repeated .BeastPrivilegeEntityVO privileges = 2;</code>
     */
    public int getPrivilegesCount() {
      return privileges_.size();
    }
    /**
     * <pre>
     ** 所有特权 
     * </pre>
     *
     * <code>repeated .BeastPrivilegeEntityVO privileges = 2;</code>
     */
    public cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO getPrivileges(int index) {
      return privileges_.get(index);
    }
    /**
     * <pre>
     ** 所有特权 
     * </pre>
     *
     * <code>repeated .BeastPrivilegeEntityVO privileges = 2;</code>
     */
    public cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVOOrBuilder getPrivilegesOrBuilder(
        int index) {
      return privileges_.get(index);
    }

    public static final int GACHATIMES_FIELD_NUMBER = 3;
    private int gachaTimes_;
    /**
     * <pre>
     ** 已抽奖次数 
     * </pre>
     *
     * <code>int32 gachaTimes = 3;</code>
     */
    public int getGachaTimes() {
      return gachaTimes_;
    }

    public static final int SCORE_FIELD_NUMBER = 4;
    private int score_;
    /**
     * <pre>
     ** 积分 
     * </pre>
     *
     * <code>int32 score = 4;</code>
     */
    public int getScore() {
      return score_;
    }

    public static final int LOWESTLUCKY_FIELD_NUMBER = 5;
    private int lowestLucky_;
    /**
     * <pre>
     ** 历史最低幸运值 -1为未初始化过 
     * </pre>
     *
     * <code>int32 lowestLucky = 5;</code>
     */
    public int getLowestLucky() {
      return lowestLucky_;
    }

    public static final int HIGHESTLUCKY_FIELD_NUMBER = 6;
    private int highestLucky_;
    /**
     * <pre>
     ** 历史最高幸运值 
     * </pre>
     *
     * <code>int32 highestLucky = 6;</code>
     */
    public int getHighestLucky() {
      return highestLucky_;
    }

    public static final int BADLUCKY_FIELD_NUMBER = 7;
    private int badLucky_;
    /**
     * <pre>
     ** 厄运值 
     * </pre>
     *
     * <code>int32 badLucky = 7;</code>
     */
    public int getBadLucky() {
      return badLucky_;
    }

    public static final int RECEIVEFEEDBACKIDS_FIELD_NUMBER = 8;
    private java.util.List<java.lang.Integer> receiveFeedbackIds_;
    /**
     * <pre>
     ** 已领回馈奖励
     * </pre>
     *
     * <code>repeated int32 receiveFeedbackIds = 8;</code>
     */
    public java.util.List<java.lang.Integer>
        getReceiveFeedbackIdsList() {
      return receiveFeedbackIds_;
    }
    /**
     * <pre>
     ** 已领回馈奖励
     * </pre>
     *
     * <code>repeated int32 receiveFeedbackIds = 8;</code>
     */
    public int getReceiveFeedbackIdsCount() {
      return receiveFeedbackIds_.size();
    }
    /**
     * <pre>
     ** 已领回馈奖励
     * </pre>
     *
     * <code>repeated int32 receiveFeedbackIds = 8;</code>
     */
    public int getReceiveFeedbackIds(int index) {
      return receiveFeedbackIds_.get(index);
    }
    private int receiveFeedbackIdsMemoizedSerializedSize = -1;

    public static final int DAYBUYTIMES_FIELD_NUMBER = 9;
    private static final class DayBuyTimesDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Integer, java.lang.Integer> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Integer, java.lang.Integer>newDefaultInstance(
                  cn.daxiang.protocol.game.BeastProtocol.internal_static_BeastGachaInfoResponse_DayBuyTimesEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0,
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0);
    }
    private com.google.protobuf.MapField<
        java.lang.Integer, java.lang.Integer> dayBuyTimes_;
    private com.google.protobuf.MapField<java.lang.Integer, java.lang.Integer>
    internalGetDayBuyTimes() {
      if (dayBuyTimes_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            DayBuyTimesDefaultEntryHolder.defaultEntry);
      }
      return dayBuyTimes_;
    }

    public int getDayBuyTimesCount() {
      return internalGetDayBuyTimes().getMap().size();
    }
    /**
     * <pre>
     ** key:id,value:当天购买数量 
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; dayBuyTimes = 9;</code>
     */

    public boolean containsDayBuyTimes(
        int key) {
      
      return internalGetDayBuyTimes().getMap().containsKey(key);
    }
    /**
     * Use {@link #getDayBuyTimesMap()} instead.
     */
    @java.lang.Deprecated
    public java.util.Map<java.lang.Integer, java.lang.Integer> getDayBuyTimes() {
      return getDayBuyTimesMap();
    }
    /**
     * <pre>
     ** key:id,value:当天购买数量 
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; dayBuyTimes = 9;</code>
     */

    public java.util.Map<java.lang.Integer, java.lang.Integer> getDayBuyTimesMap() {
      return internalGetDayBuyTimes().getMap();
    }
    /**
     * <pre>
     ** key:id,value:当天购买数量 
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; dayBuyTimes = 9;</code>
     */

    public int getDayBuyTimesOrDefault(
        int key,
        int defaultValue) {
      
      java.util.Map<java.lang.Integer, java.lang.Integer> map =
          internalGetDayBuyTimes().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <pre>
     ** key:id,value:当天购买数量 
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; dayBuyTimes = 9;</code>
     */

    public int getDayBuyTimesOrThrow(
        int key) {
      
      java.util.Map<java.lang.Integer, java.lang.Integer> map =
          internalGetDayBuyTimes().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    public static final int BUYTIMES_FIELD_NUMBER = 10;
    private static final class BuyTimesDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Integer, java.lang.Integer> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Integer, java.lang.Integer>newDefaultInstance(
                  cn.daxiang.protocol.game.BeastProtocol.internal_static_BeastGachaInfoResponse_BuyTimesEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0,
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0);
    }
    private com.google.protobuf.MapField<
        java.lang.Integer, java.lang.Integer> buyTimes_;
    private com.google.protobuf.MapField<java.lang.Integer, java.lang.Integer>
    internalGetBuyTimes() {
      if (buyTimes_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            BuyTimesDefaultEntryHolder.defaultEntry);
      }
      return buyTimes_;
    }

    public int getBuyTimesCount() {
      return internalGetBuyTimes().getMap().size();
    }
    /**
     * <pre>
     ** key:id,value:累计购买数量 
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; buyTimes = 10;</code>
     */

    public boolean containsBuyTimes(
        int key) {
      
      return internalGetBuyTimes().getMap().containsKey(key);
    }
    /**
     * Use {@link #getBuyTimesMap()} instead.
     */
    @java.lang.Deprecated
    public java.util.Map<java.lang.Integer, java.lang.Integer> getBuyTimes() {
      return getBuyTimesMap();
    }
    /**
     * <pre>
     ** key:id,value:累计购买数量 
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; buyTimes = 10;</code>
     */

    public java.util.Map<java.lang.Integer, java.lang.Integer> getBuyTimesMap() {
      return internalGetBuyTimes().getMap();
    }
    /**
     * <pre>
     ** key:id,value:累计购买数量 
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; buyTimes = 10;</code>
     */

    public int getBuyTimesOrDefault(
        int key,
        int defaultValue) {
      
      java.util.Map<java.lang.Integer, java.lang.Integer> map =
          internalGetBuyTimes().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <pre>
     ** key:id,value:累计购买数量 
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; buyTimes = 10;</code>
     */

    public int getBuyTimesOrThrow(
        int key) {
      
      java.util.Map<java.lang.Integer, java.lang.Integer> map =
          internalGetBuyTimes().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    public static final int RECEIVES_FIELD_NUMBER = 11;
    private static final class ReceivesDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Integer, java.lang.Integer> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Integer, java.lang.Integer>newDefaultInstance(
                  cn.daxiang.protocol.game.BeastProtocol.internal_static_BeastGachaInfoResponse_ReceivesEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0,
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0);
    }
    private com.google.protobuf.MapField<
        java.lang.Integer, java.lang.Integer> receives_;
    private com.google.protobuf.MapField<java.lang.Integer, java.lang.Integer>
    internalGetReceives() {
      if (receives_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            ReceivesDefaultEntryHolder.defaultEntry);
      }
      return receives_;
    }

    public int getReceivesCount() {
      return internalGetReceives().getMap().size();
    }
    /**
     * <pre>
     ** key:id,value:累计已领奖励数量 
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; receives = 11;</code>
     */

    public boolean containsReceives(
        int key) {
      
      return internalGetReceives().getMap().containsKey(key);
    }
    /**
     * Use {@link #getReceivesMap()} instead.
     */
    @java.lang.Deprecated
    public java.util.Map<java.lang.Integer, java.lang.Integer> getReceives() {
      return getReceivesMap();
    }
    /**
     * <pre>
     ** key:id,value:累计已领奖励数量 
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; receives = 11;</code>
     */

    public java.util.Map<java.lang.Integer, java.lang.Integer> getReceivesMap() {
      return internalGetReceives().getMap();
    }
    /**
     * <pre>
     ** key:id,value:累计已领奖励数量 
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; receives = 11;</code>
     */

    public int getReceivesOrDefault(
        int key,
        int defaultValue) {
      
      java.util.Map<java.lang.Integer, java.lang.Integer> map =
          internalGetReceives().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <pre>
     ** key:id,value:累计已领奖励数量 
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; receives = 11;</code>
     */

    public int getReceivesOrThrow(
        int key) {
      
      java.util.Map<java.lang.Integer, java.lang.Integer> map =
          internalGetReceives().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (configId_ != 0) {
        output.writeInt32(1, configId_);
      }
      for (int i = 0; i < privileges_.size(); i++) {
        output.writeMessage(2, privileges_.get(i));
      }
      if (gachaTimes_ != 0) {
        output.writeInt32(3, gachaTimes_);
      }
      if (score_ != 0) {
        output.writeInt32(4, score_);
      }
      if (lowestLucky_ != 0) {
        output.writeInt32(5, lowestLucky_);
      }
      if (highestLucky_ != 0) {
        output.writeInt32(6, highestLucky_);
      }
      if (badLucky_ != 0) {
        output.writeInt32(7, badLucky_);
      }
      if (getReceiveFeedbackIdsList().size() > 0) {
        output.writeUInt32NoTag(66);
        output.writeUInt32NoTag(receiveFeedbackIdsMemoizedSerializedSize);
      }
      for (int i = 0; i < receiveFeedbackIds_.size(); i++) {
        output.writeInt32NoTag(receiveFeedbackIds_.get(i));
      }
      com.google.protobuf.GeneratedMessageV3
        .serializeIntegerMapTo(
          output,
          internalGetDayBuyTimes(),
          DayBuyTimesDefaultEntryHolder.defaultEntry,
          9);
      com.google.protobuf.GeneratedMessageV3
        .serializeIntegerMapTo(
          output,
          internalGetBuyTimes(),
          BuyTimesDefaultEntryHolder.defaultEntry,
          10);
      com.google.protobuf.GeneratedMessageV3
        .serializeIntegerMapTo(
          output,
          internalGetReceives(),
          ReceivesDefaultEntryHolder.defaultEntry,
          11);
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (configId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, configId_);
      }
      for (int i = 0; i < privileges_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, privileges_.get(i));
      }
      if (gachaTimes_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, gachaTimes_);
      }
      if (score_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, score_);
      }
      if (lowestLucky_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(5, lowestLucky_);
      }
      if (highestLucky_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(6, highestLucky_);
      }
      if (badLucky_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(7, badLucky_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < receiveFeedbackIds_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt32SizeNoTag(receiveFeedbackIds_.get(i));
        }
        size += dataSize;
        if (!getReceiveFeedbackIdsList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        receiveFeedbackIdsMemoizedSerializedSize = dataSize;
      }
      for (java.util.Map.Entry<java.lang.Integer, java.lang.Integer> entry
           : internalGetDayBuyTimes().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Integer, java.lang.Integer>
        dayBuyTimes__ = DayBuyTimesDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(9, dayBuyTimes__);
      }
      for (java.util.Map.Entry<java.lang.Integer, java.lang.Integer> entry
           : internalGetBuyTimes().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Integer, java.lang.Integer>
        buyTimes__ = BuyTimesDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(10, buyTimes__);
      }
      for (java.util.Map.Entry<java.lang.Integer, java.lang.Integer> entry
           : internalGetReceives().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Integer, java.lang.Integer>
        receives__ = ReceivesDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(11, receives__);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof cn.daxiang.protocol.game.BeastProtocol.BeastGachaInfoResponse)) {
        return super.equals(obj);
      }
      cn.daxiang.protocol.game.BeastProtocol.BeastGachaInfoResponse other = (cn.daxiang.protocol.game.BeastProtocol.BeastGachaInfoResponse) obj;

      boolean result = true;
      result = result && (getConfigId()
          == other.getConfigId());
      result = result && getPrivilegesList()
          .equals(other.getPrivilegesList());
      result = result && (getGachaTimes()
          == other.getGachaTimes());
      result = result && (getScore()
          == other.getScore());
      result = result && (getLowestLucky()
          == other.getLowestLucky());
      result = result && (getHighestLucky()
          == other.getHighestLucky());
      result = result && (getBadLucky()
          == other.getBadLucky());
      result = result && getReceiveFeedbackIdsList()
          .equals(other.getReceiveFeedbackIdsList());
      result = result && internalGetDayBuyTimes().equals(
          other.internalGetDayBuyTimes());
      result = result && internalGetBuyTimes().equals(
          other.internalGetBuyTimes());
      result = result && internalGetReceives().equals(
          other.internalGetReceives());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CONFIGID_FIELD_NUMBER;
      hash = (53 * hash) + getConfigId();
      if (getPrivilegesCount() > 0) {
        hash = (37 * hash) + PRIVILEGES_FIELD_NUMBER;
        hash = (53 * hash) + getPrivilegesList().hashCode();
      }
      hash = (37 * hash) + GACHATIMES_FIELD_NUMBER;
      hash = (53 * hash) + getGachaTimes();
      hash = (37 * hash) + SCORE_FIELD_NUMBER;
      hash = (53 * hash) + getScore();
      hash = (37 * hash) + LOWESTLUCKY_FIELD_NUMBER;
      hash = (53 * hash) + getLowestLucky();
      hash = (37 * hash) + HIGHESTLUCKY_FIELD_NUMBER;
      hash = (53 * hash) + getHighestLucky();
      hash = (37 * hash) + BADLUCKY_FIELD_NUMBER;
      hash = (53 * hash) + getBadLucky();
      if (getReceiveFeedbackIdsCount() > 0) {
        hash = (37 * hash) + RECEIVEFEEDBACKIDS_FIELD_NUMBER;
        hash = (53 * hash) + getReceiveFeedbackIdsList().hashCode();
      }
      if (!internalGetDayBuyTimes().getMap().isEmpty()) {
        hash = (37 * hash) + DAYBUYTIMES_FIELD_NUMBER;
        hash = (53 * hash) + internalGetDayBuyTimes().hashCode();
      }
      if (!internalGetBuyTimes().getMap().isEmpty()) {
        hash = (37 * hash) + BUYTIMES_FIELD_NUMBER;
        hash = (53 * hash) + internalGetBuyTimes().hashCode();
      }
      if (!internalGetReceives().getMap().isEmpty()) {
        hash = (37 * hash) + RECEIVES_FIELD_NUMBER;
        hash = (53 * hash) + internalGetReceives().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static cn.daxiang.protocol.game.BeastProtocol.BeastGachaInfoResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastGachaInfoResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastGachaInfoResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastGachaInfoResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastGachaInfoResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastGachaInfoResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastGachaInfoResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastGachaInfoResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastGachaInfoResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastGachaInfoResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastGachaInfoResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastGachaInfoResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(cn.daxiang.protocol.game.BeastProtocol.BeastGachaInfoResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** 神兽召唤信息 
     * </pre>
     *
     * Protobuf type {@code BeastGachaInfoResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:BeastGachaInfoResponse)
        cn.daxiang.protocol.game.BeastProtocol.BeastGachaInfoResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.daxiang.protocol.game.BeastProtocol.internal_static_BeastGachaInfoResponse_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 9:
            return internalGetDayBuyTimes();
          case 10:
            return internalGetBuyTimes();
          case 11:
            return internalGetReceives();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 9:
            return internalGetMutableDayBuyTimes();
          case 10:
            return internalGetMutableBuyTimes();
          case 11:
            return internalGetMutableReceives();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.daxiang.protocol.game.BeastProtocol.internal_static_BeastGachaInfoResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.daxiang.protocol.game.BeastProtocol.BeastGachaInfoResponse.class, cn.daxiang.protocol.game.BeastProtocol.BeastGachaInfoResponse.Builder.class);
      }

      // Construct using cn.daxiang.protocol.game.BeastProtocol.BeastGachaInfoResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getPrivilegesFieldBuilder();
        }
      }
      public Builder clear() {
        super.clear();
        configId_ = 0;

        if (privilegesBuilder_ == null) {
          privileges_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
        } else {
          privilegesBuilder_.clear();
        }
        gachaTimes_ = 0;

        score_ = 0;

        lowestLucky_ = 0;

        highestLucky_ = 0;

        badLucky_ = 0;

        receiveFeedbackIds_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000080);
        internalGetMutableDayBuyTimes().clear();
        internalGetMutableBuyTimes().clear();
        internalGetMutableReceives().clear();
        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return cn.daxiang.protocol.game.BeastProtocol.internal_static_BeastGachaInfoResponse_descriptor;
      }

      public cn.daxiang.protocol.game.BeastProtocol.BeastGachaInfoResponse getDefaultInstanceForType() {
        return cn.daxiang.protocol.game.BeastProtocol.BeastGachaInfoResponse.getDefaultInstance();
      }

      public cn.daxiang.protocol.game.BeastProtocol.BeastGachaInfoResponse build() {
        cn.daxiang.protocol.game.BeastProtocol.BeastGachaInfoResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public cn.daxiang.protocol.game.BeastProtocol.BeastGachaInfoResponse buildPartial() {
        cn.daxiang.protocol.game.BeastProtocol.BeastGachaInfoResponse result = new cn.daxiang.protocol.game.BeastProtocol.BeastGachaInfoResponse(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        result.configId_ = configId_;
        if (privilegesBuilder_ == null) {
          if (((bitField0_ & 0x00000002) == 0x00000002)) {
            privileges_ = java.util.Collections.unmodifiableList(privileges_);
            bitField0_ = (bitField0_ & ~0x00000002);
          }
          result.privileges_ = privileges_;
        } else {
          result.privileges_ = privilegesBuilder_.build();
        }
        result.gachaTimes_ = gachaTimes_;
        result.score_ = score_;
        result.lowestLucky_ = lowestLucky_;
        result.highestLucky_ = highestLucky_;
        result.badLucky_ = badLucky_;
        if (((bitField0_ & 0x00000080) == 0x00000080)) {
          receiveFeedbackIds_ = java.util.Collections.unmodifiableList(receiveFeedbackIds_);
          bitField0_ = (bitField0_ & ~0x00000080);
        }
        result.receiveFeedbackIds_ = receiveFeedbackIds_;
        result.dayBuyTimes_ = internalGetDayBuyTimes();
        result.dayBuyTimes_.makeImmutable();
        result.buyTimes_ = internalGetBuyTimes();
        result.buyTimes_.makeImmutable();
        result.receives_ = internalGetReceives();
        result.receives_.makeImmutable();
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof cn.daxiang.protocol.game.BeastProtocol.BeastGachaInfoResponse) {
          return mergeFrom((cn.daxiang.protocol.game.BeastProtocol.BeastGachaInfoResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(cn.daxiang.protocol.game.BeastProtocol.BeastGachaInfoResponse other) {
        if (other == cn.daxiang.protocol.game.BeastProtocol.BeastGachaInfoResponse.getDefaultInstance()) return this;
        if (other.getConfigId() != 0) {
          setConfigId(other.getConfigId());
        }
        if (privilegesBuilder_ == null) {
          if (!other.privileges_.isEmpty()) {
            if (privileges_.isEmpty()) {
              privileges_ = other.privileges_;
              bitField0_ = (bitField0_ & ~0x00000002);
            } else {
              ensurePrivilegesIsMutable();
              privileges_.addAll(other.privileges_);
            }
            onChanged();
          }
        } else {
          if (!other.privileges_.isEmpty()) {
            if (privilegesBuilder_.isEmpty()) {
              privilegesBuilder_.dispose();
              privilegesBuilder_ = null;
              privileges_ = other.privileges_;
              bitField0_ = (bitField0_ & ~0x00000002);
              privilegesBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getPrivilegesFieldBuilder() : null;
            } else {
              privilegesBuilder_.addAllMessages(other.privileges_);
            }
          }
        }
        if (other.getGachaTimes() != 0) {
          setGachaTimes(other.getGachaTimes());
        }
        if (other.getScore() != 0) {
          setScore(other.getScore());
        }
        if (other.getLowestLucky() != 0) {
          setLowestLucky(other.getLowestLucky());
        }
        if (other.getHighestLucky() != 0) {
          setHighestLucky(other.getHighestLucky());
        }
        if (other.getBadLucky() != 0) {
          setBadLucky(other.getBadLucky());
        }
        if (!other.receiveFeedbackIds_.isEmpty()) {
          if (receiveFeedbackIds_.isEmpty()) {
            receiveFeedbackIds_ = other.receiveFeedbackIds_;
            bitField0_ = (bitField0_ & ~0x00000080);
          } else {
            ensureReceiveFeedbackIdsIsMutable();
            receiveFeedbackIds_.addAll(other.receiveFeedbackIds_);
          }
          onChanged();
        }
        internalGetMutableDayBuyTimes().mergeFrom(
            other.internalGetDayBuyTimes());
        internalGetMutableBuyTimes().mergeFrom(
            other.internalGetBuyTimes());
        internalGetMutableReceives().mergeFrom(
            other.internalGetReceives());
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        cn.daxiang.protocol.game.BeastProtocol.BeastGachaInfoResponse parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (cn.daxiang.protocol.game.BeastProtocol.BeastGachaInfoResponse) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int configId_ ;
      /**
       * <pre>
       ** 当前所选奖池 
       * </pre>
       *
       * <code>int32 configId = 1;</code>
       */
      public int getConfigId() {
        return configId_;
      }
      /**
       * <pre>
       ** 当前所选奖池 
       * </pre>
       *
       * <code>int32 configId = 1;</code>
       */
      public Builder setConfigId(int value) {
        
        configId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 当前所选奖池 
       * </pre>
       *
       * <code>int32 configId = 1;</code>
       */
      public Builder clearConfigId() {
        
        configId_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO> privileges_ =
        java.util.Collections.emptyList();
      private void ensurePrivilegesIsMutable() {
        if (!((bitField0_ & 0x00000002) == 0x00000002)) {
          privileges_ = new java.util.ArrayList<cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO>(privileges_);
          bitField0_ |= 0x00000002;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO, cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO.Builder, cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVOOrBuilder> privilegesBuilder_;

      /**
       * <pre>
       ** 所有特权 
       * </pre>
       *
       * <code>repeated .BeastPrivilegeEntityVO privileges = 2;</code>
       */
      public java.util.List<cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO> getPrivilegesList() {
        if (privilegesBuilder_ == null) {
          return java.util.Collections.unmodifiableList(privileges_);
        } else {
          return privilegesBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       ** 所有特权 
       * </pre>
       *
       * <code>repeated .BeastPrivilegeEntityVO privileges = 2;</code>
       */
      public int getPrivilegesCount() {
        if (privilegesBuilder_ == null) {
          return privileges_.size();
        } else {
          return privilegesBuilder_.getCount();
        }
      }
      /**
       * <pre>
       ** 所有特权 
       * </pre>
       *
       * <code>repeated .BeastPrivilegeEntityVO privileges = 2;</code>
       */
      public cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO getPrivileges(int index) {
        if (privilegesBuilder_ == null) {
          return privileges_.get(index);
        } else {
          return privilegesBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       ** 所有特权 
       * </pre>
       *
       * <code>repeated .BeastPrivilegeEntityVO privileges = 2;</code>
       */
      public Builder setPrivileges(
          int index, cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO value) {
        if (privilegesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePrivilegesIsMutable();
          privileges_.set(index, value);
          onChanged();
        } else {
          privilegesBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       ** 所有特权 
       * </pre>
       *
       * <code>repeated .BeastPrivilegeEntityVO privileges = 2;</code>
       */
      public Builder setPrivileges(
          int index, cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO.Builder builderForValue) {
        if (privilegesBuilder_ == null) {
          ensurePrivilegesIsMutable();
          privileges_.set(index, builderForValue.build());
          onChanged();
        } else {
          privilegesBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       ** 所有特权 
       * </pre>
       *
       * <code>repeated .BeastPrivilegeEntityVO privileges = 2;</code>
       */
      public Builder addPrivileges(cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO value) {
        if (privilegesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePrivilegesIsMutable();
          privileges_.add(value);
          onChanged();
        } else {
          privilegesBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       ** 所有特权 
       * </pre>
       *
       * <code>repeated .BeastPrivilegeEntityVO privileges = 2;</code>
       */
      public Builder addPrivileges(
          int index, cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO value) {
        if (privilegesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePrivilegesIsMutable();
          privileges_.add(index, value);
          onChanged();
        } else {
          privilegesBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       ** 所有特权 
       * </pre>
       *
       * <code>repeated .BeastPrivilegeEntityVO privileges = 2;</code>
       */
      public Builder addPrivileges(
          cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO.Builder builderForValue) {
        if (privilegesBuilder_ == null) {
          ensurePrivilegesIsMutable();
          privileges_.add(builderForValue.build());
          onChanged();
        } else {
          privilegesBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       ** 所有特权 
       * </pre>
       *
       * <code>repeated .BeastPrivilegeEntityVO privileges = 2;</code>
       */
      public Builder addPrivileges(
          int index, cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO.Builder builderForValue) {
        if (privilegesBuilder_ == null) {
          ensurePrivilegesIsMutable();
          privileges_.add(index, builderForValue.build());
          onChanged();
        } else {
          privilegesBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       ** 所有特权 
       * </pre>
       *
       * <code>repeated .BeastPrivilegeEntityVO privileges = 2;</code>
       */
      public Builder addAllPrivileges(
          java.lang.Iterable<? extends cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO> values) {
        if (privilegesBuilder_ == null) {
          ensurePrivilegesIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, privileges_);
          onChanged();
        } else {
          privilegesBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       ** 所有特权 
       * </pre>
       *
       * <code>repeated .BeastPrivilegeEntityVO privileges = 2;</code>
       */
      public Builder clearPrivileges() {
        if (privilegesBuilder_ == null) {
          privileges_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
        } else {
          privilegesBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       ** 所有特权 
       * </pre>
       *
       * <code>repeated .BeastPrivilegeEntityVO privileges = 2;</code>
       */
      public Builder removePrivileges(int index) {
        if (privilegesBuilder_ == null) {
          ensurePrivilegesIsMutable();
          privileges_.remove(index);
          onChanged();
        } else {
          privilegesBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       ** 所有特权 
       * </pre>
       *
       * <code>repeated .BeastPrivilegeEntityVO privileges = 2;</code>
       */
      public cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO.Builder getPrivilegesBuilder(
          int index) {
        return getPrivilegesFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       ** 所有特权 
       * </pre>
       *
       * <code>repeated .BeastPrivilegeEntityVO privileges = 2;</code>
       */
      public cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVOOrBuilder getPrivilegesOrBuilder(
          int index) {
        if (privilegesBuilder_ == null) {
          return privileges_.get(index);  } else {
          return privilegesBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       ** 所有特权 
       * </pre>
       *
       * <code>repeated .BeastPrivilegeEntityVO privileges = 2;</code>
       */
      public java.util.List<? extends cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVOOrBuilder> 
           getPrivilegesOrBuilderList() {
        if (privilegesBuilder_ != null) {
          return privilegesBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(privileges_);
        }
      }
      /**
       * <pre>
       ** 所有特权 
       * </pre>
       *
       * <code>repeated .BeastPrivilegeEntityVO privileges = 2;</code>
       */
      public cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO.Builder addPrivilegesBuilder() {
        return getPrivilegesFieldBuilder().addBuilder(
            cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO.getDefaultInstance());
      }
      /**
       * <pre>
       ** 所有特权 
       * </pre>
       *
       * <code>repeated .BeastPrivilegeEntityVO privileges = 2;</code>
       */
      public cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO.Builder addPrivilegesBuilder(
          int index) {
        return getPrivilegesFieldBuilder().addBuilder(
            index, cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO.getDefaultInstance());
      }
      /**
       * <pre>
       ** 所有特权 
       * </pre>
       *
       * <code>repeated .BeastPrivilegeEntityVO privileges = 2;</code>
       */
      public java.util.List<cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO.Builder> 
           getPrivilegesBuilderList() {
        return getPrivilegesFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO, cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO.Builder, cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVOOrBuilder> 
          getPrivilegesFieldBuilder() {
        if (privilegesBuilder_ == null) {
          privilegesBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO, cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO.Builder, cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVOOrBuilder>(
                  privileges_,
                  ((bitField0_ & 0x00000002) == 0x00000002),
                  getParentForChildren(),
                  isClean());
          privileges_ = null;
        }
        return privilegesBuilder_;
      }

      private int gachaTimes_ ;
      /**
       * <pre>
       ** 已抽奖次数 
       * </pre>
       *
       * <code>int32 gachaTimes = 3;</code>
       */
      public int getGachaTimes() {
        return gachaTimes_;
      }
      /**
       * <pre>
       ** 已抽奖次数 
       * </pre>
       *
       * <code>int32 gachaTimes = 3;</code>
       */
      public Builder setGachaTimes(int value) {
        
        gachaTimes_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 已抽奖次数 
       * </pre>
       *
       * <code>int32 gachaTimes = 3;</code>
       */
      public Builder clearGachaTimes() {
        
        gachaTimes_ = 0;
        onChanged();
        return this;
      }

      private int score_ ;
      /**
       * <pre>
       ** 积分 
       * </pre>
       *
       * <code>int32 score = 4;</code>
       */
      public int getScore() {
        return score_;
      }
      /**
       * <pre>
       ** 积分 
       * </pre>
       *
       * <code>int32 score = 4;</code>
       */
      public Builder setScore(int value) {
        
        score_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 积分 
       * </pre>
       *
       * <code>int32 score = 4;</code>
       */
      public Builder clearScore() {
        
        score_ = 0;
        onChanged();
        return this;
      }

      private int lowestLucky_ ;
      /**
       * <pre>
       ** 历史最低幸运值 -1为未初始化过 
       * </pre>
       *
       * <code>int32 lowestLucky = 5;</code>
       */
      public int getLowestLucky() {
        return lowestLucky_;
      }
      /**
       * <pre>
       ** 历史最低幸运值 -1为未初始化过 
       * </pre>
       *
       * <code>int32 lowestLucky = 5;</code>
       */
      public Builder setLowestLucky(int value) {
        
        lowestLucky_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 历史最低幸运值 -1为未初始化过 
       * </pre>
       *
       * <code>int32 lowestLucky = 5;</code>
       */
      public Builder clearLowestLucky() {
        
        lowestLucky_ = 0;
        onChanged();
        return this;
      }

      private int highestLucky_ ;
      /**
       * <pre>
       ** 历史最高幸运值 
       * </pre>
       *
       * <code>int32 highestLucky = 6;</code>
       */
      public int getHighestLucky() {
        return highestLucky_;
      }
      /**
       * <pre>
       ** 历史最高幸运值 
       * </pre>
       *
       * <code>int32 highestLucky = 6;</code>
       */
      public Builder setHighestLucky(int value) {
        
        highestLucky_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 历史最高幸运值 
       * </pre>
       *
       * <code>int32 highestLucky = 6;</code>
       */
      public Builder clearHighestLucky() {
        
        highestLucky_ = 0;
        onChanged();
        return this;
      }

      private int badLucky_ ;
      /**
       * <pre>
       ** 厄运值 
       * </pre>
       *
       * <code>int32 badLucky = 7;</code>
       */
      public int getBadLucky() {
        return badLucky_;
      }
      /**
       * <pre>
       ** 厄运值 
       * </pre>
       *
       * <code>int32 badLucky = 7;</code>
       */
      public Builder setBadLucky(int value) {
        
        badLucky_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 厄运值 
       * </pre>
       *
       * <code>int32 badLucky = 7;</code>
       */
      public Builder clearBadLucky() {
        
        badLucky_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<java.lang.Integer> receiveFeedbackIds_ = java.util.Collections.emptyList();
      private void ensureReceiveFeedbackIdsIsMutable() {
        if (!((bitField0_ & 0x00000080) == 0x00000080)) {
          receiveFeedbackIds_ = new java.util.ArrayList<java.lang.Integer>(receiveFeedbackIds_);
          bitField0_ |= 0x00000080;
         }
      }
      /**
       * <pre>
       ** 已领回馈奖励
       * </pre>
       *
       * <code>repeated int32 receiveFeedbackIds = 8;</code>
       */
      public java.util.List<java.lang.Integer>
          getReceiveFeedbackIdsList() {
        return java.util.Collections.unmodifiableList(receiveFeedbackIds_);
      }
      /**
       * <pre>
       ** 已领回馈奖励
       * </pre>
       *
       * <code>repeated int32 receiveFeedbackIds = 8;</code>
       */
      public int getReceiveFeedbackIdsCount() {
        return receiveFeedbackIds_.size();
      }
      /**
       * <pre>
       ** 已领回馈奖励
       * </pre>
       *
       * <code>repeated int32 receiveFeedbackIds = 8;</code>
       */
      public int getReceiveFeedbackIds(int index) {
        return receiveFeedbackIds_.get(index);
      }
      /**
       * <pre>
       ** 已领回馈奖励
       * </pre>
       *
       * <code>repeated int32 receiveFeedbackIds = 8;</code>
       */
      public Builder setReceiveFeedbackIds(
          int index, int value) {
        ensureReceiveFeedbackIdsIsMutable();
        receiveFeedbackIds_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 已领回馈奖励
       * </pre>
       *
       * <code>repeated int32 receiveFeedbackIds = 8;</code>
       */
      public Builder addReceiveFeedbackIds(int value) {
        ensureReceiveFeedbackIdsIsMutable();
        receiveFeedbackIds_.add(value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 已领回馈奖励
       * </pre>
       *
       * <code>repeated int32 receiveFeedbackIds = 8;</code>
       */
      public Builder addAllReceiveFeedbackIds(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureReceiveFeedbackIdsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, receiveFeedbackIds_);
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 已领回馈奖励
       * </pre>
       *
       * <code>repeated int32 receiveFeedbackIds = 8;</code>
       */
      public Builder clearReceiveFeedbackIds() {
        receiveFeedbackIds_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000080);
        onChanged();
        return this;
      }

      private com.google.protobuf.MapField<
          java.lang.Integer, java.lang.Integer> dayBuyTimes_;
      private com.google.protobuf.MapField<java.lang.Integer, java.lang.Integer>
      internalGetDayBuyTimes() {
        if (dayBuyTimes_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              DayBuyTimesDefaultEntryHolder.defaultEntry);
        }
        return dayBuyTimes_;
      }
      private com.google.protobuf.MapField<java.lang.Integer, java.lang.Integer>
      internalGetMutableDayBuyTimes() {
        onChanged();;
        if (dayBuyTimes_ == null) {
          dayBuyTimes_ = com.google.protobuf.MapField.newMapField(
              DayBuyTimesDefaultEntryHolder.defaultEntry);
        }
        if (!dayBuyTimes_.isMutable()) {
          dayBuyTimes_ = dayBuyTimes_.copy();
        }
        return dayBuyTimes_;
      }

      public int getDayBuyTimesCount() {
        return internalGetDayBuyTimes().getMap().size();
      }
      /**
       * <pre>
       ** key:id,value:当天购买数量 
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; dayBuyTimes = 9;</code>
       */

      public boolean containsDayBuyTimes(
          int key) {
        
        return internalGetDayBuyTimes().getMap().containsKey(key);
      }
      /**
       * Use {@link #getDayBuyTimesMap()} instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, java.lang.Integer> getDayBuyTimes() {
        return getDayBuyTimesMap();
      }
      /**
       * <pre>
       ** key:id,value:当天购买数量 
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; dayBuyTimes = 9;</code>
       */

      public java.util.Map<java.lang.Integer, java.lang.Integer> getDayBuyTimesMap() {
        return internalGetDayBuyTimes().getMap();
      }
      /**
       * <pre>
       ** key:id,value:当天购买数量 
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; dayBuyTimes = 9;</code>
       */

      public int getDayBuyTimesOrDefault(
          int key,
          int defaultValue) {
        
        java.util.Map<java.lang.Integer, java.lang.Integer> map =
            internalGetDayBuyTimes().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <pre>
       ** key:id,value:当天购买数量 
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; dayBuyTimes = 9;</code>
       */

      public int getDayBuyTimesOrThrow(
          int key) {
        
        java.util.Map<java.lang.Integer, java.lang.Integer> map =
            internalGetDayBuyTimes().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearDayBuyTimes() {
        internalGetMutableDayBuyTimes().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <pre>
       ** key:id,value:当天购买数量 
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; dayBuyTimes = 9;</code>
       */

      public Builder removeDayBuyTimes(
          int key) {
        
        internalGetMutableDayBuyTimes().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, java.lang.Integer>
      getMutableDayBuyTimes() {
        return internalGetMutableDayBuyTimes().getMutableMap();
      }
      /**
       * <pre>
       ** key:id,value:当天购买数量 
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; dayBuyTimes = 9;</code>
       */
      public Builder putDayBuyTimes(
          int key,
          int value) {
        
        
        internalGetMutableDayBuyTimes().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <pre>
       ** key:id,value:当天购买数量 
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; dayBuyTimes = 9;</code>
       */

      public Builder putAllDayBuyTimes(
          java.util.Map<java.lang.Integer, java.lang.Integer> values) {
        internalGetMutableDayBuyTimes().getMutableMap()
            .putAll(values);
        return this;
      }

      private com.google.protobuf.MapField<
          java.lang.Integer, java.lang.Integer> buyTimes_;
      private com.google.protobuf.MapField<java.lang.Integer, java.lang.Integer>
      internalGetBuyTimes() {
        if (buyTimes_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              BuyTimesDefaultEntryHolder.defaultEntry);
        }
        return buyTimes_;
      }
      private com.google.protobuf.MapField<java.lang.Integer, java.lang.Integer>
      internalGetMutableBuyTimes() {
        onChanged();;
        if (buyTimes_ == null) {
          buyTimes_ = com.google.protobuf.MapField.newMapField(
              BuyTimesDefaultEntryHolder.defaultEntry);
        }
        if (!buyTimes_.isMutable()) {
          buyTimes_ = buyTimes_.copy();
        }
        return buyTimes_;
      }

      public int getBuyTimesCount() {
        return internalGetBuyTimes().getMap().size();
      }
      /**
       * <pre>
       ** key:id,value:累计购买数量 
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; buyTimes = 10;</code>
       */

      public boolean containsBuyTimes(
          int key) {
        
        return internalGetBuyTimes().getMap().containsKey(key);
      }
      /**
       * Use {@link #getBuyTimesMap()} instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, java.lang.Integer> getBuyTimes() {
        return getBuyTimesMap();
      }
      /**
       * <pre>
       ** key:id,value:累计购买数量 
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; buyTimes = 10;</code>
       */

      public java.util.Map<java.lang.Integer, java.lang.Integer> getBuyTimesMap() {
        return internalGetBuyTimes().getMap();
      }
      /**
       * <pre>
       ** key:id,value:累计购买数量 
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; buyTimes = 10;</code>
       */

      public int getBuyTimesOrDefault(
          int key,
          int defaultValue) {
        
        java.util.Map<java.lang.Integer, java.lang.Integer> map =
            internalGetBuyTimes().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <pre>
       ** key:id,value:累计购买数量 
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; buyTimes = 10;</code>
       */

      public int getBuyTimesOrThrow(
          int key) {
        
        java.util.Map<java.lang.Integer, java.lang.Integer> map =
            internalGetBuyTimes().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearBuyTimes() {
        internalGetMutableBuyTimes().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <pre>
       ** key:id,value:累计购买数量 
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; buyTimes = 10;</code>
       */

      public Builder removeBuyTimes(
          int key) {
        
        internalGetMutableBuyTimes().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, java.lang.Integer>
      getMutableBuyTimes() {
        return internalGetMutableBuyTimes().getMutableMap();
      }
      /**
       * <pre>
       ** key:id,value:累计购买数量 
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; buyTimes = 10;</code>
       */
      public Builder putBuyTimes(
          int key,
          int value) {
        
        
        internalGetMutableBuyTimes().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <pre>
       ** key:id,value:累计购买数量 
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; buyTimes = 10;</code>
       */

      public Builder putAllBuyTimes(
          java.util.Map<java.lang.Integer, java.lang.Integer> values) {
        internalGetMutableBuyTimes().getMutableMap()
            .putAll(values);
        return this;
      }

      private com.google.protobuf.MapField<
          java.lang.Integer, java.lang.Integer> receives_;
      private com.google.protobuf.MapField<java.lang.Integer, java.lang.Integer>
      internalGetReceives() {
        if (receives_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              ReceivesDefaultEntryHolder.defaultEntry);
        }
        return receives_;
      }
      private com.google.protobuf.MapField<java.lang.Integer, java.lang.Integer>
      internalGetMutableReceives() {
        onChanged();;
        if (receives_ == null) {
          receives_ = com.google.protobuf.MapField.newMapField(
              ReceivesDefaultEntryHolder.defaultEntry);
        }
        if (!receives_.isMutable()) {
          receives_ = receives_.copy();
        }
        return receives_;
      }

      public int getReceivesCount() {
        return internalGetReceives().getMap().size();
      }
      /**
       * <pre>
       ** key:id,value:累计已领奖励数量 
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; receives = 11;</code>
       */

      public boolean containsReceives(
          int key) {
        
        return internalGetReceives().getMap().containsKey(key);
      }
      /**
       * Use {@link #getReceivesMap()} instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, java.lang.Integer> getReceives() {
        return getReceivesMap();
      }
      /**
       * <pre>
       ** key:id,value:累计已领奖励数量 
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; receives = 11;</code>
       */

      public java.util.Map<java.lang.Integer, java.lang.Integer> getReceivesMap() {
        return internalGetReceives().getMap();
      }
      /**
       * <pre>
       ** key:id,value:累计已领奖励数量 
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; receives = 11;</code>
       */

      public int getReceivesOrDefault(
          int key,
          int defaultValue) {
        
        java.util.Map<java.lang.Integer, java.lang.Integer> map =
            internalGetReceives().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <pre>
       ** key:id,value:累计已领奖励数量 
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; receives = 11;</code>
       */

      public int getReceivesOrThrow(
          int key) {
        
        java.util.Map<java.lang.Integer, java.lang.Integer> map =
            internalGetReceives().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearReceives() {
        internalGetMutableReceives().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <pre>
       ** key:id,value:累计已领奖励数量 
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; receives = 11;</code>
       */

      public Builder removeReceives(
          int key) {
        
        internalGetMutableReceives().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, java.lang.Integer>
      getMutableReceives() {
        return internalGetMutableReceives().getMutableMap();
      }
      /**
       * <pre>
       ** key:id,value:累计已领奖励数量 
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; receives = 11;</code>
       */
      public Builder putReceives(
          int key,
          int value) {
        
        
        internalGetMutableReceives().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <pre>
       ** key:id,value:累计已领奖励数量 
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; receives = 11;</code>
       */

      public Builder putAllReceives(
          java.util.Map<java.lang.Integer, java.lang.Integer> values) {
        internalGetMutableReceives().getMutableMap()
            .putAll(values);
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:BeastGachaInfoResponse)
    }

    // @@protoc_insertion_point(class_scope:BeastGachaInfoResponse)
    private static final cn.daxiang.protocol.game.BeastProtocol.BeastGachaInfoResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new cn.daxiang.protocol.game.BeastProtocol.BeastGachaInfoResponse();
    }

    public static cn.daxiang.protocol.game.BeastProtocol.BeastGachaInfoResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<BeastGachaInfoResponse>
        PARSER = new com.google.protobuf.AbstractParser<BeastGachaInfoResponse>() {
      public BeastGachaInfoResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new BeastGachaInfoResponse(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<BeastGachaInfoResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<BeastGachaInfoResponse> getParserForType() {
      return PARSER;
    }

    public cn.daxiang.protocol.game.BeastProtocol.BeastGachaInfoResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface BeastPrivilegeEntityVOOrBuilder extends
      // @@protoc_insertion_point(interface_extends:BeastPrivilegeEntityVO)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     ** 特权类型 
     * </pre>
     *
     * <code>int32 type = 1;</code>
     */
    int getType();

    /**
     * <pre>
     ** 已领取天数 
     * </pre>
     *
     * <code>int32 count = 2;</code>
     */
    int getCount();

    /**
     * <pre>
     ** 上次领取当前特权奖励时间戳 
     * </pre>
     *
     * <code>int64 lastReceiveTime = 3;</code>
     */
    long getLastReceiveTime();
  }
  /**
   * <pre>
   ** 特权信息体
   * </pre>
   *
   * Protobuf type {@code BeastPrivilegeEntityVO}
   */
  public  static final class BeastPrivilegeEntityVO extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:BeastPrivilegeEntityVO)
      BeastPrivilegeEntityVOOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use BeastPrivilegeEntityVO.newBuilder() to construct.
    private BeastPrivilegeEntityVO(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private BeastPrivilegeEntityVO() {
      type_ = 0;
      count_ = 0;
      lastReceiveTime_ = 0L;
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private BeastPrivilegeEntityVO(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {

              type_ = input.readInt32();
              break;
            }
            case 16: {

              count_ = input.readInt32();
              break;
            }
            case 24: {

              lastReceiveTime_ = input.readInt64();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.BeastProtocol.internal_static_BeastPrivilegeEntityVO_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.daxiang.protocol.game.BeastProtocol.internal_static_BeastPrivilegeEntityVO_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO.class, cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO.Builder.class);
    }

    public static final int TYPE_FIELD_NUMBER = 1;
    private int type_;
    /**
     * <pre>
     ** 特权类型 
     * </pre>
     *
     * <code>int32 type = 1;</code>
     */
    public int getType() {
      return type_;
    }

    public static final int COUNT_FIELD_NUMBER = 2;
    private int count_;
    /**
     * <pre>
     ** 已领取天数 
     * </pre>
     *
     * <code>int32 count = 2;</code>
     */
    public int getCount() {
      return count_;
    }

    public static final int LASTRECEIVETIME_FIELD_NUMBER = 3;
    private long lastReceiveTime_;
    /**
     * <pre>
     ** 上次领取当前特权奖励时间戳 
     * </pre>
     *
     * <code>int64 lastReceiveTime = 3;</code>
     */
    public long getLastReceiveTime() {
      return lastReceiveTime_;
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (type_ != 0) {
        output.writeInt32(1, type_);
      }
      if (count_ != 0) {
        output.writeInt32(2, count_);
      }
      if (lastReceiveTime_ != 0L) {
        output.writeInt64(3, lastReceiveTime_);
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (type_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, type_);
      }
      if (count_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, count_);
      }
      if (lastReceiveTime_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, lastReceiveTime_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO)) {
        return super.equals(obj);
      }
      cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO other = (cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO) obj;

      boolean result = true;
      result = result && (getType()
          == other.getType());
      result = result && (getCount()
          == other.getCount());
      result = result && (getLastReceiveTime()
          == other.getLastReceiveTime());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
      hash = (37 * hash) + COUNT_FIELD_NUMBER;
      hash = (53 * hash) + getCount();
      hash = (37 * hash) + LASTRECEIVETIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getLastReceiveTime());
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** 特权信息体
     * </pre>
     *
     * Protobuf type {@code BeastPrivilegeEntityVO}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:BeastPrivilegeEntityVO)
        cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVOOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.daxiang.protocol.game.BeastProtocol.internal_static_BeastPrivilegeEntityVO_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.daxiang.protocol.game.BeastProtocol.internal_static_BeastPrivilegeEntityVO_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO.class, cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO.Builder.class);
      }

      // Construct using cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        type_ = 0;

        count_ = 0;

        lastReceiveTime_ = 0L;

        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return cn.daxiang.protocol.game.BeastProtocol.internal_static_BeastPrivilegeEntityVO_descriptor;
      }

      public cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO getDefaultInstanceForType() {
        return cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO.getDefaultInstance();
      }

      public cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO build() {
        cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO buildPartial() {
        cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO result = new cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO(this);
        result.type_ = type_;
        result.count_ = count_;
        result.lastReceiveTime_ = lastReceiveTime_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO) {
          return mergeFrom((cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO other) {
        if (other == cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO.getDefaultInstance()) return this;
        if (other.getType() != 0) {
          setType(other.getType());
        }
        if (other.getCount() != 0) {
          setCount(other.getCount());
        }
        if (other.getLastReceiveTime() != 0L) {
          setLastReceiveTime(other.getLastReceiveTime());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int type_ ;
      /**
       * <pre>
       ** 特权类型 
       * </pre>
       *
       * <code>int32 type = 1;</code>
       */
      public int getType() {
        return type_;
      }
      /**
       * <pre>
       ** 特权类型 
       * </pre>
       *
       * <code>int32 type = 1;</code>
       */
      public Builder setType(int value) {
        
        type_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 特权类型 
       * </pre>
       *
       * <code>int32 type = 1;</code>
       */
      public Builder clearType() {
        
        type_ = 0;
        onChanged();
        return this;
      }

      private int count_ ;
      /**
       * <pre>
       ** 已领取天数 
       * </pre>
       *
       * <code>int32 count = 2;</code>
       */
      public int getCount() {
        return count_;
      }
      /**
       * <pre>
       ** 已领取天数 
       * </pre>
       *
       * <code>int32 count = 2;</code>
       */
      public Builder setCount(int value) {
        
        count_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 已领取天数 
       * </pre>
       *
       * <code>int32 count = 2;</code>
       */
      public Builder clearCount() {
        
        count_ = 0;
        onChanged();
        return this;
      }

      private long lastReceiveTime_ ;
      /**
       * <pre>
       ** 上次领取当前特权奖励时间戳 
       * </pre>
       *
       * <code>int64 lastReceiveTime = 3;</code>
       */
      public long getLastReceiveTime() {
        return lastReceiveTime_;
      }
      /**
       * <pre>
       ** 上次领取当前特权奖励时间戳 
       * </pre>
       *
       * <code>int64 lastReceiveTime = 3;</code>
       */
      public Builder setLastReceiveTime(long value) {
        
        lastReceiveTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 上次领取当前特权奖励时间戳 
       * </pre>
       *
       * <code>int64 lastReceiveTime = 3;</code>
       */
      public Builder clearLastReceiveTime() {
        
        lastReceiveTime_ = 0L;
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:BeastPrivilegeEntityVO)
    }

    // @@protoc_insertion_point(class_scope:BeastPrivilegeEntityVO)
    private static final cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO();
    }

    public static cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<BeastPrivilegeEntityVO>
        PARSER = new com.google.protobuf.AbstractParser<BeastPrivilegeEntityVO>() {
      public BeastPrivilegeEntityVO parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new BeastPrivilegeEntityVO(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<BeastPrivilegeEntityVO> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<BeastPrivilegeEntityVO> getParserForType() {
      return PARSER;
    }

    public cn.daxiang.protocol.game.BeastProtocol.BeastPrivilegeEntityVO getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface BeastGachaRecordEntityOrBuilder extends
      // @@protoc_insertion_point(interface_extends:BeastGachaRecordEntity)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     ** 角色属性 
     * </pre>
     *
     * <code>.ActorProfile actorProfile = 1;</code>
     */
    boolean hasActorProfile();
    /**
     * <pre>
     ** 角色属性 
     * </pre>
     *
     * <code>.ActorProfile actorProfile = 1;</code>
     */
    cn.daxiang.protocol.game.CommonProtocol.ActorProfile getActorProfile();
    /**
     * <pre>
     ** 角色属性 
     * </pre>
     *
     * <code>.ActorProfile actorProfile = 1;</code>
     */
    cn.daxiang.protocol.game.CommonProtocol.ActorProfileOrBuilder getActorProfileOrBuilder();

    /**
     * <pre>
     ** 奖励列表 
     * </pre>
     *
     * <code>.RewardObjectList rewards = 2;</code>
     */
    boolean hasRewards();
    /**
     * <pre>
     ** 奖励列表 
     * </pre>
     *
     * <code>.RewardObjectList rewards = 2;</code>
     */
    cn.daxiang.protocol.game.CommonProtocol.RewardObjectList getRewards();
    /**
     * <pre>
     ** 奖励列表 
     * </pre>
     *
     * <code>.RewardObjectList rewards = 2;</code>
     */
    cn.daxiang.protocol.game.CommonProtocol.RewardObjectListOrBuilder getRewardsOrBuilder();

    /**
     * <pre>
     ** 描述 
     * </pre>
     *
     * <code>string des = 3;</code>
     */
    java.lang.String getDes();
    /**
     * <pre>
     ** 描述 
     * </pre>
     *
     * <code>string des = 3;</code>
     */
    com.google.protobuf.ByteString
        getDesBytes();
  }
  /**
   * <pre>
   ** 中奖记录实体 
   * </pre>
   *
   * Protobuf type {@code BeastGachaRecordEntity}
   */
  public  static final class BeastGachaRecordEntity extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:BeastGachaRecordEntity)
      BeastGachaRecordEntityOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use BeastGachaRecordEntity.newBuilder() to construct.
    private BeastGachaRecordEntity(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private BeastGachaRecordEntity() {
      des_ = "";
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private BeastGachaRecordEntity(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              cn.daxiang.protocol.game.CommonProtocol.ActorProfile.Builder subBuilder = null;
              if (actorProfile_ != null) {
                subBuilder = actorProfile_.toBuilder();
              }
              actorProfile_ = input.readMessage(cn.daxiang.protocol.game.CommonProtocol.ActorProfile.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(actorProfile_);
                actorProfile_ = subBuilder.buildPartial();
              }

              break;
            }
            case 18: {
              cn.daxiang.protocol.game.CommonProtocol.RewardObjectList.Builder subBuilder = null;
              if (rewards_ != null) {
                subBuilder = rewards_.toBuilder();
              }
              rewards_ = input.readMessage(cn.daxiang.protocol.game.CommonProtocol.RewardObjectList.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(rewards_);
                rewards_ = subBuilder.buildPartial();
              }

              break;
            }
            case 26: {
              java.lang.String s = input.readStringRequireUtf8();

              des_ = s;
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.BeastProtocol.internal_static_BeastGachaRecordEntity_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.daxiang.protocol.game.BeastProtocol.internal_static_BeastGachaRecordEntity_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity.class, cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity.Builder.class);
    }

    public static final int ACTORPROFILE_FIELD_NUMBER = 1;
    private cn.daxiang.protocol.game.CommonProtocol.ActorProfile actorProfile_;
    /**
     * <pre>
     ** 角色属性 
     * </pre>
     *
     * <code>.ActorProfile actorProfile = 1;</code>
     */
    public boolean hasActorProfile() {
      return actorProfile_ != null;
    }
    /**
     * <pre>
     ** 角色属性 
     * </pre>
     *
     * <code>.ActorProfile actorProfile = 1;</code>
     */
    public cn.daxiang.protocol.game.CommonProtocol.ActorProfile getActorProfile() {
      return actorProfile_ == null ? cn.daxiang.protocol.game.CommonProtocol.ActorProfile.getDefaultInstance() : actorProfile_;
    }
    /**
     * <pre>
     ** 角色属性 
     * </pre>
     *
     * <code>.ActorProfile actorProfile = 1;</code>
     */
    public cn.daxiang.protocol.game.CommonProtocol.ActorProfileOrBuilder getActorProfileOrBuilder() {
      return getActorProfile();
    }

    public static final int REWARDS_FIELD_NUMBER = 2;
    private cn.daxiang.protocol.game.CommonProtocol.RewardObjectList rewards_;
    /**
     * <pre>
     ** 奖励列表 
     * </pre>
     *
     * <code>.RewardObjectList rewards = 2;</code>
     */
    public boolean hasRewards() {
      return rewards_ != null;
    }
    /**
     * <pre>
     ** 奖励列表 
     * </pre>
     *
     * <code>.RewardObjectList rewards = 2;</code>
     */
    public cn.daxiang.protocol.game.CommonProtocol.RewardObjectList getRewards() {
      return rewards_ == null ? cn.daxiang.protocol.game.CommonProtocol.RewardObjectList.getDefaultInstance() : rewards_;
    }
    /**
     * <pre>
     ** 奖励列表 
     * </pre>
     *
     * <code>.RewardObjectList rewards = 2;</code>
     */
    public cn.daxiang.protocol.game.CommonProtocol.RewardObjectListOrBuilder getRewardsOrBuilder() {
      return getRewards();
    }

    public static final int DES_FIELD_NUMBER = 3;
    private volatile java.lang.Object des_;
    /**
     * <pre>
     ** 描述 
     * </pre>
     *
     * <code>string des = 3;</code>
     */
    public java.lang.String getDes() {
      java.lang.Object ref = des_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        des_ = s;
        return s;
      }
    }
    /**
     * <pre>
     ** 描述 
     * </pre>
     *
     * <code>string des = 3;</code>
     */
    public com.google.protobuf.ByteString
        getDesBytes() {
      java.lang.Object ref = des_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        des_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (actorProfile_ != null) {
        output.writeMessage(1, getActorProfile());
      }
      if (rewards_ != null) {
        output.writeMessage(2, getRewards());
      }
      if (!getDesBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, des_);
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (actorProfile_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getActorProfile());
      }
      if (rewards_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getRewards());
      }
      if (!getDesBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, des_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity)) {
        return super.equals(obj);
      }
      cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity other = (cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity) obj;

      boolean result = true;
      result = result && (hasActorProfile() == other.hasActorProfile());
      if (hasActorProfile()) {
        result = result && getActorProfile()
            .equals(other.getActorProfile());
      }
      result = result && (hasRewards() == other.hasRewards());
      if (hasRewards()) {
        result = result && getRewards()
            .equals(other.getRewards());
      }
      result = result && getDes()
          .equals(other.getDes());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasActorProfile()) {
        hash = (37 * hash) + ACTORPROFILE_FIELD_NUMBER;
        hash = (53 * hash) + getActorProfile().hashCode();
      }
      if (hasRewards()) {
        hash = (37 * hash) + REWARDS_FIELD_NUMBER;
        hash = (53 * hash) + getRewards().hashCode();
      }
      hash = (37 * hash) + DES_FIELD_NUMBER;
      hash = (53 * hash) + getDes().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** 中奖记录实体 
     * </pre>
     *
     * Protobuf type {@code BeastGachaRecordEntity}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:BeastGachaRecordEntity)
        cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntityOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.daxiang.protocol.game.BeastProtocol.internal_static_BeastGachaRecordEntity_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.daxiang.protocol.game.BeastProtocol.internal_static_BeastGachaRecordEntity_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity.class, cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity.Builder.class);
      }

      // Construct using cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        if (actorProfileBuilder_ == null) {
          actorProfile_ = null;
        } else {
          actorProfile_ = null;
          actorProfileBuilder_ = null;
        }
        if (rewardsBuilder_ == null) {
          rewards_ = null;
        } else {
          rewards_ = null;
          rewardsBuilder_ = null;
        }
        des_ = "";

        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return cn.daxiang.protocol.game.BeastProtocol.internal_static_BeastGachaRecordEntity_descriptor;
      }

      public cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity getDefaultInstanceForType() {
        return cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity.getDefaultInstance();
      }

      public cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity build() {
        cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity buildPartial() {
        cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity result = new cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity(this);
        if (actorProfileBuilder_ == null) {
          result.actorProfile_ = actorProfile_;
        } else {
          result.actorProfile_ = actorProfileBuilder_.build();
        }
        if (rewardsBuilder_ == null) {
          result.rewards_ = rewards_;
        } else {
          result.rewards_ = rewardsBuilder_.build();
        }
        result.des_ = des_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity) {
          return mergeFrom((cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity other) {
        if (other == cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity.getDefaultInstance()) return this;
        if (other.hasActorProfile()) {
          mergeActorProfile(other.getActorProfile());
        }
        if (other.hasRewards()) {
          mergeRewards(other.getRewards());
        }
        if (!other.getDes().isEmpty()) {
          des_ = other.des_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private cn.daxiang.protocol.game.CommonProtocol.ActorProfile actorProfile_ = null;
      private com.google.protobuf.SingleFieldBuilderV3<
          cn.daxiang.protocol.game.CommonProtocol.ActorProfile, cn.daxiang.protocol.game.CommonProtocol.ActorProfile.Builder, cn.daxiang.protocol.game.CommonProtocol.ActorProfileOrBuilder> actorProfileBuilder_;
      /**
       * <pre>
       ** 角色属性 
       * </pre>
       *
       * <code>.ActorProfile actorProfile = 1;</code>
       */
      public boolean hasActorProfile() {
        return actorProfileBuilder_ != null || actorProfile_ != null;
      }
      /**
       * <pre>
       ** 角色属性 
       * </pre>
       *
       * <code>.ActorProfile actorProfile = 1;</code>
       */
      public cn.daxiang.protocol.game.CommonProtocol.ActorProfile getActorProfile() {
        if (actorProfileBuilder_ == null) {
          return actorProfile_ == null ? cn.daxiang.protocol.game.CommonProtocol.ActorProfile.getDefaultInstance() : actorProfile_;
        } else {
          return actorProfileBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       ** 角色属性 
       * </pre>
       *
       * <code>.ActorProfile actorProfile = 1;</code>
       */
      public Builder setActorProfile(cn.daxiang.protocol.game.CommonProtocol.ActorProfile value) {
        if (actorProfileBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          actorProfile_ = value;
          onChanged();
        } else {
          actorProfileBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <pre>
       ** 角色属性 
       * </pre>
       *
       * <code>.ActorProfile actorProfile = 1;</code>
       */
      public Builder setActorProfile(
          cn.daxiang.protocol.game.CommonProtocol.ActorProfile.Builder builderForValue) {
        if (actorProfileBuilder_ == null) {
          actorProfile_ = builderForValue.build();
          onChanged();
        } else {
          actorProfileBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <pre>
       ** 角色属性 
       * </pre>
       *
       * <code>.ActorProfile actorProfile = 1;</code>
       */
      public Builder mergeActorProfile(cn.daxiang.protocol.game.CommonProtocol.ActorProfile value) {
        if (actorProfileBuilder_ == null) {
          if (actorProfile_ != null) {
            actorProfile_ =
              cn.daxiang.protocol.game.CommonProtocol.ActorProfile.newBuilder(actorProfile_).mergeFrom(value).buildPartial();
          } else {
            actorProfile_ = value;
          }
          onChanged();
        } else {
          actorProfileBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <pre>
       ** 角色属性 
       * </pre>
       *
       * <code>.ActorProfile actorProfile = 1;</code>
       */
      public Builder clearActorProfile() {
        if (actorProfileBuilder_ == null) {
          actorProfile_ = null;
          onChanged();
        } else {
          actorProfile_ = null;
          actorProfileBuilder_ = null;
        }

        return this;
      }
      /**
       * <pre>
       ** 角色属性 
       * </pre>
       *
       * <code>.ActorProfile actorProfile = 1;</code>
       */
      public cn.daxiang.protocol.game.CommonProtocol.ActorProfile.Builder getActorProfileBuilder() {
        
        onChanged();
        return getActorProfileFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       ** 角色属性 
       * </pre>
       *
       * <code>.ActorProfile actorProfile = 1;</code>
       */
      public cn.daxiang.protocol.game.CommonProtocol.ActorProfileOrBuilder getActorProfileOrBuilder() {
        if (actorProfileBuilder_ != null) {
          return actorProfileBuilder_.getMessageOrBuilder();
        } else {
          return actorProfile_ == null ?
              cn.daxiang.protocol.game.CommonProtocol.ActorProfile.getDefaultInstance() : actorProfile_;
        }
      }
      /**
       * <pre>
       ** 角色属性 
       * </pre>
       *
       * <code>.ActorProfile actorProfile = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          cn.daxiang.protocol.game.CommonProtocol.ActorProfile, cn.daxiang.protocol.game.CommonProtocol.ActorProfile.Builder, cn.daxiang.protocol.game.CommonProtocol.ActorProfileOrBuilder> 
          getActorProfileFieldBuilder() {
        if (actorProfileBuilder_ == null) {
          actorProfileBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              cn.daxiang.protocol.game.CommonProtocol.ActorProfile, cn.daxiang.protocol.game.CommonProtocol.ActorProfile.Builder, cn.daxiang.protocol.game.CommonProtocol.ActorProfileOrBuilder>(
                  getActorProfile(),
                  getParentForChildren(),
                  isClean());
          actorProfile_ = null;
        }
        return actorProfileBuilder_;
      }

      private cn.daxiang.protocol.game.CommonProtocol.RewardObjectList rewards_ = null;
      private com.google.protobuf.SingleFieldBuilderV3<
          cn.daxiang.protocol.game.CommonProtocol.RewardObjectList, cn.daxiang.protocol.game.CommonProtocol.RewardObjectList.Builder, cn.daxiang.protocol.game.CommonProtocol.RewardObjectListOrBuilder> rewardsBuilder_;
      /**
       * <pre>
       ** 奖励列表 
       * </pre>
       *
       * <code>.RewardObjectList rewards = 2;</code>
       */
      public boolean hasRewards() {
        return rewardsBuilder_ != null || rewards_ != null;
      }
      /**
       * <pre>
       ** 奖励列表 
       * </pre>
       *
       * <code>.RewardObjectList rewards = 2;</code>
       */
      public cn.daxiang.protocol.game.CommonProtocol.RewardObjectList getRewards() {
        if (rewardsBuilder_ == null) {
          return rewards_ == null ? cn.daxiang.protocol.game.CommonProtocol.RewardObjectList.getDefaultInstance() : rewards_;
        } else {
          return rewardsBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       ** 奖励列表 
       * </pre>
       *
       * <code>.RewardObjectList rewards = 2;</code>
       */
      public Builder setRewards(cn.daxiang.protocol.game.CommonProtocol.RewardObjectList value) {
        if (rewardsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          rewards_ = value;
          onChanged();
        } else {
          rewardsBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <pre>
       ** 奖励列表 
       * </pre>
       *
       * <code>.RewardObjectList rewards = 2;</code>
       */
      public Builder setRewards(
          cn.daxiang.protocol.game.CommonProtocol.RewardObjectList.Builder builderForValue) {
        if (rewardsBuilder_ == null) {
          rewards_ = builderForValue.build();
          onChanged();
        } else {
          rewardsBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <pre>
       ** 奖励列表 
       * </pre>
       *
       * <code>.RewardObjectList rewards = 2;</code>
       */
      public Builder mergeRewards(cn.daxiang.protocol.game.CommonProtocol.RewardObjectList value) {
        if (rewardsBuilder_ == null) {
          if (rewards_ != null) {
            rewards_ =
              cn.daxiang.protocol.game.CommonProtocol.RewardObjectList.newBuilder(rewards_).mergeFrom(value).buildPartial();
          } else {
            rewards_ = value;
          }
          onChanged();
        } else {
          rewardsBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <pre>
       ** 奖励列表 
       * </pre>
       *
       * <code>.RewardObjectList rewards = 2;</code>
       */
      public Builder clearRewards() {
        if (rewardsBuilder_ == null) {
          rewards_ = null;
          onChanged();
        } else {
          rewards_ = null;
          rewardsBuilder_ = null;
        }

        return this;
      }
      /**
       * <pre>
       ** 奖励列表 
       * </pre>
       *
       * <code>.RewardObjectList rewards = 2;</code>
       */
      public cn.daxiang.protocol.game.CommonProtocol.RewardObjectList.Builder getRewardsBuilder() {
        
        onChanged();
        return getRewardsFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       ** 奖励列表 
       * </pre>
       *
       * <code>.RewardObjectList rewards = 2;</code>
       */
      public cn.daxiang.protocol.game.CommonProtocol.RewardObjectListOrBuilder getRewardsOrBuilder() {
        if (rewardsBuilder_ != null) {
          return rewardsBuilder_.getMessageOrBuilder();
        } else {
          return rewards_ == null ?
              cn.daxiang.protocol.game.CommonProtocol.RewardObjectList.getDefaultInstance() : rewards_;
        }
      }
      /**
       * <pre>
       ** 奖励列表 
       * </pre>
       *
       * <code>.RewardObjectList rewards = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          cn.daxiang.protocol.game.CommonProtocol.RewardObjectList, cn.daxiang.protocol.game.CommonProtocol.RewardObjectList.Builder, cn.daxiang.protocol.game.CommonProtocol.RewardObjectListOrBuilder> 
          getRewardsFieldBuilder() {
        if (rewardsBuilder_ == null) {
          rewardsBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              cn.daxiang.protocol.game.CommonProtocol.RewardObjectList, cn.daxiang.protocol.game.CommonProtocol.RewardObjectList.Builder, cn.daxiang.protocol.game.CommonProtocol.RewardObjectListOrBuilder>(
                  getRewards(),
                  getParentForChildren(),
                  isClean());
          rewards_ = null;
        }
        return rewardsBuilder_;
      }

      private java.lang.Object des_ = "";
      /**
       * <pre>
       ** 描述 
       * </pre>
       *
       * <code>string des = 3;</code>
       */
      public java.lang.String getDes() {
        java.lang.Object ref = des_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          des_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       ** 描述 
       * </pre>
       *
       * <code>string des = 3;</code>
       */
      public com.google.protobuf.ByteString
          getDesBytes() {
        java.lang.Object ref = des_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          des_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       ** 描述 
       * </pre>
       *
       * <code>string des = 3;</code>
       */
      public Builder setDes(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        des_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 描述 
       * </pre>
       *
       * <code>string des = 3;</code>
       */
      public Builder clearDes() {
        
        des_ = getDefaultInstance().getDes();
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 描述 
       * </pre>
       *
       * <code>string des = 3;</code>
       */
      public Builder setDesBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        des_ = value;
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:BeastGachaRecordEntity)
    }

    // @@protoc_insertion_point(class_scope:BeastGachaRecordEntity)
    private static final cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity();
    }

    public static cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<BeastGachaRecordEntity>
        PARSER = new com.google.protobuf.AbstractParser<BeastGachaRecordEntity>() {
      public BeastGachaRecordEntity parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new BeastGachaRecordEntity(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<BeastGachaRecordEntity> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<BeastGachaRecordEntity> getParserForType() {
      return PARSER;
    }

    public cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface BeastGachaReportResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:BeastGachaReportResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .BeastGachaRecordEntity list = 1;</code>
     */
    java.util.List<cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity> 
        getListList();
    /**
     * <code>repeated .BeastGachaRecordEntity list = 1;</code>
     */
    cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity getList(int index);
    /**
     * <code>repeated .BeastGachaRecordEntity list = 1;</code>
     */
    int getListCount();
    /**
     * <code>repeated .BeastGachaRecordEntity list = 1;</code>
     */
    java.util.List<? extends cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntityOrBuilder> 
        getListOrBuilderList();
    /**
     * <code>repeated .BeastGachaRecordEntity list = 1;</code>
     */
    cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntityOrBuilder getListOrBuilder(
        int index);
  }
  /**
   * <pre>
   ** 神兽召唤中奖播报响应体 
   * </pre>
   *
   * Protobuf type {@code BeastGachaReportResponse}
   */
  public  static final class BeastGachaReportResponse extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:BeastGachaReportResponse)
      BeastGachaReportResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use BeastGachaReportResponse.newBuilder() to construct.
    private BeastGachaReportResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private BeastGachaReportResponse() {
      list_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private BeastGachaReportResponse(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
                list_ = new java.util.ArrayList<cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity>();
                mutable_bitField0_ |= 0x00000001;
              }
              list_.add(
                  input.readMessage(cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity.parser(), extensionRegistry));
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
          list_ = java.util.Collections.unmodifiableList(list_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.BeastProtocol.internal_static_BeastGachaReportResponse_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.daxiang.protocol.game.BeastProtocol.internal_static_BeastGachaReportResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.daxiang.protocol.game.BeastProtocol.BeastGachaReportResponse.class, cn.daxiang.protocol.game.BeastProtocol.BeastGachaReportResponse.Builder.class);
    }

    public static final int LIST_FIELD_NUMBER = 1;
    private java.util.List<cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity> list_;
    /**
     * <code>repeated .BeastGachaRecordEntity list = 1;</code>
     */
    public java.util.List<cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity> getListList() {
      return list_;
    }
    /**
     * <code>repeated .BeastGachaRecordEntity list = 1;</code>
     */
    public java.util.List<? extends cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntityOrBuilder> 
        getListOrBuilderList() {
      return list_;
    }
    /**
     * <code>repeated .BeastGachaRecordEntity list = 1;</code>
     */
    public int getListCount() {
      return list_.size();
    }
    /**
     * <code>repeated .BeastGachaRecordEntity list = 1;</code>
     */
    public cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity getList(int index) {
      return list_.get(index);
    }
    /**
     * <code>repeated .BeastGachaRecordEntity list = 1;</code>
     */
    public cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntityOrBuilder getListOrBuilder(
        int index) {
      return list_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < list_.size(); i++) {
        output.writeMessage(1, list_.get(i));
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < list_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, list_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof cn.daxiang.protocol.game.BeastProtocol.BeastGachaReportResponse)) {
        return super.equals(obj);
      }
      cn.daxiang.protocol.game.BeastProtocol.BeastGachaReportResponse other = (cn.daxiang.protocol.game.BeastProtocol.BeastGachaReportResponse) obj;

      boolean result = true;
      result = result && getListList()
          .equals(other.getListList());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getListCount() > 0) {
        hash = (37 * hash) + LIST_FIELD_NUMBER;
        hash = (53 * hash) + getListList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static cn.daxiang.protocol.game.BeastProtocol.BeastGachaReportResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastGachaReportResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastGachaReportResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastGachaReportResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastGachaReportResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastGachaReportResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastGachaReportResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastGachaReportResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastGachaReportResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastGachaReportResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastGachaReportResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastGachaReportResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(cn.daxiang.protocol.game.BeastProtocol.BeastGachaReportResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** 神兽召唤中奖播报响应体 
     * </pre>
     *
     * Protobuf type {@code BeastGachaReportResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:BeastGachaReportResponse)
        cn.daxiang.protocol.game.BeastProtocol.BeastGachaReportResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.daxiang.protocol.game.BeastProtocol.internal_static_BeastGachaReportResponse_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.daxiang.protocol.game.BeastProtocol.internal_static_BeastGachaReportResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.daxiang.protocol.game.BeastProtocol.BeastGachaReportResponse.class, cn.daxiang.protocol.game.BeastProtocol.BeastGachaReportResponse.Builder.class);
      }

      // Construct using cn.daxiang.protocol.game.BeastProtocol.BeastGachaReportResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getListFieldBuilder();
        }
      }
      public Builder clear() {
        super.clear();
        if (listBuilder_ == null) {
          list_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          listBuilder_.clear();
        }
        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return cn.daxiang.protocol.game.BeastProtocol.internal_static_BeastGachaReportResponse_descriptor;
      }

      public cn.daxiang.protocol.game.BeastProtocol.BeastGachaReportResponse getDefaultInstanceForType() {
        return cn.daxiang.protocol.game.BeastProtocol.BeastGachaReportResponse.getDefaultInstance();
      }

      public cn.daxiang.protocol.game.BeastProtocol.BeastGachaReportResponse build() {
        cn.daxiang.protocol.game.BeastProtocol.BeastGachaReportResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public cn.daxiang.protocol.game.BeastProtocol.BeastGachaReportResponse buildPartial() {
        cn.daxiang.protocol.game.BeastProtocol.BeastGachaReportResponse result = new cn.daxiang.protocol.game.BeastProtocol.BeastGachaReportResponse(this);
        int from_bitField0_ = bitField0_;
        if (listBuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001)) {
            list_ = java.util.Collections.unmodifiableList(list_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.list_ = list_;
        } else {
          result.list_ = listBuilder_.build();
        }
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof cn.daxiang.protocol.game.BeastProtocol.BeastGachaReportResponse) {
          return mergeFrom((cn.daxiang.protocol.game.BeastProtocol.BeastGachaReportResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(cn.daxiang.protocol.game.BeastProtocol.BeastGachaReportResponse other) {
        if (other == cn.daxiang.protocol.game.BeastProtocol.BeastGachaReportResponse.getDefaultInstance()) return this;
        if (listBuilder_ == null) {
          if (!other.list_.isEmpty()) {
            if (list_.isEmpty()) {
              list_ = other.list_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureListIsMutable();
              list_.addAll(other.list_);
            }
            onChanged();
          }
        } else {
          if (!other.list_.isEmpty()) {
            if (listBuilder_.isEmpty()) {
              listBuilder_.dispose();
              listBuilder_ = null;
              list_ = other.list_;
              bitField0_ = (bitField0_ & ~0x00000001);
              listBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getListFieldBuilder() : null;
            } else {
              listBuilder_.addAllMessages(other.list_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        cn.daxiang.protocol.game.BeastProtocol.BeastGachaReportResponse parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (cn.daxiang.protocol.game.BeastProtocol.BeastGachaReportResponse) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity> list_ =
        java.util.Collections.emptyList();
      private void ensureListIsMutable() {
        if (!((bitField0_ & 0x00000001) == 0x00000001)) {
          list_ = new java.util.ArrayList<cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity>(list_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity, cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity.Builder, cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntityOrBuilder> listBuilder_;

      /**
       * <code>repeated .BeastGachaRecordEntity list = 1;</code>
       */
      public java.util.List<cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity> getListList() {
        if (listBuilder_ == null) {
          return java.util.Collections.unmodifiableList(list_);
        } else {
          return listBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .BeastGachaRecordEntity list = 1;</code>
       */
      public int getListCount() {
        if (listBuilder_ == null) {
          return list_.size();
        } else {
          return listBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .BeastGachaRecordEntity list = 1;</code>
       */
      public cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity getList(int index) {
        if (listBuilder_ == null) {
          return list_.get(index);
        } else {
          return listBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .BeastGachaRecordEntity list = 1;</code>
       */
      public Builder setList(
          int index, cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity value) {
        if (listBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureListIsMutable();
          list_.set(index, value);
          onChanged();
        } else {
          listBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .BeastGachaRecordEntity list = 1;</code>
       */
      public Builder setList(
          int index, cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity.Builder builderForValue) {
        if (listBuilder_ == null) {
          ensureListIsMutable();
          list_.set(index, builderForValue.build());
          onChanged();
        } else {
          listBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .BeastGachaRecordEntity list = 1;</code>
       */
      public Builder addList(cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity value) {
        if (listBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureListIsMutable();
          list_.add(value);
          onChanged();
        } else {
          listBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .BeastGachaRecordEntity list = 1;</code>
       */
      public Builder addList(
          int index, cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity value) {
        if (listBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureListIsMutable();
          list_.add(index, value);
          onChanged();
        } else {
          listBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .BeastGachaRecordEntity list = 1;</code>
       */
      public Builder addList(
          cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity.Builder builderForValue) {
        if (listBuilder_ == null) {
          ensureListIsMutable();
          list_.add(builderForValue.build());
          onChanged();
        } else {
          listBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .BeastGachaRecordEntity list = 1;</code>
       */
      public Builder addList(
          int index, cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity.Builder builderForValue) {
        if (listBuilder_ == null) {
          ensureListIsMutable();
          list_.add(index, builderForValue.build());
          onChanged();
        } else {
          listBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .BeastGachaRecordEntity list = 1;</code>
       */
      public Builder addAllList(
          java.lang.Iterable<? extends cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity> values) {
        if (listBuilder_ == null) {
          ensureListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, list_);
          onChanged();
        } else {
          listBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .BeastGachaRecordEntity list = 1;</code>
       */
      public Builder clearList() {
        if (listBuilder_ == null) {
          list_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          listBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .BeastGachaRecordEntity list = 1;</code>
       */
      public Builder removeList(int index) {
        if (listBuilder_ == null) {
          ensureListIsMutable();
          list_.remove(index);
          onChanged();
        } else {
          listBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .BeastGachaRecordEntity list = 1;</code>
       */
      public cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity.Builder getListBuilder(
          int index) {
        return getListFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .BeastGachaRecordEntity list = 1;</code>
       */
      public cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntityOrBuilder getListOrBuilder(
          int index) {
        if (listBuilder_ == null) {
          return list_.get(index);  } else {
          return listBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .BeastGachaRecordEntity list = 1;</code>
       */
      public java.util.List<? extends cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntityOrBuilder> 
           getListOrBuilderList() {
        if (listBuilder_ != null) {
          return listBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(list_);
        }
      }
      /**
       * <code>repeated .BeastGachaRecordEntity list = 1;</code>
       */
      public cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity.Builder addListBuilder() {
        return getListFieldBuilder().addBuilder(
            cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity.getDefaultInstance());
      }
      /**
       * <code>repeated .BeastGachaRecordEntity list = 1;</code>
       */
      public cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity.Builder addListBuilder(
          int index) {
        return getListFieldBuilder().addBuilder(
            index, cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity.getDefaultInstance());
      }
      /**
       * <code>repeated .BeastGachaRecordEntity list = 1;</code>
       */
      public java.util.List<cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity.Builder> 
           getListBuilderList() {
        return getListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity, cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity.Builder, cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntityOrBuilder> 
          getListFieldBuilder() {
        if (listBuilder_ == null) {
          listBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity, cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntity.Builder, cn.daxiang.protocol.game.BeastProtocol.BeastGachaRecordEntityOrBuilder>(
                  list_,
                  ((bitField0_ & 0x00000001) == 0x00000001),
                  getParentForChildren(),
                  isClean());
          list_ = null;
        }
        return listBuilder_;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:BeastGachaReportResponse)
    }

    // @@protoc_insertion_point(class_scope:BeastGachaReportResponse)
    private static final cn.daxiang.protocol.game.BeastProtocol.BeastGachaReportResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new cn.daxiang.protocol.game.BeastProtocol.BeastGachaReportResponse();
    }

    public static cn.daxiang.protocol.game.BeastProtocol.BeastGachaReportResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<BeastGachaReportResponse>
        PARSER = new com.google.protobuf.AbstractParser<BeastGachaReportResponse>() {
      public BeastGachaReportResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new BeastGachaReportResponse(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<BeastGachaReportResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<BeastGachaReportResponse> getParserForType() {
      return PARSER;
    }

    public cn.daxiang.protocol.game.BeastProtocol.BeastGachaReportResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface BeastGachaResultResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:BeastGachaResultResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     ** 抽奖奖励结果 
     * </pre>
     *
     * <code>.RewardObjectList gachaRewards = 1;</code>
     */
    boolean hasGachaRewards();
    /**
     * <pre>
     ** 抽奖奖励结果 
     * </pre>
     *
     * <code>.RewardObjectList gachaRewards = 1;</code>
     */
    cn.daxiang.protocol.game.CommonProtocol.RewardObjectList getGachaRewards();
    /**
     * <pre>
     ** 抽奖奖励结果 
     * </pre>
     *
     * <code>.RewardObjectList gachaRewards = 1;</code>
     */
    cn.daxiang.protocol.game.CommonProtocol.RewardObjectListOrBuilder getGachaRewardsOrBuilder();

    /**
     * <pre>
     ** 幸运值 
     * </pre>
     *
     * <code>int32 lucky = 2;</code>
     */
    int getLucky();

    /**
     * <pre>
     ** 基础奖励结果 
     * </pre>
     *
     * <code>.RewardObjectList baseRewards = 3;</code>
     */
    boolean hasBaseRewards();
    /**
     * <pre>
     ** 基础奖励结果 
     * </pre>
     *
     * <code>.RewardObjectList baseRewards = 3;</code>
     */
    cn.daxiang.protocol.game.CommonProtocol.RewardObjectList getBaseRewards();
    /**
     * <pre>
     ** 基础奖励结果 
     * </pre>
     *
     * <code>.RewardObjectList baseRewards = 3;</code>
     */
    cn.daxiang.protocol.game.CommonProtocol.RewardObjectListOrBuilder getBaseRewardsOrBuilder();
  }
  /**
   * <pre>
   ** 神兽召唤结果响应体 
   * </pre>
   *
   * Protobuf type {@code BeastGachaResultResponse}
   */
  public  static final class BeastGachaResultResponse extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:BeastGachaResultResponse)
      BeastGachaResultResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use BeastGachaResultResponse.newBuilder() to construct.
    private BeastGachaResultResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private BeastGachaResultResponse() {
      lucky_ = 0;
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private BeastGachaResultResponse(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              cn.daxiang.protocol.game.CommonProtocol.RewardObjectList.Builder subBuilder = null;
              if (gachaRewards_ != null) {
                subBuilder = gachaRewards_.toBuilder();
              }
              gachaRewards_ = input.readMessage(cn.daxiang.protocol.game.CommonProtocol.RewardObjectList.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(gachaRewards_);
                gachaRewards_ = subBuilder.buildPartial();
              }

              break;
            }
            case 16: {

              lucky_ = input.readInt32();
              break;
            }
            case 26: {
              cn.daxiang.protocol.game.CommonProtocol.RewardObjectList.Builder subBuilder = null;
              if (baseRewards_ != null) {
                subBuilder = baseRewards_.toBuilder();
              }
              baseRewards_ = input.readMessage(cn.daxiang.protocol.game.CommonProtocol.RewardObjectList.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(baseRewards_);
                baseRewards_ = subBuilder.buildPartial();
              }

              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.BeastProtocol.internal_static_BeastGachaResultResponse_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.daxiang.protocol.game.BeastProtocol.internal_static_BeastGachaResultResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.daxiang.protocol.game.BeastProtocol.BeastGachaResultResponse.class, cn.daxiang.protocol.game.BeastProtocol.BeastGachaResultResponse.Builder.class);
    }

    public static final int GACHAREWARDS_FIELD_NUMBER = 1;
    private cn.daxiang.protocol.game.CommonProtocol.RewardObjectList gachaRewards_;
    /**
     * <pre>
     ** 抽奖奖励结果 
     * </pre>
     *
     * <code>.RewardObjectList gachaRewards = 1;</code>
     */
    public boolean hasGachaRewards() {
      return gachaRewards_ != null;
    }
    /**
     * <pre>
     ** 抽奖奖励结果 
     * </pre>
     *
     * <code>.RewardObjectList gachaRewards = 1;</code>
     */
    public cn.daxiang.protocol.game.CommonProtocol.RewardObjectList getGachaRewards() {
      return gachaRewards_ == null ? cn.daxiang.protocol.game.CommonProtocol.RewardObjectList.getDefaultInstance() : gachaRewards_;
    }
    /**
     * <pre>
     ** 抽奖奖励结果 
     * </pre>
     *
     * <code>.RewardObjectList gachaRewards = 1;</code>
     */
    public cn.daxiang.protocol.game.CommonProtocol.RewardObjectListOrBuilder getGachaRewardsOrBuilder() {
      return getGachaRewards();
    }

    public static final int LUCKY_FIELD_NUMBER = 2;
    private int lucky_;
    /**
     * <pre>
     ** 幸运值 
     * </pre>
     *
     * <code>int32 lucky = 2;</code>
     */
    public int getLucky() {
      return lucky_;
    }

    public static final int BASEREWARDS_FIELD_NUMBER = 3;
    private cn.daxiang.protocol.game.CommonProtocol.RewardObjectList baseRewards_;
    /**
     * <pre>
     ** 基础奖励结果 
     * </pre>
     *
     * <code>.RewardObjectList baseRewards = 3;</code>
     */
    public boolean hasBaseRewards() {
      return baseRewards_ != null;
    }
    /**
     * <pre>
     ** 基础奖励结果 
     * </pre>
     *
     * <code>.RewardObjectList baseRewards = 3;</code>
     */
    public cn.daxiang.protocol.game.CommonProtocol.RewardObjectList getBaseRewards() {
      return baseRewards_ == null ? cn.daxiang.protocol.game.CommonProtocol.RewardObjectList.getDefaultInstance() : baseRewards_;
    }
    /**
     * <pre>
     ** 基础奖励结果 
     * </pre>
     *
     * <code>.RewardObjectList baseRewards = 3;</code>
     */
    public cn.daxiang.protocol.game.CommonProtocol.RewardObjectListOrBuilder getBaseRewardsOrBuilder() {
      return getBaseRewards();
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (gachaRewards_ != null) {
        output.writeMessage(1, getGachaRewards());
      }
      if (lucky_ != 0) {
        output.writeInt32(2, lucky_);
      }
      if (baseRewards_ != null) {
        output.writeMessage(3, getBaseRewards());
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (gachaRewards_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getGachaRewards());
      }
      if (lucky_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, lucky_);
      }
      if (baseRewards_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, getBaseRewards());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof cn.daxiang.protocol.game.BeastProtocol.BeastGachaResultResponse)) {
        return super.equals(obj);
      }
      cn.daxiang.protocol.game.BeastProtocol.BeastGachaResultResponse other = (cn.daxiang.protocol.game.BeastProtocol.BeastGachaResultResponse) obj;

      boolean result = true;
      result = result && (hasGachaRewards() == other.hasGachaRewards());
      if (hasGachaRewards()) {
        result = result && getGachaRewards()
            .equals(other.getGachaRewards());
      }
      result = result && (getLucky()
          == other.getLucky());
      result = result && (hasBaseRewards() == other.hasBaseRewards());
      if (hasBaseRewards()) {
        result = result && getBaseRewards()
            .equals(other.getBaseRewards());
      }
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasGachaRewards()) {
        hash = (37 * hash) + GACHAREWARDS_FIELD_NUMBER;
        hash = (53 * hash) + getGachaRewards().hashCode();
      }
      hash = (37 * hash) + LUCKY_FIELD_NUMBER;
      hash = (53 * hash) + getLucky();
      if (hasBaseRewards()) {
        hash = (37 * hash) + BASEREWARDS_FIELD_NUMBER;
        hash = (53 * hash) + getBaseRewards().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static cn.daxiang.protocol.game.BeastProtocol.BeastGachaResultResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastGachaResultResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastGachaResultResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastGachaResultResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastGachaResultResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastGachaResultResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastGachaResultResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastGachaResultResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastGachaResultResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastGachaResultResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastGachaResultResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastGachaResultResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(cn.daxiang.protocol.game.BeastProtocol.BeastGachaResultResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** 神兽召唤结果响应体 
     * </pre>
     *
     * Protobuf type {@code BeastGachaResultResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:BeastGachaResultResponse)
        cn.daxiang.protocol.game.BeastProtocol.BeastGachaResultResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.daxiang.protocol.game.BeastProtocol.internal_static_BeastGachaResultResponse_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.daxiang.protocol.game.BeastProtocol.internal_static_BeastGachaResultResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.daxiang.protocol.game.BeastProtocol.BeastGachaResultResponse.class, cn.daxiang.protocol.game.BeastProtocol.BeastGachaResultResponse.Builder.class);
      }

      // Construct using cn.daxiang.protocol.game.BeastProtocol.BeastGachaResultResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        if (gachaRewardsBuilder_ == null) {
          gachaRewards_ = null;
        } else {
          gachaRewards_ = null;
          gachaRewardsBuilder_ = null;
        }
        lucky_ = 0;

        if (baseRewardsBuilder_ == null) {
          baseRewards_ = null;
        } else {
          baseRewards_ = null;
          baseRewardsBuilder_ = null;
        }
        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return cn.daxiang.protocol.game.BeastProtocol.internal_static_BeastGachaResultResponse_descriptor;
      }

      public cn.daxiang.protocol.game.BeastProtocol.BeastGachaResultResponse getDefaultInstanceForType() {
        return cn.daxiang.protocol.game.BeastProtocol.BeastGachaResultResponse.getDefaultInstance();
      }

      public cn.daxiang.protocol.game.BeastProtocol.BeastGachaResultResponse build() {
        cn.daxiang.protocol.game.BeastProtocol.BeastGachaResultResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public cn.daxiang.protocol.game.BeastProtocol.BeastGachaResultResponse buildPartial() {
        cn.daxiang.protocol.game.BeastProtocol.BeastGachaResultResponse result = new cn.daxiang.protocol.game.BeastProtocol.BeastGachaResultResponse(this);
        if (gachaRewardsBuilder_ == null) {
          result.gachaRewards_ = gachaRewards_;
        } else {
          result.gachaRewards_ = gachaRewardsBuilder_.build();
        }
        result.lucky_ = lucky_;
        if (baseRewardsBuilder_ == null) {
          result.baseRewards_ = baseRewards_;
        } else {
          result.baseRewards_ = baseRewardsBuilder_.build();
        }
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof cn.daxiang.protocol.game.BeastProtocol.BeastGachaResultResponse) {
          return mergeFrom((cn.daxiang.protocol.game.BeastProtocol.BeastGachaResultResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(cn.daxiang.protocol.game.BeastProtocol.BeastGachaResultResponse other) {
        if (other == cn.daxiang.protocol.game.BeastProtocol.BeastGachaResultResponse.getDefaultInstance()) return this;
        if (other.hasGachaRewards()) {
          mergeGachaRewards(other.getGachaRewards());
        }
        if (other.getLucky() != 0) {
          setLucky(other.getLucky());
        }
        if (other.hasBaseRewards()) {
          mergeBaseRewards(other.getBaseRewards());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        cn.daxiang.protocol.game.BeastProtocol.BeastGachaResultResponse parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (cn.daxiang.protocol.game.BeastProtocol.BeastGachaResultResponse) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private cn.daxiang.protocol.game.CommonProtocol.RewardObjectList gachaRewards_ = null;
      private com.google.protobuf.SingleFieldBuilderV3<
          cn.daxiang.protocol.game.CommonProtocol.RewardObjectList, cn.daxiang.protocol.game.CommonProtocol.RewardObjectList.Builder, cn.daxiang.protocol.game.CommonProtocol.RewardObjectListOrBuilder> gachaRewardsBuilder_;
      /**
       * <pre>
       ** 抽奖奖励结果 
       * </pre>
       *
       * <code>.RewardObjectList gachaRewards = 1;</code>
       */
      public boolean hasGachaRewards() {
        return gachaRewardsBuilder_ != null || gachaRewards_ != null;
      }
      /**
       * <pre>
       ** 抽奖奖励结果 
       * </pre>
       *
       * <code>.RewardObjectList gachaRewards = 1;</code>
       */
      public cn.daxiang.protocol.game.CommonProtocol.RewardObjectList getGachaRewards() {
        if (gachaRewardsBuilder_ == null) {
          return gachaRewards_ == null ? cn.daxiang.protocol.game.CommonProtocol.RewardObjectList.getDefaultInstance() : gachaRewards_;
        } else {
          return gachaRewardsBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       ** 抽奖奖励结果 
       * </pre>
       *
       * <code>.RewardObjectList gachaRewards = 1;</code>
       */
      public Builder setGachaRewards(cn.daxiang.protocol.game.CommonProtocol.RewardObjectList value) {
        if (gachaRewardsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          gachaRewards_ = value;
          onChanged();
        } else {
          gachaRewardsBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <pre>
       ** 抽奖奖励结果 
       * </pre>
       *
       * <code>.RewardObjectList gachaRewards = 1;</code>
       */
      public Builder setGachaRewards(
          cn.daxiang.protocol.game.CommonProtocol.RewardObjectList.Builder builderForValue) {
        if (gachaRewardsBuilder_ == null) {
          gachaRewards_ = builderForValue.build();
          onChanged();
        } else {
          gachaRewardsBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <pre>
       ** 抽奖奖励结果 
       * </pre>
       *
       * <code>.RewardObjectList gachaRewards = 1;</code>
       */
      public Builder mergeGachaRewards(cn.daxiang.protocol.game.CommonProtocol.RewardObjectList value) {
        if (gachaRewardsBuilder_ == null) {
          if (gachaRewards_ != null) {
            gachaRewards_ =
              cn.daxiang.protocol.game.CommonProtocol.RewardObjectList.newBuilder(gachaRewards_).mergeFrom(value).buildPartial();
          } else {
            gachaRewards_ = value;
          }
          onChanged();
        } else {
          gachaRewardsBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <pre>
       ** 抽奖奖励结果 
       * </pre>
       *
       * <code>.RewardObjectList gachaRewards = 1;</code>
       */
      public Builder clearGachaRewards() {
        if (gachaRewardsBuilder_ == null) {
          gachaRewards_ = null;
          onChanged();
        } else {
          gachaRewards_ = null;
          gachaRewardsBuilder_ = null;
        }

        return this;
      }
      /**
       * <pre>
       ** 抽奖奖励结果 
       * </pre>
       *
       * <code>.RewardObjectList gachaRewards = 1;</code>
       */
      public cn.daxiang.protocol.game.CommonProtocol.RewardObjectList.Builder getGachaRewardsBuilder() {
        
        onChanged();
        return getGachaRewardsFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       ** 抽奖奖励结果 
       * </pre>
       *
       * <code>.RewardObjectList gachaRewards = 1;</code>
       */
      public cn.daxiang.protocol.game.CommonProtocol.RewardObjectListOrBuilder getGachaRewardsOrBuilder() {
        if (gachaRewardsBuilder_ != null) {
          return gachaRewardsBuilder_.getMessageOrBuilder();
        } else {
          return gachaRewards_ == null ?
              cn.daxiang.protocol.game.CommonProtocol.RewardObjectList.getDefaultInstance() : gachaRewards_;
        }
      }
      /**
       * <pre>
       ** 抽奖奖励结果 
       * </pre>
       *
       * <code>.RewardObjectList gachaRewards = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          cn.daxiang.protocol.game.CommonProtocol.RewardObjectList, cn.daxiang.protocol.game.CommonProtocol.RewardObjectList.Builder, cn.daxiang.protocol.game.CommonProtocol.RewardObjectListOrBuilder> 
          getGachaRewardsFieldBuilder() {
        if (gachaRewardsBuilder_ == null) {
          gachaRewardsBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              cn.daxiang.protocol.game.CommonProtocol.RewardObjectList, cn.daxiang.protocol.game.CommonProtocol.RewardObjectList.Builder, cn.daxiang.protocol.game.CommonProtocol.RewardObjectListOrBuilder>(
                  getGachaRewards(),
                  getParentForChildren(),
                  isClean());
          gachaRewards_ = null;
        }
        return gachaRewardsBuilder_;
      }

      private int lucky_ ;
      /**
       * <pre>
       ** 幸运值 
       * </pre>
       *
       * <code>int32 lucky = 2;</code>
       */
      public int getLucky() {
        return lucky_;
      }
      /**
       * <pre>
       ** 幸运值 
       * </pre>
       *
       * <code>int32 lucky = 2;</code>
       */
      public Builder setLucky(int value) {
        
        lucky_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 幸运值 
       * </pre>
       *
       * <code>int32 lucky = 2;</code>
       */
      public Builder clearLucky() {
        
        lucky_ = 0;
        onChanged();
        return this;
      }

      private cn.daxiang.protocol.game.CommonProtocol.RewardObjectList baseRewards_ = null;
      private com.google.protobuf.SingleFieldBuilderV3<
          cn.daxiang.protocol.game.CommonProtocol.RewardObjectList, cn.daxiang.protocol.game.CommonProtocol.RewardObjectList.Builder, cn.daxiang.protocol.game.CommonProtocol.RewardObjectListOrBuilder> baseRewardsBuilder_;
      /**
       * <pre>
       ** 基础奖励结果 
       * </pre>
       *
       * <code>.RewardObjectList baseRewards = 3;</code>
       */
      public boolean hasBaseRewards() {
        return baseRewardsBuilder_ != null || baseRewards_ != null;
      }
      /**
       * <pre>
       ** 基础奖励结果 
       * </pre>
       *
       * <code>.RewardObjectList baseRewards = 3;</code>
       */
      public cn.daxiang.protocol.game.CommonProtocol.RewardObjectList getBaseRewards() {
        if (baseRewardsBuilder_ == null) {
          return baseRewards_ == null ? cn.daxiang.protocol.game.CommonProtocol.RewardObjectList.getDefaultInstance() : baseRewards_;
        } else {
          return baseRewardsBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       ** 基础奖励结果 
       * </pre>
       *
       * <code>.RewardObjectList baseRewards = 3;</code>
       */
      public Builder setBaseRewards(cn.daxiang.protocol.game.CommonProtocol.RewardObjectList value) {
        if (baseRewardsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          baseRewards_ = value;
          onChanged();
        } else {
          baseRewardsBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <pre>
       ** 基础奖励结果 
       * </pre>
       *
       * <code>.RewardObjectList baseRewards = 3;</code>
       */
      public Builder setBaseRewards(
          cn.daxiang.protocol.game.CommonProtocol.RewardObjectList.Builder builderForValue) {
        if (baseRewardsBuilder_ == null) {
          baseRewards_ = builderForValue.build();
          onChanged();
        } else {
          baseRewardsBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <pre>
       ** 基础奖励结果 
       * </pre>
       *
       * <code>.RewardObjectList baseRewards = 3;</code>
       */
      public Builder mergeBaseRewards(cn.daxiang.protocol.game.CommonProtocol.RewardObjectList value) {
        if (baseRewardsBuilder_ == null) {
          if (baseRewards_ != null) {
            baseRewards_ =
              cn.daxiang.protocol.game.CommonProtocol.RewardObjectList.newBuilder(baseRewards_).mergeFrom(value).buildPartial();
          } else {
            baseRewards_ = value;
          }
          onChanged();
        } else {
          baseRewardsBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <pre>
       ** 基础奖励结果 
       * </pre>
       *
       * <code>.RewardObjectList baseRewards = 3;</code>
       */
      public Builder clearBaseRewards() {
        if (baseRewardsBuilder_ == null) {
          baseRewards_ = null;
          onChanged();
        } else {
          baseRewards_ = null;
          baseRewardsBuilder_ = null;
        }

        return this;
      }
      /**
       * <pre>
       ** 基础奖励结果 
       * </pre>
       *
       * <code>.RewardObjectList baseRewards = 3;</code>
       */
      public cn.daxiang.protocol.game.CommonProtocol.RewardObjectList.Builder getBaseRewardsBuilder() {
        
        onChanged();
        return getBaseRewardsFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       ** 基础奖励结果 
       * </pre>
       *
       * <code>.RewardObjectList baseRewards = 3;</code>
       */
      public cn.daxiang.protocol.game.CommonProtocol.RewardObjectListOrBuilder getBaseRewardsOrBuilder() {
        if (baseRewardsBuilder_ != null) {
          return baseRewardsBuilder_.getMessageOrBuilder();
        } else {
          return baseRewards_ == null ?
              cn.daxiang.protocol.game.CommonProtocol.RewardObjectList.getDefaultInstance() : baseRewards_;
        }
      }
      /**
       * <pre>
       ** 基础奖励结果 
       * </pre>
       *
       * <code>.RewardObjectList baseRewards = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          cn.daxiang.protocol.game.CommonProtocol.RewardObjectList, cn.daxiang.protocol.game.CommonProtocol.RewardObjectList.Builder, cn.daxiang.protocol.game.CommonProtocol.RewardObjectListOrBuilder> 
          getBaseRewardsFieldBuilder() {
        if (baseRewardsBuilder_ == null) {
          baseRewardsBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              cn.daxiang.protocol.game.CommonProtocol.RewardObjectList, cn.daxiang.protocol.game.CommonProtocol.RewardObjectList.Builder, cn.daxiang.protocol.game.CommonProtocol.RewardObjectListOrBuilder>(
                  getBaseRewards(),
                  getParentForChildren(),
                  isClean());
          baseRewards_ = null;
        }
        return baseRewardsBuilder_;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:BeastGachaResultResponse)
    }

    // @@protoc_insertion_point(class_scope:BeastGachaResultResponse)
    private static final cn.daxiang.protocol.game.BeastProtocol.BeastGachaResultResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new cn.daxiang.protocol.game.BeastProtocol.BeastGachaResultResponse();
    }

    public static cn.daxiang.protocol.game.BeastProtocol.BeastGachaResultResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<BeastGachaResultResponse>
        PARSER = new com.google.protobuf.AbstractParser<BeastGachaResultResponse>() {
      public BeastGachaResultResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new BeastGachaResultResponse(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<BeastGachaResultResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<BeastGachaResultResponse> getParserForType() {
      return PARSER;
    }

    public cn.daxiang.protocol.game.BeastProtocol.BeastGachaResultResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface BeastTotemInfoResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:BeastTotemInfoResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     ** 神兽图腾信息 
     * </pre>
     *
     * <code>repeated .BeastTotemEntityVO entities = 1;</code>
     */
    java.util.List<cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO> 
        getEntitiesList();
    /**
     * <pre>
     ** 神兽图腾信息 
     * </pre>
     *
     * <code>repeated .BeastTotemEntityVO entities = 1;</code>
     */
    cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO getEntities(int index);
    /**
     * <pre>
     ** 神兽图腾信息 
     * </pre>
     *
     * <code>repeated .BeastTotemEntityVO entities = 1;</code>
     */
    int getEntitiesCount();
    /**
     * <pre>
     ** 神兽图腾信息 
     * </pre>
     *
     * <code>repeated .BeastTotemEntityVO entities = 1;</code>
     */
    java.util.List<? extends cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVOOrBuilder> 
        getEntitiesOrBuilderList();
    /**
     * <pre>
     ** 神兽图腾信息 
     * </pre>
     *
     * <code>repeated .BeastTotemEntityVO entities = 1;</code>
     */
    cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVOOrBuilder getEntitiesOrBuilder(
        int index);
  }
  /**
   * <pre>
   ** 神兽图腾信息响应体 
   * </pre>
   *
   * Protobuf type {@code BeastTotemInfoResponse}
   */
  public  static final class BeastTotemInfoResponse extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:BeastTotemInfoResponse)
      BeastTotemInfoResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use BeastTotemInfoResponse.newBuilder() to construct.
    private BeastTotemInfoResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private BeastTotemInfoResponse() {
      entities_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private BeastTotemInfoResponse(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
                entities_ = new java.util.ArrayList<cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO>();
                mutable_bitField0_ |= 0x00000001;
              }
              entities_.add(
                  input.readMessage(cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO.parser(), extensionRegistry));
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
          entities_ = java.util.Collections.unmodifiableList(entities_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.BeastProtocol.internal_static_BeastTotemInfoResponse_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.daxiang.protocol.game.BeastProtocol.internal_static_BeastTotemInfoResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.daxiang.protocol.game.BeastProtocol.BeastTotemInfoResponse.class, cn.daxiang.protocol.game.BeastProtocol.BeastTotemInfoResponse.Builder.class);
    }

    public static final int ENTITIES_FIELD_NUMBER = 1;
    private java.util.List<cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO> entities_;
    /**
     * <pre>
     ** 神兽图腾信息 
     * </pre>
     *
     * <code>repeated .BeastTotemEntityVO entities = 1;</code>
     */
    public java.util.List<cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO> getEntitiesList() {
      return entities_;
    }
    /**
     * <pre>
     ** 神兽图腾信息 
     * </pre>
     *
     * <code>repeated .BeastTotemEntityVO entities = 1;</code>
     */
    public java.util.List<? extends cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVOOrBuilder> 
        getEntitiesOrBuilderList() {
      return entities_;
    }
    /**
     * <pre>
     ** 神兽图腾信息 
     * </pre>
     *
     * <code>repeated .BeastTotemEntityVO entities = 1;</code>
     */
    public int getEntitiesCount() {
      return entities_.size();
    }
    /**
     * <pre>
     ** 神兽图腾信息 
     * </pre>
     *
     * <code>repeated .BeastTotemEntityVO entities = 1;</code>
     */
    public cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO getEntities(int index) {
      return entities_.get(index);
    }
    /**
     * <pre>
     ** 神兽图腾信息 
     * </pre>
     *
     * <code>repeated .BeastTotemEntityVO entities = 1;</code>
     */
    public cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVOOrBuilder getEntitiesOrBuilder(
        int index) {
      return entities_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < entities_.size(); i++) {
        output.writeMessage(1, entities_.get(i));
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < entities_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, entities_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof cn.daxiang.protocol.game.BeastProtocol.BeastTotemInfoResponse)) {
        return super.equals(obj);
      }
      cn.daxiang.protocol.game.BeastProtocol.BeastTotemInfoResponse other = (cn.daxiang.protocol.game.BeastProtocol.BeastTotemInfoResponse) obj;

      boolean result = true;
      result = result && getEntitiesList()
          .equals(other.getEntitiesList());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getEntitiesCount() > 0) {
        hash = (37 * hash) + ENTITIES_FIELD_NUMBER;
        hash = (53 * hash) + getEntitiesList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static cn.daxiang.protocol.game.BeastProtocol.BeastTotemInfoResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastTotemInfoResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastTotemInfoResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastTotemInfoResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastTotemInfoResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastTotemInfoResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastTotemInfoResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastTotemInfoResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastTotemInfoResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastTotemInfoResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastTotemInfoResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastTotemInfoResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(cn.daxiang.protocol.game.BeastProtocol.BeastTotemInfoResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** 神兽图腾信息响应体 
     * </pre>
     *
     * Protobuf type {@code BeastTotemInfoResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:BeastTotemInfoResponse)
        cn.daxiang.protocol.game.BeastProtocol.BeastTotemInfoResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.daxiang.protocol.game.BeastProtocol.internal_static_BeastTotemInfoResponse_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.daxiang.protocol.game.BeastProtocol.internal_static_BeastTotemInfoResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.daxiang.protocol.game.BeastProtocol.BeastTotemInfoResponse.class, cn.daxiang.protocol.game.BeastProtocol.BeastTotemInfoResponse.Builder.class);
      }

      // Construct using cn.daxiang.protocol.game.BeastProtocol.BeastTotemInfoResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getEntitiesFieldBuilder();
        }
      }
      public Builder clear() {
        super.clear();
        if (entitiesBuilder_ == null) {
          entities_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          entitiesBuilder_.clear();
        }
        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return cn.daxiang.protocol.game.BeastProtocol.internal_static_BeastTotemInfoResponse_descriptor;
      }

      public cn.daxiang.protocol.game.BeastProtocol.BeastTotemInfoResponse getDefaultInstanceForType() {
        return cn.daxiang.protocol.game.BeastProtocol.BeastTotemInfoResponse.getDefaultInstance();
      }

      public cn.daxiang.protocol.game.BeastProtocol.BeastTotemInfoResponse build() {
        cn.daxiang.protocol.game.BeastProtocol.BeastTotemInfoResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public cn.daxiang.protocol.game.BeastProtocol.BeastTotemInfoResponse buildPartial() {
        cn.daxiang.protocol.game.BeastProtocol.BeastTotemInfoResponse result = new cn.daxiang.protocol.game.BeastProtocol.BeastTotemInfoResponse(this);
        int from_bitField0_ = bitField0_;
        if (entitiesBuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001)) {
            entities_ = java.util.Collections.unmodifiableList(entities_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.entities_ = entities_;
        } else {
          result.entities_ = entitiesBuilder_.build();
        }
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof cn.daxiang.protocol.game.BeastProtocol.BeastTotemInfoResponse) {
          return mergeFrom((cn.daxiang.protocol.game.BeastProtocol.BeastTotemInfoResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(cn.daxiang.protocol.game.BeastProtocol.BeastTotemInfoResponse other) {
        if (other == cn.daxiang.protocol.game.BeastProtocol.BeastTotemInfoResponse.getDefaultInstance()) return this;
        if (entitiesBuilder_ == null) {
          if (!other.entities_.isEmpty()) {
            if (entities_.isEmpty()) {
              entities_ = other.entities_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureEntitiesIsMutable();
              entities_.addAll(other.entities_);
            }
            onChanged();
          }
        } else {
          if (!other.entities_.isEmpty()) {
            if (entitiesBuilder_.isEmpty()) {
              entitiesBuilder_.dispose();
              entitiesBuilder_ = null;
              entities_ = other.entities_;
              bitField0_ = (bitField0_ & ~0x00000001);
              entitiesBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getEntitiesFieldBuilder() : null;
            } else {
              entitiesBuilder_.addAllMessages(other.entities_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        cn.daxiang.protocol.game.BeastProtocol.BeastTotemInfoResponse parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (cn.daxiang.protocol.game.BeastProtocol.BeastTotemInfoResponse) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO> entities_ =
        java.util.Collections.emptyList();
      private void ensureEntitiesIsMutable() {
        if (!((bitField0_ & 0x00000001) == 0x00000001)) {
          entities_ = new java.util.ArrayList<cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO>(entities_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO, cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO.Builder, cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVOOrBuilder> entitiesBuilder_;

      /**
       * <pre>
       ** 神兽图腾信息 
       * </pre>
       *
       * <code>repeated .BeastTotemEntityVO entities = 1;</code>
       */
      public java.util.List<cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO> getEntitiesList() {
        if (entitiesBuilder_ == null) {
          return java.util.Collections.unmodifiableList(entities_);
        } else {
          return entitiesBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       ** 神兽图腾信息 
       * </pre>
       *
       * <code>repeated .BeastTotemEntityVO entities = 1;</code>
       */
      public int getEntitiesCount() {
        if (entitiesBuilder_ == null) {
          return entities_.size();
        } else {
          return entitiesBuilder_.getCount();
        }
      }
      /**
       * <pre>
       ** 神兽图腾信息 
       * </pre>
       *
       * <code>repeated .BeastTotemEntityVO entities = 1;</code>
       */
      public cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO getEntities(int index) {
        if (entitiesBuilder_ == null) {
          return entities_.get(index);
        } else {
          return entitiesBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       ** 神兽图腾信息 
       * </pre>
       *
       * <code>repeated .BeastTotemEntityVO entities = 1;</code>
       */
      public Builder setEntities(
          int index, cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO value) {
        if (entitiesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureEntitiesIsMutable();
          entities_.set(index, value);
          onChanged();
        } else {
          entitiesBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       ** 神兽图腾信息 
       * </pre>
       *
       * <code>repeated .BeastTotemEntityVO entities = 1;</code>
       */
      public Builder setEntities(
          int index, cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO.Builder builderForValue) {
        if (entitiesBuilder_ == null) {
          ensureEntitiesIsMutable();
          entities_.set(index, builderForValue.build());
          onChanged();
        } else {
          entitiesBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       ** 神兽图腾信息 
       * </pre>
       *
       * <code>repeated .BeastTotemEntityVO entities = 1;</code>
       */
      public Builder addEntities(cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO value) {
        if (entitiesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureEntitiesIsMutable();
          entities_.add(value);
          onChanged();
        } else {
          entitiesBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       ** 神兽图腾信息 
       * </pre>
       *
       * <code>repeated .BeastTotemEntityVO entities = 1;</code>
       */
      public Builder addEntities(
          int index, cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO value) {
        if (entitiesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureEntitiesIsMutable();
          entities_.add(index, value);
          onChanged();
        } else {
          entitiesBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       ** 神兽图腾信息 
       * </pre>
       *
       * <code>repeated .BeastTotemEntityVO entities = 1;</code>
       */
      public Builder addEntities(
          cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO.Builder builderForValue) {
        if (entitiesBuilder_ == null) {
          ensureEntitiesIsMutable();
          entities_.add(builderForValue.build());
          onChanged();
        } else {
          entitiesBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       ** 神兽图腾信息 
       * </pre>
       *
       * <code>repeated .BeastTotemEntityVO entities = 1;</code>
       */
      public Builder addEntities(
          int index, cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO.Builder builderForValue) {
        if (entitiesBuilder_ == null) {
          ensureEntitiesIsMutable();
          entities_.add(index, builderForValue.build());
          onChanged();
        } else {
          entitiesBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       ** 神兽图腾信息 
       * </pre>
       *
       * <code>repeated .BeastTotemEntityVO entities = 1;</code>
       */
      public Builder addAllEntities(
          java.lang.Iterable<? extends cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO> values) {
        if (entitiesBuilder_ == null) {
          ensureEntitiesIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, entities_);
          onChanged();
        } else {
          entitiesBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       ** 神兽图腾信息 
       * </pre>
       *
       * <code>repeated .BeastTotemEntityVO entities = 1;</code>
       */
      public Builder clearEntities() {
        if (entitiesBuilder_ == null) {
          entities_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          entitiesBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       ** 神兽图腾信息 
       * </pre>
       *
       * <code>repeated .BeastTotemEntityVO entities = 1;</code>
       */
      public Builder removeEntities(int index) {
        if (entitiesBuilder_ == null) {
          ensureEntitiesIsMutable();
          entities_.remove(index);
          onChanged();
        } else {
          entitiesBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       ** 神兽图腾信息 
       * </pre>
       *
       * <code>repeated .BeastTotemEntityVO entities = 1;</code>
       */
      public cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO.Builder getEntitiesBuilder(
          int index) {
        return getEntitiesFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       ** 神兽图腾信息 
       * </pre>
       *
       * <code>repeated .BeastTotemEntityVO entities = 1;</code>
       */
      public cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVOOrBuilder getEntitiesOrBuilder(
          int index) {
        if (entitiesBuilder_ == null) {
          return entities_.get(index);  } else {
          return entitiesBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       ** 神兽图腾信息 
       * </pre>
       *
       * <code>repeated .BeastTotemEntityVO entities = 1;</code>
       */
      public java.util.List<? extends cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVOOrBuilder> 
           getEntitiesOrBuilderList() {
        if (entitiesBuilder_ != null) {
          return entitiesBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(entities_);
        }
      }
      /**
       * <pre>
       ** 神兽图腾信息 
       * </pre>
       *
       * <code>repeated .BeastTotemEntityVO entities = 1;</code>
       */
      public cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO.Builder addEntitiesBuilder() {
        return getEntitiesFieldBuilder().addBuilder(
            cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO.getDefaultInstance());
      }
      /**
       * <pre>
       ** 神兽图腾信息 
       * </pre>
       *
       * <code>repeated .BeastTotemEntityVO entities = 1;</code>
       */
      public cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO.Builder addEntitiesBuilder(
          int index) {
        return getEntitiesFieldBuilder().addBuilder(
            index, cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO.getDefaultInstance());
      }
      /**
       * <pre>
       ** 神兽图腾信息 
       * </pre>
       *
       * <code>repeated .BeastTotemEntityVO entities = 1;</code>
       */
      public java.util.List<cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO.Builder> 
           getEntitiesBuilderList() {
        return getEntitiesFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO, cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO.Builder, cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVOOrBuilder> 
          getEntitiesFieldBuilder() {
        if (entitiesBuilder_ == null) {
          entitiesBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO, cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO.Builder, cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVOOrBuilder>(
                  entities_,
                  ((bitField0_ & 0x00000001) == 0x00000001),
                  getParentForChildren(),
                  isClean());
          entities_ = null;
        }
        return entitiesBuilder_;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:BeastTotemInfoResponse)
    }

    // @@protoc_insertion_point(class_scope:BeastTotemInfoResponse)
    private static final cn.daxiang.protocol.game.BeastProtocol.BeastTotemInfoResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new cn.daxiang.protocol.game.BeastProtocol.BeastTotemInfoResponse();
    }

    public static cn.daxiang.protocol.game.BeastProtocol.BeastTotemInfoResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<BeastTotemInfoResponse>
        PARSER = new com.google.protobuf.AbstractParser<BeastTotemInfoResponse>() {
      public BeastTotemInfoResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new BeastTotemInfoResponse(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<BeastTotemInfoResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<BeastTotemInfoResponse> getParserForType() {
      return PARSER;
    }

    public cn.daxiang.protocol.game.BeastProtocol.BeastTotemInfoResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface BeastTotemEntityVOOrBuilder extends
      // @@protoc_insertion_point(interface_extends:BeastTotemEntityVO)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     ** 图腾类型 
     * </pre>
     *
     * <code>int32 type = 1;</code>
     */
    int getType();

    /**
     * <pre>
     * 进阶等级 
     * </pre>
     *
     * <code>int32 advanceLevel = 2;</code>
     */
    int getAdvanceLevel();

    /**
     * <pre>
     ** 神兽图腾部位信息 
     * </pre>
     *
     * <code>repeated .BeastTotemIndexEntityVO indexEntities = 3;</code>
     */
    java.util.List<cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO> 
        getIndexEntitiesList();
    /**
     * <pre>
     ** 神兽图腾部位信息 
     * </pre>
     *
     * <code>repeated .BeastTotemIndexEntityVO indexEntities = 3;</code>
     */
    cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO getIndexEntities(int index);
    /**
     * <pre>
     ** 神兽图腾部位信息 
     * </pre>
     *
     * <code>repeated .BeastTotemIndexEntityVO indexEntities = 3;</code>
     */
    int getIndexEntitiesCount();
    /**
     * <pre>
     ** 神兽图腾部位信息 
     * </pre>
     *
     * <code>repeated .BeastTotemIndexEntityVO indexEntities = 3;</code>
     */
    java.util.List<? extends cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVOOrBuilder> 
        getIndexEntitiesOrBuilderList();
    /**
     * <pre>
     ** 神兽图腾部位信息 
     * </pre>
     *
     * <code>repeated .BeastTotemIndexEntityVO indexEntities = 3;</code>
     */
    cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVOOrBuilder getIndexEntitiesOrBuilder(
        int index);
  }
  /**
   * <pre>
   ** 神兽图腾信息实体 
   * </pre>
   *
   * Protobuf type {@code BeastTotemEntityVO}
   */
  public  static final class BeastTotemEntityVO extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:BeastTotemEntityVO)
      BeastTotemEntityVOOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use BeastTotemEntityVO.newBuilder() to construct.
    private BeastTotemEntityVO(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private BeastTotemEntityVO() {
      type_ = 0;
      advanceLevel_ = 0;
      indexEntities_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private BeastTotemEntityVO(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {

              type_ = input.readInt32();
              break;
            }
            case 16: {

              advanceLevel_ = input.readInt32();
              break;
            }
            case 26: {
              if (!((mutable_bitField0_ & 0x00000004) == 0x00000004)) {
                indexEntities_ = new java.util.ArrayList<cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO>();
                mutable_bitField0_ |= 0x00000004;
              }
              indexEntities_.add(
                  input.readMessage(cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO.parser(), extensionRegistry));
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000004) == 0x00000004)) {
          indexEntities_ = java.util.Collections.unmodifiableList(indexEntities_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.BeastProtocol.internal_static_BeastTotemEntityVO_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.daxiang.protocol.game.BeastProtocol.internal_static_BeastTotemEntityVO_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO.class, cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO.Builder.class);
    }

    private int bitField0_;
    public static final int TYPE_FIELD_NUMBER = 1;
    private int type_;
    /**
     * <pre>
     ** 图腾类型 
     * </pre>
     *
     * <code>int32 type = 1;</code>
     */
    public int getType() {
      return type_;
    }

    public static final int ADVANCELEVEL_FIELD_NUMBER = 2;
    private int advanceLevel_;
    /**
     * <pre>
     * 进阶等级 
     * </pre>
     *
     * <code>int32 advanceLevel = 2;</code>
     */
    public int getAdvanceLevel() {
      return advanceLevel_;
    }

    public static final int INDEXENTITIES_FIELD_NUMBER = 3;
    private java.util.List<cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO> indexEntities_;
    /**
     * <pre>
     ** 神兽图腾部位信息 
     * </pre>
     *
     * <code>repeated .BeastTotemIndexEntityVO indexEntities = 3;</code>
     */
    public java.util.List<cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO> getIndexEntitiesList() {
      return indexEntities_;
    }
    /**
     * <pre>
     ** 神兽图腾部位信息 
     * </pre>
     *
     * <code>repeated .BeastTotemIndexEntityVO indexEntities = 3;</code>
     */
    public java.util.List<? extends cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVOOrBuilder> 
        getIndexEntitiesOrBuilderList() {
      return indexEntities_;
    }
    /**
     * <pre>
     ** 神兽图腾部位信息 
     * </pre>
     *
     * <code>repeated .BeastTotemIndexEntityVO indexEntities = 3;</code>
     */
    public int getIndexEntitiesCount() {
      return indexEntities_.size();
    }
    /**
     * <pre>
     ** 神兽图腾部位信息 
     * </pre>
     *
     * <code>repeated .BeastTotemIndexEntityVO indexEntities = 3;</code>
     */
    public cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO getIndexEntities(int index) {
      return indexEntities_.get(index);
    }
    /**
     * <pre>
     ** 神兽图腾部位信息 
     * </pre>
     *
     * <code>repeated .BeastTotemIndexEntityVO indexEntities = 3;</code>
     */
    public cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVOOrBuilder getIndexEntitiesOrBuilder(
        int index) {
      return indexEntities_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (type_ != 0) {
        output.writeInt32(1, type_);
      }
      if (advanceLevel_ != 0) {
        output.writeInt32(2, advanceLevel_);
      }
      for (int i = 0; i < indexEntities_.size(); i++) {
        output.writeMessage(3, indexEntities_.get(i));
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (type_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, type_);
      }
      if (advanceLevel_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, advanceLevel_);
      }
      for (int i = 0; i < indexEntities_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, indexEntities_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO)) {
        return super.equals(obj);
      }
      cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO other = (cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO) obj;

      boolean result = true;
      result = result && (getType()
          == other.getType());
      result = result && (getAdvanceLevel()
          == other.getAdvanceLevel());
      result = result && getIndexEntitiesList()
          .equals(other.getIndexEntitiesList());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
      hash = (37 * hash) + ADVANCELEVEL_FIELD_NUMBER;
      hash = (53 * hash) + getAdvanceLevel();
      if (getIndexEntitiesCount() > 0) {
        hash = (37 * hash) + INDEXENTITIES_FIELD_NUMBER;
        hash = (53 * hash) + getIndexEntitiesList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** 神兽图腾信息实体 
     * </pre>
     *
     * Protobuf type {@code BeastTotemEntityVO}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:BeastTotemEntityVO)
        cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVOOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.daxiang.protocol.game.BeastProtocol.internal_static_BeastTotemEntityVO_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.daxiang.protocol.game.BeastProtocol.internal_static_BeastTotemEntityVO_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO.class, cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO.Builder.class);
      }

      // Construct using cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getIndexEntitiesFieldBuilder();
        }
      }
      public Builder clear() {
        super.clear();
        type_ = 0;

        advanceLevel_ = 0;

        if (indexEntitiesBuilder_ == null) {
          indexEntities_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000004);
        } else {
          indexEntitiesBuilder_.clear();
        }
        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return cn.daxiang.protocol.game.BeastProtocol.internal_static_BeastTotemEntityVO_descriptor;
      }

      public cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO getDefaultInstanceForType() {
        return cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO.getDefaultInstance();
      }

      public cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO build() {
        cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO buildPartial() {
        cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO result = new cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        result.type_ = type_;
        result.advanceLevel_ = advanceLevel_;
        if (indexEntitiesBuilder_ == null) {
          if (((bitField0_ & 0x00000004) == 0x00000004)) {
            indexEntities_ = java.util.Collections.unmodifiableList(indexEntities_);
            bitField0_ = (bitField0_ & ~0x00000004);
          }
          result.indexEntities_ = indexEntities_;
        } else {
          result.indexEntities_ = indexEntitiesBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO) {
          return mergeFrom((cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO other) {
        if (other == cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO.getDefaultInstance()) return this;
        if (other.getType() != 0) {
          setType(other.getType());
        }
        if (other.getAdvanceLevel() != 0) {
          setAdvanceLevel(other.getAdvanceLevel());
        }
        if (indexEntitiesBuilder_ == null) {
          if (!other.indexEntities_.isEmpty()) {
            if (indexEntities_.isEmpty()) {
              indexEntities_ = other.indexEntities_;
              bitField0_ = (bitField0_ & ~0x00000004);
            } else {
              ensureIndexEntitiesIsMutable();
              indexEntities_.addAll(other.indexEntities_);
            }
            onChanged();
          }
        } else {
          if (!other.indexEntities_.isEmpty()) {
            if (indexEntitiesBuilder_.isEmpty()) {
              indexEntitiesBuilder_.dispose();
              indexEntitiesBuilder_ = null;
              indexEntities_ = other.indexEntities_;
              bitField0_ = (bitField0_ & ~0x00000004);
              indexEntitiesBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getIndexEntitiesFieldBuilder() : null;
            } else {
              indexEntitiesBuilder_.addAllMessages(other.indexEntities_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int type_ ;
      /**
       * <pre>
       ** 图腾类型 
       * </pre>
       *
       * <code>int32 type = 1;</code>
       */
      public int getType() {
        return type_;
      }
      /**
       * <pre>
       ** 图腾类型 
       * </pre>
       *
       * <code>int32 type = 1;</code>
       */
      public Builder setType(int value) {
        
        type_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 图腾类型 
       * </pre>
       *
       * <code>int32 type = 1;</code>
       */
      public Builder clearType() {
        
        type_ = 0;
        onChanged();
        return this;
      }

      private int advanceLevel_ ;
      /**
       * <pre>
       * 进阶等级 
       * </pre>
       *
       * <code>int32 advanceLevel = 2;</code>
       */
      public int getAdvanceLevel() {
        return advanceLevel_;
      }
      /**
       * <pre>
       * 进阶等级 
       * </pre>
       *
       * <code>int32 advanceLevel = 2;</code>
       */
      public Builder setAdvanceLevel(int value) {
        
        advanceLevel_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 进阶等级 
       * </pre>
       *
       * <code>int32 advanceLevel = 2;</code>
       */
      public Builder clearAdvanceLevel() {
        
        advanceLevel_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO> indexEntities_ =
        java.util.Collections.emptyList();
      private void ensureIndexEntitiesIsMutable() {
        if (!((bitField0_ & 0x00000004) == 0x00000004)) {
          indexEntities_ = new java.util.ArrayList<cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO>(indexEntities_);
          bitField0_ |= 0x00000004;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO, cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO.Builder, cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVOOrBuilder> indexEntitiesBuilder_;

      /**
       * <pre>
       ** 神兽图腾部位信息 
       * </pre>
       *
       * <code>repeated .BeastTotemIndexEntityVO indexEntities = 3;</code>
       */
      public java.util.List<cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO> getIndexEntitiesList() {
        if (indexEntitiesBuilder_ == null) {
          return java.util.Collections.unmodifiableList(indexEntities_);
        } else {
          return indexEntitiesBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       ** 神兽图腾部位信息 
       * </pre>
       *
       * <code>repeated .BeastTotemIndexEntityVO indexEntities = 3;</code>
       */
      public int getIndexEntitiesCount() {
        if (indexEntitiesBuilder_ == null) {
          return indexEntities_.size();
        } else {
          return indexEntitiesBuilder_.getCount();
        }
      }
      /**
       * <pre>
       ** 神兽图腾部位信息 
       * </pre>
       *
       * <code>repeated .BeastTotemIndexEntityVO indexEntities = 3;</code>
       */
      public cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO getIndexEntities(int index) {
        if (indexEntitiesBuilder_ == null) {
          return indexEntities_.get(index);
        } else {
          return indexEntitiesBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       ** 神兽图腾部位信息 
       * </pre>
       *
       * <code>repeated .BeastTotemIndexEntityVO indexEntities = 3;</code>
       */
      public Builder setIndexEntities(
          int index, cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO value) {
        if (indexEntitiesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureIndexEntitiesIsMutable();
          indexEntities_.set(index, value);
          onChanged();
        } else {
          indexEntitiesBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       ** 神兽图腾部位信息 
       * </pre>
       *
       * <code>repeated .BeastTotemIndexEntityVO indexEntities = 3;</code>
       */
      public Builder setIndexEntities(
          int index, cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO.Builder builderForValue) {
        if (indexEntitiesBuilder_ == null) {
          ensureIndexEntitiesIsMutable();
          indexEntities_.set(index, builderForValue.build());
          onChanged();
        } else {
          indexEntitiesBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       ** 神兽图腾部位信息 
       * </pre>
       *
       * <code>repeated .BeastTotemIndexEntityVO indexEntities = 3;</code>
       */
      public Builder addIndexEntities(cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO value) {
        if (indexEntitiesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureIndexEntitiesIsMutable();
          indexEntities_.add(value);
          onChanged();
        } else {
          indexEntitiesBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       ** 神兽图腾部位信息 
       * </pre>
       *
       * <code>repeated .BeastTotemIndexEntityVO indexEntities = 3;</code>
       */
      public Builder addIndexEntities(
          int index, cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO value) {
        if (indexEntitiesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureIndexEntitiesIsMutable();
          indexEntities_.add(index, value);
          onChanged();
        } else {
          indexEntitiesBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       ** 神兽图腾部位信息 
       * </pre>
       *
       * <code>repeated .BeastTotemIndexEntityVO indexEntities = 3;</code>
       */
      public Builder addIndexEntities(
          cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO.Builder builderForValue) {
        if (indexEntitiesBuilder_ == null) {
          ensureIndexEntitiesIsMutable();
          indexEntities_.add(builderForValue.build());
          onChanged();
        } else {
          indexEntitiesBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       ** 神兽图腾部位信息 
       * </pre>
       *
       * <code>repeated .BeastTotemIndexEntityVO indexEntities = 3;</code>
       */
      public Builder addIndexEntities(
          int index, cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO.Builder builderForValue) {
        if (indexEntitiesBuilder_ == null) {
          ensureIndexEntitiesIsMutable();
          indexEntities_.add(index, builderForValue.build());
          onChanged();
        } else {
          indexEntitiesBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       ** 神兽图腾部位信息 
       * </pre>
       *
       * <code>repeated .BeastTotemIndexEntityVO indexEntities = 3;</code>
       */
      public Builder addAllIndexEntities(
          java.lang.Iterable<? extends cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO> values) {
        if (indexEntitiesBuilder_ == null) {
          ensureIndexEntitiesIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, indexEntities_);
          onChanged();
        } else {
          indexEntitiesBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       ** 神兽图腾部位信息 
       * </pre>
       *
       * <code>repeated .BeastTotemIndexEntityVO indexEntities = 3;</code>
       */
      public Builder clearIndexEntities() {
        if (indexEntitiesBuilder_ == null) {
          indexEntities_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000004);
          onChanged();
        } else {
          indexEntitiesBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       ** 神兽图腾部位信息 
       * </pre>
       *
       * <code>repeated .BeastTotemIndexEntityVO indexEntities = 3;</code>
       */
      public Builder removeIndexEntities(int index) {
        if (indexEntitiesBuilder_ == null) {
          ensureIndexEntitiesIsMutable();
          indexEntities_.remove(index);
          onChanged();
        } else {
          indexEntitiesBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       ** 神兽图腾部位信息 
       * </pre>
       *
       * <code>repeated .BeastTotemIndexEntityVO indexEntities = 3;</code>
       */
      public cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO.Builder getIndexEntitiesBuilder(
          int index) {
        return getIndexEntitiesFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       ** 神兽图腾部位信息 
       * </pre>
       *
       * <code>repeated .BeastTotemIndexEntityVO indexEntities = 3;</code>
       */
      public cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVOOrBuilder getIndexEntitiesOrBuilder(
          int index) {
        if (indexEntitiesBuilder_ == null) {
          return indexEntities_.get(index);  } else {
          return indexEntitiesBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       ** 神兽图腾部位信息 
       * </pre>
       *
       * <code>repeated .BeastTotemIndexEntityVO indexEntities = 3;</code>
       */
      public java.util.List<? extends cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVOOrBuilder> 
           getIndexEntitiesOrBuilderList() {
        if (indexEntitiesBuilder_ != null) {
          return indexEntitiesBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(indexEntities_);
        }
      }
      /**
       * <pre>
       ** 神兽图腾部位信息 
       * </pre>
       *
       * <code>repeated .BeastTotemIndexEntityVO indexEntities = 3;</code>
       */
      public cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO.Builder addIndexEntitiesBuilder() {
        return getIndexEntitiesFieldBuilder().addBuilder(
            cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO.getDefaultInstance());
      }
      /**
       * <pre>
       ** 神兽图腾部位信息 
       * </pre>
       *
       * <code>repeated .BeastTotemIndexEntityVO indexEntities = 3;</code>
       */
      public cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO.Builder addIndexEntitiesBuilder(
          int index) {
        return getIndexEntitiesFieldBuilder().addBuilder(
            index, cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO.getDefaultInstance());
      }
      /**
       * <pre>
       ** 神兽图腾部位信息 
       * </pre>
       *
       * <code>repeated .BeastTotemIndexEntityVO indexEntities = 3;</code>
       */
      public java.util.List<cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO.Builder> 
           getIndexEntitiesBuilderList() {
        return getIndexEntitiesFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO, cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO.Builder, cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVOOrBuilder> 
          getIndexEntitiesFieldBuilder() {
        if (indexEntitiesBuilder_ == null) {
          indexEntitiesBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO, cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO.Builder, cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVOOrBuilder>(
                  indexEntities_,
                  ((bitField0_ & 0x00000004) == 0x00000004),
                  getParentForChildren(),
                  isClean());
          indexEntities_ = null;
        }
        return indexEntitiesBuilder_;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:BeastTotemEntityVO)
    }

    // @@protoc_insertion_point(class_scope:BeastTotemEntityVO)
    private static final cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO();
    }

    public static cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<BeastTotemEntityVO>
        PARSER = new com.google.protobuf.AbstractParser<BeastTotemEntityVO>() {
      public BeastTotemEntityVO parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new BeastTotemEntityVO(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<BeastTotemEntityVO> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<BeastTotemEntityVO> getParserForType() {
      return PARSER;
    }

    public cn.daxiang.protocol.game.BeastProtocol.BeastTotemEntityVO getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface BeastTotemIndexEntityVOOrBuilder extends
      // @@protoc_insertion_point(interface_extends:BeastTotemIndexEntityVO)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     ** 部位 
     * </pre>
     *
     * <code>int32 index = 1;</code>
     */
    int getIndex();

    /**
     * <pre>
     ** 等级 
     * </pre>
     *
     * <code>int32 level = 2;</code>
     */
    int getLevel();
  }
  /**
   * <pre>
   ** 神兽图腾部位信息 
   * </pre>
   *
   * Protobuf type {@code BeastTotemIndexEntityVO}
   */
  public  static final class BeastTotemIndexEntityVO extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:BeastTotemIndexEntityVO)
      BeastTotemIndexEntityVOOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use BeastTotemIndexEntityVO.newBuilder() to construct.
    private BeastTotemIndexEntityVO(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private BeastTotemIndexEntityVO() {
      index_ = 0;
      level_ = 0;
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private BeastTotemIndexEntityVO(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {

              index_ = input.readInt32();
              break;
            }
            case 16: {

              level_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.BeastProtocol.internal_static_BeastTotemIndexEntityVO_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.daxiang.protocol.game.BeastProtocol.internal_static_BeastTotemIndexEntityVO_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO.class, cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO.Builder.class);
    }

    public static final int INDEX_FIELD_NUMBER = 1;
    private int index_;
    /**
     * <pre>
     ** 部位 
     * </pre>
     *
     * <code>int32 index = 1;</code>
     */
    public int getIndex() {
      return index_;
    }

    public static final int LEVEL_FIELD_NUMBER = 2;
    private int level_;
    /**
     * <pre>
     ** 等级 
     * </pre>
     *
     * <code>int32 level = 2;</code>
     */
    public int getLevel() {
      return level_;
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (index_ != 0) {
        output.writeInt32(1, index_);
      }
      if (level_ != 0) {
        output.writeInt32(2, level_);
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (index_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, index_);
      }
      if (level_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, level_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO)) {
        return super.equals(obj);
      }
      cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO other = (cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO) obj;

      boolean result = true;
      result = result && (getIndex()
          == other.getIndex());
      result = result && (getLevel()
          == other.getLevel());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + INDEX_FIELD_NUMBER;
      hash = (53 * hash) + getIndex();
      hash = (37 * hash) + LEVEL_FIELD_NUMBER;
      hash = (53 * hash) + getLevel();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** 神兽图腾部位信息 
     * </pre>
     *
     * Protobuf type {@code BeastTotemIndexEntityVO}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:BeastTotemIndexEntityVO)
        cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVOOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.daxiang.protocol.game.BeastProtocol.internal_static_BeastTotemIndexEntityVO_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.daxiang.protocol.game.BeastProtocol.internal_static_BeastTotemIndexEntityVO_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO.class, cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO.Builder.class);
      }

      // Construct using cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        index_ = 0;

        level_ = 0;

        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return cn.daxiang.protocol.game.BeastProtocol.internal_static_BeastTotemIndexEntityVO_descriptor;
      }

      public cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO getDefaultInstanceForType() {
        return cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO.getDefaultInstance();
      }

      public cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO build() {
        cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO buildPartial() {
        cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO result = new cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO(this);
        result.index_ = index_;
        result.level_ = level_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO) {
          return mergeFrom((cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO other) {
        if (other == cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO.getDefaultInstance()) return this;
        if (other.getIndex() != 0) {
          setIndex(other.getIndex());
        }
        if (other.getLevel() != 0) {
          setLevel(other.getLevel());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int index_ ;
      /**
       * <pre>
       ** 部位 
       * </pre>
       *
       * <code>int32 index = 1;</code>
       */
      public int getIndex() {
        return index_;
      }
      /**
       * <pre>
       ** 部位 
       * </pre>
       *
       * <code>int32 index = 1;</code>
       */
      public Builder setIndex(int value) {
        
        index_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 部位 
       * </pre>
       *
       * <code>int32 index = 1;</code>
       */
      public Builder clearIndex() {
        
        index_ = 0;
        onChanged();
        return this;
      }

      private int level_ ;
      /**
       * <pre>
       ** 等级 
       * </pre>
       *
       * <code>int32 level = 2;</code>
       */
      public int getLevel() {
        return level_;
      }
      /**
       * <pre>
       ** 等级 
       * </pre>
       *
       * <code>int32 level = 2;</code>
       */
      public Builder setLevel(int value) {
        
        level_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 等级 
       * </pre>
       *
       * <code>int32 level = 2;</code>
       */
      public Builder clearLevel() {
        
        level_ = 0;
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:BeastTotemIndexEntityVO)
    }

    // @@protoc_insertion_point(class_scope:BeastTotemIndexEntityVO)
    private static final cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO();
    }

    public static cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<BeastTotemIndexEntityVO>
        PARSER = new com.google.protobuf.AbstractParser<BeastTotemIndexEntityVO>() {
      public BeastTotemIndexEntityVO parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new BeastTotemIndexEntityVO(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<BeastTotemIndexEntityVO> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<BeastTotemIndexEntityVO> getParserForType() {
      return PARSER;
    }

    public cn.daxiang.protocol.game.BeastProtocol.BeastTotemIndexEntityVO getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_BeastsInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_BeastsInfo_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_BeastBagInfoResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_BeastBagInfoResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_BeastManualResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_BeastManualResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_BeastManualResponse_ManualValueMapEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_BeastManualResponse_ManualValueMapEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_BeastAddExpRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_BeastAddExpRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_BeastGachaInfoResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_BeastGachaInfoResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_BeastGachaInfoResponse_DayBuyTimesEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_BeastGachaInfoResponse_DayBuyTimesEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_BeastGachaInfoResponse_BuyTimesEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_BeastGachaInfoResponse_BuyTimesEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_BeastGachaInfoResponse_ReceivesEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_BeastGachaInfoResponse_ReceivesEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_BeastPrivilegeEntityVO_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_BeastPrivilegeEntityVO_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_BeastGachaRecordEntity_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_BeastGachaRecordEntity_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_BeastGachaReportResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_BeastGachaReportResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_BeastGachaResultResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_BeastGachaResultResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_BeastTotemInfoResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_BeastTotemInfoResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_BeastTotemEntityVO_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_BeastTotemEntityVO_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_BeastTotemIndexEntityVO_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_BeastTotemIndexEntityVO_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\030game/beastProtocol.proto\032\031game/commonP" +
      "rotocol.proto\"H\n\nBeastsInfo\022\020\n\010configId\030" +
      "\001 \001(\005\022\r\n\005level\030\002 \001(\005\022\014\n\004star\030\003 \001(\005\022\013\n\003ex" +
      "p\030\004 \001(\005\"2\n\024BeastBagInfoResponse\022\032\n\005infos" +
      "\030\001 \003(\0132\013.BeastsInfo\"\326\001\n\023BeastManualRespo" +
      "nse\022@\n\016manualValueMap\030\001 \003(\0132(.BeastManua" +
      "lResponse.ManualValueMapEntry\022\020\n\010beastId" +
      "s\030\002 \003(\005\022\027\n\017receiveStageIds\030\003 \003(\005\022\033\n\023rece" +
      "iveResonanceIds\030\004 \003(\005\0325\n\023ManualValueMapE" +
      "ntry\022\013\n\003key\030\001 \001(\005\022\r\n\005value\030\002 \001(\005:\0028\001\"5\n\022" +
      "BeastAddExpRequest\022\020\n\010configId\030\001 \001(\005\022\r\n\005" +
      "level\030\002 \001(\005\"\232\004\n\026BeastGachaInfoResponse\022\020" +
      "\n\010configId\030\001 \001(\005\022+\n\nprivileges\030\002 \003(\0132\027.B" +
      "eastPrivilegeEntityVO\022\022\n\ngachaTimes\030\003 \001(" +
      "\005\022\r\n\005score\030\004 \001(\005\022\023\n\013lowestLucky\030\005 \001(\005\022\024\n" +
      "\014highestLucky\030\006 \001(\005\022\020\n\010badLucky\030\007 \001(\005\022\032\n" +
      "\022receiveFeedbackIds\030\010 \003(\005\022=\n\013dayBuyTimes" +
      "\030\t \003(\0132(.BeastGachaInfoResponse.DayBuyTi" +
      "mesEntry\0227\n\010buyTimes\030\n \003(\0132%.BeastGachaI" +
      "nfoResponse.BuyTimesEntry\0227\n\010receives\030\013 " +
      "\003(\0132%.BeastGachaInfoResponse.ReceivesEnt" +
      "ry\0322\n\020DayBuyTimesEntry\022\013\n\003key\030\001 \001(\005\022\r\n\005v" +
      "alue\030\002 \001(\005:\0028\001\032/\n\rBuyTimesEntry\022\013\n\003key\030\001" +
      " \001(\005\022\r\n\005value\030\002 \001(\005:\0028\001\032/\n\rReceivesEntry" +
      "\022\013\n\003key\030\001 \001(\005\022\r\n\005value\030\002 \001(\005:\0028\001\"N\n\026Beas" +
      "tPrivilegeEntityVO\022\014\n\004type\030\001 \001(\005\022\r\n\005coun" +
      "t\030\002 \001(\005\022\027\n\017lastReceiveTime\030\003 \001(\003\"n\n\026Beas" +
      "tGachaRecordEntity\022#\n\014actorProfile\030\001 \001(\013" +
      "2\r.ActorProfile\022\"\n\007rewards\030\002 \001(\0132\021.Rewar" +
      "dObjectList\022\013\n\003des\030\003 \001(\t\"A\n\030BeastGachaRe" +
      "portResponse\022%\n\004list\030\001 \003(\0132\027.BeastGachaR" +
      "ecordEntity\"z\n\030BeastGachaResultResponse\022" +
      "\'\n\014gachaRewards\030\001 \001(\0132\021.RewardObjectList" +
      "\022\r\n\005lucky\030\002 \001(\005\022&\n\013baseRewards\030\003 \001(\0132\021.R" +
      "ewardObjectList\"?\n\026BeastTotemInfoRespons" +
      "e\022%\n\010entities\030\001 \003(\0132\023.BeastTotemEntityVO" +
      "\"i\n\022BeastTotemEntityVO\022\014\n\004type\030\001 \001(\005\022\024\n\014" +
      "advanceLevel\030\002 \001(\005\022/\n\rindexEntities\030\003 \003(" +
      "\0132\030.BeastTotemIndexEntityVO\"7\n\027BeastTote" +
      "mIndexEntityVO\022\r\n\005index\030\001 \001(\005\022\r\n\005level\030\002" +
      " \001(\005*\226\005\n\010BeastCmd\022\022\n\016BEAST_CMD_NONE\020\000\022\026\n" +
      "\022GET_BEAST_BAG_INFO\020\001\022\022\n\016BEAST_ACTIVATE\020" +
      "\002\022\021\n\rBEAST_LEVE_UP\020\003\022\021\n\rBEAST_STAR_UP\020\004\022" +
      "\031\n\025GET_BEAST_MANUAL_INFO\020\005\022\031\n\025BEAST_MANU" +
      "AL_ACTIVATE\020\006\022%\n!BEAST_MANUAL_RECEIVE_ST" +
      "AGE_REWARD\020\007\022)\n%BEAST_MANUAL_RECEIVE_RES" +
      "ONANCE_REWARD\020\010\022\030\n\024GET_BEAST_GACHA_INFO\020" +
      "\t\022\"\n\036BEAST_GACHA_CHANGE_REWARD_POOL\020\n\022\017\n" +
      "\013BEAST_GACHA\020\013\022\037\n\033GET_BEAST_GACHA_REPORT" +
      "_INFO\020\014\022\033\n\027RECEIVE_FEEDBACK_REWARD\020\r\022(\n$" +
      "RECEIVE_BEAST_GACHA_PRIVILEGE_REWARD\020\016\022%" +
      "\n!RECEIVE_BEAST_GACHA_CHARGE_REWARD\020\017\022\030\n" +
      "\024GET_BEAST_TOTEM_INFO\020\020\022\030\n\024BEAST_TOTEM_L" +
      "EVEL_UP\020\021\022\030\n\024BEAST_TOTEM_ADVANCED\020\022\022 \n\034B" +
      "EAST_TOTEM_QUICKLY_LEVEL_UP\020\023\022\027\n\023PUSH_BE" +
      "AST_BAG_INFO\020e\022\032\n\026PUSH_BEAST_MANUAL_INFO" +
      "\020f\022\031\n\025PUSH_BEAST_GACHA_INFO\020gB\034\n\030cn.daxi" +
      "ang.protocol.gameH\001b\006proto3"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
        new com.google.protobuf.Descriptors.FileDescriptor.    InternalDescriptorAssigner() {
          public com.google.protobuf.ExtensionRegistry assignDescriptors(
              com.google.protobuf.Descriptors.FileDescriptor root) {
            descriptor = root;
            return null;
          }
        };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          cn.daxiang.protocol.game.CommonProtocol.getDescriptor(),
        }, assigner);
    internal_static_BeastsInfo_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_BeastsInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_BeastsInfo_descriptor,
        new java.lang.String[] { "ConfigId", "Level", "Star", "Exp", });
    internal_static_BeastBagInfoResponse_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_BeastBagInfoResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_BeastBagInfoResponse_descriptor,
        new java.lang.String[] { "Infos", });
    internal_static_BeastManualResponse_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_BeastManualResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_BeastManualResponse_descriptor,
        new java.lang.String[] { "ManualValueMap", "BeastIds", "ReceiveStageIds", "ReceiveResonanceIds", });
    internal_static_BeastManualResponse_ManualValueMapEntry_descriptor =
      internal_static_BeastManualResponse_descriptor.getNestedTypes().get(0);
    internal_static_BeastManualResponse_ManualValueMapEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_BeastManualResponse_ManualValueMapEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_BeastAddExpRequest_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_BeastAddExpRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_BeastAddExpRequest_descriptor,
        new java.lang.String[] { "ConfigId", "Level", });
    internal_static_BeastGachaInfoResponse_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_BeastGachaInfoResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_BeastGachaInfoResponse_descriptor,
        new java.lang.String[] { "ConfigId", "Privileges", "GachaTimes", "Score", "LowestLucky", "HighestLucky", "BadLucky", "ReceiveFeedbackIds", "DayBuyTimes", "BuyTimes", "Receives", });
    internal_static_BeastGachaInfoResponse_DayBuyTimesEntry_descriptor =
      internal_static_BeastGachaInfoResponse_descriptor.getNestedTypes().get(0);
    internal_static_BeastGachaInfoResponse_DayBuyTimesEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_BeastGachaInfoResponse_DayBuyTimesEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_BeastGachaInfoResponse_BuyTimesEntry_descriptor =
      internal_static_BeastGachaInfoResponse_descriptor.getNestedTypes().get(1);
    internal_static_BeastGachaInfoResponse_BuyTimesEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_BeastGachaInfoResponse_BuyTimesEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_BeastGachaInfoResponse_ReceivesEntry_descriptor =
      internal_static_BeastGachaInfoResponse_descriptor.getNestedTypes().get(2);
    internal_static_BeastGachaInfoResponse_ReceivesEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_BeastGachaInfoResponse_ReceivesEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_BeastPrivilegeEntityVO_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_BeastPrivilegeEntityVO_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_BeastPrivilegeEntityVO_descriptor,
        new java.lang.String[] { "Type", "Count", "LastReceiveTime", });
    internal_static_BeastGachaRecordEntity_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_BeastGachaRecordEntity_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_BeastGachaRecordEntity_descriptor,
        new java.lang.String[] { "ActorProfile", "Rewards", "Des", });
    internal_static_BeastGachaReportResponse_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_BeastGachaReportResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_BeastGachaReportResponse_descriptor,
        new java.lang.String[] { "List", });
    internal_static_BeastGachaResultResponse_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_BeastGachaResultResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_BeastGachaResultResponse_descriptor,
        new java.lang.String[] { "GachaRewards", "Lucky", "BaseRewards", });
    internal_static_BeastTotemInfoResponse_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_BeastTotemInfoResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_BeastTotemInfoResponse_descriptor,
        new java.lang.String[] { "Entities", });
    internal_static_BeastTotemEntityVO_descriptor =
      getDescriptor().getMessageTypes().get(10);
    internal_static_BeastTotemEntityVO_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_BeastTotemEntityVO_descriptor,
        new java.lang.String[] { "Type", "AdvanceLevel", "IndexEntities", });
    internal_static_BeastTotemIndexEntityVO_descriptor =
      getDescriptor().getMessageTypes().get(11);
    internal_static_BeastTotemIndexEntityVO_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_BeastTotemIndexEntityVO_descriptor,
        new java.lang.String[] { "Index", "Level", });
    cn.daxiang.protocol.game.CommonProtocol.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
