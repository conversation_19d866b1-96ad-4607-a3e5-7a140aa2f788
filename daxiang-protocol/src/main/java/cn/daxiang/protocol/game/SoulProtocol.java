// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: game/soulProtocol.proto

package cn.daxiang.protocol.game;

public final class SoulProtocol {
  private SoulProtocol() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  /**
   * <pre>
   ** 武魂 
   * </pre>
   *
   * Protobuf enum {@code SoulCmd}
   */
  public enum SoulCmd
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>SOUL_CMD_NONE = 0;</code>
     */
    SOUL_CMD_NONE(0),
    /**
     * <pre>
     **
     * 获取武魂信息
     * &lt;pre&gt;
     * 请求:{&#64;code Request}
     * 响应:{&#64;code SoulInfoResponse}
     * &lt;/pre&gt;
     * </pre>
     *
     * <code>GET_SOULS = 1;</code>
     */
    GET_SOULS(1),
    /**
     * <pre>
     **
     * 获取武魂图鉴信息
     * &lt;pre&gt;
     * 请求:{&#64;code Request}
     * 响应:{&#64;code SoulManualInfoResponse}
     * &lt;/pre&gt;
     * </pre>
     *
     * <code>GET_SOUL_MANUAL = 2;</code>
     */
    GET_SOUL_MANUAL(2),
    /**
     * <pre>
     **
     * 觉醒武魂
     * &lt;pre&gt;
     * 请求:{&#64;code AwakeSoulRequest}
     * 响应:{&#64;code Response}
     * 推送：{&#64;code SoulInfoResponse}
     * 推送:{&#64;code LongListPacket}被删除武魂的唯一Id
     * &lt;/pre&gt;
     * </pre>
     *
     * <code>AWAKE_SOUL = 3;</code>
     */
    AWAKE_SOUL(3),
    /**
     * <pre>
     **
     * 武魂图鉴激活
     * &lt;pre&gt;
     * 请求:{&#64;code IntPacket} 武魂配置表的配置ID
     * 响应:{&#64;code IntPacket} 激活的武魂配置表的配置ID
     * 推送：{&#64;code SoulManualInfoResponse}
     * &lt;/pre&gt;
     * </pre>
     *
     * <code>ACTIVATE_SOUL_MANUAL = 4;</code>
     */
    ACTIVATE_SOUL_MANUAL(4),
    /**
     * <pre>
     **
     * 一键塔防觉醒武魂
     * &lt;pre&gt;
     * 请求:{&#64;code Request}
     * 响应:{&#64;code Response}
     * 推送：{&#64;code SoulInfoResponse}
     * 推送:{&#64;code LongListPacket}被删除武魂的唯一Id
     * &lt;/pre&gt;
     * </pre>
     *
     * <code>ONE_CLICK_AWAKE_SOUL = 5;</code>
     */
    ONE_CLICK_AWAKE_SOUL(5),
    /**
     * <pre>
     **
     * 推送武魂信息
     * &lt;pre&gt;
     * 推送:{&#64;code SoulInfoResponse}
     * &lt;/pre&gt;
     * </pre>
     *
     * <code>PUSH_SOUL_INFO = 100;</code>
     */
    PUSH_SOUL_INFO(100),
    /**
     * <pre>
     **
     * 推送武魂图鉴信息
     * &lt;pre&gt;
     * 推送:{&#64;code SoulManualInfoResponse}
     * &lt;/pre&gt;
     * </pre>
     *
     * <code>PUSH_SOUL_MANUAL_INFO = 101;</code>
     */
    PUSH_SOUL_MANUAL_INFO(101),
    /**
     * <code>PUSH_SOUL_DELETE = 102;</code>
     */
    PUSH_SOUL_DELETE(102),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>SOUL_CMD_NONE = 0;</code>
     */
    public static final int SOUL_CMD_NONE_VALUE = 0;
    /**
     * <pre>
     **
     * 获取武魂信息
     * &lt;pre&gt;
     * 请求:{&#64;code Request}
     * 响应:{&#64;code SoulInfoResponse}
     * &lt;/pre&gt;
     * </pre>
     *
     * <code>GET_SOULS = 1;</code>
     */
    public static final int GET_SOULS_VALUE = 1;
    /**
     * <pre>
     **
     * 获取武魂图鉴信息
     * &lt;pre&gt;
     * 请求:{&#64;code Request}
     * 响应:{&#64;code SoulManualInfoResponse}
     * &lt;/pre&gt;
     * </pre>
     *
     * <code>GET_SOUL_MANUAL = 2;</code>
     */
    public static final int GET_SOUL_MANUAL_VALUE = 2;
    /**
     * <pre>
     **
     * 觉醒武魂
     * &lt;pre&gt;
     * 请求:{&#64;code AwakeSoulRequest}
     * 响应:{&#64;code Response}
     * 推送：{&#64;code SoulInfoResponse}
     * 推送:{&#64;code LongListPacket}被删除武魂的唯一Id
     * &lt;/pre&gt;
     * </pre>
     *
     * <code>AWAKE_SOUL = 3;</code>
     */
    public static final int AWAKE_SOUL_VALUE = 3;
    /**
     * <pre>
     **
     * 武魂图鉴激活
     * &lt;pre&gt;
     * 请求:{&#64;code IntPacket} 武魂配置表的配置ID
     * 响应:{&#64;code IntPacket} 激活的武魂配置表的配置ID
     * 推送：{&#64;code SoulManualInfoResponse}
     * &lt;/pre&gt;
     * </pre>
     *
     * <code>ACTIVATE_SOUL_MANUAL = 4;</code>
     */
    public static final int ACTIVATE_SOUL_MANUAL_VALUE = 4;
    /**
     * <pre>
     **
     * 一键塔防觉醒武魂
     * &lt;pre&gt;
     * 请求:{&#64;code Request}
     * 响应:{&#64;code Response}
     * 推送：{&#64;code SoulInfoResponse}
     * 推送:{&#64;code LongListPacket}被删除武魂的唯一Id
     * &lt;/pre&gt;
     * </pre>
     *
     * <code>ONE_CLICK_AWAKE_SOUL = 5;</code>
     */
    public static final int ONE_CLICK_AWAKE_SOUL_VALUE = 5;
    /**
     * <pre>
     **
     * 推送武魂信息
     * &lt;pre&gt;
     * 推送:{&#64;code SoulInfoResponse}
     * &lt;/pre&gt;
     * </pre>
     *
     * <code>PUSH_SOUL_INFO = 100;</code>
     */
    public static final int PUSH_SOUL_INFO_VALUE = 100;
    /**
     * <pre>
     **
     * 推送武魂图鉴信息
     * &lt;pre&gt;
     * 推送:{&#64;code SoulManualInfoResponse}
     * &lt;/pre&gt;
     * </pre>
     *
     * <code>PUSH_SOUL_MANUAL_INFO = 101;</code>
     */
    public static final int PUSH_SOUL_MANUAL_INFO_VALUE = 101;
    /**
     * <code>PUSH_SOUL_DELETE = 102;</code>
     */
    public static final int PUSH_SOUL_DELETE_VALUE = 102;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static SoulCmd valueOf(int value) {
      return forNumber(value);
    }

    public static SoulCmd forNumber(int value) {
      switch (value) {
        case 0: return SOUL_CMD_NONE;
        case 1: return GET_SOULS;
        case 2: return GET_SOUL_MANUAL;
        case 3: return AWAKE_SOUL;
        case 4: return ACTIVATE_SOUL_MANUAL;
        case 5: return ONE_CLICK_AWAKE_SOUL;
        case 100: return PUSH_SOUL_INFO;
        case 101: return PUSH_SOUL_MANUAL_INFO;
        case 102: return PUSH_SOUL_DELETE;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<SoulCmd>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        SoulCmd> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<SoulCmd>() {
            public SoulCmd findValueByNumber(int number) {
              return SoulCmd.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.SoulProtocol.getDescriptor().getEnumTypes().get(0);
    }

    private static final SoulCmd[] VALUES = values();

    public static SoulCmd valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private SoulCmd(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:SoulCmd)
  }

  public interface SoulInfoResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:SoulInfoResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     ** 武魂列表 
     * </pre>
     *
     * <code>repeated .Soul souls = 1;</code>
     */
    java.util.List<cn.daxiang.protocol.game.CommonProtocol.Soul> 
        getSoulsList();
    /**
     * <pre>
     ** 武魂列表 
     * </pre>
     *
     * <code>repeated .Soul souls = 1;</code>
     */
    cn.daxiang.protocol.game.CommonProtocol.Soul getSouls(int index);
    /**
     * <pre>
     ** 武魂列表 
     * </pre>
     *
     * <code>repeated .Soul souls = 1;</code>
     */
    int getSoulsCount();
    /**
     * <pre>
     ** 武魂列表 
     * </pre>
     *
     * <code>repeated .Soul souls = 1;</code>
     */
    java.util.List<? extends cn.daxiang.protocol.game.CommonProtocol.SoulOrBuilder> 
        getSoulsOrBuilderList();
    /**
     * <pre>
     ** 武魂列表 
     * </pre>
     *
     * <code>repeated .Soul souls = 1;</code>
     */
    cn.daxiang.protocol.game.CommonProtocol.SoulOrBuilder getSoulsOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code SoulInfoResponse}
   */
  public  static final class SoulInfoResponse extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:SoulInfoResponse)
      SoulInfoResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SoulInfoResponse.newBuilder() to construct.
    private SoulInfoResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SoulInfoResponse() {
      souls_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private SoulInfoResponse(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
                souls_ = new java.util.ArrayList<cn.daxiang.protocol.game.CommonProtocol.Soul>();
                mutable_bitField0_ |= 0x00000001;
              }
              souls_.add(
                  input.readMessage(cn.daxiang.protocol.game.CommonProtocol.Soul.parser(), extensionRegistry));
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
          souls_ = java.util.Collections.unmodifiableList(souls_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.SoulProtocol.internal_static_SoulInfoResponse_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.daxiang.protocol.game.SoulProtocol.internal_static_SoulInfoResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.daxiang.protocol.game.SoulProtocol.SoulInfoResponse.class, cn.daxiang.protocol.game.SoulProtocol.SoulInfoResponse.Builder.class);
    }

    public static final int SOULS_FIELD_NUMBER = 1;
    private java.util.List<cn.daxiang.protocol.game.CommonProtocol.Soul> souls_;
    /**
     * <pre>
     ** 武魂列表 
     * </pre>
     *
     * <code>repeated .Soul souls = 1;</code>
     */
    public java.util.List<cn.daxiang.protocol.game.CommonProtocol.Soul> getSoulsList() {
      return souls_;
    }
    /**
     * <pre>
     ** 武魂列表 
     * </pre>
     *
     * <code>repeated .Soul souls = 1;</code>
     */
    public java.util.List<? extends cn.daxiang.protocol.game.CommonProtocol.SoulOrBuilder> 
        getSoulsOrBuilderList() {
      return souls_;
    }
    /**
     * <pre>
     ** 武魂列表 
     * </pre>
     *
     * <code>repeated .Soul souls = 1;</code>
     */
    public int getSoulsCount() {
      return souls_.size();
    }
    /**
     * <pre>
     ** 武魂列表 
     * </pre>
     *
     * <code>repeated .Soul souls = 1;</code>
     */
    public cn.daxiang.protocol.game.CommonProtocol.Soul getSouls(int index) {
      return souls_.get(index);
    }
    /**
     * <pre>
     ** 武魂列表 
     * </pre>
     *
     * <code>repeated .Soul souls = 1;</code>
     */
    public cn.daxiang.protocol.game.CommonProtocol.SoulOrBuilder getSoulsOrBuilder(
        int index) {
      return souls_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < souls_.size(); i++) {
        output.writeMessage(1, souls_.get(i));
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < souls_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, souls_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof cn.daxiang.protocol.game.SoulProtocol.SoulInfoResponse)) {
        return super.equals(obj);
      }
      cn.daxiang.protocol.game.SoulProtocol.SoulInfoResponse other = (cn.daxiang.protocol.game.SoulProtocol.SoulInfoResponse) obj;

      boolean result = true;
      result = result && getSoulsList()
          .equals(other.getSoulsList());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getSoulsCount() > 0) {
        hash = (37 * hash) + SOULS_FIELD_NUMBER;
        hash = (53 * hash) + getSoulsList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static cn.daxiang.protocol.game.SoulProtocol.SoulInfoResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.SoulProtocol.SoulInfoResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.SoulProtocol.SoulInfoResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.SoulProtocol.SoulInfoResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.SoulProtocol.SoulInfoResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.SoulProtocol.SoulInfoResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.SoulProtocol.SoulInfoResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.SoulProtocol.SoulInfoResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.SoulProtocol.SoulInfoResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.SoulProtocol.SoulInfoResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.SoulProtocol.SoulInfoResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.SoulProtocol.SoulInfoResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(cn.daxiang.protocol.game.SoulProtocol.SoulInfoResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code SoulInfoResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:SoulInfoResponse)
        cn.daxiang.protocol.game.SoulProtocol.SoulInfoResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.daxiang.protocol.game.SoulProtocol.internal_static_SoulInfoResponse_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.daxiang.protocol.game.SoulProtocol.internal_static_SoulInfoResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.daxiang.protocol.game.SoulProtocol.SoulInfoResponse.class, cn.daxiang.protocol.game.SoulProtocol.SoulInfoResponse.Builder.class);
      }

      // Construct using cn.daxiang.protocol.game.SoulProtocol.SoulInfoResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getSoulsFieldBuilder();
        }
      }
      public Builder clear() {
        super.clear();
        if (soulsBuilder_ == null) {
          souls_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          soulsBuilder_.clear();
        }
        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return cn.daxiang.protocol.game.SoulProtocol.internal_static_SoulInfoResponse_descriptor;
      }

      public cn.daxiang.protocol.game.SoulProtocol.SoulInfoResponse getDefaultInstanceForType() {
        return cn.daxiang.protocol.game.SoulProtocol.SoulInfoResponse.getDefaultInstance();
      }

      public cn.daxiang.protocol.game.SoulProtocol.SoulInfoResponse build() {
        cn.daxiang.protocol.game.SoulProtocol.SoulInfoResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public cn.daxiang.protocol.game.SoulProtocol.SoulInfoResponse buildPartial() {
        cn.daxiang.protocol.game.SoulProtocol.SoulInfoResponse result = new cn.daxiang.protocol.game.SoulProtocol.SoulInfoResponse(this);
        int from_bitField0_ = bitField0_;
        if (soulsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001)) {
            souls_ = java.util.Collections.unmodifiableList(souls_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.souls_ = souls_;
        } else {
          result.souls_ = soulsBuilder_.build();
        }
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof cn.daxiang.protocol.game.SoulProtocol.SoulInfoResponse) {
          return mergeFrom((cn.daxiang.protocol.game.SoulProtocol.SoulInfoResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(cn.daxiang.protocol.game.SoulProtocol.SoulInfoResponse other) {
        if (other == cn.daxiang.protocol.game.SoulProtocol.SoulInfoResponse.getDefaultInstance()) return this;
        if (soulsBuilder_ == null) {
          if (!other.souls_.isEmpty()) {
            if (souls_.isEmpty()) {
              souls_ = other.souls_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureSoulsIsMutable();
              souls_.addAll(other.souls_);
            }
            onChanged();
          }
        } else {
          if (!other.souls_.isEmpty()) {
            if (soulsBuilder_.isEmpty()) {
              soulsBuilder_.dispose();
              soulsBuilder_ = null;
              souls_ = other.souls_;
              bitField0_ = (bitField0_ & ~0x00000001);
              soulsBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getSoulsFieldBuilder() : null;
            } else {
              soulsBuilder_.addAllMessages(other.souls_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        cn.daxiang.protocol.game.SoulProtocol.SoulInfoResponse parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (cn.daxiang.protocol.game.SoulProtocol.SoulInfoResponse) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<cn.daxiang.protocol.game.CommonProtocol.Soul> souls_ =
        java.util.Collections.emptyList();
      private void ensureSoulsIsMutable() {
        if (!((bitField0_ & 0x00000001) == 0x00000001)) {
          souls_ = new java.util.ArrayList<cn.daxiang.protocol.game.CommonProtocol.Soul>(souls_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          cn.daxiang.protocol.game.CommonProtocol.Soul, cn.daxiang.protocol.game.CommonProtocol.Soul.Builder, cn.daxiang.protocol.game.CommonProtocol.SoulOrBuilder> soulsBuilder_;

      /**
       * <pre>
       ** 武魂列表 
       * </pre>
       *
       * <code>repeated .Soul souls = 1;</code>
       */
      public java.util.List<cn.daxiang.protocol.game.CommonProtocol.Soul> getSoulsList() {
        if (soulsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(souls_);
        } else {
          return soulsBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       ** 武魂列表 
       * </pre>
       *
       * <code>repeated .Soul souls = 1;</code>
       */
      public int getSoulsCount() {
        if (soulsBuilder_ == null) {
          return souls_.size();
        } else {
          return soulsBuilder_.getCount();
        }
      }
      /**
       * <pre>
       ** 武魂列表 
       * </pre>
       *
       * <code>repeated .Soul souls = 1;</code>
       */
      public cn.daxiang.protocol.game.CommonProtocol.Soul getSouls(int index) {
        if (soulsBuilder_ == null) {
          return souls_.get(index);
        } else {
          return soulsBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       ** 武魂列表 
       * </pre>
       *
       * <code>repeated .Soul souls = 1;</code>
       */
      public Builder setSouls(
          int index, cn.daxiang.protocol.game.CommonProtocol.Soul value) {
        if (soulsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureSoulsIsMutable();
          souls_.set(index, value);
          onChanged();
        } else {
          soulsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       ** 武魂列表 
       * </pre>
       *
       * <code>repeated .Soul souls = 1;</code>
       */
      public Builder setSouls(
          int index, cn.daxiang.protocol.game.CommonProtocol.Soul.Builder builderForValue) {
        if (soulsBuilder_ == null) {
          ensureSoulsIsMutable();
          souls_.set(index, builderForValue.build());
          onChanged();
        } else {
          soulsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       ** 武魂列表 
       * </pre>
       *
       * <code>repeated .Soul souls = 1;</code>
       */
      public Builder addSouls(cn.daxiang.protocol.game.CommonProtocol.Soul value) {
        if (soulsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureSoulsIsMutable();
          souls_.add(value);
          onChanged();
        } else {
          soulsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       ** 武魂列表 
       * </pre>
       *
       * <code>repeated .Soul souls = 1;</code>
       */
      public Builder addSouls(
          int index, cn.daxiang.protocol.game.CommonProtocol.Soul value) {
        if (soulsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureSoulsIsMutable();
          souls_.add(index, value);
          onChanged();
        } else {
          soulsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       ** 武魂列表 
       * </pre>
       *
       * <code>repeated .Soul souls = 1;</code>
       */
      public Builder addSouls(
          cn.daxiang.protocol.game.CommonProtocol.Soul.Builder builderForValue) {
        if (soulsBuilder_ == null) {
          ensureSoulsIsMutable();
          souls_.add(builderForValue.build());
          onChanged();
        } else {
          soulsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       ** 武魂列表 
       * </pre>
       *
       * <code>repeated .Soul souls = 1;</code>
       */
      public Builder addSouls(
          int index, cn.daxiang.protocol.game.CommonProtocol.Soul.Builder builderForValue) {
        if (soulsBuilder_ == null) {
          ensureSoulsIsMutable();
          souls_.add(index, builderForValue.build());
          onChanged();
        } else {
          soulsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       ** 武魂列表 
       * </pre>
       *
       * <code>repeated .Soul souls = 1;</code>
       */
      public Builder addAllSouls(
          java.lang.Iterable<? extends cn.daxiang.protocol.game.CommonProtocol.Soul> values) {
        if (soulsBuilder_ == null) {
          ensureSoulsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, souls_);
          onChanged();
        } else {
          soulsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       ** 武魂列表 
       * </pre>
       *
       * <code>repeated .Soul souls = 1;</code>
       */
      public Builder clearSouls() {
        if (soulsBuilder_ == null) {
          souls_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          soulsBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       ** 武魂列表 
       * </pre>
       *
       * <code>repeated .Soul souls = 1;</code>
       */
      public Builder removeSouls(int index) {
        if (soulsBuilder_ == null) {
          ensureSoulsIsMutable();
          souls_.remove(index);
          onChanged();
        } else {
          soulsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       ** 武魂列表 
       * </pre>
       *
       * <code>repeated .Soul souls = 1;</code>
       */
      public cn.daxiang.protocol.game.CommonProtocol.Soul.Builder getSoulsBuilder(
          int index) {
        return getSoulsFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       ** 武魂列表 
       * </pre>
       *
       * <code>repeated .Soul souls = 1;</code>
       */
      public cn.daxiang.protocol.game.CommonProtocol.SoulOrBuilder getSoulsOrBuilder(
          int index) {
        if (soulsBuilder_ == null) {
          return souls_.get(index);  } else {
          return soulsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       ** 武魂列表 
       * </pre>
       *
       * <code>repeated .Soul souls = 1;</code>
       */
      public java.util.List<? extends cn.daxiang.protocol.game.CommonProtocol.SoulOrBuilder> 
           getSoulsOrBuilderList() {
        if (soulsBuilder_ != null) {
          return soulsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(souls_);
        }
      }
      /**
       * <pre>
       ** 武魂列表 
       * </pre>
       *
       * <code>repeated .Soul souls = 1;</code>
       */
      public cn.daxiang.protocol.game.CommonProtocol.Soul.Builder addSoulsBuilder() {
        return getSoulsFieldBuilder().addBuilder(
            cn.daxiang.protocol.game.CommonProtocol.Soul.getDefaultInstance());
      }
      /**
       * <pre>
       ** 武魂列表 
       * </pre>
       *
       * <code>repeated .Soul souls = 1;</code>
       */
      public cn.daxiang.protocol.game.CommonProtocol.Soul.Builder addSoulsBuilder(
          int index) {
        return getSoulsFieldBuilder().addBuilder(
            index, cn.daxiang.protocol.game.CommonProtocol.Soul.getDefaultInstance());
      }
      /**
       * <pre>
       ** 武魂列表 
       * </pre>
       *
       * <code>repeated .Soul souls = 1;</code>
       */
      public java.util.List<cn.daxiang.protocol.game.CommonProtocol.Soul.Builder> 
           getSoulsBuilderList() {
        return getSoulsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          cn.daxiang.protocol.game.CommonProtocol.Soul, cn.daxiang.protocol.game.CommonProtocol.Soul.Builder, cn.daxiang.protocol.game.CommonProtocol.SoulOrBuilder> 
          getSoulsFieldBuilder() {
        if (soulsBuilder_ == null) {
          soulsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              cn.daxiang.protocol.game.CommonProtocol.Soul, cn.daxiang.protocol.game.CommonProtocol.Soul.Builder, cn.daxiang.protocol.game.CommonProtocol.SoulOrBuilder>(
                  souls_,
                  ((bitField0_ & 0x00000001) == 0x00000001),
                  getParentForChildren(),
                  isClean());
          souls_ = null;
        }
        return soulsBuilder_;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:SoulInfoResponse)
    }

    // @@protoc_insertion_point(class_scope:SoulInfoResponse)
    private static final cn.daxiang.protocol.game.SoulProtocol.SoulInfoResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new cn.daxiang.protocol.game.SoulProtocol.SoulInfoResponse();
    }

    public static cn.daxiang.protocol.game.SoulProtocol.SoulInfoResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<SoulInfoResponse>
        PARSER = new com.google.protobuf.AbstractParser<SoulInfoResponse>() {
      public SoulInfoResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new SoulInfoResponse(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SoulInfoResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SoulInfoResponse> getParserForType() {
      return PARSER;
    }

    public cn.daxiang.protocol.game.SoulProtocol.SoulInfoResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SoulManualInfoResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:SoulManualInfoResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     **已经激活的图鉴列表 存放的是配置ID
     * </pre>
     *
     * <code>repeated int32 manuals = 1;</code>
     */
    java.util.List<java.lang.Integer> getManualsList();
    /**
     * <pre>
     **已经激活的图鉴列表 存放的是配置ID
     * </pre>
     *
     * <code>repeated int32 manuals = 1;</code>
     */
    int getManualsCount();
    /**
     * <pre>
     **已经激活的图鉴列表 存放的是配置ID
     * </pre>
     *
     * <code>repeated int32 manuals = 1;</code>
     */
    int getManuals(int index);
  }
  /**
   * Protobuf type {@code SoulManualInfoResponse}
   */
  public  static final class SoulManualInfoResponse extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:SoulManualInfoResponse)
      SoulManualInfoResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SoulManualInfoResponse.newBuilder() to construct.
    private SoulManualInfoResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SoulManualInfoResponse() {
      manuals_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private SoulManualInfoResponse(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
                manuals_ = new java.util.ArrayList<java.lang.Integer>();
                mutable_bitField0_ |= 0x00000001;
              }
              manuals_.add(input.readInt32());
              break;
            }
            case 10: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001) && input.getBytesUntilLimit() > 0) {
                manuals_ = new java.util.ArrayList<java.lang.Integer>();
                mutable_bitField0_ |= 0x00000001;
              }
              while (input.getBytesUntilLimit() > 0) {
                manuals_.add(input.readInt32());
              }
              input.popLimit(limit);
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
          manuals_ = java.util.Collections.unmodifiableList(manuals_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.SoulProtocol.internal_static_SoulManualInfoResponse_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.daxiang.protocol.game.SoulProtocol.internal_static_SoulManualInfoResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.daxiang.protocol.game.SoulProtocol.SoulManualInfoResponse.class, cn.daxiang.protocol.game.SoulProtocol.SoulManualInfoResponse.Builder.class);
    }

    public static final int MANUALS_FIELD_NUMBER = 1;
    private java.util.List<java.lang.Integer> manuals_;
    /**
     * <pre>
     **已经激活的图鉴列表 存放的是配置ID
     * </pre>
     *
     * <code>repeated int32 manuals = 1;</code>
     */
    public java.util.List<java.lang.Integer>
        getManualsList() {
      return manuals_;
    }
    /**
     * <pre>
     **已经激活的图鉴列表 存放的是配置ID
     * </pre>
     *
     * <code>repeated int32 manuals = 1;</code>
     */
    public int getManualsCount() {
      return manuals_.size();
    }
    /**
     * <pre>
     **已经激活的图鉴列表 存放的是配置ID
     * </pre>
     *
     * <code>repeated int32 manuals = 1;</code>
     */
    public int getManuals(int index) {
      return manuals_.get(index);
    }
    private int manualsMemoizedSerializedSize = -1;

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (getManualsList().size() > 0) {
        output.writeUInt32NoTag(10);
        output.writeUInt32NoTag(manualsMemoizedSerializedSize);
      }
      for (int i = 0; i < manuals_.size(); i++) {
        output.writeInt32NoTag(manuals_.get(i));
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      {
        int dataSize = 0;
        for (int i = 0; i < manuals_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt32SizeNoTag(manuals_.get(i));
        }
        size += dataSize;
        if (!getManualsList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        manualsMemoizedSerializedSize = dataSize;
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof cn.daxiang.protocol.game.SoulProtocol.SoulManualInfoResponse)) {
        return super.equals(obj);
      }
      cn.daxiang.protocol.game.SoulProtocol.SoulManualInfoResponse other = (cn.daxiang.protocol.game.SoulProtocol.SoulManualInfoResponse) obj;

      boolean result = true;
      result = result && getManualsList()
          .equals(other.getManualsList());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getManualsCount() > 0) {
        hash = (37 * hash) + MANUALS_FIELD_NUMBER;
        hash = (53 * hash) + getManualsList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static cn.daxiang.protocol.game.SoulProtocol.SoulManualInfoResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.SoulProtocol.SoulManualInfoResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.SoulProtocol.SoulManualInfoResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.SoulProtocol.SoulManualInfoResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.SoulProtocol.SoulManualInfoResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.SoulProtocol.SoulManualInfoResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.SoulProtocol.SoulManualInfoResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.SoulProtocol.SoulManualInfoResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.SoulProtocol.SoulManualInfoResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.SoulProtocol.SoulManualInfoResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.SoulProtocol.SoulManualInfoResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.SoulProtocol.SoulManualInfoResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(cn.daxiang.protocol.game.SoulProtocol.SoulManualInfoResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code SoulManualInfoResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:SoulManualInfoResponse)
        cn.daxiang.protocol.game.SoulProtocol.SoulManualInfoResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.daxiang.protocol.game.SoulProtocol.internal_static_SoulManualInfoResponse_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.daxiang.protocol.game.SoulProtocol.internal_static_SoulManualInfoResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.daxiang.protocol.game.SoulProtocol.SoulManualInfoResponse.class, cn.daxiang.protocol.game.SoulProtocol.SoulManualInfoResponse.Builder.class);
      }

      // Construct using cn.daxiang.protocol.game.SoulProtocol.SoulManualInfoResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        manuals_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return cn.daxiang.protocol.game.SoulProtocol.internal_static_SoulManualInfoResponse_descriptor;
      }

      public cn.daxiang.protocol.game.SoulProtocol.SoulManualInfoResponse getDefaultInstanceForType() {
        return cn.daxiang.protocol.game.SoulProtocol.SoulManualInfoResponse.getDefaultInstance();
      }

      public cn.daxiang.protocol.game.SoulProtocol.SoulManualInfoResponse build() {
        cn.daxiang.protocol.game.SoulProtocol.SoulManualInfoResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public cn.daxiang.protocol.game.SoulProtocol.SoulManualInfoResponse buildPartial() {
        cn.daxiang.protocol.game.SoulProtocol.SoulManualInfoResponse result = new cn.daxiang.protocol.game.SoulProtocol.SoulManualInfoResponse(this);
        int from_bitField0_ = bitField0_;
        if (((bitField0_ & 0x00000001) == 0x00000001)) {
          manuals_ = java.util.Collections.unmodifiableList(manuals_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.manuals_ = manuals_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof cn.daxiang.protocol.game.SoulProtocol.SoulManualInfoResponse) {
          return mergeFrom((cn.daxiang.protocol.game.SoulProtocol.SoulManualInfoResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(cn.daxiang.protocol.game.SoulProtocol.SoulManualInfoResponse other) {
        if (other == cn.daxiang.protocol.game.SoulProtocol.SoulManualInfoResponse.getDefaultInstance()) return this;
        if (!other.manuals_.isEmpty()) {
          if (manuals_.isEmpty()) {
            manuals_ = other.manuals_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureManualsIsMutable();
            manuals_.addAll(other.manuals_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        cn.daxiang.protocol.game.SoulProtocol.SoulManualInfoResponse parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (cn.daxiang.protocol.game.SoulProtocol.SoulManualInfoResponse) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<java.lang.Integer> manuals_ = java.util.Collections.emptyList();
      private void ensureManualsIsMutable() {
        if (!((bitField0_ & 0x00000001) == 0x00000001)) {
          manuals_ = new java.util.ArrayList<java.lang.Integer>(manuals_);
          bitField0_ |= 0x00000001;
         }
      }
      /**
       * <pre>
       **已经激活的图鉴列表 存放的是配置ID
       * </pre>
       *
       * <code>repeated int32 manuals = 1;</code>
       */
      public java.util.List<java.lang.Integer>
          getManualsList() {
        return java.util.Collections.unmodifiableList(manuals_);
      }
      /**
       * <pre>
       **已经激活的图鉴列表 存放的是配置ID
       * </pre>
       *
       * <code>repeated int32 manuals = 1;</code>
       */
      public int getManualsCount() {
        return manuals_.size();
      }
      /**
       * <pre>
       **已经激活的图鉴列表 存放的是配置ID
       * </pre>
       *
       * <code>repeated int32 manuals = 1;</code>
       */
      public int getManuals(int index) {
        return manuals_.get(index);
      }
      /**
       * <pre>
       **已经激活的图鉴列表 存放的是配置ID
       * </pre>
       *
       * <code>repeated int32 manuals = 1;</code>
       */
      public Builder setManuals(
          int index, int value) {
        ensureManualsIsMutable();
        manuals_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       **已经激活的图鉴列表 存放的是配置ID
       * </pre>
       *
       * <code>repeated int32 manuals = 1;</code>
       */
      public Builder addManuals(int value) {
        ensureManualsIsMutable();
        manuals_.add(value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       **已经激活的图鉴列表 存放的是配置ID
       * </pre>
       *
       * <code>repeated int32 manuals = 1;</code>
       */
      public Builder addAllManuals(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureManualsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, manuals_);
        onChanged();
        return this;
      }
      /**
       * <pre>
       **已经激活的图鉴列表 存放的是配置ID
       * </pre>
       *
       * <code>repeated int32 manuals = 1;</code>
       */
      public Builder clearManuals() {
        manuals_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:SoulManualInfoResponse)
    }

    // @@protoc_insertion_point(class_scope:SoulManualInfoResponse)
    private static final cn.daxiang.protocol.game.SoulProtocol.SoulManualInfoResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new cn.daxiang.protocol.game.SoulProtocol.SoulManualInfoResponse();
    }

    public static cn.daxiang.protocol.game.SoulProtocol.SoulManualInfoResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<SoulManualInfoResponse>
        PARSER = new com.google.protobuf.AbstractParser<SoulManualInfoResponse>() {
      public SoulManualInfoResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new SoulManualInfoResponse(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SoulManualInfoResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SoulManualInfoResponse> getParserForType() {
      return PARSER;
    }

    public cn.daxiang.protocol.game.SoulProtocol.SoulManualInfoResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface AwakeSoulRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:AwakeSoulRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     ** 需要合成的武魂的唯一ID
     * </pre>
     *
     * <code>int64 soulId = 1;</code>
     */
    long getSoulId();

    /**
     * <pre>
     ** 被消耗武魂唯一ID 注意：被消耗的武魂要求未养成过
     * </pre>
     *
     * <code>repeated int64 costIds = 2;</code>
     */
    java.util.List<java.lang.Long> getCostIdsList();
    /**
     * <pre>
     ** 被消耗武魂唯一ID 注意：被消耗的武魂要求未养成过
     * </pre>
     *
     * <code>repeated int64 costIds = 2;</code>
     */
    int getCostIdsCount();
    /**
     * <pre>
     ** 被消耗武魂唯一ID 注意：被消耗的武魂要求未养成过
     * </pre>
     *
     * <code>repeated int64 costIds = 2;</code>
     */
    long getCostIds(int index);
  }
  /**
   * Protobuf type {@code AwakeSoulRequest}
   */
  public  static final class AwakeSoulRequest extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:AwakeSoulRequest)
      AwakeSoulRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use AwakeSoulRequest.newBuilder() to construct.
    private AwakeSoulRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private AwakeSoulRequest() {
      soulId_ = 0L;
      costIds_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private AwakeSoulRequest(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {

              soulId_ = input.readInt64();
              break;
            }
            case 16: {
              if (!((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
                costIds_ = new java.util.ArrayList<java.lang.Long>();
                mutable_bitField0_ |= 0x00000002;
              }
              costIds_.add(input.readInt64());
              break;
            }
            case 18: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000002) == 0x00000002) && input.getBytesUntilLimit() > 0) {
                costIds_ = new java.util.ArrayList<java.lang.Long>();
                mutable_bitField0_ |= 0x00000002;
              }
              while (input.getBytesUntilLimit() > 0) {
                costIds_.add(input.readInt64());
              }
              input.popLimit(limit);
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
          costIds_ = java.util.Collections.unmodifiableList(costIds_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.SoulProtocol.internal_static_AwakeSoulRequest_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.daxiang.protocol.game.SoulProtocol.internal_static_AwakeSoulRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.daxiang.protocol.game.SoulProtocol.AwakeSoulRequest.class, cn.daxiang.protocol.game.SoulProtocol.AwakeSoulRequest.Builder.class);
    }

    private int bitField0_;
    public static final int SOULID_FIELD_NUMBER = 1;
    private long soulId_;
    /**
     * <pre>
     ** 需要合成的武魂的唯一ID
     * </pre>
     *
     * <code>int64 soulId = 1;</code>
     */
    public long getSoulId() {
      return soulId_;
    }

    public static final int COSTIDS_FIELD_NUMBER = 2;
    private java.util.List<java.lang.Long> costIds_;
    /**
     * <pre>
     ** 被消耗武魂唯一ID 注意：被消耗的武魂要求未养成过
     * </pre>
     *
     * <code>repeated int64 costIds = 2;</code>
     */
    public java.util.List<java.lang.Long>
        getCostIdsList() {
      return costIds_;
    }
    /**
     * <pre>
     ** 被消耗武魂唯一ID 注意：被消耗的武魂要求未养成过
     * </pre>
     *
     * <code>repeated int64 costIds = 2;</code>
     */
    public int getCostIdsCount() {
      return costIds_.size();
    }
    /**
     * <pre>
     ** 被消耗武魂唯一ID 注意：被消耗的武魂要求未养成过
     * </pre>
     *
     * <code>repeated int64 costIds = 2;</code>
     */
    public long getCostIds(int index) {
      return costIds_.get(index);
    }
    private int costIdsMemoizedSerializedSize = -1;

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (soulId_ != 0L) {
        output.writeInt64(1, soulId_);
      }
      if (getCostIdsList().size() > 0) {
        output.writeUInt32NoTag(18);
        output.writeUInt32NoTag(costIdsMemoizedSerializedSize);
      }
      for (int i = 0; i < costIds_.size(); i++) {
        output.writeInt64NoTag(costIds_.get(i));
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (soulId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, soulId_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < costIds_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt64SizeNoTag(costIds_.get(i));
        }
        size += dataSize;
        if (!getCostIdsList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        costIdsMemoizedSerializedSize = dataSize;
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof cn.daxiang.protocol.game.SoulProtocol.AwakeSoulRequest)) {
        return super.equals(obj);
      }
      cn.daxiang.protocol.game.SoulProtocol.AwakeSoulRequest other = (cn.daxiang.protocol.game.SoulProtocol.AwakeSoulRequest) obj;

      boolean result = true;
      result = result && (getSoulId()
          == other.getSoulId());
      result = result && getCostIdsList()
          .equals(other.getCostIdsList());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + SOULID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getSoulId());
      if (getCostIdsCount() > 0) {
        hash = (37 * hash) + COSTIDS_FIELD_NUMBER;
        hash = (53 * hash) + getCostIdsList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static cn.daxiang.protocol.game.SoulProtocol.AwakeSoulRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.SoulProtocol.AwakeSoulRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.SoulProtocol.AwakeSoulRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.SoulProtocol.AwakeSoulRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.SoulProtocol.AwakeSoulRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.SoulProtocol.AwakeSoulRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.SoulProtocol.AwakeSoulRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.SoulProtocol.AwakeSoulRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.SoulProtocol.AwakeSoulRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.SoulProtocol.AwakeSoulRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.SoulProtocol.AwakeSoulRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.SoulProtocol.AwakeSoulRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(cn.daxiang.protocol.game.SoulProtocol.AwakeSoulRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code AwakeSoulRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:AwakeSoulRequest)
        cn.daxiang.protocol.game.SoulProtocol.AwakeSoulRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.daxiang.protocol.game.SoulProtocol.internal_static_AwakeSoulRequest_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.daxiang.protocol.game.SoulProtocol.internal_static_AwakeSoulRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.daxiang.protocol.game.SoulProtocol.AwakeSoulRequest.class, cn.daxiang.protocol.game.SoulProtocol.AwakeSoulRequest.Builder.class);
      }

      // Construct using cn.daxiang.protocol.game.SoulProtocol.AwakeSoulRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        soulId_ = 0L;

        costIds_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return cn.daxiang.protocol.game.SoulProtocol.internal_static_AwakeSoulRequest_descriptor;
      }

      public cn.daxiang.protocol.game.SoulProtocol.AwakeSoulRequest getDefaultInstanceForType() {
        return cn.daxiang.protocol.game.SoulProtocol.AwakeSoulRequest.getDefaultInstance();
      }

      public cn.daxiang.protocol.game.SoulProtocol.AwakeSoulRequest build() {
        cn.daxiang.protocol.game.SoulProtocol.AwakeSoulRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public cn.daxiang.protocol.game.SoulProtocol.AwakeSoulRequest buildPartial() {
        cn.daxiang.protocol.game.SoulProtocol.AwakeSoulRequest result = new cn.daxiang.protocol.game.SoulProtocol.AwakeSoulRequest(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        result.soulId_ = soulId_;
        if (((bitField0_ & 0x00000002) == 0x00000002)) {
          costIds_ = java.util.Collections.unmodifiableList(costIds_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.costIds_ = costIds_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof cn.daxiang.protocol.game.SoulProtocol.AwakeSoulRequest) {
          return mergeFrom((cn.daxiang.protocol.game.SoulProtocol.AwakeSoulRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(cn.daxiang.protocol.game.SoulProtocol.AwakeSoulRequest other) {
        if (other == cn.daxiang.protocol.game.SoulProtocol.AwakeSoulRequest.getDefaultInstance()) return this;
        if (other.getSoulId() != 0L) {
          setSoulId(other.getSoulId());
        }
        if (!other.costIds_.isEmpty()) {
          if (costIds_.isEmpty()) {
            costIds_ = other.costIds_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureCostIdsIsMutable();
            costIds_.addAll(other.costIds_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        cn.daxiang.protocol.game.SoulProtocol.AwakeSoulRequest parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (cn.daxiang.protocol.game.SoulProtocol.AwakeSoulRequest) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long soulId_ ;
      /**
       * <pre>
       ** 需要合成的武魂的唯一ID
       * </pre>
       *
       * <code>int64 soulId = 1;</code>
       */
      public long getSoulId() {
        return soulId_;
      }
      /**
       * <pre>
       ** 需要合成的武魂的唯一ID
       * </pre>
       *
       * <code>int64 soulId = 1;</code>
       */
      public Builder setSoulId(long value) {
        
        soulId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 需要合成的武魂的唯一ID
       * </pre>
       *
       * <code>int64 soulId = 1;</code>
       */
      public Builder clearSoulId() {
        
        soulId_ = 0L;
        onChanged();
        return this;
      }

      private java.util.List<java.lang.Long> costIds_ = java.util.Collections.emptyList();
      private void ensureCostIdsIsMutable() {
        if (!((bitField0_ & 0x00000002) == 0x00000002)) {
          costIds_ = new java.util.ArrayList<java.lang.Long>(costIds_);
          bitField0_ |= 0x00000002;
         }
      }
      /**
       * <pre>
       ** 被消耗武魂唯一ID 注意：被消耗的武魂要求未养成过
       * </pre>
       *
       * <code>repeated int64 costIds = 2;</code>
       */
      public java.util.List<java.lang.Long>
          getCostIdsList() {
        return java.util.Collections.unmodifiableList(costIds_);
      }
      /**
       * <pre>
       ** 被消耗武魂唯一ID 注意：被消耗的武魂要求未养成过
       * </pre>
       *
       * <code>repeated int64 costIds = 2;</code>
       */
      public int getCostIdsCount() {
        return costIds_.size();
      }
      /**
       * <pre>
       ** 被消耗武魂唯一ID 注意：被消耗的武魂要求未养成过
       * </pre>
       *
       * <code>repeated int64 costIds = 2;</code>
       */
      public long getCostIds(int index) {
        return costIds_.get(index);
      }
      /**
       * <pre>
       ** 被消耗武魂唯一ID 注意：被消耗的武魂要求未养成过
       * </pre>
       *
       * <code>repeated int64 costIds = 2;</code>
       */
      public Builder setCostIds(
          int index, long value) {
        ensureCostIdsIsMutable();
        costIds_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 被消耗武魂唯一ID 注意：被消耗的武魂要求未养成过
       * </pre>
       *
       * <code>repeated int64 costIds = 2;</code>
       */
      public Builder addCostIds(long value) {
        ensureCostIdsIsMutable();
        costIds_.add(value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 被消耗武魂唯一ID 注意：被消耗的武魂要求未养成过
       * </pre>
       *
       * <code>repeated int64 costIds = 2;</code>
       */
      public Builder addAllCostIds(
          java.lang.Iterable<? extends java.lang.Long> values) {
        ensureCostIdsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, costIds_);
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 被消耗武魂唯一ID 注意：被消耗的武魂要求未养成过
       * </pre>
       *
       * <code>repeated int64 costIds = 2;</code>
       */
      public Builder clearCostIds() {
        costIds_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:AwakeSoulRequest)
    }

    // @@protoc_insertion_point(class_scope:AwakeSoulRequest)
    private static final cn.daxiang.protocol.game.SoulProtocol.AwakeSoulRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new cn.daxiang.protocol.game.SoulProtocol.AwakeSoulRequest();
    }

    public static cn.daxiang.protocol.game.SoulProtocol.AwakeSoulRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<AwakeSoulRequest>
        PARSER = new com.google.protobuf.AbstractParser<AwakeSoulRequest>() {
      public AwakeSoulRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new AwakeSoulRequest(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<AwakeSoulRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<AwakeSoulRequest> getParserForType() {
      return PARSER;
    }

    public cn.daxiang.protocol.game.SoulProtocol.AwakeSoulRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_SoulInfoResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_SoulInfoResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_SoulManualInfoResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_SoulManualInfoResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_AwakeSoulRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_AwakeSoulRequest_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\027game/soulProtocol.proto\032\031game/commonPr" +
      "otocol.proto\"(\n\020SoulInfoResponse\022\024\n\005soul" +
      "s\030\001 \003(\0132\005.Soul\")\n\026SoulManualInfoResponse" +
      "\022\017\n\007manuals\030\001 \003(\005\"3\n\020AwakeSoulRequest\022\016\n" +
      "\006soulId\030\001 \001(\003\022\017\n\007costIds\030\002 \003(\003*\311\001\n\007SoulC" +
      "md\022\021\n\rSOUL_CMD_NONE\020\000\022\r\n\tGET_SOULS\020\001\022\023\n\017" +
      "GET_SOUL_MANUAL\020\002\022\016\n\nAWAKE_SOUL\020\003\022\030\n\024ACT" +
      "IVATE_SOUL_MANUAL\020\004\022\030\n\024ONE_CLICK_AWAKE_S" +
      "OUL\020\005\022\022\n\016PUSH_SOUL_INFO\020d\022\031\n\025PUSH_SOUL_M" +
      "ANUAL_INFO\020e\022\024\n\020PUSH_SOUL_DELETE\020fB\034\n\030cn" +
      ".daxiang.protocol.gameH\001b\006proto3"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
        new com.google.protobuf.Descriptors.FileDescriptor.    InternalDescriptorAssigner() {
          public com.google.protobuf.ExtensionRegistry assignDescriptors(
              com.google.protobuf.Descriptors.FileDescriptor root) {
            descriptor = root;
            return null;
          }
        };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          cn.daxiang.protocol.game.CommonProtocol.getDescriptor(),
        }, assigner);
    internal_static_SoulInfoResponse_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_SoulInfoResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_SoulInfoResponse_descriptor,
        new java.lang.String[] { "Souls", });
    internal_static_SoulManualInfoResponse_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_SoulManualInfoResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_SoulManualInfoResponse_descriptor,
        new java.lang.String[] { "Manuals", });
    internal_static_AwakeSoulRequest_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_AwakeSoulRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_AwakeSoulRequest_descriptor,
        new java.lang.String[] { "SoulId", "CostIds", });
    cn.daxiang.protocol.game.CommonProtocol.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
