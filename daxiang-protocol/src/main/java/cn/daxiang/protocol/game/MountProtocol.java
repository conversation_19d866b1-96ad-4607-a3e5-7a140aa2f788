// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: game/mountProtocol.proto

package cn.daxiang.protocol.game;

public final class MountProtocol {
  private MountProtocol() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  /**
   * <pre>
   ** 坐骑 
   * </pre>
   *
   * Protobuf enum {@code MountCmd}
   */
  public enum MountCmd
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>MOUNT_CMD_NONE = 0;</code>
     */
    MOUNT_CMD_NONE(0),
    /**
     * <pre>
     **
     * 1.获取坐骑信息
     * 请求:{&#64;code Request}
     * 响应:{&#64;code MountInfoResponse}
     * </pre>
     *
     * <code>GET_MOUNT_INFO = 1;</code>
     */
    GET_MOUNT_INFO(1),
    /**
     * <pre>
     **
     * 2.获取坐骑列表
     * 请求:{&#64;code Request}
     * 响应:{&#64;code MountListResponse}
     * </pre>
     *
     * <code>GET_MOUNT_LIST = 2;</code>
     */
    GET_MOUNT_LIST(2),
    /**
     * <pre>
     **
     * 3.激活坐骑
     * 请求: {&#64;code IntPacket} mountCid
     * 响应: {&#64;code Response}
     * 推送:{&#64;code MountListResponse}
     * </pre>
     *
     * <code>MOUNT_ACTIVATION = 3;</code>
     */
    MOUNT_ACTIVATION(3),
    /**
     * <pre>
     **
     * 4.坐骑升星
     * 请求: {&#64;code IntPacket} mountCid
     * 响应: {&#64;code Response}
     * 推送:{&#64;code MountListResponse}
     * </pre>
     *
     * <code>MOUNT_STAR_UP = 4;</code>
     */
    MOUNT_STAR_UP(4),
    /**
     * <pre>
     **
     * 5.坐骑伏兽台升级
     * 请求: {&#64;code Request}
     * 响应: {&#64;code Response}
     * 推送: {&#64;code MountInfoResponse}
     * </pre>
     *
     * <code>MOUNT_PLATFORM_UPGRADE = 5;</code>
     */
    MOUNT_PLATFORM_UPGRADE(5),
    /**
     * <pre>
     **
     * 6.坐骑招募积分兑换礼包
     * 请求: {&#64;code IntPacket} 积分
     * 响应: {&#64;code RewardResultResponse}
     * 推送: {&#64;code MountInfoResponse}
     * </pre>
     *
     * <code>MOUNT_INTEGRAL_REWARD = 6;</code>
     */
    MOUNT_INTEGRAL_REWARD(6),
    /**
     * <pre>
     **
     * 7.坐骑特权礼包领取
     * 请求: {&#64;code IntPacket} 特权类型
     * 响应: {&#64;code RewardResult}
     * 推送: {&#64;code MountInfoResponse}
     * </pre>
     *
     * <code>MOUNT_PRIVILEGE_REWARD_RECEIVE = 7;</code>
     */
    MOUNT_PRIVILEGE_REWARD_RECEIVE(7),
    /**
     * <pre>
     **
     * 8.选择心愿坐骑（特权功能）
     * 请求: {&#64;code IntPacket} mountCid
     * 响应: {&#64;code Response}
     * 推送: {&#64;code MountInfoResponse}
     * </pre>
     *
     * <code>MOUNT_PRIVILEGE_CHOOSE_WISH = 8;</code>
     */
    MOUNT_PRIVILEGE_CHOOSE_WISH(8),
    /**
     * <pre>
     **
     * 9.坐骑招募
     * 请求: {&#64;code MountGachaRequest}
     * 响应: {&#64;code MountGachaResultResponse}
     * 推送: {&#64;code MountInfoResponse}
     * </pre>
     *
     * <code>MOUNT_GACHA = 9;</code>
     */
    MOUNT_GACHA(9),
    /**
     * <pre>
     **
     * 领取充值礼包
     * 请求:{&#64;code IntPacket} -配置Id
     * 响应:{&#64;code RewardResult}
     * 推送:{&#64;code MountGachaInfoResponse}
     * </pre>
     *
     * <code>RECEIVE_MOUNT_GACHA_CHARGE_REWARD = 10;</code>
     */
    RECEIVE_MOUNT_GACHA_CHARGE_REWARD(10),
    /**
     * <pre>
     **
     * 推送坐骑信息
     * 推送:{&#64;code MountInfoResponse}
     * </pre>
     *
     * <code>PUSH_MOUNT_INFO = 100;</code>
     */
    PUSH_MOUNT_INFO(100),
    /**
     * <pre>
     **
     * 推送坐骑列表
     * 推送:{&#64;code MountListResponse}
     * </pre>
     *
     * <code>PUSH_MOUNT_LIST = 101;</code>
     */
    PUSH_MOUNT_LIST(101),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>MOUNT_CMD_NONE = 0;</code>
     */
    public static final int MOUNT_CMD_NONE_VALUE = 0;
    /**
     * <pre>
     **
     * 1.获取坐骑信息
     * 请求:{&#64;code Request}
     * 响应:{&#64;code MountInfoResponse}
     * </pre>
     *
     * <code>GET_MOUNT_INFO = 1;</code>
     */
    public static final int GET_MOUNT_INFO_VALUE = 1;
    /**
     * <pre>
     **
     * 2.获取坐骑列表
     * 请求:{&#64;code Request}
     * 响应:{&#64;code MountListResponse}
     * </pre>
     *
     * <code>GET_MOUNT_LIST = 2;</code>
     */
    public static final int GET_MOUNT_LIST_VALUE = 2;
    /**
     * <pre>
     **
     * 3.激活坐骑
     * 请求: {&#64;code IntPacket} mountCid
     * 响应: {&#64;code Response}
     * 推送:{&#64;code MountListResponse}
     * </pre>
     *
     * <code>MOUNT_ACTIVATION = 3;</code>
     */
    public static final int MOUNT_ACTIVATION_VALUE = 3;
    /**
     * <pre>
     **
     * 4.坐骑升星
     * 请求: {&#64;code IntPacket} mountCid
     * 响应: {&#64;code Response}
     * 推送:{&#64;code MountListResponse}
     * </pre>
     *
     * <code>MOUNT_STAR_UP = 4;</code>
     */
    public static final int MOUNT_STAR_UP_VALUE = 4;
    /**
     * <pre>
     **
     * 5.坐骑伏兽台升级
     * 请求: {&#64;code Request}
     * 响应: {&#64;code Response}
     * 推送: {&#64;code MountInfoResponse}
     * </pre>
     *
     * <code>MOUNT_PLATFORM_UPGRADE = 5;</code>
     */
    public static final int MOUNT_PLATFORM_UPGRADE_VALUE = 5;
    /**
     * <pre>
     **
     * 6.坐骑招募积分兑换礼包
     * 请求: {&#64;code IntPacket} 积分
     * 响应: {&#64;code RewardResultResponse}
     * 推送: {&#64;code MountInfoResponse}
     * </pre>
     *
     * <code>MOUNT_INTEGRAL_REWARD = 6;</code>
     */
    public static final int MOUNT_INTEGRAL_REWARD_VALUE = 6;
    /**
     * <pre>
     **
     * 7.坐骑特权礼包领取
     * 请求: {&#64;code IntPacket} 特权类型
     * 响应: {&#64;code RewardResult}
     * 推送: {&#64;code MountInfoResponse}
     * </pre>
     *
     * <code>MOUNT_PRIVILEGE_REWARD_RECEIVE = 7;</code>
     */
    public static final int MOUNT_PRIVILEGE_REWARD_RECEIVE_VALUE = 7;
    /**
     * <pre>
     **
     * 8.选择心愿坐骑（特权功能）
     * 请求: {&#64;code IntPacket} mountCid
     * 响应: {&#64;code Response}
     * 推送: {&#64;code MountInfoResponse}
     * </pre>
     *
     * <code>MOUNT_PRIVILEGE_CHOOSE_WISH = 8;</code>
     */
    public static final int MOUNT_PRIVILEGE_CHOOSE_WISH_VALUE = 8;
    /**
     * <pre>
     **
     * 9.坐骑招募
     * 请求: {&#64;code MountGachaRequest}
     * 响应: {&#64;code MountGachaResultResponse}
     * 推送: {&#64;code MountInfoResponse}
     * </pre>
     *
     * <code>MOUNT_GACHA = 9;</code>
     */
    public static final int MOUNT_GACHA_VALUE = 9;
    /**
     * <pre>
     **
     * 领取充值礼包
     * 请求:{&#64;code IntPacket} -配置Id
     * 响应:{&#64;code RewardResult}
     * 推送:{&#64;code MountGachaInfoResponse}
     * </pre>
     *
     * <code>RECEIVE_MOUNT_GACHA_CHARGE_REWARD = 10;</code>
     */
    public static final int RECEIVE_MOUNT_GACHA_CHARGE_REWARD_VALUE = 10;
    /**
     * <pre>
     **
     * 推送坐骑信息
     * 推送:{&#64;code MountInfoResponse}
     * </pre>
     *
     * <code>PUSH_MOUNT_INFO = 100;</code>
     */
    public static final int PUSH_MOUNT_INFO_VALUE = 100;
    /**
     * <pre>
     **
     * 推送坐骑列表
     * 推送:{&#64;code MountListResponse}
     * </pre>
     *
     * <code>PUSH_MOUNT_LIST = 101;</code>
     */
    public static final int PUSH_MOUNT_LIST_VALUE = 101;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static MountCmd valueOf(int value) {
      return forNumber(value);
    }

    public static MountCmd forNumber(int value) {
      switch (value) {
        case 0: return MOUNT_CMD_NONE;
        case 1: return GET_MOUNT_INFO;
        case 2: return GET_MOUNT_LIST;
        case 3: return MOUNT_ACTIVATION;
        case 4: return MOUNT_STAR_UP;
        case 5: return MOUNT_PLATFORM_UPGRADE;
        case 6: return MOUNT_INTEGRAL_REWARD;
        case 7: return MOUNT_PRIVILEGE_REWARD_RECEIVE;
        case 8: return MOUNT_PRIVILEGE_CHOOSE_WISH;
        case 9: return MOUNT_GACHA;
        case 10: return RECEIVE_MOUNT_GACHA_CHARGE_REWARD;
        case 100: return PUSH_MOUNT_INFO;
        case 101: return PUSH_MOUNT_LIST;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<MountCmd>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        MountCmd> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<MountCmd>() {
            public MountCmd findValueByNumber(int number) {
              return MountCmd.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.MountProtocol.getDescriptor().getEnumTypes().get(0);
    }

    private static final MountCmd[] VALUES = values();

    public static MountCmd valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private MountCmd(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:MountCmd)
  }

  public interface MountInfoResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:MountInfoResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     ** 伏兽台等级 
     * </pre>
     *
     * <code>int32 mountPlatformLevel = 1;</code>
     */
    int getMountPlatformLevel();

    /**
     * <pre>
     ** 所有特权 
     * </pre>
     *
     * <code>repeated .MountPrivilegeEntity privileges = 2;</code>
     */
    java.util.List<cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity> 
        getPrivilegesList();
    /**
     * <pre>
     ** 所有特权 
     * </pre>
     *
     * <code>repeated .MountPrivilegeEntity privileges = 2;</code>
     */
    cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity getPrivileges(int index);
    /**
     * <pre>
     ** 所有特权 
     * </pre>
     *
     * <code>repeated .MountPrivilegeEntity privileges = 2;</code>
     */
    int getPrivilegesCount();
    /**
     * <pre>
     ** 所有特权 
     * </pre>
     *
     * <code>repeated .MountPrivilegeEntity privileges = 2;</code>
     */
    java.util.List<? extends cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntityOrBuilder> 
        getPrivilegesOrBuilderList();
    /**
     * <pre>
     ** 所有特权 
     * </pre>
     *
     * <code>repeated .MountPrivilegeEntity privileges = 2;</code>
     */
    cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntityOrBuilder getPrivilegesOrBuilder(
        int index);

    /**
     * <pre>
     ** 已领积分奖励（积分） 
     * </pre>
     *
     * <code>repeated int32 integralRewardReceive = 3;</code>
     */
    java.util.List<java.lang.Integer> getIntegralRewardReceiveList();
    /**
     * <pre>
     ** 已领积分奖励（积分） 
     * </pre>
     *
     * <code>repeated int32 integralRewardReceive = 3;</code>
     */
    int getIntegralRewardReceiveCount();
    /**
     * <pre>
     ** 已领积分奖励（积分） 
     * </pre>
     *
     * <code>repeated int32 integralRewardReceive = 3;</code>
     */
    int getIntegralRewardReceive(int index);

    /**
     * <pre>
     ** 心愿坐骑配置ID 
     * </pre>
     *
     * <code>int32 wishMountCid = 4;</code>
     */
    int getWishMountCid();

    /**
     * <pre>
     ** 今天已使用的免费招募次数 
     * </pre>
     *
     * <code>int32 gachaUseFreeTimes = 5;</code>
     */
    int getGachaUseFreeTimes();

    /**
     * <pre>
     ** 今天已使用的钻石招募次数 
     * </pre>
     *
     * <code>int32 gachaUseDiamondTimes = 6;</code>
     */
    int getGachaUseDiamondTimes();

    /**
     * <pre>
     ** 招募积分 
     * </pre>
     *
     * <code>int32 gachaIntegral = 7;</code>
     */
    int getGachaIntegral();

    /**
     * <pre>
     ** key:id,value:当天购买数量 
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; dayBuyTimes = 8;</code>
     */
    int getDayBuyTimesCount();
    /**
     * <pre>
     ** key:id,value:当天购买数量 
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; dayBuyTimes = 8;</code>
     */
    boolean containsDayBuyTimes(
        int key);
    /**
     * Use {@link #getDayBuyTimesMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Integer, java.lang.Integer>
    getDayBuyTimes();
    /**
     * <pre>
     ** key:id,value:当天购买数量 
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; dayBuyTimes = 8;</code>
     */
    java.util.Map<java.lang.Integer, java.lang.Integer>
    getDayBuyTimesMap();
    /**
     * <pre>
     ** key:id,value:当天购买数量 
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; dayBuyTimes = 8;</code>
     */

    int getDayBuyTimesOrDefault(
        int key,
        int defaultValue);
    /**
     * <pre>
     ** key:id,value:当天购买数量 
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; dayBuyTimes = 8;</code>
     */

    int getDayBuyTimesOrThrow(
        int key);

    /**
     * <pre>
     ** key:id,value:累计购买数量 
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; buyTimes = 9;</code>
     */
    int getBuyTimesCount();
    /**
     * <pre>
     ** key:id,value:累计购买数量 
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; buyTimes = 9;</code>
     */
    boolean containsBuyTimes(
        int key);
    /**
     * Use {@link #getBuyTimesMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Integer, java.lang.Integer>
    getBuyTimes();
    /**
     * <pre>
     ** key:id,value:累计购买数量 
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; buyTimes = 9;</code>
     */
    java.util.Map<java.lang.Integer, java.lang.Integer>
    getBuyTimesMap();
    /**
     * <pre>
     ** key:id,value:累计购买数量 
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; buyTimes = 9;</code>
     */

    int getBuyTimesOrDefault(
        int key,
        int defaultValue);
    /**
     * <pre>
     ** key:id,value:累计购买数量 
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; buyTimes = 9;</code>
     */

    int getBuyTimesOrThrow(
        int key);

    /**
     * <pre>
     ** key:id,value:累计已领奖励数量 
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; receives = 10;</code>
     */
    int getReceivesCount();
    /**
     * <pre>
     ** key:id,value:累计已领奖励数量 
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; receives = 10;</code>
     */
    boolean containsReceives(
        int key);
    /**
     * Use {@link #getReceivesMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Integer, java.lang.Integer>
    getReceives();
    /**
     * <pre>
     ** key:id,value:累计已领奖励数量 
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; receives = 10;</code>
     */
    java.util.Map<java.lang.Integer, java.lang.Integer>
    getReceivesMap();
    /**
     * <pre>
     ** key:id,value:累计已领奖励数量 
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; receives = 10;</code>
     */

    int getReceivesOrDefault(
        int key,
        int defaultValue);
    /**
     * <pre>
     ** key:id,value:累计已领奖励数量 
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; receives = 10;</code>
     */

    int getReceivesOrThrow(
        int key);
  }
  /**
   * <pre>
   ** 坐骑信息响应 
   * </pre>
   *
   * Protobuf type {@code MountInfoResponse}
   */
  public  static final class MountInfoResponse extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:MountInfoResponse)
      MountInfoResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use MountInfoResponse.newBuilder() to construct.
    private MountInfoResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private MountInfoResponse() {
      mountPlatformLevel_ = 0;
      privileges_ = java.util.Collections.emptyList();
      integralRewardReceive_ = java.util.Collections.emptyList();
      wishMountCid_ = 0;
      gachaUseFreeTimes_ = 0;
      gachaUseDiamondTimes_ = 0;
      gachaIntegral_ = 0;
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private MountInfoResponse(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {

              mountPlatformLevel_ = input.readInt32();
              break;
            }
            case 18: {
              if (!((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
                privileges_ = new java.util.ArrayList<cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity>();
                mutable_bitField0_ |= 0x00000002;
              }
              privileges_.add(
                  input.readMessage(cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity.parser(), extensionRegistry));
              break;
            }
            case 24: {
              if (!((mutable_bitField0_ & 0x00000004) == 0x00000004)) {
                integralRewardReceive_ = new java.util.ArrayList<java.lang.Integer>();
                mutable_bitField0_ |= 0x00000004;
              }
              integralRewardReceive_.add(input.readInt32());
              break;
            }
            case 26: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000004) == 0x00000004) && input.getBytesUntilLimit() > 0) {
                integralRewardReceive_ = new java.util.ArrayList<java.lang.Integer>();
                mutable_bitField0_ |= 0x00000004;
              }
              while (input.getBytesUntilLimit() > 0) {
                integralRewardReceive_.add(input.readInt32());
              }
              input.popLimit(limit);
              break;
            }
            case 32: {

              wishMountCid_ = input.readInt32();
              break;
            }
            case 40: {

              gachaUseFreeTimes_ = input.readInt32();
              break;
            }
            case 48: {

              gachaUseDiamondTimes_ = input.readInt32();
              break;
            }
            case 56: {

              gachaIntegral_ = input.readInt32();
              break;
            }
            case 66: {
              if (!((mutable_bitField0_ & 0x00000080) == 0x00000080)) {
                dayBuyTimes_ = com.google.protobuf.MapField.newMapField(
                    DayBuyTimesDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000080;
              }
              com.google.protobuf.MapEntry<java.lang.Integer, java.lang.Integer>
              dayBuyTimes__ = input.readMessage(
                  DayBuyTimesDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              dayBuyTimes_.getMutableMap().put(
                  dayBuyTimes__.getKey(), dayBuyTimes__.getValue());
              break;
            }
            case 74: {
              if (!((mutable_bitField0_ & 0x00000100) == 0x00000100)) {
                buyTimes_ = com.google.protobuf.MapField.newMapField(
                    BuyTimesDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000100;
              }
              com.google.protobuf.MapEntry<java.lang.Integer, java.lang.Integer>
              buyTimes__ = input.readMessage(
                  BuyTimesDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              buyTimes_.getMutableMap().put(
                  buyTimes__.getKey(), buyTimes__.getValue());
              break;
            }
            case 82: {
              if (!((mutable_bitField0_ & 0x00000200) == 0x00000200)) {
                receives_ = com.google.protobuf.MapField.newMapField(
                    ReceivesDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000200;
              }
              com.google.protobuf.MapEntry<java.lang.Integer, java.lang.Integer>
              receives__ = input.readMessage(
                  ReceivesDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              receives_.getMutableMap().put(
                  receives__.getKey(), receives__.getValue());
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
          privileges_ = java.util.Collections.unmodifiableList(privileges_);
        }
        if (((mutable_bitField0_ & 0x00000004) == 0x00000004)) {
          integralRewardReceive_ = java.util.Collections.unmodifiableList(integralRewardReceive_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.MountProtocol.internal_static_MountInfoResponse_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 8:
          return internalGetDayBuyTimes();
        case 9:
          return internalGetBuyTimes();
        case 10:
          return internalGetReceives();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.daxiang.protocol.game.MountProtocol.internal_static_MountInfoResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.daxiang.protocol.game.MountProtocol.MountInfoResponse.class, cn.daxiang.protocol.game.MountProtocol.MountInfoResponse.Builder.class);
    }

    private int bitField0_;
    public static final int MOUNTPLATFORMLEVEL_FIELD_NUMBER = 1;
    private int mountPlatformLevel_;
    /**
     * <pre>
     ** 伏兽台等级 
     * </pre>
     *
     * <code>int32 mountPlatformLevel = 1;</code>
     */
    public int getMountPlatformLevel() {
      return mountPlatformLevel_;
    }

    public static final int PRIVILEGES_FIELD_NUMBER = 2;
    private java.util.List<cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity> privileges_;
    /**
     * <pre>
     ** 所有特权 
     * </pre>
     *
     * <code>repeated .MountPrivilegeEntity privileges = 2;</code>
     */
    public java.util.List<cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity> getPrivilegesList() {
      return privileges_;
    }
    /**
     * <pre>
     ** 所有特权 
     * </pre>
     *
     * <code>repeated .MountPrivilegeEntity privileges = 2;</code>
     */
    public java.util.List<? extends cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntityOrBuilder> 
        getPrivilegesOrBuilderList() {
      return privileges_;
    }
    /**
     * <pre>
     ** 所有特权 
     * </pre>
     *
     * <code>repeated .MountPrivilegeEntity privileges = 2;</code>
     */
    public int getPrivilegesCount() {
      return privileges_.size();
    }
    /**
     * <pre>
     ** 所有特权 
     * </pre>
     *
     * <code>repeated .MountPrivilegeEntity privileges = 2;</code>
     */
    public cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity getPrivileges(int index) {
      return privileges_.get(index);
    }
    /**
     * <pre>
     ** 所有特权 
     * </pre>
     *
     * <code>repeated .MountPrivilegeEntity privileges = 2;</code>
     */
    public cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntityOrBuilder getPrivilegesOrBuilder(
        int index) {
      return privileges_.get(index);
    }

    public static final int INTEGRALREWARDRECEIVE_FIELD_NUMBER = 3;
    private java.util.List<java.lang.Integer> integralRewardReceive_;
    /**
     * <pre>
     ** 已领积分奖励（积分） 
     * </pre>
     *
     * <code>repeated int32 integralRewardReceive = 3;</code>
     */
    public java.util.List<java.lang.Integer>
        getIntegralRewardReceiveList() {
      return integralRewardReceive_;
    }
    /**
     * <pre>
     ** 已领积分奖励（积分） 
     * </pre>
     *
     * <code>repeated int32 integralRewardReceive = 3;</code>
     */
    public int getIntegralRewardReceiveCount() {
      return integralRewardReceive_.size();
    }
    /**
     * <pre>
     ** 已领积分奖励（积分） 
     * </pre>
     *
     * <code>repeated int32 integralRewardReceive = 3;</code>
     */
    public int getIntegralRewardReceive(int index) {
      return integralRewardReceive_.get(index);
    }
    private int integralRewardReceiveMemoizedSerializedSize = -1;

    public static final int WISHMOUNTCID_FIELD_NUMBER = 4;
    private int wishMountCid_;
    /**
     * <pre>
     ** 心愿坐骑配置ID 
     * </pre>
     *
     * <code>int32 wishMountCid = 4;</code>
     */
    public int getWishMountCid() {
      return wishMountCid_;
    }

    public static final int GACHAUSEFREETIMES_FIELD_NUMBER = 5;
    private int gachaUseFreeTimes_;
    /**
     * <pre>
     ** 今天已使用的免费招募次数 
     * </pre>
     *
     * <code>int32 gachaUseFreeTimes = 5;</code>
     */
    public int getGachaUseFreeTimes() {
      return gachaUseFreeTimes_;
    }

    public static final int GACHAUSEDIAMONDTIMES_FIELD_NUMBER = 6;
    private int gachaUseDiamondTimes_;
    /**
     * <pre>
     ** 今天已使用的钻石招募次数 
     * </pre>
     *
     * <code>int32 gachaUseDiamondTimes = 6;</code>
     */
    public int getGachaUseDiamondTimes() {
      return gachaUseDiamondTimes_;
    }

    public static final int GACHAINTEGRAL_FIELD_NUMBER = 7;
    private int gachaIntegral_;
    /**
     * <pre>
     ** 招募积分 
     * </pre>
     *
     * <code>int32 gachaIntegral = 7;</code>
     */
    public int getGachaIntegral() {
      return gachaIntegral_;
    }

    public static final int DAYBUYTIMES_FIELD_NUMBER = 8;
    private static final class DayBuyTimesDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Integer, java.lang.Integer> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Integer, java.lang.Integer>newDefaultInstance(
                  cn.daxiang.protocol.game.MountProtocol.internal_static_MountInfoResponse_DayBuyTimesEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0,
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0);
    }
    private com.google.protobuf.MapField<
        java.lang.Integer, java.lang.Integer> dayBuyTimes_;
    private com.google.protobuf.MapField<java.lang.Integer, java.lang.Integer>
    internalGetDayBuyTimes() {
      if (dayBuyTimes_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            DayBuyTimesDefaultEntryHolder.defaultEntry);
      }
      return dayBuyTimes_;
    }

    public int getDayBuyTimesCount() {
      return internalGetDayBuyTimes().getMap().size();
    }
    /**
     * <pre>
     ** key:id,value:当天购买数量 
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; dayBuyTimes = 8;</code>
     */

    public boolean containsDayBuyTimes(
        int key) {
      
      return internalGetDayBuyTimes().getMap().containsKey(key);
    }
    /**
     * Use {@link #getDayBuyTimesMap()} instead.
     */
    @java.lang.Deprecated
    public java.util.Map<java.lang.Integer, java.lang.Integer> getDayBuyTimes() {
      return getDayBuyTimesMap();
    }
    /**
     * <pre>
     ** key:id,value:当天购买数量 
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; dayBuyTimes = 8;</code>
     */

    public java.util.Map<java.lang.Integer, java.lang.Integer> getDayBuyTimesMap() {
      return internalGetDayBuyTimes().getMap();
    }
    /**
     * <pre>
     ** key:id,value:当天购买数量 
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; dayBuyTimes = 8;</code>
     */

    public int getDayBuyTimesOrDefault(
        int key,
        int defaultValue) {
      
      java.util.Map<java.lang.Integer, java.lang.Integer> map =
          internalGetDayBuyTimes().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <pre>
     ** key:id,value:当天购买数量 
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; dayBuyTimes = 8;</code>
     */

    public int getDayBuyTimesOrThrow(
        int key) {
      
      java.util.Map<java.lang.Integer, java.lang.Integer> map =
          internalGetDayBuyTimes().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    public static final int BUYTIMES_FIELD_NUMBER = 9;
    private static final class BuyTimesDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Integer, java.lang.Integer> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Integer, java.lang.Integer>newDefaultInstance(
                  cn.daxiang.protocol.game.MountProtocol.internal_static_MountInfoResponse_BuyTimesEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0,
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0);
    }
    private com.google.protobuf.MapField<
        java.lang.Integer, java.lang.Integer> buyTimes_;
    private com.google.protobuf.MapField<java.lang.Integer, java.lang.Integer>
    internalGetBuyTimes() {
      if (buyTimes_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            BuyTimesDefaultEntryHolder.defaultEntry);
      }
      return buyTimes_;
    }

    public int getBuyTimesCount() {
      return internalGetBuyTimes().getMap().size();
    }
    /**
     * <pre>
     ** key:id,value:累计购买数量 
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; buyTimes = 9;</code>
     */

    public boolean containsBuyTimes(
        int key) {
      
      return internalGetBuyTimes().getMap().containsKey(key);
    }
    /**
     * Use {@link #getBuyTimesMap()} instead.
     */
    @java.lang.Deprecated
    public java.util.Map<java.lang.Integer, java.lang.Integer> getBuyTimes() {
      return getBuyTimesMap();
    }
    /**
     * <pre>
     ** key:id,value:累计购买数量 
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; buyTimes = 9;</code>
     */

    public java.util.Map<java.lang.Integer, java.lang.Integer> getBuyTimesMap() {
      return internalGetBuyTimes().getMap();
    }
    /**
     * <pre>
     ** key:id,value:累计购买数量 
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; buyTimes = 9;</code>
     */

    public int getBuyTimesOrDefault(
        int key,
        int defaultValue) {
      
      java.util.Map<java.lang.Integer, java.lang.Integer> map =
          internalGetBuyTimes().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <pre>
     ** key:id,value:累计购买数量 
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; buyTimes = 9;</code>
     */

    public int getBuyTimesOrThrow(
        int key) {
      
      java.util.Map<java.lang.Integer, java.lang.Integer> map =
          internalGetBuyTimes().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    public static final int RECEIVES_FIELD_NUMBER = 10;
    private static final class ReceivesDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Integer, java.lang.Integer> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Integer, java.lang.Integer>newDefaultInstance(
                  cn.daxiang.protocol.game.MountProtocol.internal_static_MountInfoResponse_ReceivesEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0,
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0);
    }
    private com.google.protobuf.MapField<
        java.lang.Integer, java.lang.Integer> receives_;
    private com.google.protobuf.MapField<java.lang.Integer, java.lang.Integer>
    internalGetReceives() {
      if (receives_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            ReceivesDefaultEntryHolder.defaultEntry);
      }
      return receives_;
    }

    public int getReceivesCount() {
      return internalGetReceives().getMap().size();
    }
    /**
     * <pre>
     ** key:id,value:累计已领奖励数量 
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; receives = 10;</code>
     */

    public boolean containsReceives(
        int key) {
      
      return internalGetReceives().getMap().containsKey(key);
    }
    /**
     * Use {@link #getReceivesMap()} instead.
     */
    @java.lang.Deprecated
    public java.util.Map<java.lang.Integer, java.lang.Integer> getReceives() {
      return getReceivesMap();
    }
    /**
     * <pre>
     ** key:id,value:累计已领奖励数量 
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; receives = 10;</code>
     */

    public java.util.Map<java.lang.Integer, java.lang.Integer> getReceivesMap() {
      return internalGetReceives().getMap();
    }
    /**
     * <pre>
     ** key:id,value:累计已领奖励数量 
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; receives = 10;</code>
     */

    public int getReceivesOrDefault(
        int key,
        int defaultValue) {
      
      java.util.Map<java.lang.Integer, java.lang.Integer> map =
          internalGetReceives().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <pre>
     ** key:id,value:累计已领奖励数量 
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; receives = 10;</code>
     */

    public int getReceivesOrThrow(
        int key) {
      
      java.util.Map<java.lang.Integer, java.lang.Integer> map =
          internalGetReceives().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (mountPlatformLevel_ != 0) {
        output.writeInt32(1, mountPlatformLevel_);
      }
      for (int i = 0; i < privileges_.size(); i++) {
        output.writeMessage(2, privileges_.get(i));
      }
      if (getIntegralRewardReceiveList().size() > 0) {
        output.writeUInt32NoTag(26);
        output.writeUInt32NoTag(integralRewardReceiveMemoizedSerializedSize);
      }
      for (int i = 0; i < integralRewardReceive_.size(); i++) {
        output.writeInt32NoTag(integralRewardReceive_.get(i));
      }
      if (wishMountCid_ != 0) {
        output.writeInt32(4, wishMountCid_);
      }
      if (gachaUseFreeTimes_ != 0) {
        output.writeInt32(5, gachaUseFreeTimes_);
      }
      if (gachaUseDiamondTimes_ != 0) {
        output.writeInt32(6, gachaUseDiamondTimes_);
      }
      if (gachaIntegral_ != 0) {
        output.writeInt32(7, gachaIntegral_);
      }
      com.google.protobuf.GeneratedMessageV3
        .serializeIntegerMapTo(
          output,
          internalGetDayBuyTimes(),
          DayBuyTimesDefaultEntryHolder.defaultEntry,
          8);
      com.google.protobuf.GeneratedMessageV3
        .serializeIntegerMapTo(
          output,
          internalGetBuyTimes(),
          BuyTimesDefaultEntryHolder.defaultEntry,
          9);
      com.google.protobuf.GeneratedMessageV3
        .serializeIntegerMapTo(
          output,
          internalGetReceives(),
          ReceivesDefaultEntryHolder.defaultEntry,
          10);
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (mountPlatformLevel_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, mountPlatformLevel_);
      }
      for (int i = 0; i < privileges_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, privileges_.get(i));
      }
      {
        int dataSize = 0;
        for (int i = 0; i < integralRewardReceive_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt32SizeNoTag(integralRewardReceive_.get(i));
        }
        size += dataSize;
        if (!getIntegralRewardReceiveList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        integralRewardReceiveMemoizedSerializedSize = dataSize;
      }
      if (wishMountCid_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, wishMountCid_);
      }
      if (gachaUseFreeTimes_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(5, gachaUseFreeTimes_);
      }
      if (gachaUseDiamondTimes_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(6, gachaUseDiamondTimes_);
      }
      if (gachaIntegral_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(7, gachaIntegral_);
      }
      for (java.util.Map.Entry<java.lang.Integer, java.lang.Integer> entry
           : internalGetDayBuyTimes().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Integer, java.lang.Integer>
        dayBuyTimes__ = DayBuyTimesDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(8, dayBuyTimes__);
      }
      for (java.util.Map.Entry<java.lang.Integer, java.lang.Integer> entry
           : internalGetBuyTimes().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Integer, java.lang.Integer>
        buyTimes__ = BuyTimesDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(9, buyTimes__);
      }
      for (java.util.Map.Entry<java.lang.Integer, java.lang.Integer> entry
           : internalGetReceives().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Integer, java.lang.Integer>
        receives__ = ReceivesDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(10, receives__);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof cn.daxiang.protocol.game.MountProtocol.MountInfoResponse)) {
        return super.equals(obj);
      }
      cn.daxiang.protocol.game.MountProtocol.MountInfoResponse other = (cn.daxiang.protocol.game.MountProtocol.MountInfoResponse) obj;

      boolean result = true;
      result = result && (getMountPlatformLevel()
          == other.getMountPlatformLevel());
      result = result && getPrivilegesList()
          .equals(other.getPrivilegesList());
      result = result && getIntegralRewardReceiveList()
          .equals(other.getIntegralRewardReceiveList());
      result = result && (getWishMountCid()
          == other.getWishMountCid());
      result = result && (getGachaUseFreeTimes()
          == other.getGachaUseFreeTimes());
      result = result && (getGachaUseDiamondTimes()
          == other.getGachaUseDiamondTimes());
      result = result && (getGachaIntegral()
          == other.getGachaIntegral());
      result = result && internalGetDayBuyTimes().equals(
          other.internalGetDayBuyTimes());
      result = result && internalGetBuyTimes().equals(
          other.internalGetBuyTimes());
      result = result && internalGetReceives().equals(
          other.internalGetReceives());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + MOUNTPLATFORMLEVEL_FIELD_NUMBER;
      hash = (53 * hash) + getMountPlatformLevel();
      if (getPrivilegesCount() > 0) {
        hash = (37 * hash) + PRIVILEGES_FIELD_NUMBER;
        hash = (53 * hash) + getPrivilegesList().hashCode();
      }
      if (getIntegralRewardReceiveCount() > 0) {
        hash = (37 * hash) + INTEGRALREWARDRECEIVE_FIELD_NUMBER;
        hash = (53 * hash) + getIntegralRewardReceiveList().hashCode();
      }
      hash = (37 * hash) + WISHMOUNTCID_FIELD_NUMBER;
      hash = (53 * hash) + getWishMountCid();
      hash = (37 * hash) + GACHAUSEFREETIMES_FIELD_NUMBER;
      hash = (53 * hash) + getGachaUseFreeTimes();
      hash = (37 * hash) + GACHAUSEDIAMONDTIMES_FIELD_NUMBER;
      hash = (53 * hash) + getGachaUseDiamondTimes();
      hash = (37 * hash) + GACHAINTEGRAL_FIELD_NUMBER;
      hash = (53 * hash) + getGachaIntegral();
      if (!internalGetDayBuyTimes().getMap().isEmpty()) {
        hash = (37 * hash) + DAYBUYTIMES_FIELD_NUMBER;
        hash = (53 * hash) + internalGetDayBuyTimes().hashCode();
      }
      if (!internalGetBuyTimes().getMap().isEmpty()) {
        hash = (37 * hash) + BUYTIMES_FIELD_NUMBER;
        hash = (53 * hash) + internalGetBuyTimes().hashCode();
      }
      if (!internalGetReceives().getMap().isEmpty()) {
        hash = (37 * hash) + RECEIVES_FIELD_NUMBER;
        hash = (53 * hash) + internalGetReceives().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static cn.daxiang.protocol.game.MountProtocol.MountInfoResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.MountProtocol.MountInfoResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.MountProtocol.MountInfoResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.MountProtocol.MountInfoResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.MountProtocol.MountInfoResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.MountProtocol.MountInfoResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.MountProtocol.MountInfoResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.MountProtocol.MountInfoResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.MountProtocol.MountInfoResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.MountProtocol.MountInfoResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.MountProtocol.MountInfoResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.MountProtocol.MountInfoResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(cn.daxiang.protocol.game.MountProtocol.MountInfoResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** 坐骑信息响应 
     * </pre>
     *
     * Protobuf type {@code MountInfoResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:MountInfoResponse)
        cn.daxiang.protocol.game.MountProtocol.MountInfoResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.daxiang.protocol.game.MountProtocol.internal_static_MountInfoResponse_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 8:
            return internalGetDayBuyTimes();
          case 9:
            return internalGetBuyTimes();
          case 10:
            return internalGetReceives();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 8:
            return internalGetMutableDayBuyTimes();
          case 9:
            return internalGetMutableBuyTimes();
          case 10:
            return internalGetMutableReceives();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.daxiang.protocol.game.MountProtocol.internal_static_MountInfoResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.daxiang.protocol.game.MountProtocol.MountInfoResponse.class, cn.daxiang.protocol.game.MountProtocol.MountInfoResponse.Builder.class);
      }

      // Construct using cn.daxiang.protocol.game.MountProtocol.MountInfoResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getPrivilegesFieldBuilder();
        }
      }
      public Builder clear() {
        super.clear();
        mountPlatformLevel_ = 0;

        if (privilegesBuilder_ == null) {
          privileges_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
        } else {
          privilegesBuilder_.clear();
        }
        integralRewardReceive_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);
        wishMountCid_ = 0;

        gachaUseFreeTimes_ = 0;

        gachaUseDiamondTimes_ = 0;

        gachaIntegral_ = 0;

        internalGetMutableDayBuyTimes().clear();
        internalGetMutableBuyTimes().clear();
        internalGetMutableReceives().clear();
        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return cn.daxiang.protocol.game.MountProtocol.internal_static_MountInfoResponse_descriptor;
      }

      public cn.daxiang.protocol.game.MountProtocol.MountInfoResponse getDefaultInstanceForType() {
        return cn.daxiang.protocol.game.MountProtocol.MountInfoResponse.getDefaultInstance();
      }

      public cn.daxiang.protocol.game.MountProtocol.MountInfoResponse build() {
        cn.daxiang.protocol.game.MountProtocol.MountInfoResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public cn.daxiang.protocol.game.MountProtocol.MountInfoResponse buildPartial() {
        cn.daxiang.protocol.game.MountProtocol.MountInfoResponse result = new cn.daxiang.protocol.game.MountProtocol.MountInfoResponse(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        result.mountPlatformLevel_ = mountPlatformLevel_;
        if (privilegesBuilder_ == null) {
          if (((bitField0_ & 0x00000002) == 0x00000002)) {
            privileges_ = java.util.Collections.unmodifiableList(privileges_);
            bitField0_ = (bitField0_ & ~0x00000002);
          }
          result.privileges_ = privileges_;
        } else {
          result.privileges_ = privilegesBuilder_.build();
        }
        if (((bitField0_ & 0x00000004) == 0x00000004)) {
          integralRewardReceive_ = java.util.Collections.unmodifiableList(integralRewardReceive_);
          bitField0_ = (bitField0_ & ~0x00000004);
        }
        result.integralRewardReceive_ = integralRewardReceive_;
        result.wishMountCid_ = wishMountCid_;
        result.gachaUseFreeTimes_ = gachaUseFreeTimes_;
        result.gachaUseDiamondTimes_ = gachaUseDiamondTimes_;
        result.gachaIntegral_ = gachaIntegral_;
        result.dayBuyTimes_ = internalGetDayBuyTimes();
        result.dayBuyTimes_.makeImmutable();
        result.buyTimes_ = internalGetBuyTimes();
        result.buyTimes_.makeImmutable();
        result.receives_ = internalGetReceives();
        result.receives_.makeImmutable();
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof cn.daxiang.protocol.game.MountProtocol.MountInfoResponse) {
          return mergeFrom((cn.daxiang.protocol.game.MountProtocol.MountInfoResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(cn.daxiang.protocol.game.MountProtocol.MountInfoResponse other) {
        if (other == cn.daxiang.protocol.game.MountProtocol.MountInfoResponse.getDefaultInstance()) return this;
        if (other.getMountPlatformLevel() != 0) {
          setMountPlatformLevel(other.getMountPlatformLevel());
        }
        if (privilegesBuilder_ == null) {
          if (!other.privileges_.isEmpty()) {
            if (privileges_.isEmpty()) {
              privileges_ = other.privileges_;
              bitField0_ = (bitField0_ & ~0x00000002);
            } else {
              ensurePrivilegesIsMutable();
              privileges_.addAll(other.privileges_);
            }
            onChanged();
          }
        } else {
          if (!other.privileges_.isEmpty()) {
            if (privilegesBuilder_.isEmpty()) {
              privilegesBuilder_.dispose();
              privilegesBuilder_ = null;
              privileges_ = other.privileges_;
              bitField0_ = (bitField0_ & ~0x00000002);
              privilegesBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getPrivilegesFieldBuilder() : null;
            } else {
              privilegesBuilder_.addAllMessages(other.privileges_);
            }
          }
        }
        if (!other.integralRewardReceive_.isEmpty()) {
          if (integralRewardReceive_.isEmpty()) {
            integralRewardReceive_ = other.integralRewardReceive_;
            bitField0_ = (bitField0_ & ~0x00000004);
          } else {
            ensureIntegralRewardReceiveIsMutable();
            integralRewardReceive_.addAll(other.integralRewardReceive_);
          }
          onChanged();
        }
        if (other.getWishMountCid() != 0) {
          setWishMountCid(other.getWishMountCid());
        }
        if (other.getGachaUseFreeTimes() != 0) {
          setGachaUseFreeTimes(other.getGachaUseFreeTimes());
        }
        if (other.getGachaUseDiamondTimes() != 0) {
          setGachaUseDiamondTimes(other.getGachaUseDiamondTimes());
        }
        if (other.getGachaIntegral() != 0) {
          setGachaIntegral(other.getGachaIntegral());
        }
        internalGetMutableDayBuyTimes().mergeFrom(
            other.internalGetDayBuyTimes());
        internalGetMutableBuyTimes().mergeFrom(
            other.internalGetBuyTimes());
        internalGetMutableReceives().mergeFrom(
            other.internalGetReceives());
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        cn.daxiang.protocol.game.MountProtocol.MountInfoResponse parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (cn.daxiang.protocol.game.MountProtocol.MountInfoResponse) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int mountPlatformLevel_ ;
      /**
       * <pre>
       ** 伏兽台等级 
       * </pre>
       *
       * <code>int32 mountPlatformLevel = 1;</code>
       */
      public int getMountPlatformLevel() {
        return mountPlatformLevel_;
      }
      /**
       * <pre>
       ** 伏兽台等级 
       * </pre>
       *
       * <code>int32 mountPlatformLevel = 1;</code>
       */
      public Builder setMountPlatformLevel(int value) {
        
        mountPlatformLevel_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 伏兽台等级 
       * </pre>
       *
       * <code>int32 mountPlatformLevel = 1;</code>
       */
      public Builder clearMountPlatformLevel() {
        
        mountPlatformLevel_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity> privileges_ =
        java.util.Collections.emptyList();
      private void ensurePrivilegesIsMutable() {
        if (!((bitField0_ & 0x00000002) == 0x00000002)) {
          privileges_ = new java.util.ArrayList<cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity>(privileges_);
          bitField0_ |= 0x00000002;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity, cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity.Builder, cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntityOrBuilder> privilegesBuilder_;

      /**
       * <pre>
       ** 所有特权 
       * </pre>
       *
       * <code>repeated .MountPrivilegeEntity privileges = 2;</code>
       */
      public java.util.List<cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity> getPrivilegesList() {
        if (privilegesBuilder_ == null) {
          return java.util.Collections.unmodifiableList(privileges_);
        } else {
          return privilegesBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       ** 所有特权 
       * </pre>
       *
       * <code>repeated .MountPrivilegeEntity privileges = 2;</code>
       */
      public int getPrivilegesCount() {
        if (privilegesBuilder_ == null) {
          return privileges_.size();
        } else {
          return privilegesBuilder_.getCount();
        }
      }
      /**
       * <pre>
       ** 所有特权 
       * </pre>
       *
       * <code>repeated .MountPrivilegeEntity privileges = 2;</code>
       */
      public cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity getPrivileges(int index) {
        if (privilegesBuilder_ == null) {
          return privileges_.get(index);
        } else {
          return privilegesBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       ** 所有特权 
       * </pre>
       *
       * <code>repeated .MountPrivilegeEntity privileges = 2;</code>
       */
      public Builder setPrivileges(
          int index, cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity value) {
        if (privilegesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePrivilegesIsMutable();
          privileges_.set(index, value);
          onChanged();
        } else {
          privilegesBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       ** 所有特权 
       * </pre>
       *
       * <code>repeated .MountPrivilegeEntity privileges = 2;</code>
       */
      public Builder setPrivileges(
          int index, cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity.Builder builderForValue) {
        if (privilegesBuilder_ == null) {
          ensurePrivilegesIsMutable();
          privileges_.set(index, builderForValue.build());
          onChanged();
        } else {
          privilegesBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       ** 所有特权 
       * </pre>
       *
       * <code>repeated .MountPrivilegeEntity privileges = 2;</code>
       */
      public Builder addPrivileges(cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity value) {
        if (privilegesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePrivilegesIsMutable();
          privileges_.add(value);
          onChanged();
        } else {
          privilegesBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       ** 所有特权 
       * </pre>
       *
       * <code>repeated .MountPrivilegeEntity privileges = 2;</code>
       */
      public Builder addPrivileges(
          int index, cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity value) {
        if (privilegesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePrivilegesIsMutable();
          privileges_.add(index, value);
          onChanged();
        } else {
          privilegesBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       ** 所有特权 
       * </pre>
       *
       * <code>repeated .MountPrivilegeEntity privileges = 2;</code>
       */
      public Builder addPrivileges(
          cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity.Builder builderForValue) {
        if (privilegesBuilder_ == null) {
          ensurePrivilegesIsMutable();
          privileges_.add(builderForValue.build());
          onChanged();
        } else {
          privilegesBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       ** 所有特权 
       * </pre>
       *
       * <code>repeated .MountPrivilegeEntity privileges = 2;</code>
       */
      public Builder addPrivileges(
          int index, cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity.Builder builderForValue) {
        if (privilegesBuilder_ == null) {
          ensurePrivilegesIsMutable();
          privileges_.add(index, builderForValue.build());
          onChanged();
        } else {
          privilegesBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       ** 所有特权 
       * </pre>
       *
       * <code>repeated .MountPrivilegeEntity privileges = 2;</code>
       */
      public Builder addAllPrivileges(
          java.lang.Iterable<? extends cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity> values) {
        if (privilegesBuilder_ == null) {
          ensurePrivilegesIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, privileges_);
          onChanged();
        } else {
          privilegesBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       ** 所有特权 
       * </pre>
       *
       * <code>repeated .MountPrivilegeEntity privileges = 2;</code>
       */
      public Builder clearPrivileges() {
        if (privilegesBuilder_ == null) {
          privileges_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
        } else {
          privilegesBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       ** 所有特权 
       * </pre>
       *
       * <code>repeated .MountPrivilegeEntity privileges = 2;</code>
       */
      public Builder removePrivileges(int index) {
        if (privilegesBuilder_ == null) {
          ensurePrivilegesIsMutable();
          privileges_.remove(index);
          onChanged();
        } else {
          privilegesBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       ** 所有特权 
       * </pre>
       *
       * <code>repeated .MountPrivilegeEntity privileges = 2;</code>
       */
      public cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity.Builder getPrivilegesBuilder(
          int index) {
        return getPrivilegesFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       ** 所有特权 
       * </pre>
       *
       * <code>repeated .MountPrivilegeEntity privileges = 2;</code>
       */
      public cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntityOrBuilder getPrivilegesOrBuilder(
          int index) {
        if (privilegesBuilder_ == null) {
          return privileges_.get(index);  } else {
          return privilegesBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       ** 所有特权 
       * </pre>
       *
       * <code>repeated .MountPrivilegeEntity privileges = 2;</code>
       */
      public java.util.List<? extends cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntityOrBuilder> 
           getPrivilegesOrBuilderList() {
        if (privilegesBuilder_ != null) {
          return privilegesBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(privileges_);
        }
      }
      /**
       * <pre>
       ** 所有特权 
       * </pre>
       *
       * <code>repeated .MountPrivilegeEntity privileges = 2;</code>
       */
      public cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity.Builder addPrivilegesBuilder() {
        return getPrivilegesFieldBuilder().addBuilder(
            cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity.getDefaultInstance());
      }
      /**
       * <pre>
       ** 所有特权 
       * </pre>
       *
       * <code>repeated .MountPrivilegeEntity privileges = 2;</code>
       */
      public cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity.Builder addPrivilegesBuilder(
          int index) {
        return getPrivilegesFieldBuilder().addBuilder(
            index, cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity.getDefaultInstance());
      }
      /**
       * <pre>
       ** 所有特权 
       * </pre>
       *
       * <code>repeated .MountPrivilegeEntity privileges = 2;</code>
       */
      public java.util.List<cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity.Builder> 
           getPrivilegesBuilderList() {
        return getPrivilegesFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity, cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity.Builder, cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntityOrBuilder> 
          getPrivilegesFieldBuilder() {
        if (privilegesBuilder_ == null) {
          privilegesBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity, cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity.Builder, cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntityOrBuilder>(
                  privileges_,
                  ((bitField0_ & 0x00000002) == 0x00000002),
                  getParentForChildren(),
                  isClean());
          privileges_ = null;
        }
        return privilegesBuilder_;
      }

      private java.util.List<java.lang.Integer> integralRewardReceive_ = java.util.Collections.emptyList();
      private void ensureIntegralRewardReceiveIsMutable() {
        if (!((bitField0_ & 0x00000004) == 0x00000004)) {
          integralRewardReceive_ = new java.util.ArrayList<java.lang.Integer>(integralRewardReceive_);
          bitField0_ |= 0x00000004;
         }
      }
      /**
       * <pre>
       ** 已领积分奖励（积分） 
       * </pre>
       *
       * <code>repeated int32 integralRewardReceive = 3;</code>
       */
      public java.util.List<java.lang.Integer>
          getIntegralRewardReceiveList() {
        return java.util.Collections.unmodifiableList(integralRewardReceive_);
      }
      /**
       * <pre>
       ** 已领积分奖励（积分） 
       * </pre>
       *
       * <code>repeated int32 integralRewardReceive = 3;</code>
       */
      public int getIntegralRewardReceiveCount() {
        return integralRewardReceive_.size();
      }
      /**
       * <pre>
       ** 已领积分奖励（积分） 
       * </pre>
       *
       * <code>repeated int32 integralRewardReceive = 3;</code>
       */
      public int getIntegralRewardReceive(int index) {
        return integralRewardReceive_.get(index);
      }
      /**
       * <pre>
       ** 已领积分奖励（积分） 
       * </pre>
       *
       * <code>repeated int32 integralRewardReceive = 3;</code>
       */
      public Builder setIntegralRewardReceive(
          int index, int value) {
        ensureIntegralRewardReceiveIsMutable();
        integralRewardReceive_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 已领积分奖励（积分） 
       * </pre>
       *
       * <code>repeated int32 integralRewardReceive = 3;</code>
       */
      public Builder addIntegralRewardReceive(int value) {
        ensureIntegralRewardReceiveIsMutable();
        integralRewardReceive_.add(value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 已领积分奖励（积分） 
       * </pre>
       *
       * <code>repeated int32 integralRewardReceive = 3;</code>
       */
      public Builder addAllIntegralRewardReceive(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureIntegralRewardReceiveIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, integralRewardReceive_);
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 已领积分奖励（积分） 
       * </pre>
       *
       * <code>repeated int32 integralRewardReceive = 3;</code>
       */
      public Builder clearIntegralRewardReceive() {
        integralRewardReceive_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
        return this;
      }

      private int wishMountCid_ ;
      /**
       * <pre>
       ** 心愿坐骑配置ID 
       * </pre>
       *
       * <code>int32 wishMountCid = 4;</code>
       */
      public int getWishMountCid() {
        return wishMountCid_;
      }
      /**
       * <pre>
       ** 心愿坐骑配置ID 
       * </pre>
       *
       * <code>int32 wishMountCid = 4;</code>
       */
      public Builder setWishMountCid(int value) {
        
        wishMountCid_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 心愿坐骑配置ID 
       * </pre>
       *
       * <code>int32 wishMountCid = 4;</code>
       */
      public Builder clearWishMountCid() {
        
        wishMountCid_ = 0;
        onChanged();
        return this;
      }

      private int gachaUseFreeTimes_ ;
      /**
       * <pre>
       ** 今天已使用的免费招募次数 
       * </pre>
       *
       * <code>int32 gachaUseFreeTimes = 5;</code>
       */
      public int getGachaUseFreeTimes() {
        return gachaUseFreeTimes_;
      }
      /**
       * <pre>
       ** 今天已使用的免费招募次数 
       * </pre>
       *
       * <code>int32 gachaUseFreeTimes = 5;</code>
       */
      public Builder setGachaUseFreeTimes(int value) {
        
        gachaUseFreeTimes_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 今天已使用的免费招募次数 
       * </pre>
       *
       * <code>int32 gachaUseFreeTimes = 5;</code>
       */
      public Builder clearGachaUseFreeTimes() {
        
        gachaUseFreeTimes_ = 0;
        onChanged();
        return this;
      }

      private int gachaUseDiamondTimes_ ;
      /**
       * <pre>
       ** 今天已使用的钻石招募次数 
       * </pre>
       *
       * <code>int32 gachaUseDiamondTimes = 6;</code>
       */
      public int getGachaUseDiamondTimes() {
        return gachaUseDiamondTimes_;
      }
      /**
       * <pre>
       ** 今天已使用的钻石招募次数 
       * </pre>
       *
       * <code>int32 gachaUseDiamondTimes = 6;</code>
       */
      public Builder setGachaUseDiamondTimes(int value) {
        
        gachaUseDiamondTimes_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 今天已使用的钻石招募次数 
       * </pre>
       *
       * <code>int32 gachaUseDiamondTimes = 6;</code>
       */
      public Builder clearGachaUseDiamondTimes() {
        
        gachaUseDiamondTimes_ = 0;
        onChanged();
        return this;
      }

      private int gachaIntegral_ ;
      /**
       * <pre>
       ** 招募积分 
       * </pre>
       *
       * <code>int32 gachaIntegral = 7;</code>
       */
      public int getGachaIntegral() {
        return gachaIntegral_;
      }
      /**
       * <pre>
       ** 招募积分 
       * </pre>
       *
       * <code>int32 gachaIntegral = 7;</code>
       */
      public Builder setGachaIntegral(int value) {
        
        gachaIntegral_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 招募积分 
       * </pre>
       *
       * <code>int32 gachaIntegral = 7;</code>
       */
      public Builder clearGachaIntegral() {
        
        gachaIntegral_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.MapField<
          java.lang.Integer, java.lang.Integer> dayBuyTimes_;
      private com.google.protobuf.MapField<java.lang.Integer, java.lang.Integer>
      internalGetDayBuyTimes() {
        if (dayBuyTimes_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              DayBuyTimesDefaultEntryHolder.defaultEntry);
        }
        return dayBuyTimes_;
      }
      private com.google.protobuf.MapField<java.lang.Integer, java.lang.Integer>
      internalGetMutableDayBuyTimes() {
        onChanged();;
        if (dayBuyTimes_ == null) {
          dayBuyTimes_ = com.google.protobuf.MapField.newMapField(
              DayBuyTimesDefaultEntryHolder.defaultEntry);
        }
        if (!dayBuyTimes_.isMutable()) {
          dayBuyTimes_ = dayBuyTimes_.copy();
        }
        return dayBuyTimes_;
      }

      public int getDayBuyTimesCount() {
        return internalGetDayBuyTimes().getMap().size();
      }
      /**
       * <pre>
       ** key:id,value:当天购买数量 
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; dayBuyTimes = 8;</code>
       */

      public boolean containsDayBuyTimes(
          int key) {
        
        return internalGetDayBuyTimes().getMap().containsKey(key);
      }
      /**
       * Use {@link #getDayBuyTimesMap()} instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, java.lang.Integer> getDayBuyTimes() {
        return getDayBuyTimesMap();
      }
      /**
       * <pre>
       ** key:id,value:当天购买数量 
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; dayBuyTimes = 8;</code>
       */

      public java.util.Map<java.lang.Integer, java.lang.Integer> getDayBuyTimesMap() {
        return internalGetDayBuyTimes().getMap();
      }
      /**
       * <pre>
       ** key:id,value:当天购买数量 
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; dayBuyTimes = 8;</code>
       */

      public int getDayBuyTimesOrDefault(
          int key,
          int defaultValue) {
        
        java.util.Map<java.lang.Integer, java.lang.Integer> map =
            internalGetDayBuyTimes().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <pre>
       ** key:id,value:当天购买数量 
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; dayBuyTimes = 8;</code>
       */

      public int getDayBuyTimesOrThrow(
          int key) {
        
        java.util.Map<java.lang.Integer, java.lang.Integer> map =
            internalGetDayBuyTimes().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearDayBuyTimes() {
        internalGetMutableDayBuyTimes().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <pre>
       ** key:id,value:当天购买数量 
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; dayBuyTimes = 8;</code>
       */

      public Builder removeDayBuyTimes(
          int key) {
        
        internalGetMutableDayBuyTimes().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, java.lang.Integer>
      getMutableDayBuyTimes() {
        return internalGetMutableDayBuyTimes().getMutableMap();
      }
      /**
       * <pre>
       ** key:id,value:当天购买数量 
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; dayBuyTimes = 8;</code>
       */
      public Builder putDayBuyTimes(
          int key,
          int value) {
        
        
        internalGetMutableDayBuyTimes().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <pre>
       ** key:id,value:当天购买数量 
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; dayBuyTimes = 8;</code>
       */

      public Builder putAllDayBuyTimes(
          java.util.Map<java.lang.Integer, java.lang.Integer> values) {
        internalGetMutableDayBuyTimes().getMutableMap()
            .putAll(values);
        return this;
      }

      private com.google.protobuf.MapField<
          java.lang.Integer, java.lang.Integer> buyTimes_;
      private com.google.protobuf.MapField<java.lang.Integer, java.lang.Integer>
      internalGetBuyTimes() {
        if (buyTimes_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              BuyTimesDefaultEntryHolder.defaultEntry);
        }
        return buyTimes_;
      }
      private com.google.protobuf.MapField<java.lang.Integer, java.lang.Integer>
      internalGetMutableBuyTimes() {
        onChanged();;
        if (buyTimes_ == null) {
          buyTimes_ = com.google.protobuf.MapField.newMapField(
              BuyTimesDefaultEntryHolder.defaultEntry);
        }
        if (!buyTimes_.isMutable()) {
          buyTimes_ = buyTimes_.copy();
        }
        return buyTimes_;
      }

      public int getBuyTimesCount() {
        return internalGetBuyTimes().getMap().size();
      }
      /**
       * <pre>
       ** key:id,value:累计购买数量 
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; buyTimes = 9;</code>
       */

      public boolean containsBuyTimes(
          int key) {
        
        return internalGetBuyTimes().getMap().containsKey(key);
      }
      /**
       * Use {@link #getBuyTimesMap()} instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, java.lang.Integer> getBuyTimes() {
        return getBuyTimesMap();
      }
      /**
       * <pre>
       ** key:id,value:累计购买数量 
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; buyTimes = 9;</code>
       */

      public java.util.Map<java.lang.Integer, java.lang.Integer> getBuyTimesMap() {
        return internalGetBuyTimes().getMap();
      }
      /**
       * <pre>
       ** key:id,value:累计购买数量 
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; buyTimes = 9;</code>
       */

      public int getBuyTimesOrDefault(
          int key,
          int defaultValue) {
        
        java.util.Map<java.lang.Integer, java.lang.Integer> map =
            internalGetBuyTimes().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <pre>
       ** key:id,value:累计购买数量 
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; buyTimes = 9;</code>
       */

      public int getBuyTimesOrThrow(
          int key) {
        
        java.util.Map<java.lang.Integer, java.lang.Integer> map =
            internalGetBuyTimes().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearBuyTimes() {
        internalGetMutableBuyTimes().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <pre>
       ** key:id,value:累计购买数量 
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; buyTimes = 9;</code>
       */

      public Builder removeBuyTimes(
          int key) {
        
        internalGetMutableBuyTimes().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, java.lang.Integer>
      getMutableBuyTimes() {
        return internalGetMutableBuyTimes().getMutableMap();
      }
      /**
       * <pre>
       ** key:id,value:累计购买数量 
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; buyTimes = 9;</code>
       */
      public Builder putBuyTimes(
          int key,
          int value) {
        
        
        internalGetMutableBuyTimes().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <pre>
       ** key:id,value:累计购买数量 
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; buyTimes = 9;</code>
       */

      public Builder putAllBuyTimes(
          java.util.Map<java.lang.Integer, java.lang.Integer> values) {
        internalGetMutableBuyTimes().getMutableMap()
            .putAll(values);
        return this;
      }

      private com.google.protobuf.MapField<
          java.lang.Integer, java.lang.Integer> receives_;
      private com.google.protobuf.MapField<java.lang.Integer, java.lang.Integer>
      internalGetReceives() {
        if (receives_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              ReceivesDefaultEntryHolder.defaultEntry);
        }
        return receives_;
      }
      private com.google.protobuf.MapField<java.lang.Integer, java.lang.Integer>
      internalGetMutableReceives() {
        onChanged();;
        if (receives_ == null) {
          receives_ = com.google.protobuf.MapField.newMapField(
              ReceivesDefaultEntryHolder.defaultEntry);
        }
        if (!receives_.isMutable()) {
          receives_ = receives_.copy();
        }
        return receives_;
      }

      public int getReceivesCount() {
        return internalGetReceives().getMap().size();
      }
      /**
       * <pre>
       ** key:id,value:累计已领奖励数量 
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; receives = 10;</code>
       */

      public boolean containsReceives(
          int key) {
        
        return internalGetReceives().getMap().containsKey(key);
      }
      /**
       * Use {@link #getReceivesMap()} instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, java.lang.Integer> getReceives() {
        return getReceivesMap();
      }
      /**
       * <pre>
       ** key:id,value:累计已领奖励数量 
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; receives = 10;</code>
       */

      public java.util.Map<java.lang.Integer, java.lang.Integer> getReceivesMap() {
        return internalGetReceives().getMap();
      }
      /**
       * <pre>
       ** key:id,value:累计已领奖励数量 
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; receives = 10;</code>
       */

      public int getReceivesOrDefault(
          int key,
          int defaultValue) {
        
        java.util.Map<java.lang.Integer, java.lang.Integer> map =
            internalGetReceives().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <pre>
       ** key:id,value:累计已领奖励数量 
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; receives = 10;</code>
       */

      public int getReceivesOrThrow(
          int key) {
        
        java.util.Map<java.lang.Integer, java.lang.Integer> map =
            internalGetReceives().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearReceives() {
        internalGetMutableReceives().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <pre>
       ** key:id,value:累计已领奖励数量 
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; receives = 10;</code>
       */

      public Builder removeReceives(
          int key) {
        
        internalGetMutableReceives().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, java.lang.Integer>
      getMutableReceives() {
        return internalGetMutableReceives().getMutableMap();
      }
      /**
       * <pre>
       ** key:id,value:累计已领奖励数量 
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; receives = 10;</code>
       */
      public Builder putReceives(
          int key,
          int value) {
        
        
        internalGetMutableReceives().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <pre>
       ** key:id,value:累计已领奖励数量 
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; receives = 10;</code>
       */

      public Builder putAllReceives(
          java.util.Map<java.lang.Integer, java.lang.Integer> values) {
        internalGetMutableReceives().getMutableMap()
            .putAll(values);
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:MountInfoResponse)
    }

    // @@protoc_insertion_point(class_scope:MountInfoResponse)
    private static final cn.daxiang.protocol.game.MountProtocol.MountInfoResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new cn.daxiang.protocol.game.MountProtocol.MountInfoResponse();
    }

    public static cn.daxiang.protocol.game.MountProtocol.MountInfoResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<MountInfoResponse>
        PARSER = new com.google.protobuf.AbstractParser<MountInfoResponse>() {
      public MountInfoResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new MountInfoResponse(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<MountInfoResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MountInfoResponse> getParserForType() {
      return PARSER;
    }

    public cn.daxiang.protocol.game.MountProtocol.MountInfoResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface MountListResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:MountListResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     ** 坐骑列表 
     * </pre>
     *
     * <code>repeated .Mount infos = 1;</code>
     */
    java.util.List<cn.daxiang.protocol.game.CommonProtocol.Mount> 
        getInfosList();
    /**
     * <pre>
     ** 坐骑列表 
     * </pre>
     *
     * <code>repeated .Mount infos = 1;</code>
     */
    cn.daxiang.protocol.game.CommonProtocol.Mount getInfos(int index);
    /**
     * <pre>
     ** 坐骑列表 
     * </pre>
     *
     * <code>repeated .Mount infos = 1;</code>
     */
    int getInfosCount();
    /**
     * <pre>
     ** 坐骑列表 
     * </pre>
     *
     * <code>repeated .Mount infos = 1;</code>
     */
    java.util.List<? extends cn.daxiang.protocol.game.CommonProtocol.MountOrBuilder> 
        getInfosOrBuilderList();
    /**
     * <pre>
     ** 坐骑列表 
     * </pre>
     *
     * <code>repeated .Mount infos = 1;</code>
     */
    cn.daxiang.protocol.game.CommonProtocol.MountOrBuilder getInfosOrBuilder(
        int index);
  }
  /**
   * <pre>
   ** 坐骑信息响应 
   * </pre>
   *
   * Protobuf type {@code MountListResponse}
   */
  public  static final class MountListResponse extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:MountListResponse)
      MountListResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use MountListResponse.newBuilder() to construct.
    private MountListResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private MountListResponse() {
      infos_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private MountListResponse(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
                infos_ = new java.util.ArrayList<cn.daxiang.protocol.game.CommonProtocol.Mount>();
                mutable_bitField0_ |= 0x00000001;
              }
              infos_.add(
                  input.readMessage(cn.daxiang.protocol.game.CommonProtocol.Mount.parser(), extensionRegistry));
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
          infos_ = java.util.Collections.unmodifiableList(infos_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.MountProtocol.internal_static_MountListResponse_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.daxiang.protocol.game.MountProtocol.internal_static_MountListResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.daxiang.protocol.game.MountProtocol.MountListResponse.class, cn.daxiang.protocol.game.MountProtocol.MountListResponse.Builder.class);
    }

    public static final int INFOS_FIELD_NUMBER = 1;
    private java.util.List<cn.daxiang.protocol.game.CommonProtocol.Mount> infos_;
    /**
     * <pre>
     ** 坐骑列表 
     * </pre>
     *
     * <code>repeated .Mount infos = 1;</code>
     */
    public java.util.List<cn.daxiang.protocol.game.CommonProtocol.Mount> getInfosList() {
      return infos_;
    }
    /**
     * <pre>
     ** 坐骑列表 
     * </pre>
     *
     * <code>repeated .Mount infos = 1;</code>
     */
    public java.util.List<? extends cn.daxiang.protocol.game.CommonProtocol.MountOrBuilder> 
        getInfosOrBuilderList() {
      return infos_;
    }
    /**
     * <pre>
     ** 坐骑列表 
     * </pre>
     *
     * <code>repeated .Mount infos = 1;</code>
     */
    public int getInfosCount() {
      return infos_.size();
    }
    /**
     * <pre>
     ** 坐骑列表 
     * </pre>
     *
     * <code>repeated .Mount infos = 1;</code>
     */
    public cn.daxiang.protocol.game.CommonProtocol.Mount getInfos(int index) {
      return infos_.get(index);
    }
    /**
     * <pre>
     ** 坐骑列表 
     * </pre>
     *
     * <code>repeated .Mount infos = 1;</code>
     */
    public cn.daxiang.protocol.game.CommonProtocol.MountOrBuilder getInfosOrBuilder(
        int index) {
      return infos_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < infos_.size(); i++) {
        output.writeMessage(1, infos_.get(i));
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < infos_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, infos_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof cn.daxiang.protocol.game.MountProtocol.MountListResponse)) {
        return super.equals(obj);
      }
      cn.daxiang.protocol.game.MountProtocol.MountListResponse other = (cn.daxiang.protocol.game.MountProtocol.MountListResponse) obj;

      boolean result = true;
      result = result && getInfosList()
          .equals(other.getInfosList());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getInfosCount() > 0) {
        hash = (37 * hash) + INFOS_FIELD_NUMBER;
        hash = (53 * hash) + getInfosList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static cn.daxiang.protocol.game.MountProtocol.MountListResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.MountProtocol.MountListResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.MountProtocol.MountListResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.MountProtocol.MountListResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.MountProtocol.MountListResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.MountProtocol.MountListResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.MountProtocol.MountListResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.MountProtocol.MountListResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.MountProtocol.MountListResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.MountProtocol.MountListResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.MountProtocol.MountListResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.MountProtocol.MountListResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(cn.daxiang.protocol.game.MountProtocol.MountListResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** 坐骑信息响应 
     * </pre>
     *
     * Protobuf type {@code MountListResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:MountListResponse)
        cn.daxiang.protocol.game.MountProtocol.MountListResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.daxiang.protocol.game.MountProtocol.internal_static_MountListResponse_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.daxiang.protocol.game.MountProtocol.internal_static_MountListResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.daxiang.protocol.game.MountProtocol.MountListResponse.class, cn.daxiang.protocol.game.MountProtocol.MountListResponse.Builder.class);
      }

      // Construct using cn.daxiang.protocol.game.MountProtocol.MountListResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getInfosFieldBuilder();
        }
      }
      public Builder clear() {
        super.clear();
        if (infosBuilder_ == null) {
          infos_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          infosBuilder_.clear();
        }
        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return cn.daxiang.protocol.game.MountProtocol.internal_static_MountListResponse_descriptor;
      }

      public cn.daxiang.protocol.game.MountProtocol.MountListResponse getDefaultInstanceForType() {
        return cn.daxiang.protocol.game.MountProtocol.MountListResponse.getDefaultInstance();
      }

      public cn.daxiang.protocol.game.MountProtocol.MountListResponse build() {
        cn.daxiang.protocol.game.MountProtocol.MountListResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public cn.daxiang.protocol.game.MountProtocol.MountListResponse buildPartial() {
        cn.daxiang.protocol.game.MountProtocol.MountListResponse result = new cn.daxiang.protocol.game.MountProtocol.MountListResponse(this);
        int from_bitField0_ = bitField0_;
        if (infosBuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001)) {
            infos_ = java.util.Collections.unmodifiableList(infos_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.infos_ = infos_;
        } else {
          result.infos_ = infosBuilder_.build();
        }
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof cn.daxiang.protocol.game.MountProtocol.MountListResponse) {
          return mergeFrom((cn.daxiang.protocol.game.MountProtocol.MountListResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(cn.daxiang.protocol.game.MountProtocol.MountListResponse other) {
        if (other == cn.daxiang.protocol.game.MountProtocol.MountListResponse.getDefaultInstance()) return this;
        if (infosBuilder_ == null) {
          if (!other.infos_.isEmpty()) {
            if (infos_.isEmpty()) {
              infos_ = other.infos_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureInfosIsMutable();
              infos_.addAll(other.infos_);
            }
            onChanged();
          }
        } else {
          if (!other.infos_.isEmpty()) {
            if (infosBuilder_.isEmpty()) {
              infosBuilder_.dispose();
              infosBuilder_ = null;
              infos_ = other.infos_;
              bitField0_ = (bitField0_ & ~0x00000001);
              infosBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getInfosFieldBuilder() : null;
            } else {
              infosBuilder_.addAllMessages(other.infos_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        cn.daxiang.protocol.game.MountProtocol.MountListResponse parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (cn.daxiang.protocol.game.MountProtocol.MountListResponse) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<cn.daxiang.protocol.game.CommonProtocol.Mount> infos_ =
        java.util.Collections.emptyList();
      private void ensureInfosIsMutable() {
        if (!((bitField0_ & 0x00000001) == 0x00000001)) {
          infos_ = new java.util.ArrayList<cn.daxiang.protocol.game.CommonProtocol.Mount>(infos_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          cn.daxiang.protocol.game.CommonProtocol.Mount, cn.daxiang.protocol.game.CommonProtocol.Mount.Builder, cn.daxiang.protocol.game.CommonProtocol.MountOrBuilder> infosBuilder_;

      /**
       * <pre>
       ** 坐骑列表 
       * </pre>
       *
       * <code>repeated .Mount infos = 1;</code>
       */
      public java.util.List<cn.daxiang.protocol.game.CommonProtocol.Mount> getInfosList() {
        if (infosBuilder_ == null) {
          return java.util.Collections.unmodifiableList(infos_);
        } else {
          return infosBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       ** 坐骑列表 
       * </pre>
       *
       * <code>repeated .Mount infos = 1;</code>
       */
      public int getInfosCount() {
        if (infosBuilder_ == null) {
          return infos_.size();
        } else {
          return infosBuilder_.getCount();
        }
      }
      /**
       * <pre>
       ** 坐骑列表 
       * </pre>
       *
       * <code>repeated .Mount infos = 1;</code>
       */
      public cn.daxiang.protocol.game.CommonProtocol.Mount getInfos(int index) {
        if (infosBuilder_ == null) {
          return infos_.get(index);
        } else {
          return infosBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       ** 坐骑列表 
       * </pre>
       *
       * <code>repeated .Mount infos = 1;</code>
       */
      public Builder setInfos(
          int index, cn.daxiang.protocol.game.CommonProtocol.Mount value) {
        if (infosBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureInfosIsMutable();
          infos_.set(index, value);
          onChanged();
        } else {
          infosBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       ** 坐骑列表 
       * </pre>
       *
       * <code>repeated .Mount infos = 1;</code>
       */
      public Builder setInfos(
          int index, cn.daxiang.protocol.game.CommonProtocol.Mount.Builder builderForValue) {
        if (infosBuilder_ == null) {
          ensureInfosIsMutable();
          infos_.set(index, builderForValue.build());
          onChanged();
        } else {
          infosBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       ** 坐骑列表 
       * </pre>
       *
       * <code>repeated .Mount infos = 1;</code>
       */
      public Builder addInfos(cn.daxiang.protocol.game.CommonProtocol.Mount value) {
        if (infosBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureInfosIsMutable();
          infos_.add(value);
          onChanged();
        } else {
          infosBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       ** 坐骑列表 
       * </pre>
       *
       * <code>repeated .Mount infos = 1;</code>
       */
      public Builder addInfos(
          int index, cn.daxiang.protocol.game.CommonProtocol.Mount value) {
        if (infosBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureInfosIsMutable();
          infos_.add(index, value);
          onChanged();
        } else {
          infosBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       ** 坐骑列表 
       * </pre>
       *
       * <code>repeated .Mount infos = 1;</code>
       */
      public Builder addInfos(
          cn.daxiang.protocol.game.CommonProtocol.Mount.Builder builderForValue) {
        if (infosBuilder_ == null) {
          ensureInfosIsMutable();
          infos_.add(builderForValue.build());
          onChanged();
        } else {
          infosBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       ** 坐骑列表 
       * </pre>
       *
       * <code>repeated .Mount infos = 1;</code>
       */
      public Builder addInfos(
          int index, cn.daxiang.protocol.game.CommonProtocol.Mount.Builder builderForValue) {
        if (infosBuilder_ == null) {
          ensureInfosIsMutable();
          infos_.add(index, builderForValue.build());
          onChanged();
        } else {
          infosBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       ** 坐骑列表 
       * </pre>
       *
       * <code>repeated .Mount infos = 1;</code>
       */
      public Builder addAllInfos(
          java.lang.Iterable<? extends cn.daxiang.protocol.game.CommonProtocol.Mount> values) {
        if (infosBuilder_ == null) {
          ensureInfosIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, infos_);
          onChanged();
        } else {
          infosBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       ** 坐骑列表 
       * </pre>
       *
       * <code>repeated .Mount infos = 1;</code>
       */
      public Builder clearInfos() {
        if (infosBuilder_ == null) {
          infos_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          infosBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       ** 坐骑列表 
       * </pre>
       *
       * <code>repeated .Mount infos = 1;</code>
       */
      public Builder removeInfos(int index) {
        if (infosBuilder_ == null) {
          ensureInfosIsMutable();
          infos_.remove(index);
          onChanged();
        } else {
          infosBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       ** 坐骑列表 
       * </pre>
       *
       * <code>repeated .Mount infos = 1;</code>
       */
      public cn.daxiang.protocol.game.CommonProtocol.Mount.Builder getInfosBuilder(
          int index) {
        return getInfosFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       ** 坐骑列表 
       * </pre>
       *
       * <code>repeated .Mount infos = 1;</code>
       */
      public cn.daxiang.protocol.game.CommonProtocol.MountOrBuilder getInfosOrBuilder(
          int index) {
        if (infosBuilder_ == null) {
          return infos_.get(index);  } else {
          return infosBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       ** 坐骑列表 
       * </pre>
       *
       * <code>repeated .Mount infos = 1;</code>
       */
      public java.util.List<? extends cn.daxiang.protocol.game.CommonProtocol.MountOrBuilder> 
           getInfosOrBuilderList() {
        if (infosBuilder_ != null) {
          return infosBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(infos_);
        }
      }
      /**
       * <pre>
       ** 坐骑列表 
       * </pre>
       *
       * <code>repeated .Mount infos = 1;</code>
       */
      public cn.daxiang.protocol.game.CommonProtocol.Mount.Builder addInfosBuilder() {
        return getInfosFieldBuilder().addBuilder(
            cn.daxiang.protocol.game.CommonProtocol.Mount.getDefaultInstance());
      }
      /**
       * <pre>
       ** 坐骑列表 
       * </pre>
       *
       * <code>repeated .Mount infos = 1;</code>
       */
      public cn.daxiang.protocol.game.CommonProtocol.Mount.Builder addInfosBuilder(
          int index) {
        return getInfosFieldBuilder().addBuilder(
            index, cn.daxiang.protocol.game.CommonProtocol.Mount.getDefaultInstance());
      }
      /**
       * <pre>
       ** 坐骑列表 
       * </pre>
       *
       * <code>repeated .Mount infos = 1;</code>
       */
      public java.util.List<cn.daxiang.protocol.game.CommonProtocol.Mount.Builder> 
           getInfosBuilderList() {
        return getInfosFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          cn.daxiang.protocol.game.CommonProtocol.Mount, cn.daxiang.protocol.game.CommonProtocol.Mount.Builder, cn.daxiang.protocol.game.CommonProtocol.MountOrBuilder> 
          getInfosFieldBuilder() {
        if (infosBuilder_ == null) {
          infosBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              cn.daxiang.protocol.game.CommonProtocol.Mount, cn.daxiang.protocol.game.CommonProtocol.Mount.Builder, cn.daxiang.protocol.game.CommonProtocol.MountOrBuilder>(
                  infos_,
                  ((bitField0_ & 0x00000001) == 0x00000001),
                  getParentForChildren(),
                  isClean());
          infos_ = null;
        }
        return infosBuilder_;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:MountListResponse)
    }

    // @@protoc_insertion_point(class_scope:MountListResponse)
    private static final cn.daxiang.protocol.game.MountProtocol.MountListResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new cn.daxiang.protocol.game.MountProtocol.MountListResponse();
    }

    public static cn.daxiang.protocol.game.MountProtocol.MountListResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<MountListResponse>
        PARSER = new com.google.protobuf.AbstractParser<MountListResponse>() {
      public MountListResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new MountListResponse(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<MountListResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MountListResponse> getParserForType() {
      return PARSER;
    }

    public cn.daxiang.protocol.game.MountProtocol.MountListResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface MountGachaRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:MountGachaRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     ** 次数（1次或10次） 
     * </pre>
     *
     * <code>int32 times = 1;</code>
     */
    int getTimes();

    /**
     * <pre>
     ** 是否使用物品 
     * </pre>
     *
     * <code>bool useGoods = 2;</code>
     */
    boolean getUseGoods();
  }
  /**
   * <pre>
   ** 坐骑招募请求 
   * </pre>
   *
   * Protobuf type {@code MountGachaRequest}
   */
  public  static final class MountGachaRequest extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:MountGachaRequest)
      MountGachaRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use MountGachaRequest.newBuilder() to construct.
    private MountGachaRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private MountGachaRequest() {
      times_ = 0;
      useGoods_ = false;
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private MountGachaRequest(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {

              times_ = input.readInt32();
              break;
            }
            case 16: {

              useGoods_ = input.readBool();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.MountProtocol.internal_static_MountGachaRequest_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.daxiang.protocol.game.MountProtocol.internal_static_MountGachaRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.daxiang.protocol.game.MountProtocol.MountGachaRequest.class, cn.daxiang.protocol.game.MountProtocol.MountGachaRequest.Builder.class);
    }

    public static final int TIMES_FIELD_NUMBER = 1;
    private int times_;
    /**
     * <pre>
     ** 次数（1次或10次） 
     * </pre>
     *
     * <code>int32 times = 1;</code>
     */
    public int getTimes() {
      return times_;
    }

    public static final int USEGOODS_FIELD_NUMBER = 2;
    private boolean useGoods_;
    /**
     * <pre>
     ** 是否使用物品 
     * </pre>
     *
     * <code>bool useGoods = 2;</code>
     */
    public boolean getUseGoods() {
      return useGoods_;
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (times_ != 0) {
        output.writeInt32(1, times_);
      }
      if (useGoods_ != false) {
        output.writeBool(2, useGoods_);
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (times_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, times_);
      }
      if (useGoods_ != false) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(2, useGoods_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof cn.daxiang.protocol.game.MountProtocol.MountGachaRequest)) {
        return super.equals(obj);
      }
      cn.daxiang.protocol.game.MountProtocol.MountGachaRequest other = (cn.daxiang.protocol.game.MountProtocol.MountGachaRequest) obj;

      boolean result = true;
      result = result && (getTimes()
          == other.getTimes());
      result = result && (getUseGoods()
          == other.getUseGoods());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TIMES_FIELD_NUMBER;
      hash = (53 * hash) + getTimes();
      hash = (37 * hash) + USEGOODS_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getUseGoods());
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static cn.daxiang.protocol.game.MountProtocol.MountGachaRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.MountProtocol.MountGachaRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.MountProtocol.MountGachaRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.MountProtocol.MountGachaRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.MountProtocol.MountGachaRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.MountProtocol.MountGachaRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.MountProtocol.MountGachaRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.MountProtocol.MountGachaRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.MountProtocol.MountGachaRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.MountProtocol.MountGachaRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.MountProtocol.MountGachaRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.MountProtocol.MountGachaRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(cn.daxiang.protocol.game.MountProtocol.MountGachaRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** 坐骑招募请求 
     * </pre>
     *
     * Protobuf type {@code MountGachaRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:MountGachaRequest)
        cn.daxiang.protocol.game.MountProtocol.MountGachaRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.daxiang.protocol.game.MountProtocol.internal_static_MountGachaRequest_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.daxiang.protocol.game.MountProtocol.internal_static_MountGachaRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.daxiang.protocol.game.MountProtocol.MountGachaRequest.class, cn.daxiang.protocol.game.MountProtocol.MountGachaRequest.Builder.class);
      }

      // Construct using cn.daxiang.protocol.game.MountProtocol.MountGachaRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        times_ = 0;

        useGoods_ = false;

        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return cn.daxiang.protocol.game.MountProtocol.internal_static_MountGachaRequest_descriptor;
      }

      public cn.daxiang.protocol.game.MountProtocol.MountGachaRequest getDefaultInstanceForType() {
        return cn.daxiang.protocol.game.MountProtocol.MountGachaRequest.getDefaultInstance();
      }

      public cn.daxiang.protocol.game.MountProtocol.MountGachaRequest build() {
        cn.daxiang.protocol.game.MountProtocol.MountGachaRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public cn.daxiang.protocol.game.MountProtocol.MountGachaRequest buildPartial() {
        cn.daxiang.protocol.game.MountProtocol.MountGachaRequest result = new cn.daxiang.protocol.game.MountProtocol.MountGachaRequest(this);
        result.times_ = times_;
        result.useGoods_ = useGoods_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof cn.daxiang.protocol.game.MountProtocol.MountGachaRequest) {
          return mergeFrom((cn.daxiang.protocol.game.MountProtocol.MountGachaRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(cn.daxiang.protocol.game.MountProtocol.MountGachaRequest other) {
        if (other == cn.daxiang.protocol.game.MountProtocol.MountGachaRequest.getDefaultInstance()) return this;
        if (other.getTimes() != 0) {
          setTimes(other.getTimes());
        }
        if (other.getUseGoods() != false) {
          setUseGoods(other.getUseGoods());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        cn.daxiang.protocol.game.MountProtocol.MountGachaRequest parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (cn.daxiang.protocol.game.MountProtocol.MountGachaRequest) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int times_ ;
      /**
       * <pre>
       ** 次数（1次或10次） 
       * </pre>
       *
       * <code>int32 times = 1;</code>
       */
      public int getTimes() {
        return times_;
      }
      /**
       * <pre>
       ** 次数（1次或10次） 
       * </pre>
       *
       * <code>int32 times = 1;</code>
       */
      public Builder setTimes(int value) {
        
        times_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 次数（1次或10次） 
       * </pre>
       *
       * <code>int32 times = 1;</code>
       */
      public Builder clearTimes() {
        
        times_ = 0;
        onChanged();
        return this;
      }

      private boolean useGoods_ ;
      /**
       * <pre>
       ** 是否使用物品 
       * </pre>
       *
       * <code>bool useGoods = 2;</code>
       */
      public boolean getUseGoods() {
        return useGoods_;
      }
      /**
       * <pre>
       ** 是否使用物品 
       * </pre>
       *
       * <code>bool useGoods = 2;</code>
       */
      public Builder setUseGoods(boolean value) {
        
        useGoods_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 是否使用物品 
       * </pre>
       *
       * <code>bool useGoods = 2;</code>
       */
      public Builder clearUseGoods() {
        
        useGoods_ = false;
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:MountGachaRequest)
    }

    // @@protoc_insertion_point(class_scope:MountGachaRequest)
    private static final cn.daxiang.protocol.game.MountProtocol.MountGachaRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new cn.daxiang.protocol.game.MountProtocol.MountGachaRequest();
    }

    public static cn.daxiang.protocol.game.MountProtocol.MountGachaRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<MountGachaRequest>
        PARSER = new com.google.protobuf.AbstractParser<MountGachaRequest>() {
      public MountGachaRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new MountGachaRequest(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<MountGachaRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MountGachaRequest> getParserForType() {
      return PARSER;
    }

    public cn.daxiang.protocol.game.MountProtocol.MountGachaRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface MountGachaResultResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:MountGachaResultResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>map&lt;int32, .RewardObjectList&gt; rewardObjectMap = 1;</code>
     */
    int getRewardObjectMapCount();
    /**
     * <code>map&lt;int32, .RewardObjectList&gt; rewardObjectMap = 1;</code>
     */
    boolean containsRewardObjectMap(
        int key);
    /**
     * Use {@link #getRewardObjectMapMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Integer, cn.daxiang.protocol.game.CommonProtocol.RewardObjectList>
    getRewardObjectMap();
    /**
     * <code>map&lt;int32, .RewardObjectList&gt; rewardObjectMap = 1;</code>
     */
    java.util.Map<java.lang.Integer, cn.daxiang.protocol.game.CommonProtocol.RewardObjectList>
    getRewardObjectMapMap();
    /**
     * <code>map&lt;int32, .RewardObjectList&gt; rewardObjectMap = 1;</code>
     */

    cn.daxiang.protocol.game.CommonProtocol.RewardObjectList getRewardObjectMapOrDefault(
        int key,
        cn.daxiang.protocol.game.CommonProtocol.RewardObjectList defaultValue);
    /**
     * <code>map&lt;int32, .RewardObjectList&gt; rewardObjectMap = 1;</code>
     */

    cn.daxiang.protocol.game.CommonProtocol.RewardObjectList getRewardObjectMapOrThrow(
        int key);
  }
  /**
   * <pre>
   ** 坐骑招募响应 
   * </pre>
   *
   * Protobuf type {@code MountGachaResultResponse}
   */
  public  static final class MountGachaResultResponse extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:MountGachaResultResponse)
      MountGachaResultResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use MountGachaResultResponse.newBuilder() to construct.
    private MountGachaResultResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private MountGachaResultResponse() {
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private MountGachaResultResponse(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
                rewardObjectMap_ = com.google.protobuf.MapField.newMapField(
                    RewardObjectMapDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000001;
              }
              com.google.protobuf.MapEntry<java.lang.Integer, cn.daxiang.protocol.game.CommonProtocol.RewardObjectList>
              rewardObjectMap__ = input.readMessage(
                  RewardObjectMapDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              rewardObjectMap_.getMutableMap().put(
                  rewardObjectMap__.getKey(), rewardObjectMap__.getValue());
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.MountProtocol.internal_static_MountGachaResultResponse_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 1:
          return internalGetRewardObjectMap();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.daxiang.protocol.game.MountProtocol.internal_static_MountGachaResultResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.daxiang.protocol.game.MountProtocol.MountGachaResultResponse.class, cn.daxiang.protocol.game.MountProtocol.MountGachaResultResponse.Builder.class);
    }

    public static final int REWARDOBJECTMAP_FIELD_NUMBER = 1;
    private static final class RewardObjectMapDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Integer, cn.daxiang.protocol.game.CommonProtocol.RewardObjectList> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Integer, cn.daxiang.protocol.game.CommonProtocol.RewardObjectList>newDefaultInstance(
                  cn.daxiang.protocol.game.MountProtocol.internal_static_MountGachaResultResponse_RewardObjectMapEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0,
                  com.google.protobuf.WireFormat.FieldType.MESSAGE,
                  cn.daxiang.protocol.game.CommonProtocol.RewardObjectList.getDefaultInstance());
    }
    private com.google.protobuf.MapField<
        java.lang.Integer, cn.daxiang.protocol.game.CommonProtocol.RewardObjectList> rewardObjectMap_;
    private com.google.protobuf.MapField<java.lang.Integer, cn.daxiang.protocol.game.CommonProtocol.RewardObjectList>
    internalGetRewardObjectMap() {
      if (rewardObjectMap_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            RewardObjectMapDefaultEntryHolder.defaultEntry);
      }
      return rewardObjectMap_;
    }

    public int getRewardObjectMapCount() {
      return internalGetRewardObjectMap().getMap().size();
    }
    /**
     * <code>map&lt;int32, .RewardObjectList&gt; rewardObjectMap = 1;</code>
     */

    public boolean containsRewardObjectMap(
        int key) {
      
      return internalGetRewardObjectMap().getMap().containsKey(key);
    }
    /**
     * Use {@link #getRewardObjectMapMap()} instead.
     */
    @java.lang.Deprecated
    public java.util.Map<java.lang.Integer, cn.daxiang.protocol.game.CommonProtocol.RewardObjectList> getRewardObjectMap() {
      return getRewardObjectMapMap();
    }
    /**
     * <code>map&lt;int32, .RewardObjectList&gt; rewardObjectMap = 1;</code>
     */

    public java.util.Map<java.lang.Integer, cn.daxiang.protocol.game.CommonProtocol.RewardObjectList> getRewardObjectMapMap() {
      return internalGetRewardObjectMap().getMap();
    }
    /**
     * <code>map&lt;int32, .RewardObjectList&gt; rewardObjectMap = 1;</code>
     */

    public cn.daxiang.protocol.game.CommonProtocol.RewardObjectList getRewardObjectMapOrDefault(
        int key,
        cn.daxiang.protocol.game.CommonProtocol.RewardObjectList defaultValue) {
      
      java.util.Map<java.lang.Integer, cn.daxiang.protocol.game.CommonProtocol.RewardObjectList> map =
          internalGetRewardObjectMap().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <code>map&lt;int32, .RewardObjectList&gt; rewardObjectMap = 1;</code>
     */

    public cn.daxiang.protocol.game.CommonProtocol.RewardObjectList getRewardObjectMapOrThrow(
        int key) {
      
      java.util.Map<java.lang.Integer, cn.daxiang.protocol.game.CommonProtocol.RewardObjectList> map =
          internalGetRewardObjectMap().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      com.google.protobuf.GeneratedMessageV3
        .serializeIntegerMapTo(
          output,
          internalGetRewardObjectMap(),
          RewardObjectMapDefaultEntryHolder.defaultEntry,
          1);
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (java.util.Map.Entry<java.lang.Integer, cn.daxiang.protocol.game.CommonProtocol.RewardObjectList> entry
           : internalGetRewardObjectMap().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Integer, cn.daxiang.protocol.game.CommonProtocol.RewardObjectList>
        rewardObjectMap__ = RewardObjectMapDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(1, rewardObjectMap__);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof cn.daxiang.protocol.game.MountProtocol.MountGachaResultResponse)) {
        return super.equals(obj);
      }
      cn.daxiang.protocol.game.MountProtocol.MountGachaResultResponse other = (cn.daxiang.protocol.game.MountProtocol.MountGachaResultResponse) obj;

      boolean result = true;
      result = result && internalGetRewardObjectMap().equals(
          other.internalGetRewardObjectMap());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (!internalGetRewardObjectMap().getMap().isEmpty()) {
        hash = (37 * hash) + REWARDOBJECTMAP_FIELD_NUMBER;
        hash = (53 * hash) + internalGetRewardObjectMap().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static cn.daxiang.protocol.game.MountProtocol.MountGachaResultResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.MountProtocol.MountGachaResultResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.MountProtocol.MountGachaResultResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.MountProtocol.MountGachaResultResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.MountProtocol.MountGachaResultResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.MountProtocol.MountGachaResultResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.MountProtocol.MountGachaResultResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.MountProtocol.MountGachaResultResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.MountProtocol.MountGachaResultResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.MountProtocol.MountGachaResultResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.MountProtocol.MountGachaResultResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.MountProtocol.MountGachaResultResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(cn.daxiang.protocol.game.MountProtocol.MountGachaResultResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** 坐骑招募响应 
     * </pre>
     *
     * Protobuf type {@code MountGachaResultResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:MountGachaResultResponse)
        cn.daxiang.protocol.game.MountProtocol.MountGachaResultResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.daxiang.protocol.game.MountProtocol.internal_static_MountGachaResultResponse_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetRewardObjectMap();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetMutableRewardObjectMap();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.daxiang.protocol.game.MountProtocol.internal_static_MountGachaResultResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.daxiang.protocol.game.MountProtocol.MountGachaResultResponse.class, cn.daxiang.protocol.game.MountProtocol.MountGachaResultResponse.Builder.class);
      }

      // Construct using cn.daxiang.protocol.game.MountProtocol.MountGachaResultResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        internalGetMutableRewardObjectMap().clear();
        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return cn.daxiang.protocol.game.MountProtocol.internal_static_MountGachaResultResponse_descriptor;
      }

      public cn.daxiang.protocol.game.MountProtocol.MountGachaResultResponse getDefaultInstanceForType() {
        return cn.daxiang.protocol.game.MountProtocol.MountGachaResultResponse.getDefaultInstance();
      }

      public cn.daxiang.protocol.game.MountProtocol.MountGachaResultResponse build() {
        cn.daxiang.protocol.game.MountProtocol.MountGachaResultResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public cn.daxiang.protocol.game.MountProtocol.MountGachaResultResponse buildPartial() {
        cn.daxiang.protocol.game.MountProtocol.MountGachaResultResponse result = new cn.daxiang.protocol.game.MountProtocol.MountGachaResultResponse(this);
        int from_bitField0_ = bitField0_;
        result.rewardObjectMap_ = internalGetRewardObjectMap();
        result.rewardObjectMap_.makeImmutable();
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof cn.daxiang.protocol.game.MountProtocol.MountGachaResultResponse) {
          return mergeFrom((cn.daxiang.protocol.game.MountProtocol.MountGachaResultResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(cn.daxiang.protocol.game.MountProtocol.MountGachaResultResponse other) {
        if (other == cn.daxiang.protocol.game.MountProtocol.MountGachaResultResponse.getDefaultInstance()) return this;
        internalGetMutableRewardObjectMap().mergeFrom(
            other.internalGetRewardObjectMap());
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        cn.daxiang.protocol.game.MountProtocol.MountGachaResultResponse parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (cn.daxiang.protocol.game.MountProtocol.MountGachaResultResponse) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.MapField<
          java.lang.Integer, cn.daxiang.protocol.game.CommonProtocol.RewardObjectList> rewardObjectMap_;
      private com.google.protobuf.MapField<java.lang.Integer, cn.daxiang.protocol.game.CommonProtocol.RewardObjectList>
      internalGetRewardObjectMap() {
        if (rewardObjectMap_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              RewardObjectMapDefaultEntryHolder.defaultEntry);
        }
        return rewardObjectMap_;
      }
      private com.google.protobuf.MapField<java.lang.Integer, cn.daxiang.protocol.game.CommonProtocol.RewardObjectList>
      internalGetMutableRewardObjectMap() {
        onChanged();;
        if (rewardObjectMap_ == null) {
          rewardObjectMap_ = com.google.protobuf.MapField.newMapField(
              RewardObjectMapDefaultEntryHolder.defaultEntry);
        }
        if (!rewardObjectMap_.isMutable()) {
          rewardObjectMap_ = rewardObjectMap_.copy();
        }
        return rewardObjectMap_;
      }

      public int getRewardObjectMapCount() {
        return internalGetRewardObjectMap().getMap().size();
      }
      /**
       * <code>map&lt;int32, .RewardObjectList&gt; rewardObjectMap = 1;</code>
       */

      public boolean containsRewardObjectMap(
          int key) {
        
        return internalGetRewardObjectMap().getMap().containsKey(key);
      }
      /**
       * Use {@link #getRewardObjectMapMap()} instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, cn.daxiang.protocol.game.CommonProtocol.RewardObjectList> getRewardObjectMap() {
        return getRewardObjectMapMap();
      }
      /**
       * <code>map&lt;int32, .RewardObjectList&gt; rewardObjectMap = 1;</code>
       */

      public java.util.Map<java.lang.Integer, cn.daxiang.protocol.game.CommonProtocol.RewardObjectList> getRewardObjectMapMap() {
        return internalGetRewardObjectMap().getMap();
      }
      /**
       * <code>map&lt;int32, .RewardObjectList&gt; rewardObjectMap = 1;</code>
       */

      public cn.daxiang.protocol.game.CommonProtocol.RewardObjectList getRewardObjectMapOrDefault(
          int key,
          cn.daxiang.protocol.game.CommonProtocol.RewardObjectList defaultValue) {
        
        java.util.Map<java.lang.Integer, cn.daxiang.protocol.game.CommonProtocol.RewardObjectList> map =
            internalGetRewardObjectMap().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <code>map&lt;int32, .RewardObjectList&gt; rewardObjectMap = 1;</code>
       */

      public cn.daxiang.protocol.game.CommonProtocol.RewardObjectList getRewardObjectMapOrThrow(
          int key) {
        
        java.util.Map<java.lang.Integer, cn.daxiang.protocol.game.CommonProtocol.RewardObjectList> map =
            internalGetRewardObjectMap().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearRewardObjectMap() {
        internalGetMutableRewardObjectMap().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <code>map&lt;int32, .RewardObjectList&gt; rewardObjectMap = 1;</code>
       */

      public Builder removeRewardObjectMap(
          int key) {
        
        internalGetMutableRewardObjectMap().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, cn.daxiang.protocol.game.CommonProtocol.RewardObjectList>
      getMutableRewardObjectMap() {
        return internalGetMutableRewardObjectMap().getMutableMap();
      }
      /**
       * <code>map&lt;int32, .RewardObjectList&gt; rewardObjectMap = 1;</code>
       */
      public Builder putRewardObjectMap(
          int key,
          cn.daxiang.protocol.game.CommonProtocol.RewardObjectList value) {
        
        if (value == null) { throw new java.lang.NullPointerException(); }
        internalGetMutableRewardObjectMap().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <code>map&lt;int32, .RewardObjectList&gt; rewardObjectMap = 1;</code>
       */

      public Builder putAllRewardObjectMap(
          java.util.Map<java.lang.Integer, cn.daxiang.protocol.game.CommonProtocol.RewardObjectList> values) {
        internalGetMutableRewardObjectMap().getMutableMap()
            .putAll(values);
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:MountGachaResultResponse)
    }

    // @@protoc_insertion_point(class_scope:MountGachaResultResponse)
    private static final cn.daxiang.protocol.game.MountProtocol.MountGachaResultResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new cn.daxiang.protocol.game.MountProtocol.MountGachaResultResponse();
    }

    public static cn.daxiang.protocol.game.MountProtocol.MountGachaResultResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<MountGachaResultResponse>
        PARSER = new com.google.protobuf.AbstractParser<MountGachaResultResponse>() {
      public MountGachaResultResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new MountGachaResultResponse(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<MountGachaResultResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MountGachaResultResponse> getParserForType() {
      return PARSER;
    }

    public cn.daxiang.protocol.game.MountProtocol.MountGachaResultResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface MountPrivilegeEntityOrBuilder extends
      // @@protoc_insertion_point(interface_extends:MountPrivilegeEntity)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     ** 特权类型 
     * </pre>
     *
     * <code>int32 type = 1;</code>
     */
    int getType();

    /**
     * <pre>
     ** 已领取天数 
     * </pre>
     *
     * <code>int32 count = 2;</code>
     */
    int getCount();

    /**
     * <pre>
     ** 上次领取当前特权奖励时间戳 
     * </pre>
     *
     * <code>int64 lastReceiveTime = 3;</code>
     */
    long getLastReceiveTime();
  }
  /**
   * <pre>
   ** 特权信息体
   * </pre>
   *
   * Protobuf type {@code MountPrivilegeEntity}
   */
  public  static final class MountPrivilegeEntity extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:MountPrivilegeEntity)
      MountPrivilegeEntityOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use MountPrivilegeEntity.newBuilder() to construct.
    private MountPrivilegeEntity(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private MountPrivilegeEntity() {
      type_ = 0;
      count_ = 0;
      lastReceiveTime_ = 0L;
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private MountPrivilegeEntity(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {

              type_ = input.readInt32();
              break;
            }
            case 16: {

              count_ = input.readInt32();
              break;
            }
            case 24: {

              lastReceiveTime_ = input.readInt64();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.MountProtocol.internal_static_MountPrivilegeEntity_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.daxiang.protocol.game.MountProtocol.internal_static_MountPrivilegeEntity_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity.class, cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity.Builder.class);
    }

    public static final int TYPE_FIELD_NUMBER = 1;
    private int type_;
    /**
     * <pre>
     ** 特权类型 
     * </pre>
     *
     * <code>int32 type = 1;</code>
     */
    public int getType() {
      return type_;
    }

    public static final int COUNT_FIELD_NUMBER = 2;
    private int count_;
    /**
     * <pre>
     ** 已领取天数 
     * </pre>
     *
     * <code>int32 count = 2;</code>
     */
    public int getCount() {
      return count_;
    }

    public static final int LASTRECEIVETIME_FIELD_NUMBER = 3;
    private long lastReceiveTime_;
    /**
     * <pre>
     ** 上次领取当前特权奖励时间戳 
     * </pre>
     *
     * <code>int64 lastReceiveTime = 3;</code>
     */
    public long getLastReceiveTime() {
      return lastReceiveTime_;
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (type_ != 0) {
        output.writeInt32(1, type_);
      }
      if (count_ != 0) {
        output.writeInt32(2, count_);
      }
      if (lastReceiveTime_ != 0L) {
        output.writeInt64(3, lastReceiveTime_);
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (type_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, type_);
      }
      if (count_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, count_);
      }
      if (lastReceiveTime_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, lastReceiveTime_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity)) {
        return super.equals(obj);
      }
      cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity other = (cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity) obj;

      boolean result = true;
      result = result && (getType()
          == other.getType());
      result = result && (getCount()
          == other.getCount());
      result = result && (getLastReceiveTime()
          == other.getLastReceiveTime());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
      hash = (37 * hash) + COUNT_FIELD_NUMBER;
      hash = (53 * hash) + getCount();
      hash = (37 * hash) + LASTRECEIVETIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getLastReceiveTime());
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** 特权信息体
     * </pre>
     *
     * Protobuf type {@code MountPrivilegeEntity}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:MountPrivilegeEntity)
        cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntityOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.daxiang.protocol.game.MountProtocol.internal_static_MountPrivilegeEntity_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.daxiang.protocol.game.MountProtocol.internal_static_MountPrivilegeEntity_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity.class, cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity.Builder.class);
      }

      // Construct using cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        type_ = 0;

        count_ = 0;

        lastReceiveTime_ = 0L;

        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return cn.daxiang.protocol.game.MountProtocol.internal_static_MountPrivilegeEntity_descriptor;
      }

      public cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity getDefaultInstanceForType() {
        return cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity.getDefaultInstance();
      }

      public cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity build() {
        cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity buildPartial() {
        cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity result = new cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity(this);
        result.type_ = type_;
        result.count_ = count_;
        result.lastReceiveTime_ = lastReceiveTime_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity) {
          return mergeFrom((cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity other) {
        if (other == cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity.getDefaultInstance()) return this;
        if (other.getType() != 0) {
          setType(other.getType());
        }
        if (other.getCount() != 0) {
          setCount(other.getCount());
        }
        if (other.getLastReceiveTime() != 0L) {
          setLastReceiveTime(other.getLastReceiveTime());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int type_ ;
      /**
       * <pre>
       ** 特权类型 
       * </pre>
       *
       * <code>int32 type = 1;</code>
       */
      public int getType() {
        return type_;
      }
      /**
       * <pre>
       ** 特权类型 
       * </pre>
       *
       * <code>int32 type = 1;</code>
       */
      public Builder setType(int value) {
        
        type_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 特权类型 
       * </pre>
       *
       * <code>int32 type = 1;</code>
       */
      public Builder clearType() {
        
        type_ = 0;
        onChanged();
        return this;
      }

      private int count_ ;
      /**
       * <pre>
       ** 已领取天数 
       * </pre>
       *
       * <code>int32 count = 2;</code>
       */
      public int getCount() {
        return count_;
      }
      /**
       * <pre>
       ** 已领取天数 
       * </pre>
       *
       * <code>int32 count = 2;</code>
       */
      public Builder setCount(int value) {
        
        count_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 已领取天数 
       * </pre>
       *
       * <code>int32 count = 2;</code>
       */
      public Builder clearCount() {
        
        count_ = 0;
        onChanged();
        return this;
      }

      private long lastReceiveTime_ ;
      /**
       * <pre>
       ** 上次领取当前特权奖励时间戳 
       * </pre>
       *
       * <code>int64 lastReceiveTime = 3;</code>
       */
      public long getLastReceiveTime() {
        return lastReceiveTime_;
      }
      /**
       * <pre>
       ** 上次领取当前特权奖励时间戳 
       * </pre>
       *
       * <code>int64 lastReceiveTime = 3;</code>
       */
      public Builder setLastReceiveTime(long value) {
        
        lastReceiveTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 上次领取当前特权奖励时间戳 
       * </pre>
       *
       * <code>int64 lastReceiveTime = 3;</code>
       */
      public Builder clearLastReceiveTime() {
        
        lastReceiveTime_ = 0L;
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:MountPrivilegeEntity)
    }

    // @@protoc_insertion_point(class_scope:MountPrivilegeEntity)
    private static final cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity();
    }

    public static cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<MountPrivilegeEntity>
        PARSER = new com.google.protobuf.AbstractParser<MountPrivilegeEntity>() {
      public MountPrivilegeEntity parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new MountPrivilegeEntity(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<MountPrivilegeEntity> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MountPrivilegeEntity> getParserForType() {
      return PARSER;
    }

    public cn.daxiang.protocol.game.MountProtocol.MountPrivilegeEntity getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_MountInfoResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_MountInfoResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_MountInfoResponse_DayBuyTimesEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_MountInfoResponse_DayBuyTimesEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_MountInfoResponse_BuyTimesEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_MountInfoResponse_BuyTimesEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_MountInfoResponse_ReceivesEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_MountInfoResponse_ReceivesEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_MountListResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_MountListResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_MountGachaRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_MountGachaRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_MountGachaResultResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_MountGachaResultResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_MountGachaResultResponse_RewardObjectMapEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_MountGachaResultResponse_RewardObjectMapEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_MountPrivilegeEntity_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_MountPrivilegeEntity_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\030game/mountProtocol.proto\032\031game/commonP" +
      "rotocol.proto\"\227\004\n\021MountInfoResponse\022\032\n\022m" +
      "ountPlatformLevel\030\001 \001(\005\022)\n\nprivileges\030\002 " +
      "\003(\0132\025.MountPrivilegeEntity\022\035\n\025integralRe" +
      "wardReceive\030\003 \003(\005\022\024\n\014wishMountCid\030\004 \001(\005\022" +
      "\031\n\021gachaUseFreeTimes\030\005 \001(\005\022\034\n\024gachaUseDi" +
      "amondTimes\030\006 \001(\005\022\025\n\rgachaIntegral\030\007 \001(\005\022" +
      "8\n\013dayBuyTimes\030\010 \003(\0132#.MountInfoResponse" +
      ".DayBuyTimesEntry\0222\n\010buyTimes\030\t \003(\0132 .Mo" +
      "untInfoResponse.BuyTimesEntry\0222\n\010receive" +
      "s\030\n \003(\0132 .MountInfoResponse.ReceivesEntr" +
      "y\0322\n\020DayBuyTimesEntry\022\013\n\003key\030\001 \001(\005\022\r\n\005va" +
      "lue\030\002 \001(\005:\0028\001\032/\n\rBuyTimesEntry\022\013\n\003key\030\001 " +
      "\001(\005\022\r\n\005value\030\002 \001(\005:\0028\001\032/\n\rReceivesEntry\022" +
      "\013\n\003key\030\001 \001(\005\022\r\n\005value\030\002 \001(\005:\0028\001\"*\n\021Mount" +
      "ListResponse\022\025\n\005infos\030\001 \003(\0132\006.Mount\"4\n\021M" +
      "ountGachaRequest\022\r\n\005times\030\001 \001(\005\022\020\n\010useGo" +
      "ods\030\002 \001(\010\"\256\001\n\030MountGachaResultResponse\022G" +
      "\n\017rewardObjectMap\030\001 \003(\0132..MountGachaResu" +
      "ltResponse.RewardObjectMapEntry\032I\n\024Rewar" +
      "dObjectMapEntry\022\013\n\003key\030\001 \001(\005\022 \n\005value\030\002 " +
      "\001(\0132\021.RewardObjectList:\0028\001\"L\n\024MountPrivi" +
      "legeEntity\022\014\n\004type\030\001 \001(\005\022\r\n\005count\030\002 \001(\005\022" +
      "\027\n\017lastReceiveTime\030\003 \001(\003*\315\002\n\010MountCmd\022\022\n" +
      "\016MOUNT_CMD_NONE\020\000\022\022\n\016GET_MOUNT_INFO\020\001\022\022\n" +
      "\016GET_MOUNT_LIST\020\002\022\024\n\020MOUNT_ACTIVATION\020\003\022" +
      "\021\n\rMOUNT_STAR_UP\020\004\022\032\n\026MOUNT_PLATFORM_UPG" +
      "RADE\020\005\022\031\n\025MOUNT_INTEGRAL_REWARD\020\006\022\"\n\036MOU" +
      "NT_PRIVILEGE_REWARD_RECEIVE\020\007\022\037\n\033MOUNT_P" +
      "RIVILEGE_CHOOSE_WISH\020\010\022\017\n\013MOUNT_GACHA\020\t\022" +
      "%\n!RECEIVE_MOUNT_GACHA_CHARGE_REWARD\020\n\022\023" +
      "\n\017PUSH_MOUNT_INFO\020d\022\023\n\017PUSH_MOUNT_LIST\020e" +
      "B\034\n\030cn.daxiang.protocol.gameH\001b\006proto3"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
        new com.google.protobuf.Descriptors.FileDescriptor.    InternalDescriptorAssigner() {
          public com.google.protobuf.ExtensionRegistry assignDescriptors(
              com.google.protobuf.Descriptors.FileDescriptor root) {
            descriptor = root;
            return null;
          }
        };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          cn.daxiang.protocol.game.CommonProtocol.getDescriptor(),
        }, assigner);
    internal_static_MountInfoResponse_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_MountInfoResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_MountInfoResponse_descriptor,
        new java.lang.String[] { "MountPlatformLevel", "Privileges", "IntegralRewardReceive", "WishMountCid", "GachaUseFreeTimes", "GachaUseDiamondTimes", "GachaIntegral", "DayBuyTimes", "BuyTimes", "Receives", });
    internal_static_MountInfoResponse_DayBuyTimesEntry_descriptor =
      internal_static_MountInfoResponse_descriptor.getNestedTypes().get(0);
    internal_static_MountInfoResponse_DayBuyTimesEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_MountInfoResponse_DayBuyTimesEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_MountInfoResponse_BuyTimesEntry_descriptor =
      internal_static_MountInfoResponse_descriptor.getNestedTypes().get(1);
    internal_static_MountInfoResponse_BuyTimesEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_MountInfoResponse_BuyTimesEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_MountInfoResponse_ReceivesEntry_descriptor =
      internal_static_MountInfoResponse_descriptor.getNestedTypes().get(2);
    internal_static_MountInfoResponse_ReceivesEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_MountInfoResponse_ReceivesEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_MountListResponse_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_MountListResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_MountListResponse_descriptor,
        new java.lang.String[] { "Infos", });
    internal_static_MountGachaRequest_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_MountGachaRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_MountGachaRequest_descriptor,
        new java.lang.String[] { "Times", "UseGoods", });
    internal_static_MountGachaResultResponse_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_MountGachaResultResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_MountGachaResultResponse_descriptor,
        new java.lang.String[] { "RewardObjectMap", });
    internal_static_MountGachaResultResponse_RewardObjectMapEntry_descriptor =
      internal_static_MountGachaResultResponse_descriptor.getNestedTypes().get(0);
    internal_static_MountGachaResultResponse_RewardObjectMapEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_MountGachaResultResponse_RewardObjectMapEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_MountPrivilegeEntity_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_MountPrivilegeEntity_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_MountPrivilegeEntity_descriptor,
        new java.lang.String[] { "Type", "Count", "LastReceiveTime", });
    cn.daxiang.protocol.game.CommonProtocol.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
