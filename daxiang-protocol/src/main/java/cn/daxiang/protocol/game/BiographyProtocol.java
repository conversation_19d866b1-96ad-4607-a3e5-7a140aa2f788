// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: game/biographyProtocol.proto

package cn.daxiang.protocol.game;

public final class BiographyProtocol {
  private BiographyProtocol() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  /**
   * <pre>
   ** 英雄传记 
   * </pre>
   *
   * Protobuf enum {@code BiographyCmd}
   */
  public enum BiographyCmd
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>BIOGRAPHY_CMD_NONE = 0;</code>
     */
    BIOGRAPHY_CMD_NONE(0),
    /**
     * <pre>
     **
     * 获取英雄传记信息
     * 请求:{&#64;code Request}
     * 响应:{&#64;code HeroBiographyResponse}
     * </pre>
     *
     * <code>GET_BIOGRAPHY_INFO = 1;</code>
     */
    GET_BIOGRAPHY_INFO(1),
    /**
     * <pre>
     **
     * 点击英雄传记
     * 请求:{&#64;code IntPacket} 传id
     * 响应:{&#64;code Response}
     * 推送:{&#64;code HeroBiographyResponse}
     * </pre>
     *
     * <code>CLICK_BIOGRAPHY = 2;</code>
     */
    CLICK_BIOGRAPHY(2),
    /**
     * <pre>
     **
     * 领取英雄传记奖励
     * 请求:{&#64;code IntPacket} 传id
     * 响应:{&#64;code RewardResultResponse}
     * 推送:{&#64;code HeroBiographyResponse}
     * </pre>
     *
     * <code>RECIEVE_BIOGRAPHY = 3;</code>
     */
    RECIEVE_BIOGRAPHY(3),
    /**
     * <pre>
     **
     * 推送英雄传记
     * 推送:{&#64;code HeroBiographyResponse}
     * </pre>
     *
     * <code>PUSH_BIOGRAPHY = 100;</code>
     */
    PUSH_BIOGRAPHY(100),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>BIOGRAPHY_CMD_NONE = 0;</code>
     */
    public static final int BIOGRAPHY_CMD_NONE_VALUE = 0;
    /**
     * <pre>
     **
     * 获取英雄传记信息
     * 请求:{&#64;code Request}
     * 响应:{&#64;code HeroBiographyResponse}
     * </pre>
     *
     * <code>GET_BIOGRAPHY_INFO = 1;</code>
     */
    public static final int GET_BIOGRAPHY_INFO_VALUE = 1;
    /**
     * <pre>
     **
     * 点击英雄传记
     * 请求:{&#64;code IntPacket} 传id
     * 响应:{&#64;code Response}
     * 推送:{&#64;code HeroBiographyResponse}
     * </pre>
     *
     * <code>CLICK_BIOGRAPHY = 2;</code>
     */
    public static final int CLICK_BIOGRAPHY_VALUE = 2;
    /**
     * <pre>
     **
     * 领取英雄传记奖励
     * 请求:{&#64;code IntPacket} 传id
     * 响应:{&#64;code RewardResultResponse}
     * 推送:{&#64;code HeroBiographyResponse}
     * </pre>
     *
     * <code>RECIEVE_BIOGRAPHY = 3;</code>
     */
    public static final int RECIEVE_BIOGRAPHY_VALUE = 3;
    /**
     * <pre>
     **
     * 推送英雄传记
     * 推送:{&#64;code HeroBiographyResponse}
     * </pre>
     *
     * <code>PUSH_BIOGRAPHY = 100;</code>
     */
    public static final int PUSH_BIOGRAPHY_VALUE = 100;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static BiographyCmd valueOf(int value) {
      return forNumber(value);
    }

    public static BiographyCmd forNumber(int value) {
      switch (value) {
        case 0: return BIOGRAPHY_CMD_NONE;
        case 1: return GET_BIOGRAPHY_INFO;
        case 2: return CLICK_BIOGRAPHY;
        case 3: return RECIEVE_BIOGRAPHY;
        case 100: return PUSH_BIOGRAPHY;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<BiographyCmd>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        BiographyCmd> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<BiographyCmd>() {
            public BiographyCmd findValueByNumber(int number) {
              return BiographyCmd.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.BiographyProtocol.getDescriptor().getEnumTypes().get(0);
    }

    private static final BiographyCmd[] VALUES = values();

    public static BiographyCmd valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private BiographyCmd(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:BiographyCmd)
  }

  public interface HeroBiographyResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:HeroBiographyResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     **已经点击过的id集合
     * </pre>
     *
     * <code>repeated int32 clickId = 1;</code>
     */
    java.util.List<java.lang.Integer> getClickIdList();
    /**
     * <pre>
     **已经点击过的id集合
     * </pre>
     *
     * <code>repeated int32 clickId = 1;</code>
     */
    int getClickIdCount();
    /**
     * <pre>
     **已经点击过的id集合
     * </pre>
     *
     * <code>repeated int32 clickId = 1;</code>
     */
    int getClickId(int index);

    /**
     * <pre>
     ** 已经领取的id集合
     * </pre>
     *
     * <code>repeated int32 recieveId = 2;</code>
     */
    java.util.List<java.lang.Integer> getRecieveIdList();
    /**
     * <pre>
     ** 已经领取的id集合
     * </pre>
     *
     * <code>repeated int32 recieveId = 2;</code>
     */
    int getRecieveIdCount();
    /**
     * <pre>
     ** 已经领取的id集合
     * </pre>
     *
     * <code>repeated int32 recieveId = 2;</code>
     */
    int getRecieveId(int index);
  }
  /**
   * Protobuf type {@code HeroBiographyResponse}
   */
  public  static final class HeroBiographyResponse extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:HeroBiographyResponse)
      HeroBiographyResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use HeroBiographyResponse.newBuilder() to construct.
    private HeroBiographyResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private HeroBiographyResponse() {
      clickId_ = java.util.Collections.emptyList();
      recieveId_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private HeroBiographyResponse(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
                clickId_ = new java.util.ArrayList<java.lang.Integer>();
                mutable_bitField0_ |= 0x00000001;
              }
              clickId_.add(input.readInt32());
              break;
            }
            case 10: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001) && input.getBytesUntilLimit() > 0) {
                clickId_ = new java.util.ArrayList<java.lang.Integer>();
                mutable_bitField0_ |= 0x00000001;
              }
              while (input.getBytesUntilLimit() > 0) {
                clickId_.add(input.readInt32());
              }
              input.popLimit(limit);
              break;
            }
            case 16: {
              if (!((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
                recieveId_ = new java.util.ArrayList<java.lang.Integer>();
                mutable_bitField0_ |= 0x00000002;
              }
              recieveId_.add(input.readInt32());
              break;
            }
            case 18: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000002) == 0x00000002) && input.getBytesUntilLimit() > 0) {
                recieveId_ = new java.util.ArrayList<java.lang.Integer>();
                mutable_bitField0_ |= 0x00000002;
              }
              while (input.getBytesUntilLimit() > 0) {
                recieveId_.add(input.readInt32());
              }
              input.popLimit(limit);
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
          clickId_ = java.util.Collections.unmodifiableList(clickId_);
        }
        if (((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
          recieveId_ = java.util.Collections.unmodifiableList(recieveId_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.BiographyProtocol.internal_static_HeroBiographyResponse_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.daxiang.protocol.game.BiographyProtocol.internal_static_HeroBiographyResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.daxiang.protocol.game.BiographyProtocol.HeroBiographyResponse.class, cn.daxiang.protocol.game.BiographyProtocol.HeroBiographyResponse.Builder.class);
    }

    public static final int CLICKID_FIELD_NUMBER = 1;
    private java.util.List<java.lang.Integer> clickId_;
    /**
     * <pre>
     **已经点击过的id集合
     * </pre>
     *
     * <code>repeated int32 clickId = 1;</code>
     */
    public java.util.List<java.lang.Integer>
        getClickIdList() {
      return clickId_;
    }
    /**
     * <pre>
     **已经点击过的id集合
     * </pre>
     *
     * <code>repeated int32 clickId = 1;</code>
     */
    public int getClickIdCount() {
      return clickId_.size();
    }
    /**
     * <pre>
     **已经点击过的id集合
     * </pre>
     *
     * <code>repeated int32 clickId = 1;</code>
     */
    public int getClickId(int index) {
      return clickId_.get(index);
    }
    private int clickIdMemoizedSerializedSize = -1;

    public static final int RECIEVEID_FIELD_NUMBER = 2;
    private java.util.List<java.lang.Integer> recieveId_;
    /**
     * <pre>
     ** 已经领取的id集合
     * </pre>
     *
     * <code>repeated int32 recieveId = 2;</code>
     */
    public java.util.List<java.lang.Integer>
        getRecieveIdList() {
      return recieveId_;
    }
    /**
     * <pre>
     ** 已经领取的id集合
     * </pre>
     *
     * <code>repeated int32 recieveId = 2;</code>
     */
    public int getRecieveIdCount() {
      return recieveId_.size();
    }
    /**
     * <pre>
     ** 已经领取的id集合
     * </pre>
     *
     * <code>repeated int32 recieveId = 2;</code>
     */
    public int getRecieveId(int index) {
      return recieveId_.get(index);
    }
    private int recieveIdMemoizedSerializedSize = -1;

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (getClickIdList().size() > 0) {
        output.writeUInt32NoTag(10);
        output.writeUInt32NoTag(clickIdMemoizedSerializedSize);
      }
      for (int i = 0; i < clickId_.size(); i++) {
        output.writeInt32NoTag(clickId_.get(i));
      }
      if (getRecieveIdList().size() > 0) {
        output.writeUInt32NoTag(18);
        output.writeUInt32NoTag(recieveIdMemoizedSerializedSize);
      }
      for (int i = 0; i < recieveId_.size(); i++) {
        output.writeInt32NoTag(recieveId_.get(i));
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      {
        int dataSize = 0;
        for (int i = 0; i < clickId_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt32SizeNoTag(clickId_.get(i));
        }
        size += dataSize;
        if (!getClickIdList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        clickIdMemoizedSerializedSize = dataSize;
      }
      {
        int dataSize = 0;
        for (int i = 0; i < recieveId_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt32SizeNoTag(recieveId_.get(i));
        }
        size += dataSize;
        if (!getRecieveIdList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        recieveIdMemoizedSerializedSize = dataSize;
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof cn.daxiang.protocol.game.BiographyProtocol.HeroBiographyResponse)) {
        return super.equals(obj);
      }
      cn.daxiang.protocol.game.BiographyProtocol.HeroBiographyResponse other = (cn.daxiang.protocol.game.BiographyProtocol.HeroBiographyResponse) obj;

      boolean result = true;
      result = result && getClickIdList()
          .equals(other.getClickIdList());
      result = result && getRecieveIdList()
          .equals(other.getRecieveIdList());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getClickIdCount() > 0) {
        hash = (37 * hash) + CLICKID_FIELD_NUMBER;
        hash = (53 * hash) + getClickIdList().hashCode();
      }
      if (getRecieveIdCount() > 0) {
        hash = (37 * hash) + RECIEVEID_FIELD_NUMBER;
        hash = (53 * hash) + getRecieveIdList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static cn.daxiang.protocol.game.BiographyProtocol.HeroBiographyResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BiographyProtocol.HeroBiographyResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BiographyProtocol.HeroBiographyResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BiographyProtocol.HeroBiographyResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BiographyProtocol.HeroBiographyResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.BiographyProtocol.HeroBiographyResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BiographyProtocol.HeroBiographyResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BiographyProtocol.HeroBiographyResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BiographyProtocol.HeroBiographyResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BiographyProtocol.HeroBiographyResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.BiographyProtocol.HeroBiographyResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.BiographyProtocol.HeroBiographyResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(cn.daxiang.protocol.game.BiographyProtocol.HeroBiographyResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code HeroBiographyResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:HeroBiographyResponse)
        cn.daxiang.protocol.game.BiographyProtocol.HeroBiographyResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.daxiang.protocol.game.BiographyProtocol.internal_static_HeroBiographyResponse_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.daxiang.protocol.game.BiographyProtocol.internal_static_HeroBiographyResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.daxiang.protocol.game.BiographyProtocol.HeroBiographyResponse.class, cn.daxiang.protocol.game.BiographyProtocol.HeroBiographyResponse.Builder.class);
      }

      // Construct using cn.daxiang.protocol.game.BiographyProtocol.HeroBiographyResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        clickId_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        recieveId_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return cn.daxiang.protocol.game.BiographyProtocol.internal_static_HeroBiographyResponse_descriptor;
      }

      public cn.daxiang.protocol.game.BiographyProtocol.HeroBiographyResponse getDefaultInstanceForType() {
        return cn.daxiang.protocol.game.BiographyProtocol.HeroBiographyResponse.getDefaultInstance();
      }

      public cn.daxiang.protocol.game.BiographyProtocol.HeroBiographyResponse build() {
        cn.daxiang.protocol.game.BiographyProtocol.HeroBiographyResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public cn.daxiang.protocol.game.BiographyProtocol.HeroBiographyResponse buildPartial() {
        cn.daxiang.protocol.game.BiographyProtocol.HeroBiographyResponse result = new cn.daxiang.protocol.game.BiographyProtocol.HeroBiographyResponse(this);
        int from_bitField0_ = bitField0_;
        if (((bitField0_ & 0x00000001) == 0x00000001)) {
          clickId_ = java.util.Collections.unmodifiableList(clickId_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.clickId_ = clickId_;
        if (((bitField0_ & 0x00000002) == 0x00000002)) {
          recieveId_ = java.util.Collections.unmodifiableList(recieveId_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.recieveId_ = recieveId_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof cn.daxiang.protocol.game.BiographyProtocol.HeroBiographyResponse) {
          return mergeFrom((cn.daxiang.protocol.game.BiographyProtocol.HeroBiographyResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(cn.daxiang.protocol.game.BiographyProtocol.HeroBiographyResponse other) {
        if (other == cn.daxiang.protocol.game.BiographyProtocol.HeroBiographyResponse.getDefaultInstance()) return this;
        if (!other.clickId_.isEmpty()) {
          if (clickId_.isEmpty()) {
            clickId_ = other.clickId_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureClickIdIsMutable();
            clickId_.addAll(other.clickId_);
          }
          onChanged();
        }
        if (!other.recieveId_.isEmpty()) {
          if (recieveId_.isEmpty()) {
            recieveId_ = other.recieveId_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureRecieveIdIsMutable();
            recieveId_.addAll(other.recieveId_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        cn.daxiang.protocol.game.BiographyProtocol.HeroBiographyResponse parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (cn.daxiang.protocol.game.BiographyProtocol.HeroBiographyResponse) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<java.lang.Integer> clickId_ = java.util.Collections.emptyList();
      private void ensureClickIdIsMutable() {
        if (!((bitField0_ & 0x00000001) == 0x00000001)) {
          clickId_ = new java.util.ArrayList<java.lang.Integer>(clickId_);
          bitField0_ |= 0x00000001;
         }
      }
      /**
       * <pre>
       **已经点击过的id集合
       * </pre>
       *
       * <code>repeated int32 clickId = 1;</code>
       */
      public java.util.List<java.lang.Integer>
          getClickIdList() {
        return java.util.Collections.unmodifiableList(clickId_);
      }
      /**
       * <pre>
       **已经点击过的id集合
       * </pre>
       *
       * <code>repeated int32 clickId = 1;</code>
       */
      public int getClickIdCount() {
        return clickId_.size();
      }
      /**
       * <pre>
       **已经点击过的id集合
       * </pre>
       *
       * <code>repeated int32 clickId = 1;</code>
       */
      public int getClickId(int index) {
        return clickId_.get(index);
      }
      /**
       * <pre>
       **已经点击过的id集合
       * </pre>
       *
       * <code>repeated int32 clickId = 1;</code>
       */
      public Builder setClickId(
          int index, int value) {
        ensureClickIdIsMutable();
        clickId_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       **已经点击过的id集合
       * </pre>
       *
       * <code>repeated int32 clickId = 1;</code>
       */
      public Builder addClickId(int value) {
        ensureClickIdIsMutable();
        clickId_.add(value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       **已经点击过的id集合
       * </pre>
       *
       * <code>repeated int32 clickId = 1;</code>
       */
      public Builder addAllClickId(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureClickIdIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, clickId_);
        onChanged();
        return this;
      }
      /**
       * <pre>
       **已经点击过的id集合
       * </pre>
       *
       * <code>repeated int32 clickId = 1;</code>
       */
      public Builder clearClickId() {
        clickId_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }

      private java.util.List<java.lang.Integer> recieveId_ = java.util.Collections.emptyList();
      private void ensureRecieveIdIsMutable() {
        if (!((bitField0_ & 0x00000002) == 0x00000002)) {
          recieveId_ = new java.util.ArrayList<java.lang.Integer>(recieveId_);
          bitField0_ |= 0x00000002;
         }
      }
      /**
       * <pre>
       ** 已经领取的id集合
       * </pre>
       *
       * <code>repeated int32 recieveId = 2;</code>
       */
      public java.util.List<java.lang.Integer>
          getRecieveIdList() {
        return java.util.Collections.unmodifiableList(recieveId_);
      }
      /**
       * <pre>
       ** 已经领取的id集合
       * </pre>
       *
       * <code>repeated int32 recieveId = 2;</code>
       */
      public int getRecieveIdCount() {
        return recieveId_.size();
      }
      /**
       * <pre>
       ** 已经领取的id集合
       * </pre>
       *
       * <code>repeated int32 recieveId = 2;</code>
       */
      public int getRecieveId(int index) {
        return recieveId_.get(index);
      }
      /**
       * <pre>
       ** 已经领取的id集合
       * </pre>
       *
       * <code>repeated int32 recieveId = 2;</code>
       */
      public Builder setRecieveId(
          int index, int value) {
        ensureRecieveIdIsMutable();
        recieveId_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 已经领取的id集合
       * </pre>
       *
       * <code>repeated int32 recieveId = 2;</code>
       */
      public Builder addRecieveId(int value) {
        ensureRecieveIdIsMutable();
        recieveId_.add(value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 已经领取的id集合
       * </pre>
       *
       * <code>repeated int32 recieveId = 2;</code>
       */
      public Builder addAllRecieveId(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureRecieveIdIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, recieveId_);
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 已经领取的id集合
       * </pre>
       *
       * <code>repeated int32 recieveId = 2;</code>
       */
      public Builder clearRecieveId() {
        recieveId_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:HeroBiographyResponse)
    }

    // @@protoc_insertion_point(class_scope:HeroBiographyResponse)
    private static final cn.daxiang.protocol.game.BiographyProtocol.HeroBiographyResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new cn.daxiang.protocol.game.BiographyProtocol.HeroBiographyResponse();
    }

    public static cn.daxiang.protocol.game.BiographyProtocol.HeroBiographyResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<HeroBiographyResponse>
        PARSER = new com.google.protobuf.AbstractParser<HeroBiographyResponse>() {
      public HeroBiographyResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new HeroBiographyResponse(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<HeroBiographyResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<HeroBiographyResponse> getParserForType() {
      return PARSER;
    }

    public cn.daxiang.protocol.game.BiographyProtocol.HeroBiographyResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_HeroBiographyResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_HeroBiographyResponse_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\034game/biographyProtocol.proto\032\031game/com" +
      "monProtocol.proto\";\n\025HeroBiographyRespon" +
      "se\022\017\n\007clickId\030\001 \003(\005\022\021\n\trecieveId\030\002 \003(\005*~" +
      "\n\014BiographyCmd\022\026\n\022BIOGRAPHY_CMD_NONE\020\000\022\026" +
      "\n\022GET_BIOGRAPHY_INFO\020\001\022\023\n\017CLICK_BIOGRAPH" +
      "Y\020\002\022\025\n\021RECIEVE_BIOGRAPHY\020\003\022\022\n\016PUSH_BIOGR" +
      "APHY\020dB\034\n\030cn.daxiang.protocol.gameH\001b\006pr" +
      "oto3"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
        new com.google.protobuf.Descriptors.FileDescriptor.    InternalDescriptorAssigner() {
          public com.google.protobuf.ExtensionRegistry assignDescriptors(
              com.google.protobuf.Descriptors.FileDescriptor root) {
            descriptor = root;
            return null;
          }
        };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          cn.daxiang.protocol.game.CommonProtocol.getDescriptor(),
        }, assigner);
    internal_static_HeroBiographyResponse_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_HeroBiographyResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_HeroBiographyResponse_descriptor,
        new java.lang.String[] { "ClickId", "RecieveId", });
    cn.daxiang.protocol.game.CommonProtocol.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
