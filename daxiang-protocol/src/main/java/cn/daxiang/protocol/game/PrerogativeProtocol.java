// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: game/prerogativeProtocol.proto

package cn.daxiang.protocol.game;

public final class PrerogativeProtocol {
  private PrerogativeProtocol() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  /**
   * <pre>
   ** 特权 
   * </pre>
   *
   * Protobuf enum {@code PrerogativeCmd}
   */
  public enum PrerogativeCmd
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>USER_CMD_NONE = 0;</code>
     */
    USER_CMD_NONE(0),
    /**
     * <pre>
     **
     * 获取特权列表
     * 请求: {&#64;code Request}
     * 响应: {&#64;code PrerogativeInfoResponse}
     * </pre>
     *
     * <code>GET_PREROGATIVE_LIST = 1;</code>
     */
    GET_PREROGATIVE_LIST(1),
    /**
     * <pre>
     **
     * 踢人下线
     * 请求:无
     * 推送:{&#64;code PrerogativeInfoResponse}
     * </pre>
     *
     * <code>PUSH_PREROGATIVE_LIST = 100;</code>
     */
    PUSH_PREROGATIVE_LIST(100),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>USER_CMD_NONE = 0;</code>
     */
    public static final int USER_CMD_NONE_VALUE = 0;
    /**
     * <pre>
     **
     * 获取特权列表
     * 请求: {&#64;code Request}
     * 响应: {&#64;code PrerogativeInfoResponse}
     * </pre>
     *
     * <code>GET_PREROGATIVE_LIST = 1;</code>
     */
    public static final int GET_PREROGATIVE_LIST_VALUE = 1;
    /**
     * <pre>
     **
     * 踢人下线
     * 请求:无
     * 推送:{&#64;code PrerogativeInfoResponse}
     * </pre>
     *
     * <code>PUSH_PREROGATIVE_LIST = 100;</code>
     */
    public static final int PUSH_PREROGATIVE_LIST_VALUE = 100;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static PrerogativeCmd valueOf(int value) {
      return forNumber(value);
    }

    public static PrerogativeCmd forNumber(int value) {
      switch (value) {
        case 0: return USER_CMD_NONE;
        case 1: return GET_PREROGATIVE_LIST;
        case 100: return PUSH_PREROGATIVE_LIST;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<PrerogativeCmd>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        PrerogativeCmd> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<PrerogativeCmd>() {
            public PrerogativeCmd findValueByNumber(int number) {
              return PrerogativeCmd.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.PrerogativeProtocol.getDescriptor().getEnumTypes().get(0);
    }

    private static final PrerogativeCmd[] VALUES = values();

    public static PrerogativeCmd valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private PrerogativeCmd(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:PrerogativeCmd)
  }

  public interface PrerogativeInfoResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:PrerogativeInfoResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     ** key: PrerogativeType value:Prerogative 
     * </pre>
     *
     * <code>map&lt;int32, .PrerogativeList&gt; prerogativeMap = 1;</code>
     */
    int getPrerogativeMapCount();
    /**
     * <pre>
     ** key: PrerogativeType value:Prerogative 
     * </pre>
     *
     * <code>map&lt;int32, .PrerogativeList&gt; prerogativeMap = 1;</code>
     */
    boolean containsPrerogativeMap(
        int key);
    /**
     * Use {@link #getPrerogativeMapMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Integer, cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList>
    getPrerogativeMap();
    /**
     * <pre>
     ** key: PrerogativeType value:Prerogative 
     * </pre>
     *
     * <code>map&lt;int32, .PrerogativeList&gt; prerogativeMap = 1;</code>
     */
    java.util.Map<java.lang.Integer, cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList>
    getPrerogativeMapMap();
    /**
     * <pre>
     ** key: PrerogativeType value:Prerogative 
     * </pre>
     *
     * <code>map&lt;int32, .PrerogativeList&gt; prerogativeMap = 1;</code>
     */

    cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList getPrerogativeMapOrDefault(
        int key,
        cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList defaultValue);
    /**
     * <pre>
     ** key: PrerogativeType value:Prerogative 
     * </pre>
     *
     * <code>map&lt;int32, .PrerogativeList&gt; prerogativeMap = 1;</code>
     */

    cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList getPrerogativeMapOrThrow(
        int key);
  }
  /**
   * <pre>
   ** 获取玩家信息响应
   * </pre>
   *
   * Protobuf type {@code PrerogativeInfoResponse}
   */
  public  static final class PrerogativeInfoResponse extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:PrerogativeInfoResponse)
      PrerogativeInfoResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use PrerogativeInfoResponse.newBuilder() to construct.
    private PrerogativeInfoResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private PrerogativeInfoResponse() {
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private PrerogativeInfoResponse(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
                prerogativeMap_ = com.google.protobuf.MapField.newMapField(
                    PrerogativeMapDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000001;
              }
              com.google.protobuf.MapEntry<java.lang.Integer, cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList>
              prerogativeMap__ = input.readMessage(
                  PrerogativeMapDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              prerogativeMap_.getMutableMap().put(
                  prerogativeMap__.getKey(), prerogativeMap__.getValue());
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.PrerogativeProtocol.internal_static_PrerogativeInfoResponse_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 1:
          return internalGetPrerogativeMap();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.daxiang.protocol.game.PrerogativeProtocol.internal_static_PrerogativeInfoResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeInfoResponse.class, cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeInfoResponse.Builder.class);
    }

    public static final int PREROGATIVEMAP_FIELD_NUMBER = 1;
    private static final class PrerogativeMapDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Integer, cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Integer, cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList>newDefaultInstance(
                  cn.daxiang.protocol.game.PrerogativeProtocol.internal_static_PrerogativeInfoResponse_PrerogativeMapEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0,
                  com.google.protobuf.WireFormat.FieldType.MESSAGE,
                  cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList.getDefaultInstance());
    }
    private com.google.protobuf.MapField<
        java.lang.Integer, cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList> prerogativeMap_;
    private com.google.protobuf.MapField<java.lang.Integer, cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList>
    internalGetPrerogativeMap() {
      if (prerogativeMap_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            PrerogativeMapDefaultEntryHolder.defaultEntry);
      }
      return prerogativeMap_;
    }

    public int getPrerogativeMapCount() {
      return internalGetPrerogativeMap().getMap().size();
    }
    /**
     * <pre>
     ** key: PrerogativeType value:Prerogative 
     * </pre>
     *
     * <code>map&lt;int32, .PrerogativeList&gt; prerogativeMap = 1;</code>
     */

    public boolean containsPrerogativeMap(
        int key) {
      
      return internalGetPrerogativeMap().getMap().containsKey(key);
    }
    /**
     * Use {@link #getPrerogativeMapMap()} instead.
     */
    @java.lang.Deprecated
    public java.util.Map<java.lang.Integer, cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList> getPrerogativeMap() {
      return getPrerogativeMapMap();
    }
    /**
     * <pre>
     ** key: PrerogativeType value:Prerogative 
     * </pre>
     *
     * <code>map&lt;int32, .PrerogativeList&gt; prerogativeMap = 1;</code>
     */

    public java.util.Map<java.lang.Integer, cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList> getPrerogativeMapMap() {
      return internalGetPrerogativeMap().getMap();
    }
    /**
     * <pre>
     ** key: PrerogativeType value:Prerogative 
     * </pre>
     *
     * <code>map&lt;int32, .PrerogativeList&gt; prerogativeMap = 1;</code>
     */

    public cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList getPrerogativeMapOrDefault(
        int key,
        cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList defaultValue) {
      
      java.util.Map<java.lang.Integer, cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList> map =
          internalGetPrerogativeMap().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <pre>
     ** key: PrerogativeType value:Prerogative 
     * </pre>
     *
     * <code>map&lt;int32, .PrerogativeList&gt; prerogativeMap = 1;</code>
     */

    public cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList getPrerogativeMapOrThrow(
        int key) {
      
      java.util.Map<java.lang.Integer, cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList> map =
          internalGetPrerogativeMap().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      com.google.protobuf.GeneratedMessageV3
        .serializeIntegerMapTo(
          output,
          internalGetPrerogativeMap(),
          PrerogativeMapDefaultEntryHolder.defaultEntry,
          1);
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (java.util.Map.Entry<java.lang.Integer, cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList> entry
           : internalGetPrerogativeMap().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Integer, cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList>
        prerogativeMap__ = PrerogativeMapDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(1, prerogativeMap__);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeInfoResponse)) {
        return super.equals(obj);
      }
      cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeInfoResponse other = (cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeInfoResponse) obj;

      boolean result = true;
      result = result && internalGetPrerogativeMap().equals(
          other.internalGetPrerogativeMap());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (!internalGetPrerogativeMap().getMap().isEmpty()) {
        hash = (37 * hash) + PREROGATIVEMAP_FIELD_NUMBER;
        hash = (53 * hash) + internalGetPrerogativeMap().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeInfoResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeInfoResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeInfoResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeInfoResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeInfoResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeInfoResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeInfoResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeInfoResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeInfoResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeInfoResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeInfoResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeInfoResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeInfoResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** 获取玩家信息响应
     * </pre>
     *
     * Protobuf type {@code PrerogativeInfoResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:PrerogativeInfoResponse)
        cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeInfoResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.daxiang.protocol.game.PrerogativeProtocol.internal_static_PrerogativeInfoResponse_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetPrerogativeMap();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetMutablePrerogativeMap();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.daxiang.protocol.game.PrerogativeProtocol.internal_static_PrerogativeInfoResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeInfoResponse.class, cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeInfoResponse.Builder.class);
      }

      // Construct using cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeInfoResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        internalGetMutablePrerogativeMap().clear();
        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return cn.daxiang.protocol.game.PrerogativeProtocol.internal_static_PrerogativeInfoResponse_descriptor;
      }

      public cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeInfoResponse getDefaultInstanceForType() {
        return cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeInfoResponse.getDefaultInstance();
      }

      public cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeInfoResponse build() {
        cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeInfoResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeInfoResponse buildPartial() {
        cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeInfoResponse result = new cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeInfoResponse(this);
        int from_bitField0_ = bitField0_;
        result.prerogativeMap_ = internalGetPrerogativeMap();
        result.prerogativeMap_.makeImmutable();
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeInfoResponse) {
          return mergeFrom((cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeInfoResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeInfoResponse other) {
        if (other == cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeInfoResponse.getDefaultInstance()) return this;
        internalGetMutablePrerogativeMap().mergeFrom(
            other.internalGetPrerogativeMap());
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeInfoResponse parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeInfoResponse) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.MapField<
          java.lang.Integer, cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList> prerogativeMap_;
      private com.google.protobuf.MapField<java.lang.Integer, cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList>
      internalGetPrerogativeMap() {
        if (prerogativeMap_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              PrerogativeMapDefaultEntryHolder.defaultEntry);
        }
        return prerogativeMap_;
      }
      private com.google.protobuf.MapField<java.lang.Integer, cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList>
      internalGetMutablePrerogativeMap() {
        onChanged();;
        if (prerogativeMap_ == null) {
          prerogativeMap_ = com.google.protobuf.MapField.newMapField(
              PrerogativeMapDefaultEntryHolder.defaultEntry);
        }
        if (!prerogativeMap_.isMutable()) {
          prerogativeMap_ = prerogativeMap_.copy();
        }
        return prerogativeMap_;
      }

      public int getPrerogativeMapCount() {
        return internalGetPrerogativeMap().getMap().size();
      }
      /**
       * <pre>
       ** key: PrerogativeType value:Prerogative 
       * </pre>
       *
       * <code>map&lt;int32, .PrerogativeList&gt; prerogativeMap = 1;</code>
       */

      public boolean containsPrerogativeMap(
          int key) {
        
        return internalGetPrerogativeMap().getMap().containsKey(key);
      }
      /**
       * Use {@link #getPrerogativeMapMap()} instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList> getPrerogativeMap() {
        return getPrerogativeMapMap();
      }
      /**
       * <pre>
       ** key: PrerogativeType value:Prerogative 
       * </pre>
       *
       * <code>map&lt;int32, .PrerogativeList&gt; prerogativeMap = 1;</code>
       */

      public java.util.Map<java.lang.Integer, cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList> getPrerogativeMapMap() {
        return internalGetPrerogativeMap().getMap();
      }
      /**
       * <pre>
       ** key: PrerogativeType value:Prerogative 
       * </pre>
       *
       * <code>map&lt;int32, .PrerogativeList&gt; prerogativeMap = 1;</code>
       */

      public cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList getPrerogativeMapOrDefault(
          int key,
          cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList defaultValue) {
        
        java.util.Map<java.lang.Integer, cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList> map =
            internalGetPrerogativeMap().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <pre>
       ** key: PrerogativeType value:Prerogative 
       * </pre>
       *
       * <code>map&lt;int32, .PrerogativeList&gt; prerogativeMap = 1;</code>
       */

      public cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList getPrerogativeMapOrThrow(
          int key) {
        
        java.util.Map<java.lang.Integer, cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList> map =
            internalGetPrerogativeMap().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearPrerogativeMap() {
        internalGetMutablePrerogativeMap().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <pre>
       ** key: PrerogativeType value:Prerogative 
       * </pre>
       *
       * <code>map&lt;int32, .PrerogativeList&gt; prerogativeMap = 1;</code>
       */

      public Builder removePrerogativeMap(
          int key) {
        
        internalGetMutablePrerogativeMap().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList>
      getMutablePrerogativeMap() {
        return internalGetMutablePrerogativeMap().getMutableMap();
      }
      /**
       * <pre>
       ** key: PrerogativeType value:Prerogative 
       * </pre>
       *
       * <code>map&lt;int32, .PrerogativeList&gt; prerogativeMap = 1;</code>
       */
      public Builder putPrerogativeMap(
          int key,
          cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList value) {
        
        if (value == null) { throw new java.lang.NullPointerException(); }
        internalGetMutablePrerogativeMap().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <pre>
       ** key: PrerogativeType value:Prerogative 
       * </pre>
       *
       * <code>map&lt;int32, .PrerogativeList&gt; prerogativeMap = 1;</code>
       */

      public Builder putAllPrerogativeMap(
          java.util.Map<java.lang.Integer, cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList> values) {
        internalGetMutablePrerogativeMap().getMutableMap()
            .putAll(values);
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:PrerogativeInfoResponse)
    }

    // @@protoc_insertion_point(class_scope:PrerogativeInfoResponse)
    private static final cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeInfoResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeInfoResponse();
    }

    public static cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeInfoResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<PrerogativeInfoResponse>
        PARSER = new com.google.protobuf.AbstractParser<PrerogativeInfoResponse>() {
      public PrerogativeInfoResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new PrerogativeInfoResponse(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<PrerogativeInfoResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<PrerogativeInfoResponse> getParserForType() {
      return PARSER;
    }

    public cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeInfoResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface PrerogativeListOrBuilder extends
      // @@protoc_insertion_point(interface_extends:PrerogativeList)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .Prerogative prerogatives = 1;</code>
     */
    java.util.List<cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative> 
        getPrerogativesList();
    /**
     * <code>repeated .Prerogative prerogatives = 1;</code>
     */
    cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative getPrerogatives(int index);
    /**
     * <code>repeated .Prerogative prerogatives = 1;</code>
     */
    int getPrerogativesCount();
    /**
     * <code>repeated .Prerogative prerogatives = 1;</code>
     */
    java.util.List<? extends cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeOrBuilder> 
        getPrerogativesOrBuilderList();
    /**
     * <code>repeated .Prerogative prerogatives = 1;</code>
     */
    cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeOrBuilder getPrerogativesOrBuilder(
        int index);
  }
  /**
   * <pre>
   ** 特权列表 
   * </pre>
   *
   * Protobuf type {@code PrerogativeList}
   */
  public  static final class PrerogativeList extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:PrerogativeList)
      PrerogativeListOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use PrerogativeList.newBuilder() to construct.
    private PrerogativeList(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private PrerogativeList() {
      prerogatives_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private PrerogativeList(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
                prerogatives_ = new java.util.ArrayList<cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative>();
                mutable_bitField0_ |= 0x00000001;
              }
              prerogatives_.add(
                  input.readMessage(cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative.parser(), extensionRegistry));
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
          prerogatives_ = java.util.Collections.unmodifiableList(prerogatives_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.PrerogativeProtocol.internal_static_PrerogativeList_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.daxiang.protocol.game.PrerogativeProtocol.internal_static_PrerogativeList_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList.class, cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList.Builder.class);
    }

    public static final int PREROGATIVES_FIELD_NUMBER = 1;
    private java.util.List<cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative> prerogatives_;
    /**
     * <code>repeated .Prerogative prerogatives = 1;</code>
     */
    public java.util.List<cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative> getPrerogativesList() {
      return prerogatives_;
    }
    /**
     * <code>repeated .Prerogative prerogatives = 1;</code>
     */
    public java.util.List<? extends cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeOrBuilder> 
        getPrerogativesOrBuilderList() {
      return prerogatives_;
    }
    /**
     * <code>repeated .Prerogative prerogatives = 1;</code>
     */
    public int getPrerogativesCount() {
      return prerogatives_.size();
    }
    /**
     * <code>repeated .Prerogative prerogatives = 1;</code>
     */
    public cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative getPrerogatives(int index) {
      return prerogatives_.get(index);
    }
    /**
     * <code>repeated .Prerogative prerogatives = 1;</code>
     */
    public cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeOrBuilder getPrerogativesOrBuilder(
        int index) {
      return prerogatives_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < prerogatives_.size(); i++) {
        output.writeMessage(1, prerogatives_.get(i));
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < prerogatives_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, prerogatives_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList)) {
        return super.equals(obj);
      }
      cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList other = (cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList) obj;

      boolean result = true;
      result = result && getPrerogativesList()
          .equals(other.getPrerogativesList());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getPrerogativesCount() > 0) {
        hash = (37 * hash) + PREROGATIVES_FIELD_NUMBER;
        hash = (53 * hash) + getPrerogativesList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** 特权列表 
     * </pre>
     *
     * Protobuf type {@code PrerogativeList}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:PrerogativeList)
        cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeListOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.daxiang.protocol.game.PrerogativeProtocol.internal_static_PrerogativeList_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.daxiang.protocol.game.PrerogativeProtocol.internal_static_PrerogativeList_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList.class, cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList.Builder.class);
      }

      // Construct using cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getPrerogativesFieldBuilder();
        }
      }
      public Builder clear() {
        super.clear();
        if (prerogativesBuilder_ == null) {
          prerogatives_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          prerogativesBuilder_.clear();
        }
        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return cn.daxiang.protocol.game.PrerogativeProtocol.internal_static_PrerogativeList_descriptor;
      }

      public cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList getDefaultInstanceForType() {
        return cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList.getDefaultInstance();
      }

      public cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList build() {
        cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList buildPartial() {
        cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList result = new cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList(this);
        int from_bitField0_ = bitField0_;
        if (prerogativesBuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001)) {
            prerogatives_ = java.util.Collections.unmodifiableList(prerogatives_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.prerogatives_ = prerogatives_;
        } else {
          result.prerogatives_ = prerogativesBuilder_.build();
        }
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList) {
          return mergeFrom((cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList other) {
        if (other == cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList.getDefaultInstance()) return this;
        if (prerogativesBuilder_ == null) {
          if (!other.prerogatives_.isEmpty()) {
            if (prerogatives_.isEmpty()) {
              prerogatives_ = other.prerogatives_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensurePrerogativesIsMutable();
              prerogatives_.addAll(other.prerogatives_);
            }
            onChanged();
          }
        } else {
          if (!other.prerogatives_.isEmpty()) {
            if (prerogativesBuilder_.isEmpty()) {
              prerogativesBuilder_.dispose();
              prerogativesBuilder_ = null;
              prerogatives_ = other.prerogatives_;
              bitField0_ = (bitField0_ & ~0x00000001);
              prerogativesBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getPrerogativesFieldBuilder() : null;
            } else {
              prerogativesBuilder_.addAllMessages(other.prerogatives_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative> prerogatives_ =
        java.util.Collections.emptyList();
      private void ensurePrerogativesIsMutable() {
        if (!((bitField0_ & 0x00000001) == 0x00000001)) {
          prerogatives_ = new java.util.ArrayList<cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative>(prerogatives_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative, cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative.Builder, cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeOrBuilder> prerogativesBuilder_;

      /**
       * <code>repeated .Prerogative prerogatives = 1;</code>
       */
      public java.util.List<cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative> getPrerogativesList() {
        if (prerogativesBuilder_ == null) {
          return java.util.Collections.unmodifiableList(prerogatives_);
        } else {
          return prerogativesBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .Prerogative prerogatives = 1;</code>
       */
      public int getPrerogativesCount() {
        if (prerogativesBuilder_ == null) {
          return prerogatives_.size();
        } else {
          return prerogativesBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .Prerogative prerogatives = 1;</code>
       */
      public cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative getPrerogatives(int index) {
        if (prerogativesBuilder_ == null) {
          return prerogatives_.get(index);
        } else {
          return prerogativesBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .Prerogative prerogatives = 1;</code>
       */
      public Builder setPrerogatives(
          int index, cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative value) {
        if (prerogativesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePrerogativesIsMutable();
          prerogatives_.set(index, value);
          onChanged();
        } else {
          prerogativesBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .Prerogative prerogatives = 1;</code>
       */
      public Builder setPrerogatives(
          int index, cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative.Builder builderForValue) {
        if (prerogativesBuilder_ == null) {
          ensurePrerogativesIsMutable();
          prerogatives_.set(index, builderForValue.build());
          onChanged();
        } else {
          prerogativesBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .Prerogative prerogatives = 1;</code>
       */
      public Builder addPrerogatives(cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative value) {
        if (prerogativesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePrerogativesIsMutable();
          prerogatives_.add(value);
          onChanged();
        } else {
          prerogativesBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .Prerogative prerogatives = 1;</code>
       */
      public Builder addPrerogatives(
          int index, cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative value) {
        if (prerogativesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePrerogativesIsMutable();
          prerogatives_.add(index, value);
          onChanged();
        } else {
          prerogativesBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .Prerogative prerogatives = 1;</code>
       */
      public Builder addPrerogatives(
          cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative.Builder builderForValue) {
        if (prerogativesBuilder_ == null) {
          ensurePrerogativesIsMutable();
          prerogatives_.add(builderForValue.build());
          onChanged();
        } else {
          prerogativesBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .Prerogative prerogatives = 1;</code>
       */
      public Builder addPrerogatives(
          int index, cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative.Builder builderForValue) {
        if (prerogativesBuilder_ == null) {
          ensurePrerogativesIsMutable();
          prerogatives_.add(index, builderForValue.build());
          onChanged();
        } else {
          prerogativesBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .Prerogative prerogatives = 1;</code>
       */
      public Builder addAllPrerogatives(
          java.lang.Iterable<? extends cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative> values) {
        if (prerogativesBuilder_ == null) {
          ensurePrerogativesIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, prerogatives_);
          onChanged();
        } else {
          prerogativesBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .Prerogative prerogatives = 1;</code>
       */
      public Builder clearPrerogatives() {
        if (prerogativesBuilder_ == null) {
          prerogatives_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          prerogativesBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .Prerogative prerogatives = 1;</code>
       */
      public Builder removePrerogatives(int index) {
        if (prerogativesBuilder_ == null) {
          ensurePrerogativesIsMutable();
          prerogatives_.remove(index);
          onChanged();
        } else {
          prerogativesBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .Prerogative prerogatives = 1;</code>
       */
      public cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative.Builder getPrerogativesBuilder(
          int index) {
        return getPrerogativesFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .Prerogative prerogatives = 1;</code>
       */
      public cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeOrBuilder getPrerogativesOrBuilder(
          int index) {
        if (prerogativesBuilder_ == null) {
          return prerogatives_.get(index);  } else {
          return prerogativesBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .Prerogative prerogatives = 1;</code>
       */
      public java.util.List<? extends cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeOrBuilder> 
           getPrerogativesOrBuilderList() {
        if (prerogativesBuilder_ != null) {
          return prerogativesBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(prerogatives_);
        }
      }
      /**
       * <code>repeated .Prerogative prerogatives = 1;</code>
       */
      public cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative.Builder addPrerogativesBuilder() {
        return getPrerogativesFieldBuilder().addBuilder(
            cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative.getDefaultInstance());
      }
      /**
       * <code>repeated .Prerogative prerogatives = 1;</code>
       */
      public cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative.Builder addPrerogativesBuilder(
          int index) {
        return getPrerogativesFieldBuilder().addBuilder(
            index, cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative.getDefaultInstance());
      }
      /**
       * <code>repeated .Prerogative prerogatives = 1;</code>
       */
      public java.util.List<cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative.Builder> 
           getPrerogativesBuilderList() {
        return getPrerogativesFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative, cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative.Builder, cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeOrBuilder> 
          getPrerogativesFieldBuilder() {
        if (prerogativesBuilder_ == null) {
          prerogativesBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative, cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative.Builder, cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeOrBuilder>(
                  prerogatives_,
                  ((bitField0_ & 0x00000001) == 0x00000001),
                  getParentForChildren(),
                  isClean());
          prerogatives_ = null;
        }
        return prerogativesBuilder_;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:PrerogativeList)
    }

    // @@protoc_insertion_point(class_scope:PrerogativeList)
    private static final cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList();
    }

    public static cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<PrerogativeList>
        PARSER = new com.google.protobuf.AbstractParser<PrerogativeList>() {
      public PrerogativeList parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new PrerogativeList(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<PrerogativeList> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<PrerogativeList> getParserForType() {
      return PARSER;
    }

    public cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface PrerogativeOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Prerogative)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     ** 特权ID 
     * </pre>
     *
     * <code>int32 id = 1;</code>
     */
    int getId();

    /**
     * <pre>
     ** 到期时间 
     * </pre>
     *
     * <code>int64 expirationTime = 2;</code>
     */
    long getExpirationTime();
  }
  /**
   * <pre>
   ** 特权 
   * </pre>
   *
   * Protobuf type {@code Prerogative}
   */
  public  static final class Prerogative extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Prerogative)
      PrerogativeOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Prerogative.newBuilder() to construct.
    private Prerogative(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Prerogative() {
      id_ = 0;
      expirationTime_ = 0L;
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Prerogative(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {

              id_ = input.readInt32();
              break;
            }
            case 16: {

              expirationTime_ = input.readInt64();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.PrerogativeProtocol.internal_static_Prerogative_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.daxiang.protocol.game.PrerogativeProtocol.internal_static_Prerogative_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative.class, cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative.Builder.class);
    }

    public static final int ID_FIELD_NUMBER = 1;
    private int id_;
    /**
     * <pre>
     ** 特权ID 
     * </pre>
     *
     * <code>int32 id = 1;</code>
     */
    public int getId() {
      return id_;
    }

    public static final int EXPIRATIONTIME_FIELD_NUMBER = 2;
    private long expirationTime_;
    /**
     * <pre>
     ** 到期时间 
     * </pre>
     *
     * <code>int64 expirationTime = 2;</code>
     */
    public long getExpirationTime() {
      return expirationTime_;
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (id_ != 0) {
        output.writeInt32(1, id_);
      }
      if (expirationTime_ != 0L) {
        output.writeInt64(2, expirationTime_);
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (id_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, id_);
      }
      if (expirationTime_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, expirationTime_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative)) {
        return super.equals(obj);
      }
      cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative other = (cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative) obj;

      boolean result = true;
      result = result && (getId()
          == other.getId());
      result = result && (getExpirationTime()
          == other.getExpirationTime());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + ID_FIELD_NUMBER;
      hash = (53 * hash) + getId();
      hash = (37 * hash) + EXPIRATIONTIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getExpirationTime());
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** 特权 
     * </pre>
     *
     * Protobuf type {@code Prerogative}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Prerogative)
        cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.daxiang.protocol.game.PrerogativeProtocol.internal_static_Prerogative_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.daxiang.protocol.game.PrerogativeProtocol.internal_static_Prerogative_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative.class, cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative.Builder.class);
      }

      // Construct using cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        id_ = 0;

        expirationTime_ = 0L;

        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return cn.daxiang.protocol.game.PrerogativeProtocol.internal_static_Prerogative_descriptor;
      }

      public cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative getDefaultInstanceForType() {
        return cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative.getDefaultInstance();
      }

      public cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative build() {
        cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative buildPartial() {
        cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative result = new cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative(this);
        result.id_ = id_;
        result.expirationTime_ = expirationTime_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative) {
          return mergeFrom((cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative other) {
        if (other == cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative.getDefaultInstance()) return this;
        if (other.getId() != 0) {
          setId(other.getId());
        }
        if (other.getExpirationTime() != 0L) {
          setExpirationTime(other.getExpirationTime());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int id_ ;
      /**
       * <pre>
       ** 特权ID 
       * </pre>
       *
       * <code>int32 id = 1;</code>
       */
      public int getId() {
        return id_;
      }
      /**
       * <pre>
       ** 特权ID 
       * </pre>
       *
       * <code>int32 id = 1;</code>
       */
      public Builder setId(int value) {
        
        id_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 特权ID 
       * </pre>
       *
       * <code>int32 id = 1;</code>
       */
      public Builder clearId() {
        
        id_ = 0;
        onChanged();
        return this;
      }

      private long expirationTime_ ;
      /**
       * <pre>
       ** 到期时间 
       * </pre>
       *
       * <code>int64 expirationTime = 2;</code>
       */
      public long getExpirationTime() {
        return expirationTime_;
      }
      /**
       * <pre>
       ** 到期时间 
       * </pre>
       *
       * <code>int64 expirationTime = 2;</code>
       */
      public Builder setExpirationTime(long value) {
        
        expirationTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 到期时间 
       * </pre>
       *
       * <code>int64 expirationTime = 2;</code>
       */
      public Builder clearExpirationTime() {
        
        expirationTime_ = 0L;
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Prerogative)
    }

    // @@protoc_insertion_point(class_scope:Prerogative)
    private static final cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative();
    }

    public static cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<Prerogative>
        PARSER = new com.google.protobuf.AbstractParser<Prerogative>() {
      public Prerogative parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Prerogative(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Prerogative> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Prerogative> getParserForType() {
      return PARSER;
    }

    public cn.daxiang.protocol.game.PrerogativeProtocol.Prerogative getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_PrerogativeInfoResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_PrerogativeInfoResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_PrerogativeInfoResponse_PrerogativeMapEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_PrerogativeInfoResponse_PrerogativeMapEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_PrerogativeList_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_PrerogativeList_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Prerogative_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Prerogative_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\036game/prerogativeProtocol.proto\"\250\001\n\027Pre" +
      "rogativeInfoResponse\022D\n\016prerogativeMap\030\001" +
      " \003(\0132,.PrerogativeInfoResponse.Prerogati" +
      "veMapEntry\032G\n\023PrerogativeMapEntry\022\013\n\003key" +
      "\030\001 \001(\005\022\037\n\005value\030\002 \001(\0132\020.PrerogativeList:" +
      "\0028\001\"5\n\017PrerogativeList\022\"\n\014prerogatives\030\001" +
      " \003(\0132\014.Prerogative\"1\n\013Prerogative\022\n\n\002id\030" +
      "\001 \001(\005\022\026\n\016expirationTime\030\002 \001(\003*X\n\016Preroga" +
      "tiveCmd\022\021\n\rUSER_CMD_NONE\020\000\022\030\n\024GET_PREROG" +
      "ATIVE_LIST\020\001\022\031\n\025PUSH_PREROGATIVE_LIST\020dB" +
      "\034\n\030cn.daxiang.protocol.gameH\001b\006proto3"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
        new com.google.protobuf.Descriptors.FileDescriptor.    InternalDescriptorAssigner() {
          public com.google.protobuf.ExtensionRegistry assignDescriptors(
              com.google.protobuf.Descriptors.FileDescriptor root) {
            descriptor = root;
            return null;
          }
        };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        }, assigner);
    internal_static_PrerogativeInfoResponse_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_PrerogativeInfoResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_PrerogativeInfoResponse_descriptor,
        new java.lang.String[] { "PrerogativeMap", });
    internal_static_PrerogativeInfoResponse_PrerogativeMapEntry_descriptor =
      internal_static_PrerogativeInfoResponse_descriptor.getNestedTypes().get(0);
    internal_static_PrerogativeInfoResponse_PrerogativeMapEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_PrerogativeInfoResponse_PrerogativeMapEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_PrerogativeList_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_PrerogativeList_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_PrerogativeList_descriptor,
        new java.lang.String[] { "Prerogatives", });
    internal_static_Prerogative_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_Prerogative_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Prerogative_descriptor,
        new java.lang.String[] { "Id", "ExpirationTime", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
