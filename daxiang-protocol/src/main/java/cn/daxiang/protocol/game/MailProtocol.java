// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: game/mailProtocol.proto

package cn.daxiang.protocol.game;

public final class MailProtocol {
  private MailProtocol() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  /**
   * Protobuf enum {@code MailCmd}
   */
  public enum MailCmd
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>MAIL_CMD_NONE = 0;</code>
     */
    MAIL_CMD_NONE(0),
    /**
     * <pre>
     **
     * 获取邮件列表
     * 请求:{&#64;code Request}
     * 响应:{&#64;code MailListResponse}
     * </pre>
     *
     * <code>GET_MAIL_LIST = 1;</code>
     */
    GET_MAIL_LIST(1),
    /**
     * <pre>
     **
     * 邮件领取(有附件奖励)
     * 请求:{&#64;code LongPacket} 邮件ID:mailId
     * 响应:{&#64;code RewardResultResponse}
     * 推送:{&#64;code MailListResponse}
     * </pre>
     *
     * <code>MAIL_RECEIVE = 2;</code>
     */
    MAIL_RECEIVE(2),
    /**
     * <pre>
     **
     * 邮件一键领取(有附件奖励)
     * 请求:{&#64;code Request}
     * 响应:{&#64;code RewardResultResponse}
     * 推送:{&#64;code MailListResponse}
     * </pre>
     *
     * <code>MAIL_QUICKLY_RECEIVE = 3;</code>
     */
    MAIL_QUICKLY_RECEIVE(3),
    /**
     * <pre>
     **
     * 邮件读取(没有附件奖励)
     * 请求:{&#64;code LongPacket} 邮件ID:mailId
     * 响应:{&#64;code Response}
     * 推送:{&#64;code MailListResponse}
     * </pre>
     *
     * <code>MAIL_READ = 4;</code>
     */
    MAIL_READ(4),
    /**
     * <pre>
     **
     * 邮件一键删除(只会删除已阅读且无附件的邮件)
     * 请求:{&#64;code Request}
     * 响应:{&#64;code Response}
     * 推送:{&#64;code MailListResponse}
     * </pre>
     *
     * <code>MAIL_QUICKLY_DELETE = 5;</code>
     */
    MAIL_QUICKLY_DELETE(5),
    /**
     * <pre>
     **
     * 推送邮件列表
     * 推送:{&#64;code MailListResponse}
     * </pre>
     *
     * <code>PUSH_MAIL_LIST = 100;</code>
     */
    PUSH_MAIL_LIST(100),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>MAIL_CMD_NONE = 0;</code>
     */
    public static final int MAIL_CMD_NONE_VALUE = 0;
    /**
     * <pre>
     **
     * 获取邮件列表
     * 请求:{&#64;code Request}
     * 响应:{&#64;code MailListResponse}
     * </pre>
     *
     * <code>GET_MAIL_LIST = 1;</code>
     */
    public static final int GET_MAIL_LIST_VALUE = 1;
    /**
     * <pre>
     **
     * 邮件领取(有附件奖励)
     * 请求:{&#64;code LongPacket} 邮件ID:mailId
     * 响应:{&#64;code RewardResultResponse}
     * 推送:{&#64;code MailListResponse}
     * </pre>
     *
     * <code>MAIL_RECEIVE = 2;</code>
     */
    public static final int MAIL_RECEIVE_VALUE = 2;
    /**
     * <pre>
     **
     * 邮件一键领取(有附件奖励)
     * 请求:{&#64;code Request}
     * 响应:{&#64;code RewardResultResponse}
     * 推送:{&#64;code MailListResponse}
     * </pre>
     *
     * <code>MAIL_QUICKLY_RECEIVE = 3;</code>
     */
    public static final int MAIL_QUICKLY_RECEIVE_VALUE = 3;
    /**
     * <pre>
     **
     * 邮件读取(没有附件奖励)
     * 请求:{&#64;code LongPacket} 邮件ID:mailId
     * 响应:{&#64;code Response}
     * 推送:{&#64;code MailListResponse}
     * </pre>
     *
     * <code>MAIL_READ = 4;</code>
     */
    public static final int MAIL_READ_VALUE = 4;
    /**
     * <pre>
     **
     * 邮件一键删除(只会删除已阅读且无附件的邮件)
     * 请求:{&#64;code Request}
     * 响应:{&#64;code Response}
     * 推送:{&#64;code MailListResponse}
     * </pre>
     *
     * <code>MAIL_QUICKLY_DELETE = 5;</code>
     */
    public static final int MAIL_QUICKLY_DELETE_VALUE = 5;
    /**
     * <pre>
     **
     * 推送邮件列表
     * 推送:{&#64;code MailListResponse}
     * </pre>
     *
     * <code>PUSH_MAIL_LIST = 100;</code>
     */
    public static final int PUSH_MAIL_LIST_VALUE = 100;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static MailCmd valueOf(int value) {
      return forNumber(value);
    }

    public static MailCmd forNumber(int value) {
      switch (value) {
        case 0: return MAIL_CMD_NONE;
        case 1: return GET_MAIL_LIST;
        case 2: return MAIL_RECEIVE;
        case 3: return MAIL_QUICKLY_RECEIVE;
        case 4: return MAIL_READ;
        case 5: return MAIL_QUICKLY_DELETE;
        case 100: return PUSH_MAIL_LIST;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<MailCmd>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        MailCmd> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<MailCmd>() {
            public MailCmd findValueByNumber(int number) {
              return MailCmd.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.MailProtocol.getDescriptor().getEnumTypes().get(0);
    }

    private static final MailCmd[] VALUES = values();

    public static MailCmd valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private MailCmd(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:MailCmd)
  }

  public interface MailOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Mail)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     ** 邮件ID 
     * </pre>
     *
     * <code>int64 mailId = 1;</code>
     */
    long getMailId();

    /**
     * <pre>
     ** 发件人 
     * </pre>
     *
     * <code>string from = 2;</code>
     */
    java.lang.String getFrom();
    /**
     * <pre>
     ** 发件人 
     * </pre>
     *
     * <code>string from = 2;</code>
     */
    com.google.protobuf.ByteString
        getFromBytes();

    /**
     * <pre>
     ** 标题 
     * </pre>
     *
     * <code>string title = 3;</code>
     */
    java.lang.String getTitle();
    /**
     * <pre>
     ** 标题 
     * </pre>
     *
     * <code>string title = 3;</code>
     */
    com.google.protobuf.ByteString
        getTitleBytes();

    /**
     * <pre>
     ** 邮件内容 
     * </pre>
     *
     * <code>string content = 4;</code>
     */
    java.lang.String getContent();
    /**
     * <pre>
     ** 邮件内容 
     * </pre>
     *
     * <code>string content = 4;</code>
     */
    com.google.protobuf.ByteString
        getContentBytes();

    /**
     * <pre>
     ** 附件列表 
     * </pre>
     *
     * <code>repeated .RewardObject attachments = 5;</code>
     */
    java.util.List<cn.daxiang.protocol.game.CommonProtocol.RewardObject> 
        getAttachmentsList();
    /**
     * <pre>
     ** 附件列表 
     * </pre>
     *
     * <code>repeated .RewardObject attachments = 5;</code>
     */
    cn.daxiang.protocol.game.CommonProtocol.RewardObject getAttachments(int index);
    /**
     * <pre>
     ** 附件列表 
     * </pre>
     *
     * <code>repeated .RewardObject attachments = 5;</code>
     */
    int getAttachmentsCount();
    /**
     * <pre>
     ** 附件列表 
     * </pre>
     *
     * <code>repeated .RewardObject attachments = 5;</code>
     */
    java.util.List<? extends cn.daxiang.protocol.game.CommonProtocol.RewardObjectOrBuilder> 
        getAttachmentsOrBuilderList();
    /**
     * <pre>
     ** 附件列表 
     * </pre>
     *
     * <code>repeated .RewardObject attachments = 5;</code>
     */
    cn.daxiang.protocol.game.CommonProtocol.RewardObjectOrBuilder getAttachmentsOrBuilder(
        int index);

    /**
     * <pre>
     ** 发送时间 
     * </pre>
     *
     * <code>int64 sendTime = 6;</code>
     */
    long getSendTime();

    /**
     * <pre>
     ** 邮件状态 
     * </pre>
     *
     * <code>.MailStateType state = 7;</code>
     */
    int getStateValue();
    /**
     * <pre>
     ** 邮件状态 
     * </pre>
     *
     * <code>.MailStateType state = 7;</code>
     */
    cn.daxiang.protocol.game.TypeProtocol.MailStateType getState();

    /**
     * <pre>
     ** 过期时间 
     * </pre>
     *
     * <code>int64 expirationTime = 8;</code>
     */
    long getExpirationTime();
  }
  /**
   * <pre>
   ** 邮件内容 
   * </pre>
   *
   * Protobuf type {@code Mail}
   */
  public  static final class Mail extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Mail)
      MailOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Mail.newBuilder() to construct.
    private Mail(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Mail() {
      mailId_ = 0L;
      from_ = "";
      title_ = "";
      content_ = "";
      attachments_ = java.util.Collections.emptyList();
      sendTime_ = 0L;
      state_ = 0;
      expirationTime_ = 0L;
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Mail(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {

              mailId_ = input.readInt64();
              break;
            }
            case 18: {
              java.lang.String s = input.readStringRequireUtf8();

              from_ = s;
              break;
            }
            case 26: {
              java.lang.String s = input.readStringRequireUtf8();

              title_ = s;
              break;
            }
            case 34: {
              java.lang.String s = input.readStringRequireUtf8();

              content_ = s;
              break;
            }
            case 42: {
              if (!((mutable_bitField0_ & 0x00000010) == 0x00000010)) {
                attachments_ = new java.util.ArrayList<cn.daxiang.protocol.game.CommonProtocol.RewardObject>();
                mutable_bitField0_ |= 0x00000010;
              }
              attachments_.add(
                  input.readMessage(cn.daxiang.protocol.game.CommonProtocol.RewardObject.parser(), extensionRegistry));
              break;
            }
            case 48: {

              sendTime_ = input.readInt64();
              break;
            }
            case 56: {
              int rawValue = input.readEnum();

              state_ = rawValue;
              break;
            }
            case 64: {

              expirationTime_ = input.readInt64();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000010) == 0x00000010)) {
          attachments_ = java.util.Collections.unmodifiableList(attachments_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.MailProtocol.internal_static_Mail_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.daxiang.protocol.game.MailProtocol.internal_static_Mail_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.daxiang.protocol.game.MailProtocol.Mail.class, cn.daxiang.protocol.game.MailProtocol.Mail.Builder.class);
    }

    private int bitField0_;
    public static final int MAILID_FIELD_NUMBER = 1;
    private long mailId_;
    /**
     * <pre>
     ** 邮件ID 
     * </pre>
     *
     * <code>int64 mailId = 1;</code>
     */
    public long getMailId() {
      return mailId_;
    }

    public static final int FROM_FIELD_NUMBER = 2;
    private volatile java.lang.Object from_;
    /**
     * <pre>
     ** 发件人 
     * </pre>
     *
     * <code>string from = 2;</code>
     */
    public java.lang.String getFrom() {
      java.lang.Object ref = from_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        from_ = s;
        return s;
      }
    }
    /**
     * <pre>
     ** 发件人 
     * </pre>
     *
     * <code>string from = 2;</code>
     */
    public com.google.protobuf.ByteString
        getFromBytes() {
      java.lang.Object ref = from_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        from_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int TITLE_FIELD_NUMBER = 3;
    private volatile java.lang.Object title_;
    /**
     * <pre>
     ** 标题 
     * </pre>
     *
     * <code>string title = 3;</code>
     */
    public java.lang.String getTitle() {
      java.lang.Object ref = title_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        title_ = s;
        return s;
      }
    }
    /**
     * <pre>
     ** 标题 
     * </pre>
     *
     * <code>string title = 3;</code>
     */
    public com.google.protobuf.ByteString
        getTitleBytes() {
      java.lang.Object ref = title_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        title_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CONTENT_FIELD_NUMBER = 4;
    private volatile java.lang.Object content_;
    /**
     * <pre>
     ** 邮件内容 
     * </pre>
     *
     * <code>string content = 4;</code>
     */
    public java.lang.String getContent() {
      java.lang.Object ref = content_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        content_ = s;
        return s;
      }
    }
    /**
     * <pre>
     ** 邮件内容 
     * </pre>
     *
     * <code>string content = 4;</code>
     */
    public com.google.protobuf.ByteString
        getContentBytes() {
      java.lang.Object ref = content_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        content_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int ATTACHMENTS_FIELD_NUMBER = 5;
    private java.util.List<cn.daxiang.protocol.game.CommonProtocol.RewardObject> attachments_;
    /**
     * <pre>
     ** 附件列表 
     * </pre>
     *
     * <code>repeated .RewardObject attachments = 5;</code>
     */
    public java.util.List<cn.daxiang.protocol.game.CommonProtocol.RewardObject> getAttachmentsList() {
      return attachments_;
    }
    /**
     * <pre>
     ** 附件列表 
     * </pre>
     *
     * <code>repeated .RewardObject attachments = 5;</code>
     */
    public java.util.List<? extends cn.daxiang.protocol.game.CommonProtocol.RewardObjectOrBuilder> 
        getAttachmentsOrBuilderList() {
      return attachments_;
    }
    /**
     * <pre>
     ** 附件列表 
     * </pre>
     *
     * <code>repeated .RewardObject attachments = 5;</code>
     */
    public int getAttachmentsCount() {
      return attachments_.size();
    }
    /**
     * <pre>
     ** 附件列表 
     * </pre>
     *
     * <code>repeated .RewardObject attachments = 5;</code>
     */
    public cn.daxiang.protocol.game.CommonProtocol.RewardObject getAttachments(int index) {
      return attachments_.get(index);
    }
    /**
     * <pre>
     ** 附件列表 
     * </pre>
     *
     * <code>repeated .RewardObject attachments = 5;</code>
     */
    public cn.daxiang.protocol.game.CommonProtocol.RewardObjectOrBuilder getAttachmentsOrBuilder(
        int index) {
      return attachments_.get(index);
    }

    public static final int SENDTIME_FIELD_NUMBER = 6;
    private long sendTime_;
    /**
     * <pre>
     ** 发送时间 
     * </pre>
     *
     * <code>int64 sendTime = 6;</code>
     */
    public long getSendTime() {
      return sendTime_;
    }

    public static final int STATE_FIELD_NUMBER = 7;
    private int state_;
    /**
     * <pre>
     ** 邮件状态 
     * </pre>
     *
     * <code>.MailStateType state = 7;</code>
     */
    public int getStateValue() {
      return state_;
    }
    /**
     * <pre>
     ** 邮件状态 
     * </pre>
     *
     * <code>.MailStateType state = 7;</code>
     */
    public cn.daxiang.protocol.game.TypeProtocol.MailStateType getState() {
      cn.daxiang.protocol.game.TypeProtocol.MailStateType result = cn.daxiang.protocol.game.TypeProtocol.MailStateType.valueOf(state_);
      return result == null ? cn.daxiang.protocol.game.TypeProtocol.MailStateType.UNRECOGNIZED : result;
    }

    public static final int EXPIRATIONTIME_FIELD_NUMBER = 8;
    private long expirationTime_;
    /**
     * <pre>
     ** 过期时间 
     * </pre>
     *
     * <code>int64 expirationTime = 8;</code>
     */
    public long getExpirationTime() {
      return expirationTime_;
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (mailId_ != 0L) {
        output.writeInt64(1, mailId_);
      }
      if (!getFromBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, from_);
      }
      if (!getTitleBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, title_);
      }
      if (!getContentBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 4, content_);
      }
      for (int i = 0; i < attachments_.size(); i++) {
        output.writeMessage(5, attachments_.get(i));
      }
      if (sendTime_ != 0L) {
        output.writeInt64(6, sendTime_);
      }
      if (state_ != cn.daxiang.protocol.game.TypeProtocol.MailStateType.MAIL_STATE_TYPE_NONE.getNumber()) {
        output.writeEnum(7, state_);
      }
      if (expirationTime_ != 0L) {
        output.writeInt64(8, expirationTime_);
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (mailId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, mailId_);
      }
      if (!getFromBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, from_);
      }
      if (!getTitleBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, title_);
      }
      if (!getContentBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, content_);
      }
      for (int i = 0; i < attachments_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(5, attachments_.get(i));
      }
      if (sendTime_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(6, sendTime_);
      }
      if (state_ != cn.daxiang.protocol.game.TypeProtocol.MailStateType.MAIL_STATE_TYPE_NONE.getNumber()) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(7, state_);
      }
      if (expirationTime_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(8, expirationTime_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof cn.daxiang.protocol.game.MailProtocol.Mail)) {
        return super.equals(obj);
      }
      cn.daxiang.protocol.game.MailProtocol.Mail other = (cn.daxiang.protocol.game.MailProtocol.Mail) obj;

      boolean result = true;
      result = result && (getMailId()
          == other.getMailId());
      result = result && getFrom()
          .equals(other.getFrom());
      result = result && getTitle()
          .equals(other.getTitle());
      result = result && getContent()
          .equals(other.getContent());
      result = result && getAttachmentsList()
          .equals(other.getAttachmentsList());
      result = result && (getSendTime()
          == other.getSendTime());
      result = result && state_ == other.state_;
      result = result && (getExpirationTime()
          == other.getExpirationTime());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + MAILID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getMailId());
      hash = (37 * hash) + FROM_FIELD_NUMBER;
      hash = (53 * hash) + getFrom().hashCode();
      hash = (37 * hash) + TITLE_FIELD_NUMBER;
      hash = (53 * hash) + getTitle().hashCode();
      hash = (37 * hash) + CONTENT_FIELD_NUMBER;
      hash = (53 * hash) + getContent().hashCode();
      if (getAttachmentsCount() > 0) {
        hash = (37 * hash) + ATTACHMENTS_FIELD_NUMBER;
        hash = (53 * hash) + getAttachmentsList().hashCode();
      }
      hash = (37 * hash) + SENDTIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getSendTime());
      hash = (37 * hash) + STATE_FIELD_NUMBER;
      hash = (53 * hash) + state_;
      hash = (37 * hash) + EXPIRATIONTIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getExpirationTime());
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static cn.daxiang.protocol.game.MailProtocol.Mail parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.MailProtocol.Mail parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.MailProtocol.Mail parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.MailProtocol.Mail parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.MailProtocol.Mail parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.MailProtocol.Mail parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.MailProtocol.Mail parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.MailProtocol.Mail parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.MailProtocol.Mail parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.MailProtocol.Mail parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.MailProtocol.Mail parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.MailProtocol.Mail parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(cn.daxiang.protocol.game.MailProtocol.Mail prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** 邮件内容 
     * </pre>
     *
     * Protobuf type {@code Mail}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Mail)
        cn.daxiang.protocol.game.MailProtocol.MailOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.daxiang.protocol.game.MailProtocol.internal_static_Mail_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.daxiang.protocol.game.MailProtocol.internal_static_Mail_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.daxiang.protocol.game.MailProtocol.Mail.class, cn.daxiang.protocol.game.MailProtocol.Mail.Builder.class);
      }

      // Construct using cn.daxiang.protocol.game.MailProtocol.Mail.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getAttachmentsFieldBuilder();
        }
      }
      public Builder clear() {
        super.clear();
        mailId_ = 0L;

        from_ = "";

        title_ = "";

        content_ = "";

        if (attachmentsBuilder_ == null) {
          attachments_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000010);
        } else {
          attachmentsBuilder_.clear();
        }
        sendTime_ = 0L;

        state_ = 0;

        expirationTime_ = 0L;

        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return cn.daxiang.protocol.game.MailProtocol.internal_static_Mail_descriptor;
      }

      public cn.daxiang.protocol.game.MailProtocol.Mail getDefaultInstanceForType() {
        return cn.daxiang.protocol.game.MailProtocol.Mail.getDefaultInstance();
      }

      public cn.daxiang.protocol.game.MailProtocol.Mail build() {
        cn.daxiang.protocol.game.MailProtocol.Mail result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public cn.daxiang.protocol.game.MailProtocol.Mail buildPartial() {
        cn.daxiang.protocol.game.MailProtocol.Mail result = new cn.daxiang.protocol.game.MailProtocol.Mail(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        result.mailId_ = mailId_;
        result.from_ = from_;
        result.title_ = title_;
        result.content_ = content_;
        if (attachmentsBuilder_ == null) {
          if (((bitField0_ & 0x00000010) == 0x00000010)) {
            attachments_ = java.util.Collections.unmodifiableList(attachments_);
            bitField0_ = (bitField0_ & ~0x00000010);
          }
          result.attachments_ = attachments_;
        } else {
          result.attachments_ = attachmentsBuilder_.build();
        }
        result.sendTime_ = sendTime_;
        result.state_ = state_;
        result.expirationTime_ = expirationTime_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof cn.daxiang.protocol.game.MailProtocol.Mail) {
          return mergeFrom((cn.daxiang.protocol.game.MailProtocol.Mail)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(cn.daxiang.protocol.game.MailProtocol.Mail other) {
        if (other == cn.daxiang.protocol.game.MailProtocol.Mail.getDefaultInstance()) return this;
        if (other.getMailId() != 0L) {
          setMailId(other.getMailId());
        }
        if (!other.getFrom().isEmpty()) {
          from_ = other.from_;
          onChanged();
        }
        if (!other.getTitle().isEmpty()) {
          title_ = other.title_;
          onChanged();
        }
        if (!other.getContent().isEmpty()) {
          content_ = other.content_;
          onChanged();
        }
        if (attachmentsBuilder_ == null) {
          if (!other.attachments_.isEmpty()) {
            if (attachments_.isEmpty()) {
              attachments_ = other.attachments_;
              bitField0_ = (bitField0_ & ~0x00000010);
            } else {
              ensureAttachmentsIsMutable();
              attachments_.addAll(other.attachments_);
            }
            onChanged();
          }
        } else {
          if (!other.attachments_.isEmpty()) {
            if (attachmentsBuilder_.isEmpty()) {
              attachmentsBuilder_.dispose();
              attachmentsBuilder_ = null;
              attachments_ = other.attachments_;
              bitField0_ = (bitField0_ & ~0x00000010);
              attachmentsBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getAttachmentsFieldBuilder() : null;
            } else {
              attachmentsBuilder_.addAllMessages(other.attachments_);
            }
          }
        }
        if (other.getSendTime() != 0L) {
          setSendTime(other.getSendTime());
        }
        if (other.state_ != 0) {
          setStateValue(other.getStateValue());
        }
        if (other.getExpirationTime() != 0L) {
          setExpirationTime(other.getExpirationTime());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        cn.daxiang.protocol.game.MailProtocol.Mail parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (cn.daxiang.protocol.game.MailProtocol.Mail) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long mailId_ ;
      /**
       * <pre>
       ** 邮件ID 
       * </pre>
       *
       * <code>int64 mailId = 1;</code>
       */
      public long getMailId() {
        return mailId_;
      }
      /**
       * <pre>
       ** 邮件ID 
       * </pre>
       *
       * <code>int64 mailId = 1;</code>
       */
      public Builder setMailId(long value) {
        
        mailId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 邮件ID 
       * </pre>
       *
       * <code>int64 mailId = 1;</code>
       */
      public Builder clearMailId() {
        
        mailId_ = 0L;
        onChanged();
        return this;
      }

      private java.lang.Object from_ = "";
      /**
       * <pre>
       ** 发件人 
       * </pre>
       *
       * <code>string from = 2;</code>
       */
      public java.lang.String getFrom() {
        java.lang.Object ref = from_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          from_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       ** 发件人 
       * </pre>
       *
       * <code>string from = 2;</code>
       */
      public com.google.protobuf.ByteString
          getFromBytes() {
        java.lang.Object ref = from_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          from_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       ** 发件人 
       * </pre>
       *
       * <code>string from = 2;</code>
       */
      public Builder setFrom(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        from_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 发件人 
       * </pre>
       *
       * <code>string from = 2;</code>
       */
      public Builder clearFrom() {
        
        from_ = getDefaultInstance().getFrom();
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 发件人 
       * </pre>
       *
       * <code>string from = 2;</code>
       */
      public Builder setFromBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        from_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object title_ = "";
      /**
       * <pre>
       ** 标题 
       * </pre>
       *
       * <code>string title = 3;</code>
       */
      public java.lang.String getTitle() {
        java.lang.Object ref = title_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          title_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       ** 标题 
       * </pre>
       *
       * <code>string title = 3;</code>
       */
      public com.google.protobuf.ByteString
          getTitleBytes() {
        java.lang.Object ref = title_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          title_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       ** 标题 
       * </pre>
       *
       * <code>string title = 3;</code>
       */
      public Builder setTitle(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        title_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 标题 
       * </pre>
       *
       * <code>string title = 3;</code>
       */
      public Builder clearTitle() {
        
        title_ = getDefaultInstance().getTitle();
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 标题 
       * </pre>
       *
       * <code>string title = 3;</code>
       */
      public Builder setTitleBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        title_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object content_ = "";
      /**
       * <pre>
       ** 邮件内容 
       * </pre>
       *
       * <code>string content = 4;</code>
       */
      public java.lang.String getContent() {
        java.lang.Object ref = content_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          content_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       ** 邮件内容 
       * </pre>
       *
       * <code>string content = 4;</code>
       */
      public com.google.protobuf.ByteString
          getContentBytes() {
        java.lang.Object ref = content_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          content_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       ** 邮件内容 
       * </pre>
       *
       * <code>string content = 4;</code>
       */
      public Builder setContent(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        content_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 邮件内容 
       * </pre>
       *
       * <code>string content = 4;</code>
       */
      public Builder clearContent() {
        
        content_ = getDefaultInstance().getContent();
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 邮件内容 
       * </pre>
       *
       * <code>string content = 4;</code>
       */
      public Builder setContentBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        content_ = value;
        onChanged();
        return this;
      }

      private java.util.List<cn.daxiang.protocol.game.CommonProtocol.RewardObject> attachments_ =
        java.util.Collections.emptyList();
      private void ensureAttachmentsIsMutable() {
        if (!((bitField0_ & 0x00000010) == 0x00000010)) {
          attachments_ = new java.util.ArrayList<cn.daxiang.protocol.game.CommonProtocol.RewardObject>(attachments_);
          bitField0_ |= 0x00000010;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          cn.daxiang.protocol.game.CommonProtocol.RewardObject, cn.daxiang.protocol.game.CommonProtocol.RewardObject.Builder, cn.daxiang.protocol.game.CommonProtocol.RewardObjectOrBuilder> attachmentsBuilder_;

      /**
       * <pre>
       ** 附件列表 
       * </pre>
       *
       * <code>repeated .RewardObject attachments = 5;</code>
       */
      public java.util.List<cn.daxiang.protocol.game.CommonProtocol.RewardObject> getAttachmentsList() {
        if (attachmentsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(attachments_);
        } else {
          return attachmentsBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       ** 附件列表 
       * </pre>
       *
       * <code>repeated .RewardObject attachments = 5;</code>
       */
      public int getAttachmentsCount() {
        if (attachmentsBuilder_ == null) {
          return attachments_.size();
        } else {
          return attachmentsBuilder_.getCount();
        }
      }
      /**
       * <pre>
       ** 附件列表 
       * </pre>
       *
       * <code>repeated .RewardObject attachments = 5;</code>
       */
      public cn.daxiang.protocol.game.CommonProtocol.RewardObject getAttachments(int index) {
        if (attachmentsBuilder_ == null) {
          return attachments_.get(index);
        } else {
          return attachmentsBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       ** 附件列表 
       * </pre>
       *
       * <code>repeated .RewardObject attachments = 5;</code>
       */
      public Builder setAttachments(
          int index, cn.daxiang.protocol.game.CommonProtocol.RewardObject value) {
        if (attachmentsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureAttachmentsIsMutable();
          attachments_.set(index, value);
          onChanged();
        } else {
          attachmentsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       ** 附件列表 
       * </pre>
       *
       * <code>repeated .RewardObject attachments = 5;</code>
       */
      public Builder setAttachments(
          int index, cn.daxiang.protocol.game.CommonProtocol.RewardObject.Builder builderForValue) {
        if (attachmentsBuilder_ == null) {
          ensureAttachmentsIsMutable();
          attachments_.set(index, builderForValue.build());
          onChanged();
        } else {
          attachmentsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       ** 附件列表 
       * </pre>
       *
       * <code>repeated .RewardObject attachments = 5;</code>
       */
      public Builder addAttachments(cn.daxiang.protocol.game.CommonProtocol.RewardObject value) {
        if (attachmentsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureAttachmentsIsMutable();
          attachments_.add(value);
          onChanged();
        } else {
          attachmentsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       ** 附件列表 
       * </pre>
       *
       * <code>repeated .RewardObject attachments = 5;</code>
       */
      public Builder addAttachments(
          int index, cn.daxiang.protocol.game.CommonProtocol.RewardObject value) {
        if (attachmentsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureAttachmentsIsMutable();
          attachments_.add(index, value);
          onChanged();
        } else {
          attachmentsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       ** 附件列表 
       * </pre>
       *
       * <code>repeated .RewardObject attachments = 5;</code>
       */
      public Builder addAttachments(
          cn.daxiang.protocol.game.CommonProtocol.RewardObject.Builder builderForValue) {
        if (attachmentsBuilder_ == null) {
          ensureAttachmentsIsMutable();
          attachments_.add(builderForValue.build());
          onChanged();
        } else {
          attachmentsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       ** 附件列表 
       * </pre>
       *
       * <code>repeated .RewardObject attachments = 5;</code>
       */
      public Builder addAttachments(
          int index, cn.daxiang.protocol.game.CommonProtocol.RewardObject.Builder builderForValue) {
        if (attachmentsBuilder_ == null) {
          ensureAttachmentsIsMutable();
          attachments_.add(index, builderForValue.build());
          onChanged();
        } else {
          attachmentsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       ** 附件列表 
       * </pre>
       *
       * <code>repeated .RewardObject attachments = 5;</code>
       */
      public Builder addAllAttachments(
          java.lang.Iterable<? extends cn.daxiang.protocol.game.CommonProtocol.RewardObject> values) {
        if (attachmentsBuilder_ == null) {
          ensureAttachmentsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, attachments_);
          onChanged();
        } else {
          attachmentsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       ** 附件列表 
       * </pre>
       *
       * <code>repeated .RewardObject attachments = 5;</code>
       */
      public Builder clearAttachments() {
        if (attachmentsBuilder_ == null) {
          attachments_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000010);
          onChanged();
        } else {
          attachmentsBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       ** 附件列表 
       * </pre>
       *
       * <code>repeated .RewardObject attachments = 5;</code>
       */
      public Builder removeAttachments(int index) {
        if (attachmentsBuilder_ == null) {
          ensureAttachmentsIsMutable();
          attachments_.remove(index);
          onChanged();
        } else {
          attachmentsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       ** 附件列表 
       * </pre>
       *
       * <code>repeated .RewardObject attachments = 5;</code>
       */
      public cn.daxiang.protocol.game.CommonProtocol.RewardObject.Builder getAttachmentsBuilder(
          int index) {
        return getAttachmentsFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       ** 附件列表 
       * </pre>
       *
       * <code>repeated .RewardObject attachments = 5;</code>
       */
      public cn.daxiang.protocol.game.CommonProtocol.RewardObjectOrBuilder getAttachmentsOrBuilder(
          int index) {
        if (attachmentsBuilder_ == null) {
          return attachments_.get(index);  } else {
          return attachmentsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       ** 附件列表 
       * </pre>
       *
       * <code>repeated .RewardObject attachments = 5;</code>
       */
      public java.util.List<? extends cn.daxiang.protocol.game.CommonProtocol.RewardObjectOrBuilder> 
           getAttachmentsOrBuilderList() {
        if (attachmentsBuilder_ != null) {
          return attachmentsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(attachments_);
        }
      }
      /**
       * <pre>
       ** 附件列表 
       * </pre>
       *
       * <code>repeated .RewardObject attachments = 5;</code>
       */
      public cn.daxiang.protocol.game.CommonProtocol.RewardObject.Builder addAttachmentsBuilder() {
        return getAttachmentsFieldBuilder().addBuilder(
            cn.daxiang.protocol.game.CommonProtocol.RewardObject.getDefaultInstance());
      }
      /**
       * <pre>
       ** 附件列表 
       * </pre>
       *
       * <code>repeated .RewardObject attachments = 5;</code>
       */
      public cn.daxiang.protocol.game.CommonProtocol.RewardObject.Builder addAttachmentsBuilder(
          int index) {
        return getAttachmentsFieldBuilder().addBuilder(
            index, cn.daxiang.protocol.game.CommonProtocol.RewardObject.getDefaultInstance());
      }
      /**
       * <pre>
       ** 附件列表 
       * </pre>
       *
       * <code>repeated .RewardObject attachments = 5;</code>
       */
      public java.util.List<cn.daxiang.protocol.game.CommonProtocol.RewardObject.Builder> 
           getAttachmentsBuilderList() {
        return getAttachmentsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          cn.daxiang.protocol.game.CommonProtocol.RewardObject, cn.daxiang.protocol.game.CommonProtocol.RewardObject.Builder, cn.daxiang.protocol.game.CommonProtocol.RewardObjectOrBuilder> 
          getAttachmentsFieldBuilder() {
        if (attachmentsBuilder_ == null) {
          attachmentsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              cn.daxiang.protocol.game.CommonProtocol.RewardObject, cn.daxiang.protocol.game.CommonProtocol.RewardObject.Builder, cn.daxiang.protocol.game.CommonProtocol.RewardObjectOrBuilder>(
                  attachments_,
                  ((bitField0_ & 0x00000010) == 0x00000010),
                  getParentForChildren(),
                  isClean());
          attachments_ = null;
        }
        return attachmentsBuilder_;
      }

      private long sendTime_ ;
      /**
       * <pre>
       ** 发送时间 
       * </pre>
       *
       * <code>int64 sendTime = 6;</code>
       */
      public long getSendTime() {
        return sendTime_;
      }
      /**
       * <pre>
       ** 发送时间 
       * </pre>
       *
       * <code>int64 sendTime = 6;</code>
       */
      public Builder setSendTime(long value) {
        
        sendTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 发送时间 
       * </pre>
       *
       * <code>int64 sendTime = 6;</code>
       */
      public Builder clearSendTime() {
        
        sendTime_ = 0L;
        onChanged();
        return this;
      }

      private int state_ = 0;
      /**
       * <pre>
       ** 邮件状态 
       * </pre>
       *
       * <code>.MailStateType state = 7;</code>
       */
      public int getStateValue() {
        return state_;
      }
      /**
       * <pre>
       ** 邮件状态 
       * </pre>
       *
       * <code>.MailStateType state = 7;</code>
       */
      public Builder setStateValue(int value) {
        state_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 邮件状态 
       * </pre>
       *
       * <code>.MailStateType state = 7;</code>
       */
      public cn.daxiang.protocol.game.TypeProtocol.MailStateType getState() {
        cn.daxiang.protocol.game.TypeProtocol.MailStateType result = cn.daxiang.protocol.game.TypeProtocol.MailStateType.valueOf(state_);
        return result == null ? cn.daxiang.protocol.game.TypeProtocol.MailStateType.UNRECOGNIZED : result;
      }
      /**
       * <pre>
       ** 邮件状态 
       * </pre>
       *
       * <code>.MailStateType state = 7;</code>
       */
      public Builder setState(cn.daxiang.protocol.game.TypeProtocol.MailStateType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        
        state_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 邮件状态 
       * </pre>
       *
       * <code>.MailStateType state = 7;</code>
       */
      public Builder clearState() {
        
        state_ = 0;
        onChanged();
        return this;
      }

      private long expirationTime_ ;
      /**
       * <pre>
       ** 过期时间 
       * </pre>
       *
       * <code>int64 expirationTime = 8;</code>
       */
      public long getExpirationTime() {
        return expirationTime_;
      }
      /**
       * <pre>
       ** 过期时间 
       * </pre>
       *
       * <code>int64 expirationTime = 8;</code>
       */
      public Builder setExpirationTime(long value) {
        
        expirationTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 过期时间 
       * </pre>
       *
       * <code>int64 expirationTime = 8;</code>
       */
      public Builder clearExpirationTime() {
        
        expirationTime_ = 0L;
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Mail)
    }

    // @@protoc_insertion_point(class_scope:Mail)
    private static final cn.daxiang.protocol.game.MailProtocol.Mail DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new cn.daxiang.protocol.game.MailProtocol.Mail();
    }

    public static cn.daxiang.protocol.game.MailProtocol.Mail getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<Mail>
        PARSER = new com.google.protobuf.AbstractParser<Mail>() {
      public Mail parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Mail(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Mail> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Mail> getParserForType() {
      return PARSER;
    }

    public cn.daxiang.protocol.game.MailProtocol.Mail getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface MailListResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:MailListResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .Mail mails = 1;</code>
     */
    java.util.List<cn.daxiang.protocol.game.MailProtocol.Mail> 
        getMailsList();
    /**
     * <code>repeated .Mail mails = 1;</code>
     */
    cn.daxiang.protocol.game.MailProtocol.Mail getMails(int index);
    /**
     * <code>repeated .Mail mails = 1;</code>
     */
    int getMailsCount();
    /**
     * <code>repeated .Mail mails = 1;</code>
     */
    java.util.List<? extends cn.daxiang.protocol.game.MailProtocol.MailOrBuilder> 
        getMailsOrBuilderList();
    /**
     * <code>repeated .Mail mails = 1;</code>
     */
    cn.daxiang.protocol.game.MailProtocol.MailOrBuilder getMailsOrBuilder(
        int index);
  }
  /**
   * <pre>
   ** 邮件列表响应 
   * </pre>
   *
   * Protobuf type {@code MailListResponse}
   */
  public  static final class MailListResponse extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:MailListResponse)
      MailListResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use MailListResponse.newBuilder() to construct.
    private MailListResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private MailListResponse() {
      mails_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private MailListResponse(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
                mails_ = new java.util.ArrayList<cn.daxiang.protocol.game.MailProtocol.Mail>();
                mutable_bitField0_ |= 0x00000001;
              }
              mails_.add(
                  input.readMessage(cn.daxiang.protocol.game.MailProtocol.Mail.parser(), extensionRegistry));
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
          mails_ = java.util.Collections.unmodifiableList(mails_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.MailProtocol.internal_static_MailListResponse_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.daxiang.protocol.game.MailProtocol.internal_static_MailListResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.daxiang.protocol.game.MailProtocol.MailListResponse.class, cn.daxiang.protocol.game.MailProtocol.MailListResponse.Builder.class);
    }

    public static final int MAILS_FIELD_NUMBER = 1;
    private java.util.List<cn.daxiang.protocol.game.MailProtocol.Mail> mails_;
    /**
     * <code>repeated .Mail mails = 1;</code>
     */
    public java.util.List<cn.daxiang.protocol.game.MailProtocol.Mail> getMailsList() {
      return mails_;
    }
    /**
     * <code>repeated .Mail mails = 1;</code>
     */
    public java.util.List<? extends cn.daxiang.protocol.game.MailProtocol.MailOrBuilder> 
        getMailsOrBuilderList() {
      return mails_;
    }
    /**
     * <code>repeated .Mail mails = 1;</code>
     */
    public int getMailsCount() {
      return mails_.size();
    }
    /**
     * <code>repeated .Mail mails = 1;</code>
     */
    public cn.daxiang.protocol.game.MailProtocol.Mail getMails(int index) {
      return mails_.get(index);
    }
    /**
     * <code>repeated .Mail mails = 1;</code>
     */
    public cn.daxiang.protocol.game.MailProtocol.MailOrBuilder getMailsOrBuilder(
        int index) {
      return mails_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < mails_.size(); i++) {
        output.writeMessage(1, mails_.get(i));
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < mails_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, mails_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof cn.daxiang.protocol.game.MailProtocol.MailListResponse)) {
        return super.equals(obj);
      }
      cn.daxiang.protocol.game.MailProtocol.MailListResponse other = (cn.daxiang.protocol.game.MailProtocol.MailListResponse) obj;

      boolean result = true;
      result = result && getMailsList()
          .equals(other.getMailsList());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getMailsCount() > 0) {
        hash = (37 * hash) + MAILS_FIELD_NUMBER;
        hash = (53 * hash) + getMailsList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static cn.daxiang.protocol.game.MailProtocol.MailListResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.MailProtocol.MailListResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.MailProtocol.MailListResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.MailProtocol.MailListResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.MailProtocol.MailListResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.MailProtocol.MailListResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.MailProtocol.MailListResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.MailProtocol.MailListResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.MailProtocol.MailListResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.MailProtocol.MailListResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.MailProtocol.MailListResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.MailProtocol.MailListResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(cn.daxiang.protocol.game.MailProtocol.MailListResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** 邮件列表响应 
     * </pre>
     *
     * Protobuf type {@code MailListResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:MailListResponse)
        cn.daxiang.protocol.game.MailProtocol.MailListResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.daxiang.protocol.game.MailProtocol.internal_static_MailListResponse_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.daxiang.protocol.game.MailProtocol.internal_static_MailListResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.daxiang.protocol.game.MailProtocol.MailListResponse.class, cn.daxiang.protocol.game.MailProtocol.MailListResponse.Builder.class);
      }

      // Construct using cn.daxiang.protocol.game.MailProtocol.MailListResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getMailsFieldBuilder();
        }
      }
      public Builder clear() {
        super.clear();
        if (mailsBuilder_ == null) {
          mails_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          mailsBuilder_.clear();
        }
        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return cn.daxiang.protocol.game.MailProtocol.internal_static_MailListResponse_descriptor;
      }

      public cn.daxiang.protocol.game.MailProtocol.MailListResponse getDefaultInstanceForType() {
        return cn.daxiang.protocol.game.MailProtocol.MailListResponse.getDefaultInstance();
      }

      public cn.daxiang.protocol.game.MailProtocol.MailListResponse build() {
        cn.daxiang.protocol.game.MailProtocol.MailListResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public cn.daxiang.protocol.game.MailProtocol.MailListResponse buildPartial() {
        cn.daxiang.protocol.game.MailProtocol.MailListResponse result = new cn.daxiang.protocol.game.MailProtocol.MailListResponse(this);
        int from_bitField0_ = bitField0_;
        if (mailsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001)) {
            mails_ = java.util.Collections.unmodifiableList(mails_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.mails_ = mails_;
        } else {
          result.mails_ = mailsBuilder_.build();
        }
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof cn.daxiang.protocol.game.MailProtocol.MailListResponse) {
          return mergeFrom((cn.daxiang.protocol.game.MailProtocol.MailListResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(cn.daxiang.protocol.game.MailProtocol.MailListResponse other) {
        if (other == cn.daxiang.protocol.game.MailProtocol.MailListResponse.getDefaultInstance()) return this;
        if (mailsBuilder_ == null) {
          if (!other.mails_.isEmpty()) {
            if (mails_.isEmpty()) {
              mails_ = other.mails_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureMailsIsMutable();
              mails_.addAll(other.mails_);
            }
            onChanged();
          }
        } else {
          if (!other.mails_.isEmpty()) {
            if (mailsBuilder_.isEmpty()) {
              mailsBuilder_.dispose();
              mailsBuilder_ = null;
              mails_ = other.mails_;
              bitField0_ = (bitField0_ & ~0x00000001);
              mailsBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getMailsFieldBuilder() : null;
            } else {
              mailsBuilder_.addAllMessages(other.mails_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        cn.daxiang.protocol.game.MailProtocol.MailListResponse parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (cn.daxiang.protocol.game.MailProtocol.MailListResponse) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<cn.daxiang.protocol.game.MailProtocol.Mail> mails_ =
        java.util.Collections.emptyList();
      private void ensureMailsIsMutable() {
        if (!((bitField0_ & 0x00000001) == 0x00000001)) {
          mails_ = new java.util.ArrayList<cn.daxiang.protocol.game.MailProtocol.Mail>(mails_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          cn.daxiang.protocol.game.MailProtocol.Mail, cn.daxiang.protocol.game.MailProtocol.Mail.Builder, cn.daxiang.protocol.game.MailProtocol.MailOrBuilder> mailsBuilder_;

      /**
       * <code>repeated .Mail mails = 1;</code>
       */
      public java.util.List<cn.daxiang.protocol.game.MailProtocol.Mail> getMailsList() {
        if (mailsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(mails_);
        } else {
          return mailsBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .Mail mails = 1;</code>
       */
      public int getMailsCount() {
        if (mailsBuilder_ == null) {
          return mails_.size();
        } else {
          return mailsBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .Mail mails = 1;</code>
       */
      public cn.daxiang.protocol.game.MailProtocol.Mail getMails(int index) {
        if (mailsBuilder_ == null) {
          return mails_.get(index);
        } else {
          return mailsBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .Mail mails = 1;</code>
       */
      public Builder setMails(
          int index, cn.daxiang.protocol.game.MailProtocol.Mail value) {
        if (mailsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureMailsIsMutable();
          mails_.set(index, value);
          onChanged();
        } else {
          mailsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .Mail mails = 1;</code>
       */
      public Builder setMails(
          int index, cn.daxiang.protocol.game.MailProtocol.Mail.Builder builderForValue) {
        if (mailsBuilder_ == null) {
          ensureMailsIsMutable();
          mails_.set(index, builderForValue.build());
          onChanged();
        } else {
          mailsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .Mail mails = 1;</code>
       */
      public Builder addMails(cn.daxiang.protocol.game.MailProtocol.Mail value) {
        if (mailsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureMailsIsMutable();
          mails_.add(value);
          onChanged();
        } else {
          mailsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .Mail mails = 1;</code>
       */
      public Builder addMails(
          int index, cn.daxiang.protocol.game.MailProtocol.Mail value) {
        if (mailsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureMailsIsMutable();
          mails_.add(index, value);
          onChanged();
        } else {
          mailsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .Mail mails = 1;</code>
       */
      public Builder addMails(
          cn.daxiang.protocol.game.MailProtocol.Mail.Builder builderForValue) {
        if (mailsBuilder_ == null) {
          ensureMailsIsMutable();
          mails_.add(builderForValue.build());
          onChanged();
        } else {
          mailsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .Mail mails = 1;</code>
       */
      public Builder addMails(
          int index, cn.daxiang.protocol.game.MailProtocol.Mail.Builder builderForValue) {
        if (mailsBuilder_ == null) {
          ensureMailsIsMutable();
          mails_.add(index, builderForValue.build());
          onChanged();
        } else {
          mailsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .Mail mails = 1;</code>
       */
      public Builder addAllMails(
          java.lang.Iterable<? extends cn.daxiang.protocol.game.MailProtocol.Mail> values) {
        if (mailsBuilder_ == null) {
          ensureMailsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, mails_);
          onChanged();
        } else {
          mailsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .Mail mails = 1;</code>
       */
      public Builder clearMails() {
        if (mailsBuilder_ == null) {
          mails_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          mailsBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .Mail mails = 1;</code>
       */
      public Builder removeMails(int index) {
        if (mailsBuilder_ == null) {
          ensureMailsIsMutable();
          mails_.remove(index);
          onChanged();
        } else {
          mailsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .Mail mails = 1;</code>
       */
      public cn.daxiang.protocol.game.MailProtocol.Mail.Builder getMailsBuilder(
          int index) {
        return getMailsFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .Mail mails = 1;</code>
       */
      public cn.daxiang.protocol.game.MailProtocol.MailOrBuilder getMailsOrBuilder(
          int index) {
        if (mailsBuilder_ == null) {
          return mails_.get(index);  } else {
          return mailsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .Mail mails = 1;</code>
       */
      public java.util.List<? extends cn.daxiang.protocol.game.MailProtocol.MailOrBuilder> 
           getMailsOrBuilderList() {
        if (mailsBuilder_ != null) {
          return mailsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(mails_);
        }
      }
      /**
       * <code>repeated .Mail mails = 1;</code>
       */
      public cn.daxiang.protocol.game.MailProtocol.Mail.Builder addMailsBuilder() {
        return getMailsFieldBuilder().addBuilder(
            cn.daxiang.protocol.game.MailProtocol.Mail.getDefaultInstance());
      }
      /**
       * <code>repeated .Mail mails = 1;</code>
       */
      public cn.daxiang.protocol.game.MailProtocol.Mail.Builder addMailsBuilder(
          int index) {
        return getMailsFieldBuilder().addBuilder(
            index, cn.daxiang.protocol.game.MailProtocol.Mail.getDefaultInstance());
      }
      /**
       * <code>repeated .Mail mails = 1;</code>
       */
      public java.util.List<cn.daxiang.protocol.game.MailProtocol.Mail.Builder> 
           getMailsBuilderList() {
        return getMailsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          cn.daxiang.protocol.game.MailProtocol.Mail, cn.daxiang.protocol.game.MailProtocol.Mail.Builder, cn.daxiang.protocol.game.MailProtocol.MailOrBuilder> 
          getMailsFieldBuilder() {
        if (mailsBuilder_ == null) {
          mailsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              cn.daxiang.protocol.game.MailProtocol.Mail, cn.daxiang.protocol.game.MailProtocol.Mail.Builder, cn.daxiang.protocol.game.MailProtocol.MailOrBuilder>(
                  mails_,
                  ((bitField0_ & 0x00000001) == 0x00000001),
                  getParentForChildren(),
                  isClean());
          mails_ = null;
        }
        return mailsBuilder_;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:MailListResponse)
    }

    // @@protoc_insertion_point(class_scope:MailListResponse)
    private static final cn.daxiang.protocol.game.MailProtocol.MailListResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new cn.daxiang.protocol.game.MailProtocol.MailListResponse();
    }

    public static cn.daxiang.protocol.game.MailProtocol.MailListResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<MailListResponse>
        PARSER = new com.google.protobuf.AbstractParser<MailListResponse>() {
      public MailListResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new MailListResponse(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<MailListResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MailListResponse> getParserForType() {
      return PARSER;
    }

    public cn.daxiang.protocol.game.MailProtocol.MailListResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Mail_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Mail_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_MailListResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_MailListResponse_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\027game/mailProtocol.proto\032\031game/commonPr" +
      "otocol.proto\032\027game/typeProtocol.proto\"\261\001" +
      "\n\004Mail\022\016\n\006mailId\030\001 \001(\003\022\014\n\004from\030\002 \001(\t\022\r\n\005" +
      "title\030\003 \001(\t\022\017\n\007content\030\004 \001(\t\022\"\n\013attachme" +
      "nts\030\005 \003(\0132\r.RewardObject\022\020\n\010sendTime\030\006 \001" +
      "(\003\022\035\n\005state\030\007 \001(\0162\016.MailStateType\022\026\n\016exp" +
      "irationTime\030\010 \001(\003\"(\n\020MailListResponse\022\024\n" +
      "\005mails\030\001 \003(\0132\005.Mail*\227\001\n\007MailCmd\022\021\n\rMAIL_" +
      "CMD_NONE\020\000\022\021\n\rGET_MAIL_LIST\020\001\022\020\n\014MAIL_RE" +
      "CEIVE\020\002\022\030\n\024MAIL_QUICKLY_RECEIVE\020\003\022\r\n\tMAI" +
      "L_READ\020\004\022\027\n\023MAIL_QUICKLY_DELETE\020\005\022\022\n\016PUS" +
      "H_MAIL_LIST\020dB\034\n\030cn.daxiang.protocol.gam" +
      "eH\001b\006proto3"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
        new com.google.protobuf.Descriptors.FileDescriptor.    InternalDescriptorAssigner() {
          public com.google.protobuf.ExtensionRegistry assignDescriptors(
              com.google.protobuf.Descriptors.FileDescriptor root) {
            descriptor = root;
            return null;
          }
        };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          cn.daxiang.protocol.game.CommonProtocol.getDescriptor(),
          cn.daxiang.protocol.game.TypeProtocol.getDescriptor(),
        }, assigner);
    internal_static_Mail_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_Mail_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Mail_descriptor,
        new java.lang.String[] { "MailId", "From", "Title", "Content", "Attachments", "SendTime", "State", "ExpirationTime", });
    internal_static_MailListResponse_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_MailListResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_MailListResponse_descriptor,
        new java.lang.String[] { "Mails", });
    cn.daxiang.protocol.game.CommonProtocol.getDescriptor();
    cn.daxiang.protocol.game.TypeProtocol.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
