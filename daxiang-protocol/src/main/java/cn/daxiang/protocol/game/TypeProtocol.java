// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: game/typeProtocol.proto

package cn.daxiang.protocol.game;

public final class TypeProtocol {
  private TypeProtocol() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  /**
   * <pre>
   ** 奖励类型
   * </pre>
   *
   * Protobuf enum {@code RewardType}
   */
  public enum RewardType
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>REWARD_TYPE_NONE = 0;</code>
     */
    REWARD_TYPE_NONE(0),
    /**
     * <pre>
     ** 资源
     * </pre>
     *
     * <code>RESOURCE = 10;</code>
     */
    RESOURCE(10),
    /**
     * <pre>
     ** 物品
     * </pre>
     *
     * <code>GOODS = 11;</code>
     */
    GOODS(11),
    /**
     * <pre>
     ** 英雄
     * </pre>
     *
     * <code>HERO = 12;</code>
     */
    HERO(12),
    /**
     * <pre>
     ** 装备
     * </pre>
     *
     * <code>EQUIPMENT = 13;</code>
     */
    EQUIPMENT(13),
    /**
     * <pre>
     ** 符文
     * </pre>
     *
     * <code>RUNE = 14;</code>
     */
    RUNE(14),
    /**
     * <pre>
     ** 特权
     * </pre>
     *
     * <code>PREROGATIVE = 15;</code>
     */
    PREROGATIVE(15),
    /**
     * <pre>
     ** 聊天气泡 
     * </pre>
     *
     * <code>CHAT_BUBBLE = 16;</code>
     */
    CHAT_BUBBLE(16),
    /**
     * <pre>
     ** 头像框 
     * </pre>
     *
     * <code>AVATAR_FRAME = 17;</code>
     */
    AVATAR_FRAME(17),
    /**
     * <pre>
     ** 头像
     * </pre>
     *
     * <code>AVATAR = 18;</code>
     */
    AVATAR(18),
    /**
     * <pre>
     ** 形象
     * </pre>
     *
     * <code>SHOW = 19;</code>
     */
    SHOW(19),
    /**
     * <pre>
     ** 20.宝物 
     * </pre>
     *
     * <code>TREASURE = 20;</code>
     */
    TREASURE(20),
    /**
     * <pre>
     ** 22.上古珍宝 
     * </pre>
     *
     * <code>ANTIQUE = 22;</code>
     */
    ANTIQUE(22),
    /**
     * <pre>
     ** 23.专武
     * </pre>
     *
     * <code>IMMORTALS = 23;</code>
     */
    IMMORTALS(23),
    /**
     * <pre>
     **24.红颜
     * </pre>
     *
     * <code>BEAUTY = 24;</code>
     */
    BEAUTY(24),
    /**
     * <pre>
     **25.兵符
     * </pre>
     *
     * <code>RUNES = 25;</code>
     */
    RUNES(25),
    /**
     * <pre>
     **26.灵石
     * </pre>
     *
     * <code>GEMSTONE = 26;</code>
     */
    GEMSTONE(26),
    /**
     * <pre>
     **27.武魂
     * </pre>
     *
     * <code>SOUL = 27;</code>
     */
    SOUL(27),
    /**
     * <pre>
     **28.神器
     * </pre>
     *
     * <code>RELIC = 28;</code>
     */
    RELIC(28),
    /**
     * <pre>
     **29.神兽
     * </pre>
     *
     * <code>BEAST = 29;</code>
     */
    BEAST(29),
    /**
     * <pre>
     **30.时空红颜
     * </pre>
     *
     * <code>SPACETIME_BEAUTY = 30;</code>
     */
    SPACETIME_BEAUTY(30),
    /**
     * <pre>
     **31.归葬秘境装备
     * </pre>
     *
     * <code>SANCTUARY_EQUIPMENT = 31;</code>
     */
    SANCTUARY_EQUIPMENT(31),
    /**
     * <pre>
     **32.新神器
     * </pre>
     *
     * <code>HALLOWS = 32;</code>
     */
    HALLOWS(32),
    /**
     * <pre>
     **33.神兵
     * </pre>
     *
     * <code>FROSTMOURNE = 33;</code>
     */
    FROSTMOURNE(33),
    /**
     * <pre>
     **34.英雄皮肤
     * </pre>
     *
     * <code>HERO_SKIN = 34;</code>
     */
    HERO_SKIN(34),
    /**
     * <pre>
     ** 35.坐骑 
     * </pre>
     *
     * <code>MOUNT = 35;</code>
     */
    MOUNT(35),
    /**
     * <pre>
     ** 36.英雄皮肤碎片 
     * </pre>
     *
     * <code>HERO_SKIN_FRAGMENT = 36;</code>
     */
    HERO_SKIN_FRAGMENT(36),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>REWARD_TYPE_NONE = 0;</code>
     */
    public static final int REWARD_TYPE_NONE_VALUE = 0;
    /**
     * <pre>
     ** 资源
     * </pre>
     *
     * <code>RESOURCE = 10;</code>
     */
    public static final int RESOURCE_VALUE = 10;
    /**
     * <pre>
     ** 物品
     * </pre>
     *
     * <code>GOODS = 11;</code>
     */
    public static final int GOODS_VALUE = 11;
    /**
     * <pre>
     ** 英雄
     * </pre>
     *
     * <code>HERO = 12;</code>
     */
    public static final int HERO_VALUE = 12;
    /**
     * <pre>
     ** 装备
     * </pre>
     *
     * <code>EQUIPMENT = 13;</code>
     */
    public static final int EQUIPMENT_VALUE = 13;
    /**
     * <pre>
     ** 符文
     * </pre>
     *
     * <code>RUNE = 14;</code>
     */
    public static final int RUNE_VALUE = 14;
    /**
     * <pre>
     ** 特权
     * </pre>
     *
     * <code>PREROGATIVE = 15;</code>
     */
    public static final int PREROGATIVE_VALUE = 15;
    /**
     * <pre>
     ** 聊天气泡 
     * </pre>
     *
     * <code>CHAT_BUBBLE = 16;</code>
     */
    public static final int CHAT_BUBBLE_VALUE = 16;
    /**
     * <pre>
     ** 头像框 
     * </pre>
     *
     * <code>AVATAR_FRAME = 17;</code>
     */
    public static final int AVATAR_FRAME_VALUE = 17;
    /**
     * <pre>
     ** 头像
     * </pre>
     *
     * <code>AVATAR = 18;</code>
     */
    public static final int AVATAR_VALUE = 18;
    /**
     * <pre>
     ** 形象
     * </pre>
     *
     * <code>SHOW = 19;</code>
     */
    public static final int SHOW_VALUE = 19;
    /**
     * <pre>
     ** 20.宝物 
     * </pre>
     *
     * <code>TREASURE = 20;</code>
     */
    public static final int TREASURE_VALUE = 20;
    /**
     * <pre>
     ** 22.上古珍宝 
     * </pre>
     *
     * <code>ANTIQUE = 22;</code>
     */
    public static final int ANTIQUE_VALUE = 22;
    /**
     * <pre>
     ** 23.专武
     * </pre>
     *
     * <code>IMMORTALS = 23;</code>
     */
    public static final int IMMORTALS_VALUE = 23;
    /**
     * <pre>
     **24.红颜
     * </pre>
     *
     * <code>BEAUTY = 24;</code>
     */
    public static final int BEAUTY_VALUE = 24;
    /**
     * <pre>
     **25.兵符
     * </pre>
     *
     * <code>RUNES = 25;</code>
     */
    public static final int RUNES_VALUE = 25;
    /**
     * <pre>
     **26.灵石
     * </pre>
     *
     * <code>GEMSTONE = 26;</code>
     */
    public static final int GEMSTONE_VALUE = 26;
    /**
     * <pre>
     **27.武魂
     * </pre>
     *
     * <code>SOUL = 27;</code>
     */
    public static final int SOUL_VALUE = 27;
    /**
     * <pre>
     **28.神器
     * </pre>
     *
     * <code>RELIC = 28;</code>
     */
    public static final int RELIC_VALUE = 28;
    /**
     * <pre>
     **29.神兽
     * </pre>
     *
     * <code>BEAST = 29;</code>
     */
    public static final int BEAST_VALUE = 29;
    /**
     * <pre>
     **30.时空红颜
     * </pre>
     *
     * <code>SPACETIME_BEAUTY = 30;</code>
     */
    public static final int SPACETIME_BEAUTY_VALUE = 30;
    /**
     * <pre>
     **31.归葬秘境装备
     * </pre>
     *
     * <code>SANCTUARY_EQUIPMENT = 31;</code>
     */
    public static final int SANCTUARY_EQUIPMENT_VALUE = 31;
    /**
     * <pre>
     **32.新神器
     * </pre>
     *
     * <code>HALLOWS = 32;</code>
     */
    public static final int HALLOWS_VALUE = 32;
    /**
     * <pre>
     **33.神兵
     * </pre>
     *
     * <code>FROSTMOURNE = 33;</code>
     */
    public static final int FROSTMOURNE_VALUE = 33;
    /**
     * <pre>
     **34.英雄皮肤
     * </pre>
     *
     * <code>HERO_SKIN = 34;</code>
     */
    public static final int HERO_SKIN_VALUE = 34;
    /**
     * <pre>
     ** 35.坐骑 
     * </pre>
     *
     * <code>MOUNT = 35;</code>
     */
    public static final int MOUNT_VALUE = 35;
    /**
     * <pre>
     ** 36.英雄皮肤碎片 
     * </pre>
     *
     * <code>HERO_SKIN_FRAGMENT = 36;</code>
     */
    public static final int HERO_SKIN_FRAGMENT_VALUE = 36;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static RewardType valueOf(int value) {
      return forNumber(value);
    }

    public static RewardType forNumber(int value) {
      switch (value) {
        case 0: return REWARD_TYPE_NONE;
        case 10: return RESOURCE;
        case 11: return GOODS;
        case 12: return HERO;
        case 13: return EQUIPMENT;
        case 14: return RUNE;
        case 15: return PREROGATIVE;
        case 16: return CHAT_BUBBLE;
        case 17: return AVATAR_FRAME;
        case 18: return AVATAR;
        case 19: return SHOW;
        case 20: return TREASURE;
        case 22: return ANTIQUE;
        case 23: return IMMORTALS;
        case 24: return BEAUTY;
        case 25: return RUNES;
        case 26: return GEMSTONE;
        case 27: return SOUL;
        case 28: return RELIC;
        case 29: return BEAST;
        case 30: return SPACETIME_BEAUTY;
        case 31: return SANCTUARY_EQUIPMENT;
        case 32: return HALLOWS;
        case 33: return FROSTMOURNE;
        case 34: return HERO_SKIN;
        case 35: return MOUNT;
        case 36: return HERO_SKIN_FRAGMENT;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<RewardType>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        RewardType> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<RewardType>() {
            public RewardType findValueByNumber(int number) {
              return RewardType.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.TypeProtocol.getDescriptor().getEnumTypes().get(0);
    }

    private static final RewardType[] VALUES = values();

    public static RewardType valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private RewardType(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:RewardType)
  }

  /**
   * <pre>
   ** 资源id
   * </pre>
   *
   * Protobuf enum {@code ResourceId}
   */
  public enum ResourceId
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>RESOURCE_ID_NONE = 0;</code>
     */
    RESOURCE_ID_NONE(0),
    /**
     * <pre>
     ** 钻石
     * </pre>
     *
     * <code>DIAMOND = 1;</code>
     */
    DIAMOND(1),
    /**
     * <pre>
     ** 金币
     * </pre>
     *
     * <code>GOLD = 2;</code>
     */
    GOLD(2),
    /**
     * <pre>
     ** 经验
     * </pre>
     *
     * <code>EXP = 3;</code>
     */
    EXP(3),
    /**
     * <pre>
     ** 竞技币
     * </pre>
     *
     * <code>ARENA_COIN = 4;</code>
     */
    ARENA_COIN(4),
    /**
     * <pre>
     ** 荣耀币 
     * </pre>
     *
     * <code>HONOUR_COIN = 5;</code>
     */
    HONOUR_COIN(5),
    /**
     * <pre>
     ** 精力 
     * </pre>
     *
     * <code>VIGOUR = 6;</code>
     */
    VIGOUR(6),
    /**
     * <pre>
     ** 战棋币
     * </pre>
     *
     * <code>CHESS_COIN = 7;</code>
     */
    CHESS_COIN(7),
    /**
     * <pre>
     ** 在线组队TD币(赏金) 
     * </pre>
     *
     * <code>TEAM_ONLINE_TD_COIN = 8;</code>
     */
    TEAM_ONLINE_TD_COIN(8),
    /**
     * <pre>
     ** 军团币 
     * </pre>
     *
     * <code>NATION_COIN = 9;</code>
     */
    NATION_COIN(9),
    /**
     * <pre>
     ** 军团经验
     * </pre>
     *
     * <code>NATION_EXP = 10;</code>
     */
    NATION_EXP(10),
    /**
     * <pre>
     ** 专武币
     * </pre>
     *
     * <code>WEAPON_COIN = 13;</code>
     */
    WEAPON_COIN(13),
    /**
     * <pre>
     ** 体力 
     * </pre>
     *
     * <code>ENERGY = 14;</code>
     */
    ENERGY(14),
    /**
     * <pre>
     ** 军团贡献
     * </pre>
     *
     * <code>NATION_CONTRIBUTION = 16;</code>
     */
    NATION_CONTRIBUTION(16),
    /**
     * <pre>
     ** VIP经验 
     * </pre>
     *
     * <code>VIP_EXP = 17;</code>
     */
    VIP_EXP(17),
    /**
     * <pre>
     **名将币（名将塔掉落）
     * </pre>
     *
     * <code>TOWER_COIN = 19;</code>
     */
    TOWER_COIN(19),
    /**
     * <pre>
     **无尽币（无尽试炼掉落）
     * </pre>
     *
     * <code>ENDLESS_COIN = 20;</code>
     */
    ENDLESS_COIN(20),
    /**
     * <pre>
     ** 友情点
     * </pre>
     *
     * <code>FRIEND_POINT = 21;</code>
     */
    FRIEND_POINT(21),
    /**
     * <pre>
     **战魂玉(组队魔窟掉落)
     * </pre>
     *
     * <code>CAVE_COIN = 22;</code>
     */
    CAVE_COIN(22),
    /**
     * <pre>
     ** 攻城略地积分 
     * </pre>
     *
     * <code>CAPTURE_COIN = 23;</code>
     */
    CAPTURE_COIN(23),
    /**
     * <pre>
     ** 巅峰竞技场货币 
     * </pre>
     *
     * <code>CROSS_CUP_COIN = 24;</code>
     */
    CROSS_CUP_COIN(24),
    /**
     * <pre>
     ** 群雄币 
     * </pre>
     *
     * <code>HEGEMONY_COIN = 25;</code>
     */
    HEGEMONY_COIN(25),
    /**
     * <pre>
     ** 王城币 
     * </pre>
     *
     * <code>GVG_COIN = 26;</code>
     */
    GVG_COIN(26),
    /**
     * <pre>
     ** 活动31任务代币 
     * </pre>
     *
     * <code>ACTIVITY_31_COIN = 28;</code>
     */
    ACTIVITY_31_COIN(28),
    /**
     * <pre>
     ** 镖银 
     * </pre>
     *
     * <code>ESCORT_COIN = 29;</code>
     */
    ESCORT_COIN(29),
    /**
     * <pre>
     ** 棋魂 
     * </pre>
     *
     * <code>CHESS_2_COIN = 30;</code>
     */
    CHESS_2_COIN(30),
    /**
     * <pre>
     ** 战魂币 
     * </pre>
     *
     * <code>QINRACE_COIN = 31;</code>
     */
    QINRACE_COIN(31),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>RESOURCE_ID_NONE = 0;</code>
     */
    public static final int RESOURCE_ID_NONE_VALUE = 0;
    /**
     * <pre>
     ** 钻石
     * </pre>
     *
     * <code>DIAMOND = 1;</code>
     */
    public static final int DIAMOND_VALUE = 1;
    /**
     * <pre>
     ** 金币
     * </pre>
     *
     * <code>GOLD = 2;</code>
     */
    public static final int GOLD_VALUE = 2;
    /**
     * <pre>
     ** 经验
     * </pre>
     *
     * <code>EXP = 3;</code>
     */
    public static final int EXP_VALUE = 3;
    /**
     * <pre>
     ** 竞技币
     * </pre>
     *
     * <code>ARENA_COIN = 4;</code>
     */
    public static final int ARENA_COIN_VALUE = 4;
    /**
     * <pre>
     ** 荣耀币 
     * </pre>
     *
     * <code>HONOUR_COIN = 5;</code>
     */
    public static final int HONOUR_COIN_VALUE = 5;
    /**
     * <pre>
     ** 精力 
     * </pre>
     *
     * <code>VIGOUR = 6;</code>
     */
    public static final int VIGOUR_VALUE = 6;
    /**
     * <pre>
     ** 战棋币
     * </pre>
     *
     * <code>CHESS_COIN = 7;</code>
     */
    public static final int CHESS_COIN_VALUE = 7;
    /**
     * <pre>
     ** 在线组队TD币(赏金) 
     * </pre>
     *
     * <code>TEAM_ONLINE_TD_COIN = 8;</code>
     */
    public static final int TEAM_ONLINE_TD_COIN_VALUE = 8;
    /**
     * <pre>
     ** 军团币 
     * </pre>
     *
     * <code>NATION_COIN = 9;</code>
     */
    public static final int NATION_COIN_VALUE = 9;
    /**
     * <pre>
     ** 军团经验
     * </pre>
     *
     * <code>NATION_EXP = 10;</code>
     */
    public static final int NATION_EXP_VALUE = 10;
    /**
     * <pre>
     ** 专武币
     * </pre>
     *
     * <code>WEAPON_COIN = 13;</code>
     */
    public static final int WEAPON_COIN_VALUE = 13;
    /**
     * <pre>
     ** 体力 
     * </pre>
     *
     * <code>ENERGY = 14;</code>
     */
    public static final int ENERGY_VALUE = 14;
    /**
     * <pre>
     ** 军团贡献
     * </pre>
     *
     * <code>NATION_CONTRIBUTION = 16;</code>
     */
    public static final int NATION_CONTRIBUTION_VALUE = 16;
    /**
     * <pre>
     ** VIP经验 
     * </pre>
     *
     * <code>VIP_EXP = 17;</code>
     */
    public static final int VIP_EXP_VALUE = 17;
    /**
     * <pre>
     **名将币（名将塔掉落）
     * </pre>
     *
     * <code>TOWER_COIN = 19;</code>
     */
    public static final int TOWER_COIN_VALUE = 19;
    /**
     * <pre>
     **无尽币（无尽试炼掉落）
     * </pre>
     *
     * <code>ENDLESS_COIN = 20;</code>
     */
    public static final int ENDLESS_COIN_VALUE = 20;
    /**
     * <pre>
     ** 友情点
     * </pre>
     *
     * <code>FRIEND_POINT = 21;</code>
     */
    public static final int FRIEND_POINT_VALUE = 21;
    /**
     * <pre>
     **战魂玉(组队魔窟掉落)
     * </pre>
     *
     * <code>CAVE_COIN = 22;</code>
     */
    public static final int CAVE_COIN_VALUE = 22;
    /**
     * <pre>
     ** 攻城略地积分 
     * </pre>
     *
     * <code>CAPTURE_COIN = 23;</code>
     */
    public static final int CAPTURE_COIN_VALUE = 23;
    /**
     * <pre>
     ** 巅峰竞技场货币 
     * </pre>
     *
     * <code>CROSS_CUP_COIN = 24;</code>
     */
    public static final int CROSS_CUP_COIN_VALUE = 24;
    /**
     * <pre>
     ** 群雄币 
     * </pre>
     *
     * <code>HEGEMONY_COIN = 25;</code>
     */
    public static final int HEGEMONY_COIN_VALUE = 25;
    /**
     * <pre>
     ** 王城币 
     * </pre>
     *
     * <code>GVG_COIN = 26;</code>
     */
    public static final int GVG_COIN_VALUE = 26;
    /**
     * <pre>
     ** 活动31任务代币 
     * </pre>
     *
     * <code>ACTIVITY_31_COIN = 28;</code>
     */
    public static final int ACTIVITY_31_COIN_VALUE = 28;
    /**
     * <pre>
     ** 镖银 
     * </pre>
     *
     * <code>ESCORT_COIN = 29;</code>
     */
    public static final int ESCORT_COIN_VALUE = 29;
    /**
     * <pre>
     ** 棋魂 
     * </pre>
     *
     * <code>CHESS_2_COIN = 30;</code>
     */
    public static final int CHESS_2_COIN_VALUE = 30;
    /**
     * <pre>
     ** 战魂币 
     * </pre>
     *
     * <code>QINRACE_COIN = 31;</code>
     */
    public static final int QINRACE_COIN_VALUE = 31;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static ResourceId valueOf(int value) {
      return forNumber(value);
    }

    public static ResourceId forNumber(int value) {
      switch (value) {
        case 0: return RESOURCE_ID_NONE;
        case 1: return DIAMOND;
        case 2: return GOLD;
        case 3: return EXP;
        case 4: return ARENA_COIN;
        case 5: return HONOUR_COIN;
        case 6: return VIGOUR;
        case 7: return CHESS_COIN;
        case 8: return TEAM_ONLINE_TD_COIN;
        case 9: return NATION_COIN;
        case 10: return NATION_EXP;
        case 13: return WEAPON_COIN;
        case 14: return ENERGY;
        case 16: return NATION_CONTRIBUTION;
        case 17: return VIP_EXP;
        case 19: return TOWER_COIN;
        case 20: return ENDLESS_COIN;
        case 21: return FRIEND_POINT;
        case 22: return CAVE_COIN;
        case 23: return CAPTURE_COIN;
        case 24: return CROSS_CUP_COIN;
        case 25: return HEGEMONY_COIN;
        case 26: return GVG_COIN;
        case 28: return ACTIVITY_31_COIN;
        case 29: return ESCORT_COIN;
        case 30: return CHESS_2_COIN;
        case 31: return QINRACE_COIN;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<ResourceId>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        ResourceId> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<ResourceId>() {
            public ResourceId findValueByNumber(int number) {
              return ResourceId.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.TypeProtocol.getDescriptor().getEnumTypes().get(1);
    }

    private static final ResourceId[] VALUES = values();

    public static ResourceId valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private ResourceId(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:ResourceId)
  }

  /**
   * <pre>
   ** 物品类型
   * </pre>
   *
   * Protobuf enum {@code GoodsType}
   */
  public enum GoodsType
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>ITEM_TYPE_NONE = 0;</code>
     */
    ITEM_TYPE_NONE(0),
    /**
     * <pre>
     ** 货币类
     * </pre>
     *
     * <code>ITEM_GEM = 1;</code>
     */
    ITEM_GEM(1),
    /**
     * <pre>
     ** 资源类
     * </pre>
     *
     * <code>ITEM_GOLD = 2;</code>
     */
    ITEM_GOLD(2),
    /**
     * <pre>
     ** 使用类 
     * </pre>
     *
     * <code>USE = 3;</code>
     */
    USE(3),
    /**
     * <pre>
     ** 碎片 
     * </pre>
     *
     * <code>FRAMENT = 4;</code>
     */
    FRAMENT(4),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>ITEM_TYPE_NONE = 0;</code>
     */
    public static final int ITEM_TYPE_NONE_VALUE = 0;
    /**
     * <pre>
     ** 货币类
     * </pre>
     *
     * <code>ITEM_GEM = 1;</code>
     */
    public static final int ITEM_GEM_VALUE = 1;
    /**
     * <pre>
     ** 资源类
     * </pre>
     *
     * <code>ITEM_GOLD = 2;</code>
     */
    public static final int ITEM_GOLD_VALUE = 2;
    /**
     * <pre>
     ** 使用类 
     * </pre>
     *
     * <code>USE = 3;</code>
     */
    public static final int USE_VALUE = 3;
    /**
     * <pre>
     ** 碎片 
     * </pre>
     *
     * <code>FRAMENT = 4;</code>
     */
    public static final int FRAMENT_VALUE = 4;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static GoodsType valueOf(int value) {
      return forNumber(value);
    }

    public static GoodsType forNumber(int value) {
      switch (value) {
        case 0: return ITEM_TYPE_NONE;
        case 1: return ITEM_GEM;
        case 2: return ITEM_GOLD;
        case 3: return USE;
        case 4: return FRAMENT;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<GoodsType>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        GoodsType> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<GoodsType>() {
            public GoodsType findValueByNumber(int number) {
              return GoodsType.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.TypeProtocol.getDescriptor().getEnumTypes().get(2);
    }

    private static final GoodsType[] VALUES = values();

    public static GoodsType valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private GoodsType(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:GoodsType)
  }

  /**
   * <pre>
   ** 踢下线类型 
   * </pre>
   *
   * Protobuf enum {@code KickOffType}
   */
  public enum KickOffType
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>KICK_OFF_TYPE_NONE = 0;</code>
     */
    KICK_OFF_TYPE_NONE(0),
    /**
     * <pre>
     ** 账号重复登录, 或者在其他地方上线 
     * </pre>
     *
     * <code>LOGIN_DUPLICATE = 1;</code>
     */
    LOGIN_DUPLICATE(1),
    /**
     * <pre>
     ** 服务器即将停服 
     * </pre>
     *
     * <code>CLOSEING = 2;</code>
     */
    CLOSEING(2),
    /**
     * <pre>
     **  玩家被封禁 
     * </pre>
     *
     * <code>USER_BLOCK = 3;</code>
     */
    USER_BLOCK(3),
    /**
     * <pre>
     ** 服务器维护中 
     * </pre>
     *
     * <code>SERVER_MAINTENANCE = 4;</code>
     */
    SERVER_MAINTENANCE(4),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>KICK_OFF_TYPE_NONE = 0;</code>
     */
    public static final int KICK_OFF_TYPE_NONE_VALUE = 0;
    /**
     * <pre>
     ** 账号重复登录, 或者在其他地方上线 
     * </pre>
     *
     * <code>LOGIN_DUPLICATE = 1;</code>
     */
    public static final int LOGIN_DUPLICATE_VALUE = 1;
    /**
     * <pre>
     ** 服务器即将停服 
     * </pre>
     *
     * <code>CLOSEING = 2;</code>
     */
    public static final int CLOSEING_VALUE = 2;
    /**
     * <pre>
     **  玩家被封禁 
     * </pre>
     *
     * <code>USER_BLOCK = 3;</code>
     */
    public static final int USER_BLOCK_VALUE = 3;
    /**
     * <pre>
     ** 服务器维护中 
     * </pre>
     *
     * <code>SERVER_MAINTENANCE = 4;</code>
     */
    public static final int SERVER_MAINTENANCE_VALUE = 4;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static KickOffType valueOf(int value) {
      return forNumber(value);
    }

    public static KickOffType forNumber(int value) {
      switch (value) {
        case 0: return KICK_OFF_TYPE_NONE;
        case 1: return LOGIN_DUPLICATE;
        case 2: return CLOSEING;
        case 3: return USER_BLOCK;
        case 4: return SERVER_MAINTENANCE;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<KickOffType>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        KickOffType> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<KickOffType>() {
            public KickOffType findValueByNumber(int number) {
              return KickOffType.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.TypeProtocol.getDescriptor().getEnumTypes().get(3);
    }

    private static final KickOffType[] VALUES = values();

    public static KickOffType valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private KickOffType(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:KickOffType)
  }

  /**
   * <pre>
   ** VIP卡类型 
   * </pre>
   *
   * Protobuf enum {@code VipCardType}
   */
  public enum VipCardType
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>VIP_CARD_TYPE_NON = 0;</code>
     */
    VIP_CARD_TYPE_NON(0),
    /**
     * <pre>
     ** 1.月卡 
     * </pre>
     *
     * <code>MOUTH_VIP_CARD = 1;</code>
     */
    MOUTH_VIP_CARD(1),
    /**
     * <pre>
     ** 2.至尊月卡 
     * </pre>
     *
     * <code>SUPREME_MOUTH_VIP_CARD = 2;</code>
     */
    SUPREME_MOUTH_VIP_CARD(2),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>VIP_CARD_TYPE_NON = 0;</code>
     */
    public static final int VIP_CARD_TYPE_NON_VALUE = 0;
    /**
     * <pre>
     ** 1.月卡 
     * </pre>
     *
     * <code>MOUTH_VIP_CARD = 1;</code>
     */
    public static final int MOUTH_VIP_CARD_VALUE = 1;
    /**
     * <pre>
     ** 2.至尊月卡 
     * </pre>
     *
     * <code>SUPREME_MOUTH_VIP_CARD = 2;</code>
     */
    public static final int SUPREME_MOUTH_VIP_CARD_VALUE = 2;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static VipCardType valueOf(int value) {
      return forNumber(value);
    }

    public static VipCardType forNumber(int value) {
      switch (value) {
        case 0: return VIP_CARD_TYPE_NON;
        case 1: return MOUTH_VIP_CARD;
        case 2: return SUPREME_MOUTH_VIP_CARD;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<VipCardType>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        VipCardType> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<VipCardType>() {
            public VipCardType findValueByNumber(int number) {
              return VipCardType.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.TypeProtocol.getDescriptor().getEnumTypes().get(4);
    }

    private static final VipCardType[] VALUES = values();

    public static VipCardType valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private VipCardType(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:VipCardType)
  }

  /**
   * <pre>
   ** 邮件状态类型 
   * </pre>
   *
   * Protobuf enum {@code MailStateType}
   */
  public enum MailStateType
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>MAIL_STATE_TYPE_NONE = 0;</code>
     */
    MAIL_STATE_TYPE_NONE(0),
    /**
     * <pre>
     ** 1.未读 
     * </pre>
     *
     * <code>UNREAD = 1;</code>
     */
    UNREAD(1),
    /**
     * <pre>
     ** 2.已读 
     * </pre>
     *
     * <code>READ = 2;</code>
     */
    READ(2),
    /**
     * <pre>
     ** 3.已领取 
     * </pre>
     *
     * <code>RECEIVE = 3;</code>
     */
    RECEIVE(3),
    /**
     * <pre>
     ** 4.删除 
     * </pre>
     *
     * <code>DELETE = 4;</code>
     */
    DELETE(4),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>MAIL_STATE_TYPE_NONE = 0;</code>
     */
    public static final int MAIL_STATE_TYPE_NONE_VALUE = 0;
    /**
     * <pre>
     ** 1.未读 
     * </pre>
     *
     * <code>UNREAD = 1;</code>
     */
    public static final int UNREAD_VALUE = 1;
    /**
     * <pre>
     ** 2.已读 
     * </pre>
     *
     * <code>READ = 2;</code>
     */
    public static final int READ_VALUE = 2;
    /**
     * <pre>
     ** 3.已领取 
     * </pre>
     *
     * <code>RECEIVE = 3;</code>
     */
    public static final int RECEIVE_VALUE = 3;
    /**
     * <pre>
     ** 4.删除 
     * </pre>
     *
     * <code>DELETE = 4;</code>
     */
    public static final int DELETE_VALUE = 4;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static MailStateType valueOf(int value) {
      return forNumber(value);
    }

    public static MailStateType forNumber(int value) {
      switch (value) {
        case 0: return MAIL_STATE_TYPE_NONE;
        case 1: return UNREAD;
        case 2: return READ;
        case 3: return RECEIVE;
        case 4: return DELETE;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<MailStateType>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        MailStateType> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<MailStateType>() {
            public MailStateType findValueByNumber(int number) {
              return MailStateType.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.TypeProtocol.getDescriptor().getEnumTypes().get(5);
    }

    private static final MailStateType[] VALUES = values();

    public static MailStateType valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private MailStateType(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:MailStateType)
  }

  /**
   * <pre>
   ** 系统设置KEY 
   * </pre>
   *
   * Protobuf enum {@code SettingType}
   */
  public enum SettingType
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>SETTING_KEY_NONE = 0;</code>
     */
    SETTING_KEY_NONE(0),
    /**
     * <pre>
     **
     * 1.开服时间
     * value:&lt;IntPacket&gt;
     * </pre>
     *
     * <code>SERVER_START_TIME = 1;</code>
     */
    SERVER_START_TIME(1),
    /**
     * <pre>
     **
     * 12.系统邮件最大ID
     * value:&lt;IntPacket&gt;
     * </pre>
     *
     * <code>SYSTEM_MAIL_MAX_ID = 2;</code>
     */
    SYSTEM_MAIL_MAX_ID(2),
    /**
     * <pre>
     **
     * 998.合服时间
     * value:&lt;IntPacket&gt;
     * </pre>
     *
     * <code>MERGE_SERVER_TIME = 998;</code>
     */
    MERGE_SERVER_TIME(998),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>SETTING_KEY_NONE = 0;</code>
     */
    public static final int SETTING_KEY_NONE_VALUE = 0;
    /**
     * <pre>
     **
     * 1.开服时间
     * value:&lt;IntPacket&gt;
     * </pre>
     *
     * <code>SERVER_START_TIME = 1;</code>
     */
    public static final int SERVER_START_TIME_VALUE = 1;
    /**
     * <pre>
     **
     * 12.系统邮件最大ID
     * value:&lt;IntPacket&gt;
     * </pre>
     *
     * <code>SYSTEM_MAIL_MAX_ID = 2;</code>
     */
    public static final int SYSTEM_MAIL_MAX_ID_VALUE = 2;
    /**
     * <pre>
     **
     * 998.合服时间
     * value:&lt;IntPacket&gt;
     * </pre>
     *
     * <code>MERGE_SERVER_TIME = 998;</code>
     */
    public static final int MERGE_SERVER_TIME_VALUE = 998;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static SettingType valueOf(int value) {
      return forNumber(value);
    }

    public static SettingType forNumber(int value) {
      switch (value) {
        case 0: return SETTING_KEY_NONE;
        case 1: return SERVER_START_TIME;
        case 2: return SYSTEM_MAIL_MAX_ID;
        case 998: return MERGE_SERVER_TIME;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<SettingType>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        SettingType> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<SettingType>() {
            public SettingType findValueByNumber(int number) {
              return SettingType.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.TypeProtocol.getDescriptor().getEnumTypes().get(6);
    }

    private static final SettingType[] VALUES = values();

    public static SettingType valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private SettingType(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:SettingType)
  }

  /**
   * <pre>
   ** 特权类型 
   * </pre>
   *
   * Protobuf enum {@code PrerogativeType}
   */
  public enum PrerogativeType
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>PREROGATIVE_TYPE_NONE = 0;</code>
     */
    PREROGATIVE_TYPE_NONE(0),
    /**
     * <pre>
     **
     * 1.离线挂机奖励时间(单位:10秒) value:10second
     * </pre>
     *
     * <code>STORY_HANG_UP_OFFLINE_REWARD_TIME = 1;</code>
     */
    STORY_HANG_UP_OFFLINE_REWARD_TIME(1),
    /**
     * <pre>
     **
     * 2.飞升之路重置次数  value:times
     * </pre>
     *
     * <code>STAR_UP_ROAD_RESET_TIMES = 2;</code>
     */
    STAR_UP_ROAD_RESET_TIMES(2),
    /**
     * <pre>
     **
     * 3.试炼重置次数 value:times(两种类型同一个次数,如果需要不一样再改)
     * </pre>
     *
     * <code>ENDLESS_RESET_TIMES = 3;</code>
     */
    ENDLESS_RESET_TIMES(3),
    /**
     * <pre>
     **
     * 4.竞技场重置次数 value:times
     * </pre>
     *
     * <code>ARNEA_RESET_TIMES = 4;</code>
     */
    ARNEA_RESET_TIMES(4),
    /**
     * <pre>
     **
     * 5.摇钱树次数 value:times
     * </pre>
     *
     * <code>MONEY_TREE_TIMES = 5;</code>
     */
    MONEY_TREE_TIMES(5),
    /**
     * <pre>
     **
     * 6.副本购买次数 value:[[type,times],[type,times]] type:{&#64;code DungeonType}
     * {&#64;code DungeonTimesPrerogative}
     * </pre>
     *
     * <code>DUNGEON_BUY_TIMES = 6;</code>
     */
    DUNGEON_BUY_TIMES(6),
    /**
     * <pre>
     **
     * 7.玲珑塔重置次数  value:times
     * </pre>
     *
     * <code>PAGODA_RESET_TIMES = 7;</code>
     */
    PAGODA_RESET_TIMES(7),
    /**
     * <pre>
     **
     * 8.修仙巡访次数 value:times
     * </pre>
     *
     * <code>IMMORTAL_VISIT_TIMES = 8;</code>
     */
    IMMORTAL_VISIT_TIMES(8),
    /**
     * <pre>
     **
     * 9.挂机奖励增加万分比 value:addTenThousand
     * </pre>
     *
     * <code>HANG_UP_REWARD_ADD_TEN_THOUSAND = 9;</code>
     */
    HANG_UP_REWARD_ADD_TEN_THOUSAND(9),
    /**
     * <pre>
     **
     * 10.全民BOSS购买参战次数 value:times
     * </pre>
     *
     * <code>WHOLE_BOSS_BUY_TIMES = 10;</code>
     */
    WHOLE_BOSS_BUY_TIMES(10),
    /**
     * <pre>
     **
     * 11.增加经验万分比 value:addTenThousand
     * 目前只增加关卡,飞升之路
     * </pre>
     *
     * <code>ADD_EXP_TEN_THOUSAND = 11;</code>
     */
    ADD_EXP_TEN_THOUSAND(11),
    /**
     * <pre>
     **
     * 12.攻城战购买次数 value:times
     * </pre>
     *
     * <code>SIEGE_BUY_TIMES = 12;</code>
     */
    SIEGE_BUY_TIMES(12),
    /**
     * <pre>
     **
     * 13.镇妖台次数 value:times
     * </pre>
     *
     * <code>SUPPRESS_DEMON_TIMES = 13;</code>
     */
    SUPPRESS_DEMON_TIMES(13),
    /**
     * <pre>
     **
     * 14.夺旗战挑战购买次数上限 value:times
     * </pre>
     *
     * <code>FLAG_WAR_CHALLENGE_LIMIT_TIMES = 14;</code>
     */
    FLAG_WAR_CHALLENGE_LIMIT_TIMES(14),
    /**
     * <pre>
     **
     * 15.征途挑战购买次数上限 value:times
     * </pre>
     *
     * <code>VIE_CHALLENGE_BUY_LIMIT_TIMES = 15;</code>
     */
    VIE_CHALLENGE_BUY_LIMIT_TIMES(15),
    /**
     * <pre>
     **
     * 18.普通关卡挑战重置次数 value:times
     * </pre>
     *
     * <code>STORY_NORMAL_CHALLENGE_RESET_TIMES = 18;</code>
     */
    STORY_NORMAL_CHALLENGE_RESET_TIMES(18),
    /**
     * <pre>
     **
     * 19.精英关卡挑战重置次数 value:times
     * </pre>
     *
     * <code>STORY_ELITE_CHALLENGE_RESET_TIMES = 19;</code>
     */
    STORY_ELITE_CHALLENGE_RESET_TIMES(19),
    /**
     * <pre>
     **
     * 20.地域关卡挑战重置次数 value:times
     * </pre>
     *
     * <code>STORY_HELL_CHALLENGE_RESET_TIMES = 20;</code>
     */
    STORY_HELL_CHALLENGE_RESET_TIMES(20),
    /**
     * <pre>
     **
     * 23.军团副本挑战购买次数  value:times
     * </pre>
     *
     * <code>NATION_DUNGEON_BUY_TIMES = 23;</code>
     */
    NATION_DUNGEON_BUY_TIMES(23),
    /**
     * <pre>
     **
     * 24.夺城战购买次数 value:times
     * </pre>
     *
     * <code>NATION_WAR_BUY_TIMES = 24;</code>
     */
    NATION_WAR_BUY_TIMES(24),
    /**
     * <pre>
     **
     * 27.荣耀殿堂全服排行-购买次数
     * value:times
     * </pre>
     *
     * <code>HONOR_BUY_TIMES = 27;</code>
     */
    HONOR_BUY_TIMES(27),
    /**
     * <pre>
     **
     * 28.宝藏地精攻击双倍奖励特权 value:true
     * </pre>
     *
     * <code>GOBLIN_DOUBLE_REWARD = 28;</code>
     */
    GOBLIN_DOUBLE_REWARD(28),
    /**
     * <pre>
     **
     * 31.仙魔录购买次数上限 value:times
     * </pre>
     *
     * <code>MAGIC_RECORD_BUY_LIMIT_TIMES = 31;</code>
     */
    MAGIC_RECORD_BUY_LIMIT_TIMES(31),
    /**
     * <pre>
     **
     * 32.灭神殿重置次数
     * </pre>
     *
     * <code>DESTROY_TEMPLE_RESET_TIMES = 32;</code>
     */
    DESTROY_TEMPLE_RESET_TIMES(32),
    /**
     * <pre>
     **
     * 33.仙魔录重置次数上限 value:times
     * </pre>
     *
     * <code>MAGIC_RECORD_RESET_LIMIT_TIMES = 33;</code>
     */
    MAGIC_RECORD_RESET_LIMIT_TIMES(33),
    /**
     * <pre>
     **
     * 34.神脉开采次数  value:times
     * </pre>
     *
     * <code>EXPLOIT_PULSE_TIMES_LIMITS = 34;</code>
     */
    EXPLOIT_PULSE_TIMES_LIMITS(34),
    /**
     * <pre>
     **
     * 35.神脉占领次数上限  value:times
     * </pre>
     *
     * <code>OCCUPY_PULSE_TIMES_MAX = 35;</code>
     */
    OCCUPY_PULSE_TIMES_MAX(35),
    /**
     * <pre>
     **
     * 36.神炉开炉次数  value:times
     * </pre>
     *
     * <code>OPEN_GOD_FURNACE_TIMES = 36;</code>
     */
    OPEN_GOD_FURNACE_TIMES(36),
    /**
     * <pre>
     **
     * 37.仙境探险快速探险次数
     * </pre>
     *
     * <code>WONDERLAND_QUICK_ADVENTURE_TIMES = 37;</code>
     */
    WONDERLAND_QUICK_ADVENTURE_TIMES(37),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>PREROGATIVE_TYPE_NONE = 0;</code>
     */
    public static final int PREROGATIVE_TYPE_NONE_VALUE = 0;
    /**
     * <pre>
     **
     * 1.离线挂机奖励时间(单位:10秒) value:10second
     * </pre>
     *
     * <code>STORY_HANG_UP_OFFLINE_REWARD_TIME = 1;</code>
     */
    public static final int STORY_HANG_UP_OFFLINE_REWARD_TIME_VALUE = 1;
    /**
     * <pre>
     **
     * 2.飞升之路重置次数  value:times
     * </pre>
     *
     * <code>STAR_UP_ROAD_RESET_TIMES = 2;</code>
     */
    public static final int STAR_UP_ROAD_RESET_TIMES_VALUE = 2;
    /**
     * <pre>
     **
     * 3.试炼重置次数 value:times(两种类型同一个次数,如果需要不一样再改)
     * </pre>
     *
     * <code>ENDLESS_RESET_TIMES = 3;</code>
     */
    public static final int ENDLESS_RESET_TIMES_VALUE = 3;
    /**
     * <pre>
     **
     * 4.竞技场重置次数 value:times
     * </pre>
     *
     * <code>ARNEA_RESET_TIMES = 4;</code>
     */
    public static final int ARNEA_RESET_TIMES_VALUE = 4;
    /**
     * <pre>
     **
     * 5.摇钱树次数 value:times
     * </pre>
     *
     * <code>MONEY_TREE_TIMES = 5;</code>
     */
    public static final int MONEY_TREE_TIMES_VALUE = 5;
    /**
     * <pre>
     **
     * 6.副本购买次数 value:[[type,times],[type,times]] type:{&#64;code DungeonType}
     * {&#64;code DungeonTimesPrerogative}
     * </pre>
     *
     * <code>DUNGEON_BUY_TIMES = 6;</code>
     */
    public static final int DUNGEON_BUY_TIMES_VALUE = 6;
    /**
     * <pre>
     **
     * 7.玲珑塔重置次数  value:times
     * </pre>
     *
     * <code>PAGODA_RESET_TIMES = 7;</code>
     */
    public static final int PAGODA_RESET_TIMES_VALUE = 7;
    /**
     * <pre>
     **
     * 8.修仙巡访次数 value:times
     * </pre>
     *
     * <code>IMMORTAL_VISIT_TIMES = 8;</code>
     */
    public static final int IMMORTAL_VISIT_TIMES_VALUE = 8;
    /**
     * <pre>
     **
     * 9.挂机奖励增加万分比 value:addTenThousand
     * </pre>
     *
     * <code>HANG_UP_REWARD_ADD_TEN_THOUSAND = 9;</code>
     */
    public static final int HANG_UP_REWARD_ADD_TEN_THOUSAND_VALUE = 9;
    /**
     * <pre>
     **
     * 10.全民BOSS购买参战次数 value:times
     * </pre>
     *
     * <code>WHOLE_BOSS_BUY_TIMES = 10;</code>
     */
    public static final int WHOLE_BOSS_BUY_TIMES_VALUE = 10;
    /**
     * <pre>
     **
     * 11.增加经验万分比 value:addTenThousand
     * 目前只增加关卡,飞升之路
     * </pre>
     *
     * <code>ADD_EXP_TEN_THOUSAND = 11;</code>
     */
    public static final int ADD_EXP_TEN_THOUSAND_VALUE = 11;
    /**
     * <pre>
     **
     * 12.攻城战购买次数 value:times
     * </pre>
     *
     * <code>SIEGE_BUY_TIMES = 12;</code>
     */
    public static final int SIEGE_BUY_TIMES_VALUE = 12;
    /**
     * <pre>
     **
     * 13.镇妖台次数 value:times
     * </pre>
     *
     * <code>SUPPRESS_DEMON_TIMES = 13;</code>
     */
    public static final int SUPPRESS_DEMON_TIMES_VALUE = 13;
    /**
     * <pre>
     **
     * 14.夺旗战挑战购买次数上限 value:times
     * </pre>
     *
     * <code>FLAG_WAR_CHALLENGE_LIMIT_TIMES = 14;</code>
     */
    public static final int FLAG_WAR_CHALLENGE_LIMIT_TIMES_VALUE = 14;
    /**
     * <pre>
     **
     * 15.征途挑战购买次数上限 value:times
     * </pre>
     *
     * <code>VIE_CHALLENGE_BUY_LIMIT_TIMES = 15;</code>
     */
    public static final int VIE_CHALLENGE_BUY_LIMIT_TIMES_VALUE = 15;
    /**
     * <pre>
     **
     * 18.普通关卡挑战重置次数 value:times
     * </pre>
     *
     * <code>STORY_NORMAL_CHALLENGE_RESET_TIMES = 18;</code>
     */
    public static final int STORY_NORMAL_CHALLENGE_RESET_TIMES_VALUE = 18;
    /**
     * <pre>
     **
     * 19.精英关卡挑战重置次数 value:times
     * </pre>
     *
     * <code>STORY_ELITE_CHALLENGE_RESET_TIMES = 19;</code>
     */
    public static final int STORY_ELITE_CHALLENGE_RESET_TIMES_VALUE = 19;
    /**
     * <pre>
     **
     * 20.地域关卡挑战重置次数 value:times
     * </pre>
     *
     * <code>STORY_HELL_CHALLENGE_RESET_TIMES = 20;</code>
     */
    public static final int STORY_HELL_CHALLENGE_RESET_TIMES_VALUE = 20;
    /**
     * <pre>
     **
     * 23.军团副本挑战购买次数  value:times
     * </pre>
     *
     * <code>NATION_DUNGEON_BUY_TIMES = 23;</code>
     */
    public static final int NATION_DUNGEON_BUY_TIMES_VALUE = 23;
    /**
     * <pre>
     **
     * 24.夺城战购买次数 value:times
     * </pre>
     *
     * <code>NATION_WAR_BUY_TIMES = 24;</code>
     */
    public static final int NATION_WAR_BUY_TIMES_VALUE = 24;
    /**
     * <pre>
     **
     * 27.荣耀殿堂全服排行-购买次数
     * value:times
     * </pre>
     *
     * <code>HONOR_BUY_TIMES = 27;</code>
     */
    public static final int HONOR_BUY_TIMES_VALUE = 27;
    /**
     * <pre>
     **
     * 28.宝藏地精攻击双倍奖励特权 value:true
     * </pre>
     *
     * <code>GOBLIN_DOUBLE_REWARD = 28;</code>
     */
    public static final int GOBLIN_DOUBLE_REWARD_VALUE = 28;
    /**
     * <pre>
     **
     * 31.仙魔录购买次数上限 value:times
     * </pre>
     *
     * <code>MAGIC_RECORD_BUY_LIMIT_TIMES = 31;</code>
     */
    public static final int MAGIC_RECORD_BUY_LIMIT_TIMES_VALUE = 31;
    /**
     * <pre>
     **
     * 32.灭神殿重置次数
     * </pre>
     *
     * <code>DESTROY_TEMPLE_RESET_TIMES = 32;</code>
     */
    public static final int DESTROY_TEMPLE_RESET_TIMES_VALUE = 32;
    /**
     * <pre>
     **
     * 33.仙魔录重置次数上限 value:times
     * </pre>
     *
     * <code>MAGIC_RECORD_RESET_LIMIT_TIMES = 33;</code>
     */
    public static final int MAGIC_RECORD_RESET_LIMIT_TIMES_VALUE = 33;
    /**
     * <pre>
     **
     * 34.神脉开采次数  value:times
     * </pre>
     *
     * <code>EXPLOIT_PULSE_TIMES_LIMITS = 34;</code>
     */
    public static final int EXPLOIT_PULSE_TIMES_LIMITS_VALUE = 34;
    /**
     * <pre>
     **
     * 35.神脉占领次数上限  value:times
     * </pre>
     *
     * <code>OCCUPY_PULSE_TIMES_MAX = 35;</code>
     */
    public static final int OCCUPY_PULSE_TIMES_MAX_VALUE = 35;
    /**
     * <pre>
     **
     * 36.神炉开炉次数  value:times
     * </pre>
     *
     * <code>OPEN_GOD_FURNACE_TIMES = 36;</code>
     */
    public static final int OPEN_GOD_FURNACE_TIMES_VALUE = 36;
    /**
     * <pre>
     **
     * 37.仙境探险快速探险次数
     * </pre>
     *
     * <code>WONDERLAND_QUICK_ADVENTURE_TIMES = 37;</code>
     */
    public static final int WONDERLAND_QUICK_ADVENTURE_TIMES_VALUE = 37;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static PrerogativeType valueOf(int value) {
      return forNumber(value);
    }

    public static PrerogativeType forNumber(int value) {
      switch (value) {
        case 0: return PREROGATIVE_TYPE_NONE;
        case 1: return STORY_HANG_UP_OFFLINE_REWARD_TIME;
        case 2: return STAR_UP_ROAD_RESET_TIMES;
        case 3: return ENDLESS_RESET_TIMES;
        case 4: return ARNEA_RESET_TIMES;
        case 5: return MONEY_TREE_TIMES;
        case 6: return DUNGEON_BUY_TIMES;
        case 7: return PAGODA_RESET_TIMES;
        case 8: return IMMORTAL_VISIT_TIMES;
        case 9: return HANG_UP_REWARD_ADD_TEN_THOUSAND;
        case 10: return WHOLE_BOSS_BUY_TIMES;
        case 11: return ADD_EXP_TEN_THOUSAND;
        case 12: return SIEGE_BUY_TIMES;
        case 13: return SUPPRESS_DEMON_TIMES;
        case 14: return FLAG_WAR_CHALLENGE_LIMIT_TIMES;
        case 15: return VIE_CHALLENGE_BUY_LIMIT_TIMES;
        case 18: return STORY_NORMAL_CHALLENGE_RESET_TIMES;
        case 19: return STORY_ELITE_CHALLENGE_RESET_TIMES;
        case 20: return STORY_HELL_CHALLENGE_RESET_TIMES;
        case 23: return NATION_DUNGEON_BUY_TIMES;
        case 24: return NATION_WAR_BUY_TIMES;
        case 27: return HONOR_BUY_TIMES;
        case 28: return GOBLIN_DOUBLE_REWARD;
        case 31: return MAGIC_RECORD_BUY_LIMIT_TIMES;
        case 32: return DESTROY_TEMPLE_RESET_TIMES;
        case 33: return MAGIC_RECORD_RESET_LIMIT_TIMES;
        case 34: return EXPLOIT_PULSE_TIMES_LIMITS;
        case 35: return OCCUPY_PULSE_TIMES_MAX;
        case 36: return OPEN_GOD_FURNACE_TIMES;
        case 37: return WONDERLAND_QUICK_ADVENTURE_TIMES;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<PrerogativeType>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        PrerogativeType> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<PrerogativeType>() {
            public PrerogativeType findValueByNumber(int number) {
              return PrerogativeType.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.TypeProtocol.getDescriptor().getEnumTypes().get(7);
    }

    private static final PrerogativeType[] VALUES = values();

    public static PrerogativeType valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private PrerogativeType(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:PrerogativeType)
  }

  /**
   * Protobuf enum {@code ChargeType}
   */
  public enum ChargeType
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>CHARGE_TYPE_NONE = 0;</code>
     */
    CHARGE_TYPE_NONE(0),
    /**
     * <pre>
     ** 1.元宝商店 
     * </pre>
     *
     * <code>CHARGE_DIAMOND = 1;</code>
     */
    CHARGE_DIAMOND(1),
    /**
     * <pre>
     ** 2.直购礼包 
     * </pre>
     *
     * <code>CHARGE_DIRECT_PURCHASING = 2;</code>
     */
    CHARGE_DIRECT_PURCHASING(2),
    /**
     * <pre>
     ** 3.平台直购代金卷邮件发放 
     * </pre>
     *
     * <code>CHARGE_VOUCHER_MAIL = 3;</code>
     */
    CHARGE_VOUCHER_MAIL(3),
    /**
     * <pre>
     ** 4.平台直购礼包邮件发放 
     * </pre>
     *
     * <code>CHARGE_DIRECT_PURCHASING_MAIL = 4;</code>
     */
    CHARGE_DIRECT_PURCHASING_MAIL(4),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>CHARGE_TYPE_NONE = 0;</code>
     */
    public static final int CHARGE_TYPE_NONE_VALUE = 0;
    /**
     * <pre>
     ** 1.元宝商店 
     * </pre>
     *
     * <code>CHARGE_DIAMOND = 1;</code>
     */
    public static final int CHARGE_DIAMOND_VALUE = 1;
    /**
     * <pre>
     ** 2.直购礼包 
     * </pre>
     *
     * <code>CHARGE_DIRECT_PURCHASING = 2;</code>
     */
    public static final int CHARGE_DIRECT_PURCHASING_VALUE = 2;
    /**
     * <pre>
     ** 3.平台直购代金卷邮件发放 
     * </pre>
     *
     * <code>CHARGE_VOUCHER_MAIL = 3;</code>
     */
    public static final int CHARGE_VOUCHER_MAIL_VALUE = 3;
    /**
     * <pre>
     ** 4.平台直购礼包邮件发放 
     * </pre>
     *
     * <code>CHARGE_DIRECT_PURCHASING_MAIL = 4;</code>
     */
    public static final int CHARGE_DIRECT_PURCHASING_MAIL_VALUE = 4;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static ChargeType valueOf(int value) {
      return forNumber(value);
    }

    public static ChargeType forNumber(int value) {
      switch (value) {
        case 0: return CHARGE_TYPE_NONE;
        case 1: return CHARGE_DIAMOND;
        case 2: return CHARGE_DIRECT_PURCHASING;
        case 3: return CHARGE_VOUCHER_MAIL;
        case 4: return CHARGE_DIRECT_PURCHASING_MAIL;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<ChargeType>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        ChargeType> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<ChargeType>() {
            public ChargeType findValueByNumber(int number) {
              return ChargeType.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.TypeProtocol.getDescriptor().getEnumTypes().get(8);
    }

    private static final ChargeType[] VALUES = values();

    public static ChargeType valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private ChargeType(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:ChargeType)
  }

  /**
   * Protobuf enum {@code ActorFieldType}
   */
  public enum ActorFieldType
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>ACTOR_FIELD_TYPE_NONE = 0;</code>
     */
    ACTOR_FIELD_TYPE_NONE(0),
    /**
     * <pre>
     ** 用户ID 
     * </pre>
     *
     * <code>UID = 1;</code>
     */
    UID(1),
    /**
     * <pre>
     ** 平台ID 
     * </pre>
     *
     * <code>PLATFORM_ID = 2;</code>
     */
    PLATFORM_ID(2),
    /**
     * <pre>
     ** 渠道ID 
     * </pre>
     *
     * <code>CHANNEL = 3;</code>
     */
    CHANNEL(3),
    /**
     * <pre>
     ** 服务器ID 
     * </pre>
     *
     * <code>SERVER_ID = 4;</code>
     */
    SERVER_ID(4),
    /**
     * <pre>
     ** 角色ID 
     * </pre>
     *
     * <code>ACTOR_ID = 5;</code>
     */
    ACTOR_ID(5),
    /**
     * <pre>
     ** 角色名称 
     * </pre>
     *
     * <code>ACTOR_NAME = 6;</code>
     */
    ACTOR_NAME(6),
    /**
     * <pre>
     ** 性别 
     * </pre>
     *
     * <code>SEX = 7;</code>
     */
    SEX(7),
    /**
     * <pre>
     ** 角色等级 
     * </pre>
     *
     * <code>ACTOR_LEVEL = 8;</code>
     */
    ACTOR_LEVEL(8),
    /**
     * <pre>
     ** 角色经验 
     * </pre>
     *
     * <code>ACTOR_EXP = 9;</code>
     */
    ACTOR_EXP(9),
    /**
     * <pre>
     ** 金币 
     * </pre>
     *
     * <code>ACTOR_GOLD = 10;</code>
     */
    ACTOR_GOLD(10),
    /**
     * <pre>
     ** 钻石 
     * </pre>
     *
     * <code>ACTOR_DIAMOND = 11;</code>
     */
    ACTOR_DIAMOND(11),
    /**
     * <pre>
     ** vip等级 
     * </pre>
     *
     * <code>VIP_LEVEL = 12;</code>
     */
    VIP_LEVEL(12),
    /**
     * <pre>
     ** 头像ID 
     * </pre>
     *
     * <code>AVATAR_ID = 13;</code>
     */
    AVATAR_ID(13),
    /**
     * <pre>
     ** 新手引导ID列表 
     * </pre>
     *
     * <code>GUIDE_ID_LIST = 14;</code>
     */
    GUIDE_ID_LIST(14),
    /**
     * <pre>
     ** 充值数 
     * </pre>
     *
     * <code>RECHARGE = 15;</code>
     */
    RECHARGE(15),
    /**
     * <pre>
     ** 最后一次改名时间 
     * </pre>
     *
     * <code>LAST_RENAME_TIME = 16;</code>
     */
    LAST_RENAME_TIME(16),
    /**
     * <pre>
     ** 战力 
     * </pre>
     *
     * <code>ACTOR_POWER = 17;</code>
     */
    ACTOR_POWER(17),
    /**
     * <pre>
     ** 赠送的充值数 
     * </pre>
     *
     * <code>PRESENT = 18;</code>
     */
    PRESENT(18),
    /**
     * <pre>
     ** VIP卡信息 
     * </pre>
     *
     * <code>VIP_CARDS = 19;</code>
     */
    VIP_CARDS(19),
    /**
     * <pre>
     ** 精力 
     * </pre>
     *
     * <code>ACTOR_VIGOUR = 20;</code>
     */
    ACTOR_VIGOUR(20),
    /**
     * <pre>
     ** 体力 
     * </pre>
     *
     * <code>ACTOR_ENERGY = 21;</code>
     */
    ACTOR_ENERGY(21),
    /**
     * <pre>
     ** 头像列表 
     * </pre>
     *
     * <code>AVATAR_LIST = 22;</code>
     */
    AVATAR_LIST(22),
    /**
     * <pre>
     ** 形象列表 
     * </pre>
     *
     * <code>SHOW_LIST = 23;</code>
     */
    SHOW_LIST(23),
    /**
     * <pre>
     ** 最后一次恢复精力时间 
     * </pre>
     *
     * <code>LAST_REFRESH_VIGOUR_TIME = 24;</code>
     */
    LAST_REFRESH_VIGOUR_TIME(24),
    /**
     * <pre>
     ** 最后一次恢复体力 
     * </pre>
     *
     * <code>LAST_REFRESH_ENERGY_TIME = 25;</code>
     */
    LAST_REFRESH_ENERGY_TIME(25),
    /**
     * <pre>
     ** 用户创建时间 
     * </pre>
     *
     * <code>ACTOR_CREATE_TIME = 26;</code>
     */
    ACTOR_CREATE_TIME(26),
    /**
     * <pre>
     ** 累计登陆第几天 
     * </pre>
     *
     * <code>LOGIN_TOTAL_DAY = 27;</code>
     */
    LOGIN_TOTAL_DAY(27),
    /**
     * <pre>
     ** 形象ID 
     * </pre>
     *
     * <code>SHOW_ID = 28;</code>
     */
    SHOW_ID(28),
    /**
     * <pre>
     ** 竞技场排名(历史最高) 
     * </pre>
     *
     * <code>ARENA_MAX_RANK = 29;</code>
     */
    ARENA_MAX_RANK(29),
    /**
     * <pre>
     ** 关卡ID 
     * </pre>
     *
     * <code>STORY_ID = 30;</code>
     */
    STORY_ID(30),
    /**
     * <pre>
     ** 历史最高战力 
     * </pre>
     *
     * <code>MAX_POWER = 31;</code>
     */
    MAX_POWER(31),
    /**
     * <pre>
     ** 地宫币
     * </pre>
     *
     * <code>ACTOR_CHESS_COIN = 32;</code>
     */
    ACTOR_CHESS_COIN(32),
    /**
     * <pre>
     ** 竞技币
     * </pre>
     *
     * <code>ACTOR_ARENA_COIN = 33;</code>
     */
    ACTOR_ARENA_COIN(33),
    /**
     * <pre>
     **点赞状态
     * </pre>
     *
     * <code>PRAISE_STATE = 34;</code>
     */
    PRAISE_STATE(34),
    /**
     * <pre>
     **国家名称
     * </pre>
     *
     * <code>NATION_NAME = 35;</code>
     */
    NATION_NAME(35),
    /**
     * <pre>
     **在线状态
     * </pre>
     *
     * <code>ONLINE_STATE = 36;</code>
     */
    ONLINE_STATE(36),
    /**
     * <pre>
     **最后一次退出时间
     * </pre>
     *
     * <code>LAST_LOGOUT_TIME = 37;</code>
     */
    LAST_LOGOUT_TIME(37),
    /**
     * <pre>
     **国家旗帜
     * </pre>
     *
     * <code>NATION_FLAG = 38;</code>
     */
    NATION_FLAG(38),
    /**
     * <pre>
     **国家旗帜id
     * </pre>
     *
     * <code>NATION_FLAGID = 39;</code>
     */
    NATION_FLAGID(39),
    /**
     * <pre>
     **当前服务器ID
     * </pre>
     *
     * <code>CURRENT_SERVER_ID = 40;</code>
     */
    CURRENT_SERVER_ID(40),
    /**
     * <pre>
     **国家ID
     * </pre>
     *
     * <code>NATION_ID = 41;</code>
     */
    NATION_ID(41),
    /**
     * <pre>
     **国家等级
     * </pre>
     *
     * <code>NATION_LEVEL = 42;</code>
     */
    NATION_LEVEL(42),
    /**
     * <pre>
     ** 专武币（藏兵阁掉落）
     * </pre>
     *
     * <code>ACTOR_WEAPON_COIN = 43;</code>
     */
    ACTOR_WEAPON_COIN(43),
    /**
     * <pre>
     ** 在线组队TD币（赏金） 
     * </pre>
     *
     * <code>ACTOR_TEAM_ONLINE_TD_COIN = 44;</code>
     */
    ACTOR_TEAM_ONLINE_TD_COIN(44),
    /**
     * <pre>
     ** 个人签名 
     * </pre>
     *
     * <code>AUTOGRAPH = 45;</code>
     */
    AUTOGRAPH(45),
    /**
     * <pre>
     ** 头像框ID 
     * </pre>
     *
     * <code>AVATAR_FRAME_ID = 46;</code>
     */
    AVATAR_FRAME_ID(46),
    /**
     * <pre>
     ** 头像框列表 
     * </pre>
     *
     * <code>AVATAR_FRAME_LIST = 47;</code>
     */
    AVATAR_FRAME_LIST(47),
    /**
     * <pre>
     ** 聊天气泡ID 
     * </pre>
     *
     * <code>CHAT_BUBBLE_ID = 48;</code>
     */
    CHAT_BUBBLE_ID(48),
    /**
     * <pre>
     ** 聊天气泡框列表 
     * </pre>
     *
     * <code>CHAT_BUBBLE_LIST = 49;</code>
     */
    CHAT_BUBBLE_LIST(49),
    /**
     * <pre>
     ** 友情点
     * </pre>
     *
     * <code>ACTOR_FRIEND_POINT = 50;</code>
     */
    ACTOR_FRIEND_POINT(50),
    /**
     * <pre>
     ** 军团币 
     * </pre>
     *
     * <code>ACTOR_NATION_COIN = 51;</code>
     */
    ACTOR_NATION_COIN(51),
    /**
     * <pre>
     ** 军团经验
     * </pre>
     *
     * <code>ACTOR_NATION_EXP = 52;</code>
     */
    ACTOR_NATION_EXP(52),
    /**
     * <pre>
     ** 军团贡献
     * </pre>
     *
     * <code>ACTOR_NATION_CONTRIBUTION = 53;</code>
     */
    ACTOR_NATION_CONTRIBUTION(53),
    /**
     * <pre>
     ** VIP经验 
     * </pre>
     *
     * <code>ACTOR_VIP_EXP = 54;</code>
     */
    ACTOR_VIP_EXP(54),
    /**
     * <pre>
     **名将币（名将塔掉落）
     * </pre>
     *
     * <code>ACTOR_TOWER_COIN = 55;</code>
     */
    ACTOR_TOWER_COIN(55),
    /**
     * <pre>
     **无尽币（无尽试炼掉落）
     * </pre>
     *
     * <code>ACTOR_ENDLESS_COIN = 56;</code>
     */
    ACTOR_ENDLESS_COIN(56),
    /**
     * <pre>
     ** 剧情ID列表 
     * </pre>
     *
     * <code>PLOT_ID_LIST = 57;</code>
     */
    PLOT_ID_LIST(57),
    /**
     * <pre>
     ** 魔窟单人解锁状态 
     * </pre>
     *
     * <code>CAVE_SINGLE_CHALLENGE_LIST = 58;</code>
     */
    CAVE_SINGLE_CHALLENGE_LIST(58),
    /**
     * <pre>
     ** 战魂玉(组队魔窟掉落) 
     * </pre>
     *
     * <code>ACTOR_CAVE_COIN = 59;</code>
     */
    ACTOR_CAVE_COIN(59),
    /**
     * <pre>
     ** 官职 
     * </pre>
     *
     * <code>ACTOR_OFFICIAL = 60;</code>
     */
    ACTOR_OFFICIAL(60),
    /**
     * <pre>
     ** 攻城略地积分  
     * </pre>
     *
     * <code>ACTOR_CAPTURE_COIN = 61;</code>
     */
    ACTOR_CAPTURE_COIN(61),
    /**
     * <pre>
     ** 称号 
     * </pre>
     *
     * <code>TITLE_ID = 63;</code>
     */
    TITLE_ID(63),
    /**
     * <pre>
     ** 巅峰竞技场货币 
     * </pre>
     *
     * <code>ACTOR_CROSS_CUP_COIN = 64;</code>
     */
    ACTOR_CROSS_CUP_COIN(64),
    /**
     * <pre>
     ** 第三方帐号ID 
     * </pre>
     *
     * <code>SDK_USER_ID = 65;</code>
     */
    SDK_USER_ID(65),
    /**
     * <pre>
     ** 成长任务 
     * </pre>
     *
     * <code>GROWTH_TASK = 66;</code>
     */
    GROWTH_TASK(66),
    /**
     * <pre>
     ** 群雄币 
     * </pre>
     *
     * <code>ACTOR_HEGEMONY_COIN = 67;</code>
     */
    ACTOR_HEGEMONY_COIN(67),
    /**
     * <pre>
     ** 形象专武状态 
     * </pre>
     *
     * <code>SHOW_IMMORTALS = 68;</code>
     */
    SHOW_IMMORTALS(68),
    /**
     * <pre>
     ** 形象英雄星级（小星级） 
     * </pre>
     *
     * <code>SHOW_HERO_STAR = 69;</code>
     */
    SHOW_HERO_STAR(69),
    /**
     * <pre>
     ** 王城币 
     * </pre>
     *
     * <code>ACTOR_GVG_COIN = 70;</code>
     */
    ACTOR_GVG_COIN(70),
    /**
     * <pre>
     ** 活动31任务代币 
     * </pre>
     *
     * <code>ACTOR_ACTIVITY_31_COIN = 71;</code>
     */
    ACTOR_ACTIVITY_31_COIN(71),
    /**
     * <pre>
     ** 镖银 
     * </pre>
     *
     * <code>ACTOR_ESCORT_COIN = 72;</code>
     */
    ACTOR_ESCORT_COIN(72),
    /**
     * <pre>
     ** 棋魂 
     * </pre>
     *
     * <code>ACTOR_CHESS_2_COIN = 73;</code>
     */
    ACTOR_CHESS_2_COIN(73),
    /**
     * <pre>
     ** 战魂币 
     * </pre>
     *
     * <code>ACTOR_QINRACE_COIN = 74;</code>
     */
    ACTOR_QINRACE_COIN(74),
    /**
     * <pre>
     ** 被点赞数量 {rankType:praisedCount}
     * </pre>
     *
     * <code>PRAISED_COUNT_MAP = 75;</code>
     */
    PRAISED_COUNT_MAP(75),
    /**
     * <pre>
     ** 英雄幻彩重生次数 
     * </pre>
     *
     * <code>HERO_COLOR_REBIRTH_TIMES = 76;</code>
     */
    HERO_COLOR_REBIRTH_TIMES(76),
    /**
     * <pre>
     ** 专武幻彩重生次数 
     * </pre>
     *
     * <code>IMMORTALS_COLOR_REBIRTH_TIMES = 77;</code>
     */
    IMMORTALS_COLOR_REBIRTH_TIMES(77),
    /**
     * <pre>
     ** showId幻彩等级 
     * </pre>
     *
     * <code>SHOW_HERO_COLOR_LEVEL = 78;</code>
     */
    SHOW_HERO_COLOR_LEVEL(78),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>ACTOR_FIELD_TYPE_NONE = 0;</code>
     */
    public static final int ACTOR_FIELD_TYPE_NONE_VALUE = 0;
    /**
     * <pre>
     ** 用户ID 
     * </pre>
     *
     * <code>UID = 1;</code>
     */
    public static final int UID_VALUE = 1;
    /**
     * <pre>
     ** 平台ID 
     * </pre>
     *
     * <code>PLATFORM_ID = 2;</code>
     */
    public static final int PLATFORM_ID_VALUE = 2;
    /**
     * <pre>
     ** 渠道ID 
     * </pre>
     *
     * <code>CHANNEL = 3;</code>
     */
    public static final int CHANNEL_VALUE = 3;
    /**
     * <pre>
     ** 服务器ID 
     * </pre>
     *
     * <code>SERVER_ID = 4;</code>
     */
    public static final int SERVER_ID_VALUE = 4;
    /**
     * <pre>
     ** 角色ID 
     * </pre>
     *
     * <code>ACTOR_ID = 5;</code>
     */
    public static final int ACTOR_ID_VALUE = 5;
    /**
     * <pre>
     ** 角色名称 
     * </pre>
     *
     * <code>ACTOR_NAME = 6;</code>
     */
    public static final int ACTOR_NAME_VALUE = 6;
    /**
     * <pre>
     ** 性别 
     * </pre>
     *
     * <code>SEX = 7;</code>
     */
    public static final int SEX_VALUE = 7;
    /**
     * <pre>
     ** 角色等级 
     * </pre>
     *
     * <code>ACTOR_LEVEL = 8;</code>
     */
    public static final int ACTOR_LEVEL_VALUE = 8;
    /**
     * <pre>
     ** 角色经验 
     * </pre>
     *
     * <code>ACTOR_EXP = 9;</code>
     */
    public static final int ACTOR_EXP_VALUE = 9;
    /**
     * <pre>
     ** 金币 
     * </pre>
     *
     * <code>ACTOR_GOLD = 10;</code>
     */
    public static final int ACTOR_GOLD_VALUE = 10;
    /**
     * <pre>
     ** 钻石 
     * </pre>
     *
     * <code>ACTOR_DIAMOND = 11;</code>
     */
    public static final int ACTOR_DIAMOND_VALUE = 11;
    /**
     * <pre>
     ** vip等级 
     * </pre>
     *
     * <code>VIP_LEVEL = 12;</code>
     */
    public static final int VIP_LEVEL_VALUE = 12;
    /**
     * <pre>
     ** 头像ID 
     * </pre>
     *
     * <code>AVATAR_ID = 13;</code>
     */
    public static final int AVATAR_ID_VALUE = 13;
    /**
     * <pre>
     ** 新手引导ID列表 
     * </pre>
     *
     * <code>GUIDE_ID_LIST = 14;</code>
     */
    public static final int GUIDE_ID_LIST_VALUE = 14;
    /**
     * <pre>
     ** 充值数 
     * </pre>
     *
     * <code>RECHARGE = 15;</code>
     */
    public static final int RECHARGE_VALUE = 15;
    /**
     * <pre>
     ** 最后一次改名时间 
     * </pre>
     *
     * <code>LAST_RENAME_TIME = 16;</code>
     */
    public static final int LAST_RENAME_TIME_VALUE = 16;
    /**
     * <pre>
     ** 战力 
     * </pre>
     *
     * <code>ACTOR_POWER = 17;</code>
     */
    public static final int ACTOR_POWER_VALUE = 17;
    /**
     * <pre>
     ** 赠送的充值数 
     * </pre>
     *
     * <code>PRESENT = 18;</code>
     */
    public static final int PRESENT_VALUE = 18;
    /**
     * <pre>
     ** VIP卡信息 
     * </pre>
     *
     * <code>VIP_CARDS = 19;</code>
     */
    public static final int VIP_CARDS_VALUE = 19;
    /**
     * <pre>
     ** 精力 
     * </pre>
     *
     * <code>ACTOR_VIGOUR = 20;</code>
     */
    public static final int ACTOR_VIGOUR_VALUE = 20;
    /**
     * <pre>
     ** 体力 
     * </pre>
     *
     * <code>ACTOR_ENERGY = 21;</code>
     */
    public static final int ACTOR_ENERGY_VALUE = 21;
    /**
     * <pre>
     ** 头像列表 
     * </pre>
     *
     * <code>AVATAR_LIST = 22;</code>
     */
    public static final int AVATAR_LIST_VALUE = 22;
    /**
     * <pre>
     ** 形象列表 
     * </pre>
     *
     * <code>SHOW_LIST = 23;</code>
     */
    public static final int SHOW_LIST_VALUE = 23;
    /**
     * <pre>
     ** 最后一次恢复精力时间 
     * </pre>
     *
     * <code>LAST_REFRESH_VIGOUR_TIME = 24;</code>
     */
    public static final int LAST_REFRESH_VIGOUR_TIME_VALUE = 24;
    /**
     * <pre>
     ** 最后一次恢复体力 
     * </pre>
     *
     * <code>LAST_REFRESH_ENERGY_TIME = 25;</code>
     */
    public static final int LAST_REFRESH_ENERGY_TIME_VALUE = 25;
    /**
     * <pre>
     ** 用户创建时间 
     * </pre>
     *
     * <code>ACTOR_CREATE_TIME = 26;</code>
     */
    public static final int ACTOR_CREATE_TIME_VALUE = 26;
    /**
     * <pre>
     ** 累计登陆第几天 
     * </pre>
     *
     * <code>LOGIN_TOTAL_DAY = 27;</code>
     */
    public static final int LOGIN_TOTAL_DAY_VALUE = 27;
    /**
     * <pre>
     ** 形象ID 
     * </pre>
     *
     * <code>SHOW_ID = 28;</code>
     */
    public static final int SHOW_ID_VALUE = 28;
    /**
     * <pre>
     ** 竞技场排名(历史最高) 
     * </pre>
     *
     * <code>ARENA_MAX_RANK = 29;</code>
     */
    public static final int ARENA_MAX_RANK_VALUE = 29;
    /**
     * <pre>
     ** 关卡ID 
     * </pre>
     *
     * <code>STORY_ID = 30;</code>
     */
    public static final int STORY_ID_VALUE = 30;
    /**
     * <pre>
     ** 历史最高战力 
     * </pre>
     *
     * <code>MAX_POWER = 31;</code>
     */
    public static final int MAX_POWER_VALUE = 31;
    /**
     * <pre>
     ** 地宫币
     * </pre>
     *
     * <code>ACTOR_CHESS_COIN = 32;</code>
     */
    public static final int ACTOR_CHESS_COIN_VALUE = 32;
    /**
     * <pre>
     ** 竞技币
     * </pre>
     *
     * <code>ACTOR_ARENA_COIN = 33;</code>
     */
    public static final int ACTOR_ARENA_COIN_VALUE = 33;
    /**
     * <pre>
     **点赞状态
     * </pre>
     *
     * <code>PRAISE_STATE = 34;</code>
     */
    public static final int PRAISE_STATE_VALUE = 34;
    /**
     * <pre>
     **国家名称
     * </pre>
     *
     * <code>NATION_NAME = 35;</code>
     */
    public static final int NATION_NAME_VALUE = 35;
    /**
     * <pre>
     **在线状态
     * </pre>
     *
     * <code>ONLINE_STATE = 36;</code>
     */
    public static final int ONLINE_STATE_VALUE = 36;
    /**
     * <pre>
     **最后一次退出时间
     * </pre>
     *
     * <code>LAST_LOGOUT_TIME = 37;</code>
     */
    public static final int LAST_LOGOUT_TIME_VALUE = 37;
    /**
     * <pre>
     **国家旗帜
     * </pre>
     *
     * <code>NATION_FLAG = 38;</code>
     */
    public static final int NATION_FLAG_VALUE = 38;
    /**
     * <pre>
     **国家旗帜id
     * </pre>
     *
     * <code>NATION_FLAGID = 39;</code>
     */
    public static final int NATION_FLAGID_VALUE = 39;
    /**
     * <pre>
     **当前服务器ID
     * </pre>
     *
     * <code>CURRENT_SERVER_ID = 40;</code>
     */
    public static final int CURRENT_SERVER_ID_VALUE = 40;
    /**
     * <pre>
     **国家ID
     * </pre>
     *
     * <code>NATION_ID = 41;</code>
     */
    public static final int NATION_ID_VALUE = 41;
    /**
     * <pre>
     **国家等级
     * </pre>
     *
     * <code>NATION_LEVEL = 42;</code>
     */
    public static final int NATION_LEVEL_VALUE = 42;
    /**
     * <pre>
     ** 专武币（藏兵阁掉落）
     * </pre>
     *
     * <code>ACTOR_WEAPON_COIN = 43;</code>
     */
    public static final int ACTOR_WEAPON_COIN_VALUE = 43;
    /**
     * <pre>
     ** 在线组队TD币（赏金） 
     * </pre>
     *
     * <code>ACTOR_TEAM_ONLINE_TD_COIN = 44;</code>
     */
    public static final int ACTOR_TEAM_ONLINE_TD_COIN_VALUE = 44;
    /**
     * <pre>
     ** 个人签名 
     * </pre>
     *
     * <code>AUTOGRAPH = 45;</code>
     */
    public static final int AUTOGRAPH_VALUE = 45;
    /**
     * <pre>
     ** 头像框ID 
     * </pre>
     *
     * <code>AVATAR_FRAME_ID = 46;</code>
     */
    public static final int AVATAR_FRAME_ID_VALUE = 46;
    /**
     * <pre>
     ** 头像框列表 
     * </pre>
     *
     * <code>AVATAR_FRAME_LIST = 47;</code>
     */
    public static final int AVATAR_FRAME_LIST_VALUE = 47;
    /**
     * <pre>
     ** 聊天气泡ID 
     * </pre>
     *
     * <code>CHAT_BUBBLE_ID = 48;</code>
     */
    public static final int CHAT_BUBBLE_ID_VALUE = 48;
    /**
     * <pre>
     ** 聊天气泡框列表 
     * </pre>
     *
     * <code>CHAT_BUBBLE_LIST = 49;</code>
     */
    public static final int CHAT_BUBBLE_LIST_VALUE = 49;
    /**
     * <pre>
     ** 友情点
     * </pre>
     *
     * <code>ACTOR_FRIEND_POINT = 50;</code>
     */
    public static final int ACTOR_FRIEND_POINT_VALUE = 50;
    /**
     * <pre>
     ** 军团币 
     * </pre>
     *
     * <code>ACTOR_NATION_COIN = 51;</code>
     */
    public static final int ACTOR_NATION_COIN_VALUE = 51;
    /**
     * <pre>
     ** 军团经验
     * </pre>
     *
     * <code>ACTOR_NATION_EXP = 52;</code>
     */
    public static final int ACTOR_NATION_EXP_VALUE = 52;
    /**
     * <pre>
     ** 军团贡献
     * </pre>
     *
     * <code>ACTOR_NATION_CONTRIBUTION = 53;</code>
     */
    public static final int ACTOR_NATION_CONTRIBUTION_VALUE = 53;
    /**
     * <pre>
     ** VIP经验 
     * </pre>
     *
     * <code>ACTOR_VIP_EXP = 54;</code>
     */
    public static final int ACTOR_VIP_EXP_VALUE = 54;
    /**
     * <pre>
     **名将币（名将塔掉落）
     * </pre>
     *
     * <code>ACTOR_TOWER_COIN = 55;</code>
     */
    public static final int ACTOR_TOWER_COIN_VALUE = 55;
    /**
     * <pre>
     **无尽币（无尽试炼掉落）
     * </pre>
     *
     * <code>ACTOR_ENDLESS_COIN = 56;</code>
     */
    public static final int ACTOR_ENDLESS_COIN_VALUE = 56;
    /**
     * <pre>
     ** 剧情ID列表 
     * </pre>
     *
     * <code>PLOT_ID_LIST = 57;</code>
     */
    public static final int PLOT_ID_LIST_VALUE = 57;
    /**
     * <pre>
     ** 魔窟单人解锁状态 
     * </pre>
     *
     * <code>CAVE_SINGLE_CHALLENGE_LIST = 58;</code>
     */
    public static final int CAVE_SINGLE_CHALLENGE_LIST_VALUE = 58;
    /**
     * <pre>
     ** 战魂玉(组队魔窟掉落) 
     * </pre>
     *
     * <code>ACTOR_CAVE_COIN = 59;</code>
     */
    public static final int ACTOR_CAVE_COIN_VALUE = 59;
    /**
     * <pre>
     ** 官职 
     * </pre>
     *
     * <code>ACTOR_OFFICIAL = 60;</code>
     */
    public static final int ACTOR_OFFICIAL_VALUE = 60;
    /**
     * <pre>
     ** 攻城略地积分  
     * </pre>
     *
     * <code>ACTOR_CAPTURE_COIN = 61;</code>
     */
    public static final int ACTOR_CAPTURE_COIN_VALUE = 61;
    /**
     * <pre>
     ** 称号 
     * </pre>
     *
     * <code>TITLE_ID = 63;</code>
     */
    public static final int TITLE_ID_VALUE = 63;
    /**
     * <pre>
     ** 巅峰竞技场货币 
     * </pre>
     *
     * <code>ACTOR_CROSS_CUP_COIN = 64;</code>
     */
    public static final int ACTOR_CROSS_CUP_COIN_VALUE = 64;
    /**
     * <pre>
     ** 第三方帐号ID 
     * </pre>
     *
     * <code>SDK_USER_ID = 65;</code>
     */
    public static final int SDK_USER_ID_VALUE = 65;
    /**
     * <pre>
     ** 成长任务 
     * </pre>
     *
     * <code>GROWTH_TASK = 66;</code>
     */
    public static final int GROWTH_TASK_VALUE = 66;
    /**
     * <pre>
     ** 群雄币 
     * </pre>
     *
     * <code>ACTOR_HEGEMONY_COIN = 67;</code>
     */
    public static final int ACTOR_HEGEMONY_COIN_VALUE = 67;
    /**
     * <pre>
     ** 形象专武状态 
     * </pre>
     *
     * <code>SHOW_IMMORTALS = 68;</code>
     */
    public static final int SHOW_IMMORTALS_VALUE = 68;
    /**
     * <pre>
     ** 形象英雄星级（小星级） 
     * </pre>
     *
     * <code>SHOW_HERO_STAR = 69;</code>
     */
    public static final int SHOW_HERO_STAR_VALUE = 69;
    /**
     * <pre>
     ** 王城币 
     * </pre>
     *
     * <code>ACTOR_GVG_COIN = 70;</code>
     */
    public static final int ACTOR_GVG_COIN_VALUE = 70;
    /**
     * <pre>
     ** 活动31任务代币 
     * </pre>
     *
     * <code>ACTOR_ACTIVITY_31_COIN = 71;</code>
     */
    public static final int ACTOR_ACTIVITY_31_COIN_VALUE = 71;
    /**
     * <pre>
     ** 镖银 
     * </pre>
     *
     * <code>ACTOR_ESCORT_COIN = 72;</code>
     */
    public static final int ACTOR_ESCORT_COIN_VALUE = 72;
    /**
     * <pre>
     ** 棋魂 
     * </pre>
     *
     * <code>ACTOR_CHESS_2_COIN = 73;</code>
     */
    public static final int ACTOR_CHESS_2_COIN_VALUE = 73;
    /**
     * <pre>
     ** 战魂币 
     * </pre>
     *
     * <code>ACTOR_QINRACE_COIN = 74;</code>
     */
    public static final int ACTOR_QINRACE_COIN_VALUE = 74;
    /**
     * <pre>
     ** 被点赞数量 {rankType:praisedCount}
     * </pre>
     *
     * <code>PRAISED_COUNT_MAP = 75;</code>
     */
    public static final int PRAISED_COUNT_MAP_VALUE = 75;
    /**
     * <pre>
     ** 英雄幻彩重生次数 
     * </pre>
     *
     * <code>HERO_COLOR_REBIRTH_TIMES = 76;</code>
     */
    public static final int HERO_COLOR_REBIRTH_TIMES_VALUE = 76;
    /**
     * <pre>
     ** 专武幻彩重生次数 
     * </pre>
     *
     * <code>IMMORTALS_COLOR_REBIRTH_TIMES = 77;</code>
     */
    public static final int IMMORTALS_COLOR_REBIRTH_TIMES_VALUE = 77;
    /**
     * <pre>
     ** showId幻彩等级 
     * </pre>
     *
     * <code>SHOW_HERO_COLOR_LEVEL = 78;</code>
     */
    public static final int SHOW_HERO_COLOR_LEVEL_VALUE = 78;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static ActorFieldType valueOf(int value) {
      return forNumber(value);
    }

    public static ActorFieldType forNumber(int value) {
      switch (value) {
        case 0: return ACTOR_FIELD_TYPE_NONE;
        case 1: return UID;
        case 2: return PLATFORM_ID;
        case 3: return CHANNEL;
        case 4: return SERVER_ID;
        case 5: return ACTOR_ID;
        case 6: return ACTOR_NAME;
        case 7: return SEX;
        case 8: return ACTOR_LEVEL;
        case 9: return ACTOR_EXP;
        case 10: return ACTOR_GOLD;
        case 11: return ACTOR_DIAMOND;
        case 12: return VIP_LEVEL;
        case 13: return AVATAR_ID;
        case 14: return GUIDE_ID_LIST;
        case 15: return RECHARGE;
        case 16: return LAST_RENAME_TIME;
        case 17: return ACTOR_POWER;
        case 18: return PRESENT;
        case 19: return VIP_CARDS;
        case 20: return ACTOR_VIGOUR;
        case 21: return ACTOR_ENERGY;
        case 22: return AVATAR_LIST;
        case 23: return SHOW_LIST;
        case 24: return LAST_REFRESH_VIGOUR_TIME;
        case 25: return LAST_REFRESH_ENERGY_TIME;
        case 26: return ACTOR_CREATE_TIME;
        case 27: return LOGIN_TOTAL_DAY;
        case 28: return SHOW_ID;
        case 29: return ARENA_MAX_RANK;
        case 30: return STORY_ID;
        case 31: return MAX_POWER;
        case 32: return ACTOR_CHESS_COIN;
        case 33: return ACTOR_ARENA_COIN;
        case 34: return PRAISE_STATE;
        case 35: return NATION_NAME;
        case 36: return ONLINE_STATE;
        case 37: return LAST_LOGOUT_TIME;
        case 38: return NATION_FLAG;
        case 39: return NATION_FLAGID;
        case 40: return CURRENT_SERVER_ID;
        case 41: return NATION_ID;
        case 42: return NATION_LEVEL;
        case 43: return ACTOR_WEAPON_COIN;
        case 44: return ACTOR_TEAM_ONLINE_TD_COIN;
        case 45: return AUTOGRAPH;
        case 46: return AVATAR_FRAME_ID;
        case 47: return AVATAR_FRAME_LIST;
        case 48: return CHAT_BUBBLE_ID;
        case 49: return CHAT_BUBBLE_LIST;
        case 50: return ACTOR_FRIEND_POINT;
        case 51: return ACTOR_NATION_COIN;
        case 52: return ACTOR_NATION_EXP;
        case 53: return ACTOR_NATION_CONTRIBUTION;
        case 54: return ACTOR_VIP_EXP;
        case 55: return ACTOR_TOWER_COIN;
        case 56: return ACTOR_ENDLESS_COIN;
        case 57: return PLOT_ID_LIST;
        case 58: return CAVE_SINGLE_CHALLENGE_LIST;
        case 59: return ACTOR_CAVE_COIN;
        case 60: return ACTOR_OFFICIAL;
        case 61: return ACTOR_CAPTURE_COIN;
        case 63: return TITLE_ID;
        case 64: return ACTOR_CROSS_CUP_COIN;
        case 65: return SDK_USER_ID;
        case 66: return GROWTH_TASK;
        case 67: return ACTOR_HEGEMONY_COIN;
        case 68: return SHOW_IMMORTALS;
        case 69: return SHOW_HERO_STAR;
        case 70: return ACTOR_GVG_COIN;
        case 71: return ACTOR_ACTIVITY_31_COIN;
        case 72: return ACTOR_ESCORT_COIN;
        case 73: return ACTOR_CHESS_2_COIN;
        case 74: return ACTOR_QINRACE_COIN;
        case 75: return PRAISED_COUNT_MAP;
        case 76: return HERO_COLOR_REBIRTH_TIMES;
        case 77: return IMMORTALS_COLOR_REBIRTH_TIMES;
        case 78: return SHOW_HERO_COLOR_LEVEL;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<ActorFieldType>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        ActorFieldType> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<ActorFieldType>() {
            public ActorFieldType findValueByNumber(int number) {
              return ActorFieldType.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.TypeProtocol.getDescriptor().getEnumTypes().get(9);
    }

    private static final ActorFieldType[] VALUES = values();

    public static ActorFieldType valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private ActorFieldType(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:ActorFieldType)
  }

  /**
   * <pre>
   ** 宝物抢夺记录状态 
   * </pre>
   *
   * Protobuf enum {@code TreasureSnatchRecordState}
   */
  public enum TreasureSnatchRecordState
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>TREASURE_SNATCH_RECORD_STATE_NON = 0;</code>
     */
    TREASURE_SNATCH_RECORD_STATE_NON(0),
    /**
     * <pre>
     ** 1.掠夺 
     * </pre>
     *
     * <code>SNATCH = 1;</code>
     */
    SNATCH(1),
    /**
     * <pre>
     ** 2.复仇 
     * </pre>
     *
     * <code>REVENGE = 2;</code>
     */
    REVENGE(2),
    /**
     * <pre>
     ** 3.完成复仇 
     * </pre>
     *
     * <code>REVENGE_COMPLETE = 3;</code>
     */
    REVENGE_COMPLETE(3),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>TREASURE_SNATCH_RECORD_STATE_NON = 0;</code>
     */
    public static final int TREASURE_SNATCH_RECORD_STATE_NON_VALUE = 0;
    /**
     * <pre>
     ** 1.掠夺 
     * </pre>
     *
     * <code>SNATCH = 1;</code>
     */
    public static final int SNATCH_VALUE = 1;
    /**
     * <pre>
     ** 2.复仇 
     * </pre>
     *
     * <code>REVENGE = 2;</code>
     */
    public static final int REVENGE_VALUE = 2;
    /**
     * <pre>
     ** 3.完成复仇 
     * </pre>
     *
     * <code>REVENGE_COMPLETE = 3;</code>
     */
    public static final int REVENGE_COMPLETE_VALUE = 3;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static TreasureSnatchRecordState valueOf(int value) {
      return forNumber(value);
    }

    public static TreasureSnatchRecordState forNumber(int value) {
      switch (value) {
        case 0: return TREASURE_SNATCH_RECORD_STATE_NON;
        case 1: return SNATCH;
        case 2: return REVENGE;
        case 3: return REVENGE_COMPLETE;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<TreasureSnatchRecordState>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        TreasureSnatchRecordState> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<TreasureSnatchRecordState>() {
            public TreasureSnatchRecordState findValueByNumber(int number) {
              return TreasureSnatchRecordState.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.TypeProtocol.getDescriptor().getEnumTypes().get(10);
    }

    private static final TreasureSnatchRecordState[] VALUES = values();

    public static TreasureSnatchRecordState valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private TreasureSnatchRecordState(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:TreasureSnatchRecordState)
  }

  /**
   * Protobuf enum {@code TowerType}
   */
  public enum TowerType
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <pre>
     ** 0.NONE
     * </pre>
     *
     * <code>NONE = 0;</code>
     */
    NONE(0),
    /**
     * <pre>
     ** 1.A类型塔
     * </pre>
     *
     * <code>ATYPE = 1;</code>
     */
    ATYPE(1),
    UNRECOGNIZED(-1),
    ;

    /**
     * <pre>
     ** 0.NONE
     * </pre>
     *
     * <code>NONE = 0;</code>
     */
    public static final int NONE_VALUE = 0;
    /**
     * <pre>
     ** 1.A类型塔
     * </pre>
     *
     * <code>ATYPE = 1;</code>
     */
    public static final int ATYPE_VALUE = 1;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static TowerType valueOf(int value) {
      return forNumber(value);
    }

    public static TowerType forNumber(int value) {
      switch (value) {
        case 0: return NONE;
        case 1: return ATYPE;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<TowerType>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        TowerType> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<TowerType>() {
            public TowerType findValueByNumber(int number) {
              return TowerType.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.TypeProtocol.getDescriptor().getEnumTypes().get(11);
    }

    private static final TowerType[] VALUES = values();

    public static TowerType valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private TowerType(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:TowerType)
  }

  /**
   * Protobuf enum {@code ChatInfoType}
   */
  public enum ChatInfoType
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <pre>
     ** NONE 
     * </pre>
     *
     * <code>CHAT_INFO_NONE = 0;</code>
     */
    CHAT_INFO_NONE(0),
    /**
     * <pre>
     **
     * 1.获得英雄
     * x1:operationType
     * x2:quality
     * x3:heroId
     * (x1==101||x1==102)&amp;&amp;(x2&gt;=4)
     * </pre>
     *
     * <code>HERO_GET_CHAT_INFO = 1;</code>
     */
    HERO_GET_CHAT_INFO(1),
    /**
     * <pre>
     **
     * 2.获得物品
     * x1:operationType
     * x2:goodsId
     * </pre>
     *
     * <code>GOODS_GET_CHAT_INFO = 2;</code>
     */
    GOODS_GET_CHAT_INFO(2),
    /**
     * <pre>
     **
     * 3.获得装备
     * x1:operationType
     * x2:equipmentId
     * x3:quality
     * </pre>
     *
     * <code>EQUIPMENT_GET_CHAT_INFO = 3;</code>
     */
    EQUIPMENT_GET_CHAT_INFO(3),
    /**
     * <pre>
     **
     * 4.获得宝物
     * x1:operationType
     * x2:treasureId
     * x3:quality
     * </pre>
     *
     * <code>TREASURE_GET_CHAT_INFO = 4;</code>
     */
    TREASURE_GET_CHAT_INFO(4),
    /**
     * <pre>
     **
     * 5.10连招募获得两个和以上
     * x1:quality
     * x2:heroId
     * (x1==101||x1==102)&amp;&amp;(x2&gt;=4)
     * </pre>
     *
     * <code>GACHA_HERO_GET_CHAT_INFO = 5;</code>
     */
    GACHA_HERO_GET_CHAT_INFO(5),
    /**
     * <pre>
     **
     * 6.战力排行榜前X名登陆
     * x1:排名
     * </pre>
     *
     * <code>POWER_TOP_RANK_LOGIN_CHAT_INFO = 6;</code>
     */
    POWER_TOP_RANK_LOGIN_CHAT_INFO(6),
    /**
     * <pre>
     **
     * 7.竞技场排行前X名登陆
     * x1:排名
     * </pre>
     *
     * <code>ARENA_TOP_RANK_LOGIN_CHAT_INFO = 7;</code>
     */
    ARENA_TOP_RANK_LOGIN_CHAT_INFO(7),
    /**
     * <pre>
     **
     * 8.英雄升星消息
     * x1:quality
     * x2:starLevel（hero_star_config配置中）
     * x3:heroId
     * </pre>
     *
     * <code>HERO_STAR_UP_CHAT_INFO = 8;</code>
     */
    HERO_STAR_UP_CHAT_INFO(8),
    /**
     * <pre>
     **
     * 9.天魔触发
     * x1:天魔类型（1.攻击，2.防守）
     * </pre>
     *
     * <code>CAVE_HARD_TRIGGER_CHAT_INFO = 9;</code>
     */
    CAVE_HARD_TRIGGER_CHAT_INFO(9),
    /**
     * <pre>
     **
     * 10.加入军团
     * </pre>
     *
     * <code>NATION_JOIN_CHAT_INFO = 10;</code>
     */
    NATION_JOIN_CHAT_INFO(10),
    /**
     * <pre>
     **
     * 11.离开军团
     * </pre>
     *
     * <code>NATION_EXIT_CHAT_INFO = 11;</code>
     */
    NATION_EXIT_CHAT_INFO(11),
    /**
     * <pre>
     **
     * 12.官职
     * </pre>
     *
     * <code>OFFICIAL_CHAT_INFO = 12;</code>
     */
    OFFICIAL_CHAT_INFO(12),
    /**
     * <pre>
     **
     * 13.远古棋局赛季首次达到指定段位
     * </pre>
     *
     * <code>QIN_DUNGEON_DAN_CHAT_INFO = 13;</code>
     */
    QIN_DUNGEON_DAN_CHAT_INFO(13),
    /**
     * <pre>
     **
     * 14.神器获得
     * </pre>
     *
     * <code>ANTIQUE_GET_CHAT_INFO = 14;</code>
     */
    ANTIQUE_GET_CHAT_INFO(14),
    /**
     * <pre>
     **
     * 15.激活珍宝情缘
     * </pre>
     *
     * <code>ANTIQUE_RELATION_ACTIVATE_CHAT_INFO = 15;</code>
     */
    ANTIQUE_RELATION_ACTIVATE_CHAT_INFO(15),
    /**
     * <pre>
     **
     * 16.升级珍宝情缘
     * </pre>
     *
     * <code>ANTIQUE_RELATION_UPGRADE_CHAT_INFO = 16;</code>
     */
    ANTIQUE_RELATION_UPGRADE_CHAT_INFO(16),
    /**
     * <pre>
     **
     * 17.幻化专武
     * </pre>
     *
     * <code>IMMORTALS_ACTIVATE_CHAT_INFO = 17;</code>
     */
    IMMORTALS_ACTIVATE_CHAT_INFO(17),
    /**
     * <pre>
     **
     * 18.专武升星到特定星级
     * </pre>
     *
     * <code>IMMORTALS_STAR_CHAT_INFO = 18;</code>
     */
    IMMORTALS_STAR_CHAT_INFO(18),
    /**
     * <pre>
     **
     * 19.名将塔特定层次
     * </pre>
     *
     * <code>TOWER_LAYER_CHAT_INFO = 19;</code>
     */
    TOWER_LAYER_CHAT_INFO(19),
    /**
     * <pre>
     **
     * 20.无尽试炼首次特定层次
     * </pre>
     *
     * <code>BATTLEFIELD_DRILL_CHALLENGE_CHAT_INFO = 20;</code>
     */
    BATTLEFIELD_DRILL_CHALLENGE_CHAT_INFO(20),
    /**
     * <pre>
     **
     * 21.竞技场达到历史排名
     * x1:历史最高名次
     * </pre>
     *
     * <code>ARENA_TOP_RANK_HISTORY_CHAT_INFO = 21;</code>
     */
    ARENA_TOP_RANK_HISTORY_CHAT_INFO(21),
    /**
     * <pre>
     **
     * 22.获得专武
     * x1:operationType
     * x2:quality
     * x3:immortalsSkinId
     * </pre>
     *
     * <code>IMMORTAL_GET_CHAT_INFO = 22;</code>
     */
    IMMORTAL_GET_CHAT_INFO(22),
    /**
     * <pre>
     **
     * 23.获得货币类型
     * x1:operationType
     * x2:goodsId
     * x3:number
     * </pre>
     *
     * <code>RESOURCE_GET_CHAT_INFO = 23;</code>
     */
    RESOURCE_GET_CHAT_INFO(23),
    /**
     * <pre>
     **
     * 24.每日抽奖跑马灯
     * </pre>
     *
     * <code>DAILY_DRAW_REWARD = 24;</code>
     */
    DAILY_DRAW_REWARD(24),
    /**
     * <pre>
     **
     * 25.天官赐福跑马灯
     * </pre>
     *
     * <code>ACTIVITY_21_CHAT_INFO = 25;</code>
     */
    ACTIVITY_21_CHAT_INFO(25),
    /**
     * <pre>
     **
     * 26.摘星揽月建造
     * </pre>
     *
     * <code>ACTIVITY_31_GOLD_HOUSE = 26;</code>
     */
    ACTIVITY_31_GOLD_HOUSE(26),
    /**
     * <pre>
     **
     * 27.专武转盘中奖跑马灯
     * </pre>
     *
     * <code>IMMORTALS_TURNTABLE_WINNING = 27;</code>
     */
    IMMORTALS_TURNTABLE_WINNING(27),
    /**
     * <pre>
     **
     * 28.迎春新象
     * </pre>
     *
     * <code>ACTIVITY_33_CHAT_INFO = 28;</code>
     */
    ACTIVITY_33_CHAT_INFO(28),
    /**
     * <pre>
     **
     * 29.兵符鉴定跑马灯
     * </pre>
     *
     * <code>RUNES_APPRAISE_CHAT_INFO = 29;</code>
     */
    RUNES_APPRAISE_CHAT_INFO(29),
    /**
     * <pre>
     **
     * 30.平定天下转盘跑马灯
     * </pre>
     *
     * <code>REPUTATION_TURNTABLE_CHAT_INFO = 30;</code>
     */
    REPUTATION_TURNTABLE_CHAT_INFO(30),
    /**
     * <pre>
     **
     * 31.获得神兽
     * x1:operationType
     * x2:quality
     * x3:beastId
     * </pre>
     *
     * <code>BEAST_GET_CHAT_INFO = 31;</code>
     */
    BEAST_GET_CHAT_INFO(31),
    /**
     * <pre>
     **
     * 32.神兽升星
     * x1:星级
     * </pre>
     *
     * <code>BEAST_STAR_UP_CHAT_INFO = 32;</code>
     */
    BEAST_STAR_UP_CHAT_INFO(32),
    /**
     * <pre>
     **
     * 33.神兽图腾进阶
     * x1:阶级
     * </pre>
     *
     * <code>BEAST_TOTEM_ADVANCE_INFO = 33;</code>
     */
    BEAST_TOTEM_ADVANCE_INFO(33),
    /**
     * <pre>
     **
     * 34.神兽图腾激活
     * </pre>
     *
     * <code>BEAST_TOTEM_ACTIVATE_INFO = 34;</code>
     */
    BEAST_TOTEM_ACTIVATE_INFO(34),
    /**
     * <pre>
     **
     * 35.时空红颜合成激活
     * </pre>
     *
     * <code>SPACETIME_BEAUTY_ACTIVATE_INFO = 35;</code>
     */
    SPACETIME_BEAUTY_ACTIVATE_INFO(35),
    /**
     * <pre>
     **
     * 36.时空红颜美名提升
     * </pre>
     *
     * <code>SPACETIME_BEAUTY_LEVEL_UP_INFO = 36;</code>
     */
    SPACETIME_BEAUTY_LEVEL_UP_INFO(36),
    /**
     * <pre>
     **
     * 36.时空红颜觉醒升星
     * </pre>
     *
     * <code>SPACETIME_BEAUTY_AWAKEN_INFO = 37;</code>
     */
    SPACETIME_BEAUTY_AWAKEN_INFO(37),
    /**
     * <pre>
     **
     * 38.珍珑宝库转盘跑马灯
     * </pre>
     *
     * <code>CHESS_TURNTABLE_CHAT_INFO = 38;</code>
     */
    CHESS_TURNTABLE_CHAT_INFO(38),
    /**
     * <pre>
     **
     * 39.获得神器跑马灯
     * </pre>
     *
     * <code>HALLOWS_GET_CHAT_INFO = 39;</code>
     */
    HALLOWS_GET_CHAT_INFO(39),
    /**
     * <pre>
     **
     * 40.获得神兵跑马灯
     * </pre>
     *
     * <code>FROSTMOURNE_GET_CHAT_INFO = 40;</code>
     */
    FROSTMOURNE_GET_CHAT_INFO(40),
    /**
     * <pre>
     **
     * 41.神兵升星跑马灯
     * </pre>
     *
     * <code>FROSTMOURNE_STAR_UP = 41;</code>
     */
    FROSTMOURNE_STAR_UP(41),
    /**
     * <pre>
     **
     * 42.决战皇城招募
     * </pre>
     *
     * <code>IMPERIALISM_RECRUIT_INFO = 42;</code>
     */
    IMPERIALISM_RECRUIT_INFO(42),
    /**
     * <pre>
     **
     * 43.决战皇城殿主登录
     * x1:宫殿id
     * </pre>
     *
     * <code>IMPERIALISM_PALACE_LOGIN = 43;</code>
     */
    IMPERIALISM_PALACE_LOGIN(43),
    /**
     * <pre>
     **
     * 44.王城乱斗掠夺暴击跑马灯
     * </pre>
     *
     * <code>GVG_BRAWLING_MULTIPLE_INFO = 44;</code>
     */
    GVG_BRAWLING_MULTIPLE_INFO(44),
    /**
     * <pre>
     **
     * 45.轮回战场完成X重试炼
     * x1:X重
     * x2:是否是本服
     * x3:是否是本小战区
     * x4:是否是本大战区
     * (x1&gt;=5&amp;&amp;x1&lt;10&amp;&amp;x2)||(x1&gt;=10&amp;&amp;x1&lt;15&amp;&amp;x3)||(x1&gt;=15&amp;&amp;x1&lt;20&amp;&amp;x4)||(x1&gt;=20)
     * </pre>
     *
     * <code>REINCARNATION_LAYER = 45;</code>
     */
    REINCARNATION_LAYER(45),
    /**
     * <pre>
     **
     * 46.装备升品跑马灯
     * </pre>
     *
     * <code>EQUIPMENT_UPGRADE_INFO = 46;</code>
     */
    EQUIPMENT_UPGRADE_INFO(46),
    /**
     * <pre>
     **
     * 47.装备进阶跑马灯
     * </pre>
     *
     * <code>EQUIPMENT_ENHANCE_INFO = 47;</code>
     */
    EQUIPMENT_ENHANCE_INFO(47),
    /**
     * <pre>
     **
     * 48.英雄幻彩
     * </pre>
     *
     * <code>HERO_COLOR_CHAT_INFO = 48;</code>
     */
    HERO_COLOR_CHAT_INFO(48),
    /**
     * <pre>
     **
     * 49.英雄幻彩进阶
     * x1:X阶(已-1)
     * </pre>
     *
     * <code>HERO_COLOR_LEVE_UP_CHAT_INFO = 49;</code>
     */
    HERO_COLOR_LEVE_UP_CHAT_INFO(49),
    /**
     * <pre>
     **
     * 59.血脉升级
     * x1:X级
     * </pre>
     *
     * <code>BLOODLINE_LEVEL_UP_CHAT_INFO = 50;</code>
     */
    BLOODLINE_LEVEL_UP_CHAT_INFO(50),
    /**
     * <pre>
     **
     * 51.血脉升级至最高重
     * </pre>
     *
     * <code>BLOODLINE_MAX_LEVEL_UP_CHAT_INFO = 51;</code>
     */
    BLOODLINE_MAX_LEVEL_UP_CHAT_INFO(51),
    /**
     * <pre>
     **
     * 52.专武幻彩
     * </pre>
     *
     * <code>IMMORTALS_COLOR_CHAT_INFO = 52;</code>
     */
    IMMORTALS_COLOR_CHAT_INFO(52),
    /**
     * <pre>
     **
     * 53.专武幻彩进阶
     * x1:X阶(已-1)
     * </pre>
     *
     * <code>IMMORTALS_COLOR_LEVE_UP_CHAT_INFO = 53;</code>
     */
    IMMORTALS_COLOR_LEVE_UP_CHAT_INFO(53),
    /**
     * <pre>
     **
     * 54.获得坐骑
     * x1:operationType
     * x2:quality
     * x3:mountId
     * </pre>
     *
     * <code>MOUNT_GET_CHAT_INFO = 54;</code>
     */
    MOUNT_GET_CHAT_INFO(54),
    /**
     * <pre>
     **
     * 55.坐骑升星
     * x1:quality
     * x2:星级
     * </pre>
     *
     * <code>MOUNT_STAR_UP_CHAT_INFO = 55;</code>
     */
    MOUNT_STAR_UP_CHAT_INFO(55),
    /**
     * <pre>
     **
     * 56.伏兽台升级
     * X1:伏兽台等级
     * </pre>
     *
     * <code>MOUNT_PLATFORM_LEVEL_UP_CHAT_INFO = 56;</code>
     */
    MOUNT_PLATFORM_LEVEL_UP_CHAT_INFO(56),
    /**
     * <pre>
     **
     * 100.普通文本消息
     * </pre>
     *
     * <code>NORMAL_TEXT_CHAT_INFO = 100;</code>
     */
    NORMAL_TEXT_CHAT_INFO(100),
    /**
     * <pre>
     **
     * 101.滚动公告
     * </pre>
     *
     * <code>ADMIN_TEXT_CHAT_INFO = 101;</code>
     */
    ADMIN_TEXT_CHAT_INFO(101),
    /**
     * <pre>
     **
     * 1001.藏兵阁组队邀请
     * </pre>
     *
     * <code>TEAM1_ONE_CLICK_INVITATION_CHAT_INFO = 1001;</code>
     */
    TEAM1_ONE_CLICK_INVITATION_CHAT_INFO(1001),
    /**
     * <pre>
     **
     * 1002.魔窟组队魔将邀请
     * </pre>
     *
     * <code>TEAM2_ONE_CLICK_INVITATION_CHAT_INFO = 1002;</code>
     */
    TEAM2_ONE_CLICK_INVITATION_CHAT_INFO(1002),
    /**
     * <pre>
     **
     * 1002.魔窟组队天魔邀请
     * </pre>
     *
     * <code>TEAM3_ONE_CLICK_INVITATION_CHAT_INFO = 1003;</code>
     */
    TEAM3_ONE_CLICK_INVITATION_CHAT_INFO(1003),
    /**
     * <pre>
     **
     * 2001.获得英雄(小战区)
     * HeroGetChatInfo
     * x1:operationType
     * x2:quality
     * x3:heroId
     * (x1==101||x1==102)&amp;&amp;(x2&gt;=4)
     * </pre>
     *
     * <code>ZONE_HERO_GET_CHAT_INFO = 2001;</code>
     */
    ZONE_HERO_GET_CHAT_INFO(2001),
    /**
     * <pre>
     **
     * 2002.获得物品(小战区)
     * GoodsGetChatInfo
     * x1:operationType
     * x2:goodsId
     * </pre>
     *
     * <code>ZONE_GOODS_GET_CHAT_INFO = 2002;</code>
     */
    ZONE_GOODS_GET_CHAT_INFO(2002),
    UNRECOGNIZED(-1),
    ;

    /**
     * <pre>
     ** NONE 
     * </pre>
     *
     * <code>CHAT_INFO_NONE = 0;</code>
     */
    public static final int CHAT_INFO_NONE_VALUE = 0;
    /**
     * <pre>
     **
     * 1.获得英雄
     * x1:operationType
     * x2:quality
     * x3:heroId
     * (x1==101||x1==102)&amp;&amp;(x2&gt;=4)
     * </pre>
     *
     * <code>HERO_GET_CHAT_INFO = 1;</code>
     */
    public static final int HERO_GET_CHAT_INFO_VALUE = 1;
    /**
     * <pre>
     **
     * 2.获得物品
     * x1:operationType
     * x2:goodsId
     * </pre>
     *
     * <code>GOODS_GET_CHAT_INFO = 2;</code>
     */
    public static final int GOODS_GET_CHAT_INFO_VALUE = 2;
    /**
     * <pre>
     **
     * 3.获得装备
     * x1:operationType
     * x2:equipmentId
     * x3:quality
     * </pre>
     *
     * <code>EQUIPMENT_GET_CHAT_INFO = 3;</code>
     */
    public static final int EQUIPMENT_GET_CHAT_INFO_VALUE = 3;
    /**
     * <pre>
     **
     * 4.获得宝物
     * x1:operationType
     * x2:treasureId
     * x3:quality
     * </pre>
     *
     * <code>TREASURE_GET_CHAT_INFO = 4;</code>
     */
    public static final int TREASURE_GET_CHAT_INFO_VALUE = 4;
    /**
     * <pre>
     **
     * 5.10连招募获得两个和以上
     * x1:quality
     * x2:heroId
     * (x1==101||x1==102)&amp;&amp;(x2&gt;=4)
     * </pre>
     *
     * <code>GACHA_HERO_GET_CHAT_INFO = 5;</code>
     */
    public static final int GACHA_HERO_GET_CHAT_INFO_VALUE = 5;
    /**
     * <pre>
     **
     * 6.战力排行榜前X名登陆
     * x1:排名
     * </pre>
     *
     * <code>POWER_TOP_RANK_LOGIN_CHAT_INFO = 6;</code>
     */
    public static final int POWER_TOP_RANK_LOGIN_CHAT_INFO_VALUE = 6;
    /**
     * <pre>
     **
     * 7.竞技场排行前X名登陆
     * x1:排名
     * </pre>
     *
     * <code>ARENA_TOP_RANK_LOGIN_CHAT_INFO = 7;</code>
     */
    public static final int ARENA_TOP_RANK_LOGIN_CHAT_INFO_VALUE = 7;
    /**
     * <pre>
     **
     * 8.英雄升星消息
     * x1:quality
     * x2:starLevel（hero_star_config配置中）
     * x3:heroId
     * </pre>
     *
     * <code>HERO_STAR_UP_CHAT_INFO = 8;</code>
     */
    public static final int HERO_STAR_UP_CHAT_INFO_VALUE = 8;
    /**
     * <pre>
     **
     * 9.天魔触发
     * x1:天魔类型（1.攻击，2.防守）
     * </pre>
     *
     * <code>CAVE_HARD_TRIGGER_CHAT_INFO = 9;</code>
     */
    public static final int CAVE_HARD_TRIGGER_CHAT_INFO_VALUE = 9;
    /**
     * <pre>
     **
     * 10.加入军团
     * </pre>
     *
     * <code>NATION_JOIN_CHAT_INFO = 10;</code>
     */
    public static final int NATION_JOIN_CHAT_INFO_VALUE = 10;
    /**
     * <pre>
     **
     * 11.离开军团
     * </pre>
     *
     * <code>NATION_EXIT_CHAT_INFO = 11;</code>
     */
    public static final int NATION_EXIT_CHAT_INFO_VALUE = 11;
    /**
     * <pre>
     **
     * 12.官职
     * </pre>
     *
     * <code>OFFICIAL_CHAT_INFO = 12;</code>
     */
    public static final int OFFICIAL_CHAT_INFO_VALUE = 12;
    /**
     * <pre>
     **
     * 13.远古棋局赛季首次达到指定段位
     * </pre>
     *
     * <code>QIN_DUNGEON_DAN_CHAT_INFO = 13;</code>
     */
    public static final int QIN_DUNGEON_DAN_CHAT_INFO_VALUE = 13;
    /**
     * <pre>
     **
     * 14.神器获得
     * </pre>
     *
     * <code>ANTIQUE_GET_CHAT_INFO = 14;</code>
     */
    public static final int ANTIQUE_GET_CHAT_INFO_VALUE = 14;
    /**
     * <pre>
     **
     * 15.激活珍宝情缘
     * </pre>
     *
     * <code>ANTIQUE_RELATION_ACTIVATE_CHAT_INFO = 15;</code>
     */
    public static final int ANTIQUE_RELATION_ACTIVATE_CHAT_INFO_VALUE = 15;
    /**
     * <pre>
     **
     * 16.升级珍宝情缘
     * </pre>
     *
     * <code>ANTIQUE_RELATION_UPGRADE_CHAT_INFO = 16;</code>
     */
    public static final int ANTIQUE_RELATION_UPGRADE_CHAT_INFO_VALUE = 16;
    /**
     * <pre>
     **
     * 17.幻化专武
     * </pre>
     *
     * <code>IMMORTALS_ACTIVATE_CHAT_INFO = 17;</code>
     */
    public static final int IMMORTALS_ACTIVATE_CHAT_INFO_VALUE = 17;
    /**
     * <pre>
     **
     * 18.专武升星到特定星级
     * </pre>
     *
     * <code>IMMORTALS_STAR_CHAT_INFO = 18;</code>
     */
    public static final int IMMORTALS_STAR_CHAT_INFO_VALUE = 18;
    /**
     * <pre>
     **
     * 19.名将塔特定层次
     * </pre>
     *
     * <code>TOWER_LAYER_CHAT_INFO = 19;</code>
     */
    public static final int TOWER_LAYER_CHAT_INFO_VALUE = 19;
    /**
     * <pre>
     **
     * 20.无尽试炼首次特定层次
     * </pre>
     *
     * <code>BATTLEFIELD_DRILL_CHALLENGE_CHAT_INFO = 20;</code>
     */
    public static final int BATTLEFIELD_DRILL_CHALLENGE_CHAT_INFO_VALUE = 20;
    /**
     * <pre>
     **
     * 21.竞技场达到历史排名
     * x1:历史最高名次
     * </pre>
     *
     * <code>ARENA_TOP_RANK_HISTORY_CHAT_INFO = 21;</code>
     */
    public static final int ARENA_TOP_RANK_HISTORY_CHAT_INFO_VALUE = 21;
    /**
     * <pre>
     **
     * 22.获得专武
     * x1:operationType
     * x2:quality
     * x3:immortalsSkinId
     * </pre>
     *
     * <code>IMMORTAL_GET_CHAT_INFO = 22;</code>
     */
    public static final int IMMORTAL_GET_CHAT_INFO_VALUE = 22;
    /**
     * <pre>
     **
     * 23.获得货币类型
     * x1:operationType
     * x2:goodsId
     * x3:number
     * </pre>
     *
     * <code>RESOURCE_GET_CHAT_INFO = 23;</code>
     */
    public static final int RESOURCE_GET_CHAT_INFO_VALUE = 23;
    /**
     * <pre>
     **
     * 24.每日抽奖跑马灯
     * </pre>
     *
     * <code>DAILY_DRAW_REWARD = 24;</code>
     */
    public static final int DAILY_DRAW_REWARD_VALUE = 24;
    /**
     * <pre>
     **
     * 25.天官赐福跑马灯
     * </pre>
     *
     * <code>ACTIVITY_21_CHAT_INFO = 25;</code>
     */
    public static final int ACTIVITY_21_CHAT_INFO_VALUE = 25;
    /**
     * <pre>
     **
     * 26.摘星揽月建造
     * </pre>
     *
     * <code>ACTIVITY_31_GOLD_HOUSE = 26;</code>
     */
    public static final int ACTIVITY_31_GOLD_HOUSE_VALUE = 26;
    /**
     * <pre>
     **
     * 27.专武转盘中奖跑马灯
     * </pre>
     *
     * <code>IMMORTALS_TURNTABLE_WINNING = 27;</code>
     */
    public static final int IMMORTALS_TURNTABLE_WINNING_VALUE = 27;
    /**
     * <pre>
     **
     * 28.迎春新象
     * </pre>
     *
     * <code>ACTIVITY_33_CHAT_INFO = 28;</code>
     */
    public static final int ACTIVITY_33_CHAT_INFO_VALUE = 28;
    /**
     * <pre>
     **
     * 29.兵符鉴定跑马灯
     * </pre>
     *
     * <code>RUNES_APPRAISE_CHAT_INFO = 29;</code>
     */
    public static final int RUNES_APPRAISE_CHAT_INFO_VALUE = 29;
    /**
     * <pre>
     **
     * 30.平定天下转盘跑马灯
     * </pre>
     *
     * <code>REPUTATION_TURNTABLE_CHAT_INFO = 30;</code>
     */
    public static final int REPUTATION_TURNTABLE_CHAT_INFO_VALUE = 30;
    /**
     * <pre>
     **
     * 31.获得神兽
     * x1:operationType
     * x2:quality
     * x3:beastId
     * </pre>
     *
     * <code>BEAST_GET_CHAT_INFO = 31;</code>
     */
    public static final int BEAST_GET_CHAT_INFO_VALUE = 31;
    /**
     * <pre>
     **
     * 32.神兽升星
     * x1:星级
     * </pre>
     *
     * <code>BEAST_STAR_UP_CHAT_INFO = 32;</code>
     */
    public static final int BEAST_STAR_UP_CHAT_INFO_VALUE = 32;
    /**
     * <pre>
     **
     * 33.神兽图腾进阶
     * x1:阶级
     * </pre>
     *
     * <code>BEAST_TOTEM_ADVANCE_INFO = 33;</code>
     */
    public static final int BEAST_TOTEM_ADVANCE_INFO_VALUE = 33;
    /**
     * <pre>
     **
     * 34.神兽图腾激活
     * </pre>
     *
     * <code>BEAST_TOTEM_ACTIVATE_INFO = 34;</code>
     */
    public static final int BEAST_TOTEM_ACTIVATE_INFO_VALUE = 34;
    /**
     * <pre>
     **
     * 35.时空红颜合成激活
     * </pre>
     *
     * <code>SPACETIME_BEAUTY_ACTIVATE_INFO = 35;</code>
     */
    public static final int SPACETIME_BEAUTY_ACTIVATE_INFO_VALUE = 35;
    /**
     * <pre>
     **
     * 36.时空红颜美名提升
     * </pre>
     *
     * <code>SPACETIME_BEAUTY_LEVEL_UP_INFO = 36;</code>
     */
    public static final int SPACETIME_BEAUTY_LEVEL_UP_INFO_VALUE = 36;
    /**
     * <pre>
     **
     * 36.时空红颜觉醒升星
     * </pre>
     *
     * <code>SPACETIME_BEAUTY_AWAKEN_INFO = 37;</code>
     */
    public static final int SPACETIME_BEAUTY_AWAKEN_INFO_VALUE = 37;
    /**
     * <pre>
     **
     * 38.珍珑宝库转盘跑马灯
     * </pre>
     *
     * <code>CHESS_TURNTABLE_CHAT_INFO = 38;</code>
     */
    public static final int CHESS_TURNTABLE_CHAT_INFO_VALUE = 38;
    /**
     * <pre>
     **
     * 39.获得神器跑马灯
     * </pre>
     *
     * <code>HALLOWS_GET_CHAT_INFO = 39;</code>
     */
    public static final int HALLOWS_GET_CHAT_INFO_VALUE = 39;
    /**
     * <pre>
     **
     * 40.获得神兵跑马灯
     * </pre>
     *
     * <code>FROSTMOURNE_GET_CHAT_INFO = 40;</code>
     */
    public static final int FROSTMOURNE_GET_CHAT_INFO_VALUE = 40;
    /**
     * <pre>
     **
     * 41.神兵升星跑马灯
     * </pre>
     *
     * <code>FROSTMOURNE_STAR_UP = 41;</code>
     */
    public static final int FROSTMOURNE_STAR_UP_VALUE = 41;
    /**
     * <pre>
     **
     * 42.决战皇城招募
     * </pre>
     *
     * <code>IMPERIALISM_RECRUIT_INFO = 42;</code>
     */
    public static final int IMPERIALISM_RECRUIT_INFO_VALUE = 42;
    /**
     * <pre>
     **
     * 43.决战皇城殿主登录
     * x1:宫殿id
     * </pre>
     *
     * <code>IMPERIALISM_PALACE_LOGIN = 43;</code>
     */
    public static final int IMPERIALISM_PALACE_LOGIN_VALUE = 43;
    /**
     * <pre>
     **
     * 44.王城乱斗掠夺暴击跑马灯
     * </pre>
     *
     * <code>GVG_BRAWLING_MULTIPLE_INFO = 44;</code>
     */
    public static final int GVG_BRAWLING_MULTIPLE_INFO_VALUE = 44;
    /**
     * <pre>
     **
     * 45.轮回战场完成X重试炼
     * x1:X重
     * x2:是否是本服
     * x3:是否是本小战区
     * x4:是否是本大战区
     * (x1&gt;=5&amp;&amp;x1&lt;10&amp;&amp;x2)||(x1&gt;=10&amp;&amp;x1&lt;15&amp;&amp;x3)||(x1&gt;=15&amp;&amp;x1&lt;20&amp;&amp;x4)||(x1&gt;=20)
     * </pre>
     *
     * <code>REINCARNATION_LAYER = 45;</code>
     */
    public static final int REINCARNATION_LAYER_VALUE = 45;
    /**
     * <pre>
     **
     * 46.装备升品跑马灯
     * </pre>
     *
     * <code>EQUIPMENT_UPGRADE_INFO = 46;</code>
     */
    public static final int EQUIPMENT_UPGRADE_INFO_VALUE = 46;
    /**
     * <pre>
     **
     * 47.装备进阶跑马灯
     * </pre>
     *
     * <code>EQUIPMENT_ENHANCE_INFO = 47;</code>
     */
    public static final int EQUIPMENT_ENHANCE_INFO_VALUE = 47;
    /**
     * <pre>
     **
     * 48.英雄幻彩
     * </pre>
     *
     * <code>HERO_COLOR_CHAT_INFO = 48;</code>
     */
    public static final int HERO_COLOR_CHAT_INFO_VALUE = 48;
    /**
     * <pre>
     **
     * 49.英雄幻彩进阶
     * x1:X阶(已-1)
     * </pre>
     *
     * <code>HERO_COLOR_LEVE_UP_CHAT_INFO = 49;</code>
     */
    public static final int HERO_COLOR_LEVE_UP_CHAT_INFO_VALUE = 49;
    /**
     * <pre>
     **
     * 59.血脉升级
     * x1:X级
     * </pre>
     *
     * <code>BLOODLINE_LEVEL_UP_CHAT_INFO = 50;</code>
     */
    public static final int BLOODLINE_LEVEL_UP_CHAT_INFO_VALUE = 50;
    /**
     * <pre>
     **
     * 51.血脉升级至最高重
     * </pre>
     *
     * <code>BLOODLINE_MAX_LEVEL_UP_CHAT_INFO = 51;</code>
     */
    public static final int BLOODLINE_MAX_LEVEL_UP_CHAT_INFO_VALUE = 51;
    /**
     * <pre>
     **
     * 52.专武幻彩
     * </pre>
     *
     * <code>IMMORTALS_COLOR_CHAT_INFO = 52;</code>
     */
    public static final int IMMORTALS_COLOR_CHAT_INFO_VALUE = 52;
    /**
     * <pre>
     **
     * 53.专武幻彩进阶
     * x1:X阶(已-1)
     * </pre>
     *
     * <code>IMMORTALS_COLOR_LEVE_UP_CHAT_INFO = 53;</code>
     */
    public static final int IMMORTALS_COLOR_LEVE_UP_CHAT_INFO_VALUE = 53;
    /**
     * <pre>
     **
     * 54.获得坐骑
     * x1:operationType
     * x2:quality
     * x3:mountId
     * </pre>
     *
     * <code>MOUNT_GET_CHAT_INFO = 54;</code>
     */
    public static final int MOUNT_GET_CHAT_INFO_VALUE = 54;
    /**
     * <pre>
     **
     * 55.坐骑升星
     * x1:quality
     * x2:星级
     * </pre>
     *
     * <code>MOUNT_STAR_UP_CHAT_INFO = 55;</code>
     */
    public static final int MOUNT_STAR_UP_CHAT_INFO_VALUE = 55;
    /**
     * <pre>
     **
     * 56.伏兽台升级
     * X1:伏兽台等级
     * </pre>
     *
     * <code>MOUNT_PLATFORM_LEVEL_UP_CHAT_INFO = 56;</code>
     */
    public static final int MOUNT_PLATFORM_LEVEL_UP_CHAT_INFO_VALUE = 56;
    /**
     * <pre>
     **
     * 100.普通文本消息
     * </pre>
     *
     * <code>NORMAL_TEXT_CHAT_INFO = 100;</code>
     */
    public static final int NORMAL_TEXT_CHAT_INFO_VALUE = 100;
    /**
     * <pre>
     **
     * 101.滚动公告
     * </pre>
     *
     * <code>ADMIN_TEXT_CHAT_INFO = 101;</code>
     */
    public static final int ADMIN_TEXT_CHAT_INFO_VALUE = 101;
    /**
     * <pre>
     **
     * 1001.藏兵阁组队邀请
     * </pre>
     *
     * <code>TEAM1_ONE_CLICK_INVITATION_CHAT_INFO = 1001;</code>
     */
    public static final int TEAM1_ONE_CLICK_INVITATION_CHAT_INFO_VALUE = 1001;
    /**
     * <pre>
     **
     * 1002.魔窟组队魔将邀请
     * </pre>
     *
     * <code>TEAM2_ONE_CLICK_INVITATION_CHAT_INFO = 1002;</code>
     */
    public static final int TEAM2_ONE_CLICK_INVITATION_CHAT_INFO_VALUE = 1002;
    /**
     * <pre>
     **
     * 1002.魔窟组队天魔邀请
     * </pre>
     *
     * <code>TEAM3_ONE_CLICK_INVITATION_CHAT_INFO = 1003;</code>
     */
    public static final int TEAM3_ONE_CLICK_INVITATION_CHAT_INFO_VALUE = 1003;
    /**
     * <pre>
     **
     * 2001.获得英雄(小战区)
     * HeroGetChatInfo
     * x1:operationType
     * x2:quality
     * x3:heroId
     * (x1==101||x1==102)&amp;&amp;(x2&gt;=4)
     * </pre>
     *
     * <code>ZONE_HERO_GET_CHAT_INFO = 2001;</code>
     */
    public static final int ZONE_HERO_GET_CHAT_INFO_VALUE = 2001;
    /**
     * <pre>
     **
     * 2002.获得物品(小战区)
     * GoodsGetChatInfo
     * x1:operationType
     * x2:goodsId
     * </pre>
     *
     * <code>ZONE_GOODS_GET_CHAT_INFO = 2002;</code>
     */
    public static final int ZONE_GOODS_GET_CHAT_INFO_VALUE = 2002;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static ChatInfoType valueOf(int value) {
      return forNumber(value);
    }

    public static ChatInfoType forNumber(int value) {
      switch (value) {
        case 0: return CHAT_INFO_NONE;
        case 1: return HERO_GET_CHAT_INFO;
        case 2: return GOODS_GET_CHAT_INFO;
        case 3: return EQUIPMENT_GET_CHAT_INFO;
        case 4: return TREASURE_GET_CHAT_INFO;
        case 5: return GACHA_HERO_GET_CHAT_INFO;
        case 6: return POWER_TOP_RANK_LOGIN_CHAT_INFO;
        case 7: return ARENA_TOP_RANK_LOGIN_CHAT_INFO;
        case 8: return HERO_STAR_UP_CHAT_INFO;
        case 9: return CAVE_HARD_TRIGGER_CHAT_INFO;
        case 10: return NATION_JOIN_CHAT_INFO;
        case 11: return NATION_EXIT_CHAT_INFO;
        case 12: return OFFICIAL_CHAT_INFO;
        case 13: return QIN_DUNGEON_DAN_CHAT_INFO;
        case 14: return ANTIQUE_GET_CHAT_INFO;
        case 15: return ANTIQUE_RELATION_ACTIVATE_CHAT_INFO;
        case 16: return ANTIQUE_RELATION_UPGRADE_CHAT_INFO;
        case 17: return IMMORTALS_ACTIVATE_CHAT_INFO;
        case 18: return IMMORTALS_STAR_CHAT_INFO;
        case 19: return TOWER_LAYER_CHAT_INFO;
        case 20: return BATTLEFIELD_DRILL_CHALLENGE_CHAT_INFO;
        case 21: return ARENA_TOP_RANK_HISTORY_CHAT_INFO;
        case 22: return IMMORTAL_GET_CHAT_INFO;
        case 23: return RESOURCE_GET_CHAT_INFO;
        case 24: return DAILY_DRAW_REWARD;
        case 25: return ACTIVITY_21_CHAT_INFO;
        case 26: return ACTIVITY_31_GOLD_HOUSE;
        case 27: return IMMORTALS_TURNTABLE_WINNING;
        case 28: return ACTIVITY_33_CHAT_INFO;
        case 29: return RUNES_APPRAISE_CHAT_INFO;
        case 30: return REPUTATION_TURNTABLE_CHAT_INFO;
        case 31: return BEAST_GET_CHAT_INFO;
        case 32: return BEAST_STAR_UP_CHAT_INFO;
        case 33: return BEAST_TOTEM_ADVANCE_INFO;
        case 34: return BEAST_TOTEM_ACTIVATE_INFO;
        case 35: return SPACETIME_BEAUTY_ACTIVATE_INFO;
        case 36: return SPACETIME_BEAUTY_LEVEL_UP_INFO;
        case 37: return SPACETIME_BEAUTY_AWAKEN_INFO;
        case 38: return CHESS_TURNTABLE_CHAT_INFO;
        case 39: return HALLOWS_GET_CHAT_INFO;
        case 40: return FROSTMOURNE_GET_CHAT_INFO;
        case 41: return FROSTMOURNE_STAR_UP;
        case 42: return IMPERIALISM_RECRUIT_INFO;
        case 43: return IMPERIALISM_PALACE_LOGIN;
        case 44: return GVG_BRAWLING_MULTIPLE_INFO;
        case 45: return REINCARNATION_LAYER;
        case 46: return EQUIPMENT_UPGRADE_INFO;
        case 47: return EQUIPMENT_ENHANCE_INFO;
        case 48: return HERO_COLOR_CHAT_INFO;
        case 49: return HERO_COLOR_LEVE_UP_CHAT_INFO;
        case 50: return BLOODLINE_LEVEL_UP_CHAT_INFO;
        case 51: return BLOODLINE_MAX_LEVEL_UP_CHAT_INFO;
        case 52: return IMMORTALS_COLOR_CHAT_INFO;
        case 53: return IMMORTALS_COLOR_LEVE_UP_CHAT_INFO;
        case 54: return MOUNT_GET_CHAT_INFO;
        case 55: return MOUNT_STAR_UP_CHAT_INFO;
        case 56: return MOUNT_PLATFORM_LEVEL_UP_CHAT_INFO;
        case 100: return NORMAL_TEXT_CHAT_INFO;
        case 101: return ADMIN_TEXT_CHAT_INFO;
        case 1001: return TEAM1_ONE_CLICK_INVITATION_CHAT_INFO;
        case 1002: return TEAM2_ONE_CLICK_INVITATION_CHAT_INFO;
        case 1003: return TEAM3_ONE_CLICK_INVITATION_CHAT_INFO;
        case 2001: return ZONE_HERO_GET_CHAT_INFO;
        case 2002: return ZONE_GOODS_GET_CHAT_INFO;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<ChatInfoType>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        ChatInfoType> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<ChatInfoType>() {
            public ChatInfoType findValueByNumber(int number) {
              return ChatInfoType.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.TypeProtocol.getDescriptor().getEnumTypes().get(12);
    }

    private static final ChatInfoType[] VALUES = values();

    public static ChatInfoType valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private ChatInfoType(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:ChatInfoType)
  }

  /**
   * Protobuf enum {@code ChatChannelType}
   */
  public enum ChatChannelType
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>CHAT_CHANNEL_TYPE_NONE = 0;</code>
     */
    CHAT_CHANNEL_TYPE_NONE(0),
    /**
     * <pre>
     ** 1.系统频道
     * </pre>
     *
     * <code>CHAT_CHANNEL_TYPE_SYSTEM = 1;</code>
     */
    CHAT_CHANNEL_TYPE_SYSTEM(1),
    /**
     * <pre>
     ** 2.世界频道
     * </pre>
     *
     * <code>CHAT_CHANNEL_TYPE_WORLD = 2;</code>
     */
    CHAT_CHANNEL_TYPE_WORLD(2),
    /**
     * <pre>
     ** 3.国家频道
     * </pre>
     *
     * <code>CHAT_CHANNEL_TYPE_NATION = 3;</code>
     */
    CHAT_CHANNEL_TYPE_NATION(3),
    /**
     * <pre>
     ** 4.私人频道
     * </pre>
     *
     * <code>CHAT_CHANNEL_TYPE_PERSONAL = 4;</code>
     */
    CHAT_CHANNEL_TYPE_PERSONAL(4),
    /**
     * <pre>
     ** 5.跨服频道
     * </pre>
     *
     * <code>CHAT_CHANNEL_TYPE_CROSS = 5;</code>
     */
    CHAT_CHANNEL_TYPE_CROSS(5),
    /**
     * <pre>
     ** 6.组队频道
     * </pre>
     *
     * <code>CHAT_CHANNEL_TYPE_TEAM = 6;</code>
     */
    CHAT_CHANNEL_TYPE_TEAM(6),
    /**
     * <pre>
     ** 7.战区频道
     * </pre>
     *
     * <code>CHAT_CHANNEL_TYPE_ZONE = 7;</code>
     */
    CHAT_CHANNEL_TYPE_ZONE(7),
    /**
     * <pre>
     ** 8.皇城战区频道
     * </pre>
     *
     * <code>CHAT_CHANNEL_TYPE_IMPERIALISM_REGION = 8;</code>
     */
    CHAT_CHANNEL_TYPE_IMPERIALISM_REGION(8),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>CHAT_CHANNEL_TYPE_NONE = 0;</code>
     */
    public static final int CHAT_CHANNEL_TYPE_NONE_VALUE = 0;
    /**
     * <pre>
     ** 1.系统频道
     * </pre>
     *
     * <code>CHAT_CHANNEL_TYPE_SYSTEM = 1;</code>
     */
    public static final int CHAT_CHANNEL_TYPE_SYSTEM_VALUE = 1;
    /**
     * <pre>
     ** 2.世界频道
     * </pre>
     *
     * <code>CHAT_CHANNEL_TYPE_WORLD = 2;</code>
     */
    public static final int CHAT_CHANNEL_TYPE_WORLD_VALUE = 2;
    /**
     * <pre>
     ** 3.国家频道
     * </pre>
     *
     * <code>CHAT_CHANNEL_TYPE_NATION = 3;</code>
     */
    public static final int CHAT_CHANNEL_TYPE_NATION_VALUE = 3;
    /**
     * <pre>
     ** 4.私人频道
     * </pre>
     *
     * <code>CHAT_CHANNEL_TYPE_PERSONAL = 4;</code>
     */
    public static final int CHAT_CHANNEL_TYPE_PERSONAL_VALUE = 4;
    /**
     * <pre>
     ** 5.跨服频道
     * </pre>
     *
     * <code>CHAT_CHANNEL_TYPE_CROSS = 5;</code>
     */
    public static final int CHAT_CHANNEL_TYPE_CROSS_VALUE = 5;
    /**
     * <pre>
     ** 6.组队频道
     * </pre>
     *
     * <code>CHAT_CHANNEL_TYPE_TEAM = 6;</code>
     */
    public static final int CHAT_CHANNEL_TYPE_TEAM_VALUE = 6;
    /**
     * <pre>
     ** 7.战区频道
     * </pre>
     *
     * <code>CHAT_CHANNEL_TYPE_ZONE = 7;</code>
     */
    public static final int CHAT_CHANNEL_TYPE_ZONE_VALUE = 7;
    /**
     * <pre>
     ** 8.皇城战区频道
     * </pre>
     *
     * <code>CHAT_CHANNEL_TYPE_IMPERIALISM_REGION = 8;</code>
     */
    public static final int CHAT_CHANNEL_TYPE_IMPERIALISM_REGION_VALUE = 8;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static ChatChannelType valueOf(int value) {
      return forNumber(value);
    }

    public static ChatChannelType forNumber(int value) {
      switch (value) {
        case 0: return CHAT_CHANNEL_TYPE_NONE;
        case 1: return CHAT_CHANNEL_TYPE_SYSTEM;
        case 2: return CHAT_CHANNEL_TYPE_WORLD;
        case 3: return CHAT_CHANNEL_TYPE_NATION;
        case 4: return CHAT_CHANNEL_TYPE_PERSONAL;
        case 5: return CHAT_CHANNEL_TYPE_CROSS;
        case 6: return CHAT_CHANNEL_TYPE_TEAM;
        case 7: return CHAT_CHANNEL_TYPE_ZONE;
        case 8: return CHAT_CHANNEL_TYPE_IMPERIALISM_REGION;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<ChatChannelType>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        ChatChannelType> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<ChatChannelType>() {
            public ChatChannelType findValueByNumber(int number) {
              return ChatChannelType.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.TypeProtocol.getDescriptor().getEnumTypes().get(13);
    }

    private static final ChatChannelType[] VALUES = values();

    public static ChatChannelType valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private ChatChannelType(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:ChatChannelType)
  }

  /**
   * <pre>
   **
   * 塔防类型
   * </pre>
   *
   * Protobuf enum {@code TeamTdType}
   */
  public enum TeamTdType
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>TEAM_TD_TYPE_NONE = 0;</code>
     */
    TEAM_TD_TYPE_NONE(0),
    /**
     * <pre>
     **
     * 1.在线TD
     * value:在线TD关卡ID
     * </pre>
     *
     * <code>ONLINE_TD = 1;</code>
     */
    ONLINE_TD(1),
    /**
     * <pre>
     **
     * 2.合战锦标赛
     * value:难度
     * </pre>
     *
     * <code>ACTIVITY_55 = 2;</code>
     */
    ACTIVITY_55(2),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>TEAM_TD_TYPE_NONE = 0;</code>
     */
    public static final int TEAM_TD_TYPE_NONE_VALUE = 0;
    /**
     * <pre>
     **
     * 1.在线TD
     * value:在线TD关卡ID
     * </pre>
     *
     * <code>ONLINE_TD = 1;</code>
     */
    public static final int ONLINE_TD_VALUE = 1;
    /**
     * <pre>
     **
     * 2.合战锦标赛
     * value:难度
     * </pre>
     *
     * <code>ACTIVITY_55 = 2;</code>
     */
    public static final int ACTIVITY_55_VALUE = 2;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static TeamTdType valueOf(int value) {
      return forNumber(value);
    }

    public static TeamTdType forNumber(int value) {
      switch (value) {
        case 0: return TEAM_TD_TYPE_NONE;
        case 1: return ONLINE_TD;
        case 2: return ACTIVITY_55;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<TeamTdType>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        TeamTdType> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<TeamTdType>() {
            public TeamTdType findValueByNumber(int number) {
              return TeamTdType.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.TypeProtocol.getDescriptor().getEnumTypes().get(14);
    }

    private static final TeamTdType[] VALUES = values();

    public static TeamTdType valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private TeamTdType(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:TeamTdType)
  }

  /**
   * <pre>
   **
   * 塔防组队加入类型
   * </pre>
   *
   * Protobuf enum {@code TeamTdJoinLimitType}
   */
  public enum TeamTdJoinLimitType
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>TEAM_TD_JOIN_LIMIT_TYPE_NONE = 0;</code>
     */
    TEAM_TD_JOIN_LIMIT_TYPE_NONE(0),
    /**
     * <pre>
     **
     * 1.口令
     * </pre>
     *
     * <code>CODE = 1;</code>
     */
    CODE(1),
    /**
     * <pre>
     **
     * 2.战力
     * </pre>
     *
     * <code>POWER = 2;</code>
     */
    POWER(2),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>TEAM_TD_JOIN_LIMIT_TYPE_NONE = 0;</code>
     */
    public static final int TEAM_TD_JOIN_LIMIT_TYPE_NONE_VALUE = 0;
    /**
     * <pre>
     **
     * 1.口令
     * </pre>
     *
     * <code>CODE = 1;</code>
     */
    public static final int CODE_VALUE = 1;
    /**
     * <pre>
     **
     * 2.战力
     * </pre>
     *
     * <code>POWER = 2;</code>
     */
    public static final int POWER_VALUE = 2;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static TeamTdJoinLimitType valueOf(int value) {
      return forNumber(value);
    }

    public static TeamTdJoinLimitType forNumber(int value) {
      switch (value) {
        case 0: return TEAM_TD_JOIN_LIMIT_TYPE_NONE;
        case 1: return CODE;
        case 2: return POWER;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<TeamTdJoinLimitType>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        TeamTdJoinLimitType> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<TeamTdJoinLimitType>() {
            public TeamTdJoinLimitType findValueByNumber(int number) {
              return TeamTdJoinLimitType.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.TypeProtocol.getDescriptor().getEnumTypes().get(15);
    }

    private static final TeamTdJoinLimitType[] VALUES = values();

    public static TeamTdJoinLimitType valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private TeamTdJoinLimitType(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:TeamTdJoinLimitType)
  }

  /**
   * <pre>
   **
   * 塔防组队队伍状态
   * </pre>
   *
   * Protobuf enum {@code TeamTdState}
   */
  public enum TeamTdState
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>TEAM_TD_STATE_NONE = 0;</code>
     */
    TEAM_TD_STATE_NONE(0),
    /**
     * <pre>
     **
     * 1.空闲
     * </pre>
     *
     * <code>IDLE = 1;</code>
     */
    IDLE(1),
    /**
     * <pre>
     **
     * 2.战斗
     * </pre>
     *
     * <code>BATTLE = 2;</code>
     */
    BATTLE(2),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>TEAM_TD_STATE_NONE = 0;</code>
     */
    public static final int TEAM_TD_STATE_NONE_VALUE = 0;
    /**
     * <pre>
     **
     * 1.空闲
     * </pre>
     *
     * <code>IDLE = 1;</code>
     */
    public static final int IDLE_VALUE = 1;
    /**
     * <pre>
     **
     * 2.战斗
     * </pre>
     *
     * <code>BATTLE = 2;</code>
     */
    public static final int BATTLE_VALUE = 2;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static TeamTdState valueOf(int value) {
      return forNumber(value);
    }

    public static TeamTdState forNumber(int value) {
      switch (value) {
        case 0: return TEAM_TD_STATE_NONE;
        case 1: return IDLE;
        case 2: return BATTLE;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<TeamTdState>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        TeamTdState> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<TeamTdState>() {
            public TeamTdState findValueByNumber(int number) {
              return TeamTdState.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.TypeProtocol.getDescriptor().getEnumTypes().get(16);
    }

    private static final TeamTdState[] VALUES = values();

    public static TeamTdState valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private TeamTdState(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:TeamTdState)
  }

  /**
   * <pre>
   **
   * 塔防组队成员类型
   * </pre>
   *
   * Protobuf enum {@code TeamTdMemberType}
   */
  public enum TeamTdMemberType
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>TEAM_TD_MEMBER_TYPE_NONE = 0;</code>
     */
    TEAM_TD_MEMBER_TYPE_NONE(0),
    /**
     * <pre>
     **
     * 1.队长
     * </pre>
     *
     * <code>CAPTAIN = 1;</code>
     */
    CAPTAIN(1),
    /**
     * <pre>
     **
     * 2.成员
     * </pre>
     *
     * <code>MEMBER = 2;</code>
     */
    MEMBER(2),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>TEAM_TD_MEMBER_TYPE_NONE = 0;</code>
     */
    public static final int TEAM_TD_MEMBER_TYPE_NONE_VALUE = 0;
    /**
     * <pre>
     **
     * 1.队长
     * </pre>
     *
     * <code>CAPTAIN = 1;</code>
     */
    public static final int CAPTAIN_VALUE = 1;
    /**
     * <pre>
     **
     * 2.成员
     * </pre>
     *
     * <code>MEMBER = 2;</code>
     */
    public static final int MEMBER_VALUE = 2;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static TeamTdMemberType valueOf(int value) {
      return forNumber(value);
    }

    public static TeamTdMemberType forNumber(int value) {
      switch (value) {
        case 0: return TEAM_TD_MEMBER_TYPE_NONE;
        case 1: return CAPTAIN;
        case 2: return MEMBER;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<TeamTdMemberType>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        TeamTdMemberType> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<TeamTdMemberType>() {
            public TeamTdMemberType findValueByNumber(int number) {
              return TeamTdMemberType.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.TypeProtocol.getDescriptor().getEnumTypes().get(17);
    }

    private static final TeamTdMemberType[] VALUES = values();

    public static TeamTdMemberType valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private TeamTdMemberType(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:TeamTdMemberType)
  }


  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\027game/typeProtocol.proto*\226\003\n\nRewardType" +
      "\022\024\n\020REWARD_TYPE_NONE\020\000\022\014\n\010RESOURCE\020\n\022\t\n\005" +
      "GOODS\020\013\022\010\n\004HERO\020\014\022\r\n\tEQUIPMENT\020\r\022\010\n\004RUNE" +
      "\020\016\022\017\n\013PREROGATIVE\020\017\022\017\n\013CHAT_BUBBLE\020\020\022\020\n\014" +
      "AVATAR_FRAME\020\021\022\n\n\006AVATAR\020\022\022\010\n\004SHOW\020\023\022\014\n\010" +
      "TREASURE\020\024\022\013\n\007ANTIQUE\020\026\022\r\n\tIMMORTALS\020\027\022\n" +
      "\n\006BEAUTY\020\030\022\t\n\005RUNES\020\031\022\014\n\010GEMSTONE\020\032\022\010\n\004S" +
      "OUL\020\033\022\t\n\005RELIC\020\034\022\t\n\005BEAST\020\035\022\024\n\020SPACETIME" +
      "_BEAUTY\020\036\022\027\n\023SANCTUARY_EQUIPMENT\020\037\022\013\n\007HA" +
      "LLOWS\020 \022\017\n\013FROSTMOURNE\020!\022\r\n\tHERO_SKIN\020\"\022" +
      "\t\n\005MOUNT\020#\022\026\n\022HERO_SKIN_FRAGMENT\020$*\321\003\n\nR" +
      "esourceId\022\024\n\020RESOURCE_ID_NONE\020\000\022\013\n\007DIAMO" +
      "ND\020\001\022\010\n\004GOLD\020\002\022\007\n\003EXP\020\003\022\016\n\nARENA_COIN\020\004\022" +
      "\017\n\013HONOUR_COIN\020\005\022\n\n\006VIGOUR\020\006\022\016\n\nCHESS_CO" +
      "IN\020\007\022\027\n\023TEAM_ONLINE_TD_COIN\020\010\022\017\n\013NATION_" +
      "COIN\020\t\022\016\n\nNATION_EXP\020\n\022\017\n\013WEAPON_COIN\020\r\022" +
      "\n\n\006ENERGY\020\016\022\027\n\023NATION_CONTRIBUTION\020\020\022\013\n\007" +
      "VIP_EXP\020\021\022\016\n\nTOWER_COIN\020\023\022\020\n\014ENDLESS_COI" +
      "N\020\024\022\020\n\014FRIEND_POINT\020\025\022\r\n\tCAVE_COIN\020\026\022\020\n\014" +
      "CAPTURE_COIN\020\027\022\022\n\016CROSS_CUP_COIN\020\030\022\021\n\rHE" +
      "GEMONY_COIN\020\031\022\014\n\010GVG_COIN\020\032\022\024\n\020ACTIVITY_" +
      "31_COIN\020\034\022\017\n\013ESCORT_COIN\020\035\022\020\n\014CHESS_2_CO" +
      "IN\020\036\022\020\n\014QINRACE_COIN\020\037*R\n\tGoodsType\022\022\n\016I" +
      "TEM_TYPE_NONE\020\000\022\014\n\010ITEM_GEM\020\001\022\r\n\tITEM_GO" +
      "LD\020\002\022\007\n\003USE\020\003\022\013\n\007FRAMENT\020\004*p\n\013KickOffTyp" +
      "e\022\026\n\022KICK_OFF_TYPE_NONE\020\000\022\023\n\017LOGIN_DUPLI" +
      "CATE\020\001\022\014\n\010CLOSEING\020\002\022\016\n\nUSER_BLOCK\020\003\022\026\n\022" +
      "SERVER_MAINTENANCE\020\004*T\n\013VipCardType\022\025\n\021V" +
      "IP_CARD_TYPE_NON\020\000\022\022\n\016MOUTH_VIP_CARD\020\001\022\032" +
      "\n\026SUPREME_MOUTH_VIP_CARD\020\002*X\n\rMailStateT" +
      "ype\022\030\n\024MAIL_STATE_TYPE_NONE\020\000\022\n\n\006UNREAD\020" +
      "\001\022\010\n\004READ\020\002\022\013\n\007RECEIVE\020\003\022\n\n\006DELETE\020\004*j\n\013" +
      "SettingType\022\024\n\020SETTING_KEY_NONE\020\000\022\025\n\021SER" +
      "VER_START_TIME\020\001\022\026\n\022SYSTEM_MAIL_MAX_ID\020\002" +
      "\022\026\n\021MERGE_SERVER_TIME\020\346\007*\217\007\n\017Prerogative" +
      "Type\022\031\n\025PREROGATIVE_TYPE_NONE\020\000\022%\n!STORY" +
      "_HANG_UP_OFFLINE_REWARD_TIME\020\001\022\034\n\030STAR_U" +
      "P_ROAD_RESET_TIMES\020\002\022\027\n\023ENDLESS_RESET_TI" +
      "MES\020\003\022\025\n\021ARNEA_RESET_TIMES\020\004\022\024\n\020MONEY_TR" +
      "EE_TIMES\020\005\022\025\n\021DUNGEON_BUY_TIMES\020\006\022\026\n\022PAG" +
      "ODA_RESET_TIMES\020\007\022\030\n\024IMMORTAL_VISIT_TIME" +
      "S\020\010\022#\n\037HANG_UP_REWARD_ADD_TEN_THOUSAND\020\t" +
      "\022\030\n\024WHOLE_BOSS_BUY_TIMES\020\n\022\030\n\024ADD_EXP_TE" +
      "N_THOUSAND\020\013\022\023\n\017SIEGE_BUY_TIMES\020\014\022\030\n\024SUP" +
      "PRESS_DEMON_TIMES\020\r\022\"\n\036FLAG_WAR_CHALLENG" +
      "E_LIMIT_TIMES\020\016\022!\n\035VIE_CHALLENGE_BUY_LIM" +
      "IT_TIMES\020\017\022&\n\"STORY_NORMAL_CHALLENGE_RES" +
      "ET_TIMES\020\022\022%\n!STORY_ELITE_CHALLENGE_RESE" +
      "T_TIMES\020\023\022$\n STORY_HELL_CHALLENGE_RESET_" +
      "TIMES\020\024\022\034\n\030NATION_DUNGEON_BUY_TIMES\020\027\022\030\n" +
      "\024NATION_WAR_BUY_TIMES\020\030\022\023\n\017HONOR_BUY_TIM" +
      "ES\020\033\022\030\n\024GOBLIN_DOUBLE_REWARD\020\034\022 \n\034MAGIC_" +
      "RECORD_BUY_LIMIT_TIMES\020\037\022\036\n\032DESTROY_TEMP" +
      "LE_RESET_TIMES\020 \022\"\n\036MAGIC_RECORD_RESET_L" +
      "IMIT_TIMES\020!\022\036\n\032EXPLOIT_PULSE_TIMES_LIMI" +
      "TS\020\"\022\032\n\026OCCUPY_PULSE_TIMES_MAX\020#\022\032\n\026OPEN" +
      "_GOD_FURNACE_TIMES\020$\022$\n WONDERLAND_QUICK" +
      "_ADVENTURE_TIMES\020%*\220\001\n\nChargeType\022\024\n\020CHA" +
      "RGE_TYPE_NONE\020\000\022\022\n\016CHARGE_DIAMOND\020\001\022\034\n\030C" +
      "HARGE_DIRECT_PURCHASING\020\002\022\027\n\023CHARGE_VOUC" +
      "HER_MAIL\020\003\022!\n\035CHARGE_DIRECT_PURCHASING_M" +
      "AIL\020\004*\262\014\n\016ActorFieldType\022\031\n\025ACTOR_FIELD_" +
      "TYPE_NONE\020\000\022\007\n\003UID\020\001\022\017\n\013PLATFORM_ID\020\002\022\013\n" +
      "\007CHANNEL\020\003\022\r\n\tSERVER_ID\020\004\022\014\n\010ACTOR_ID\020\005\022" +
      "\016\n\nACTOR_NAME\020\006\022\007\n\003SEX\020\007\022\017\n\013ACTOR_LEVEL\020" +
      "\010\022\r\n\tACTOR_EXP\020\t\022\016\n\nACTOR_GOLD\020\n\022\021\n\rACTO" +
      "R_DIAMOND\020\013\022\r\n\tVIP_LEVEL\020\014\022\r\n\tAVATAR_ID\020" +
      "\r\022\021\n\rGUIDE_ID_LIST\020\016\022\014\n\010RECHARGE\020\017\022\024\n\020LA" +
      "ST_RENAME_TIME\020\020\022\017\n\013ACTOR_POWER\020\021\022\013\n\007PRE" +
      "SENT\020\022\022\r\n\tVIP_CARDS\020\023\022\020\n\014ACTOR_VIGOUR\020\024\022" +
      "\020\n\014ACTOR_ENERGY\020\025\022\017\n\013AVATAR_LIST\020\026\022\r\n\tSH" +
      "OW_LIST\020\027\022\034\n\030LAST_REFRESH_VIGOUR_TIME\020\030\022" +
      "\034\n\030LAST_REFRESH_ENERGY_TIME\020\031\022\025\n\021ACTOR_C" +
      "REATE_TIME\020\032\022\023\n\017LOGIN_TOTAL_DAY\020\033\022\013\n\007SHO" +
      "W_ID\020\034\022\022\n\016ARENA_MAX_RANK\020\035\022\014\n\010STORY_ID\020\036" +
      "\022\r\n\tMAX_POWER\020\037\022\024\n\020ACTOR_CHESS_COIN\020 \022\024\n" +
      "\020ACTOR_ARENA_COIN\020!\022\020\n\014PRAISE_STATE\020\"\022\017\n" +
      "\013NATION_NAME\020#\022\020\n\014ONLINE_STATE\020$\022\024\n\020LAST" +
      "_LOGOUT_TIME\020%\022\017\n\013NATION_FLAG\020&\022\021\n\rNATIO" +
      "N_FLAGID\020\'\022\025\n\021CURRENT_SERVER_ID\020(\022\r\n\tNAT" +
      "ION_ID\020)\022\020\n\014NATION_LEVEL\020*\022\025\n\021ACTOR_WEAP" +
      "ON_COIN\020+\022\035\n\031ACTOR_TEAM_ONLINE_TD_COIN\020," +
      "\022\r\n\tAUTOGRAPH\020-\022\023\n\017AVATAR_FRAME_ID\020.\022\025\n\021" +
      "AVATAR_FRAME_LIST\020/\022\022\n\016CHAT_BUBBLE_ID\0200\022" +
      "\024\n\020CHAT_BUBBLE_LIST\0201\022\026\n\022ACTOR_FRIEND_PO" +
      "INT\0202\022\025\n\021ACTOR_NATION_COIN\0203\022\024\n\020ACTOR_NA" +
      "TION_EXP\0204\022\035\n\031ACTOR_NATION_CONTRIBUTION\020" +
      "5\022\021\n\rACTOR_VIP_EXP\0206\022\024\n\020ACTOR_TOWER_COIN" +
      "\0207\022\026\n\022ACTOR_ENDLESS_COIN\0208\022\020\n\014PLOT_ID_LI" +
      "ST\0209\022\036\n\032CAVE_SINGLE_CHALLENGE_LIST\020:\022\023\n\017" +
      "ACTOR_CAVE_COIN\020;\022\022\n\016ACTOR_OFFICIAL\020<\022\026\n" +
      "\022ACTOR_CAPTURE_COIN\020=\022\014\n\010TITLE_ID\020?\022\030\n\024A" +
      "CTOR_CROSS_CUP_COIN\020@\022\017\n\013SDK_USER_ID\020A\022\017" +
      "\n\013GROWTH_TASK\020B\022\027\n\023ACTOR_HEGEMONY_COIN\020C" +
      "\022\022\n\016SHOW_IMMORTALS\020D\022\022\n\016SHOW_HERO_STAR\020E" +
      "\022\022\n\016ACTOR_GVG_COIN\020F\022\032\n\026ACTOR_ACTIVITY_3" +
      "1_COIN\020G\022\025\n\021ACTOR_ESCORT_COIN\020H\022\026\n\022ACTOR" +
      "_CHESS_2_COIN\020I\022\026\n\022ACTOR_QINRACE_COIN\020J\022" +
      "\025\n\021PRAISED_COUNT_MAP\020K\022\034\n\030HERO_COLOR_REB" +
      "IRTH_TIMES\020L\022!\n\035IMMORTALS_COLOR_REBIRTH_" +
      "TIMES\020M\022\031\n\025SHOW_HERO_COLOR_LEVEL\020N*p\n\031Tr" +
      "easureSnatchRecordState\022$\n TREASURE_SNAT" +
      "CH_RECORD_STATE_NON\020\000\022\n\n\006SNATCH\020\001\022\013\n\007REV" +
      "ENGE\020\002\022\024\n\020REVENGE_COMPLETE\020\003* \n\tTowerTyp" +
      "e\022\010\n\004NONE\020\000\022\t\n\005ATYPE\020\001*\316\017\n\014ChatInfoType\022" +
      "\022\n\016CHAT_INFO_NONE\020\000\022\026\n\022HERO_GET_CHAT_INF" +
      "O\020\001\022\027\n\023GOODS_GET_CHAT_INFO\020\002\022\033\n\027EQUIPMEN" +
      "T_GET_CHAT_INFO\020\003\022\032\n\026TREASURE_GET_CHAT_I" +
      "NFO\020\004\022\034\n\030GACHA_HERO_GET_CHAT_INFO\020\005\022\"\n\036P" +
      "OWER_TOP_RANK_LOGIN_CHAT_INFO\020\006\022\"\n\036ARENA" +
      "_TOP_RANK_LOGIN_CHAT_INFO\020\007\022\032\n\026HERO_STAR" +
      "_UP_CHAT_INFO\020\010\022\037\n\033CAVE_HARD_TRIGGER_CHA" +
      "T_INFO\020\t\022\031\n\025NATION_JOIN_CHAT_INFO\020\n\022\031\n\025N" +
      "ATION_EXIT_CHAT_INFO\020\013\022\026\n\022OFFICIAL_CHAT_" +
      "INFO\020\014\022\035\n\031QIN_DUNGEON_DAN_CHAT_INFO\020\r\022\031\n" +
      "\025ANTIQUE_GET_CHAT_INFO\020\016\022\'\n#ANTIQUE_RELA" +
      "TION_ACTIVATE_CHAT_INFO\020\017\022&\n\"ANTIQUE_REL" +
      "ATION_UPGRADE_CHAT_INFO\020\020\022 \n\034IMMORTALS_A" +
      "CTIVATE_CHAT_INFO\020\021\022\034\n\030IMMORTALS_STAR_CH" +
      "AT_INFO\020\022\022\031\n\025TOWER_LAYER_CHAT_INFO\020\023\022)\n%" +
      "BATTLEFIELD_DRILL_CHALLENGE_CHAT_INFO\020\024\022" +
      "$\n ARENA_TOP_RANK_HISTORY_CHAT_INFO\020\025\022\032\n" +
      "\026IMMORTAL_GET_CHAT_INFO\020\026\022\032\n\026RESOURCE_GE" +
      "T_CHAT_INFO\020\027\022\025\n\021DAILY_DRAW_REWARD\020\030\022\031\n\025" +
      "ACTIVITY_21_CHAT_INFO\020\031\022\032\n\026ACTIVITY_31_G" +
      "OLD_HOUSE\020\032\022\037\n\033IMMORTALS_TURNTABLE_WINNI" +
      "NG\020\033\022\031\n\025ACTIVITY_33_CHAT_INFO\020\034\022\034\n\030RUNES" +
      "_APPRAISE_CHAT_INFO\020\035\022\"\n\036REPUTATION_TURN" +
      "TABLE_CHAT_INFO\020\036\022\027\n\023BEAST_GET_CHAT_INFO" +
      "\020\037\022\033\n\027BEAST_STAR_UP_CHAT_INFO\020 \022\034\n\030BEAST" +
      "_TOTEM_ADVANCE_INFO\020!\022\035\n\031BEAST_TOTEM_ACT" +
      "IVATE_INFO\020\"\022\"\n\036SPACETIME_BEAUTY_ACTIVAT" +
      "E_INFO\020#\022\"\n\036SPACETIME_BEAUTY_LEVEL_UP_IN" +
      "FO\020$\022 \n\034SPACETIME_BEAUTY_AWAKEN_INFO\020%\022\035" +
      "\n\031CHESS_TURNTABLE_CHAT_INFO\020&\022\031\n\025HALLOWS" +
      "_GET_CHAT_INFO\020\'\022\035\n\031FROSTMOURNE_GET_CHAT" +
      "_INFO\020(\022\027\n\023FROSTMOURNE_STAR_UP\020)\022\034\n\030IMPE" +
      "RIALISM_RECRUIT_INFO\020*\022\034\n\030IMPERIALISM_PA" +
      "LACE_LOGIN\020+\022\036\n\032GVG_BRAWLING_MULTIPLE_IN" +
      "FO\020,\022\027\n\023REINCARNATION_LAYER\020-\022\032\n\026EQUIPME" +
      "NT_UPGRADE_INFO\020.\022\032\n\026EQUIPMENT_ENHANCE_I" +
      "NFO\020/\022\030\n\024HERO_COLOR_CHAT_INFO\0200\022 \n\034HERO_" +
      "COLOR_LEVE_UP_CHAT_INFO\0201\022 \n\034BLOODLINE_L" +
      "EVEL_UP_CHAT_INFO\0202\022$\n BLOODLINE_MAX_LEV" +
      "EL_UP_CHAT_INFO\0203\022\035\n\031IMMORTALS_COLOR_CHA" +
      "T_INFO\0204\022%\n!IMMORTALS_COLOR_LEVE_UP_CHAT" +
      "_INFO\0205\022\027\n\023MOUNT_GET_CHAT_INFO\0206\022\033\n\027MOUN" +
      "T_STAR_UP_CHAT_INFO\0207\022%\n!MOUNT_PLATFORM_" +
      "LEVEL_UP_CHAT_INFO\0208\022\031\n\025NORMAL_TEXT_CHAT" +
      "_INFO\020d\022\030\n\024ADMIN_TEXT_CHAT_INFO\020e\022)\n$TEA" +
      "M1_ONE_CLICK_INVITATION_CHAT_INFO\020\351\007\022)\n$" +
      "TEAM2_ONE_CLICK_INVITATION_CHAT_INFO\020\352\007\022" +
      ")\n$TEAM3_ONE_CLICK_INVITATION_CHAT_INFO\020" +
      "\353\007\022\034\n\027ZONE_HERO_GET_CHAT_INFO\020\321\017\022\035\n\030ZONE" +
      "_GOODS_GET_CHAT_INFO\020\322\017*\245\002\n\017ChatChannelT" +
      "ype\022\032\n\026CHAT_CHANNEL_TYPE_NONE\020\000\022\034\n\030CHAT_" +
      "CHANNEL_TYPE_SYSTEM\020\001\022\033\n\027CHAT_CHANNEL_TY" +
      "PE_WORLD\020\002\022\034\n\030CHAT_CHANNEL_TYPE_NATION\020\003" +
      "\022\036\n\032CHAT_CHANNEL_TYPE_PERSONAL\020\004\022\033\n\027CHAT" +
      "_CHANNEL_TYPE_CROSS\020\005\022\032\n\026CHAT_CHANNEL_TY" +
      "PE_TEAM\020\006\022\032\n\026CHAT_CHANNEL_TYPE_ZONE\020\007\022(\n" +
      "$CHAT_CHANNEL_TYPE_IMPERIALISM_REGION\020\010*" +
      "C\n\nTeamTdType\022\025\n\021TEAM_TD_TYPE_NONE\020\000\022\r\n\t" +
      "ONLINE_TD\020\001\022\017\n\013ACTIVITY_55\020\002*L\n\023TeamTdJo" +
      "inLimitType\022 \n\034TEAM_TD_JOIN_LIMIT_TYPE_N" +
      "ONE\020\000\022\010\n\004CODE\020\001\022\t\n\005POWER\020\002*;\n\013TeamTdStat" +
      "e\022\026\n\022TEAM_TD_STATE_NONE\020\000\022\010\n\004IDLE\020\001\022\n\n\006B" +
      "ATTLE\020\002*I\n\020TeamTdMemberType\022\034\n\030TEAM_TD_M" +
      "EMBER_TYPE_NONE\020\000\022\013\n\007CAPTAIN\020\001\022\n\n\006MEMBER" +
      "\020\002B\034\n\030cn.daxiang.protocol.gameH\001b\006proto3"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
        new com.google.protobuf.Descriptors.FileDescriptor.    InternalDescriptorAssigner() {
          public com.google.protobuf.ExtensionRegistry assignDescriptors(
              com.google.protobuf.Descriptors.FileDescriptor root) {
            descriptor = root;
            return null;
          }
        };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        }, assigner);
  }

  // @@protoc_insertion_point(outer_class_scope)
}
