// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: game/chatProtocol.proto

package cn.daxiang.protocol.game;

public final class ChatProtocol {
  private ChatProtocol() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  /**
   * <pre>
   ** 聊天 
   * </pre>
   *
   * Protobuf enum {@code ChatCmd}
   */
  public enum ChatCmd
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>CHAT_CMD_NONE = 0;</code>
     */
    CHAT_CMD_NONE(0),
    /**
     * <pre>
     **SimpleDateFormat
     * 获取聊天信息
     * &lt;pre&gt;
     * 请求:{&#64;code Request}
     * 响应:{&#64;code ChatMessageResponse}
     * &lt;/pre&gt;
     * </pre>
     *
     * <code>GET_CHAT_INFO = 1;</code>
     */
    GET_CHAT_INFO(1),
    /**
     * <pre>
     **
     * 获取聊天时间
     * &lt;pre&gt;
     * 请求:{&#64;code Request}
     * 响应:{&#64;code ChatTimeResponse}
     * &lt;/pre&gt;
     * </pre>
     *
     * <code>GET_CHAT_TIME = 2;</code>
     */
    GET_CHAT_TIME(2),
    /**
     * <pre>
     **
     * 发送聊天消息
     * &lt;pre&gt;
     * 请求:{&#64;code ChatSendMessageRequest}
     * 响应:{&#64;code ChatTimeResponse}
     * &lt;/pre&gt;
     * </pre>
     *
     * <code>SEND_CHAT_MSG = 3;</code>
     */
    SEND_CHAT_MSG(3),
    /**
     * <pre>
     **
     * 删除私人聊天消息
     * &lt;pre&gt;
     * 请求:{&#64;code ChatDeletePersonalRequest}
     * 响应:{&#64;code Response}
     * &lt;/pre&gt;
     * </pre>
     *
     * <code>DELETE_PERSONAL = 4;</code>
     */
    DELETE_PERSONAL(4),
    /**
     * <pre>
     **
     * 获取置顶信息
     * &lt;pre&gt;
     * 请求:{&#64;code Request}
     * 响应:{&#64;code LongListPacket} 被置顶人的actorId
     * &lt;/pre&gt;
     * </pre>
     *
     * <code>GET_TOP_INFO = 5;</code>
     */
    GET_TOP_INFO(5),
    /**
     * <pre>
     **
     * 增加置顶
     * &lt;pre&gt;
     * 请求:{&#64;code LongPacket} 被增加玩家的ID
     * 响应:{&#64;code LongListPacket} 全部被置顶人的actorId
     * &lt;/pre&gt;
     * </pre>
     *
     * <code>ADD_TOP = 6;</code>
     */
    ADD_TOP(6),
    /**
     * <pre>
     **
     * 删除置顶
     * &lt;pre&gt;
     * 请求:{&#64;code LongPacket} 被删除玩家的ID
     * 响应:{&#64;code LongListPacket}剩余被置顶人的actorId
     * &lt;/pre&gt;
     * </pre>
     *
     * <code>DELETE_TOP = 7;</code>
     */
    DELETE_TOP(7),
    /**
     * <pre>
     **
     * 推送聊天消息
     * &lt;pre&gt;
     * 推送:{&#64;code ChatMessageResponse}
     * &lt;/pre&gt;
     * </pre>
     *
     * <code>PUSH_CHAT_MSG = 100;</code>
     */
    PUSH_CHAT_MSG(100),
    /**
     * <pre>
     **
     * 推送清除消息
     * &lt;pre&gt;
     * 推送:{&#64;code LongPacket} 清除角色ID，0为系统公告
     * &lt;/pre&gt;
     * </pre>
     *
     * <code>PUSH_CLEAR_MSG = 101;</code>
     */
    PUSH_CLEAR_MSG(101),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>CHAT_CMD_NONE = 0;</code>
     */
    public static final int CHAT_CMD_NONE_VALUE = 0;
    /**
     * <pre>
     **SimpleDateFormat
     * 获取聊天信息
     * &lt;pre&gt;
     * 请求:{&#64;code Request}
     * 响应:{&#64;code ChatMessageResponse}
     * &lt;/pre&gt;
     * </pre>
     *
     * <code>GET_CHAT_INFO = 1;</code>
     */
    public static final int GET_CHAT_INFO_VALUE = 1;
    /**
     * <pre>
     **
     * 获取聊天时间
     * &lt;pre&gt;
     * 请求:{&#64;code Request}
     * 响应:{&#64;code ChatTimeResponse}
     * &lt;/pre&gt;
     * </pre>
     *
     * <code>GET_CHAT_TIME = 2;</code>
     */
    public static final int GET_CHAT_TIME_VALUE = 2;
    /**
     * <pre>
     **
     * 发送聊天消息
     * &lt;pre&gt;
     * 请求:{&#64;code ChatSendMessageRequest}
     * 响应:{&#64;code ChatTimeResponse}
     * &lt;/pre&gt;
     * </pre>
     *
     * <code>SEND_CHAT_MSG = 3;</code>
     */
    public static final int SEND_CHAT_MSG_VALUE = 3;
    /**
     * <pre>
     **
     * 删除私人聊天消息
     * &lt;pre&gt;
     * 请求:{&#64;code ChatDeletePersonalRequest}
     * 响应:{&#64;code Response}
     * &lt;/pre&gt;
     * </pre>
     *
     * <code>DELETE_PERSONAL = 4;</code>
     */
    public static final int DELETE_PERSONAL_VALUE = 4;
    /**
     * <pre>
     **
     * 获取置顶信息
     * &lt;pre&gt;
     * 请求:{&#64;code Request}
     * 响应:{&#64;code LongListPacket} 被置顶人的actorId
     * &lt;/pre&gt;
     * </pre>
     *
     * <code>GET_TOP_INFO = 5;</code>
     */
    public static final int GET_TOP_INFO_VALUE = 5;
    /**
     * <pre>
     **
     * 增加置顶
     * &lt;pre&gt;
     * 请求:{&#64;code LongPacket} 被增加玩家的ID
     * 响应:{&#64;code LongListPacket} 全部被置顶人的actorId
     * &lt;/pre&gt;
     * </pre>
     *
     * <code>ADD_TOP = 6;</code>
     */
    public static final int ADD_TOP_VALUE = 6;
    /**
     * <pre>
     **
     * 删除置顶
     * &lt;pre&gt;
     * 请求:{&#64;code LongPacket} 被删除玩家的ID
     * 响应:{&#64;code LongListPacket}剩余被置顶人的actorId
     * &lt;/pre&gt;
     * </pre>
     *
     * <code>DELETE_TOP = 7;</code>
     */
    public static final int DELETE_TOP_VALUE = 7;
    /**
     * <pre>
     **
     * 推送聊天消息
     * &lt;pre&gt;
     * 推送:{&#64;code ChatMessageResponse}
     * &lt;/pre&gt;
     * </pre>
     *
     * <code>PUSH_CHAT_MSG = 100;</code>
     */
    public static final int PUSH_CHAT_MSG_VALUE = 100;
    /**
     * <pre>
     **
     * 推送清除消息
     * &lt;pre&gt;
     * 推送:{&#64;code LongPacket} 清除角色ID，0为系统公告
     * &lt;/pre&gt;
     * </pre>
     *
     * <code>PUSH_CLEAR_MSG = 101;</code>
     */
    public static final int PUSH_CLEAR_MSG_VALUE = 101;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static ChatCmd valueOf(int value) {
      return forNumber(value);
    }

    public static ChatCmd forNumber(int value) {
      switch (value) {
        case 0: return CHAT_CMD_NONE;
        case 1: return GET_CHAT_INFO;
        case 2: return GET_CHAT_TIME;
        case 3: return SEND_CHAT_MSG;
        case 4: return DELETE_PERSONAL;
        case 5: return GET_TOP_INFO;
        case 6: return ADD_TOP;
        case 7: return DELETE_TOP;
        case 100: return PUSH_CHAT_MSG;
        case 101: return PUSH_CLEAR_MSG;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<ChatCmd>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        ChatCmd> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<ChatCmd>() {
            public ChatCmd findValueByNumber(int number) {
              return ChatCmd.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.ChatProtocol.getDescriptor().getEnumTypes().get(0);
    }

    private static final ChatCmd[] VALUES = values();

    public static ChatCmd valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private ChatCmd(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:ChatCmd)
  }

  public interface ChatMessageResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:ChatMessageResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     ** 聊天消息
     * </pre>
     *
     * <code>repeated .ChatMessage chatMessages = 1;</code>
     */
    java.util.List<cn.daxiang.protocol.game.ChatProtocol.ChatMessage> 
        getChatMessagesList();
    /**
     * <pre>
     ** 聊天消息
     * </pre>
     *
     * <code>repeated .ChatMessage chatMessages = 1;</code>
     */
    cn.daxiang.protocol.game.ChatProtocol.ChatMessage getChatMessages(int index);
    /**
     * <pre>
     ** 聊天消息
     * </pre>
     *
     * <code>repeated .ChatMessage chatMessages = 1;</code>
     */
    int getChatMessagesCount();
    /**
     * <pre>
     ** 聊天消息
     * </pre>
     *
     * <code>repeated .ChatMessage chatMessages = 1;</code>
     */
    java.util.List<? extends cn.daxiang.protocol.game.ChatProtocol.ChatMessageOrBuilder> 
        getChatMessagesOrBuilderList();
    /**
     * <pre>
     ** 聊天消息
     * </pre>
     *
     * <code>repeated .ChatMessage chatMessages = 1;</code>
     */
    cn.daxiang.protocol.game.ChatProtocol.ChatMessageOrBuilder getChatMessagesOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code ChatMessageResponse}
   */
  public  static final class ChatMessageResponse extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:ChatMessageResponse)
      ChatMessageResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ChatMessageResponse.newBuilder() to construct.
    private ChatMessageResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ChatMessageResponse() {
      chatMessages_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ChatMessageResponse(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
                chatMessages_ = new java.util.ArrayList<cn.daxiang.protocol.game.ChatProtocol.ChatMessage>();
                mutable_bitField0_ |= 0x00000001;
              }
              chatMessages_.add(
                  input.readMessage(cn.daxiang.protocol.game.ChatProtocol.ChatMessage.parser(), extensionRegistry));
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
          chatMessages_ = java.util.Collections.unmodifiableList(chatMessages_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.ChatProtocol.internal_static_ChatMessageResponse_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.daxiang.protocol.game.ChatProtocol.internal_static_ChatMessageResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.daxiang.protocol.game.ChatProtocol.ChatMessageResponse.class, cn.daxiang.protocol.game.ChatProtocol.ChatMessageResponse.Builder.class);
    }

    public static final int CHATMESSAGES_FIELD_NUMBER = 1;
    private java.util.List<cn.daxiang.protocol.game.ChatProtocol.ChatMessage> chatMessages_;
    /**
     * <pre>
     ** 聊天消息
     * </pre>
     *
     * <code>repeated .ChatMessage chatMessages = 1;</code>
     */
    public java.util.List<cn.daxiang.protocol.game.ChatProtocol.ChatMessage> getChatMessagesList() {
      return chatMessages_;
    }
    /**
     * <pre>
     ** 聊天消息
     * </pre>
     *
     * <code>repeated .ChatMessage chatMessages = 1;</code>
     */
    public java.util.List<? extends cn.daxiang.protocol.game.ChatProtocol.ChatMessageOrBuilder> 
        getChatMessagesOrBuilderList() {
      return chatMessages_;
    }
    /**
     * <pre>
     ** 聊天消息
     * </pre>
     *
     * <code>repeated .ChatMessage chatMessages = 1;</code>
     */
    public int getChatMessagesCount() {
      return chatMessages_.size();
    }
    /**
     * <pre>
     ** 聊天消息
     * </pre>
     *
     * <code>repeated .ChatMessage chatMessages = 1;</code>
     */
    public cn.daxiang.protocol.game.ChatProtocol.ChatMessage getChatMessages(int index) {
      return chatMessages_.get(index);
    }
    /**
     * <pre>
     ** 聊天消息
     * </pre>
     *
     * <code>repeated .ChatMessage chatMessages = 1;</code>
     */
    public cn.daxiang.protocol.game.ChatProtocol.ChatMessageOrBuilder getChatMessagesOrBuilder(
        int index) {
      return chatMessages_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < chatMessages_.size(); i++) {
        output.writeMessage(1, chatMessages_.get(i));
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < chatMessages_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, chatMessages_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof cn.daxiang.protocol.game.ChatProtocol.ChatMessageResponse)) {
        return super.equals(obj);
      }
      cn.daxiang.protocol.game.ChatProtocol.ChatMessageResponse other = (cn.daxiang.protocol.game.ChatProtocol.ChatMessageResponse) obj;

      boolean result = true;
      result = result && getChatMessagesList()
          .equals(other.getChatMessagesList());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getChatMessagesCount() > 0) {
        hash = (37 * hash) + CHATMESSAGES_FIELD_NUMBER;
        hash = (53 * hash) + getChatMessagesList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static cn.daxiang.protocol.game.ChatProtocol.ChatMessageResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.ChatProtocol.ChatMessageResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.ChatProtocol.ChatMessageResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.ChatProtocol.ChatMessageResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.ChatProtocol.ChatMessageResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.ChatProtocol.ChatMessageResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.ChatProtocol.ChatMessageResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.ChatProtocol.ChatMessageResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.ChatProtocol.ChatMessageResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.ChatProtocol.ChatMessageResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.ChatProtocol.ChatMessageResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.ChatProtocol.ChatMessageResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(cn.daxiang.protocol.game.ChatProtocol.ChatMessageResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code ChatMessageResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:ChatMessageResponse)
        cn.daxiang.protocol.game.ChatProtocol.ChatMessageResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.daxiang.protocol.game.ChatProtocol.internal_static_ChatMessageResponse_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.daxiang.protocol.game.ChatProtocol.internal_static_ChatMessageResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.daxiang.protocol.game.ChatProtocol.ChatMessageResponse.class, cn.daxiang.protocol.game.ChatProtocol.ChatMessageResponse.Builder.class);
      }

      // Construct using cn.daxiang.protocol.game.ChatProtocol.ChatMessageResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getChatMessagesFieldBuilder();
        }
      }
      public Builder clear() {
        super.clear();
        if (chatMessagesBuilder_ == null) {
          chatMessages_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          chatMessagesBuilder_.clear();
        }
        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return cn.daxiang.protocol.game.ChatProtocol.internal_static_ChatMessageResponse_descriptor;
      }

      public cn.daxiang.protocol.game.ChatProtocol.ChatMessageResponse getDefaultInstanceForType() {
        return cn.daxiang.protocol.game.ChatProtocol.ChatMessageResponse.getDefaultInstance();
      }

      public cn.daxiang.protocol.game.ChatProtocol.ChatMessageResponse build() {
        cn.daxiang.protocol.game.ChatProtocol.ChatMessageResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public cn.daxiang.protocol.game.ChatProtocol.ChatMessageResponse buildPartial() {
        cn.daxiang.protocol.game.ChatProtocol.ChatMessageResponse result = new cn.daxiang.protocol.game.ChatProtocol.ChatMessageResponse(this);
        int from_bitField0_ = bitField0_;
        if (chatMessagesBuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001)) {
            chatMessages_ = java.util.Collections.unmodifiableList(chatMessages_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.chatMessages_ = chatMessages_;
        } else {
          result.chatMessages_ = chatMessagesBuilder_.build();
        }
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof cn.daxiang.protocol.game.ChatProtocol.ChatMessageResponse) {
          return mergeFrom((cn.daxiang.protocol.game.ChatProtocol.ChatMessageResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(cn.daxiang.protocol.game.ChatProtocol.ChatMessageResponse other) {
        if (other == cn.daxiang.protocol.game.ChatProtocol.ChatMessageResponse.getDefaultInstance()) return this;
        if (chatMessagesBuilder_ == null) {
          if (!other.chatMessages_.isEmpty()) {
            if (chatMessages_.isEmpty()) {
              chatMessages_ = other.chatMessages_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureChatMessagesIsMutable();
              chatMessages_.addAll(other.chatMessages_);
            }
            onChanged();
          }
        } else {
          if (!other.chatMessages_.isEmpty()) {
            if (chatMessagesBuilder_.isEmpty()) {
              chatMessagesBuilder_.dispose();
              chatMessagesBuilder_ = null;
              chatMessages_ = other.chatMessages_;
              bitField0_ = (bitField0_ & ~0x00000001);
              chatMessagesBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getChatMessagesFieldBuilder() : null;
            } else {
              chatMessagesBuilder_.addAllMessages(other.chatMessages_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        cn.daxiang.protocol.game.ChatProtocol.ChatMessageResponse parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (cn.daxiang.protocol.game.ChatProtocol.ChatMessageResponse) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<cn.daxiang.protocol.game.ChatProtocol.ChatMessage> chatMessages_ =
        java.util.Collections.emptyList();
      private void ensureChatMessagesIsMutable() {
        if (!((bitField0_ & 0x00000001) == 0x00000001)) {
          chatMessages_ = new java.util.ArrayList<cn.daxiang.protocol.game.ChatProtocol.ChatMessage>(chatMessages_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          cn.daxiang.protocol.game.ChatProtocol.ChatMessage, cn.daxiang.protocol.game.ChatProtocol.ChatMessage.Builder, cn.daxiang.protocol.game.ChatProtocol.ChatMessageOrBuilder> chatMessagesBuilder_;

      /**
       * <pre>
       ** 聊天消息
       * </pre>
       *
       * <code>repeated .ChatMessage chatMessages = 1;</code>
       */
      public java.util.List<cn.daxiang.protocol.game.ChatProtocol.ChatMessage> getChatMessagesList() {
        if (chatMessagesBuilder_ == null) {
          return java.util.Collections.unmodifiableList(chatMessages_);
        } else {
          return chatMessagesBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       ** 聊天消息
       * </pre>
       *
       * <code>repeated .ChatMessage chatMessages = 1;</code>
       */
      public int getChatMessagesCount() {
        if (chatMessagesBuilder_ == null) {
          return chatMessages_.size();
        } else {
          return chatMessagesBuilder_.getCount();
        }
      }
      /**
       * <pre>
       ** 聊天消息
       * </pre>
       *
       * <code>repeated .ChatMessage chatMessages = 1;</code>
       */
      public cn.daxiang.protocol.game.ChatProtocol.ChatMessage getChatMessages(int index) {
        if (chatMessagesBuilder_ == null) {
          return chatMessages_.get(index);
        } else {
          return chatMessagesBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       ** 聊天消息
       * </pre>
       *
       * <code>repeated .ChatMessage chatMessages = 1;</code>
       */
      public Builder setChatMessages(
          int index, cn.daxiang.protocol.game.ChatProtocol.ChatMessage value) {
        if (chatMessagesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureChatMessagesIsMutable();
          chatMessages_.set(index, value);
          onChanged();
        } else {
          chatMessagesBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       ** 聊天消息
       * </pre>
       *
       * <code>repeated .ChatMessage chatMessages = 1;</code>
       */
      public Builder setChatMessages(
          int index, cn.daxiang.protocol.game.ChatProtocol.ChatMessage.Builder builderForValue) {
        if (chatMessagesBuilder_ == null) {
          ensureChatMessagesIsMutable();
          chatMessages_.set(index, builderForValue.build());
          onChanged();
        } else {
          chatMessagesBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       ** 聊天消息
       * </pre>
       *
       * <code>repeated .ChatMessage chatMessages = 1;</code>
       */
      public Builder addChatMessages(cn.daxiang.protocol.game.ChatProtocol.ChatMessage value) {
        if (chatMessagesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureChatMessagesIsMutable();
          chatMessages_.add(value);
          onChanged();
        } else {
          chatMessagesBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       ** 聊天消息
       * </pre>
       *
       * <code>repeated .ChatMessage chatMessages = 1;</code>
       */
      public Builder addChatMessages(
          int index, cn.daxiang.protocol.game.ChatProtocol.ChatMessage value) {
        if (chatMessagesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureChatMessagesIsMutable();
          chatMessages_.add(index, value);
          onChanged();
        } else {
          chatMessagesBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       ** 聊天消息
       * </pre>
       *
       * <code>repeated .ChatMessage chatMessages = 1;</code>
       */
      public Builder addChatMessages(
          cn.daxiang.protocol.game.ChatProtocol.ChatMessage.Builder builderForValue) {
        if (chatMessagesBuilder_ == null) {
          ensureChatMessagesIsMutable();
          chatMessages_.add(builderForValue.build());
          onChanged();
        } else {
          chatMessagesBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       ** 聊天消息
       * </pre>
       *
       * <code>repeated .ChatMessage chatMessages = 1;</code>
       */
      public Builder addChatMessages(
          int index, cn.daxiang.protocol.game.ChatProtocol.ChatMessage.Builder builderForValue) {
        if (chatMessagesBuilder_ == null) {
          ensureChatMessagesIsMutable();
          chatMessages_.add(index, builderForValue.build());
          onChanged();
        } else {
          chatMessagesBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       ** 聊天消息
       * </pre>
       *
       * <code>repeated .ChatMessage chatMessages = 1;</code>
       */
      public Builder addAllChatMessages(
          java.lang.Iterable<? extends cn.daxiang.protocol.game.ChatProtocol.ChatMessage> values) {
        if (chatMessagesBuilder_ == null) {
          ensureChatMessagesIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, chatMessages_);
          onChanged();
        } else {
          chatMessagesBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       ** 聊天消息
       * </pre>
       *
       * <code>repeated .ChatMessage chatMessages = 1;</code>
       */
      public Builder clearChatMessages() {
        if (chatMessagesBuilder_ == null) {
          chatMessages_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          chatMessagesBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       ** 聊天消息
       * </pre>
       *
       * <code>repeated .ChatMessage chatMessages = 1;</code>
       */
      public Builder removeChatMessages(int index) {
        if (chatMessagesBuilder_ == null) {
          ensureChatMessagesIsMutable();
          chatMessages_.remove(index);
          onChanged();
        } else {
          chatMessagesBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       ** 聊天消息
       * </pre>
       *
       * <code>repeated .ChatMessage chatMessages = 1;</code>
       */
      public cn.daxiang.protocol.game.ChatProtocol.ChatMessage.Builder getChatMessagesBuilder(
          int index) {
        return getChatMessagesFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       ** 聊天消息
       * </pre>
       *
       * <code>repeated .ChatMessage chatMessages = 1;</code>
       */
      public cn.daxiang.protocol.game.ChatProtocol.ChatMessageOrBuilder getChatMessagesOrBuilder(
          int index) {
        if (chatMessagesBuilder_ == null) {
          return chatMessages_.get(index);  } else {
          return chatMessagesBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       ** 聊天消息
       * </pre>
       *
       * <code>repeated .ChatMessage chatMessages = 1;</code>
       */
      public java.util.List<? extends cn.daxiang.protocol.game.ChatProtocol.ChatMessageOrBuilder> 
           getChatMessagesOrBuilderList() {
        if (chatMessagesBuilder_ != null) {
          return chatMessagesBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(chatMessages_);
        }
      }
      /**
       * <pre>
       ** 聊天消息
       * </pre>
       *
       * <code>repeated .ChatMessage chatMessages = 1;</code>
       */
      public cn.daxiang.protocol.game.ChatProtocol.ChatMessage.Builder addChatMessagesBuilder() {
        return getChatMessagesFieldBuilder().addBuilder(
            cn.daxiang.protocol.game.ChatProtocol.ChatMessage.getDefaultInstance());
      }
      /**
       * <pre>
       ** 聊天消息
       * </pre>
       *
       * <code>repeated .ChatMessage chatMessages = 1;</code>
       */
      public cn.daxiang.protocol.game.ChatProtocol.ChatMessage.Builder addChatMessagesBuilder(
          int index) {
        return getChatMessagesFieldBuilder().addBuilder(
            index, cn.daxiang.protocol.game.ChatProtocol.ChatMessage.getDefaultInstance());
      }
      /**
       * <pre>
       ** 聊天消息
       * </pre>
       *
       * <code>repeated .ChatMessage chatMessages = 1;</code>
       */
      public java.util.List<cn.daxiang.protocol.game.ChatProtocol.ChatMessage.Builder> 
           getChatMessagesBuilderList() {
        return getChatMessagesFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          cn.daxiang.protocol.game.ChatProtocol.ChatMessage, cn.daxiang.protocol.game.ChatProtocol.ChatMessage.Builder, cn.daxiang.protocol.game.ChatProtocol.ChatMessageOrBuilder> 
          getChatMessagesFieldBuilder() {
        if (chatMessagesBuilder_ == null) {
          chatMessagesBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              cn.daxiang.protocol.game.ChatProtocol.ChatMessage, cn.daxiang.protocol.game.ChatProtocol.ChatMessage.Builder, cn.daxiang.protocol.game.ChatProtocol.ChatMessageOrBuilder>(
                  chatMessages_,
                  ((bitField0_ & 0x00000001) == 0x00000001),
                  getParentForChildren(),
                  isClean());
          chatMessages_ = null;
        }
        return chatMessagesBuilder_;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:ChatMessageResponse)
    }

    // @@protoc_insertion_point(class_scope:ChatMessageResponse)
    private static final cn.daxiang.protocol.game.ChatProtocol.ChatMessageResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new cn.daxiang.protocol.game.ChatProtocol.ChatMessageResponse();
    }

    public static cn.daxiang.protocol.game.ChatProtocol.ChatMessageResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ChatMessageResponse>
        PARSER = new com.google.protobuf.AbstractParser<ChatMessageResponse>() {
      public ChatMessageResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ChatMessageResponse(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ChatMessageResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ChatMessageResponse> getParserForType() {
      return PARSER;
    }

    public cn.daxiang.protocol.game.ChatProtocol.ChatMessageResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ChatMessageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:ChatMessage)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     **消息唯一id
     * </pre>
     *
     * <code>int64 msgId = 1;</code>
     */
    long getMsgId();

    /**
     * <pre>
     **频道类型
     * </pre>
     *
     * <code>.ChatChannelType channelType = 2;</code>
     */
    int getChannelTypeValue();
    /**
     * <pre>
     **频道类型
     * </pre>
     *
     * <code>.ChatChannelType channelType = 2;</code>
     */
    cn.daxiang.protocol.game.TypeProtocol.ChatChannelType getChannelType();

    /**
     * <pre>
     **发送者id， 若为0则是系统消息
     * </pre>
     *
     * <code>int64 fromActorId = 3;</code>
     */
    long getFromActorId();

    /**
     * <pre>
     **接受者id
     * </pre>
     *
     * <code>int64 targeterId = 4;</code>
     */
    long getTargeterId();

    /**
     * <pre>
     **玩家信息列表-ChatInfo中所涉及到的角色id所对应的各种属性
     * </pre>
     *
     * <code>repeated .ActorProfile actorAttributes = 5;</code>
     */
    java.util.List<cn.daxiang.protocol.game.CommonProtocol.ActorProfile> 
        getActorAttributesList();
    /**
     * <pre>
     **玩家信息列表-ChatInfo中所涉及到的角色id所对应的各种属性
     * </pre>
     *
     * <code>repeated .ActorProfile actorAttributes = 5;</code>
     */
    cn.daxiang.protocol.game.CommonProtocol.ActorProfile getActorAttributes(int index);
    /**
     * <pre>
     **玩家信息列表-ChatInfo中所涉及到的角色id所对应的各种属性
     * </pre>
     *
     * <code>repeated .ActorProfile actorAttributes = 5;</code>
     */
    int getActorAttributesCount();
    /**
     * <pre>
     **玩家信息列表-ChatInfo中所涉及到的角色id所对应的各种属性
     * </pre>
     *
     * <code>repeated .ActorProfile actorAttributes = 5;</code>
     */
    java.util.List<? extends cn.daxiang.protocol.game.CommonProtocol.ActorProfileOrBuilder> 
        getActorAttributesOrBuilderList();
    /**
     * <pre>
     **玩家信息列表-ChatInfo中所涉及到的角色id所对应的各种属性
     * </pre>
     *
     * <code>repeated .ActorProfile actorAttributes = 5;</code>
     */
    cn.daxiang.protocol.game.CommonProtocol.ActorProfileOrBuilder getActorAttributesOrBuilder(
        int index);

    /**
     * <pre>
     **内容 &#64;code ChatInfo
     * </pre>
     *
     * <code>.ChatInfo chatInfo = 6;</code>
     */
    boolean hasChatInfo();
    /**
     * <pre>
     **内容 &#64;code ChatInfo
     * </pre>
     *
     * <code>.ChatInfo chatInfo = 6;</code>
     */
    cn.daxiang.protocol.game.ChatProtocol.ChatInfo getChatInfo();
    /**
     * <pre>
     **内容 &#64;code ChatInfo
     * </pre>
     *
     * <code>.ChatInfo chatInfo = 6;</code>
     */
    cn.daxiang.protocol.game.ChatProtocol.ChatInfoOrBuilder getChatInfoOrBuilder();

    /**
     * <pre>
     **发送消息时间戳
     * </pre>
     *
     * <code>int64 time = 7;</code>
     */
    long getTime();
  }
  /**
   * <pre>
   **聊天消息显示对象
   * </pre>
   *
   * Protobuf type {@code ChatMessage}
   */
  public  static final class ChatMessage extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:ChatMessage)
      ChatMessageOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ChatMessage.newBuilder() to construct.
    private ChatMessage(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ChatMessage() {
      msgId_ = 0L;
      channelType_ = 0;
      fromActorId_ = 0L;
      targeterId_ = 0L;
      actorAttributes_ = java.util.Collections.emptyList();
      time_ = 0L;
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ChatMessage(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {

              msgId_ = input.readInt64();
              break;
            }
            case 16: {
              int rawValue = input.readEnum();

              channelType_ = rawValue;
              break;
            }
            case 24: {

              fromActorId_ = input.readInt64();
              break;
            }
            case 32: {

              targeterId_ = input.readInt64();
              break;
            }
            case 42: {
              if (!((mutable_bitField0_ & 0x00000010) == 0x00000010)) {
                actorAttributes_ = new java.util.ArrayList<cn.daxiang.protocol.game.CommonProtocol.ActorProfile>();
                mutable_bitField0_ |= 0x00000010;
              }
              actorAttributes_.add(
                  input.readMessage(cn.daxiang.protocol.game.CommonProtocol.ActorProfile.parser(), extensionRegistry));
              break;
            }
            case 50: {
              cn.daxiang.protocol.game.ChatProtocol.ChatInfo.Builder subBuilder = null;
              if (chatInfo_ != null) {
                subBuilder = chatInfo_.toBuilder();
              }
              chatInfo_ = input.readMessage(cn.daxiang.protocol.game.ChatProtocol.ChatInfo.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(chatInfo_);
                chatInfo_ = subBuilder.buildPartial();
              }

              break;
            }
            case 56: {

              time_ = input.readInt64();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000010) == 0x00000010)) {
          actorAttributes_ = java.util.Collections.unmodifiableList(actorAttributes_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.ChatProtocol.internal_static_ChatMessage_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.daxiang.protocol.game.ChatProtocol.internal_static_ChatMessage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.daxiang.protocol.game.ChatProtocol.ChatMessage.class, cn.daxiang.protocol.game.ChatProtocol.ChatMessage.Builder.class);
    }

    private int bitField0_;
    public static final int MSGID_FIELD_NUMBER = 1;
    private long msgId_;
    /**
     * <pre>
     **消息唯一id
     * </pre>
     *
     * <code>int64 msgId = 1;</code>
     */
    public long getMsgId() {
      return msgId_;
    }

    public static final int CHANNELTYPE_FIELD_NUMBER = 2;
    private int channelType_;
    /**
     * <pre>
     **频道类型
     * </pre>
     *
     * <code>.ChatChannelType channelType = 2;</code>
     */
    public int getChannelTypeValue() {
      return channelType_;
    }
    /**
     * <pre>
     **频道类型
     * </pre>
     *
     * <code>.ChatChannelType channelType = 2;</code>
     */
    public cn.daxiang.protocol.game.TypeProtocol.ChatChannelType getChannelType() {
      cn.daxiang.protocol.game.TypeProtocol.ChatChannelType result = cn.daxiang.protocol.game.TypeProtocol.ChatChannelType.valueOf(channelType_);
      return result == null ? cn.daxiang.protocol.game.TypeProtocol.ChatChannelType.UNRECOGNIZED : result;
    }

    public static final int FROMACTORID_FIELD_NUMBER = 3;
    private long fromActorId_;
    /**
     * <pre>
     **发送者id， 若为0则是系统消息
     * </pre>
     *
     * <code>int64 fromActorId = 3;</code>
     */
    public long getFromActorId() {
      return fromActorId_;
    }

    public static final int TARGETERID_FIELD_NUMBER = 4;
    private long targeterId_;
    /**
     * <pre>
     **接受者id
     * </pre>
     *
     * <code>int64 targeterId = 4;</code>
     */
    public long getTargeterId() {
      return targeterId_;
    }

    public static final int ACTORATTRIBUTES_FIELD_NUMBER = 5;
    private java.util.List<cn.daxiang.protocol.game.CommonProtocol.ActorProfile> actorAttributes_;
    /**
     * <pre>
     **玩家信息列表-ChatInfo中所涉及到的角色id所对应的各种属性
     * </pre>
     *
     * <code>repeated .ActorProfile actorAttributes = 5;</code>
     */
    public java.util.List<cn.daxiang.protocol.game.CommonProtocol.ActorProfile> getActorAttributesList() {
      return actorAttributes_;
    }
    /**
     * <pre>
     **玩家信息列表-ChatInfo中所涉及到的角色id所对应的各种属性
     * </pre>
     *
     * <code>repeated .ActorProfile actorAttributes = 5;</code>
     */
    public java.util.List<? extends cn.daxiang.protocol.game.CommonProtocol.ActorProfileOrBuilder> 
        getActorAttributesOrBuilderList() {
      return actorAttributes_;
    }
    /**
     * <pre>
     **玩家信息列表-ChatInfo中所涉及到的角色id所对应的各种属性
     * </pre>
     *
     * <code>repeated .ActorProfile actorAttributes = 5;</code>
     */
    public int getActorAttributesCount() {
      return actorAttributes_.size();
    }
    /**
     * <pre>
     **玩家信息列表-ChatInfo中所涉及到的角色id所对应的各种属性
     * </pre>
     *
     * <code>repeated .ActorProfile actorAttributes = 5;</code>
     */
    public cn.daxiang.protocol.game.CommonProtocol.ActorProfile getActorAttributes(int index) {
      return actorAttributes_.get(index);
    }
    /**
     * <pre>
     **玩家信息列表-ChatInfo中所涉及到的角色id所对应的各种属性
     * </pre>
     *
     * <code>repeated .ActorProfile actorAttributes = 5;</code>
     */
    public cn.daxiang.protocol.game.CommonProtocol.ActorProfileOrBuilder getActorAttributesOrBuilder(
        int index) {
      return actorAttributes_.get(index);
    }

    public static final int CHATINFO_FIELD_NUMBER = 6;
    private cn.daxiang.protocol.game.ChatProtocol.ChatInfo chatInfo_;
    /**
     * <pre>
     **内容 &#64;code ChatInfo
     * </pre>
     *
     * <code>.ChatInfo chatInfo = 6;</code>
     */
    public boolean hasChatInfo() {
      return chatInfo_ != null;
    }
    /**
     * <pre>
     **内容 &#64;code ChatInfo
     * </pre>
     *
     * <code>.ChatInfo chatInfo = 6;</code>
     */
    public cn.daxiang.protocol.game.ChatProtocol.ChatInfo getChatInfo() {
      return chatInfo_ == null ? cn.daxiang.protocol.game.ChatProtocol.ChatInfo.getDefaultInstance() : chatInfo_;
    }
    /**
     * <pre>
     **内容 &#64;code ChatInfo
     * </pre>
     *
     * <code>.ChatInfo chatInfo = 6;</code>
     */
    public cn.daxiang.protocol.game.ChatProtocol.ChatInfoOrBuilder getChatInfoOrBuilder() {
      return getChatInfo();
    }

    public static final int TIME_FIELD_NUMBER = 7;
    private long time_;
    /**
     * <pre>
     **发送消息时间戳
     * </pre>
     *
     * <code>int64 time = 7;</code>
     */
    public long getTime() {
      return time_;
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (msgId_ != 0L) {
        output.writeInt64(1, msgId_);
      }
      if (channelType_ != cn.daxiang.protocol.game.TypeProtocol.ChatChannelType.CHAT_CHANNEL_TYPE_NONE.getNumber()) {
        output.writeEnum(2, channelType_);
      }
      if (fromActorId_ != 0L) {
        output.writeInt64(3, fromActorId_);
      }
      if (targeterId_ != 0L) {
        output.writeInt64(4, targeterId_);
      }
      for (int i = 0; i < actorAttributes_.size(); i++) {
        output.writeMessage(5, actorAttributes_.get(i));
      }
      if (chatInfo_ != null) {
        output.writeMessage(6, getChatInfo());
      }
      if (time_ != 0L) {
        output.writeInt64(7, time_);
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (msgId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, msgId_);
      }
      if (channelType_ != cn.daxiang.protocol.game.TypeProtocol.ChatChannelType.CHAT_CHANNEL_TYPE_NONE.getNumber()) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(2, channelType_);
      }
      if (fromActorId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, fromActorId_);
      }
      if (targeterId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(4, targeterId_);
      }
      for (int i = 0; i < actorAttributes_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(5, actorAttributes_.get(i));
      }
      if (chatInfo_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(6, getChatInfo());
      }
      if (time_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(7, time_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof cn.daxiang.protocol.game.ChatProtocol.ChatMessage)) {
        return super.equals(obj);
      }
      cn.daxiang.protocol.game.ChatProtocol.ChatMessage other = (cn.daxiang.protocol.game.ChatProtocol.ChatMessage) obj;

      boolean result = true;
      result = result && (getMsgId()
          == other.getMsgId());
      result = result && channelType_ == other.channelType_;
      result = result && (getFromActorId()
          == other.getFromActorId());
      result = result && (getTargeterId()
          == other.getTargeterId());
      result = result && getActorAttributesList()
          .equals(other.getActorAttributesList());
      result = result && (hasChatInfo() == other.hasChatInfo());
      if (hasChatInfo()) {
        result = result && getChatInfo()
            .equals(other.getChatInfo());
      }
      result = result && (getTime()
          == other.getTime());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + MSGID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getMsgId());
      hash = (37 * hash) + CHANNELTYPE_FIELD_NUMBER;
      hash = (53 * hash) + channelType_;
      hash = (37 * hash) + FROMACTORID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getFromActorId());
      hash = (37 * hash) + TARGETERID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTargeterId());
      if (getActorAttributesCount() > 0) {
        hash = (37 * hash) + ACTORATTRIBUTES_FIELD_NUMBER;
        hash = (53 * hash) + getActorAttributesList().hashCode();
      }
      if (hasChatInfo()) {
        hash = (37 * hash) + CHATINFO_FIELD_NUMBER;
        hash = (53 * hash) + getChatInfo().hashCode();
      }
      hash = (37 * hash) + TIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTime());
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static cn.daxiang.protocol.game.ChatProtocol.ChatMessage parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.ChatProtocol.ChatMessage parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.ChatProtocol.ChatMessage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.ChatProtocol.ChatMessage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.ChatProtocol.ChatMessage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.ChatProtocol.ChatMessage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.ChatProtocol.ChatMessage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.ChatProtocol.ChatMessage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.ChatProtocol.ChatMessage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.ChatProtocol.ChatMessage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.ChatProtocol.ChatMessage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.ChatProtocol.ChatMessage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(cn.daxiang.protocol.game.ChatProtocol.ChatMessage prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     **聊天消息显示对象
     * </pre>
     *
     * Protobuf type {@code ChatMessage}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:ChatMessage)
        cn.daxiang.protocol.game.ChatProtocol.ChatMessageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.daxiang.protocol.game.ChatProtocol.internal_static_ChatMessage_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.daxiang.protocol.game.ChatProtocol.internal_static_ChatMessage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.daxiang.protocol.game.ChatProtocol.ChatMessage.class, cn.daxiang.protocol.game.ChatProtocol.ChatMessage.Builder.class);
      }

      // Construct using cn.daxiang.protocol.game.ChatProtocol.ChatMessage.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getActorAttributesFieldBuilder();
        }
      }
      public Builder clear() {
        super.clear();
        msgId_ = 0L;

        channelType_ = 0;

        fromActorId_ = 0L;

        targeterId_ = 0L;

        if (actorAttributesBuilder_ == null) {
          actorAttributes_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000010);
        } else {
          actorAttributesBuilder_.clear();
        }
        if (chatInfoBuilder_ == null) {
          chatInfo_ = null;
        } else {
          chatInfo_ = null;
          chatInfoBuilder_ = null;
        }
        time_ = 0L;

        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return cn.daxiang.protocol.game.ChatProtocol.internal_static_ChatMessage_descriptor;
      }

      public cn.daxiang.protocol.game.ChatProtocol.ChatMessage getDefaultInstanceForType() {
        return cn.daxiang.protocol.game.ChatProtocol.ChatMessage.getDefaultInstance();
      }

      public cn.daxiang.protocol.game.ChatProtocol.ChatMessage build() {
        cn.daxiang.protocol.game.ChatProtocol.ChatMessage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public cn.daxiang.protocol.game.ChatProtocol.ChatMessage buildPartial() {
        cn.daxiang.protocol.game.ChatProtocol.ChatMessage result = new cn.daxiang.protocol.game.ChatProtocol.ChatMessage(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        result.msgId_ = msgId_;
        result.channelType_ = channelType_;
        result.fromActorId_ = fromActorId_;
        result.targeterId_ = targeterId_;
        if (actorAttributesBuilder_ == null) {
          if (((bitField0_ & 0x00000010) == 0x00000010)) {
            actorAttributes_ = java.util.Collections.unmodifiableList(actorAttributes_);
            bitField0_ = (bitField0_ & ~0x00000010);
          }
          result.actorAttributes_ = actorAttributes_;
        } else {
          result.actorAttributes_ = actorAttributesBuilder_.build();
        }
        if (chatInfoBuilder_ == null) {
          result.chatInfo_ = chatInfo_;
        } else {
          result.chatInfo_ = chatInfoBuilder_.build();
        }
        result.time_ = time_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof cn.daxiang.protocol.game.ChatProtocol.ChatMessage) {
          return mergeFrom((cn.daxiang.protocol.game.ChatProtocol.ChatMessage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(cn.daxiang.protocol.game.ChatProtocol.ChatMessage other) {
        if (other == cn.daxiang.protocol.game.ChatProtocol.ChatMessage.getDefaultInstance()) return this;
        if (other.getMsgId() != 0L) {
          setMsgId(other.getMsgId());
        }
        if (other.channelType_ != 0) {
          setChannelTypeValue(other.getChannelTypeValue());
        }
        if (other.getFromActorId() != 0L) {
          setFromActorId(other.getFromActorId());
        }
        if (other.getTargeterId() != 0L) {
          setTargeterId(other.getTargeterId());
        }
        if (actorAttributesBuilder_ == null) {
          if (!other.actorAttributes_.isEmpty()) {
            if (actorAttributes_.isEmpty()) {
              actorAttributes_ = other.actorAttributes_;
              bitField0_ = (bitField0_ & ~0x00000010);
            } else {
              ensureActorAttributesIsMutable();
              actorAttributes_.addAll(other.actorAttributes_);
            }
            onChanged();
          }
        } else {
          if (!other.actorAttributes_.isEmpty()) {
            if (actorAttributesBuilder_.isEmpty()) {
              actorAttributesBuilder_.dispose();
              actorAttributesBuilder_ = null;
              actorAttributes_ = other.actorAttributes_;
              bitField0_ = (bitField0_ & ~0x00000010);
              actorAttributesBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getActorAttributesFieldBuilder() : null;
            } else {
              actorAttributesBuilder_.addAllMessages(other.actorAttributes_);
            }
          }
        }
        if (other.hasChatInfo()) {
          mergeChatInfo(other.getChatInfo());
        }
        if (other.getTime() != 0L) {
          setTime(other.getTime());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        cn.daxiang.protocol.game.ChatProtocol.ChatMessage parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (cn.daxiang.protocol.game.ChatProtocol.ChatMessage) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long msgId_ ;
      /**
       * <pre>
       **消息唯一id
       * </pre>
       *
       * <code>int64 msgId = 1;</code>
       */
      public long getMsgId() {
        return msgId_;
      }
      /**
       * <pre>
       **消息唯一id
       * </pre>
       *
       * <code>int64 msgId = 1;</code>
       */
      public Builder setMsgId(long value) {
        
        msgId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       **消息唯一id
       * </pre>
       *
       * <code>int64 msgId = 1;</code>
       */
      public Builder clearMsgId() {
        
        msgId_ = 0L;
        onChanged();
        return this;
      }

      private int channelType_ = 0;
      /**
       * <pre>
       **频道类型
       * </pre>
       *
       * <code>.ChatChannelType channelType = 2;</code>
       */
      public int getChannelTypeValue() {
        return channelType_;
      }
      /**
       * <pre>
       **频道类型
       * </pre>
       *
       * <code>.ChatChannelType channelType = 2;</code>
       */
      public Builder setChannelTypeValue(int value) {
        channelType_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       **频道类型
       * </pre>
       *
       * <code>.ChatChannelType channelType = 2;</code>
       */
      public cn.daxiang.protocol.game.TypeProtocol.ChatChannelType getChannelType() {
        cn.daxiang.protocol.game.TypeProtocol.ChatChannelType result = cn.daxiang.protocol.game.TypeProtocol.ChatChannelType.valueOf(channelType_);
        return result == null ? cn.daxiang.protocol.game.TypeProtocol.ChatChannelType.UNRECOGNIZED : result;
      }
      /**
       * <pre>
       **频道类型
       * </pre>
       *
       * <code>.ChatChannelType channelType = 2;</code>
       */
      public Builder setChannelType(cn.daxiang.protocol.game.TypeProtocol.ChatChannelType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        
        channelType_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       **频道类型
       * </pre>
       *
       * <code>.ChatChannelType channelType = 2;</code>
       */
      public Builder clearChannelType() {
        
        channelType_ = 0;
        onChanged();
        return this;
      }

      private long fromActorId_ ;
      /**
       * <pre>
       **发送者id， 若为0则是系统消息
       * </pre>
       *
       * <code>int64 fromActorId = 3;</code>
       */
      public long getFromActorId() {
        return fromActorId_;
      }
      /**
       * <pre>
       **发送者id， 若为0则是系统消息
       * </pre>
       *
       * <code>int64 fromActorId = 3;</code>
       */
      public Builder setFromActorId(long value) {
        
        fromActorId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       **发送者id， 若为0则是系统消息
       * </pre>
       *
       * <code>int64 fromActorId = 3;</code>
       */
      public Builder clearFromActorId() {
        
        fromActorId_ = 0L;
        onChanged();
        return this;
      }

      private long targeterId_ ;
      /**
       * <pre>
       **接受者id
       * </pre>
       *
       * <code>int64 targeterId = 4;</code>
       */
      public long getTargeterId() {
        return targeterId_;
      }
      /**
       * <pre>
       **接受者id
       * </pre>
       *
       * <code>int64 targeterId = 4;</code>
       */
      public Builder setTargeterId(long value) {
        
        targeterId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       **接受者id
       * </pre>
       *
       * <code>int64 targeterId = 4;</code>
       */
      public Builder clearTargeterId() {
        
        targeterId_ = 0L;
        onChanged();
        return this;
      }

      private java.util.List<cn.daxiang.protocol.game.CommonProtocol.ActorProfile> actorAttributes_ =
        java.util.Collections.emptyList();
      private void ensureActorAttributesIsMutable() {
        if (!((bitField0_ & 0x00000010) == 0x00000010)) {
          actorAttributes_ = new java.util.ArrayList<cn.daxiang.protocol.game.CommonProtocol.ActorProfile>(actorAttributes_);
          bitField0_ |= 0x00000010;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          cn.daxiang.protocol.game.CommonProtocol.ActorProfile, cn.daxiang.protocol.game.CommonProtocol.ActorProfile.Builder, cn.daxiang.protocol.game.CommonProtocol.ActorProfileOrBuilder> actorAttributesBuilder_;

      /**
       * <pre>
       **玩家信息列表-ChatInfo中所涉及到的角色id所对应的各种属性
       * </pre>
       *
       * <code>repeated .ActorProfile actorAttributes = 5;</code>
       */
      public java.util.List<cn.daxiang.protocol.game.CommonProtocol.ActorProfile> getActorAttributesList() {
        if (actorAttributesBuilder_ == null) {
          return java.util.Collections.unmodifiableList(actorAttributes_);
        } else {
          return actorAttributesBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       **玩家信息列表-ChatInfo中所涉及到的角色id所对应的各种属性
       * </pre>
       *
       * <code>repeated .ActorProfile actorAttributes = 5;</code>
       */
      public int getActorAttributesCount() {
        if (actorAttributesBuilder_ == null) {
          return actorAttributes_.size();
        } else {
          return actorAttributesBuilder_.getCount();
        }
      }
      /**
       * <pre>
       **玩家信息列表-ChatInfo中所涉及到的角色id所对应的各种属性
       * </pre>
       *
       * <code>repeated .ActorProfile actorAttributes = 5;</code>
       */
      public cn.daxiang.protocol.game.CommonProtocol.ActorProfile getActorAttributes(int index) {
        if (actorAttributesBuilder_ == null) {
          return actorAttributes_.get(index);
        } else {
          return actorAttributesBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       **玩家信息列表-ChatInfo中所涉及到的角色id所对应的各种属性
       * </pre>
       *
       * <code>repeated .ActorProfile actorAttributes = 5;</code>
       */
      public Builder setActorAttributes(
          int index, cn.daxiang.protocol.game.CommonProtocol.ActorProfile value) {
        if (actorAttributesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureActorAttributesIsMutable();
          actorAttributes_.set(index, value);
          onChanged();
        } else {
          actorAttributesBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       **玩家信息列表-ChatInfo中所涉及到的角色id所对应的各种属性
       * </pre>
       *
       * <code>repeated .ActorProfile actorAttributes = 5;</code>
       */
      public Builder setActorAttributes(
          int index, cn.daxiang.protocol.game.CommonProtocol.ActorProfile.Builder builderForValue) {
        if (actorAttributesBuilder_ == null) {
          ensureActorAttributesIsMutable();
          actorAttributes_.set(index, builderForValue.build());
          onChanged();
        } else {
          actorAttributesBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       **玩家信息列表-ChatInfo中所涉及到的角色id所对应的各种属性
       * </pre>
       *
       * <code>repeated .ActorProfile actorAttributes = 5;</code>
       */
      public Builder addActorAttributes(cn.daxiang.protocol.game.CommonProtocol.ActorProfile value) {
        if (actorAttributesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureActorAttributesIsMutable();
          actorAttributes_.add(value);
          onChanged();
        } else {
          actorAttributesBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       **玩家信息列表-ChatInfo中所涉及到的角色id所对应的各种属性
       * </pre>
       *
       * <code>repeated .ActorProfile actorAttributes = 5;</code>
       */
      public Builder addActorAttributes(
          int index, cn.daxiang.protocol.game.CommonProtocol.ActorProfile value) {
        if (actorAttributesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureActorAttributesIsMutable();
          actorAttributes_.add(index, value);
          onChanged();
        } else {
          actorAttributesBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       **玩家信息列表-ChatInfo中所涉及到的角色id所对应的各种属性
       * </pre>
       *
       * <code>repeated .ActorProfile actorAttributes = 5;</code>
       */
      public Builder addActorAttributes(
          cn.daxiang.protocol.game.CommonProtocol.ActorProfile.Builder builderForValue) {
        if (actorAttributesBuilder_ == null) {
          ensureActorAttributesIsMutable();
          actorAttributes_.add(builderForValue.build());
          onChanged();
        } else {
          actorAttributesBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       **玩家信息列表-ChatInfo中所涉及到的角色id所对应的各种属性
       * </pre>
       *
       * <code>repeated .ActorProfile actorAttributes = 5;</code>
       */
      public Builder addActorAttributes(
          int index, cn.daxiang.protocol.game.CommonProtocol.ActorProfile.Builder builderForValue) {
        if (actorAttributesBuilder_ == null) {
          ensureActorAttributesIsMutable();
          actorAttributes_.add(index, builderForValue.build());
          onChanged();
        } else {
          actorAttributesBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       **玩家信息列表-ChatInfo中所涉及到的角色id所对应的各种属性
       * </pre>
       *
       * <code>repeated .ActorProfile actorAttributes = 5;</code>
       */
      public Builder addAllActorAttributes(
          java.lang.Iterable<? extends cn.daxiang.protocol.game.CommonProtocol.ActorProfile> values) {
        if (actorAttributesBuilder_ == null) {
          ensureActorAttributesIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, actorAttributes_);
          onChanged();
        } else {
          actorAttributesBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       **玩家信息列表-ChatInfo中所涉及到的角色id所对应的各种属性
       * </pre>
       *
       * <code>repeated .ActorProfile actorAttributes = 5;</code>
       */
      public Builder clearActorAttributes() {
        if (actorAttributesBuilder_ == null) {
          actorAttributes_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000010);
          onChanged();
        } else {
          actorAttributesBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       **玩家信息列表-ChatInfo中所涉及到的角色id所对应的各种属性
       * </pre>
       *
       * <code>repeated .ActorProfile actorAttributes = 5;</code>
       */
      public Builder removeActorAttributes(int index) {
        if (actorAttributesBuilder_ == null) {
          ensureActorAttributesIsMutable();
          actorAttributes_.remove(index);
          onChanged();
        } else {
          actorAttributesBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       **玩家信息列表-ChatInfo中所涉及到的角色id所对应的各种属性
       * </pre>
       *
       * <code>repeated .ActorProfile actorAttributes = 5;</code>
       */
      public cn.daxiang.protocol.game.CommonProtocol.ActorProfile.Builder getActorAttributesBuilder(
          int index) {
        return getActorAttributesFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       **玩家信息列表-ChatInfo中所涉及到的角色id所对应的各种属性
       * </pre>
       *
       * <code>repeated .ActorProfile actorAttributes = 5;</code>
       */
      public cn.daxiang.protocol.game.CommonProtocol.ActorProfileOrBuilder getActorAttributesOrBuilder(
          int index) {
        if (actorAttributesBuilder_ == null) {
          return actorAttributes_.get(index);  } else {
          return actorAttributesBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       **玩家信息列表-ChatInfo中所涉及到的角色id所对应的各种属性
       * </pre>
       *
       * <code>repeated .ActorProfile actorAttributes = 5;</code>
       */
      public java.util.List<? extends cn.daxiang.protocol.game.CommonProtocol.ActorProfileOrBuilder> 
           getActorAttributesOrBuilderList() {
        if (actorAttributesBuilder_ != null) {
          return actorAttributesBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(actorAttributes_);
        }
      }
      /**
       * <pre>
       **玩家信息列表-ChatInfo中所涉及到的角色id所对应的各种属性
       * </pre>
       *
       * <code>repeated .ActorProfile actorAttributes = 5;</code>
       */
      public cn.daxiang.protocol.game.CommonProtocol.ActorProfile.Builder addActorAttributesBuilder() {
        return getActorAttributesFieldBuilder().addBuilder(
            cn.daxiang.protocol.game.CommonProtocol.ActorProfile.getDefaultInstance());
      }
      /**
       * <pre>
       **玩家信息列表-ChatInfo中所涉及到的角色id所对应的各种属性
       * </pre>
       *
       * <code>repeated .ActorProfile actorAttributes = 5;</code>
       */
      public cn.daxiang.protocol.game.CommonProtocol.ActorProfile.Builder addActorAttributesBuilder(
          int index) {
        return getActorAttributesFieldBuilder().addBuilder(
            index, cn.daxiang.protocol.game.CommonProtocol.ActorProfile.getDefaultInstance());
      }
      /**
       * <pre>
       **玩家信息列表-ChatInfo中所涉及到的角色id所对应的各种属性
       * </pre>
       *
       * <code>repeated .ActorProfile actorAttributes = 5;</code>
       */
      public java.util.List<cn.daxiang.protocol.game.CommonProtocol.ActorProfile.Builder> 
           getActorAttributesBuilderList() {
        return getActorAttributesFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          cn.daxiang.protocol.game.CommonProtocol.ActorProfile, cn.daxiang.protocol.game.CommonProtocol.ActorProfile.Builder, cn.daxiang.protocol.game.CommonProtocol.ActorProfileOrBuilder> 
          getActorAttributesFieldBuilder() {
        if (actorAttributesBuilder_ == null) {
          actorAttributesBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              cn.daxiang.protocol.game.CommonProtocol.ActorProfile, cn.daxiang.protocol.game.CommonProtocol.ActorProfile.Builder, cn.daxiang.protocol.game.CommonProtocol.ActorProfileOrBuilder>(
                  actorAttributes_,
                  ((bitField0_ & 0x00000010) == 0x00000010),
                  getParentForChildren(),
                  isClean());
          actorAttributes_ = null;
        }
        return actorAttributesBuilder_;
      }

      private cn.daxiang.protocol.game.ChatProtocol.ChatInfo chatInfo_ = null;
      private com.google.protobuf.SingleFieldBuilderV3<
          cn.daxiang.protocol.game.ChatProtocol.ChatInfo, cn.daxiang.protocol.game.ChatProtocol.ChatInfo.Builder, cn.daxiang.protocol.game.ChatProtocol.ChatInfoOrBuilder> chatInfoBuilder_;
      /**
       * <pre>
       **内容 &#64;code ChatInfo
       * </pre>
       *
       * <code>.ChatInfo chatInfo = 6;</code>
       */
      public boolean hasChatInfo() {
        return chatInfoBuilder_ != null || chatInfo_ != null;
      }
      /**
       * <pre>
       **内容 &#64;code ChatInfo
       * </pre>
       *
       * <code>.ChatInfo chatInfo = 6;</code>
       */
      public cn.daxiang.protocol.game.ChatProtocol.ChatInfo getChatInfo() {
        if (chatInfoBuilder_ == null) {
          return chatInfo_ == null ? cn.daxiang.protocol.game.ChatProtocol.ChatInfo.getDefaultInstance() : chatInfo_;
        } else {
          return chatInfoBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       **内容 &#64;code ChatInfo
       * </pre>
       *
       * <code>.ChatInfo chatInfo = 6;</code>
       */
      public Builder setChatInfo(cn.daxiang.protocol.game.ChatProtocol.ChatInfo value) {
        if (chatInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          chatInfo_ = value;
          onChanged();
        } else {
          chatInfoBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <pre>
       **内容 &#64;code ChatInfo
       * </pre>
       *
       * <code>.ChatInfo chatInfo = 6;</code>
       */
      public Builder setChatInfo(
          cn.daxiang.protocol.game.ChatProtocol.ChatInfo.Builder builderForValue) {
        if (chatInfoBuilder_ == null) {
          chatInfo_ = builderForValue.build();
          onChanged();
        } else {
          chatInfoBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <pre>
       **内容 &#64;code ChatInfo
       * </pre>
       *
       * <code>.ChatInfo chatInfo = 6;</code>
       */
      public Builder mergeChatInfo(cn.daxiang.protocol.game.ChatProtocol.ChatInfo value) {
        if (chatInfoBuilder_ == null) {
          if (chatInfo_ != null) {
            chatInfo_ =
              cn.daxiang.protocol.game.ChatProtocol.ChatInfo.newBuilder(chatInfo_).mergeFrom(value).buildPartial();
          } else {
            chatInfo_ = value;
          }
          onChanged();
        } else {
          chatInfoBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <pre>
       **内容 &#64;code ChatInfo
       * </pre>
       *
       * <code>.ChatInfo chatInfo = 6;</code>
       */
      public Builder clearChatInfo() {
        if (chatInfoBuilder_ == null) {
          chatInfo_ = null;
          onChanged();
        } else {
          chatInfo_ = null;
          chatInfoBuilder_ = null;
        }

        return this;
      }
      /**
       * <pre>
       **内容 &#64;code ChatInfo
       * </pre>
       *
       * <code>.ChatInfo chatInfo = 6;</code>
       */
      public cn.daxiang.protocol.game.ChatProtocol.ChatInfo.Builder getChatInfoBuilder() {
        
        onChanged();
        return getChatInfoFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       **内容 &#64;code ChatInfo
       * </pre>
       *
       * <code>.ChatInfo chatInfo = 6;</code>
       */
      public cn.daxiang.protocol.game.ChatProtocol.ChatInfoOrBuilder getChatInfoOrBuilder() {
        if (chatInfoBuilder_ != null) {
          return chatInfoBuilder_.getMessageOrBuilder();
        } else {
          return chatInfo_ == null ?
              cn.daxiang.protocol.game.ChatProtocol.ChatInfo.getDefaultInstance() : chatInfo_;
        }
      }
      /**
       * <pre>
       **内容 &#64;code ChatInfo
       * </pre>
       *
       * <code>.ChatInfo chatInfo = 6;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          cn.daxiang.protocol.game.ChatProtocol.ChatInfo, cn.daxiang.protocol.game.ChatProtocol.ChatInfo.Builder, cn.daxiang.protocol.game.ChatProtocol.ChatInfoOrBuilder> 
          getChatInfoFieldBuilder() {
        if (chatInfoBuilder_ == null) {
          chatInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              cn.daxiang.protocol.game.ChatProtocol.ChatInfo, cn.daxiang.protocol.game.ChatProtocol.ChatInfo.Builder, cn.daxiang.protocol.game.ChatProtocol.ChatInfoOrBuilder>(
                  getChatInfo(),
                  getParentForChildren(),
                  isClean());
          chatInfo_ = null;
        }
        return chatInfoBuilder_;
      }

      private long time_ ;
      /**
       * <pre>
       **发送消息时间戳
       * </pre>
       *
       * <code>int64 time = 7;</code>
       */
      public long getTime() {
        return time_;
      }
      /**
       * <pre>
       **发送消息时间戳
       * </pre>
       *
       * <code>int64 time = 7;</code>
       */
      public Builder setTime(long value) {
        
        time_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       **发送消息时间戳
       * </pre>
       *
       * <code>int64 time = 7;</code>
       */
      public Builder clearTime() {
        
        time_ = 0L;
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:ChatMessage)
    }

    // @@protoc_insertion_point(class_scope:ChatMessage)
    private static final cn.daxiang.protocol.game.ChatProtocol.ChatMessage DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new cn.daxiang.protocol.game.ChatProtocol.ChatMessage();
    }

    public static cn.daxiang.protocol.game.ChatProtocol.ChatMessage getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ChatMessage>
        PARSER = new com.google.protobuf.AbstractParser<ChatMessage>() {
      public ChatMessage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ChatMessage(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ChatMessage> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ChatMessage> getParserForType() {
      return PARSER;
    }

    public cn.daxiang.protocol.game.ChatProtocol.ChatMessage getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ChatInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:ChatInfo)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     ** 消息类型  ChatInfoType 
     * </pre>
     *
     * <code>.ChatInfoType type = 1;</code>
     */
    int getTypeValue();
    /**
     * <pre>
     ** 消息类型  ChatInfoType 
     * </pre>
     *
     * <code>.ChatInfoType type = 1;</code>
     */
    cn.daxiang.protocol.game.TypeProtocol.ChatInfoType getType();

    /**
     * <pre>
     ** 角色ID 
     * </pre>
     *
     * <code>int64 actorId = 2;</code>
     */
    long getActorId();

    /**
     * <pre>
     ** 消息具体数据 根据ChatInfoType去chatInfoProtocol.propo文件去找出对应的message 解析 
     * </pre>
     *
     * <code>bytes value = 3;</code>
     */
    com.google.protobuf.ByteString getValue();

    /**
     * <pre>
     ** 服务器专用字段，客户端可不管
     * </pre>
     *
     * <code>repeated int64 actorIds = 4;</code>
     */
    java.util.List<java.lang.Long> getActorIdsList();
    /**
     * <pre>
     ** 服务器专用字段，客户端可不管
     * </pre>
     *
     * <code>repeated int64 actorIds = 4;</code>
     */
    int getActorIdsCount();
    /**
     * <pre>
     ** 服务器专用字段，客户端可不管
     * </pre>
     *
     * <code>repeated int64 actorIds = 4;</code>
     */
    long getActorIds(int index);
  }
  /**
   * Protobuf type {@code ChatInfo}
   */
  public  static final class ChatInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:ChatInfo)
      ChatInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ChatInfo.newBuilder() to construct.
    private ChatInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ChatInfo() {
      type_ = 0;
      actorId_ = 0L;
      value_ = com.google.protobuf.ByteString.EMPTY;
      actorIds_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ChatInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              int rawValue = input.readEnum();

              type_ = rawValue;
              break;
            }
            case 16: {

              actorId_ = input.readInt64();
              break;
            }
            case 26: {

              value_ = input.readBytes();
              break;
            }
            case 32: {
              if (!((mutable_bitField0_ & 0x00000008) == 0x00000008)) {
                actorIds_ = new java.util.ArrayList<java.lang.Long>();
                mutable_bitField0_ |= 0x00000008;
              }
              actorIds_.add(input.readInt64());
              break;
            }
            case 34: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000008) == 0x00000008) && input.getBytesUntilLimit() > 0) {
                actorIds_ = new java.util.ArrayList<java.lang.Long>();
                mutable_bitField0_ |= 0x00000008;
              }
              while (input.getBytesUntilLimit() > 0) {
                actorIds_.add(input.readInt64());
              }
              input.popLimit(limit);
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000008) == 0x00000008)) {
          actorIds_ = java.util.Collections.unmodifiableList(actorIds_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.ChatProtocol.internal_static_ChatInfo_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.daxiang.protocol.game.ChatProtocol.internal_static_ChatInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.daxiang.protocol.game.ChatProtocol.ChatInfo.class, cn.daxiang.protocol.game.ChatProtocol.ChatInfo.Builder.class);
    }

    private int bitField0_;
    public static final int TYPE_FIELD_NUMBER = 1;
    private int type_;
    /**
     * <pre>
     ** 消息类型  ChatInfoType 
     * </pre>
     *
     * <code>.ChatInfoType type = 1;</code>
     */
    public int getTypeValue() {
      return type_;
    }
    /**
     * <pre>
     ** 消息类型  ChatInfoType 
     * </pre>
     *
     * <code>.ChatInfoType type = 1;</code>
     */
    public cn.daxiang.protocol.game.TypeProtocol.ChatInfoType getType() {
      cn.daxiang.protocol.game.TypeProtocol.ChatInfoType result = cn.daxiang.protocol.game.TypeProtocol.ChatInfoType.valueOf(type_);
      return result == null ? cn.daxiang.protocol.game.TypeProtocol.ChatInfoType.UNRECOGNIZED : result;
    }

    public static final int ACTORID_FIELD_NUMBER = 2;
    private long actorId_;
    /**
     * <pre>
     ** 角色ID 
     * </pre>
     *
     * <code>int64 actorId = 2;</code>
     */
    public long getActorId() {
      return actorId_;
    }

    public static final int VALUE_FIELD_NUMBER = 3;
    private com.google.protobuf.ByteString value_;
    /**
     * <pre>
     ** 消息具体数据 根据ChatInfoType去chatInfoProtocol.propo文件去找出对应的message 解析 
     * </pre>
     *
     * <code>bytes value = 3;</code>
     */
    public com.google.protobuf.ByteString getValue() {
      return value_;
    }

    public static final int ACTORIDS_FIELD_NUMBER = 4;
    private java.util.List<java.lang.Long> actorIds_;
    /**
     * <pre>
     ** 服务器专用字段，客户端可不管
     * </pre>
     *
     * <code>repeated int64 actorIds = 4;</code>
     */
    public java.util.List<java.lang.Long>
        getActorIdsList() {
      return actorIds_;
    }
    /**
     * <pre>
     ** 服务器专用字段，客户端可不管
     * </pre>
     *
     * <code>repeated int64 actorIds = 4;</code>
     */
    public int getActorIdsCount() {
      return actorIds_.size();
    }
    /**
     * <pre>
     ** 服务器专用字段，客户端可不管
     * </pre>
     *
     * <code>repeated int64 actorIds = 4;</code>
     */
    public long getActorIds(int index) {
      return actorIds_.get(index);
    }
    private int actorIdsMemoizedSerializedSize = -1;

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (type_ != cn.daxiang.protocol.game.TypeProtocol.ChatInfoType.CHAT_INFO_NONE.getNumber()) {
        output.writeEnum(1, type_);
      }
      if (actorId_ != 0L) {
        output.writeInt64(2, actorId_);
      }
      if (!value_.isEmpty()) {
        output.writeBytes(3, value_);
      }
      if (getActorIdsList().size() > 0) {
        output.writeUInt32NoTag(34);
        output.writeUInt32NoTag(actorIdsMemoizedSerializedSize);
      }
      for (int i = 0; i < actorIds_.size(); i++) {
        output.writeInt64NoTag(actorIds_.get(i));
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (type_ != cn.daxiang.protocol.game.TypeProtocol.ChatInfoType.CHAT_INFO_NONE.getNumber()) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, type_);
      }
      if (actorId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, actorId_);
      }
      if (!value_.isEmpty()) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(3, value_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < actorIds_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt64SizeNoTag(actorIds_.get(i));
        }
        size += dataSize;
        if (!getActorIdsList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        actorIdsMemoizedSerializedSize = dataSize;
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof cn.daxiang.protocol.game.ChatProtocol.ChatInfo)) {
        return super.equals(obj);
      }
      cn.daxiang.protocol.game.ChatProtocol.ChatInfo other = (cn.daxiang.protocol.game.ChatProtocol.ChatInfo) obj;

      boolean result = true;
      result = result && type_ == other.type_;
      result = result && (getActorId()
          == other.getActorId());
      result = result && getValue()
          .equals(other.getValue());
      result = result && getActorIdsList()
          .equals(other.getActorIdsList());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + type_;
      hash = (37 * hash) + ACTORID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getActorId());
      hash = (37 * hash) + VALUE_FIELD_NUMBER;
      hash = (53 * hash) + getValue().hashCode();
      if (getActorIdsCount() > 0) {
        hash = (37 * hash) + ACTORIDS_FIELD_NUMBER;
        hash = (53 * hash) + getActorIdsList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static cn.daxiang.protocol.game.ChatProtocol.ChatInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.ChatProtocol.ChatInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.ChatProtocol.ChatInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.ChatProtocol.ChatInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.ChatProtocol.ChatInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.ChatProtocol.ChatInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.ChatProtocol.ChatInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.ChatProtocol.ChatInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.ChatProtocol.ChatInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.ChatProtocol.ChatInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.ChatProtocol.ChatInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.ChatProtocol.ChatInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(cn.daxiang.protocol.game.ChatProtocol.ChatInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code ChatInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:ChatInfo)
        cn.daxiang.protocol.game.ChatProtocol.ChatInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.daxiang.protocol.game.ChatProtocol.internal_static_ChatInfo_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.daxiang.protocol.game.ChatProtocol.internal_static_ChatInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.daxiang.protocol.game.ChatProtocol.ChatInfo.class, cn.daxiang.protocol.game.ChatProtocol.ChatInfo.Builder.class);
      }

      // Construct using cn.daxiang.protocol.game.ChatProtocol.ChatInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        type_ = 0;

        actorId_ = 0L;

        value_ = com.google.protobuf.ByteString.EMPTY;

        actorIds_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return cn.daxiang.protocol.game.ChatProtocol.internal_static_ChatInfo_descriptor;
      }

      public cn.daxiang.protocol.game.ChatProtocol.ChatInfo getDefaultInstanceForType() {
        return cn.daxiang.protocol.game.ChatProtocol.ChatInfo.getDefaultInstance();
      }

      public cn.daxiang.protocol.game.ChatProtocol.ChatInfo build() {
        cn.daxiang.protocol.game.ChatProtocol.ChatInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public cn.daxiang.protocol.game.ChatProtocol.ChatInfo buildPartial() {
        cn.daxiang.protocol.game.ChatProtocol.ChatInfo result = new cn.daxiang.protocol.game.ChatProtocol.ChatInfo(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        result.type_ = type_;
        result.actorId_ = actorId_;
        result.value_ = value_;
        if (((bitField0_ & 0x00000008) == 0x00000008)) {
          actorIds_ = java.util.Collections.unmodifiableList(actorIds_);
          bitField0_ = (bitField0_ & ~0x00000008);
        }
        result.actorIds_ = actorIds_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof cn.daxiang.protocol.game.ChatProtocol.ChatInfo) {
          return mergeFrom((cn.daxiang.protocol.game.ChatProtocol.ChatInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(cn.daxiang.protocol.game.ChatProtocol.ChatInfo other) {
        if (other == cn.daxiang.protocol.game.ChatProtocol.ChatInfo.getDefaultInstance()) return this;
        if (other.type_ != 0) {
          setTypeValue(other.getTypeValue());
        }
        if (other.getActorId() != 0L) {
          setActorId(other.getActorId());
        }
        if (other.getValue() != com.google.protobuf.ByteString.EMPTY) {
          setValue(other.getValue());
        }
        if (!other.actorIds_.isEmpty()) {
          if (actorIds_.isEmpty()) {
            actorIds_ = other.actorIds_;
            bitField0_ = (bitField0_ & ~0x00000008);
          } else {
            ensureActorIdsIsMutable();
            actorIds_.addAll(other.actorIds_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        cn.daxiang.protocol.game.ChatProtocol.ChatInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (cn.daxiang.protocol.game.ChatProtocol.ChatInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int type_ = 0;
      /**
       * <pre>
       ** 消息类型  ChatInfoType 
       * </pre>
       *
       * <code>.ChatInfoType type = 1;</code>
       */
      public int getTypeValue() {
        return type_;
      }
      /**
       * <pre>
       ** 消息类型  ChatInfoType 
       * </pre>
       *
       * <code>.ChatInfoType type = 1;</code>
       */
      public Builder setTypeValue(int value) {
        type_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 消息类型  ChatInfoType 
       * </pre>
       *
       * <code>.ChatInfoType type = 1;</code>
       */
      public cn.daxiang.protocol.game.TypeProtocol.ChatInfoType getType() {
        cn.daxiang.protocol.game.TypeProtocol.ChatInfoType result = cn.daxiang.protocol.game.TypeProtocol.ChatInfoType.valueOf(type_);
        return result == null ? cn.daxiang.protocol.game.TypeProtocol.ChatInfoType.UNRECOGNIZED : result;
      }
      /**
       * <pre>
       ** 消息类型  ChatInfoType 
       * </pre>
       *
       * <code>.ChatInfoType type = 1;</code>
       */
      public Builder setType(cn.daxiang.protocol.game.TypeProtocol.ChatInfoType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        
        type_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 消息类型  ChatInfoType 
       * </pre>
       *
       * <code>.ChatInfoType type = 1;</code>
       */
      public Builder clearType() {
        
        type_ = 0;
        onChanged();
        return this;
      }

      private long actorId_ ;
      /**
       * <pre>
       ** 角色ID 
       * </pre>
       *
       * <code>int64 actorId = 2;</code>
       */
      public long getActorId() {
        return actorId_;
      }
      /**
       * <pre>
       ** 角色ID 
       * </pre>
       *
       * <code>int64 actorId = 2;</code>
       */
      public Builder setActorId(long value) {
        
        actorId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 角色ID 
       * </pre>
       *
       * <code>int64 actorId = 2;</code>
       */
      public Builder clearActorId() {
        
        actorId_ = 0L;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString value_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <pre>
       ** 消息具体数据 根据ChatInfoType去chatInfoProtocol.propo文件去找出对应的message 解析 
       * </pre>
       *
       * <code>bytes value = 3;</code>
       */
      public com.google.protobuf.ByteString getValue() {
        return value_;
      }
      /**
       * <pre>
       ** 消息具体数据 根据ChatInfoType去chatInfoProtocol.propo文件去找出对应的message 解析 
       * </pre>
       *
       * <code>bytes value = 3;</code>
       */
      public Builder setValue(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        value_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 消息具体数据 根据ChatInfoType去chatInfoProtocol.propo文件去找出对应的message 解析 
       * </pre>
       *
       * <code>bytes value = 3;</code>
       */
      public Builder clearValue() {
        
        value_ = getDefaultInstance().getValue();
        onChanged();
        return this;
      }

      private java.util.List<java.lang.Long> actorIds_ = java.util.Collections.emptyList();
      private void ensureActorIdsIsMutable() {
        if (!((bitField0_ & 0x00000008) == 0x00000008)) {
          actorIds_ = new java.util.ArrayList<java.lang.Long>(actorIds_);
          bitField0_ |= 0x00000008;
         }
      }
      /**
       * <pre>
       ** 服务器专用字段，客户端可不管
       * </pre>
       *
       * <code>repeated int64 actorIds = 4;</code>
       */
      public java.util.List<java.lang.Long>
          getActorIdsList() {
        return java.util.Collections.unmodifiableList(actorIds_);
      }
      /**
       * <pre>
       ** 服务器专用字段，客户端可不管
       * </pre>
       *
       * <code>repeated int64 actorIds = 4;</code>
       */
      public int getActorIdsCount() {
        return actorIds_.size();
      }
      /**
       * <pre>
       ** 服务器专用字段，客户端可不管
       * </pre>
       *
       * <code>repeated int64 actorIds = 4;</code>
       */
      public long getActorIds(int index) {
        return actorIds_.get(index);
      }
      /**
       * <pre>
       ** 服务器专用字段，客户端可不管
       * </pre>
       *
       * <code>repeated int64 actorIds = 4;</code>
       */
      public Builder setActorIds(
          int index, long value) {
        ensureActorIdsIsMutable();
        actorIds_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 服务器专用字段，客户端可不管
       * </pre>
       *
       * <code>repeated int64 actorIds = 4;</code>
       */
      public Builder addActorIds(long value) {
        ensureActorIdsIsMutable();
        actorIds_.add(value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 服务器专用字段，客户端可不管
       * </pre>
       *
       * <code>repeated int64 actorIds = 4;</code>
       */
      public Builder addAllActorIds(
          java.lang.Iterable<? extends java.lang.Long> values) {
        ensureActorIdsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, actorIds_);
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 服务器专用字段，客户端可不管
       * </pre>
       *
       * <code>repeated int64 actorIds = 4;</code>
       */
      public Builder clearActorIds() {
        actorIds_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000008);
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:ChatInfo)
    }

    // @@protoc_insertion_point(class_scope:ChatInfo)
    private static final cn.daxiang.protocol.game.ChatProtocol.ChatInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new cn.daxiang.protocol.game.ChatProtocol.ChatInfo();
    }

    public static cn.daxiang.protocol.game.ChatProtocol.ChatInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ChatInfo>
        PARSER = new com.google.protobuf.AbstractParser<ChatInfo>() {
      public ChatInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ChatInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ChatInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ChatInfo> getParserForType() {
      return PARSER;
    }

    public cn.daxiang.protocol.game.ChatProtocol.ChatInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ChatTimeResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:ChatTimeResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     ** 下一次聊天时间 
     * </pre>
     *
     * <code>int64 time = 1;</code>
     */
    long getTime();
  }
  /**
   * <pre>
   ** 聊天时间响应
   * </pre>
   *
   * Protobuf type {@code ChatTimeResponse}
   */
  public  static final class ChatTimeResponse extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:ChatTimeResponse)
      ChatTimeResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ChatTimeResponse.newBuilder() to construct.
    private ChatTimeResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ChatTimeResponse() {
      time_ = 0L;
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ChatTimeResponse(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {

              time_ = input.readInt64();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.ChatProtocol.internal_static_ChatTimeResponse_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.daxiang.protocol.game.ChatProtocol.internal_static_ChatTimeResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.daxiang.protocol.game.ChatProtocol.ChatTimeResponse.class, cn.daxiang.protocol.game.ChatProtocol.ChatTimeResponse.Builder.class);
    }

    public static final int TIME_FIELD_NUMBER = 1;
    private long time_;
    /**
     * <pre>
     ** 下一次聊天时间 
     * </pre>
     *
     * <code>int64 time = 1;</code>
     */
    public long getTime() {
      return time_;
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (time_ != 0L) {
        output.writeInt64(1, time_);
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (time_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, time_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof cn.daxiang.protocol.game.ChatProtocol.ChatTimeResponse)) {
        return super.equals(obj);
      }
      cn.daxiang.protocol.game.ChatProtocol.ChatTimeResponse other = (cn.daxiang.protocol.game.ChatProtocol.ChatTimeResponse) obj;

      boolean result = true;
      result = result && (getTime()
          == other.getTime());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTime());
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static cn.daxiang.protocol.game.ChatProtocol.ChatTimeResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.ChatProtocol.ChatTimeResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.ChatProtocol.ChatTimeResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.ChatProtocol.ChatTimeResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.ChatProtocol.ChatTimeResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.ChatProtocol.ChatTimeResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.ChatProtocol.ChatTimeResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.ChatProtocol.ChatTimeResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.ChatProtocol.ChatTimeResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.ChatProtocol.ChatTimeResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.ChatProtocol.ChatTimeResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.ChatProtocol.ChatTimeResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(cn.daxiang.protocol.game.ChatProtocol.ChatTimeResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** 聊天时间响应
     * </pre>
     *
     * Protobuf type {@code ChatTimeResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:ChatTimeResponse)
        cn.daxiang.protocol.game.ChatProtocol.ChatTimeResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.daxiang.protocol.game.ChatProtocol.internal_static_ChatTimeResponse_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.daxiang.protocol.game.ChatProtocol.internal_static_ChatTimeResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.daxiang.protocol.game.ChatProtocol.ChatTimeResponse.class, cn.daxiang.protocol.game.ChatProtocol.ChatTimeResponse.Builder.class);
      }

      // Construct using cn.daxiang.protocol.game.ChatProtocol.ChatTimeResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        time_ = 0L;

        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return cn.daxiang.protocol.game.ChatProtocol.internal_static_ChatTimeResponse_descriptor;
      }

      public cn.daxiang.protocol.game.ChatProtocol.ChatTimeResponse getDefaultInstanceForType() {
        return cn.daxiang.protocol.game.ChatProtocol.ChatTimeResponse.getDefaultInstance();
      }

      public cn.daxiang.protocol.game.ChatProtocol.ChatTimeResponse build() {
        cn.daxiang.protocol.game.ChatProtocol.ChatTimeResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public cn.daxiang.protocol.game.ChatProtocol.ChatTimeResponse buildPartial() {
        cn.daxiang.protocol.game.ChatProtocol.ChatTimeResponse result = new cn.daxiang.protocol.game.ChatProtocol.ChatTimeResponse(this);
        result.time_ = time_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof cn.daxiang.protocol.game.ChatProtocol.ChatTimeResponse) {
          return mergeFrom((cn.daxiang.protocol.game.ChatProtocol.ChatTimeResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(cn.daxiang.protocol.game.ChatProtocol.ChatTimeResponse other) {
        if (other == cn.daxiang.protocol.game.ChatProtocol.ChatTimeResponse.getDefaultInstance()) return this;
        if (other.getTime() != 0L) {
          setTime(other.getTime());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        cn.daxiang.protocol.game.ChatProtocol.ChatTimeResponse parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (cn.daxiang.protocol.game.ChatProtocol.ChatTimeResponse) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private long time_ ;
      /**
       * <pre>
       ** 下一次聊天时间 
       * </pre>
       *
       * <code>int64 time = 1;</code>
       */
      public long getTime() {
        return time_;
      }
      /**
       * <pre>
       ** 下一次聊天时间 
       * </pre>
       *
       * <code>int64 time = 1;</code>
       */
      public Builder setTime(long value) {
        
        time_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 下一次聊天时间 
       * </pre>
       *
       * <code>int64 time = 1;</code>
       */
      public Builder clearTime() {
        
        time_ = 0L;
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:ChatTimeResponse)
    }

    // @@protoc_insertion_point(class_scope:ChatTimeResponse)
    private static final cn.daxiang.protocol.game.ChatProtocol.ChatTimeResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new cn.daxiang.protocol.game.ChatProtocol.ChatTimeResponse();
    }

    public static cn.daxiang.protocol.game.ChatProtocol.ChatTimeResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ChatTimeResponse>
        PARSER = new com.google.protobuf.AbstractParser<ChatTimeResponse>() {
      public ChatTimeResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ChatTimeResponse(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ChatTimeResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ChatTimeResponse> getParserForType() {
      return PARSER;
    }

    public cn.daxiang.protocol.game.ChatProtocol.ChatTimeResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ChatSendMessageRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:ChatSendMessageRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     ** 聊天频道类型  ChatChannelType
     * </pre>
     *
     * <code>.ChatChannelType type = 1;</code>
     */
    int getTypeValue();
    /**
     * <pre>
     ** 聊天频道类型  ChatChannelType
     * </pre>
     *
     * <code>.ChatChannelType type = 1;</code>
     */
    cn.daxiang.protocol.game.TypeProtocol.ChatChannelType getType();

    /**
     * <pre>
     ** serverID(跨服私聊用)
     * </pre>
     *
     * <code>int32 serverId = 2;</code>
     */
    int getServerId();

    /**
     * <pre>
     ** 接收ID
     * </pre>
     *
     * <code>int64 channelValue = 3;</code>
     */
    long getChannelValue();

    /**
     * <pre>
     ** 消息
     * </pre>
     *
     * <code>string msg = 4;</code>
     */
    java.lang.String getMsg();
    /**
     * <pre>
     ** 消息
     * </pre>
     *
     * <code>string msg = 4;</code>
     */
    com.google.protobuf.ByteString
        getMsgBytes();

    /**
     * <pre>
     ** 被呼叫玩家列表 key:角色ID value:名字 
     * </pre>
     *
     * <code>map&lt;int64, string&gt; calledActorMap = 5;</code>
     */
    int getCalledActorMapCount();
    /**
     * <pre>
     ** 被呼叫玩家列表 key:角色ID value:名字 
     * </pre>
     *
     * <code>map&lt;int64, string&gt; calledActorMap = 5;</code>
     */
    boolean containsCalledActorMap(
        long key);
    /**
     * Use {@link #getCalledActorMapMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Long, java.lang.String>
    getCalledActorMap();
    /**
     * <pre>
     ** 被呼叫玩家列表 key:角色ID value:名字 
     * </pre>
     *
     * <code>map&lt;int64, string&gt; calledActorMap = 5;</code>
     */
    java.util.Map<java.lang.Long, java.lang.String>
    getCalledActorMapMap();
    /**
     * <pre>
     ** 被呼叫玩家列表 key:角色ID value:名字 
     * </pre>
     *
     * <code>map&lt;int64, string&gt; calledActorMap = 5;</code>
     */

    java.lang.String getCalledActorMapOrDefault(
        long key,
        java.lang.String defaultValue);
    /**
     * <pre>
     ** 被呼叫玩家列表 key:角色ID value:名字 
     * </pre>
     *
     * <code>map&lt;int64, string&gt; calledActorMap = 5;</code>
     */

    java.lang.String getCalledActorMapOrThrow(
        long key);

    /**
     * <pre>
     ** 被呼叫玩家第三方渠道用户ID 
     * </pre>
     *
     * <code>string toToken = 6;</code>
     */
    java.lang.String getToToken();
    /**
     * <pre>
     ** 被呼叫玩家第三方渠道用户ID 
     * </pre>
     *
     * <code>string toToken = 6;</code>
     */
    com.google.protobuf.ByteString
        getToTokenBytes();

    /**
     * <pre>
     ** 被呼叫玩家名称 
     * </pre>
     *
     * <code>string toActorName = 7;</code>
     */
    java.lang.String getToActorName();
    /**
     * <pre>
     ** 被呼叫玩家名称 
     * </pre>
     *
     * <code>string toActorName = 7;</code>
     */
    com.google.protobuf.ByteString
        getToActorNameBytes();
  }
  /**
   * <pre>
   ** 发送聊天消息请求
   * </pre>
   *
   * Protobuf type {@code ChatSendMessageRequest}
   */
  public  static final class ChatSendMessageRequest extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:ChatSendMessageRequest)
      ChatSendMessageRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ChatSendMessageRequest.newBuilder() to construct.
    private ChatSendMessageRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ChatSendMessageRequest() {
      type_ = 0;
      serverId_ = 0;
      channelValue_ = 0L;
      msg_ = "";
      toToken_ = "";
      toActorName_ = "";
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ChatSendMessageRequest(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              int rawValue = input.readEnum();

              type_ = rawValue;
              break;
            }
            case 16: {

              serverId_ = input.readInt32();
              break;
            }
            case 24: {

              channelValue_ = input.readInt64();
              break;
            }
            case 34: {
              java.lang.String s = input.readStringRequireUtf8();

              msg_ = s;
              break;
            }
            case 42: {
              if (!((mutable_bitField0_ & 0x00000010) == 0x00000010)) {
                calledActorMap_ = com.google.protobuf.MapField.newMapField(
                    CalledActorMapDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000010;
              }
              com.google.protobuf.MapEntry<java.lang.Long, java.lang.String>
              calledActorMap__ = input.readMessage(
                  CalledActorMapDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              calledActorMap_.getMutableMap().put(
                  calledActorMap__.getKey(), calledActorMap__.getValue());
              break;
            }
            case 50: {
              java.lang.String s = input.readStringRequireUtf8();

              toToken_ = s;
              break;
            }
            case 58: {
              java.lang.String s = input.readStringRequireUtf8();

              toActorName_ = s;
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.ChatProtocol.internal_static_ChatSendMessageRequest_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 5:
          return internalGetCalledActorMap();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.daxiang.protocol.game.ChatProtocol.internal_static_ChatSendMessageRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.daxiang.protocol.game.ChatProtocol.ChatSendMessageRequest.class, cn.daxiang.protocol.game.ChatProtocol.ChatSendMessageRequest.Builder.class);
    }

    private int bitField0_;
    public static final int TYPE_FIELD_NUMBER = 1;
    private int type_;
    /**
     * <pre>
     ** 聊天频道类型  ChatChannelType
     * </pre>
     *
     * <code>.ChatChannelType type = 1;</code>
     */
    public int getTypeValue() {
      return type_;
    }
    /**
     * <pre>
     ** 聊天频道类型  ChatChannelType
     * </pre>
     *
     * <code>.ChatChannelType type = 1;</code>
     */
    public cn.daxiang.protocol.game.TypeProtocol.ChatChannelType getType() {
      cn.daxiang.protocol.game.TypeProtocol.ChatChannelType result = cn.daxiang.protocol.game.TypeProtocol.ChatChannelType.valueOf(type_);
      return result == null ? cn.daxiang.protocol.game.TypeProtocol.ChatChannelType.UNRECOGNIZED : result;
    }

    public static final int SERVERID_FIELD_NUMBER = 2;
    private int serverId_;
    /**
     * <pre>
     ** serverID(跨服私聊用)
     * </pre>
     *
     * <code>int32 serverId = 2;</code>
     */
    public int getServerId() {
      return serverId_;
    }

    public static final int CHANNELVALUE_FIELD_NUMBER = 3;
    private long channelValue_;
    /**
     * <pre>
     ** 接收ID
     * </pre>
     *
     * <code>int64 channelValue = 3;</code>
     */
    public long getChannelValue() {
      return channelValue_;
    }

    public static final int MSG_FIELD_NUMBER = 4;
    private volatile java.lang.Object msg_;
    /**
     * <pre>
     ** 消息
     * </pre>
     *
     * <code>string msg = 4;</code>
     */
    public java.lang.String getMsg() {
      java.lang.Object ref = msg_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        msg_ = s;
        return s;
      }
    }
    /**
     * <pre>
     ** 消息
     * </pre>
     *
     * <code>string msg = 4;</code>
     */
    public com.google.protobuf.ByteString
        getMsgBytes() {
      java.lang.Object ref = msg_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        msg_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CALLEDACTORMAP_FIELD_NUMBER = 5;
    private static final class CalledActorMapDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Long, java.lang.String> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Long, java.lang.String>newDefaultInstance(
                  cn.daxiang.protocol.game.ChatProtocol.internal_static_ChatSendMessageRequest_CalledActorMapEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.INT64,
                  0L,
                  com.google.protobuf.WireFormat.FieldType.STRING,
                  "");
    }
    private com.google.protobuf.MapField<
        java.lang.Long, java.lang.String> calledActorMap_;
    private com.google.protobuf.MapField<java.lang.Long, java.lang.String>
    internalGetCalledActorMap() {
      if (calledActorMap_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            CalledActorMapDefaultEntryHolder.defaultEntry);
      }
      return calledActorMap_;
    }

    public int getCalledActorMapCount() {
      return internalGetCalledActorMap().getMap().size();
    }
    /**
     * <pre>
     ** 被呼叫玩家列表 key:角色ID value:名字 
     * </pre>
     *
     * <code>map&lt;int64, string&gt; calledActorMap = 5;</code>
     */

    public boolean containsCalledActorMap(
        long key) {
      
      return internalGetCalledActorMap().getMap().containsKey(key);
    }
    /**
     * Use {@link #getCalledActorMapMap()} instead.
     */
    @java.lang.Deprecated
    public java.util.Map<java.lang.Long, java.lang.String> getCalledActorMap() {
      return getCalledActorMapMap();
    }
    /**
     * <pre>
     ** 被呼叫玩家列表 key:角色ID value:名字 
     * </pre>
     *
     * <code>map&lt;int64, string&gt; calledActorMap = 5;</code>
     */

    public java.util.Map<java.lang.Long, java.lang.String> getCalledActorMapMap() {
      return internalGetCalledActorMap().getMap();
    }
    /**
     * <pre>
     ** 被呼叫玩家列表 key:角色ID value:名字 
     * </pre>
     *
     * <code>map&lt;int64, string&gt; calledActorMap = 5;</code>
     */

    public java.lang.String getCalledActorMapOrDefault(
        long key,
        java.lang.String defaultValue) {
      
      java.util.Map<java.lang.Long, java.lang.String> map =
          internalGetCalledActorMap().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <pre>
     ** 被呼叫玩家列表 key:角色ID value:名字 
     * </pre>
     *
     * <code>map&lt;int64, string&gt; calledActorMap = 5;</code>
     */

    public java.lang.String getCalledActorMapOrThrow(
        long key) {
      
      java.util.Map<java.lang.Long, java.lang.String> map =
          internalGetCalledActorMap().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    public static final int TOTOKEN_FIELD_NUMBER = 6;
    private volatile java.lang.Object toToken_;
    /**
     * <pre>
     ** 被呼叫玩家第三方渠道用户ID 
     * </pre>
     *
     * <code>string toToken = 6;</code>
     */
    public java.lang.String getToToken() {
      java.lang.Object ref = toToken_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        toToken_ = s;
        return s;
      }
    }
    /**
     * <pre>
     ** 被呼叫玩家第三方渠道用户ID 
     * </pre>
     *
     * <code>string toToken = 6;</code>
     */
    public com.google.protobuf.ByteString
        getToTokenBytes() {
      java.lang.Object ref = toToken_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        toToken_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int TOACTORNAME_FIELD_NUMBER = 7;
    private volatile java.lang.Object toActorName_;
    /**
     * <pre>
     ** 被呼叫玩家名称 
     * </pre>
     *
     * <code>string toActorName = 7;</code>
     */
    public java.lang.String getToActorName() {
      java.lang.Object ref = toActorName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        toActorName_ = s;
        return s;
      }
    }
    /**
     * <pre>
     ** 被呼叫玩家名称 
     * </pre>
     *
     * <code>string toActorName = 7;</code>
     */
    public com.google.protobuf.ByteString
        getToActorNameBytes() {
      java.lang.Object ref = toActorName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        toActorName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (type_ != cn.daxiang.protocol.game.TypeProtocol.ChatChannelType.CHAT_CHANNEL_TYPE_NONE.getNumber()) {
        output.writeEnum(1, type_);
      }
      if (serverId_ != 0) {
        output.writeInt32(2, serverId_);
      }
      if (channelValue_ != 0L) {
        output.writeInt64(3, channelValue_);
      }
      if (!getMsgBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 4, msg_);
      }
      com.google.protobuf.GeneratedMessageV3
        .serializeLongMapTo(
          output,
          internalGetCalledActorMap(),
          CalledActorMapDefaultEntryHolder.defaultEntry,
          5);
      if (!getToTokenBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 6, toToken_);
      }
      if (!getToActorNameBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 7, toActorName_);
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (type_ != cn.daxiang.protocol.game.TypeProtocol.ChatChannelType.CHAT_CHANNEL_TYPE_NONE.getNumber()) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, type_);
      }
      if (serverId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, serverId_);
      }
      if (channelValue_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, channelValue_);
      }
      if (!getMsgBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, msg_);
      }
      for (java.util.Map.Entry<java.lang.Long, java.lang.String> entry
           : internalGetCalledActorMap().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Long, java.lang.String>
        calledActorMap__ = CalledActorMapDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(5, calledActorMap__);
      }
      if (!getToTokenBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, toToken_);
      }
      if (!getToActorNameBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(7, toActorName_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof cn.daxiang.protocol.game.ChatProtocol.ChatSendMessageRequest)) {
        return super.equals(obj);
      }
      cn.daxiang.protocol.game.ChatProtocol.ChatSendMessageRequest other = (cn.daxiang.protocol.game.ChatProtocol.ChatSendMessageRequest) obj;

      boolean result = true;
      result = result && type_ == other.type_;
      result = result && (getServerId()
          == other.getServerId());
      result = result && (getChannelValue()
          == other.getChannelValue());
      result = result && getMsg()
          .equals(other.getMsg());
      result = result && internalGetCalledActorMap().equals(
          other.internalGetCalledActorMap());
      result = result && getToToken()
          .equals(other.getToToken());
      result = result && getToActorName()
          .equals(other.getToActorName());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + type_;
      hash = (37 * hash) + SERVERID_FIELD_NUMBER;
      hash = (53 * hash) + getServerId();
      hash = (37 * hash) + CHANNELVALUE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getChannelValue());
      hash = (37 * hash) + MSG_FIELD_NUMBER;
      hash = (53 * hash) + getMsg().hashCode();
      if (!internalGetCalledActorMap().getMap().isEmpty()) {
        hash = (37 * hash) + CALLEDACTORMAP_FIELD_NUMBER;
        hash = (53 * hash) + internalGetCalledActorMap().hashCode();
      }
      hash = (37 * hash) + TOTOKEN_FIELD_NUMBER;
      hash = (53 * hash) + getToToken().hashCode();
      hash = (37 * hash) + TOACTORNAME_FIELD_NUMBER;
      hash = (53 * hash) + getToActorName().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static cn.daxiang.protocol.game.ChatProtocol.ChatSendMessageRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.ChatProtocol.ChatSendMessageRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.ChatProtocol.ChatSendMessageRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.ChatProtocol.ChatSendMessageRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.ChatProtocol.ChatSendMessageRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.ChatProtocol.ChatSendMessageRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.ChatProtocol.ChatSendMessageRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.ChatProtocol.ChatSendMessageRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.ChatProtocol.ChatSendMessageRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.ChatProtocol.ChatSendMessageRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.ChatProtocol.ChatSendMessageRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.ChatProtocol.ChatSendMessageRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(cn.daxiang.protocol.game.ChatProtocol.ChatSendMessageRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** 发送聊天消息请求
     * </pre>
     *
     * Protobuf type {@code ChatSendMessageRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:ChatSendMessageRequest)
        cn.daxiang.protocol.game.ChatProtocol.ChatSendMessageRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.daxiang.protocol.game.ChatProtocol.internal_static_ChatSendMessageRequest_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 5:
            return internalGetCalledActorMap();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 5:
            return internalGetMutableCalledActorMap();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.daxiang.protocol.game.ChatProtocol.internal_static_ChatSendMessageRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.daxiang.protocol.game.ChatProtocol.ChatSendMessageRequest.class, cn.daxiang.protocol.game.ChatProtocol.ChatSendMessageRequest.Builder.class);
      }

      // Construct using cn.daxiang.protocol.game.ChatProtocol.ChatSendMessageRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        type_ = 0;

        serverId_ = 0;

        channelValue_ = 0L;

        msg_ = "";

        internalGetMutableCalledActorMap().clear();
        toToken_ = "";

        toActorName_ = "";

        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return cn.daxiang.protocol.game.ChatProtocol.internal_static_ChatSendMessageRequest_descriptor;
      }

      public cn.daxiang.protocol.game.ChatProtocol.ChatSendMessageRequest getDefaultInstanceForType() {
        return cn.daxiang.protocol.game.ChatProtocol.ChatSendMessageRequest.getDefaultInstance();
      }

      public cn.daxiang.protocol.game.ChatProtocol.ChatSendMessageRequest build() {
        cn.daxiang.protocol.game.ChatProtocol.ChatSendMessageRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public cn.daxiang.protocol.game.ChatProtocol.ChatSendMessageRequest buildPartial() {
        cn.daxiang.protocol.game.ChatProtocol.ChatSendMessageRequest result = new cn.daxiang.protocol.game.ChatProtocol.ChatSendMessageRequest(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        result.type_ = type_;
        result.serverId_ = serverId_;
        result.channelValue_ = channelValue_;
        result.msg_ = msg_;
        result.calledActorMap_ = internalGetCalledActorMap();
        result.calledActorMap_.makeImmutable();
        result.toToken_ = toToken_;
        result.toActorName_ = toActorName_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof cn.daxiang.protocol.game.ChatProtocol.ChatSendMessageRequest) {
          return mergeFrom((cn.daxiang.protocol.game.ChatProtocol.ChatSendMessageRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(cn.daxiang.protocol.game.ChatProtocol.ChatSendMessageRequest other) {
        if (other == cn.daxiang.protocol.game.ChatProtocol.ChatSendMessageRequest.getDefaultInstance()) return this;
        if (other.type_ != 0) {
          setTypeValue(other.getTypeValue());
        }
        if (other.getServerId() != 0) {
          setServerId(other.getServerId());
        }
        if (other.getChannelValue() != 0L) {
          setChannelValue(other.getChannelValue());
        }
        if (!other.getMsg().isEmpty()) {
          msg_ = other.msg_;
          onChanged();
        }
        internalGetMutableCalledActorMap().mergeFrom(
            other.internalGetCalledActorMap());
        if (!other.getToToken().isEmpty()) {
          toToken_ = other.toToken_;
          onChanged();
        }
        if (!other.getToActorName().isEmpty()) {
          toActorName_ = other.toActorName_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        cn.daxiang.protocol.game.ChatProtocol.ChatSendMessageRequest parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (cn.daxiang.protocol.game.ChatProtocol.ChatSendMessageRequest) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int type_ = 0;
      /**
       * <pre>
       ** 聊天频道类型  ChatChannelType
       * </pre>
       *
       * <code>.ChatChannelType type = 1;</code>
       */
      public int getTypeValue() {
        return type_;
      }
      /**
       * <pre>
       ** 聊天频道类型  ChatChannelType
       * </pre>
       *
       * <code>.ChatChannelType type = 1;</code>
       */
      public Builder setTypeValue(int value) {
        type_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 聊天频道类型  ChatChannelType
       * </pre>
       *
       * <code>.ChatChannelType type = 1;</code>
       */
      public cn.daxiang.protocol.game.TypeProtocol.ChatChannelType getType() {
        cn.daxiang.protocol.game.TypeProtocol.ChatChannelType result = cn.daxiang.protocol.game.TypeProtocol.ChatChannelType.valueOf(type_);
        return result == null ? cn.daxiang.protocol.game.TypeProtocol.ChatChannelType.UNRECOGNIZED : result;
      }
      /**
       * <pre>
       ** 聊天频道类型  ChatChannelType
       * </pre>
       *
       * <code>.ChatChannelType type = 1;</code>
       */
      public Builder setType(cn.daxiang.protocol.game.TypeProtocol.ChatChannelType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        
        type_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 聊天频道类型  ChatChannelType
       * </pre>
       *
       * <code>.ChatChannelType type = 1;</code>
       */
      public Builder clearType() {
        
        type_ = 0;
        onChanged();
        return this;
      }

      private int serverId_ ;
      /**
       * <pre>
       ** serverID(跨服私聊用)
       * </pre>
       *
       * <code>int32 serverId = 2;</code>
       */
      public int getServerId() {
        return serverId_;
      }
      /**
       * <pre>
       ** serverID(跨服私聊用)
       * </pre>
       *
       * <code>int32 serverId = 2;</code>
       */
      public Builder setServerId(int value) {
        
        serverId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** serverID(跨服私聊用)
       * </pre>
       *
       * <code>int32 serverId = 2;</code>
       */
      public Builder clearServerId() {
        
        serverId_ = 0;
        onChanged();
        return this;
      }

      private long channelValue_ ;
      /**
       * <pre>
       ** 接收ID
       * </pre>
       *
       * <code>int64 channelValue = 3;</code>
       */
      public long getChannelValue() {
        return channelValue_;
      }
      /**
       * <pre>
       ** 接收ID
       * </pre>
       *
       * <code>int64 channelValue = 3;</code>
       */
      public Builder setChannelValue(long value) {
        
        channelValue_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 接收ID
       * </pre>
       *
       * <code>int64 channelValue = 3;</code>
       */
      public Builder clearChannelValue() {
        
        channelValue_ = 0L;
        onChanged();
        return this;
      }

      private java.lang.Object msg_ = "";
      /**
       * <pre>
       ** 消息
       * </pre>
       *
       * <code>string msg = 4;</code>
       */
      public java.lang.String getMsg() {
        java.lang.Object ref = msg_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          msg_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       ** 消息
       * </pre>
       *
       * <code>string msg = 4;</code>
       */
      public com.google.protobuf.ByteString
          getMsgBytes() {
        java.lang.Object ref = msg_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          msg_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       ** 消息
       * </pre>
       *
       * <code>string msg = 4;</code>
       */
      public Builder setMsg(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        msg_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 消息
       * </pre>
       *
       * <code>string msg = 4;</code>
       */
      public Builder clearMsg() {
        
        msg_ = getDefaultInstance().getMsg();
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 消息
       * </pre>
       *
       * <code>string msg = 4;</code>
       */
      public Builder setMsgBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        msg_ = value;
        onChanged();
        return this;
      }

      private com.google.protobuf.MapField<
          java.lang.Long, java.lang.String> calledActorMap_;
      private com.google.protobuf.MapField<java.lang.Long, java.lang.String>
      internalGetCalledActorMap() {
        if (calledActorMap_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              CalledActorMapDefaultEntryHolder.defaultEntry);
        }
        return calledActorMap_;
      }
      private com.google.protobuf.MapField<java.lang.Long, java.lang.String>
      internalGetMutableCalledActorMap() {
        onChanged();;
        if (calledActorMap_ == null) {
          calledActorMap_ = com.google.protobuf.MapField.newMapField(
              CalledActorMapDefaultEntryHolder.defaultEntry);
        }
        if (!calledActorMap_.isMutable()) {
          calledActorMap_ = calledActorMap_.copy();
        }
        return calledActorMap_;
      }

      public int getCalledActorMapCount() {
        return internalGetCalledActorMap().getMap().size();
      }
      /**
       * <pre>
       ** 被呼叫玩家列表 key:角色ID value:名字 
       * </pre>
       *
       * <code>map&lt;int64, string&gt; calledActorMap = 5;</code>
       */

      public boolean containsCalledActorMap(
          long key) {
        
        return internalGetCalledActorMap().getMap().containsKey(key);
      }
      /**
       * Use {@link #getCalledActorMapMap()} instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Long, java.lang.String> getCalledActorMap() {
        return getCalledActorMapMap();
      }
      /**
       * <pre>
       ** 被呼叫玩家列表 key:角色ID value:名字 
       * </pre>
       *
       * <code>map&lt;int64, string&gt; calledActorMap = 5;</code>
       */

      public java.util.Map<java.lang.Long, java.lang.String> getCalledActorMapMap() {
        return internalGetCalledActorMap().getMap();
      }
      /**
       * <pre>
       ** 被呼叫玩家列表 key:角色ID value:名字 
       * </pre>
       *
       * <code>map&lt;int64, string&gt; calledActorMap = 5;</code>
       */

      public java.lang.String getCalledActorMapOrDefault(
          long key,
          java.lang.String defaultValue) {
        
        java.util.Map<java.lang.Long, java.lang.String> map =
            internalGetCalledActorMap().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <pre>
       ** 被呼叫玩家列表 key:角色ID value:名字 
       * </pre>
       *
       * <code>map&lt;int64, string&gt; calledActorMap = 5;</code>
       */

      public java.lang.String getCalledActorMapOrThrow(
          long key) {
        
        java.util.Map<java.lang.Long, java.lang.String> map =
            internalGetCalledActorMap().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearCalledActorMap() {
        internalGetMutableCalledActorMap().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <pre>
       ** 被呼叫玩家列表 key:角色ID value:名字 
       * </pre>
       *
       * <code>map&lt;int64, string&gt; calledActorMap = 5;</code>
       */

      public Builder removeCalledActorMap(
          long key) {
        
        internalGetMutableCalledActorMap().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Long, java.lang.String>
      getMutableCalledActorMap() {
        return internalGetMutableCalledActorMap().getMutableMap();
      }
      /**
       * <pre>
       ** 被呼叫玩家列表 key:角色ID value:名字 
       * </pre>
       *
       * <code>map&lt;int64, string&gt; calledActorMap = 5;</code>
       */
      public Builder putCalledActorMap(
          long key,
          java.lang.String value) {
        
        if (value == null) { throw new java.lang.NullPointerException(); }
        internalGetMutableCalledActorMap().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <pre>
       ** 被呼叫玩家列表 key:角色ID value:名字 
       * </pre>
       *
       * <code>map&lt;int64, string&gt; calledActorMap = 5;</code>
       */

      public Builder putAllCalledActorMap(
          java.util.Map<java.lang.Long, java.lang.String> values) {
        internalGetMutableCalledActorMap().getMutableMap()
            .putAll(values);
        return this;
      }

      private java.lang.Object toToken_ = "";
      /**
       * <pre>
       ** 被呼叫玩家第三方渠道用户ID 
       * </pre>
       *
       * <code>string toToken = 6;</code>
       */
      public java.lang.String getToToken() {
        java.lang.Object ref = toToken_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          toToken_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       ** 被呼叫玩家第三方渠道用户ID 
       * </pre>
       *
       * <code>string toToken = 6;</code>
       */
      public com.google.protobuf.ByteString
          getToTokenBytes() {
        java.lang.Object ref = toToken_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          toToken_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       ** 被呼叫玩家第三方渠道用户ID 
       * </pre>
       *
       * <code>string toToken = 6;</code>
       */
      public Builder setToToken(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        toToken_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 被呼叫玩家第三方渠道用户ID 
       * </pre>
       *
       * <code>string toToken = 6;</code>
       */
      public Builder clearToToken() {
        
        toToken_ = getDefaultInstance().getToToken();
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 被呼叫玩家第三方渠道用户ID 
       * </pre>
       *
       * <code>string toToken = 6;</code>
       */
      public Builder setToTokenBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        toToken_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object toActorName_ = "";
      /**
       * <pre>
       ** 被呼叫玩家名称 
       * </pre>
       *
       * <code>string toActorName = 7;</code>
       */
      public java.lang.String getToActorName() {
        java.lang.Object ref = toActorName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          toActorName_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       ** 被呼叫玩家名称 
       * </pre>
       *
       * <code>string toActorName = 7;</code>
       */
      public com.google.protobuf.ByteString
          getToActorNameBytes() {
        java.lang.Object ref = toActorName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          toActorName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       ** 被呼叫玩家名称 
       * </pre>
       *
       * <code>string toActorName = 7;</code>
       */
      public Builder setToActorName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        toActorName_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 被呼叫玩家名称 
       * </pre>
       *
       * <code>string toActorName = 7;</code>
       */
      public Builder clearToActorName() {
        
        toActorName_ = getDefaultInstance().getToActorName();
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 被呼叫玩家名称 
       * </pre>
       *
       * <code>string toActorName = 7;</code>
       */
      public Builder setToActorNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        toActorName_ = value;
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:ChatSendMessageRequest)
    }

    // @@protoc_insertion_point(class_scope:ChatSendMessageRequest)
    private static final cn.daxiang.protocol.game.ChatProtocol.ChatSendMessageRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new cn.daxiang.protocol.game.ChatProtocol.ChatSendMessageRequest();
    }

    public static cn.daxiang.protocol.game.ChatProtocol.ChatSendMessageRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ChatSendMessageRequest>
        PARSER = new com.google.protobuf.AbstractParser<ChatSendMessageRequest>() {
      public ChatSendMessageRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ChatSendMessageRequest(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ChatSendMessageRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ChatSendMessageRequest> getParserForType() {
      return PARSER;
    }

    public cn.daxiang.protocol.game.ChatProtocol.ChatSendMessageRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ChatDeletePersonalRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:ChatDeletePersonalRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     **
     * 目标actorId
     * </pre>
     *
     * <code>int64 id = 1;</code>
     */
    long getId();
  }
  /**
   * <pre>
   ** 删除私人聊天消息
   * </pre>
   *
   * Protobuf type {@code ChatDeletePersonalRequest}
   */
  public  static final class ChatDeletePersonalRequest extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:ChatDeletePersonalRequest)
      ChatDeletePersonalRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ChatDeletePersonalRequest.newBuilder() to construct.
    private ChatDeletePersonalRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ChatDeletePersonalRequest() {
      id_ = 0L;
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ChatDeletePersonalRequest(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {

              id_ = input.readInt64();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.ChatProtocol.internal_static_ChatDeletePersonalRequest_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.daxiang.protocol.game.ChatProtocol.internal_static_ChatDeletePersonalRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.daxiang.protocol.game.ChatProtocol.ChatDeletePersonalRequest.class, cn.daxiang.protocol.game.ChatProtocol.ChatDeletePersonalRequest.Builder.class);
    }

    public static final int ID_FIELD_NUMBER = 1;
    private long id_;
    /**
     * <pre>
     **
     * 目标actorId
     * </pre>
     *
     * <code>int64 id = 1;</code>
     */
    public long getId() {
      return id_;
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (id_ != 0L) {
        output.writeInt64(1, id_);
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (id_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, id_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof cn.daxiang.protocol.game.ChatProtocol.ChatDeletePersonalRequest)) {
        return super.equals(obj);
      }
      cn.daxiang.protocol.game.ChatProtocol.ChatDeletePersonalRequest other = (cn.daxiang.protocol.game.ChatProtocol.ChatDeletePersonalRequest) obj;

      boolean result = true;
      result = result && (getId()
          == other.getId());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + ID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getId());
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static cn.daxiang.protocol.game.ChatProtocol.ChatDeletePersonalRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.ChatProtocol.ChatDeletePersonalRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.ChatProtocol.ChatDeletePersonalRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.ChatProtocol.ChatDeletePersonalRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.ChatProtocol.ChatDeletePersonalRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.ChatProtocol.ChatDeletePersonalRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.ChatProtocol.ChatDeletePersonalRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.ChatProtocol.ChatDeletePersonalRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.ChatProtocol.ChatDeletePersonalRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.ChatProtocol.ChatDeletePersonalRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.ChatProtocol.ChatDeletePersonalRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.ChatProtocol.ChatDeletePersonalRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(cn.daxiang.protocol.game.ChatProtocol.ChatDeletePersonalRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** 删除私人聊天消息
     * </pre>
     *
     * Protobuf type {@code ChatDeletePersonalRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:ChatDeletePersonalRequest)
        cn.daxiang.protocol.game.ChatProtocol.ChatDeletePersonalRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.daxiang.protocol.game.ChatProtocol.internal_static_ChatDeletePersonalRequest_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.daxiang.protocol.game.ChatProtocol.internal_static_ChatDeletePersonalRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.daxiang.protocol.game.ChatProtocol.ChatDeletePersonalRequest.class, cn.daxiang.protocol.game.ChatProtocol.ChatDeletePersonalRequest.Builder.class);
      }

      // Construct using cn.daxiang.protocol.game.ChatProtocol.ChatDeletePersonalRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        id_ = 0L;

        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return cn.daxiang.protocol.game.ChatProtocol.internal_static_ChatDeletePersonalRequest_descriptor;
      }

      public cn.daxiang.protocol.game.ChatProtocol.ChatDeletePersonalRequest getDefaultInstanceForType() {
        return cn.daxiang.protocol.game.ChatProtocol.ChatDeletePersonalRequest.getDefaultInstance();
      }

      public cn.daxiang.protocol.game.ChatProtocol.ChatDeletePersonalRequest build() {
        cn.daxiang.protocol.game.ChatProtocol.ChatDeletePersonalRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public cn.daxiang.protocol.game.ChatProtocol.ChatDeletePersonalRequest buildPartial() {
        cn.daxiang.protocol.game.ChatProtocol.ChatDeletePersonalRequest result = new cn.daxiang.protocol.game.ChatProtocol.ChatDeletePersonalRequest(this);
        result.id_ = id_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof cn.daxiang.protocol.game.ChatProtocol.ChatDeletePersonalRequest) {
          return mergeFrom((cn.daxiang.protocol.game.ChatProtocol.ChatDeletePersonalRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(cn.daxiang.protocol.game.ChatProtocol.ChatDeletePersonalRequest other) {
        if (other == cn.daxiang.protocol.game.ChatProtocol.ChatDeletePersonalRequest.getDefaultInstance()) return this;
        if (other.getId() != 0L) {
          setId(other.getId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        cn.daxiang.protocol.game.ChatProtocol.ChatDeletePersonalRequest parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (cn.daxiang.protocol.game.ChatProtocol.ChatDeletePersonalRequest) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private long id_ ;
      /**
       * <pre>
       **
       * 目标actorId
       * </pre>
       *
       * <code>int64 id = 1;</code>
       */
      public long getId() {
        return id_;
      }
      /**
       * <pre>
       **
       * 目标actorId
       * </pre>
       *
       * <code>int64 id = 1;</code>
       */
      public Builder setId(long value) {
        
        id_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       **
       * 目标actorId
       * </pre>
       *
       * <code>int64 id = 1;</code>
       */
      public Builder clearId() {
        
        id_ = 0L;
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:ChatDeletePersonalRequest)
    }

    // @@protoc_insertion_point(class_scope:ChatDeletePersonalRequest)
    private static final cn.daxiang.protocol.game.ChatProtocol.ChatDeletePersonalRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new cn.daxiang.protocol.game.ChatProtocol.ChatDeletePersonalRequest();
    }

    public static cn.daxiang.protocol.game.ChatProtocol.ChatDeletePersonalRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ChatDeletePersonalRequest>
        PARSER = new com.google.protobuf.AbstractParser<ChatDeletePersonalRequest>() {
      public ChatDeletePersonalRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ChatDeletePersonalRequest(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ChatDeletePersonalRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ChatDeletePersonalRequest> getParserForType() {
      return PARSER;
    }

    public cn.daxiang.protocol.game.ChatProtocol.ChatDeletePersonalRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ChatMessageResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_ChatMessageResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ChatMessage_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_ChatMessage_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ChatInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_ChatInfo_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ChatTimeResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_ChatTimeResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ChatSendMessageRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_ChatSendMessageRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ChatSendMessageRequest_CalledActorMapEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_ChatSendMessageRequest_CalledActorMapEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ChatDeletePersonalRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_ChatDeletePersonalRequest_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\027game/chatProtocol.proto\032\027game/typeProt" +
      "ocol.proto\032\031game/commonProtocol.proto\"9\n" +
      "\023ChatMessageResponse\022\"\n\014chatMessages\030\001 \003" +
      "(\0132\014.ChatMessage\"\277\001\n\013ChatMessage\022\r\n\005msgI" +
      "d\030\001 \001(\003\022%\n\013channelType\030\002 \001(\0162\020.ChatChann" +
      "elType\022\023\n\013fromActorId\030\003 \001(\003\022\022\n\ntargeterI" +
      "d\030\004 \001(\003\022&\n\017actorAttributes\030\005 \003(\0132\r.Actor" +
      "Profile\022\033\n\010chatInfo\030\006 \001(\0132\t.ChatInfo\022\014\n\004" +
      "time\030\007 \001(\003\"Y\n\010ChatInfo\022\033\n\004type\030\001 \001(\0162\r.C" +
      "hatInfoType\022\017\n\007actorId\030\002 \001(\003\022\r\n\005value\030\003 " +
      "\001(\014\022\020\n\010actorIds\030\004 \003(\003\" \n\020ChatTimeRespons" +
      "e\022\014\n\004time\030\001 \001(\003\"\217\002\n\026ChatSendMessageReque" +
      "st\022\036\n\004type\030\001 \001(\0162\020.ChatChannelType\022\020\n\010se" +
      "rverId\030\002 \001(\005\022\024\n\014channelValue\030\003 \001(\003\022\013\n\003ms" +
      "g\030\004 \001(\t\022C\n\016calledActorMap\030\005 \003(\0132+.ChatSe" +
      "ndMessageRequest.CalledActorMapEntry\022\017\n\007" +
      "toToken\030\006 \001(\t\022\023\n\013toActorName\030\007 \001(\t\0325\n\023Ca" +
      "lledActorMapEntry\022\013\n\003key\030\001 \001(\003\022\r\n\005value\030" +
      "\002 \001(\t:\0028\001\"\'\n\031ChatDeletePersonalRequest\022\n" +
      "\n\002id\030\001 \001(\003*\300\001\n\007ChatCmd\022\021\n\rCHAT_CMD_NONE\020" +
      "\000\022\021\n\rGET_CHAT_INFO\020\001\022\021\n\rGET_CHAT_TIME\020\002\022" +
      "\021\n\rSEND_CHAT_MSG\020\003\022\023\n\017DELETE_PERSONAL\020\004\022" +
      "\020\n\014GET_TOP_INFO\020\005\022\013\n\007ADD_TOP\020\006\022\016\n\nDELETE" +
      "_TOP\020\007\022\021\n\rPUSH_CHAT_MSG\020d\022\022\n\016PUSH_CLEAR_" +
      "MSG\020eB\034\n\030cn.daxiang.protocol.gameH\001b\006pro" +
      "to3"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
        new com.google.protobuf.Descriptors.FileDescriptor.    InternalDescriptorAssigner() {
          public com.google.protobuf.ExtensionRegistry assignDescriptors(
              com.google.protobuf.Descriptors.FileDescriptor root) {
            descriptor = root;
            return null;
          }
        };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          cn.daxiang.protocol.game.TypeProtocol.getDescriptor(),
          cn.daxiang.protocol.game.CommonProtocol.getDescriptor(),
        }, assigner);
    internal_static_ChatMessageResponse_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_ChatMessageResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_ChatMessageResponse_descriptor,
        new java.lang.String[] { "ChatMessages", });
    internal_static_ChatMessage_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_ChatMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_ChatMessage_descriptor,
        new java.lang.String[] { "MsgId", "ChannelType", "FromActorId", "TargeterId", "ActorAttributes", "ChatInfo", "Time", });
    internal_static_ChatInfo_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_ChatInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_ChatInfo_descriptor,
        new java.lang.String[] { "Type", "ActorId", "Value", "ActorIds", });
    internal_static_ChatTimeResponse_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_ChatTimeResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_ChatTimeResponse_descriptor,
        new java.lang.String[] { "Time", });
    internal_static_ChatSendMessageRequest_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_ChatSendMessageRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_ChatSendMessageRequest_descriptor,
        new java.lang.String[] { "Type", "ServerId", "ChannelValue", "Msg", "CalledActorMap", "ToToken", "ToActorName", });
    internal_static_ChatSendMessageRequest_CalledActorMapEntry_descriptor =
      internal_static_ChatSendMessageRequest_descriptor.getNestedTypes().get(0);
    internal_static_ChatSendMessageRequest_CalledActorMapEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_ChatSendMessageRequest_CalledActorMapEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_ChatDeletePersonalRequest_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_ChatDeletePersonalRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_ChatDeletePersonalRequest_descriptor,
        new java.lang.String[] { "Id", });
    cn.daxiang.protocol.game.TypeProtocol.getDescriptor();
    cn.daxiang.protocol.game.CommonProtocol.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
