// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: game/sanctuaryEquipmentProtocol.proto

package cn.daxiang.protocol.game;

public final class SanctuaryEquipmentProtocol {
  private SanctuaryEquipmentProtocol() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  /**
   * <pre>
   ** 归葬秘境装备 
   * </pre>
   *
   * Protobuf enum {@code SanctuaryEquipmentCmd}
   */
  public enum SanctuaryEquipmentCmd
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>SANCTUARY_EQUIPMENT_CMD_NONE = 0;</code>
     */
    SANCTUARY_EQUIPMENT_CMD_NONE(0),
    /**
     * <pre>
     **
     * 获取归葬秘境装备信息
     * &lt;pre&gt;
     * 请求:{&#64;code Request}
     * 响应:{&#64;code SanctuaryEquipmentResponse}
     * &lt;/pre&gt;
     * </pre>
     *
     * <code>GET_SANCTUARY_EQUIPMENT = 1;</code>
     */
    GET_SANCTUARY_EQUIPMENT(1),
    /**
     * <pre>
     **
     * 推送归葬秘境装备信息
     * &lt;pre&gt;
     * 推送:{&#64;code SanctuaryEquipmentResponse}
     * &lt;/pre&gt;
     * </pre>
     *
     * <code>PUSH_SANCTUARY_EQUIPMENT_LIST = 100;</code>
     */
    PUSH_SANCTUARY_EQUIPMENT_LIST(100),
    /**
     * <pre>
     **
     * 推送删除归葬秘境装备信息
     * &lt;pre&gt;
     * 推送:{&#64;code LongListPacket}被删除装备的唯一Id列表
     * &lt;/pre&gt;
     * </pre>
     *
     * <code>PUSH_SANCTUARY_EQUIPMENT_DELETE_LIST = 101;</code>
     */
    PUSH_SANCTUARY_EQUIPMENT_DELETE_LIST(101),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>SANCTUARY_EQUIPMENT_CMD_NONE = 0;</code>
     */
    public static final int SANCTUARY_EQUIPMENT_CMD_NONE_VALUE = 0;
    /**
     * <pre>
     **
     * 获取归葬秘境装备信息
     * &lt;pre&gt;
     * 请求:{&#64;code Request}
     * 响应:{&#64;code SanctuaryEquipmentResponse}
     * &lt;/pre&gt;
     * </pre>
     *
     * <code>GET_SANCTUARY_EQUIPMENT = 1;</code>
     */
    public static final int GET_SANCTUARY_EQUIPMENT_VALUE = 1;
    /**
     * <pre>
     **
     * 推送归葬秘境装备信息
     * &lt;pre&gt;
     * 推送:{&#64;code SanctuaryEquipmentResponse}
     * &lt;/pre&gt;
     * </pre>
     *
     * <code>PUSH_SANCTUARY_EQUIPMENT_LIST = 100;</code>
     */
    public static final int PUSH_SANCTUARY_EQUIPMENT_LIST_VALUE = 100;
    /**
     * <pre>
     **
     * 推送删除归葬秘境装备信息
     * &lt;pre&gt;
     * 推送:{&#64;code LongListPacket}被删除装备的唯一Id列表
     * &lt;/pre&gt;
     * </pre>
     *
     * <code>PUSH_SANCTUARY_EQUIPMENT_DELETE_LIST = 101;</code>
     */
    public static final int PUSH_SANCTUARY_EQUIPMENT_DELETE_LIST_VALUE = 101;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static SanctuaryEquipmentCmd valueOf(int value) {
      return forNumber(value);
    }

    public static SanctuaryEquipmentCmd forNumber(int value) {
      switch (value) {
        case 0: return SANCTUARY_EQUIPMENT_CMD_NONE;
        case 1: return GET_SANCTUARY_EQUIPMENT;
        case 100: return PUSH_SANCTUARY_EQUIPMENT_LIST;
        case 101: return PUSH_SANCTUARY_EQUIPMENT_DELETE_LIST;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<SanctuaryEquipmentCmd>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        SanctuaryEquipmentCmd> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<SanctuaryEquipmentCmd>() {
            public SanctuaryEquipmentCmd findValueByNumber(int number) {
              return SanctuaryEquipmentCmd.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.getDescriptor().getEnumTypes().get(0);
    }

    private static final SanctuaryEquipmentCmd[] VALUES = values();

    public static SanctuaryEquipmentCmd valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private SanctuaryEquipmentCmd(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:SanctuaryEquipmentCmd)
  }

  public interface SanctuaryEquipmentResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:SanctuaryEquipmentResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .SanctuaryEquipment sanctuaryEquipment = 1;</code>
     */
    java.util.List<cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment> 
        getSanctuaryEquipmentList();
    /**
     * <code>repeated .SanctuaryEquipment sanctuaryEquipment = 1;</code>
     */
    cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment getSanctuaryEquipment(int index);
    /**
     * <code>repeated .SanctuaryEquipment sanctuaryEquipment = 1;</code>
     */
    int getSanctuaryEquipmentCount();
    /**
     * <code>repeated .SanctuaryEquipment sanctuaryEquipment = 1;</code>
     */
    java.util.List<? extends cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipmentOrBuilder> 
        getSanctuaryEquipmentOrBuilderList();
    /**
     * <code>repeated .SanctuaryEquipment sanctuaryEquipment = 1;</code>
     */
    cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipmentOrBuilder getSanctuaryEquipmentOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code SanctuaryEquipmentResponse}
   */
  public  static final class SanctuaryEquipmentResponse extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:SanctuaryEquipmentResponse)
      SanctuaryEquipmentResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SanctuaryEquipmentResponse.newBuilder() to construct.
    private SanctuaryEquipmentResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SanctuaryEquipmentResponse() {
      sanctuaryEquipment_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private SanctuaryEquipmentResponse(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
                sanctuaryEquipment_ = new java.util.ArrayList<cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment>();
                mutable_bitField0_ |= 0x00000001;
              }
              sanctuaryEquipment_.add(
                  input.readMessage(cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment.parser(), extensionRegistry));
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
          sanctuaryEquipment_ = java.util.Collections.unmodifiableList(sanctuaryEquipment_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.internal_static_SanctuaryEquipmentResponse_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.internal_static_SanctuaryEquipmentResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipmentResponse.class, cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipmentResponse.Builder.class);
    }

    public static final int SANCTUARYEQUIPMENT_FIELD_NUMBER = 1;
    private java.util.List<cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment> sanctuaryEquipment_;
    /**
     * <code>repeated .SanctuaryEquipment sanctuaryEquipment = 1;</code>
     */
    public java.util.List<cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment> getSanctuaryEquipmentList() {
      return sanctuaryEquipment_;
    }
    /**
     * <code>repeated .SanctuaryEquipment sanctuaryEquipment = 1;</code>
     */
    public java.util.List<? extends cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipmentOrBuilder> 
        getSanctuaryEquipmentOrBuilderList() {
      return sanctuaryEquipment_;
    }
    /**
     * <code>repeated .SanctuaryEquipment sanctuaryEquipment = 1;</code>
     */
    public int getSanctuaryEquipmentCount() {
      return sanctuaryEquipment_.size();
    }
    /**
     * <code>repeated .SanctuaryEquipment sanctuaryEquipment = 1;</code>
     */
    public cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment getSanctuaryEquipment(int index) {
      return sanctuaryEquipment_.get(index);
    }
    /**
     * <code>repeated .SanctuaryEquipment sanctuaryEquipment = 1;</code>
     */
    public cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipmentOrBuilder getSanctuaryEquipmentOrBuilder(
        int index) {
      return sanctuaryEquipment_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < sanctuaryEquipment_.size(); i++) {
        output.writeMessage(1, sanctuaryEquipment_.get(i));
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < sanctuaryEquipment_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, sanctuaryEquipment_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipmentResponse)) {
        return super.equals(obj);
      }
      cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipmentResponse other = (cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipmentResponse) obj;

      boolean result = true;
      result = result && getSanctuaryEquipmentList()
          .equals(other.getSanctuaryEquipmentList());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getSanctuaryEquipmentCount() > 0) {
        hash = (37 * hash) + SANCTUARYEQUIPMENT_FIELD_NUMBER;
        hash = (53 * hash) + getSanctuaryEquipmentList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipmentResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipmentResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipmentResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipmentResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipmentResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipmentResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipmentResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipmentResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipmentResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipmentResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipmentResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipmentResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipmentResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code SanctuaryEquipmentResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:SanctuaryEquipmentResponse)
        cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipmentResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.internal_static_SanctuaryEquipmentResponse_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.internal_static_SanctuaryEquipmentResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipmentResponse.class, cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipmentResponse.Builder.class);
      }

      // Construct using cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipmentResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getSanctuaryEquipmentFieldBuilder();
        }
      }
      public Builder clear() {
        super.clear();
        if (sanctuaryEquipmentBuilder_ == null) {
          sanctuaryEquipment_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          sanctuaryEquipmentBuilder_.clear();
        }
        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.internal_static_SanctuaryEquipmentResponse_descriptor;
      }

      public cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipmentResponse getDefaultInstanceForType() {
        return cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipmentResponse.getDefaultInstance();
      }

      public cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipmentResponse build() {
        cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipmentResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipmentResponse buildPartial() {
        cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipmentResponse result = new cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipmentResponse(this);
        int from_bitField0_ = bitField0_;
        if (sanctuaryEquipmentBuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001)) {
            sanctuaryEquipment_ = java.util.Collections.unmodifiableList(sanctuaryEquipment_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.sanctuaryEquipment_ = sanctuaryEquipment_;
        } else {
          result.sanctuaryEquipment_ = sanctuaryEquipmentBuilder_.build();
        }
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipmentResponse) {
          return mergeFrom((cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipmentResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipmentResponse other) {
        if (other == cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipmentResponse.getDefaultInstance()) return this;
        if (sanctuaryEquipmentBuilder_ == null) {
          if (!other.sanctuaryEquipment_.isEmpty()) {
            if (sanctuaryEquipment_.isEmpty()) {
              sanctuaryEquipment_ = other.sanctuaryEquipment_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureSanctuaryEquipmentIsMutable();
              sanctuaryEquipment_.addAll(other.sanctuaryEquipment_);
            }
            onChanged();
          }
        } else {
          if (!other.sanctuaryEquipment_.isEmpty()) {
            if (sanctuaryEquipmentBuilder_.isEmpty()) {
              sanctuaryEquipmentBuilder_.dispose();
              sanctuaryEquipmentBuilder_ = null;
              sanctuaryEquipment_ = other.sanctuaryEquipment_;
              bitField0_ = (bitField0_ & ~0x00000001);
              sanctuaryEquipmentBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getSanctuaryEquipmentFieldBuilder() : null;
            } else {
              sanctuaryEquipmentBuilder_.addAllMessages(other.sanctuaryEquipment_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipmentResponse parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipmentResponse) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment> sanctuaryEquipment_ =
        java.util.Collections.emptyList();
      private void ensureSanctuaryEquipmentIsMutable() {
        if (!((bitField0_ & 0x00000001) == 0x00000001)) {
          sanctuaryEquipment_ = new java.util.ArrayList<cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment>(sanctuaryEquipment_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment, cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment.Builder, cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipmentOrBuilder> sanctuaryEquipmentBuilder_;

      /**
       * <code>repeated .SanctuaryEquipment sanctuaryEquipment = 1;</code>
       */
      public java.util.List<cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment> getSanctuaryEquipmentList() {
        if (sanctuaryEquipmentBuilder_ == null) {
          return java.util.Collections.unmodifiableList(sanctuaryEquipment_);
        } else {
          return sanctuaryEquipmentBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .SanctuaryEquipment sanctuaryEquipment = 1;</code>
       */
      public int getSanctuaryEquipmentCount() {
        if (sanctuaryEquipmentBuilder_ == null) {
          return sanctuaryEquipment_.size();
        } else {
          return sanctuaryEquipmentBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .SanctuaryEquipment sanctuaryEquipment = 1;</code>
       */
      public cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment getSanctuaryEquipment(int index) {
        if (sanctuaryEquipmentBuilder_ == null) {
          return sanctuaryEquipment_.get(index);
        } else {
          return sanctuaryEquipmentBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .SanctuaryEquipment sanctuaryEquipment = 1;</code>
       */
      public Builder setSanctuaryEquipment(
          int index, cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment value) {
        if (sanctuaryEquipmentBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureSanctuaryEquipmentIsMutable();
          sanctuaryEquipment_.set(index, value);
          onChanged();
        } else {
          sanctuaryEquipmentBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .SanctuaryEquipment sanctuaryEquipment = 1;</code>
       */
      public Builder setSanctuaryEquipment(
          int index, cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment.Builder builderForValue) {
        if (sanctuaryEquipmentBuilder_ == null) {
          ensureSanctuaryEquipmentIsMutable();
          sanctuaryEquipment_.set(index, builderForValue.build());
          onChanged();
        } else {
          sanctuaryEquipmentBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .SanctuaryEquipment sanctuaryEquipment = 1;</code>
       */
      public Builder addSanctuaryEquipment(cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment value) {
        if (sanctuaryEquipmentBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureSanctuaryEquipmentIsMutable();
          sanctuaryEquipment_.add(value);
          onChanged();
        } else {
          sanctuaryEquipmentBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .SanctuaryEquipment sanctuaryEquipment = 1;</code>
       */
      public Builder addSanctuaryEquipment(
          int index, cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment value) {
        if (sanctuaryEquipmentBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureSanctuaryEquipmentIsMutable();
          sanctuaryEquipment_.add(index, value);
          onChanged();
        } else {
          sanctuaryEquipmentBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .SanctuaryEquipment sanctuaryEquipment = 1;</code>
       */
      public Builder addSanctuaryEquipment(
          cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment.Builder builderForValue) {
        if (sanctuaryEquipmentBuilder_ == null) {
          ensureSanctuaryEquipmentIsMutable();
          sanctuaryEquipment_.add(builderForValue.build());
          onChanged();
        } else {
          sanctuaryEquipmentBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .SanctuaryEquipment sanctuaryEquipment = 1;</code>
       */
      public Builder addSanctuaryEquipment(
          int index, cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment.Builder builderForValue) {
        if (sanctuaryEquipmentBuilder_ == null) {
          ensureSanctuaryEquipmentIsMutable();
          sanctuaryEquipment_.add(index, builderForValue.build());
          onChanged();
        } else {
          sanctuaryEquipmentBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .SanctuaryEquipment sanctuaryEquipment = 1;</code>
       */
      public Builder addAllSanctuaryEquipment(
          java.lang.Iterable<? extends cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment> values) {
        if (sanctuaryEquipmentBuilder_ == null) {
          ensureSanctuaryEquipmentIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, sanctuaryEquipment_);
          onChanged();
        } else {
          sanctuaryEquipmentBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .SanctuaryEquipment sanctuaryEquipment = 1;</code>
       */
      public Builder clearSanctuaryEquipment() {
        if (sanctuaryEquipmentBuilder_ == null) {
          sanctuaryEquipment_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          sanctuaryEquipmentBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .SanctuaryEquipment sanctuaryEquipment = 1;</code>
       */
      public Builder removeSanctuaryEquipment(int index) {
        if (sanctuaryEquipmentBuilder_ == null) {
          ensureSanctuaryEquipmentIsMutable();
          sanctuaryEquipment_.remove(index);
          onChanged();
        } else {
          sanctuaryEquipmentBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .SanctuaryEquipment sanctuaryEquipment = 1;</code>
       */
      public cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment.Builder getSanctuaryEquipmentBuilder(
          int index) {
        return getSanctuaryEquipmentFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .SanctuaryEquipment sanctuaryEquipment = 1;</code>
       */
      public cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipmentOrBuilder getSanctuaryEquipmentOrBuilder(
          int index) {
        if (sanctuaryEquipmentBuilder_ == null) {
          return sanctuaryEquipment_.get(index);  } else {
          return sanctuaryEquipmentBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .SanctuaryEquipment sanctuaryEquipment = 1;</code>
       */
      public java.util.List<? extends cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipmentOrBuilder> 
           getSanctuaryEquipmentOrBuilderList() {
        if (sanctuaryEquipmentBuilder_ != null) {
          return sanctuaryEquipmentBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(sanctuaryEquipment_);
        }
      }
      /**
       * <code>repeated .SanctuaryEquipment sanctuaryEquipment = 1;</code>
       */
      public cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment.Builder addSanctuaryEquipmentBuilder() {
        return getSanctuaryEquipmentFieldBuilder().addBuilder(
            cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment.getDefaultInstance());
      }
      /**
       * <code>repeated .SanctuaryEquipment sanctuaryEquipment = 1;</code>
       */
      public cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment.Builder addSanctuaryEquipmentBuilder(
          int index) {
        return getSanctuaryEquipmentFieldBuilder().addBuilder(
            index, cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment.getDefaultInstance());
      }
      /**
       * <code>repeated .SanctuaryEquipment sanctuaryEquipment = 1;</code>
       */
      public java.util.List<cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment.Builder> 
           getSanctuaryEquipmentBuilderList() {
        return getSanctuaryEquipmentFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment, cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment.Builder, cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipmentOrBuilder> 
          getSanctuaryEquipmentFieldBuilder() {
        if (sanctuaryEquipmentBuilder_ == null) {
          sanctuaryEquipmentBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment, cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment.Builder, cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipmentOrBuilder>(
                  sanctuaryEquipment_,
                  ((bitField0_ & 0x00000001) == 0x00000001),
                  getParentForChildren(),
                  isClean());
          sanctuaryEquipment_ = null;
        }
        return sanctuaryEquipmentBuilder_;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:SanctuaryEquipmentResponse)
    }

    // @@protoc_insertion_point(class_scope:SanctuaryEquipmentResponse)
    private static final cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipmentResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipmentResponse();
    }

    public static cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipmentResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<SanctuaryEquipmentResponse>
        PARSER = new com.google.protobuf.AbstractParser<SanctuaryEquipmentResponse>() {
      public SanctuaryEquipmentResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new SanctuaryEquipmentResponse(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SanctuaryEquipmentResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SanctuaryEquipmentResponse> getParserForType() {
      return PARSER;
    }

    public cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipmentResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SanctuaryEquipmentOrBuilder extends
      // @@protoc_insertion_point(interface_extends:SanctuaryEquipment)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     ** 归葬秘境装备ID 
     * </pre>
     *
     * <code>int64 sanctuaryEquipmentId = 1;</code>
     */
    long getSanctuaryEquipmentId();

    /**
     * <pre>
     ** 配置ID 
     * </pre>
     *
     * <code>int32 configId = 2;</code>
     */
    int getConfigId();
  }
  /**
   * Protobuf type {@code SanctuaryEquipment}
   */
  public  static final class SanctuaryEquipment extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:SanctuaryEquipment)
      SanctuaryEquipmentOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SanctuaryEquipment.newBuilder() to construct.
    private SanctuaryEquipment(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SanctuaryEquipment() {
      sanctuaryEquipmentId_ = 0L;
      configId_ = 0;
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private SanctuaryEquipment(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {

              sanctuaryEquipmentId_ = input.readInt64();
              break;
            }
            case 16: {

              configId_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.internal_static_SanctuaryEquipment_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.internal_static_SanctuaryEquipment_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment.class, cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment.Builder.class);
    }

    public static final int SANCTUARYEQUIPMENTID_FIELD_NUMBER = 1;
    private long sanctuaryEquipmentId_;
    /**
     * <pre>
     ** 归葬秘境装备ID 
     * </pre>
     *
     * <code>int64 sanctuaryEquipmentId = 1;</code>
     */
    public long getSanctuaryEquipmentId() {
      return sanctuaryEquipmentId_;
    }

    public static final int CONFIGID_FIELD_NUMBER = 2;
    private int configId_;
    /**
     * <pre>
     ** 配置ID 
     * </pre>
     *
     * <code>int32 configId = 2;</code>
     */
    public int getConfigId() {
      return configId_;
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (sanctuaryEquipmentId_ != 0L) {
        output.writeInt64(1, sanctuaryEquipmentId_);
      }
      if (configId_ != 0) {
        output.writeInt32(2, configId_);
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (sanctuaryEquipmentId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, sanctuaryEquipmentId_);
      }
      if (configId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, configId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment)) {
        return super.equals(obj);
      }
      cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment other = (cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment) obj;

      boolean result = true;
      result = result && (getSanctuaryEquipmentId()
          == other.getSanctuaryEquipmentId());
      result = result && (getConfigId()
          == other.getConfigId());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + SANCTUARYEQUIPMENTID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getSanctuaryEquipmentId());
      hash = (37 * hash) + CONFIGID_FIELD_NUMBER;
      hash = (53 * hash) + getConfigId();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code SanctuaryEquipment}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:SanctuaryEquipment)
        cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipmentOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.internal_static_SanctuaryEquipment_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.internal_static_SanctuaryEquipment_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment.class, cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment.Builder.class);
      }

      // Construct using cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        sanctuaryEquipmentId_ = 0L;

        configId_ = 0;

        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.internal_static_SanctuaryEquipment_descriptor;
      }

      public cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment getDefaultInstanceForType() {
        return cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment.getDefaultInstance();
      }

      public cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment build() {
        cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment buildPartial() {
        cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment result = new cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment(this);
        result.sanctuaryEquipmentId_ = sanctuaryEquipmentId_;
        result.configId_ = configId_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment) {
          return mergeFrom((cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment other) {
        if (other == cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment.getDefaultInstance()) return this;
        if (other.getSanctuaryEquipmentId() != 0L) {
          setSanctuaryEquipmentId(other.getSanctuaryEquipmentId());
        }
        if (other.getConfigId() != 0) {
          setConfigId(other.getConfigId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private long sanctuaryEquipmentId_ ;
      /**
       * <pre>
       ** 归葬秘境装备ID 
       * </pre>
       *
       * <code>int64 sanctuaryEquipmentId = 1;</code>
       */
      public long getSanctuaryEquipmentId() {
        return sanctuaryEquipmentId_;
      }
      /**
       * <pre>
       ** 归葬秘境装备ID 
       * </pre>
       *
       * <code>int64 sanctuaryEquipmentId = 1;</code>
       */
      public Builder setSanctuaryEquipmentId(long value) {
        
        sanctuaryEquipmentId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 归葬秘境装备ID 
       * </pre>
       *
       * <code>int64 sanctuaryEquipmentId = 1;</code>
       */
      public Builder clearSanctuaryEquipmentId() {
        
        sanctuaryEquipmentId_ = 0L;
        onChanged();
        return this;
      }

      private int configId_ ;
      /**
       * <pre>
       ** 配置ID 
       * </pre>
       *
       * <code>int32 configId = 2;</code>
       */
      public int getConfigId() {
        return configId_;
      }
      /**
       * <pre>
       ** 配置ID 
       * </pre>
       *
       * <code>int32 configId = 2;</code>
       */
      public Builder setConfigId(int value) {
        
        configId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 配置ID 
       * </pre>
       *
       * <code>int32 configId = 2;</code>
       */
      public Builder clearConfigId() {
        
        configId_ = 0;
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:SanctuaryEquipment)
    }

    // @@protoc_insertion_point(class_scope:SanctuaryEquipment)
    private static final cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment();
    }

    public static cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<SanctuaryEquipment>
        PARSER = new com.google.protobuf.AbstractParser<SanctuaryEquipment>() {
      public SanctuaryEquipment parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new SanctuaryEquipment(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SanctuaryEquipment> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SanctuaryEquipment> getParserForType() {
      return PARSER;
    }

    public cn.daxiang.protocol.game.SanctuaryEquipmentProtocol.SanctuaryEquipment getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_SanctuaryEquipmentResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_SanctuaryEquipmentResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_SanctuaryEquipment_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_SanctuaryEquipment_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n%game/sanctuaryEquipmentProtocol.proto\032" +
      "\031game/commonProtocol.proto\"M\n\032SanctuaryE" +
      "quipmentResponse\022/\n\022sanctuaryEquipment\030\001" +
      " \003(\0132\023.SanctuaryEquipment\"D\n\022SanctuaryEq" +
      "uipment\022\034\n\024sanctuaryEquipmentId\030\001 \001(\003\022\020\n" +
      "\010configId\030\002 \001(\005*\243\001\n\025SanctuaryEquipmentCm" +
      "d\022 \n\034SANCTUARY_EQUIPMENT_CMD_NONE\020\000\022\033\n\027G" +
      "ET_SANCTUARY_EQUIPMENT\020\001\022!\n\035PUSH_SANCTUA" +
      "RY_EQUIPMENT_LIST\020d\022(\n$PUSH_SANCTUARY_EQ" +
      "UIPMENT_DELETE_LIST\020eB\034\n\030cn.daxiang.prot" +
      "ocol.gameH\001b\006proto3"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
        new com.google.protobuf.Descriptors.FileDescriptor.    InternalDescriptorAssigner() {
          public com.google.protobuf.ExtensionRegistry assignDescriptors(
              com.google.protobuf.Descriptors.FileDescriptor root) {
            descriptor = root;
            return null;
          }
        };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          cn.daxiang.protocol.game.CommonProtocol.getDescriptor(),
        }, assigner);
    internal_static_SanctuaryEquipmentResponse_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_SanctuaryEquipmentResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_SanctuaryEquipmentResponse_descriptor,
        new java.lang.String[] { "SanctuaryEquipment", });
    internal_static_SanctuaryEquipment_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_SanctuaryEquipment_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_SanctuaryEquipment_descriptor,
        new java.lang.String[] { "SanctuaryEquipmentId", "ConfigId", });
    cn.daxiang.protocol.game.CommonProtocol.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
