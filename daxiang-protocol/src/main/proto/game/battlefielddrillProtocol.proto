syntax = "proto3";
option java_package = "cn.daxiang.protocol.game";
option optimize_for = SPEED;

import "game/commonProtocol.proto";

/** 沙场演武 */
enum BattlefielddrillCmd {
  BATTLEFIELD_DRILL_CMD_NONE = 0;
  /**
    * 获取沙场演武信息
    * <pre>
    * 请求:{@code Request}
    * 响应:{@code BattlefieldDrillResponse}
    * </pre>
    */
  BATTLEFIELD_DRILL_GET_INFO = 1;
  /**
   * 获取沙场演武排名
   * <pre>
   * 请求:{@code Request}
   * 响应:{@code BattlefieldDrillRankResponse}
   * </pre>
   */
  BATTLEFIELD_DRILL_RANK = 2;
  /**
   * 沙场演武准备
   * <pre>
   * 请求:{@code BattlefieldDrillIdRequest}
   * 响应:{@code BattlefieldDrillPrepareResponse}
   * </pre>
   */
  BATTLEFIELD_DRILL_PREPARE = 3;
  /**
   * 沙场演武挑战
   * <pre>
   * 请求:{@code BattlefieldDrillChallengeRequest}
   * 响应:{@code RewardResultResponse}
   * 推送:{@code BattlefieldDrillResponse}
   * </pre>
   */
  BATTLEFIELD_DRILL_CHALLENGE = 4;

  /**
   * 沙场演武跳过
   * <pre>
   * 请求:{@code Request}
   * 响应:{@code RewardResultResponse}
   * 推送:{@code BattlefieldDrillResponse}
   * </pre>
   */
  BATTLEFIELD_DRILL_SKIP = 5;
  /**
	 * 领取阶段奖励
	 * 请求:{@code BattlefieldDrillStageRewardRequest}
	 * 响应:{@code RewardResultResponse}
 	 * 推送:{@code BattlefieldDrillResponse}
	 */
  BATTLEFIELD_DRILL_STAGE_REWARD = 6;
  /**
   * 获取荣耀殿堂排行
   * <pre>
   * 请求:{@code Request}
   * 响应:{@code HonourRankResponse}
   * </pre>
   */
  BATTLEFIELD_DRILL_HONOUR_RANK = 7;
  /**
   * 领取首通奖励
   * 请求:{@code IntListPacket} InfiniteStageFloorConfig.id
   * 响应:{@code RewardResultResponse}
   * 推送:{@code BattlefieldDrillResponse}
   */
  BATTLEFIELD_DRILL_FIRST_PASS_REWARD = 8;
  /**
   * 信物碎片提交
   * 请求:{@code IntPacket} InfiniteStageTokenConfig.id
   * 响应:{@code BattlefieldDrillResponse}
   */
  BATTLEFIELD_DRILL_TOKEN_UPGRADE_REWARD = 9;
  /**
   * 每日奖励
   * 请求:{@code Request}
   * 响应:{@code RewardResultResponse}
   * 推送:{@code BattlefieldDrillResponse}
   */
  BATTLEFIELD_DRILL_TODAY_REWARD_REWARD = 10;

  /**
   * 推送沙场演武信息
   * <pre>
   * 推送:{@code BattlefieldDrillResponse}
   * </pre>
   */
  PUSH_BATTLEFIELD_DRILL_INFO = 100;
}
message BattlefieldDrillResponse {
  /**
    * 沙场演武波次ID
    */
  int32 id = 1;
  /**
   * 历史最高波次ID
   */
  int32 maxId = 2;
  /**
   * 沙场演武自定义数据
   */
  BattlefieldDrillData data = 3;
  /** 
   * 已领取阶段奖励list(configId)
   */
  repeated int32 receiveReward = 4;
  /**
   * 已领取首通奖励list(configId)
   */
  repeated int32 firstPassReceives = 5;
  /**
   * 乾坤信物 key:configId,value:使用碎片数量
   */
  map<int32, int32> token = 6;
  /**
   * 每日奖励领取状态
   */
  bool todayReward = 7;
  /**
   * 昨天最高波次ID
   */
  int32 yesterdayMaxId = 8;
  /**
  * 排行榜中的位置（采用万分比）
  */
  int32 ratio = 9;
}
message BattlefieldDrillData {
  /**  战场英雄数据  */
  repeated BattlefieldDrillInfoHeroData heroDatas = 1;
  /**  怪物英雄数据  */
  repeated BattlefieldDrillInfoMonsterData monsterDatas = 2;
}
message BattlefieldDrillRankResponse {
  /**  排名列表  */
  repeated BattlefieldDrillRank ranks = 1;
}
message BattlefieldDrillRank {
  /** 角色属性 */
  ActorProfile actorProfile = 1;
  /** 排名 */
  int64 rank = 2;
  /** 当前波次ID */
  int32 id = 3;
}
message BattlefieldDrillIdRequest {
  /** ID*/
  int32 id = 1;
}
message BattlefieldDrillChallengeRequest {
  /**
     * ID
     */
  int32 id = 1;
  /**
   * 自定义数据
   */
  BattlefieldDrillData data = 2;
  /**
   * 检查所需数据：[{上阵英雄配置ID:战力,上阵英雄配置ID:战力},[[英雄配置ID,技能攻击ID,伤害,是否暴击(0:false.1:true)],[英雄配置ID,技能攻击ID,伤害,是否暴击(0:false.1:true)]]]
   */
  string check = 3;
}
message BattlefieldDrillInfoHeroData {
  /** 英雄配置id*/
  string heroConfigId = 1;
  /** 塔座id*/
  int32 siteId = 2;
  /** 能量*/
  int32 energy = 3;
}
message BattlefieldDrillInfoMonsterData {
  /** 怪物配置id*/
  string monsterResId = 1;
  /** 路径id*/
  int32 pathIndex = 2;
  /** 路径百分比*/
  int32 passedDist = 3;
  /** 血量*/
  int64 hp = 4;
}

message BattlefieldDrillStageRewardRequest {
  /**阶段奖励配置表的id*/
  repeated int32 configId = 1;
}

message BattlefieldDrillPrepareResponse {
  /**是否碾压*/
  bool isRoll = 1;
  /** 碾压奖励*/
  RewardResult reward = 2;
  /** 沙场演武波次ID*/
  int32 id = 3;
  /** 历史最高波次ID*/
  int32 maxId = 4;
}

message HonourRankResponse {
  map<int32, HonourRankList> list = 1;
}

message HonourRankList {
  /**  排名列表  */
  repeated HonourRank ranks = 1;
}

message HonourRank {
  /** 角色属性 */
  ActorProfile actorProfile = 1;
  /** 排名 */
  int64 rank = 2;
  /** 当前波次ID */
  int64 power = 3;
}