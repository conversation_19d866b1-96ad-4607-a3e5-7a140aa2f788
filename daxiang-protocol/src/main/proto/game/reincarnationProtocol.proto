syntax = "proto3";
option java_package = "cn.daxiang.protocol.game";
option optimize_for = SPEED;
import "game/commonProtocol.proto";
import "game/battleProtocol.proto";
/** 轮回战场 */
enum ReincarnationCmd {
  REINCARNATION_CMD_NONE = 0;
  /**
   * 轮回战场-获取信息
   * 请求:{@code Request}
   * 响应:{@code ReincarnationResponse}
   */
  REINCARNATION_INFO = 1;
  /**
   * 轮回战场-获取个人信息
   * 请求:{@code Request}
   * 响应:{@code ReincarnationActorResponse}
   */
  REINCARNATION_ACTOR_INFO = 2;
  /**
   * 轮回战场-获取英雄信息
   * 请求:{@code Request}
   * 响应:{@code ReincarnationHeroResponse}
   */
  REINCARNATION_HERO_INFO = 3;
  /**
   * 轮回战场-领取试炼奖励
   * 请求:{@code Request}
   * 响应:{@code ReincarnationRewardResultResponse}
   * 推送:{@code ReincarnationActorResponse}
   * 推送:{@code ReincarnationHeroResponse}
   */
  REINCARNATION_FLOOR_REWARD = 4;
  /**
   * 轮回战场-布阵
   * 请求:{@code ReincarnationLineupRequest}
   * 响应:{@code Response}
   * 推送:{@code ReincarnationActorResponse}
   */
  REINCARNATION_CHANGE_LINEUP = 5;
  /**
   * 轮回战场-进入试炼
   * 请求:{@code Request}
   * 响应:{@code ReincarnationResponse}
   */
  REINCARNATION_ENTER_BATTLEFIELD = 6;
  /**
   * 轮回战场-战斗
   * 请求:{@code LongPacket}->(目标角色ID)
   * 响应:{@code BoolPacket}->战斗是否成功（停服等原因）
   * 推送:{@code ReincarnationBattleResultResponse}
   */
  REINCARNATION_BATTLE = 7;
  /**
   * 轮回战场-一键试炼开始
   * 请求:{@code Request}
   * 响应:{@code Response}
   */
  REINCARNATION_QUICK_BATTLE_START = 8;
  /**
   * 轮回战场-一键试炼战斗
   * 请求:{@code LongPacket}->(目标角色ID)
   * 响应:{@code BoolPacket}->战斗是否成功（停服等原因）
   * 推送:{@code ReincarnationBattleResultResponse}
   */
  REINCARNATION_QUICK_BATTLE = 9;
  /**
   * 轮回战场-一键试炼结束
   * 请求:{@code Request}
   * 响应:{@code Response}
   * 推送:{@code ReincarnationResponse}
   */
  REINCARNATION_QUICK_BATTLE_STOP = 10;
  /**
   * 轮回战场-出售英雄
   * 请求:{@code LongListPacket}->英雄UID列表
   * 响应:{@code ReincarnationRewardResultResponse}
   * 推送:{@code ReincarnationActorResponse}
   * 推送:{@code LongListPacket}->删除英雄UID列表
   */
  REINCARNATION_SELL_HERO = 11;
  /**
   * 轮回战场-宝藏奖励
   * 请求:{@code Request}
   * 响应:{@code ReincarnationRewardResultResponse}
   * 推送:{@code ReincarnationActorResponse}
   * 推送:{@code ReincarnationHeroResponse}
   */
  REINCARNATION_TREASURE_REWARD = 12;
  /**
   * 轮回战场-购买宝箱
   * 请求:{@code IntPacket}->(宝箱星级)
   * 响应:{@code ReincarnationRewardResultResponse}
   * 推送:{@code ReincarnationActorResponse}
   * 推送:{@code ReincarnationHeroResponse}
   */
  REINCARNATION_BUY_BOX = 13;
  /**
   * 轮回战场-领取特权奖励
   * 请求:{@code IntPacket}->特权ID
   * 响应:{@code ReincarnationRewardResultResponse}
   * 推送:{@code ReincarnationActorResponse}
   * 推送:{@code ReincarnationHeroResponse}
   */
  REINCARNATION_PRIVILEGE_REWARD = 14;
  /**
   * 轮回战场-英雄升星
   * 请求:{@code LongPacket}->(英雄UID)
   * 响应:{@code ReincarnationHeroResponse}
   */
  REINCARNATION_HERO_UPGRADE_STAR = 15;
  /**
   * 轮回战场-获取排名列表
   * 请求:{@code Request}
   * 响应:{@code ReincarnationRankResponse}
   */
  REINCARNATION_GET_RANK_LIST = 16;
  /**
   * 轮回战场-获取目标阵容
   * 请求:{@code LongPacket}->(目标角色ID)
   * 响应:{@code ReincarnationLineupResponse}
   */
  REINCARNATION_GET_TARGET_LINEUP = 17;
  /**
   * 轮回战场-初始化
   * 请求:{@code Request}
   * 响应:{@code ReincarnationRewardResultResponse}
   * 推送:{@code ReincarnationActorResponse}
   * 推送:{@code ReincarnationHeroResponse}
   */
  REINCARNATION_INITIALIZE = 18;
  /**
   * 轮回战场-领取礼包
   * 请求:{@code IntPacket}->reincarnation_gift_config.id
   * 响应:{@code ReincarnationRewardResultResponse}
   * 推送:{@code ReincarnationActorResponse}
   * 推送:{@code ReincarnationHeroResponse}
   */
  REINCARNATION_DAY_GIFT = 19;
  /**
   * 轮回战场-购买特权
   * 请求:{@code IntPacket}->特权ID,reincarnation_privilege_config.id
   * 响应:{@code Response}
   * 推送:{@code ReincarnationActorResponse}
   */
  REINCARNATION_PRIVILEGE_BUY = 20;
  /**
   * 推送轮回战场信息
   * 推送:{@code ReincarnationResponse}
   */
  PUSH_REINCARNATION_INFO = 100;
  /**
   * 推送轮回战场个人信息
   * 推送:{@code ReincarnationActorResponse}
   */
  PUSH_REINCARNATION_ACTOR_INFO = 101;
  /**
   * 推送轮回战场英雄信息
   * 推送:{@code ReincarnationHeroResponse}
   */
  PUSH_REINCARNATION_HERO = 102;
  /**
   * 推送轮回战场删除英雄
   * 推送:{@code LongListPacket}->删除英雄UID列表
   */
  PUSH_REINCARNATION_DELETE_HERO = 103;
  /**
    * 推送轮回战场战斗结果
    * 推送:{@code ReincarnationBattleResultResponse}
    */
  PUSH_REINCARNATION_BATTLE_RESULT = 104;
}
/** 轮回战场信息 */
message ReincarnationInfo{
  /** 轮回战场状态 */
  ReincarnationState state = 1;
  /** 开始时间 */
  int64 startTime = 2;
  /** 结束 */
  int64 endTime = 3;
}

/** 轮回战场房间排名显示对象 */
message ReincarnationBattlefieldRankVO{
  /**
  * 角色属性Map
  * key:{@code ActorKey},value:Value
  */
  ActorProfile actorProfile = 1;
  /** 排名 */
  int64 rank = 2;
  /** 试炼重数 */
  int32 layer = 3;
  /** 徽章 */
  int32 badge = 4;
}


/** 轮回战场战场信息显示对象 */
message ReincarnationBattlefieldVO{
  /** 排名列表 */
  repeated ReincarnationBattlefieldRankVO ranks = 1;
  /** 战胜ID列表 */
  LongListPacket defeats = 2;
}
/** 轮回战场信息 */
message ReincarnationResponse{
  /** 赛季信息 */
  ReincarnationInfo info = 1;
  /** 战场信息 */
  ReincarnationBattlefieldVO battlefield = 2;
}


message ReincarnationPrivilege{
  /** 特权ID */
  int32 id = 1;
  /** 已领取天数 */
  int32  days = 2;
  /**
   * 下一次每日奖励领取时间
   * 默认为0
   * 如果当前时间小于此时间则不能领取
   * 每次领取后会设置为下一次可领取时间
   */
  int64 time = 3;
}

/** 轮回战场个人信息 */
message ReincarnationActorResponse{
  /** 试炼重数 */
  int32 layer = 1;
  /** 试炼层数 */
  int32 floor = 2;
  /**
   * 晋升条件
   * {floor:rank}
   * 没有对应的floor就是一键试炼直升
   */
  map<int32, int64> promotion = 3;
  /**
   * 试炼奖励信息
   * -1   直升
   * 0    已领取
   * 1-18 排名
   */
  int64 floorReward = 4;
  /** 轮回印记 */
  int32 stamp = 5;
  /**
   * 阵容信息
   * {positionId:heroUID}
   */
  map<int32, int64> lineup = 6;
  /** 轮回宝藏可领取次数 */
  int32  treasureTimes = 7;
  /**
   * 轮回宝藏下一次增加时间
   * 0表示可领取次数达到上限
   */
  int64 treasureNextTime = 8;
  /** 特权信息 */
  repeated ReincarnationPrivilege privileges = 9;
  /**
   * 每日奖励已领取列表
   * [id,id]
   * id->reincarnation_gift_config.id
   */
  IntListPacket dayGiftList = 10;
  /**
   * 购买每日奖励列表
   * [id,id]
   * id->reincarnation_gift_config.id
   */
  IntListPacket buyDayGiftList = 11;
}
/** 轮回战场英雄信息 */
message ReincarnationHero{
  /** 英雄UID */
  int64 uid = 1;
  /**
   * 英雄配置ID
   * reincarnation_hero_config.id
   */
  int32 id = 2;
  /**
   * 英雄星级
   * reincarnation_hero_config.star
   */
  int32 star = 3;
}
/** 轮回战场英雄响应 */
message ReincarnationHeroResponse{
  /** 英雄列表，增量更新 */
  repeated ReincarnationHero heroList = 1;
}
/** 轮回战场阵容显示对象 */
message ReincarnationLineupVO{
  /** 位置 */
  int32 position = 1;
  /**
   * 英雄配置ID
   * reincarnation_hero_config.id
   */
  int32 id = 2;
  /**
   * 英雄星级
   * reincarnation_hero_config.star
   */
  int32 star = 3;
  /** 战力 */
  int64 power = 4;
}
/** 轮回战场阵容响应  */
message ReincarnationLineupResponse{
  /** 阵容信息列表 */
  repeated ReincarnationLineupVO lineup = 1;
}

message ReincarnationBoxResult{
  /** 星级 */
  int32 star = 1;
  /** 英雄UID列表 */
  LongListPacket heroUIDList = 2;
}
/** 轮回战场奖励响应  */
message ReincarnationRewardResultResponse{
  /** 奖励结果 */
  RewardResult rewardResult = 1;
  /** 轮回印记 */
  int32 stamp = 2;
  /** 开宝箱结果列表 */
  repeated ReincarnationBoxResult boxResultList = 3;
}

/** 轮回战场排名显示对象 */
message ReincarnationRankVO{
  /**
  * 角色属性Map
  * key:{@code ActorKey},value:Value
  */
  ActorProfile actorProfile = 1;
  /** 排名 */
  int64 rank = 2;
  /** 试炼重数 */
  int32 layer = 3;
  /** 试炼层数 */
  int32 floor = 4;
}
/** 轮回战场排名响应 */
message ReincarnationRankResponse{
  repeated ReincarnationRankVO ranks=1;
}


/** 轮回战场布阵请求*/
message ReincarnationLineupRequest{
  /** 布阵信息{position,heroUid} */
  map<int32, int64> lineup = 1;
}
/** 轮回战场战斗结果响应  */
message ReincarnationBattleResultResponse{
  /**
   * 战报结果
   */
  BattleResultResponse battle = 1;
  /**
   * 重数信息
   * {actorId:layer}
   */
  map<int64,int32> layer=2;
}
/** 轮回战场状态 */
enum ReincarnationState{
  REINCARNATION_STATE_NONE = 0;
  /** 战斗 */
  REINCARNATION_STATE_BATTLE = 1;
  /** 休战 */
  REINCARNATION_STATE_SLEEP = 2;
}