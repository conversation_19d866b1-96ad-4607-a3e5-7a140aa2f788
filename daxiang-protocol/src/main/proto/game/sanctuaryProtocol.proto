syntax = "proto3";
option java_package = "cn.daxiang.protocol.game";
option optimize_for = SPEED;

import "game/commonProtocol.proto";
import "game/battleProtocol.proto";
/** 归葬秘境 */
enum SanctuaryCmd {
  SANCTUARY_CMD_NONE = 0;
  /**
   * 获取归藏秘境状态信息
   * <pre>
   * 请求:{@code Request}
   * 响应:{@code SanctuaryRegionStateResponse}
   * </pre>
   */
  SANCTUARY_REGION_STATE = 1;
  /**
   * 获取归藏秘境玩家信息
   * <pre>
   * 请求:{@code Request}
   * 响应:{@code SanctuaryActorResponse}
   * </pre>
   */
  GET_SANCTUARY_ACTOR = 2;
  /**
    * 获取归藏秘境玩家养成信息
    * <pre>
    * 请求:{@code Request}
    * 响应:{@code SanctuaryFosterResponse}
    * </pre>
    */
  GET_SANCTUARY_FOSTOR = 3;
  /**
   * 归藏秘境回合战斗
   * <pre>
   * 请求:{@code SanctuaryPVPRequest}
   * 响应:{@code Response}
   * 推送:{@code SanctuaryActorResponse}
   * 推送:{@code SanctuaryBattleResponse}
   * </pre>
   */
  SANCTUARY_PVP_BATTLE = 4;
  /**
   * 归藏秘境塔防准备
   * <pre>
   * 请求:{@code IntPacket}传塔防关卡Id
   * 响应:{@code Response}
   * </pre>
   */
  SANCTUARY_PVE_PREPARE = 5;
  /**
   * 归藏秘境塔防战斗
   * <pre>
   * 请求:{@code SanctuaryPVERequest}
   * 响应:{@code RewardResultResponse}
   * 推送:{@code SanctuaryActorResponse}
   * </pre>
   */
  SANCTUARY_PVE_BATTLE = 6;
  /**
   * 获取归藏秘境通关记录
   * <pre>
   * 请求:{@code IntPacket}传回合关卡Id
   * 响应:{@code SanctuaryRecordResponse}
   * </pre>
   */
  GET_SANCTUARY_RECORD = 7;
  /**
   * 获取归藏秘境关卡加成
   * <pre>
   * 请求:{@code IntPacket}传回合关卡Id
   * 响应:{@code SanctuaryAdditionResponse}
   * </pre>
   */
  GET_SANCTUARY_ADDITION = 8;
  /**
   * 获取归藏秘境进度排行榜
   * <pre>
   * 请求:{@code Request}
   * 响应:{@code SanctuaryProgressRankResponse}
   * </pre>
   */
  GET_SANCTUARY_PROGRESS_RANK = 9;
  /**
   * 获取我的归藏秘境进度排名
   * <pre>
   * 请求:{@code Request}
   * 响应:{@code LongPacket} -1代表未上榜
   * </pre>
   */
  GET_MY_SANCTUARY_PROGRESS_RANK = 10;
  /**
   * 获取归藏秘境山河值排行榜
   * <pre>
   * 请求:{@code Request}
   * 响应:{@code SanctuaryFosterRankResponse}
   * </pre>
   */
  GET_SANCTUARY_FOSTER_RANK = 11;
  /**
   * 领取归藏秘境补给
   * <pre>
   * 请求:{@code IntPacket}传配置ID
   * 响应:{@code RewardResultResponse}
   * 推送:{@code SanctuaryActorResponse}
   * </pre>
   */
  RECEIVE_SANCTUARY_SUPPLY = 12;
  /**
   * 领取归藏秘境评星奖励
   * <pre>
   * 请求:{@code IntPacket}传配置ID
   * 响应:{@code RewardResultResponse}
   * 推送:{@code SanctuaryActorResponse}
   * </pre>
   */
  RECEIVE_SANCTUARY_STAR_REWARD = 13;
  /**
   * 获取归藏秘境基金信息
   * <pre>
   * 请求:{@code Request}
   * 响应:{@code SanctuaryFundResponse}
   * </pre>
   */
  GET_SANCTUARY_FUND = 14;
  /**
    * 领取基金
    * <pre>
    * 请求:{@code RecieveSanctuaryFundRequest}
    * 响应:{@code RewardResultResponse}
    * 推送:{@code SanctuaryFundResponse}
    * </pre>
    */
  RECIEVE_SANCTUARY_FFUND = 15;
  /**
    * 一键领取基金
    * <pre>
    * 请求:{@code IntPacket} 传阶段
    * 响应:{@code RewardResultResponse}
    * 推送:{@code SanctuaryFundResponse}
    * </pre>
    */
  QUICKLY_RECIEVE_SANCTUARY_FFUND = 16;
  /**
    * 领取全民福利
    * <pre>
    * 请求:{@code IntPacket} 传阶段
    * 响应:{@code RewardResultResponse}
    * 推送:{@code SanctuaryFundResponse}
    * </pre>
    */
  RECIEVE_SANCTUARY_FWELFARE = 17;
  /**
    * 阶段任务奖励
    * <pre>
    * 请求:{@code IntPacket} 传阶段Id
    * 响应:{@code RewardResultResponse}
    * 推送:{@code SanctuaryActorResponse}
    * </pre>
    */
  RECIEVE_SANCTUARY_STAGE_TASK = 18;
  /**
   * 归葬秘境装备合成并且穿戴
   * 请求:{@code SanctuaryEquipmentSynthesizeRequest}
   * 响应:{@code Response}
   * 推送:{@code SanctuaryFosterResponse}
   */
  SANCTUARY_EQUIPMENT_SYNTHESIZE_AND_EQUIP = 19;
  /**
   * 归葬秘境装备穿戴
   * 请求:{@code SanctuaryEquipmentEquipRequest}
   * 响应:{@code Response}
   * 推送:{@code SanctuaryFosterResponse}
   */
  SANCTUARY_EQUIPMENT_EQUIP = 20;
  /**
   * 归葬秘境主建筑升级
   * 请求:{@code Request}
   * 响应:{@code Response}
   * 推送:{@code SanctuaryFosterResponse}
   */
  SANCTUARY_MAIN_BUILDING_LEVEL_UP = 21;
  /**
    * 归葬秘境阵营建筑升阶段
    * 请求:{@code IntPacket} 传阵营类型的Id
    * 响应:{@code Response}
    * 推送:{@code SanctuaryFosterResponse}
    */
  SANCTUARY_CAMP_BUILDING_STAGE_UP = 22;
  /**
   * 归葬秘境挂机奖励领取
   * 请求:{@code Request}
   * 响应:{@code RewardResultResponse}
   * 推送:{@code SanctuaryActorResponse}
   */
  SANCTUARY_RECEIVE_HANG_UP = 23;
  /**
   * 归葬秘境使用挂机道具
   * 请求:{@code KeyValuePacket}key:加速道具配置id value:使用个数
   * 响应:{@code RewardResultResponse}
   */
  SANCTUARY_USE_HANG_UP = 24;
  /**
   * 归藏秘境boss挑战
   * 请求:{@code SanctuaryLineup}传玩家的阵容
   * 响应:{@code Response}
   * 推送:{@code SanctuaryBossResponse}
   * 推送:{@code SanctuaryBossBattleResponse}
   */
  SANCTUARY_BOSS_CHALLENGE = 25;
  /**
  * 归藏秘境BOSS奖励领取
  * 请求:{@code Request}
  * 响应:{@code RewardResultResponse}
  * 推送:{@code SanctuaryBossResponse}
  */
  SANCTUARY_BOSS_RECEIVE = 26;
  /**
   * 归藏秘境Boss排行榜获取
   * 请求:{@code Request}
   * 响应:{@code SanctuaryBossRankResponse}
   */
  SANCTUARY_BOSS_RANK = 27;
  /**
   *  获取归藏秘境Boss信息
   *  请求:{@code Request}
   *  响应:{@code SanctuaryBossResponse}
   */
  SANCTUARY_BOSS_GET = 28;
  /**
   * 归藏秘境塔防快速通关
   * 请求:{@code IntPacket}塔防关卡Id
   * 响应:{@code RewardResultResponse}
   * 推送:{@code SanctuaryActorResponse}
   */
  SANCTUARY_PVE_BATTLE_QUICKLY_PASS = 29;
  /**
   * 归藏秘境关卡奖励领取
   * 请求:{@code IntPacket}-配置Id
   * 响应:{@code RewardResultResponse}
   * 推送:{@code SanctuaryActorResponse}
   */
  SANCTUARY_RECEIVE_STORY_REWARDS = 30;
  /**
   * 推送归藏秘境状态信息
   * <pre>
   * 推送:{@code SanctuaryRegionStateResponse}
   * </pre>
   */
  PUSH_SANCTUARY_REGION_STATE = 100;
  /**
    * 推送归藏秘境玩家个人信息
    * <pre>
    * 推送:{@code SanctuaryActorResponse}
    * </pre>
    */
  PUSH_SANCTUARY_ACTOR = 101;
  /**
    * 推送归藏秘境玩家培养信息
    * <pre>
    * 推送:{@code SanctuaryFosterResponse}
    * </pre>
    */
  PUSH_SANCTUARY_FOSTER = 102;
  /**
   * 推送归藏秘境基金信息
   * <pre>
   * 推送:{@code SanctuaryFundResponse}
   * </pre>
   */
  PUSH_SANCTUARY_FUND = 103;
  /**
   * 推送归藏秘境战斗信息
   * <pre>
   * 推送:{@code SanctuaryBattleResponse}
   * </pre>
   */
  PUSH_SANCTUARY_BATTLE = 104;
  /**
   * 推送归藏秘境基金购买次数
   * <pre>
   * 推送:{@code IntPacket}
   * </pre>
   */
  PUSH_SANCTUARY_FUND_BUY_TIMES = 105;
  /**
  * 推送归藏秘境BOSS信息
  * <pre>
  * 推送:{@code SanctuaryBossResponse}
  * </pre>
  */
  PUSH_SANCTUARY_BOSS = 106;
  /**
   * 推送归藏秘境BOSS战斗信息
   * <pre>
   * 推送:{@code SanctuaryBossBattleResponse}
   * </pre>
   */
  PUSH_SANCTUARY_BOSS_BATTLE = 107;
}

message SanctuaryRegionStateResponse {
  /** 大战区ID */
  int32 regionId = 1;
  /** 赛季ID */
  int32 seasonId = 2;
  /** 状态 1、预展期 2、战斗中 3、展示期 4、休战期 */
  int32 stateId = 3;
  /** 开始时间 */
  int64 startTime = 4;
  /** 预展示结束时间 */
  int64 previewEndTime = 5;
  /** 展示期结束时间 */
  int64 showEndTime = 6;
  /** 结束时间 */
  int64 endTime = 7;
  /** 阶段ID */
  int32 stageId = 8;
  /** 阶段结束时间 { key:stageId, value:endTime } */
  map<int32, int64> stageEndTime = 9;
  /** 初始阶段（从第几点阶段开起） */
  int32 initStageId = 10;
  /** 大战区前20战力平均值*/
  int64 averagePower = 11;
}

message SanctuaryActorResponse {
  /** 玩家当前回合关卡将要打的关卡id（初始值为1）*/
  int32 pvpId = 1;
  /** 玩家当前回合评星关卡已通关信息 key:id,value:通关星数[1,2,3]*/
  map<int32, IntListPacket> pvpPass = 2;
  /** 玩家当前塔防关卡将要打的关卡id（初始值为1）*/
  int32 pveId = 3;
  /** 玩家当前塔防关卡已通关信息 key:configId,value:通关星数[1,2,3]*/
  map<int32, IntListPacket> pvePass = 4;
  /** 已经领取过的补给配置ID*/
  repeated int32 receiveId = 5;
  /** 已经领取过的评星配置ID*/
  repeated int32 starId = 6;
  /** 已经领取过的阶段ID*/
  repeated int32 stageTaskId = 7;
  /** 挂机上次领取时间 */
  int64 lastReceiveTime = 8;
  /** 挂机已结算奖励 */
  repeated RewardObject settledRewards = 9;
  /** 最后一关通关时间*/
  int64 passTime = 10;
  /**
   * 初始的时候是空的，默认是可以使用十连战斗
   * key:pvpId,value: long值：下一次重新计数时间/int值战斗次数
   */
  map<int32, LongIntPacket> battleLimit = 11;
  /**
   * 当前赛季已领取的关卡奖励
   */
  repeated int32 storyRewardIds = 12;
}
message  SanctuaryBossResponse {
  /** 是否开启*/
  bool isOpen = 1;
  /** 当前boss的轮询id，此id唯一*/
  int32 loopId = 2;
  /** 当前boss阵容Id*/
  int32 bossLineupId = 3;
  /** 当前boss特性配置Id*/
  int32 featureId = 4;
  /** 是否结束（为true代表后面不再有boss）*/
  bool ended = 5;
  /** 玩家参与上次boss玩法获得的分数*/
  int64 historyScore = 6;
  /** 玩家已经领取的唯一的轮询id列表*/
  repeated int32 receives = 7;
  /**玩家挑战次数*/
  int32 challengeTimes = 8;
  /** 玩家参与过的boss的轮询id，此id唯一*/
  int32 historyLoopId = 9;
  /** 玩家参与过的boss配置Id*/
  int32 historyBossConfigId = 10;
  /** 玩家排名（为0代表未上榜）*/
  int32 historyRank = 11;
  /** boss结束时间*/
  int64 closeTime = 12;
}

message SanctuaryFosterResponse {
  /** 山河值*/
  int64 value = 1;
  /** 主建筑等级*/
  int32 level = 2;
  /** key:建筑类型 SanctuaryBuildingType,value:建筑信息*/
  map<int32, SanctuaryBuilding> building = 3;
}

message SanctuaryBuilding {
  /** 建筑类型 SanctuaryBuildingType*/
  int32 type = 1;
  /** 建筑阶数*/
  int32 stage = 2;
  /** key:建筑装备类型,value:建筑装备唯一id*/
  map<int32, int64> equipment = 3;
}

message SanctuaryPVPRequest {
  /** 要打的回合关卡id*/
  int32 id = 1;
  /** 回合关卡阵容key:{第几场战斗},value:{positionId,value:LineupMapValue}*/
  map<int32, SanctuaryLineup> lineup = 2;
}

message SanctuaryLineup {
  /** key:positionId,value:heroConfigId*/
  map<int32, int32> hero = 1;
  /**key:positionId,value:（客户端给我传阵容的时候是武魂唯一Id，我给客户端传阵容信息的时候是武魂配置id）*/
  map<int32, LongListPacket> soul = 2;
  /**key:positionId,value:beastConfigId*/
  map<int32, int32> beast = 3;
  /**key:positionId,value:frostmourneConfigId*/
  map<int32, int32> frostmourne = 4;
}

message SanctuaryPVERequest {
  /** 要打的塔防关卡id*/
  int32 id = 1;
  /** 通关星数 */
  IntListPacket starNum = 2;
  /** 检测数据 */
  string check = 3;
}

message SanctuaryRecordResponse {
  repeated SanctuaryRecord record = 1;
}

message SanctuaryRecord {
  /** 角色简介 */
  ActorProfile actorProfile = 1;
  /** key:第几场战斗，value:阵容*/
  map<int32, SanctuaryRecordLineup> recordLineup = 2;
  /** 排行*/
  int32 rank = 3;
}
message SanctuaryRecordLineup {
  /** key:heroConfigId, value:star*/
  map<int32, int32> hero = 2;
  /** 玩家阵容信息 */
  SanctuaryLineup lineup = 3;
  /** key:heroConfigId, value:heroSkinId*/
  map<int32, int32> heroSkin = 4;
  /** key:heroConfigId, value:heroColorLevel*/
  map<int32, int32> heroColor = 5;
}
message SanctuaryAdditionResponse {
  /** 通关人数*/
  int32 passNum = 1;
  /** 首通玩家简介 */
  ActorProfile actorProfile = 2;
  /** key:heroConfigId, value:heroSkinId*/
  map<int32, int32> heroSkin = 4;
}

message SanctuaryProgressRankResponse {
  repeated SanctuaryProgressRank rank = 1;
}

message SanctuaryProgressRank {
  /** 角色简介 */
  ActorProfile actorProfile = 1;
  /** 排名 */
  int64 rank = 2;
  /** 代表的是关卡id，需要客户端自己转换*/
  int32 value = 3;
  /** 通关时间*/
  int64 time = 4;
}

message SanctuaryFosterRankResponse {
  repeated SanctuaryFosterRank rank = 1;
}

message SanctuaryFosterRank {
  /** 角色简介 */
  ActorProfile actorProfile = 1;
  /** 排名 */
  int64 rank = 2;
  /** 山河值*/
  int64 value = 3;
}

message SanctuaryFundResponse {
  /**
    * 是否激活豪华基金
    * key:阶段 value：是否激活
    */
  map<int32, bool> isActivate = 1;
  /**
    * 普通基金领取情况
    * key:阶段 value：已领取列表
    */
  map<int32, IntListPacket> normalReceives = 2;
  /**
    * 豪华基金领取情况
    * key:阶段 value：已领取列表
    */
  map<int32, IntListPacket> LuxuryReceives = 3;
  /**
    * 基金购买人数
    */
  int32 buyTimes = 4;
  /**
  * 全民福利已领取列表
  */
  repeated int32 welfareReceives = 5;
}

message RecieveSanctuaryFundRequest {
  /**
  * 是否是领取豪华基金的奖励
  */
  bool isRecieceLuxury = 1;
  /**
  * 配置ID
  */
  int32 configId = 2;
}

enum SanctuaryBuildingType{
  SANCTUARY_BUILDING_NONE = 0;
  /** 蜀国建筑*/
  SANCTUARY_SHU = 1;
  /** 魏国建筑*/
  SANCTUARY_WEI = 2;
  /** 吴国建筑*/
  SANCTUARY_WU = 3;
  /** 蜀国建筑*/
  SANCTUARY_QUN = 4;
  /** 公共建筑*/
  SANCTUARY_PUBLIC = 5;
}

message SanctuaryEquipmentSynthesizeRequest {
  /** 归葬秘境建筑阵营*/
  SanctuaryBuildingType type = 1;
  /** 装备部位*/
  int32 position = 2;
  /** 需要合成的建筑装备配置id*/
  int32 configId = 3;
}

message SanctuaryEquipmentEquipRequest {
  /** 归葬秘境建筑阵营*/
  SanctuaryBuildingType type = 1;
  /** 装备部位*/
  int32 position = 2;
  /** 装备唯一ID*/
  int64 sanctuaryEquipmentId = 3;
}

message SanctuaryBattleResponse {
  /** 是否胜利 */
  bool isWin = 1;
  /** 战斗信息BattleResultResponse */
  repeated  BattleResultResponse battle = 2;
  /** 评星关卡获得星数[1，2，3] 分别代表达到第一颗星，第二颗星，第三颗星*/
  IntListPacket star = 3;
  /** 奖励*/
  RewardResult reward = 4;
  /** 回合关卡ID*/
  int32 pvpId = 5;
  /** 第几个通关*/
  int32 passRank = 6;
}

message SanctuaryBossBattleResponse {
  BattleResultResponse battle = 1;
  /** 排名（未上榜则为0）*/
  int64 rank = 2;
  /** 积分*/
  int64 score = 3;
}

message SanctuaryBossRankResponse {
  repeated SanctuaryBossRank rank = 1;
}
message SanctuaryBossRank {
  /** 角色简介 */
  ActorProfile actorProfile = 1;
  /** 排名 */
  int64 rank = 2;
  /** 积分*/
  int64 score = 3;
}