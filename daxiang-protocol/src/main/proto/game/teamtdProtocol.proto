syntax = "proto3";
option java_package = "cn.daxiang.protocol.game";
option optimize_for = SPEED;

import "game/typeProtocol.proto";
import "game/commonProtocol.proto";

/** 组队塔防 */
enum TeamtdCmd {
  TEAM_TD_CMD_NONE = 0;
  /**
   * 获取队伍信息
   * <pre>
   * 请求:{@code Request}
   * 响应:{@code TeamTdListResponse}
   * </pre>
   */
  TEAM_TD_INFO = 1;
  /**
   * 队伍列表
   * <pre>
   * 请求:{@code TeamTdListRequest}
   * 响应:{@code TeamTdListResponse}
   * </pre>
   */
  TEAM_TD_LIST = 2;

  /**
   * 创建队伍
   * <pre>
   * 请求:{@code TeamTdCreateRequest}
   * 响应:{@code Response}
   * 推送:{@code TeamTdListResponse}
   * </pre>
   */
  TEAM_TD_CREATE = 3;

  /**
   * 加入队伍
   * <pre>
   * 请求:{@code TeamTdIdRequest}
   * 响应:{@code Response}
   * 推送:{@code TeamTdListResponse}
   * </pre>
   */
  TEAM_TD_JOIN = 4;

  /**
   * 口令队伍
   * <pre>
   * 请求:{@code TeamTdCodeRequest}
   * 响应:{@code Response}
   * 推送:{@code TeamTdListResponse}
   * </pre>
   */
  TEAM_TD_CODE = 5;
  /**
   * 退出队伍
   * <pre>
   * 请求:{@code Request}
   * 响应:{@code Response}
   * 推送:{@code TeamTdListResponse}
   * </pre>
   */
  TEAM_TD_EXIT = 6;

  /**
   * 踢出队伍
   * <pre>
   * 请求:{@code Request}
   * 响应:{@code Response}
   * 推送:{@code TeamTdListResponse}
   * </pre>
   */
  TEAM_TD_KICK = 7;
  /**
   * 战斗准备
   * <pre>
   * 请求:{@code TeamTdReadyRequest}
   * 响应:{@code Response}
   * 推送:{@code TeamTdListResponse}
   * </pre>
   */
  TEAM_TD_READY = 8;
  /**
   * 开始战斗
   * <pre>
   * 请求:{@code Request}
   * 响应:{@code Response}
   * 推送:{@code TeamTdListResponse}
   * 推送:{@code TeamBattleStartResponse}
   * </pre>
   */
  TEAM_TD_START_BATTLE = 9;
  /**
   * 队伍取消监听
   * <pre>
   * 请求:{@code TeamTdListRequest}
   * 响应:{@code Response}
   * </pre>
   */
  TEAM_TD_CANCEL_LISTEN = 10;
  /**
   * 队伍创建口令
   * <pre>
   * 请求:{@code TeamTdTypeRequest}
   * 响应:{@code TeamCodeResponse}
   * </pre>
   */
  TEAM_TD_CREATE_CODE = 11;
  /**
   * 队伍取消口令
   * <pre>
   * 请求:{@code Request}
   * 响应:{@code Response}
   * </pre>
   */
  TEAM_TD_CANCEL_CODE = 12;
  /**
   * 运行环境分数
   * Runtime Environment Score
   * <pre>
   * 请求:{@code TeamTdScoreRequest}
   * 响应:{@code Response}
   * </pre>
   */
  TEAM_TD_RE_SCORE = 14;
  /**
   * 队伍观战
   * <pre>
   * 请求:{@code TeamTdIdRequest}
   * 响应:{@code TeamTdBattleStartResponse}
   * </pre>
   */
  TEAM_TD_SPECTATE = 15;

  /**
   * 推送队伍列表
   * <pre>
   * 推送:{@code TeamTdListResponse}
   * </pre>
   */
  PUSH_TEAM_TD_LIST = 100;
  /**
   * 推送队伍解散
   * <pre>
   * 推送:{@code TeamTdDisbandResponse}
   * </pre>
   */
  PUSH_TEAM_TD_DISBAND = 101;
  /**
   * 推送队伍战斗结果
   * <pre>
   * 推送:{@code TeamTdBattleResultResponse}
   * </pre>
   */
  PUSH_TEAM_TD_BATTLE_RESULT = 102;
  /**
   * 推送队伍战斗开始
   * <pre>
   * 推送:{@code TeamTdBattleStartResponse}
   * </pre>
   */
  PUSH_TEAM_TD_BATTLE_START = 103;
  /**
   * 推送自己的队伍
   * <pre>
   * 推送:{@code TeamTdListResponse}
   * </pre>
   */
  PUSH_TEAM_TD = 104;
}

/** 队列显示对象 */
message TeamTd {
  /**
  * 战斗服ID
  */
  int32 bsId = 1;
  /**
   * 战斗房间ID
   */
  int64 roomId = 2;
  /**
   * 链接所需Token
   */
  string token = 3;
  /**
   * 队伍ID
   */
  int64 teamId = 4;
  /**
   * 队伍类型
   * {@code TeamTdType}
   */
  TeamTdType teamTdType = 5;
  /**
   * 队伍类型值
   */
  int32 typeValue = 6;
  /**
  * 加入类型
  * {@code TeamTdJoinType}
  */
  TeamTdJoinLimitType limitType = 7;
  /**
  * 队伍加入类型值
  */
  int64 limitValue = 8;
  /**
  * 创建时间
  */
  int64 createTime = 9;
  /**
  * 是否可以观战
  */
  bool spectate = 10;
  /**
   * 队伍状态
   */
  TeamTdState state = 11;
  /**
  * 队伍成员
  * {key:actorId,value:TeamTdMember}
  */
  map<int64, TeamTdMember> members = 12;
  /**
  * 战斗服IP或域名
  */
  string battleHost = 13;
  /**
  * 战斗服端口
  */
  int32 battlePort = 14;
}

/** 队伍成员信息 */
message TeamTdMember {
  /**
  * 角色属性Map
  * key:{@code ActorKey},value:Value
  */
  ActorProfile actorProfile = 1;
  /**
  * 成员类型
  * {@code TeamMemberType}
  */
  TeamTdMemberType teamTdMemberType = 2;
  /**
  * 是否准备
  */
  bool ready = 3;
  /**
  * 运行环境分数
  */
  int32 reScore = 4;
}

/** 获取队伍请求 */
message TeamTdListRequest {
  /**
  * 队伍类型
  * {@code TeamTdType}
  */
  TeamTdType teamTdType = 1;
  /**
  * 队伍类型值
  */
  int32 typeValue = 2;
}

/** 获取队伍列表 */
message TeamTdListResponse {
  /**
  * 队伍类型
  * {@code TeamTdType}
  */
  TeamTdType teamTdType = 1;
  /**
  * 队伍类型值
  */
  int32 typeValue = 2;
  /**
  * 队伍列表
  */
  repeated TeamTd teamTds = 3;
}

/** 创建队伍请求 */
message TeamTdCreateRequest {
  /**
  * 队伍类型
  * {@code TeamType}
  */
  TeamTdType teamTdType = 1;
  /**
  * 队伍类型值
  */
  int32 typeValue = 2;
  /**
  * 加入类型
  * {@code TeamJoinLimitType}
  */
  TeamTdJoinLimitType limitType = 3;
  /**
  * 队伍类型值
  */
  int64 limitValue = 4;
  /**
  * 是否可以观战
  */
  bool spectate = 5;
}

/** 队伍ID请求 */
message TeamTdIdRequest {
  /** 队伍ID */
  int64 id = 1;
}

/** 加入口令队伍请求 */
message TeamTdCodeRequest {
  /**
  * 队伍类型
  * {@code TeamType}
  */
  TeamTdType teamTdType = 1;
  /**
  * 口令
  */
  int32 code = 2;
}

/** 队伍类型请求 */
message TeamTdTypeRequest {
  /**
  * 队伍类型
  */
  TeamTdType teamTdType = 1;
}

/** 队伍准备请求 */
message TeamTdReadyRequest {
  /**
  * 是否准备
  */
  bool ready = 1;
}

/** 运行环境分数请求 */
message TeamTdScoreRequest {
  /** 分数 */
  int32 score = 2;
}

/** 队伍解散响应 */
message TeamTdDisbandResponse {
  /**
  * 队伍类型
  * {@code TeamTdType}
  */
  TeamTdType teamTdType = 1;
  /**
  * 队伍类型值
  */
  int32 typeValue = 2;
  /**
  * 队伍ID
  */
  int64 id = 3;
}

/** 推送队伍战斗结果 */
message TeamTdBattleResultResponse {
  /**
  * 队伍类型
  * {@code TeamTdType}
  */
  TeamTdType teamTdType = 1;
  /**
  * 队伍类型值
  */
  int32 typeValue = 2;
  /**
  * 额外参数 key：TeamTdParameterKey value：string
  */
  map<int32, string> parameter = 3;
  /**
  * 奖励结果
  */
  RewardResult rewardResult = 4;
  /**
   * 是否作弊
   */
  bool isCheat = 5;
}

/** 队伍开始响应 */
message TeamTdBattleStartResponse {
  /**
  * 战斗服ID
  */
  int32 bsId = 1;
  /**
  * 队伍类型
  * {@code TeamTdType}
  */
  TeamTdType teamTdType = 2;
  /**
  * 战斗房间ID
  */
  int64 roomId = 3;
  /**
  * 链接所需Token
  */
  string token = 4;
  /**
  * 战斗服IP或域名
  */
  string battleHost = 5;
  /**
  * 战斗服端口
  */
  int32 battlePort = 6;
}

/** 队伍口令响应 */
message TeamCodeResponse {
  /** 口令 */
  int32 code = 1;
}