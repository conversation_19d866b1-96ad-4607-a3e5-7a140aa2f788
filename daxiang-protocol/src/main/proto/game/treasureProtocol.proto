syntax = "proto3";
option java_package = "cn.daxiang.protocol.game";
option optimize_for = SPEED;

import "game/commonProtocol.proto";
import "game/typeProtocol.proto";

/** 用户 */
enum TreasureCmd {
  TREASURE_CMD_NONE = 0;
  /**
* 获取宝物信息
* 请求:{@code Request}
* 响应:{@code TreasureInfoResponse}
*/
  GET_TREASURE_INFO = 1;
  /**
   * 宝物增加经验(强化)
   * 请求:{@code TreasureAddExpRequest}
   * 响应:{@code Response}
   * 推送:{@code TreasureInfoResponse}
   */
  TREASURE_ADD_EXP = 2;
  /**
   * 宝物精炼
   * 请求:{@code TreasureDevourRequest}
   * 响应:{@code Response}
   * 推送:{@code TreasureInfoResponse}
   * 推送:{@code TreasureDeleteResponse}
   */
  TREASURE_REFINE = 3;
  /**
   * 宝物雕纹
   * 请求:{@code TreasureGlyphsRequest}
   * 响应:{@code Response}
   * 推送:{@code TreasureInfoResponse}
   */
  TREASURE_GLYPHS = 4;
  /**
   * 宝物合成
   * 请求:{@code TreasureSynthesizeRequest}
   * 响应:{@code RewardResultResponse}
   * 推送:{@code TreasureInfoResponse}
   */
  TREASURE_SYNTHESIZE = 5;
  /**
   * 宝物一键增加经验(装备大师一键)
   * 请求:{@code TreasureBatchAddExpRequest}
   * 响应:{@code Response}
   * 推送:{@code TreasureInfoResponse}
   */
  TREASURE_BATCH_ADD_EXP = 6;
  /**
   * 宝物觉醒
   * 请求:{@code LongPacket} 宝物配置ID
   * 响应:{@code Response}
   * 推送:{@code TreasureInfoResponse}
   */
  TREASURE_AWAKEN = 7;
  /**
   * 获取宝物抢夺信息
   * 请求:{@code Request}
   * 响应:{@code TreasureSnatchResponse}
   */
  TREASURE_SNATCH_INFO = 20;
  /**
   * 刷新宝物抢夺目标
   * 请求:{@code Request}
   * 响应:{@code Response}
   * 推送:{@code TreasureSnatchResponse}
   */
  TREASURE_SNATCH_REFRESH = 21;
  /**
   * 宝物抢夺
   * 请求:{@code TreasureSnatchRequest}
   * 响应:{@code Response}
   * 推送:{@code TreasureSnatchResponse}
   */
  TREASURE_SNATCH = 22;
  /**
   * 宝物快速抢夺
   * 请求:{@code TreasureQuickSnatchRequest}
   * 响应:{@code RewardResultResponse}
   */
  TREASURE_QUICK_SNATCH = 23;

  /**
   * 购买隐身符
   * 请求:{@code TreasureInvisibleRequest}
   * 响应:{@code Response}
   */
  TREASURE_INVISIBLE = 24;
  /**
   * 宝物抢夺记录
   * 请求:{@code Request}
   * 响应:{@code TreasureSnatchRecordResponse}
   */
  TREASURE_SNATCH_RECORD = 25;
  /**
   * 宝物抢夺复仇
   * 请求:{@code TreasureRevengeRequest}
   * 响应:{@code Response}
   * 推送:{@code RewardResultResponse}
   */
  TREASURE_REVENGE = 26;
  /**
   * 宝物一键合成
   * 请求:{@code TreasureQuickSynthesizeRequest}
   * 响应:{@code TreasureQuickSynthesizeResponse}
   */
  TREASURE_QUICK_SYNTHESIZE = 27;
  /**
   * 宝物一键觉醒
   * 请求:{@code LongIntPacket} -k:宝物唯一ID -v:等级
   * 响应:{@code Response}
   * 推送:{@code TreasureInfoResponse}
   */
  TREASURE_QUICK_AWAKEN = 28;
  /**
   * 推送宝物信息
   * 推送:{@code TreasureInfoResponse}
   */
  PUSH_TREASURE_INFO = 100;
  /**
   * 推送宝物删除
   * 推送:{@code TreasureDeleteResponse}
   */
  PUSH_TREASURE_DELETE = 101;
  /**
   * 推送宝物抢夺目标
   * 推送:{@code TreasureSnatchResponse}
   */
  PUSH_TREASURE_SNATCH_INFO = 102;
  /**
   * 推送宝物抢夺记录
   * 推送:{@code TreasureSnatchRecordResponse}
   */
  PUSH_TREASURE_SNATCH_RECORD = 103;
}


/** 宝物抢夺记录显示对象 */
message TreasureSnatchRecord {
  /** 角色属性列表 */
  ActorProfile actorProfile = 1;
  /** 碎片ID*/
  int32 fragmentId = 2;
  /** 状态*/
  TreasureSnatchRecordState state = 3;
  /** 时间*/
  int64 time = 4;
}
/** 宝物信息响应 */
message TreasureInfoResponse {
  /** 装备列表 */
  repeated Treasure treasure = 1;
}
/** 宝物增加经验请求 */
message TreasureAddExpRequest {
  /**宝物ID*/
  int64 id = 1;
  /**提升等级*/
  int32 level = 2;
}
/** 宝物删除响应 */
message TreasureDeleteResponse {
  /** 宝物ID列表 */
  repeated int64 ids = 1;
}
/** 宝物精炼请求 */
message TreasureDevourRequest {
  /**宝物ID*/
  int64 id = 1;
}
/** 宝物雕纹请求 */
message TreasureGlyphsRequest {
  /** 宝物雕纹升级次数雕纹 key:treasureId,value:times*/
  map<int64, int32> times = 1;
}
/** 宝物合成请求 */
message TreasureSynthesizeRequest {
  /**目标宝物配置ID*/
  int32 id = 1;
  /**合成数量*/
  int32 count = 2;
}
/** 宝物一键增加经验请求 */
message TreasureBatchAddExpRequest {
  /** 宝物强化次数Map key:treasureId,value:goods */
  map<int64, GoodsMap> goods = 1;
}
/** 物品map */
message GoodsMap {
  /** key:道具配置Id,value:道具数量*/
  map<int32, int32> goods = 1;
}
/** 宝物抢夺目标响应 */
message TreasureSnatchResponse {
  /** 角色属性列表 */
  repeated ActorProfile actorProfile = 1;
  /** 隐身结束时间 */
  int64 invisibleEndTime = 2;
}
/** 宝物抢夺请求 */
message TreasureSnatchRequest {
  /**碎片ID*/
  int32 fragmentId = 1;
  /**目标角色ID*/
  int64 targetId = 2;
}
/** 宝物一键抢夺请求 */
message TreasureQuickSnatchRequest {
  /**碎片ID*/
  int32 fragmentId = 1;
}
/** 夺宝购买隐身符请求 */
message TreasureInvisibleRequest {
  /**碎片ID*/
  int32 hour = 1;
}
/** 宝物抢夺记录响应 */
message TreasureSnatchRecordResponse {
  /** 宝物抢夺记录列表 */
  repeated TreasureSnatchRecord records = 1;
}
/** 宝物抢夺请求 */
message TreasureRevengeRequest {
  /**碎片ID*/
  int32 fragmentId = 1;
  /**目标角色ID*/
  int64 targetId = 2;
  /**时间*/
  int64 time = 3;
}
/** 宝物一键合成请求 */
message TreasureQuickSynthesizeRequest {
  /**合成宝物Map key:configId,value:count*/
  map<int32, int32> synthesizes = 1;
}
/** 宝物一键合成响应 */
message TreasureQuickSynthesizeResponse {
  /** 奖励结果 */
  repeated RewardResult rewardResult = 1;
}

