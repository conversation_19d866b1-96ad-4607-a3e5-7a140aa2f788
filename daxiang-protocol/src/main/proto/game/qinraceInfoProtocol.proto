syntax = "proto3";
option java_package = "cn.daxiang.protocol.game";
option optimize_for = SPEED;

import "game/commonProtocol.proto";

/** 无尽棋局-无限火力玩法 */
message Qinrace1RequestValue {
  /** 伤害值*/
  int64 value = 1;
}
message Qinrace1Record {
  /** 最大伤害值*/
  int64 maxValue = 1;
}
/** 无尽棋局-生存挑战玩法 */
message Qinrace2RequestValue {
  /** 生存时间*/
  int64 value = 1;
}
message Qinrace2Record {
  /** 最大生存时间*/
  int64 maxTime = 1;
}
/** 无尽棋局-大富翁玩法 */
message Qinrace3RequestValue {
  /** 难度*/
  int32 level = 1;
}
message Qinrace3Record {
  /** 最大剩余水晶数量*/
  int32 maxNum = 1;
}

/** 无尽棋局-火力全开玩法 */
message Qinrace4RequestValue {
  /**key:bossId,value:难度等级*/
  map<int32, int32> level = 1;
  /** 人口*/
  int32 population = 2;
}
message Qinrace4Record {
  /** 最大人口*/
  int32 maxPopulation = 1;
}