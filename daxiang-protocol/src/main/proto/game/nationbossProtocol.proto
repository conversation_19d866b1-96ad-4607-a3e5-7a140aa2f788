syntax = "proto3";
option java_package = "cn.daxiang.protocol.game";
option optimize_for = SPEED;
import "game/commonProtocol.proto";
import "game/battleProtocol.proto";

enum NationbossCmd {
  NATION_BOSS_CMD_NONE = 0;
  /**
   * 获取军团Boss阶段信息
   * 请求:{@code Request}
   * 响应:{@code NationBossStateResponse}
  */
  GET_NATION_BOSS_STATE = 1;

  /**
   * 获取玩家挑战信息
   * 请求:{@code Request}
   * 响应:{@code NationBossActorResponse}
  */
  GET_NATION_BOSS_ACTOR_INFO = 2;

  /**
   * 获取挑战目标对象
   * 请求:{@code Request}
   * 响应:{@code Response}
   * 推送:{@code TargetListResponse}
  */
  GET_CHALLENGE_TARGET_LIST = 3;

  /**
   * 挑战
   * 请求:{@code ChallengeRequest}
   * 响应:{@code Response}
   * 推送:{@code NationBossActorResponse}
   * 推送:{@code BattleResponse}
   * 推送:{@code ChallengeResponse}
  */
  CHALLENGE = 4;

  /**
   * 获取玩家排行榜
   * 请求:{@code Request}
   * 响应:{@code Response}
   * 推送:{@code ActorRankResponse}
  */
  GET_ACTOR_RANK = 5;

  /**
   * 获取军团排行榜
   * 请求:{@code Request}
   * 响应:{@code Response}
   * 推送:{@code NationRankResponse}
  */
  GET_NATION_RANK = 6;

  /**
   * 获取挑战记录
   * 请求:{@code Request}
   * 响应:{@code Response}
   * 推送:{@code ChallengeRecordsResponse}
  */
  GET_CHALLENGE_RECORD = 7;

  /**
   * 军团boss鼓舞
   * 请求:{@code Request}
   * 响应:{@code RewardResultResponse}
   * 推送:{@code NationBossActorResponse}
   */
  INSPIRE = 8;

  /**
   * 信息推送
   * 推送:{@code NationBossStateResponse}
  */
  PUSH_NATION_BOSS_STATE = 100;
  /**
   * 玩家信息推送
   * 推送:{@code NationBossActorResponse}
  */
  PUSH_NATION_BOSS_ACTOR_INFO = 101;
  /**
   * 挑战列表推送
   * 推送:{@code TargetListResponse}
   */
  PUSH_CHALLENGE_TARGET_LIST = 102;
  /**
   * 玩家排行榜推送
   * 推送:{@code ActorRankResponse}
   */
  PUSH_ACTOR_RANK = 103;
  /**
   * 军团排行榜推送
   * 推送:{@code NationRankResponse}
   */
  PUSH_NATION_RANK = 104;
  /**
   * 挑战记录推送
   * 推送:{@code ChallengeRecordsResponse}
   */
  PUSH_CHALLENGE_RECORD = 105;
  /**
   * 奖励展示推送
   * 推送:{@code RewardObjectList}
   */
  PUSH_TANG_HELL_REWARD_SHOW = 106;
  /**
   * 挑战结果推送
   * 推送:{@code ChallengeResponse}
   */
  PUSH_CHALLENGE_RESULT = 107;
}
/** Boss阶段状态 */
enum BossStateType{
  BOSS_STATE_TYPE_NONE = 0;
  /** 预热展示阶段 */
  PREPARE = 1;
  /** 战斗阶段 */
  BEGINS = 2;
  /** 休战阶段 */
  SLEEP = 3;
}
/** 挑战类型 */
enum ChallengeType {
  NATION_BOSS_BATTLE_TYPE_NONE = 0;
  /** 挑战BOSS */
  BOSS = 1;
  /** 争夺 */
  AGAINST = 2;
  /** 被争夺 */
  BEATEN = 3;
}
/** 军团boss阶段状态信息响应体 */
message NationBossStateResponse{
  /** 阶段状态 */
  BossStateType state = 1;
  /** 开始时间 */
  int64 openTime = 2;
  /** 结束时间 */
  int64 closeTime = 3;
}
/** 军团boss玩家信息 */
message NationBossActorResponse{
  /** 当前积分 */
  int64 score = 1;
  /** 上一次挑战boss时间 */
  int64 lastChallengeBossTime = 2;
  /** 上一次争夺时间 */
  int64 lastChallengeActorTime = 3;
  /** 上一次被争夺时间 */
  int64 lastBeatenTime = 4;
  /** 排名 */
  int64 rank = 5;
  /** 军团排名 */
  int64 nationRank = 6;
  /** 军团积分 */
  int64 nationScore = 7;
  /** 鼓舞次数 */
  int32 inspireTime = 8;
  /** 鼓舞列表 */
  repeated string inspireActorList = 9;
  /** 挑战次数 */
  int32 challengeTimes = 10;
  /** 抢夺次数 */
  int32 againstTimes = 11;
}
/** 挑战目标、复仇列表响应体 */
message TargetListResponse{
  /** 复仇列表 */
  repeated ActorRank revengeList = 1;
  /** 目标列表 */
  repeated ActorRank targetList = 2;
}

/** 挑战请求体 */
message ChallengeRequest{
  /** 挑战boss -0 挑战玩家 -玩家Id*/
  int64 targetId = 1;
  /** 挑战类型 */
  ChallengeType type = 2;
}

/** 挑战响应 */
message ChallengeResponse {
  /** 挑战类型类型 */
  ChallengeType type = 1;
  /** 战报结果*/
  BattleResultResponse battle = 2;
  /** 积分 */
  int32 score = 3;
  /** 被攻击方死亡人数 */
  int32 killCount = 4;
  /** 公共积分 */
  int32 publicScore = 5;
}

/** 玩家排行榜响应体 */
message ActorRankResponse {
  repeated ActorRank ranks = 1;
}
/** 军团排行榜响应体 */
message NationRankResponse{
  repeated NationRank ranks = 1;
}
/** 挑战记录实体 */
message ChallengeRecordEntity{
  /** 挑战者 */
  ActorProfile attend = 1;
  /** 被挑战者 */
  ActorProfile defend = 2;
  /** 积分奖励 */
  int64 score = 3;
  /** 挑战类型 */
  ChallengeType type = 4;
  /** 序号 */
  int64 index = 5;
  /** 伤害 */
  int64 damageValue = 6;
  /** 获得的公共积分 */
  int64 publicScore = 7;
}
/** 挑战记录响应体 */
message ChallengeRecordsResponse{
  /** 挑战记录列表 */
  repeated ChallengeRecordEntity records = 1;
}

/** 玩家排行榜展示信息 */
message ActorRank{
  /** 角色属性 */
  ActorProfile actorProfile = 1;
  /** 排名 */
  int64 rank = 2;
  /** 值(玩家个人积分) */
  int64 value = 3;
  /** 上次被争夺时间 */
  int64 lastBeatenTime = 4;
  /** 玩家公共积分 */
  int64 publicScore = 5;
}
/** 军团排行榜展示信息 */
message NationRank{
  /** 军团Id */
  int64 nationId = 1;
  /** 军团名称 */
  string nationName = 2;
  /** 参与人数 */
  int32 memberCount = 3;
  /** 军团积分 */
  int64 score = 4;
  /** 排名 */
  int64 rank = 5;
}