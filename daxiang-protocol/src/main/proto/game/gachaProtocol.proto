syntax = "proto3";
option java_package = "cn.daxiang.protocol.game";
option optimize_for = SPEED;

import "game/commonProtocol.proto";
/** 任务 */
enum GachaCmd {
  TASK_CMD_NONE = 0;
  /**
   * 获取招募信息
   * <pre>
   * 请求:{@code Request}
   * 响应:{@code GachaInfoResponse}
   * </pre>
   */
  GET_GACHA_INFO = 1;

  /**
   * 获取活跃度信息
   * <pre>
   * 请求:{@code GachaRequest}
   * 响应:{@code GachaResultResponse}
   * </pre>
   */
  GACHA = 2;

  /**
   * 获取招募信息
   * <pre>
   * 请求:{@code Request}
   * 响应:{@code GachaHeroResponse}
   * </pre>
   */
  GET_GACHA_HERO_INFO = 3;

  /**
   * 选择心愿武将
   * <pre>
   * 请求:{@code IntListPacket} heroConfigId
   * 响应:{@code Response}
   * 推送:{@code GachaHeroResponse}
   * </pre>
   */
  WISH_CHOICE = 4;

  /**
   * 选择祝福武将
   * <pre>
   * 请求:{@code IntPacket}
   * 响应:{@code Response}
   * 推送:{@code GachaHeroResponse}
   * </pre>
   */
  BLESS_CHOICE = 5;

  /**
   * 领取祝福武将
   * <pre>
   * 请求:{@code Request}
   * 响应:{@code GachaResultResponse}
   * 推送:{@code GachaHeroResponse}
   * </pre>
   */
  BLESS_RECEIVE = 6;

  /**
   * 推送招募信息
   * <pre>
   * 推送:{@code GachaInfoResponse}
   * </pre>
   */
  PUSH_GACHA_INFO = 100;

  /**
   * 推送点将台信息
   * <pre>
   * 推送:{@code GachaHeroResponse}
   * </pre>
   */
  PUSH_GACHA_HERO_INFO = 101;


}

/** 抽奖请求 */
message GachaRequest {
  /** 抽奖ID */
  int32 id = 1;
  /** 次数 */
  int32 times = 2;
  /** 是否使用物品 */
  bool useGoods = 3;
}

/** 抽奖响应 */
message GachaResultResponse {
  map<int32, RewardObjectList> rewardObjectMap = 1;
}

/** 抽奖推送 */
message GachaInfoResponse {
  repeated Gacha gachas = 1;
}

/** 抽奖信息 */
message Gacha {
  /** ID */
  int32 id = 1;
  /** 次数(循环用于固定奖励) */
  int32 times = 2;
  /** 每天抽奖次数 */
  int32 dayTimes = 3;
  /** 总次数 */
  int32 totalTimes = 4;
  /** 下一次免费时间(等于0不免费) */
  int64 nextFreeTime = 5;
}

/** 点将台信息响应 */
message GachaHeroResponse {
  /** 选中的心愿武将 */
  repeated int32 wishHeroId = 1;
  /** 已中心愿武将 */
  repeated int32 wishRecord = 2;
  /** 下次重置心愿武将时间 */
  int64 nextResetWishTime = 3;
  /** 祝福英雄ID */
  int32 blessHeroId = 4;
  /** 祝福值 */
  int32 blessValue = 5;
  /** 红将祝福领取次数 */
  int32 blessTimes = 6;
  /** 下次重置心愿武将时间 */
  int64 nextResetBlessTime = 7;
  /** 金将祝福领取次数 */
  int32 epicBlessTimes = 8;
}