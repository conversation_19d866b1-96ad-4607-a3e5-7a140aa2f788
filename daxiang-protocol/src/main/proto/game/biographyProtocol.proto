syntax = "proto3";
option java_package = "cn.daxiang.protocol.game";
option optimize_for = SPEED;

import "game/commonProtocol.proto";

/** 英雄传记 */
enum BiographyCmd {
  BIOGRAPHY_CMD_NONE = 0;

  /**
  * 获取英雄传记信息
  * 请求:{@code Request}
  * 响应:{@code HeroBiographyResponse}
  */
  GET_BIOGRAPHY_INFO = 1;
  /**
  * 点击英雄传记
  * 请求:{@code IntPacket} 传id
  * 响应:{@code Response}
  * 推送:{@code HeroBiographyResponse}
  */
  CLICK_BIOGRAPHY = 2;
  /**
  * 领取英雄传记奖励
  * 请求:{@code IntPacket} 传id
  * 响应:{@code RewardResultResponse}
  * 推送:{@code HeroBiographyResponse}
  */
  RECIEVE_BIOGRAPHY = 3;

  /**
  * 推送英雄传记
  * 推送:{@code HeroBiographyResponse}
  */
  PUSH_BIOGRAPHY = 100;
}

message HeroBiographyResponse {
  /**已经点击过的id集合*/
  repeated int32 clickId = 1;
  /** 已经领取的id集合*/
  repeated int32 recieveId = 2;
}
 