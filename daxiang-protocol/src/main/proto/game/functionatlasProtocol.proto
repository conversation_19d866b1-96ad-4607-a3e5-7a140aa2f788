syntax = "proto3";
option java_package = "cn.daxiang.protocol.game";
option optimize_for = SPEED;

/** 功能手册 */
enum FunctionatlasCmd {
  FUNCTION_ATLAS_CMD_NONE = 0;
  /**
   * 获取功能手册状态信息
   * <pre>
   * 请求:{@code Request}
   * 响应:{@code FunctionAtlasInfoResponse}
   * </pre>
   */
  GET_FUNCTION_ATLAS_INFO = 1;
  /**
   * 领取功能手册奖励
   * <pre>
   * 请求:{@code IntPacket} functionId
   * 响应:{@code RewardResultResponse}
   * 推送:{@code FunctionAtlasInfoResponse}
   * </pre>
   */
  RECEIVE_FUNCTION_ATLAS = 2;

  /**
   * 推送功能手册状态信息
   * <pre>
   * 推送:{@code FunctionAtlasInfoResponse}
   * </pre>
   */
  PUSH_FUNCTION_ATLAS_INFO = 100;
}
message FunctionAtlasInfoResponse {
  repeated FunctionAtlasInfo infos = 1;
}

message FunctionAtlasInfo {
  int32 functionId = 1;
  FunctionAtlasStateType state = 2;
}

enum FunctionAtlasStateType {
  FUNCTION_ATLAS_TYPE_NONE = 0;
  /** 未解锁*/
  FUNCTION_ATLAS_LOCK = 1;
  /** 未领取*/
  FUNCTION_ATLAS_NOT_RECEIVE = 2;
  /** 已领取*/
  FUNCTION_ATLAS_HAD_RECEIVED = 3;
}