syntax = "proto3";
option java_package = "cn.daxiang.protocol.game";
option optimize_for = SPEED;

/** 系统 */
enum SystemCmd {
  SYSTEM_CMD_NONE = 0;
  /**
  * 获取Settings信息
  * 请求:{@code Request}
  * 响应:{@code SettingResponse}
  */
  GET_SETTING = 1;

  /**
  * 获取服务器开启天数
  * 请求:{@code Response}
  * 响应:{@code ServerStartDaysResponse}
  */
  GET_SERVER_START_DAYS = 2;

  /**
  * 获取服务器最大等级
  * 请求:{@code Request}
  * 响应:{@code ServerMaxLevelResponse}
  */
  GET_SERVER_MAX_LEVEL = 3;

  /**
  * 获取战区名称
  * 请求:{@code Request}
  * 响应:{@code ServerZoneNameResponse}
  */
  GET_SERVER_ZONE_NAME = 4;

  /**
  * 获取服务器世界等级
  * 请求:{@code Request}
  * 响应:{@code ServerWorldLevelResponse}
  */
  GET_SERVER_WORLD_LEVEL = 5;
  /**
  * 获取战区开启天数
  * 请求:{@code Request}
  * 响应:{@code ZoneOpenDayResponse}
  */
  GET_ZONE_OPEN_DAY = 6;

  /**
  * 推送服务器开启天数
  * 推送:{@code ServerStartDaysResponse}
  */
  PUSH_SERVER_START_DAYS = 100;
  /**
  * 推送Settings信息
  * 推送:{@code SettingResponse}
  */
  PUSH_SETTING = 101;
  /**
  * 推送服务器世界等级
  * 推送:{@code ServerWorldLevelResponse}
  */
  PUSH_SERVER_WORLD_LEVEL = 102;
  /**
  * 推送战区开启天数
  * 推送:{@code ZoneOpenDayResponse}
  */
  PUSH_ZONE_OPEN_DAY = 103;

}

/** 设置响应 */
message SettingResponse {
  /** 开服时间 */
  int64 serverStartTime = 1;
  /** 合服时间 */
  int64 mergeServerTime = 998;
}

/** 服务器最大等级响应 */
message ServerMaxLevelResponse {
  int32 level = 1;
}

/** 服务器世界等级响应 */
message ServerWorldLevelResponse {
  int32 level = 1;
}

/** 服务器开启天数响应 */
message ServerStartDaysResponse {
  int32 days = 1;
}
/** 服务器属性Key*/
enum ServerAttributeKey{
  SERVER_ATTRIBUTE_NONE = 0;
  /** 1.每个等级活跃人数(Map<Level,activeCount>)*/
  SERVER_ATTRIBUTE_ACTIVE_COUNT = 1;
  /** 2.第10名战斗力*/
  SERVER_ATTRIBUTE_RANK_10_POWER = 2;
  /** 3.前10名平均战斗力*/
  SERVER_ATTRIBUTE_AVERAGE_POWER = 3;
}

/** 获取战区开启天数响应 */
message ZoneOpenDayResponse {
  int32 day = 1;
}
