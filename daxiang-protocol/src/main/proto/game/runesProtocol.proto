syntax = "proto3";
option java_package = "cn.daxiang.protocol.game";
option optimize_for = SPEED;

import "game/commonProtocol.proto";
/** 兵符 */
enum RunesCmd {
  RUNES_CMD_NONE = 0;
  /**
   * 获取兵符信息
   * <pre>
   * 请求:{@code Request}
   * 响应:{@code RunesInfoResponse}
   * </pre>
   */
  GET_RUNES = 1;
  /**
   * 获取灵石信息
   * <pre>
   * 请求:{@code Request}
   * 响应:{@code GemstoneInfoResponse}
   * </pre>
   */
  GET_GEMSTONE = 2;
  /**
   * 获取已穿戴兵符信息
   * <pre>
   * 请求:{@code Request}
   * 响应:{@code RunesLineupInfoResponse}
   * </pre>
   */
  GET_RUNES_WEAR_INFO = 3;
  /**
   * 更换（穿戴）兵符
   * <pre>
   * 请求:{@code ChangeRunesRequest}
   * 响应:{@code Response}
   * 推送:{@code RunesLineupInfoResponse}
   * </pre>
   */
  CHANGE_RUNES = 4;
  /**
   * 鉴定兵符
   * <pre>
   * 请求:{@code RunesAppraiseRequest}
   * 响应:{@code RunesInfoResponse}
   * 推送:{@code RunesInfoResponse}
   * </pre>
   */
  APPRAISE_RUNES = 5;
  /**
   * 置换兵符
   * <pre>
   * 请求:{@code DisplaceRunesRequest}
   * 响应:{@code Response}
   * 推送:{@code RunesInfoResponse}
   * 推送:{@code RunesLineupInfoResponse}
   * </pre>
   */
  DISPLACE_RUNES = 6;
  /**
   * 培养阵位兵符
   * <pre>
   * 请求:{@code KeyValuePacket}key:传阵营Id，value：传次数
   * 响应:{@code RunesLineupFosterResponse}
   * 推送:{@code RunesLineupInfoResponse}
   * </pre>
   */
  FOSTER_RUNES = 7;
  /**
   * 升级阵位兵符
   * <pre>
   * 请求:{@code IntPacket}key:传阵营Id
   * 响应:{@code IntPacket}返回当前阵位升级后兵符等级
   * 推送:{@code RunesLineupInfoResponse}
   * </pre>
   */
  LEVEL_UP_RUNES = 8;
  /**
   * 分解兵符
   * <pre>
   * 请求:{@code RunesDecomposeRequest}
   * 响应:{@code RewardResultResponse}
   * 推送:{@code LongListPacket}推送删除的runesId
   * </pre>
   */
  DECOMPOSE_RUNES = 9;
  /**
   * 推送兵符信息
   * <pre>
   * 推送:{@code RunesInfoResponse}
   * </pre>
   */
  PUSH_RUNES_INFO = 100;
  /**
   * 推送灵石信息
   * <pre>
   * 推送:{@code GemstoneInfoResponse}
   * </pre>
   */
  PUSH_GEMSTONE_INFO = 101;
  /**
   * 推送已穿戴兵符信息
   * <pre>
   * 推送:{@code RunesLineupInfoResponse}
   * </pre>
   */
  PUSH_RUNES_LINEUP_INFO = 102;
  /**
   * 推送删除的runesId集合
   * <pre>
   * 推送:{@code LongListPacket}
   * </pre>
   */
  PUSH_DELETE_RUNES_INFO = 103;

}

message RunesInfoResponse {
  /** 兵符列表 */
  repeated Runes runes = 1;
}

message GemstoneInfoResponse {
  /** 灵石列表*/
  repeated Gemstone  gemstone = 1;
}

message RunesLineupInfoResponse {
  map<int32, RunesLineupEntity> runesLineup = 1;
}

message RunesLineupEntity {
  /** 培养等级*/
  int32 fosterLevel = 1;
  /** 当前培养等级已获得属性*/
  map<int32, int64> fosterAttribute = 2;
  /** 已装备符文*/
  int64 rune = 3;
  /** 已装备灵石Map*/
  map<int32, int64> gemstone = 4;
}

message DisplaceRunesRequest {
  /** 阵上的兵符的runesId*/
  int64 lineupRunesId = 1;
  /** 阵上的兵符被置换的附加词条*/
  RunesExtra lineupExtra = 2;
  /** 置换兵符的runesId*/
  int64 runesId = 3;
  /** 置换兵符的附加词条*/
  RunesExtra extra = 4;
}

message ChangeRunesRequest {
  /**阵营Id*/
  int32 camp = 1;
  /** 兵符唯一ID*/
  int64 runesId = 2;
}
message RunesAppraiseRequest {
  /** 兵符唯一ID */
  int64 runesId = 1;
  /** 数量*/
  int64 num = 2;
}
message RunesLineupFosterResponse {
  /** 阵位培养已获得属性 */
  map<int32, int64> fosterAttribute = 1;
  /** 培养次数 */
  int32 fosterTimes = 2;
  /**培养等级*/
  int32 fosterLevel = 3;
  /**培养消耗状态类型*/
  RunesLineuFosterStateType state = 4;
}
enum RunesLineuFosterStateType {
  RUNES_LINEUP_FOSTER_STATE_TYPE_NONE = 0;
  /**正常消耗*/
  RUNES_LINEUP_FOSTER_NORMAL = 1;
  /**培养丹不足*/
  RUNES_LINEUP_FOSTER_NOT_ENOUGH = 2;
  /**培养丹溢出*/
  RUNES_LINEUP_FOSTER_OVERFLOWED = 3;
}

message RunesDecomposeRequest{
  /** key:兵符唯一ID ,num:数量*/
  map<int64, int64> decompose = 1;
}