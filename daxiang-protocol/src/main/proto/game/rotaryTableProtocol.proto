syntax = "proto3";
option java_package = "cn.daxiang.protocol.game";
option optimize_for = SPEED;
import "game/commonProtocol.proto";
/** 大转盘 */
enum RotaryTableCmd {
  ROTARY_TABLE_CMD_NONE = 0;
  /**
   * 获取大转盘信息
   * 请求:{@code RotaryTableTypeRequest}
   * 响应:{@code RotaryTableInfoResponse}
   */
  ROTARY_TABLE_INFO = 1;
  /**
   * 转动转盘
   * 请求:{@code RotaryTableTurnRequest}
   * 响应:{@code RotaryTableTurnResponse}
   * 推送:{@code RotaryTableInfoResponse}
   */
  ROTARY_TABLE_TURN = 2;
  /**
   * 开启新转盘
   * 请求:{@code RotaryTableTypeRequest}
   * 响应:{@code Response}
   * 推送:{@code RotaryTableInfoResponse}
   */
  ROTARY_TABLE_NEW_ROUND = 3;
  /**
   * 推送大转盘个人信息
   * 推送:{@code RotaryTableInfoResponse}
   */
  PUSH_ROTARY_TABLE = 100;
}
/** 大转盘奖励项显示对象 */
message RotaryTableItemVO{
  /** 序号：1=大奖 */
  int32 index = 1;
  /** 奖励可抽取最大数量 */
  int32 count = 2;
  /** 奖励列表 */
  RewardObjectList rewards = 3;
}

/** 大转盘显示对象 */
message RotaryTableVO{
  /** 大转盘类型 */
  RotaryTableType type = 1;
  /** 奖励(已抽取)记录->{index,count} */
  map<int32, int32> rewardRecords = 2;
  /** 大转盘奖励项显示对象 */
  repeated RotaryTableItemVO itemVOList = 3;
  /** 下轮大奖 */
  RewardObjectList nextRoundLuckRewards = 4;
}

/** 大转盘信息请求 */
message RotaryTableTypeRequest{
  /** 大转盘类型(0:全部) */
  RotaryTableType type = 1;
}
/** 大转盘信息响应 */
message RotaryTableInfoResponse{
  /** 大转盘显示对象列表 */
  repeated RotaryTableVO rotaryTableList = 1 ;
}

/** 大转盘转动转盘请求对象 */
message RotaryTableTurnRequest{
  /** 大转盘类型 */
  RotaryTableType type = 1;
  /** 抽取次数 */
  int32 times = 2;
}

/** 大转盘转动转盘信息响应 */
message RotaryTableTurnResponse{
  /** 奖励索引列表-> RotaryTableItemVO.index*/
  IntListPacket rewardIndexes = 1;
}
/**
 * 大转盘类型
 */
enum RotaryTableType{
  ROTARY_TABLE_NONE = 0;
  /**
   * 1.轮回战场低级
   */
  ROTARY_TABLE_REINCARNATION_LOWER = 1;
  /**
   * 2.轮回战场高级
   */
  ROTARY_TABLE_REINCARNATION_HIGHER = 2;
}