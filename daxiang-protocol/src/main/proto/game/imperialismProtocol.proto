syntax = "proto3";
option java_package = "cn.daxiang.protocol.game";
option optimize_for = SPEED;

import "game/commonProtocol.proto";
import "game/battleProtocol.proto";

enum ImperialismCmd {
  IMPERIALISM_CMD_NONE = 0;
  /**
   * 获取决战皇城公共信息
   * 请求:{@code Request}
   * 响应:{@code ImperialismGlobalResponse}
   */
  IMPERIALISM_GLOBAL_INFO = 1;
  /**
   * 获取决战皇城个人信息
   * 请求:{@code Request}
   * 响应:{@code ImperialismActorResponse}
   */
  IMPERIALISM_ACTOR_INFO = 2;
  /**
   * 获取决战皇城宫殿信息
   * 请求:{@code LongPacket}    id->0为获取所有
   * 响应:{@code ImperialismPalaceResponse}
   */
  IMPERIALISM_PALACE_INFO = 3;
  /**
   * 获取决战皇城宫殿个人信息
   * 请求:{@code Request}
   * 响应:{@code ImperialismPalaceActorResponse}
   */
  IMPERIALISM_PALACE_ACTOR_INFO = 4;
  /**
   * 决战皇城-雕像升级
   * 请求:{@code Request}
   * 响应:{@code Response}
   * 推送:{@code ImperialismActorResponse}
   */
  IMPERIALISM_STATUE_LEVEL = 5;
  /**
   * 决战皇城-刷新对手
   * 请求:{@code Request}
   * 响应:{@code ImperialismChallengeTargetResponse}
   */
  IMPERIALISM_REFRESH_TARGET = 7;
  /**
   * 决战皇城-挑战
   * 请求:{@code ImperialismChallengeRequest}
   * 响应:{@code Response}
   * 推送:{@code ImperialismActorResponse}
   * 推送:{@code ImperialismBattleResponse}
   */
  IMPERIALISM_CHALLENGE = 8;
  /**
   * 决战皇城-购买次数
   * 请求:{@code ImperialismBuyTimesRequest}
   * 响应:{@code Response}
   * 推送:{@code ImperialismActorResponse}
   */
  IMPERIALISM_BUY_TIMES = 9;
  /**
   * 决战皇城-追随
   * 请求:{@code LongPacket}
   * 响应:{@code Response}
   * 推送:{@code ImperialismPalaceActorResponse}
   * 推送:{@code ImperialismPalaceResponse}
   */
  IMPERIALISM_FOLLOW = 10;
  /**
   * 决战皇城-申请护佑
   * 如果已在申请列表，再次发送就是取消申请
   * 请求:{@code LongPacket}
   * 响应:{@code Response}
   * 推送:{@code ImperialismPalaceActorResponse}
   * 推送:{@code ImperialismPalaceResponse}
   */
  IMPERIALISM_APPLY = 11;
  /**
   * 决战皇城-申请通过
   *
   * 请求:{@code LongPacket}       id->0=全部
   * 响应:{@code Response}
   * 推送:{@code ImperialismPalaceActorResponse}  被通过的申请人
   * 推送:{@code ImperialismPalaceResponse}       被通过的申请人，殿主
   */
  IMPERIALISM_APPROVE = 12;
  /**
   * 决战皇城-拒绝申请
   *
   * 请求:{@code LongPacket}       id->0=全部
   * 响应:{@code Response}
   * 推送:{@code ImperialismPalaceActorResponse}  被拒绝的申请人
   * 推送:{@code ImperialismPalaceResponse}       被拒绝的申请人，殿主
   */
  IMPERIALISM_REJECT = 13;
  /**
   * 决战皇城-殿主招募
   *
   * 请求:{@code Request}
   * 响应:{@code Response}
   * 推送:{@code ImperialismActorResponse}
   */
  IMPERIALISM_RECRUIT = 14;
  /**
   * 决战皇城-殿主设置等级限制
   *
   * 请求:{@code IntPacket}
   * 响应:{@code Response}
   * 推送:{@code ImperialismActorResponse}
   */
  IMPERIALISM_LEVEL_LIMIT = 15;
  /**
   * 决战皇城-殿主设置军团免入
   *
   * 请求:{@code BoolPacket}
   * 响应:{@code Response}
   * 推送:{@code ImperialismActorResponse}
   * 推送:{@code ImperialismPalaceResponse}
   */
  IMPERIALISM_AUDIT = 16;

  /**
   * 决战皇城-踢出
   *
   * 请求:{@code LongPacket}
   * 响应:{@code Response}
   * 推送:{@code ImperialismPalaceActorResponse} 被踢出的玩家
   * 推送:{@code ImperialismPalaceResponse}      被踢出的玩家，殿主
   */
  IMPERIALISM_KICK_OFF = 17;
  /**
   * 决战皇城-领取分成奖励
   *
   * 请求:{@code Request}
   * 响应:{@code RewardResultResponse}
   * 推送:{@code ImperialismPalaceResponse}
   */
  IMPERIALISM_SHARED_REWARD = 18;
  /**
   * 决战皇城-护佑/跟随结算
   *
   * 请求:{@code Request}
   * 响应:{@code RewardResultResponse}
   * 推送:{@code ImperialismPalaceActorResponse}
   * 推送:{@code ImperialismPalaceResponse}
   */
  IMPERIALISM_SETTLEMENT = 19;

  /**
   * 决战皇城-成就奖励
   * 请求:{@code IntPacket}
   * 响应:{@code RewardResultResponse}
   * 推送:{@code ImperialismActorResponse}
   */
  IMPERIALISM_ACHIEVE_REWARD = 20;

  /**
   * 决战皇城-获取排名
   * 请求:{@code ImperialismRankRequest}
   * 响应:{@code ImperialismChallengeTargetResponse}
   */
  IMPERIALISM_RANK = 21;
  /**
   * 决战皇城-获取战报
   * 这里面存在1类型第五名打2类型的298名或者2类型3名打3类型19名这样的情况，需要客户端自行处理
   * 请求:{@code ImperialismRankTypeRequest}
   * 响应:{@code ImperialismBattleReportResponse}
   */
  IMPERIALISM_BATTLE_REPORT = 22;
  /**
   * 决战皇城-特权一次性奖励领取
   *
   * 请求:{@code Request}
   * 响应:{@code RewardResultResponse}
   * 推送:{@code ImperialismActorResponse}
   */
  IMPERIALISM_PRIVILEGE_DISPOSABLE_REWARD = 23;
  /**
   * 决战皇城-特权每日奖励领取
   *
   * 请求:{@code Request}
   * 响应:{@code RewardResultResponse}
   * 推送:{@code ImperialismActorResponse}
   */
  IMPERIALISM_PRIVILEGE_DAILY_REWARD = 24;
  /**
   * 决战皇城-重置排名
   * 请求:{@code Request}
   * 响应:{@code Response}
   * 推送:{@code ImperialismActorResponse}
   */
  IMPERIALISM_RESET_RANK = 25;
  /**
   * 决战皇城-殿主设置全员免入
   *
   * 请求:{@code BoolPacket}
   * 响应:{@code Response}
   * 推送:{@code ImperialismActorResponse}
   * 推送:{@code ImperialismPalaceResponse}
   */
  IMPERIALISM_ALL_AUDIT = 26;
  /**
   * 推送决战皇城个人信息
   * 推送:{@code ImperialismActorResponse}
   */
  PUSH_IMPERIALISM_ACTOR_INFO = 100;
  /**
   * 推送决战皇城宫殿信息
   * 推送:{@code ImperialismPalaceResponse}
   */
  PUSH_IMPERIALISM_PALACE_INFO = 101;
  /**
    * 推送决战皇城宫殿个人信息
    * 推送:{@code ImperialismPalaceActorResponse}
    */
  PUSH_IMPERIALISM_PALACE_ACTOR_INFO = 102;
  /**
    * 推送决战皇城战斗结果
    * 推送:{@code ImperialismBattleResponse}
    */
  PUSH_IMPERIALISM_BATTLE_RESULT = 103;
  /**
  * 推送决战皇城战区排名变化
  *（只有自己被打才会推送，在皇城界面获取排名，不在皇城界面就别来获取排名）
  * 推送:{@code Response}
  */
  PUSH_IMPERIALISM_REGION_RANK_REFRESH = 104;
}

/** 决战皇城公共响应 */
message ImperialismGlobalResponse{
  /** 赛季 */
  int32 season = 1;
  /** 统治榜 */
  repeated ImperialismRank ranks = 2;
}

/** 决战皇城战斗响应 */
message ImperialismBattleResponse {
  /**
   * 战报结果
   */
  BattleResultResponse battle = 1;
  /**
   * 排行类型
   */
  ImperialismRankType type = 2;
  /**
   * 当前排名
   */
  int64 rank = 3;
  /**
   * 本次排名变化
   */
  int64 change = 4;
  /**
   * 历史最高排名变化
   */
  int64 highestChange = 5;
}
/** 决战皇城个人信息 */
message ImperialismActorResponse{
  /** 雕像等级 */
  int32 statueLevel = 1;
  /** 排名类型 1:本服；2.小战区；默认为0，这里注意3不会有，皇城大战区的排名不在这里，去大战区列表自己找*/
  ImperialismRankType type = 2;
  /** 排名,-1代表合战区回退 */
  int64 rank = 3;
  /** 本服&小战区剩余挑战次数 */
  int32 challengeTimes = 4;
  /** 本服&小战区已购买次数 */
  int32 buyTimes = 5;
  /** 皇城剩余挑战次数 */
  int32 palaceChallengeTimes = 6;
  /** 皇城已购买次数 */
  int32 palaceBuyTimes = 7;
  /** 已领取成就奖励Id */
  repeated int32 receiveList = 8;
  /** 是否购买特权*/
  bool prerogative = 9;
  /** 是否领取特权一次性奖励 */
  bool prerogativeDisposable = 10;
  /** 是否领取特权每日奖励 */
  bool prerogativeDaily = 11;
  /**
   * 历史最高排名,这里123都有，用来判断成就奖励和历史排名提升奖励
   * {type,rank}
   */
  map<int32, int64> highestRanks = 12;
  /**
   * 已招募次数
   */
  int32 recruitTimes = 13;
}

/** 决战皇城宫殿信息响应 */
message ImperialismPalaceResponse{
  /** 决战皇城宫殿信息列表 */
  repeated ImperialismPalace palaceList = 1;
}

/** 决战皇城宫殿信息 */
message ImperialismPalace{
  /** 宫殿ID */
  int64 id = 1;
  /** 殿主角色属性 */
  ActorProfile actorProfile = 2;
  /** 等级限制 */
  int32 levelLimit = 3;
  /** 军团免审状态*/
  bool audit = 4;
  /** 申请列表 */
  repeated ImperialismApplyActor applyList = 5;
  /** 护佑列表 */
  repeated ImperialismPalaceActor protectList = 6;
  /** 追随列表 */
  repeated ImperialismPalaceActor followList = 7;
  /** 分成奖励列表 */
  RewardObjectList sharedRewardList = 8;
  /** 全员免审状态*/
  bool allAudit = 9;
}
/** 决战皇城申请玩家信息 */
message ImperialismApplyActor{
  /** 角色属性 */
  ActorProfile actorProfile = 1;
  /** 是否购买特权*/
  bool prerogative = 2;
  /** 申请时间 */
  int64 time = 3;
}
/** 决战皇城宫殿玩家信息*/
message ImperialismPalaceActor{
  /** 角色属性 */
  ActorProfile actorProfile = 1;
  /** 过期时间 */
  int64 expiredTime = 2;
}
/** 决战皇城宫殿个人信息响应 */
message ImperialismPalaceActorResponse{
  /**
   * 宫殿ID
   */
  int64 palaceId = 1;
  /**
   * 护佑/追随开始时间
   */
  int64 startTime = 2;
  /**
  * 已申请列表
  * [palaceId,palaceId,palaceId]
  */
  repeated int64 appliedList = 3;
}
/** 决战皇城购买挑战次数请求 */
message ImperialismBuyTimesRequest{
  /**  类型 */
  ImperialismRankType type = 1;
  /** 次数 */
  int32 times = 2;
}

/** 决战皇城挑战请求 */
message ImperialismChallengeRequest{
  /**  类型 */
  ImperialismRankType type = 1;
  /** 排名 */
  int64 rank = 2;
}

/** 决战皇城挑战目标响应 */
message ImperialismChallengeTargetResponse{
  /** 挑战目标列表 */
  repeated ImperialismRank targets = 1;
}


/** 决战皇城排行显示对象 */
message ImperialismRank {
  /**  类型 */
  ImperialismRankType type = 1;
  /** 排名，这里如果突然变大就意味着从本服到小战区，需要客户端自行处理 */
  int64 rank = 2;
  /** 角色属性(如果为空则代表无人，需要客户端自行显示机器人) */
  ActorProfile actorProfile = 3;
}
/** 决战皇城获取排名请求 */
message ImperialismRankRequest{
  /** 排行榜类型 */
  ImperialismRankType type = 1;
  /** 起始排名 */
  int64 begin = 2;
  /** 结束排名 */
  int64  end = 3;
  /** 真实玩家数量，等于0就无效*/
  int32 count = 4;
}

/** 决战皇城排名类型请求 */
message ImperialismRankTypeRequest{
  /** 排行榜类型 */
  ImperialismRankType type = 1;
}
/** 决战皇城战报响应 */
message ImperialismBattleReportResponse{
  /** 战报列表 */
  repeated ImperialismBattleReport reportList = 1;
}
/** 决战皇城战报 */
message ImperialismBattleReport {
  /** 挑战时间 */
  int64 time = 1;
  /** 战斗回放 */
  ImperialismBattleResponse battleResult = 2;
}

/** 决战皇城排行榜类型 */
enum ImperialismRankType {
  RANK_TYPE_NONE = 0;
  /** 本服 */
  RANK_TYPE_SERVER = 1;
  /** 小战区 */
  RANK_TYPE_ZONE = 2;
  /** 皇城战区 */
  RANK_TYPE_REGION = 3;
}