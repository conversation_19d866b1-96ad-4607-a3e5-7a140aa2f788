syntax = "proto3";
option java_package = "cn.daxiang.protocol.game";
option optimize_for = SPEED;

import "game/commonProtocol.proto";

/** 镇妖台 */
enum SuppressdemonCmd {
  SUPPRESS_DEMON_CMD_NONE = 0;
  /**
    * 获取镇妖台信息
    * <pre>
    * 请求:{@code Request}
    * 响应:{@code SuppressDemonInfoResponse}
    * </pre>
    */
  SUPPRESS_DEMON_GET_INFO = 1;
  /**
   * 获取镇妖台排名
   * <pre>
   * 请求:{@code IntPacket}
   * 响应:{@code SuppressDemonRankResponse}
   * </pre>
   */
  SUPPRESS_DEMON_RANK = 2;
  /**
   * 镇妖台准备
   * <pre>
   * 请求:{@code IntPacket}
   * 响应:{@code Response}
   * </pre>
   */
  SUPPRESS_DEMON_PREPARE = 3;
  /**
   * 镇妖台挑战
   * <pre>
   * 请求:{@code SuppressDemonChallengeRequest}
   * 响应:{@code RewardResultResponse}
   * </pre>
   */
  SUPPRESS_DEMON_CHALLENGE = 4;
  /**
   * 购买镇妖台次数
   * <pre>
   * 请求:{@code IntPacket}
   * 响应:{@code Response}
   * </pre>
   */
  SUPPRESS_DEMON_BUY_TIMES = 5;
  /**
   * 镇妖台扫荡
   * <pre>
   * 请求:{@code IntPacket}传当前所在boss的配置Id
   * 响应:{@code RewardResultResponse}
   * </pre>
   */
  SUPPRESS_DEMON_WIPE_OUT = 6;
  /**
   * 推送镇妖台信息
   * <pre>
   * 推送:{@code SuppressDemonInfoResponse}
   * </pre>
   */
  PUSH_SUPPRESS_DEMON_INFO = 100;
}
message SuppressDemonInfoResponse{
  /**
    * 挑战次数
    */
  int32 challengeTimes = 1;
  /**
   * 购买次数
   */
  int32 buyTimes = 2;
  /**
   * 历史最高击杀BossId
   */
  int32 maxId = 3;
}
message SuppressDemonRankResponse {
  /**
     * ID
     */
  int32 id = 1;
  /**
   * 排名列表
   */
  repeated SuppressDemonRankVO ranks = 2;
}
message SuppressDemonRankVO {
  /** 角色属性 */
  ActorProfile actorProfile = 1;
  /**
    * 排名
    */
  int64 rank = 2;
  /**
   * 伤害
   */
  int64 damage = 3;
  /**
   * 击杀耗时(MS)
   */
  int64 time = 4;
}
message SuppressDemonChallengeRequest {
  /**
    * 副本ID
    */
  int32 id = 1;
  /**
   * 剩余血量
   */
  int64 leftHP = 2;
  /**
   * 击杀耗时(单位:ms,只有击杀的时候才可以发)
   */
  int64 time = 3;
  /**
   * 检查所需数据：[{上阵英雄配置ID:战力,上阵英雄配置ID:战力},[[英雄配置ID,技能攻击ID,伤害,是否暴击(0:false.1:true)],[英雄配置ID,技能攻击ID,伤害,是否暴击(0:false.1:true)]]]
   */
  string check = 4;

}