syntax = "proto3";
option java_package = "cn.daxiang.protocol.game";
option optimize_for = SPEED;

import "game/commonProtocol.proto";

/** 活动 */
enum ActivityCmd {
  ACTIVITY_CMD_NONE = 0;
  /**
  * 获取活动配置
  * 请求:{@code Request}
  * 响应:{@code ActivityConfigResponse}
  */
  GET_ACTIVITY_CONFIG = 1;
  /**
  * 获取活动信息
  * 请求:{@code ActivityInfoRequest}
  * 响应:{@code ActivityInfoResponse}
  */
  GET_ACTIVITY_INFO = 2;
  /**
  * 活动动作
  * 请求:{@code ActivityActionRequest}
  * 响应:{@code ActivityActionResponse}
  * 推送:{@code ActivityInfoResponse}
  */
  ACTIVITY_ACTION = 3;
  /**
  * 推送活动信息
  * 推送:{@code ActivityInfoResponse}
  */
  PUSH_ACTIVITY_INFO = 100;
  /**
  * 推送活动信息
  * 推送:{@code ActivityConfigResponse}
  */
  PUSH_ACTIVITY_CONFIG = 101;

}


/** 活动配置响应 */
message ActivityConfigResponse {
  /**  活动开启表 */
  repeated ActivityOpenConfig config = 5;
}

/** 活动开启表 */
message ActivityOpenConfig {
  /** 活动ID */
  int32 id = 1;
  /** 活动类型:ActivityType */
  int32 activityType = 2;
  /** 角色等级限制 */
  int32 level = 3;
  /** VIP等级限制 */
  int32 vipLevel = 4;
  /** 活动数据ID */
  int32 data = 5;
  /** 开启时间 */
  int64 open = 6;
  /** 结束时间 */
  int64 close = 7;
  /** 展示时间 */
  int64 show = 8;
  /** 活动描述 */
  string desc = 9;
}

/** 活动信息请求 */
message ActivityInfoRequest {
  /** 活动ID */
  int32 activityId = 1;
}

/** 活动信息响应 */
message ActivityInfoResponse {
  /** 活动列表 */
  map<int32, ActivityInfo> activityInfo = 1;
}

/** 活动信息 */
message ActivityInfo {
  /** 活动ID */
  int32 activityId = 1;
  /** 活动ID */
  int32 activityType = 2;
  /** 活动公共数据 */
  bytes global = 3;
  /** 活动记录 */
  bytes record = 4;
}

/** 活动动作响应 */
message ActivityActionResponse {
  /** 活动ID */
  int32 activityId = 1;
  /** 奖励结果 */
  RewardResult rewardResult = 2;
  /** 奖励展示 */
  map<int32, RewardObjectList> rewardObject = 3;
  /** 额外参数 */
  bytes value = 4;
  /** ID */
  int32 id = 5;
}


/** 活动动作请求 */
message ActivityActionRequest {
  /** 活动ID */
  int32 activityId = 1;
  /** ID */
  int32 id = 2;
  /** 其他参数 */
  bytes value = 3;
}

/** 活动21活动动作响应体 */
message Activity21DrawResponse {
  repeated Activity21Result list = 1;
}

message Activity21Result {
  /** 数字 */
  int32 number = 1;
  /** 奖励列表 */
  RewardObjectList rewardObject = 2;
  /** 获得积分 */
  int32 score = 3;
  /** 是否幸运奖 */
  bool trigger = 4;
}