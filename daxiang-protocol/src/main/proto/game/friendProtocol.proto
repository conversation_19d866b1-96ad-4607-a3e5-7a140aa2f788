syntax = "proto3";
option java_package = "cn.daxiang.protocol.game";
option optimize_for = SPEED;

import "game/commonProtocol.proto";

/** 好友 */
enum FriendCmd {
  FRIEND_NONE = 0;
  /**
  * 1.获得好友
  * <pre>
  * 请求:{@code Request}
  * 响应:{@code FriendInfoResponse}
  * </pre>
  */
  GET_FRIEND_INFO = 1;

  /**
   * 2.系统推荐
   * <pre>
   * 请求:{@code Request}
   * 响应:{@code FriendSystemRecommendResponse}
   * </pre>
   */
  SYSTEM_RECOMMEND = 2;

  /**
   * 3.添加好友
   * <pre>
   * 请求:{@code FriendAddRequest}
   * 响应:{@code Response}
   * 推送:{@code FriendInfoResponse}
   * </pre>
   */
  ADD_FRIEND = 3;

  /**
   * 4.手动查找添加好友
   * <pre>
   * 请求:{@code FriendManualAddRequest}
   * 响应:{@code FriendSystemRecommendResponse}
   * </pre>
   */
  MANUAL_FIND = 4;

  /**
   * 5.同意/忽略
   * <pre>
   * 请求:{@code FriendAgreeIgnoreRequest}
   * 响应:{@code Response}
   * 推送:{@code FriendInfoResponse}
   * 推送:{@code FriendDeleteResponse}
   * </pre>
   */
  AGREE_IGNORE = 5;

  /**
   * 6.刪除好友
   * <pre>
   * 请求:{@code FriendSystemRequest}
   * 响应:{@code Response}
   * 推送:{@code FriendInfoResponse}
   * 推送:{@code FriendDeleteResponse}
   * </pre>
   */
  DELETE_FRIEND = 6;
  /**
  * 7.一键同意
  * <pre>
  * 请求:{@code Request}
  * 响应:{@code Response}
  * 推送:{@code FriendInfoResponse}
  * 推送:{@code FriendDeleteResponse}
  * </pre>
  */
  QUICK_AGREE = 7;
  /**
   * 8.一键拒绝
   * <pre>
   * 请求:{@code Request}
   * 响应:{@code Response}
   * 推送:{@code FriendDeleteResponse}
   * </pre>
   */
  QUICK_IGNORE = 8;
  /**
  * 9.赠送友情点
  * <pre>
  * 请求:{@code FriendSystemRequest}
  * 响应:{@code Response}
  * 推送:{@code FriendInfoResponse}
  * </pre>
  */
  SEND_FRIEND_COIN = 9;

  /**
   * 10.一键赠送友情点
   * <pre>
   * 请求:{@code Request}
   * 响应:{@code Response}
   * 推送:{@code FriendInfoResponse}
   * </pre>
   */
  QUICK_SEND_FRIEND_COIN = 10;

  /**
   * 11.领取友情点
   * <pre>
   * 请求:{@code FriendSystemRequest}
   * 响应:{@code RewardResultResponse}
   * 推送:{@code FriendInfoResponse}
   * </pre>
   */
  RECEIVE_FRIEND_COIN = 11;

  /**
   * 12.一键领取友情点
   * <pre>
   * 请求:{@code Request}
   * 响应:{@code RewardResultResponse}
   * 推送:{@code FriendInfoResponse}
   * </pre>
   */
  QUICK_RECEIVE_FRIEND_COIN = 12;


  /**
   * 13.获取黑名单黑名单加入/移除
   * <pre>
   * 请求:{@code FriendBlackListProcessRequest}
   * 响应:{@code FriendBlackListProcessResponse}
   * </pre>
   */
  BLACK_LIST_PROCESS = 13;
  /**
   * 14. 获取某个好友当前信息
   * <pre>
   * 请求:{@code FriendSystemRequest}
   * 响应:{@code Response}
   * 推送:{@code FriendInfoResponse}
   * </pre>
   */
  GET_FRIEND_ATTRIBUTE = 14;
  /**
   * 15.一键刪除好友
   * <pre>
   * 请求:{@code FriendSystemListRequest}
   * 响应:{@code Response}
   * 推送:{@code FriendInfoResponse}
   * 推送:{@code FriendDeleteResponse}
   * </pre>
   */
  QUICK_DELETE_FRIEND = 15;

  /**
   * 100.推送好友信息
   * <pre>
   * 推送:{@code FriendInfoResponse}
   * </pre>
   */
  PUSH_FRIEND_INFO = 100;

  /**
   * 101.推送删除好友信息
   * <pre>
   * 推送:{@code FriendDeleteResponse}
   * </pre>
   */
  PUSH_FRIEND_DELETE = 101;

  /**
   * 102.推送删除好友申请信息
   * <pre>
   * 推送:{@code FriendDeleteResponse}
   * </pre>
   */
  PUSH_APPLY_DELETE = 102;

  /**
  * 103.推送好友部分信息
  * <pre>
  * 推送:{@code FriendInfoResponse}
  * </pre>
  */
  PUSH_FRIEND_PARTIAL_INFO = 103;
}
message FriendInfoResponse {
  /**
   * 好友属性list
   */
  repeated FriendVO friendAttributeList = 1;
  /**
   * 好友申请列表
   */
  repeated FriendVO applyAttributeList = 2;
  /**
   * 黑名单 key:actorId,value:ActorProfile
   */
  map<int64, ActorProfile> blackListmap = 3;
  /**
    * 今日领取次数
    */
  int32 receiveTimes = 4;
  /**
     * 今日已赠送列表 actorId
     */
  repeated int64 hadSends = 5;
  /**
     * 今日已领取列表 actorId
     */
  repeated int64 hadReceives = 6;
  /**
     * 今日可领取列表 actorId
     */
  repeated int64 canReceives = 7;

}
message FriendVO {
  /**
    * 角色属性
  */
  ActorProfile actorProfile = 1;
  /**
   * 是否在线
   */
  bool isOnline = 2;
  /**
   * 最后在线时间
   */
  int64 lastLogoutTime = 3;
  /**
     * 亲密度
     */
  int32 intimacy = 4;
  /**
     * 亲密等级
     */
  int32 intimacyLevel = 5;


}
message FriendSystemRecommendResponse {
  /**
   * 推荐名单list
   */
  repeated FriendVO recommendVOList = 1;
}
message FriendSystemRequest {
  /**
   * 目标服ID
   */
  int32 serverId = 1;
  /**
   * 目标ID
   */
  int64 targetId = 2;
}
message FriendSystemListRequest {
  repeated FriendSystemRequest friendSystemLists = 1;
}
message FriendManualAddRequest {
  string actorName = 1;
}
message FriendAgreeIgnoreRequest {
  /**
   * 目标服ID
   */
  int32 serverId = 1;
  /**
   * 目标ID
   */
  int64 targetId = 2;
  /**
   * 是否添加
   * true:添加,false:忽略
   */
  bool isAdd = 3;
}
message FriendDeleteResponse {
  /**
   * 角色ID
   */
  repeated int64 ids = 1;
}
message FriendBlackListProcessRequest {
  /**
     * 角色属性
   */
  ActorProfile actorProfile = 1;
}
message FriendBlackListProcessResponse {
  /**
     * 角色属性
   */
  ActorProfile actorProfile = 1;
}
message FriendAddRequest {
  /** 添加好友列表*/
  repeated FriendSystemRequest friends = 1;
}