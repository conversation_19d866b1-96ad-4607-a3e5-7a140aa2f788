syntax = "proto3";
option java_package = "cn.daxiang.protocol.game";
option optimize_for = SPEED;

import "game/commonProtocol.proto";

/** activityType:1 新服狂欢活动响应 */
message CarnivalRequestValue {
  /** 1、礼包奖励 2、积分奖励 */
  int32 typeId = 1;
}

/** activityType:1 新服狂欢活动响应 */
message CarnivalRecord {
  /** 活动类型ID */
  int32 activityType = 1;
  /** 活动第几天 */
  int32 day = 2;
  /**
   * 新服狂欢优惠购买情况
   * {key:id,value:times}
   */
  map<int32, int32> discountMap = 3;
  /**
   * 新服狂欢领取积分奖励列表
   */
  repeated int32 scoreList = 4;
}

/** activityType:2 群雄逐鹿活动响应 */
message Activity2Record {
  /** key:type,value:target */
  map<int32, int64> target = 1;
  /** 达标奖励领取ID */
  repeated int32 receives = 2;
}

/** activityType:2 群雄逐鹿 */
message Activity2Global {
  /** key:type, value:List<ActivityRank>*/
  map<int32, Activity2RankList> rank = 1;
}

message Activity2RankList{
  repeated ActivityRank rank = 1;
}

/** activityType:3 充值每日特惠活动响应 */
message Activity3Record {
  /** key:id,value:购买数量 */
  map<int32, int32> buyTimes = 1;
  /** key:id,value:已领奖励数量 */
  map<int32, int32> receives = 2;
}

/** activityType:4 任务战令请求 */
message Activity4RequestValue {
  /** 1、一键领取 2、购买经验 3、普通战令领取 4、高级战令领取*/
  int32 typeId = 1;
  /** 购买几级*/
  int32 buyLevel = 2;
  /** 等级*/
  int32 level = 3;
}


/** activityType:4 任务战令 */
message Activity4Record {
  /** 是否激活高级战令 */
  bool isActivate = 1;
  /** 普通战令已领取列表 */
  repeated int32 normalReceives = 2;
  /** 高级战令已领取列表 */
  repeated int32 supremeReceives = 3;
}

/** activityType:5 新服签到 */
message Activity5Record {
  /** 活动开启第几天 */
  int32 day = 1;
  /** 已领奖天数列表 */
  repeated int32 receives = 2;
}

/** activityType:6 充值每周礼包活动响应 */
message Activity6Record {
  /** key:id,value:购买数量 */
  map<int32, int32> buyTimes = 1;
  /** key:id,value:已领奖励数量 */
  map<int32, int32> receives = 2;
}

/** activityType:7 定制礼包 */
message CustomizePackageRequestValue {
  /** 1: 奖励选定 2: 奖励领取*/
  int32 type = 1;
  /** 定制礼包(选中奖励) key:optionId, value:rewardId */
  map<int32, int32> option = 2;
}

/** activityType:7 定制礼包活动响应 */
message CustomizePackageRecord {
  /** key:chargeId, value: CustomizePackageRecordInfo*/
  map<int32, CustomizePackageRecordInfo> record = 1;
}

/** 定制礼包信息 */
message CustomizePackageRecordInfo {
  /** 配置Id */
  int32 configId = 1;
  /** 当前所选奖励 [optionId, rewardId] */
  map<int32, int32> rewardMap = 2;
  /** 当前状态 0.初始状态 1.已充值*/
  int32 status = 3;
  /** 已购买次数 */
  int32 times = 4;
}

/** activityType:8 充值推荐连续充值活动响应 */
message Activity8Record {
  /** key:id（第几天）,value:充值(分) */
  map<int32, int64> recharge = 1;
  /** 已领奖励ID */
  repeated int32 receives = 2;
}

/** activityType:9 充值推荐累计充值活动响应 */
message Activity9Record {
  /** 累计充值(分) */
  int64 recharge = 1;
  /** 已领奖励ID */
  repeated int32 receives = 2;
}

/** activityType:10 上古珍宝寻宝活动请求 */
message Activity10RequestValue {
  /** 1、抽奖 2、进度值领取 */
  int32 typeId = 1;
  /** type为1的时候代表抽奖次数 type为2的时候代表配置Id */
  int32 num = 2;
}

/** activityType:10 上古珍宝寻宝活动响应 */
message Activity10Record {
  /** 幸运值 */
  int32 luck = 1;
  /** 每日免费一次抽奖状态(true已领取 false未领取) */
  bool receive = 2;
  /** 已领取进度列表*/
  repeated int32 progressReceive = 3;
}

/** activityType:11 推荐礼包活动响应 */
message Activity11Record {
  /** key:id,value:购买数量 */
  map<int32, int32> buyTimes = 1;
  /** key:id,value:已领奖励数量 */
  map<int32, int32> receives = 2;
}

message Activity12Record {
  /** 战力 */
  int64 power = 1;
  /** 星数 */
  int32 star = 2;
  /** type:3、4达标奖励领取ID */
  repeated int32 receives = 3;
}

/** activityType:12 群雄逐鹿 */
message Activity12Global {
  /** 1、战力排行 */
  repeated Activity12Rank powerRank = 1;
  /** 2、关卡排行 */
  repeated Activity12Rank storyRank = 2;
}

/** 英雄战力排行显示对象 */
message Activity12Rank {
  /** 角色属性 */
  ActorProfile actorProfile = 1;
  /** 排名 */
  int64 rank = 2;
  /**  战力/关卡ID */
  int64 value = 3;
}

/** activityType:13 限时商人 */
message Activity13Record {
  /** key:id,value:当天购买数量 */
  map<int32, int32> dayBuyTimes = 1;
  /** key:id,value:累计购买数量 */
  map<int32, int32> buyTimes = 2;
  /** key:id,value:累计已领奖励数量 */
  map<int32, int32> receives = 3;
}

/** activityType:14 招兵买马活动响应 */
message Activity14Record {
  /** key:type,value:累计已获得的活动值 */
  map<int32, int32> value = 1;
  /** 已领取列表 */
  repeated int32 receives = 2;
}

/** activityType:15 过关斩将活动请求 */
message Activity15RequestValue {
  /** 1、挑战 2、礼包购买 3、商店购买 */
  int32 typeId = 1;
  /** 1和3类型的次数 */
  int32 num = 2;
}

/** activityType:15 过关斩将活动响应 */
message Activity15Record{
  /** 进度值 */
  int32 value = 1;
  /** 礼包购买次数 key:id value:购买次数 */
  map<int32, int32> buyTimes = 2;
  /** 直购礼包可领取次数 key:id value:可领取次数 */
  map<int32, int32> directPurchaseTimes = 3;
  /** 商店购买数量 key:id value:购买数量 */
  map<int32, int32> storeTimes = 4;
}

/** activityType:16 新手礼包请求 */
message Activity16RequestValue {
  /** 领取奖励配置ID */
  repeated int32 id = 1;
}

/** activityType:16 新手礼包响应 */
message Activity16Record {
  /** 多档直购激活page  */
  repeated int32 page = 1;
  /** 已领取奖励的config集合 */
  repeated int32 receive = 2;
  /** 活动第几天 */
  int32 day = 3;
}

/** activityType:17 超值材料礼包 */
message Activity17Record {
  /** 已购买礼包信息*/
  map<int32, IntListPacket> buyBag = 1;
}

/** activityType:18 群雄逐鹿2活动响应 */
message Activity18Record {
  /** 精英副本星数 */
  int32 eliteStoryStar = 1;
  /** 名将塔通关层数 */
  int32 towerLayer = 2;
  /** type:9、10达标奖励领取ID */
  repeated int32 receives = 3;
}

/** activityType:18 群雄逐鹿2 */
message Activity18Global {
  /** 5-装备评分 */
  repeated Activity18Rank equipmentScoreRank = 1;
  /** 7-宝物评分 */
  repeated Activity18Rank treasureScoreRank = 2;
}

/** 排行显示对象 */
message Activity18Rank {
  /** 角色属性 */
  ActorProfile actorProfile = 1;
  /** 排名 */
  int64 rank = 2;
  /**  装备评分|宝物评分 */
  int64 value = 3;
}

/** activityType:19 群雄逐鹿3活动响应 */
message Activity19Record {
  /** 专武图鉴值 */
  int64 immortalsManualValue = 1;
  /** 活动期间藏兵阁挑战金将次数 */
  int64 arsenalChallengeTimes = 2;
  /** type:12、13达标奖励领取ID */
  repeated int32 receives = 3;
}

/** activityType:19 群雄逐鹿3 */
message Activity19Global {
  /** 11-专武图鉴值 */
  repeated Activity19Rank immortalsManualValueRank = 1;
}

/** 排行显示对象 */
message Activity19Rank {
  /** 角色属性 */
  ActorProfile actorProfile = 1;
  /** 排名 */
  int64 rank = 2;
  /**  装备评分|宝物评分 */
  int64 value = 3;
}

/** activityType:20 稀世绘卷活动信息响应 */
message Activity20RecordVO{
  repeated int32 receives = 1;
  /** 活动第几天 */
  int32 day = 2;
}

/** activityType:21 天官赐福玩家个人记录 */
message Activity21RecordVO{
  /** 积分 */
  int32 score = 1;
  /** 最大数 */
  int32 maxNumber = 2;
  /** 礼包购买次数 key:id value:总购买次数 */
  map<int32, int32> buyMap = 3;
  /** 礼包领取次数 key:id value:总领取次数 */
  map<int32, int32> receiveMap = 4;
  /** 已获取奖励列表 **/
  RewardObjectList rewardList = 5;
  /** 活动第几天 */
  int32 day = 6;
  /** 礼包今天购买次数 key:id value:今天购买次数 */
  map<int32, int32> todayBuyMap = 7;
}

/** activityType:21 天官赐福活动请求信息 */
message Activity21RequestValue{
  /**
   * id-1 : 摇奖次数
   * id-2 : 礼包Id
   * id-3 : 排行榜类型
   */
  int32 num = 1;
}

/** activityType:21 天官赐福排行榜 */
message Activity21Global {
  /** 1、积分排行 */
  repeated ActivityRank scoreRank = 1;
  /** 2、幸运排行 */
  repeated ActivityRank luckRank = 2;
  /** 3.中奖播报列表 */
  repeated Activity21ReportEntity reportList = 3;
}
/** activityType:21 天官赐福中奖播报实体 */
message Activity21ReportEntity{
  /** 角色属性 */
  ActorProfile actorProfile = 1;
  /** 中奖数字 */
  int32 num = 2;
  /** 奖励列表 **/
  RewardObjectList rewardList = 3;
}

/** 排行显示对象 */
message ActivityRank {
  /** 角色属性 */
  ActorProfile actorProfile = 1;
  /** 排名 */
  int64 rank = 2;
  /** 值 */
  int64 value = 3;
}
/** 排行榜列表 */
message ActivityRankList {
  repeated ActivityRank ranks = 1;
}

/** activityType:22 迎财神活动请求 */
message Activity22RequestValue {
  /** 1、供奉财神 2、获取香炉 3、购买礼包 */
  int32 typeId = 1;
}

/** activityType:22 迎财神 */
message Activity22Record {
  /** key:id,value:当天购买数量 */
  map<int32, int32> dayBuyTimes = 1;
  /** key:id,value:累计购买数量 */
  map<int32, int32> buyTimes = 2;
  /** key:id,value:累计已领奖励数量 */
  map<int32, int32> receives = 3;
  /** 已领取奖励的config集合 */
  repeated int32 receive = 4;
  /** 供奉次数 */
  int32 worshipTimes = 5;
  /** 累计充值(分) */
  int64 recharge = 6;
  /** 活动第几天 */
  int32 day = 7;
}

/** activityType:22 迎财神-供奉记录展示 */
message Activity22Global {
  /** 11-供奉记录 */
  repeated Activity22RewardRecord record = 1;
}

message Activity22RewardRecord {
  /** 角色名 */
  string actorName = 1;
  /** 元宝 */
  int64 diamond = 2;
}

/** activityType:23 天赐神将-每日签到 */
message Activity23Record {
  /** 活动开启第几天 */
  int32 day = 1;
  /** 已领奖天数列表 */
  repeated int32 receives = 2;
}

/** activityType:24 天赐神将-神将招募 */
message Activity24Record{
  /** 心愿武将 */
  int32 heroId = 1;
  /** 今日招募次数 */
  int32 todayTimes = 2;
  /** 招募次数 */
  int32 totalTimes = 3;
  /** 活动第几天 */
  int32 day = 4;
  /** 抽奖记录 */
  RewardObjectList rewards = 5;
  /** 本此活动累计招募次数 */
  int32 gachaCount = 6;
}

/** activityType:24-天赐神将-排行榜 */
message Activity24ResponseValue{
  ActivityRankList rank = 1;
}

/** activityType:26 天赐神将-代币商店 */
message Activity26Record {
  /** key:id,value:购买数量 */
  map<int32, int32> buyTimes = 1;
}

/** activityType:27 天赐神将-礼包商店 */
message Activity27Record {
  /** key:id,value:当天购买数量 */
  map<int32, int32> dayBuyTimes = 1;
  /** key:id,value:累计购买数量 */
  map<int32, int32> buyTimes = 2;
  /** key:id,value:累计已领奖励数量 */
  map<int32, int32> receives = 3;
}

/** activityType:28 天赐神将-封神之路 */
message Activity28Record {
  /** 已领取的免费礼包id列表 */
  repeated int32 freeRewards = 1;
  /** 已购买的付费礼包数量 */
  map<int32, int32> buyTimes = 2;
  /** 已领取的付费礼包数量 */
  map<int32, int32> receivesTimes = 3;
  /** 活动第几天 */
  int32 day = 4;
}
/** 天赐神将-神将试炼*/
message Activity29RecordVO {
  /** 已领取列表 */
  repeated int32 receiveList = 1;
}
/**
 * activityType:31 摘星揽月
 * id:
 * 1.建造摘星楼 value:次数 IntPacket
 * 2.领取目标任务奖励 value:InPacket
 * 3.获取排行榜 value:区间 KeyValuePacket
 * 4.登高揽月 value: {宝箱类型,数量} IntMap
 * 5.购买宝库商品 value:配置Id KeyValuePacket
 * 6.领取宝库解锁奖励 value:配置Id IntPacket
 * 7.领取直购充值奖励 value:配置Id IntPacket
 * 8.领取积分奖励 value:积分档位 IntPacket
 */
message Activity31RecordVO {
  /** 摘星楼层数 */
  int32 level = 1;
  /** 已领取目标列表 */
  repeated int32 targetIds = 2;
  /** key:id,value:当天购买数量 */
  map<int32, int32> dayBuyTimes = 3;
  /** key:id,value:累计购买数量 */
  map<int32, int32> buyTimes = 4;
  /** key:id,value:累计已领奖励数量 */
  map<int32, int32> receives = 5;
  /** 已购买商品Map key：id,value:count */
  map<int32, int32> products = 6;
  /** 今日登高揽月次数 */
  int32 dailyReceivesTimes = 7;
  /** 已消耗活动资源数量 */
  int32 count = 8;
  /** 任务轮次 */
  int32 cycle = 9;
  /** 已领取的积分奖励档位 */
  repeated int32 receivesScores = 10;
  /** 抽奖记录 */
  RewardObjectList rewards = 11;
  /** 目标任务建造次数 */
  int32  buildTimes = 12;
  /** 已领取的层数奖励 */
  repeated int32 floors = 13;
  /** 今日建造次数 */
  int32 todayBuildTimes = 14;
  /** 活动第几天 */
  int32 day = 15;
}
/** 摘星揽月响应体 */
message Activity31Response{
  /** 动作id */
  int32 id = 1;
  /** 额外参数 */
  /** 可变参数
    * id = 1 RewardObjectList
    * id = 2,4,5,6,7 RewardResult
    * id = 3 ActivityRankList
   */
  bytes value = 2;
}


/**
  * activityType:32 寻龙点穴
  * id:
  * 1-抽卡 value:Activity32Request RewardObject
  * 2-领取进度奖励 value:Activity32Request
  * 3-领取礼包奖励 value:Activity32Request
 */
message Activity32RecordVO {
  /** 幸运值 */
  map<int32, int32> luckMap = 1;
  /** 进度奖励 */
  map<int32, IntListPacket> progressRewardId = 2;
  /** 充值礼包今日购买数量*/
  map<int32, int32> dayBuyTimes = 3;
  /** 充值礼包总购买数量*/
  map<int32, int32> buyTimes = 4;
  /** 充值礼包领取数量*/
  map<int32, int32> receives = 5;
  /** 今日抽奖次数 */
  map<int32, int32> todayTimes = 6;
}

/** 活动32请求 */
message Activity32Request{
  /** 宝库类型*/
  int32 functionType = 1;
  /** 次数 || id */
  int32 times = 2;
}

/**
 * activityType:33 请求value
 */
message Activity33RequestValue {
  /**
   * id:
   * 1.许愿 value:次数
   * 2.充值礼包 value:配置Id
   * 3.领取累计充值奖励 value:配置Id
   * 4.排行榜
   * 5.领取登录有礼奖励 value:配置Id
   * 6.领取七日活动奖励 value:配置Id
   * 7.领取增值礼包奖励 value:配置Id
   * 8.领取奖励兑换奖励 value:配置Id
   * 9.领取登录今日累计充值奖励 value:配置Id
   * 10.选择心愿奖励 value:配置ID IntPacket
   */
  int32 id = 1;
  /**
   * 9，对换次数
   */
  int32 times = 2;
}

/**
 * activityType:33 活动集合
 */
message Activity33Record {
  /** 心愿配置 */
  int32 wishConfigId = 1;
  /** 本轮抽奖次数 */
  int32 raffleTimes = 2;
  /** 活动期间累计抽奖次数 */
  int32 raffleTotalTimes = 3;
  /** 中奖记录 */
  RewardObjectList rewards = 4;
  /** key:id,value:当天购买数量 */
  map<int32, int32> dayBuyTimes = 5;
  /** key:id,value:累计购买数量 */
  map<int32, int32> buyTimes = 6;
  /** key:id,value:累计已领奖励数量 */
  map<int32, int32> receives = 7;
  /** key:day,value:当天的累计充值金额（分） */
  map<int32, int64> rechargeMap = 8;
  /** 累计充值 领取奖励ID列表 */
  repeated int32 totalRechargeReceives = 9;
  /** 登录有礼 key:天 value:领取状态 0.未领取 1.已领取 */
  map<int32, int32> loginMap = 10;
  /** 今日累计充值X元可以领取状态 true:已领取 */
  bool loginRechargeRewards = 11;
  /** 今天充值金额 */
  int64 dayRecharge = 12;
  /** 累计充值金额 */
  int64 totalRecharge = 13;
  /** 任务已对换礼包configId */
  repeated int32 taskReceives = 14;
  /** 增值礼包购买记录 */
  repeated int32 buyReceives = 15;
  /** 奖励对换记录 key:id, value:num*/
  map<int32, int32> exchangeMap = 16;
  /** 活动第几天 */
  int32 day = 17;
}

message Activity33Response{
  /** 动作id */
  int32 id = 1;
  /** 额外参数 */
  /** 可变参数
    * id = 1 RewardObjectList
    * id = 2,3,5,6,7,8 RewardResult
    * id = 4 ActivityRankList
   */
  bytes value = 2;
}


/** activityType:34 天天充值活动响应 */
message Activity34Record {
  /** 充值第几天 */
  int32 rechargeDay = 1;
  /** 已领奖励ID */
  repeated int32 receives = 2;
  /** 今日奖励是否领取 false：未领取 true:已领取*/
  bool receiveDailyReward = 3;
  /** 今天是否已经购买礼包*/
  bool isBuy = 4;
}
/**
 * activityType:35 请求value
 */
message Activity35RequestValue {
  /**
   * id:
   * 1.团购狂欢领取 value:配置Id
   * 2.团购排行榜
   * 3.庆典兑换 value:配置Id
   * 4.获取福泽天下当前已经购买人数
   * 5.领取福泽天下奖励
   * 6.登陆有礼签到
   * 7.登陆有礼补领特权奖励 value:day
   * 8.盛典礼包领取 value:配置Id
   * 9.以酒会友豪爽排行榜
   * 10. 以酒会友情义排行榜
   * 11. 以酒会友送酒: value:服务器ID
   * 12. 庆典任务领取奖励 value:配置Id
   */
  int32 id = 1;
  /**
   * 1.团购狂欢领取 value：次数
   * 3.庆典兑换 value：次数
   * 11.以酒会友送酒 value：次数
   */
  int32 times = 2;
  /** 11. 以酒会友送酒: 角色ID */
  int64 targetId = 3;
  /** 11.以酒会友送酒: 角色名字*/
  string targetName = 4;
}
/**
 * activityType:35 活动集合
 */
message Activity35Record {
  /** 团购礼包活动内已经购买的次数*/
  int32 groupBuyTotalTimes = 1;
  /** 团购礼包已经购买次数 key:configId,value:times*/
  map<int32, int32> groupBuyTimes = 2;
  /** 团购礼包可以领取次数 key:configId,value:times*/
  map<int32, int32> groupReceiveTimes = 3;
  /** 庆典兑换次数 key:configId,value:times*/
  map<int32, int32> exchangeTimes = 4;
  /** 福泽天下奖励是否被领取*/
  bool receive = 5;
  /**登陆有礼玩家登陆到第几天 */
  int32 day = 6;
  /**登陆有礼 玩家今日有没有签到 */
  bool sign = 7;
  /**登陆有礼 玩家有没有激活特权*/
  bool activate = 8;
  /**登陆有礼 玩家特权奖励领取情况*/
  repeated int32 receives = 9;
  /** 庆典礼包已经购买次数 key:configId,value:times*/
  map<int32, int32> buyTimes = 10;
  /** 庆典礼包可以领取次数 key:configId,value:times*/
  map<int32, int32> receiveTimes = 11;
  /**已开启该活动的服务器列表（主服）*/
  repeated int32 serverId = 12;
  /**任务已领取领取列表*/
  repeated int32 score = 13;
  /**好友送给我的金兰酒次数 key:好友的actorId,value:times*/
  map<int64, int64> acceptTimes = 14;
  /** 活动开启第几天 */
  int32 openDay = 15;
}
message Activity35Response{
  /** 动作id */
  int32 id = 1;
  /** 额外参数 */
  /** 可变参数
    * id = 1,3,5,6,7,8,11,12 RewardResult
    * id = 2,9,10 ActivityRankList
    * id = 4 LongPacket
    * id = 12 Activity35ResponseValue
   */
  bytes value = 2;
}
message Activity35ResponseValue{
  /** 被赠送者名字*/
  string targetName = 1;
  /** 赠送次数*/
  int32 times = 2;
  /** 豪爽值*/
  int32 send = 3;
  /** 情义值*/
  int32 receive = 4;
}

/**
 * activityType:36 请求value
 */
message Activity36RequestValue {
  /**
   * id:
   * 1.登陆有礼签到
   * 2.登陆有礼补领特权奖励 value:day
   */
  int32 id = 1;
}
/**
 * activityType:36 登录有礼
 */
message Activity36Record {
  /**登陆有礼玩家登陆到第几天 */
  int32 day = 1;
  /**登陆有礼 玩家今日有没有签到 */
  bool sign = 2;
  /**登陆有礼 玩家有没有激活特权*/
  bool activate = 3;
  /**登陆有礼 玩家特权奖励领取情况*/
  repeated int32 receives = 4;
  /** 活动开启第几天 */
  int32 openDay = 5;
}

/** 37-天赐神将-排行榜 */
message Activity37ResponseValue{
  ActivityRankList rank = 1;
}

/** activityType:50 战力达标 */
message Activity50Record {
  /** 已领奖配置ID列表 */
  repeated int32 receives = 1;
}

/** activityType:51 群雄逐鹿（跨服版）活动响应 */
message Activity51Record {
  /** key:type,value:target */
  map<int32, int64> target = 1;
  /** 达标奖励领取ID */
  repeated int32 receives = 2;
}

/** activityType:51 群雄逐鹿（跨服版）*/
message Activity51Global {
  /** key:type, value:List<ActivityRank>*/
  map<int32, Activity51RankList> rank = 1;
}

message Activity51RankList{
  repeated ActivityRank rank = 1;
}

/**
 * activityType:53星布棋局 请求value
 */
message Activity53RequestValue {
  /**
  * ActivityActionRequest.id:
  * 1.(1-星布棋局)选择大奖
  * 2.(1-星布棋局)落子抽奖 id:旗帜位置ID
  * 3.(1-星布棋局)进入下一局
  * 4.(2-神树祈福)神树领奖
  * 5.(2-神树祈福)神树刷新所需道具兑换 id:兑换次数
  * 6.(2-神树祈福)神树奖励刷新 ResponseValue:{KeyValuePacket}返回值 key：实际加的积分，id:奖励id（用于前端播特效）
  * 7.(3-祈福求签)单次抽签
  * 8.(3-祈福求签)元宝购买积分（签），购买一次加1积分 id:购买次数
  * 9.(4-直购礼包)-直购礼包领取 id:直购礼包配置ID
  * 10.(4-直购礼包)累充充值数兑换物品
  * 11.(5-棋局排行)获取排行榜 ResponseValue{ActivityRankList}
  * 12.(6-连续充值) id:Activity53TotalChargeConfig.id
  * 13.(7-7日任务)任务积分兑换领取 id:Activity53ScoreRewardsConfig.id
  */
  int32 id = 1;
  /**
  * 1.(1-星布棋局）选择大奖 map:(局,位置)
  * 4.(2-神树祈福) 神树领奖 map(type,day)
  * 6.(2-神树祈福) 神树奖励刷新 map(type,day)
  */
  map<int32, int32> value = 2;
}

/** activityType:53 神树祈福 */
message Activity53Record {
  /** 天数 */
  int32 day = 1;
  /** 星布棋局抽取次数 */
  int32 times = 2;
  /** 中奖记录 **/
  RewardObjectList rewardList = 3;
  /** 星布棋局 轮的信息 */
  Activity53Round activity53Round = 4;
  /** 星布棋局 本局抽取信息 */
  Activity53CurrentFloor activity53CurrentFloor = 5;
  /** 神树求签信息 */
  Activity53GodTree activity53GodTree = 6;
  /** 充值礼包充值次数 key:礼包id value:次数 */
  map<int32, int32> chargeTimes = 7;
  /** 充值礼包领取次数 */
  map<int32, int32> chargeReward = 8;
  /** 连续累充 活动天数的充值数 key : 活动开放第几天 value ： 充值数（单位分）*/
  map<int32, int64> chargeDay = 9;
  /** 连续累充 领取奖励列表 Activity53TotalChargeConfig.id*/
  repeated int32 chargeDayReward = 10;
  /** 任务奖励领取列表 Activity53TaskScoreConfig.id*/
  repeated int32 taskRewardIds = 11;
  /**活动期间累计已兑换充值数*/
  int64 converted = 12;
}

/** 星布棋局-轮的信息 */
message Activity53Round {
  /** 本轮抽取次数 */
  int32 times = 1;
  /** 本轮已抽中的物品（不含大奖） key:物品id value:次数*/
  map<int32, int32> roundReceives = 2;
  /** 每一局选择的大奖位置 key:局,value:奖励ID*/
  map<int32, int32> bigRewardSelected = 3;
  /** 棋局大奖配置 key:局,value:Activity53BigRewardConfig*/
  map<int32, Activity53BigRewardConfig> bigRewardConfig = 4;
  /** 每一局出大奖在第几次 key:局,value:第几次*/
  map<int32, int32> bigRewardTimes = 5;
}
/** 星布棋局 大奖配置转化 */
message Activity53BigRewardConfig {
  /** 棋局：第几局 */
  int32 floor = 1;
  /** 奖励组Id */
  int32 group = 2;
  /** 1.本局次数 2.当抽取次数到达本局次数，直接抽中大奖 */
  int32 count = 3;
  /** 本轮多少抽开始加入奖池 */
  int32 limit = 4;
  /** 权值 万分比 */
  int32 rate = 5;
  /**  奖励组 {奖励id, [[type,id,count],[type,id,count]]} */
  map<int32, RewardObjectList> groupReward = 6;
  /** 棋盘Id */
  int32 chessboardId = 7;
}
/** 本局抽取信息 */
message Activity53CurrentFloor {
  /** 奖池对应棋局 对应大奖配置中floor */
  int32 floor = 1;
  /** 本局抽取次数 */
  int32 times = 2;
  /** 本局已获得的奖励 key:位置 value:奖励 */
  map<int32, RewardObjectList> reward = 3;
  /** 本局抽中大奖并且大奖位置 (含义棋子位置，-1：表示未抽中，其他：棋子位置并抽中大奖) */
  int32 bigRewardIndex = 4;
}

/** 星布棋局 神树求签 */
message Activity53GodTree {
  /** 神树求签 总共获得积分 */
  int32 totalScore = 1;
  /** 神树求签 使用积分 */
  int32 useScore = 2;
  /** 已领奖的天数 */
  map<int32, IntListPacket> receivedDay = 3;
  /** 刷新出来的奖励 */
  map<int32, Activity53GodTreeRewardList> refreshedReward = 4;
  /** 祈福求签已抽到的奖励id */
  repeated int32 godTreeSignReceives = 5;
  /** 元宝购买签次数 */
  int32 exchangeSignNum = 6;
}

/** 刷新出来的奖励信息 key:day value:奖励*/
message Activity53GodTreeRewardList{
  map<int32, RewardObjectList> refreshedReward = 1;
}

/** activityType:54 任务战令请求 */
message Activity54RequestValue {
  /**
   * id:
   * 1.一键领取
   * 2.购买经验 value:购买几级
   * 3.普通战令领取 value:等级
   * 4.高级战令领取 value:等级
   */
  int32 value = 1;
}

/** activityType:54 任务战令 */
message Activity54Record {
  /** 是否激活高级战令 */
  bool isActivate = 1;
  /** 普通战令已领取列表 */
  repeated int32 normalReceives = 2;
  /** 高级战令已领取列表 */
  repeated int32 supremeReceives = 3;
  /** 满级后-累计获得盈余奖励数量 */
  int32 receivesTimes = 4;
}

/** activityType:55 合战锦标赛 请求*/
message Activity55RequestValue{
  /**
   * id:
   * 1.获取总排行榜(Activity55RankList放在ActivityActionResponse.value里)
   * 2.领取战令普通奖励(value->activity_55_order_config.day)
   * 3.领取战令高级奖励(value->activity_55_order_config.day)
   */
  int32 value = 1;
}

/** activityType:55 合战锦标赛 个人数据 */
message Activity55Record{
  /** 活动第几天 */
  int32 day = 1;
  /** 挑战总次数 */
  int32 times = 2;
  /**是否激活战令*/
  bool isActive=3;
  /** 普通战令已领取奖励ID列表 [activity_55_order_config.day] */
  IntListPacket normalReceives = 4;
  /** 高级战令已领取列表 [activity_55_order_config.day]*/
  IntListPacket supremeReceives = 5;
  /**最高难度*/
  int32 difficulty=6;
  /**最高难度下最小击杀耗时(ms)*/
  int32 time=7;
}

/** activityType:55 合战锦标赛 排名列表 */
message Activity55RankList {
  repeated Activity55Rank ranks = 1;
}

/** activityType:55 合战锦标赛 排名 */
message Activity55Rank {
  /** 角色属性列表 */
  repeated ActorProfile actorProfiles = 1;
  /** 排名 */
  int64 rank = 2;
  /**  战力/关卡ID */
  int32 difficulty = 3;
  /** 击杀耗时ms*/
  int64 time = 4;
}


/** activityType:56 枪出如龙 */
message Activity56RequestValue {
  /**
   * ActivityActionRequest.id:
   * 1.每日签到 id:天数
   * 2.神将招募-招募 id:招募次数
   * 3.神将招募-选择心愿武将 id:heroConfigId
   * 4.神将招募-神将排行榜
   * 5.神将试炼 id:Activity56HeroChallengeConfig.id
   * 6.代币商店 id:Activity56TokenStoreConfig.id
   * 7.礼包商店 id:Activity56GiftStoreConfig.id
   */
  int32 id = 1;
  /**
   * 6.代币商店 times:数量
   */
  int32 times = 2;
}

/** activityType:56 枪出如龙 */
message Activity56Record{
  /** 每日签到-活动签到天数 */
  int32 signInDay = 1;
  /** 每日签到-已领奖天数列表 */
  repeated int32 dailyReceives = 2;
  /** 神将招募-心愿武将 */
  int32 heroId = 3;
  /** 神将招募-今日招募次数 */
  int32 todayTimes = 4;
  /** 神将招募-招募次数 */
  int32 totalTimes = 5;
  /** 神将招募-抽奖记录 */
  RewardObjectList rewards = 6;
  /** 神将招募-本此活动累计招募次数 */
  int32 gachaCount = 7;
  /** 神将试炼-已领取列表 */
  repeated int32 receiveList = 8;
  /** 代币商店-key:id,value:购买数量 */
  map<int32, int32> tokenBuyTimes = 9;
  /** 礼包商店-key:id,value:当天购买数量 */
  map<int32, int32> dayBuyTimes = 10;
  /** 礼包商店-key:id,value:累计购买数量 */
  map<int32, int32> buyTimes = 11;
  /** 礼包商店-key:id,value:累计已领奖励数量 */
  map<int32, int32> receives = 12;
  /** 活动第几天 */
  int32 day = 13;
}

/** activityType:56-枪出如龙-排行榜 */
message Activity56ResponseValue{
  ActivityRankList rank = 1;
}


/** activityType:61 活动61-每日首充活动响应 */
message Activity61Record {
  /** 当天已累计充值额度(单位:分) */
  int64 dailyCharge = 1;
  /** Activity61PreferentialGiftConfig.id */
  repeated int32 buyGiftIds = 2;
  /** Activity61PreferentialGiftConfig.id */
  repeated int32 receiveIds = 3;
  /** 已领取的累充奖励 activity_61_charge_rewards_config.charge */
  repeated int32 receiveCharge = 4;
}


/** 活动63请求 */
message Activity63RequestValue{
  /** 商品id*/
  int32 id = 1;
  /** 购买次数 */
  int32 times = 2;
}

/** 活动63商店信息 */
message Activity63Record{
  /** 已购买道具次数map,key:ActivityStoreConfig->id,value:times */
  map<int32, int32> buyGoods = 1;
}

/** 活动65周卡信息 */
message Activity65Record{
  /** 周卡激活状态 0:未激活，1:激活，2:已领取*/
  int32 rewardStatus = 1;
}

/** activityType:67 充值-尊享礼包活动响应 */
message Activity67Record {
  /** key:id,value:每日购买数量 */
  map<int32, int32> dailyBuyTimes = 1;
  /** key:id,value:购买数量 */
  map<int32, int32> buyTimes = 2;
  /** key:id,value:已领奖励数量 */
  map<int32, int32> receives = 3;
}

/** activityType:68 心愿招募-招募活动请求 */
message Activity68RequestValue {
  /** 1、选择大奖 2、招募 3、中奖信息 4、排行榜 5、心愿礼物领取 6、心愿助力礼包领取 7、心愿任务奖励领取*/
  int32 typeId = 1;
  /** 2的类型（0免费，1招募令，2元宝） */
  int32 type = 2;
  /**对应typeId ： 2的招募次数 4的排行数  6的礼包id 7的礼包ID*/
  int32 num = 3;
}

/** activityType:68 心愿招募-招募活动响应 **/
message Activity68RecordResponse {
  /** 行为ID **/
  int32 typeId = 1;
  /** 响应结果 **/
  bytes value = 2;
}

/** activityType:68 心愿招募-活动记录 */
message Activity68Record {
  /** 选择的心愿大奖ID */
  int32 selectId = 1;
  /** 心愿大奖抽奖次数  如若概率/必中 抽中心愿大奖，则清0 */
  int32 bigRewardGachaTimes = 2;
  /** 今日使用元宝已招募次数 */
  int32 diamondGachaTimes = 3;
  /** 已使用免费招募次数 */
  bool usedFreeTime =4;
  /** 心愿礼物 */
  Activity68GiftEntity giftEntity = 5;
  /**  心愿助力 key:Activity68HelpRewardConfig.id   value: 已购次数 */
  map<int32, int32> helpRewardBuyTimes = 6;
  /**  心愿助力 key:Activity68HelpRewardConfig.id   value: 已领取次数 */
  map<int32, int32> helpRewardReceiveTimes = 7;
  /** 心愿助力 今日购买次数 key:Activity68HelpRewardConfig.id value:购买次数 */
  map<int32, int32>  helpRewardDayBuyTimes = 8;
  /** 活动开启第几天 **/
  int32 day = 9;
}

/** activityType:68 心愿招募-活动记录 心愿礼物 */
message Activity68GiftEntity {
  /**  是否激活高级战令 */
  bool isActive =1;
  /** 普通战令已领取列表  */
  repeated int32 normalReceiveList =2;
  /** 高级战令已领取列表 */
  repeated int32 supremeReceiveList =3;
}

/** activityType:68 心愿招募-活动记录 排行榜 */
message Activity68Global {
  /** 1、积分排行 */
  repeated ActivityRank activity2068RankList = 1;
  /** 2.中奖播报列表 */
  repeated Activity68ReportEntity reportList = 2;
}

/** activityType:68 心愿招募-活动记录 心愿礼物 */
message Activity68ReportEntity{
  /** 角色属性 */
  ActorProfile actorProfile = 1;
  /** 奖励 */
  RewardObjectList rewardList = 2;
  /** 中奖描述 **/
  string des = 3;
}


/** activityType:69 宝藏探秘-活动请求 */
message Activity69RequestValue {
  /** id （1、宝藏探秘 2、福运宝地 3、幸运宝匣 4、礼包兑换 5、进度奖励）*/
  int32 typeId = 1;
  /**对应typeId ： 1探秘次数 3钥匙孔位置（1-8）*/
  int32 num = 3;
}

/** activityType:69 宝藏探秘 */
message Activity69Record{
  /** 宝藏探秘-圈 */
  int32 adventureCircle = 1;
  /** 宝藏探秘-格子 */
  int32 adventureCell = 2;
  /** 宝藏探秘-格子随机奖励{key:格子,value:RewardObjectList} */
  map<int32, RewardObjectList> adventureCellRewardMap = 3;
  /** 福运宝地-抽取次数 */
  repeated int32 fortunePositionId = 4;
  /** 幸运宝匣-1 */
  repeated int32 luckyBox1 = 5;
  /** 幸运宝匣-2 */
  repeated int32 luckyBox2 = 6;
  /** 幸运宝匣-3 */
  repeated int32 luckyBox3 = 7;
  /** 礼包商店-key:id,value:当天购买数量 */
  map<int32, int32> dayBuyTimes = 8;
  /** 礼包商店-key:id,value:累计购买数量 */
  map<int32, int32> buyTimes = 9;
  /** 礼包商店-key:id,value:累计已领奖励数量 */
  map<int32, int32> receives = 10;
  /** 已领取进度奖励 */
  repeated int32 progressReward = 11;
  /** 幸运宝匣开启次数 */
  int32 luckyBoxOpenTimes = 12;
}

/** activityType:70 明灯祈福签到 */
message Activity70Record{
  /** 领取天数 */
  repeated int32 receives = 1;
  /** 登录天数 */
  int32 loginDays = 2;
}

/** activityType:71 宝藏探秘-活动请求 */
message Activity71RequestValue {
  /** 1、普通战令 2、中等战令 3、高级战令 */
  int32 typeId = 1;
}

/** activityType:71 战令佳赏 */
message Activity71Record{
  /** 活动第几天 */
  int32 days = 1;
  /** 是否激活中级战令 */
  bool isActiveMedium = 2;
  /** 是否激活高级战令 */
  bool isActiveSupreme = 3;
  /** 普通战令已领取奖励ID列表 [activity_71_order_config.days] */
  repeated int32 normalReceives = 4;
  /** 中级战令已领取列表 [activity_71_order_config.days] */
  repeated int32 mediumReceives = 5;
  /** 高级战令已领取列表 [activity_71_order_config.days] */
  repeated int32 supremeReceives = 6;
}

/** activityType:72 传送带礼包 */
message Activity72Record{
  /** 普通战令已领取奖励ID列表 [activity_71_order_config.days] */
  repeated int32 receives = 1;
  /** 中级战令已领取列表 [activity_71_order_config.days] */
  repeated int32 chargeConfigOrderIds = 2;
}

/** activityType:73 小游戏-活动请求 id:1挑战 2领取奖励 */
message Activity73RequestValue {
  /** 难度 */
  int32 difficulty = 1;
  /** 进度点 */
  int32 point = 2;
  /** 购买次数 */
  int32 buyTimes = 3;
  /** 准备时间 */
  int64 prepareTime = 4;
}

/** activityType:73 小游戏 */
message Activity73Record{
  /** 活动第几天 */
  int32 days = 1;
  /** 可用挑战次数 */
  int32 challengeTimes = 2;
  /** 购买挑战次数 */
  int32 buyTimes = 3;
  /** 已领取礼包列表 */
  map<int32, IntListPacket> receiveMap = 4;
  /** 游戏进度 */
  map<int32, int32> progressMap = 5;
  /** 准备时间 */
  int64 prepareTime = 6;
}

/** activityType:78 霓裳罗衣-活动请求  */
message Activity78RequestValue {
  /** typeId:1.抽奖 2.选择皮肤 3.领取进度奖励 4.礼包兑换 5.霓裳秘宝购买 */
  int32 typeId = 1;
  /** 5.秘宝兑换数量 */
  int32 num = 2;
}

message Activity78Record{
  /** 1.幸运值 */
  int32 luckyValue = 1;
  /** 2.选择英雄皮肤 */
  int32 pickSkin = 2;
  /** 3.进度值 */
  int32 progressValue = 3;
  /** 3.已领取奖励列表 */
  repeated int32 progressReceives = 4;
  /** 4.礼包商店-key:id,value:当天购买数量 */
  map<int32, int32> dayBuyTimes = 5;
  /** 4.礼包商店-key:id,value:累计购买数量 */
  map<int32, int32> buyTimes = 6;
  /** 4.礼包商店-key:id,value:累计已领奖励数量 */
  map<int32, int32> receives = 7;
  /** 5.霓裳秘宝，购买记录 */
  map<int32, int32> shopBuyMap = 8;
}