syntax = "proto3";
option java_package = "cn.daxiang.protocol.game";
option optimize_for = SPEED;

import "game/commonProtocol.proto";
import "game/immortalsProtocol.proto";
import "game/spacetimeBeautyProtocol.proto";
/** 用户 */
enum LineupCmd {
  LINEUP_CMD_NONE = 0;
  /**
     * 获取阵容列表
     * 请求:{@code Request}
     * 响应:{@code LineupListResponse}
     */
  GET_LINEUP_LIST = 1;
  /**
     * 加入阵容
     * 请求:{@code JoinLineupRequest}
     * 响应:{@code Response}
     */
  JOIN_LINEUP = 2;
  /**
     * 改变位置
     * 请求:{@code ChangeLineupPositionRequest}
     * 响应:{@code Response}
     */
  CHANGE_POSITION = 3;
  /**
  * 查询阵容显示对象
  * 请求:{@code QueryLineupRequest}
  * 响应:{@code QueryLineupResponse}
  */
  QUERY_LINE_UP = 4;
  /**
  * 查询简单阵容显示对象
  * 请求:{@code QueryLineupRequest}
  * 响应:{@code QuerySimpleLineupResponse}
  */
  QUERY_SIMPLE_LINE_UP = 5;
  /**
  * 英雄战力排行
  * 请求:{@code IntPacket} page
  * 响应:{@code HeroRankResponse}
  */
  HERO_RANK = 6;
  /**
   * 更换装备
   * 请求:{@code ChangeEquipmentRequest}
   * 响应:{@code RewardResultResponse}
   */
  CHANGE_EQUIPMENT = 7;
  /**
  * 一键穿戴装备
  * 请求:{@code QuicklyEquipmentRequest}
  * 响应:{@code Response}
  */
  EQUIPMENT_QUICKLY_WEAR = 8;
  /**
   * 卸下装备
   * 请求:{@code DischargeEquipmentRequest}
   * 响应:{@code Response}
   */
  DISCHARGE_EQUIPMENT = 9;
  /**
  * 一键卸装
  * 请求:{@code QuicklyEquipmentRequest}
  * 响应:{@code Response}
  */
  EQUIPMENT_QUICKLY_REMOVE = 10;
  /**
  * 更换宝物
  * 请求:{@code ChangeTreasureRequest}
  * 响应:{@code RewardResultResponse}
  */
  CHANGE_TREASURE = 11;
  /**
   * 卸下宝物
   * 请求:{@code ChangeTreasureRequest}
   * 响应:{@code Response}
   */
  DISCHARGE_TREASURE = 12;
  /**
   * 重生
   * <pre>
   * 请求:{@code RebirthRequest}
   * 响应:{@code RewardResultResponse}
   * </pre>
   */
  REBIRTH = 14;
  /**
   * 重生预览
   * <pre>
   * 请求:{@code RebirthRequest}
   * 响应:{@code RewardObjectList}
   * </pre>
   */
  REBIRTH_PREVIEW = 15;
  /**
  * 加入塔防共鸣
  * <pre>
  * 请求:{@code LongIntPacket} intValue:传要加入共鸣的orderId longValue:英雄的Id
  * 响应:{@code Response}
  * </pre>
  */
  JOIN_TD_RESONANCE = 16;
  /**
  * 退出塔防共鸣
  * <pre>
  * 请求:{@code IntPacket}传要退出共鸣的orderId
  * 响应:{@code Response}
  * </pre>
  */
  EXIT_TD_RESONANCE = 17;
  /**
    * 更换专武
    * 请求:{@code ChangeImmortalsRequest}
    * 响应:{@code Response}
    */
  CHANGE_IMMORTALS = 18;
  /**
   * 锁定专武
   * 请求:{@code ChangeImmortalsRequest}
   * 响应:{@code Response}
   */
  LOCK_IMMORTALS = 19;
  /**
	* 20.装备排行榜
	* 请求:{@code IntPacket} 传page
	* 响应:{@code EquipmentRankResponse}
	*/
  EQUIPMENT_RANK = 20;
  /**
	* 21.宝物排行榜
	* 请求:{@code IntPacket} 传page
	* 响应:{@code TreasureRankResponse}
	*/
  TREASURE_RANK = 21;
  /**
  * 更换武魂
  * 请求:{@code ChangeSoulRequest}
  * 响应:{@code Response}
  */
  CHANGE_SOUL = 22;
  /**
   * 卸下武魂
   * 请求:{@code DischargeSoulRequest}
   * 响应:{@code Response}
   */
  DISCHARGE_SOUL = 23;
  /**
   * 神兽上阵
   * 请求:{@code KeyValuePacket} key-神兽Id value-塔座序号
   * 响应:{@code Response}
   * 推送:{@code LineupListResponse}
   */
  BEAST_JOIN_LINEUP = 24;
  /**
   * 神兽共鸣上阵
   * 请求:{@code KeyValuePacket} key-神兽Id value-共鸣位置序号
   * 响应:{@code Response}
   * 推送:{@code LineupListResponse}
   */
  BEAST_JOIN_RESONANCE = 25;
  /**
  * 英雄战力战区排行
  * 请求:{@code IntPacket} page
  * 响应:{@code HeroRankResponse}
  */
  HERO_ZONE_RANK = 26;
  /**
   * 时空红颜上阵
   * 请求:{@code BeautyJoinLineupRequest}
   * 响应:{@code Response}
   * 推送:{@code LineupListResponse}
   */
  BEAUTY_JOIN_LINEUP = 27;
  /**
   * 更换神器
   * 请求:{@code KeyValuePacket} key-阵容序号ID value-神器Id
   * 响应:{@code Response}
   * 推送:{@code LineupListResponse}
   */
  CHANGE_HALLOWS = 28;
  /**
   * 更换神兵
   * 请求:{@code ChangeFrostmourneRequest}
   * 响应:{@code Response}
   * 推送:{@code LineupListResponse}
   */
  CHANGE_FROSTMOURNE = 29;
  /**
   * 推送阵容列表
   * 推送:{@code LineupListResponse}
   */
  PUSH_LINEUP_LIST = 100;

}

message SoulLineupEntity {
  /** key:orderId value:武魂唯一ID*/
  map<int32, int64> souls = 1;
}

/** 阵容信息*/
message LineupEntity {
  /** 阵容序号ID */
  int32 orderId = 1;
  /** 阵容位置ID */
  int32 positionId = 2;
  /** 英雄ID */
  int64 heroId = 3;
  /** 装备Map key:equipmentType,value:equipmentId */
  map<int32, int64> equipments = 4;
  /** 宝物Map key:TreasureType,value:treasureId */
  map<int32, int64> treasures = 5;
  /** 专武剑胚Id */
  int64 immortalsId = 6;
  /** 战斗力 */
  int64 power = 7;
  /** key:武魂类型，value:武魂阵容排列*/
  map<int32, SoulLineupEntity> soulLineup = 8;
  /** 专武神锻等级*/
  int32 nirvanaLevel = 9;
  /** 专武是否上锁*/
  bool immortalsLock = 10;
  /** 神器Id */
  int32 hallowsId = 11;
  /**神兵Id*/
  int32 frostmourneId = 12;
}
/** 神兽信息 */
message BeastEntity{
  /** 阵容序号(-塔座位置 -共鸣位置序号) */
  int32 orderId = 1;
  /** 神兽配置Id */
  int32 beastId = 2;
  /** 战斗力 */
  int64 power = 3;
}

/** 时空红颜信息 */
message SpacetimeBeautyEntity{
  /** 阵容序号Id(同武将) */
  int32 orderId = 1;
  /** 时空红颜配置Id */
  int64 configId = 2;
}

/** 阵容列表响应 */
message LineupListResponse {
  /** 阵容列表 */
  repeated LineupEntity lineupList = 1;
  /** 塔防共鸣英雄Map ：key:orderId value:heroId */
  map<int32, int64> TD_Resonances = 2;
  /** 神兽列表 */
  repeated BeastEntity beastList = 3;
  /** 共鸣神兽列表 */
  repeated BeastEntity resonanceBeastList = 4;
  /** 时空红颜列表 */
  repeated SpacetimeBeautyEntity beautyList = 5;
}

/** 加入阵容请求 */
message JoinLineupRequest {
  /**  阵容序号ID */
  int32 orderId = 1;
  /** 英雄ID */
  int64 heroId = 2;
  /** 是否继承*/
  bool isInherit = 3;
}

/** 时空红颜加入阵容请求 */
message BeautyJoinLineupRequest {
  /**  阵容序号ID */
  int32 orderId = 1;
  /** 时空红颜配置Id 0-下阵*/
  int32 configId = 2;
  /** 是否继承*/
  bool isInherit = 3;
}

/** 改变阵容顺序请求 */
message ChangeLineupPositionRequest{
  /** 我的位置ID */
  int32 myPositionId = 1;
  /** 改变的位置ID */
  int32 changePositionId = 2;
}
/** 更换装备请求 */
message ChangeEquipmentRequest{
  /** 阵容序号ID */
  int32 orderId = 1;
  /** 装备类型EquipmentType */
  int32 type = 2;
  /** 装备ID */
  int64 equipmentId = 3;
  /** 是否继承*/
  bool isInherit = 4;
}
/** 一键装备请求 */
message QuicklyEquipmentRequest{
  /** 阵容序号ID */
  int32 orderId = 1;
}
/**卸下装备请求 */
message DischargeEquipmentRequest{
  /** 阵容序号ID */
  int32 orderId = 1;
  /** 装备类型EquipmentType */
  int32 type = 2;
}
/** 更换宝物请求 */
message ChangeTreasureRequest{
  /** 阵容序号ID */
  int32 orderId = 1;
  /** 宝物类型TreasureType */
  int32 type = 2;
  /** 宝物ID */
  int64 treasureId = 3;
  /** 是否继承*/
  bool isInherit = 4;
}
/** 查询阵容请求 */
message QueryLineupRequest{
  /** 服务器ID */
  int32 serverId = 1;
  /** 角色ID */
  int64 actorId = 2;
}

message SoulLineup {
  map<int32, Soul> souls = 1;
}
message SoulLineupMap {
  /**key:soulType,value:SoulLineup*/
  map<int32, SoulLineup>  soulLineup = 1;
}
message Lineup {
  /**
   * 阵容序号ID
   */
  int32 orderId = 1;
  /**
   * 佣兵
   */
  Hero hero = 2;
  /**
   * 装备Map key:equipmentType,value:Equipment
   */
  map<int32, Equipment> equipments = 3;
  /**
   * 宝物Map key:TreasureType,value:Treasure
   */
  map<int32, Treasure> treasures = 4;
  /**
   * 战力
   */
  int64 power = 5;
  /** 
   * 专武剑胚
   */
  ImmortalsEntity immortals = 6;
  /**
   * 武魂
   */
  map<int32, SoulLineup> souls = 7;
  /**
   * 时空红颜信息
   */
  SpacetimeBeautyInfo spacetimeBeautyInfo = 8;
  /**
   * 神器key:神器id，value:神器等级
   */
  map<int32, int32> hallows = 9;
  /**
   * 神兵
   */
  Frostmourne frostmourne = 10;
}

/** 查询阵容显示对象 */
message QueryLineupResponse {
  /** 阵容列 */
  repeated Lineup lineupList = 1;
  /** 关卡ID */
  int32 storyId = 2;
  /** 专武皮肤激活信息*/
  repeated ImmortalsSkin immortalsSkin = 3;
  /** 开服天数 */
  int32 serverOpenDay = 4;
}

message ImmortalsSkin {
  /**专武皮肤Id*/
  int32 immortalsSkinId = 1;
  /** 星级*/
  int32 star = 2;
}

/** 阵容简单显示对象 */
message LineupSimple{
  /** 阵容序号ID */
  int32 orderId = 1;
  /** 英雄配置ID */
  int32 roleId = 2;
  /** 阵容位置ID */
  int32 positionId = 3;
  /** 英雄皮肤ID */
  int32 skinId = 4;
  /** 英雄幻彩等级 */
  int32 colorLevel = 5;
}

/** 查询简单阵容响应 */
message QuerySimpleLineupResponse{
  /** 阵容列表 */
  repeated LineupSimple lineupList = 1;
}

/** 英雄战力排行响应 */
message HeroRankResponse{
  /** 排名列表 */
  repeated HeroRank rank = 1;
  /** 当前页数 */
  int32 page = 2;
  /** 页数 */
  int32 pages = 3;
  /**
 * 被点赞数量
 */
  int64 praiseCount = 4;
}

/** 英雄战力排行显示对象 */
message HeroRank {
  /** 角色属性 */
  ActorProfile actorProfile = 1;
  /** 排名 */
  int64 rank = 2;
  /** 配置ID */
  int32 id = 3;
  /** 英雄等级 */
  int32 level = 4;
  /** 英雄战力 */
  int64 power = 5;
  /** 英雄进阶等级 */
  int32 breakoutLevel = 6;
  /** 英雄星级 */
  int32 starLevel = 7;
  /** 英雄皮肤ID */
  int32 skinId = 8;
  /** 英雄幻彩等级 */
  int32 colorLevel = 9;
}

message RebirthRequest {
  /**
  * 重生对象id
  */
  repeated int64 rebirthIds = 1;

  /**
   * 类型
   * SmeltRebirthType
   */
  int32 rebirthType = 2;
}

message ChangeImmortalsRequest {
  /** 阵容序号ID */
  int32 orderId = 1;
  /** 专武ID */
  int64 immortalsId = 2;
}

message ChangeSoulRequest {
  /** 阵容序号ID */
  int32 orderId = 1;
  /** 武魂类型*/
  int32 soulType = 2;
  /** 武魂上阵id*/
  int32 soulOrderId = 3;
  /** 武魂唯一ID*/
  int64 soulId = 4;
}

message DischargeSoulRequest {
  /** 阵容序号ID */
  int32 orderId = 1;
  /** 武魂类型*/
  int32 soulType = 2;
  /** 武魂下阵id*/
  int32 soulOrderId = 3;
}

/** 装备排行榜响应 */
message EquipmentRankResponse {
  /**  排名列表  */
  repeated EquipmentRankVO ranks = 1;
  /** 当前页数 */
  int32 page = 2;
  /** 页数 */
  int32 pages = 3;
  /** 被点赞数量 */
  int64 praiseCount = 4;
}
/** 装备排行显示对象 */
message EquipmentRankVO {
  /** 角色属性 */
  ActorProfile actorProfile = 1;
  /** 排名 */
  int64 rank = 2;
  /** 装备总分 */
  int64 score = 3;
}

/** 宝物排行榜响应 */
message TreasureRankResponse {
  /**  排名列表  */
  repeated TreasureRankVO ranks = 1;
  /** 当前页数 */
  int32 page = 2;
  /** 页数 */
  int32 pages = 3;
  /** 被点赞数量 */
  int64 praiseCount = 4;
}
/** 宝物排行显示对象 */
message TreasureRankVO {
  /** 角色属性 */
  ActorProfile actorProfile = 1;
  /** 排名 */
  int64 rank = 2;
  /** 宝物总分 */
  int64 score = 3;
}
/** 更换神兵请求 */
message ChangeFrostmourneRequest {
  /** 阵容序号ID */
  int32 orderId = 1;
  /** 神兵Id */
  int32 configId = 2;
  /** 是否继承*/
  bool isInherit = 3;
}