package cn.daxiang.hbtd.gameserver.core.dataconfig.service;

import cn.daxiang.framework.dataconfig.ConfigServiceAdapter;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.Activity66DailyTaskConfig;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Collections;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/12/27
 */
@Component
public class Activity66ConfigService extends ConfigServiceAdapter {

    /**
     * 每日任务 key:data value:taskIdList
     */
    private static final Map<Integer, Collection<Integer>> ACTIVITY_66_DAY_TASK_CONFIG_MAP = Maps.newHashMap();
    /**
     * 所有任务：key:data value:taskIdList
     */
    private static final Map<Integer, Collection<Integer>> ACTIVITY_66_TASK_CONFIG_MAP = Maps.newHashMap();

    @Override
    protected void initialize() {
        Collection<Activity66DailyTaskConfig> taskConfigs = dataConfig.listAll(this, Activity66DailyTaskConfig.class);
        for (Activity66DailyTaskConfig taskConfig : taskConfigs) {
            Collection<Integer> taskIdList = ACTIVITY_66_TASK_CONFIG_MAP.computeIfAbsent(taskConfig.getData(), k -> Sets.newHashSet());
            taskIdList.addAll(taskConfig.getTaskIdList());
            if (taskConfig.getType() == 1) {
                Collection<Integer> dayTaskIdList = ACTIVITY_66_DAY_TASK_CONFIG_MAP.computeIfAbsent(taskConfig.getData(), k -> Sets.newHashSet());
                dayTaskIdList.addAll(taskConfig.getTaskIdList());
            }
        }
    }

    @Override
    protected void clean() {
        ACTIVITY_66_TASK_CONFIG_MAP.clear();
        ACTIVITY_66_DAY_TASK_CONFIG_MAP.clear();
    }

    public static boolean isContainsTask(int data, int taskId) {
        Collection<Integer> taskIdList = ACTIVITY_66_TASK_CONFIG_MAP.getOrDefault(data, Collections.emptyList());
        return taskIdList.contains(taskId);
    }

    /**
     * 获取日常任务配置ID列表
     * @param data
     * @return
     */
    public static Collection<Integer> getDayTaskIdList(int data) {
        return ACTIVITY_66_DAY_TASK_CONFIG_MAP.get(data);
    }
}
