package cn.daxiang.hbtd.gameserver.core.dataconfig.service;

import cn.daxiang.framework.dataconfig.ConfigServiceAdapter;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.EquipmentGlyphsConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.EquipmentGlyphsResonateConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.EquipmentNewCastingConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.EquipmentUpgradeConfig;
import cn.daxiang.hbtd.gameserver.module.lineup.helper.LineupAttributeHelper;
import cn.daxiang.shared.module.lineup.SpriteAttributeType;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;
import java.util.Optional;
import java.util.TreeMap;

@Component
public class EquipmentConfigService extends ConfigServiceAdapter {

    private static Collection<EquipmentGlyphsResonateConfig> GLYPHS_RESONATE_CONFIG_LIST = Lists.newLinkedList();
    /**
     * key:configId,value:{key:castingLevel,value:EquipmentCastingConfig}
     */
    private static Map<Integer, TreeMap<Integer, EquipmentNewCastingConfig>> ID_CASTING_LEVEL_CONFIG_MAP = Maps.newHashMap();
    /**
     * key:configId,value:maxCastingLevel
     */
    private static Map<Integer, Integer> MAX_CASTING_LEVEL_MAP = Maps.newHashMap();
    /**
     * key:configId,value:{key:castingStage value: specialIds}
     */
    private static Map<Integer, TreeMap<Integer, Collection<Integer>>> CASTING_STAGE_SPECIAL_IDS_MAP = Maps.newHashMap();
    /**
     * key:configId,value:{key:castingStage value: AttributesMap} 记录的是到此阶段满的属性
     */
    private static Map<Integer, TreeMap<Integer, Map<SpriteAttributeType, Long>>> CASTING_STAGE_ATTRIBUTE_MAP = Maps.newHashMap();
    /**
     * key:configId,value:{key:castingStage value: specialAttributesMap} 记录的是到此阶段满的全体属性
     */
    private static Map<Integer, TreeMap<Integer, Map<SpriteAttributeType, Long>>> CASTING_STAGE_SPECIAL_ATTRIBUTE_MAP = Maps.newHashMap();
    /**
     * key:IdentiyKey,value:{key:glyphsLevel value: EquipmentGlyphsConfig}
     */
    private static Map<IdentiyKey, TreeMap<Integer, EquipmentGlyphsConfig>> EQUIPMENT_GLYPHS_CONFIG_MAP = Maps.newHashMap();
    /**
     * key:{@link EquipmentUpgradeConfig#getNewEquipId()}
     * value:{@link EquipmentUpgradeConfig}
     */
    private static Map<Integer, EquipmentUpgradeConfig> EQUIPMENT_UPGRADE_CONFIG_MAPS = Maps.newHashMap();

    public static EquipmentNewCastingConfig getEquipmentCastingConfigByStage(int configId, int stage) {
        Map<Integer, EquipmentNewCastingConfig> levelConfigMap = ID_CASTING_LEVEL_CONFIG_MAP.get(configId);
        if (levelConfigMap == null) {
            return null;
        }
        return levelConfigMap.get(stage);
    }

    /**
     * 获取装备雕纹共鸣激活的specialId
     *
     * @param glyphsLevels
     * @return
     */
    public static Collection<Integer> getGlyphsResonateList(Collection<Integer> glyphsLevels) {
        Collection<Integer> specialIdList = Lists.newArrayList();
        for (EquipmentGlyphsResonateConfig config : GLYPHS_RESONATE_CONFIG_LIST) {
            if (isConformGlyphsResonate(config.getLevel(), config.getSuitNum(), glyphsLevels)) {
                specialIdList.add(config.getSpecialEffectId());
            }
        }
        return specialIdList;
    }

    /**
     * 获取装备铸灵激活的specialId
     *
     * @param configId
     * @param castingStage
     * @return
     */
    public static Collection<Integer> getCastingSpecialEffectId(int configId, int castingStage, int castingNum) {
        Collection<Integer> specialIdList = Lists.newArrayList();
        TreeMap<Integer, Collection<Integer>> castingSpecialIdsMap = CASTING_STAGE_SPECIAL_IDS_MAP.get(configId);
        if (castingSpecialIdsMap == null) {
            return specialIdList;
        }

        Integer floorKey = castingSpecialIdsMap.floorKey(castingStage);
        if (floorKey == null) {
            return specialIdList;
        }
        EquipmentNewCastingConfig config = ID_CASTING_LEVEL_CONFIG_MAP.get(configId).get(castingStage);
        if (castingStage == floorKey && castingNum != config.getKeyLevelNum() + config.getLevelNum()) {
            floorKey = castingSpecialIdsMap.lowerKey(floorKey);
            if (floorKey == null) {
                return specialIdList;
            }
        }
        return castingSpecialIdsMap.get(floorKey);
    }

    /**
     * 获取铸灵基础属性
     *
     * @param configId
     * @param castingStage
     * @param castingNum
     * @return
     */
    public static Map<SpriteAttributeType, Long> getCastingAttributeMap(int configId, int castingStage, int castingNum) {
        Map<SpriteAttributeType, Long> attributeMap = Maps.newHashMap();
        TreeMap<Integer, Map<SpriteAttributeType, Long>> castAttributeMap = CASTING_STAGE_ATTRIBUTE_MAP.get(configId);
        if (castAttributeMap == null) {
            return attributeMap;
        }
        EquipmentNewCastingConfig config = ID_CASTING_LEVEL_CONFIG_MAP.get(configId).get(castingStage);
        if (castingNum > config.getLevelNum()) {
            castingNum = config.getLevelNum();
        }
        for (Map.Entry<SpriteAttributeType, Long> attributeEntry : config.getAttributeMap().entrySet()) {
            attributeMap.put(attributeEntry.getKey(), attributeEntry.getValue() * castingNum);
        }
        Integer lowerKey = castAttributeMap.lowerKey(castingStage);
        if (lowerKey != null) {
            LineupAttributeHelper.addSpriteAttributeValue(attributeMap, castAttributeMap.get(lowerKey));
        }
        return attributeMap;
    }

    /**
     * 获取铸灵全体属性
     *
     * @param configId
     * @param castingStage
     * @param castingNum
     * @return
     */
    public static Map<SpriteAttributeType, Long> getCastingSpecialAttributeMap(int configId, int castingStage, int castingNum) {
        Map<SpriteAttributeType, Long> specialAttributeMap = Maps.newHashMap();
        TreeMap<Integer, Map<SpriteAttributeType, Long>> castSpeacialAttributeMap = CASTING_STAGE_SPECIAL_ATTRIBUTE_MAP.get(configId);
        if (castSpeacialAttributeMap == null) {
            return specialAttributeMap;
        }
        EquipmentNewCastingConfig config = ID_CASTING_LEVEL_CONFIG_MAP.get(configId).get(castingStage);
        castingNum -= config.getLevelNum();
        if (castingNum > 0) {
            for (Map.Entry<SpriteAttributeType, Long> attributeEntry : config.getKeyAttributeMap().entrySet()) {
                specialAttributeMap.put(attributeEntry.getKey(), attributeEntry.getValue() * castingNum);
            }
        }
        Integer lowerKey = castSpeacialAttributeMap.lowerKey(castingStage);
        if (lowerKey != null) {
            LineupAttributeHelper.addSpriteAttributeValue(specialAttributeMap, castSpeacialAttributeMap.get(lowerKey));
        }
        return specialAttributeMap;
    }

    private static boolean isConformGlyphsResonate(int level, int suitNum, Collection<Integer> glyphsLevels) {
        int count = 0;
        for (int glyphsLevel : glyphsLevels) {
            if (glyphsLevel >= level) {
                count++;
            }
        }
        if (count >= suitNum) {
            return true;
        } else {
            return false;
        }
    }

    public static Integer getMaxCastingStage(int configId) {
        return MAX_CASTING_LEVEL_MAP.get(configId);
    }

    public static EquipmentGlyphsConfig getLastEquipmentGlyphsConfig(int type, int quality, int aptitude) {
        IdentiyKey identiyKey = IdentiyKey.build(type, quality, aptitude);
        TreeMap<Integer, EquipmentGlyphsConfig> map = EQUIPMENT_GLYPHS_CONFIG_MAP.get(identiyKey);
        if (map == null) {
            return null;
        }
        if (map.lastEntry() == null) {
            return null;
        }
        return map.lastEntry().getValue();
    }

    public static Optional<EquipmentUpgradeConfig> getEquipmentUpgradeConfigByNewId(int equipmentConfigId) {
        return Optional.ofNullable(EQUIPMENT_UPGRADE_CONFIG_MAPS.get(equipmentConfigId));
    }

    @Override
    protected void initialize() {
        Collection<EquipmentGlyphsResonateConfig> list = dataConfig.listAll(this, EquipmentGlyphsResonateConfig.class);
        for (EquipmentGlyphsResonateConfig config : list) {
            GLYPHS_RESONATE_CONFIG_LIST.add(config);
        }

        Collection<EquipmentNewCastingConfig> equipmentNewCastingConfigList = dataConfig.listAll(this, EquipmentNewCastingConfig.class);
        for (EquipmentNewCastingConfig config : equipmentNewCastingConfigList) {
            MAX_CASTING_LEVEL_MAP.put(config.getId(), Math.max(MAX_CASTING_LEVEL_MAP.getOrDefault(config.getId(), 0), config.getStage()));
            TreeMap<Integer, EquipmentNewCastingConfig> levelConfigMap = ID_CASTING_LEVEL_CONFIG_MAP.get(config.getId());
            if (levelConfigMap == null) {
                levelConfigMap = Maps.newTreeMap();
                ID_CASTING_LEVEL_CONFIG_MAP.put(config.getId(), levelConfigMap);
            }
            levelConfigMap.put(config.getStage(), config);

        }
        for (Map.Entry<Integer, TreeMap<Integer, EquipmentNewCastingConfig>> entry : ID_CASTING_LEVEL_CONFIG_MAP.entrySet()) {
            TreeMap<Integer, Collection<Integer>> stageSpecialIdsMap = Maps.newTreeMap();
            CASTING_STAGE_SPECIAL_IDS_MAP.put(entry.getKey(), stageSpecialIdsMap);
            TreeMap<Integer, Map<SpriteAttributeType, Long>> stageAttributeMap = Maps.newTreeMap();
            CASTING_STAGE_ATTRIBUTE_MAP.put(entry.getKey(), stageAttributeMap);
            TreeMap<Integer, Map<SpriteAttributeType, Long>> stageSpecialAttributeMap = Maps.newTreeMap();
            CASTING_STAGE_SPECIAL_ATTRIBUTE_MAP.put(entry.getKey(), stageSpecialAttributeMap);
            TreeMap<Integer, EquipmentNewCastingConfig> stageCastingMap = entry.getValue();
            for (EquipmentNewCastingConfig config : stageCastingMap.values()) {
                if (config.getSpecialEffectId() != 0) {
                    Collection<Integer> specialIdList = stageSpecialIdsMap.get(config.getStage());
                    if (specialIdList == null) {
                        specialIdList = Lists.newArrayList();
                        stageSpecialIdsMap.put(config.getStage(), specialIdList);
                    }
                    Integer lowerKey = stageSpecialIdsMap.lowerKey(config.getStage());
                    if (lowerKey != null) {
                        specialIdList.addAll(stageSpecialIdsMap.get(lowerKey));
                    }
                    specialIdList.add(config.getSpecialEffectId());
                }

                Map<SpriteAttributeType, Long> attributeMap = Maps.newHashMap();
                stageAttributeMap.put(config.getStage(), attributeMap);
                for (Map.Entry<SpriteAttributeType, Long> attributeEntry : config.getAttributeMap().entrySet()) {
                    attributeMap.put(attributeEntry.getKey(), attributeEntry.getValue() * config.getLevelNum());
                }
                Map.Entry<Integer, Map<SpriteAttributeType, Long>> lowerEntry = stageAttributeMap.lowerEntry(config.getStage());
                if (lowerEntry != null) {
                    LineupAttributeHelper.addSpriteAttributeValue(attributeMap, lowerEntry.getValue());
                }

                Map<SpriteAttributeType, Long> specialAttributeMap = Maps.newHashMap();
                stageSpecialAttributeMap.put(config.getStage(), specialAttributeMap);
                for (Map.Entry<SpriteAttributeType, Long> attributeEntry : config.getKeyAttributeMap().entrySet()) {
                    specialAttributeMap.put(attributeEntry.getKey(), attributeEntry.getValue() * config.getKeyLevelNum());
                }
                Map.Entry<Integer, Map<SpriteAttributeType, Long>> lowerSpecialEntry = stageSpecialAttributeMap.lowerEntry(config.getStage());
                if (lowerSpecialEntry != null) {
                    LineupAttributeHelper.addSpriteAttributeValue(specialAttributeMap, lowerSpecialEntry.getValue());
                }
            }
        }

        Collection<EquipmentGlyphsConfig> equipmentGlyphsConfigList = dataConfig.listAll(this, EquipmentGlyphsConfig.class);
        for (EquipmentGlyphsConfig config : equipmentGlyphsConfigList) {
            IdentiyKey identiyKey = IdentiyKey.build(config.getType(), config.getQuality(), config.getAptitude());
            TreeMap<Integer, EquipmentGlyphsConfig> treeMap = EQUIPMENT_GLYPHS_CONFIG_MAP.get(identiyKey);
            if (treeMap == null) {
                treeMap = Maps.newTreeMap();
                EQUIPMENT_GLYPHS_CONFIG_MAP.put(identiyKey, treeMap);
            }
            treeMap.put(config.getLevel(), config);
        }
        Collection<EquipmentUpgradeConfig> equipmentUpgradeConfigs = dataConfig.listAll(this, EquipmentUpgradeConfig.class);
        for (EquipmentUpgradeConfig equipmentUpgradeConfig : equipmentUpgradeConfigs) {
            EQUIPMENT_UPGRADE_CONFIG_MAPS.put(equipmentUpgradeConfig.getNewEquipId(), equipmentUpgradeConfig);
        }
    }

    @Override
    protected void clean() {
        GLYPHS_RESONATE_CONFIG_LIST.clear();
        ID_CASTING_LEVEL_CONFIG_MAP.clear();
        MAX_CASTING_LEVEL_MAP.clear();
        CASTING_STAGE_SPECIAL_IDS_MAP.clear();
        CASTING_STAGE_ATTRIBUTE_MAP.clear();
        CASTING_STAGE_SPECIAL_ATTRIBUTE_MAP.clear();
        EQUIPMENT_GLYPHS_CONFIG_MAP.clear();
        EQUIPMENT_UPGRADE_CONFIG_MAPS.clear();
    }
}