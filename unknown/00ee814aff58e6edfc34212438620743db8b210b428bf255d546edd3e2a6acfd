package cn.daxiang.hbtd.gameserver.core.dataconfig.service;

import cn.daxiang.framework.dataconfig.ConfigServiceAdapter;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.EightGateBreakoutConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.EightGateCostGroupConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.EightGateUnlockConfig;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;
import java.util.Optional;
import java.util.TreeMap;

/**
 * @Author: Gary
 * @Date 2022-10-21 15:04
 * @Description:
 */
@Component
public class EightGateConfigService extends ConfigServiceAdapter {
    /**
     * key:roleId,value:{key:gateId,value:EightGateUnlockConfig}
     */
    private static final Map<Integer, TreeMap<Integer, EightGateUnlockConfig>> EIGHT_GATE_UNLOCK_CONFIG_MAP = Maps.newHashMap();

    /**
     * key:roleId,value:{key:gateId,value:maxLevel}
     */
    private static final Map<Integer, Map<Integer, Integer>> EIGHT_GATE_BREAKOUT_MAX_LEVEL_CONFIG = Maps.newHashMap();

    /**
     * key:roleId,value:{key:level,value:EightGateBreakoutConfig}
     */
    private static final Map<Integer, TreeMap<Integer, EightGateBreakoutConfig>> EIGHT_GATE_BREAKOUT_CONFIG_MAP = Maps.newHashMap();

    /**
     * key:groupId,value:collection{items}
     */
    private static final Map<Integer, Collection<Integer>> EIGHT_GATE_COST_GROUP_CONFIG = Maps.newHashMap();

    /**
     * 获取下一门的激活配置
     *
     * @param roleId
     * @param gateId
     * @return
     */
    public static Optional<EightGateUnlockConfig> getNextEightGateUnlockConfig(int roleId, int gateId) {
        TreeMap<Integer, EightGateUnlockConfig> map = EIGHT_GATE_UNLOCK_CONFIG_MAP.get(roleId);
        if (map == null) {
            return Optional.empty();
        }
        return Optional.ofNullable(map.higherEntry(gateId).getValue());
    }

    /**
     * 获取英雄某一门的最大等级
     *
     * @param roleId
     * @param gateId
     * @return
     */
    public static Optional<Integer> getMaxLevelByGateId(int roleId, int gateId) {
        Map<Integer, Integer> map = EIGHT_GATE_BREAKOUT_MAX_LEVEL_CONFIG.get(roleId);
        if (map == null) {
            return Optional.empty();
        }
        return Optional.ofNullable(map.get(gateId));
    }

    /**
     * 获取英雄所有的八门突破配置
     *
     * @param roleId
     * @return
     */
    public static Optional<TreeMap<Integer, EightGateBreakoutConfig>> getAllEightGateBreakoutConfigByHeroId(int roleId) {

        return Optional.ofNullable(EIGHT_GATE_BREAKOUT_CONFIG_MAP.get(roleId));
    }

    public static Optional<EightGateBreakoutConfig> getNextEightGateBreakoutConfig(int roleId, int level) {
        TreeMap<Integer, EightGateBreakoutConfig> treeMap = EIGHT_GATE_BREAKOUT_CONFIG_MAP.get(roleId);
        if (treeMap == null) {
            return Optional.empty();
        }
        if (treeMap.higherEntry(level) == null) {
            return Optional.empty();
        }
        return Optional.ofNullable(treeMap.higherEntry(level).getValue());
    }

    public static Optional<Integer> findContainsGroup(int goodsId) {
        Map.Entry<Integer, Collection<Integer>> entry = EIGHT_GATE_COST_GROUP_CONFIG.entrySet().stream().filter(e -> e.getValue().contains(goodsId)).findAny().orElse(null);
        if (entry == null) {
            return Optional.empty();
        }
        return Optional.ofNullable(entry.getKey());
    }

    @Override
    protected void initialize() {
        Collection<EightGateUnlockConfig> unlockConfigs = dataConfig.listAll(this, EightGateUnlockConfig.class);
        for (EightGateUnlockConfig config : unlockConfigs) {
            TreeMap<Integer, EightGateUnlockConfig> map = EIGHT_GATE_UNLOCK_CONFIG_MAP.computeIfAbsent(config.getHeroId(), x -> Maps.newTreeMap());
            map.put(config.getGateId(), config);
        }
        Collection<EightGateBreakoutConfig> breakoutConfigs = dataConfig.listAll(this, EightGateBreakoutConfig.class);
        for (EightGateBreakoutConfig config : breakoutConfigs) {
            Map<Integer, Integer> map = EIGHT_GATE_BREAKOUT_MAX_LEVEL_CONFIG.computeIfAbsent(config.getHeroId(), x -> Maps.newHashMap());
            if (config.getLevel() > map.getOrDefault(config.getGateId(), 0)) {
                map.put(config.getGateId(), config.getLevel());
            }
            TreeMap<Integer, EightGateBreakoutConfig> breakoutMap = EIGHT_GATE_BREAKOUT_CONFIG_MAP.computeIfAbsent(config.getHeroId(), x -> Maps.newTreeMap());
            breakoutMap.put(config.getLevel(), config);
        }
        Collection<EightGateCostGroupConfig> costGroupConfigs = dataConfig.listAll(this, EightGateCostGroupConfig.class);
        for (EightGateCostGroupConfig config : costGroupConfigs) {
            EIGHT_GATE_COST_GROUP_CONFIG.put(config.getGroupId(), config.getItemList());
        }
    }

    @Override
    protected void clean() {
        EIGHT_GATE_UNLOCK_CONFIG_MAP.clear();
        EIGHT_GATE_BREAKOUT_MAX_LEVEL_CONFIG.clear();
        EIGHT_GATE_BREAKOUT_CONFIG_MAP.clear();
        EIGHT_GATE_COST_GROUP_CONFIG.clear();
    }
}
