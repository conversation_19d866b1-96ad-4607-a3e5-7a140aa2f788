package cn.daxiang.hbtd.gameserver.core.dataconfig.service;

import cn.daxiang.framework.dataconfig.ConfigServiceAdapter;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.framework.utils.RandomUtils;
import cn.daxiang.hbtd.gameserver.core.dataconfig.convert.impl.IntMapConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.convert.impl.IntTreeMapConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.convert.impl.MapIntListConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.GlobalConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.QinDungeonBossSkillConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.QinDungeonDanConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.QinDungeonFloorConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.QinDungeonHeroConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.QinDungeonLevelConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.QinDungeonRankRewardConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.QinDungeonRougueConfig;
import cn.daxiang.hbtd.gameserver.module.qindungeon.model.ChessHeroEntity;
import cn.daxiang.hbtd.gameserver.module.qinrace.type.QinRaceType;
import cn.daxiang.shared.GlobalConfigKey;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;
import java.util.TreeMap;
import java.util.TreeSet;

/**
 * <AUTHOR>
 */
@Component
public class QinDungeonConfigService extends ConfigServiceAdapter {
    /**
     * key:type,value:{key:rank,value:ArenaRankRewardConfig}
     */
    private static Map<Integer, TreeMap<Long, QinDungeonRankRewardConfig>> QINDUNGEON_RANK_REWARD_CONFIG_MAP = Maps.newHashMap();
    /**
     * key:quality,value:{key:heroId,value:count}
     */
    private static Map<Integer, Map<Integer, Integer>> QINDUNGEON_QUALITY_HERO_COUNT_MAP = Maps.newHashMap();
    /**
     * key:heroId,value:MAX_count
     */
    private static Map<Integer, Integer> QINDUNGEON_HERO_MAX_COUNT_MAP = Maps.newHashMap();
    /**
     * key:type,value:{key:level,value:QinDungeonLevelConfig}}
     */
    private static Map<Integer, TreeMap<Integer, QinDungeonLevelConfig>> QINDUNGEON_LEVEL_CONFIG_MAP = Maps.newHashMap();
    /**
     * key:type,value:{key:danMax,value:TreeSet<floor>}
     */
    private static Map<Integer, TreeMap<Integer, TreeSet<Integer>>> QINDUNGEON_DAN_FLOOR_MAP = Maps.newHashMap();
    /**
     * key:dan,value:danMax
     */
    private static TreeMap<Integer, Integer> QINDUNGEON_DAN_CONVERT_DANMAX_MAP = Maps.newTreeMap();
    /**
     * key:type,value:{key:danMax,value:{key:floor,value:QinDungeonRougueConfig}}
     */
    private static Map<Integer, Map<Integer, Map<Integer, QinDungeonRougueConfig>>> QINDUNGEON_ROUGUE_CONFIG_MAP = Maps.newHashMap();
    /**
     * key:danMax,value:{key:randId,value:weight}
     */
    private static Map<Integer, Collection<Integer>> RANDOM_BOSS_ID_MAP = Maps.newTreeMap();

    public static QinDungeonRankRewardConfig getQinDungeonRankRewardConfig(int type, long rank) {
        TreeMap<Long, QinDungeonRankRewardConfig> rankRewardConfigMap = QINDUNGEON_RANK_REWARD_CONFIG_MAP.get(type);
        if (rankRewardConfigMap == null) {
            return null;
        }
        Long key = rankRewardConfigMap.ceilingKey(rank);
        if (key == null) {
            return null;
        }
        return rankRewardConfigMap.get(key);
    }

    public static long getQinDungeonRankLimit(int type) {
        TreeMap<Long, QinDungeonRankRewardConfig> rankRewardConfigMap = QINDUNGEON_RANK_REWARD_CONFIG_MAP.get(type);
        if (rankRewardConfigMap == null) {
            return 0L;
        }
        return rankRewardConfigMap.lastKey();
    }

    public static int getHeroId(int quality, Map<Integer, Integer> heroMap) {
        Map<Integer, Integer> aptitudeCountMap = QINDUNGEON_QUALITY_HERO_COUNT_MAP.get(quality);
        Map<Integer, Integer> randomMap = Maps.newHashMap();
        for (Map.Entry<Integer, Integer> entry : aptitudeCountMap.entrySet()) {
            Integer heroId = entry.getKey();
            Integer count = heroMap.getOrDefault(heroId, 0);
            //            if (count >= QINDUNGEON_HERO_MAX_COUNT_MAP.get(heroId)) {
            //                continue;
            //            }
            randomMap.put(heroId, entry.getValue() - count);
        }
        return RandomUtils.randomByWeight(randomMap);
    }

    public static Map<Integer, Integer> getSaleMap(QinDungeonLevelConfig levelConfig, int saleCount, Collection<ChessHeroEntity> chessHeroList, TreeMap<Integer, Integer> starMap) {
        Map<Integer, Integer> saleMap = Maps.newHashMap();
        Map<Integer, Integer> heroMap = Maps.newHashMap();
        for (ChessHeroEntity chessHeroEntity : chessHeroList) {
            heroMap.put(chessHeroEntity.getHeroId(), heroMap.getOrDefault(chessHeroEntity.getHeroId(), 0) + starMap.get(chessHeroEntity.getStar()));
        }
        Map<Integer, Collection<Integer>> qualityMap = Maps.newHashMap();
        for (Map.Entry<Integer, Map<Integer, Integer>> entry : QINDUNGEON_QUALITY_HERO_COUNT_MAP.entrySet()) {
            Collection<Integer> heroIdList = Lists.newArrayList();
            for (Integer heroId : entry.getValue().keySet()) {
                heroIdList.add(heroId);
            }
            if (heroIdList.isEmpty()) {
                continue;
            }
            qualityMap.put(entry.getKey(), heroIdList);
        }
        for (int i = 1; i <= saleCount; i++) {
            int quality = levelConfig.getQuality(qualityMap.keySet());
            int heroId = getHeroId(quality, heroMap);
            heroMap.put(heroId, heroMap.getOrDefault(heroId, 0) + 1);
            saleMap.put(i, heroId);
        }
        return saleMap;
    }

    public static QinDungeonLevelConfig getInitLevelConfig() {
        return QINDUNGEON_LEVEL_CONFIG_MAP.get(QinRaceType.NONE.getId()).firstEntry().getValue();
    }

    public static QinDungeonLevelConfig getQinDungeonLevelConfig(int type, int level) {
        TreeMap<Integer, QinDungeonLevelConfig> levelConfigTreeMap = QINDUNGEON_LEVEL_CONFIG_MAP.get(type);
        if (levelConfigTreeMap == null) {
            return null;
        }
        return levelConfigTreeMap.get(level);
    }

    public static int getDanLastFloor(int type, int danMax) {
        TreeSet<Integer> floorList = QINDUNGEON_DAN_FLOOR_MAP.get(type).get(danMax);
        return floorList.last();
    }

    public static int getDanMax(int dan) {
        return QINDUNGEON_DAN_CONVERT_DANMAX_MAP.get(dan);
    }

    public static int getLastDan() {
        return QINDUNGEON_DAN_CONVERT_DANMAX_MAP.lastKey();
    }

    /**
     * 获取所有段位
     *
     * @return
     */
    public static Collection<Integer> getAllDan() {
        return QINDUNGEON_DAN_CONVERT_DANMAX_MAP.keySet();
    }

    /**
     * 获得随机特殊Id列表
     *
     * @param danMax
     * @param floor
     * @return
     */
    public static Collection<Integer> getRandomEffectIds(int type, int danMax, int floor) {
        Map<Integer, Map<Integer, QinDungeonRougueConfig>> typeRougueConfigMap = QINDUNGEON_ROUGUE_CONFIG_MAP.get(type);
        if (typeRougueConfigMap == null) {
            return null;
        }
        Map<Integer, QinDungeonRougueConfig> floorConfigMap = typeRougueConfigMap.get(danMax);
        if (floorConfigMap == null) {
            return null;
        }
        QinDungeonRougueConfig qinDungeonRougueConfig = floorConfigMap.get(floor);
        if (qinDungeonRougueConfig == null) {
            return null;
        }
        return qinDungeonRougueConfig.getRandomEffectIds();
    }

    /**
     * 根据段位获得随机的boss特性ID
     *
     * @param danMax
     * @return
     */
    public static Integer getRandomBossId(int danMax) {
        Collection<Integer> randomWeightList = RANDOM_BOSS_ID_MAP.get(danMax);
        if (randomWeightList == null) {
            return null;
        }
        return RandomUtils.randomHit(randomWeightList);
    }

    @Override
    protected void initialize() {
        Collection<QinDungeonRankRewardConfig> qinDungeonRankRewardConfigList = dataConfig.listAll(this, QinDungeonRankRewardConfig.class);
        for (QinDungeonRankRewardConfig rankRewardConfig : qinDungeonRankRewardConfigList) {
            TreeMap<Long, QinDungeonRankRewardConfig> rankRewardConfigMap = QINDUNGEON_RANK_REWARD_CONFIG_MAP.computeIfAbsent(rankRewardConfig.getType(), x -> Maps.newTreeMap());
            rankRewardConfigMap.put(rankRewardConfig.getRank(), rankRewardConfig);
        }
        //以下作用是当热刷QinDungeonHeroConfig时能生效
        dataConfig.listAll(this, QinDungeonHeroConfig.class);
        //取出各个资质英雄对应的生成数量的Map配置
        Map<Integer, Integer> heroAptitudeCountMap = Maps.newHashMap();
        GlobalConfig config = dataConfig.getConfig(IdentiyKey.build(GlobalConfigKey.QIN_DUNGEONS_HERO_RANDOM_COUNT_MAP.toString()), GlobalConfig.class);
        if (config == null) {
            LOGGER.error("GlobalConfig not found, id:{}", GlobalConfigKey.QIN_DUNGEONS_HERO_RANDOM_COUNT_MAP);
        } else {
            IntMapConfig intMapConfig = config.findObject(IntMapConfig.class);
            heroAptitudeCountMap.putAll(intMapConfig.getMap());
        }
        //取出本赛季的英雄Map配置
        Collection<QinDungeonHeroConfig> qinDungeonHeroConfigList = Lists.newLinkedList();
        config = dataConfig.getConfig(IdentiyKey.build(GlobalConfigKey.QIN_DUNGEONS_REFRESH_HERO_POOL.toString()), GlobalConfig.class);
        if (config == null) {
            LOGGER.error("GlobalConfig not found, id:{}", GlobalConfigKey.QIN_DUNGEONS_REFRESH_HERO_POOL.toString());
        } else {
            MapIntListConfig mapIntListConfig = config.findObject(MapIntListConfig.class);
            for (int key : mapIntListConfig.getKeys()) {
                for (int roleId : mapIntListConfig.getRewardListByKey(key)) {
                    QinDungeonHeroConfig heroConfig = dataConfig.getConfig(IdentiyKey.build(roleId), QinDungeonHeroConfig.class);
                    if (heroConfig == null) {
                        LOGGER.error("QinDungeonHeroConfig not found, roleId:{}", roleId);
                        continue;
                    }
                    qinDungeonHeroConfigList.add(heroConfig);
                }
            }

        }
        //根据本赛季的英雄生成英雄池
        config = dataConfig.getConfig(IdentiyKey.build(GlobalConfigKey.QIN_DUNGEONS_STAR_COUNT.toString()), GlobalConfig.class);
        TreeMap<Integer, Integer> starMap = Maps.newTreeMap();
        if (config == null) {
            LOGGER.error("GlobalConfig not found, id:{}", GlobalConfigKey.QIN_DUNGEONS_REFRESH_HERO_POOL);
        } else {
            IntTreeMapConfig intTreeMapConfig = config.findObject(IntTreeMapConfig.class);
            starMap.putAll(intTreeMapConfig.getCache());
        }
        for (QinDungeonHeroConfig qinDungeonHeroConfig : qinDungeonHeroConfigList) {
            Map<Integer, Integer> aptitudeCountMap = QINDUNGEON_QUALITY_HERO_COUNT_MAP.get(qinDungeonHeroConfig.getQuality());
            if (aptitudeCountMap == null) {
                aptitudeCountMap = Maps.newHashMap();
                QINDUNGEON_QUALITY_HERO_COUNT_MAP.put(qinDungeonHeroConfig.getQuality(), aptitudeCountMap);
            }
            aptitudeCountMap.put(qinDungeonHeroConfig.getRoleId(),
                heroAptitudeCountMap.get(qinDungeonHeroConfig.getQuality()) == null ? 0 : heroAptitudeCountMap.get(qinDungeonHeroConfig.getQuality()));
            QINDUNGEON_HERO_MAX_COUNT_MAP.put(qinDungeonHeroConfig.getRoleId(), starMap.lastEntry().getValue());
        }

        Collection<QinDungeonLevelConfig> qinDungeonLevelConfigList = dataConfig.listAll(this, QinDungeonLevelConfig.class);
        for (QinDungeonLevelConfig qinDungeonLevelConfig : qinDungeonLevelConfigList) {
            TreeMap<Integer, QinDungeonLevelConfig> levelConfigMap = QINDUNGEON_LEVEL_CONFIG_MAP.computeIfAbsent(qinDungeonLevelConfig.getType(), x -> Maps.newTreeMap());
            levelConfigMap.put(qinDungeonLevelConfig.getLevel(), qinDungeonLevelConfig);
        }
        Collection<QinDungeonFloorConfig> chessFloorConfigList = dataConfig.listAll(this, QinDungeonFloorConfig.class);
        for (QinDungeonFloorConfig floorConfig : chessFloorConfigList) {
            TreeMap<Integer, TreeSet<Integer>> typeDanFloorMap = QINDUNGEON_DAN_FLOOR_MAP.computeIfAbsent(floorConfig.getType(), x -> Maps.newTreeMap());
            TreeSet<Integer> floorList = typeDanFloorMap.get(floorConfig.getDanMax());
            if (floorList == null) {
                floorList = Sets.newTreeSet();
                typeDanFloorMap.put(floorConfig.getDanMax(), floorList);
            }
            floorList.add(floorConfig.getFloor());
        }
        Collection<QinDungeonDanConfig> dungeonDanConfigList = dataConfig.listAll(this, QinDungeonDanConfig.class);
        for (QinDungeonDanConfig qinDungeonDanConfig : dungeonDanConfigList) {
            QINDUNGEON_DAN_CONVERT_DANMAX_MAP.put(qinDungeonDanConfig.getDan(), qinDungeonDanConfig.getDanMax());
        }

        Collection<QinDungeonRougueConfig> qinDungeonRougueConfigList = dataConfig.listAll(this, QinDungeonRougueConfig.class);
        for (QinDungeonRougueConfig rougueConfig : qinDungeonRougueConfigList) {
            Map<Integer, Map<Integer, QinDungeonRougueConfig>> typeRougueConfigMap = QINDUNGEON_ROUGUE_CONFIG_MAP.computeIfAbsent(rougueConfig.getType(), x -> Maps.newHashMap());
            Map<Integer, QinDungeonRougueConfig> rougueConfigMap = typeRougueConfigMap.get(rougueConfig.getDanMax());
            if (rougueConfigMap == null) {
                rougueConfigMap = Maps.newHashMap();
                typeRougueConfigMap.put(rougueConfig.getDanMax(), rougueConfigMap);
            }
            rougueConfigMap.put(rougueConfig.getFloor(), rougueConfig);
        }

        Collection<QinDungeonBossSkillConfig> qinDungeonBossSkillConfigList = dataConfig.listAll(this, QinDungeonBossSkillConfig.class);
        for (QinDungeonBossSkillConfig qinDungeonBossSkillConfig : qinDungeonBossSkillConfigList) {
            Collection<Integer> randomWeightList = RANDOM_BOSS_ID_MAP.get(qinDungeonBossSkillConfig.getDanMax());
            if (randomWeightList == null) {
                randomWeightList = Lists.newArrayList();
                RANDOM_BOSS_ID_MAP.put(qinDungeonBossSkillConfig.getDanMax(), randomWeightList);
            }
            randomWeightList.add(qinDungeonBossSkillConfig.getRand());
        }
    }

    @Override
    protected void clean() {
        QINDUNGEON_RANK_REWARD_CONFIG_MAP.clear();
        QINDUNGEON_LEVEL_CONFIG_MAP.clear();
        QINDUNGEON_DAN_FLOOR_MAP.clear();
        QINDUNGEON_HERO_MAX_COUNT_MAP.clear();
        QINDUNGEON_QUALITY_HERO_COUNT_MAP.clear();
        QINDUNGEON_DAN_CONVERT_DANMAX_MAP.clear();
        QINDUNGEON_ROUGUE_CONFIG_MAP.clear();
        RANDOM_BOSS_ID_MAP.clear();
    }
}
