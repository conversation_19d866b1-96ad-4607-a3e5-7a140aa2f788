package cn.daxiang.hbtd.dbclean.core.dataconfig.convert.impl;

import cn.daxiang.hbtd.dbclean.core.dataconfig.convert.IConfigable;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Maps;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/10/24
 */
public class MapJsonArrayConfig implements IConfigable {

    private Map<Integer, JSONArray> cache = Maps.newHashMap();

    public static void main(String[] args) {
        Map<Integer, JSONArray> cache = Maps.newHashMap();
        String config = "[[1,[1,50]],[2,[51,100]]]";
        JSONArray array = JSONArray.parseArray(config);
        for (Object item : array) {
            JSONArray valueArray = JSONArray.parseArray(item.toString());
            cache.put(valueArray.getInteger(0), valueArray.getJSONArray(1));
        }
        System.err.println(cache);
    }

    @Override
    public void buildObject(String config) {
        JSONArray array = JSONArray.parseArray(config);
        for (Object item : array) {
            JSONArray valueArray = JSONArray.parseArray(item.toString());
            cache.put(valueArray.getInteger(0), valueArray.getJSONArray(1));
        }
    }

    public Map<Integer, JSONArray> getCache() {
        return cache;
    }
}