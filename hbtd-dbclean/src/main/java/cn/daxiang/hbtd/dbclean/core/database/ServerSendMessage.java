package cn.daxiang.hbtd.dbclean.core.database;

import cn.daxiang.framework.utils.HttpUtils;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@Component
public class ServerSendMessage {

    private static final Logger LOGGER = LoggerFactory.getLogger(ServerSendMessage.class);

    private static String WX_GET_TOKEN_URL = "https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=%s&corpsecret=%s";
    private static String WX_SEND_MESSAGE_URL = "https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token=";
    private static String SERVER_TYPE_NAME = "";
    @Value("wx7fa959e496d77149")
    private String WX_CORP_ID;
    @Value("AuEJHDQLSTEJr_r4AYuWSBTWGcaAKpdDmm22kEtRy0M")
    private String WX_CORP_SECRET;
    @Value("${serverId}")
    private int serverId;
    @Value("${server_type}")
    private int serverType;

    public int getServerId() {
        return serverId;
    }

    public String getWX_CORP_ID() {
        return WX_CORP_ID;
    }

    @PostConstruct
    private void initialize() {
        WX_GET_TOKEN_URL = String.format(WX_GET_TOKEN_URL, WX_CORP_ID, WX_CORP_SECRET);
        this.getWXToken();
        this.getServerTypeName();
    }

    private void getServerTypeName() {
        switch (serverType) {
            case 1:
                SERVER_TYPE_NAME = "内网测试";
                return;
            case 50:
                SERVER_TYPE_NAME = "新马七七游";
                return;
            default:
                SERVER_TYPE_NAME = "未知：serverType：" + serverType;
                return;
        }
    }

    private void getWXToken() {
        String response = HttpUtils.sendGet(WX_GET_TOKEN_URL);
        if (response == null) {
            LOGGER.error("WX_GET_TOKEN error, response is null. url:{}", WX_GET_TOKEN_URL);
            return;
        }
        JSONObject responseJson = JSONObject.parseObject(response);
        if (responseJson.getIntValue("errcode") == 0) {
            WX_SEND_MESSAGE_URL = WX_SEND_MESSAGE_URL.substring(0, WX_SEND_MESSAGE_URL.indexOf("=") + 1);
            WX_SEND_MESSAGE_URL += responseJson.getString("access_token");
            return;
        }
        LOGGER.error("WX_GET_TOKEN error, url:{},response:{}", WX_GET_TOKEN_URL, response);
    }

    /**
     * 发送微信消息
     *
     * @param content
     */
    private void sendWXMessage(JSONObject content) {
        JSONObject request = new JSONObject();
        request.put("toparty", "17");
        request.put("msgtype", "text");
        request.put("agentid", "1000015");
        request.put("text", content);
        String response = HttpUtils.sendPost(WX_SEND_MESSAGE_URL, request);
        if (response == null) {
            LOGGER.error("WX_SEND_MESSAGE error, response is null");
            return;
        }
        JSONObject responseJson = JSONObject.parseObject(response);
        if (responseJson.getIntValue("errcode") != 0) {
            LOGGER.error("WX_SEND_MESSAGE error, response:{}", WX_SEND_MESSAGE_URL, response);
            return;
        }
    }

    public void DBCleanException() {
        JSONObject content = new JSONObject();
        content.put("content", "游戏: 横版塔防\n" + "平台: " + SERVER_TYPE_NAME + "\n" + "区服: " + serverId + "服\n" + "消息: DBClean数据处理异常");
        this.sendWXMessage(content);
    }
}
