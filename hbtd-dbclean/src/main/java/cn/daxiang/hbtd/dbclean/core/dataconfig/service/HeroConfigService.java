package cn.daxiang.hbtd.dbclean.core.dataconfig.service;

import cn.daxiang.framework.dataconfig.ConfigServiceAdapter;
import cn.daxiang.hbtd.dbclean.core.dataconfig.model.EightGateBreakoutConfig;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/3/11
 */
@Component
public class HeroConfigService extends ConfigServiceAdapter {
    private static final Map<Integer, Map<Integer, EightGateBreakoutConfig>> EIGHT_GATE_BREAKOUT_CONFIG_MAP = Maps.newHashMap();

    @Override
    protected void initialize() {
        Collection<EightGateBreakoutConfig> eightGateBreakoutConfig = dataConfig.listAll(this, EightGateBreakoutConfig.class);
        for (EightGateBreakoutConfig config : eightGateBreakoutConfig) {
            Map<Integer, EightGateBreakoutConfig> map = EIGHT_GATE_BREAKOUT_CONFIG_MAP.computeIfAbsent(config.getHeroId(), x -> Maps.newHashMap());
            map.put(config.getLevel(), config);
        }
    }

    public static EightGateBreakoutConfig getEightGateBreakoutConfig(int heroId, int level) {
        Map<Integer, EightGateBreakoutConfig> map = EIGHT_GATE_BREAKOUT_CONFIG_MAP.get(heroId);
        if (map == null) {
            return null;
        }
        return map.get(level);
    }

    @Override
    protected void clean() {

    }
}
