package cn.daxiang.hbtd.dbclean.core.dataconfig.convert.impl;

import cn.daxiang.hbtd.dbclean.core.dataconfig.convert.HashMapConfig;

public class IntLongMapConfig extends HashMapConfig<Integer, Long> {

    @Override
    protected int fromSize() {
        return 2;
    }

    @Override
    protected Integer fromKey(String[] array) {
        return Integer.valueOf(array[0]);
    }

    @Override
    protected Long fromValue(String[] array) {
        return Long.valueOf(array[1]);
    }
}
