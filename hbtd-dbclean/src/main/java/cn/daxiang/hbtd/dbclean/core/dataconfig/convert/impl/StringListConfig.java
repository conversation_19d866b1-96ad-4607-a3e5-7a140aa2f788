package cn.daxiang.hbtd.dbclean.core.dataconfig.convert.impl;

import cn.daxiang.hbtd.dbclean.core.dataconfig.convert.IConfigable;
import com.google.common.collect.Lists;

import java.util.Collection;

/**
 * <AUTHOR>
 * @date 2020/11/20
 */
public class StringListConfig implements IConfigable {

    private Collection<String> list;

    @Override
    public void buildObject(String config) {
        list = Lists.newArrayList(config.split("-"));
    }

    public Collection<String> getList() {
        return list;
    }
}
