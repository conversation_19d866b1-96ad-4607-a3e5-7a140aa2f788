package cn.daxiang.hbtd.dbclean.core.database.table;

import cn.daxiang.framework.database.MultiEntity;
import cn.daxiang.framework.database.annotation.Column;
import cn.daxiang.framework.database.annotation.DBQueueType;
import cn.daxiang.framework.database.annotation.Table;
import cn.daxiang.framework.identity.IdentiyKey;
import com.alibaba.fastjson.annotation.JSONField;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 玩家物品信息
 *
 * <AUTHOR>
 */
@Table(name = "goods", type = DBQueueType.IMPORTANT)
public class Goods extends MultiEntity<Long> implements Comparable<Goods> {
    /**
     * 角色ID
     */
    @Column(fk = true)
    @JSONField(serialize = false)
    private long actorId;
    /**
     * 物品UID
     */
    @Column(pk = true)
    private long goodsUid;
    /**
     * 物品ID
     */
    @Column
    private int goodsId;
    /**
     * 物品数量
     */
    @Column
    private int num;
    /**
     * 物品使用次数
     */
    @Column
    private int times;
    /**
     * 到期时间
     */
    @Column
    private long expirationTime;

    public static Goods valueOf(long actorId, long goodsUid, int goodsId, long expirationTime) {
        Goods goods = new Goods();
        goods.actorId = actorId;
        goods.goodsUid = goodsUid;
        goods.goodsId = goodsId;
        goods.expirationTime = expirationTime;
        return goods;
    }

    public static void collectGoods(Collection<Goods> srcGoodsList, Collection<Goods> willAddGoodsList) {
        List<Goods> unAddedGoodsList = null;
        for (Goods goodsTemp : willAddGoodsList) {
            boolean isAdded = false;
            for (Goods addedGoods : srcGoodsList) {
                if (addedGoods.getGoodsUid() == goodsTemp.getGoodsUid()) {
                    addedGoods.addNumber(goodsTemp.getNum());
                    isAdded = true;
                    break;
                }
            }
            if (!isAdded) {
                if (unAddedGoodsList == null) {
                    unAddedGoodsList = new ArrayList<>();
                }
                unAddedGoodsList.add(goodsTemp);
            }
        }
        if (unAddedGoodsList != null && unAddedGoodsList.size() > 0) {
            srcGoodsList.addAll(unAddedGoodsList);
        }
    }

    @Override
    public Long findFkId() {
        return actorId;
    }

    @Override
    public void setFkId(Long fk) {
        this.actorId = fk;
    }

    @Override
    public IdentiyKey findPkId() {
        return IdentiyKey.build(goodsUid);
    }

    @Override
    public void setPkId(IdentiyKey pk) {
        this.goodsUid = pk.getFirstLongId();
    }

    public long getActorId() {
        return actorId;
    }

    public long getGoodsUid() {
        return goodsUid;
    }

    public int getGoodsId() {
        return goodsId;
    }

    public int getNum() {
        return num;
    }

    public void setNum(int num) {
        this.num = num;
    }

    public void addNumber(long num) {
        this.num += num;
    }

    public int getTimes() {
        return times;
    }

    public void setTimes(int times) {
        this.times = times;
    }

    public long getExpirationTime() {
        return expirationTime;
    }

    public void setExpirationTime(long expirationTime) {
        this.expirationTime = expirationTime;
    }

    @Override
    public int compareTo(Goods o) {
        if (this.num > o.num) {
            return 1;
        } else if (this.num < o.getNum()) {
            return -1;
        }
        return this.goodsUid > o.getGoodsUid() ? 1 : -1;
    }
}
