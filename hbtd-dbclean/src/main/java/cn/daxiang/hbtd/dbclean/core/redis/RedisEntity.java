package cn.daxiang.hbtd.dbclean.core.redis;

import cn.daxiang.hbtd.dbclean.core.redis.parser.StringKeyGenerator;
import com.alibaba.fastjson.annotation.JSONField;

public abstract class RedisEntity implements Identifiable {
    /**
     * key 生成
     */
    public static KeyGenerator<String> keyGenerator = new StringKeyGenerator();

    @JSONField(serialize = false)
    public String getKey() {
        return keyGenerator.generate(this);
    }
}
