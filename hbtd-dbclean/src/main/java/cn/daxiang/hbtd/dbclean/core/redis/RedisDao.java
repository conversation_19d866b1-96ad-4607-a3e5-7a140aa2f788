package cn.daxiang.hbtd.dbclean.core.redis;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

public class RedisDao {
    protected Logger LOGGER = LoggerFactory.getLogger(getClass());
    @Resource
    protected RedisTemplate<String, String> redisTemplate;

    protected <T extends RedisEntity> T get(Class<T> clz, Object... args) {
        String key = RedisEntity.keyGenerator.generate(clz, args);
        String json = redisTemplate.opsForValue().get(key);
        if (json == null)
            return null;
        return JSON.parseObject(json, clz);
    }

    protected <T> T get(Class<T> clz, String key) {
        String json = redisTemplate.opsForValue().get(key);
        if (json == null)
            return null;
        return JSON.parseObject(json, clz);
    }

    protected void update(RedisEntity redisEntity) {
        String json = JSON.toJSONString(redisEntity);
        String key = redisEntity.getKey();
        redisTemplate.opsForValue().set(key, json);
    }

    protected <T extends Object> void update(T obj, String key) {
        String json = JSON.toJSONString(obj);
        redisTemplate.opsForValue().set(key, json);
    }

    /**
     * 添加到排序set
     *
     * @param key
     * @param value
     * @param sortValue
     * @return
     */
    protected boolean addZSet(String key, String value, double sortValue) {
        return redisTemplate.opsForZSet().add(key, value, sortValue);
    }

    /**
     * 获取排序set
     *
     * @param setKey
     * @param start
     * @param end
     * @return
     */
    protected Set<String> getSortKeysListRangeByScore(String setKey, long start, long end) {
        Set<String> result = redisTemplate.opsForZSet().reverseRangeByScore(setKey, start, end);
        return result;
    }

    protected Set<String> getSortKeysListRange(String setKey, long start, long end) {
        Set<String> result = redisTemplate.opsForZSet().reverseRange(setKey, start, end);
        return result;
    }

    protected void clearSortSet(String setKey) {
        redisTemplate.delete(setKey);
    }

    protected void addList(String key, List<String> values) {
        redisTemplate.opsForList().leftPushAll(key, values);
    }

    protected <T extends RedisEntity> void addListEntity(String key, List<T> values) {
        List<String> list = new ArrayList<>();
        for (T entity : values) {
            list.add(JSON.toJSONString(entity));
        }
        redisTemplate.opsForList().leftPushAll(key, list);
    }

    protected <T extends Object> void addListObject(String key, Collection<T> values) {
        if (values.isEmpty()) {
            return;
        }
        List<String> list = new ArrayList<>();
        for (T entity : values) {
            list.add(JSON.toJSONString(entity));
        }
        redisTemplate.opsForList().leftPushAll(key, list);
    }

    protected <T extends RedisEntity> List<T> getListEntity(String key, long start, long end, Class<T> clz) {
        List<String> list = redisTemplate.opsForList().range(key, start, end);
        List<T> result = new ArrayList<>();
        for (String string : list) {
            T t = JSON.parseObject(string, clz);
            result.add(t);
        }
        Collections.reverse(result);
        return result;
    }

    protected <T extends Object> List<T> getListObject(String key, Class<T> clz) {
        List<String> list = redisTemplate.opsForList().range(key, 0, redisTemplate.opsForList().size(key));
        List<T> result = new ArrayList<>();
        for (String string : list) {
            T t = JSON.parseObject(string, clz);
            result.add(t);
        }
        Collections.reverse(result);
        return result;
    }

    protected void putHashObject(String key, String hashKey, Object value) {
        redisTemplate.opsForHash().put(key, hashKey, JSON.toJSONString(value));
    }

    protected void deleteHashObject(String key, String hashKey) {
        redisTemplate.opsForHash().delete(key, hashKey);
    }

    protected boolean hasHashKey(String key, String hashKey) {
        return redisTemplate.opsForHash().hasKey(key, hashKey);
    }

    protected <T extends Object> T getHashObject(String key, String hashKey, Class<T> clz) {
        if (!hasHashKey(key, hashKey)) {
            return null;
        }
        String valueString = redisTemplate.opsForHash().get(key, hashKey).toString();
        T t = JSON.parseObject(valueString, clz);
        return t;
    }

    @SuppressWarnings("unchecked")
    protected <T, K extends Object> Map<K, T> getHashMap(String key, Class<T> valueClass) {
        Map<K, T> map = Maps.newHashMap();
        Map<Object, Object> entityMap = redisTemplate.opsForHash().entries(key);
        for (Entry<Object, Object> entry : entityMap.entrySet()) {
            T t = JSON.parseObject(entry.getValue().toString(), valueClass);
            map.put((K) (entry.getKey()), t);
        }
        return map;
    }

    protected <T extends Object> Collection<T> getHashValues(String key, Class<T> clz) {
        Collection<T> list = Lists.newArrayList();
        Map<Object, Object> entityMap = redisTemplate.opsForHash().entries(key);
        for (Entry<Object, Object> entry : entityMap.entrySet()) {
            T t = JSON.parseObject(entry.getValue().toString(), clz);
            list.add(t);
        }
        return list;
    }

    protected Collection<Object> getHashKeys(String key) {
        Collection<Object> list = Lists.newArrayList();
        Map<Object, Object> entityMap = redisTemplate.opsForHash().entries(key);
        for (Entry<Object, Object> entry : entityMap.entrySet()) {
            list.add(entry.getKey());
        }
        return list;
    }

    protected void delete(String key) {
        Set<String> keys = redisTemplate.keys(key);
        redisTemplate.delete(keys);
    }

    protected Collection<String> getKeys(String key) {
        return redisTemplate.keys(key);
    }

    /**
     * 慎用
     *
     * @return
     */
    protected RedisTemplate<String, String> getTemplate() {
        return redisTemplate;
    }

    protected String getString(String key) {
        return redisTemplate.opsForValue().get(key);
    }

    protected void setString(String key, String value) {
        redisTemplate.opsForValue().set(key, value);
    }
}
