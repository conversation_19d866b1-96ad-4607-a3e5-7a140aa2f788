package cn.daxiang.hbtd.gameserver.module.chat.parser.info.impl;

import cn.daxiang.framework.event.GameEvent;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.MountConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.SystemMsgConfig;
import cn.daxiang.hbtd.gameserver.core.event.impl.MountGetEvent;
import cn.daxiang.hbtd.gameserver.module.chat.helper.ChatHelper;
import cn.daxiang.hbtd.gameserver.module.chat.model.ChatInfoReceiver;
import cn.daxiang.hbtd.gameserver.module.chat.parser.info.AbstractChatInfoParser;
import cn.daxiang.protocol.game.ChatInfoProtocol;
import cn.daxiang.protocol.game.ChatProtocol;
import cn.daxiang.protocol.game.TypeProtocol;
import cn.daxiang.shared.event.EventKey;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

/**
 * @author: Cary
 * @date: 2025/3/31 19:54
 * @Description:
 */
@Component
public class MountGetChatInfoParser extends AbstractChatInfoParser {
    @Override
    protected List<String> getEventName() {
        return Lists.newArrayList(EventKey.MOUNT_GET_EVENT);
    }

    @Override
    public Map<ChatInfoReceiver, ChatProtocol.ChatInfo> parse(GameEvent e) {
        MountGetEvent event = e.convert();
        SystemMsgConfig config = globalConfigService.findConfig(IdentiyKey.build(TypeProtocol.ChatInfoType.MOUNT_GET_CHAT_INFO_VALUE), SystemMsgConfig.class);
        Map<Integer, Long> msgMap = Maps.newHashMap();
        for (Entry<Integer, Long> entry : event.mountMap.entrySet()) {
            int configId = entry.getKey();
            MountConfig beastConfig = globalConfigService.findConfig(IdentiyKey.build(configId), MountConfig.class);
            if (beastConfig != null) {
                if (config.execute(event.operationType.getId(), beastConfig.getQuality(), entry.getKey())) {
                    msgMap.put(entry.getKey(), entry.getValue());
                }
            }
        }
        if (msgMap.isEmpty()) {
            return Collections.emptyMap();
        }
        Map<ChatInfoReceiver, ChatProtocol.ChatInfo> chatMap = Maps.newHashMap();
        ChatInfoProtocol.MountGetInfo.Builder builder = ChatInfoProtocol.MountGetInfo.newBuilder();
        builder.putAllMountMap(msgMap);
        builder.setOperationType(event.operationType.getId());
        Collection<Long> actorIds = Lists.newLinkedList();
        actorIds.add(event.actorId);
        ChatProtocol.ChatInfo chatInfo = ChatHelper.buildChatInfo(TypeProtocol.ChatInfoType.MOUNT_GET_CHAT_INFO, event.getActorId(), builder.build().toByteArray(), actorIds);
        chatMap.put(ChatInfoReceiver.valueOf(TypeProtocol.ChatChannelType.CHAT_CHANNEL_TYPE_SYSTEM), chatInfo);
        return chatMap;
    }
}
