package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.dataconfig.annotation.FieldIgnore;
import cn.daxiang.framework.identity.IdentiyKey;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Sets;

import java.util.Collection;

/**
 * 万能碎片对应转化表
 *
 * <AUTHOR>
 */
@DataFile(fileName = "fragment_convert_config")
public class FragmentConvertConfig implements ModelAdapter {
    /**
     * 万能碎片配置id，唯一
     */
    private int fragmentId;
    /**
     * 万能碎片转换对应列表
     */
    private String convertList;
    /**
     * 万能碎片转换对应ID列表
     */
    @FieldIgnore
    private Collection<Integer> convertIdList = Sets.newHashSet();

    @Override
    public void initialize() {
        convertIdList.addAll(JSONArray.parseArray(convertList, Integer.class));
    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(fragmentId);
    }

    public int getFragmentId() {
        return fragmentId;
    }

    public String getConvertList() {
        return convertList;
    }

    public Collection<Integer> getConvertIdList() {
        return convertIdList;
    }
}
