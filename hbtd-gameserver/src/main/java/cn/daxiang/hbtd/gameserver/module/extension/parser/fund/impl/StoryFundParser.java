package cn.daxiang.hbtd.gameserver.module.extension.parser.fund.impl;

import cn.daxiang.framework.result.CollectionResult;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.hbtd.gameserver.core.database.table.Fund;
import cn.daxiang.hbtd.gameserver.core.database.table.StoryHangUp;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.FundRewardConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.StoryConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.StoryConfigService;
import cn.daxiang.hbtd.gameserver.module.extension.model.FundEntity;
import cn.daxiang.hbtd.gameserver.module.extension.parser.fund.AbstractFundParser;
import cn.daxiang.hbtd.gameserver.module.extension.type.FundType;
import cn.daxiang.hbtd.gameserver.module.story.facade.StoryHangUpFacade;
import cn.daxiang.shared.reward.RewardObject;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;

import static cn.daxiang.shared.GameModuleStatusCodeConstant.CONFIG_NOT_FOUND;
import static cn.daxiang.shared.GameModuleStatusCodeConstant.FUND_REWARD_CAN_NOT_RECEIVED;

/**
 * <AUTHOR>
 * @date 2022/6/14
 */
@Component
public class StoryFundParser extends AbstractFundParser {
    @Autowired
    private StoryHangUpFacade storyHangUpFacade;

    @Override
    protected FundType getType() {
        return FundType.STORY_FUND;
    }

    @Override
    public CollectionResult<RewardObject> parser(long actorId, Collection<FundRewardConfig> configs, Fund fund, boolean isLuxury) {
        Collection<RewardObject> rewardList = Lists.newArrayList();
        TResult<StoryHangUp> result = storyHangUpFacade.getStoryHangUp(actorId);
        if (result.isFail()) {
            return CollectionResult.valueOf(FUND_REWARD_CAN_NOT_RECEIVED);
        }
        int storyId = result.item.getStoryId();
        TResult<StoryConfig> configResult = StoryConfigService.getStoryConfig(storyId);
        if (configResult.isFail()) {
            LOGGER.error("StoryConfig is not found!storyId:{}", storyId);
            return CollectionResult.valueOf(CONFIG_NOT_FOUND);
        }
        FundEntity entity = null;
        Collection<Integer> recieveList;
        for (FundRewardConfig config : configs) {
            entity = fund.getFundInfoMap().get(config.getType().getId());
            recieveList = entity.getFundReceiveList(isLuxury, config.getStage());
            if (recieveList.contains(config.getId())) {
                continue;
            }
            if (config.getMinLimit() < configResult.item.getChapterId()) {
                rewardList.addAll(config.getRewardList(isLuxury));
                entity.receiveFund(config.getStage(), config.getId(), isLuxury);
            } else {
                break;
            }
        }
        if (rewardList.isEmpty()) {
            return CollectionResult.valueOf(FUND_REWARD_CAN_NOT_RECEIVED);
        }
        return CollectionResult.collection(rewardList);
    }
}
