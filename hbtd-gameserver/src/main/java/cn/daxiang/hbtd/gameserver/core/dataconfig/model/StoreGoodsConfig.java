package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.dataconfig.annotation.FieldIgnore;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.protocol.game.StoreProtocol;
import cn.daxiang.shared.reward.RewardObject;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.Collection;
import java.util.TreeMap;

/**
 * <AUTHOR>
 */
@DataFile(fileName = "store_goods_config")
public class StoreGoodsConfig implements ModelAdapter {
    /**
     * 商店TYPE
     * {@code StoreType}
     */
    private int storeType;
    /**
     * groupID
     */
    private int groupId;
    /**
     * 物品ID
     */
    private int productId;
    /**
     * 物品价格类型、价格、数量 (向下取)
     * [[times,[[rewardType,id,num],[rewardType,id,num]]],[times,[[rewardType,id,num],[rewardType,id,num]]]]
     * {@code RewardObject}
     */
    private String price;
    /**
     * 购买次数(向下取)
     * [[vipLevel,times],[vipLevel,times]]
     */
    private String times;
    /**
     * 奖励，购买后的结果
     * ([rewardType,id,num])
     * {@code RewardObject}
     */
    private String rewards;
    /**
     * 解锁ID(没有解锁条件填0)
     */
    private int unlockId;
    /*** 下面这些字段只有StoreType中随机的类型有用*/
    /**
     * 等级限制
     */
    private int levelLimit;
    /**
     * 权重
     */
    private int weight;
    @FieldIgnore
    private StoreProtocol.StoreType storeTypeEnum;
    @FieldIgnore
    private TreeMap<Integer, Integer> timesMap = Maps.newTreeMap();
    @FieldIgnore
    private TreeMap<Integer, Collection<RewardObject>> costMap = Maps.newTreeMap();
    @FieldIgnore
    private RewardObject rewardObject;

    @Override
    public void initialize() {
        storeTypeEnum = StoreProtocol.StoreType.forNumber(storeType);
        JSONArray timesArray = JSONArray.parseArray(times);
        for (Object timesItem : timesArray) {
            JSONArray array = JSONArray.parseArray(timesItem.toString());
            timesMap.put(array.getInteger(0), array.getInteger(1));
        }
        JSONArray array = JSONArray.parseArray(price);
        for (Object item : array) {
            JSONArray valueArray = JSONArray.parseArray(item.toString());
            JSONArray rewardListArray = JSONArray.parseArray(valueArray.getString(1));
            Collection<RewardObject> rewardList = Lists.newArrayListWithCapacity(rewardListArray.size());
            for (Object rewardListItem : rewardListArray) {
                JSONArray rewardArray = JSONArray.parseArray(rewardListItem.toString());
                RewardObject rewardObject = RewardObject.valueOf(rewardArray);
                rewardList.add(rewardObject);
            }
            costMap.put(valueArray.getInteger(0), rewardList);
        }
        JSONArray rewardArray = JSONArray.parseArray(rewards);
        rewardObject = RewardObject.valueOf(rewardArray);
    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(storeType, productId);
    }

    public StoreProtocol.StoreType getStoreType() {
        return storeTypeEnum;
    }

    public int getproductId() {
        return productId;
    }

    public int getTimes(int vipLevel) {
        return timesMap.isEmpty() ? 0 : timesMap.floorEntry(vipLevel).getValue();
    }

    public Collection<RewardObject> getCost(int times) {
        return costMap.floorEntry(times).getValue();
    }

    public RewardObject getRewardObject() {
        return rewardObject;
    }

    public int getUnlockId() {
        return unlockId;
    }

    public int getLevelLimit() {
        return levelLimit;
    }

    public int getWeight() {
        return weight;
    }

    public int getGroupId() {
        return groupId;
    }

    public int getProductId() {
        return productId;
    }

    public String getPrice() {
        return price;
    }

    public String getTimes() {
        return times;
    }

    public String getRewards() {
        return rewards;
    }

    public StoreProtocol.StoreType getStoreTypeEnum() {
        return storeTypeEnum;
    }

    public TreeMap<Integer, Integer> getTimesMap() {
        return timesMap;
    }

    public TreeMap<Integer, Collection<RewardObject>> getCostMap() {
        return costMap;
    }
}