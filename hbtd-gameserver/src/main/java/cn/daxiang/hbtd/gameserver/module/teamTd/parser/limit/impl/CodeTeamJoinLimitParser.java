package cn.daxiang.hbtd.gameserver.module.teamTd.parser.limit.impl;

import cn.daxiang.framework.result.Result;
import cn.daxiang.hbtd.gameserver.module.teamTd.parser.limit.AbstractTeamJoinLimitParser;
import cn.daxiang.shared.module.teamTd.type.TeamTdJoinLimitType;
import org.springframework.stereotype.Component;

import static cn.daxiang.shared.GameModuleStatusCodeConstant.INVALID_PARAM;

/**
 * <AUTHOR>
 * @date 2020/12/26
 */
@Component
public class CodeTeamJoinLimitParser extends AbstractTeamJoinLimitParser {
    @Override
    protected TeamTdJoinLimitType getType() {
        return TeamTdJoinLimitType.CODE;
    }

    @Override
    public Result check(long actorId, long limitValue) {
        return Result.valueOf(INVALID_PARAM);
    }
}
