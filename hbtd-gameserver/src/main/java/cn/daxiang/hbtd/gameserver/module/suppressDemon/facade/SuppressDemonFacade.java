package cn.daxiang.hbtd.gameserver.module.suppressDemon.facade;

import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.hbtd.gameserver.core.database.table.SuppressDemon;
import cn.daxiang.protocol.game.CommonProtocol;
import cn.daxiang.protocol.game.SuppressdemonProtocol;

/**
 * <AUTHOR>
 */
public interface SuppressDemonFacade {
    /**
     * 获取镇妖台信息
     *
     * @param actorId
     * @return
     */
    TResult<SuppressDemon> getSuppressDemon(long actorId);

    /**
     * 获取镇妖台排行榜
     *
     * @param actorId
     * @param id
     * @return
     */
    TResult<SuppressdemonProtocol.SuppressDemonRankResponse> getSuppressDemonRank(long actorId, int id);

    /**
     * 准备挑战镇妖台
     *
     * @param actorId
     * @param id
     * @return
     */
    Result prepare(long actorId, int id);

    /**
     * 挑战镇妖台
     *
     * @param actorId
     * @param request
     * @return
     */
    Result challenge(long actorId, SuppressdemonProtocol.SuppressDemonChallengeRequest request);

    /**
     * 购买镇妖台次数
     *
     * @param actorId
     * @param times
     * @return
     */
    Result buyTimes(long actorId, int times);

    /**
     * 检测结果
     *
     * @param actorId
     * @param request
     * @param result
     * @return
     */
    TResult<CommonProtocol.RewardResult> verifyResult(long actorId, SuppressdemonProtocol.SuppressDemonChallengeRequest request, boolean result);

    /**
     * 扫荡
     *
     * @param actorId
     * @param id
     * @return
     */
    TResult<CommonProtocol.RewardResultResponse> wipeOut(long actorId, int id);

}
