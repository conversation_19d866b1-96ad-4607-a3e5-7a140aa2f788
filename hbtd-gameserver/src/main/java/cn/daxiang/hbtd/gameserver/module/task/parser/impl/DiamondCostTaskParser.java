package cn.daxiang.hbtd.gameserver.module.task.parser.impl;

import cn.daxiang.hbtd.gameserver.core.database.table.Task;
import cn.daxiang.hbtd.gameserver.core.dataconfig.convert.impl.IntListConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.TaskConfig;
import cn.daxiang.hbtd.gameserver.core.event.impl.ActorDiamondDecreaseEvent;
import cn.daxiang.hbtd.gameserver.module.task.parser.AbstractTaskParser;
import cn.daxiang.hbtd.gameserver.module.task.type.TaskConditionType;
import cn.daxiang.protocol.game.TaskProtocol;
import cn.daxiang.shared.GlobalConfigKey;
import org.springframework.stereotype.Component;

import java.util.Collection;

/**
 * <AUTHOR>
 * @date 2022/6/17
 */
@Component
public class DiamondCostTaskParser extends AbstractTaskParser<ActorDiamondDecreaseEvent> {
    @Override
    public void init(Task task, TaskConfig taskConfig) {

    }

    @Override
    protected TaskConditionType getType() {
        return TaskConditionType.DIAMOND_COST;
    }

    @Override
    protected boolean parseCondition(ActorDiamondDecreaseEvent event, Task task, TaskConfig taskConfig) {
        Collection<Integer> operationType = globalConfigService.findGlobalObject(GlobalConfigKey.NOT_INCLUDED_IN_CONSUMPTION, IntListConfig.class).findValues();
        if (operationType.contains(event.operationType.getId())) {
            return false;
        }
        task.setValue(task.getValue() + event.count);
        if (task.getValue() >= taskConfig.getValue()) {
            task.setStatus(TaskProtocol.TaskStatus.TASK_FINISH);
        }
        return true;
    }

}
