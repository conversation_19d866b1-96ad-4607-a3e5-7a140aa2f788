package cn.daxiang.hbtd.gameserver.module.heroRoad;

import cn.daxiang.framework.dispatch.DispatchType;
import cn.daxiang.framework.protocal.DataPacket;
import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.framework.router.annotation.Cmd;
import cn.daxiang.hbtd.gameserver.core.database.table.HeroRoad;
import cn.daxiang.hbtd.gameserver.core.router.GatewayRouterHandlerImpl;
import cn.daxiang.hbtd.gameserver.module.PbBuilder;
import cn.daxiang.hbtd.gameserver.module.heroRoad.facade.HeroRoadFacade;
import cn.daxiang.hbtd.gameserver.module.heroRoad.helper.HeroRoadHelper;
import cn.daxiang.protocol.game.CommonProtocol;
import cn.daxiang.protocol.game.HeroroadProtocol;
import cn.daxiang.protocol.game.HeroroadProtocol.HeroRoadResponse;
import cn.daxiang.protocol.game.ModuleProtocol.Module;
import io.netty.channel.Channel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/7/18
 */
@Component
public class HeroRoadHandler extends GatewayRouterHandlerImpl {
    @Autowired
    private HeroRoadFacade heroRoadFacade;

    @Override
    public int getModule() {
        return Module.HEROROAD_VALUE;
    }

    @Cmd(Id = HeroroadProtocol.HeroroadCmd.HERO_ROAD_INFO_VALUE, dispatchType = DispatchType.ACTOR)
    public void getHeroRoad(Channel channel, Long actorId, DataPacket packet) {
        TResult<HeroRoad> result = heroRoadFacade.getHeroRoad(actorId);
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        HeroRoadResponse response = HeroRoadHelper.buildHeroRoadResponse(result.item);
        channelWrite(channel, packet, response);
    }

    @Cmd(Id = HeroroadProtocol.HeroroadCmd.HERO_ROAD_PREPARE_VALUE, dispatchType = DispatchType.ACTOR)
    public void prepare(Channel channel, Long actorId, DataPacket packet) {
        HeroroadProtocol.HeroRoadPrepareRequest request = packet.getValue(HeroroadProtocol.HeroRoadPrepareRequest.parser());
        Result result = heroRoadFacade.prepare(actorId, request.getStoryId(), request.getDifficultyLevel());
        channelWrite(channel, packet, result.statusCode);
    }

    @Cmd(Id = HeroroadProtocol.HeroroadCmd.HERO_ROAD_CHALLENGE_VALUE, dispatchType = DispatchType.ACTOR)
    public void challenge(Channel channel, Long actorId, DataPacket packet) {
        HeroroadProtocol.HeroRoadChallengeRequest request = packet.getValue(HeroroadProtocol.HeroRoadChallengeRequest.parser());
        TResult<CommonProtocol.RewardResult> result = heroRoadFacade.challenge(actorId, request);
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        CommonProtocol.RewardResultResponse response = PbBuilder.buildRewardResultResponse(result.item);
        channelWrite(channel, packet, response);
    }

    @Cmd(Id = HeroroadProtocol.HeroroadCmd.HERO_ROAD_REWARD_VALUE, dispatchType = DispatchType.ACTOR)
    public void receiveReward(Channel channel, Long actorId, DataPacket packet) {
        CommonProtocol.IntPacket request = packet.getValue(CommonProtocol.IntPacket.parser());
        TResult<CommonProtocol.RewardResult> result = heroRoadFacade.receiveReward(actorId, request.getValue());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        CommonProtocol.RewardResultResponse response = PbBuilder.buildRewardResultResponse(result.item);
        channelWrite(channel, packet, response);
    }

    @Cmd(Id = HeroroadProtocol.HeroroadCmd.PASS_LINEUP_LIST_VALUE, dispatchType = DispatchType.ACTOR)
    public void passLineupList(Channel channel, Long actorId, DataPacket packet) {
        HeroroadProtocol.PassLineupListRequest request = packet.getValue(HeroroadProtocol.PassLineupListRequest.parser());
        TResult<HeroroadProtocol.PassLineupListResponse> result = heroRoadFacade.passLineupList(actorId, request.getStoryId(), request.getDifficultyLevel());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }
}
