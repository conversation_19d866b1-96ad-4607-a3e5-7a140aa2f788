package cn.daxiang.hbtd.gameserver.module.activity.type;

import cn.daxiang.shared.event.EventKey;
import com.google.common.collect.Sets;

import java.util.Set;

/**
 * 活动类型
 *
 * <AUTHOR>
 */
public enum ActivityType {
    /**
     * 1.新服狂欢
     */
    ACTIVITY_TYPE_1(1),
    /**
     * 2.群雄逐鹿（新）
     */
    ACTIVITY_TYPE_2(2, EventKey.ARSENAL_CHALLENGE_EVENT, EventKey.GACHA_EVENT, EventKey.RECRUIT_GACHA_EVENT, EventKey.REPUTATION_EVENT_FINISH_EVENT),
    /**
     * 3.充值每日特惠
     */
    ACTIVITY_TYPE_3(3, EventKey.ACTOR_RECHARGE_EVENT),
    /**
     * 4.任务战令
     */
    ACTIVITY_TYPE_4(4, EventKey.ACTOR_RECHARGE_EVENT),
    /**
     * 5.新服签到
     */
    ACTIVITY_TYPE_5(5),
    /**
     * 6.充值每周礼包 同[6, 38, 39]
     */
    ACTIVITY_TYPE_6(6, EventKey.ACTOR_RECHARGE_EVENT),
    /**
     * 7.定制礼包 同[7, 40]
     */
    ACTIVITY_TYPE_7(7, EventKey.ACTOR_RECHARGE_EVENT),
    /**
     * 8.推荐-连续充值
     */
    ACTIVITY_TYPE_8(8, EventKey.ACTOR_RECHARGE_EVENT),
    /**
     * 9.推荐-累计充值 同[9, 41, 42, 43, 44, 45]
     */
    ACTIVITY_TYPE_9(9, EventKey.ACTOR_RECHARGE_EVENT),
    /**
     * 10.上古珍宝
     */
    ACTIVITY_TYPE_10(10),
    /**
     * 11.推荐-推荐礼包
     */
    ACTIVITY_TYPE_11(11, EventKey.ACTOR_RECHARGE_EVENT),
    /**
     * 12.群雄逐鹿
     */
    ACTIVITY_TYPE_12(12),
    /**
     * 13.限时商人 同[13, 46, 47]
     */
    ACTIVITY_TYPE_13(13, EventKey.ACTOR_RECHARGE_EVENT),
    /**
     * 14.招兵买马 同[14,52]
     */
    ACTIVITY_TYPE_14(14),
    /**
     * 15.过关斩将
     */
    ACTIVITY_TYPE_15(15, EventKey.ACTOR_RECHARGE_EVENT),
    /**
     * 16.新手礼包
     */
    ACTIVITY_TYPE_16(16, EventKey.ACTOR_RECHARGE_EVENT),
    /**
     * 17.超值材料礼包
     */
    ACTIVITY_TYPE_17(17),
    /**
     * 18.群雄逐鹿2
     */
    ACTIVITY_TYPE_18(18),
    /**
     * 19.群雄逐鹿3
     */
    ACTIVITY_TYPE_19(19, EventKey.ARSENAL_CHALLENGE_EVENT),
    /**
     * 20.稀世绘卷
     */
    ACTIVITY_TYPE_20(20),
    /**
     * 21.天官赐福
     */
    ACTIVITY_TYPE_21(21, EventKey.ACTOR_RECHARGE_EVENT),
    /**
     * 22.迎财神
     */
    ACTIVITY_TYPE_22(22, EventKey.ACTOR_RECHARGE_EVENT),
    /**
     * 23.天赐神将-签到
     */
    ACTIVITY_TYPE_23(23),
    /**
     * 24.天赐神将-神将招募
     */
    ACTIVITY_TYPE_24(24),
    /**
     * 25.天赐神将-洛书的委托
     */
    ACTIVITY_TYPE_25(25),
    /**
     * 26.天赐神将-代币商店
     */
    ACTIVITY_TYPE_26(26),
    /**
     * 27.天赐神将-礼包商店
     */
    ACTIVITY_TYPE_27(27, EventKey.ACTOR_RECHARGE_EVENT),
    /**
     * 28.天赐神将-封神之路
     */
    ACTIVITY_TYPE_28(28, EventKey.ACTOR_RECHARGE_EVENT),
    /**
     * 29.天赐神将-神将试炼
     */
    ACTIVITY_TYPE_29(29),
    /**
     * 30.天赐神将-活动页签开关
     */
    ACTIVITY_TYPE_30(30),
    /**
     * 31.摘星揽月
     */
    ACTIVITY_TYPE_31(31, EventKey.ACTOR_RECHARGE_EVENT),
    /**
     * 33.节日活动集合-迎春新像
     */
    ACTIVITY_TYPE_33(33, EventKey.ACTOR_RECHARGE_EVENT),
    /**
     * 32.神器-神龙点穴
     */
    ACTIVITY_TYPE_32(32, EventKey.ACTOR_RECHARGE_EVENT),
    /**
     * 34.天天充值
     */
    ACTIVITY_TYPE_34(34, EventKey.ACTOR_RECHARGE_EVENT),
    /**
     * 35.狂欢庆典
     */
    ACTIVITY_TYPE_35(35, EventKey.ACTOR_RECHARGE_EVENT),
    /**
     * 36.登录有礼 同[36, 48, 49]
     */
    ACTIVITY_TYPE_36(36, EventKey.ACTOR_RECHARGE_EVENT),
    /**
     * 37.天赐神将-排行榜
     */
    ACTIVITY_TYPE_37(37),
    /**
     * 38.充值每周礼包 同[6, 38, 39]
     */
    ACTIVITY_TYPE_38(38, EventKey.ACTOR_RECHARGE_EVENT),
    /**
     * 39.充值每周礼包 同[6, 38, 39]
     */
    ACTIVITY_TYPE_39(39, EventKey.ACTOR_RECHARGE_EVENT),
    /**
     * 40.定制礼包 同[7, 40]
     */
    ACTIVITY_TYPE_40(40, EventKey.ACTOR_RECHARGE_EVENT),
    /**
     * 41.累计充值 同[9, 41, 42, 43, 44, 45]
     */
    ACTIVITY_TYPE_41(41, EventKey.ACTOR_RECHARGE_EVENT),
    /**
     * 42.累计充值 同[9, 41, 42, 43, 44, 45]
     */
    ACTIVITY_TYPE_42(42, EventKey.ACTOR_RECHARGE_EVENT),
    /**
     * 43.累计充值 同[9, 41, 42, 43, 44, 45]
     */
    ACTIVITY_TYPE_43(43, EventKey.ACTOR_RECHARGE_EVENT),
    /**
     * 44.累计充值 同[9, 41, 42, 43, 44, 45]
     */
    ACTIVITY_TYPE_44(44, EventKey.ACTOR_RECHARGE_EVENT),
    /**
     * 45.累计充值 同[9, 41, 42, 43, 44, 45]
     */
    ACTIVITY_TYPE_45(45, EventKey.ACTOR_RECHARGE_EVENT),
    /**
     * 46.限时商人 同[13, 46, 47]
     */
    ACTIVITY_TYPE_46(46, EventKey.ACTOR_RECHARGE_EVENT),
    /**
     * 47.限时商人 同[13, 46, 47]
     */
    ACTIVITY_TYPE_47(47, EventKey.ACTOR_RECHARGE_EVENT),
    /**
     * 48.登录有礼 同[36, 48, 49]
     */
    ACTIVITY_TYPE_48(48, EventKey.ACTOR_RECHARGE_EVENT),
    /**
     * 49.登录有礼 同[36, 48, 49]
     */
    ACTIVITY_TYPE_49(49, EventKey.ACTOR_RECHARGE_EVENT),
    /**
     * 50.战力达标
     */
    ACTIVITY_TYPE_50(50),
    /**
     * 51.群雄逐鹿（跨服版）
     */
    ACTIVITY_TYPE_51(51, EventKey.ARSENAL_CHALLENGE_EVENT, EventKey.GACHA_EVENT, EventKey.RECRUIT_GACHA_EVENT, EventKey.ACTOR_POWER_MAX_LEVEL_EVENT,
        EventKey.STORY_PASS_MAX_LEVEL_EVENT, EventKey.EQUIPMENT_SCORE_CHANGE_EVENT, EventKey.TREASURE_SCORE_CHANGE_EVENT, EventKey.IMMORTALS_MANUAL_ACTIVATE_EVENT,
        EventKey.IMMORTALS_STAR_UP_EVENT, EventKey.LINEUP_CHANGE_EQUIPMENT, EventKey.LINEUP_CHANGE_TREASURE, EventKey.UPDATE_IMMORTALS_MANUAL_EVENT,
        EventKey.REPUTATION_EVENT_FINISH_EVENT),
    /**
     * 52.招兵买马 同[14,52]
     */
    ACTIVITY_TYPE_52(52),
    /**
     * 53.星布棋局
     */
    ACTIVITY_TYPE_53(53, EventKey.ACTOR_RECHARGE_EVENT),
    /**
     * 54.任务战令新
     */
    ACTIVITY_TYPE_54(54, EventKey.ACTOR_RECHARGE_EVENT),
    /**
     * 55.合战锦标赛
     */
    ACTIVITY_TYPE_55(55, EventKey.ACTIVITY_55_BATTLE_EVENT, EventKey.ACTOR_RECHARGE_EVENT),
    /**
     * 56.枪出如龙
     */
    ACTIVITY_TYPE_56(56, EventKey.ACTOR_RECHARGE_EVENT),
    /**
     * 61.累天充值
     * id:activity_57_gift_config.id
     */
    ACTIVITY_TYPE_61(61, EventKey.ACTOR_RECHARGE_EVENT),
    /**
     * 63.超值商店
     */
    ACTIVITY_TYPE_63(63),
    /**
     * 64.超值兑换
     */
    ACTIVITY_TYPE_64(64),
    /**
     * 65.超值周卡
     */
    ACTIVITY_TYPE_65(65, EventKey.ACTOR_RECHARGE_EVENT),
    /**
     * 66.活跃挑战
     */
    ACTIVITY_TYPE_66(66),
    /**
     * 67.尊享礼包 同[6, 38, 39, 60, 67] 67客户端多一个跳转按钮
     */
    ACTIVITY_TYPE_67(67, EventKey.ACTOR_RECHARGE_EVENT),
    /**
     * 68.心愿招募
     */
    ACTIVITY_TYPE_68(68, EventKey.ACTOR_RECHARGE_EVENT),
    /**
     * 69.宝藏探秘
     */
    ACTIVITY_TYPE_69(69, EventKey.ACTOR_RECHARGE_EVENT),
    /**
     * 70.明灯祈福——签到
     */
    ACTIVITY_TYPE_70(70),
    /**
     * 71.战令佳赏——组合战令（同 71，79）
     */
    ACTIVITY_TYPE_71(71, EventKey.ACTOR_RECHARGE_EVENT),
    /**
     * 72.传送带礼包
     */
    ACTIVITY_TYPE_72(72, EventKey.ACTOR_RECHARGE_EVENT),
    /**
     * 73.小游戏
     */
    ACTIVITY_TYPE_73(73),
    /**
     * 78.霓裳罗衣
     */
    ACTIVITY_TYPE_78(78, EventKey.ACTOR_RECHARGE_EVENT),
    /**
     * 79.霓裳罗衣——组合战令 （同 71，79）
     */
    ACTIVITY_TYPE_79(79, EventKey.ACTOR_RECHARGE_EVENT),
    /**
     * 1000.合服
     */
    ACTIVITY_TYPE_1000(1000),
    /**
     * 0.none
     */
    NONE(0);
    /**
     * ID
     */
    private int id;
    /**
     * 事件列表
     */
    private Set<String> eventNames = Sets.newHashSet();

    private ActivityType(int id, String... eventNames) {
        this.id = id;
        for (String eventName : eventNames) {
            this.eventNames.add(eventName);
        }
    }

    public static ActivityType getType(int id) {
        for (ActivityType type : ActivityType.values()) {
            if (type.getId() == id) {
                return type;
            }
        }
        return null;
    }

    public int getId() {
        return id;
    }

    public Set<String> getEventNames() {
        return eventNames;
    }

    @Override
    public String toString() {
        return String.valueOf(id);
    }
}
