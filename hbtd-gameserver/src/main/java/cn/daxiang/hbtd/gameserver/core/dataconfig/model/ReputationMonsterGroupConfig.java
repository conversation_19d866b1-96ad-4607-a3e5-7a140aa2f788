package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.dataconfig.annotation.FieldIgnore;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.shared.reward.RewardObject;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;

import java.util.Collection;

/**
 * @author: <PERSON>
 * @date: 2023/3/14 14:45
 * @Description:
 */
@DataFile(fileName = "reputation_monster_group_config")
public class ReputationMonsterGroupConfig implements ModelAdapter {

    /**
     * id
     */
    private int id;
    /**
     * 战斗组
     */
    private int monsterGroup;
    /**
     * 怪物Id
     */
    private int monsterId;
    /**
     *
     */
    private int weight;
    /**
     *
     */
    private String reward;

    @FieldIgnore
    private Collection<RewardObject> rewardList = Lists.newArrayList();

    @Override
    public void initialize() {

        JSONArray rewardsArray = JSONArray.parseArray(reward);
        for (Object rewardsItem : rewardsArray) {
            JSONArray rewardArray = JSONArray.parseArray(rewardsItem.toString());
            RewardObject rewardObject = RewardObject.valueOf(rewardArray);
            rewardList.add(rewardObject);
        }
    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(id);
    }

    public int getId() {
        return id;
    }

    public int getMonsterGroup() {
        return monsterGroup;
    }

    public int getMonsterId() {
        return monsterId;
    }

    public int getWeight() {
        return weight;
    }

    public Collection<RewardObject> getRewardList() {
        return rewardList;
    }
}
