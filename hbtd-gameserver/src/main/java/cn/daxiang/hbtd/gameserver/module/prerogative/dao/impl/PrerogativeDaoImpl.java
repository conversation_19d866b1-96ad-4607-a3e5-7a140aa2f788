package cn.daxiang.hbtd.gameserver.module.prerogative.dao.impl;

import cn.daxiang.framework.database.SingleEntity;
import cn.daxiang.framework.database.dao.SingleEntityDaoImpl;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.hbtd.gameserver.core.database.table.Prerogative;
import cn.daxiang.hbtd.gameserver.module.prerogative.dao.PrerogativeDao;
import org.springframework.stereotype.Component;

@Component
public class PrerogativeDaoImpl extends SingleEntityDaoImpl implements PrerogativeDao {

    @Override
    public Prerogative getPrerogative(long actorId) {
        Prerogative table = this.get(IdentiyKey.build(actorId));
        if (table.newEntity()) {
            table.setNewEntity(false);
            dbQueue.updateQueue(table);
        }
        return table;
    }

    @Override
    protected Class<? extends SingleEntity<?>> forClass() {
        return Prerogative.class;
    }

    @Override
    protected void initMaxId() {
    }

}
