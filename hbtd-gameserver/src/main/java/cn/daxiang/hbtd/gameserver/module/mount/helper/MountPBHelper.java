package cn.daxiang.hbtd.gameserver.module.mount.helper;

import cn.daxiang.hbtd.gameserver.core.database.table.Mount;
import cn.daxiang.hbtd.gameserver.core.database.table.MountActor;
import cn.daxiang.hbtd.gameserver.module.mount.module.MountPrivilegeEntity;
import cn.daxiang.protocol.game.CommonProtocol;
import cn.daxiang.protocol.game.MountProtocol;

import java.util.Collection;

/**
 * 坐骑
 *
 * <AUTHOR>
 * @date 2024/4/8
 */
public class MountPBHelper {

    public static MountProtocol.MountInfoResponse buildMountInfoResponse(MountActor mountActor) {
        MountProtocol.MountInfoResponse.Builder builder = MountProtocol.MountInfoResponse.newBuilder();
        builder.setMountPlatformLevel(mountActor.getMountPlatformLevel());
        for (MountPrivilegeEntity privilegeEntity : mountActor.getPrivilegeMap().values()) {
            builder.addPrivileges(buildMountPrivilegeEntity(privilegeEntity));
        }
        builder.setWishMountCid(mountActor.getWishMountCid());
        builder.setGachaUseFreeTimes(mountActor.getGachaUseFreeTimes());
        builder.setGachaUseDiamondTimes(mountActor.getGachaUseDiamondTimes());
        builder.setGachaIntegral(mountActor.getGachaIntegral());
        builder.addAllIntegralRewardReceive(mountActor.getIntegralRewardReceiveList());
        builder.putAllDayBuyTimes(mountActor.getDayBuyTimes());
        builder.putAllBuyTimes(mountActor.getBuyTimes());
        builder.putAllReceives(mountActor.getReceives());
        return builder.build();
    }

    public static MountProtocol.MountListResponse buildMountListResponse(Collection<Mount> list) {
        MountProtocol.MountListResponse.Builder builder = MountProtocol.MountListResponse.newBuilder();
        for (Mount mount : list) {
            builder.addInfos(buildMountInfo(mount));
        }
        return builder.build();
    }

    public static CommonProtocol.Mount buildMountInfo(Mount mount) {
        CommonProtocol.Mount.Builder builder = CommonProtocol.Mount.newBuilder();
        builder.setCid(mount.getCid());
        builder.setStarLevel(mount.getStarLevel());
        builder.setHeroUid(mount.getHeroUid());
        return builder.build();
    }

    public static MountProtocol.MountPrivilegeEntity buildMountPrivilegeEntity(MountPrivilegeEntity privilegeEntity) {
        MountProtocol.MountPrivilegeEntity.Builder builder = MountProtocol.MountPrivilegeEntity.newBuilder();
        builder.setType(privilegeEntity.getType());
        builder.setCount(privilegeEntity.getCount());
        builder.setLastReceiveTime(privilegeEntity.getLastReceiveTime());
        return builder.build();
    }
}
