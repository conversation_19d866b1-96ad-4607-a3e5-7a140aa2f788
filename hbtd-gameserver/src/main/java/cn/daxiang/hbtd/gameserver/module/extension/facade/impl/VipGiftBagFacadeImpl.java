package cn.daxiang.hbtd.gameserver.module.extension.facade.impl;

import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.hbtd.gameserver.core.GameBaseFacade;
import cn.daxiang.hbtd.gameserver.core.database.table.Actor;
import cn.daxiang.hbtd.gameserver.core.database.table.VipGiftBag;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.VipConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.VipConfigService;
import cn.daxiang.hbtd.gameserver.core.oss.type.OperationType;
import cn.daxiang.hbtd.gameserver.module.extension.dao.VipGiftBagDao;
import cn.daxiang.hbtd.gameserver.module.extension.facade.VipGiftBagFacade;
import cn.daxiang.hbtd.gameserver.module.extension.helper.ExtensionPushHelper;
import cn.daxiang.hbtd.gameserver.module.goods.helper.RewardHelper;
import cn.daxiang.hbtd.gameserver.module.user.facade.ActorFacade;
import cn.daxiang.protocol.game.CommonProtocol;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class VipGiftBagFacadeImpl extends GameBaseFacade implements VipGiftBagFacade {

    @Autowired
    private VipGiftBagDao vipGiftBagDao;
    @Autowired
    private ActorFacade actorFacade;

    @Override
    public TResult<VipGiftBag> getVipGiftBag(long actorId) {
        VipGiftBag vipGiftBag = vipGiftBagDao.getVipGiftBag(actorId);
        return TResult.sucess(vipGiftBag);
    }

    @Override
    public TResult<CommonProtocol.RewardResult> buyGiftBag(long actorId, int vipLevel) {
        VipConfig vipConfig = VipConfigService.getVipConfig(vipLevel);
        if (vipConfig == null) {
            LOGGER.error("vipLevel not found,vipLevel:{}", vipLevel);
            return TResult.valueOf(CONFIG_NOT_FOUND);
        }
        TResult<Actor> actorResult = actorFacade.getActor(actorId);
        if (actorResult.isFail()) {
            return TResult.valueOf(actorResult.statusCode);
        }
        if (actorResult.item.getVipLevel() < vipConfig.getVip()) {
            return TResult.valueOf(ACTOR_VIP_LEVEL_NOT_ENOUGH);
        }
        TResult<VipGiftBag> vipGiftBagResult = this.getVipGiftBag(actorId);
        if (vipGiftBagResult.isFail()) {
            return TResult.valueOf(vipGiftBagResult.statusCode);
        }
        VipGiftBag vipGiftBag = vipGiftBagResult.item;
        if (vipGiftBag.isBuy(vipLevel)) {
            return TResult.valueOf(GIFT_BAG_ALREADY_BUY);
        }
        Result result = RewardHelper.decrease(actorId, vipConfig.getActPriceList(), OperationType.VIP_GIFT_BAG_BUY);
        if (result.isFail()) {
            return TResult.valueOf(result.statusCode);
        }
        CommonProtocol.RewardResult rewardResult = RewardHelper.sendRewardList(actorId, vipConfig.getRewardList(), OperationType.VIP_GIFT_BAG_BUY);
        vipGiftBag.buy(vipLevel);
        dbQueue.updateQueue(vipGiftBag);
        ExtensionPushHelper.pushVipGiftBag(actorId, vipGiftBag);
        return TResult.sucess(rewardResult);
    }

}
