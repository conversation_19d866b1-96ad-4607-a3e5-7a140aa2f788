package cn.daxiang.hbtd.gameserver.module.beast.module;

/**
 * 神兽特权实体
 *
 * @author: <PERSON>
 * @date: 2023/5/31 17:33
 * @Description:
 */
public class BeastPrivilegeEntity {
    /**
     * 特权类型
     */
    private int type;
    /**
     * 已领取天数
     */
    private int count;
    /**
     * 特权抽奖次数
     */
    private int gachaTimes;
    /**
     * 最后一次领取奖励时间
     */
    private long lastReceiveTime;

    public static BeastPrivilegeEntity valueOf(int type) {
        BeastPrivilegeEntity entity = new BeastPrivilegeEntity();
        entity.setType(type);
        return entity;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public int getGachaTimes() {
        return gachaTimes;
    }

    public void setGachaTimes(int gachaTimes) {
        this.gachaTimes = gachaTimes;
    }

    public long getLastReceiveTime() {
        return lastReceiveTime;
    }

    public void setLastReceiveTime(long lastReceiveTime) {
        this.lastReceiveTime = lastReceiveTime;
    }

    public void addTimes(int times) {
        this.gachaTimes += times;
    }
}
