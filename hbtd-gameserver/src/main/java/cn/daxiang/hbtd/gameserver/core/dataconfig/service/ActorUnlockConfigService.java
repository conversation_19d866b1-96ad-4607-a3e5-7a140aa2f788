package cn.daxiang.hbtd.gameserver.core.dataconfig.service;

import cn.daxiang.framework.dataconfig.ConfigServiceAdapter;
import cn.daxiang.framework.rpc.event.DataConfigReloadEvent;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.ActorUnlockConfig;
import cn.daxiang.hbtd.gameserver.core.dispatch.DispatchHelper;
import cn.daxiang.hbtd.gameserver.module.user.type.ActorUnlockType;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;

@Component
public class ActorUnlockConfigService extends ConfigServiceAdapter {

    private static Map<Integer, ActorUnlockConfig> ACTOR_UNLOCK_MAP = Maps.newHashMap();

    private static Map<ActorUnlockType, Map<Integer, ActorUnlockConfig>> ACTOR_UNLOCK_TYPE_MAP = Maps.newHashMap();

    public static ActorUnlockConfig getActorUnlockConfig(int id) {
        return ACTOR_UNLOCK_MAP.get(id);
    }

    public static ActorUnlockConfig getActorUnlockConfig(ActorUnlockType type, int orderId) {
        Map<Integer, ActorUnlockConfig> unlockMap = ACTOR_UNLOCK_TYPE_MAP.get(type);
        if (unlockMap == null) {
            return null;
        }
        return unlockMap.get(orderId);
    }

    @Override
    protected void initialize() {
        Collection<ActorUnlockConfig> configList = dataConfig.listAll(this, ActorUnlockConfig.class);
        for (ActorUnlockConfig actorUnlockConfig : configList) {
            if (ACTOR_UNLOCK_MAP.containsKey(actorUnlockConfig.getId())) {
                LOGGER.error("ActorUnlockConfig repetitive error, id:{}", actorUnlockConfig.getId());
                continue;
            }
            ACTOR_UNLOCK_MAP.put(actorUnlockConfig.getId(), actorUnlockConfig);
            if (actorUnlockConfig.getUnlockType() == ActorUnlockType.NONE) {
                continue;
            }
            Map<Integer, ActorUnlockConfig> unlockMap = ACTOR_UNLOCK_TYPE_MAP.get(actorUnlockConfig.getUnlockType());
            if (unlockMap == null) {
                unlockMap = Maps.newHashMap();
                ACTOR_UNLOCK_TYPE_MAP.put(actorUnlockConfig.getUnlockType(), unlockMap);
            }
            unlockMap.put(actorUnlockConfig.getOrderId(), actorUnlockConfig);
        }
    }

    @Override
    public void reload() {
        DispatchHelper.postEvent(new DataConfigReloadEvent(this));
    }

    @Override
    protected void clean() {
        ACTOR_UNLOCK_MAP.clear();
        ACTOR_UNLOCK_TYPE_MAP.clear();
    }
}
