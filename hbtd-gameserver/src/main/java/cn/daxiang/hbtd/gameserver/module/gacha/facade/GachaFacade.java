package cn.daxiang.hbtd.gameserver.module.gacha.facade;

import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.hbtd.gameserver.core.database.table.Gacha;
import cn.daxiang.hbtd.gameserver.core.database.table.GachaHero;
import cn.daxiang.protocol.game.GachaProtocol.GachaResultResponse;

import java.util.Set;

/**
 * 抽奖业务处理接口
 *
 * <AUTHOR>
 */
public interface GachaFacade {
    /**
     * 获取抽奖信息
     *
     * @param actorId
     * @return
     */
    TResult<Gacha> getGacha(long actorId);

    /**
     * 抽奖
     *
     * @param actorId
     * @param gachaId
     * @param times
     * @param useGoods
     * @return
     */
    TResult<GachaResultResponse> gacha(long actorId, int gachaId, int times, boolean useGoods);

    /**
     * 获取招募点将台信息
     *
     * @param actorId
     * @return
     */
    TResult<GachaHero> getGacha<PERSON>ero(long actorId);

    /**
     * 选择心愿武将
     *
     * @param actorId
     * @param heroIds
     * @return
     */
    Result wishChoice(long actorId, Set<Integer> heroIds);

    /**
     * 选择祝福武将
     *
     * @param actorId
     * @param heroId
     * @return
     */
    Result blessChoice(long actorId, int heroId);

    /**
     * 领取祝福武将
     *
     * @param actorId
     * @return
     */
    TResult<GachaResultResponse> blessReceive(long actorId);
}
