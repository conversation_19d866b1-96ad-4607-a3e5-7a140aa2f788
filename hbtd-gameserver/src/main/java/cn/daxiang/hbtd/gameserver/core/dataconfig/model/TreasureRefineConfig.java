package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.dataconfig.annotation.FieldIgnore;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.shared.module.lineup.SpriteAttributeType;
import cn.daxiang.shared.reward.RewardObject;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.List;
import java.util.Map;

/**
 * 宝物精炼配置
 *
 * <AUTHOR>
 */
@DataFile(fileName = "treasure_evolve_config")
public class TreasureRefineConfig implements ModelAdapter {
    /**
     * 宝物ID
     */
    private int id;
    /**
     * 精炼等级
     */
    private int evolveLevel;
    /**
     * 消耗([[rewardType,id,num],[rewardType,id,num]])
     * {@code RewardType}
     */
    private String cost;
    /**
     * 属性加成([[type,value],[type,value]])
     * {@code SpriteAttributeType}
     */
    private String attributes;
    /**
     * 特殊ID
     */
    private int specialEffectId;
    /**
     * 精炼属性万分比Map
     * key:SpriteAttributeType,value:ttpercent
     */
    @FieldIgnore
    private Map<SpriteAttributeType, Integer> attributeTTPercentMap = Maps.newHashMap();
    /**
     * 精炼属性Map
     * key:SpriteAttributeType,value:value
     */
    @FieldIgnore
    private Map<SpriteAttributeType, Long> attributeMap = Maps.newHashMap();
    /**
     * 消耗列表
     */
    @FieldIgnore
    private List<RewardObject> costList = Lists.newArrayList();

    @Override
    public void initialize() {
        JSONArray evolveAttributeArray = JSONArray.parseArray(attributes);
        for (Object evolveAttributeItem : evolveAttributeArray) {
            JSONArray attributeArray = JSONArray.parseArray(evolveAttributeItem.toString());
            if (attributeArray.getIntValue(0) > 100) {
                attributeTTPercentMap.put(SpriteAttributeType.getType(attributeArray.getIntValue(0) - 100), attributeArray.getInteger(1));
            } else {
                attributeMap.put(SpriteAttributeType.getType(attributeArray.getIntValue(0)), attributeArray.getLong(1));
            }
        }
        JSONArray costArray = JSONArray.parseArray(cost);
        for (Object costItem : costArray) {
            JSONArray rewardArray = JSONArray.parseArray(costItem.toString());
            RewardObject rewardObject = RewardObject.valueOf(rewardArray);
            costList.add(rewardObject);
        }
    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(id, evolveLevel);
    }

    public int getId() {
        return id;
    }

    public int getEvolveLevel() {
        return evolveLevel;
    }

    public Map<SpriteAttributeType, Integer> getAttributeTTPercentMap() {
        return attributeTTPercentMap;
    }

    public Map<SpriteAttributeType, Long> getAttributeMap() {
        return attributeMap;
    }

    public List<RewardObject> getCostList() {
        return costList;
    }

    public int getSpecialEffectId() {
        return specialEffectId;
    }
}
