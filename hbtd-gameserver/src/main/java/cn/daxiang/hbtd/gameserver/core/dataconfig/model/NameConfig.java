package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.identity.IdentiyKey;

@DataFile(fileName = "name_config")
public class NameConfig implements ModelAdapter {

    private int id;
    /**
     * 姓
     */
    private String firstName;
    /**
     * 中间名
     */
    private String lastMaleName;
    /**
     * 最后一个字
     */
    private String lastFemaleName;

    @Override
    public void initialize() {

    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(id);
    }

    public int getId() {
        return id;
    }

    public String getFirstName() {
        return firstName;
    }

    public String getLastMaleName() {
        return lastMaleName;
    }

    public String getLastFemaleName() {
        return lastFemaleName;
    }

}
