package cn.daxiang.hbtd.gameserver.module.battle.parser.effect.impl;

import cn.daxiang.framework.extend.KeyValue;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.framework.utils.NumberUtils;
import cn.daxiang.framework.utils.RandomUtils;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.BeastConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.SkillConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.SkillEffectConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.GlobalConfigService;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.SkillConfigService;
import cn.daxiang.hbtd.gameserver.module.battle.helper.BuffHelper;
import cn.daxiang.hbtd.gameserver.module.battle.helper.FightHelper;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleBuff;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleMember;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleRoom;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleSprite;
import cn.daxiang.hbtd.gameserver.module.battle.model.FightEffect;
import cn.daxiang.hbtd.gameserver.module.battle.model.FightReport;
import cn.daxiang.hbtd.gameserver.module.battle.parser.effect.AbstractSkillEffectParser;
import cn.daxiang.hbtd.gameserver.module.battle.type.SkillEffectType;
import cn.daxiang.hbtd.gameserver.module.battle.type.SkillTriggerType;
import cn.daxiang.hbtd.gameserver.module.beast.type.BeastAttributeType;
import cn.daxiang.protocol.game.BattleProtocol;
import cn.daxiang.protocol.game.BattleProtocol.SpriteKey;
import cn.daxiang.protocol.game.BattleProtocol.SpriteType;
import cn.daxiang.shared.GlobalConfigKey;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * 伤害百分比效果解析器
 *
 * <AUTHOR>
 */
@Component
public class DamageEffectParser extends AbstractSkillEffectParser {
    @Autowired
    private GlobalConfigService globalConfigService;

    @Override
    protected SkillEffectType getType() {
        return SkillEffectType.DAMAGE_EFFECT;
    }

    @Override
    public boolean execute(FightReport report, BattleSprite attacker, BattleSprite targeter, SkillConfig skillConfig, SkillEffectConfig effectConfig, BattleRoom battleRoom,
        long triggerValue) {
        if (targeter.isDead()) {
            return false;
        }
        int startHpPercent = targeter.getHPPercent();
        if (targeter.hasBuffType(SkillEffectType.IMMUNE_DAMAGE_BUFF_EFFECT)) {
            report.addFightEffect(FightEffect.immune(targeter, effectConfig.getEffectId()));
            targeter.setLastDamageSpriteId(attacker.getSpriteId());
            //反击
            FightHelper.processSkill(battleRoom, targeter, attacker, targeter, skillConfig, SkillTriggerType.STRIKE_BACK);
            //反击必中
            FightHelper.processSkill(battleRoom, targeter, attacker, targeter, skillConfig, SkillTriggerType.MUST_HIT_STRIKE_BACK);
            return true;
        }
        long substituteValue = targeter.getBuffValue(SkillEffectType.SUBSTITUTE_BUFF_EFFECT);
        if (substituteValue > 0) {
            FightEffect substituteEffect = FightEffect.substitute(targeter.getSpriteId(), effectConfig.getEffectId());
            report.addFightEffect(substituteEffect);
            targeter.decreaseBuffValueByDesc(SkillEffectType.SUBSTITUTE_BUFF_EFFECT, report, 1);
            return true;
        }
        /**
         * x1:伤害值
         * x2:场上友军数量
         * X3：目标身上是否有灼烧效果
         * X4：目标身上是否有中毒效果
         * X5：目标身上是否有眩晕效果
         * X6：目标身上是否有debuff效果
         * X7：目标剩余血量（万分比）
         * X8：自身剩余血量（万分比）
         * X9：目标剩余血量（绝对值）
         * X10：自身剩余血量（绝对值）
         * x11:目标怒气值
         * x12:是否是BOSS
         * x13:自身怒气值
         * x14:目标职业
         * x15:目标性别
         * X16：自身击杀人数（累计）
         * x17:目标身上是否有冥炎效果
         * x18:场上人数
         * x19:我方全体武将平均攻击力(全体武将)
         * X20:自身最大生命值（绝对值）
         * X21:是否持有背水增强效果
         * X22:是否持有瞄准增强效果
         * X23:八门开放层数
         * X24:目标最大生命值
         * x25:触发值
         * x26:回合内触发次数
         */
        List<BattleSprite> friendlySprite = battleRoom.getBattleSpriteList(attacker.getBattleCamp());
        BattleProtocol.BattleCamp oppositeCamp = FightHelper.BattleCampOpposite(attacker.getBattleCamp());
        long attackerAttack = attacker.getAttack();
        int friendlySpriteCount = battleRoom.getSpriteCount(attacker.getBattleCamp());
        boolean hasBurnBuff = targeter.hasBuffType(SkillEffectType.BURN_BUFF_EFFECT);
        boolean hasPoisonBuff = targeter.hasBuffType(SkillEffectType.POISON_BUFF_EFFECT);
        boolean hasStunBuff = targeter.hasBuffType(SkillEffectType.STUN_BUFF_EFFECT) || targeter.hasBuffType(SkillEffectType.SUPER_STUN_BUFF_EFFECT);
        boolean hasDeBuff = targeter.hasDebuff();
        int targetHPPercent = targeter.getHPPercent();
        long targetHP = targeter.getSpriteBattle().getHP();
        int attackerHPPercent = attacker.getHPPercent();
        long attackerHP = attacker.getSpriteBattle().getHP();
        long targetRage = targeter.getSpriteBattle().getRage();
        boolean isBoss = battleRoom.getBattleType().isBoss();
        int rage = attacker.getSpriteBattle().getRage();
        int heroVocationType = targeter.getHeroVocationType().getId();
        int heroSexType = targeter.getHeroSexType().getId();
        int targetKillCount = attacker.getSpriteBattle().getKillCount(SkillEffectType.NONE);
        boolean hasHellFireBuff = targeter.hasBuffType(SkillEffectType.HELL_FIRE_BUFF_EFFECT);
        int aliveCount = friendlySpriteCount + battleRoom.getSpriteCount(oppositeCamp);
        long attackSum = 0;
        int spriteCount = 0;
        for (BattleSprite sprite : friendlySprite) {
            attackSum += sprite.getAttack();
            spriteCount++;
        }
        long attackAverage = spriteCount == 0 ? 0 : attackSum / spriteCount;
        long attackerHPMax = attacker.getSpriteBattle().getHPMax();
        Collection<BattleBuff> desperatelyBuffList = attacker.getBuffList(SkillEffectType.DESPERATELY_BUFF_EFFECT);
        Collection<BattleBuff> aimingUpBuffList = attacker.getBuffList(SkillEffectType.AIMING_UP_BUFF_EFFECT);
        long targeterHPMax = targeter.getSpriteBattle().getHPMax();
        int roundTimes = 1;
        if (attacker.getBattleSkill(skillConfig.getSkillId()) != null) {
            roundTimes = attacker.getBattleSkill(skillConfig.getSkillId()).getRoundTimes();
        }
        long damage =
            effectConfig.calcSkillEffect(attackerAttack, friendlySpriteCount, hasBurnBuff, hasPoisonBuff, hasStunBuff, hasDeBuff, targetHPPercent, attackerHPPercent, targetHP,
                attackerHP, targetRage, isBoss, rage, heroVocationType, heroSexType, targetKillCount, hasHellFireBuff, aliveCount, attackAverage, attackerHPMax,
                !desperatelyBuffList.isEmpty(), !aimingUpBuffList.isEmpty(), attacker.getEightGateId(), targeterHPMax, triggerValue, roundTimes);
        long vampireDamage = 0;
        // 计算暴击
        int crit = attacker.getCrit();
        int toughness = attacker.getSpriteType() == BattleProtocol.SpriteType.SPRITE_BEAST ? targeter.getSpriteBattle().getBeastToughness() : targeter.getToughness();
        boolean critical = false;
        // 是否触发会心一击
        boolean isCriticalOne = false;
        int criticalOne = (int) BuffHelper.getBuffValue(attacker, SkillEffectType.CRITICAL_BUFF_EFFECT);
        int criticalReductionOne = (int) BuffHelper.getBuffValue(targeter, SkillEffectType.CRITICAL_REDUCTION_BUFF_EFFECT);
        int criticalOneProbability = criticalOne - criticalReductionOne;
        if (criticalOneProbability > 0 && skillConfig.getTriggerType() == SkillTriggerType.NORMAL_ATTACK) {
            if (RandomUtils.is10000Hit(criticalOneProbability)) {
                isCriticalOne = true;
            }
        }

        //攻击方暴击率大于受击方抗暴率则有几率暴击
        if (toughness <= crit && !isCriticalOne) {
            int critPercent = crit - toughness;
            //如果攻击方的攻击力小于受击方的防御则该伤害的暴击率下降
            long armor = getArmor(report, targeter);
            if (attacker.getAttack() < armor) {
                long rate = attacker.getAttack() / (armor * 2);
                critPercent = (int) (critPercent * rate);
            }
            //pvp最大暴击概率
            int maxCritPercent = globalConfigService.findGlobalConfig(GlobalConfigKey.PVP_MAX_CRITICAL_PERCENT).findInt();
            critPercent = Math.min(maxCritPercent, critPercent);
            critical = RandomUtils.is10000Hit(critPercent);
        }
        //计算格挡
        int block = targeter.getBlock();
        int smash = attacker.getSmash();
        boolean isBlock = false;
        //攻击方破击率小于受击方格挡率则有几率被格挡
        if (smash <= block) {
            int blockPercent = block - smash;
            //如果攻击方的攻击力小于受击方的防御则该伤害的格挡率上升
            if (attacker.getAttack() < getArmor(report, targeter)) {
                long rate = (targeter.getPhysicalArmor() * 2) / attacker.getAttack();
                blockPercent = (int) (blockPercent * rate);
            }
            //pvp最大格挡概率
            int maxBlockPercent = globalConfigService.findGlobalConfig(GlobalConfigKey.PVP_MAX_BLOCK_PERCENT).findInt();
            blockPercent = Math.min(maxBlockPercent, blockPercent);
            isBlock = RandomUtils.is10000Hit(blockPercent);
        }
        //队友给我加的分摊伤害buff
        Collection<BattleBuff> sharingDamageBuffList = targeter.getBuffList(SkillEffectType.SHARING_DAMAGE_BUFF_EFFECT);
        if (!sharingDamageBuffList.isEmpty()) {
            BattleBuff sharingDamageBuff = sharingDamageBuffList.iterator().next();
            SkillEffectConfig sharingDamageBuffConfig = SkillConfigService.getSkillEffectConfig(sharingDamageBuff.getEffectId());
            BattleSprite sharingDamageBuffCaster = battleRoom.getBattleSprite(sharingDamageBuff.getCastSpriteUid());
            if (!sharingDamageBuffCaster.isDead()) {
                long sharingDamage = sharingDamageBuffConfig.calcSkillEffect(damage, sharingDamageBuffCaster.getSpriteBattle().getHPMax());
                damage -= sharingDamage;
                TResult<Long> vampireDamageResult =
                    this.damageEffect(report, attacker, sharingDamageBuffCaster, skillConfig, effectConfig, battleRoom, sharingDamage, false, false, true, startHpPercent, false,
                        isCriticalOne);
                if (vampireDamageResult.isFail()) {
                    return true;
                }
                vampireDamage += vampireDamageResult.item;
            }
        }
        //受击者携带的把伤害分摊给所有活着队友的buff
        Collection<BattleBuff> sharingAllFriendBuffList = targeter.getBuffList(SkillEffectType.SHARING_DAMAGE_ALL_FRIEND_BUFF_EFFECT);
        if (!sharingAllFriendBuffList.isEmpty()) {
            BattleBuff sharingDamageBuff = sharingAllFriendBuffList.iterator().next();
            SkillEffectConfig sharingDamageBuffConfig = SkillConfigService.getSkillEffectConfig(sharingDamageBuff.getEffectId());
            List<BattleSprite> sharingDamageList = battleRoom.getAliveSprite(targeter.getBattleCamp());
            //分摊给所有人之前的伤害
            long initDamage = damage;
            for (BattleSprite battleSprite : sharingDamageList) {
                if (battleSprite.getSpriteId() == targeter.getSpriteId()) {
                    continue;
                }
                long sharingDamage = sharingDamageBuffConfig.calcSkillEffect(initDamage, sharingDamageList.size());
                damage -= sharingDamage;
                TResult<Long> vampireDamageResult =
                    this.damageEffect(report, attacker, battleSprite, skillConfig, effectConfig, battleRoom, sharingDamage, false, false, true, startHpPercent, true,
                        isCriticalOne);
                if (vampireDamageResult.isFail()) {
                    return true;
                }
                vampireDamage += vampireDamageResult.item;
            }
        }
        TResult<Long> vampireDamageResult =
            this.damageEffect(report, attacker, targeter, skillConfig, effectConfig, battleRoom, damage, critical, isBlock, false, startHpPercent, false, isCriticalOne);
        if (vampireDamageResult.isFail()) {
            return true;
        }
        vampireDamage += vampireDamageResult.item;
        if (critical) {
            // 暴击时触发技能(带目标)
            FightHelper.processSkillByTarget(battleRoom, attacker, SkillTriggerType.CRITICAL);
        }
        //如果攻击方有禁疗buff则不会计算吸血
        long vampire = BuffHelper.getBuffValue(attacker, SkillEffectType.VAMPIRE_BUFF_EFFECT, battleRoom, report);
        vampire = Math.min(10000, vampire + attacker.getSpriteBattle().getSuckBloodRatio());
        if (vampire > 0) {
            if (attacker.hasBuffType(SkillEffectType.FORBID_HEAL_BUFF_EFFECT)) {
                FightEffect forbidHealEffect = FightEffect.forbidHeal(targeter, FightEffect.VAMPIRE_EFFECT_ID);
                report.addFightEffect(forbidHealEffect);
            } else {
                long vampireHP = NumberUtils.getPercentLongValue(vampireDamage, (int) vampire);
                //计算恢复提升
                long healBonus = BuffHelper.getBuffValue(attacker, SkillEffectType.HEAL_BONUS_BUFF_EFFECT, battleRoom, report) + attacker.getSpriteBattle().getHealBonus();
                healBonus = Math.min(10000, healBonus);
                vampireHP += NumberUtils.getPercentLongValue(vampireHP, (int) healBonus);
                long healReduction = BuffHelper.getBuffValue(attacker, SkillEffectType.REDUCE_HEAL_BUFF_EFFECT, battleRoom, report);
                if (healReduction > 0) {
                    vampireHP -= NumberUtils.getPercentLongValue(vampireHP, (int) healReduction);
                }
                FightEffect vampireEffect = FightEffect.vampire(attacker, SpriteKey.HP, vampireHP);
                report.addFightEffect(vampireEffect);
            }
        }
        //神兽不被反击
        if (attacker.getSpriteType() != BattleProtocol.SpriteType.SPRITE_BEAST || attacker.getSpriteType() != SpriteType.SYSTEM) {
            //反击
            FightHelper.processSkill(battleRoom, targeter, attacker, targeter, skillConfig, SkillTriggerType.STRIKE_BACK);
            //反击必中
            FightHelper.processSkill(battleRoom, targeter, attacker, targeter, skillConfig, SkillTriggerType.MUST_HIT_STRIKE_BACK);
        }
        //造成伤害时
        FightHelper.processSkill(battleRoom, attacker, attacker, targeter, vampireDamageResult.item, skillConfig, SkillTriggerType.DEALING_DAMAGE, null);
        return true;
    }

    private TResult<Long> damageEffect(FightReport report, BattleSprite attacker, BattleSprite targeter, SkillConfig skillConfig, SkillEffectConfig effectConfig,
        BattleRoom battleRoom, long damage, boolean critical, boolean isBlock, boolean shared, int startHpPercent, boolean sharedAll, boolean isCriticalOne) {
        targeter.setLastDamageSpriteId(attacker.getSpriteId());
        //避免分担伤害之后产生的技能给target释放伤害免疫或者替身,导致target直接受到伤害
        if (targeter.hasBuffType(SkillEffectType.IMMUNE_DAMAGE_BUFF_EFFECT)) {
            report.addFightEffect(FightEffect.immune(targeter, effectConfig.getEffectId()));
            //反击
            FightHelper.processSkill(battleRoom, targeter, attacker, targeter, skillConfig, SkillTriggerType.STRIKE_BACK);
            //反击必中
            FightHelper.processSkill(battleRoom, targeter, attacker, targeter, skillConfig, SkillTriggerType.MUST_HIT_STRIKE_BACK);
            return TResult.fail();
        }
        if (skillConfig.getTriggerType() == SkillTriggerType.CAST && BuffHelper.getBuffValue(targeter, SkillEffectType.CAST_DAMAGE_EXEMPT_BUFF_EFFECT) > 0) {
            report.addFightEffect(FightEffect.exempt(targeter, effectConfig.getEffectId()));
            Collection<BattleBuff> buffList = targeter.getBuffList(SkillEffectType.CAST_DAMAGE_EXEMPT_BUFF_EFFECT);
            for (Iterator<BattleBuff> iterator = buffList.iterator(); iterator.hasNext(); ) {
                BattleBuff buff = iterator.next();
                iterator.remove();
                report.addFightEffect(FightEffect.removeBuff(targeter.getSpriteId(), buff));
            }
            //反击
            FightHelper.processSkill(battleRoom, targeter, attacker, targeter, skillConfig, SkillTriggerType.STRIKE_BACK);
            //反击必中
            FightHelper.processSkill(battleRoom, targeter, attacker, targeter, skillConfig, SkillTriggerType.MUST_HIT_STRIKE_BACK);
            return TResult.fail();
        }
        long substituteValue = targeter.getBuffValue(SkillEffectType.SUBSTITUTE_BUFF_EFFECT);
        if (substituteValue > 0) {
            FightEffect substituteEffect = FightEffect.substitute(targeter.getSpriteId(), effectConfig.getEffectId());
            report.addFightEffect(substituteEffect);
            targeter.decreaseBuffValueByDesc(SkillEffectType.SUBSTITUTE_BUFF_EFFECT, report, 1);
            return TResult.fail();
        }
        long attackerAttack = attacker.getAttack();
        //防御
        long armor = getArmor(report, targeter);
        //攻击方的无视防御属性万分比属性
        int ignore = Math.min(7500, attacker.getSpriteBattle().getIgnoreDefense());
        armor -= NumberUtils.getPercentLongValue(armor, ignore);
        //crw 特殊需求  设计者:冯程程   神兽伤害加成减免计算公式独立计算;
        //伤害减免万分比
        long damageReduction = 0;
        long damageBonus = 0;
        //神兽属性伤害减免
        if (attacker.getSpriteType() == BattleProtocol.SpriteType.SPRITE_BEAST) {
            damageBonus = attacker.getDamageBonus();
            BeastConfig beastConfig = globalConfigService.findConfig(IdentiyKey.build(attacker.getRoleId()), BeastConfig.class);
            if (beastConfig != null) {
                switch (BeastAttributeType.getType(beastConfig.getType())) {
                    case WIND:
                        damageReduction += targeter.getSpriteBattle().getBeastWindDamageReduction();
                        break;
                    case RAY:
                        damageReduction += targeter.getSpriteBattle().getBeastRayDamageReduction();
                        break;
                    case FIRE:
                        damageReduction += targeter.getSpriteBattle().getBeastFireDamageReduction();
                        break;
                    case WATER:
                        damageReduction += targeter.getSpriteBattle().getBeastWaterDamageReduction();
                        break;
                }
                damageReduction += targeter.getSpriteBattle().getBeastDamageReduction();
            }
        } else {
            damageReduction = targeter.getDamageReduction() + BuffHelper.getBuffValue(targeter, attacker, SkillEffectType.DAMAGE_REDUCTION_PASSIVE_BUFF_EFFECT);
            damageBonus = attacker.getDamageBonus() + BuffHelper.getBuffValue(attacker, targeter, SkillEffectType.DAMAGE_BONUS_PASSIVE_BUFF_EFFECT);
            if (battleRoom.getBattleType().isPVP()) {
                damageReduction += targeter.getSpriteBattle().getPvpDamageReduction();
                damageBonus += attacker.getSpriteBattle().getPvpDamageBonus();
                //克防技攻特伤害加成
                switch (targeter.getHeroVocationType()) {
                    case DEFENSIVE:
                        damageBonus += attacker.getRestraintDefensiveDamageBonus();
                        break;
                    case SKILL:
                        damageBonus += attacker.getRestraintSkillDamageBonus();
                        break;
                    case ATTACK:
                        damageBonus += attacker.getRestraintAttackDamageBonus();
                        break;
                }
                //抗防技攻特伤害减免
                switch (attacker.getHeroVocationType()) {
                    case DEFENSIVE:
                        damageReduction += targeter.getIndulgeDefensiveDamageReduction();
                        break;
                    case SKILL:
                        damageReduction += targeter.getIndulgeSkillDamageReduction();
                        break;
                    case ATTACK:
                        damageReduction += targeter.getIndulgeAttackDamageReduction();
                        break;
                }
                //灭魏蜀吴群伤害加成
                switch (targeter.getSpriteBattle().getSpriteCamp()) {
                    case WEI:
                        damageBonus += attacker.getSpriteBattle().getRestraintWeiDamageBonus();
                        break;
                    case SHU:
                        damageBonus += attacker.getSpriteBattle().getRestraintShuDamageBonus();
                        break;
                    case WU:
                        damageBonus += attacker.getSpriteBattle().getRestraintWuDamageBonus();
                        break;
                    case WARLORDS:
                        damageBonus += attacker.getSpriteBattle().getRestraintQunDamageBonus();
                        break;
                }
                //抗魏蜀吴群伤害减免
                switch (attacker.getSpriteBattle().getSpriteCamp()) {
                    case WEI:
                        damageReduction += targeter.getSpriteBattle().getIndulgeWeiDamageReduction();
                        break;
                    case SHU:
                        damageReduction += targeter.getSpriteBattle().getIndulgeShuDamageReduction();
                        break;
                    case WU:
                        damageReduction += targeter.getSpriteBattle().getIndulgeWuDamageReduction();
                        break;
                    case WARLORDS:
                        damageReduction += targeter.getSpriteBattle().getIndulgeQunDamageReduction();
                        break;
                }
            } else {
                damageReduction += targeter.getSpriteBattle().getPveDamageReduction();
                damageBonus += attacker.getSpriteBattle().getPveDamageBonus();
            }
        }
        int breakAllowance = 500;
        int suppressionCoefficient = 0;
        if (battleRoom.getBattleType().isPVP()) {
            KeyValue<Integer, Integer> officialSuppressMap = battleRoom.getOfficialSuppressMap(attacker.getActorId());
            if (officialSuppressMap != null) {
                breakAllowance = officialSuppressMap.getKey();
                suppressionCoefficient = officialSuppressMap.getValue();
            }
            Integer battleSuppress = battleRoom.getBattleSuppress(attacker.getActorId());
            if (battleSuppress != null) {
                suppressionCoefficient += battleSuppress;
            }
        }
        // 伤害计算 (攻击力-防御)*(伤害加成%-伤害减免%)
        // ((无双一击攻击力+攻击力)-防御)*(1.5 + 无双一击伤害加成)*(1 + 伤害加成%-伤害减免%)
        long value = attackerAttack - armor;
        boolean isUnparalleled = RandomUtils.is10000Hit(attacker.getSpriteBattle().getPvpUnparalleledProbability());
        if (isUnparalleled) {
            value = NumberUtils.getPercentLongValue(attackerAttack + attacker.getSpriteBattle().getUnparalleledAtk() - armor,
                15000 + attacker.getSpriteBattle().getUnparalleledDamageBonus());
        }
        value = NumberUtils.getPercentLongValue(value, Math.max(5000, (int) (RandomUtils.TEN_THOUSAND + (damageBonus - damageReduction))));
        // 伤害计算 保底 技能加成
        damage = Math.max(NumberUtils.getPercentLongValue(damage, breakAllowance), NumberUtils.getPercentLongValue(value, NumberUtils.getValuePercent(damage, attackerAttack)));
        // 怒气增加伤害百分比
        if (skillConfig.getTriggerType() == SkillTriggerType.CAST) {
            int spilledRage = attacker.getSpriteBattle().getSpilledRage();
            if (spilledRage > 0) {
                int damageTTPercent = (spilledRage * RandomUtils.TEN_THOUSAND) / attacker.getSpriteBattle().getRageLimit();
                damage += NumberUtils.getPercentLongValue(damage, damageTTPercent);
            }
            // 怒攻伤害加成/减免
            int castIncreaseDamage = attacker.getCastIncreaseDamage();
            int castSubtractDamage = targeter.getCastSubtractDamage();
            int castDamagePercent = Math.max(-3000, castIncreaseDamage - castSubtractDamage);
            damage += NumberUtils.getPercentLongValue(damage, castDamagePercent);
        }
        // 被攻击回复怒气/灵力值
        if (!sharedAll && (skillConfig.getTriggerType() == SkillTriggerType.CAST || skillConfig.getTriggerType() == SkillTriggerType.NORMAL_ATTACK)) {
            FightHelper.passiveRage(battleRoom, targeter, report);
            FightHelper.passiveSpiritual(targeter, report);
        }

        // 计算特性
        if (attacker.getSpriteBattle().getSpriteCamp().isRestraint(targeter.getSpriteBattle().getSpriteCamp())) {
            int restraintPercent = globalConfigService.findGlobalConfig(GlobalConfigKey.BATTLE_RESTRAINT_PERCENT).findInt();
            damage += NumberUtils.getPercentLongValue(damage, restraintPercent);
        }
        // 增加压制系数伤害
        if (battleRoom.getBattleType().isPVP()) {
            damage += NumberUtils.getPercentLongValue(damage, suppressionCoefficient);
        }

        if (isCriticalOne) {
            Map<Integer, Integer> percentMap = globalConfigService.findGlobalConfig(GlobalConfigKey.CRITICAL_ONE_DAMAGE).getIntMap();
            Integer criticalOneDamagePercent = RandomUtils.randomByWeight(percentMap);
            damage += NumberUtils.getPercentLongValue(damage, criticalOneDamagePercent);
        }

        if (critical) {
            int criticalDouble = effectConfig.getCriticalDouble() + attacker.getCriticalDamage();
            if (attacker.getSpriteType() == BattleProtocol.SpriteType.SPRITE_BEAST) {
                criticalDouble -= targeter.getSpriteBattle().getBeastCritDamageReduction();
            }
            if (criticalDouble > 0) {
                damage += NumberUtils.getPercentLongValue(damage, criticalDouble);
            }
        }
        if (isBlock) {
            //格挡减一半伤害
            damage -= NumberUtils.getPercentLongValue(damage, 5000);
        }
        //普攻伤害 减免|减伤
        if (skillConfig.getTriggerType() == SkillTriggerType.NORMAL_ATTACK) {
            //判断普通伤害加成
            long normalAttack =
                attacker.getSpriteBattle().getNormalAttackDamageBonus() - BuffHelper.getBuffValue(targeter, SkillEffectType.NORMAL_ATTACK_REDUCTION_BUFF_EFFECT, battleRoom,
                    report);
            if (normalAttack != 0) {
                damage += NumberUtils.getPercentLongValue(damage, (int) normalAttack);
            }
        }
        damage += NumberUtils.getPercentLongValue(damage, attacker.getSpriteBattle().getFinalDamageBonus());
        long holyBuffValue = 0;//神圣一击提升万分比
        if (skillConfig.getTriggerType() == SkillTriggerType.CAST || skillConfig.getTriggerType() == SkillTriggerType.NORMAL_ATTACK) {
            holyBuffValue = BuffHelper.getBuffValue(attacker, SkillEffectType.HOLY_STRIKE_BUFF_EFFECT);
        }
        //怒气伤害加成和怒气抗性加成对抗
        long fataLBuffValue = 0;//致命一击提升万分比
        if (skillConfig.getTriggerType() == SkillTriggerType.CAST) {
            int ragedamageTTPercent = attacker.getSpriteBattle().getRageSkillDamageBonus() - targeter.getSpriteBattle().getRageSkillDamageReduction();
            ragedamageTTPercent = Math.max(-7500, Math.min(7500, ragedamageTTPercent));
            damage += NumberUtils.getPercentLongValue(damage, ragedamageTTPercent);
            fataLBuffValue = BuffHelper.getBuffValue(attacker, SkillEffectType.FATAL_STRIKE_BUFF_EFFECT);
        }
        damage = finalDamageBuff(attacker, targeter, damage, battleRoom, report);
        damage = realFinalDamageBuff(attacker, targeter, damage, battleRoom, report);
        //神圣一击计算
        if (holyBuffValue > 0) {
            damage += NumberUtils.getPercentLongValue(damage, Integer.parseInt(String.valueOf(holyBuffValue)));
        }
        //致命一击计算
        if (fataLBuffValue > 0) {
            damage += NumberUtils.getPercentLongValue(damage, Integer.parseInt(String.valueOf(fataLBuffValue)));
            attacker.addFatalStrikeTimes();
        }
        //灵魂锁链计算
        if (attacker.hasBuffType(SkillEffectType.SOUL_CHAIN_ATTACK_BUFF_EFFECT) && targeter.hasBuffType(SkillEffectType.SOUL_CHAIN_TARGET_BUFF_EFFECT)) {
            long soulChainDamageReduction = BuffHelper.getBuffValue(targeter, SkillEffectType.SOUL_CHAIN_DAMAGE_REDUCTION_BUFF_EFFECT);
            damage -= NumberUtils.getPercentLongValue(damage, Integer.parseInt(String.valueOf(soulChainDamageReduction)));
        }
        //彩金装备压制(神器(系统)释放的技能不走压制逻辑)
        if (attacker.getSpriteType() != SpriteType.SYSTEM && !attacker.isHeroColor()) {
            int equipmentQualitySuppressReduction = targeter.getSpriteBattle().getEquipmentQualitySuppressReduction();
            if (equipmentQualitySuppressReduction > 0) {
                damage -= NumberUtils.getPercentLongValue(damage, equipmentQualitySuppressReduction);
            }
        }
        //伤害吸收
        int damageAbsorbProbability = (int) BuffHelper.getBuffValue(targeter, SkillEffectType.FINAL_DAMAGE_ABSORB_PROBABILITY_BUFF_EFFECT);
        if (RandomUtils.is10000Hit(damageAbsorbProbability)) {
            int damageAbsorbBuffValue = (int) BuffHelper.getBuffValue(targeter, SkillEffectType.FINAL_DAMAGE_ABSORB_PERCENT_BUFF_EFFECT);
            if (damageAbsorbBuffValue > 0) {
                long percentLongValue = NumberUtils.getPercentLongValue(damage, damageAbsorbBuffValue);
                damage -= percentLongValue;
                FightEffect damageAbsorbEffect = FightEffect.damageAbsorb(targeter);
                report.addFightEffect(damageAbsorbEffect);
                int damageAbsorbRecoverBuffValue = (int) BuffHelper.getBuffValue(targeter, SkillEffectType.FINAL_DAMAGE_ABSORB_RECOVER_BUFF_EFFECT);
                long recoverValue = NumberUtils.getPercentLongValue(percentLongValue, damageAbsorbRecoverBuffValue);
                FightEffect recoverEffect = FightEffect.vampire(attacker, SpriteKey.HP, recoverValue);
                report.addFightEffect(recoverEffect);
            }
        }

        //伤害转移
        int transferDamagePercent = (int) BuffHelper.getBuffValue(targeter, SkillEffectType.TRANSFER_DAMAGE_BUFF_EFFECT);
        if (transferDamagePercent > 0 && attacker.getBattleCamp() != targeter.getBattleCamp() && damage > 1) {
            int triggerRate = (int) BuffHelper.getBuffValue(attacker, SkillEffectType.TRANSFER_DAMAGE_ATTACKER_BUFF_EFFECT);
            if (triggerRate > 0) {
                boolean isTransfer = RandomUtils.is10000Hit(triggerRate);
                if (isTransfer) {
                    Collection<BattleSprite> targetBattleSpriteList = Lists.newArrayList();
                    BattleMember battleMember = battleRoom.getBattleMember(attacker.getBattleCamp());
                    for (BattleSprite battleSprite : battleMember.getSpriteMap().values()) {
                        Collection<BattleBuff> buffList = battleSprite.getBuffList(SkillEffectType.TRANSFER_DAMAGE_TARGET_BUFF_EFFECT);
                        if (buffList.isEmpty()) {
                            continue;
                        }
                        for (BattleBuff battleBuff : buffList) {
                            if (battleBuff.getCastSpriteUid() == targeter.getSpriteId()) {
                                targetBattleSpriteList.add(battleSprite);
                                break;
                            }
                        }
                    }
                    if (!targetBattleSpriteList.isEmpty()) {
                        long percentDamage = NumberUtils.getPercentLongValue(damage, transferDamagePercent);
                        isTransfer = false;
                        if (targeter.getSpriteBattle().getHP() > percentDamage) {
                            for (BattleSprite battleSprite : targetBattleSpriteList) {
                                if (battleSprite.isDead()) {
                                    continue;
                                }
                                FightEffect attackEffect = FightEffect.transferDamage(battleSprite, SpriteKey.HP, -percentDamage, critical, isBlock);
                                report.addFightEffect(attackEffect);
                                report.addSpriteDamage(targeter, battleSprite, percentDamage);
                                isTransfer = true;
                            }
                        }
                        if (isTransfer) {
                            damage -= percentDamage;
                        }
                    }
                }
            }
        }

        damage = this.decreaseShield(battleRoom, attacker, targeter, effectConfig.getEffectId(), report, damage);
        damage = this.damageLimit(targeter, report, damage);
        damage = Math.min(targeter.getSpriteBattle().getHP(), damage);
        //        if (damage <= 0) {
        //            LOGGER.error("DamageEffectParser actorId:{} heroId:{} skillConfig:{} effectConfigId:{} damage:{}", attacker.getActorId(), attacker.getRoleId(),
        //                skillConfig.getSkillId(), effectConfig.getEffectId(), damage);
        //        }
        damage = Math.max(0, damage);
        //如果可以触发受到致命伤害的技能,则伤害减1保持1滴血
        boolean isTargeterTakingLethalDamage = FightHelper.isTakingLethalDamage(targeter, damage);
        if (isTargeterTakingLethalDamage) {
            damage -= 1;
        }
        if (shared) {
            FightEffect attackEffect = FightEffect.sharingDamage(targeter, SpriteKey.HP, -damage, critical, isBlock);
            report.addFightEffect(attackEffect);
        } else {
            FightEffect attackEffect = FightEffect.attack(targeter, effectConfig.getEffectId(), SpriteKey.HP, -damage, critical, isBlock);
            if (isUnparalleled) {
                attackEffect.setHitState(BattleProtocol.HitState.UNPARALLELED);
            }
            if (holyBuffValue > 0) {
                attackEffect.setHitState(BattleProtocol.HitState.HOLY_STRIKE);
            }
            if (fataLBuffValue > 0) {
                attackEffect.setHitState(BattleProtocol.HitState.FATAL_STRIKE);
            }
            if (isCriticalOne) {
                attackEffect.setHitState(BattleProtocol.HitState.CRITICAL_ONE);
            }
            report.addFightEffect(attackEffect);
        }
        report.addSpriteDamage(attacker, targeter, damage);
        // 掉血恢复怒气
        int endHpPercent = targeter.getHPPercent();
        FightHelper.bloodLossRage(battleRoom, targeter, startHpPercent, endHpPercent, report);

        if (!attacker.isDead() && attacker.getSpriteType() != BattleProtocol.SpriteType.SPRITE_BEAST && attacker.getSpriteType() != SpriteType.SYSTEM) {
            int reboundRatio = targeter.getSpriteBattle().getReboundRatio();
            long rebound = Math.min(10000, BuffHelper.getBuffValue(targeter, SkillEffectType.REBOUND_BUFF_EFFECT, battleRoom, report) + reboundRatio);
            //反弹的触发概率 默认必定触发  如果存在187buff则替换概率值
            int reboundProbability = RandomUtils.TEN_THOUSAND;
            long buffValue = BuffHelper.getBuffValue(targeter, SkillEffectType.REVISE_REBOUND_PROBABILITY_BUFF_EFFECT);
            reboundProbability = reboundProbability + (int) buffValue;
            boolean trigger = RandomUtils.is10000Hit(reboundProbability);
            if (rebound > 0 && trigger) {
                if (attacker.getSpriteBattle().isBoss() || attacker.hasBuffType(SkillEffectType.IMMUNE_DAMAGE_BUFF_EFFECT)) {
                    report.addFightEffect(FightEffect.immune(attacker, FightEffect.REBOUND_EFFECT_ID));
                } else {
                    long attackerSubstituteValue = attacker.getBuffValue(SkillEffectType.SUBSTITUTE_BUFF_EFFECT);
                    if (attackerSubstituteValue > 0) {
                        FightEffect substituteEffect = FightEffect.substitute(attacker.getSpriteId(), FightEffect.REBOUND_EFFECT_ID);
                        report.addFightEffect(substituteEffect);
                        attacker.decreaseBuffValueByDesc(SkillEffectType.SUBSTITUTE_BUFF_EFFECT, report, 1);
                    } else {
                        long Bonus = BuffHelper.getBuffValue(attacker, SkillEffectType.REBOUND_BONUS_BUFF_EFFECT);
                        if (Bonus < 0) {
                            report.addFightEffect(FightEffect.damageReduction(attacker.getSpriteId()));
                        }
                        long reboundHP = NumberUtils.getPercentLongValue(damage, (int) rebound);
                        reboundHP += NumberUtils.getPercentLongValue(reboundHP, (int) Bonus);
                        reboundHP = finalDamageBuff(targeter, attacker, reboundHP, battleRoom, report);
                        reboundHP = this.decreaseShield(battleRoom, targeter, attacker, FightEffect.REBOUND_EFFECT_ID, report, reboundHP);
                        reboundHP = this.damageLimit(attacker, report, reboundHP);
                        reboundHP = Math.min(attacker.getSpriteBattle().getHP(), reboundHP);
                        boolean isAttackerTakingLethalDamage = FightHelper.isTakingLethalDamage(attacker, reboundHP);
                        if (isAttackerTakingLethalDamage) {
                            reboundHP -= 1;
                        }
                        FightEffect reboundEffect = FightEffect.rebound(attacker, SpriteKey.HP, -reboundHP);
                        report.addSpriteDamage(targeter, attacker, reboundHP);
                        report.addFightEffect(reboundEffect);
                        if (isAttackerTakingLethalDamage) {
                            FightHelper.processSkill(battleRoom, attacker, targeter, attacker, skillConfig, SkillTriggerType.TAKING_LETHAL_DAMAGE);
                        }
                    }
                }
            }
        }
        Collection<BattleBuff> reboundAllBuffList = targeter.getBuffList(SkillEffectType.REBOUND_ALL_BUFF_EFFECT);
        if (!reboundAllBuffList.isEmpty() && !sharedAll) {
            List<BattleSprite> aliveAttackerCampSprite = battleRoom.getAliveSprite(attacker.getBattleCamp());
            BattleBuff reboundAllBuff = reboundAllBuffList.iterator().next();
            SkillEffectConfig reboundAllBuffConfig = SkillConfigService.getSkillEffectConfig(reboundAllBuff.getEffectId());
            for (BattleSprite battleSprite : aliveAttackerCampSprite) {
                if (battleSprite.getSpriteBattle().isBoss() || battleSprite.hasBuffType(SkillEffectType.IMMUNE_DAMAGE_BUFF_EFFECT)) {
                    report.addFightEffect(FightEffect.immune(battleSprite, FightEffect.REBOUND_EFFECT_ID));
                } else {
                    long attackerSubstituteValue = battleSprite.getBuffValue(SkillEffectType.SUBSTITUTE_BUFF_EFFECT);
                    if (attackerSubstituteValue > 0) {
                        FightEffect substituteEffect = FightEffect.substitute(battleSprite.getSpriteId(), FightEffect.REBOUND_EFFECT_ID);
                        report.addFightEffect(substituteEffect);
                        battleSprite.decreaseBuffValueByDesc(SkillEffectType.SUBSTITUTE_BUFF_EFFECT, report, 1);
                    } else {
                        long Bonus = BuffHelper.getBuffValue(battleSprite, SkillEffectType.REBOUND_BONUS_BUFF_EFFECT);
                        if (Bonus < 0) {
                            report.addFightEffect(FightEffect.damageReduction(battleSprite.getSpriteId()));
                        }
                        long reboundHP = Math.max(1, NumberUtils.getPercentLongValue(damage, (int) reboundAllBuffConfig.calcSkillEffect()) / aliveAttackerCampSprite.size());
                        reboundHP += NumberUtils.getPercentLongValue(reboundHP, (int) Bonus);
                        reboundHP = finalDamageBuff(targeter, battleSprite, reboundHP, battleRoom, report);
                        reboundHP = this.decreaseShield(battleRoom, targeter, battleSprite, FightEffect.REBOUND_EFFECT_ID, report, reboundHP);
                        reboundHP = this.damageLimit(battleSprite, report, reboundHP);
                        reboundHP = Math.min(battleSprite.getSpriteBattle().getHP(), reboundHP);
                        boolean isAttackerTakingLethalDamage = FightHelper.isTakingLethalDamage(battleSprite, reboundHP);
                        if (isAttackerTakingLethalDamage) {
                            reboundHP -= 1;
                        }
                        FightEffect reboundEffect = FightEffect.rebound(battleSprite, SpriteKey.HP, -reboundHP);
                        report.addSpriteDamage(targeter, battleSprite, reboundHP);
                        report.addFightEffect(reboundEffect);
                        if (isAttackerTakingLethalDamage) {
                            FightHelper.processSkill(battleRoom, battleSprite, targeter, battleSprite, skillConfig, SkillTriggerType.TAKING_LETHAL_DAMAGE);
                        }
                    }
                }
            }
        }
        if (isTargeterTakingLethalDamage) {
            FightHelper.processSkill(battleRoom, targeter, attacker, targeter, skillConfig, SkillTriggerType.TAKING_LETHAL_DAMAGE);
        }
        //受到伤害时
        FightHelper.processSkill(battleRoom, targeter, attacker, attacker, damage, skillConfig, SkillTriggerType.TAKING_DAMAGE, null);
        //生命发生变动时
        FightHelper.processSkill(battleRoom, targeter, attacker, targeter, -damage, skillConfig, SkillTriggerType.HP_CHANGE, null);

        BattleMember battleMember = battleRoom.getBattleMember(targeter.getBattleCamp());
        for (BattleSprite battleSprite : battleMember.getPetMap().values()) {
            FightHelper.processSkill(battleRoom, battleSprite, battleSprite, null, 0, skillConfig, SkillTriggerType.FRIENDLY_HERO_TAKING_DAMAGE, null);
        }
        return TResult.sucess(damage);
    }
}
