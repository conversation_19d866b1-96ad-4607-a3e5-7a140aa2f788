package cn.daxiang.hbtd.gameserver.core.dataconfig.service;

import cn.daxiang.framework.dataconfig.ConfigServiceAdapter;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.ActivityOptionBaseConfig;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/6/15
 */
@Component
public class ActivityOptionBaseConfigService extends ConfigServiceAdapter {

    private static Map<Integer, Map<Integer, ActivityOptionBaseConfig>> ACTIVITY_OPTION_BASE_CONFIG_MAP = Maps.newHashMap();

    public static ActivityOptionBaseConfig getActivityOptionBaseConfig(int data, int chargeId) {
        Map<Integer, ActivityOptionBaseConfig> map = ACTIVITY_OPTION_BASE_CONFIG_MAP.get(data);
        if (map == null) {
            return null;
        }
        return map.get(chargeId);
    }

    @Override
    protected void initialize() {
        Collection<ActivityOptionBaseConfig> list = dataConfig.listAll(this, ActivityOptionBaseConfig.class);
        for (ActivityOptionBaseConfig config : list) {
            Map<Integer, ActivityOptionBaseConfig> configMap = ACTIVITY_OPTION_BASE_CONFIG_MAP.get(config.getData());
            if (configMap == null) {
                configMap = Maps.newHashMap();
                ACTIVITY_OPTION_BASE_CONFIG_MAP.put(config.getData(), configMap);
            }
            if (config.getChargeId() > 0) {
                configMap.put(config.getChargeId(), config);
            }
        }
    }

    @Override
    protected void clean() {
        ACTIVITY_OPTION_BASE_CONFIG_MAP.clear();
    }
}
