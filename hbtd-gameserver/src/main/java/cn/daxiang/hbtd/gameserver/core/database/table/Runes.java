package cn.daxiang.hbtd.gameserver.core.database.table;

import cn.daxiang.framework.database.MultiEntity;
import cn.daxiang.framework.database.annotation.Column;
import cn.daxiang.framework.database.annotation.DBQueueType;
import cn.daxiang.framework.database.annotation.Table;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.hbtd.gameserver.module.runes.model.RunesExtra;
import com.alibaba.fastjson.annotation.JSONField;
import com.google.common.collect.Lists;

import java.util.Collection;
import java.util.Iterator;

/**
 * 玩家兵符信息
 *
 * <AUTHOR>
 */
@Table(name = "runes", type = DBQueueType.IMPORTANT)
public class Runes extends MultiEntity<Long> {
    /**
     * 角色ID
     */
    @Column(fk = true)
    @JSONField(serialize = false)
    private long actorId;
    /**
     * 兵符UID
     */
    @Column(pk = true)
    private long runesUid;
    /**
     * 兵符配置Id
     */
    @Column
    private int configId;
    /**
     * 阵营Id
     */
    @Column
    private int campId;
    /**
     * 兵符数量
     */
    @Column
    private long num;
    /**
     * 符文额外属性值
     */
    @Column(alias = "runesExtras")
    private Collection<RunesExtra> runesExtraList = Lists.newLinkedList();
    /**
     * 是否被鉴定过
     */
    @Column
    private boolean hasAppraised;

    public static Runes valueOf(long actorId, long runesUid, int configId, int campId, long num) {
        Runes model = new Runes();
        model.actorId = actorId;
        model.runesUid = runesUid;
        model.configId = configId;
        model.campId = campId;
        model.num = num;
        model.hasAppraised = false;
        return model;
    }

    @Override
    public Long findFkId() {
        return actorId;
    }

    @Override
    public void setFkId(Long fk) {
        this.actorId = fk;
    }

    @Override
    public IdentiyKey findPkId() {
        return IdentiyKey.build(runesUid);
    }

    @Override
    public void setPkId(IdentiyKey pk) {
        this.runesUid = pk.getFirstLongId();
    }

    public long getActorId() {
        return actorId;
    }

    public long getRunesUid() {
        return runesUid;
    }

    public void setRunesUid(long runesUid) {
        this.runesUid = runesUid;
    }

    public int getConfigId() {
        return configId;
    }

    public void setConfigId(int configId) {
        this.configId = configId;
    }

    public int getCampId() {
        return campId;
    }

    public void setCampId(int campId) {
        this.campId = campId;
    }

    public long getNum() {
        return num;
    }

    public void setNum(long num) {
        this.num = num;
    }

    public void addNum(long num) {
        this.num += num;
    }

    public Collection<RunesExtra> getRunesExtraList() {
        return runesExtraList;
    }

    public void setRunesExtraList(Collection<RunesExtra> runesExtraList) {
        this.runesExtraList = runesExtraList;
    }

    public boolean isHasAppraised() {
        return hasAppraised;
    }

    public void setHasAppraised(boolean hasAppraised) {
        this.hasAppraised = hasAppraised;
    }

    public void appraiseRunes( RunesExtra extra) {
        this.hasAppraised = true;
        this.runesExtraList.add(extra);
    }

    public boolean addRunesExtra(int type, int value) {
        for (RunesExtra runesExtra : runesExtraList) {
            if (runesExtra.getType() == type) {
                return false;
            }
        }
        runesExtraList.add(RunesExtra.valueOf(type, value));
        return true;
    }

    public boolean containsType(int type) {
        for (RunesExtra runesExtra : runesExtraList) {
            if (runesExtra.getType() == type) {
                return true;
            }
        }
        return false;
    }

    public RunesExtra getRunesExtra(int type, int value) {
        for (RunesExtra runesExtra : runesExtraList) {
            if (runesExtra.getType() == type && runesExtra.getValue() == value) {
                return runesExtra;
            }
        }
        return null;
    }

    public void deleteRunesExtra(int type, int value) {
        Iterator<RunesExtra> iterator = runesExtraList.iterator();
        while (iterator.hasNext()) {
            RunesExtra next = iterator.next();
            if (next.getType() == type && next.getValue() == value) {
                iterator.remove();
                return;
            }
        }
    }
}
