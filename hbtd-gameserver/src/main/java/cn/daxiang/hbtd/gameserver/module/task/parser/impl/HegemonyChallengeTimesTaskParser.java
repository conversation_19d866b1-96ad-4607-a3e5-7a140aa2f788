package cn.daxiang.hbtd.gameserver.module.task.parser.impl;

import cn.daxiang.hbtd.gameserver.core.database.table.Task;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.TaskConfig;
import cn.daxiang.hbtd.gameserver.core.event.impl.HegemonyOffensiveBattleEvent;
import cn.daxiang.hbtd.gameserver.module.task.parser.AbstractTaskParser;
import cn.daxiang.hbtd.gameserver.module.task.type.TaskConditionType;
import cn.daxiang.protocol.game.TaskProtocol;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/12/12
 */
@Component
public class HegemonyChallengeTimesTaskParser extends AbstractTaskParser<HegemonyOffensiveBattleEvent> {
    @Override
    public void init(Task task, TaskConfig taskConfig) {
        if (task.getValue() >= taskConfig.getValue()) {
            task.setStatus(TaskProtocol.TaskStatus.TASK_FINISH);
        }
    }

    @Override
    protected TaskConditionType getType() {
        return TaskConditionType.HEGEMONY_CHALLENGE_TINES;
    }

    @Override
    protected boolean parseCondition(HegemonyOffensiveBattleEvent event, Task task, TaskConfig taskConfig) {
        task.setValue(task.getValue() + 1);
        if (task.getValue() >= taskConfig.getValue()) {
            task.setStatus(TaskProtocol.TaskStatus.TASK_FINISH);
        }
        return true;
    }
}
