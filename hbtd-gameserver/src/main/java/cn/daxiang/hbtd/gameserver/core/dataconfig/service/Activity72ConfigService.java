package cn.daxiang.hbtd.gameserver.core.dataconfig.service;

import cn.daxiang.framework.dataconfig.ConfigServiceAdapter;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.Activity72Config;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;

/**
 * 传送带礼包配置
 *
 * <AUTHOR>
 * @date 2025/2/19
 */
@Component
public class Activity72ConfigService extends ConfigServiceAdapter {

    /**
     * key:data, value:{ key:order, value:Activity72Config }
     */
    private static final Map<Integer, Map<Integer, Activity72Config>> ACTIVITY_72_CONFIG_MAP = Maps.newHashMap();

    /**
     * 中级战令直购
     * key:chargeId value:order
     */
    private static final Map<Integer, Integer> CHARGE_ORDER_MAP = Maps.newHashMap();

    @Override
    public void initialize() {
        Collection<Activity72Config> activity72Configs = dataConfig.listAll(this, Activity72Config.class);
        for (Activity72Config config : activity72Configs) {
            ACTIVITY_72_CONFIG_MAP.computeIfAbsent(config.getData(), k -> Maps.newHashMap()).put(config.getOrder(), config);
            if (config.getChargeId() > 0) {
                CHARGE_ORDER_MAP.put(config.getChargeId(), config.getOrder());
            }
        }
    }

    @Override
    protected void clean() {
        CHARGE_ORDER_MAP.clear();
        ACTIVITY_72_CONFIG_MAP.clear();
    }

    public static Integer getChargeOrderId(int chargeId) {
        return CHARGE_ORDER_MAP.get(chargeId);
    }

    public static Map<Integer, Activity72Config> getOrderConfigsMap(int data) {
        return ACTIVITY_72_CONFIG_MAP.get(data);
    }

}
