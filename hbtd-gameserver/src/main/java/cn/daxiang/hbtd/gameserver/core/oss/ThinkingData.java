package cn.daxiang.hbtd.gameserver.core.oss;

import cn.daxiang.framework.database.DBQueue;
import cn.daxiang.framework.event.ActorEvent;
import cn.daxiang.framework.event.GameEvent;
import cn.daxiang.framework.event.annotation.Event;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.framework.utils.DateUtils;
import cn.daxiang.framework.utils.StringUtils;
import cn.daxiang.framework.utils.TimeUtils;
import cn.daxiang.framework.utils.schedule.Schedule;
import cn.daxiang.hbtd.gameserver.core.GameConfig;
import cn.daxiang.hbtd.gameserver.core.database.table.Actor;
import cn.daxiang.hbtd.gameserver.core.database.table.BattlefieldDrill;
import cn.daxiang.hbtd.gameserver.core.database.table.Charge;
import cn.daxiang.hbtd.gameserver.core.database.table.Hero;
import cn.daxiang.hbtd.gameserver.core.database.table.Lineup;
import cn.daxiang.hbtd.gameserver.core.database.table.StoryHangUp;
import cn.daxiang.hbtd.gameserver.core.database.table.Tower;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.ChargeCommodityConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.GrowthTaskConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.HeroConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.ReportDataConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.GlobalConfigService;
import cn.daxiang.hbtd.gameserver.core.event.impl.ActorDiamondDecreaseEvent;
import cn.daxiang.hbtd.gameserver.core.event.impl.ActorFirstRechargeEvent;
import cn.daxiang.hbtd.gameserver.core.event.impl.ActorRechargeDisposeEvent;
import cn.daxiang.hbtd.gameserver.core.event.impl.ActorRechargeEvent;
import cn.daxiang.hbtd.gameserver.core.event.impl.ActorVnRechargeEvent;
import cn.daxiang.hbtd.gameserver.core.netty.channel.PlayerChannel;
import cn.daxiang.hbtd.gameserver.module.battlefieldDrill.facade.BattlefieldDrillFacade;
import cn.daxiang.hbtd.gameserver.module.goods.facade.GoodsFacade;
import cn.daxiang.hbtd.gameserver.module.goods.type.GoodsTdType;
import cn.daxiang.hbtd.gameserver.module.hero.facade.HeroFacade;
import cn.daxiang.hbtd.gameserver.module.lineup.facade.LineupFacade;
import cn.daxiang.hbtd.gameserver.module.lineup.model.entity.LineupEntity;
import cn.daxiang.hbtd.gameserver.module.story.facade.StoryHangUpFacade;
import cn.daxiang.hbtd.gameserver.module.task.facade.TaskFacade;
import cn.daxiang.hbtd.gameserver.module.tower.facade.TowerFacade;
import cn.daxiang.hbtd.gameserver.module.user.dao.ActorDao;
import cn.daxiang.hbtd.gameserver.module.user.dao.ChargeDao;
import cn.daxiang.hbtd.gameserver.module.user.facade.ActorFacade;
import cn.daxiang.hbtd.gameserver.module.user.helper.ActorHelper;
import cn.daxiang.hbtd.gameserver.module.user.type.ChargeType;
import cn.daxiang.protocol.game.TypeProtocol;
import cn.daxiang.shared.event.EventKey;
import cn.daxiang.shared.type.QualityType;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * 数数日志
 */
@Component
public class ThinkingData implements ApplicationListener<ContextClosedEvent> {
    private static final Logger LOGGER = LoggerFactory.getLogger(ThinkingData.class);
    private static ThinkingDataAnalytics ta;
    @Autowired
    private DBQueue dbQueue;
    @Autowired
    private Schedule schedule;
    @Autowired
    private GlobalConfigService globalConfigService;
    @Autowired
    private StoryHangUpFacade storyHangUpFacade;
    @Autowired
    private ChargeDao chargeDao;
    @Autowired
    private LineupFacade lineupFacade;
    @Autowired
    private ActorDao actorDao;
    @Autowired
    private TowerFacade towerFacade;
    @Autowired
    private BattlefieldDrillFacade battlefieldDrillFacade;
    @Autowired
    private HeroFacade heroFacade;
    @Autowired
    private TaskFacade taskFacade;
    @Autowired
    private ActorFacade actorFacade;
    @Autowired
    private GoodsFacade goodsFacade;
    @Value("${serverId}")
    private int serverId = 1;
    @Value("${server_type}")
    private int serverType = 1;
    @Value("#{'${thinking.data.linux.directory}'}")
    private String linux_directory;
    @Value("#{'${thinking.data.windows.directory}'}")
    private String windows_directory;
    @Value("${thinking.data.autoFlush:true}")
    private Boolean autoFlush;
    @Value("${thinking.data.interval:60}")
    private Integer interval;
    @Value("#{'${thinking.currency.type}'}")
    private String currencyType;

    /**
     * 判断操作系统是否是windows
     *
     * @return
     */
    private static boolean isWindows() {
        return System.getProperty("os.name").toUpperCase().indexOf("WINDOWS") >= 0 ? true : false;
    }

    /**
     * 钻石消耗
     */
    @Event(name = EventKey.ACTOR_DIAMOND_DECREASE_EVENT)
    public void resourceChange(ActorDiamondDecreaseEvent e) {
        long actorId = e.getUniqueId();
        ActorDiamondDecreaseEvent event = e.convert();
        Actor actor = ActorHelper.getActor(actorId);
        Map<String, Object> properties = Maps.newHashMap();
        properties.put("reason", event.operationType);
        properties.put("resource_id", 1);
        properties.put("resource_name", "diamond");
        properties.put("change_type", 2);
        properties.put("change_num", event.count);
        properties.put("change_after", actor.getDiamond());
        addTrack(actorId, "resource_change", properties);
    }

    /**
     * 角色登录
     */
    @Event(name = EventKey.ACTOR_LOGIN)
    public void onActorLogin(ActorEvent e) {
        long actorId = e.getUniqueId();
        Actor actor = ActorHelper.getActor(actorId);
        Map<String, Object> properties = Maps.newHashMap();
        properties.put("is_first_login", actor.getLastLoginTime() == actor.getLastLogoutTime());
        properties.put("add_day", actor.getLoginTotalDay());
        properties.put("last_out_time", actor.getLastLogoutTime());
        addTrack(actorId, "login", properties);
    }

    /**
     * 成长任务进度
     */
    private void growMission() {
        long lastLogoutTime = System.currentTimeMillis() - TimeUtils.ONE_DAY_MILLISECOND * 3;
        List<Long> actorIdList = actorFacade.getActorIdList(9, lastLogoutTime);
        for (Long actorId : actorIdList) {
            Actor actor = ActorHelper.getActor(actorId);
            GrowthTaskConfig config = globalConfigService.findConfig(actor.getGrowthTask(), GrowthTaskConfig.class);
            if (config == null || config.getNextId() == 0) {
                return;
            }
            JSONArray array = new JSONArray();
            for (Integer taskId : config.getTaskGroupList()) {
                if (!taskFacade.isTaskOK(actorId, taskId)) {
                    array.add(taskId);
                }
            }

            Map<String, Object> properties = Maps.newHashMap();
            properties.put("mission_id", actor.getGrowthTask());
            properties.put("task_id", array);
            addTrack(actorId, "grow_mission", properties);
        }
    }

    /**
     * 角色退出
     */
    @Event(name = EventKey.ACTOR_LOGOUT)
    public void onActorLogout(ActorEvent e) {
        long actorId = e.getUniqueId();
        Actor actor = ActorHelper.getActor(actorId);
        int add_day = actor.getLoginTotalDay();
        int add_time = DateUtils.getNowInSecondes() - (int) (actor.getLastLoginTime() / 1000);
        actor.addOnlineTime(add_time);
        dbQueue.updateQueue(actor);
        Map<String, Object> properties = Maps.newHashMap();
        properties.put("add_day", add_day);
        properties.put("online_time", actor.getOnlineTime());
        properties.put("add_time", add_time);
        addTrack(actorId, "logout", properties);
        actorLoginUserSet(actor, true);
    }

    @Event(name = EventKey.ACTOR_CREATE_EVENT)
    public void onCreateActor(GameEvent e) {
        if (e.getUniqueId() > 0) {
            Actor actor = ActorHelper.getActor(e.getUniqueId());
            this.createActorSetOnce(actor);
        }
    }

    @Event(name = EventKey.ACTOR_FIRST_CHARGE_EVENT)
    public void onActorFirstCharge(GameEvent e) {
        ActorFirstRechargeEvent event = e.convert();
        String firstChargeTime = DateUtils.SDF_LONG_DATE.get().format(new Date());
        Map<String, Object> properties = Maps.newHashMap();
        properties.put("first_pay_time", firstChargeTime);
        properties.put("first_pay_revenue", event.getRecharge() / 100);
        this.setOnce(e.getUniqueId(), properties);
    }

    @Event(name = EventKey.ACTOR_RECHARGE_EVENT)
    public void onActorRecharge(ActorRechargeEvent e) {
        String last_pay_time = DateUtils.SDF_LONG_DATE.get().format(new Date());
        Map<String, Object> properties = Maps.newHashMap();
        properties.put("total_revenue", e.getTotalRecharge() / 100);
        properties.put("last_pay_time", last_pay_time);
        this.userSet(e.getUniqueId(), properties);
    }

    @Event(name = EventKey.ACTOR_RECHARGE_DISPOSE_EVENT)
    public void onActorRechargeDisposeEvent(ActorRechargeDisposeEvent event) {
        ChargeCommodityConfig config = globalConfigService.findConfig(IdentiyKey.build(event.getChargeId()), ChargeCommodityConfig.class);
        if (config == null) {
            LOGGER.error("ChargeCommodityConfig is null chargeId():{}", event.getChargeId());
            return;
        }
        // 消耗代金券
        if (StringUtils.isBlank(event.getOrderId())) {
            Actor actor = ActorHelper.getActor(event.getActorId());
            if (actor != null) {
                int goodsNum = goodsFacade.getGoodsNum(event.getUniqueId(), GoodsTdType.VOUCHERS.getId());
                Map<String, Object> properties = Maps.newHashMap();
                properties.put("pay_amount", config.getCostValue() / 100);
                properties.put("pay_reason_id", config.getChargeId());
                properties.put("change_after", goodsNum / 100);
                properties.put("pay_type", currencyType);
                this.addTrack(event.getUniqueId(), "cost_voucher", properties);
            }
            return;
        }
        // 线下充值成功
        if (config.getType() == ChargeType.CHARGE_VOUCHER_MAIL.getId() || config.getType() == ChargeType.CHARGE_DIRECT_PURCHASING_MAIL.getId()) {
            Map<String, Object> properties = Maps.newHashMap();
            properties.put("order_id", event.getOrderId());
            properties.put("pay_amount", config.getCostValue() / 100);
            properties.put("pay_reason_id", event.getChargeId());
            properties.put("pay_type", currencyType);
            this.addTrack(event.getUniqueId(), "offline_recharge_success", properties);
        }
        // 购买代金券玩家
        if (config.getType() == ChargeType.CHARGE_VOUCHER_MAIL.getId()) {
            Map<String, Object> onceProperties = Maps.newHashMap();
            onceProperties.put("is_voucher", true);
            this.setOnce(event.getUniqueId(), onceProperties);
        }
    }

    @Event(name = EventKey.ACTOR_VN_RECHARGE_EVENT)
    public void onActorRechargeDisposeEvent(ActorVnRechargeEvent event) {
        long actorId = event.getUniqueId();
        Actor actor = ActorHelper.getActor(actorId);
        if (actor == null) {
            return;
        }
        Map<String, Object> properties = Maps.newHashMap();
        properties.put("order_id", event.getOrderId());
        properties.put("pay_amount", event.getCostValue() / 100);
        if (event.getChargeId() > 0) {
            properties.put("pay_reason", "game payment");
        } else {
            properties.put("pay_reason", "web payment");
        }
        properties.put("pay_reason_id", event.getChargeId());
        properties.put("pay_type", currencyType);
        properties.put("item_gain", event.getRewardList());
        properties.put("is_first_pay", event.isFirstCharge());
        properties.put("#uuid", UUID.randomUUID().toString());
        this.addTrack(event.getUniqueId(), "recharge_success_vn", properties);
    }

    private void createActorSetOnce(Actor actor) {
        String createTime = DateUtils.SDF_LONG_DATE.get().format(new Date(actor.getCreateTime()));
        String userCreateTime = DateUtils.SDF_LONG_DATE.get().format(new Date(actor.getUserCreateTime()));
        Map<String, Object> properties = Maps.newHashMap();
        properties.put("unite_operation", actor.getUniteOperation());
        properties.put("channel_id", actor.getChannelId());
        properties.put("put_operation", actor.getPutOperation());
        properties.put("open_id", actor.getUid());
        properties.put("server_id", actor.getServerId());
        properties.put("create_time", createTime);
        properties.put("role_id", actor.getActorId());
        properties.put("first_login_time", createTime);
        properties.put("server_type", serverType);
        properties.put("register_time", userCreateTime);
        this.setOnce(actor.getActorId(), properties);
    }

    public static void main(String[] args) {
        String createTime = DateUtils.SDF_LONG_DATE.get().format(new Date(0));
        System.err.println(createTime);
    }

    private void actorLoginUserSet(Actor actor, boolean isRefreshLastLogoutTime) {
        int storyId = 0;
        TResult<StoryHangUp> hangUpResult = storyHangUpFacade.getStoryHangUp(actor.getActorId());
        if (hangUpResult.isOk()) {
            StoryHangUp item = hangUpResult.item;
            storyId = item.getStoryId();
        }
        int chargeTimes = 0;
        Collection<Charge> charges = chargeDao.getChargeList(actor.getActorId());
        for (Charge Charge : charges) {
            chargeTimes += Charge.getTimes();
        }
        JSONArray heroList = new JSONArray();
        Lineup lineup = lineupFacade.getLineup(actor.getActorId()).item;
        for (LineupEntity entity : lineup.getLineupMap().values()) {
            JSONObject heroObject = new JSONObject();
            TResult<Hero> heroResult = heroFacade.getHero(actor.getActorId(), entity.getHeroId());
            if (heroResult.isOk()) {
                heroObject.put("heroId", heroResult.item.getConfigId());
            }
            heroObject.put("power", entity.getPower());
            heroList.add(heroObject);
        }
        int infiniteLimit = 0;
        TResult<BattlefieldDrill> battlefieldDrillResult = battlefieldDrillFacade.getBattlefieldDrill(actor.getActorId());
        if (battlefieldDrillResult.isOk()) {
            infiniteLimit = battlefieldDrillResult.item.getMaxId();
        }
        int towerLevel = 0;
        TResult<Tower> towerResult = towerFacade.getTower(actor.getActorId(), TypeProtocol.TowerType.ATYPE);
        if (towerResult.isOk()) {
            towerLevel = towerResult.item.getLayer();
        }
        String last_login_time = DateUtils.SDF_LONG_DATE.get().format(new Date(actor.getLastLoginTime()));
        String last_logout_time = DateUtils.SDF_LONG_DATE.get().format(new Date());
        if (!isRefreshLastLogoutTime && actor.getLastLogoutTime() > 0) {
            last_logout_time = DateUtils.SDF_LONG_DATE.get().format(new Date(actor.getLastLogoutTime()));
        }
        JSONArray heroQualityList = new JSONArray();
        Collection<Hero> heroAllList = heroFacade.getHeroList(actor.getActorId());
        for (Hero hero : heroAllList) {
            HeroConfig heroConfig = globalConfigService.findConfig(hero.getConfigId(), HeroConfig.class);
            if (heroConfig.getQuality() >= QualityType.EPIC.getId()) {
                JSONObject heroObject = new JSONObject();
                heroObject.put("heroId", hero.getConfigId());
                heroObject.put("quality", heroConfig.getQuality());
                heroQualityList.add(heroObject);
            }
        }

        Map<String, Object> properties = Maps.newHashMap();
        properties.put("role_level", actor.getLevel());
        properties.put("role_name", actor.getActorName());
        properties.put("power", actor.getPower());
        properties.put("vip_level", actor.getVipLevel());
        properties.put("last_login_time", last_login_time);
        properties.put("last_logout_time", last_logout_time);
        properties.put("revenue_times", chargeTimes);
        properties.put("total_login_day", actor.getLoginTotalDay());
        properties.put("diamond", actor.getDiamond());
        properties.put("gold", actor.getGold());
        properties.put("main_node", storyId);
        properties.put("hero", heroList);
        properties.put("total_revenue", actor.getRevenue() / 100);
        properties.put("infinite_limit", infiniteLimit);
        properties.put("tower_level", towerLevel);
        properties.put("server_type", serverType);
        properties.put("hero_quality", heroQualityList);
        this.userSet(actor.getActorId(), properties);
    }

    /**
     * 10分钟定时上报在线人数
     */
    public void currentOnline() {
        Map<String, Object> properties = Maps.newHashMap();
        properties.put("fuwuqi_type", GameConfig.getServerType() + "");
        properties.put("fuwuqi_id", GameConfig.getServerId() + "");
        properties.put("current_online_num", PlayerChannel.onlineActorCount());
        addTrack(0L, "current_online", properties);
    }

    public void addTrack(long actorId, String eventName, Map<String, Object> properties) {
        try {
            ReportDataConfig config = globalConfigService.findConfig(eventName, ReportDataConfig.class);
            if (config == null) {
                LOGGER.error("ReportDataConfig is null eventName:{}", eventName);
                return;
            }
            if (!config.isOpen()) {
                return;
            }
            //            if (GameConfig.isDebug()) {
            //                return;
            //            }

            String accountId = String.valueOf(actorId);
            String distinctId = "";
            if (actorId > 0) {
                Actor actor = ActorHelper.getActor(actorId);
                distinctId = actor.getDistinctId();
                // 公共属性
                properties.put("channel_id", actor.getChannelId());
                properties.put("unite_operation", actor.getUniteOperation());
                properties.put("put_operation", actor.getPutOperation());
                properties.put("open_id", actor.getUid());
                properties.put("server_id", actor.getServerId());
                properties.put("role_level", actor.getLevel());
                properties.put("role_exp", actor.getExp());
                properties.put("role_id", actor.getActorId());
                properties.put("role_name", actor.getActorName());
                properties.put("power", actor.getPower());
                properties.put("diamond", actor.getDiamond());
                properties.put("gold", actor.getGold());
                properties.put("tili", actor.getEnergy());
                int chargeTimes = 0;
                Collection<Charge> charges = chargeDao.getChargeList(actorId);
                for (Charge Charge : charges) {
                    chargeTimes += Charge.getTimes();
                }
                properties.put("revenue", (actor.getRecharge() - actor.getPresent()) / 100);
                properties.put("revenue_times", chargeTimes);
                int storyId = 0;
                TResult<StoryHangUp> hangUpResult = storyHangUpFacade.getStoryHangUp(actor.getActorId());
                if (hangUpResult.isOk()) {
                    StoryHangUp item = hangUpResult.item;
                    storyId = item.getStoryId();
                }
                properties.put("main_node", storyId);
                properties.put("#ip", actor.getCreateIp());
            }
            ta.track(accountId, distinctId, eventName, properties);
        } catch (Exception e) {
            LOGGER.error("ThinkingData track error {}", e);
        }
    }

    public void setOnce(long actorId, Map<String, Object> properties) {
        try {
            String accountId = String.valueOf(actorId);
            String distinctId = "";
            if (actorId > 0) {
                Actor actor = ActorHelper.getActor(actorId);
                distinctId = actor.getDistinctId();
            }
            ta.user_setOnce(accountId, distinctId, properties);
        } catch (Exception e) {
            LOGGER.error("ThinkingData setOnce error {}", e);
        }
    }

    public void userSet(long actorId, Map<String, Object> properties) {
        try {
            String accountId = String.valueOf(actorId);
            String distinctId = "";
            if (actorId > 0) {
                Actor actor = ActorHelper.getActor(actorId);
                distinctId = actor.getDistinctId();
            }
            ta.user_set(accountId, distinctId, properties);
        } catch (Exception e) {
            LOGGER.error("ThinkingData userSet error {}", e);
        }
    }

    @Override
    public void onApplicationEvent(ContextClosedEvent event) {
        try {
            ta.close();
        } catch (Exception e) {
            LOGGER.error("ThinkingData close error {}", e);
        }
    }

    public void initialize() {
        String directory;
        if (isWindows()) {
            directory = windows_directory + "/" + serverId;
        } else {
            directory = linux_directory + "/" + serverId;
        }
        ThinkingDataAnalytics.LoggerConsumer.Config config = new ThinkingDataAnalytics.LoggerConsumer.Config(directory);
        LOGGER.info("ThinkingData config autoFlush:{}, interval:{} second, log save directory:{}", autoFlush, interval, directory);
        config.setAutoFlush(autoFlush);
        config.setInterval(interval);
        ta = new ThinkingDataAnalytics(new ThinkingDataAnalytics.LoggerConsumer(config));

        schedule.addEveryMinuteZeroStart(new Runnable() {
            @Override
            public void run() {
                currentOnline();
            }
        }, 10);

        schedule.addFixedTime(new Runnable() {
            @Override
            public void run() {
                for (long actorId : PlayerChannel.onlineActorList()) {
                    Actor actor = ActorHelper.getActor(actorId);
                    actorLoginUserSet(actor, false);
                }
            }
        }, 1);

        schedule.addFixedTime(new Runnable() {
            @Override
            public void run() {
                growMission();
            }
        }, 23);

    }
}


