package cn.daxiang.hbtd.gameserver.module.extension.dao.impl;

import cn.daxiang.framework.database.SingleEntity;
import cn.daxiang.framework.database.dao.SingleEntityDaoImpl;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.hbtd.gameserver.core.database.table.VipCard;
import cn.daxiang.hbtd.gameserver.module.extension.dao.VipCardDao;
import org.springframework.stereotype.Component;

@Component
public class VipCardDaoImpl extends SingleEntityDaoImpl implements VipCardDao {
    @Override
    protected Class<? extends SingleEntity<?>> forClass() {
        return VipCard.class;
    }

    @Override
    protected void initMaxId() {

    }

    @Override
    public VipCard getVipCard(long actorId) {
        VipCard table = this.get(IdentiyKey.build(actorId));
        if (table.newEntity()) {
            table.setNewEntity(false);
            table.reset();
            this.updateQueue(table);
        }
        return table;
    }
}
