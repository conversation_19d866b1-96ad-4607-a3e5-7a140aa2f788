package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.dataconfig.annotation.FieldIgnore;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.shared.module.lineup.SpriteAttributeType;
import cn.daxiang.shared.reward.RewardObject;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.Collection;
import java.util.Map;

/**
 * 装备铸灵配置表
 *
 * <AUTHOR>
 * @date 2022/6/28
 */
@DataFile(fileName = "equipment_new_casting_config")
public class EquipmentNewCastingConfig implements ModelAdapter {
    /**
     * 配置ID
     */
    private int id;
    /**
     * 阶段
     */
    private int stage;
    /**
     * 普通等级数量
     */
    private int levelNum;
    /**
     * 阶段等级数量
     */
    private int keyLevelNum;
    /**
     * 普通等级增加属性，每级增加属性一致，配置基础四维属性
     */
    private String attributes;
    /**
     * 阶段等级增加属性，配置全体属性
     */
    private String keyAttributes;
    /**
     * 普通等级消耗物品
     */
    private String costs;
    /**
     * 阶段等级消耗物品
     */
    private String keyCosts;
    /**
     * 特殊属性Id
     */
    private int specialEffectId;
    /**
     * 解锁等级
     */
    private int unlockLevel;
    /**
     * 属性Map
     * key:SpriteAttributeType,value:upValue
     */
    @FieldIgnore
    private Map<SpriteAttributeType, Long> attributeMap = Maps.newHashMap();
    /**
     * 阶段等级属性Map
     * key:SpriteAttributeType,value:upValue
     */
    @FieldIgnore
    private Map<SpriteAttributeType, Long> keyAttributeMap = Maps.newHashMap();
    @FieldIgnore
    private Collection<RewardObject> costList = Lists.newArrayList();
    @FieldIgnore
    private Collection<RewardObject> keyCostList = Lists.newArrayList();

    @Override
    public void initialize() {
        JSONArray attributesArray = JSONArray.parseArray(attributes);
        for (Object attributeItem : attributesArray) {
            JSONArray attributeArray = JSONArray.parseArray(attributeItem.toString());
            attributeMap.put(SpriteAttributeType.getType(attributeArray.getIntValue(0)), attributeArray.getLong(1));
        }
        JSONArray keyAttributesArray = JSONArray.parseArray(keyAttributes);
        for (Object attributeItem : keyAttributesArray) {
            JSONArray attributeArray = JSONArray.parseArray(attributeItem.toString());
            keyAttributeMap.put(SpriteAttributeType.getType(attributeArray.getIntValue(0)), attributeArray.getLong(1));
        }

        JSONArray costArray = JSONArray.parseArray(costs);
        for (Object costItem : costArray) {
            JSONArray rewardArray = JSONArray.parseArray(costItem.toString());
            RewardObject rewardObject = RewardObject.valueOf(rewardArray);
            costList.add(rewardObject);
        }

        JSONArray keyCostArray = JSONArray.parseArray(keyCosts);
        for (Object costItem : keyCostArray) {
            JSONArray rewardArray = JSONArray.parseArray(costItem.toString());
            RewardObject rewardObject = RewardObject.valueOf(rewardArray);
            keyCostList.add(rewardObject);
        }
    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(id, stage);
    }

    public int getId() {
        return id;
    }

    public int getStage() {
        return stage;
    }

    public int getLevelNum() {
        return levelNum;
    }

    public int getKeyLevelNum() {
        return keyLevelNum;
    }

    public int getSpecialEffectId() {
        return specialEffectId;
    }

    public int getUnlockLevel() {
        return unlockLevel;
    }

    public Map<SpriteAttributeType, Long> getAttributeMap() {
        return attributeMap;
    }

    public Map<SpriteAttributeType, Long> getKeyAttributeMap() {
        return keyAttributeMap;
    }

    public Collection<RewardObject> getCostList() {
        return costList;
    }

    public Collection<RewardObject> getKeyCostList() {
        return keyCostList;
    }
}
