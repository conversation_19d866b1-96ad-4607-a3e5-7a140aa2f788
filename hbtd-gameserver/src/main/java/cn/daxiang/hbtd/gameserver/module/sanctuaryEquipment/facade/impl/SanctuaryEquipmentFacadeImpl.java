package cn.daxiang.hbtd.gameserver.module.sanctuaryEquipment.facade.impl;

import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.framework.result.CollectionResult;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.hbtd.gameserver.core.GameBaseFacade;
import cn.daxiang.hbtd.gameserver.core.database.table.SanctuaryEquipment;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.SanctuaryBuildEquipmentConfig;
import cn.daxiang.hbtd.gameserver.core.oss.GameOssLogger;
import cn.daxiang.hbtd.gameserver.core.oss.type.OperationType;
import cn.daxiang.hbtd.gameserver.module.sanctuaryEquipment.dao.SanctuaryEquipmentDao;
import cn.daxiang.hbtd.gameserver.module.sanctuaryEquipment.facade.SanctuaryEquipmentFacade;
import cn.daxiang.hbtd.gameserver.module.sanctuaryEquipment.helper.SanctuaryEquipmentPushHelper;
import cn.daxiang.protocol.game.TypeProtocol;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/8/24
 */
@Component
public class SanctuaryEquipmentFacadeImpl extends GameBaseFacade implements SanctuaryEquipmentFacade {
    @Autowired
    private SanctuaryEquipmentDao sanctuaryEquipmentDao;

    @Override
    public CollectionResult<SanctuaryEquipment> getSanctuaryEquipmentList(long actorId) {
        Collection<SanctuaryEquipment> sanctuaryEquipmentList = sanctuaryEquipmentDao.getSanctuaryEquipmentList(actorId);
        return CollectionResult.collection(sanctuaryEquipmentList);
    }

    @Override
    public TResult<SanctuaryEquipment> getSanctuaryEquipment(long actorId, long equipmentId) {
        SanctuaryEquipment sanctuaryEquipment = sanctuaryEquipmentDao.getSanctuaryEquipment(actorId, equipmentId);
        if (sanctuaryEquipment == null) {
            return TResult.valueOf(SANCTUARY_EQUIPMENT_NOT_FOUND);
        }
        return TResult.sucess(sanctuaryEquipment);
    }

    @Override
    public CollectionResult<SanctuaryEquipment> createSanctuaryEquipment(long actorId, Map<Integer, Long> data, OperationType operationType) {
        Collection<SanctuaryEquipment> sanctuaryEquipmentList = Lists.newArrayList();
        for (Map.Entry<Integer, Long> entry : data.entrySet()) {
            int id = entry.getKey();
            long count = entry.getValue();
            SanctuaryBuildEquipmentConfig sanctuaryBuildEquipmentConfig = globalConfigService.findConfig(IdentiyKey.build(id), SanctuaryBuildEquipmentConfig.class);
            if (sanctuaryBuildEquipmentConfig == null) {
                LOGGER.error("SanctuaryEquipmentConfig not found,id:{}", id);
                continue;
            }
            for (long i = 0; i < count; i++) {
                SanctuaryEquipment sanctuaryEquipment = sanctuaryEquipmentDao.createSanctuaryEquipment(actorId, id);
                GameOssLogger.goodsAdd(actorId, operationType, TypeProtocol.RewardType.SANCTUARY_EQUIPMENT, sanctuaryEquipment.getEquipmentId(), sanctuaryEquipment.getConfigId(),
                    1, 1);
                sanctuaryEquipmentList.add(sanctuaryEquipment);
            }
        }
        SanctuaryEquipmentPushHelper.pushSanctuaryEquipmentList(actorId, sanctuaryEquipmentList);
        return CollectionResult.collection(sanctuaryEquipmentList);
    }

    @Override
    public void deleteSanctuaryEquipment(long actorId, Collection<Long> ids, OperationType operationType) {
        sanctuaryEquipmentDao.deleteSanctuaryEquipment(actorId, ids, operationType);
        SanctuaryEquipmentPushHelper.pushSanctuaryEquipmentDelete(actorId, ids);
    }

    @Override
    public void cleanAllSanctuaryEquipment(long actorId) {
        Collection<SanctuaryEquipment> sanctuaryEquipmentList = sanctuaryEquipmentDao.getSanctuaryEquipmentList(actorId);
        for (SanctuaryEquipment sanctuaryEquipment : sanctuaryEquipmentList) {
            dbQueue.deleteQueue(sanctuaryEquipment);
        }
    }
}
