package cn.daxiang.hbtd.gameserver.module.heroRoad.helper;

import cn.daxiang.framework.protocal.DataPacket;
import cn.daxiang.hbtd.gameserver.core.database.table.HeroRoad;
import cn.daxiang.hbtd.gameserver.core.database.table.Immortals;
import cn.daxiang.hbtd.gameserver.core.netty.channel.PlayerChannel;
import cn.daxiang.hbtd.gameserver.module.heroRoad.model.entity.HeroRecordEntity;
import cn.daxiang.hbtd.gameserver.module.immortals.helper.ImmortalsHelper;
import cn.daxiang.protocol.game.CommonProtocol;
import cn.daxiang.protocol.game.HeroroadProtocol;
import cn.daxiang.protocol.game.ImmortalsProtocol;
import cn.daxiang.protocol.game.ModuleProtocol;

import java.util.Collection;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/7/18
 */
public class HeroRoadPushHelper {

    public static void pushHeroRoadInfo(long actorId, HeroRoad heroRoad) {
        HeroroadProtocol.HeroRoadResponse.Builder builder = HeroroadProtocol.HeroRoadResponse.newBuilder();
        HeroroadProtocol.HeroRoadResponse response = HeroRoadHelper.buildHeroRoadResponse(heroRoad);
        DataPacket packet = DataPacket.valueOf(ModuleProtocol.Module.HEROROAD_VALUE, HeroroadProtocol.HeroroadCmd.PUSH_HERO_ROAD_INFO_VALUE, response);
        PlayerChannel.push(actorId, packet);
    }

}
