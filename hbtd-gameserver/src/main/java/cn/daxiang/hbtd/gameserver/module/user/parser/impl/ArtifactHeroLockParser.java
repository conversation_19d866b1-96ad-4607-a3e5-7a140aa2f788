package cn.daxiang.hbtd.gameserver.module.user.parser.impl;

import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.hbtd.gameserver.core.database.table.HeroManual;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.HeroConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.GlobalConfigService;
import cn.daxiang.hbtd.gameserver.module.heroManual.facade.HeroManualFacade;
import cn.daxiang.hbtd.gameserver.module.user.parser.AbstractActorLockParser;
import cn.daxiang.hbtd.gameserver.module.user.type.ActorLockType;
import cn.daxiang.shared.type.QualityType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

import static cn.daxiang.shared.GameModuleStatusCodeConstant.HERO_QUALITY_NOT_ENOUGH;

/**
 * 红色品质以上的武将达成7星
 *
 * @Author: Gary
 * @Date 2022-10-26 16:22
 * @Description:
 */
@Component
public class ArtifactHeroLockParser extends AbstractActorLockParser {
    @Autowired
    private HeroManualFacade heroManualFacade;
    @Autowired
    private GlobalConfigService globalConfigService;

    @Override
    protected ActorLockType getType() {
        return ActorLockType.ARTIFACT_HERO_SEVEN_LEVEL_COUNT;
    }

    @Override
    public Result unlock(long actorId, int condition) {
        TResult<HeroManual> result = heroManualFacade.getHeroManual(actorId);
        if (result.isFail()) {
            return Result.valueOf(result.statusCode);
        }
        Map<Integer, Integer> manualsMap = result.item.getManualsMap();
        long count = manualsMap.entrySet().stream().filter(x -> {
            HeroConfig config = globalConfigService.findConfig(IdentiyKey.build(x.getKey()), HeroConfig.class);
            return config.getQuality() >= QualityType.RARE.getId() && x.getValue() >= 7;
        }).count();
        if (count < condition) {
            return Result.valueOf(HERO_QUALITY_NOT_ENOUGH);
        }
        return Result.valueOf();
    }
}
