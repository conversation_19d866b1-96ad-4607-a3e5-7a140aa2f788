package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.dataconfig.annotation.FieldIgnore;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.framework.utils.DateUtils;
import cn.daxiang.protocol.game.TypeProtocol.VipCardType;
import cn.daxiang.shared.reward.RewardObject;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;

import java.util.Collection;
import java.util.concurrent.TimeUnit;

/**
 * VIP卡配置
 *
 * <AUTHOR>
 */
@DataFile(fileName = "vip_card_config")
public class VipCardConfig implements ModelAdapter {
    /**
     * VIP卡类型 {@code VipCardType}
     */
    private int type;
    /**
     * 过期天数
     */
    private int expirationDays;
    /**
     * 每日领取奖励
     */
    private String reward;
    /**
     * 累积额度
     */
    private int cumulative;

    @FieldIgnore
    private VipCardType vipCardType;
    @FieldIgnore
    private Collection<RewardObject> rewardList = Lists.newArrayList();

    @Override
    public void initialize() {
        vipCardType = VipCardType.forNumber(type);
        JSONArray rewardsArray = JSONArray.parseArray(reward);
        for (Object rewardItem : rewardsArray) {
            JSONArray rewardArray = JSONArray.parseArray(rewardItem.toString());
            RewardObject rewardObject = RewardObject.valueOf(rewardArray);
            rewardList.add(rewardObject);
        }
    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(type);
    }

    public VipCardType getVipCardType() {
        return vipCardType;
    }

    public int getType() {
        return type;
    }

    public int getExpirationDays() {
        return expirationDays;
    }

    public int getCumulative() {
        return cumulative;
    }

    public Collection<RewardObject> getRewardList() {
        return rewardList;
    }

    public long getExpirationTime(long expirationTime) {
        if (expirationDays == 0) {
            return 0;
        }
        if (expirationTime == 0L) {
            //获取当天的零点
            expirationTime = DateUtils.getDelayDay0AM(0).getTime();
        }
        return DateUtils.getDelayDate(expirationTime, expirationDays, TimeUnit.DAYS).getTime();
    }
}
