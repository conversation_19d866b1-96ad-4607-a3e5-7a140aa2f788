package cn.daxiang.hbtd.gameserver.core.dataconfig.service;

import cn.daxiang.framework.dataconfig.ConfigServiceAdapter;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.Activity2OpeningCeremonyConfig;
import cn.daxiang.hbtd.gameserver.module.activity.type.Activity2Type;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;

/**
 * <AUTHOR>
 * @date 2022/10/13
 */
@Component
public class Activity2OpeningCeremonyConfigService extends ConfigServiceAdapter {
    /**
     * 排行类型 key:data,value:type
     */
    private static Map<Integer, Set<Integer>> RANK_TYPE_MAP = Maps.newHashMap();
    /**
     * 成就类型 key:data,value:type
     */
    private static Map<Integer, Set<Integer>> TARGET_TYPE_MAP = Maps.newHashMap();
    /**
     * 排行类型配置列表 key:data,value:{key:rankType,value:{key:rank,value:Activity2OpeningCeremonyConfig}}
     */
    private static Map<Integer, Map<Integer, TreeMap<Long, Activity2OpeningCeremonyConfig>>> RANK_CONFIG_MAP = Maps.newHashMap();

    /**
     * 获取配置信息
     *
     * @param data
     * @param rankType
     * @return key:rank,value:Activity2OpeningCeremonyConfig
     */
    public static TreeMap<Long, Activity2OpeningCeremonyConfig> getRankConfigMap(int data, Activity2Type rankType) {
        Map<Integer, TreeMap<Long, Activity2OpeningCeremonyConfig>> rankMap = RANK_CONFIG_MAP.get(data);
        if (rankMap == null) {
            return null;
        }
        return rankMap.get(rankType.getId());
    }

    /**
     * 获取-data下所有排行类型
     *
     * @param data
     * @return
     */
    public static Set<Integer> getRankTypeIdList(int data) {
        return RANK_TYPE_MAP.get(data);
    }

    /**
     * 获取-data下所有成就类型
     *
     * @param data
     * @return
     */
    public static Set<Integer> getTargetTypeIdList(int data) {
        return TARGET_TYPE_MAP.get(data);
    }

    @Override
    protected void initialize() {
        Collection<Activity2OpeningCeremonyConfig> activity2OpeningCeremonyConfig = dataConfig.listAll(this, Activity2OpeningCeremonyConfig.class);
        for (Activity2OpeningCeremonyConfig config : activity2OpeningCeremonyConfig) {

            if (Activity2Type.getType(config.getType()).isRankType()) {
                Set<Integer> rankSet = RANK_TYPE_MAP.get(config.getData());
                if (rankSet == null) {
                    rankSet = Sets.newHashSet();
                    RANK_TYPE_MAP.put(config.getData(), rankSet);
                }
                rankSet.add(config.getType());
            } else {
                Set<Integer> targetSet = TARGET_TYPE_MAP.get(config.getData());
                if (targetSet == null) {
                    targetSet = Sets.newHashSet();
                    TARGET_TYPE_MAP.put(config.getData(), targetSet);
                }
                targetSet.add(config.getType());
            }

            if (config.getRank() <= 0) {
                continue;
            }
            Map<Integer, TreeMap<Long, Activity2OpeningCeremonyConfig>> rankMap = RANK_CONFIG_MAP.get(config.getData());
            if (rankMap == null) {
                rankMap = Maps.newHashMap();
                RANK_CONFIG_MAP.put(config.getData(), rankMap);
            }
            TreeMap<Long, Activity2OpeningCeremonyConfig> treeMap = rankMap.get(config.getType());
            if (treeMap == null) {
                treeMap = Maps.newTreeMap();
                rankMap.put(config.getType(), treeMap);
            }
            treeMap.put(config.getRank(), config);
        }
    }

    @Override
    protected void clean() {
        RANK_CONFIG_MAP.clear();
        RANK_TYPE_MAP.clear();
        TARGET_TYPE_MAP.clear();
    }
}
