package cn.daxiang.hbtd.gameserver.module.extension.parser.limitedCharge.impl;

import cn.daxiang.framework.result.TResult;
import cn.daxiang.framework.utils.FormulaUtils;
import cn.daxiang.hbtd.gameserver.core.database.table.LimitedTimeCharge;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.LimitedTimeChargeConfig;
import cn.daxiang.hbtd.gameserver.core.event.impl.HeroManualAchievementLevelUpEvent;
import cn.daxiang.hbtd.gameserver.module.extension.model.LimitedTimeChargeEntity;
import cn.daxiang.hbtd.gameserver.module.extension.parser.limitedCharge.AbstractLimitedChargeParser;
import cn.daxiang.hbtd.gameserver.module.extension.type.LimitedTimeChargeType;
import cn.daxiang.hbtd.gameserver.module.user.helper.ActorHelper;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/12/8
 */
@Component
public class HeroManualAchievementLevelParser extends AbstractLimitedChargeParser<HeroManualAchievementLevelUpEvent> {
    @Override
    protected LimitedTimeChargeType getType() {
        return LimitedTimeChargeType.HERO_MANUAL_ACHIEVEMENT_LEVEL;
    }

    @Override
    protected TResult<LimitedTimeChargeEntity> parseCondition(HeroManualAchievementLevelUpEvent event, LimitedTimeChargeConfig config, LimitedTimeCharge limitedTimeCharge) {
        int actorLevel = ActorHelper.getActorLevel(event.getActorId());
        int actorVipLevel = ActorHelper.getVipLevel(event.getActorId());
        long actorMaxPower = ActorHelper.getActorMaxPower(event.getActorId());
        if (FormulaUtils.executeBool(config.getCondition(), actorLevel, actorVipLevel, actorMaxPower, event.newLevel)) {
            return TResult.sucess(limitedTimeCharge.addEntity(config));
        }
        return TResult.fail();
    }
}
