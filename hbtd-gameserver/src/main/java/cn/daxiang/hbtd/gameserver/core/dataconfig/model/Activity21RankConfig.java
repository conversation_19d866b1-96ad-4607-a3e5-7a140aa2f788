package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.dataconfig.annotation.FieldIgnore;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.hbtd.gameserver.module.activity.type.Activity21RankType;
import cn.daxiang.shared.reward.RewardObject;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * @Author: Gary
 * @Date 2022-10-10 10:40
 * @Description:
 */
@DataFile(fileName = "activity_21_rank_config")
public class Activity21RankConfig implements ModelAdapter {
    /**
     * 活动data
     */
    private int data;
    /**
     * 排行榜类型
     * 1-积分排行榜
     * 2-幸运排行榜
     * {@link Activity21RankType}
     */
    private int type;
    /**
     * 排名
     */
    private long rank;
    /**
     * 奖励([[rewardType,id,num],[rewardType,id,num]])
     * {@code RewardObject}
     */
    private String rankRewards;
    @FieldIgnore
    private List<RewardObject> rewardList = Lists.newArrayList();

    @Override
    public void initialize() {
        JSONArray rewards = JSONArray.parseArray(rankRewards);
        for (Object rewardItem : rewards) {
            JSONArray itemArray = JSONArray.parseArray(rewardItem.toString());
            RewardObject rewardObject = RewardObject.valueOf(itemArray);
            rewardList.add(rewardObject);
        }
    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(data, type, rank);
    }

    public int getData() {
        return data;
    }

    public int getType() {
        return type;
    }

    public long getRank() {
        return rank;
    }

    public List<RewardObject> getRewardList() {
        return rewardList;
    }
}
