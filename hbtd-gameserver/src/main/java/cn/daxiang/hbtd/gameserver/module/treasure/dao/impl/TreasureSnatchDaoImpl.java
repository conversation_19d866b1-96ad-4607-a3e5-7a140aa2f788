package cn.daxiang.hbtd.gameserver.module.treasure.dao.impl;

import cn.daxiang.framework.database.SingleEntity;
import cn.daxiang.framework.database.dao.SingleEntityDaoImpl;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.hbtd.gameserver.core.database.table.TreasureSnatch;
import cn.daxiang.hbtd.gameserver.module.treasure.dao.TreasureSnatchDao;
import org.springframework.stereotype.Component;

@Component
public class TreasureSnatchDaoImpl extends SingleEntityDaoImpl implements TreasureSnatchDao {

    @Override
    public TreasureSnatch getTreasureSnatch(long actorId) {
        TreasureSnatch table = this.get(IdentiyKey.build(actorId));
        if (table.newEntity()) {
            table.setNewEntity(false);
            dbQueue.updateQueue(table);
        }
        return table;
    }

    @Override
    protected Class<? extends SingleEntity<?>> forClass() {
        return TreasureSnatch.class;
    }

    @Override
    protected void initMaxId() {
    }
}
