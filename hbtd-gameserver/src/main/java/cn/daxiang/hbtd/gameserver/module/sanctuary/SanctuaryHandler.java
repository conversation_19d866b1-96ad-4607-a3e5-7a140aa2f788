package cn.daxiang.hbtd.gameserver.module.sanctuary;

import cn.daxiang.framework.dispatch.DispatchType;
import cn.daxiang.framework.protocal.DataPacket;
import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.framework.router.annotation.Cmd;
import cn.daxiang.hbtd.gameserver.core.database.table.SanctuaryActor;
import cn.daxiang.hbtd.gameserver.core.database.table.SanctuaryBuilding;
import cn.daxiang.hbtd.gameserver.core.database.table.SanctuaryFund;
import cn.daxiang.hbtd.gameserver.core.router.GatewayRouterHandlerImpl;
import cn.daxiang.hbtd.gameserver.module.PbBuilder;
import cn.daxiang.hbtd.gameserver.module.sanctuary.facade.SanctuaryActorFacade;
import cn.daxiang.hbtd.gameserver.module.sanctuary.facade.SanctuaryBossFacade;
import cn.daxiang.hbtd.gameserver.module.sanctuary.facade.SanctuaryBuildingFacade;
import cn.daxiang.hbtd.gameserver.module.sanctuary.facade.SanctuaryFundFacade;
import cn.daxiang.hbtd.gameserver.module.sanctuary.helper.SanctuaryHelper;
import cn.daxiang.protocol.game.CommonProtocol;
import cn.daxiang.protocol.game.CommonProtocol.IntPacket;
import cn.daxiang.protocol.game.CommonProtocol.RewardResultResponse;
import cn.daxiang.protocol.game.ModuleProtocol;
import cn.daxiang.protocol.game.SanctuaryProtocol;
import cn.daxiang.shared.module.sanctuary.StoryPassInfo;
import io.netty.channel.Channel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/8/24
 */
@Component
public class SanctuaryHandler extends GatewayRouterHandlerImpl {
    @Autowired
    private SanctuaryActorFacade sanctuaryActorFacade;
    @Autowired
    private SanctuaryBuildingFacade sanctuaryBuildingFacade;
    @Autowired
    private SanctuaryFundFacade sanctuaryFundFacade;
    @Autowired
    private SanctuaryBossFacade sanctuaryBossFacade;

    @Override
    public int getModule() {
        return ModuleProtocol.Module.SANCTUARY_VALUE;
    }

    @Cmd(Id = SanctuaryProtocol.SanctuaryCmd.SANCTUARY_REGION_STATE_VALUE, dispatchType = DispatchType.ACTOR)
    public void getSanctuaryRegionState(Channel channel, Long actorId, DataPacket packet) {
        TResult<SanctuaryProtocol.SanctuaryRegionStateResponse> result = sanctuaryActorFacade.getSanctuaryRegionStateResponse();
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = SanctuaryProtocol.SanctuaryCmd.GET_SANCTUARY_ACTOR_VALUE, dispatchType = DispatchType.ACTOR)
    public void getSanctuaryActor(Channel channel, Long actorId, DataPacket packet) {
        TResult<SanctuaryActor> result = sanctuaryActorFacade.getSanctuaryActor(actorId);
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        SanctuaryProtocol.SanctuaryActorResponse response = SanctuaryHelper.buildSanctuaryActorResponse(result.item);
        channelWrite(channel, packet, response);
    }

    @Cmd(Id = SanctuaryProtocol.SanctuaryCmd.GET_SANCTUARY_FOSTOR_VALUE, dispatchType = DispatchType.ACTOR)
    public void getSanctuaryFoster(Channel channel, Long actorId, DataPacket packet) {
        TResult<SanctuaryBuilding> result = sanctuaryBuildingFacade.getSanctuaryBuilding(actorId);
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        SanctuaryProtocol.SanctuaryFosterResponse response = SanctuaryHelper.buildSanctuaryFosterResponse(result.item);
        channelWrite(channel, packet, response);
    }

    @Cmd(Id = SanctuaryProtocol.SanctuaryCmd.SANCTUARY_PVP_BATTLE_VALUE, dispatchType = DispatchType.ACTOR)
    public void pvpBattle(Channel channel, Long actorId, DataPacket packet) {
        SanctuaryProtocol.SanctuaryPVPRequest request = packet.getValue(SanctuaryProtocol.SanctuaryPVPRequest.parser());
        Result result = sanctuaryActorFacade.pvpBattle(actorId, request);
        channelWrite(channel, packet, result.statusCode);
    }

    @Cmd(Id = SanctuaryProtocol.SanctuaryCmd.SANCTUARY_PVE_PREPARE_VALUE, dispatchType = DispatchType.ACTOR)
    public void prepare(Channel channel, Long actorId, DataPacket packet) {
        IntPacket request = packet.getValue(IntPacket.parser());
        Result result = sanctuaryActorFacade.prepare(actorId, request.getValue());
        channelWrite(channel, packet, result.statusCode);
    }

    @Cmd(Id = SanctuaryProtocol.SanctuaryCmd.SANCTUARY_PVE_BATTLE_VALUE, dispatchType = DispatchType.ACTOR)
    public void pveBattle(Channel channel, Long actorId, DataPacket packet) {
        SanctuaryProtocol.SanctuaryPVERequest request = packet.getValue(SanctuaryProtocol.SanctuaryPVERequest.parser());
        TResult<CommonProtocol.RewardResultResponse> result = sanctuaryActorFacade.pveBattle(actorId, request.getId(), request.getStarNum().getListList(), request.getCheck());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = SanctuaryProtocol.SanctuaryCmd.GET_SANCTUARY_RECORD_VALUE, dispatchType = DispatchType.ACTOR)
    public void getRecord(Channel channel, Long actorId, DataPacket packet) {
        IntPacket request = packet.getValue(IntPacket.parser());
        TResult<SanctuaryProtocol.SanctuaryRecordResponse> result = sanctuaryActorFacade.getRecord(actorId, request.getValue());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = SanctuaryProtocol.SanctuaryCmd.GET_SANCTUARY_ADDITION_VALUE, dispatchType = DispatchType.ACTOR)
    public void getAddition(Channel channel, Long actorId, DataPacket packet) {
        IntPacket request = packet.getValue(IntPacket.parser());
        TResult<StoryPassInfo> result = sanctuaryActorFacade.getAddition(actorId, request.getValue());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        StoryPassInfo storyPassInfo = result.item;
        SanctuaryProtocol.SanctuaryAdditionResponse.Builder builder = SanctuaryProtocol.SanctuaryAdditionResponse.newBuilder();
        builder.setPassNum(storyPassInfo.getPassNumber());
        if (!storyPassInfo.getAttributes().isEmpty()) {
            builder.setActorProfile(PbBuilder.buildActorProfile(storyPassInfo.getAttributes()));
        }
        channelWrite(channel, packet, builder.build());
    }

    @Cmd(Id = SanctuaryProtocol.SanctuaryCmd.GET_SANCTUARY_PROGRESS_RANK_VALUE, dispatchType = DispatchType.ACTOR)
    public void getProgressRank(Channel channel, Long actorId, DataPacket packet) {
        TResult<SanctuaryProtocol.SanctuaryProgressRankResponse> result = sanctuaryActorFacade.getProgressRank(actorId);
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = SanctuaryProtocol.SanctuaryCmd.GET_MY_SANCTUARY_PROGRESS_RANK_VALUE, dispatchType = DispatchType.ACTOR)
    public void getMyProgressRank(Channel channel, Long actorId, DataPacket packet) {
        TResult<CommonProtocol.LongPacket> result = sanctuaryActorFacade.getMyProgressRank(actorId);
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = SanctuaryProtocol.SanctuaryCmd.GET_SANCTUARY_FOSTER_RANK_VALUE, dispatchType = DispatchType.ACTOR)
    public void getFosterRank(Channel channel, Long actorId, DataPacket packet) {
        TResult<SanctuaryProtocol.SanctuaryFosterRankResponse> result = sanctuaryActorFacade.getFosterRank(actorId);
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = SanctuaryProtocol.SanctuaryCmd.RECEIVE_SANCTUARY_SUPPLY_VALUE, dispatchType = DispatchType.ACTOR)
    public void receiveSupply(Channel channel, Long actorId, DataPacket packet) {
        IntPacket request = packet.getValue(IntPacket.parser());
        TResult<CommonProtocol.RewardResultResponse> result = sanctuaryActorFacade.receiveSupply(actorId, request.getValue());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = SanctuaryProtocol.SanctuaryCmd.RECEIVE_SANCTUARY_STAR_REWARD_VALUE, dispatchType = DispatchType.ACTOR)
    public void receiveStarReward(Channel channel, Long actorId, DataPacket packet) {
        IntPacket request = packet.getValue(IntPacket.parser());
        TResult<CommonProtocol.RewardResultResponse> result = sanctuaryActorFacade.receiveStarReward(actorId, request.getValue());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = SanctuaryProtocol.SanctuaryCmd.GET_SANCTUARY_FUND_VALUE, dispatchType = DispatchType.ACTOR)
    public void getFund(Channel channel, Long actorId, DataPacket packet) {
        TResult<SanctuaryFund> result = sanctuaryFundFacade.getFund(actorId);
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        SanctuaryProtocol.SanctuaryFundResponse response = SanctuaryHelper.buildSanctuaryFundResponse(result.item);
        channelWrite(channel, packet, response);
    }

    @Cmd(Id = SanctuaryProtocol.SanctuaryCmd.RECIEVE_SANCTUARY_FFUND_VALUE, dispatchType = DispatchType.ACTOR)
    public void receiveFund(Channel channel, Long actorId, DataPacket packet) {
        SanctuaryProtocol.RecieveSanctuaryFundRequest request = packet.getValue(SanctuaryProtocol.RecieveSanctuaryFundRequest.parser());
        TResult<CommonProtocol.RewardResultResponse> result = sanctuaryFundFacade.receiveFund(actorId, request.getIsRecieceLuxury(), request.getConfigId());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = SanctuaryProtocol.SanctuaryCmd.QUICKLY_RECIEVE_SANCTUARY_FFUND_VALUE, dispatchType = DispatchType.ACTOR)
    public void quicklyReceiveFund(Channel channel, Long actorId, DataPacket packet) {
        IntPacket request = packet.getValue(IntPacket.parser());
        TResult<CommonProtocol.RewardResultResponse> result = sanctuaryFundFacade.quicklyReceiveFund(actorId, request.getValue());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = SanctuaryProtocol.SanctuaryCmd.RECIEVE_SANCTUARY_FWELFARE_VALUE, dispatchType = DispatchType.ACTOR)
    public void receiveWelfare(Channel channel, Long actorId, DataPacket packet) {
        IntPacket request = packet.getValue(IntPacket.parser());
        TResult<CommonProtocol.RewardResultResponse> result = sanctuaryFundFacade.receiveWelfare(actorId, request.getValue());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = SanctuaryProtocol.SanctuaryCmd.RECIEVE_SANCTUARY_STAGE_TASK_VALUE, dispatchType = DispatchType.ACTOR)
    public void receiveStageTask(Channel channel, Long actorId, DataPacket packet) {
        IntPacket request = packet.getValue(IntPacket.parser());
        TResult<CommonProtocol.RewardResultResponse> result = sanctuaryActorFacade.receiveStageTask(actorId, request.getValue());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = SanctuaryProtocol.SanctuaryCmd.SANCTUARY_EQUIPMENT_SYNTHESIZE_AND_EQUIP_VALUE, dispatchType = DispatchType.ACTOR)
    public void synthesizeAndEquip(Channel channel, Long actorId, DataPacket packet) {
        SanctuaryProtocol.SanctuaryEquipmentSynthesizeRequest request = packet.getValue(SanctuaryProtocol.SanctuaryEquipmentSynthesizeRequest.parser());
        Result result = sanctuaryBuildingFacade.synthesizeAndEquip(actorId, request.getType(), request.getPosition(), request.getConfigId());
        channelWrite(channel, packet, result.statusCode);
    }

    @Cmd(Id = SanctuaryProtocol.SanctuaryCmd.SANCTUARY_EQUIPMENT_EQUIP_VALUE, dispatchType = DispatchType.ACTOR)
    public void equip(Channel channel, Long actorId, DataPacket packet) {
        SanctuaryProtocol.SanctuaryEquipmentEquipRequest request = packet.getValue(SanctuaryProtocol.SanctuaryEquipmentEquipRequest.parser());
        Result result = sanctuaryBuildingFacade.equip(actorId, request.getType(), request.getPosition(), request.getSanctuaryEquipmentId());
        channelWrite(channel, packet, result.statusCode);
    }

    @Cmd(Id = SanctuaryProtocol.SanctuaryCmd.SANCTUARY_MAIN_BUILDING_LEVEL_UP_VALUE, dispatchType = DispatchType.ACTOR)
    public void mainLevelUp(Channel channel, Long actorId, DataPacket packet) {
        Result result = sanctuaryBuildingFacade.mainLevelUp(actorId);
        channelWrite(channel, packet, result.statusCode);
    }

    @Cmd(Id = SanctuaryProtocol.SanctuaryCmd.SANCTUARY_CAMP_BUILDING_STAGE_UP_VALUE, dispatchType = DispatchType.ACTOR)
    public void campLevelUp(Channel channel, Long actorId, DataPacket packet) {
        IntPacket request = packet.getValue(IntPacket.parser());
        Result result = sanctuaryBuildingFacade.campLevelUp(actorId, request.getValue());
        channelWrite(channel, packet, result.statusCode);

    }

    @Cmd(Id = SanctuaryProtocol.SanctuaryCmd.SANCTUARY_RECEIVE_HANG_UP_VALUE, dispatchType = DispatchType.ACTOR)
    public void receiveHangUp(Channel channel, Long actorId, DataPacket packet) {
        TResult<CommonProtocol.RewardResultResponse> result = sanctuaryActorFacade.receiveHangUp(actorId);
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = SanctuaryProtocol.SanctuaryCmd.SANCTUARY_USE_HANG_UP_VALUE, dispatchType = DispatchType.ACTOR)
    public void useHangUp(Channel channel, Long actorId, DataPacket packet) {
        CommonProtocol.KeyValuePacket request = packet.getValue(CommonProtocol.KeyValuePacket.parser());
        TResult<CommonProtocol.RewardResultResponse> result = sanctuaryActorFacade.useHangUp(actorId, request.getKey(), request.getValue());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = SanctuaryProtocol.SanctuaryCmd.SANCTUARY_BOSS_CHALLENGE_VALUE, dispatchType = DispatchType.ACTOR)
    public void bossChallenge(Channel channel, Long actorId, DataPacket packet) {
        SanctuaryProtocol.SanctuaryLineup request = packet.getValue(SanctuaryProtocol.SanctuaryLineup.parser());
        Result result = sanctuaryBossFacade.bossChallenge(actorId, request.getHeroMap(), request.getSoulMap(), request.getBeastMap(), request.getFrostmourneMap());
        channelWrite(channel, packet, result.statusCode);
    }

    @Cmd(Id = SanctuaryProtocol.SanctuaryCmd.SANCTUARY_BOSS_RECEIVE_VALUE, dispatchType = DispatchType.ACTOR)
    public void bossReceive(Channel channel, Long actorId, DataPacket packet) {
        TResult<CommonProtocol.RewardResultResponse> result = sanctuaryBossFacade.bossReceive(actorId);
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = SanctuaryProtocol.SanctuaryCmd.SANCTUARY_BOSS_RANK_VALUE, dispatchType = DispatchType.ACTOR)
    public void bossRank(Channel channel, Long actorId, DataPacket packet) {
        TResult<SanctuaryProtocol.SanctuaryBossRankResponse> result = sanctuaryBossFacade.bossRank(actorId);
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = SanctuaryProtocol.SanctuaryCmd.SANCTUARY_BOSS_GET_VALUE, dispatchType = DispatchType.ACTOR)
    public void getBossInfo(Channel channel, Long actorId, DataPacket packet) {
        TResult<SanctuaryProtocol.SanctuaryBossResponse> result = sanctuaryBossFacade.getSanctuaryBoss(actorId);
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = SanctuaryProtocol.SanctuaryCmd.SANCTUARY_PVE_BATTLE_QUICKLY_PASS_VALUE, dispatchType = DispatchType.ACTOR)
    public void sanctuaryPveBattleQuicklyPass(Channel channel, Long actorId, DataPacket packet) {
        IntPacket request = packet.getValue(IntPacket.parser());
        TResult<RewardResultResponse> result = sanctuaryActorFacade.sanctuaryPveBattleQuicklyPass(actorId, request.getValue());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = SanctuaryProtocol.SanctuaryCmd.SANCTUARY_RECEIVE_STORY_REWARDS_VALUE, dispatchType = DispatchType.ACTOR)
    public void sanctuaryReceiveStoryRewards(Channel channel, Long actorId, DataPacket packet) {
        IntPacket request = packet.getValue(IntPacket.parser());
        TResult<RewardResultResponse> result = sanctuaryActorFacade.sanctuaryReceiveStoryRewards(actorId, request.getValue());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }
}
