package cn.daxiang.hbtd.gameserver.module.user.parser.impl;

import cn.daxiang.framework.result.Result;
import cn.daxiang.hbtd.gameserver.module.qindungeon.helper.QinDungeonHelper;
import cn.daxiang.hbtd.gameserver.module.user.parser.AbstractActorLockParser;
import cn.daxiang.hbtd.gameserver.module.user.type.ActorLockType;
import org.springframework.stereotype.Component;

import static cn.daxiang.shared.GameModuleStatusCodeConstant.QINDUNGEON_DAN_NOT_ENOUGH;

/**
 * 无尽棋局段位解锁条件解析
 */
@Component
public class QinDungeonDanLockParser extends AbstractActorLockParser {

    @Override
    public Result unlock(long actorId, int condition) {
        if (QinDungeonHelper.getHistoryDan(actorId) < condition) {
            return Result.valueOf(QINDUNGEON_DAN_NOT_ENOUGH);
        }
        return Result.valueOf();
    }

    @Override
    protected ActorLockType getType() {
        return ActorLockType.QIN_DUNGEON_DAN;
    }

}
