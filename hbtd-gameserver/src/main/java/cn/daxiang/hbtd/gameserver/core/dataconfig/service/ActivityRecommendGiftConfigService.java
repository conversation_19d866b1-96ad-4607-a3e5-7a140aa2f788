package cn.daxiang.hbtd.gameserver.core.dataconfig.service;

import cn.daxiang.framework.dataconfig.ConfigServiceAdapter;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.ActivityRecommendGiftConfig;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;

@Component
public class ActivityRecommendGiftConfigService extends ConfigServiceAdapter {

    /**
     * key:data value{key:getChargeId,value:ActivityDailyPreferentialConfig}
     */
    private static Map<Integer, Map<Integer, ActivityRecommendGiftConfig>> ACTIVITY_RECOMMEND_GIFT_CONFIG_MAP = Maps.newHashMap();

    public static Map<Integer, ActivityRecommendGiftConfig> getActivityRecommendGiftConfigMap(int data) {
        Map<Integer, ActivityRecommendGiftConfig> map = ACTIVITY_RECOMMEND_GIFT_CONFIG_MAP.get(data);
        return map;
    }

    @Override
    protected void initialize() {
        Collection<ActivityRecommendGiftConfig> activityDailyPreferentialConfigs = dataConfig.listAll(this, ActivityRecommendGiftConfig.class);

        for (ActivityRecommendGiftConfig config : activityDailyPreferentialConfigs) {
            Map<Integer, ActivityRecommendGiftConfig> configMap = ACTIVITY_RECOMMEND_GIFT_CONFIG_MAP.get(config.getData());
            if (configMap == null) {
                configMap = Maps.newHashMap();
                ACTIVITY_RECOMMEND_GIFT_CONFIG_MAP.put(config.getData(), configMap);
            }
            configMap.put(config.getChargeId(), config);
        }

    }

    @Override
    protected void clean() {
        ACTIVITY_RECOMMEND_GIFT_CONFIG_MAP.clear();
    }
}
