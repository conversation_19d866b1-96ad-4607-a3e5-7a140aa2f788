package cn.daxiang.hbtd.gameserver.module.frostmourne.facade.impl;

import cn.daxiang.framework.extend.KeyValue;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.framework.result.CollectionResult;
import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.framework.utils.FormulaUtils;
import cn.daxiang.hbtd.gameserver.core.GameBaseFacade;
import cn.daxiang.hbtd.gameserver.core.GameConfig;
import cn.daxiang.hbtd.gameserver.core.database.table.Frostmourne;
import cn.daxiang.hbtd.gameserver.core.database.table.Goods;
import cn.daxiang.hbtd.gameserver.core.dataconfig.convert.impl.RewardObjectListConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.FrostmourneConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.FrostmourneForgeConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.FrostmourneForgeCostGroupConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.FrostmourneLevelConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.FrostmourneStarConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.FrostmourneConfigService;
import cn.daxiang.hbtd.gameserver.core.dispatch.DispatchHelper;
import cn.daxiang.hbtd.gameserver.core.event.impl.FrostmourneStarUpEvent;
import cn.daxiang.hbtd.gameserver.core.event.impl.LineupPowerRefreshEvent;
import cn.daxiang.hbtd.gameserver.core.oss.GameOssLogger;
import cn.daxiang.hbtd.gameserver.core.oss.type.OperationType;
import cn.daxiang.hbtd.gameserver.module.PbBuilder;
import cn.daxiang.hbtd.gameserver.module.frostmourne.dao.FrostmourneDao;
import cn.daxiang.hbtd.gameserver.module.frostmourne.facade.FrostmourneFacade;
import cn.daxiang.hbtd.gameserver.module.frostmourne.helper.FrostmournePushHelper;
import cn.daxiang.hbtd.gameserver.module.goods.facade.GoodsFacade;
import cn.daxiang.hbtd.gameserver.module.goods.helper.RewardHelper;
import cn.daxiang.hbtd.gameserver.module.lineup.helper.LineupHelper;
import cn.daxiang.hbtd.gameserver.module.user.helper.ActorHelper;
import cn.daxiang.hbtd.gameserver.module.user.type.ActorUnlockType;
import cn.daxiang.protocol.game.CommonProtocol;
import cn.daxiang.protocol.game.TypeProtocol;
import cn.daxiang.shared.GlobalConfigKey;
import cn.daxiang.shared.reward.RewardObject;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/11/11
 */
@Component
public class FrostmourneFacadeImpl extends GameBaseFacade implements FrostmourneFacade {
    /**
     * 神兵初始强化等级
     */
    private static final int INIT_LEVEL = 1;
    @Autowired
    private FrostmourneDao frostmourneDao;
    @Autowired
    private GoodsFacade goodsFacade;

    @Override
    public CollectionResult getFrostmourneList(long actorId) {
        Collection<Frostmourne> frostmourneInfoList = frostmourneDao.getFrostmourneInfoList(actorId);
        return CollectionResult.collection(frostmourneInfoList);
    }

    @Override
    public TResult<Frostmourne> getFrostmourne(long actorId, int configId) {
        Result unlock = ActorHelper.unlock(actorId, ActorUnlockType.FROSTMOURNE);
        if (unlock.isFail()) {
            return TResult.valueOf(unlock);
        }
        Frostmourne frostmourne = frostmourneDao.getFrostmourne(actorId, configId);
        if (frostmourne == null) {
            return TResult.valueOf(FROSTMOURNE_NOT_FOUND);
        }
        return TResult.sucess(frostmourne);
    }

    @Override
    public CollectionResult<Frostmourne> createFrostmourne(long actorId, Map<Integer, Long> data, OperationType operationType) {
        Collection<Frostmourne> frostmourneList = Lists.newArrayList();
        for (Map.Entry<Integer, Long> entry : data.entrySet()) {
            int id = entry.getKey();
            long count = entry.getValue();
            FrostmourneConfig frostmourneConfig = globalConfigService.findConfig(IdentiyKey.build(id), FrostmourneConfig.class);
            if (frostmourneConfig == null) {
                LOGGER.error("FrostmourneConfig not found,id:{}", id);
                continue;
            }
            for (long i = 0; i < count; i++) {
                Frostmourne frostmourne = frostmourneDao.createFrostmourne(actorId, id, INIT_LEVEL);
                GameOssLogger.goodsAdd(actorId, operationType, TypeProtocol.RewardType.FROSTMOURNE, frostmourne.getConfigId(), frostmourne.getConfigId(), 1, 1);
                frostmourneList.add(frostmourne);
            }
        }
        FrostmournePushHelper.pushFrostmourneInfoList(actorId, frostmourneList);
        return CollectionResult.collection(frostmourneList);
    }

    @Override
    public Result levelUp(Long actorId, Map<Integer, Long> configMap) {
        Result unlock = ActorHelper.unlock(actorId, ActorUnlockType.FROSTMOURNE);
        if (unlock.isFail()) {
            return unlock;
        }
        Collection<Frostmourne> pushList = Lists.newLinkedList();
        for (Map.Entry<Integer, Long> entry : configMap.entrySet()) {
            int configId = entry.getKey();
            long times = entry.getValue();
            if (times <= 0 || times > GameConfig.getClientCountLimit()) {
                return Result.valueOf(INVALID_PARAM);
            }
            Frostmourne frostmourne = frostmourneDao.getFrostmourne(actorId, configId);
            if (frostmourne == null) {
                return Result.valueOf(FROSTMOURNE_NOT_FOUND);
            }
            FrostmourneLevelConfig frostmourneConfig = FrostmourneConfigService.getFrostmourneLevelConfig(configId, frostmourne.getLevel());
            if (frostmourneConfig == null) {
                LOGGER.error("FrostmourneLevelConfig not found,id:{},Level:{}", configId, frostmourne.getLevel());
                return Result.valueOf(CONFIG_NOT_FOUND);
            }
            if (frostmourneConfig.getNextLevel() == 0) {
                return Result.valueOf(FROSTMOURNE_LEVEL_HAVE_FULL);
            }
            KeyValue<Integer, Collection<RewardObject>> kvResult = countLevelUpObjects(actorId, configId, frostmourne.getLevel(), times);
            int levelupTimes = kvResult.getKey();
            if (levelupTimes == -1) {
                return Result.valueOf(CONFIG_NOT_FOUND);
            }
            if (levelupTimes == 0) {
                return Result.valueOf(GOODS_NOT_ENOUGH);
            }
            Result decrease = RewardHelper.decrease(actorId, kvResult.getValue(), OperationType.FROSTMOURNE_LEVEL);
            if (decrease.isFail()) {
                return decrease;
            }
            frostmourne.levelUp(levelupTimes);
            dbQueue.updateQueue(frostmourne);
            pushList.add(frostmourne);
        }
        FrostmournePushHelper.pushFrostmourneInfoList(actorId, pushList);
        this.refreshPower(actorId, configMap.keySet());
        return Result.valueOf();
    }

    /**
     * 计算实际的升级次数和升级消耗
     *
     * @param actorId
     * @param configId
     * @param level
     * @param times
     * @return 返回的KeyValue中的key==-1说明配置错误，key==0说明材料不足
     */
    private KeyValue<Integer, Collection<RewardObject>> countLevelUpObjects(long actorId, int configId, int level, long times) {
        Collection<RewardObject> rewardList = Lists.newArrayList();
        Collection<RewardObject> enoughList = Lists.newArrayList();
        //真正升级次数
        int realTimes = 0;
        for (int i = level; i < level + times; i++) {
            FrostmourneLevelConfig frostmourneConfig = FrostmourneConfigService.getFrostmourneLevelConfig(configId, i);
            if (frostmourneConfig == null) {
                LOGGER.error("FrostmourneLevelConfig not found,id:{},Level:{}", configId, i);
                return new KeyValue<>(-1, rewardList);
            }
            //满级就直接返回
            if (frostmourneConfig.getNextLevel() == 0) {
                return new KeyValue<>(realTimes, rewardList);
            }
            enoughList.addAll(frostmourneConfig.getCostList());
            Result hasEnough = RewardHelper.hasEnough(actorId, enoughList);
            if (hasEnough.isFail()) {
                //如果升一级的材料都不足，那么realTimes==0
                return new KeyValue<>(realTimes, rewardList);
            }
            realTimes++;
            rewardList.addAll(frostmourneConfig.getCostList());
        }
        return new KeyValue<>(realTimes, rewardList);
    }

    @Override
    public Result starUp(Long actorId, int configId) {
        Result unlock = ActorHelper.unlock(actorId, ActorUnlockType.FROSTMOURNE, 2);
        if (unlock.isFail()) {
            return unlock;
        }
        Frostmourne frostmourne = frostmourneDao.getFrostmourne(actorId, configId);
        if (frostmourne == null) {
            return Result.valueOf(FROSTMOURNE_NOT_FOUND);
        }
        FrostmourneStarConfig starConfig = globalConfigService.findConfig(IdentiyKey.build(configId, frostmourne.getStarLevel()), FrostmourneStarConfig.class);
        if (starConfig == null) {
            return Result.valueOf(CONFIG_NOT_FOUND);
        }
        if (starConfig.getCostList().isEmpty()) {
            return Result.valueOf(FROSTMOURNE_STAR_HAVE_FULL);
        }

        Collection<FrostmourneStarConfig> frostmourneStarConfigList =
            FrostmourneConfigService.getFrostmourneStarConfigList(configId, starConfig.getStar(), starConfig.getShowStarStage());
        if (frostmourneStarConfigList == null) {
            LOGGER.error("getFrostmourneStarConfigList  error！id:{},star:{},starStage:{}", configId, starConfig.getStar(), starConfig.getShowStarStage());
            return Result.valueOf(INVALID_PARAM);
        }
        //升星次数
        int starUptimes = 0;
        Collection<RewardObject> enoughList = Lists.newArrayList();
        Collection<RewardObject> realCostList = Lists.newArrayList();
        for (FrostmourneStarConfig config : frostmourneStarConfigList) {
            //当配置的消耗列表为空说明满级了，就需要跳出循环
            if (config.getCostList().isEmpty()) {
                continue;
            }
            enoughList.addAll(config.getCostList());
            Result hasEnough = RewardHelper.hasEnough(actorId, enoughList);
            if (hasEnough.isFail()) {
                continue;
            }
            realCostList.addAll(config.getCostList());
            starUptimes++;
        }
        //如果升级次数等于0，说明升一星的材料都不足，就返回错误码
        if (starUptimes == 0) {
            return Result.valueOf(GOODS_NOT_ENOUGH);
        }
        Result decrease = RewardHelper.decrease(actorId, realCostList, OperationType.FROSTMOURNE_STAR);
        if (decrease.isFail()) {
            return decrease;
        }
        frostmourne.starUp(starUptimes);
        dbQueue.updateQueue(frostmourne);
        DispatchHelper.postEvent(new FrostmourneStarUpEvent(actorId, configId, frostmourne.getStarLevel(), starUptimes));
        FrostmournePushHelper.pushFrostmourneInfo(actorId, frostmourne);
        this.refreshPower(actorId, configId);
        return Result.valueOf();
    }

    @Override
    public Result forge(Long actorId, int configId, Map<Integer, Map<Long, Integer>> costMap) {
        Result unlock = ActorHelper.unlock(actorId, ActorUnlockType.FROSTMOURNE, 3);
        if (unlock.isFail()) {
            return unlock;
        }
        FrostmourneConfig frostmourneConfig = globalConfigService.findConfig(IdentiyKey.build(configId), FrostmourneConfig.class);
        if (frostmourneConfig == null) {
            LOGGER.error("FrostmourneConfig not found,id:{}", configId);
            return Result.valueOf(CONFIG_NOT_FOUND);
        }
        Frostmourne frostmourne = frostmourneDao.getFrostmourne(actorId, configId);
        if (frostmourne == null) {
            return Result.valueOf(FROSTMOURNE_NOT_FOUND);
        }
        if (frostmourne.getStarLevel() < frostmourneConfig.getNeedStar()) {
            return Result.valueOf(FROSTMOURNE_CANNOT_FORGE);
        }
        FrostmourneForgeConfig forgeConfig = globalConfigService.findConfig(IdentiyKey.build(configId, frostmourne.getForgeLevel()), FrostmourneForgeConfig.class);
        if (forgeConfig == null) {
            LOGGER.error("FrostmourneForgeConfig not found,id:{},level:{}", configId, frostmourne.getForgeLevel());
            return Result.valueOf(CONFIG_NOT_FOUND);
        }
        if (forgeConfig.getCostList().isEmpty()) {
            return Result.valueOf(FROSTMOURNE_FORGE_FULL);
        }
        CollectionResult<RewardObject> collectionResult = this.checkForgeCostMap(actorId, costMap, forgeConfig);
        if (collectionResult.isFail()) {
            return Result.valueOf(collectionResult.statusCode);
        }
        Collection<RewardObject> cost = Lists.newArrayList(forgeConfig.getCostList());
        cost.addAll(collectionResult.item);
        Result result = RewardHelper.decrease(actorId, cost, OperationType.HERO_EIGHT_GATE);
        if (result.isFail()) {
            return result;
        }
        frostmourne.forge(cost);
        dbQueue.updateQueue(frostmourne);
        FrostmournePushHelper.pushFrostmourneInfo(actorId, frostmourne);
        this.refreshPower(actorId, configId);
        return Result.valueOf();
    }

    private CollectionResult<RewardObject> checkForgeCostMap(long actorId, Map<Integer, Map<Long, Integer>> costMap, FrostmourneForgeConfig forgeConfig) {
        Collection<RewardObject> costChipList = Lists.newArrayList();
        Map<Integer, Integer> groupCosts = forgeConfig.getGroupCostMap();
        for (Map.Entry<Integer, Map<Long, Integer>> mapEntry : costMap.entrySet()) {
            int groupId = mapEntry.getKey();
            Map<Long, Integer> costs = mapEntry.getValue();
            int haveCount = 0;
            FrostmourneForgeCostGroupConfig config = globalConfigService.findConfig(IdentiyKey.build(groupId), FrostmourneForgeCostGroupConfig.class);
            if (config == null) {
                return CollectionResult.valueOf(CONFIG_NOT_FOUND);
            }
            for (Map.Entry<Long, Integer> entry : costs.entrySet()) {
                long goodsUid = entry.getKey();
                int count = entry.getValue();
                TResult<Goods> tResult = goodsFacade.getGoods(actorId, goodsUid);
                if (tResult.isFail()) {
                    return CollectionResult.valueOf(tResult.statusCode);
                }
                Goods goods = tResult.item;
                int goodsId = goods.getGoodsId();
                if (goodsFacade.getGoodsNum(actorId, goodsId) < count) {
                    return CollectionResult.valueOf(GOODS_NOT_ENOUGH);
                }
                if (!config.getItemList().contains(goodsId)) {
                    return CollectionResult.valueOf(INVALID_PARAM);
                }
                costChipList.add(RewardObject.valueOf(TypeProtocol.RewardType.GOODS_VALUE, goodsId, count));
                haveCount += count;
            }
            if (groupCosts.get(groupId) == null) {
                LOGGER.error("BreakoutConfig groupCosts not exist key:{}", groupId);
                return CollectionResult.valueOf(INVALID_PARAM);
            }
            if (haveCount != groupCosts.get(groupId)) {
                return CollectionResult.valueOf(INVALID_PARAM);
            }
        }
        return CollectionResult.collection(costChipList);
    }

    @Override
    public TResult<CommonProtocol.RewardResultResponse> synthesize(Long actorId, int configId) {
        TResult<Frostmourne> frostmourneTResult = getFrostmourne(actorId, configId);
        if (frostmourneTResult.isOk()) {
            return TResult.valueOf(FROSTMOURNE_HAVE_EXIST);
        }
        FrostmourneConfig frostmourneConfig = globalConfigService.findConfig(IdentiyKey.build(configId), FrostmourneConfig.class);
        if (frostmourneConfig == null) {
            LOGGER.error("FrostmourneConfig not found,id:{}", configId);
            return TResult.valueOf(CONFIG_NOT_FOUND);
        }
        RewardObject decreaseObject = RewardObject.valueOf(TypeProtocol.RewardType.GOODS_VALUE, frostmourneConfig.getFragmentId(), frostmourneConfig.getFragmentCount());
        Result decrease = RewardHelper.decrease(actorId, Lists.newArrayList(decreaseObject), OperationType.FROSTMOURNE_SYNTHESIZE);
        if (decrease.isFail()) {
            return TResult.valueOf(decrease);
        }
        RewardObject rewardObject = RewardObject.valueOf(TypeProtocol.RewardType.FROSTMOURNE_VALUE, configId, 1);
        CommonProtocol.RewardResult rewardResult = RewardHelper.sendRewardList(actorId, Lists.newArrayList(rewardObject), OperationType.FROSTMOURNE_SYNTHESIZE);
        CommonProtocol.RewardResultResponse response = PbBuilder.buildRewardResultResponse(rewardResult);
        return TResult.sucess(response);
    }

    @Override
    public CollectionResult<RewardObject> frostmourneRebirthPreview(long actorId, Collection<Long> configIds) {
        Result res = ActorHelper.unlock(actorId, ActorUnlockType.REBIRTH, 7);
        if (res.isFail()) {
            return CollectionResult.valueOf(res.statusCode);
        }
        Collection<RewardObject> rewardList = Lists.newArrayList();
        for (long frostmourneId : configIds) {
            Frostmourne frostmourne = frostmourneDao.getFrostmourne(actorId, (int) frostmourneId);
            if (frostmourne == null) {
                return CollectionResult.valueOf(FRIEND_NOT_EXIST);
            }
            //这里frostmourneId需要强转为int，不然就算传一样的配置里已有的一样的id，也是会找不到。
            FrostmourneConfig frostmourneConfig = globalConfigService.findConfig(IdentiyKey.build((int) frostmourneId), FrostmourneConfig.class);
            if (frostmourneConfig == null) {
                LOGGER.error("FrostmourneConfig not found,id:{}", frostmourneId);
                return CollectionResult.valueOf(CONFIG_NOT_FOUND);
            }
            //分解获得的碎片奖励
            rewardList.add(RewardObject.valueOf(TypeProtocol.RewardType.GOODS_VALUE, frostmourneConfig.getFragmentId(), frostmourneConfig.getFragmentCount()));

            CollectionResult<RewardObject> levelRewardResult = this.relifeFrostmourneLevel(frostmourne);
            if (levelRewardResult.isFail()) {
                return levelRewardResult;
            }
            rewardList.addAll(levelRewardResult.item);
            CollectionResult<RewardObject> starRewardResult = this.relifeFrostmourneStar(frostmourne);
            if (starRewardResult.isFail()) {
                return starRewardResult;
            }
            rewardList.addAll(starRewardResult.item);
            //重生锻魂奖励
            rewardList.addAll(frostmourne.getForgeCostList());
        }
        if (rewardList.isEmpty()) {
            return CollectionResult.valueOf(FROSTMOURNE_NOT_REBIRTH);
        }
        return CollectionResult.collection(RewardHelper.groupByTypeAndId(rewardList));
    }

    /**
     * 获取重生神兵等级的奖励
     *
     * @param frostmourne
     * @return
     */
    private CollectionResult<RewardObject> relifeFrostmourneLevel(Frostmourne frostmourne) {
        Collection<RewardObject> rewardList = Lists.newArrayList();
        for (int i = 1; i < frostmourne.getLevel(); i++) {
            FrostmourneLevelConfig config = globalConfigService.findConfig(IdentiyKey.build(frostmourne.getConfigId(), i), FrostmourneLevelConfig.class);
            if (config == null) {
                LOGGER.error("FrostmourneLevelConfig not found,id:{},Level:{}", frostmourne.getConfigId(), i);
                return CollectionResult.valueOf(CONFIG_NOT_FOUND);
            }
            rewardList.addAll(config.getCostList());
        }
        return CollectionResult.collection(rewardList);
    }

    /**
     * 获取重生神兵星级奖励
     *
     * @param frostmourne
     * @return
     */
    private CollectionResult<RewardObject> relifeFrostmourneStar(Frostmourne frostmourne) {
        Collection<RewardObject> rewardList = Lists.newArrayList();
        for (int i = 0; i < frostmourne.getStarLevel(); i++) {
            FrostmourneStarConfig config = globalConfigService.findConfig(IdentiyKey.build(frostmourne.getConfigId(), i), FrostmourneStarConfig.class);
            if (config == null) {
                LOGGER.error("FrostmourneStarConfig not found,id:{},star:{}", frostmourne.getConfigId(), i);
                return CollectionResult.valueOf(CONFIG_NOT_FOUND);
            }
            rewardList.addAll(config.getCostList());
        }
        return CollectionResult.collection(rewardList);
    }

    @Override
    public TResult<CommonProtocol.RewardResult> frostmourneRebirth(long actorId, Collection<Long> configIds) {
        CollectionResult<RewardObject> collectionResult = frostmourneRebirthPreview(actorId, configIds);
        if (collectionResult.isFail()) {
            return TResult.valueOf(collectionResult.statusCode);
        }
        Collection<Integer> configIdList = Lists.newArrayList();
        for (long configId : configIds) {
            configIdList.add((int) configId);
        }
        frostmourneDao.deleteFrostmourne(actorId, configIdList, OperationType.FROSTMOURNE_REBIRTH);
        CommonProtocol.RewardResult rewardResult = RewardHelper.sendRewardList(actorId, collectionResult.item, OperationType.FROSTMOURNE_REBIRTH);
        CommonProtocol.IntListPacket.Builder builder = CommonProtocol.IntListPacket.newBuilder();
        builder.addAllList(configIdList);
        FrostmournePushHelper.pushFrostmourneDeleteList(actorId, builder.build());
        return TResult.sucess(rewardResult);
    }

    @Override
    public long getFrostmourneScore(long actorId, int frostmourneId) {
        Frostmourne frostmourne = frostmourneDao.getFrostmourne(actorId, frostmourneId);
        if (frostmourne == null) {
            return 0L;
        }
        FrostmourneConfig frostmourneConfig = globalConfigService.findConfig(IdentiyKey.build(frostmourneId), FrostmourneConfig.class);
        if (frostmourneConfig == null) {
            LOGGER.error("FrostmourneConfig not found,id:{}", frostmourneId);
            return 0L;
        }
        return FormulaUtils.executeRoundingLong(frostmourneConfig.getScoreExpr(), frostmourne.getLevel(), frostmourne.getStarLevel(), frostmourne.getForgeLevel());
    }

    @Override
    public TResult<CommonProtocol.RewardResultResponse> frostmourneLevelReset(long actorId, int frostmourneId) {
        Result unlock = ActorHelper.unlock(actorId, ActorUnlockType.FROSTMOURNE);
        if (unlock.isFail()) {
            return TResult.valueOf(unlock);
        }
        Frostmourne frostmourne = frostmourneDao.getFrostmourne(actorId, frostmourneId);
        if (frostmourne == null) {
            return TResult.valueOf(FROSTMOURNE_NOT_FOUND);
        }
        if (frostmourne.getLevel() <= 1) {
            return TResult.valueOf(INVALID_PARAM);
        }
        Collection<RewardObject> costRewards = globalConfigService.findGlobalObject(GlobalConfigKey.FROSTMOURNE_LEVEL_RESET_COST, RewardObjectListConfig.class).findValues();
        Result result = RewardHelper.decrease(actorId, costRewards, OperationType.FROSTMOURNE_LEVEL_RESET);
        if (result.isFail()) {
            return TResult.valueOf(result);
        }
        Collection<RewardObject> resultList = Lists.newArrayList();
        for (int i = 1; i < frostmourne.getLevel(); i++) {
            FrostmourneLevelConfig config = FrostmourneConfigService.getFrostmourneLevelConfig(frostmourneId, i);
            if (config == null) {
                return TResult.valueOf(CONFIG_ERROR);
            }
            resultList.addAll(config.getCostList());
        }
        frostmourne.setLevel(1);
        dbQueue.updateQueue(frostmourne);
        CommonProtocol.RewardResult rewardResult = RewardHelper.sendRewardList(actorId, resultList, OperationType.FROSTMOURNE_SYNTHESIZE);
        CommonProtocol.RewardResultResponse response = PbBuilder.buildRewardResultResponse(rewardResult);
        FrostmournePushHelper.pushFrostmourneInfo(actorId, frostmourne);
        this.refreshPower(actorId, frostmourneId);
        return TResult.sucess(response);
    }

    /**
     * 刷新战力
     *
     * @param actorId
     * @param configId
     */
    private void refreshPower(long actorId, int configId) {
        refreshPower(actorId, Lists.newArrayList(configId));
    }

    private void refreshPower(long actorId, Collection<Integer> configIds) {
        if (LineupHelper.frostmourneInLineup(actorId, configIds)) {
            DispatchHelper.postEvent(new LineupPowerRefreshEvent(actorId));
        }
    }
}
