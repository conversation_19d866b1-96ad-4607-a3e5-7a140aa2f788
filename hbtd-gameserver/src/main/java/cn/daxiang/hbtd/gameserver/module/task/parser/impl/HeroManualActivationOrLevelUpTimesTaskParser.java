package cn.daxiang.hbtd.gameserver.module.task.parser.impl;

import cn.daxiang.framework.event.ActorEvent;
import cn.daxiang.hbtd.gameserver.core.database.table.Task;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.TaskConfig;
import cn.daxiang.hbtd.gameserver.module.task.parser.AbstractTaskParser;
import cn.daxiang.hbtd.gameserver.module.task.type.TaskConditionType;
import cn.daxiang.protocol.game.TaskProtocol;
import org.springframework.stereotype.Component;

/**
 * 英雄图鉴激活或者升级次数任务
 *
 * <AUTHOR>
 */
@Component
public class HeroManualActivationOrLevelUpTimesTaskParser extends AbstractTaskParser<ActorEvent> {

    @Override
    public void init(Task task, TaskConfig taskConfig) {
        if (task.getValue() >= taskConfig.getValue()) {
            task.setStatus(TaskProtocol.TaskStatus.TASK_FINISH);
        }
    }

    @Override
    protected TaskConditionType getType() {
        return TaskConditionType.HERO_MANUAL_ACTIVATION_OR_LEVEL_UP_TIMES;
    }

    @Override
    protected boolean parseCondition(ActorEvent event, Task task, TaskConfig taskConfig) {
        task.setValue(task.getValue() + 1);
        if (task.getValue() >= taskConfig.getValue()) {
            task.setStatus(TaskProtocol.TaskStatus.TASK_FINISH);
        }
        return true;
    }

}
