package cn.daxiang.hbtd.gameserver.module.task.parser.impl;

import cn.daxiang.framework.result.CollectionResult;
import cn.daxiang.framework.utils.FormulaUtils;
import cn.daxiang.hbtd.gameserver.core.database.table.Task;
import cn.daxiang.hbtd.gameserver.core.database.table.Treasure;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.TaskConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.TreasureConfig;
import cn.daxiang.hbtd.gameserver.core.event.impl.TreasureAwakenEvent;
import cn.daxiang.hbtd.gameserver.module.task.parser.AbstractTaskParser;
import cn.daxiang.hbtd.gameserver.module.task.type.TaskConditionType;
import cn.daxiang.hbtd.gameserver.module.treasure.facade.TreasureFacade;
import cn.daxiang.protocol.game.TaskProtocol;
import cn.daxiang.shared.GlobalConfigKey;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class TreasureNodeLevelTimesTaskParser extends AbstractTaskParser<TreasureAwakenEvent> {

    @Autowired
    private TreasureFacade treasureFacade;

    @Override
    public void init(Task task, TaskConfig taskConfig) {
        task.setValue(0);
        parser(task, taskConfig);
    }

    private void parser(Task task, TaskConfig taskConfig) {
        int qualityLimit = globalConfigService.findGlobalConfig(GlobalConfigKey.TREASURE_SUIT_CONDITION).findInt();
        CollectionResult<Treasure> treasureList = treasureFacade.getTreasures(task.getActorId());
        int value = 0;
        for (Treasure treasure : treasureList.item) {
            TreasureConfig treasureConfig = globalConfigService.findConfig(treasure.getConfigId(), TreasureConfig.class);
            if (treasureConfig == null) {
                LOGGER.error("TreasureConfig not found,configId:{}", treasure.getConfigId());
                continue;
            }
            if (qualityLimit > treasureConfig.getQualityType().getId()) {
                continue;
            }
            if (!FormulaUtils.executeBool(taskConfig.getCondition(), treasure.getAwakenNodeLevel())) {
                continue;
            }
            value += 1;
            if (value >= taskConfig.getValue()) {
                break;
            }
        }
        if (task.getValue() < value) {
            task.setValue(value);
        }
        if (task.getValue() >= taskConfig.getValue()) {
            task.setStatus(TaskProtocol.TaskStatus.TASK_FINISH);
        }
    }

    @Override
    protected TaskConditionType getType() {
        return TaskConditionType.TREASURE_NODE_LEVEL_TIMES;
    }

    @Override
    protected boolean parseCondition(TreasureAwakenEvent event, Task task, TaskConfig taskConfig) {
        if (!FormulaUtils.executeBool(taskConfig.getCondition(), event.awakenNodeLevel)) {
            return false;
        }
        parser(task, taskConfig);
        return true;
    }
}