package cn.daxiang.hbtd.gameserver.module.arena.model.entity;

/**
 * 竞技场战报实体
 *
 * <AUTHOR>
 */
public class ArenaReportEntity {
    /**
     * 挑战者
     */
    private long challenge;
    /**
     * 对手
     */
    private long opponent;
    /**
     * 胜利者
     */
    private long winner;
    /**
     * 挑战时间
     */
    private long time;
    /**
     * 挑战者排名
     */
    private long challengeRank;
    /**
     * 对手排名
     */
    private long opponentRank;

    /**
     * 战斗回放Id
     */
    private long battleReplyId;

    public static ArenaReportEntity valueOf(long challenge, long opponent, long winner, long time, long challengeRank, long opponentRank, long battleReplyId) {
        ArenaReportEntity entity = new ArenaReportEntity();
        entity.challenge = challenge;
        entity.opponent = opponent;
        entity.winner = winner;
        entity.time = time;
        entity.challengeRank = challengeRank;
        entity.opponentRank = opponentRank;
        entity.battleReplyId = battleReplyId;
        return entity;
    }

    public long getChallenge() {
        return challenge;
    }

    public void setChallenge(long challenge) {
        this.challenge = challenge;
    }

    public long getOpponent() {
        return opponent;
    }

    public void setOpponent(long opponent) {
        this.opponent = opponent;
    }

    public long getWinner() {
        return winner;
    }

    public void setWinner(long winner) {
        this.winner = winner;
    }

    public long getTime() {
        return time;
    }

    public void setTime(long time) {
        this.time = time;
    }

    public long getChallengeRank() {
        return challengeRank;
    }

    public void setChallengeRank(long challengeRank) {
        this.challengeRank = challengeRank;
    }

    public long getBattleReplyId() {
        return battleReplyId;
    }

    public void setBattleReplyId(long battleReplyId) {
        this.battleReplyId = battleReplyId;
    }

    public long getOpponentRank() {
        return opponentRank;
    }

    public void setOpponentRank(long opponentRank) {
        this.opponentRank = opponentRank;
    }
}
