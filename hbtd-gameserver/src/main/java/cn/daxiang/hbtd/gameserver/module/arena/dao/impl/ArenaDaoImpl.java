package cn.daxiang.hbtd.gameserver.module.arena.dao.impl;

import cn.daxiang.framework.database.SingleEntity;
import cn.daxiang.framework.database.dao.SingleEntityDaoImpl;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.framework.utils.StopWatch;
import cn.daxiang.framework.utils.rank.cache.ILineRankCache;
import cn.daxiang.hbtd.gameserver.core.GameConfig;
import cn.daxiang.hbtd.gameserver.core.database.GameInit;
import cn.daxiang.hbtd.gameserver.core.database.IdGenerator;
import cn.daxiang.hbtd.gameserver.core.database.InitIndex;
import cn.daxiang.hbtd.gameserver.core.database.table.Arena;
import cn.daxiang.hbtd.gameserver.module.arena.dao.ArenaDao;
import cn.daxiang.hbtd.gameserver.module.arena.model.ArenaRank;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;

@Component
public class ArenaDaoImpl extends SingleEntityDaoImpl implements ArenaDao, GameInit {
    private final static int RANK_LIMIT = 100;
    private ILineRankCache<Long, ArenaRank> LINE_RANK_CACHE = new ILineRankCache<>(this, RANK_LIMIT);

    private Map<Long, ArenaRank> ARENA_RANK_MAP = Maps.newConcurrentMap();

    @Autowired
    private IdGenerator idGenerator;

    @Override
    public Arena getArena(long actorId) {
        Arena table = this.get(IdentiyKey.build(actorId));
        if (table.newEntity()) {
            table.setNewEntity(false);
            table.reset();
            if (actorId != GameConfig.getServerId()) {
                table.setRank(this.findLastRankIncrement());
                this.addRank(ArenaRank.valueOf(actorId, table.getRank()));
            }
            this.updateQueue(table);
        }
        return table;
    }

    @Override
    protected Class<? extends SingleEntity<?>> forClass() {
        return Arena.class;
    }

    @Override
    protected void initMaxId() {
        Long maxId = jdbc.queryForObject("select max(rank) from arena", Long.class);
        AtomicLong base;
        if (maxId == null || maxId <= 0) {
            base = new AtomicLong(0);

        } else {
            base = new AtomicLong(maxId);
        }
        idGenerator.init(IdentiyKey.build(this.getClass()), base);
    }

    @Override
    public void gameInit() {
        StopWatch sw = new StopWatch(true);
        int pageIndex = 0;
        while (true) {
            int limitBegin = pageIndex * jdbc.PAGE_SIZE;
            int limitEnd = jdbc.PAGE_SIZE;
            String sql = "select actorId,rank from arena limit ?";
            LinkedHashMap<String, Object> paramMaps = new LinkedHashMap<>();
            if (limitBegin > 0) {
                sql += " , ?";
                paramMaps.put("limitBegin", limitBegin);
            }
            paramMaps.put("limitEnd", limitEnd);
            List<ArenaRank> list = jdbc.query(sql, paramMaps.values().toArray(), new ArenaRank());
            if (list.isEmpty()) {
                break;
            }
            for (ArenaRank arenaRank : list) {
                if (arenaRank.getActorId() == GameConfig.getServerId()) {
                    continue;
                }
                this.addRank(arenaRank);
            }
            pageIndex++;
        }
        // 初始化服务器公用Arena
        this.getArena(GameConfig.getServerId());
        sw.stop();
        LOGGER.info("arena rank loading complete! total:[{}] record. time:{}ms", LINE_RANK_CACHE.findSize(), sw.runTime());
    }

    @Override
    public long getLastRank() {
        return idGenerator.get(IdentiyKey.build(this.getClass()));
    }

    public long findLastRankIncrement() {
        return idGenerator.increment(IdentiyKey.build(this.getClass()));
    }

    @Override
    public InitIndex index() {
        return InitIndex.INTI_FOUR;
    }

    @Override
    public ArenaRank findLineRank(Object key) {
        IdentiyKey idKey = IdentiyKey.build(key);
        if (exsit(idKey, Arena.class)) {
            return ARENA_RANK_MAP.get(idKey.getFirstLongId());
        }
        return null;
    }

    @Override
    public void addRank(ArenaRank rank) {
        LINE_RANK_CACHE.put(rank);
        ARENA_RANK_MAP.put(rank.getKey(), rank);
    }

    @Override
    public ArenaRank getArenaRank(long rank) {
        return LINE_RANK_CACHE.findByRank(rank);
    }

    @Override
    public long getArenaRankByActorId(long actorId) {
        IdentiyKey key = IdentiyKey.build(actorId);
        if (this.exsit(key, Arena.class)) {
            Arena table = this.get(key);
            return table.getRank();
        }
        return 0;
    }

    @Override
    public void swapRank(long challengeRank, long opponentRank) {
        ArenaRank challenge = this.getArenaRank(challengeRank);
        ArenaRank opponent = this.getArenaRank(opponentRank);
        LINE_RANK_CACHE.rankChange(challenge, opponent);
    }

    @Override
    public Map<Long, Long> getArenaRankMap() {
        return LINE_RANK_CACHE.getCopyCacheMap();
    }

    @Override
    public int getArenaRankPages() {
        return LINE_RANK_CACHE.findPages();
    }

    @Override
    public Collection<ArenaRank> getArenaRanks(int page) {
        return LINE_RANK_CACHE.findRanks(page);
    }
}
