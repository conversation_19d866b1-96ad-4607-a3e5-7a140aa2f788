package cn.daxiang.hbtd.gameserver.module.teamTd.facade;

import cn.daxiang.framework.result.CollectionResult;
import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.protocol.game.TeamtdProtocol.TeamTdBattleStartResponse;
import cn.daxiang.shared.module.teamTd.model.TeamTd;
import cn.daxiang.shared.module.teamTd.type.TeamTdJoinLimitType;
import cn.daxiang.shared.module.teamTd.type.TeamTdType;

/**
 * <AUTHOR>
 * @date 2020/12/25
 */
public interface TeamTdFacade {

    /**
     * 获取队伍信息
     *
     * @param actorId
     * @return
     */
    TResult<TeamTd> getTeam(long actorId);

    /**
     * 获取队伍列表
     *
     * @param actorId
     * @param type
     * @param typeValue
     * @return
     */
    CollectionResult<TeamTd> getTeamList(long actorId, TeamTdType type, int typeValue);

    /**
     * 创建口令
     *
     * @param actorId
     * @param type
     * @return
     */
    TResult<Integer> createCode(long actorId, TeamTdType type);

    /**
     * 取消口令
     *
     * @param actorId
     * @return
     */
    Result cancelCode(long actorId);

    /**
     * 创建队伍
     *
     * @param actorId
     * @param type
     * @param typeValue
     * @param limitType
     * @param limitValue
     * @param spectate
     * @return
     */
    Result createTeam(long actorId, TeamTdType type, int typeValue, TeamTdJoinLimitType limitType, long limitValue, boolean spectate);

    /**
     * 加入队伍
     *
     * @param actorId
     * @param teamId
     * @return
     */
    Result joinTeam(long actorId, long teamId);

    /**
     * 口令队伍
     *
     * @param actorId
     * @param type
     * @param code
     * @return
     */
    Result codeTeam(long actorId, TeamTdType type, int code);

    /**
     * 退出队伍
     *
     * @param actorId
     * @return
     */
    Result exitTeam(long actorId);

    /**
     * 踢出队伍
     *
     * @param actorId
     * @return
     */
    Result kickTeam(long actorId);

    /**
     * 准备
     *
     * @param actorId
     * @param ready
     * @return
     */
    Result readyTeam(long actorId, boolean ready);

    /**
     * 刷新运行环境分数
     *
     * @param actorId
     * @param score
     * @return
     */
    Result refreshREScore(long actorId, int score);

    /**
     * 取消监听队伍
     *
     * @param actorId
     * @param type
     * @param typeValue
     * @return
     */
    Result cancelListenTeam(long actorId, TeamTdType type, int typeValue);

    /**
     * 开始战斗
     *
     * @param actorId
     * @return
     */
    Result startBattle(long actorId);

    /**
     * 观战
     *
     * @param actorId
     * @param teamId
     * @return
     */
    TResult<TeamTdBattleStartResponse> spectate(long actorId, long teamId);
}
