package cn.daxiang.hbtd.gameserver.module.relic.model;

import cn.daxiang.hbtd.gameserver.core.dataconfig.model.RelicConfig;

/**
 * 上阵神器信息
 *
 * @author: <PERSON>
 * @date: 2023/1/31 14:18
 * @Description:
 */
public class RelicInfo {
    /**
     * 位置
     * {@link RelicConfig#getRank()}
     */
    private int index;

    /**
     * 强化等级
     */
    private int level;

    /**
     * 强化经验
     */
    private int exp;

    /**
     * 升星等级
     */
    private int star;

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public int getStar() {
        return star;
    }

    public void setStar(int star) {
        this.star = star;
    }

    public int getExp() {
        return exp;
    }

    public void setExp(int exp) {
        this.exp = exp;
    }

    public static RelicInfo valueOf(int index) {
        RelicInfo model = new RelicInfo();
        model.index = index;
        model.level = 1;
        model.exp = 0;
        model.star = 0;
        return model;
    }

    public void levelUp(int level, int exp) {
        this.level = level;
        this.exp = exp;
    }

    public void starUp() {
        this.star++;
    }
}
