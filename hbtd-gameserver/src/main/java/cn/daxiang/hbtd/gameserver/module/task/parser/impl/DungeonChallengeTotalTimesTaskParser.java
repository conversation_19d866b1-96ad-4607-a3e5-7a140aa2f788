package cn.daxiang.hbtd.gameserver.module.task.parser.impl;

import cn.daxiang.framework.result.TResult;
import cn.daxiang.hbtd.gameserver.core.database.table.Dungeon;
import cn.daxiang.hbtd.gameserver.core.database.table.Task;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.TaskConfig;
import cn.daxiang.hbtd.gameserver.core.event.impl.DungeonChallengeEvent;
import cn.daxiang.hbtd.gameserver.module.dungeon.facade.DungeonFacade;
import cn.daxiang.hbtd.gameserver.module.task.parser.AbstractTaskParser;
import cn.daxiang.hbtd.gameserver.module.task.type.TaskConditionType;
import cn.daxiang.hbtd.gameserver.module.task.type.TaskType;
import cn.daxiang.protocol.game.DungeonProtocol;
import cn.daxiang.protocol.game.TaskProtocol;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class DungeonChallengeTotalTimesTaskParser extends AbstractTaskParser<DungeonChallengeEvent> {
    @Autowired
    private DungeonFacade dungeonFacade;

    public static void main(String[] args) {
        Integer type = Integer.valueOf("1");
        System.out.println(DungeonProtocol.DungeonType.values());
        for (DungeonProtocol.DungeonType dungeonType : DungeonProtocol.DungeonType.values()) {
            if (dungeonType == DungeonProtocol.DungeonType.UNRECOGNIZED || dungeonType == DungeonProtocol.DungeonType.DUNGEON_TYPE_NONE) {
                continue;
            }
            System.out.println(type.equals(dungeonType.getNumber()));
            if (type == 0 || type.equals(dungeonType.getNumber())) {

            }
        }

    }

    @Override
    public void init(Task task, TaskConfig taskConfig) {
        if (task.getType() == TaskType.DAILY || task.getType() == TaskType.WEEKLY) {
            return;
        }
        Integer type = Integer.valueOf(taskConfig.getCondition());
        long value = 0;
        for (DungeonProtocol.DungeonType dungeonType : DungeonProtocol.DungeonType.values()) {
            if (dungeonType == DungeonProtocol.DungeonType.UNRECOGNIZED || dungeonType == DungeonProtocol.DungeonType.DUNGEON_TYPE_NONE) {
                continue;
            }
            if (type == 0 || type.equals(dungeonType.getNumber())) {
                TResult<Dungeon> dungeonResult = dungeonFacade.getDungeon(task.getActorId(), dungeonType);
                if (dungeonResult.isFail()) {
                    continue;
                }
                value += dungeonResult.item.getChallengeTotalTimes();
            }
        }
        task.setValue(value);
        if (task.getValue() >= taskConfig.getValue()) {
            task.setStatus(TaskProtocol.TaskStatus.TASK_FINISH);
        }

    }

    @Override
    protected TaskConditionType getType() {
        return TaskConditionType.DUNGEON_CHALLENGE;
    }

    @Override
    protected boolean parseCondition(DungeonChallengeEvent event, Task task, TaskConfig taskConfig) {
        int dungeonType = Integer.parseInt(taskConfig.getCondition());
        if (dungeonType != 0 && event.type.getNumber() != dungeonType) {
            return false;
        }
        task.setValue(task.getValue() + event.times);
        if (task.getValue() >= taskConfig.getValue()) {
            task.setStatus(TaskProtocol.TaskStatus.TASK_FINISH);
        }
        return true;
    }
}
