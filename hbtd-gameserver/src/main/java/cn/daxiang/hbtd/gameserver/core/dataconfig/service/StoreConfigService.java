package cn.daxiang.hbtd.gameserver.core.dataconfig.service;

import cn.daxiang.framework.dataconfig.ConfigServiceAdapter;
import cn.daxiang.framework.utils.RandomUtils;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.StoreGoodsConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.StoreRefreshConfig;
import cn.daxiang.protocol.game.StoreProtocol.StoreType;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;
import java.util.TreeMap;

/**
 * <AUTHOR>
 */
@Component
public class StoreConfigService extends ConfigServiceAdapter {
    /**
     * key:StoreType,value:{key:groupId,value:{key:level,value:{key:productId,value:weight}}}
     */
    private static Map<StoreType, TreeMap<Integer, TreeMap<Integer, Map<Integer, Integer>>>> STORE_LEVEL_RANDOM_MAP = Maps.newHashMap();
    /**
     * key:StoreType,value:StoreRefreshConfig}
     */
    private static Map<StoreType, StoreRefreshConfig> STORE_REFRESH_CONFIG_MAP = Maps.newHashMap();

    /**
     * 获取随机商品ID列表
     *
     * @param storeType
     * @param level
     * @param countMap
     * @return
     */
    public static Collection<Integer> getRandomProductIds(StoreType storeType, int level, TreeMap<Integer, Integer> countMap) {
        TreeMap<Integer, TreeMap<Integer, Map<Integer, Integer>>> typeMap = STORE_LEVEL_RANDOM_MAP.get(storeType);
        if (typeMap == null) {
            return Lists.newArrayList();
        }
        Collection<Integer> productIds = Lists.newArrayList();
        for (Integer groupId : countMap.keySet()) {
            TreeMap<Integer, Map<Integer, Integer>> groupMap = typeMap.get(groupId);
            Integer floorKey = groupMap.floorKey(level);
            if (floorKey == null) {
                continue;
            }
            Map<Integer, Integer> levelMap = groupMap.get(floorKey);
            int count = countMap.get(groupId);
            if (count == 0) {
                productIds.addAll(levelMap.keySet());
            }
            if (levelMap.size() < count) {
                count = levelMap.size();
            }
            Map<Integer, Integer> randomMap = Maps.newHashMap(levelMap);
            for (int i = 0; i < count; i++) {
                Integer productId = RandomUtils.randomByWeight(randomMap);
                randomMap.remove(productId);
                productIds.add(productId);
            }
        }
        return productIds;
    }

    /**
     * 获取商店刷新配置
     *
     * @param storeType
     * @return
     */
    public static StoreRefreshConfig getStoreRefreshConfig(StoreType storeType) {
        return STORE_REFRESH_CONFIG_MAP.get(storeType);
    }

    @Override
    protected void initialize() {
        Collection<StoreGoodsConfig> configList = dataConfig.listAll(this, StoreGoodsConfig.class);
        for (StoreGoodsConfig storeGoodsConfig : configList) {
            TreeMap<Integer, TreeMap<Integer, Map<Integer, Integer>>> typeMap = STORE_LEVEL_RANDOM_MAP.get(storeGoodsConfig.getStoreType());
            if (typeMap == null) {
                typeMap = Maps.newTreeMap();
                STORE_LEVEL_RANDOM_MAP.put(storeGoodsConfig.getStoreType(), typeMap);
            }
            TreeMap<Integer, Map<Integer, Integer>> groupMap = typeMap.get(storeGoodsConfig.getGroupId());
            if (groupMap == null) {
                groupMap = Maps.newTreeMap();
                typeMap.put(storeGoodsConfig.getGroupId(), groupMap);
            }
            Map<Integer, Integer> levelMap = groupMap.get(storeGoodsConfig.getLevelLimit());
            if (levelMap == null) {
                levelMap = Maps.newHashMap();
                groupMap.put(storeGoodsConfig.getLevelLimit(), levelMap);
            }
            levelMap.put(storeGoodsConfig.getproductId(), storeGoodsConfig.getWeight());
        }
        Collection<StoreRefreshConfig> refreshConfigList = dataConfig.listAll(this, StoreRefreshConfig.class);
        for (StoreRefreshConfig storeRefreshConfig : refreshConfigList) {
            STORE_REFRESH_CONFIG_MAP.put(storeRefreshConfig.getStoreType(), storeRefreshConfig);
        }
    }

    @Override
    protected void clean() {
        STORE_LEVEL_RANDOM_MAP.clear();
        STORE_REFRESH_CONFIG_MAP.clear();
    }
}
