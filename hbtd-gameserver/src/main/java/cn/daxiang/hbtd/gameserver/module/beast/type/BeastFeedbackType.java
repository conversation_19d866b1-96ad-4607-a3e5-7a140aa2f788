package cn.daxiang.hbtd.gameserver.module.beast.type;

/**
 * 神兽召唤回馈奖励类型
 *
 * @author: <PERSON>
 * @date: 2023/6/2 10:36
 * @Description:
 */
public enum BeastFeedbackType {
    /**
     * 1.积分
     */
    SCORE(1),

    /**
     * 2.厄运
     */
    BAD_LUCKY(2),

    /**
     * 3.最高幸运积分
     */
    HIGHEST_LUCKY(3),

    /**
     * 4.最低幸运积分
     */
    LOWEST_LUCKY(4),

    /**
     * NONE
     */
    NONE(0);
    private final int type;

    BeastFeedbackType(int type) {
        this.type = type;
    }

    public static BeastFeedbackType getType(int type) {
        for (BeastFeedbackType feedbackType : BeastFeedbackType.values()) {
            if (feedbackType.getType() == type) {
                return feedbackType;
            }
        }
        return NONE;
    }

    public int getType() {
        return type;
    }
}
