package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.dataconfig.annotation.FieldIgnore;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.hbtd.gameserver.module.battle.type.SkillTriggerType;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 归葬秘境英雄表
 *
 * <AUTHOR>
 * @date 2023/8/29
 */
@DataFile(fileName = "sanctuary_hero_config")
public class SanctuaryHeroConfig implements ModelAdapter {
    /**
     * 英雄Id
     */
    private int roleId;
    /**
     * 普攻
     */
    private int commonAttack;
    /**
     * 被动技能
     */
    private int passiveSkill;
    /**
     * 大招
     */
    private int skill;
    /**
     * 觉醒技能
     */
    private int awakeSkill;
    /**
     * 怪物数值Id来源于怪物数值表-monster_capability_config
     */
    private int capabilityId;
    /**
     * 玩家英雄专武星级大招技能
     * [技能Id,技能Id,技能Id]
     */
    private String immortalsSkill;
    /**
     * 玩家英雄专武觉醒技
     * [[专武星级,技能Id],[专武星级,技能Id],[专武星级,技能Id]]
     */
    private String immortalsAwakeSkill;
    /**
     * 回合战-技能map
     */
    @FieldIgnore
    private Map<Integer, Collection<Integer>> skillMap = Maps.newHashMap();
    /**
     * 不为空,则对应星级替换技能
     * {starLevel=skillId, starLevel=skillId, starLevel=skillId}
     */
    @FieldIgnore
    private Map<Integer, Integer> immortalsSkillMap = Maps.newHashMap();
    /**
     * 不为空,则对应星级替换技能
     * {starLevel=skillId, starLevel=skillId, starLevel=skillId}
     */
    @FieldIgnore
    private Map<Integer, Integer> immortalsAwakeSkillMap = Maps.newHashMap();

    @Override
    public void initialize() {
        skillMap.put(SkillTriggerType.NORMAL_ATTACK.getId(), Lists.newArrayList(commonAttack));
        skillMap.put(SkillTriggerType.CAST.getId(), Lists.newArrayList(skill));
        List<Integer> immortalsSkillList = JSONArray.parseArray(immortalsSkill, Integer.class);
        for (int i = 0; i < immortalsSkillList.size(); i++) {
            immortalsSkillMap.put(i, immortalsSkillList.get(i));
        }
        List<Integer> immortalsAwakeSkillList = JSONArray.parseArray(immortalsAwakeSkill, Integer.class);
        for (int i = 0; i < immortalsAwakeSkillList.size(); i++) {
            immortalsAwakeSkillMap.put(i, immortalsAwakeSkillList.get(i));
        }
    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(roleId);
    }

    public int getRoleId() {
        return roleId;
    }

    public int getCommonAttack() {
        return commonAttack;
    }

    public int getPassiveSkill() {
        return passiveSkill;
    }

    public int getSkill() {
        return skill;
    }

    public int getAwakeSkill() {
        return awakeSkill;
    }

    public int getCapabilityId() {
        return capabilityId;
    }

    public Map<Integer, Collection<Integer>> getSkillMap() {
        return skillMap;
    }

    public Map<Integer, Integer> getImmortalsSkillMap() {
        return immortalsSkillMap;
    }

    public Map<Integer, Integer> getImmortalsAwakeSkillMap() {
        return immortalsAwakeSkillMap;
    }
}
