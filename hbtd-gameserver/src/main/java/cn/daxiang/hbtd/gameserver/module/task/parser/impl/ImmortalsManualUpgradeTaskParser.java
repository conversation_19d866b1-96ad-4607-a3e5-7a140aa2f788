package cn.daxiang.hbtd.gameserver.module.task.parser.impl;

import cn.daxiang.framework.event.ActorEvent;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.framework.utils.FormulaUtils;
import cn.daxiang.hbtd.gameserver.core.database.table.ImmortalsManual;
import cn.daxiang.hbtd.gameserver.core.database.table.Task;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.ImmortalsManualConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.TaskConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.ImmortalsManualConfigService;
import cn.daxiang.hbtd.gameserver.module.immortalsManual.facade.ImmortalsManualFacade;
import cn.daxiang.hbtd.gameserver.module.task.parser.AbstractTaskParser;
import cn.daxiang.hbtd.gameserver.module.task.type.TaskConditionType;
import cn.daxiang.protocol.game.TaskProtocol;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/7/14
 */
@Component
public class ImmortalsManualUpgradeTaskParser extends AbstractTaskParser<ActorEvent> {
    @Autowired
    private ImmortalsManualFacade immortalsManualFacade;

    @Override
    public void init(Task task, TaskConfig taskConfig) {
        task.setValue(0);
        parser(task, taskConfig);
    }

    private void parser(Task task, TaskConfig taskConfig) {
        TResult<ImmortalsManual> result = immortalsManualFacade.getImmortalsManual(task.getActorId());
        if (result.isFail()) {
            return;
        }
        ImmortalsManual immortalsManual = result.item;
        int value = 0;
        for (Map.Entry<Integer, Integer> entry : immortalsManual.getManualsMap().entrySet()) {
            ImmortalsManualConfig config = ImmortalsManualConfigService.getImmortalsManualConfig(entry.getKey());
            if (config == null) {
                continue;
            }
            if (!FormulaUtils.executeBool(taskConfig.getCondition(), config.getQuality(), entry.getValue())) {
                continue;
            }
            value += 1;
            if (value >= taskConfig.getValue()) {
                break;
            }
        }
        if (task.getValue() < value) {
            task.setValue(value);
        }
        if (task.getValue() >= taskConfig.getValue()) {
            task.setStatus(TaskProtocol.TaskStatus.TASK_FINISH);
        }
    }

    @Override
    protected TaskConditionType getType() {
        return TaskConditionType.IMMORTALS_MANUAL_UPGRADE;
    }

    @Override
    protected boolean parseCondition(ActorEvent event, Task task, TaskConfig taskConfig) {
        parser(task, taskConfig);
        return true;
    }

}
