package cn.daxiang.hbtd.gameserver.module.goods.parser.reward;

import cn.daxiang.protocol.game.TypeProtocol.RewardType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.PostConstruct;

public abstract class AbstractRewardParser implements RewardParser {

    protected final Logger LOGGER = LoggerFactory.getLogger(getClass());

    @Autowired
    private RewardContext context;

    @PostConstruct
    private void initialize() {
        context.register(getType(), this);
    }

    protected abstract RewardType getType();
}
