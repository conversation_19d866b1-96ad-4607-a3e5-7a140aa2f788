package cn.daxiang.hbtd.gameserver.core.event.impl;

import cn.daxiang.framework.event.ActorEvent;
import cn.daxiang.hbtd.gameserver.core.oss.type.OperationType;
import cn.daxiang.shared.event.EventKey;

import java.util.Collection;

/**
 * 删除物品事件
 *
 * <AUTHOR>
 */
public class GoodsDeleteEvent extends ActorEvent {

    /**
     * 物品配置ID列表
     */
    public Collection<Integer> goodsIdList;
    /**
     * 操作类型
     */
    public OperationType operationType;

    public GoodsDeleteEvent(long actorId, Collection<Integer> goodsIdList, OperationType operationType) {
        super(EventKey.GOODS_DELETE_EVENT, actorId);
        this.goodsIdList = goodsIdList;
        this.operationType = operationType;
    }
}
