package cn.daxiang.hbtd.gameserver.core.event.impl;

import cn.daxiang.framework.event.ActorEvent;
import cn.daxiang.shared.event.EventKey;

/**
 * 红颜激活,创建事件
 *
 * @Author: <PERSON>
 * @Date 2022-11-09 15:47
 * @Description:
 */
public class BeautyActivationEvent extends ActorEvent {
    /**
     * 红颜Id
     */
    public int configId;

    public BeautyActivationEvent(long actorId, int configId) {
        super(EventKey.BEAUTY_ACTIVATION_EVENT, actorId);
        this.configId = configId;
    }
}
