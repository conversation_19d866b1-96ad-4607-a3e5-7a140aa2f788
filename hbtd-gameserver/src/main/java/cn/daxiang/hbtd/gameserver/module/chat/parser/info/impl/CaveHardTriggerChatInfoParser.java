package cn.daxiang.hbtd.gameserver.module.chat.parser.info.impl;

import cn.daxiang.framework.event.GameEvent;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.SystemMsgConfig;
import cn.daxiang.hbtd.gameserver.core.event.impl.CaveHardTriggerEvent;
import cn.daxiang.hbtd.gameserver.module.chat.helper.ChatHelper;
import cn.daxiang.hbtd.gameserver.module.chat.model.ChatInfoReceiver;
import cn.daxiang.hbtd.gameserver.module.chat.parser.info.AbstractChatInfoParser;
import cn.daxiang.protocol.game.ChatInfoProtocol.CaveHardTriggerChatInfo;
import cn.daxiang.protocol.game.ChatProtocol;
import cn.daxiang.protocol.game.TypeProtocol;
import cn.daxiang.shared.event.EventKey;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Component
public class CaveHardTriggerChatInfoParser extends AbstractChatInfoParser {

    @Override
    protected List<String> getEventName() {
        return Lists.newArrayList(EventKey.CAVE_HARD_TRIGGER_EVENT);
    }

    @Override
    public Map<ChatInfoReceiver, ChatProtocol.ChatInfo> parse(GameEvent e) {
        CaveHardTriggerEvent event = e.convert();
        SystemMsgConfig config = globalConfigService.findConfig(IdentiyKey.build(TypeProtocol.ChatInfoType.CAVE_HARD_TRIGGER_CHAT_INFO_VALUE), SystemMsgConfig.class);
        if (config == null) {
            return Collections.emptyMap();
        }

        if (config == null || !config.execute(event.triggerType)) {
            return Collections.emptyMap();
        }

        Map<ChatInfoReceiver, ChatProtocol.ChatInfo> chatMap = Maps.newHashMap();
        CaveHardTriggerChatInfo arenaTopRankLoginChatInfo = CaveHardTriggerChatInfo.newBuilder().setTriggerType(event.triggerType).build();

        Collection<Long> actorIds = Lists.newLinkedList();
        actorIds.add(event.getActorId());
        ChatProtocol.ChatInfo chatInfo =
            ChatHelper.buildChatInfo(TypeProtocol.ChatInfoType.CAVE_HARD_TRIGGER_CHAT_INFO, event.getActorId(), arenaTopRankLoginChatInfo.toByteArray(), actorIds);
        chatMap.put(ChatInfoReceiver.valueOf(TypeProtocol.ChatChannelType.CHAT_CHANNEL_TYPE_SYSTEM), chatInfo);
        return chatMap;
    }
}
