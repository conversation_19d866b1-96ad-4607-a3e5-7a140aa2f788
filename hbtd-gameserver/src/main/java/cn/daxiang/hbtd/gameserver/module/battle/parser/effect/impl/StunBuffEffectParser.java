package cn.daxiang.hbtd.gameserver.module.battle.parser.effect.impl;

import cn.daxiang.hbtd.gameserver.core.dataconfig.model.SkillConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.SkillEffectConfig;
import cn.daxiang.hbtd.gameserver.module.battle.helper.FightHelper;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleRoom;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleSprite;
import cn.daxiang.hbtd.gameserver.module.battle.model.FightEffect;
import cn.daxiang.hbtd.gameserver.module.battle.model.FightReport;
import cn.daxiang.hbtd.gameserver.module.battle.parser.effect.AbstractSkillEffectParser;
import cn.daxiang.hbtd.gameserver.module.battle.type.SkillEffectType;
import cn.daxiang.hbtd.gameserver.module.battle.type.SkillTriggerType;
import org.springframework.stereotype.Component;

@Component
public class StunBuffEffectParser extends AbstractSkillEffectParser {
    @Override
    protected SkillEffectType getType() {
        return SkillEffectType.STUN_BUFF_EFFECT;
    }

    @Override
    public boolean execute(FightReport report, BattleSprite attacker, BattleSprite targeter, SkillConfig skillConfig, SkillEffectConfig effectConfig, BattleRoom battleRoom) {
        if (targeter.getSpriteBattle().isBoss()) {
            report.addFightEffect(FightEffect.immune(targeter, effectConfig.getEffectId()));
            return true;
        }
        if (targeter.hasBuffType(SkillEffectType.IMMUNE_CONTROL_BUFF_EFFECT)) {
            report.addFightEffect(FightEffect.immune(targeter, effectConfig.getEffectId()));
        } else {
            this.addBattleBuff(report, attacker, targeter, skillConfig, effectConfig, battleRoom);
            FightHelper.processSkill(battleRoom, targeter, null, null, 0L, null, SkillTriggerType.STUN_BUFF_TRIGGER, null);
        }
        return true;
    }
}