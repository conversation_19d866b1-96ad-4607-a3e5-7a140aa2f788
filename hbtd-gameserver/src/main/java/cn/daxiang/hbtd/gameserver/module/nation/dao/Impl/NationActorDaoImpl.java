package cn.daxiang.hbtd.gameserver.module.nation.dao.Impl;

import cn.daxiang.framework.database.SingleEntity;
import cn.daxiang.framework.database.dao.SingleEntityDaoImpl;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.hbtd.gameserver.core.database.table.NationActor;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.GlobalConfigService;
import cn.daxiang.hbtd.gameserver.module.nation.dao.NationActorDao;
import cn.daxiang.shared.GlobalConfigKey;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;

/**
 * <AUTHOR>
 */
@Component
public class NationActorDaoImpl extends SingleEntityDaoImpl implements NationActorDao {
    @Autowired
    private GlobalConfigService globalConfigService;

    @Override
    public NationActor getNationActor(long actorId) {
        NationActor table = this.get(IdentiyKey.build(actorId));
        if (table.newEntity()) {
            table.setNewEntity(false);
            int recoverLimit = globalConfigService.findGlobalConfig(GlobalConfigKey.NATION_CRUSADE_TIMES_RECOVER).findInt();
            int storeLimit = globalConfigService.findGlobalConfig(GlobalConfigKey.NATION_CRUSADE_TIMES_MAX).findInt();
            table.reset(recoverLimit, storeLimit);
            this.updateQueue(table);
        }
        return table;
    }

    @Override
    public Collection<Long> getNationKingActor() {
        return jdbc.queryForList("SELECT actorId FROM nation_actor where nationId !=0 and type = 1", Long.class);
    }

    @Override
    protected Class<? extends SingleEntity<?>> forClass() {
        return NationActor.class;
    }

    @Override
    protected void initMaxId() {
    }
}
