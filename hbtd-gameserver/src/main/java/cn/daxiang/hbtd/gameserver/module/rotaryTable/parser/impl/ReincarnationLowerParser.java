package cn.daxiang.hbtd.gameserver.module.rotaryTable.parser.impl;

import cn.daxiang.hbtd.gameserver.core.oss.type.OperationType;
import cn.daxiang.hbtd.gameserver.module.rotaryTable.parser.AbstractRotaryTableParser;
import cn.daxiang.protocol.game.RotaryTableProtocol.RotaryTableType;
import org.springframework.stereotype.Component;

/**
 * 轮回战场低级转盘解析器
 *
 * <AUTHOR>
 * @date 2023/12/22
 */
@Component
public class ReincarnationLowerParser extends AbstractRotaryTableParser {
    @Override
    protected RotaryTableType getType() {
        return RotaryTableType.ROTARY_TABLE_REINCARNATION_LOWER;
    }

    @Override
    public OperationType getOperationType() {
        return OperationType.REINCARNATION_LOWER_ROTARY_TABLE;
    }
}