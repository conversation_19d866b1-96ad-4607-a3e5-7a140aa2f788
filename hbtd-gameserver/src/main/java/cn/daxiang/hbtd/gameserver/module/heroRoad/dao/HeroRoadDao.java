package cn.daxiang.hbtd.gameserver.module.heroRoad.dao;

import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.hbtd.gameserver.core.database.table.HeroRoad;
import cn.daxiang.hbtd.gameserver.module.heroRoad.model.HeroRoadRank;
import cn.daxiang.shared.module.heroRoad.HeroRoadLineupInfo;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/18
 */
public interface HeroRoadDao {
    /**
     * 获取英雄之路信息
     *
     * @param actorId
     * @return
     */
    HeroRoad getHeroRoad(long actorId);

    /**
     * 加入本服排行
     *
     * @param actorId
     * @param storyId
     * @param difficultyLevel
     * @param official
     * @param rewardTimes
     * @param lineup
     */
    void achieveHeroRoad(long actorId, int storyId, int difficultyLevel, int official, int rewardTimes, Collection<Collection<HeroRoadLineupInfo>> lineup);

    /**
     * 获取阵容信息排行
     *
     * @param actorId
     * @param storyId
     * @param difficultyLevel
     * @return
     */
    List<HeroRoadRank> getHeroRoadRankList(long actorId, int storyId, int difficultyLevel);

    /**
     * 获取阵容信息
     *
     * @param actorId
     * @param storyId
     * @param difficultyLevel
     * @return
     */
    TResult<HeroRoadRank> getHeroRoadRank(long actorId, int storyId, int difficultyLevel);

}
