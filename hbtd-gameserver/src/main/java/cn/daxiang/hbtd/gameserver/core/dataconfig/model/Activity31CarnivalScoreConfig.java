package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.dataconfig.annotation.FieldIgnore;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.shared.reward.RewardObject;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;

import java.util.Collection;

/**
 * @author: <PERSON>
 * @date: 2023/2/7 9:30
 * @Description:
 */
@DataFile(fileName = "activity_31_carnival_score_config")
public class Activity31CarnivalScoreConfig implements ModelAdapter {

    /**
     * data
     */
    private int data;
    /**
     * 分数
     */
    private int score;
    /**
     * 所需物品
     */
    private String goods;
    /**
     * 奖励列表
     */
    private String rewards;
    /**
     * 礼包奖励
     */
    @FieldIgnore
    private RewardObject goodsReward;
    /**
     * 礼包奖励
     */
    @FieldIgnore
    private Collection<RewardObject> rewardList = Lists.newArrayList();

    @Override
    public void initialize() {
        goodsReward = RewardObject.valueOf(JSONArray.parseArray(goods));

        JSONArray rewardArrayList = JSONArray.parseArray(rewards);
        for (Object rewardItem : rewardArrayList) {
            JSONArray rewardArray = JSONArray.parseArray(rewardItem.toString());
            RewardObject rewardObject = RewardObject.valueOf(rewardArray);
            rewardList.add(rewardObject);
        }
    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(data, score);
    }

    public int getData() {
        return data;
    }

    public int getScore() {
        return score;
    }

    public RewardObject getGoodsReward() {
        return goodsReward;
    }

    public Collection<RewardObject> getRewardList() {
        return rewardList;
    }
}
