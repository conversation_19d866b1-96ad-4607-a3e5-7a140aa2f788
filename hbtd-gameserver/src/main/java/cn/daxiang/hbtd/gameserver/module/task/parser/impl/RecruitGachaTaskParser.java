package cn.daxiang.hbtd.gameserver.module.task.parser.impl;

import cn.daxiang.hbtd.gameserver.core.database.table.Task;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.TaskConfig;
import cn.daxiang.hbtd.gameserver.core.event.impl.RecruitGachaEvent;
import cn.daxiang.hbtd.gameserver.module.task.parser.AbstractTaskParser;
import cn.daxiang.hbtd.gameserver.module.task.type.TaskConditionType;
import cn.daxiang.protocol.game.TaskProtocol;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/7/14
 */
@Component
public class RecruitGachaTaskParser extends AbstractTaskParser<RecruitGachaEvent> {
    @Override
    public void init(Task task, TaskConfig taskConfig) {
        if (task.getValue() >= taskConfig.getValue()) {
            task.setStatus(TaskProtocol.TaskStatus.TASK_FINISH);
        }
    }

    @Override
    protected TaskConditionType getType() {
        return TaskConditionType.RECRUIT_GACHA;
    }

    @Override
    protected boolean parseCondition(RecruitGachaEvent event, Task task, TaskConfig taskConfig) {
        task.setValue(task.getValue() + event.times);
        if (task.getValue() >= taskConfig.getValue()) {
            task.setStatus(TaskProtocol.TaskStatus.TASK_FINISH);
        }
        return true;
    }
}
