package cn.daxiang.hbtd.gameserver.module.recruit.facade;

import cn.daxiang.framework.result.TResult;
import cn.daxiang.hbtd.gameserver.core.database.table.Recruit;
import cn.daxiang.protocol.game.CommonProtocol;
import cn.daxiang.protocol.game.RecruitProtocol;

/**
 * 名将招募
 *
 * <AUTHOR>
 * @date 2022/6/14
 */
public interface RecruitFacade {
    /**
     * 获取名将招募信息
     *
     * @param actorId
     * @return
     */
    TResult<Recruit> getRecruit(long actorId);

    /**
     * 选择心愿武将
     *
     * @param actorId
     * @param heroId
     * @return
     */
    TResult<CommonProtocol.IntPacket> chooseWishFamousHero(long actorId, int heroId);

    /**
     * 招募名将
     *
     * @param actorId
     * @param times
     * @return
     */
    TResult<RecruitProtocol.RecruitResultResponse> recruitFamousHero(long actorId, int times);

    /**
     * 元宝购买礼包
     *
     * @param actorId
     * @param id
     * @return
     */
    TResult<CommonProtocol.RewardResultResponse> buyGoods(long actorId, int id);

    /**
     * 领取直购礼包
     *
     * @param actorId
     * @param id
     * @return
     */
    TResult<CommonProtocol.RewardResultResponse> receiveGoods(long actorId, int id);

    /**
     * 获得名将招募上榜记录
     *
     * @param actorId
     * @return
     */
    TResult<RecruitProtocol.RecruitRecordResponse> getRecruitRecords(long actorId);

}
