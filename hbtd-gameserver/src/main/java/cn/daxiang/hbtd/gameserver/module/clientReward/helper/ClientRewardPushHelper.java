package cn.daxiang.hbtd.gameserver.module.clientReward.helper;

import cn.daxiang.framework.protocal.DataPacket;
import cn.daxiang.hbtd.gameserver.core.netty.channel.PlayerChannel;
import cn.daxiang.protocol.game.ClientrewardProtocol;
import cn.daxiang.protocol.game.ClientrewardProtocol.ClientRewardInfoResponse;
import cn.daxiang.protocol.game.ClientrewardProtocol.ClientRewardInfoResponse.Builder;
import cn.daxiang.protocol.game.ClientrewardProtocol.ClientRewardType;
import cn.daxiang.protocol.game.ModuleProtocol.Module;

import java.util.Collection;

/**
 * @author: Gary
 * @date: 2024/3/21 10:13
 * @Description:
 */
public class ClientRewardPushHelper {
    /**
     * 推送客户端奖励信息
     *
     * @param actorId
     * @param type
     * @param receiveIds
     */
    public static void pushClientRewardInfo(long actorId, ClientRewardType type, Collection<Integer> receiveIds) {
        Builder builder = ClientRewardInfoResponse.newBuilder();
        builder.addClientRewardInfoMap(ClientRewardHelper.buildClientRewardReceiveMapInfo(type, receiveIds));
        DataPacket packet = DataPacket.valueOf(Module.CLIENT_REWARD_VALUE, ClientrewardProtocol.ClientRewardCmd.PUSH_CLIENT_REWARD_INFO_VALUE, builder.build());
        PlayerChannel.push(actorId, packet);
    }
}
