package cn.daxiang.hbtd.gameserver.module.mission.parser;

import cn.daxiang.hbtd.gameserver.module.mission.type.MissionConditionType;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.PostConstruct;

/**
 * <AUTHOR>
 * @date 2022/11/3
 */
public abstract class AbstractMissionParser implements MissionParser {

    @Autowired
    private MissionContext context;

    @PostConstruct
    private void initialize() {
        context.register(getType(), this);
    }

    protected abstract MissionConditionType getType();

}
