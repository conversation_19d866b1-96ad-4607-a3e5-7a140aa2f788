package cn.daxiang.hbtd.gameserver.module.extension;

import cn.daxiang.framework.dispatch.DispatchType;
import cn.daxiang.framework.protocal.DataPacket;
import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.framework.router.annotation.Cmd;
import cn.daxiang.hbtd.gameserver.core.database.table.Client;
import cn.daxiang.hbtd.gameserver.core.database.table.DrawEnergy;
import cn.daxiang.hbtd.gameserver.core.database.table.FirstRecharge;
import cn.daxiang.hbtd.gameserver.core.database.table.Fund;
import cn.daxiang.hbtd.gameserver.core.database.table.HeroRecommend;
import cn.daxiang.hbtd.gameserver.core.database.table.LimitedTimeCharge;
import cn.daxiang.hbtd.gameserver.core.database.table.PlayerCentre;
import cn.daxiang.hbtd.gameserver.core.database.table.SignIn;
import cn.daxiang.hbtd.gameserver.core.database.table.ThousandGacha;
import cn.daxiang.hbtd.gameserver.core.database.table.VipCard;
import cn.daxiang.hbtd.gameserver.core.database.table.VipGiftBag;
import cn.daxiang.hbtd.gameserver.core.database.table.WeekGift;
import cn.daxiang.hbtd.gameserver.core.router.GatewayRouterHandlerImpl;
import cn.daxiang.hbtd.gameserver.module.PbBuilder;
import cn.daxiang.hbtd.gameserver.module.extension.facade.ChargeReturnFacade;
import cn.daxiang.hbtd.gameserver.module.extension.facade.ClientFacade;
import cn.daxiang.hbtd.gameserver.module.extension.facade.DailyGiftFacade;
import cn.daxiang.hbtd.gameserver.module.extension.facade.DrawEnergyFacade;
import cn.daxiang.hbtd.gameserver.module.extension.facade.FirstRechargeFacade;
import cn.daxiang.hbtd.gameserver.module.extension.facade.FundFacade;
import cn.daxiang.hbtd.gameserver.module.extension.facade.GrowthTaskFacade;
import cn.daxiang.hbtd.gameserver.module.extension.facade.HeroLineupRecommendFacade;
import cn.daxiang.hbtd.gameserver.module.extension.facade.LimitedTimeChargeFacade;
import cn.daxiang.hbtd.gameserver.module.extension.facade.PlayerCentreFacade;
import cn.daxiang.hbtd.gameserver.module.extension.facade.RankTargetFacade;
import cn.daxiang.hbtd.gameserver.module.extension.facade.SignInFacade;
import cn.daxiang.hbtd.gameserver.module.extension.facade.ThousandGachaFacade;
import cn.daxiang.hbtd.gameserver.module.extension.facade.VipCardFacade;
import cn.daxiang.hbtd.gameserver.module.extension.facade.VipGiftBagFacade;
import cn.daxiang.hbtd.gameserver.module.extension.facade.WeekGiftFacade;
import cn.daxiang.hbtd.gameserver.module.extension.helper.ExtensionHelper;
import cn.daxiang.hbtd.gameserver.module.extension.model.SignInEntity;
import cn.daxiang.protocol.game.CommonProtocol;
import cn.daxiang.protocol.game.CommonProtocol.IntListPacket;
import cn.daxiang.protocol.game.CommonProtocol.IntPacket;
import cn.daxiang.protocol.game.CommonProtocol.RewardResult;
import cn.daxiang.protocol.game.CommonProtocol.RewardResultResponse;
import cn.daxiang.protocol.game.ExtensionProtocol;
import cn.daxiang.protocol.game.ExtensionProtocol.ChargeReturnResponse;
import cn.daxiang.protocol.game.ExtensionProtocol.ExtensionCmd;
import cn.daxiang.protocol.game.ModuleProtocol.Module;
import com.google.common.collect.Sets;
import io.netty.channel.Channel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ExtensionHandler extends GatewayRouterHandlerImpl {
    @Autowired
    private DrawEnergyFacade drawEnergyFacade;
    @Autowired
    private SignInFacade signInFacade;
    @Autowired
    private VipCardFacade vipCardFacade;
    @Autowired
    private ClientFacade clientFacade;
    @Autowired
    private VipGiftBagFacade vipGiftBagFacade;
    @Autowired
    private FirstRechargeFacade firstRechargeFacade;
    @Autowired
    private LimitedTimeChargeFacade limitedTimeChargeFacade;
    @Autowired
    private FundFacade fundFacade;
    @Autowired
    private PlayerCentreFacade playerCentreFacade;
    @Autowired
    private GrowthTaskFacade growthTaskFacade;
    @Autowired
    private DailyGiftFacade dailyGiftFacade;
    @Autowired
    private WeekGiftFacade weekGiftFacade;
    @Autowired
    private RankTargetFacade rankTargetFacade;
    @Autowired
    private ChargeReturnFacade chargeReturnFacade;
    @Autowired
    private ThousandGachaFacade thousandGachaFacade;
    @Autowired
    private HeroLineupRecommendFacade heroLineupRecommendFacade;

    @Override
    public int getModule() {
        return Module.EXTENSION_VALUE;
    }

    @Cmd(Id = ExtensionCmd.GET_DRAW_ENERGY_INFO_VALUE, dispatchType = DispatchType.ACTOR)
    public void getDrawEnergy(Channel channel, Long actorId, DataPacket packet) {
        TResult<DrawEnergy> result = drawEnergyFacade.getDrawEnergy(actorId);
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        IntListPacket response = IntListPacket.newBuilder().addAllList(result.item.getReceiveList()).build();
        channelWrite(channel, packet, response);
    }

    @Cmd(Id = ExtensionCmd.DRAW_ENERGY_RECEIVE_VALUE, dispatchType = DispatchType.ACTOR)
    public void receiveDrawEnergy(Channel channel, Long actorId, DataPacket packet) {
        IntPacket request = packet.getValue(IntPacket.parser());
        TResult<RewardResult> result = drawEnergyFacade.receiveDrawEnergy(actorId, request.getValue());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        RewardResultResponse response = PbBuilder.buildRewardResultResponse(result.item);
        channelWrite(channel, packet, response);
    }

    @Cmd(Id = ExtensionCmd.GET_SIGN_IN_VALUE, dispatchType = DispatchType.ACTOR)
    public void getSignInInfo(Channel channel, Long actorId, DataPacket packet) {
        TResult<SignIn> result = signInFacade.getSignIn(actorId);
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        SignInEntity entity = result.item.getSignInEntity();
        ExtensionProtocol.SignInResponse response =
            ExtensionProtocol.SignInResponse.newBuilder().setDays(entity.getDays()).setStage(entity.getStage()).setLastSignInTime(entity.getLastSignInTime())
                .addAllReceiveReward(entity.getRecieveList()).build();
        channelWrite(channel, packet, response);
    }

    @Cmd(Id = ExtensionCmd.SIGN_IN_VALUE, dispatchType = DispatchType.ACTOR)
    public void signIn(Channel channel, Long actorId, DataPacket packet) {
        TResult<RewardResult> result = signInFacade.signIn(actorId);
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        RewardResultResponse response = PbBuilder.buildRewardResultResponse(result.item);
        channelWrite(channel, packet, response);
    }

    @Cmd(Id = ExtensionCmd.RECEIVE_TOTAL_SIGN_IN_VALUE, dispatchType = DispatchType.ACTOR)
    public void receiveTotalSignIn(Channel channel, Long actorId, DataPacket packet) {
        IntListPacket request = packet.getValue(IntListPacket.parser());
        TResult<RewardResult> result = signInFacade.recieveTotalSignIn(actorId, Sets.newHashSet(request.getListList()));
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        RewardResultResponse response = PbBuilder.buildRewardResultResponse(result.item);
        channelWrite(channel, packet, response);
    }

    @Cmd(Id = ExtensionCmd.GET_VIP_CARD_INFO_VALUE, dispatchType = DispatchType.ACTOR)
    public void getVipCardInfo(Channel channel, Long actorId, DataPacket packet) {
        TResult<VipCard> result = vipCardFacade.getVipCard(actorId);
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        ExtensionProtocol.VipCardResponse response = ExtensionHelper.buildVipCardResponse(result.item);
        channelWrite(channel, packet, response);
    }

    @Cmd(Id = ExtensionCmd.VIP_CARD_RECEIVE_VALUE, dispatchType = DispatchType.ACTOR)
    public void receiveVipCard(Channel channel, Long actorId, DataPacket packet) {
        IntPacket request = packet.getValue(IntPacket.parser());
        TResult<RewardResult> result = vipCardFacade.receiveDailyReward(actorId, request.getValue());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        RewardResultResponse response = PbBuilder.buildRewardResultResponse(result.item);
        channelWrite(channel, packet, response);
    }

    @Cmd(Id = ExtensionCmd.VIP_CARD_SCORE_RECEIVE_VALUE, dispatchType = DispatchType.ACTOR)
    public void receiveVipCardScoreReward(Channel channel, Long actorId, DataPacket packet) {
        IntPacket request = packet.getValue(IntPacket.parser());
        TResult<RewardResult> result = vipCardFacade.receiveVipScoreReward(actorId, request.getValue());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        RewardResultResponse response = PbBuilder.buildRewardResultResponse(result.item);
        channelWrite(channel, packet, response);
    }

    @Cmd(Id = ExtensionCmd.GET_CLIENT_DATA_VALUE, dispatchType = DispatchType.ACTOR)
    public void getClientData(Channel channel, Long actorId, DataPacket packet) {
        Client client = clientFacade.getClient(actorId);
        ExtensionProtocol.ClientDataResponse.Builder builder = ExtensionProtocol.ClientDataResponse.newBuilder();
        builder.putAllData(client.getData());
        channelWrite(channel, packet, builder.build());
    }

    @Cmd(Id = ExtensionCmd.REFRESH_CLIENT_DATA_VALUE, dispatchType = DispatchType.ACTOR)
    public void refreshClientData(Channel channel, Long actorId, DataPacket packet) {
        ExtensionProtocol.ClientDataRequest request = packet.getValue(ExtensionProtocol.ClientDataRequest.parser());
        Result result = clientFacade.refresh(actorId, request.getData());
        channelWrite(channel, packet, result.statusCode);
    }

    @Cmd(Id = ExtensionCmd.GET_VIP_GIFT_BAG_VALUE, dispatchType = DispatchType.ACTOR)
    public void getVipGiftBag(Channel channel, Long actorId, DataPacket packet) {
        TResult<VipGiftBag> result = vipGiftBagFacade.getVipGiftBag(actorId);
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        ExtensionProtocol.VipGiftBagResponse.Builder builder = ExtensionProtocol.VipGiftBagResponse.newBuilder();
        builder.addAllReceives(result.item.getGiftBagList());
        channelWrite(channel, packet, builder.build());
    }

    @Cmd(Id = ExtensionCmd.BUY_VIP_GIFT_BAG_VALUE, dispatchType = DispatchType.ACTOR)
    public void getBuyVipGiftBag(Channel channel, Long actorId, DataPacket packet) {
        IntPacket request = packet.getValue(IntPacket.parser());
        TResult<CommonProtocol.RewardResult> result = vipGiftBagFacade.buyGiftBag(actorId, request.getValue());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        RewardResultResponse response = PbBuilder.buildRewardResultResponse(result.item);
        channelWrite(channel, packet, response);
    }

    @Cmd(Id = ExtensionCmd.GET_FIRST_RECHARGE_VALUE, dispatchType = DispatchType.ACTOR)
    public void getFirstRecharge(Channel channel, Long actorId, DataPacket packet) {
        FirstRecharge firstRecharge = firstRechargeFacade.getFirstRecharge(actorId);
        ExtensionProtocol.FirstRechargeResponse response = ExtensionHelper.buildFirstRechargeResponse(firstRecharge);
        channelWrite(channel, packet, response);
    }

    @Cmd(Id = ExtensionCmd.RECEIVE_FIRST_RECHARGE_VALUE, dispatchType = DispatchType.ACTOR)
    public void receiveFirstRecharge(Channel channel, Long actorId, DataPacket packet) {
        IntListPacket request = packet.getValue(IntListPacket.parser());
        TResult<CommonProtocol.RewardResultResponse> result = firstRechargeFacade.receiveFirstRecharge(actorId, Sets.newHashSet(request.getListList()));
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = ExtensionCmd.GET_LIMITED_TIME_CHARGE_INFO_VALUE, dispatchType = DispatchType.ACTOR)
    public void getLimitedTimeChargeInfo(Channel channel, Long actorId, DataPacket packet) {
        TResult<LimitedTimeCharge> result = limitedTimeChargeFacade.getLimitedTimeCharge(actorId);
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        ExtensionProtocol.LimitedTimeChargeResponse response = ExtensionHelper.buildLimitedTimeChargeResponse(result.item.getLimitedTimeChargeMap().values());
        channelWrite(channel, packet, response);
    }

    @Cmd(Id = ExtensionCmd.LIMITED_TIME_CHARGE_RECEIVE_VALUE, dispatchType = DispatchType.ACTOR)
    public void receiveLimitedTimeCharge(Channel channel, Long actorId, DataPacket packet) {
        IntPacket request = packet.getValue(IntPacket.parser());
        TResult<RewardResultResponse> result = limitedTimeChargeFacade.receive(actorId, request.getValue());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = ExtensionCmd.GET_FUND_INFO_VALUE, dispatchType = DispatchType.ACTOR)
    public void getFundInfo(Channel channel, Long actorId, DataPacket packet) {
        TResult<Fund> result = fundFacade.getFund(actorId);
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        ExtensionProtocol.FundResponse fundResponse = ExtensionHelper.buildFundResponse(result.item);
        channelWrite(channel, packet, fundResponse);
    }

    @Cmd(Id = ExtensionCmd.RECIEVE_FUND_VALUE, dispatchType = DispatchType.ACTOR)
    public void receiveFund(Channel channel, Long actorId, DataPacket packet) {
        ExtensionProtocol.RecieveFundRequest request = packet.getValue(ExtensionProtocol.RecieveFundRequest.parser());
        TResult<RewardResultResponse> result = fundFacade.receiveFund(actorId, request.getIsRecieceLuxury(), request.getConfigId());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = ExtensionCmd.QUICKLY_RECIEVE_FUND_VALUE, dispatchType = DispatchType.ACTOR)
    public void quicklyReceiveFund(Channel channel, Long actorId, DataPacket packet) {
        CommonProtocol.KeyValuePacket request = packet.getValue(CommonProtocol.KeyValuePacket.parser());
        TResult<RewardResultResponse> result = fundFacade.quicklyReceiveFund(actorId, request.getKey(), request.getValue());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = ExtensionCmd.RECIEVE_WELFARE_VALUE, dispatchType = DispatchType.ACTOR)
    public void receiveWelfare(Channel channel, Long actorId, DataPacket packet) {
        CommonProtocol.KeyValuePacket request = packet.getValue(CommonProtocol.KeyValuePacket.parser());
        TResult<RewardResultResponse> result = fundFacade.receiveWelfare(actorId, request.getValue());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = ExtensionCmd.GET_ENERGY_PRIVILEGE_VALUE, dispatchType = DispatchType.ACTOR)
    public void getEnergyPrivilege(Channel channel, Long actorId, DataPacket packet) {
        TResult<ExtensionProtocol.EnergyPrivilegeResponse> result = drawEnergyFacade.getEnergyPrivilege(actorId);
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = ExtensionCmd.ENERGY_PRIVILEGE_POOL_EXCHANGE_VALUE, dispatchType = DispatchType.ACTOR)
    public void energyPrivilegePoolExchange(Channel channel, Long actorId, DataPacket packet) {
        IntPacket request = packet.getValue(IntPacket.parser());
        TResult<RewardResult> result = drawEnergyFacade.energyPrivilegePoolExchange(actorId, request.getValue());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        RewardResultResponse response = PbBuilder.buildRewardResultResponse(result.item);
        channelWrite(channel, packet, response);
    }

    @Cmd(Id = ExtensionCmd.ENERGY_PRIVILEGE_REPLACEMENT_VALUE, dispatchType = DispatchType.ACTOR)
    public void energyPrivilegeReplacement(Channel channel, Long actorId, DataPacket packet) {
        IntPacket request = packet.getValue(IntPacket.parser());
        TResult<RewardResult> result = drawEnergyFacade.energyPrivilegeReplacement(actorId, request.getValue());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        RewardResultResponse response = PbBuilder.buildRewardResultResponse(result.item);
        channelWrite(channel, packet, response);
    }

    @Cmd(Id = ExtensionCmd.ENERGY_PRIVILEGE_BUY_VALUE, dispatchType = DispatchType.ACTOR)
    public void energyPrivilegeBuy(Channel channel, Long actorId, DataPacket packet) {
        IntPacket request = packet.getValue(IntPacket.parser());
        TResult<RewardResult> result = drawEnergyFacade.energyPrivilegeBuy(actorId, request.getValue());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        RewardResultResponse response = PbBuilder.buildRewardResultResponse(result.item);
        channelWrite(channel, packet, response);
    }

    @Cmd(Id = ExtensionCmd.PLAYER_CENTRE_INFO_VALUE, dispatchType = DispatchType.ACTOR)
    public void getPlayerCentre(Channel channel, Long actorId, DataPacket packet) {
        PlayerCentre playerCentre = playerCentreFacade.get(actorId);
        ExtensionProtocol.PlayerCentreResponse response =
            ExtensionProtocol.PlayerCentreResponse.newBuilder().setAntiFraudReward(playerCentre.isAntiFraudReward()).setFeedbackTimes(playerCentre.getFeedbackTimes()).build();
        channelWrite(channel, packet, response);
    }

    @Cmd(Id = ExtensionCmd.PLAYER_CENTRE_ANTI_FRAUD_GUIDE_REWARD_VALUE, dispatchType = DispatchType.ACTOR)
    public void antiFraudGuideReward(Channel channel, Long actorId, DataPacket packet) {
        TResult<RewardResult> result = playerCentreFacade.antiFraudGuideReward(actorId);
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        RewardResultResponse response = PbBuilder.buildRewardResultResponse(result.item);
        channelWrite(channel, packet, response);
    }

    @Cmd(Id = ExtensionCmd.PLAYER_CENTRE_FEEDBACK_VALUE, dispatchType = DispatchType.ACTOR)
    public void feedback(Channel channel, Long actorId, DataPacket packet) {
        CommonProtocol.StringPacket request = packet.getValue(CommonProtocol.StringPacket.parser());
        Result result = playerCentreFacade.feedback(actorId, request.getValue());
        channelWrite(channel, packet, result.statusCode);
    }

    @Cmd(Id = ExtensionCmd.GET_DAILY_GIFT_INFO_VALUE, dispatchType = DispatchType.ACTOR)
    public void getDailyGiftInfo(Channel channel, Long actorId, DataPacket packet) {
        TResult<ExtensionProtocol.DailyGiftInfoResponse> result = dailyGiftFacade.getDailyGiftInfo(actorId);
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = ExtensionCmd.RECEIVE_DAILY_GIFT_VALUE, dispatchType = DispatchType.ACTOR)
    public void receiveDailyGift(Channel channel, Long actorId, DataPacket packet) {
        IntListPacket request = packet.getValue(IntListPacket.parser());
        TResult<RewardResultResponse> result = dailyGiftFacade.receiveDailyGift(actorId, request.getListList());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = ExtensionCmd.DRAW_DAILY_GIFT_VALUE, dispatchType = DispatchType.ACTOR)
    public void drawDailyGift(Channel channel, Long actorId, DataPacket packet) {
        TResult<ExtensionProtocol.DrawRewardResponse> result = dailyGiftFacade.drawDailyGift(actorId);
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = ExtensionCmd.GROWTH_TASK_RECEIVE_VALUE, dispatchType = DispatchType.ACTOR)
    public void growthTaskReceive(Channel channel, Long actorId, DataPacket packet) {
        TResult<RewardResult> result = growthTaskFacade.receive(actorId);
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        RewardResultResponse response = PbBuilder.buildRewardResultResponse(result.item);
        channelWrite(channel, packet, response);
    }

    @Cmd(Id = ExtensionCmd.WEEK_GIFT_VALUE, dispatchType = DispatchType.ACTOR)
    public void weekGift(Channel channel, Long actorId, DataPacket packet) {
        TResult<WeekGift> result = weekGiftFacade.getWeekGift(actorId);
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        int week = weekGiftFacade.getWeek();
        ExtensionProtocol.WeekGiftInfoResponse response = ExtensionHelper.buildWeekGiftInfoResponse(week, result.item);
        channelWrite(channel, packet, response);
    }

    @Cmd(Id = ExtensionCmd.RECEIVE_WEEK_GIFT_VALUE, dispatchType = DispatchType.ACTOR)
    public void receiveWeekGift(Channel channel, Long actorId, DataPacket packet) {
        TResult<RewardResult> result = weekGiftFacade.receive(actorId);
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        RewardResultResponse response = PbBuilder.buildRewardResultResponse(result.item);
        channelWrite(channel, packet, response);
    }

    @Cmd(Id = ExtensionCmd.GET_RANK_TARGET_INFO_VALUE, dispatchType = DispatchType.ACTOR)
    public void getRankTarget(Channel channel, Long actorId, DataPacket packet) {
        TResult<ExtensionProtocol.RankTargetResponse> result = rankTargetFacade.getRankTargetResponse(actorId);
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = ExtensionCmd.RECEIVE_RANK_TARGET_REWARD_VALUE, dispatchType = DispatchType.ACTOR)
    public void receiveRankTarget(Channel channel, Long actorId, DataPacket packet) {
        IntPacket request = packet.getValue(IntPacket.parser());
        TResult<RewardResultResponse> result = rankTargetFacade.receive(actorId, request.getValue());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = ExtensionCmd.GET_CHARGE_RETURN_VALUE, dispatchType = DispatchType.ACTOR)
    public void getChargeReturn(Channel channel, Long actorId, DataPacket packet) {
        TResult<ChargeReturnResponse> result = chargeReturnFacade.getChargeReturn(actorId);
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = ExtensionCmd.CHARGE_RETURN_RECEIVE_VALUE, dispatchType = DispatchType.ACTOR)
    public void chargeReturnReceive(Channel channel, Long actorId, DataPacket packet) {
        TResult<RewardResultResponse> result = chargeReturnFacade.receive(actorId);
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = ExtensionCmd.GET_THOUSAND_GACHA_VALUE, dispatchType = DispatchType.ACTOR)
    public void getThousandGacha(Channel channel, Long actorId, DataPacket packet) {
        TResult<ThousandGacha> result = thousandGachaFacade.getThousandGacha(actorId);
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, ExtensionHelper.buildThousandGachaResponse(result.item));
    }

    @Cmd(Id = ExtensionCmd.RECEIVE_THOUSAND_GACHA_VALUE, dispatchType = DispatchType.ACTOR)
    public void thousandGachaReceive(Channel channel, Long actorId, DataPacket packet) {
        TResult<RewardResultResponse> result = thousandGachaFacade.receive(actorId);
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = ExtensionCmd.GET_HERO_LINEUP_RECOMMEND_VALUE, dispatchType = DispatchType.ACTOR)
    public void getHeroLineupRecommend(Channel channel, Long actorId, DataPacket packet) {
        TResult<HeroRecommend> result = heroLineupRecommendFacade.getHeroLineupRecommend(actorId);
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, ExtensionHelper.buildHeroLineupRecommendResponse(result.item));
    }

    @Cmd(Id = ExtensionCmd.RECEIVE_HERO_LINEUP_RECOMMEND_VALUE, dispatchType = DispatchType.ACTOR)
    public void receiveHeroLineupRecommend(Channel channel, Long actorId, DataPacket packet) {
        IntPacket request = packet.getValue(IntPacket.parser());
        TResult<RewardResultResponse> result = heroLineupRecommendFacade.receive(actorId, request.getValue());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }
}
