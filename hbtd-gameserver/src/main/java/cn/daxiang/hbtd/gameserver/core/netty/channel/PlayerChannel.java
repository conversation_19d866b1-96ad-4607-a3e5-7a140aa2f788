package cn.daxiang.hbtd.gameserver.core.netty.channel;

import cn.daxiang.framework.context.ApplicationShutdownEvent;
import cn.daxiang.framework.context.SpringContext;
import cn.daxiang.framework.netty.delay.DelayedChannel;
import cn.daxiang.framework.protocal.DataPacket;
import cn.daxiang.framework.utils.RandomUtils;
import cn.daxiang.framework.utils.TimeCacheMap;
import cn.daxiang.framework.utils.TimeConstant;
import cn.daxiang.hbtd.gameserver.core.GameConfig;
import cn.daxiang.hbtd.gameserver.core.oss.GameOssLogger;
import cn.daxiang.hbtd.gameserver.module.user.helper.ActorHelper;
import cn.daxiang.hbtd.gameserver.module.user.helper.UserPushHelper;
import cn.daxiang.hbtd.gameserver.module.user.type.ServerState;
import cn.daxiang.protocol.game.TypeProtocol.KickOffType;
import com.google.common.collect.Maps;
import io.netty.channel.Channel;
import io.netty.channel.ChannelId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Collection;
import java.util.Date;
import java.util.HashSet;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.DelayQueue;
import java.util.concurrent.atomic.AtomicInteger;

@Component
public class PlayerChannel {
    private static final Logger LOGGER = LoggerFactory.getLogger(PlayerChannel.class);

    /**
     * 匿名Channel
     * key:ChannelId value:Channel
     */
    private static final Map<ChannelId, Channel> ANONYMOUS_MAP = new ConcurrentHashMap<ChannelId, Channel>();

    /**
     * 在线角色Channel
     * key:actorId value:Channel
     */
    private static final Map<Long, Channel> ACTORID_MAP = new ConcurrentHashMap<Long, Channel>();
    /**
     * 用户重连Map
     * key:uid_serverId,value:uuid
     */
    private static final TimeCacheMap<String, Entry<String, Boolean>> USER_RECONNECTION_MAP = new TimeCacheMap<>((int) TimeConstant.ONE_HOUR_SECOND * 6, 2);
    /**
     * 延迟关闭Channel队列
     */
    private static final DelayQueue<DelayedChannel> CHANNEL_CLOSE_QUEUES = new DelayQueue<DelayedChannel>();

    /**
     * 最大在线数
     */
    private static AtomicInteger maxOnlineCount = new AtomicInteger(0);
    /**
     * 最小大线数
     */
    private static AtomicInteger minOnlineCount = new AtomicInteger(0);

    /**
     * 获取在线玩家最大数量
     *
     * @return
     */
    public static int getMaxOnlineCount() {
        return maxOnlineCount.get();
    }

    /**
     * 获取在线玩家最小数量
     *
     * @return
     */
    public static int getMinOnlineCount() {
        return minOnlineCount.get();
    }

    /**
     * 延迟关闭channel
     *
     * @param channel     当前channel
     * @param delaySecond 延迟秒
     */
    public static void delayCloseChannel(Channel channel, int delaySecond) {
        if (delaySecond < 0 || channel == null) {
            return;
        }
        DelayedChannel element = new DelayedChannel(channel, new Date(), delaySecond);
        if (!CHANNEL_CLOSE_QUEUES.contains(element)) {
            CHANNEL_CLOSE_QUEUES.add(element);
        }
    }

    /**
     * 发送状态码
     *
     * @param channel        当前channel
     * @param statusCode     状态码
     * @param isCloseChannel 是否关闭channel
     */
    public static void writeStatusCode(Channel channel, short statusCode, boolean isCloseChannel) {
        if (channel == null) {
            return;
        }
        DataPacket response = DataPacket.valueOf((byte) 0, (byte) 0, statusCode);
        channel.writeAndFlush(response);
        if (isCloseChannel) {
            closeIoChannel(channel);
        }
    }

    /**
     * 角色加入在线列表
     *
     * @param channel
     * @param actorId
     * @param kickOrignChannel 是否删除在这之前actroId相同的channel
     */
    public static void put2OnlineList(Channel channel, long actorId, boolean kickOrignChannel) {
        if (channel == null) {
            return;
        }
        if (ANONYMOUS_MAP.containsKey(channel.id())) {
            ANONYMOUS_MAP.remove(channel.id());
        }
        if (kickOrignChannel) {
            Channel orignChannel = ACTORID_MAP.put(actorId, channel);
            if (orignChannel != null && orignChannel.isActive()) {
                if (LOGGER.isDebugEnabled()) {
                    LOGGER.debug("actorid:[{}] 加入在线用户列表前关闭之前的Channel", actorId);
                }
                orignChannel.close();
            }
        } else {
            ACTORID_MAP.remove(actorId);
            ACTORID_MAP.put(actorId, channel);
        }
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("channel:[{}] bind actorid:[{}] ", channel.id(), actorId);
        }
        int onlineCount = onlineActorCount();
        if (maxOnlineCount.get() < onlineCount) {
            maxOnlineCount.set(onlineCount);
        }
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("actorid:[{}] join online channel list", actorId);
        }
    }

    /**
     * 移除在线玩家
     *
     * @param oldChannel
     */
    public static void removeFromOnlineList(Channel oldChannel) {
        ChannelId oldChaanneId = oldChannel.id();
        ANONYMOUS_MAP.remove(oldChaanneId);
        Long actorId = getActorId(oldChannel);
        if (actorId > 0) {
            Channel channel = ACTORID_MAP.get(actorId);
            if (channel == null) {
                return;
            }
            if (oldChannel.id().equals(channel.id())) {
                ACTORID_MAP.remove(actorId);
            }
            int onlineCount = onlineActorCount();
            if (minOnlineCount.get() < onlineCount) {
                minOnlineCount.set(onlineCount);
            }
            String createIP = PlayerChannel.getRemoteIp(channel);
            GameOssLogger.actorLogin(actorId, createIP);
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("remove actorId:[{}] in online channel list", actorId);
            }
        }
    }

    /**
     * 添加匿名玩家
     *
     * @param channel
     */
    public static void put2AnonymousList(Channel channel) {
        if (channel != null) {
            ANONYMOUS_MAP.put(channel.id(), channel);
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("channel:[{}] join anonymous channel list", channel.id());
            }
        }
    }

    /**
     * 根据channelId获取匿名的Channel
     *
     * @param id
     * @return
     */
    public static Channel getAnonymouseChannel(ChannelId id) {
        return ANONYMOUS_MAP.get(id);
    }

    /**
     * 移除匿名玩家
     *
     * @param channel
     */
    public static void removeFromAnonymousList(Channel channel) {
        if (channel != null) {
            if (ANONYMOUS_MAP.containsKey(channel.id())) {
                ANONYMOUS_MAP.remove(channel.id());
                if (LOGGER.isDebugEnabled()) {
                    LOGGER.debug("remove channel:[{}] in anonymous channel list", channel.id());
                }
            }
        }
    }

    /**
     * 角色是否在线
     *
     * @param actorId
     * @return
     */
    public static boolean isOnline(long actorId) {
        return ACTORID_MAP.containsKey(actorId);
    }

    /**
     * 角色是否在线
     *
     * @param channel
     * @return
     */
    public static boolean isOnline(Channel channel) {
        long actorId = getActorId(channel);
        if (actorId < 1L) {
            return false;
        }
        return ACTORID_MAP.containsKey(actorId);
    }

    /**
     * 获取在线玩家playerId列表
     *
     * @return
     */
    public static Set<Long> onlineActorList() {
        return ACTORID_MAP.keySet();
    }

    /**
     * 获取在线玩家列表表
     *
     * @param filterActorIds 过滤的角色id
     * @return
     */
    public static Set<Long> onlineActorList(Collection<Long> filterActorIds) {
        Set<Long> onlineSets = new HashSet<Long>();
        for (Long actorId : ACTORID_MAP.keySet()) {
            if (filterActorIds == null || !filterActorIds.contains(actorId)) {
                onlineSets.add(actorId);
            }
        }
        return onlineSets;
    }

    /**
     * 获取在线玩家总数
     *
     * @return
     */
    public static int onlineActorCount() {
        return ACTORID_MAP.size();
    }

    /**
     * 获取所有在线玩家的Channel
     *
     * @return
     */
    public static Collection<Channel> getOnlineChannelList() {
        return ACTORID_MAP.values();
    }

    /**
     * 获取在线玩家的Channel
     *
     * @param actorId
     * @return
     */
    public static Channel getOnlineChannel(long actorId) {
        return ACTORID_MAP.get(actorId);
    }

    /**
     * 获取匿名玩家Channel列表
     *
     * @return
     */
    public static Collection<Channel> anonymousChannelList() {
        return ANONYMOUS_MAP.values();
    }

    /**
     * 广播消息给单个用户
     *
     * @param actorId
     * @param packet
     */
    public static void push(long actorId, DataPacket packet) {
        Channel channel = getOnlineChannel(actorId);
        push(channel, packet);
    }

    /**
     * 推送消息给用户列表
     *
     * @param actorIdList
     * @param packet
     */
    public static void push(Collection<Long> actorIdList, DataPacket packet) {
        if ((actorIdList == null) || (actorIdList.isEmpty())) {
            return;
        }
        for (Long actorId : actorIdList) {
            Channel channel = getOnlineChannel(actorId);
            if (channel != null) {
                push(channel, packet);
            }
        }
    }

    /**
     * 推送消息给用户列表
     *
     * @param actorIds
     * @param packet
     */
    public static void push(long[] actorIds, DataPacket packet) {
        if (actorIds == null) {
            return;
        }

        for (Long actorId : actorIds) {
            Channel channel = getOnlineChannel(actorId);
            if (channel != null) {
                push(channel, packet);
            }
        }
    }

    /**
     * 推送消息给当前Channel玩家
     *
     * @param channel
     * @param packet
     */
    public static void push(Channel channel, DataPacket packet) {
        if (channel == null) {
            return;
        }

        if (channel.isActive()) {
            if (packet != null) {
                channel.writeAndFlush(packet);
            }
        } else {
            if (LOGGER.isDebugEnabled()) {
                Long actorId = getActorId(channel);
                LOGGER.debug("broadcast to actorid:[{}] is fail. channel not found.", actorId);
            }
            closeIoChannel(channel);
        }
    }

    /**
     * 推送消息给所有在线玩家
     *
     * @param packet
     */
    public static void pushAllOnline(DataPacket packet) {
        push(ACTORID_MAP.keySet(), packet);
    }

    /**
     * 关闭当前Channel
     *
     * @param channel
     */
    public static void closeIoChannel(Channel channel) {
        if (channel != null) {
            channel.close();
        }
    }

    /**
     * 获取actorId
     *
     * @param channel 当前用户channel对象
     * @return
     */
    public static Long getActorId(Channel channel) {
        Long actorId = null;
        if (channel != null && channel.hasAttr(ChannelKey.ACTOR_ID)) {
            actorId = channel.attr(ChannelKey.ACTOR_ID).get();
        }
        return actorId == null ? RandomUtils.nextLong(-100L, 0L) : actorId;
    }

    /**
     * 设置ACTOR_ID
     *
     * @param channel
     * @param actorId
     */
    public static void setActorId(Channel channel, Long actorId) {
        channel.attr(ChannelKey.ACTOR_ID).set(actorId);
    }

    public static boolean isPlatformPrivileged(Channel channel) {
        return channel.attr(ChannelKey.PLATFORM_PRIVILEGED).get();
    }

    /**
     * 帐号是否已登陆
     *
     * @param channel
     * @return
     */
    public static boolean userIsLogin(Channel channel) {
        int platformId = getPlatformId(channel);
        long uid = getUid(channel);
        if (platformId < 1 || uid < 1) {
            return false;
        }
        return true;
    }

    /**
     * 获取远程ip
     *
     * @param channel 当前channel
     * @return
     */
    public static String getRemoteIp(Channel channel) {
        if (channel == null) {
            return "";
        }
        return channel.attr(ChannelKey.REMOTE_HOST).get();
    }

    /**
     * 获取创建Channel时，分配的唯一自增id
     *
     * @param channel
     * @return
     */
    public static Long getAtomicId(Channel channel) {
        return channel.attr(ChannelKey.ATOMIC_ID).get();
    }

    /**
     * 设置自增id
     *
     * @param channel
     * @param atomicId
     */
    public static void setAtomicId(Channel channel, Long atomicId) {
        channel.attr(ChannelKey.ATOMIC_ID).set(atomicId);
    }

    /**
     * 获取是否第一次请求
     *
     * @param channel
     * @return
     */
    public static Boolean getFirstRequest(Channel channel) {
        if (!channel.attr(ChannelKey.FIRST_REQUEST).get()) {
            setFirstRequest(channel, true);
        }
        return channel.attr(ChannelKey.FIRST_REQUEST).get();
    }

    /**
     * 设置是否第一次请求
     *
     * @param channel
     * @param firstRequest
     */
    public static void setFirstRequest(Channel channel, Boolean firstRequest) {
        channel.attr(ChannelKey.FIRST_REQUEST).set(firstRequest);
    }

    /**
     * 获取平台Id
     *
     * @param channel
     */
    public static int getPlatformId(Channel channel) {
        Integer type = channel.attr(ChannelKey.PLATFORM_ID).get();
        if (type == null) {
            return 0;
        }
        return type;
    }

    /**
     * 获取token
     *
     * @param channel
     * @return
     */
    public static long getUid(Channel channel) {
        Long uid = channel.attr(ChannelKey.PLATFORM_UID).get();
        if (uid == null) {
            return 0;
        }
        return uid;
    }

    /**
     * 获取角色登陆的游戏服Id
     *
     * @param channel
     */
    public static int getServerId(Channel channel) {
        Integer serverId = channel.attr(ChannelKey.SERVER_ID).get();
        return serverId == null ? 0 : serverId;
    }

    /**
     * 设置角色登陆的游戏服Id
     *
     * @param channel
     * @param serverId
     */
    public static void setServerId(Channel channel, int serverId) {
        channel.attr(ChannelKey.SERVER_ID).set(serverId);
    }

    /**
     * 获取角色推送标识ID
     *
     * @param channel
     */
    public static String getIosToken(Channel channel) {
        return channel.attr(ChannelKey.IOS_TOKEN).get();
    }

    /**
     * 设置角色IOS推送标识ID
     *
     * @param channel
     * @param iosToken
     */
    public static void setIosToken(Channel channel, String iosToken) {
        channel.attr(ChannelKey.IOS_TOKEN).set(iosToken);
    }

    /**
     * 获取角色推送标识ID
     *
     * @param channel
     */
    public static String getAndroidToken(Channel channel) {
        return channel.attr(ChannelKey.ANDROID_TOKEN).get();
    }

    /**
     * 设置角色推送标识ID
     *
     * @param channel
     * @param androidToken
     */
    public static void setAndroidToken(Channel channel, String androidToken) {
        channel.attr(ChannelKey.ANDROID_TOKEN).set(androidToken);
    }

    /**
     * 设置用户登陆信息
     *
     * @param channel
     * @param platformId
     * @param uid
     * @param serverId
     * @param privileged
     * @return
     */
    public static String setUserLogin(Channel channel, int platformId, long uid, int serverId, boolean privileged) {
        if (uid > 0 && platformId > 0 && serverId > 0) {
            channel.attr(ChannelKey.PLATFORM_ID).set(platformId);
            channel.attr(ChannelKey.SERVER_ID).set(serverId);
            channel.attr(ChannelKey.PLATFORM_UID).set(uid);
            channel.attr(ChannelKey.PLATFORM_PRIVILEGED).set(privileged);
            String reconnectionId = UUID.randomUUID().toString();
            USER_RECONNECTION_MAP.put(ActorHelper.getUserKey(uid, serverId), Maps.immutableEntry(reconnectionId, privileged));
            return reconnectionId;
        }
        return null;
    }

    /**
     * 获取用户重连ID
     *
     * @param uid
     * @param serverId
     * @return
     */
    public static Entry<String, Boolean> getUserReconnectionId(long uid, int serverId) {
        return USER_RECONNECTION_MAP.get(ActorHelper.getUserKey(uid, serverId));
    }

    @PostConstruct
    private void initialize() {
        Thread thread = new Thread(new Runnable() {
            public void run() {
                executeDelayChannel();
            }
        });
        thread.setDaemon(true);
        thread.setName("player channel delay thread");
        thread.start();
        Runtime.getRuntime().addShutdownHook(new Thread(new Runnable() {
            @Override
            public void run() {
                GameConfig.setMaintainState(ServerState.CLOSE);
                while (PlayerChannel.onlineActorCount() > 0) {
                    LOGGER.info("online channel kick off  online:{}", PlayerChannel.onlineActorCount());
                    for (Channel channel : PlayerChannel.getOnlineChannelList()) {
                        UserPushHelper.kickOff(channel, KickOffType.CLOSEING, 0);
                    }
                    try {
                        Thread.sleep(1000L);
                    } catch (InterruptedException e) {
                        LOGGER.error("{}", e);
                    }
                }
                LOGGER.info("online channel shutdown complete ...");
                SpringContext.getContext().publishEvent(new ApplicationShutdownEvent(this));
            }
        }, "shutdownHook"));
    }

    private void executeDelayChannel() {
        while (true) {
            try {
                DelayedChannel take = CHANNEL_CLOSE_QUEUES.take();
                if (take != null) {
                    take.getChannel().close();
                    if (LOGGER.isDebugEnabled()) {
                        LOGGER.debug("延迟关闭Channel:[{}] ", take.getChannel().id());
                    }
                }
            } catch (Exception e) {
                LOGGER.error("阻塞延迟队列异常: {}", e);
            }
        }
    }
}
