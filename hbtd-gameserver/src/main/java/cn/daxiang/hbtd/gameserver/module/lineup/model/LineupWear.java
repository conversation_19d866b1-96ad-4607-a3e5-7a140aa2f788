package cn.daxiang.hbtd.gameserver.module.lineup.model;

/**
 * 阵容穿戴对象
 *
 * <AUTHOR>
 */
public class LineupWear implements Comparable<LineupWear> {
    /**
     * ID
     */
    private long id;
    /**
     * 装备品质
     * {@code QualityType}
     */
    private int quality;
    /**
     * 装备强化等级
     */
    private int reinforceLevel;
    /**
     * 锻造等级
     */
    private int refineLevel;
    /**
     * 雕纹等级
     */
    private int glyphsLevel;

    public static LineupWear valueOf(long id, int quality, int reinforceLevel, int refineLevel, int glyphsLevel) {
        LineupWear lineupWear = new LineupWear();
        lineupWear.id = id;
        lineupWear.quality = quality;
        lineupWear.reinforceLevel = reinforceLevel;
        lineupWear.refineLevel = refineLevel;
        lineupWear.glyphsLevel = glyphsLevel;
        return lineupWear;
    }

    public static LineupWear valueOf(long id, int quality, int reinforceLevel) {
        return valueOf(id, quality, reinforceLevel, 0, 0);
    }

    public long getId() {
        return id;
    }

    public int getQuality() {
        return quality;
    }

    public int getReinforceLevel() {
        return reinforceLevel;
    }

    public int getRefineLevel() {
        return refineLevel;
    }

    public int getGlyphsLevel() {
        return glyphsLevel;
    }

    @Override
    public int compareTo(LineupWear o) {
        if (this.quality > o.quality) {
            return -1;
        } else if (this.quality < o.quality) {
            return 1;
        } else {
            if (this.refineLevel > o.refineLevel) {
                return -1;
            } else if (this.refineLevel < o.refineLevel) {
                return 1;
            } else {
                if (this.reinforceLevel > o.reinforceLevel) {
                    return -1;
                } else if (this.reinforceLevel < o.reinforceLevel) {
                    return 1;
                } else {
                    if (this.id < o.id) {
                        return -1;
                    } else {
                        return 1;
                    }
                }
            }
        }
    }

    @Override
    public String toString() {
        return "[id:" + id + ", quality:" + quality + ", reinforceLevel:" + reinforceLevel + ", refineLevel:" + refineLevel + ", glyphsLevel:" + glyphsLevel + "]";
    }
}
