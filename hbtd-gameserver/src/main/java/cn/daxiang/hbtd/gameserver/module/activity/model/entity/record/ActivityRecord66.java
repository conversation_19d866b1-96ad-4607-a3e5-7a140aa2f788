package cn.daxiang.hbtd.gameserver.module.activity.model.entity.record;

public class ActivityRecord66 {
    /**
     * 最后一次重置时间
     */
    private long lastResetTime;

    public static ActivityRecord66 valueOf() {
        ActivityRecord66 model = new ActivityRecord66();
        model.lastResetTime = System.currentTimeMillis();
        return model;
    }

    public long getLastResetTime() {
        return lastResetTime;
    }

    public void setLastResetTime(long lastResetTime) {
        this.lastResetTime = lastResetTime;
    }

    public void reset() {
        this.lastResetTime = System.currentTimeMillis();
    }
}
