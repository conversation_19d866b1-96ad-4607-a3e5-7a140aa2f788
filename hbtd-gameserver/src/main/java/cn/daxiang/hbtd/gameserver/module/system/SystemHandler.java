package cn.daxiang.hbtd.gameserver.module.system;

import cn.daxiang.framework.dispatch.DispatchType;
import cn.daxiang.framework.protocal.DataPacket;
import cn.daxiang.framework.router.annotation.Cmd;
import cn.daxiang.hbtd.gameserver.core.router.GatewayRouterHandlerImpl;
import cn.daxiang.hbtd.gameserver.module.system.helper.SettingsHelper;
import cn.daxiang.hbtd.gameserver.module.user.helper.ActorHelper;
import cn.daxiang.protocol.game.ModuleProtocol.Module;
import cn.daxiang.protocol.game.SystemProtocol.ServerMaxLevelResponse;
import cn.daxiang.protocol.game.SystemProtocol.ServerStartDaysResponse;
import cn.daxiang.protocol.game.SystemProtocol.ServerWorldLevelResponse;
import cn.daxiang.protocol.game.SystemProtocol.SettingResponse;
import cn.daxiang.protocol.game.SystemProtocol.SystemCmd;
import cn.daxiang.protocol.game.SystemProtocol.ZoneOpenDayResponse;
import cn.daxiang.protocol.game.TypeProtocol.SettingType;
import io.netty.channel.Channel;
import org.springframework.stereotype.Component;

@Component
public class SystemHandler extends GatewayRouterHandlerImpl {

    @Override
    public int getModule() {
        return Module.SYSTEM_VALUE;
    }

    @Cmd(Id = SystemCmd.GET_SETTING_VALUE, dispatchType = DispatchType.ACTOR)
    public void getSettings(Channel channel, Long actorId, DataPacket packet) {
        SettingResponse.Builder builder = SettingResponse.newBuilder();
        builder.setServerStartTime(SettingsHelper.getSettingValue(SettingType.SERVER_START_TIME));
        builder.setMergeServerTime(SettingsHelper.getSettingValue(SettingType.MERGE_SERVER_TIME));
        SettingResponse response = builder.build();
        channelWrite(channel, packet, response);
    }

    @Cmd(Id = SystemCmd.GET_SERVER_START_DAYS_VALUE, dispatchType = DispatchType.ACTOR)
    public void getServerStartDays(Channel channel, Long actorId, DataPacket packet) {
        ServerStartDaysResponse.Builder builder = ServerStartDaysResponse.newBuilder();
        builder.setDays(SettingsHelper.getServerStartDays());
        ServerStartDaysResponse response = builder.build();
        channelWrite(channel, packet, response);
    }

    @Cmd(Id = SystemCmd.GET_SERVER_MAX_LEVEL_VALUE, dispatchType = DispatchType.ACTOR)
    public void getServerMaxLevel(Channel channel, Long actorId, DataPacket packet) {
        ServerMaxLevelResponse.Builder builder = ServerMaxLevelResponse.newBuilder();
        builder.setLevel(ActorHelper.getServerMaxLevel());
        ServerMaxLevelResponse response = builder.build();
        channelWrite(channel, packet, response);
    }

    @Cmd(Id = SystemCmd.GET_SERVER_WORLD_LEVEL_VALUE, dispatchType = DispatchType.ACTOR)
    public void getServerWorldLevel(Channel channel, Long actorId, DataPacket packet) {
        ServerWorldLevelResponse.Builder builder = ServerWorldLevelResponse.newBuilder();
        builder.setLevel(ActorHelper.getServerWorldLevel());
        ServerWorldLevelResponse response = builder.build();
        channelWrite(channel, packet, response);
    }

    @Cmd(Id = SystemCmd.GET_ZONE_OPEN_DAY_VALUE, dispatchType = DispatchType.ACTOR)
    public void getZoneOpenDay(Channel channel, Long actorId, DataPacket packet) {
        ZoneOpenDayResponse.Builder builder = ZoneOpenDayResponse.newBuilder();
        builder.setDay(ActorHelper.getZoneOpenDay());
        ZoneOpenDayResponse response = builder.build();
        channelWrite(channel, packet, response);
    }
}
