package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.dataconfig.annotation.FieldIgnore;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.shared.reward.RewardObject;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;

import java.util.Collection;

/**
 * 心愿礼物奖励配置
 * <AUTHOR>
 * @date 2024/5/17
 */
@DataFile(fileName = "activity_68_gift_reward_config")
public class Activity68GiftRewardConfig implements ModelAdapter {
    /**
     * 活动data
     */
    private int data;
    /**
     * day
     */
    private int day;
    /**
     * 免费奖励
     */
    private String normalRewards;
    /**
     * 付费奖励
     */
    private String supremeRewards;

    @FieldIgnore
    private final Collection<RewardObject> normalRewardList = Lists.newArrayList();

    @FieldIgnore
    private final Collection<RewardObject> supremeRewardList = Lists.newArrayList();

    @Override
    public void initialize() {
        JSONArray normalRewardArray = JSONObject.parseArray(normalRewards);
        for (Object reward : normalRewardArray) {
            JSONArray rewardArray = JSONObject.parseArray(reward.toString());
            normalRewardList.add(RewardObject.valueOf(rewardArray));
        }

        JSONArray supremeRewardArray = JSONObject.parseArray(supremeRewards);
        for (Object reward : supremeRewardArray) {
            JSONArray rewardArray = JSONObject.parseArray(reward.toString());
            supremeRewardList.add(RewardObject.valueOf(rewardArray));
        }

    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(data,day);
    }

    public int getData() {
        return data;
    }

    public int getDay() {
        return day;
    }

    public Collection<RewardObject> getNormalRewardList() {
        return normalRewardList;
    }

    public Collection<RewardObject> getSupremeRewardList() {
        return supremeRewardList;
    }
}
