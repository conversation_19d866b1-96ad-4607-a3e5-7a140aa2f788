package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.dataconfig.annotation.FieldIgnore;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.shared.reward.RewardObject;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;

import java.util.Collection;

/**
 * 活动-17-材料超值礼包表
 *
 * <AUTHOR>
 * @date 2022/8/23
 */
@DataFile(fileName = "material_gift_bag_config")
public class MaterialGiftBagConfig implements ModelAdapter {
    private int data;
    /**
     * 配置Id
     */
    private int id;
    /**
     * 礼包类型
     */
    private int giftType;
    /**
     * 上一阶段
     */
    private int lastStage;
    /**
     * 当前阶段
     */
    private int stage;
    /**
     * 贵族等级需求
     */
    private int adelstitel;
    /**
     * 奖励
     */
    private String reward;
    /**
     * 消耗
     */
    private String cost;
    /**
     * 等级限制
     */
    private int levelLimit;
    /**
     * 奖励列表
     */
    @FieldIgnore
    private Collection<RewardObject> rewardList = Lists.newArrayList();
    /**
     * 消耗列表
     */
    @FieldIgnore
    private Collection<RewardObject> costList = Lists.newArrayList();

    @Override
    public void initialize() {
        JSONArray rewardArray = JSONArray.parseArray(reward);
        for (Object rewardItem : rewardArray) {
            JSONArray array = JSONArray.parseArray(rewardItem.toString());
            RewardObject rewardObject = RewardObject.valueOf(array);
            rewardList.add(rewardObject);
        }

        JSONArray costArray = JSONArray.parseArray(cost);
        for (Object costItem : costArray) {
            JSONArray array = JSONArray.parseArray(costItem.toString());
            RewardObject rewardObject = RewardObject.valueOf(array);
            costList.add(rewardObject);
        }
    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(data, id);
    }

    public int getData() {
        return data;
    }

    public int getId() {
        return id;
    }

    public int getGiftType() {
        return giftType;
    }

    public int getLastStage() {
        return lastStage;
    }

    public int getStage() {
        return stage;
    }

    public int getAdelstitel() {
        return adelstitel;
    }

    public int getLevelLimit() {
        return levelLimit;
    }

    public Collection<RewardObject> getRewardList() {
        return rewardList;
    }

    public Collection<RewardObject> getCostList() {
        return costList;
    }
}
