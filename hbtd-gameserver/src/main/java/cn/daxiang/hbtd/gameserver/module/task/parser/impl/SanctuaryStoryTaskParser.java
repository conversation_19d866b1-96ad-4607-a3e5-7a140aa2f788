package cn.daxiang.hbtd.gameserver.module.task.parser.impl;

import cn.daxiang.hbtd.gameserver.core.database.table.Task;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.TaskConfig;
import cn.daxiang.hbtd.gameserver.core.event.impl.SanctuaryStoryTaskEvent;
import cn.daxiang.hbtd.gameserver.module.task.parser.AbstractTaskParser;
import cn.daxiang.hbtd.gameserver.module.task.type.TaskConditionType;
import cn.daxiang.protocol.game.TaskProtocol;
import org.springframework.stereotype.Component;

/**
 * 归藏秘境-通关指定关卡
 *
 * <AUTHOR>
 * @date 2023/9/7
 */
@Component
public class SanctuaryStoryTaskParser extends AbstractTaskParser<SanctuaryStoryTaskEvent> {
    @Override
    protected TaskConditionType getType() {
        return TaskConditionType.SANCTUARY_STORY;
    }

    @Override
    protected boolean parseCondition(SanctuaryStoryTaskEvent event, Task task, TaskConfig taskConfig) {
        task.setValue(event.storyId);
        if (task.getValue() >= taskConfig.getValue()) {
            task.setStatus(TaskProtocol.TaskStatus.TASK_FINISH);
        }
        return true;
    }

    @Override
    public void init(Task task, TaskConfig taskConfig) {
        if (task.getValue() >= taskConfig.getValue()) {
            task.setStatus(TaskProtocol.TaskStatus.TASK_FINISH);
        }
    }
}
