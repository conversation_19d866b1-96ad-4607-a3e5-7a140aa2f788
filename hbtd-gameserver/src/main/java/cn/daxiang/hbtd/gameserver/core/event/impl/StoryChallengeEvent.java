package cn.daxiang.hbtd.gameserver.core.event.impl;

import cn.daxiang.framework.event.ActorEvent;
import cn.daxiang.shared.event.EventKey;

/**
 * 挑战关卡事件
 *
 * <AUTHOR>
 */
public class StoryChallengeEvent extends ActorEvent {
    /**
     * 关卡类型
     */
    public int chapterType;
    /**
     * 关卡ID
     */
    public int storyId;
    /**
     * 扫荡次数
     */
    public int times;
    /**
     * 是否扫荡
     */
    public boolean isSweep;

    public StoryChallengeEvent(long actorId, int chapterType, int storyId, boolean isSweep, int times) {
        super(EventKey.STORY_CHALLENGE_EVENT, actorId);
        this.chapterType = chapterType;
        this.storyId = storyId;
        this.isSweep = isSweep;
        this.times = times;
    }

}
