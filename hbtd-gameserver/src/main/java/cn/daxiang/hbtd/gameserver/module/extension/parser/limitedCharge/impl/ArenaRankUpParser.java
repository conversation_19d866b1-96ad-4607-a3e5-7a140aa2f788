package cn.daxiang.hbtd.gameserver.module.extension.parser.limitedCharge.impl;

import cn.daxiang.framework.result.TResult;
import cn.daxiang.framework.utils.FormulaUtils;
import cn.daxiang.hbtd.gameserver.core.database.table.LimitedTimeCharge;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.LimitedTimeChargeConfig;
import cn.daxiang.hbtd.gameserver.core.event.impl.ArenaChallengeMaxLevelEvent;
import cn.daxiang.hbtd.gameserver.module.extension.model.LimitedTimeChargeEntity;
import cn.daxiang.hbtd.gameserver.module.extension.parser.limitedCharge.AbstractLimitedChargeParser;
import cn.daxiang.hbtd.gameserver.module.extension.type.LimitedTimeChargeType;
import cn.daxiang.hbtd.gameserver.module.user.helper.ActorHelper;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/6/10
 */
@Component
public class ArenaRankUpParser extends AbstractLimitedChargeParser<ArenaChallengeMaxLevelEvent> {
    @Override
    protected LimitedTimeChargeType getType() {
        return LimitedTimeChargeType.ARENA_RANK_UP;
    }

    @Override
    protected TResult<LimitedTimeChargeEntity> parseCondition(ArenaChallengeMaxLevelEvent event, LimitedTimeChargeConfig config, LimitedTimeCharge limitedTimeCharge) {
        int actorLevel = ActorHelper.getActorLevel(event.getActorId());
        int actorVipLevel = ActorHelper.getVipLevel(event.getActorId());
        long actorMaxPower = ActorHelper.getActorMaxPower(event.getActorId());
        if (FormulaUtils.executeBool(config.getCondition(), actorLevel, actorVipLevel, actorMaxPower, event.rank)) {
            return TResult.sucess(limitedTimeCharge.addEntity(config));
        }
        return TResult.fail();
    }
}
