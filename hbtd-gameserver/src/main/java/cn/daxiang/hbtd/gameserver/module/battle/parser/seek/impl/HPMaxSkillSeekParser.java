package cn.daxiang.hbtd.gameserver.module.battle.parser.seek.impl;

import cn.daxiang.hbtd.gameserver.core.dataconfig.model.SkillSeekConfig;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleRoom;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleSprite;
import cn.daxiang.hbtd.gameserver.module.battle.parser.seek.AbstractSkillSeekParser;
import cn.daxiang.hbtd.gameserver.module.battle.type.SkillSeekType;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;

@Component
public class HPMaxSkillSeekParser extends AbstractSkillSeekParser {

    private static SpriteHPMaxComparator COMPARATOR = new SpriteHPMaxComparator();

    @Override
    protected SkillSeekType getType() {
        return SkillSeekType.HP_MAX;
    }

    @Override
    protected List<BattleSprite> seekSprites(BattleSprite attacker, BattleRoom battleRoom, SkillSeekConfig skillSeekConfig) {
        List<BattleSprite> seekSprites = Lists.newArrayList();
        List<BattleSprite> targetSprites = this.getTargetSpriteList(attacker, battleRoom, skillSeekConfig);
        int targetCount = Math.min(targetSprites.size(), skillSeekConfig.getTargetCount());
        Collections.sort(targetSprites, COMPARATOR);
        for (int i = 0; i < targetCount; i++) {
            BattleSprite targetSprite = targetSprites.remove(0);
            seekSprites.add(targetSprite);
        }
        return seekSprites;
    }
}

class SpriteHPMaxComparator implements Comparator<BattleSprite> {

    @Override
    public int compare(BattleSprite o1, BattleSprite o2) {
        long o1HP = o1.getSpriteBattle().getHP();
        long o2HP = o2.getSpriteBattle().getHP();
        if (o1HP > o2HP) {
            return -1;
        } else if (o1HP < o2HP) {
            return 1;
        } else {
            if (o1.getRoleId() < o2.getRoleId()) {
                return -1;
            } else if (o1.getRoleId() > o2.getRoleId()) {
                return 1;
            }
        }
        return 0;
    }
}
