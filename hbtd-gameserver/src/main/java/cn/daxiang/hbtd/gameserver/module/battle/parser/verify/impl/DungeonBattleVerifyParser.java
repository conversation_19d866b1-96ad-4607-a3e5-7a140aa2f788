package cn.daxiang.hbtd.gameserver.module.battle.parser.verify.impl;

import cn.daxiang.framework.result.TResult;
import cn.daxiang.hbtd.gameserver.module.battle.parser.verify.AbstractBattleVerifyParser;
import cn.daxiang.hbtd.gameserver.module.dungeon.facade.DungeonFacade;
import cn.daxiang.hbtd.gameserver.module.dungeon.helper.DungeonPushHelper;
import cn.daxiang.protocol.game.CommonProtocol;
import cn.daxiang.protocol.game.DungeonProtocol;
import cn.daxiang.shared.type.PVEVerifyParameterKey;
import cn.daxiang.shared.type.PVEVerifyType;
import com.google.protobuf.InvalidProtocolBufferException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/4/27
 */
@Component
public class DungeonBattleVerifyParser extends AbstractBattleVerifyParser {
    @Autowired
    private DungeonFacade dungeonFacade;

    @Override
    protected PVEVerifyType getType() {
        return PVEVerifyType.DUNGEON;
    }

    @Override
    protected String getClientData(byte[] data) throws InvalidProtocolBufferException {
        DungeonProtocol.DungeonChallengeRequest request = DungeonProtocol.DungeonChallengeRequest.parseFrom(data);
        return request.getCheck();
    }

    @Override
    protected String getBattleRequest(byte[] data) throws InvalidProtocolBufferException {
        DungeonProtocol.DungeonChallengeRequest request = DungeonProtocol.DungeonChallengeRequest.parseFrom(data);
        return request.toString();
    }

    @Override
    protected void verifyResult(long actorId, Map<PVEVerifyParameterKey, Object> parameter, boolean result) throws InvalidProtocolBufferException {
        DungeonProtocol.DungeonChallengeRequest request = DungeonProtocol.DungeonChallengeRequest.parseFrom((byte[]) parameter.get(PVEVerifyParameterKey.BATTLE_VERIFY_REQUEST));
        TResult<CommonProtocol.RewardResult> battleResult = dungeonFacade.verifyResult(actorId, request, result);
        DungeonPushHelper.pushDungeonBattleResult(actorId, battleResult);
    }
}
