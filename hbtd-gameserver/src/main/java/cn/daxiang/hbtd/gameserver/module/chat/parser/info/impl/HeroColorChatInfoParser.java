package cn.daxiang.hbtd.gameserver.module.chat.parser.info.impl;

import cn.daxiang.framework.event.GameEvent;
import cn.daxiang.hbtd.gameserver.core.event.impl.HeroColorLevelUpEvent;
import cn.daxiang.hbtd.gameserver.module.chat.helper.ChatHelper;
import cn.daxiang.hbtd.gameserver.module.chat.model.ChatInfoReceiver;
import cn.daxiang.hbtd.gameserver.module.chat.parser.info.AbstractChatInfoParser;
import cn.daxiang.protocol.game.ChatInfoProtocol.HeroColorChatInfo;
import cn.daxiang.protocol.game.ChatProtocol.ChatInfo;
import cn.daxiang.protocol.game.TypeProtocol.ChatChannelType;
import cn.daxiang.protocol.game.TypeProtocol.ChatInfoType;
import cn.daxiang.shared.event.EventKey;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Component
public class HeroColorChatInfoParser extends AbstractChatInfoParser {

    @Override
    public Map<ChatInfoReceiver, ChatInfo> parse(GameEvent e) {
        HeroColorLevelUpEvent event = e.convert();
        if (event.level != 0) {
            return Collections.emptyMap();
        }
        Map<ChatInfoReceiver, ChatInfo> chatMap = Maps.newHashMap();
        HeroColorChatInfo heroColorChatInfo = HeroColorChatInfo.newBuilder().setHeroId(event.heroId).build();
        Collection<Long> actorIds = Lists.newLinkedList();
        actorIds.add(event.actorId);
        ChatInfo chatInfo = ChatHelper.buildChatInfo(ChatInfoType.HERO_COLOR_CHAT_INFO, event.getActorId(), heroColorChatInfo.toByteArray(), actorIds);
        chatMap.put(ChatInfoReceiver.valueOf(ChatChannelType.CHAT_CHANNEL_TYPE_SYSTEM), chatInfo);
        return chatMap;
    }

    @Override
    protected List<String> getEventName() {
        return Lists.newArrayList(EventKey.HERO_COLOR_LEVEL_UP_EVENT);
    }

}
