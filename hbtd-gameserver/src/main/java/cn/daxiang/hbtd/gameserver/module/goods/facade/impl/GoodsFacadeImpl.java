package cn.daxiang.hbtd.gameserver.module.goods.facade.impl;

import cn.daxiang.framework.context.SpringContext;
import cn.daxiang.framework.database.dao.BaseDao;
import cn.daxiang.framework.event.annotation.Event;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.framework.result.CollectionResult;
import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.framework.rpc.RpcCall;
import cn.daxiang.framework.rpc.world.WorldAuctionRpc;
import cn.daxiang.framework.rpc.world.WorldQinDungeonRpc;
import cn.daxiang.framework.utils.RandomUtils;
import cn.daxiang.framework.utils.TimeUtils;
import cn.daxiang.hbtd.gameserver.core.GameBaseFacade;
import cn.daxiang.hbtd.gameserver.core.GameConfig;
import cn.daxiang.hbtd.gameserver.core.database.table.Arsenal;
import cn.daxiang.hbtd.gameserver.core.database.table.BattlefieldDrill;
import cn.daxiang.hbtd.gameserver.core.database.table.Beauty;
import cn.daxiang.hbtd.gameserver.core.database.table.Cave;
import cn.daxiang.hbtd.gameserver.core.database.table.Chess;
import cn.daxiang.hbtd.gameserver.core.database.table.Dungeon;
import cn.daxiang.hbtd.gameserver.core.database.table.Goods;
import cn.daxiang.hbtd.gameserver.core.database.table.Hero;
import cn.daxiang.hbtd.gameserver.core.database.table.Lineup;
import cn.daxiang.hbtd.gameserver.core.database.table.Mount;
import cn.daxiang.hbtd.gameserver.core.database.table.NationActor;
import cn.daxiang.hbtd.gameserver.core.database.table.QinDungeon;
import cn.daxiang.hbtd.gameserver.core.database.table.QinRace;
import cn.daxiang.hbtd.gameserver.core.database.table.Reputation;
import cn.daxiang.hbtd.gameserver.core.database.table.RunesLineup;
import cn.daxiang.hbtd.gameserver.core.database.table.SanctuaryActor;
import cn.daxiang.hbtd.gameserver.core.database.table.SignIn;
import cn.daxiang.hbtd.gameserver.core.database.table.StoryChapter;
import cn.daxiang.hbtd.gameserver.core.database.table.SuppressDemon;
import cn.daxiang.hbtd.gameserver.core.database.table.Task;
import cn.daxiang.hbtd.gameserver.core.database.table.TeamOnlineTD;
import cn.daxiang.hbtd.gameserver.core.database.table.Tower;
import cn.daxiang.hbtd.gameserver.core.database.table.TowerRecover;
import cn.daxiang.hbtd.gameserver.core.dataconfig.convert.impl.RewardObjectListConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.BeautyConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.CaveHardChallengeShowConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.FragmentConvertConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.GoodsConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.HeroConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.MountConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.QinDungeonDanConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.QinDungeonRandStageConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.QinDungeonRougueEffectConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.ReputationEventConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.SanctuaryStageConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.StoryConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.SuppressDemonConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.SynthesisConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.TaskConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.TitleConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.BeautyConfigService;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.CaveConfigService;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.ChessConfigService;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.HeroConfigService;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.MountConfigService;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.QinDungeonConfigService;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.SanctuaryConfigService;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.SignInConfigService;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.StoryConfigService;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.SuppressDemonConfigService;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.TaskConfigService;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.TeamOnlineTDConfigService;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.TitleConfigService;
import cn.daxiang.hbtd.gameserver.core.dispatch.DispatchHelper;
import cn.daxiang.hbtd.gameserver.core.event.impl.GoodsDeleteEvent;
import cn.daxiang.hbtd.gameserver.core.event.impl.GoodsGetEvent;
import cn.daxiang.hbtd.gameserver.core.event.impl.LineupPowerRefreshEvent;
import cn.daxiang.hbtd.gameserver.core.oss.GameOssLogger;
import cn.daxiang.hbtd.gameserver.core.oss.type.OperationType;
import cn.daxiang.hbtd.gameserver.core.rpc.WorldRpcHelper;
import cn.daxiang.hbtd.gameserver.module.arsenal.facade.ArsenalFacade;
import cn.daxiang.hbtd.gameserver.module.arsenal.helper.ArsenalPushHelper;
import cn.daxiang.hbtd.gameserver.module.battlefieldDrill.facade.BattlefieldDrillFacade;
import cn.daxiang.hbtd.gameserver.module.battlefieldDrill.helper.BattlefieldDrillPushHelper;
import cn.daxiang.hbtd.gameserver.module.battlefieldDrill.model.entity.BattlefieldDrillHeroData;
import cn.daxiang.hbtd.gameserver.module.battlefieldDrill.model.entity.BattlefieldDrillMonsterData;
import cn.daxiang.hbtd.gameserver.module.cave.facade.CaveFacade;
import cn.daxiang.hbtd.gameserver.module.cave.helper.CavePushHelper;
import cn.daxiang.hbtd.gameserver.module.cave.model.entity.CaveHardMonsterInfo;
import cn.daxiang.hbtd.gameserver.module.chess.facade.ChessFacade;
import cn.daxiang.hbtd.gameserver.module.chess.helper.ChessHelper;
import cn.daxiang.hbtd.gameserver.module.chess.helper.ChessPushHelper;
import cn.daxiang.hbtd.gameserver.module.dungeon.facade.DungeonFacade;
import cn.daxiang.hbtd.gameserver.module.dungeon.helper.DungeonPushHelper;
import cn.daxiang.hbtd.gameserver.module.extension.facade.SignInFacade;
import cn.daxiang.hbtd.gameserver.module.extension.helper.ExtensionPushHelper;
import cn.daxiang.hbtd.gameserver.module.goods.dao.GoodsDao;
import cn.daxiang.hbtd.gameserver.module.goods.facade.GoodsFacade;
import cn.daxiang.hbtd.gameserver.module.goods.helper.GoodsPushHelper;
import cn.daxiang.hbtd.gameserver.module.goods.helper.RewardHelper;
import cn.daxiang.hbtd.gameserver.module.goods.parser.effect.GoodsEffectContext;
import cn.daxiang.hbtd.gameserver.module.goods.parser.effect.GoodsEffectParser;
import cn.daxiang.hbtd.gameserver.module.goods.type.GoodsEffectType;
import cn.daxiang.hbtd.gameserver.module.goods.type.GoodsSubType;
import cn.daxiang.hbtd.gameserver.module.goods.type.GoodsType;
import cn.daxiang.hbtd.gameserver.module.hero.facade.HeroFacade;
import cn.daxiang.hbtd.gameserver.module.hero.helper.HeroPushHelper;
import cn.daxiang.hbtd.gameserver.module.lineup.facade.LineupFacade;
import cn.daxiang.hbtd.gameserver.module.lineup.helper.LineupPushHelper;
import cn.daxiang.hbtd.gameserver.module.lineup.model.entity.BeastEntity;
import cn.daxiang.hbtd.gameserver.module.lineup.model.entity.LineupEntity;
import cn.daxiang.hbtd.gameserver.module.mount.facade.MountFacade;
import cn.daxiang.hbtd.gameserver.module.nation.facade.NationFacade;
import cn.daxiang.hbtd.gameserver.module.nation.helper.NationHelper;
import cn.daxiang.hbtd.gameserver.module.nation.helper.NationPushHelper;
import cn.daxiang.hbtd.gameserver.module.qindungeon.dao.QinDungeonDao;
import cn.daxiang.hbtd.gameserver.module.qindungeon.facade.QinDungeonFacade;
import cn.daxiang.hbtd.gameserver.module.qindungeon.helper.QinDungeonHelper;
import cn.daxiang.hbtd.gameserver.module.qindungeon.helper.QinDungeonPushHelper;
import cn.daxiang.hbtd.gameserver.module.qinrace.facade.QinRaceFacade;
import cn.daxiang.hbtd.gameserver.module.qinrace.helper.QinRacePushHelper;
import cn.daxiang.hbtd.gameserver.module.qinrace.type.QinRaceType;
import cn.daxiang.hbtd.gameserver.module.reincarnation.facade.ReincarnationFacade;
import cn.daxiang.hbtd.gameserver.module.reputation.facade.ReputationFacade;
import cn.daxiang.hbtd.gameserver.module.reputation.helper.ReputationHelper;
import cn.daxiang.hbtd.gameserver.module.reputation.helper.ReputationPushHelper;
import cn.daxiang.hbtd.gameserver.module.reputation.model.EventInfoEntity;
import cn.daxiang.hbtd.gameserver.module.runes.facade.RunesLineupFacade;
import cn.daxiang.hbtd.gameserver.module.runes.helper.RunesPushHelper;
import cn.daxiang.hbtd.gameserver.module.sanctuary.facade.SanctuaryActorFacade;
import cn.daxiang.hbtd.gameserver.module.sanctuary.helper.SanctuaryPushHelper;
import cn.daxiang.hbtd.gameserver.module.story.facade.StoryFacade;
import cn.daxiang.hbtd.gameserver.module.story.model.entity.StoryEntity;
import cn.daxiang.hbtd.gameserver.module.story.type.StoryChapterType;
import cn.daxiang.hbtd.gameserver.module.suppressDemon.facade.SuppressDemonFacade;
import cn.daxiang.hbtd.gameserver.module.suppressDemon.helper.SuppressDemonPushHelper;
import cn.daxiang.hbtd.gameserver.module.system.dao.SettingsDao;
import cn.daxiang.hbtd.gameserver.module.task.facade.TaskFacade;
import cn.daxiang.hbtd.gameserver.module.task.helper.TaskPushHelper;
import cn.daxiang.hbtd.gameserver.module.teamOnlineTD.facade.TeamOnlineTDFacade;
import cn.daxiang.hbtd.gameserver.module.teamOnlineTD.helper.TeamOnlineTDHelper;
import cn.daxiang.hbtd.gameserver.module.title.facade.TitleFacade;
import cn.daxiang.hbtd.gameserver.module.tower.facade.TowerFacade;
import cn.daxiang.hbtd.gameserver.module.tower.helper.TowerPushHelper;
import cn.daxiang.hbtd.gameserver.module.user.helper.ActorHelper;
import cn.daxiang.protocol.game.CommonProtocol;
import cn.daxiang.protocol.game.CommonProtocol.RewardResult;
import cn.daxiang.protocol.game.CommonProtocol.RewardResultResponse;
import cn.daxiang.protocol.game.DungeonProtocol;
import cn.daxiang.protocol.game.GoodsProtocol.UseGoodsResponse;
import cn.daxiang.protocol.game.ModuleProtocol.Module;
import cn.daxiang.protocol.game.ReputationProtocol;
import cn.daxiang.protocol.game.TaskProtocol;
import cn.daxiang.protocol.game.TypeProtocol;
import cn.daxiang.protocol.game.TypeProtocol.RewardType;
import cn.daxiang.shared.GlobalConfigKey;
import cn.daxiang.shared.event.EventKey;
import cn.daxiang.shared.module.chess.model.entity.MonsterLineup;
import cn.daxiang.shared.module.qindungeon.QinDungeonWorldRankVO;
import cn.daxiang.shared.module.sanctuary.SanctuaryRegionState;
import cn.daxiang.shared.reward.RewardObject;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;

@Component
public class GoodsFacadeImpl extends GameBaseFacade implements GoodsFacade {
    @Autowired
    private GoodsDao goodsDao;
    @Autowired
    private GoodsEffectContext goodsEffectContext;
    @Autowired
    private LineupFacade lineupFacade;
    @Autowired
    private StoryFacade storyFacade;
    @Autowired
    private TowerFacade towerFacade;
    @Autowired
    private QinDungeonFacade qinDungeonFacade;
    @Autowired
    private QinDungeonDao qinDungeonDao;
    @Autowired
    private DungeonFacade dungeonFacade;
    @Autowired
    private CaveFacade caveFacade;
    @Autowired
    private BattlefieldDrillFacade battlefieldDrillFacade;
    @Autowired
    private SignInFacade signInFacade;
    @Autowired
    private ArsenalFacade arsenalFacade;
    @Autowired
    private TeamOnlineTDFacade teamOnlineTDFacade;
    @Autowired
    private TitleFacade titleFacade;
    @Autowired
    private TaskFacade taskFacade;
    @Autowired
    private RunesLineupFacade runesLineupFacade;
    @Autowired
    private NationFacade nationFacade;
    @Autowired
    private SuppressDemonFacade suppressDemonFacade;
    @Autowired
    private ReputationFacade reputationFacade;
    @Autowired
    private ChessFacade chessFacade;
    @Autowired
    private QinRaceFacade qinRaceFacade;
    @Autowired
    private HeroFacade heroFacade;
    @Autowired
    private SanctuaryActorFacade sanctuaryActorFacade;
    @Autowired
    private SettingsDao settingsDao;
    @Autowired
    private ReincarnationFacade reincarnationFacade;
    @Autowired
    private MountFacade mountFacade;

    @Override
    public CollectionResult<Goods> getGoodsList(long actorId) {
        Map<IdentiyKey, Goods> goodsMap = goodsDao.findGoodsBag(actorId);
        return CollectionResult.collection(goodsMap.values());
    }

    @Override
    public TResult<Goods> getGoods(long actorId, long goodsUid) {
        Goods goods = goodsDao.getGoods(actorId, goodsUid);
        if (goods == null) {
            return TResult.valueOf(GOODS_NOT_FOUND);
        }
        return TResult.sucess(goods);
    }

    @Override
    public TResult<UseGoodsResponse> useGoods(long actorId, long goodsUid, long num, byte[] value) {
        if (num <= 0 || num > GameConfig.getClientCountLimit()) {
            return TResult.valueOf(INVALID_PARAM);
        }
        TResult<Goods> result = this.getGoods(actorId, goodsUid);
        if (result.isFail()) {
            return TResult.valueOf(result.statusCode);
        }
        Goods goods = result.item;
        GoodsConfig goodsConfig = globalConfigService.findConfig(goods.getGoodsId(), GoodsConfig.class);
        if (goodsConfig == null || goodsConfig.getGoodsType() != GoodsType.USE || goodsConfig.getGoodsEffectType() == GoodsEffectType.NONE) {
            return TResult.valueOf(CONFIG_NOT_FOUND);
        }
        GoodsEffectParser parser = goodsEffectContext.getParser(goodsConfig.getGoodsEffectType());
        if (parser == null) {
            LOGGER.error("GoodsEffectParser not found,goodsId:{},effectType:{}", goodsConfig.getGoodsId(), goodsConfig.getGoodsEffectType());
            return TResult.valueOf(USE_GOODS_ERROR);
        }
        boolean isExpiration = goods.getExpirationTime() > 0 && goods.getExpirationTime() < System.currentTimeMillis();
        if (!isExpiration) {
            Result unlockResult = parser.unlock(actorId, goodsConfig);
            if (unlockResult.isFail()) {
                return TResult.valueOf(unlockResult.statusCode);
            }
        }
        Result conditionResult = parser.useNumCondition(actorId, num, goodsConfig);
        if (conditionResult.isFail()) {
            return TResult.valueOf(conditionResult.statusCode);
        }
        TResult<Goods> decreaseGoodsResult = this.decreaseGoods(actorId, goodsUid, num, OperationType.GOODS_USE);
        if (decreaseGoodsResult.isFail()) {
            return TResult.valueOf(decreaseGoodsResult.statusCode);
        }

        RewardResult rewardResult;
        if (isExpiration) {
            List<RewardObject> rewardList = globalConfigService.findGlobalObject(GlobalConfigKey.GOODS_EXPIRATION_REWARD, RewardObjectListConfig.class).getVs();
            rewardResult = RewardHelper.sendRewardList(actorId, rewardList, OperationType.GOODS_USE);
        } else {
            TResult<RewardResult> executeResult = parser.execute(actorId, num, goodsConfig, value, OperationType.GOODS_USE);
            if (executeResult.isFail()) {
                return TResult.valueOf(executeResult.statusCode);
            } else {
                rewardResult = executeResult.item;
            }
        }

        UseGoodsResponse response = UseGoodsResponse.newBuilder().setGoodsId(goods.getGoodsId()).setCount(num).setRewardResult(rewardResult).build();

        return TResult.sucess(response);
    }

    @Override
    public TResult<RewardResultResponse> convertFragment(long actorId, int fragmentId, int goodsId, long num) {
        if (num <= 0 || num > GameConfig.getClientCountLimit()) {
            return TResult.valueOf(INVALID_PARAM);
        }
        GoodsConfig goodsConfig = globalConfigService.findConfig(fragmentId, GoodsConfig.class);
        if (goodsConfig == null) {
            return TResult.valueOf(CONFIG_NOT_FOUND);
        }
        FragmentConvertConfig fragmentConvertConfig = globalConfigService.findConfig(fragmentId, FragmentConvertConfig.class);
        if (goodsConfig == null) {
            return TResult.valueOf(CONFIG_NOT_FOUND);
        }
        Collection<Integer> goodsIds = fragmentConvertConfig.getConvertIdList();
        if (!goodsIds.contains(goodsId)) {
            return TResult.valueOf(GOODS_NOT_FOUND);
        }
        RewardObject rewardObject = RewardObject.valueOf(RewardType.GOODS.getNumber(), fragmentId, num);
        List<RewardObject> fragmentList = Lists.newArrayList(rewardObject);
        Result result = RewardHelper.decrease(actorId, fragmentList, OperationType.OMNIPOTENT_FRAGMENT_CONVERT);
        if (result.isFail()) {
            return TResult.valueOf(result);
        }
        RewardResult rewardResult =
            RewardHelper.sendRewardList(actorId, Lists.newArrayList(RewardObject.valueOf(RewardType.GOODS.getNumber(), goodsId, num)), OperationType.OMNIPOTENT_FRAGMENT_CONVERT);
        RewardResultResponse response = RewardResultResponse.newBuilder().setRewardResult(rewardResult).build();
        return TResult.sucess(response);
    }

    private Result addGoods(long actorId, int goodsId, long num, OperationType operationType, Collection<Goods> refreshList) {
        if (num <= 0) {
            return Result.valueOf(INVALID_PARAM);
        }
        GoodsConfig goodsConfig = globalConfigService.findConfig(goodsId, GoodsConfig.class);
        if (goodsConfig == null) {
            return Result.valueOf(CONFIG_NOT_FOUND);
        }
        Goods goods;
        if (goodsConfig.getMaxCount() > 0) {
            goods = goodsDao.findUnFullGoods(actorId, goodsId, goodsConfig.getMaxCount());
            if (goods == null) {
                goods = goodsDao.createGoods(actorId, goodsId, goodsConfig.getExpirationTime());
            }
            if (goods.getNum() + num > goodsConfig.getMaxCount()) {
                int addNum = goodsConfig.getMaxCount() - goods.getNum();
                goods.addNumber(goodsConfig.getMaxCount() - goods.getNum());
                GoodsPushHelper.pushGoodsList(Lists.newArrayList(goods), actorId);
                dbQueue.updateQueue(goods);
                refreshList.add(goods);
                GameOssLogger.goodsAdd(actorId, operationType, RewardType.GOODS, goods.getGoodsUid(), goodsId, num - addNum, num - addNum);
                return addGoods(actorId, goodsId, num - addNum, operationType, refreshList);
            } else {
                goods.addNumber(num);
            }
        } else {
            Collection<Goods> goodsList = goodsDao.getGoodsList(actorId, goodsId);
            if (goodsList.isEmpty()) {
                goods = goodsDao.createGoods(actorId, goodsId, goodsConfig.getExpirationTime());
            } else {
                goods = goodsList.iterator().next();
            }
            goods.addNumber(num);
        }
        GameOssLogger.goodsAdd(actorId, operationType, RewardType.GOODS, goods.getGoodsUid(), goodsId, num, num);
        dbQueue.updateQueue(goods);
        refreshList.add(goods);
        DispatchHelper.postEvent(new GoodsGetEvent(actorId, goodsId, num, operationType));
        return Result.valueOf();
    }

    @Override
    public Result addGoods(long actorId, Map<Integer, Long> data, OperationType operationType) {
        Collection<Goods> refreshList = Lists.newArrayList();
        for (Entry<Integer, Long> entry : data.entrySet()) {
            int id = entry.getKey();
            long count = entry.getValue();
            Result result = this.addGoods(actorId, id, count, operationType, refreshList);
            if (result.isFail()) {
                LOGGER.error("addGoods error actorId:{} id:{} count:{} operationType:{}", actorId, id, count, operationType);
                continue;
            }
        }
        Iterator<Goods> iterator = refreshList.iterator();
        while (iterator.hasNext()) {
            Goods goods = iterator.next();
            GoodsConfig goodsConfig = globalConfigService.findConfig(goods.getGoodsId(), GoodsConfig.class);
            if (goodsConfig == null) {
                LOGGER.error("GoodsConfig is not found! configId:{}", goods.getGoodsId());
                continue;
            }
            if (goodsConfig.getAutoUse()) {
                TResult<UseGoodsResponse> useGoodsResult = useGoods(actorId, goods.getGoodsUid(), goods.getNum(), null);
                if (useGoodsResult.isFail()) {
                    continue;
                }
                iterator.remove();
            }
        }
        GoodsPushHelper.pushGoodsList(refreshList, actorId);
        return Result.valueOf();
    }

    @Override
    public TResult<Goods> decreaseGoods(long actorId, long goodsUid, long num, OperationType operationType) {
        Result result = this.hasEnoughGoods(actorId, goodsUid, num);
        if (result.isFail()) {
            return TResult.valueOf(result);
        }
        Goods goods = goodsDao.getGoods(actorId, goodsUid);
        GameOssLogger.goodsDecrease(actorId, operationType, RewardType.GOODS, goods.getGoodsUid(), goods.getGoodsId(), num, goods.getNum());
        goods.addNumber(-num);
        GoodsConfig goodsConfig = globalConfigService.findConfig(goods.getGoodsId(), GoodsConfig.class);
        if (goodsConfig == null) {
            return TResult.valueOf(CONFIG_NOT_FOUND);
        }
        if (goods.getNum() <= 0 && goodsConfig.getGoodsEffectType() != GoodsEffectType.TIMES_BAG) {
            goodsDao.deleteGoods(actorId, goods.getGoodsUid());
        } else {
            dbQueue.updateQueue(goods);
        }
        GoodsPushHelper.pushGoodsList(Lists.newArrayList(goods), actorId);
        return TResult.sucess(goods);
    }

    @Override
    public Result decreaseGoods(long actorId, Map<Integer, Long> data, OperationType operationType) {
        Result result = this.hasEnoughGoods(actorId, data);
        if (result.isFail()) {
            return result;
        }
        Collection<Goods> changes = Lists.newArrayList();
        for (Entry<Integer, Long> entry : data.entrySet()) {
            int id = entry.getKey();
            long count = entry.getValue();
            List<Goods> goodsList = goodsDao.getGoodsList(actorId, id);
            Collections.sort(goodsList);
            for (Goods goods : goodsList) {
                if (count <= 0) {
                    break;
                }
                GameOssLogger.goodsDecrease(actorId, operationType, RewardType.GOODS, goods.getGoodsUid(), goods.getGoodsId(), count, goods.getNum());
                changes.add(goods);
                if (goods.getNum() <= count) {
                    count -= goods.getNum();
                    goods.addNumber(-goods.getNum());
                    goodsDao.deleteGoods(actorId, goods.getGoodsUid());
                } else {
                    goods.addNumber(-count);
                    dbQueue.updateQueue(goods);
                    break;
                }
            }
        }
        GoodsPushHelper.pushGoodsList(changes, actorId);
        return Result.valueOf();
    }

    @Override
    public Result hasEnoughGoods(long actorId, long goodsUid, long num) {
        Goods goods = goodsDao.getGoods(actorId, goodsUid);
        if (goods == null) {
            return Result.valueOf(GOODS_NOT_FOUND);
        }
        if (num < 0 || goods.getNum() < num) {
            return Result.valueOf(GOODS_NOT_ENOUGH);
        }
        return Result.valueOf();
    }

    @Override
    public Result hasEnoughGoods(long actorId, Map<Integer, Long> data) {
        for (Entry<Integer, Long> entry : data.entrySet()) {
            int id = entry.getKey();
            long count = entry.getValue();
            List<Goods> goodsList = goodsDao.getGoodsList(actorId, id);
            long totalNum = 0;
            for (Goods goods : goodsList) {
                totalNum += goods.getNum();
            }
            if (count < 0 || totalNum < count) {
                return Result.valueOf(GOODS_NOT_ENOUGH);
            }
        }
        return Result.valueOf();
    }

    @Override
    public Result testReward(long actorId, int rewardType, int id, long num) {
        if (!GameConfig.isDebug()) {
            return Result.valueOf();
        }
        switch (rewardType) {
            case -999:
                Collection<BaseDao> daos = SpringContext.getBeanList(BaseDao.class);
                daos.forEach(BaseDao::clear);
                break;
            case -3:
                TResult<Lineup> lineupResult = lineupFacade.getLineup(actorId);
                if (lineupResult.isFail()) {
                    return Result.fail();
                }
                Lineup item = lineupResult.item;
                Map<Integer, BeastEntity> beastMap = item.getBeastMap();
                if (beastMap.size() == 0) {
                    return Result.fail();
                }
                beastMap.remove(id);
                dbQueue.updateQueue(item);
                LineupPushHelper.pushLineupList(actorId, item.getLineupMap().values(), beastMap.values(), item.getBeastResonanceMap().values());
                DispatchHelper.postEvent(new LineupPowerRefreshEvent(actorId));
                return Result.valueOf();
            case -4:
                if (id == 0) {
                    TResult<Lineup> lineupTResult = lineupFacade.getLineup(actorId);
                    if (lineupTResult.isFail()) {
                        return Result.fail();
                    }
                    for (LineupEntity lineupEntity : lineupTResult.item.getLineupMap().values()) {
                        lineupEntity.removeImmortals();
                    }
                    dbQueue.updateQueue(lineupTResult.item);
                    LineupPushHelper.pushLineupList(actorId, lineupTResult.item.getLineupMap().values(), lineupTResult.item.getBeastMap().values(),
                        lineupTResult.item.getBeastResonanceMap().values());
                    DispatchHelper.postEvent(new LineupPowerRefreshEvent(actorId));
                    return Result.valueOf();
                } else {
                    TResult<Lineup> lineupTResult = lineupFacade.getLineup(actorId);
                    if (lineupTResult.isFail()) {
                        return Result.fail();
                    }
                    LineupEntity lineupEntity = lineupTResult.item.getLineupMap().get(id);
                    if (lineupEntity == null) {
                        return Result.fail();
                    }
                    lineupEntity.removeImmortals();
                    dbQueue.updateQueue(lineupTResult.item);
                    LineupPushHelper.pushLineupList(actorId, lineupTResult.item.getLineupMap().values(), lineupTResult.item.getBeastMap().values(),
                        lineupTResult.item.getBeastResonanceMap().values());
                    DispatchHelper.postEvent(new LineupPowerRefreshEvent(actorId));
                    return Result.valueOf();
                }
            case -2:
                if (id == 0) {
                    TResult<NationActor> nationActorTResult = nationFacade.getNationActor(actorId);
                    if (nationActorTResult.isFail()) {
                        return Result.fail();
                    }
                    NationActor nationActor = nationActorTResult.item;
                    nationActor.setBuildRunesTimes(0);
                    dbQueue.updateQueue(nationActor);
                    NationPushHelper.pushNationActor(nationActor);
                    return Result.valueOf();
                } else {
                    TResult<RunesLineup> runesLineupTResult = runesLineupFacade.getRunesLineup(actorId);
                    if (runesLineupTResult.isFail()) {
                        return Result.fail();
                    }
                    RunesLineup runesLineup = runesLineupTResult.item;
                    runesLineup.getRunesLineupMap().remove(id);
                    dbQueue.updateQueue(runesLineup);
                    RunesPushHelper.pushRunesLineup(actorId, runesLineup);
                    DispatchHelper.postEvent(new LineupPowerRefreshEvent(actorId));
                    return Result.valueOf();
                }

            case -1:
                TResult<Lineup> lineupTResult = lineupFacade.getLineup(actorId);
                if (lineupTResult.isFail()) {
                    return Result.fail();
                }
                Lineup lineup = lineupTResult.item;
                if (lineup.getLineupMap().size() == 1) {
                    return Result.fail();
                }
                lineup.getLineupMap().remove(id);
                lineup.getTdRelationMap().clear();
                dbQueue.updateQueue(lineup);
                LineupPushHelper.pushLineupList(actorId, lineup.getLineupMap().values(), lineup.getBeastMap().values(), lineup.getBeastResonanceMap().values());
                DispatchHelper.postEvent(new LineupPowerRefreshEvent(actorId));
                return Result.valueOf();
            case 0:
                if (StoryChapterType.getType(id) == StoryChapterType.NONE) {
                    return Result.fail();
                }
                int storyId = ActorHelper.getStoryId(actorId);
                if (id == 2) {
                    storyId = 0;
                    List<Integer> storyIds = Lists.newArrayList();
                    CollectionResult<StoryChapter> result = storyFacade.getStoryChapterList(actorId);
                    for (StoryChapter storyChapter : result.item) {
                        if (storyChapter.getChapterType() == id) {
                            for (StoryEntity storyEntity : storyChapter.getStoryMap().values()) {
                                if (storyEntity.getStarNum() == 0) {
                                    storyIds.add(storyEntity.getStoryId());
                                }
                            }
                        }
                    }
                    if (storyIds.isEmpty()) {
                        return Result.fail();
                    }
                    storyId = Collections.min(storyIds);
                }
                for (int i = 0; i < num; i++) {
                    TResult<StoryConfig> storyConfigResult = StoryConfigService.getStoryConfig(storyId);
                    if (storyConfigResult.isFail()) {
                        continue;
                    }
                    StoryConfig storyConfig = storyConfigResult.item;
                    TResult<StoryChapter> storyChapter = storyFacade.getStoryChapter(actorId, storyConfig.getChapterId());
                    if (storyChapter.isFail()) {
                        break;
                    }
                    storyFacade.recordChallengeResult(actorId, storyConfig, 3);
                    if (storyConfig.getNextStoryId() != 0) {
                        storyId = storyConfig.getNextStoryId();
                    }
                }
                break;
            case 1:
                if (TypeProtocol.TowerType.forNumber(id) == null || TypeProtocol.TowerType.forNumber(id) == TypeProtocol.TowerType.NONE) {
                    return Result.fail();
                }
                TResult<Tower> towerTResult = towerFacade.getTower(actorId, TypeProtocol.TowerType.forNumber(id));
                if (towerTResult.isFail()) {
                    return Result.fail();
                }
                Tower tower = towerTResult.item;
                tower.setLayer(tower.getLayer() + (int) num);
                if (tower.getLayer() < 1 || tower.getLayer() > 2000) {
                    return Result.fail();
                }
                dbQueue.updateQueue(tower);
                TResult<TowerRecover> tResult = towerFacade.getTowerRecover(actorId);
                TowerPushHelper.pushTowerInfo(actorId, TypeProtocol.TowerType.forNumber(id), tower, tResult.item);
                break;
            case 2:
                TResult<QinDungeon> qinDungeonTResult = qinDungeonFacade.getQinDungeon(actorId);
                if (qinDungeonTResult.isFail()) {
                    return Result.fail();
                }
                if (num > Integer.MAX_VALUE) {
                    return Result.fail();
                }
                QinDungeon qinDungeon = qinDungeonTResult.item;
                if (id == 1) {
                    qinDungeon.setScore(Math.max(0, qinDungeon.getScore() + (int) num));
                    QinDungeonDanConfig config = globalConfigService.findConfig(QinDungeonHelper.getDan(qinDungeon.getScore()), QinDungeonDanConfig.class);
                    int randomId = globalConfigService.findConfig(IdentiyKey.build(config.getDanMax(), QinRaceType.NONE.getId()), QinDungeonRandStageConfig.class).getRandomId();
                    int randomBossId = QinDungeonConfigService.getRandomBossId(config.getDanMax());
                    qinDungeon.setRandomBossId(randomBossId);
                    qinDungeon.setRandomId(randomId);
                    qinDungeonDao.achieveQinDungeonRank(actorId, qinDungeon.getScore(), qinDungeon.getLastScoreUpdateTime());
                    QinDungeonWorldRankVO rankVO = QinDungeonWorldRankVO.valueOf(ActorHelper.getActorAttributeMap(actorId), 0L, qinDungeon.getScore());
                    WorldRpcHelper.asynCall(actorId, WorldQinDungeonRpc.class, new RpcCall<WorldQinDungeonRpc>() {
                        @Override
                        public void run(WorldQinDungeonRpc rpcProxy) {
                            rpcProxy.refreshQinDungeonRank(GameConfig.getServerType(), GameConfig.getServerId(), rankVO);
                        }
                    });
                } else if (id == 2) {
                    qinDungeon.setCurrency((int) num);
                } else if (id == 3) {
                    qinDungeon.setFloor((int) num);
                } else if (id == 4) {
                    QinDungeonRougueEffectConfig config = globalConfigService.findConfig(IdentiyKey.build((int) num), QinDungeonRougueEffectConfig.class);
                    if (config == null) {
                        return Result.valueOf(CONFIG_NOT_FOUND);
                    }
                    qinDungeon.setChooseEffectList(Lists.newArrayList((int) num));
                    dbQueue.updateQueue(qinDungeon);
                    TResult<CommonProtocol.IntPacket> chooseIdResult = qinDungeonFacade.chooseEffectId(actorId, (int) num);
                    return Result.valueOf(chooseIdResult.statusCode);
                } else if (id == 5) {
                    if (num < 0) {
                        return Result.fail();
                    }
                    qinDungeon.setLuckStarNum((int) num);
                } else if (id == 6) {
                    TResult<QinRace> qinRaceResult = qinRaceFacade.getQinRace(actorId);
                    if (qinRaceResult.isFail()) {
                        return Result.valueOf(qinRaceResult.statusCode);
                    }
                    QinRace qinRace = qinRaceResult.item;
                    qinRace.setCurrency((int) num);
                    dbQueue.updateQueue(qinRace);
                    QinRacePushHelper.pushQinRace(qinRace);
                    return Result.valueOf();
                } else if (id == 7) {
                    TResult<QinRace> qinRaceResult = qinRaceFacade.getQinRace(actorId);
                    if (qinRaceResult.isFail()) {
                        return Result.valueOf(qinRaceResult.statusCode);
                    }
                    QinRace qinRace = qinRaceResult.item;
                    qinRace.setLuckStarNum((int) num);
                    dbQueue.updateQueue(qinRace);
                    QinRacePushHelper.pushQinRace(qinRace);
                    return Result.valueOf();
                } else {
                    return Result.valueOf(INVALID_PARAM);
                }
                dbQueue.updateQueue(qinDungeon);
                QinDungeonPushHelper.pushQinDungeon(qinDungeon);
                break;
            case 3:
                DungeonProtocol.DungeonType dungeonType = DungeonProtocol.DungeonType.forNumber(id);
                if (dungeonType == null) {
                    return Result.valueOf(INVALID_PARAM);
                }
                TResult<Dungeon> dungeonResult = dungeonFacade.getDungeon(actorId, dungeonType);
                Dungeon dungeon = dungeonResult.item;
                dungeon.reset();
                dbQueue.updateQueue(dungeon);
                DungeonPushHelper.pushDungeon(actorId, dungeon);
                break;
            case 4:
                TResult<Cave> caveResult = caveFacade.getCave(actorId);
                if (caveResult.isFail()) {
                    return Result.valueOf(caveResult.statusCode);
                }
                Cave cave = caveResult.item;
                if (id == 0) {
                    Map<Integer, Integer> challengeInfoMap = cave.getChallengeInfoMap();
                    challengeInfoMap.put(1, 1);
                    challengeInfoMap.put(2, 1);
                    challengeInfoMap.put(3, 1);
                    challengeInfoMap.put(4, 1);
                    challengeInfoMap.put(5, 1);
                    challengeInfoMap.put(6, 1);
                    challengeInfoMap.put(7, 1);
                    challengeInfoMap.put(8, 1);
                    challengeInfoMap.put(9, 1);
                    challengeInfoMap.put(10, 1);
                    challengeInfoMap.put(11, 1);
                    challengeInfoMap.put(12, 1);
                } else {
                    CaveHardChallengeShowConfig caveHardChallengeShowConfig = CaveConfigService.getCaveHardChallengeShowConfig(id);
                    if (caveHardChallengeShowConfig == null) {
                        return Result.valueOf(INVALID_PARAM);
                    }
                    int retentionMinute = globalConfigService.findGlobalConfig(GlobalConfigKey.CAVE_RETENTION_TIME).findInt();
                    long retentionMillis = retentionMinute * 60 * 1000;
                    cave.getMonsterInfoList().add(CaveHardMonsterInfo.valueOf(id, retentionMillis + System.currentTimeMillis(), 1));
                }
                dbQueue.updateQueue(cave);
                CavePushHelper.pushCave(actorId, cave);
                break;
            case 5:
                TResult<BattlefieldDrill> battlefieldDrillResult = battlefieldDrillFacade.getBattlefieldDrill(actorId);
                if (battlefieldDrillResult.isFail()) {
                    return Result.valueOf(battlefieldDrillResult.statusCode);
                }
                BattlefieldDrill battlefieldDrill = battlefieldDrillResult.item;
                if (num <= 0) {
                    return Result.valueOf(INVALID_PARAM);
                }
                Collection<BattlefieldDrillHeroData> heroDataList = Lists.newArrayList();
                Collection<BattlefieldDrillMonsterData> monsterDataList = Lists.newArrayList();
                battlefieldDrill.refresh((int) num, heroDataList, monsterDataList);
                dbQueue.updateQueue(battlefieldDrill);
                BattlefieldDrillPushHelper.pushBattlefieldDrill(battlefieldDrill);
                break;
            case 6:
                TResult<SignIn> signInResult = signInFacade.getSignIn(actorId);
                if (signInResult.isFail()) {
                    return Result.valueOf(signInResult.statusCode);
                }
                SignIn signIn = signInResult.item;
                if (num <= 0) {
                    return Result.valueOf(INVALID_PARAM);
                }
                if (SignInConfigService.getStageDays() < num) {
                    num = SignInConfigService.getStageDays();
                }
                signIn.getSignInEntity().setDays((int) num);
                signIn.setTotalDays(signIn.getTotalDays() + (int) num);
                dbQueue.updateQueue(signIn);
                ExtensionPushHelper.pushSignIn(actorId, signIn);
                break;
            case 7:
                TResult<Arsenal> arsenalResult = arsenalFacade.getArsenal(actorId);
                if (arsenalResult.isFail()) {
                    return Result.valueOf(arsenalResult.statusCode);
                }
                Arsenal arsenal = arsenalResult.item;
                arsenal.setPlayTimes(0);
                dbQueue.updateQueue(arsenal);
                ArsenalPushHelper.pushArsenal(actorId, arsenal);
                break;
            case 8:
                TResult<TeamOnlineTD> teamOnlineTDResult = teamOnlineTDFacade.getTeamOnlineTD(actorId);
                if (teamOnlineTDResult.isFail()) {
                    return Result.valueOf(teamOnlineTDResult.statusCode);
                }
                TeamOnlineTD teamOnlineTD = teamOnlineTDResult.item;
                teamOnlineTD.setId(TeamOnlineTDConfigService.getFinalTeamOnlineTDId());
                teamOnlineTD.setPassId(TeamOnlineTDConfigService.getSecondToLastTeamOnlineTDId());
                dbQueue.updateQueue(teamOnlineTD);
                TeamOnlineTDHelper.pushTeamOnlineTD(teamOnlineTD);
                break;
            case 9:
                TitleConfig titleConfig = TitleConfigService.getTitleConfig(id);
                if (titleConfig == null) {
                    return Result.valueOf(CONFIG_NOT_FOUND);
                }
                titleFacade.createTitle(actorId, titleConfig);
                break;
            case Module.REINCARNATION_VALUE:
                Result reincarnationResult = reincarnationFacade.refreshLayerFloor(actorId, id, (int) num);
                if (reincarnationResult.isFail()) {
                    return reincarnationResult;
                }
                break;
            case 100:
                for (int i = id; i <= num; i++) {
                    TaskConfig taskConfig = TaskConfigService.getTaskConfig(i);
                    if (taskConfig == null) {
                        LOGGER.error("TaskConfig not found, id:{}", i);
                        return Result.valueOf(CONFIG_NOT_FOUND);
                    }
                    TResult<Task> taskResult = taskFacade.getTask(actorId, i);
                    if (taskResult.isFail()) {
                        return Result.valueOf(taskResult.statusCode);
                    }
                    Task task = taskResult.item;
                    task.setStatus(TaskProtocol.TaskStatus.TASK_FINISH);
                    dbQueue.updateQueue(task);
                    TaskPushHelper.pushTaskList(actorId, Lists.newArrayList(task));
                }
                break;
            case 101:
                long nationId = NationHelper.getNationId(actorId);
                if (nationId == 0) {
                    return Result.valueOf(NATION_NOT_JOIN);
                }
                WorldAuctionRpc rpc = WorldRpcHelper.getProxy(WorldAuctionRpc.class);
                Result result = rpc.textGoods(GameConfig.getServerType(), GameConfig.getServerId(), actorId, nationId, id);
                if (result.isFail()) {
                    return result;
                }
                break;
            case 102:
                TResult<SuppressDemon> suppressDemonTResult = suppressDemonFacade.getSuppressDemon(actorId);
                if (suppressDemonTResult.isFail()) {
                    return Result.valueOf(suppressDemonTResult.statusCode);
                }
                SuppressDemon suppressDemon = suppressDemonTResult.item;
                SuppressDemonConfig config = SuppressDemonConfigService.getSuppressDemonConfig(id);
                if (config == null) {
                    return Result.valueOf(CONFIG_NOT_FOUND);
                }
                suppressDemon.challenge(id, 0l, 1000l, true);
                dbQueue.updateQueue(suppressDemon);
                SuppressDemonPushHelper.pushSuppressDemon(suppressDemon);
                break;
            case 103:
                TResult<Chess> chessTResult = chessFacade.getChessInfo(actorId);
                if (chessTResult.isFail()) {
                    return Result.valueOf(chessTResult.statusCode);
                }
                Chess chess = chessTResult.item;
                MonsterLineup monsterLineup = chess.getMonsterLineupMap().get(id);
                if (monsterLineup == null) {
                    return Result.valueOf(INVALID_PARAM);
                }
                monsterLineup.getFeatureIdList().add((int) num);
                dbQueue.updateQueue(chess);
                ChessPushHelper.pushChess(actorId, chess, chessFacade.getCirculate(), true);
                break;
            case 104:
                TResult<Chess> chess1TResult = chessFacade.getChessInfo(actorId);
                if (chess1TResult.isFail()) {
                    return Result.valueOf(chess1TResult.statusCode);
                }
                Chess chess1 = chess1TResult.item;
                MonsterLineup monsterLineup1 = chess1.getMonsterLineupMap().get(id);
                if (monsterLineup1 == null) {
                    return Result.valueOf(INVALID_PARAM);
                }
                monsterLineup1.getFeatureIdList().remove((int) num);
                dbQueue.updateQueue(chess1);
                ChessPushHelper.pushChess(actorId, chess1, chessFacade.getCirculate(), true);
                break;
            case 105:
                TResult<Chess> chess2TResult = chessFacade.getChessInfo(actorId);
                if (chess2TResult.isFail()) {
                    return Result.valueOf(chess2TResult.statusCode);
                }
                Chess chess2 = chess2TResult.item;
                chess2.getStoryIdList().clear();
                chess2.getStoryIdList().addAll(ChessConfigService.getStoryIdList(chessFacade.getCirculate(), id, (int) num));
                dbQueue.updateQueue(chess2);
                ChessPushHelper.pushChess(actorId, chess2, chessFacade.getCirculate(), false);
                break;
            case 106:
                TResult<Chess> chess3TResult = chessFacade.getChessInfo(actorId);
                if (chess3TResult.isFail()) {
                    return Result.valueOf(chess3TResult.statusCode);
                }
                Chess chess3 = chess3TResult.item;
                if (!chess3.getMonsterLineupMap().keySet().contains(id)) {
                    return Result.valueOf(INVALID_PARAM);
                }
                MonsterLineup monsterLineup3 = ChessConfigService.getMonsterLineup(chessFacade.getCirculate(), id, (int) num);
                if (monsterLineup3 == null) {
                    return Result.valueOf(INVALID_PARAM);
                }
                String monsterExpr = globalConfigService.findGlobalConfig(GlobalConfigKey.CHESS_MONSTER_FORMULA).getValue();
                chess3.getMonsterLineupMap().put(id, ChessHelper.getMonsterLineup(id, monsterLineup3, monsterExpr, chessFacade.getCirculate(), chess3.getMondayPower()));
                dbQueue.updateQueue(chess3);
                ChessPushHelper.pushChess(actorId, chess3, chessFacade.getCirculate(), true);
                break;
            case 107:
                if (id == 0) {
                    TResult<Lineup> lineup1TResult = lineupFacade.getLineup(actorId);
                    if (lineup1TResult.isFail()) {
                        return Result.valueOf(lineup1TResult.statusCode);
                    }
                    Lineup lineup1 = lineup1TResult.item;
                    for (LineupEntity entity : lineup1.getLineupMap().values()) {
                        long heroId = entity.getHeroId();
                        TResult<Hero> heroTResult = heroFacade.getHero(actorId, heroId);
                        if (heroTResult.isFail()) {
                            return Result.valueOf(heroTResult.statusCode);
                        }
                        Hero hero = heroTResult.item;
                        hero.setLevel(ActorHelper.getActorLevel(actorId));
                        //当前配置最高星级为42
                        hero.setStarLevel(42);
                        //当前配置最高进阶级为30
                        hero.setBreakoutLevel(30);
                        //当前配置最高进阶级为15
                        hero.setFosterLevel(15);
                        hero.getFosterAttributeTypeMap().clear();
                        //当前配置是金将以下无门，金将开4门，暗金将开8门
                        HeroConfig heroConfig = HeroConfigService.getHeroConfigByHeroId(hero.getConfigId());
                        if (heroConfig == null) {
                            return Result.valueOf(CONFIG_NOT_FOUND);
                        }
                        //金武将最高为 4门等级为28
                        if (heroConfig.getQuality() == 5) {
                            hero.setEightGateId(4);
                            hero.setGateBreakoutLevel(28);
                        }
                        //暗金武将最高为 8门等级为56
                        if (heroConfig.getQuality() == 6) {
                            hero.setEightGateId(8);
                            hero.setGateBreakoutLevel(56);
                        }
                        dbQueue.updateQueue(hero);
                        HeroPushHelper.pushHeroList(actorId, Lists.newArrayList(hero));
                        DispatchHelper.postEvent(new LineupPowerRefreshEvent(actorId));
                    }
                } else {
                    if (id < 0 || id > 6) {
                        return Result.valueOf(INVALID_PARAM);
                    }
                    TResult<Lineup> lineup1TResult = lineupFacade.getLineup(actorId);
                    if (lineup1TResult.isFail()) {
                        return Result.valueOf(lineup1TResult.statusCode);
                    }
                    Lineup lineup1 = lineup1TResult.item;
                    LineupEntity lineupEntity = lineup1.getLineupMap().get(id);
                    if (lineupEntity == null) {
                        return Result.valueOf(INVALID_PARAM);
                    }
                    long heroId = lineupEntity.getHeroId();
                    TResult<Hero> heroTResult = heroFacade.getHero(actorId, heroId);
                    if (heroTResult.isFail()) {
                        return Result.valueOf(heroTResult.statusCode);
                    }
                    Hero hero = heroTResult.item;
                    hero.setLevel(ActorHelper.getActorLevel(actorId));
                    //当前配置最高星级为42
                    hero.setStarLevel(42);
                    //当前配置最高进阶级为30
                    hero.setBreakoutLevel(30);
                    //当前配置最高进阶级为15
                    hero.setFosterLevel(15);
                    hero.getFosterAttributeTypeMap().clear();
                    //当前配置是金将以下无门，金将开4门，暗金将开8门
                    HeroConfig heroConfig = HeroConfigService.getHeroConfigByHeroId(hero.getConfigId());
                    if (heroConfig == null) {
                        return Result.valueOf(CONFIG_NOT_FOUND);
                    }
                    //金武将最高为 4门等级为28
                    if (heroConfig.getQuality() == 5) {
                        hero.setEightGateId(4);
                        hero.setGateBreakoutLevel(28);
                    }
                    //暗金武将最高为 8门等级为56
                    if (heroConfig.getQuality() == 6) {
                        hero.setEightGateId(8);
                        hero.setGateBreakoutLevel(56);
                    }
                    dbQueue.updateQueue(hero);
                    HeroPushHelper.pushHeroList(actorId, Lists.newArrayList(hero));
                    DispatchHelper.postEvent(new LineupPowerRefreshEvent(actorId));
                }
                break;
            case 108:
                TResult<SanctuaryRegionState> regionStateResult = sanctuaryActorFacade.getSanctuaryRegionState();
                if (regionStateResult.isFail()) {
                    return Result.valueOf(regionStateResult.statusCode);
                }
                SanctuaryRegionState regionState = regionStateResult.item;
                TResult<SanctuaryActor> sanctuaryActorTResult = sanctuaryActorFacade.getSanctuaryActor(actorId);
                SanctuaryActor sanctuaryActor = sanctuaryActorTResult.item;
                int seasonId = regionState.getSeasonId();
                Collection<Integer> allPvpStarStoryId = SanctuaryConfigService.getAllPvpStarStoryId(seasonId);
                int pvpId = sanctuaryActor.getPassTime() == 0 ? sanctuaryActor.getPvpId() - 1 : sanctuaryActor.getPvpId();
                Collection<Integer> pveIds = SanctuaryConfigService.getPveIds(seasonId, pvpId);
                Collection<Integer> starList = Lists.newLinkedList();
                if (id == 1) {
                    int stageId = regionState.getStageId();
                    SanctuaryStageConfig stageConfig = globalConfigService.findConfig(IdentiyKey.build(seasonId, stageId), SanctuaryStageConfig.class);
                    int stageMaxPvpId = SanctuaryConfigService.getStageMaxPvpId(seasonId, stageConfig.getUnlockMap());
                    int countPvpId = Math.min(stageMaxPvpId, (int) num);
                    if (countPvpId > sanctuaryActor.getPvpId()) {
                        sanctuaryActor.setPvpId(countPvpId);
                    }
                    for (Integer pvpStarId : allPvpStarStoryId) {
                        if (pvpStarId < countPvpId) {
                            sanctuaryActor.putPvpStarMap(pvpStarId, Lists.newArrayList(1));
                        }
                    }
                } else if (id == 2) {
                    int i = 0;
                    for (Integer pveId : pveIds) {
                        sanctuaryActor.putPveStarMap(pveId, Lists.newArrayList(1));
                        i++;
                        if (i == pveIds.size()) {
                            sanctuaryActor.setPveId(pveId);
                        }
                    }
                } else if (id < 10000) {
                    if (!allPvpStarStoryId.contains(id)) {
                        return Result.valueOf(INVALID_PARAM);
                    }
                    if (sanctuaryActor.getPassTime() == 0 && sanctuaryActor.getPvpId() <= id) {
                        return Result.valueOf(INVALID_PARAM);
                    }
                    if (num <= 0 || num > 321) {
                        return Result.valueOf(INVALID_PARAM);
                    }
                    int first = (int) (num / 100);
                    if (first >= 1 && first <= 3) {
                        starList.add(first);
                    }
                    int second = (int) (num - first * 100) / 10;
                    if (second >= 1 && second <= 3) {
                        starList.add(second);
                    }
                    int third = (int) num % 10;
                    if (third >= 1 && third <= 3) {
                        starList.add(third);
                    }
                    sanctuaryActor.putPvpStarMap(id, starList);
                } else {
                    if (!pveIds.contains(id)) {
                        return Result.valueOf(INVALID_PARAM);
                    }
                    if (num <= 0 || num > 321) {
                        return Result.valueOf(INVALID_PARAM);
                    }
                    int first = (int) (num / 100);
                    if (first >= 1 && first <= 3) {
                        starList.add(first);
                    }
                    int second = (int) (num - first * 100) / 10;
                    if (second >= 1 && second <= 3) {
                        starList.add(second);
                    }
                    int third = (int) num % 10;
                    if (third >= 1 && third <= 3) {
                        starList.add(third);
                    }
                    sanctuaryActor.putPveStarMap(id, starList);
                }
                dbQueue.updateQueue(sanctuaryActor);
                SanctuaryPushHelper.pushSanctuaryActor(actorId, sanctuaryActor);
                break;
            //平定天下生成事件
            case -68:
                TResult<Reputation> reputationTResult = reputationFacade.getReputationInfo(actorId);
                if (reputationTResult.isFail()) {
                    return Result.valueOf(reputationTResult.statusCode);
                }
                ReputationEventConfig eventConfig = globalConfigService.findConfig(IdentiyKey.build(id), ReputationEventConfig.class);
                if (eventConfig == null) {
                    return Result.valueOf(CONFIG_NOT_FOUND);
                }
                Reputation reputation = reputationTResult.item;
                int limit = globalConfigService.findGlobalConfig(GlobalConfigKey.REPUTATION_EVENT_LIMIT).findInt();
                Collection<Integer> allIndex = Lists.newArrayList();
                for (int i = 1; i <= limit; i++) {
                    if (!reputation.getEventInfoEntities().values().stream().map(EventInfoEntity::getIndex).collect(Collectors.toList()).contains(i)
                        && reputation.getUrgentEvent().getIndex() != i) {
                        allIndex.add(i);
                    }
                }
                if (allIndex.isEmpty()) {
                    return Result.valueOf(INVALID_PARAM);
                }
                int index = RandomUtils.randomHit(allIndex);
                TResult<EventInfoEntity> entityTResult = reputationFacade.creatEventInfoEntity(eventConfig, index);
                if (entityTResult.isFail()) {
                    return Result.valueOf(entityTResult.statusCode);
                }
                reputation.addEvent(Lists.newArrayList(entityTResult.item));
                dbQueue.updateQueue(reputation);
                ReputationProtocol.ReputationResponse response = ReputationHelper.buildReputationResponse(reputation, true);
                ReputationPushHelper.pushReputationInto(actorId, response);
                break;
            case -69:
                TypeProtocol.SettingType settingType = TypeProtocol.SettingType.forNumber(id);
                if (settingType == TypeProtocol.SettingType.UNRECOGNIZED || settingType == TypeProtocol.SettingType.SETTING_KEY_NONE) {
                    return Result.valueOf(INVALID_PARAM);
                }
                if (num <= 0 || num >= 10000) {
                    return Result.valueOf(INVALID_PARAM);
                }
                long currentTimeMillis = System.currentTimeMillis();
                long time = currentTimeMillis - (num - 1) * TimeUtils.ONE_DAY_MILLISECOND;
                settingsDao.setValue(settingType, time);
                break;
            default:
                if (rewardType == RewardType.RESOURCE_VALUE && num > 100000000) {
                    return Result.valueOf(INVALID_PARAM);
                }
                if (rewardType == RewardType.GOODS_VALUE && num > 1000000) {
                    return Result.valueOf(INVALID_PARAM);
                }
                if (rewardType == RewardType.HERO_VALUE && num > GameConfig.getClientCountLimit()) {
                    return Result.valueOf(INVALID_PARAM);
                }
                if (rewardType == RewardType.EQUIPMENT_VALUE && num > GameConfig.getClientCountLimit()) {
                    return Result.valueOf(INVALID_PARAM);
                }
                if (rewardType == RewardType.TREASURE_VALUE && num > GameConfig.getClientCountLimit()) {
                    return Result.valueOf(INVALID_PARAM);
                }
                RewardObject rewardObject = RewardObject.valueOf(rewardType, (int) id, num);
                if (num <= 0) {
                    rewardObject.setCount(Math.abs(num));
                    if (rewardType == RewardType.GOODS_VALUE) {
                        int goodsNum = getGoodsNum(actorId, id);
                        if (rewardObject.getCount() > goodsNum || num == 0) {
                            rewardObject.setCount(goodsNum);
                        }
                    }
                    return RewardHelper.decrease(actorId, Lists.newArrayList(rewardObject), OperationType.TEST_REWARD);
                } else {
                    RewardHelper.sendRewardList(actorId, Lists.newArrayList(rewardObject), OperationType.TEST_REWARD);
                }
                break;
        }
        return Result.valueOf();
    }

    @Override
    public int getGoodsNum(long actorId, int goodsId) {
        List<Goods> goodsList = goodsDao.getGoodsList(actorId, goodsId);
        int goodsTotalNum = 0;
        for (Goods goods : goodsList) {
            goodsTotalNum += goods.getNum();
        }
        return goodsTotalNum;
    }

    @Override
    public int getGoodsTimes(long actorId, int goodsId) {
        List<Goods> goodsList = goodsDao.getGoodsList(actorId, goodsId);
        if (goodsList.size() > 1) {
            LOGGER.error("GoodsTimes error,actorId:{},goodsId:{},goods:{}", actorId, goodsId, JSONObject.toJSONString(goodsList));
        }
        int times = 0;
        for (Goods goods : goodsList) {
            times += goods.getTimes();
        }
        return times;
    }

    @Override
    public void setGoodsTimes(long actorId, int goodsId, int times) {
        List<Goods> goodsList = goodsDao.getGoodsList(actorId, goodsId);
        if (goodsList.size() > 1) {
            LOGGER.error("GoodsTimes error,actorId:{},goodsId:{},goods:{}", actorId, goodsId, JSONObject.toJSONString(goodsList));
        }
        for (Goods goods : goodsList) {
            goods.setTimes(times);
            dbQueue.updateQueue(goods);
        }
        GoodsPushHelper.pushGoodsList(goodsList, actorId);
    }

    @Override
    public Collection<Long> getActorIdsByGoodsId(Collection<Integer> goodsIdList) {
        return goodsDao.getActorIdsByGoodsId(goodsIdList);
    }

    @Override
    public List<Goods> getGoodsList(long actorId, int goodsId) {
        return goodsDao.getGoodsList(actorId, goodsId);
    }

    @Override
    public TResult<RewardResultResponse> decompose(long actorId, Map<Integer, Integer> goodsMap) {
        if (goodsMap.isEmpty()) {
            return TResult.valueOf(INVALID_PARAM);
        }
        Map<Integer, Long> decreaseGoodsMap = Maps.newHashMap();
        for (Entry<Integer, Integer> entry : goodsMap.entrySet()) {
            int goodsId = entry.getKey();
            long decreaseGoodsNum = entry.getValue();
            int goodsNum = getGoodsNum(actorId, entry.getKey());
            if (decreaseGoodsNum < 0 || goodsNum <= 0) {
                continue;
            }
            if (decreaseGoodsNum == 0 || decreaseGoodsNum > goodsNum) {
                decreaseGoodsNum = goodsNum;
            }
            GoodsConfig goodsConfig = globalConfigService.findConfig(goodsId, GoodsConfig.class);
            if (goodsConfig == null) {
                LOGGER.error("GoodsConfig is null goodsId:{}", goodsId);
                continue;
            }
            if (goodsConfig.getDecomposeRewardList().isEmpty()) {
                continue;
            }
            decreaseGoodsMap.put(goodsId, decreaseGoodsNum);

            // 满星红颜的碎片才能分解
            if (goodsConfig.getGoodsType() == GoodsType.FRAMENT && goodsConfig.getGoodsSubType() == GoodsSubType.MOUNT_FRAGMENT) {
                MountConfig mountConfig = MountConfigService.getMountConfig(goodsId);
                if (mountConfig == null) {
                    return TResult.valueOf(CONFIG_ERROR);
                }
                TResult<Mount> mountResult = mountFacade.getMountById(actorId, mountConfig.getId());
                if (mountResult.isFail()) {
                    return TResult.valueOf(MOUNT_DECOMPOSE_ERROR);
                }
                int mountMaxStarLevel = MountConfigService.getMountMaxStarLevel(mountResult.item.getCid());
                if (mountResult.item.getStarLevel() != mountMaxStarLevel) {
                    return TResult.valueOf(MOUNT_DECOMPOSE_ERROR);
                }
            }
        }

        if (decreaseGoodsMap.isEmpty()) {
            return TResult.valueOf(GOODS_NOT_FOUND);
        }

        Result result = decreaseGoods(actorId, decreaseGoodsMap, OperationType.DECOMPOSE);
        if (result.isFail()) {
            return TResult.valueOf(result);
        }
        Collection<RewardObject> rewardList = Lists.newArrayList();

        for (Entry<Integer, Long> entry : decreaseGoodsMap.entrySet()) {
            int goodsId = entry.getKey();
            long goodsNum = entry.getValue();
            GoodsConfig goodsConfig = globalConfigService.findConfig(goodsId, GoodsConfig.class);
            Collection<RewardObject> rewardObjects = RewardHelper.multipleRewardList(goodsConfig.getDecomposeRewardList(), goodsNum);
            rewardList.addAll(rewardObjects);
        }

        if (rewardList.isEmpty()) {
            return TResult.valueOf(INVALID_PARAM);
        }

        RewardResult rewardResult = RewardHelper.sendRewardList(actorId, rewardList, OperationType.DECOMPOSE);
        RewardResultResponse response = RewardResultResponse.newBuilder().setRewardResult(rewardResult).build();
        return TResult.sucess(response);
    }

    @Override
    public TResult<RewardResultResponse> synthesis(long actorId, int goodsId, int num) {
        if (num <= 0 || num > GameConfig.getClientCountLimit()) {
            return TResult.valueOf(INVALID_PARAM);
        }
        SynthesisConfig synthesisConfig = globalConfigService.findConfig(goodsId, SynthesisConfig.class);
        if (synthesisConfig == null) {
            return TResult.valueOf(CONFIG_NOT_FOUND);
        }

        RewardObject rewardObject = RewardObject.valueOf(RewardType.GOODS_VALUE, goodsId, num * synthesisConfig.getCostNum());

        Result result = RewardHelper.decrease(actorId, Lists.newArrayList(rewardObject), OperationType.SYNTHESIS);
        if (result.isFail()) {
            return TResult.valueOf(result);
        }

        Collection<RewardObject> rewards = Lists.newArrayList();

        for (int i = 0; i < num; i++) {
            rewards.addAll(synthesisConfig.getRewardList());
        }
        RewardHelper.convert(rewards);
        RewardResult rewardResult = RewardHelper.sendRewardList(actorId, rewards, OperationType.SYNTHESIS);
        RewardResultResponse response = RewardResultResponse.newBuilder().setRewardResult(rewardResult).build();
        return TResult.sucess(response);
    }

    @Event(name = EventKey.GOODS_DELETE_EVENT)
    public void onGoodsDeleteEvent(GoodsDeleteEvent event) {
        long actorId = event.getActorId();
        Collection<Goods> refreshList = goodsDao.deleteGoodsByGoodsId(actorId, event.goodsIdList);
        if (!refreshList.isEmpty()) {
            GoodsPushHelper.pushGoodsList(refreshList, actorId);
        }
    }
}
