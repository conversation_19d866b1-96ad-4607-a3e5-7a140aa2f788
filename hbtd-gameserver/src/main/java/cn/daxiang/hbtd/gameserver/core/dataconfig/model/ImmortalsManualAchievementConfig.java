package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.dataconfig.annotation.FieldIgnore;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.framework.utils.StringUtils;
import cn.daxiang.shared.reward.RewardObject;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * 专武皮肤图鉴成就表
 *
 * <AUTHOR>
 * @date 2022/7/4
 */
@DataFile(fileName = "immortals_manual_achievement_config")
public class ImmortalsManualAchievementConfig implements ModelAdapter {
    /**
     * 等级
     */
    private int level;
    /**
     * 图鉴值
     */
    private int manualTarget;
    /**
     * 消耗
     */
    private String cost;
    /**
     * 特殊属性
     */
    private String specialEffectId;
    /**
     * 天赋属性
     */
    private String talentSpecialEffectId;
    /**
     * 评分
     */
    private int score;
    /**
     * 消耗列表
     */
    @FieldIgnore
    private List<RewardObject> costList = Lists.newArrayList();
    @FieldIgnore
    private List<Integer> specialEffectIds = Lists.newArrayList();

    @Override
    public void initialize() {
        JSONArray costArray = JSONArray.parseArray(cost);
        for (Object costItem : costArray) {
            JSONArray rewardArray = JSONArray.parseArray(costItem.toString());
            RewardObject rewardObject = RewardObject.valueOf(rewardArray);
            costList.add(rewardObject);
        }
        if (StringUtils.isNotBlank(specialEffectId)) {
            specialEffectIds.addAll(JSONArray.parseArray(specialEffectId, Integer.class));
        }
        if (StringUtils.isNotBlank(talentSpecialEffectId)) {
            specialEffectIds.addAll(JSONArray.parseArray(talentSpecialEffectId, Integer.class));
        }
    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(manualTarget);
    }

    public int getManualTarget() {
        return manualTarget;
    }

    public int getLevel() {
        return level;
    }

    public List<RewardObject> getCostList() {
        return costList;
    }

    public List<Integer> getSpecialEffectIds() {
        return specialEffectIds;
    }

    public int getScore() {
        return score;
    }
}
