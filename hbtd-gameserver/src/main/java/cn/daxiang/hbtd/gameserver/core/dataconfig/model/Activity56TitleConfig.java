package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.identity.IdentiyKey;

/**
 * 活动-56-开启状态
 *
 * <AUTHOR>
 * @date 2024/1/15
 */
@DataFile(fileName = "activity_56_title_config")
public class Activity56TitleConfig implements ModelAdapter {
    /**
     * DATA
     */
    private int data;
    /**
     * 模块ID
     * 1、每日签到
     * 2、神将招募
     * 3、神将试炼
     * 4、洛书的委托
     * 5、神将商店
     * 6、神将排行
     */
    private int id;

    @Override
    public void initialize() {

    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(data, id);
    }

    public int getData() {
        return data;
    }

    public int getId() {
        return id;
    }
}
