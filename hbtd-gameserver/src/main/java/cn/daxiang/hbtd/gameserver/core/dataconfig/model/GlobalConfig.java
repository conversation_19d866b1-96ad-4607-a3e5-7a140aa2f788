package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.dataconfig.annotation.FieldIgnore;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.hbtd.gameserver.core.dataconfig.convert.IConfigable;
import cn.daxiang.hbtd.gameserver.core.dataconfig.convert.StringConvert;
import cn.daxiang.hbtd.gameserver.core.dataconfig.convert.callback.ConvertStringToMapCallback;
import cn.daxiang.hbtd.gameserver.core.dataconfig.convert.callback.ConvertStringToObjectCallback;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@DataFile(fileName = "global_config")
public class GlobalConfig implements ModelAdapter {

    private String attrKey;
    private String value;

    @FieldIgnore
    private Object object;

    @SuppressWarnings("unchecked")
    public <T extends IConfigable> T findObject(Class<T> clazz) {
        if (object == null) {
            try {
                T t = clazz.newInstance();
                t.buildObject(value);
                this.object = t;
            } catch (Exception e) {
                e.printStackTrace();
                return null;
            }
        }
        return (T) object;
    }

    public int findInt() {
        if (object == null) {
            object = Integer.parseInt(value);
        }
        return (Integer) object;
    }

    public boolean findBoolean() {
        if (object == null) {
            object = Boolean.parseBoolean(value);
        }
        return (Boolean) object;
    }

    public long findLong() {
        if (object == null) {
            object = new BigDecimal(value).longValue();
        }
        return (long) object;
    }

    public double findDouble() {
        if (object == null) {
            object = Double.parseDouble(value);
        }
        return (Double) object;
    }

    public short findShort() {
        if (object == null) {
            object = Short.parseShort(value);
        }
        return (Short) object;
    }

    public List<Integer> getIntList() {
        return StringConvert.convertToObjectList(new ConvertStringToObjectCallback<Integer>() {

            @Override
            public String getTokenizerString() {
                return value;
            }

            @Override
            public int getSize() {
                return 1;
            }

            @Override
            public Integer fromArray(String[] array) {
                return Integer.valueOf(array[0]);
            }
        });
    }

    public List<Double> getDoubleList() {
        return StringConvert.convertToObjectList(new ConvertStringToObjectCallback<Double>() {

            @Override
            public String getTokenizerString() {
                return value;
            }

            @Override
            public int getSize() {
                return 1;
            }

            @Override
            public Double fromArray(String[] array) {
                return Double.valueOf(array[0]);
            }
        });
    }

    public Map<Integer, Integer> getIntMap() {
        return StringConvert.convertToObjectHashMap(new ConvertStringToMapCallback<Integer, Integer>() {

            @Override
            public String getTokenizerString() {
                return value;
            }

            @Override
            public int getSize() {
                return 2;
            }

            @Override
            public void fromArray(Map<Integer, Integer> map, String[] array) {
                map.put(Integer.valueOf(array[0]), Integer.valueOf(array[1]));
            }
        });
    }

    public String getAttrKey() {
        return attrKey;
    }

    public String getValue() {
        return value;
    }

    public Object getObject() {
        return object;
    }

    @Override
    public void initialize() {
        value = value.replaceAll("\n|\t", "");
        object = null;
    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(attrKey);
    }
}