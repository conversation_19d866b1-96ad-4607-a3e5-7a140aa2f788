package cn.daxiang.hbtd.gameserver.module.task.parser.impl;

import cn.daxiang.hbtd.gameserver.core.database.table.Task;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.TaskConfig;
import cn.daxiang.hbtd.gameserver.core.event.impl.BeautyDateEvent;
import cn.daxiang.hbtd.gameserver.module.task.parser.AbstractTaskParser;
import cn.daxiang.hbtd.gameserver.module.task.type.TaskConditionType;
import cn.daxiang.protocol.game.TaskProtocol;
import org.springframework.stereotype.Component;

/**
 * @Author: Gary
 * @Date 2022-11-09 16:27
 * @Description:
 */
@Component
public class BeautyDateTaskParser extends AbstractTaskParser<BeautyDateEvent> {
    @Override
    protected TaskConditionType getType() {
        return TaskConditionType.BEAUTY_DATE;
    }

    @Override
    protected boolean parseCondition(BeautyDateEvent event, Task task, TaskConfig taskConfig) {
        task.setValue(task.getValue() + event.times);
        if (task.getValue() >= taskConfig.getValue()) {
            task.setStatus(TaskProtocol.TaskStatus.TASK_FINISH);
        }
        return true;
    }

    @Override
    public void init(Task task, TaskConfig taskConfig) {
        if (task.getValue() >= taskConfig.getValue()) {
            task.setStatus(TaskProtocol.TaskStatus.TASK_FINISH);
        }
    }
}
