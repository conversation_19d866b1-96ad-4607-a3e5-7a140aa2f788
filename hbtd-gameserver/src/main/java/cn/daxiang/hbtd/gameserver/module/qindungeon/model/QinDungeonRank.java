package cn.daxiang.hbtd.gameserver.module.qindungeon.model;

import cn.daxiang.framework.utils.rank.AbstractRank;
import cn.daxiang.framework.utils.rank.LadderRank;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * <AUTHOR>
 * @date 2022/9/17
 */
public class QinDungeonRank extends LadderRank<Long> implements RowMapper<QinDungeonRank>, Comparable<QinDungeonRank> {
    /**
     * 分数
     */
    private int score;
    /**
     * 最后一次分数更新时间
     */
    private long lastScoreUpdateTime;

    public static QinDungeonRank valueOf(long actorId, int score, long lastScoreUpdateTime) {
        QinDungeonRank qinDungeonRank = new QinDungeonRank();
        qinDungeonRank.key = actorId;
        qinDungeonRank.score = score;
        qinDungeonRank.lastScoreUpdateTime = lastScoreUpdateTime;
        return qinDungeonRank;
    }

    public static QinDungeonRank valueOf(long actorId, int score) {
        QinDungeonRank qinDungeonRank = new QinDungeonRank();
        qinDungeonRank.key = actorId;
        qinDungeonRank.score = score;
        return qinDungeonRank;
    }

    @Override
    public AbstractRank<Long> copy() {
        QinDungeonRank rank = QinDungeonRank.valueOf(this.key, this.score, this.lastScoreUpdateTime);
        this.copyRank(rank);
        return rank;
    }

    @Override
    public boolean outstrip(AbstractRank<Long> rank) {
        QinDungeonRank qinDungeonRank = (QinDungeonRank) rank;
        if (this.score > qinDungeonRank.getScore()) {
            return true;
        } else if (this.score == qinDungeonRank.getScore()) {
            return this.lastScoreUpdateTime < qinDungeonRank.getLastScoreUpdateTime();
        }
        return false;
    }

    @Override
    public void achieveRank(LadderRank<Long> rank) {
        QinDungeonRank qinDungeonRank = (QinDungeonRank) rank;
        this.score = qinDungeonRank.getScore();
        this.lastScoreUpdateTime = qinDungeonRank.getLastScoreUpdateTime();
    }

    public int getScore() {
        return score;
    }

    public void setScore(int score) {
        this.score = score;
    }

    public long getLastScoreUpdateTime() {
        return lastScoreUpdateTime;
    }

    public void setLastScoreUpdateTime(long lastScoreUpdateTime) {
        this.lastScoreUpdateTime = lastScoreUpdateTime;
    }

    public void reset(int score, long lastScoreUpdateTime) {
        this.score = score;
        this.lastScoreUpdateTime = lastScoreUpdateTime;
    }

    @Override
    public int compareTo(QinDungeonRank o) {
        if (this.score > o.getScore()) {
            return -1;
        } else if (this.score < o.getScore()) {
            return 1;
        } else {
            return this.key < o.getKey() ? -1 : 1;
        }
    }

    @Override
    public QinDungeonRank mapRow(ResultSet rs, int rowNum) throws SQLException {
        QinDungeonRank rank = new QinDungeonRank();
        rank.key = rs.getLong("actorId");
        rank.score = rs.getInt("score");
        rank.lastScoreUpdateTime = rs.getLong("lastScoreUpdateTime");
        return rank;
    }

    @Override
    public String toString() {
        return "[ key : " + this.key + " score : " + this.score + " ]";
    }
}
