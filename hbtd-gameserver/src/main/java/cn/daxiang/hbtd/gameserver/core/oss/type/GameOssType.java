package cn.daxiang.hbtd.gameserver.core.oss.type;

public enum GameOssType {
    /**
     * 程序用于测试日志写出
     */
    OSS_TEST("OSSTEST"),
    /**
     * 新进帐号
     * uid,platformId,channelId,serverType,serverId,writeTime
     * 用户唯一id,平台id,渠道id,服务器类型,服务器id,日志写入时间
     */
    NEW_USER("newUser"),
    /**
     * 战力刷新
     */
    REFRESH_POWER("refreshPower"),
    /**
     * 角色物品日志
     * uid,platformId,channelId,serverType,serverId,actorId,addFlag,originType,goodsId,num,writeTime
     * 用户唯一id,平台id,渠道id,服务器id,角色id,添加标识(1增加|2扣除),物品id,数量，写入时间
     */
    GOODS("goods"),
    /**
     * 邮件日志
     */
    MAIL("mail"),
    /**
     * 角色登陆日志
     * uid,platformId,channelId,serverType,serverId,actorId,loginTime,loginOut,writeTime
     * 用户唯一id,平台id,渠道id,服务器id,服务器类型,角色id,登陆时间,登出时间,写入时间
     */
    ACTOR_LOGIN("actorLogin"),
    /**
     * 日常任务活跃度快照日志
     */
    TASK_ACTIVE("tastActive"),
    /**
     * 角色升级日志
     * uid,platformId,channelId,serverType,serverId,actorId,oldLevel,newLevel,writeTime
     * 用户唯一id,平台id,渠道id,服务器id,角色id,旧等级,新等级,写入时间
     */
    ACTOR_UPGRADE("actorUpgrade"),
    /**
     * 竞技场排名
     */
    ARENA("arena"),
    /**
     * 试炼塔
     */
    ENDLESS("endless"),
    /**
     * 关卡
     */
    SECTION("section"),
    /**
     * 新手引导通过节点统计
     */
    GUIDANCE("guidance"),
    /**
     * 新手打点步骤统计
     */
    BRIDE_DOT("brideDot"),
    /**
     * 统计功能使用
     */
    STATISTISC("statistisc"),
    /**
     * 屠龙伤害
     */
    DRAGON_DAMAGE("dragonDamage"),
    /**
     * 角色充值
     */
    ACTOR_RECHARGE("actorRecharge"),
    /**
     * 角色首次充值
     */
    ACTOR_FIRST_RECHARGE("firstRecharge"),
    /**
     * 鲸鱼用户统计 会统计所有玩家(不能保证他撒时候充钱)
     */
    JINGYU_STATU("jingyuStatu"),
    /**
     * 玩家任务奖励领取
     */
    RECEIVE_TASK("receiveTask"),
    /**
     * 商店购买记录日志
     */
    STORE_BUY("storeBuy"),
    /**
     * 玩法参与记录日志
     */
    PLAYING_METHOD("playingMethod"),
    /**
     * 记录作弊日志
     */
    RECORD_CRIBBER("recordCribber"),
    /**
     * 聊天记录
     */
    CHAT_HISTORY("chatHistory"),
    /**
     * 双人合战挑战记录
     */
    TEAM_TD("teamTd"),
    /**
     * 名将塔挑战记录
     */
    TOWER("tower"),
    /**
     * 藏兵阁战斗记录
     */
    ARSENAL("arsenal"),
    /**
     * 材料副本
     */
    DUNGEON("dungeon"),
    /**
     * 无尽试炼
     */
    BATTLEFIELD_DRILL("battlefieldDrill"),
    /**
     * 远古棋局
     */
    QIN_DUNGEON("qinDungeon"),
    /**
     * 远古棋局选择效果ID记录
     */
    QIN_DUNGEON_EFFECTID("qinDungeonEffectId"),
    /**
     * 镇妖台
     */
    SUPPRESS_DEMON("suppressDemon"),
    /**
     * 组队魔窟
     */
    CAVE("cave"),
    /**
     * 词库匹配结果
     */
    THESAURUS_MATCH("thesaurusMatch"),
    /**
     * 无
     */
    NONE("none");
    /**
     * 日志名称
     */
    private String name;

    GameOssType(String name) {
        this.name = name;
    }

    public static String[] getEnumArray() {
        GameOssType[] ossTypeArray = GameOssType.values();
        String[] array = new String[ossTypeArray.length];
        for (int i = 0; i < ossTypeArray.length; i++) {
            array[i] = ossTypeArray[i].getName();
        }
        return array;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
