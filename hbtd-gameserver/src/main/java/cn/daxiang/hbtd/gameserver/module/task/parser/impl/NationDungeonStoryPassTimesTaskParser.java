package cn.daxiang.hbtd.gameserver.module.task.parser.impl;

import cn.daxiang.hbtd.gameserver.core.database.table.Task;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.TaskConfig;
import cn.daxiang.hbtd.gameserver.core.event.impl.NationDungeonActorRefreshEvent;
import cn.daxiang.hbtd.gameserver.module.nationDungeon.facade.NationDungeonFacade;
import cn.daxiang.hbtd.gameserver.module.task.parser.AbstractTaskParser;
import cn.daxiang.hbtd.gameserver.module.task.type.TaskConditionType;
import cn.daxiang.protocol.game.TaskProtocol;
import cn.daxiang.shared.module.nationboss.NationDungeon;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;

/**
 * 军团副本关卡通关次数任务
 */
@Component
public class NationDungeonStoryPassTimesTaskParser extends AbstractTaskParser<NationDungeonActorRefreshEvent> {
    @Autowired
    private NationDungeonFacade NationDungeonFacade;

    @Override
    public void init(Task task, TaskConfig taskConfig) {
        Collection<NationDungeon> nationDungeonList = NationDungeonFacade.getNationDungeonList(task.getActorId());
        if (nationDungeonList.isEmpty()) {
            return;
        }
        int value = 0;
        for (NationDungeon nationDungeon : nationDungeonList) {
            if (nationDungeon.getDeadTime() != 0) {
                value++;
            }
            if (value >= taskConfig.getValue()) {
                break;
            }
        }
        if (task.getValue() < value) {
            task.setValue(value);
        }
        if (task.getValue() >= taskConfig.getValue()) {
            task.setStatus(TaskProtocol.TaskStatus.TASK_FINISH);
        }
    }

    @Override
    protected TaskConditionType getType() {
        return TaskConditionType.NATION_DUNGEON_STORY_PASS_TIMES;
    }

    @Override
    protected boolean parseCondition(NationDungeonActorRefreshEvent event, Task task, TaskConfig taskConfig) {
        //因为我这里每次都是重新拉数据过来算，为了减少计算量所以增加判断当任务完成时不再计算。 一般情况下都是不加这个判断的
        if (task.getStatus() == TaskProtocol.TaskStatus.TASK_FINISH) {
            return false;
        }

        int value = 0;
        for (Collection<NationDungeon> nationDungeonList : event.nationDungeonMap.values()) {
            for (NationDungeon nationDungeon : nationDungeonList) {
                if (nationDungeon.getDeadTime() != 0) {
                    value++;
                }
            }
        }
        if (task.getValue() < value) {
            task.setValue(value);
        }
        if (task.getValue() >= taskConfig.getValue()) {
            task.setStatus(TaskProtocol.TaskStatus.TASK_FINISH);
        }
        return true;
    }

}
