package cn.daxiang.hbtd.gameserver.core.dataconfig.service;

import cn.daxiang.framework.dataconfig.ConfigServiceAdapter;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.Activity78ChargeRewardConfig;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;
import java.util.Optional;

/**
 * 霓裳罗衣-战令
 *
 * <AUTHOR>
 * @date 2025/2/19
 */
@Component
public class Activity78ConfigService extends ConfigServiceAdapter {

    /**
     * key:data,value:{key:chargeId,value:config}
     */
    private static final Map<Integer, Map<Integer, Activity78ChargeRewardConfig>> ACTIVITY_78_CHARGE_REWARD_CONFIG = Maps.newHashMap();

    @Override
    public void initialize() {

        Collection<Activity78ChargeRewardConfig> chargeRewardConfigs = dataConfig.listAll(this, Activity78ChargeRewardConfig.class);
        for (Activity78ChargeRewardConfig config : chargeRewardConfigs) {
            if (config.getChargeId() != 0) {
                Map<Integer, Activity78ChargeRewardConfig> map = ACTIVITY_78_CHARGE_REWARD_CONFIG.computeIfAbsent(config.getData(), x -> Maps.newHashMap());
                map.put(config.getChargeId(), config);
            }
        }
    }

    @Override
    protected void clean() {
        ACTIVITY_78_CHARGE_REWARD_CONFIG.clear();
    }

    public static Optional<Activity78ChargeRewardConfig> getActivity78ChargeRewardConfig(int data, int chargeId) {
        Map<Integer, Activity78ChargeRewardConfig> map = ACTIVITY_78_CHARGE_REWARD_CONFIG.get(data);
        if (map == null) {
            return Optional.empty();
        }
        return Optional.ofNullable(map.get(chargeId));
    }
}
