package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.dataconfig.annotation.FieldIgnore;
import cn.daxiang.framework.extend.KeyValue;
import cn.daxiang.framework.identity.IdentiyKey;
import com.alibaba.fastjson.JSONArray;

import java.util.Collection;

/**
 * 技能组配置
 *
 * <AUTHOR>
 * @date 2023/12/23
 */
@DataFile(fileName = "skill_group_config")
public class SkillGroupConfig implements ModelAdapter {
    /**
     * 技能组ID
     */
    private int id;
    /**
     * 技能列表
     * [skillId,skillId]
     */
    private String skillIds;
    /**
     * 八门触发技能
     * [skillId,rate]
     */
    private String triggerSkill;
    @FieldIgnore
    private Collection<Integer> skillIdList;
    @FieldIgnore
    private KeyValue<Integer, Integer> triggerSkillProb = new KeyValue<>(0, 0);

    @Override
    public void initialize() {
        skillIdList = JSONArray.parseArray(skillIds, Integer.class);
        JSONArray triggerSkillArray = JSONArray.parseArray(triggerSkill);
        if (!triggerSkillArray.isEmpty()) {
            triggerSkillProb.setKey(triggerSkillArray.getIntValue(0));
            triggerSkillProb.setValue(triggerSkillArray.getIntValue(1));
        }
    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(id);
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public Collection<Integer> getSkillIdList() {
        return skillIdList;
    }

    public void setSkillIdList(Collection<Integer> skillIdList) {
        this.skillIdList = skillIdList;
    }

    public KeyValue<Integer, Integer> getTriggerSkillProb() {
        return triggerSkillProb;
    }

    public void setTriggerSkillProb(KeyValue<Integer, Integer> triggerSkillProb) {
        this.triggerSkillProb = triggerSkillProb;
    }
}
