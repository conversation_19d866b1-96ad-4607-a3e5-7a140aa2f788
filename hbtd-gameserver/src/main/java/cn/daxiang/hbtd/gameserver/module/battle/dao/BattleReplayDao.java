package cn.daxiang.hbtd.gameserver.module.battle.dao;

import cn.daxiang.hbtd.gameserver.core.database.table.BattleReplay;
import cn.daxiang.shared.module.battle.BattleType;

import java.util.Collection;

/**
 * <AUTHOR>
 */
public interface BattleReplayDao {

    /**
     * 获取战斗回放
     *
     * @param battleReplayId
     * @return
     */
    BattleReplay getBattleReplay(long battleReplayId);

    /**
     * 创建战斗回放
     *
     * @param actorId
     * @param battleReplayRecord
     * @return
     */
    BattleReplay createBattleReply(long actorId, byte[] battleReplayRecord, long createTime, BattleType battleType);

    /**
     * 删除战斗回放列表
     *
     * @param battleReplayIds
     */
    void deleteBattleReply(Collection<Long> battleReplayIds);
}
