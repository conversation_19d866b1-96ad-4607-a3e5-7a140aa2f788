package cn.daxiang.hbtd.gameserver.module.battle.parser.seek.impl;

import cn.daxiang.hbtd.gameserver.core.dataconfig.model.SkillSeekConfig;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleRoom;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleSprite;
import cn.daxiang.hbtd.gameserver.module.battle.parser.seek.AbstractSkillSeekParser;
import cn.daxiang.hbtd.gameserver.module.battle.type.SkillSeekType;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;

@Component
public class SpellArmorMaxSkillSeekParser extends AbstractSkillSeekParser {

    private static SpriteSpellArmorMaxComparator COMPARATOR = new SpriteSpellArmorMaxComparator();

    @Override
    protected SkillSeekType getType() {
        return SkillSeekType.SPELL_ARMOR_MAX;
    }

    @Override
    protected List<BattleSprite> seekSprites(BattleSprite attacker, BattleRoom battleRoom, SkillSeekConfig skillSeekConfig) {
        List<BattleSprite> seekSprites = Lists.newArrayList();
        List<BattleSprite> targetSprites = this.getTargetSpriteList(attacker, battleRoom, skillSeekConfig);
        int targetCount = Math.min(targetSprites.size(), skillSeekConfig.getTargetCount());
        Collections.sort(targetSprites, COMPARATOR);
        for (int i = 0; i < targetCount; i++) {
            BattleSprite targetSprite = targetSprites.remove(0);
            seekSprites.add(targetSprite);
        }
        return seekSprites;
    }
}

class SpriteSpellArmorMaxComparator implements Comparator<BattleSprite> {

    @Override
    public int compare(BattleSprite o1, BattleSprite o2) {
        long o1SpellArmor = o1.getSpriteBattle().getSpellArmor();
        long o2SpellArmor = o2.getSpriteBattle().getSpellArmor();
        if (o1SpellArmor > o2SpellArmor) {
            return -1;
        } else if (o1SpellArmor < o2SpellArmor) {
            return 1;
        } else {
            if (o1.getRoleId() < o2.getRoleId()) {
                return -1;
            } else if (o1.getRoleId() > o2.getRoleId()) {
                return 1;
            }
        }
        return 0;
    }
}
