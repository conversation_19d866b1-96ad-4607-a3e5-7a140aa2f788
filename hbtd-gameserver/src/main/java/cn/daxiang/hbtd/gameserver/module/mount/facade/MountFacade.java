package cn.daxiang.hbtd.gameserver.module.mount.facade;

import cn.daxiang.framework.result.CollectionResult;
import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.hbtd.gameserver.core.database.table.Mount;
import cn.daxiang.hbtd.gameserver.core.database.table.MountActor;
import cn.daxiang.hbtd.gameserver.core.oss.type.OperationType;
import cn.daxiang.hbtd.gameserver.module.mount.module.MountPrivilegeEntity;
import cn.daxiang.hbtd.gameserver.module.mount.type.MountPrerogativeType;
import cn.daxiang.protocol.game.CommonProtocol;
import cn.daxiang.protocol.game.CommonProtocol.RewardResult;
import cn.daxiang.protocol.game.MountProtocol.MountGachaResultResponse;
import cn.daxiang.protocol.game.MountProtocol.MountInfoResponse;

import java.util.Collection;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/4/8
 */
public interface MountFacade {
    /**
     * 获取红颜信息
     *
     * @param actorId
     * @return
     */
    TResult<MountInfoResponse> getMountInfos(long actorId);

    /**
     * 获取红颜个人信息
     *
     * @param actorId
     * @return
     */
    public TResult<MountActor> getMountActor(long actorId);

    /**
     * 获取红颜信息
     *
     * @param actorId
     * @return
     */
    Collection<Mount> getMountList(long actorId);

    /**
     * 激活红颜
     *
     * @param actorId
     * @param cid
     * @return
     */
    Result mountActivation(long actorId, int cid);

    /**
     * 获取单个红颜
     *
     * @param actorId
     * @param cid
     * @return
     */
    TResult<Mount> getMountById(long actorId, int cid);

    /**
     * 创建红颜
     *
     * @param actorId
     * @param rewardMap
     * @param operationType
     * @return
     */
    CollectionResult<Mount> creatMount(long actorId, Map<Integer, Long> rewardMap, OperationType operationType);

    /**
     * 红颜升星
     *
     * @param actorId
     * @param cid
     * @return
     */
    Result mountStarUp(long actorId, int cid);

    /**
     * 铜雀台升级
     *
     * @param actorId
     * @return
     */
    Result mountPlatformUpgrade(long actorId);

    /**
     * 红颜招募积分兑换礼包
     *
     * @param actorId
     * @param integral
     * @return
     */
    TResult<RewardResult> mountIntegralReward(long actorId, int integral);

    /**
     * 红颜特权礼包领取
     *
     * @param actorId
     * @return
     */
    TResult<RewardResult> mountPrivilegeRewardReceive(long actorId, MountPrerogativeType type);

    /**
     * 选择心愿红颜（特权功能）
     *
     * @param actorId
     * @param bountCid
     * @return
     */
    Result mountPrivilegeChooseWish(long actorId, int bountCid);

    /**
     * 红颜招募
     *
     * @param actorId
     * @param times
     * @param useGoods
     * @return
     */
    TResult<MountGachaResultResponse> mountGacha(long actorId, int times, boolean useGoods);

    /**
     * 领取充值礼包
     *
     * @param actorId
     * @param configId
     * @return
     */
    TResult<RewardResult> receiveMountGachaChargeReward(long actorId, int configId);

    /**
     * 更新坐骑的英雄id
     *
     * @param actorId
     * @param mountCid
     * @param heroId
     */
    void updateMountHeroUid(long actorId, int mountCid, long heroId);

}
