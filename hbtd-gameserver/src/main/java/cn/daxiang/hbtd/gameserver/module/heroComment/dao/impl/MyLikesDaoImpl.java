package cn.daxiang.hbtd.gameserver.module.heroComment.dao.impl;

import cn.daxiang.framework.database.SingleEntity;
import cn.daxiang.framework.database.dao.SingleEntityDaoImpl;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.hbtd.gameserver.core.database.table.MyLikes;
import cn.daxiang.hbtd.gameserver.module.heroComment.dao.MyLikesDao;
import org.springframework.stereotype.Component;

@Component
public class MyLikesDaoImpl extends SingleEntityDaoImpl implements MyLikesDao {

    @Override
    protected Class<? extends SingleEntity<?>> forClass() {
        return MyLikes.class;
    }

    @Override
    protected void initMaxId() {
    }

    @Override
    public MyLikes getMyLikesList(long actorId) {
        MyLikes table = this.get(IdentiyKey.build(actorId));
        if (table.newEntity()) {
            table.setNewEntity(false);
            this.updateQueue(table);
        }
        return table;
    }
}
