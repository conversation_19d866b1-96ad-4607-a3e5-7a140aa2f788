package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.dataconfig.annotation.FieldIgnore;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.shared.reward.RewardObject;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;

import java.util.Collection;

/**
 * @author: Gary
 * @date: 2023/2/2 16:20
 * @Description:
 */
@DataFile(fileName = "relic_gacha_rewards_config")
public class RelicGachaRewardsConfig implements ModelAdapter {
    /**
     * 类型
     */
    private int type;
    /**
     * id
     */
    private int id;
    /**
     * 所需经验值
     */
    private int value;
    /**
     * 奖励列表
     */
    private String rewards;
    /**
     * 奖励列表
     */
    @FieldIgnore
    private Collection<RewardObject> rewardList = Lists.newArrayList();

    @Override
    public void initialize() {
        JSONArray rewardArray = JSON.parseArray(rewards);
        for (Object rewardItem : rewardArray) {
            JSONArray itemArray = JSON.parseArray(rewardItem.toString());
            RewardObject rewardObject = RewardObject.valueOf(itemArray);
            rewardList.add(rewardObject);
        }
    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(type, id);
    }

    public int getType() {
        return type;
    }

    public int getId() {
        return id;
    }

    public int getValue() {
        return value;
    }

    public Collection<RewardObject> getRewardList() {
        return rewardList;
    }
}
