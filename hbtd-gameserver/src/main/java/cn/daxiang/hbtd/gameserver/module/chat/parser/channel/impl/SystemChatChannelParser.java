package cn.daxiang.hbtd.gameserver.module.chat.parser.channel.impl;

import cn.daxiang.framework.result.Result;
import cn.daxiang.hbtd.gameserver.module.chat.helper.ChatPushHelper;
import cn.daxiang.hbtd.gameserver.module.chat.model.ChatInfoReceiver;
import cn.daxiang.hbtd.gameserver.module.chat.parser.channel.AbstractChatChannelParser;
import cn.daxiang.protocol.game.ChatProtocol;
import cn.daxiang.protocol.game.TypeProtocol;
import org.springframework.stereotype.Component;

import java.util.Collection;

@Component
public class SystemChatChannelParser extends AbstractChatChannelParser {

    @Override
    public Collection<ChatProtocol.ChatMessage> getCacheMessage(long actorId) {
        return cacheContext.getCacheMessage(ChatInfoReceiver.valueOf(getType()));
    }

    @Override
    public Result sendMessage(ChatInfoReceiver receiver, ChatProtocol.ChatMessage message) {
        ChatPushHelper.pushChatMessage(message);
        return cacheContext.sendMessage(receiver, message);
    }

    @Override
    protected TypeProtocol.ChatChannelType getType() {
        return TypeProtocol.ChatChannelType.CHAT_CHANNEL_TYPE_SYSTEM;
    }

}
