package cn.daxiang.hbtd.gameserver.core.event.impl;

import cn.daxiang.framework.event.ActorEvent;
import cn.daxiang.shared.event.EventKey;

/**
 * 坐骑升星事件
 *
 * <AUTHOR>
 * @date 2025/3/24
 */
public class MountStarUpEvent extends ActorEvent {
    /**
     * 坐骑Id
     */
    public int mountCid;
    /**
     * 星级
     */
    public int starLevel;

    public MountStarUpEvent(long actorId, int mountCid, int starLevel) {
        super(EventKey.MOUNT_STAR_UP_EVENT, actorId);
        this.mountCid = mountCid;
        this.starLevel = starLevel;
    }

}
