package cn.daxiang.hbtd.gameserver.module.user.parser.impl;

import cn.daxiang.framework.result.Result;
import cn.daxiang.hbtd.gameserver.module.official.helper.OfficialHelper;
import cn.daxiang.hbtd.gameserver.module.user.parser.AbstractActorLockParser;
import cn.daxiang.hbtd.gameserver.module.user.type.ActorLockType;
import org.springframework.stereotype.Component;

import static cn.daxiang.shared.GameModuleStatusCodeConstant.OFFICIAL_MAX_LEVEL_NOT_ENOUGH;

/**
 * <AUTHOR>
 * @date 2022/6/17
 */
@Component
public class OfficialLevelLockParser extends AbstractActorLockParser {
    @Override
    protected ActorLockType getType() {
        return ActorLockType.OFFICIAL_LEVEL;
    }

    @Override
    public Result unlock(long actorId, int condition) {
        if (OfficialHelper.getMaxOfficialLevel(actorId) < condition) {
            return Result.valueOf(OFFICIAL_MAX_LEVEL_NOT_ENOUGH);
        }
        return Result.valueOf();
    }
}
