package cn.daxiang.hbtd.gameserver.module.sanctuary.facade;

import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.hbtd.gameserver.core.database.table.SanctuaryBuilding;
import cn.daxiang.protocol.game.SanctuaryProtocol;

/**
 * <AUTHOR>
 * @date 2023/8/28
 */
public interface SanctuaryBuildingFacade {

    /**
     * 获取归藏秘境玩家建筑养成信息
     *
     * @param actorId
     * @return
     */
    TResult<SanctuaryBuilding> getSanctuaryBuilding(long actorId);

    /**
     * 归葬秘境建筑装备合成并且穿戴
     *
     * @param actorId
     * @param type
     * @param position
     * @param configId
     * @return
     */
    Result synthesizeAndEquip(long actorId, SanctuaryProtocol.SanctuaryBuildingType type, int position, int configId);

    /**
     * 归葬秘境建筑装备穿戴
     *
     * @param actorId
     * @param type
     * @param position
     * @return
     */
    Result equip(long actorId, SanctuaryProtocol.SanctuaryBuildingType type, int position, long sanctuaryEquipmentId);

    /**
     * 归葬秘境主建筑升级
     *
     * @param actorId
     * @return
     */
    Result mainLevelUp(long actorId);

    /**
     * 归葬秘境阵营建筑升阶段
     *
     * @param actorId
     * @param campId
     * @return
     */
    Result campLevelUp(long actorId, int campId);

    /**
     * 赛季结束清除所有玩家的归葬秘境建筑信息
     */
    void cleanSanctuaryBuilding(long actorId);
}
