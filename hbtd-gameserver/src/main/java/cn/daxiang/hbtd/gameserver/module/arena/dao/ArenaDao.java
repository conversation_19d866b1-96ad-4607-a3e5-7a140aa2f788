package cn.daxiang.hbtd.gameserver.module.arena.dao;

import cn.daxiang.framework.utils.rank.cache.ILineRankToProvider;
import cn.daxiang.hbtd.gameserver.core.database.table.Arena;
import cn.daxiang.hbtd.gameserver.module.arena.model.ArenaRank;

import java.util.Collection;
import java.util.Map;

public interface ArenaDao extends ILineRankToProvider<Long, ArenaRank> {
    /**
     * 获取竞技场信息
     *
     * @param actorId
     * @return
     */
    Arena getArena(long actorId);

    /**
     * 获取竞技场排名
     *
     * @param rank
     * @return
     */
    ArenaRank getArenaRank(long rank);

    /**
     * 获取竞技场排名
     *
     * @param
     * @return
     */
    long getArenaRankByActorId(long actorId);

    /**
     * 交换排名
     *
     * @param challengeRank
     * @param opponentRank
     */
    void swapRank(long challengeRank, long opponentRank);

    /**
     * 获取最后一名排名
     *
     * @return
     */
    long getLastRank();

    /**
     * 获取竞技场排名
     * key:rank,value:actorId
     *
     * @return
     */
    Map<Long, Long> getArenaRankMap();

    /**
     * 获取竞技排行页数
     *
     * @return
     */
    int getArenaRankPages();

    /**
     * 获取竞技排行列表
     *
     * @param page
     * @return
     */
    Collection<ArenaRank> getArenaRanks(int page);

}
