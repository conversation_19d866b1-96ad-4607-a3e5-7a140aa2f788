package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.dataconfig.annotation.FieldIgnore;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.shared.reward.RewardObject;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;

import java.util.Collection;

/**
 * @author: Gary
 * @date: 2022/12/31 13:29
 * @Description:
 */
@DataFile(fileName = "gvg_rank_reward_config")
public class GVGRankRewardConfig implements ModelAdapter {
    /**
     * 排行榜类型
     */
    private int type;
    /**
     * 排名
     */
    private long rank;
    /**
     * 奖励列表
     * [[rewardType,id,num],[rewardType,id,num]]
     */
    private String rankRewards;
    /**
     * 奖励列表
     */
    @FieldIgnore
    private Collection<RewardObject> rewardList = Lists.newArrayList();

    @Override
    public void initialize() {
        JSONArray rewardsArray = JSONObject.parseArray(rankRewards);
        for (Object reward : rewardsArray) {
            JSONArray rewardArray = JSONObject.parseArray(reward.toString());
            RewardObject rewardObject = RewardObject.valueOf(rewardArray);
            rewardList.add(rewardObject);
        }
    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(type, rank);
    }

    public int getType() {
        return type;
    }

    public long getRank() {
        return rank;
    }

    public Collection<RewardObject> getRewardList() {
        return rewardList;
    }
}
