package cn.daxiang.hbtd.gameserver.module.beauty.facade.impl;

import cn.daxiang.framework.event.GameEvent;
import cn.daxiang.framework.event.TriggerTime;
import cn.daxiang.framework.event.annotation.Event;
import cn.daxiang.framework.event.annotation.EventOnline;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.framework.result.CollectionResult;
import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.framework.utils.DateUtils;
import cn.daxiang.framework.utils.FormulaUtils;
import cn.daxiang.framework.utils.RandomUtils;
import cn.daxiang.hbtd.gameserver.core.GameBaseFacade;
import cn.daxiang.hbtd.gameserver.core.database.table.Beauty;
import cn.daxiang.hbtd.gameserver.core.database.table.BeautyExtension;
import cn.daxiang.hbtd.gameserver.core.dataconfig.convert.impl.IntListConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.BeautyConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.BeautyLikabilityConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.BeautyScriptConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.BeautyStarConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.GoodsConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.BeautyConfigService;
import cn.daxiang.hbtd.gameserver.core.dispatch.DispatchHelper;
import cn.daxiang.hbtd.gameserver.core.event.impl.BeautyActivationEvent;
import cn.daxiang.hbtd.gameserver.core.event.impl.BeautyDateEvent;
import cn.daxiang.hbtd.gameserver.core.event.impl.BeautyLikabilityUpEvent;
import cn.daxiang.hbtd.gameserver.core.event.impl.BeautyStarUpEvent;
import cn.daxiang.hbtd.gameserver.core.event.impl.LineupPowerRefreshEvent;
import cn.daxiang.hbtd.gameserver.core.oss.GameOssLogger;
import cn.daxiang.hbtd.gameserver.core.oss.type.OperationType;
import cn.daxiang.hbtd.gameserver.module.beauty.dao.BeautyDao;
import cn.daxiang.hbtd.gameserver.module.beauty.dao.BeautyExtensionDao;
import cn.daxiang.hbtd.gameserver.module.beauty.facade.BeautyFacade;
import cn.daxiang.hbtd.gameserver.module.beauty.helper.BeautyHelper;
import cn.daxiang.hbtd.gameserver.module.beauty.helper.BeautyPushHelper;
import cn.daxiang.hbtd.gameserver.module.goods.helper.RewardHelper;
import cn.daxiang.hbtd.gameserver.module.user.helper.ActorHelper;
import cn.daxiang.hbtd.gameserver.module.user.type.ActorUnlockType;
import cn.daxiang.protocol.game.BeautyProtocol;
import cn.daxiang.protocol.game.CommonProtocol;
import cn.daxiang.protocol.game.TypeProtocol;
import cn.daxiang.shared.GlobalConfigKey;
import cn.daxiang.shared.event.EventKey;
import cn.daxiang.shared.module.lineup.SpriteAttributeType;
import cn.daxiang.shared.reward.RewardObject;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.TreeMap;
import java.util.TreeSet;

/**
 * @Author: Gary
 * @Date 2022-10-28 11:55
 * @Description:
 */
@Component
public class BeautyFacadeImpl extends GameBaseFacade implements BeautyFacade {
    @Autowired
    private BeautyDao beautyDao;
    @Autowired
    private BeautyExtensionDao beautyExtensionDao;

    @Override
    public TResult<BeautyProtocol.BeautyInfoResponse> getBeautyInfos(long actorId, int configId) {
        Collection<Beauty> beautyList = beautyDao.getBeautyList(actorId);
        BeautyExtension beautyExtension = beautyExtensionDao.getBeautyExtension(actorId);
        return TResult.sucess(BeautyHelper.buildBeautyInfoResponse(beautyList, beautyExtension));
    }

    @Override
    public Result beautyActivation(long actorId, int configId) {
        Result res = ActorHelper.unlock(actorId, ActorUnlockType.BEAUTY, 1);
        if (res.isFail()) {
            return res;
        }
        TResult<Beauty> tResult = this.getBeautyById(actorId, configId);
        if (!tResult.isFail()) {
            return Result.valueOf(BEAUTY_ALREADY_ACTIVATION);
        }
        BeautyConfig beautyConfig = globalConfigService.findConfig(IdentiyKey.build(configId), BeautyConfig.class);
        if (beautyConfig == null) {
            return Result.valueOf(CONFIG_NOT_FOUND);
        }
        Optional<TreeMap<Integer, BeautyLikabilityConfig>> optional = BeautyConfigService.getLikabilityConfigByBeautyId(configId);
        if (!optional.isPresent()) {
            return Result.valueOf(CONFIG_NOT_FOUND);
        }
        Result result = RewardHelper.decrease(actorId, beautyConfig.getFragmentCosts(), OperationType.BEAUTY);
        if (result.isFail()) {
            return result;
        }
        Collection<Integer> scriptIds = Lists.newArrayList(optional.get().firstEntry().getValue().getScriptIds());
        Collection<Integer> interactionIds = optional.get().firstEntry().getValue().getInteractionIds();
        Map<Integer, Integer> baseAttributeMap = Maps.newHashMap();
        baseAttributeMap.putAll(beautyConfig.getBaseAttributeMap());
        Beauty beauty = beautyDao.creatBeauty(actorId, configId, baseAttributeMap, scriptIds, interactionIds);
        BeautyExtension beautyExtension = beautyExtensionDao.getBeautyExtension(actorId);
        BeautyPushHelper.pushBeautyInfo(actorId, Lists.newArrayList(beauty), beautyExtension);
        DispatchHelper.postEvent(new LineupPowerRefreshEvent(actorId));
        DispatchHelper.postEvent(new BeautyActivationEvent(actorId, configId));
        return Result.valueOf();
    }

    @Override
    public CollectionResult<Beauty> creatBeauty(long actorId, Map<Integer, Long> rewardMap, OperationType operationType) {
        Collection<Beauty> collection = Lists.newArrayList();
        for (int configId : rewardMap.keySet()) {
            BeautyConfig beautyConfig = globalConfigService.findConfig(IdentiyKey.build(configId), BeautyConfig.class);
            if (beautyConfig == null) {
                return CollectionResult.valueOf(CONFIG_NOT_FOUND);
            }
            Optional<TreeMap<Integer, BeautyLikabilityConfig>> optional = BeautyConfigService.getLikabilityConfigByBeautyId(configId);
            if (!optional.isPresent()) {
                return CollectionResult.valueOf(CONFIG_NOT_FOUND);
            }
            Collection<Integer> scriptIds = Lists.newArrayList(optional.get().firstEntry().getValue().getScriptIds());
            Collection<Integer> interactionIds = optional.get().firstEntry().getValue().getInteractionIds();
            Map<Integer, Integer> baseAttributeMap = Maps.newHashMap();
            baseAttributeMap.putAll(beautyConfig.getBaseAttributeMap());
            Beauty beauty = beautyDao.creatBeauty(actorId, configId, baseAttributeMap, scriptIds, interactionIds);
            GameOssLogger.goodsAdd(actorId, operationType, TypeProtocol.RewardType.BEAUTY, beauty.getConfigId(), beauty.getConfigId(), 1, 1);
            collection.add(beauty);
            DispatchHelper.postEvent(new BeautyActivationEvent(actorId, configId));
        }
        BeautyExtension beautyExtension = beautyExtensionDao.getBeautyExtension(actorId);
        BeautyPushHelper.pushBeautyInfo(actorId, collection, beautyExtension);
        DispatchHelper.postEvent(new LineupPowerRefreshEvent(actorId));
        return CollectionResult.collection(collection);
    }

    @Override
    public Result beautyStarUp(long actorId, int configId) {
        Result res = ActorHelper.unlock(actorId, ActorUnlockType.BEAUTY, 4);
        if (res.isFail()) {
            return res;
        }
        TResult<Beauty> tResult = this.getBeautyById(actorId, configId);
        if (tResult.isFail()) {
            return Result.valueOf(tResult.statusCode);
        }
        Beauty beauty = tResult.item;
        int starLevel = beauty.getStarLevel();
        BeautyStarConfig config = globalConfigService.findConfig(IdentiyKey.build(configId, starLevel), BeautyStarConfig.class);
        if (config == null) {
            return Result.valueOf(CONFIG_NOT_FOUND);
        }
        Result result = RewardHelper.decrease(actorId, config.getCostReward(), OperationType.BEAUTY);
        if (result.isFail()) {
            return result;
        }
        beauty.setStarLevel(starLevel + 1);
        dbQueue.updateQueue(beauty);
        BeautyExtension beautyExtension = beautyExtensionDao.getBeautyExtension(actorId);
        BeautyPushHelper.pushBeautyInfo(actorId, Lists.newArrayList(beauty), beautyExtension);
        DispatchHelper.postEvent(new LineupPowerRefreshEvent(actorId));
        DispatchHelper.postEvent(new BeautyStarUpEvent(actorId, configId, beauty.getStarLevel()));
        return Result.valueOf();
    }

    @Override
    public Result beautyCharmUp(long actorId, int configId, Map<Integer, Integer> costsMap) {
        Result res = ActorHelper.unlock(actorId, ActorUnlockType.BEAUTY, 2);
        if (res.isFail()) {
            return res;
        }
        if (costsMap.values().stream().anyMatch(x -> x <= 0)) {
            return Result.valueOf(INVALID_PARAM);
        }
        TResult<Beauty> tResult = this.getBeautyById(actorId, configId);
        if (tResult.isFail()) {
            return Result.valueOf(tResult.statusCode);
        }
        Beauty beauty = tResult.item;
        BeautyConfig beautyConfig = globalConfigService.findConfig(IdentiyKey.build(configId), BeautyConfig.class);
        if (beautyConfig == null) {
            return Result.valueOf(CONFIG_NOT_FOUND);
        }
        Map<Integer, Integer> beautyAttributes = beauty.getBeautyAttributes();
        Map<Integer, Integer> limitAttributeMap = beautyConfig.getLimitAttributeMap();
        Collection<Integer> goodsIds = globalConfigService.findGlobalObject(GlobalConfigKey.BEAUTY_CHARM_UP_GOODS_ID, IntListConfig.class).findValues();
        Collection<RewardObject> costs = Lists.newArrayList();
        Map<Integer, Integer> addMap = Maps.newHashMap();
        for (Map.Entry<Integer, Integer> entry : costsMap.entrySet()) {
            int id = entry.getKey();
            int count = entry.getValue();
            if (!goodsIds.contains(id)) {
                return Result.valueOf(INVALID_PARAM);
            }
            GoodsConfig config = globalConfigService.findConfig(IdentiyKey.build(id), GoodsConfig.class);
            if (config == null) {
                return Result.valueOf(CONFIG_NOT_FOUND);
            }
            JSONArray parseArray = JSONArray.parseArray(config.getGoodsEffectExpr());
            if (parseArray.isEmpty()) {
                return Result.valueOf(CONFIG_ERROR);
            }
            if (beautyAttributes.get(parseArray.getIntValue(0)) >= limitAttributeMap.get(parseArray.getIntValue(0))) {
                return Result.valueOf(INVALID_PARAM);
            }
            addMap.merge(parseArray.getIntValue(0), parseArray.getIntValue(1) * count, Integer::sum);
            costs.add(RewardObject.valueOf(TypeProtocol.RewardType.GOODS_VALUE, id, count));
        }
        Result decrease = RewardHelper.decrease(actorId, costs, OperationType.BEAUTY);
        if (decrease.isFail()) {
            return decrease;
        }
        beauty.beautyCharmUp(addMap, limitAttributeMap);
        dbQueue.updateQueue(beauty);
        BeautyExtension beautyExtension = beautyExtensionDao.getBeautyExtension(actorId);
        BeautyPushHelper.pushBeautyInfo(actorId, Lists.newArrayList(beauty), beautyExtension);
        DispatchHelper.postEvent(new LineupPowerRefreshEvent(actorId));
        return Result.valueOf();
    }

    @Override
    public Result beautySendGifts(long actorId, int configId, Map<Integer, Integer> costsMap) {
        Result res = ActorHelper.unlock(actorId, ActorUnlockType.BEAUTY, 3);
        if (res.isFail()) {
            return res;
        }
        if (costsMap.values().stream().anyMatch(x -> x <= 0)) {
            return Result.valueOf(INVALID_PARAM);
        }
        TResult<Beauty> tResult = this.getBeautyById(actorId, configId);
        if (tResult.isFail()) {
            return Result.valueOf(tResult.statusCode);
        }
        Beauty beauty = tResult.item;
        int oldLevel = beauty.getLikabilityLevel();
        int level = beauty.getLikabilityLevel();
        int exp = beauty.getLikabilityExp();
        Optional<TreeMap<Integer, BeautyLikabilityConfig>> optional = BeautyConfigService.getLikabilityConfigByBeautyId(configId);
        if (!optional.isPresent()) {
            return Result.valueOf(CONFIG_NOT_FOUND);
        }
        TreeMap<Integer, BeautyLikabilityConfig> levelMap = optional.get();
        if (level >= levelMap.lastKey()) {
            return Result.valueOf(INVALID_PARAM);
        }
        BeautyConfig beautyConfig = globalConfigService.findConfig(IdentiyKey.build(configId), BeautyConfig.class);
        if (beautyConfig == null) {
            return Result.valueOf(CONFIG_NOT_FOUND);
        }
        int multiple = globalConfigService.findGlobalConfig(GlobalConfigKey.BEAUTY_LIKING_ADD_MULTIPLE).findInt();
        Collection<RewardObject> costs = Lists.newArrayList();
        for (Map.Entry<Integer, Integer> entry : costsMap.entrySet()) {
            int id = entry.getKey();
            GoodsConfig config = globalConfigService.findConfig(IdentiyKey.build(id), GoodsConfig.class);
            if (config == null) {
                return Result.valueOf(CONFIG_NOT_FOUND);
            }
            int value = Integer.parseInt(config.getGoodsEffectExpr()) * entry.getValue();
            if (beautyConfig.getExclusivePropList().contains(id)) {
                exp += value * multiple / RandomUtils.TEN_THOUSAND;
            } else {
                exp += value;
            }
            costs.add(RewardObject.valueOf(TypeProtocol.RewardType.GOODS_VALUE, id, entry.getValue()));
        }
        for (int i = 0; i < levelMap.size(); i++) {
            int needExp = levelMap.get(level).getNeedExp();
            if (exp < needExp) {
                break;
            } else {
                exp -= needExp;
                level++;
            }
            if (levelMap.higherEntry(level) == null) {
                break;
            }
        }
        Result decrease = RewardHelper.decrease(actorId, costs, OperationType.BEAUTY);
        if (decrease.isFail()) {
            return decrease;
        }
        beauty.setLikabilityLevel(level);
        beauty.setLikabilityExp(exp);
        dbQueue.updateQueue(beauty);
        BeautyExtension beautyExtension = beautyExtensionDao.getBeautyExtension(actorId);
        BeautyPushHelper.pushBeautyInfo(actorId, Lists.newArrayList(beauty), beautyExtension);
        DispatchHelper.postEvent(new LineupPowerRefreshEvent(actorId));
        if (oldLevel != level) {
            DispatchHelper.postEvent(new BeautyLikabilityUpEvent(actorId, configId, beauty.getLikabilityLevel()));
        }
        return Result.valueOf();
    }

    @Override
    public Result beautyDate(long actorId, int configId) {
        BeautyExtension beautyExtension = beautyExtensionDao.getBeautyExtension(actorId);
        if (beautyExtension.isDating()) {
            return Result.valueOf(BEAUTY_DATING_NOW);
        }
        int dailyTimes = globalConfigService.findGlobalConfig(GlobalConfigKey.BEAUTY_DAILY_DATE_TIMES).findInt();
        if (beautyExtension.getDateTimes() >= dailyTimes) {
            return Result.valueOf(BEAUTY_DATE_TIMES_NOT_ENOUGH);
        }
        TResult<Beauty> tResult = this.getBeautyById(actorId, configId);
        if (tResult.isFail()) {
            return Result.valueOf(tResult.statusCode);
        }
        Beauty beauty = tResult.item;
        Collection<Integer> unlockIds = Lists.newArrayList(beauty.getUnlockIds().get(1));
        if (!beauty.getPerformOverIds().containsAll(unlockIds)) {
            unlockIds.removeAll(beauty.getPerformOverIds());
        }
        int interactionId = RandomUtils.randomHit(unlockIds);
        Optional<TreeMap<Integer, BeautyScriptConfig>> mapOptional = BeautyConfigService.getScriptConfigByScriptId(interactionId);
        if (!mapOptional.isPresent()) {
            return Result.valueOf(CONFIG_NOT_FOUND);
        }
        CollectionResult<Integer> collectionResult = executeInteraction(1, mapOptional.get());
        if (collectionResult.isFail()) {
            return Result.valueOf(collectionResult.statusCode);
        }
        Collection<Integer> talkIds = collectionResult.item;
        beautyExtension.date(configId, interactionId, talkIds);
        beauty.date(interactionId);
        dbQueue.updateQueue(beautyExtension);
        dbQueue.updateQueue(beauty);
        BeautyPushHelper.pushBeautyInfo(actorId, Lists.newArrayList(), beautyExtension);
        DispatchHelper.postEvent(new BeautyDateEvent(actorId, 1));
        return Result.valueOf();
    }

    private CollectionResult<Integer> executeInteraction(int talkId, TreeMap<Integer, BeautyScriptConfig> treeMap) {
        TreeSet<Integer> set = Sets.newTreeSet(Lists.newArrayList(talkId));
        for (int i = 0; i < treeMap.size(); i++) {
            BeautyScriptConfig config = treeMap.get(set.last());
            if (config == null) {
                return CollectionResult.valueOf(CONFIG_NOT_FOUND);
            }
            if (config.getNextIds().size() == 1) {
                set.add(config.getNextIds().get(0));
            }
        }
        return CollectionResult.collection(set);
    }

    @Override
    public TResult<CommonProtocol.RewardResultResponse> beautyTalk(long actorId, int talkId) {
        BeautyExtension beautyExtension = beautyExtensionDao.getBeautyExtension(actorId);
        TreeSet<Integer> talkIds = beautyExtension.getTalkIds();
        if (!beautyExtension.isDating()) {
            return TResult.valueOf(BEAUTY_DATING_TALK_ERROR);
        }
        if (beautyExtension.getDatingConfigId() <= 0 || talkIds.isEmpty()) {
            return TResult.valueOf(BEAUTY_DATING_TALK_ERROR);
        }
        int interactionId = beautyExtension.getScriptId();
        int lastTalkId = talkIds.last();
        BeautyScriptConfig config = globalConfigService.findConfig(IdentiyKey.build(interactionId, lastTalkId), BeautyScriptConfig.class);
        if (config == null) {
            return TResult.valueOf(CONFIG_NOT_FOUND);
        }
        if (!config.getNextIds().contains(talkId)) {
            return TResult.valueOf(INVALID_PARAM);
        }
        Optional<TreeMap<Integer, BeautyScriptConfig>> mapOptional = BeautyConfigService.getScriptConfigByScriptId(interactionId);
        if (!mapOptional.isPresent()) {
            return TResult.valueOf(CONFIG_NOT_FOUND);
        }
        CollectionResult<Integer> collectionResult = executeInteraction(talkId, mapOptional.get());
        if (collectionResult.isFail()) {
            return TResult.valueOf(collectionResult.statusCode);
        }
        TreeSet<Integer> set = (TreeSet<Integer>) collectionResult.item;
        BeautyScriptConfig lastConfig = globalConfigService.findConfig(IdentiyKey.build(interactionId, set.last()), BeautyScriptConfig.class);
        if (lastConfig == null) {
            return TResult.valueOf(CONFIG_NOT_FOUND);
        }
        beautyExtension.talk(set);
        CommonProtocol.RewardResultResponse.Builder builder = CommonProtocol.RewardResultResponse.newBuilder();
        Collection<RewardObject> reward = Lists.newArrayList();
        if (lastConfig.getNextIds().isEmpty()) {
            beautyExtension.dateEnd();
            CommonProtocol.RewardResult rewardResult = RewardHelper.sendRewardList(actorId, lastConfig.getRewardList(), OperationType.BEAUTY);
            builder.setRewardResult(rewardResult);
        }
        dbQueue.updateQueue(beautyExtension);
        BeautyPushHelper.pushBeautyInfo(actorId, Lists.newArrayList(), beautyExtension);
        return TResult.sucess(builder.build());
    }

    @Event(name = EventKey.ACTOR_LOGIN)
    public void onActorLogin(GameEvent event) {
        this.reset(event.getUniqueId(), false);
    }

    @EventOnline(trigger = TriggerTime.FIXED_HOUR, triggerValue = 24)
    public void onEverySecond(GameEvent e) {
        this.reset(e.getUniqueId(), true);
    }

    private void reset(long actorId, boolean push) {
        BeautyExtension beautyExtension = beautyExtensionDao.getBeautyExtension(actorId);
        if (!DateUtils.isToday(beautyExtension.getLastRestTime())) {
            beautyExtension.dailyRest();
            dbQueue.updateQueue(beautyExtension);
        }
        if (push) {
            BeautyPushHelper.pushBeautyInfo(actorId, Lists.newArrayList(), beautyExtension);
        }
    }

    @Override
    public TResult<Beauty> getBeautyById(long actorId, int configId) {
        Beauty beauty = beautyDao.getBeauty(actorId, configId);
        if (beauty == null) {
            return TResult.valueOf(BEAUTY_NOT_FOUND);
        }
        return TResult.sucess(beauty);
    }

    @Override
    public Map<Integer, Integer> getBeautyBuildingLevel(long actorId) {
        HashMap<Integer, Integer> hashMap = Maps.newHashMap();
        Collection<Beauty> beautyList = beautyDao.getBeautyList(actorId);
        for (Beauty beauty : beautyList) {
            BeautyStarConfig config = globalConfigService.findConfig(IdentiyKey.build(beauty.getConfigId(), beauty.getStarLevel()), BeautyStarConfig.class);
            if (config != null) {
                hashMap.merge(config.getBuildingType(), config.getBuildingLevel(), Integer::sum);
            }
        }
        return hashMap;
    }

    @Override
    public Result beautyUnlockScript(long actorId, int configId, int type, int unlockId) {
        TResult<Beauty> tResult = this.getBeautyById(actorId, configId);
        if (tResult.isFail()) {
            return Result.valueOf(tResult.statusCode);
        }
        Beauty beauty = tResult.item;
        if (beauty.getUnlockIds().get(type).contains(unlockId)) {
            return Result.valueOf(INVALID_PARAM);
        }
        if (type == 1) {
            Optional<TreeMap<Integer, BeautyLikabilityConfig>> optional = BeautyConfigService.getLikabilityConfigByBeautyId(configId);
            if (!optional.isPresent()) {
                return Result.valueOf(CONFIG_NOT_FOUND);
            }
            TreeMap<Integer, BeautyLikabilityConfig> configMap = optional.get();
            Collection<Integer> scriptIds = Lists.newArrayList();
            configMap.headMap(beauty.getLikabilityLevel(), true).values().forEach(x -> scriptIds.addAll(Lists.newArrayList(x.getScriptIds())));
            if (!scriptIds.contains(unlockId)) {
                return Result.valueOf(ACTOR_NOT_UNLOCK);
            }
        }
        beauty.unlockScript(type, unlockId);
        dbQueue.updateQueue(beauty);
        BeautyExtension beautyExtension = beautyExtensionDao.getBeautyExtension(actorId);
        BeautyPushHelper.pushBeautyInfo(actorId, Lists.newArrayList(beauty), beautyExtension);
        return Result.valueOf();
    }

    @Override
    public TResult<CommonProtocol.RewardResultResponse> beautyQuicklyDate(long actorId, int configId) {
        Result res = ActorHelper.unlock(actorId, ActorUnlockType.BEAUTY, 5);
        if (res.isFail()) {
            return TResult.valueOf(res.statusCode);
        }
        BeautyExtension beautyExtension = beautyExtensionDao.getBeautyExtension(actorId);
        int dailyTimes = globalConfigService.findGlobalConfig(GlobalConfigKey.BEAUTY_DAILY_DATE_TIMES).findInt();
        int dateTimes = beautyExtension.getDateTimes();
        if (!beautyExtension.isDating() && dateTimes >= dailyTimes) {
            return TResult.valueOf(BEAUTY_DATE_TIMES_NOT_ENOUGH);
        }
        //把现有的约会谈话执行完
        Collection<RewardObject> rewards = Lists.newArrayList();
        if (beautyExtension.isDating()) {
            TreeSet<Integer> talkIds = Sets.newTreeSet(beautyExtension.getTalkIds());
            CollectionResult<RewardObject> result = quicklyExecuteAllInteraction(talkIds, beautyExtension.getScriptId());
            if (result.isFail()) {
                return TResult.valueOf(result.statusCode);
            }
            rewards.addAll(result.item);
        }
        TResult<Beauty> tResult = this.getBeautyById(actorId, configId);
        if (tResult.isFail()) {
            return TResult.valueOf(tResult.statusCode);
        }
        Beauty beauty = tResult.item;
        Collection<Integer> unlockIds = Lists.newArrayList(beauty.getUnlockIds().get(1));
        int times = dailyTimes - dateTimes;
        int interactionId = 0;
        TreeSet<Integer> talkIds = Sets.newTreeSet();
        for (int i = 0; i < times; i++) {
            if (!beauty.getPerformOverIds().containsAll(unlockIds)) {
                unlockIds.removeAll(beauty.getPerformOverIds());
            }
            interactionId = RandomUtils.randomHit(unlockIds);
            Optional<TreeMap<Integer, BeautyScriptConfig>> mapOptional = BeautyConfigService.getScriptConfigByScriptId(interactionId);
            if (!mapOptional.isPresent()) {
                return TResult.valueOf(CONFIG_NOT_FOUND);
            }
            CollectionResult<Integer> collectionResult = executeInteraction(1, mapOptional.get());
            if (collectionResult.isFail()) {
                return TResult.valueOf(collectionResult.statusCode);
            }
            talkIds = Sets.newTreeSet(collectionResult.item);
            CollectionResult<RewardObject> result = quicklyExecuteAllInteraction(talkIds, interactionId);
            if (result.isFail()) {
                return TResult.valueOf(result.statusCode);
            }
            rewards.addAll(result.item);
            unlockIds.add(interactionId);
            DispatchHelper.postEvent(new BeautyDateEvent(actorId, 1));
        }
        if (times > 0) {
            beautyExtension.quicklyDate(configId, interactionId, talkIds, dailyTimes);
            beauty.refreshPerformOverIds(unlockIds);
        }
        dbQueue.updateQueue(beautyExtension);
        dbQueue.updateQueue(beauty);
        CommonProtocol.RewardResult rewardResult = RewardHelper.sendRewardList(actorId, rewards, OperationType.BEAUTY);
        CommonProtocol.RewardResultResponse.Builder builder = CommonProtocol.RewardResultResponse.newBuilder();
        builder.setRewardResult(rewardResult);
        BeautyPushHelper.pushBeautyInfo(actorId, Lists.newArrayList(beauty), beautyExtension);
        return TResult.sucess(builder.build());
    }

    @Override
    public long getBeautyScore(long actorId) {
        long totalScore = 0l;
        Collection<Beauty> beautyList = beautyDao.getBeautyList(actorId);
        for (Beauty beauty : beautyList) {
            Map<SpriteAttributeType, Long> attributeMap = Maps.newHashMap();
            //红颜基础属性
            BeautyConfig config = globalConfigService.findConfig(IdentiyKey.build(beauty.getConfigId()), BeautyConfig.class);
            if (config == null) {
                LOGGER.error("BeautyConfig is not fount,id:{}", beauty.getConfigId());
                continue;
            }
            Map<Integer, Integer> coefficientMap = config.getTypicalCoefficientMap();
            Map<Integer, Integer> beautyAttributes = beauty.getBeautyAttributes();
            for (Map.Entry<Integer, Integer> entry : beautyAttributes.entrySet()) {
                int type = entry.getKey();
                int value = entry.getValue();
                Optional<Integer> optional = BeautyConfigService.getCoefficient(beauty.getConfigId(), type, value);
                if (!optional.isPresent()) {
                    LOGGER.error("coefficient is not fount,id:{},type:{},value:{}", beauty.getConfigId(), type, value);
                    continue;
                }
                long addValue = (long) value * coefficientMap.getOrDefault(type, 0) * optional.get() / RandomUtils.TEN_THOUSAND;
                attributeMap.merge(SpriteAttributeType.getType(type), addValue, Long::sum);
            }
            String powerExpr = globalConfigService.findGlobalConfig(GlobalConfigKey.FIGHT_POWER_EXPRESSION).getValue();
            Collection<Long> attributeList = SpriteAttributeType.getAttributeList(attributeMap);
            long power = FormulaUtils.executeRoundingLong(powerExpr, attributeList.toArray(new Long[attributeList.size()]));
            totalScore += FormulaUtils.executeRoundingLong(config.getScoreExpr(), power, beauty.getLikabilityLevel(), beauty.getStarLevel());
        }
        String strengthComparison = globalConfigService.findGlobalConfig(GlobalConfigKey.STRENGTH_COMPARISON).getValue();
        return FormulaUtils.executeRoundingLong(strengthComparison, totalScore);
    }

    private CollectionResult<RewardObject> quicklyExecuteAllInteraction(TreeSet<Integer> talkIds, int scriptId) {
        Optional<TreeMap<Integer, BeautyScriptConfig>> mapOptional = BeautyConfigService.getScriptConfigByScriptId(scriptId);
        if (!mapOptional.isPresent()) {
            return CollectionResult.valueOf(CONFIG_NOT_FOUND);
        }
        Collection<RewardObject> rewards = Lists.newArrayList();
        TreeMap<Integer, BeautyScriptConfig> treeMap = mapOptional.get();
        for (int i = 0; i < treeMap.size(); i++) {
            BeautyScriptConfig config = treeMap.get(talkIds.last());
            if (config == null) {
                return CollectionResult.valueOf(CONFIG_NOT_FOUND);
            }
            if (!config.getNextIds().isEmpty()) {
                talkIds.add(RandomUtils.randomHit(config.getNextIds()));
            } else {
                rewards.addAll(config.getRewardList());
                break;
            }
        }
        return CollectionResult.collection(rewards);
    }
}
