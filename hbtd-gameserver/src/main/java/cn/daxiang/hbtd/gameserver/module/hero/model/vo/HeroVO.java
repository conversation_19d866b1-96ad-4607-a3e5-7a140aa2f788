package cn.daxiang.hbtd.gameserver.module.hero.model.vo;

import cn.daxiang.hbtd.gameserver.core.database.table.Hero;

/**
 * 英雄显示对象
 *
 * <AUTHOR>
 */
public class HeroVO {
    /**
     * 英雄ID
     */
    private long heroId;
    /**
     * 配置ID
     */
    private int configId;
    /**
     * 阵营
     */
    private int camp;
    /**
     * 等级
     */
    private int level;
    /**
     * 经验
     */
    private int exp;
    /**
     * 突破等级
     */
    private int breakoutLevel;
    /**
     * 修炼等级
     */
    private int cultivateLevel;
    /**
     * 修炼经验
     */
    private int cultivateExp;
    /**
     * 星级
     */
    private int starLevel;
    /**
     * 渡劫等级
     */
    private int evolveLevel;
    /**
     * 英雄神器ID
     */
    private long heroArtifactId;
    /**
     * 英雄神器等级
     */
    private int heroArtifactLevel;
    /**
     * 英雄神器进阶等级
     */
    private int heroArtifactAdvanceLevel;

    public static HeroVO valueOf(Hero hero) {
        HeroVO vo = new HeroVO();
        vo.heroId = hero.getHeroId();
        vo.configId = hero.getConfigId();
        vo.camp = hero.getCamp();
        vo.level = hero.getLevel();
        vo.exp = hero.getExp();
        vo.breakoutLevel = hero.getBreakoutLevel();
        vo.cultivateLevel = hero.getCultivateLevel();
        vo.cultivateExp = hero.getCultivateExp();
        vo.starLevel = hero.getStarLevel();
        vo.evolveLevel = hero.getEvolveLevel();
        vo.heroArtifactId = hero.getHeroArtifactId();
        return vo;
    }

    public static HeroVO valueOf(Hero hero, long heroArtifactId, int heroArtifactLevel, int heroArtifactAdvanceLevel) {
        HeroVO vo = new HeroVO();
        vo.heroId = hero.getHeroId();
        vo.configId = hero.getConfigId();
        vo.camp = hero.getCamp();
        vo.level = hero.getLevel();
        vo.exp = hero.getExp();
        vo.breakoutLevel = hero.getBreakoutLevel();
        vo.cultivateLevel = hero.getCultivateLevel();
        vo.cultivateExp = hero.getCultivateExp();
        vo.starLevel = hero.getStarLevel();
        vo.evolveLevel = hero.getEvolveLevel();
        vo.heroArtifactId = heroArtifactId;
        vo.heroArtifactLevel = heroArtifactLevel;
        vo.heroArtifactAdvanceLevel = heroArtifactAdvanceLevel;
        return vo;
    }

    public long getHeroId() {
        return heroId;
    }

    public int getConfigId() {
        return configId;
    }

    public int getCamp() {
        return camp;
    }

    public int getLevel() {
        return level;
    }

    public int getExp() {
        return exp;
    }

    public int getBreakoutLevel() {
        return breakoutLevel;
    }

    public int getCultivateLevel() {
        return cultivateLevel;
    }

    public int getCultivateExp() {
        return cultivateExp;
    }

    public int getStarLevel() {
        return starLevel;
    }

    public int getEvolveLevel() {
        return evolveLevel;
    }

    public long getHeroArtifactId() {
        return heroArtifactId;
    }

    public int getHeroArtifactLevel() {
        return heroArtifactLevel;
    }

    public int getHeroArtifactAdvanceLevel() {
        return heroArtifactAdvanceLevel;
    }
}
