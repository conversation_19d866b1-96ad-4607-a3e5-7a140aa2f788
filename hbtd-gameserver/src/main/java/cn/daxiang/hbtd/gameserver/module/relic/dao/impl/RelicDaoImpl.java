package cn.daxiang.hbtd.gameserver.module.relic.dao.impl;

import cn.daxiang.framework.database.MultiEntity;
import cn.daxiang.framework.database.dao.MultiEntityDaoImpl;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.hbtd.gameserver.core.GameConfig;
import cn.daxiang.hbtd.gameserver.core.database.IdGenerator;
import cn.daxiang.hbtd.gameserver.core.database.UniqueId;
import cn.daxiang.hbtd.gameserver.core.database.table.Relic;
import cn.daxiang.hbtd.gameserver.module.relic.dao.RelicDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * @author: Gary
 * @date: 2023/1/31 15:35
 * @Description:
 */
@Component
public class RelicDaoImpl extends MultiEntityDaoImpl implements RelicDao {
    @Autowired
    private IdGenerator idGenerator;

    @Override
    protected Class<? extends MultiEntity<?>> forClass() {
        return Relic.class;
    }

    @Override
    protected void initMaxId() {
        Long maxId = jdbc.queryForObject("select max(relicId) from relic", Long.class);
        AtomicLong base = null;
        if (maxId == null || maxId <= 0) {
            base = new AtomicLong(UniqueId.otherId(GameConfig.getServerId()));
        } else {
            base = new AtomicLong(maxId);
        }
        idGenerator.init(IdentiyKey.build(this.getClass()), base);
    }

    @Override
    public Collection<Relic> getRelicList(long actorId) {
        Map<IdentiyKey, Relic> relicMap = this.getByFk(actorId);
        return relicMap.values();
    }

    @Override
    public Collection<Relic> getAllRelicByConfigId(long actorId, int configId) {
        Map<IdentiyKey, Relic> relicMap = this.getByFk(actorId);
        return relicMap.values().stream().filter(x -> x.getConfigId() == configId).collect(Collectors.toList());
    }

    @Override
    public Relic getRelic(long actorId, long relicId) {
        return this.getMultiEnity(actorId, IdentiyKey.build(relicId));
    }

    @Override
    public void deleteRelic(Collection<Relic> relics) {
        for (Relic relic : relics) {
            this.delete(relic);
        }
    }

    @Override
    public Relic creatRelic(long actorId, int id) {
        long uid = this.idGenerator.increment(IdentiyKey.build(this.getClass()));
        Relic relic = Relic.valueOf(actorId, uid, id);
        updateQueue(relic);
        return relic;
    }
}
