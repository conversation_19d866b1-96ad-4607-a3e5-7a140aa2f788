package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.dataconfig.annotation.FieldIgnore;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.shared.reward.RewardObject;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;

import java.util.Collection;

/**
 * 传送带礼包配置
 *
 * <AUTHOR>
 * @date 2025/2/19
 */
@DataFile(fileName = "activity_72_config")
public class Activity72Config implements ModelAdapter {
    /**
     * 活动DATA
     */
    private int data;
    /**
     * 顺序自增ID
     */
    private int order;
    /**
     * 充值ID
     */
    private int chargeId;
    /**
     * 扣除物品
     */
    private String cost;
    /**
     * 普通战令奖励
     */
    private String rewards;

    @FieldIgnore
    private final Collection<RewardObject> costList = Lists.newArrayList();

    @FieldIgnore
    private final Collection<RewardObject> rewardList = Lists.newArrayList();

    @Override
    public void initialize() {
        for (Object reward : JSONObject.parseArray(cost)) {
            JSONArray rewardArray = JSONObject.parseArray(reward.toString());
            costList.add(RewardObject.valueOf(rewardArray));
        }

        for (Object reward : JSONObject.parseArray(rewards)) {
            JSONArray rewardArray = JSONObject.parseArray(reward.toString());
            rewardList.add(RewardObject.valueOf(rewardArray));
        }
    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(data, order);
    }

    public int getData() {
        return data;
    }

    public int getOrder() {
        return order;
    }

    public int getChargeId() {
        return chargeId;
    }

    public Collection<RewardObject> getCostList() {
        return costList;
    }

    public Collection<RewardObject> getRewardList() {
        return rewardList;
    }
}
