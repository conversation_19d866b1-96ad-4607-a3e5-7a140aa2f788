package cn.daxiang.hbtd.gameserver.module.publicity.facade.impl;

import cn.daxiang.framework.extend.KeyValue;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.hbtd.gameserver.core.GameBaseFacade;
import cn.daxiang.hbtd.gameserver.core.database.table.CityScene;
import cn.daxiang.hbtd.gameserver.core.database.table.SpacetimeBeauty;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.CitySceneConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.SpacetimeBeautyAwakenConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.SpacetimeBeautyConfig;
import cn.daxiang.hbtd.gameserver.module.publicity.dao.CitySceneDao;
import cn.daxiang.hbtd.gameserver.module.publicity.facade.CitySceneFacade;
import cn.daxiang.hbtd.gameserver.module.publicity.helper.CityScenePushHelper;
import cn.daxiang.hbtd.gameserver.module.spacetimeBeauty.dao.SpacetimeBeautyDao;
import cn.daxiang.hbtd.gameserver.module.user.helper.ActorHelper;
import cn.daxiang.hbtd.gameserver.module.user.type.ActorUnlockType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: Gary
 * @date: 2023/10/27 16:08
 * @Description:
 */
@Component
public class CitySceneFacadeImpl extends GameBaseFacade implements CitySceneFacade {
    @Autowired
    private CitySceneDao citySceneDao;
    @Autowired
    private SpacetimeBeautyDao spacetimeBeautyDao;

    @Override
    public TResult<CityScene> getCitySceneInfo(long actorId) {
        Result res = ActorHelper.unlock(actorId, ActorUnlockType.SPACETIME_BEAUTY, 2);
        if (res.isFail()) {
            return TResult.valueOf(res.statusCode);
        }
        CityScene cityScene = citySceneDao.getCitySceneInfo(actorId);
        return TResult.sucess(cityScene);
    }

    @Override
    public TResult<CityScene> changeCityScene(long actorId, int citySceneId, int spacetimeBeautyId) {
        TResult<CityScene> result = getCitySceneInfo(actorId);
        if (result.isFail()) {
            return TResult.valueOf(result.statusCode);
        }
        CityScene cityScene = result.item;
        //校验主城
        if (citySceneId != 0) {
            CitySceneConfig citySceneConfig = globalConfigService.findConfig(IdentiyKey.build(citySceneId), CitySceneConfig.class);
            if (citySceneConfig == null) {
                return TResult.valueOf(CONFIG_NOT_FOUND);
            }
            KeyValue<Integer, Integer> keyValue = citySceneConfig.getUnlockCondition();
            int configId = keyValue.getKey();
            int star = 0;
            if (configId != 0) {
                SpacetimeBeauty sceneBeauty = spacetimeBeautyDao.getSpacetimeBeauty(actorId, configId);
                if (sceneBeauty == null) {
                    return TResult.valueOf(SPACETIME_BEAUTY_IS_NOT_ACTIVATE);
                }
                for (Integer awakenIndex : sceneBeauty.getAwakenList()) {
                    SpacetimeBeautyAwakenConfig awakenConfig = globalConfigService.findConfig(IdentiyKey.build(configId, awakenIndex), SpacetimeBeautyAwakenConfig.class);
                    if (awakenConfig == null) {
                        return TResult.valueOf(CONFIG_NOT_FOUND);
                    }
                    if (awakenConfig.getStar() > star) {
                        star = awakenConfig.getStar();
                    }
                }
            }
            if (!cityScene.isActiveScene(citySceneId) && star < keyValue.getValue()) {
                return TResult.valueOf(SPACETIME_BEAUTY_AWAKEN_CONDITION_IS_NOT_ENOUGH);
            }
        }
        //校验红颜
        if (spacetimeBeautyId != 0) {
            SpacetimeBeautyConfig spacetimeBeautyConfig = globalConfigService.findConfig(IdentiyKey.build(spacetimeBeautyId), SpacetimeBeautyConfig.class);
            if (spacetimeBeautyConfig == null) {
                return TResult.valueOf(CONFIG_NOT_FOUND);
            }
            SpacetimeBeauty spacetimeBeauty = spacetimeBeautyDao.getSpacetimeBeauty(actorId, spacetimeBeautyId);
            if (spacetimeBeauty == null) {
                return TResult.valueOf(SPACETIME_BEAUTY_IS_NOT_ACTIVATE);
            }
            int star = 0;
            for (Integer awakenIndex : spacetimeBeauty.getAwakenList()) {
                SpacetimeBeautyAwakenConfig awakenConfig = globalConfigService.findConfig(IdentiyKey.build(spacetimeBeautyId, awakenIndex), SpacetimeBeautyAwakenConfig.class);
                if (awakenConfig == null) {
                    return TResult.valueOf(CONFIG_NOT_FOUND);
                }
                if (awakenConfig.getStar() > star) {
                    star = awakenConfig.getStar();
                }
            }
            if (star < spacetimeBeautyConfig.getSkinUnlocksStar()) {
                return TResult.valueOf(SPACETIME_BEAUTY_AWAKEN_CONDITION_IS_NOT_ENOUGH);
            }
        }
        cityScene.change(citySceneId, spacetimeBeautyId);
        dbQueue.updateQueue(cityScene);
        return TResult.sucess(cityScene);
    }

    @Override
    public Result addCityScene(long actorId, int configId) {
        CitySceneConfig config = globalConfigService.findConfig(IdentiyKey.build(configId), CitySceneConfig.class);
        if (config == null) {
            return Result.valueOf(CONFIG_NOT_FOUND);
        }
        CityScene cityScene = citySceneDao.getCitySceneInfo(actorId);
        cityScene.addCityScene(configId);
        dbQueue.updateQueue(cityScene);
        CityScenePushHelper.pushCitySceneInfo(cityScene);
        return Result.valueOf();
    }
}
