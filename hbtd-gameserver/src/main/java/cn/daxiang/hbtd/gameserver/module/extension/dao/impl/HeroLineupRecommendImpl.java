package cn.daxiang.hbtd.gameserver.module.extension.dao.impl;

import cn.daxiang.framework.database.SingleEntity;
import cn.daxiang.framework.database.dao.SingleEntityDaoImpl;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.hbtd.gameserver.core.database.table.HeroRecommend;
import cn.daxiang.hbtd.gameserver.module.extension.dao.HeroRecommendDao;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/11/6
 */
@Component
public class HeroLineupRecommendImpl extends SingleEntityDaoImpl implements HeroRecommendDao {
    @Override
    protected Class<? extends SingleEntity<?>> forClass() {
        return HeroRecommend.class;
    }

    @Override
    protected void initMaxId() {

    }

    @Override
    public HeroRecommend getHeroRecommend(long actorId) {
        HeroRecommend table = this.get(IdentiyKey.build(actorId));
        if (table.newEntity()) {
            table.setNewEntity(false);
            this.updateQueue(table);
        }
        return table;
    }

}
