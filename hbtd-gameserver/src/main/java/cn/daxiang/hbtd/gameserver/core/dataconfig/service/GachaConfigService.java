package cn.daxiang.hbtd.gameserver.core.dataconfig.service;

import cn.daxiang.framework.dataconfig.ConfigServiceAdapter;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.GachaConfig;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;

@Component
public class GachaConfigService extends ConfigServiceAdapter {
    /**
     * key:gachaId,value:GachaConfigService
     */
    private static Map<Integer, GachaConfig> GACHA_CONFIG_MAP = Maps.newHashMap();

    public static Collection<GachaConfig> getGachaConfigList() {
        return GACHA_CONFIG_MAP.values();
    }

    public static GachaConfig getGachaConfig(int gachaId) {
        return GACHA_CONFIG_MAP.get(gachaId);
    }

    @Override
    protected void initialize() {
        Collection<GachaConfig> list = dataConfig.listAll(this, GachaConfig.class);
        for (GachaConfig config : list) {
            GACHA_CONFIG_MAP.put(config.getId(), config);
        }
    }

    @Override
    protected void clean() {
        GACHA_CONFIG_MAP.clear();
    }
}
