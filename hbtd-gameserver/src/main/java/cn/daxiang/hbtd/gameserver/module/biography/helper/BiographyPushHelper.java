package cn.daxiang.hbtd.gameserver.module.biography.helper;

import cn.daxiang.framework.protocal.DataPacket;
import cn.daxiang.hbtd.gameserver.core.database.table.Biography;
import cn.daxiang.hbtd.gameserver.core.netty.channel.PlayerChannel;
import cn.daxiang.protocol.game.BiographyProtocol.BiographyCmd;
import cn.daxiang.protocol.game.BiographyProtocol.HeroBiographyResponse;
import cn.daxiang.protocol.game.ModuleProtocol.Module;

/**
 * <AUTHOR>
 * @date 2022/7/7
 */
public class BiographyPushHelper {

    public static void pushBiography(long actorId, Biography biography) {
        HeroBiographyResponse response = BiographyHelper.buildHeroBiographyResponse(biography);
        DataPacket packet = DataPacket.valueOf(Module.BIOGRAPHY_VALUE, BiographyCmd.PUSH_BIOGRAPHY_VALUE, response);
        PlayerChannel.push(actorId, packet);
    }
}
