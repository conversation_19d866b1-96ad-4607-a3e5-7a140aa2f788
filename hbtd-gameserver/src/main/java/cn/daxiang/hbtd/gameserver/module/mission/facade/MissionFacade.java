package cn.daxiang.hbtd.gameserver.module.mission.facade;

import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.hbtd.gameserver.core.database.table.Mission;
import cn.daxiang.protocol.game.CommonProtocol;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/1
 */
public interface MissionFacade {
    /**
     * 获取派遣信息
     *
     * @param actorId
     * @return
     */
    TResult<Mission> getMission(long actorId);

    /**
     * 任务派遣
     *
     * @param actorId
     * @param missionId
     * @param heroIds
     */
    Result performMission(long actorId, int missionId, List<Integer> heroIds);

    /**
     * 领取任务奖励
     *
     * @param actorId
     * @param missionId
     * @return
     */
    TResult<CommonProtocol.RewardResult> receiveReward(long actorId, int missionId);

    /**
     * 刷新任务
     *
     * @param actorId
     * @return
     */
    Result refreshMission(long actorId);

    /**
     * 激活建筑物
     *
     * @param actorId
     * @return
     */
    Result buildingActivation(long actorId, int buildingType);

    /**
     * 建筑指派红颜
     *
     * @param actorId
     * @param buildingType
     * @param beautyId
     * @return
     */
    Result beautyAssignBeauty(long actorId, int buildingType, int beautyId);

    /**
     * 建筑卸任红颜
     *
     * @param actorId
     * @param buildingType
     * @return
     */
    Result beautyRemovingBeauty(long actorId, int buildingType);
}
