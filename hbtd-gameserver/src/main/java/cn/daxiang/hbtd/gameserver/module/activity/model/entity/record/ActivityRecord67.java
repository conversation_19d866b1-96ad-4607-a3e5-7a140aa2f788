package cn.daxiang.hbtd.gameserver.module.activity.model.entity.record;

import com.google.common.collect.Maps;

import java.util.Collection;
import java.util.Map;

/**
 * 活动-67-充值-尊享礼包
 * HBTD\doc\策划文档\已评审文档\beta5.3商业化\充值每周礼包-keen.xlsx
 */
public class ActivityRecord67 {
    /**
     * 每日购买次数
     * key:id value:购买次数
     */
    private Map<Integer, Integer> dailyBuyTimes = Maps.newHashMap();
    /**
     * 购买次数
     * key:id value:购买次数
     */
    private Map<Integer, Integer> buyTimes = Maps.newHashMap();
    /**
     * 领取次数
     * key:id value:领取次数
     */
    private Map<Integer, Integer> receives = Maps.newHashMap();

    /**
     * 每日最后一次更新时间
     */
    private long lastRestTime;


    public static ActivityRecord67 valueOf(){
        ActivityRecord67 activityRecord67 = new ActivityRecord67();
        activityRecord67.lastRestTime = System.currentTimeMillis();
        return activityRecord67;
    }

    public long getLastRestTime() {
        return lastRestTime;
    }

    public Map<Integer, Integer> getBuyTimes() {
        return buyTimes;
    }

    public void setBuyTimes(Map<Integer, Integer> buyTimes) {
        this.buyTimes = buyTimes;
    }

    public Map<Integer, Integer> getReceives() {
        return receives;
    }

    public void setReceives(Map<Integer, Integer> receives) {
        this.receives = receives;
    }

    public Map<Integer, Integer> getDailyBuyTimes() {
        return dailyBuyTimes;
    }

    public void setDailyBuyTimes(Map<Integer, Integer> dailyBuyTimes) {
        this.dailyBuyTimes = dailyBuyTimes;
    }

    public void setLastRestTime(long lastRestTime) {
        this.lastRestTime = lastRestTime;
    }

    public void buyTimes(int id, int buyTimes) {
        int times = this.buyTimes.getOrDefault(id, 0) + buyTimes;
        this.buyTimes.put(id, times);
        int newDailyBuyTimes = this.dailyBuyTimes.getOrDefault(id, 0) + buyTimes;
        this.dailyBuyTimes.put(id, newDailyBuyTimes);
    }



    public void receives(int id, int receivesTimes) {
        int times = this.receives.getOrDefault(id, 0) + receivesTimes;
        this.receives.put(id, times);
    }

    public void reset(Collection<Integer> freeId) {
        this.dailyBuyTimes.clear();
        for (Integer id : freeId) {
            receives.remove(id);
        }
        this.lastRestTime = System.currentTimeMillis();
    }
}
