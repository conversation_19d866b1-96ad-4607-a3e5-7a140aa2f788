package cn.daxiang.hbtd.gameserver.module.battlefieldDrill.model;

import cn.daxiang.framework.utils.rank.AbstractRank;
import cn.daxiang.framework.utils.rank.LadderRank;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * 荣耀殿堂
 */
public class HonourRank extends LadderRank<Long> implements RowMapper<HonourRank> {
    /**
     * 当前波次ID
     */
    private long power;
    /**
     * 达成时间
     */
    private long achieveTime;

    public static HonourRank valueOf(long actorId, long power, long achieveTime) {
        HonourRank rank = new HonourRank();
        rank.setKey(actorId);
        rank.power = power;
        rank.achieveTime = achieveTime;
        return rank;
    }

    public long getPower() {
        return power;
    }

    public long getAchieveTime() {
        return achieveTime;
    }

    public void reset(long power, long achieveTime) {
        this.power = power;
        this.achieveTime = achieveTime;
    }

    @Override
    public AbstractRank<Long> copy() {
        HonourRank rank = HonourRank.valueOf(this.key, this.power, this.achieveTime);
        this.copyRank(rank);
        return rank;
    }

    @Override
    public boolean outstrip(AbstractRank<Long> rank) {
        HonourRank battlefieldDrillRank = (HonourRank) rank;
        if (achieveTime < battlefieldDrillRank.getAchieveTime()) {
            return true;
        } else if (key == battlefieldDrillRank.getKey()) {
            return this.key < battlefieldDrillRank.getKey();
        }
        return false;
    }

    @Override
    public void achieveRank(LadderRank<Long> rank) {
        HonourRank ancientCorridorRank = (HonourRank) rank;
        this.power = ancientCorridorRank.getPower();
        this.achieveTime = ancientCorridorRank.getAchieveTime();
    }

    @Override
    public HonourRank mapRow(ResultSet rs, int rowNum) throws SQLException {
        return HonourRank.valueOf(rs.getLong("actorId"), rs.getLong("power"), rs.getLong("achieveTime"));
    }
}
