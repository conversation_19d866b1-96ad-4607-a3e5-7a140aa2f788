package cn.daxiang.hbtd.gameserver.module.goods.parser.effect.impl;

import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.GoodsConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.TitleConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.TitleConfigService;
import cn.daxiang.hbtd.gameserver.core.oss.type.OperationType;
import cn.daxiang.hbtd.gameserver.module.goods.parser.effect.AbstractGoodsEffectParser;
import cn.daxiang.hbtd.gameserver.module.goods.type.GoodsEffectType;
import cn.daxiang.hbtd.gameserver.module.title.facade.TitleFacade;
import cn.daxiang.hbtd.gameserver.module.user.helper.ActorHelper;
import cn.daxiang.protocol.game.CommonProtocol.RewardResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static cn.daxiang.shared.GameModuleStatusCodeConstant.CONFIG_NOT_FOUND;
import static cn.daxiang.shared.GameModuleStatusCodeConstant.TITLE_NOT_UNLOCK;

/**
 * 增加称号
 *
 * <AUTHOR>
 * @date 2020/2/5
 */
@Component
public class AddTitleGoodsEffectParser extends AbstractGoodsEffectParser {
    @Autowired
    private TitleFacade titleFacade;

    @Override
    public TResult<RewardResult> execute(long actorId, long num, GoodsConfig goodsConfig, byte[] value, OperationType operationType) {
        int titleId = goodsConfig.calcEffectValue();
        TitleConfig titleConfig = TitleConfigService.getTitleConfig(titleId);
        if (titleConfig == null) {
            return TResult.valueOf(CONFIG_NOT_FOUND);
        }
        Result result = titleFacade.createTitle(actorId, titleConfig);
        if (result.isFail()) {
            return TResult.valueOf(result.statusCode);
        }

        return TResult.sucess(RewardResult.newBuilder().build());
    }

    @Override
    protected GoodsEffectType getType() {
        return GoodsEffectType.TITLE;
    }

    @Override
    public Result unlock(long actorId, GoodsConfig goodsConfig) {
        if (goodsConfig.getUnlockId() != 0) {
            Result unlockResult = ActorHelper.unlock(actorId, goodsConfig.getUnlockId());
            if (unlockResult.isFail()) {
                return Result.valueOf(TITLE_NOT_UNLOCK);
            }
        }
        return Result.valueOf();
    }
}
