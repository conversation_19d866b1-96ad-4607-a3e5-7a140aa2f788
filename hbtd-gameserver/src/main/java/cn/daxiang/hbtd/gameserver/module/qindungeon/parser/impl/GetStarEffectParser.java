package cn.daxiang.hbtd.gameserver.module.qindungeon.parser.impl;

import cn.daxiang.framework.result.TResult;
import cn.daxiang.framework.utils.FormulaUtils;
import cn.daxiang.hbtd.gameserver.core.database.table.QinDungeon;
import cn.daxiang.hbtd.gameserver.core.database.table.QinRace;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.QinDungeonRougueEffectConfig;
import cn.daxiang.hbtd.gameserver.module.qindungeon.parser.AbstractQinDungeonEffectParser;
import cn.daxiang.protocol.game.QindungeonProtocol;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/8/16
 */
@Component
public class GetStarEffectParser extends AbstractQinDungeonEffectParser {
    @Override
    protected QindungeonProtocol.QinDungeonEffectType getType() {
        return QindungeonProtocol.QinDungeonEffectType.QINDUNGEON_GET_STAR;
    }

    @Override
    public TResult<String> parser(QinDungeon qinDungeon, QinDungeonRougueEffectConfig config) {
        Integer times = qinDungeon.getBuffTimesMap().get(config.getId());
        if (times == null) {
            times = 1;
            qinDungeon.getBuffTimesMap().put(config.getId(), times);
        }
        if (times > 1) {
            return TResult.sucess(returnString(0));
        }
        int result = FormulaUtils.executeInt(config.getCondition());
        qinDungeon.setLuckStarNum(qinDungeon.getLuckStarNum() + result);
        qinDungeon.getBuffTimesMap().put(config.getId(), times + 1);
        //                LOGGER.error("GetStarEffectParser EXPR:[{}], result:{},returnString:{},floor:{}", config.getCondition(), result, returnString(result), qinDungeon.getFloor());
        return TResult.sucess(returnString(result));
    }

    @Override
    public TResult<String> parser(QinRace qinRace, QinDungeonRougueEffectConfig config) {
        Integer times = qinRace.getBuffTimesMap().get(config.getId());
        if (times == null) {
            times = 1;
            qinRace.getBuffTimesMap().put(config.getId(), times);
        }
        if (times > 1) {
            return TResult.sucess(returnString(0));
        }
        int result = FormulaUtils.executeInt(config.getCondition());
        qinRace.setLuckStarNum(qinRace.getLuckStarNum() + result);
        qinRace.getBuffTimesMap().put(config.getId(), times + 1);
        return TResult.sucess(returnString(result));
    }
}
