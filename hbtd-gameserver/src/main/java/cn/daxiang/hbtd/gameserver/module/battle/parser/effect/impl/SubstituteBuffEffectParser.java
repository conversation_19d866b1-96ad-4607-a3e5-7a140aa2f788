package cn.daxiang.hbtd.gameserver.module.battle.parser.effect.impl;

import cn.daxiang.hbtd.gameserver.core.dataconfig.model.SkillConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.SkillEffectConfig;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleRoom;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleSprite;
import cn.daxiang.hbtd.gameserver.module.battle.model.FightReport;
import cn.daxiang.hbtd.gameserver.module.battle.parser.effect.AbstractSkillEffectParser;
import cn.daxiang.hbtd.gameserver.module.battle.type.SkillEffectType;
import org.springframework.stereotype.Component;

/**
 * 替身抵挡伤害buff
 *
 * <AUTHOR>
 * @date 2020/3/18
 */
@Component
public class SubstituteBuffEffectParser extends AbstractSkillEffectParser {

    private static final int SUBSTITUTE_BUFF_STACK_MAX_TIMES = 3;

    @Override
    protected SkillEffectType getType() {
        return SkillEffectType.SUBSTITUTE_BUFF_EFFECT;
    }

    @Override
    public boolean execute(FightReport report, BattleSprite attacker, BattleSprite targeter, SkillConfig skillConfig, SkillEffectConfig effectConfig, BattleRoom battleRoom) {
        long oldBuffValue = targeter.getBuffValue(getType());
        if (oldBuffValue >= SUBSTITUTE_BUFF_STACK_MAX_TIMES) {
            return true;
        }
        long buffValue = effectConfig.calcSkillEffect();
        if (oldBuffValue + buffValue > SUBSTITUTE_BUFF_STACK_MAX_TIMES) {
            buffValue = SUBSTITUTE_BUFF_STACK_MAX_TIMES - oldBuffValue;
        }
        this.addBattleBuff(report, attacker, targeter, skillConfig, effectConfig, battleRoom, buffValue, effectConfig.calcBuffLayers());
        return true;
    }
}
