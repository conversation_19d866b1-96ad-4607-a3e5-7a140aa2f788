package cn.daxiang.hbtd.gameserver.module.battle.parser.verify;

import cn.daxiang.shared.type.PVEVerifyParameterKey;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/4/27
 */
public interface BattleVerifyParser {
    /**
     * 验证
     *
     * @param actorId
     * @param parameter
     */
    void verify(long actorId, Map<PVEVerifyParameterKey, Object> parameter);

    /**
     * 验证
     *
     * @param actorId
     * @param parameter
     * @param isQuicklyPass 是否快速通关（true:不验证作弊，内网关卡一键通关按钮）
     */
    void verify(long actorId, Map<PVEVerifyParameterKey, Object> parameter, boolean isQuicklyPass);
}
