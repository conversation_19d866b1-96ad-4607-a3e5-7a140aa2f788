package cn.daxiang.hbtd.gameserver.module.battle.dao.impl;

import cn.daxiang.framework.database.SingleEntity;
import cn.daxiang.framework.database.dao.SingleEntityDaoImpl;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.hbtd.gameserver.core.GameConfig;
import cn.daxiang.hbtd.gameserver.core.database.IdGenerator;
import cn.daxiang.hbtd.gameserver.core.database.UniqueId;
import cn.daxiang.hbtd.gameserver.core.database.table.BattleReplay;
import cn.daxiang.hbtd.gameserver.module.battle.dao.BattleReplayDao;
import cn.daxiang.shared.module.battle.BattleType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.concurrent.atomic.AtomicLong;

/**
 * <AUTHOR>
 */
@Component
public class BattleReplayDaoImpl extends SingleEntityDaoImpl implements BattleReplayDao {
    @Autowired
    private IdGenerator idGenerator;

    @Override
    protected Class<? extends SingleEntity<?>> forClass() {
        return BattleReplay.class;
    }

    @Override
    protected void initMaxId() {
        Long maxId = jdbc.queryForObject("select max(battleReplayId) from battle_replay", Long.class);
        AtomicLong base = null;
        if (maxId == null || maxId <= 0) {
            base = new AtomicLong(UniqueId.otherId(GameConfig.getServerId()));
        } else {
            base = new AtomicLong(maxId);
        }
        idGenerator.init(IdentiyKey.build(this.getClass()), base);
    }

    @Override
    public BattleReplay getBattleReplay(long battleReplayId) {
        IdentiyKey key = IdentiyKey.build(battleReplayId);
        if (!this.exsit(key, BattleReplay.class)) {
            return null;
        }
        return this.get(IdentiyKey.build(battleReplayId));
    }

    @Override
    public BattleReplay createBattleReply(long actorId, byte[] battleReplayRecord, long createTime, BattleType battleType) {
        long battleReplayId = this.idGenerator.increment(IdentiyKey.build(this.getClass()));
        BattleReplay battleReplay = BattleReplay.valueOf(battleReplayId, battleReplayRecord, createTime, battleType);
        updateQueue(battleReplay);
        return battleReplay;
    }

    @Override
    public void deleteBattleReply(Collection<Long> battleReplayIds) {
        for (Long battleReplayId : battleReplayIds) {
            BattleReplay battleReplay = this.getBattleReplay(battleReplayId);
            if (battleReplay == null) {
                continue;
            }
            this.delete(battleReplay);
        }
    }
}
