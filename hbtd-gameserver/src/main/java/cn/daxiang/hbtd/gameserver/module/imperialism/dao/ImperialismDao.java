package cn.daxiang.hbtd.gameserver.module.imperialism.dao;

import cn.daxiang.framework.utils.rank.cache.ILineRankToProvider;
import cn.daxiang.hbtd.gameserver.core.database.table.Imperialism;
import cn.daxiang.hbtd.gameserver.module.imperialism.model.ImperialismServerRank;

import java.util.Collection;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/11/13
 */
public interface ImperialismDao extends ILineRankToProvider<Long, ImperialismServerRank> {

    /**
     * 获取决战皇城个人信息
     *
     * @param actorId
     * @return
     */
    Imperialism get(long actorId);

    /**
     * 获取决战皇城本服
     *
     * @param rank
     * @return
     */
    ImperialismServerRank getImperialismRank(long rank);

    /**
     * 获取竞技场排名
     * key:rank,value:actorId
     *
     * @return
     */
    Map<Long, Long> getImperialismRankMap();

    /**
     * 交换排名
     *
     * @param challengeRank
     * @param opponentRank
     */
    void swapRank(long challengeRank, long opponentRank);

    /**
     * 删除排名
     *
     * @param imperialismServerRank
     */
    void removeRank(ImperialismServerRank imperialismServerRank);

    /**
     * 获取战区排名角色ID列表
     *
     * @return
     */
    Collection<Long> getZoneActorIds();

}
