package cn.daxiang.hbtd.gameserver.module.heroComment;

import cn.daxiang.framework.dispatch.DispatchType;
import cn.daxiang.framework.protocal.DataPacket;
import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.framework.router.annotation.Cmd;
import cn.daxiang.hbtd.gameserver.core.router.GatewayRouterHandlerImpl;
import cn.daxiang.hbtd.gameserver.module.heroComment.facade.HeroCommentFacade;
import cn.daxiang.protocol.game.CommonProtocol;
import cn.daxiang.protocol.game.CommonProtocol.RewardResultResponse;
import cn.daxiang.protocol.game.HerocommentProtocol.GiveLikeRequest;
import cn.daxiang.protocol.game.HerocommentProtocol.HeroCommentResponse;
import cn.daxiang.protocol.game.HerocommentProtocol.HerocommentCmd;
import cn.daxiang.protocol.game.HerocommentProtocol.LeaveCommentRequest;
import cn.daxiang.protocol.game.HerocommentProtocol.UnlockRewardsStateResponse;
import cn.daxiang.protocol.game.ModuleProtocol.Module;
import io.netty.channel.Channel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author: Cary
 * @Date 2022-12-01 17:33
 * @Description:
 */
@Component
public class HeroCommentHandler extends GatewayRouterHandlerImpl {
    @Autowired
    private HeroCommentFacade herocommentFacade;

    @Override
    public int getModule() {
        return Module.HEROCOMMENT_VALUE;
    }

    @Cmd(Id = HerocommentCmd.GET_UNLOCK_REWARDS_STATE_VALUE, dispatchType = DispatchType.ACTOR)
    public void getUnlockRewardsState(Channel channel, Long actorId, DataPacket packet) {
        TResult<UnlockRewardsStateResponse> result = herocommentFacade.getUnlockRewardsState(actorId);
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = HerocommentCmd.GIVE_COMMENT_LIKE_VALUE, dispatchType = DispatchType.ACTOR)
    public void giveCommentLike(Channel channel, Long actorId, DataPacket packet) {
        GiveLikeRequest request = packet.getValue(GiveLikeRequest.parser());
        Result result = herocommentFacade.giveCommentLike(actorId, request.getHeroConfigId(), request.getCommentId());
        channelWrite(channel, packet, result.statusCode);
    }

    @Cmd(Id = HerocommentCmd.LEAVE_COMMENT_VALUE, dispatchType = DispatchType.ACTOR)
    public void leaveComment(Channel channel, Long actorId, DataPacket packet) {
        LeaveCommentRequest request = packet.getValue(LeaveCommentRequest.parser());
        TResult<Long> result = herocommentFacade.leaveComment(actorId, request.getHeroId(), request.getContent());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, CommonProtocol.LongPacket.newBuilder().setValue(result.item).build());
    }

    @Cmd(Id = HerocommentCmd.GET_COMMENT_LIST_VALUE, dispatchType = DispatchType.ACTOR)
    public void getCommentList(Channel channel, Long actorId, DataPacket packet) {
        CommonProtocol.IntPacket request = packet.getValue(CommonProtocol.IntPacket.parser());
        TResult<HeroCommentResponse> result = herocommentFacade.getHeroComment(actorId, request.getValue());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = HerocommentCmd.RECEIVE_UNLOCK_REWARDS_VALUE, dispatchType = DispatchType.ACTOR)
    public void receiveUnlockRewards(Channel channel, Long actorId, DataPacket packet) {
        CommonProtocol.IntPacket request = packet.getValue(CommonProtocol.IntPacket.parser());
        TResult<RewardResultResponse> result = herocommentFacade.receiveUnlockRewards(actorId, request.getValue());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

}
