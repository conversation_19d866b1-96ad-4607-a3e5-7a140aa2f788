package cn.daxiang.hbtd.gameserver.module.qinrace.parser;

import cn.daxiang.framework.result.CollectionResult;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.hbtd.gameserver.core.database.table.QinRace;
import cn.daxiang.shared.module.qindungeon.QinRaceWorldRankVO;
import cn.daxiang.shared.reward.RewardObject;
import com.google.protobuf.ByteString;

import java.util.Collection;

/**
 * <AUTHOR>
 * @date 2023/6/19
 */
public interface QinRaceTypeParser {
    /**
     * 战斗
     *
     * @param hp
     * @param requestValue
     * @param qinRace
     * @param updateRank   判断是否需要刷新排行榜
     * @return
     */
    CollectionResult<RewardObject> battle(int hp, byte[] requestValue, QinRace qinRace, Collection<Boolean> updateRank);

    /**
     * 结算
     *
     * @param requestValue
     * @param qinRace
     * @param updateRank   判断是否需要刷新排行榜
     * @return
     */
    CollectionResult<RewardObject> settle(byte[] requestValue, QinRace qinRace, Collection<Boolean> updateRank);

    /**
     * 获取排行榜VO
     *
     * @param qinRace
     * @return
     */
    TResult<QinRaceWorldRankVO> getRankVO(QinRace qinRace);

    /**
     * 获取初始QinRaceRecord的字符串
     *
     * @return
     */
    String getInitQinRaceRecord();

    /**
     * 获取Record的实体类
     *
     * @param qinRace
     * @param clazz
     * @param <T>
     * @return
     */

    <T> T getQinRaceRecord(QinRace qinRace, Class<T> clazz);

    /**
     * 获取proto响应Message
     *
     * @param qinRace
     * @return
     */
    ByteString getRecordMessage(QinRace qinRace);

    /**
     * 获取初始等级
     *
     * @return
     */
    int getInitLevel();
}
