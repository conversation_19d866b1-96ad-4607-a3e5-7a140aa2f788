package cn.daxiang.hbtd.gameserver.module.chess.helper;

import cn.daxiang.framework.protocal.DataPacket;
import cn.daxiang.hbtd.gameserver.core.database.table.Chess;
import cn.daxiang.hbtd.gameserver.core.netty.channel.PlayerChannel;
import cn.daxiang.protocol.game.ChessProtocol;
import cn.daxiang.protocol.game.ChessProtocol.ChessCmd;
import cn.daxiang.protocol.game.CommonProtocol;
import cn.daxiang.protocol.game.ModuleProtocol.Module;

import java.util.Collection;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/4/25
 */
public class ChessPushHelper {

    public static void pushChess(long actorId, Chess chess, int circulate, boolean containMonsterLineup) {
        ChessProtocol.ChessInfoResponse response = ChessHelper.buildChess(chess, circulate, containMonsterLineup);
        DataPacket packet = DataPacket.valueOf(Module.CHESS_VALUE, ChessCmd.PUSH_CHESS_VALUE, response);
        PlayerChannel.push(actorId, packet);
    }

    public static void pushChessWeekDay(long actorId) {
        int weekDay = ChessHelper.getWeekDay();
        CommonProtocol.IntPacket response = CommonProtocol.IntPacket.newBuilder().setValue(weekDay).build();
        DataPacket packet = DataPacket.valueOf(Module.CHESS_VALUE, ChessCmd.PUSH_CHESS_WEEK_DAY_VALUE, response);
        PlayerChannel.push(actorId, packet);
    }

    public static void pushChessReceive(long actorId, Collection<Integer> receiveList) {
        CommonProtocol.IntListPacket response = CommonProtocol.IntListPacket.newBuilder().addAllList(receiveList).build();
        DataPacket packet = DataPacket.valueOf(Module.CHESS_VALUE, ChessCmd.PUSH_CHESS_RECEIVE_VALUE, response);
        PlayerChannel.push(actorId, packet);
    }

    public static void pushChessBuyTimes(long actorId, int buyTimes) {
        CommonProtocol.IntPacket response = CommonProtocol.IntPacket.newBuilder().setValue(buyTimes).build();
        DataPacket packet = DataPacket.valueOf(Module.CHESS_VALUE, ChessCmd.PUSH_CHESS_BUY_VALUE, response);
        PlayerChannel.push(actorId, packet);
    }

    public static void pushChessClose() {
        CommonProtocol.Response response = CommonProtocol.Response.newBuilder().build();
        PlayerChannel.onlineActorList().forEach(x -> {
            DataPacket packet = DataPacket.valueOf(Module.CHESS_VALUE, ChessCmd.PUSH_CHESS_CLOES_VALUE, response);
            PlayerChannel.push(x, packet);
        });
    }
}
