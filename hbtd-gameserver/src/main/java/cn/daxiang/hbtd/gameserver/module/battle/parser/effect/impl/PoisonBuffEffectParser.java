package cn.daxiang.hbtd.gameserver.module.battle.parser.effect.impl;

import cn.daxiang.framework.utils.NumberUtils;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.SkillConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.SkillEffectConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.SkillConfigService;
import cn.daxiang.hbtd.gameserver.module.battle.helper.BuffHelper;
import cn.daxiang.hbtd.gameserver.module.battle.helper.FightHelper;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleBuff;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleRoom;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleSprite;
import cn.daxiang.hbtd.gameserver.module.battle.model.FightEffect;
import cn.daxiang.hbtd.gameserver.module.battle.model.FightReport;
import cn.daxiang.hbtd.gameserver.module.battle.parser.effect.AbstractSkillEffectParser;
import cn.daxiang.hbtd.gameserver.module.battle.type.SkillEffectType;
import cn.daxiang.hbtd.gameserver.module.battle.type.SkillTriggerType;
import cn.daxiang.protocol.game.BattleProtocol.SpriteKey;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Iterator;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/11/20
 */
@Component
public class PoisonBuffEffectParser extends AbstractSkillEffectParser {
    @Override
    protected SkillEffectType getType() {
        return SkillEffectType.POISON_BUFF_EFFECT;
    }

    @Override
    public boolean execute(FightReport report, BattleSprite attacker, BattleSprite targeter, SkillConfig skillConfig, SkillEffectConfig effectConfig, BattleRoom battleRoom) {
        if (targeter.getSpriteBattle().isBoss()) {
            report.addFightEffect(FightEffect.immune(targeter, effectConfig.getEffectId()));
            return true;
        }
        this.addBattleBuff(report, attacker, targeter, skillConfig, effectConfig, battleRoom);
        return true;
    }

    @Override
    public long calcEffectValue(FightReport report, BattleSprite battleSprite, BattleRoom battleRoom) {
        Collection<BattleBuff> buffList = battleSprite.getBuffList(getType());
        if (buffList.isEmpty()) {
            return 0;
        }
        int effectId = buffList.iterator().next().getEffectId();
        if (battleSprite.hasBuffType(SkillEffectType.IMMUNE_DAMAGE_BUFF_EFFECT)) {
            FightEffect attackEffect = FightEffect.immune(battleSprite, effectId);
            report.addFightEffect(attackEffect);
            return 0;
        }
        int startHpPercent = battleSprite.getHPPercent();
        //X1：目标最高血量（计算buff）
        long hpMax = battleSprite.getSpriteBattle().getHPMax();
        long bonus = BuffHelper.getBuffValue(battleSprite, SkillEffectType.POISON_BONUS_BUFF_EFFECT);
        if (bonus < 0) {
            report.addFightEffect(FightEffect.damageReduction(battleSprite.getSpriteId()));
        }
        Map<Integer, Map<Long, Long>> buffValueMap = Maps.newHashMap();
        BattleSprite finalSprite = null;
        for (Iterator<BattleBuff> iterator = buffList.iterator(); iterator.hasNext(); ) {
            BattleBuff buff = iterator.next();
            BattleSprite casterSprite = battleRoom.getBattleSprite(buff.getCastSpriteUid());
            finalSprite = battleRoom.getBattleSprite(buff.getCastSpriteUid());
            //X2：攻击者攻击力
            SkillEffectConfig effectConfig = SkillConfigService.getSkillEffectConfig(buff.getEffectId());
            long buffValue = effectConfig.calcSkillEffect(hpMax, casterSprite.getAttack());
            if (buffValue != 0 && bonus != 0) {
                buffValue += NumberUtils.getPercentLongValue(buffValue, (int) bonus);
            }
            Map<Long, Long> buffValues = buffValueMap.get(buff.getEffectId());
            if (buffValues == null) {
                buffValues = Maps.newHashMap();
                buffValueMap.put(buff.getEffectId(), buffValues);
            }
            long spriteValue = buffValues.getOrDefault(buff.getCastSpriteUid(), 0L);
            buffValues.put(buff.getCastSpriteUid(), spriteValue + buffValue);
        }
        Map<Long, Long> spriteValueMap = this.getBuffSpriteValue(buffValueMap);
        boolean isAttackerTakingLethalDamage = FightHelper.isTakingLethalDamage(battleSprite, Long.MAX_VALUE);
        boolean triggerTakingLethalDamage = false;
        Map<Long, Long> damageMap = Maps.newHashMap();
        for (Map.Entry<Long, Long> entry : spriteValueMap.entrySet()) {
            long spriteId = entry.getKey();
            long buffValue = entry.getValue();
            BattleSprite casterSprite = battleRoom.getBattleSprite(spriteId);
            buffValue = this.decreaseShield(battleRoom, casterSprite, battleSprite, effectId, report, buffValue);
            if (buffValue <= 0) {
                continue;
            }
            buffValue = Math.min(battleSprite.getSpriteBattle().getHP(), buffValue);
            if (isAttackerTakingLethalDamage && buffValue == battleSprite.getSpriteBattle().getHP()) {
                buffValue -= 1;
                triggerTakingLethalDamage = true;
            }
            battleSprite.changeAttribute(SpriteKey.HP, -buffValue);
            Long damage = damageMap.getOrDefault(spriteId, 0L);
            damageMap.put(spriteId, damage + buffValue);
            if (triggerTakingLethalDamage) {
                FightHelper.processSkill(battleRoom, battleSprite, casterSprite, battleSprite, null, SkillTriggerType.TAKING_LETHAL_DAMAGE);
                break;
            }
        }
        long value = 0;
        for (Map.Entry<Long, Long> entry : damageMap.entrySet()) {
            BattleSprite casterSprite = battleRoom.getBattleSprite(entry.getKey());
            battleRoom.getBattleStats().recordDamage(casterSprite, battleSprite.getSpriteId(), entry.getValue());
            report.addSpriteDamage(casterSprite, battleSprite, entry.getValue());
            value += entry.getValue();
        }
        value = this.damageLimit(battleSprite, report, value);
        report.addFightEffect(FightEffect.buff(battleSprite.getSpriteId(), effectId, SpriteKey.HP, -value));

        // 掉血恢复怒气
        int endHpPercent = battleSprite.getHPPercent();
        FightHelper.bloodLossRage(battleRoom, battleSprite, startHpPercent, endHpPercent, report);
        //生命发生变动时
        FightHelper.processSkill(battleRoom, battleSprite, finalSprite, battleSprite, -value, null, SkillTriggerType.HP_CHANGE, null);
        //受到伤害时
        FightHelper.processSkill(battleRoom, battleSprite, finalSprite, battleSprite, value, null, SkillTriggerType.TAKING_DAMAGE, null);
        return value;
    }
}
