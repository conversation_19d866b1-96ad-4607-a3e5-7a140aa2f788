package cn.daxiang.hbtd.gameserver.module.extension.parser.limitedCharge;

import cn.daxiang.hbtd.gameserver.module.extension.type.LimitedTimeChargeType;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/6/14
 */
@Component
public class LimitedChargeContext {
    private Map<LimitedTimeChargeType, LimitedChargeParser> TYPE_PARSER_MAP = Maps.newHashMap();

    private Map<String, Collection<LimitedTimeChargeType>> EVENT_TYPE_MAP = Maps.newHashMap();

    public void register(LimitedTimeChargeType type, LimitedChargeParser parser) {
        TYPE_PARSER_MAP.put(type, parser);
        for (String eventName : type.events) {
            Collection<LimitedTimeChargeType> list = EVENT_TYPE_MAP.get(eventName);
            if (list == null) {
                list = Sets.newHashSet();
                EVENT_TYPE_MAP.put(eventName, list);
            }
            list.add(type);
        }
    }

    public LimitedChargeParser getParser(LimitedTimeChargeType type) {
        return TYPE_PARSER_MAP.get(type);
    }

    public Collection<LimitedTimeChargeType> getTypes(String keyName) {
        return EVENT_TYPE_MAP.get(keyName);
    }

    public Collection<String> getEvents() {
        return EVENT_TYPE_MAP.keySet();
    }
}
