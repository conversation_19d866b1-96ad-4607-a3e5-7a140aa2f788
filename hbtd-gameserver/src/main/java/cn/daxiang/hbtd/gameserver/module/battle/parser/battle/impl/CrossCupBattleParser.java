package cn.daxiang.hbtd.gameserver.module.battle.parser.battle.impl;

import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.utils.NumberUtils;
import cn.daxiang.hbtd.gameserver.module.PbBuilder;
import cn.daxiang.hbtd.gameserver.module.battle.facade.BattleFacade;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleMember;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleRoom;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleSprite;
import cn.daxiang.hbtd.gameserver.module.battle.parser.battle.AbstractBattleParser;
import cn.daxiang.hbtd.gameserver.module.user.helper.ActorHelper;
import cn.daxiang.protocol.game.BattleProtocol;
import cn.daxiang.protocol.game.BattleProtocol.BattleCamp;
import cn.daxiang.protocol.game.CommonProtocol;
import cn.daxiang.protocol.game.TypeProtocol;
import cn.daxiang.shared.module.battle.BattleParameterKey;
import cn.daxiang.shared.module.battle.BattleType;
import cn.daxiang.shared.module.lineup.BattleLineupEntity;
import cn.daxiang.shared.module.lineup.LineupAttribute;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;

/**
 * 巅峰竞技战斗解析器
 *
 * <AUTHOR>
 * @date 2019/10/24
 */
@Component
public class CrossCupBattleParser extends AbstractBattleParser {

    @Autowired
    private BattleFacade battleFacade;

    @Override
    public Result fight(long actorId, Map<BattleParameterKey, Object> parameterMap) {
        //左边玩家信息
        Map<Byte, Object> leftAttributes = (Map<Byte, Object>) parameterMap.get(BattleParameterKey.CROSS_CUP_LEFT_ATTRIBUTE);
        //左边玩家阵容
        BattleLineupEntity leftBattleLineupEntity = (BattleLineupEntity) parameterMap.get(BattleParameterKey.CROSS_CUP_LEFT_LINEUP);
        Map<Integer, LineupAttribute> leftLineupAttributeMap = leftBattleLineupEntity.getLineupAttributeMap();
        leftAttributes.put((byte) TypeProtocol.ActorFieldType.ACTOR_POWER.getNumber(), leftLineupAttributeMap.values().stream().mapToLong(LineupAttribute::getPower).sum());
        //右边玩家属性
        Map<Byte, Object> rightAttributes = (Map<Byte, Object>) parameterMap.get(BattleParameterKey.CROSS_CUP_RIGHT_ATTRIBUTE);
        //右边玩家阵容
        BattleLineupEntity rightBattleLineupEntity = (BattleLineupEntity) parameterMap.get(BattleParameterKey.CROSS_CUP_RIGHT_LINEUP);
        Map<Integer, LineupAttribute> rightLineupAttributeMap = rightBattleLineupEntity.getLineupAttributeMap();
        rightAttributes.put((byte) TypeProtocol.ActorFieldType.ACTOR_POWER.getNumber(), rightLineupAttributeMap.values().stream().mapToLong(LineupAttribute::getPower).sum());
        //左边战斗成员
        BattleMember leftMember = this.createBattleMember((long) leftAttributes.get((byte) TypeProtocol.ActorFieldType.ACTOR_ID.getNumber()),
            (long) leftAttributes.get((byte) TypeProtocol.ActorFieldType.ACTOR_POWER.getNumber()),
            (int) leftAttributes.getOrDefault((byte) TypeProtocol.ActorFieldType.ACTOR_OFFICIAL.getNumber(), 0), BattleCamp.LEFT_CAMP, leftBattleLineupEntity, getType());
        //右边战斗成员
        BattleMember rightMember = this.createBattleMember((long) rightAttributes.get((byte) TypeProtocol.ActorFieldType.ACTOR_ID.getNumber()),
            (long) rightAttributes.get((byte) TypeProtocol.ActorFieldType.ACTOR_POWER.getNumber()),
            (int) rightAttributes.getOrDefault((byte) TypeProtocol.ActorFieldType.ACTOR_OFFICIAL.getNumber(), 0), BattleCamp.RIGHT_CAMP, rightBattleLineupEntity, getType());
        BattleRoom battleRoom = this.createBattleRoom(leftMember, rightMember, parameterMap);
        battleRoomFacade.createBattleRoom(battleRoom);
        return Result.valueOf();
    }

    @Override
    public void battleEnd(long actorId, BattleRoom battleRoom) {
        // 挑战结果
        boolean isRoundLimit = true;
        for (BattleMember battleMember : battleRoom.getMemberMap().values()) {
            if (battleMember.isLose()) {
                isRoundLimit = false;
            }
        }
        if (isRoundLimit) {
            Map<BattleCamp, Integer> campHPPercentMap = Maps.newHashMap();
            for (BattleMember battleMember : battleRoom.getMemberMap().values()) {
                long hpMax = 0;
                long hp = 0;
                for (BattleSprite battleSprite : battleMember.getSpriteMap().values()) {
                    hpMax += battleSprite.getSpriteBattle().getHPMax();
                    hp += battleSprite.getSpriteBattle().getHP();
                }
                campHPPercentMap.put(battleMember.getBattleCamp(), NumberUtils.getValuePercent(hp, hpMax));
            }
            if (campHPPercentMap.get(BattleCamp.LEFT_CAMP) >= campHPPercentMap.get(BattleCamp.RIGHT_CAMP)) {
                battleRoom.getBattleStats().setWinCamp(BattleCamp.LEFT_CAMP);
            } else {
                battleRoom.getBattleStats().setWinCamp(BattleCamp.RIGHT_CAMP);
            }
        }
        Map<Byte, Object> leftAttributes = (Map<Byte, Object>) battleRoom.getParameterMap().get(BattleParameterKey.CROSS_CUP_LEFT_ATTRIBUTE);
        Map<Byte, Object> rightAttributes = (Map<Byte, Object>) battleRoom.getParameterMap().get(BattleParameterKey.CROSS_CUP_RIGHT_ATTRIBUTE);

        Collection<BattleProtocol.BattleMember> members = Lists.newArrayList();
        CommonProtocol.ActorProfile leftActorProfile = ActorHelper.getActorProfile(leftAttributes);
        BattleProtocol.BattleMember leftBattleMember = battleRoom.getBattleMember(BattleProtocol.BattleCamp.LEFT_CAMP, leftActorProfile);
        members.add(leftBattleMember);

        CommonProtocol.ActorProfile rightActorProfile = ActorHelper.getActorProfile(rightAttributes);
        BattleProtocol.BattleMember rightBattleMember = battleRoom.getBattleMember(BattleProtocol.BattleCamp.RIGHT_CAMP, rightActorProfile);
        members.add(rightBattleMember);

        CommonProtocol.RewardResult rewardResult = CommonProtocol.RewardResult.newBuilder().build();
        BattleProtocol.BattleResultResponse response =
            PbBuilder.buildBattleResultResponse(battleRoom.getBattleType(), members, battleRoom.getRightHistoryBattleSpriteVO(), battleRoom.getBattleReportMap(), rewardResult,
                battleRoom.getBattleStats());
        byte[] finalResponseBytes = response.toByteArray();
        int battleType = (int) battleRoom.getParameterMap().get(BattleParameterKey.CROSS_CUP_BATTLE_TYPE);
        long battleResultId = (long) battleRoom.getParameterMap().get(BattleParameterKey.CROSS_CUP_BATTLE_RESULT_ID);
        long winnerId = battleRoom.getBattleStats().getWinCamp() == BattleCamp.LEFT_CAMP ? battleRoom.getAttacker() : battleRoom.getTargeter();
        long loserId = battleRoom.getBattleStats().getWinCamp() == BattleCamp.LEFT_CAMP ? battleRoom.getTargeter() : battleRoom.getAttacker();
        battleFacade.crossCupBattleResult(battleResultId, battleType, winnerId, loserId, finalResponseBytes);
    }

    @Override
    protected BattleType getType() {
        return BattleType.CROSS_CUP;
    }
}