package cn.daxiang.hbtd.gameserver.core.database.table;

import cn.daxiang.framework.database.SingleEntity;
import cn.daxiang.framework.database.annotation.Column;
import cn.daxiang.framework.database.annotation.DBQueueType;
import cn.daxiang.framework.database.annotation.Table;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.hbtd.gameserver.module.heroRoad.model.entity.HeroRecordEntity;
import cn.daxiang.hbtd.gameserver.module.heroRoad.model.entity.HeroRoadLineupEntity;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 英雄之路（传记）
 *
 * <AUTHOR>
 * @date 2023/7/18
 */
@Table(name = "hero_road", type = DBQueueType.IMPORTANT)
public class HeroRoad extends SingleEntity<Long> {
    /**
     * 角色ID
     */
    @Column(pk = true, fk = true)
    private long actorId;
    /**
     * 英雄传记信息列表
     * key:storyId,value:{ key:difficultyLevel, value:英雄传记信息列表 }
     */
    @Column(alias = "heroRecord")
    private Map<Integer, Map<Integer, HeroRecordEntity>> heroRecordMap = Maps.newConcurrentMap();
    /**
     * 已领取奖励ID列表
     */
    @Column(alias = "rewardRecord")
    private Collection<Integer> rewardRecordList = Lists.newArrayList();

    /**
     * 阵容信息
     * key:storyId value{ key:difficultyLevel，value:HeroRoadLineup}
     */
    @Column(alias = "lineup")
    private Map<Integer, Map<Integer, HeroRoadLineupEntity>> lineupMap = Maps.newConcurrentMap();

    /**
     * 前置准备关卡传记ID
     */
    @Column
    private int prepareStoryId;
    /**
     * 前置准备关卡难度等级
     */
    @Column
    private int prepareDifficultyLevel;
    /**
     * 前置关卡时间记录
     */
    @Column
    private long prepareTime;

    @Override
    public IdentiyKey findPkId() {
        return IdentiyKey.build(actorId);
    }

    @Override
    public void setPkId(IdentiyKey pk) {
        this.actorId = pk.getFirstLongId();
    }

    @Override
    public List<IdentiyKey> keyLists() {
        return Lists.newArrayList(findPkId());
    }

    public long getActorId() {
        return actorId;
    }

    public Map<Integer, Map<Integer, HeroRecordEntity>> getHeroRecordMap() {
        return heroRecordMap;
    }

    public Collection<Integer> getRewardRecordList() {
        return rewardRecordList;
    }

    public Map<Integer, Map<Integer, HeroRoadLineupEntity>> getLineupMap() {
        return lineupMap;
    }

    public int getPrepareStoryId() {
        return prepareStoryId;
    }

    public int getPrepareDifficultyLevel() {
        return prepareDifficultyLevel;
    }

    public long getPrepareTime() {
        return prepareTime;
    }

    public void prepare(int prepareStoryId, int difficultyLevel) {
        this.prepareStoryId = prepareStoryId;
        this.prepareDifficultyLevel = difficultyLevel;
        this.prepareTime = System.currentTimeMillis();
    }

    public void resetPrepare() {
        this.prepareStoryId = 0;
        this.prepareDifficultyLevel = 0;
        this.prepareTime = 0L;
    }

    public void addLineup(int storyId, int difficultyLevel, HeroRoadLineupEntity heroRoadLineupEntity) {
        Map<Integer, HeroRoadLineupEntity> entityMap = this.lineupMap.get(storyId);
        if (entityMap == null) {
            entityMap = Maps.newHashMap();
            this.lineupMap.put(storyId, entityMap);
        }
        entityMap.put(difficultyLevel, heroRoadLineupEntity);
    }
}
