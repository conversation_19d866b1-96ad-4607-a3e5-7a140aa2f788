package cn.daxiang.hbtd.gameserver.module.battlefieldDrill.dao;

import cn.daxiang.hbtd.gameserver.core.database.table.BattlefieldDrill;
import cn.daxiang.hbtd.gameserver.module.battlefieldDrill.model.BattlefieldDrillRank;
import cn.daxiang.hbtd.gameserver.module.battlefieldDrill.model.HonourRank;

import java.util.Collection;
import java.util.Map;

public interface BattlefieldDrillDao {
    /**
     * 获取沙场演武信息
     *
     * @param actorId
     * @return
     */
    BattlefieldDrill getBattlefieldDrill(long actorId);

    /**
     * 获取沙场演武信息
     *
     * @param actorId
     * @return
     */
    BattlefieldDrillRank getBattlefieldDrillRank(long actorId);

    /**
     * 获取沙场演武排名
     *
     * @param rank
     * @return
     */
    BattlefieldDrillRank getBattlefieldDrillRankByRank(long rank);

    /**
     * 参与排名
     *
     * @param ancientCorridor
     */
    void achieveRank(BattlefieldDrill ancientCorridor);

    /**
     * 获取沙场演武排名
     *
     * @return
     */
    Collection<BattlefieldDrillRank> getRanks();

    /**
     * 获取沙场演武全部排名
     *
     * @return
     */
    Collection<BattlefieldDrillRank> getAllRanks();

    int getRankSize();

    void cleanRank();

    /**
     * 荣耀殿堂
     *
     * @param actorId
     * @param id
     * @param power
     * @param achieveTime
     */
    void achieveHonourRank(long actorId, int id, long power, long achieveTime);

    /**
     * 获取荣耀殿堂排行
     *
     * @param id
     * @return
     */
    Collection<HonourRank> getHonourRanks(int id);

    /**
     * 获取荣耀殿堂排行
     *
     * @return
     */
    public Map<Integer, Collection<HonourRank>> getHonourRanks();
}
