package cn.daxiang.hbtd.gameserver.module.cave.model.entity;

/**
 * 已经激活的天魔的状态
 */
public class CaveHardMonsterInfo {
    /**
     * 天魔类型 CaveMonsterType
     */
    private int type;
    /**
     * 消散时间
     */
    private long time;
    /**
     * 阶段
     */
    private int stage;

    public static CaveHardMonsterInfo valueOf(int type, long time, int stage) {
        CaveHardMonsterInfo info = new CaveHardMonsterInfo();
        info.type = type;
        info.time = time;
        info.stage = stage;
        return info;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public long getTime() {
        return time;
    }

    public void setTime(long time) {
        this.time = time;
    }

    public int getStage() {
        return stage;
    }

    public void setStage(int stage) {
        this.stage = stage;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;
        CaveHardMonsterInfo info = (CaveHardMonsterInfo) o;
        return type == info.type && time == info.time && stage == info.stage;
    }

    @Override
    public int hashCode() {
        return String.valueOf(type).hashCode() + String.valueOf(stage).hashCode() + String.valueOf(time).hashCode();
    }
}
