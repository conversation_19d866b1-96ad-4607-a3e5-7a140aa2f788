package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.dataconfig.annotation.FieldIgnore;
import cn.daxiang.framework.identity.IdentiyKey;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * @author: Gary
 * @date: 2023/2/17 16:43
 * @Description:
 */
@DataFile(fileName = "activity_31_task_config")
public class Activity31TaskConfig implements ModelAdapter {
    /**
     * data
     */
    private int data;
    /**
     * 天数
     */
    private int day;
    /**
     * 任务Id列表
     */
    private String taskIds;
    @FieldIgnore
    private List<Integer> taskIdList = Lists.newArrayList();

    @Override
    public void initialize() {
        taskIdList = JSONArray.parseArray(taskIds, Integer.class);
    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(data, day);
    }

    public int getData() {
        return data;
    }

    public int getDay() {
        return day;
    }

    public List<Integer> getTaskIdList() {
        return taskIdList;
    }
}
