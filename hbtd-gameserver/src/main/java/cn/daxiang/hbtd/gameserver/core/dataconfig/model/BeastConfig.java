package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.dataconfig.annotation.FieldIgnore;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.shared.module.lineup.SpriteAttributeType;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Maps;

import java.util.Map;

/**
 * @author: Gary
 * @date: 2023/4/26 12:12
 * @Description:
 */
@DataFile(fileName = "beast_config")
public class BeastConfig implements ModelAdapter {

    /**
     * 神兽Id
     */
    private int id;
    /**
     * 品质
     */
    private int quality;
    /**
     * 类型
     */
    private int type;
    /**
     * 神兽基础属性
     */
    private String beastAttributes;
    /**
     * 碎片Id
     */
    private int fragmentId;
    /**
     * 重复获得神兽的碎片数量
     */
    private int fragmentCount;
    /**
     * 初始合成所需碎片数量
     */
    private int initNumber;
    /**
     * 图鉴激活属性加成
     */
    private String manualAttributes;
    /**
     * 神兽实力对比积分计算公式
     */
    private String scoreExpr;

    /**
     * 神兽基础属性Map 神兽自身
     * key:SpriteAttributeType,value:value
     */
    @FieldIgnore
    private Map<SpriteAttributeType, Long> beastAttributeMap = Maps.newHashMap();

    /**
     * 图鉴属性Map  全体英雄
     * key:SpriteAttributeType,value:value
     */
    @FieldIgnore
    private Map<SpriteAttributeType, Long> manualAttributeMap = Maps.newHashMap();
    /**
     * 图鉴属性Map 属性万分比Map
     * key:SpriteAttributeType,value:ttpercent
     */
    @FieldIgnore
    private Map<SpriteAttributeType, Integer> manualAttributeTTPercentMap = Maps.newHashMap();

    @Override
    public void initialize() {
        JSONArray attributesArray = JSONArray.parseArray(manualAttributes);
        for (Object attributeItem : attributesArray) {
            JSONArray attributeArray = JSONArray.parseArray(attributeItem.toString());
            if (attributeArray.getIntValue(0) > 100) {
                manualAttributeTTPercentMap.put(SpriteAttributeType.getType(attributeArray.getIntValue(0) - 100), attributeArray.getInteger(1));
            } else {
                manualAttributeMap.put(SpriteAttributeType.getType(attributeArray.getIntValue(0)), attributeArray.getLong(1));
            }
        }
        JSONArray beastAttributesArray = JSONArray.parseArray(beastAttributes);
        for (Object attributeItem : beastAttributesArray) {
            JSONArray attributeArray = JSONArray.parseArray(attributeItem.toString());
            beastAttributeMap.put(SpriteAttributeType.getType(attributeArray.getIntValue(0)), attributeArray.getLong(1));
        }
    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(id);
    }

    public int getId() {
        return id;
    }

    public int getQuality() {
        return quality;
    }

    public int getType() {
        return type;
    }

    public int getFragmentId() {
        return fragmentId;
    }

    public int getFragmentCount() {
        return fragmentCount;
    }

    public Map<SpriteAttributeType, Long> getBeastAttributeMap() {
        return beastAttributeMap;
    }

    public Map<SpriteAttributeType, Long> getManualAttributeMap() {
        return manualAttributeMap;
    }

    public Map<SpriteAttributeType, Integer> getManualAttributeTTPercentMap() {
        return manualAttributeTTPercentMap;
    }

    public String getScoreExpr() {
        return scoreExpr;
    }

    public int getInitNumber() {
        return initNumber;
    }
}
