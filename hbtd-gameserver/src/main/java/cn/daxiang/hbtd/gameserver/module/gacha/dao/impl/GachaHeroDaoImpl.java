package cn.daxiang.hbtd.gameserver.module.gacha.dao.impl;

import cn.daxiang.framework.database.SingleEntity;
import cn.daxiang.framework.database.dao.SingleEntityDaoImpl;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.framework.utils.TimeUtils;
import cn.daxiang.hbtd.gameserver.core.database.table.GachaHero;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.GlobalConfigService;
import cn.daxiang.hbtd.gameserver.module.gacha.dao.GachaHeroDao;
import cn.daxiang.shared.GlobalConfigKey;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class GachaHeroDaoImpl extends SingleEntityDaoImpl implements GachaHeroDao {

    @Autowired
    private GlobalConfigService globalConfigService;

    @Override
    protected Class<? extends SingleEntity<?>> forClass() {
        return GachaHero.class;
    }

    @Override
    protected void initMaxId() {

    }

    @Override
    public GachaHero getGachaHero(long actorId) {
        GachaHero table = this.get(IdentiyKey.build(actorId));
        if (table.newEntity()) {
            table.setNewEntity(false);
            Long nextResetWishTime =
                System.currentTimeMillis() + globalConfigService.findGlobalConfig(GlobalConfigKey.WISHLIST_REFRESH_TIME).findLong() * (long) TimeUtils.ONE_DAY_MILLISECOND;
            table.setNextResetWishTime(nextResetWishTime);
            Long nextResetBlessTime =
                System.currentTimeMillis() + globalConfigService.findGlobalConfig(GlobalConfigKey.BLESS_REFRESH_TIME).findLong() * (long) TimeUtils.ONE_DAY_MILLISECOND;
            table.setNextResetBlessTime(nextResetBlessTime);
            dbQueue.updateQueue(table);
        } else {
            boolean isReset = false;
            if (System.currentTimeMillis() > table.getNextResetWishTime()) {
                Long nextResetWishTime =
                    System.currentTimeMillis() + globalConfigService.findGlobalConfig(GlobalConfigKey.WISHLIST_REFRESH_TIME).findLong() * (long) TimeUtils.ONE_DAY_MILLISECOND;
                table.setNextResetWishTime(nextResetWishTime);
                table.getWishRecordList().clear();
                isReset = true;
            }
            if (System.currentTimeMillis() > table.getNextResetBlessTime()) {
                Long nextResetBlessTime =
                    System.currentTimeMillis() + globalConfigService.findGlobalConfig(GlobalConfigKey.BLESS_REFRESH_TIME).findLong() * (long) TimeUtils.ONE_DAY_MILLISECOND;
                table.resetBless(nextResetBlessTime);
                isReset = true;
            }
            if (isReset) {
                dbQueue.updateQueue(table);
            }
        }
        return table;
    }

}
