package cn.daxiang.hbtd.gameserver.module.reputation.model;

/**
 * 平定天下挑战目标奖励类型
 *
 * @author: <PERSON>
 * @date: 2023/3/14 10:06
 * @Description:
 */
public enum TargetRewardType {
    /**
     * 1.事件类型目标
     */
    EVENT(1),

    /**
     * 2.难度挑战目标
     */
    DEGREE(2),

    /**
     * 0.none
     */
    NONE(0);

    private int id;

    TargetRewardType(int id) {
        this.id = id;
    }

    public static TargetRewardType getType(int id) {
        for (TargetRewardType type : TargetRewardType.values()) {
            if (type.id == id) {
                return type;
            }
        }
        return NONE;
    }

    public int getId() {
        return id;
    }

    @Override
    public String toString() {
        return String.valueOf(id);
    }
}
