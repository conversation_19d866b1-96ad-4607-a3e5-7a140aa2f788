package cn.daxiang.hbtd.gameserver.module.activity.model.global.entity;

/**
 * <AUTHOR>
 * @date 2022/10/28
 */
public class Activity22RewardRecord {
    /**
     * 角色ID
     */
    private long actorId;
    /**
     * 获得元宝数量
     */
    private long diamond;

    public static Activity22RewardRecord valueOf(long actorId, long diamond) {
        Activity22RewardRecord record = new Activity22RewardRecord();
        record.setActorId(actorId);
        record.setDiamond(diamond);
        return record;
    }

    public long getActorId() {
        return actorId;
    }

    public void setActorId(long actorId) {
        this.actorId = actorId;
    }

    public long getDiamond() {
        return diamond;
    }

    public void setDiamond(long diamond) {
        this.diamond = diamond;
    }
}
