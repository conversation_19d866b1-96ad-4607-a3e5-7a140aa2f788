package cn.daxiang.hbtd.gameserver.module.activity.model;

/**
 * 群雄逐鹿排行榜
 */
public class Global2Rank {
    /**
     * 角色ID
     */
    private long actorId;
    /**
     * 排名
     */
    private long rank;
    /**
     * 主线进度
     */
    private long value;

    public static Global2Rank valueOf(long actorId, long rank, long value) {
        Global2Rank vo = new Global2Rank();
        vo.actorId = actorId;
        vo.rank = rank;
        vo.value = value;
        return vo;
    }

    public long getActorId() {
        return actorId;
    }

    public void setActorId(long actorId) {
        this.actorId = actorId;
    }

    public long getRank() {
        return rank;
    }

    public void setRank(long rank) {
        this.rank = rank;
    }

    public long getValue() {
        return value;
    }

    public void setValue(long value) {
        this.value = value;
    }
}
