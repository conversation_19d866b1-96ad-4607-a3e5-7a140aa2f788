package cn.daxiang.hbtd.gameserver.module.arena.helper;

import cn.daxiang.hbtd.gameserver.core.database.table.Arena;
import cn.daxiang.hbtd.gameserver.module.user.helper.ActorHelper;
import cn.daxiang.protocol.game.ArenaProtocol;

import java.util.Collection;

/**
 * <AUTHOR>
 */
public class ArenaHelper {

    public static ArenaProtocol.ArenaInfoResponse buildArenaInfoResponse(Arena arena, Collection<ArenaProtocol.ArenaRank> rankList) {
        ArenaProtocol.ArenaInfoResponse.Builder builder = ArenaProtocol.ArenaInfoResponse.newBuilder();
        builder.setPlayTimes(arena.getPlayTimes());
        builder.setBuyTimes(arena.getBuyTimes());
        builder.setRank(arena.getRank());
        builder.setMaxRank(arena.getMaxRank());
        builder.addAllRanks(rankList);
        builder.addAllReceiveHistoricalRankRewards(arena.getReceiveHistoricalRankRewardList());
        builder.addAllReceiveDailyRewards(arena.getReceiveDailyRewardList());
        builder.setPlayDailyTotalTimes(arena.getPlayDailyTotalTimes());
        builder.setChangeOpponentTimes(arena.getChangeTimes());
        builder.setDeclaration(ActorHelper.getDeclaration(arena.getActorId()));
        ArenaProtocol.ArenaInfoResponse response = builder.build();
        return response;
    }
}
