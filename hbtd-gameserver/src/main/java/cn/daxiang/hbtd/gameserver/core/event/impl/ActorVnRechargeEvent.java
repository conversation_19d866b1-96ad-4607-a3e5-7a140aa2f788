package cn.daxiang.hbtd.gameserver.core.event.impl;

import cn.daxiang.framework.event.ActorEvent;
import cn.daxiang.shared.event.EventKey;
import cn.daxiang.shared.reward.RewardObject;

import java.util.Collection;

/**
 * 越南渠道玩家充值处理事件
 *
 * <AUTHOR>
 */
public class ActorVnRechargeEvent extends ActorEvent {
    /**
     * 订单id
     */
    private String orderId;
    /**
     * 充值商品ID
     */
    private int chargeId;
    /**
     * 充值金额
     */
    private long costValue;
    /**
     * 充值奖励
     */
    private Collection<RewardObject> rewardList;
    /**
     * 是否首充
     */
    private boolean firstCharge;

    public ActorVnRechargeEvent(long actorId, String orderId, long costValue, int chargeId, Collection<RewardObject> rewardList, boolean firstCharge) {
        super(EventKey.ACTOR_VN_RECHARGE_EVENT, actorId);
        this.orderId = orderId;
        this.costValue = costValue;
        this.chargeId = chargeId;
        this.rewardList = rewardList;
        this.firstCharge = firstCharge;
    }

    public String getOrderId() {
        return orderId;
    }

    public int getChargeId() {
        return chargeId;
    }

    public long getCostValue() {
        return costValue;
    }

    public Collection<RewardObject> getRewardList() {
        return rewardList;
    }

    public boolean isFirstCharge() {
        return firstCharge;
    }
}
