package cn.daxiang.hbtd.gameserver.module.gacha.type;

import cn.daxiang.hbtd.gameserver.core.oss.type.OperationType;

public enum GachaType {
    /**
     * 1.友情英雄召唤
     */
    NORMAL_HERO(1, OperationType.NORMAL_HERO),
    /**
     * 2.元宝英雄召唤
     */
    DIAMOND_HERO(2, OperationType.DIAMOND_HERO),

    /**
     * 0.NONE
     */
    NONE(0, OperationType.NONE);

    private int id;

    private OperationType operationType;

    private GachaType(int id, OperationType operationType) {
        this.id = id;
        this.operationType = operationType;
    }

    public static GachaType getType(int id) {
        for (GachaType type : GachaType.values()) {
            if (type.id == id) {
                return type;
            }
        }
        return NONE;
    }

    public int getId() {
        return this.id;
    }

    public OperationType getOperationType() {
        return operationType;
    }

    @Override
    public String toString() {
        return String.valueOf(id);
    }
}
