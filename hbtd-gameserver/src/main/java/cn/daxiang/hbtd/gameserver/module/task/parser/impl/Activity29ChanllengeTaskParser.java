package cn.daxiang.hbtd.gameserver.module.task.parser.impl;

import cn.daxiang.hbtd.gameserver.core.database.table.Task;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.TaskConfig;
import cn.daxiang.hbtd.gameserver.core.event.impl.Activity29ChanllengeEvent;
import cn.daxiang.hbtd.gameserver.module.task.parser.AbstractTaskParser;
import cn.daxiang.hbtd.gameserver.module.task.type.TaskConditionType;
import cn.daxiang.protocol.game.TaskProtocol;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/12/29
 */
@Component
public class Activity29ChanllengeTaskParser extends AbstractTaskParser<Activity29ChanllengeEvent> {
    @Override
    public void init(Task task, TaskConfig taskConfig) {
        if (task.getValue() >= taskConfig.getValue()) {
            task.setStatus(TaskProtocol.TaskStatus.TASK_FINISH);
        }
    }

    @Override
    protected TaskConditionType getType() {
        return TaskConditionType.ACTIVITY_29_CHALLENGE;
    }

    @Override
    protected boolean parseCondition(Activity29ChanllengeEvent event, Task task, TaskConfig taskConfig) {
        if (event.chapterId >= (int) taskConfig.getValue()) {
            task.setValue(event.chapterId);
            task.setStatus(TaskProtocol.TaskStatus.TASK_FINISH);
            return true;
        }
        return false;
    }

}
