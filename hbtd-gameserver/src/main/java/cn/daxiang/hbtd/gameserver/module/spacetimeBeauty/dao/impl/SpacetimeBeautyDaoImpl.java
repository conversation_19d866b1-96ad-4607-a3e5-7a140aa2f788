package cn.daxiang.hbtd.gameserver.module.spacetimeBeauty.dao.impl;

import cn.daxiang.framework.database.MultiEntity;
import cn.daxiang.framework.database.dao.MultiEntityDaoImpl;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.hbtd.gameserver.core.database.table.SpacetimeBeauty;
import cn.daxiang.hbtd.gameserver.module.spacetimeBeauty.dao.SpacetimeBeautyDao;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;

/**
 * @author: Gary
 * @date: 2023/8/16 16:47
 * @Description:
 */
@Component
public class SpacetimeBeautyDaoImpl extends MultiEntityDaoImpl implements SpacetimeBeautyDao {
    @Override
    protected Class<? extends MultiEntity<?>> forClass() {
        return SpacetimeBeauty.class;
    }

    @Override
    protected void initMaxId() {

    }

    @Override
    public Collection<SpacetimeBeauty> getSpacetimeBeautyList(long actorId) {
        Map<IdentiyKey, SpacetimeBeauty> entityMap = this.getByFk(actorId);
        return entityMap.values();
    }

    @Override
    public SpacetimeBeauty getSpacetimeBeauty(long actorId, int configId) {
        return this.getMultiEnity(actorId, IdentiyKey.build(actorId, configId));
    }

    @Override
    public SpacetimeBeauty creatSpacetimeBeauty(long actorId, int configId) {
        SpacetimeBeauty spacetimeBeauty = SpacetimeBeauty.valueOf(actorId, configId);
        this.updateQueue(spacetimeBeauty);
        return spacetimeBeauty;
    }
}
