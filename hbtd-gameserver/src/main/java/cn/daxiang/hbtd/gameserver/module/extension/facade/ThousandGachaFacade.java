package cn.daxiang.hbtd.gameserver.module.extension.facade;

import cn.daxiang.framework.result.TResult;
import cn.daxiang.hbtd.gameserver.core.database.table.ThousandGacha;
import cn.daxiang.protocol.game.CommonProtocol;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/11/6
 */
@Component
public interface ThousandGachaFacade {

    /**
     * 获取1000抽活动信息
     *
     * @param actorId
     * @return
     */
    TResult<ThousandGacha> getThousandGacha(long actorId);

    /**
     * 领取奖励
     *
     * @param actorId
     * @return
     */
    TResult<CommonProtocol.RewardResultResponse> receive(long actorId);
}
