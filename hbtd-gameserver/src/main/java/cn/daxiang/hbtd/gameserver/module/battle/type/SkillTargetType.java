package cn.daxiang.hbtd.gameserver.module.battle.type;

/**
 * 技能目标类型
 *
 * <AUTHOR>
 */
public enum SkillTargetType {
    NONE(0),
    /**
     * 1.当前攻击者
     */
    ATTACKER(1),

    /**
     * 2.敌方
     */
    ENEMY(2),

    /**
     * 3.友方
     */
    FRIENDLY(3),

    /**
     * 4.所有精灵
     */
    BATTLE_ALL_SPRITE(4),

    /**
     * 5.友方(不包含自己)
     */
    FRIENDLY_EXCLUDE_ONESELF(5),
    /**
     * 6.触发目标
     */
    TRIGGER_TARGET(6),
    ;

    private int id;

    private SkillTargetType(int id) {
        this.id = id;
    }

    public static SkillTargetType getType(int id) {
        for (SkillTargetType type : SkillTargetType.values()) {
            if (type.id == id) {
                return type;
            }
        }
        return NONE;
    }

    public int getId() {
        return id;
    }

    @Override
    public String toString() {
        return String.valueOf(id);
    }
}
