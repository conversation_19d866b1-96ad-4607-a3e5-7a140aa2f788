package cn.daxiang.hbtd.gameserver.module.battle.parser.seek.impl;

import cn.daxiang.hbtd.gameserver.core.dataconfig.model.SkillSeekConfig;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleRoom;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleSprite;
import cn.daxiang.hbtd.gameserver.module.battle.parser.seek.AbstractSkillSeekParser;
import cn.daxiang.hbtd.gameserver.module.battle.type.SkillEffectType;
import cn.daxiang.hbtd.gameserver.module.battle.type.SkillSeekType;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 阵营中，业火层数最高的目标之外的人
 *
 * @author: Gary
 * @date: 2023/11/17 10:43
 * @Description:
 */
@Component
public class ConflagrationLayersHighestOutsideSeekParser extends AbstractSkillSeekParser {
    @Override
    protected SkillSeekType getType() {
        return SkillSeekType.CONFLAGRATION_LAYERS_HIGHEST_OUTSIDE;
    }

    @Override
    protected List<BattleSprite> seekSprites(BattleSprite attacker, BattleRoom battleRoom, SkillSeekConfig skillSeekConfig) {
        List<BattleSprite> seekSprites = Lists.newArrayList();
        List<BattleSprite> targetSprites = this.getTargetSpriteList(attacker, battleRoom, skillSeekConfig);
        int targetCount = Math.min(targetSprites.size() - 1, skillSeekConfig.getTargetCount());
        List<BattleSprite> collect =
            targetSprites.stream().sorted(Comparator.comparingInt(x -> x.getBuffLayers(SkillEffectType.CONFLAGRATION_BUFF_EFFECT))).collect(Collectors.toList());
        if (collect.get(1).getBuffLayers(SkillEffectType.CONFLAGRATION_BUFF_EFFECT) == 0) {
            return seekSprites;
        }
        for (int i = 0; i < targetCount; i++) {
            BattleSprite targetSprite = collect.remove(0);
            seekSprites.add(targetSprite);
        }
        return seekSprites;
    }
}
