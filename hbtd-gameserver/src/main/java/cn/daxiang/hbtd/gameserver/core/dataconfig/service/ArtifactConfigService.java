package cn.daxiang.hbtd.gameserver.core.dataconfig.service;

import cn.daxiang.framework.dataconfig.ConfigServiceAdapter;
import cn.daxiang.framework.extend.KeyValue;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.ArtifactGatherConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.ArtifactLevelConfig;
import cn.daxiang.hbtd.gameserver.module.lineup.helper.LineupAttributeHelper;
import cn.daxiang.shared.module.lineup.SpriteAttributeType;
import cn.daxiang.shared.reward.RewardObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;
import java.util.TreeMap;

@Component
public class ArtifactConfigService extends ConfigServiceAdapter {
    /**
     * key: starId value: specialEffectIdList
     */
    private static Map<Integer, Collection<Integer>> STAR_ID_SPECIAL_EFFECT_ID_LIST_MAP = Maps.newHashMap();
    /**
     * key: type value: {key:同type配置的数量,value:specialEffectIdList}
     */
    private static Map<Integer, KeyValue<Integer, Collection<Integer>>> TYPE_SIZE_COMPLETE_EFFECT_ID_LIST_MAP = Maps.newHashMap();
    /**
     * key: level value: specialEffectIdList
     */
    private static TreeMap<Integer, Collection<Integer>> LEVEL_SPECIAL_EFFECT_ID_LIST_MAP = Maps.newTreeMap();
    /**
     * 将阶段属性换算成每个等级对应的属性方便直接使用
     * key: level value:Map<SpriteAttributeType, Long>
     */
    private static Map<Integer, Map<SpriteAttributeType, Long>> LEVEL_ATTRIBUTE_MAP = Maps.newHashMap();

    public static Collection<Integer> getCompleteEffectIds(int type, int size) {
        Collection<Integer> effectIDs = Lists.newArrayList();
        KeyValue<Integer, Collection<Integer>> sizeCompleteKY = TYPE_SIZE_COMPLETE_EFFECT_ID_LIST_MAP.get(type);
        if (sizeCompleteKY == null) {
            return effectIDs;
        }
        if (sizeCompleteKY.getKey() <= size) {
            effectIDs.addAll(sizeCompleteKY.getValue());
        }
        return effectIDs;
    }

    public static Map<SpriteAttributeType, Long> getTotalAttributeMap(int level) {
        return LEVEL_ATTRIBUTE_MAP.getOrDefault(level, Maps.newHashMap());
    }

    public static Collection<Integer> getSpecialEffectIdsByStarId(int starId) {
        return STAR_ID_SPECIAL_EFFECT_ID_LIST_MAP.get(starId);
    }

    public static Collection<Integer> getSpecialEffectIdsByLevel(int level) {
        return LEVEL_SPECIAL_EFFECT_ID_LIST_MAP.getOrDefault(level, Lists.newArrayList());
    }

    @Override
    protected void initialize() {
        Collection<ArtifactGatherConfig> ArtifactGatherConfigList = dataConfig.listAll(this, ArtifactGatherConfig.class);
        for (ArtifactGatherConfig config : ArtifactGatherConfigList) {
            Collection<Integer> specialIdList = STAR_ID_SPECIAL_EFFECT_ID_LIST_MAP.get(config.getStarID());
            if (specialIdList == null) {
                specialIdList = Lists.newArrayList();
                STAR_ID_SPECIAL_EFFECT_ID_LIST_MAP.put(config.getStarID(), specialIdList);
            }
            specialIdList.addAll(config.getSpecialEffectIds());

            KeyValue<Integer, Collection<Integer>> sizeCompleteEffectIDs = TYPE_SIZE_COMPLETE_EFFECT_ID_LIST_MAP.get(config.getType());
            if (sizeCompleteEffectIDs == null) {
                sizeCompleteEffectIDs = new KeyValue<>(0, Lists.newArrayList());
                TYPE_SIZE_COMPLETE_EFFECT_ID_LIST_MAP.put(config.getType(), sizeCompleteEffectIDs);
            }
            sizeCompleteEffectIDs.setKey(sizeCompleteEffectIDs.getKey() + 1);
            sizeCompleteEffectIDs.getValue().addAll(config.getCompleteEffectIDs());
        }

        TreeMap<Integer, ArtifactLevelConfig> treeMap = Maps.newTreeMap();
        Collection<ArtifactLevelConfig> ArtifactLevelConfigList = dataConfig.listAll(this, ArtifactLevelConfig.class);
        for (ArtifactLevelConfig config : ArtifactLevelConfigList) {
            treeMap.put(config.getLevel(), config);
        }
        Collection<Integer> specialEffectIdList = Lists.newArrayList();
        for (ArtifactLevelConfig config : treeMap.values()) {
            specialEffectIdList.addAll(config.getSpecialEffectIds());
            Collection<Integer> specialEffectIds = Lists.newArrayList();
            specialEffectIds.addAll(specialEffectIdList);
            LEVEL_SPECIAL_EFFECT_ID_LIST_MAP.put(config.getLevel(), specialEffectIds);

            Map<SpriteAttributeType, Long> totalAttributeMap = LEVEL_ATTRIBUTE_MAP.get(config.getLevel());
            if (totalAttributeMap == null) {
                totalAttributeMap = Maps.newHashMap();
                LEVEL_ATTRIBUTE_MAP.put(config.getLevel(), totalAttributeMap);
            }
            for (SpriteAttributeType type : config.getProgressAttributeMap().keySet()) {
                RewardObject costRewardObject = config.getCostRewardObject();
                if (costRewardObject == null) {
                    break;
                }
                long value = costRewardObject.getCount() * config.getProgressAttributeMap().get(type);
                totalAttributeMap.put(type, value);
            }
            if (config.getLevel() != 0) {
                Map<SpriteAttributeType, Long> lastTotalAttributeMap = LEVEL_ATTRIBUTE_MAP.get(config.getLevel() - 1);
                LineupAttributeHelper.addSpriteAttributeValue(totalAttributeMap, lastTotalAttributeMap);
            }
        }
    }

    @Override
    protected void clean() {
        LEVEL_SPECIAL_EFFECT_ID_LIST_MAP.clear();
        STAR_ID_SPECIAL_EFFECT_ID_LIST_MAP.clear();
        TYPE_SIZE_COMPLETE_EFFECT_ID_LIST_MAP.clear();
        LEVEL_ATTRIBUTE_MAP.clear();
    }
}
