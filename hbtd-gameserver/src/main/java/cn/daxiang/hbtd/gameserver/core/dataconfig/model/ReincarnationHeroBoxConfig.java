package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.dataconfig.annotation.FieldIgnore;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.framework.utils.RandomUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Maps;

import java.util.Map;

/**
 * 轮回宝箱配置表
 *
 * <AUTHOR>
 * @date 2023/12/18
 */
@DataFile(fileName = "reincarnation_box_config")
public class ReincarnationHeroBoxConfig implements ModelAdapter {
    /**
     * 星级
     */
    private int star;
    /**
     * 英雄星级概率
     * [[star,rate],[star,rate]]
     */
    private String heroRates;
    /**
     * 英雄数量
     */
    private int count;

    @FieldIgnore
    private Map<Integer, Integer> heroRateMap = Maps.newHashMap();

    @Override
    public void initialize() {
        JSONArray heroRateArray = JSON.parseArray(heroRates);
        for (Object heroRateItem : heroRateArray) {
            JSONArray itemArray = JSON.parseArray(heroRateItem.toString());
            heroRateMap.put(itemArray.getIntValue(0), itemArray.getIntValue(1));
        }
    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(star);
    }

    public int getStar() {
        return star;
    }

    public int getHeroStar() {
        return RandomUtils.randomByWeight(heroRateMap);
    }

    public int getCount() {
        return count;
    }
}