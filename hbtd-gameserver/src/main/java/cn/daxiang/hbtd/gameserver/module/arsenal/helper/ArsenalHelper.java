package cn.daxiang.hbtd.gameserver.module.arsenal.helper;

import cn.daxiang.framework.utils.FormulaUtils;
import cn.daxiang.framework.utils.ObjectReference;
import cn.daxiang.hbtd.gameserver.core.database.table.Arsenal;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.ArsenalMonsterConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.MonsterCapabilityConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.MonsterGroupConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.GlobalConfigService;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.MonsterConfigService;
import cn.daxiang.hbtd.gameserver.module.PbBuilder;
import cn.daxiang.hbtd.gameserver.module.user.helper.ActorHelper;
import cn.daxiang.protocol.game.ArsenalProtocol.ArsenalResponse;
import cn.daxiang.protocol.game.ArsenalProtocol.TeamInfoResponse;
import cn.daxiang.protocol.game.CommonProtocol.ActorProfile;
import cn.daxiang.protocol.game.CommonProtocol.RewardObjectList;
import cn.daxiang.shared.GlobalConfigKey;
import cn.daxiang.shared.module.lineup.SpriteAttributeType;
import cn.daxiang.shared.module.team.Team;
import cn.daxiang.shared.module.team.TeamExtArsenal;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;

@Component
public class ArsenalHelper {
    private static final Logger LOGGER = LoggerFactory.getLogger(ArsenalHelper.class);
    private static ObjectReference<ArsenalHelper> ref = new ObjectReference<>();

    @Autowired
    private GlobalConfigService globalConfigService;

    public static TeamInfoResponse buildTeam(Team team) {
        TeamInfoResponse.Builder builder = TeamInfoResponse.newBuilder();
        TeamExtArsenal extArsenal = (TeamExtArsenal) team.getExt();
        builder.setHeroId(extArsenal.getHeroId());
        builder.setLevel(extArsenal.getLevel());
        builder.setPower(extArsenal.getPower());
        builder.setLeaderActorId(team.getLeaderActorId());
        builder.setPassword(team.getPassword());
        for (Map.Entry<Integer, Map<Byte, Object>> entry : team.getAttributeMap().entrySet()) {
            ActorProfile actorProfile = ActorHelper.getActorProfile(entry.getValue());
            builder.putTeamActors(entry.getKey(), actorProfile);
        }
        return builder.build();
    }

    public static ArsenalResponse buildArsenal(long actorId, Arsenal arsenal) {
        ArsenalResponse.Builder builder = ArsenalResponse.newBuilder();
        builder.putAllMonsters(arsenal.getMonsterMap());
        builder.setPlayTimes(arsenal.getPlayTimes());
        builder.putAllHeroTokens(arsenal.getHeroTokenMap());
        RewardObjectList rewardObjectList = PbBuilder.buildRewardObjectList(arsenal.getDailyAwardedList());
        builder.setRewards(rewardObjectList);
        for (Map<Byte, Object> byteObjectMap : arsenal.getLastTeamActorList()) {
            builder.addLastTeamActors(ActorHelper.getActorProfile(byteObjectMap));
        }
        builder.setFreeRefreshTime(arsenal.getFreeRefreshTime());
        int actorLevel = ActorHelper.getActorLevel(actorId);
        for (Map.Entry<Integer, Integer> entry : arsenal.getMonsterMap().entrySet()) {
            long power = getPower(entry.getValue(), actorLevel);
            builder.putPowers(entry.getKey(), power);
        }
        ArsenalResponse response = builder.build();
        return response;
    }

    /**
     * 获取藏兵阁怪物战力
     *
     * @param arsenalMonsterConfigHeroId
     * @param actorLevel
     * @return
     */
    public static long getPower(int arsenalMonsterConfigHeroId, int actorLevel) {
        ArsenalMonsterConfig config = ref.get().globalConfigService.findConfig(arsenalMonsterConfigHeroId, ArsenalMonsterConfig.class);
        if (config == null) {
            return 0;
        }
        long power = getMonsterGroupPower(config.getMonsterGroup(), actorLevel);
        return power;
    }

    public static long getMonsterGroupPower(int monsterGroupId, int actorLevel) {
        MonsterGroupConfig config = ref.get().globalConfigService.findConfig(monsterGroupId, MonsterGroupConfig.class);
        long power = 0;
        for (Integer monsterId : config.getPositionMonsterMap().values()) {
            power += getMonsterPower(monsterId, actorLevel);
        }
        return power;
    }

    public static long getMonsterPower(int monsterId, int actorLevel) {
        String powerExpr = ref.get().globalConfigService.findGlobalConfig(GlobalConfigKey.FIGHT_POWER_EXPRESSION).getValue();
        MonsterCapabilityConfig monsterCapabilityConfig = MonsterConfigService.getMonsterCapabilityConfig(monsterId);
        String expr1 = ref.get().globalConfigService.findGlobalConfig(GlobalConfigKey.ARSENAL_MONSTER_ATTRIBUTES_EXPR1).getValue();
        String expr2 = ref.get().globalConfigService.findGlobalConfig(GlobalConfigKey.ARSENAL_MONSTER_ATTRIBUTES_EXPR2).getValue();
        Map<SpriteAttributeType, Long> attributeMap = monsterCapabilityConfig.getAttributeMap(expr1, expr2, actorLevel);

        List<Long> attributeList = Lists.newLinkedList();
        for (SpriteAttributeType attributeType : SpriteAttributeType.values()) {
            if (attributeType.getId() >= 99) {
                break;
            }
            attributeList.add(attributeMap.getOrDefault(attributeType, 0L));
        }
        long power = FormulaUtils.executeRoundingLong(powerExpr, attributeList.toArray(new Long[attributeList.size()]));
        return power;
    }

    @PostConstruct
    protected void init() {
        ref.set(this);
    }

}
