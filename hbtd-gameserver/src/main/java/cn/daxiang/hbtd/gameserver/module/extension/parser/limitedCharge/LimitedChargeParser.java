package cn.daxiang.hbtd.gameserver.module.extension.parser.limitedCharge;

import cn.daxiang.framework.event.GameEvent;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.hbtd.gameserver.core.database.table.LimitedTimeCharge;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.LimitedTimeChargeConfig;
import cn.daxiang.hbtd.gameserver.module.extension.model.LimitedTimeChargeEntity;

/**
 * <AUTHOR>
 * @date 2022/6/14
 */
public interface LimitedChargeParser {
    /**
     * 解析
     *
     * @param
     * @param config
     * @param limitedTimeCharge
     */
    TResult<LimitedTimeChargeEntity> parser(GameEvent event, LimitedTimeChargeConfig config, LimitedTimeCharge limitedTimeCharge);
}
