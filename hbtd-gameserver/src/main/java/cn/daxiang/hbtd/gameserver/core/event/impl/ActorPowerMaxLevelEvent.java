package cn.daxiang.hbtd.gameserver.core.event.impl;

import cn.daxiang.framework.event.ActorEvent;
import cn.daxiang.shared.event.EventKey;

/**
 * 历史最高战力提升事件
 *
 * <AUTHOR>
 * @date 2022/6/8
 */
public class ActorPowerMaxLevelEvent extends ActorEvent {
    /**
     * 当前战力
     */
    public long power;
    /**
     * 老战力
     */
    public long oldPower;

    public ActorPowerMaxLevelEvent(long actorId, long power, long oldPower) {
        super(EventKey.ACTOR_POWER_MAX_LEVEL_EVENT, actorId);
        this.power = power;
        this.oldPower = oldPower;
    }
}
