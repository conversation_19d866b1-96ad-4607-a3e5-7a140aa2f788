package cn.daxiang.hbtd.gameserver.module.sanctuary.facade.impl;

import cn.daxiang.framework.event.GameEvent;
import cn.daxiang.framework.event.TriggerTime;
import cn.daxiang.framework.event.annotation.Event;
import cn.daxiang.framework.event.annotation.EventOnline;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.framework.result.CollectionResult;
import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.framework.rpc.event.SanctuaryBossRefreshEvent;
import cn.daxiang.framework.rpc.world.WorldSanctuaryRpc;
import cn.daxiang.framework.utils.DateUtils;
import cn.daxiang.hbtd.gameserver.core.GameBaseFacade;
import cn.daxiang.hbtd.gameserver.core.GameConfig;
import cn.daxiang.hbtd.gameserver.core.database.table.Beast;
import cn.daxiang.hbtd.gameserver.core.database.table.Frostmourne;
import cn.daxiang.hbtd.gameserver.core.database.table.Hero;
import cn.daxiang.hbtd.gameserver.core.database.table.SanctuaryBoss;
import cn.daxiang.hbtd.gameserver.core.database.table.Soul;
import cn.daxiang.hbtd.gameserver.core.dataconfig.convert.impl.IntListConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.FrostmourneStarConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.HeroConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.SanctuaryBossRankRewardsConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.SoulSynthesisConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.SanctuaryConfigService;
import cn.daxiang.hbtd.gameserver.core.oss.type.OperationType;
import cn.daxiang.hbtd.gameserver.core.rpc.WorldRpcHelper;
import cn.daxiang.hbtd.gameserver.module.PbBuilder;
import cn.daxiang.hbtd.gameserver.module.battle.parser.battle.BattleContext;
import cn.daxiang.hbtd.gameserver.module.battle.parser.battle.BattleParser;
import cn.daxiang.hbtd.gameserver.module.beast.facade.BeastFacade;
import cn.daxiang.hbtd.gameserver.module.frostmourne.dao.FrostmourneDao;
import cn.daxiang.hbtd.gameserver.module.goods.helper.RewardHelper;
import cn.daxiang.hbtd.gameserver.module.hero.facade.HeroFacade;
import cn.daxiang.hbtd.gameserver.module.sanctuary.dao.SanctuaryBossDao;
import cn.daxiang.hbtd.gameserver.module.sanctuary.facade.SanctuaryActorFacade;
import cn.daxiang.hbtd.gameserver.module.sanctuary.facade.SanctuaryBossFacade;
import cn.daxiang.hbtd.gameserver.module.sanctuary.helper.SanctuaryHelper;
import cn.daxiang.hbtd.gameserver.module.sanctuary.helper.SanctuaryPushHelper;
import cn.daxiang.hbtd.gameserver.module.soul.facade.SoulFacade;
import cn.daxiang.hbtd.gameserver.module.soul.type.SoulType;
import cn.daxiang.hbtd.gameserver.module.user.helper.ActorHelper;
import cn.daxiang.hbtd.gameserver.module.user.type.ActorUnlockType;
import cn.daxiang.protocol.game.BattleProtocol;
import cn.daxiang.protocol.game.CommonProtocol;
import cn.daxiang.protocol.game.SanctuaryProtocol;
import cn.daxiang.shared.GlobalConfigKey;
import cn.daxiang.shared.event.EventKey;
import cn.daxiang.shared.module.battle.BattleParameterKey;
import cn.daxiang.shared.module.battle.BattleType;
import cn.daxiang.shared.module.sanctuary.BossScoreRankVO;
import cn.daxiang.shared.module.sanctuary.PassRecordLineup;
import cn.daxiang.shared.module.sanctuary.SanctuaryBossState;
import cn.daxiang.shared.module.sanctuary.SanctuaryRegionState;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/10/19
 */
@Component
public class SanctuaryBossFacadeImpl extends GameBaseFacade implements SanctuaryBossFacade {
    /**
     * 武将阵容最大上阵数量
     */
    private static final int HERO_LINEUP_MAX = 6;
    @Autowired
    private SanctuaryActorFacade sanctuaryActorFacade;
    @Autowired
    private SanctuaryBossDao sanctuaryBossDao;
    @Autowired
    private BattleContext battleContext;
    @Autowired
    private HeroFacade heroFacade;
    @Autowired
    private SoulFacade soulFacade;
    @Autowired
    private BeastFacade beastFacade;
    @Autowired
    private FrostmourneDao frostmourneDao;

    @Override
    public Result bossChallenge(Long actorId, Map<Integer, Integer> herosMap, Map<Integer, CommonProtocol.LongListPacket> soulMap, Map<Integer, Integer> beastMap,
        Map<Integer, Integer> frostmourneMap) {
        TResult<SanctuaryRegionState> stateTResult = sanctuaryActorFacade.getSanctuaryRegionState();
        if (stateTResult.isFail()) {
            return Result.valueOf(stateTResult.statusCode);
        }
        SanctuaryRegionState regionState = stateTResult.item;
        if (regionState.getBossState().isEnded()) {
            return Result.valueOf(SANCTUARY_BOSS_IS_CLOSED);
        }
        if (!checkTime(regionState)) {
            return Result.valueOf(SANCTUARY_BOSS_TIME_ERR);
        }
        TResult<SanctuaryBoss> sanctuaryBossTResult = this.get(actorId);
        if (sanctuaryBossTResult.isFail()) {
            return Result.valueOf(sanctuaryBossTResult.statusCode);
        }
        SanctuaryBoss sanctuaryBoss = sanctuaryBossTResult.item;
        if (!checkReceive(sanctuaryBoss, regionState.getBossState())) {
            return Result.valueOf(SANCTUARY_BOSS_CANNOT_CHALLENGE);
        }
        int configTimes = globalConfigService.findGlobalConfig(GlobalConfigKey.SANCTUARY_BOSS_DAILY_CHALLENGE_LIMIT).findInt();
        if (sanctuaryBoss.getChallengeTimes() >= configTimes) {
            return Result.valueOf(SANCTUARY_BOSS_TIMES_NOT_ENOUGH);
        }
        //-----------------验证英雄-------------------------------
        //key:positionId ,value:configId
        Map<Integer, Integer> heroMap = Maps.newHashMap();
        for (Map.Entry<Integer, Integer> entry : herosMap.entrySet()) {
            if (entry.getValue() <= 0) {
                continue;
            }
            heroMap.put(entry.getKey(), entry.getValue());
        }
        if (heroMap.size() == 0 || heroMap.size() > HERO_LINEUP_MAX) {
            return Result.valueOf(INVALID_PARAM);
        }
        Set<Integer> heroSet = Sets.newHashSet();
        //key:武将配置id，value:武将星级
        Map<Integer, Integer> heroStarMap = Maps.newHashMap();
        //key:武将配置id，value:武将品质
        Map<Integer, Integer> heroQualityMap = Maps.newHashMap();
        Map<Integer, Integer> heroSkinMap = Maps.newHashMap();
        Map<Integer, Integer> heroColorMap = Maps.newHashMap();
        HeroConfig heroConfig;
        for (Integer heroConfigId : heroMap.values()) {
            TResult<Hero> heroTResult = heroFacade.getHeroByConfigId(actorId, heroConfigId);
            if (heroTResult.isFail()) {
                return Result.valueOf(HERO_NOT_FOUND);
            }
            heroStarMap.put(heroConfigId, heroTResult.item.getStarLevel());
            heroSkinMap.put(heroConfigId, heroTResult.item.getSkinId());
            heroColorMap.put(heroConfigId, heroTResult.item.getHeroColorLevel());
            heroConfig = globalConfigService.findConfig(heroConfigId, HeroConfig.class);
            heroQualityMap.put(heroConfigId, heroConfig.getQuality());
            //判断传过来的英雄ID有没有重复
            if (!heroSet.add(heroConfigId)) {
                return Result.valueOf(HERO_IS_EXIST);
            }
        }
        //-----------------验证英雄-------------------------------
        //-----------------验证武魂-------------------------------
        //key:position,value:soulSkillEffectIds
        Map<Integer, Collection<Integer>> soulSkillEffectIdsMap = Maps.newHashMap();
        //key:position,value:soulConfigIds
        Map<Integer, Collection<Integer>> soulsMap = Maps.newHashMap();
        SoulSynthesisConfig synthesisConfig;
        for (Map.Entry<Integer, CommonProtocol.LongListPacket> entry : soulMap.entrySet()) {
            List<Long> soulUniqueIdList = Lists.newLinkedList();
            for (Long soulUniqueId : entry.getValue().getListList()) {
                if (soulUniqueId != 0) {
                    soulUniqueIdList.add(soulUniqueId);
                }
            }
            //不能装备超过两个武魂
            if (soulUniqueIdList.size() > 2) {
                return Result.valueOf(SANCTUARY_SOUL_EQUIP_ERROR);
            }
            int position = entry.getKey();
            int heroQuality = heroQualityMap.getOrDefault(heroMap.get(position), 0);
            //英雄品质在金色以下不能装备两个武魂
            if (SoulType.SOUL_TYPE_2.getQualityLimit() >= heroQuality && soulUniqueIdList.size() > SoulType.SOUL_TYPE_2.getLimit()) {
                return Result.valueOf(SANCTUARY_SOUL_CANNOT_EQUIP);
            }
            for (Long soulUniqueId : soulUniqueIdList) {
                TResult<Soul> soulTResult = soulFacade.getSoul(actorId, soulUniqueId);
                if (soulTResult.isFail()) {
                    return Result.valueOf(soulTResult.statusCode);
                }
                Soul soul = soulTResult.item;
                synthesisConfig = globalConfigService.findConfig(IdentiyKey.build(soul.getConfigId(), soul.getStage()), SoulSynthesisConfig.class);
                //非回合武魂不计算
                if (synthesisConfig.getType() != SoulType.SOUL_TYPE_2.getId()) {
                    return Result.valueOf(SANCTUARY_SOUL_TYPE_ERROR);
                }
                Collection<Integer> soulSkillEffectIdList = soulSkillEffectIdsMap.computeIfAbsent(position, x -> Lists.newLinkedList());
                soulSkillEffectIdList.addAll(synthesisConfig.getSkillEffectIdList());
                Collection<Integer> soulConfigIdList = soulsMap.computeIfAbsent(position, x -> Lists.newLinkedList());
                soulConfigIdList.add(soul.getConfigId());
            }
        }
        //-----------------验证武魂-------------------------------
        //-----------------验证神兽-------------------------------
        //key:position,value:soulConfigIds
        Collection<Integer> beasts = Lists.newArrayList();
        Map<Integer, Integer> beastsMap = Maps.newHashMap();
        for (Map.Entry<Integer, Integer> entry : beastMap.entrySet()) {
            if (entry.getValue() != 0) {
                beasts.add(entry.getValue());
                beastsMap.put(entry.getKey(), entry.getValue());
            }
        }
        CollectionResult<Beast> beastsResult = beastFacade.getBeastBagInfo(actorId);
        if (beastsResult.isOk()) {
            Collection<Beast> beastList = beastsResult.item;
            for (Beast beast : beastList) {
                beasts.remove(beast.getConfigId());
            }
        }
        //循环结束,如果客户端传过来的神兽集合没有被全部移除说明传过来的神兽有问题
        if (!beasts.isEmpty()) {
            return Result.valueOf(SANCTUARY_BEAST_ERROR);
        }
        //-----------------验证神兽-------------------------------
        //-----------------验证神兵-------------------------------
        //key:position,value:frostmourneConfigId
        Map<Integer, Integer> frostmournesMap = Maps.newHashMap();
        //key:position,value:frostmourneSkillId
        Map<Integer, Integer> frostmourneSkillIdMap = Maps.newHashMap();
        for (Map.Entry<Integer, Integer> entry : frostmourneMap.entrySet()) {
            if (entry.getValue() == 0) {
                continue;
            }
            Frostmourne frostmourne = frostmourneDao.getFrostmourne(actorId, entry.getValue());
            if (frostmourne == null) {
                continue;
            }
            frostmournesMap.put(entry.getKey(), entry.getValue());
            FrostmourneStarConfig starConfig = globalConfigService.findConfig(IdentiyKey.build(frostmourne.getConfigId(), frostmourne.getStarLevel()), FrostmourneStarConfig.class);
            if (starConfig != null) {
                frostmourneSkillIdMap.put(entry.getKey(), starConfig.getSkill());
            }
        }
        //-----------------验证神兵-------------------------------
        PassRecordLineup lineup = new PassRecordLineup();
        lineup.getHeroStarMap().putAll(heroStarMap);
        lineup.getHeroSkinMap().putAll(heroSkinMap);
        lineup.getHeroColorMap().putAll(heroColorMap);
        lineup.getHeroLineupMap().putAll(heroMap);
        lineup.getSoulMap().putAll(soulsMap);
        lineup.getBeastMap().putAll(beastsMap);
        lineup.getFrostmourneMap().putAll(frostmournesMap);
        lineup.getFrostmourneSkillMap().putAll(frostmourneSkillIdMap);
        BattleParser parser = battleContext.getParser(BattleType.SANCTUARY_BOSS);
        Map<BattleParameterKey, Object> parameterMap = Maps.newHashMap();
        parameterMap.put(BattleParameterKey.SANCTUARY_BOSS_LINEUP, lineup);
        parameterMap.put(BattleParameterKey.SANCTUARY_BOSS_SOUL_SKILL_ID_LIST, soulSkillEffectIdsMap);
        parameterMap.put(BattleParameterKey.SANCTUARY_BOSS_FEATURE_ID, regionState.getBossState().getFeatureId());
        parameterMap.put(BattleParameterKey.SANCTUARY_BOSS_ID, regionState.getBossState().getBossId());
        parameterMap.put(BattleParameterKey.SANCTUARY_SEASON_ID, regionState.getSeasonId());
        return parser.fight(actorId, parameterMap);
    }

    /**
     * 检查是否领取了奖励
     *
     * @param sanctuaryBoss
     * @return
     */
    private boolean checkReceive(SanctuaryBoss sanctuaryBoss, SanctuaryBossState bossState) {
        if (sanctuaryBoss.getBossScore() == 0) {
            return true;
        }
        if (sanctuaryBoss.getReceiveList().contains(sanctuaryBoss.getHistoryLoopId())) {
            return true;
        }
        if (sanctuaryBoss.getHistoryLoopId() == bossState.getLoopId()) {
            return true;
        }
        return false;
    }

    private boolean checkTime(SanctuaryRegionState state) {
        if (!state.getBossState().isOpened()) {
            return false;
        }
        Date date = new Date();
        int hours = date.getHours();
        int openHour = globalConfigService.findGlobalObject(GlobalConfigKey.SANCTUARY_BOSS_CHALLENGE_TIME, IntListConfig.class).findValues().get(0);
        int closeHour = globalConfigService.findGlobalObject(GlobalConfigKey.SANCTUARY_BOSS_CHALLENGE_TIME, IntListConfig.class).findValues().get(1);
        if (hours < openHour || hours >= closeHour) {
            return false;
        } else {
            return true;
        }
    }

    @Override
    public TResult<CommonProtocol.RewardResultResponse> bossReceive(long actorId) {
        TResult<SanctuaryRegionState> regionStateTResult = sanctuaryActorFacade.getSanctuaryShowState();
        if (regionStateTResult.isFail()) {
            return TResult.valueOf(regionStateTResult.statusCode);
        }
        SanctuaryBossState bossState = regionStateTResult.item.getBossState();
        TResult<SanctuaryBoss> bossTResult = this.get(actorId);
        if (bossTResult.isFail()) {
            return TResult.valueOf(bossTResult.statusCode);
        }
        SanctuaryBoss sanctuaryBoss = bossTResult.item;
        if (checkReceive(sanctuaryBoss, bossState)) {
            return TResult.valueOf(SANCTUARY_BOSS_HAS_RECEIVED);
        }
        int rank = sanctuaryBoss.getRank();
        sanctuaryBoss.receive(sanctuaryBoss.getHistoryLoopId());
        dbQueue.updateQueue(sanctuaryBoss);
        SanctuaryBossRankRewardsConfig sanctuaryBossRankRewardsConfig = SanctuaryConfigService.getSanctuaryBossRankRewardsConfig(regionStateTResult.item.getSeasonId(), rank);
        if (sanctuaryBossRankRewardsConfig == null) {
            LOGGER.error("SanctuaryBossRankRewardsConfig is not found! seasonId:{}, rank:{}", regionStateTResult.item.getSeasonId(), rank);
            return TResult.valueOf(CONFIG_NOT_FOUND);
        }
        SanctuaryProtocol.SanctuaryBossResponse response = SanctuaryHelper.buildSanctuaryBoss(bossState, sanctuaryBoss);
        SanctuaryPushHelper.pushSanctuaryBoss(actorId, response);
        CommonProtocol.RewardResult rewardResult = RewardHelper.sendRewardList(actorId, sanctuaryBossRankRewardsConfig.getRewardList(), OperationType.SANCTUARY_BOSS_RANK);
        CommonProtocol.RewardResultResponse rewardResultResponse = PbBuilder.buildRewardResultResponse(rewardResult);
        return TResult.sucess(rewardResultResponse);
    }

    @Override
    public TResult<SanctuaryProtocol.SanctuaryBossRankResponse> bossRank(long actorId) {
        TResult<SanctuaryBoss> bossTResult = this.get(actorId);
        if (bossTResult.isFail()) {
            return TResult.valueOf(bossTResult.statusCode);
        }
        WorldSanctuaryRpc proxy = WorldRpcHelper.getProxy(WorldSanctuaryRpc.class);
        BossScoreRankVO bossScoreRankVO =
            BossScoreRankVO.valueOf(ActorHelper.getActorAttributeMap(actorId), 0L, bossTResult.item.getBossScore(), bossTResult.item.getLastBattleTime());
        Collection<BossScoreRankVO> bossScoreList = proxy.getBossScoreList(GameConfig.getServerType(), GameConfig.getServerId(), actorId, bossScoreRankVO);
        SanctuaryProtocol.SanctuaryBossRankResponse response = SanctuaryHelper.buildSanctuaryBossRankResponse(bossScoreList);
        return TResult.sucess(response);
    }

    @Override
    public TResult<SanctuaryProtocol.SanctuaryBossResponse> getSanctuaryBoss(long actorId) {
        TResult<SanctuaryRegionState> regionStateTResult = sanctuaryActorFacade.getSanctuaryRegionState();
        if (regionStateTResult.isFail()) {
            return TResult.valueOf(regionStateTResult.statusCode);
        }
        TResult<SanctuaryBoss> bossTResult = this.get(actorId);
        if (bossTResult.isFail()) {
            return TResult.valueOf(bossTResult.statusCode);
        }
        SanctuaryProtocol.SanctuaryBossResponse response = SanctuaryHelper.buildSanctuaryBoss(regionStateTResult.item.getBossState(), bossTResult.item);
        return TResult.sucess(response);
    }

    @Override
    public void battleRecord(long actorId, BattleProtocol.BattleResultResponse response, long score) {
        TResult<SanctuaryRegionState> regionStateTResult = sanctuaryActorFacade.getSanctuaryRegionState();
        if (regionStateTResult.isFail()) {
            return;
        }
        SanctuaryBoss sanctuaryBoss = sanctuaryBossDao.get(actorId);
        long currentTimeMillis = System.currentTimeMillis();
        sanctuaryBoss.battle(score, currentTimeMillis, regionStateTResult.item.getBossState().getLoopId(), regionStateTResult.item.getBossState().getBossId());
        dbQueue.updateQueue(sanctuaryBoss);
        SanctuaryProtocol.SanctuaryBossResponse bossResponse = SanctuaryHelper.buildSanctuaryBoss(regionStateTResult.item.getBossState(), sanctuaryBoss);
        SanctuaryPushHelper.pushSanctuaryBoss(actorId, bossResponse);
        WorldSanctuaryRpc proxy = WorldRpcHelper.getProxy(WorldSanctuaryRpc.class);
        Map<Byte, Object> actorAttributeMap = ActorHelper.getActorAttributeMap(actorId);
        long scoreRank = proxy.refreshBossScoreRank(GameConfig.getServerType(), GameConfig.getServerId(), actorId, actorAttributeMap, sanctuaryBoss.getBossScore());
        SanctuaryProtocol.SanctuaryBossBattleResponse.Builder builder = SanctuaryProtocol.SanctuaryBossBattleResponse.newBuilder();
        builder.setBattle(response);
        builder.setRank(scoreRank);
        builder.setScore(score);
        SanctuaryPushHelper.pushSanctuaryBossBattleResponse(actorId, builder.build());
    }

    @Override
    public void cleanSanctuaryBoss(long actorId) {
        SanctuaryBoss sanctuaryBoss = sanctuaryBossDao.getExsitSanctuaryBoss(actorId);
        if (sanctuaryBoss != null) {
            dbQueue.deleteQueue(sanctuaryBoss);
        }
    }

    private TResult<SanctuaryBoss> get(long actorId) {
        Result unlock = ActorHelper.unlock(actorId, ActorUnlockType.SANCTUARY);
        if (unlock.isFail()) {
            return TResult.valueOf(unlock);
        }
        SanctuaryBoss sanctuaryBoss = sanctuaryBossDao.get(actorId);
        return TResult.sucess(sanctuaryBoss);
    }

    @Event(name = EventKey.SANCTUARY_BOSS_REFRESH_EVENT)
    public void RefreshBoss(SanctuaryBossRefreshEvent event) {
        for (BossScoreRankVO bossScoreRankVO : event.bossScoreList) {
            long actorId = (long) bossScoreRankVO.getAttributes().get((byte) 5);
            if (!ActorHelper.isExist(actorId)) {
                continue;
            }
            TResult<SanctuaryBoss> result = this.get(actorId);
            if (result.isFail()) {
                continue;
            }
            SanctuaryBoss sanctuaryBoss = result.item;
            sanctuaryBoss.setRank((int) bossScoreRankVO.getRank());
            dbQueue.updateQueue(sanctuaryBoss);
        }
    }

    @EventOnline(trigger = TriggerTime.FIXED_HOUR, triggerValue = 24)
    public void onEvery0Hour(GameEvent e) {
        this.resetSanctuaryBoss(e.getUniqueId(), true);
    }

    @Event(name = EventKey.ACTOR_LOGIN)
    public void onActorLogin(GameEvent e) {
        long actorId = e.getUniqueId();
        this.resetSanctuaryBoss(actorId, false);
    }

    private void resetSanctuaryBoss(long actorId, boolean isPush) {
        TResult<SanctuaryRegionState> regionStateTResult = sanctuaryActorFacade.getSanctuaryRegionState();
        if (regionStateTResult.isFail()) {
            return;
        }
        TResult<SanctuaryBoss> result = this.get(actorId);
        if (result.isFail()) {
            return;
        }
        SanctuaryBoss sanctuaryBoss = result.item;
        if (DateUtils.isToday(sanctuaryBoss.getLastResetTime())) {
            return;
        }
        sanctuaryBoss.dayReset();
        dbQueue.updateQueue(sanctuaryBoss);
        if (isPush) {
            SanctuaryProtocol.SanctuaryBossResponse response = SanctuaryHelper.buildSanctuaryBoss(regionStateTResult.item.getBossState(), sanctuaryBoss);
            SanctuaryPushHelper.pushSanctuaryBoss(actorId, response);
        }
    }
}
