package cn.daxiang.hbtd.gameserver.module.system.parser;

import cn.daxiang.hbtd.gameserver.core.dataconfig.service.GlobalConfigService;
import cn.daxiang.protocol.game.TypeProtocol.SettingType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.PostConstruct;

public abstract class AbstractSettingKeyParser implements SettingKeyParser {
    protected final Logger LOGGER = LoggerFactory.getLogger(getClass());
    @Autowired
    protected GlobalConfigService globalConfigService;
    @Autowired
    protected SettingKeyContext settingKeyContext;

    @PostConstruct
    void init() {
        settingKeyContext.register(getKey(), this);
    }

    protected abstract SettingType getKey();

    public void reset() {
    }

    ;
}
