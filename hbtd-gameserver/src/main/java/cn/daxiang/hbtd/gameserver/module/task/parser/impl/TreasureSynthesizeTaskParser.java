package cn.daxiang.hbtd.gameserver.module.task.parser.impl;

import cn.daxiang.hbtd.gameserver.core.database.table.Task;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.TaskConfig;
import cn.daxiang.hbtd.gameserver.core.event.impl.TreasureSynthesizeEvent;
import cn.daxiang.hbtd.gameserver.module.task.parser.AbstractTaskParser;
import cn.daxiang.hbtd.gameserver.module.task.type.TaskConditionType;
import cn.daxiang.protocol.game.TaskProtocol;
import org.springframework.stereotype.Component;

@Component
public class TreasureSynthesizeTaskParser extends AbstractTaskParser<TreasureSynthesizeEvent> {

    @Override
    public void init(Task task, TaskConfig taskConfig) {
        if (task.getValue() >= taskConfig.getValue()) {
            task.setStatus(TaskProtocol.TaskStatus.TASK_FINISH);
        }
    }

    @Override
    protected TaskConditionType getType() {
        return TaskConditionType.TREASURE_SYNTHESIZE;
    }

    @Override
    protected boolean parseCondition(TreasureSynthesizeEvent event, Task task, TaskConfig taskConfig) {
        Integer quality = Integer.valueOf(taskConfig.getCondition());
        if (quality != 0 && event.quality != quality) {
            return false;
        }
        task.setValue(task.getValue() + event.count);
        if (task.getValue() >= taskConfig.getValue()) {
            task.setStatus(TaskProtocol.TaskStatus.TASK_FINISH);
        }
        return true;
    }
}
