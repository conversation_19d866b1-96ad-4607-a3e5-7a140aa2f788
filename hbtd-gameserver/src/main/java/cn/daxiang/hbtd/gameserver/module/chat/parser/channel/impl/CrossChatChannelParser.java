package cn.daxiang.hbtd.gameserver.module.chat.parser.channel.impl;

import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.rpc.RpcCall;
import cn.daxiang.framework.rpc.world.WorldChatRpc;
import cn.daxiang.hbtd.gameserver.core.GameConfig;
import cn.daxiang.hbtd.gameserver.core.rpc.WorldRpcHelper;
import cn.daxiang.hbtd.gameserver.module.chat.model.ChatInfoReceiver;
import cn.daxiang.hbtd.gameserver.module.chat.parser.channel.AbstractChatChannelParser;
import cn.daxiang.hbtd.gameserver.module.user.helper.ActorHelper;
import cn.daxiang.protocol.game.ChatProtocol.ChatMessage;
import cn.daxiang.protocol.game.CommonProtocol;
import cn.daxiang.protocol.game.TypeProtocol;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;

@Component
public class CrossChatChannelParser extends AbstractChatChannelParser {

    @Override
    public Collection<ChatMessage> getCacheMessage(long actorId) {
        return cacheContext.getCacheMessage(ChatInfoReceiver.valueOf(getType()));
    }

    @Override
    public Result sendMessage(ChatInfoReceiver receiver, ChatMessage message) {
        Collection<Map<Byte, Object>> list = Lists.newArrayList();
        for (CommonProtocol.ActorProfile actorProfile : message.getActorAttributesList()) {
            Map<Byte, Object> actorAttributeMap = ActorHelper.getActorAttributeMap(actorProfile);
            list.add(actorAttributeMap);
        }

        WorldRpcHelper.asynCall(message.getFromActorId(), WorldChatRpc.class, new RpcCall<WorldChatRpc>() {
            @Override
            public void run(WorldChatRpc rpcProxy) {
                rpcProxy.refreshCrossChatMessageVO(GameConfig.getServerType(), message.toByteArray());
            }
        });
        return Result.valueOf();

    }

    @Override
    protected TypeProtocol.ChatChannelType getType() {
        return TypeProtocol.ChatChannelType.CHAT_CHANNEL_TYPE_CROSS;
    }
}