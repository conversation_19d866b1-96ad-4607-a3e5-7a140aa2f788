package cn.daxiang.hbtd.gameserver.module.hegemony.dao;

import cn.daxiang.hbtd.gameserver.core.database.table.HegemonyActor;
import cn.daxiang.hbtd.gameserver.module.hegemony.model.HegemonyRank;

import java.util.Collection;

/**
 * <AUTHOR>
 * @date 2022/10/15
 */
public interface HegemonyActorDao {
    /**
     * 获取群雄争霸个人信息
     *
     * @param actorId
     * @return
     */
    HegemonyActor get(long actorId);

    /**
     * 创建群雄争霸个人信息
     *
     * @param actorId
     * @return
     */
    HegemonyActor createHegemonyActor(long actorId);

    /**
     * 参与排名
     *
     * @param actorId
     * @param score
     * @param lastUpdateTime
     * @return rank
     */
    long achieveRank(long actorId, int score, int dan, long lastUpdateTime);

    /**
     * 获取群雄争霸排名
     *
     * @param actorId
     * @return
     */
    HegemonyRank getRank(long actorId);

    /**
     * 获取群雄争霸排行榜
     *
     * @return
     */
    Collection<HegemonyRank> getRanks();

    /**
     * 获取群雄争霸排行榜
     *
     * @return
     */
    Collection<HegemonyRank> getAllRanks();

    /**
     * 清除群雄争霸排行榜
     */
    void cleanHegemonyRank();

    /**
     * 获取群雄争霸角色信息列表
     *
     * @return
     */
    Collection<HegemonyActor> getHegemonyActorList();
}
