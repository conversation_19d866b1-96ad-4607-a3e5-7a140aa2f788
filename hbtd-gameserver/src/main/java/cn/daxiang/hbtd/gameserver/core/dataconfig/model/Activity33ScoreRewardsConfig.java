package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.dataconfig.annotation.FieldIgnore;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.shared.reward.RewardObject;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * 任务积分对换奖励
 *
 * <AUTHOR>
 * @date 2023/2/16
 */
@DataFile(fileName = "activity_33_score_rewards_config")
public class Activity33ScoreRewardsConfig implements ModelAdapter {
    /**
     * DATA
     */
    private int data;
    /**
     * 唯一ID
     */
    private int id;
    /**
     * 对换所达到的物品
     */
    private String score;
    /**
     * 对换奖励
     */
    private String rewards;

    @FieldIgnore
    private List<RewardObject> scoreList = Lists.newArrayList();

    @FieldIgnore
    private List<RewardObject> rewardList = Lists.newArrayList();

    @Override
    public void initialize() {
        JSONArray scoreArray = JSONArray.parseArray(score);
        for (Object rewardsItem : scoreArray) {
            JSONArray rewardArray = JSONArray.parseArray(rewardsItem.toString());
            RewardObject rewardObject = RewardObject.valueOf(rewardArray);
            scoreList.add(rewardObject);
        }

        JSONArray rewardsArray = JSONArray.parseArray(rewards);
        for (Object rewardsItem : rewardsArray) {
            JSONArray rewardArray = JSONArray.parseArray(rewardsItem.toString());
            RewardObject rewardObject = RewardObject.valueOf(rewardArray);
            rewardList.add(rewardObject);
        }
    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(data, id);
    }

    public int getData() {
        return data;
    }

    public int getId() {
        return id;
    }

    public List<RewardObject> getScoreList() {
        return scoreList;
    }

    public List<RewardObject> getRewardList() {
        return rewardList;
    }
}
