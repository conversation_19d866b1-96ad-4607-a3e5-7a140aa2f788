package cn.daxiang.hbtd.gameserver.core.database.table;

import cn.daxiang.framework.database.MultiEntity;
import cn.daxiang.framework.database.annotation.Column;
import cn.daxiang.framework.database.annotation.DBQueueType;
import cn.daxiang.framework.database.annotation.Table;
import cn.daxiang.framework.identity.IdentiyKey;

/**
 * 活动数据存储表（不删除）
 * <AUTHOR>
 * @date 2024/5/17
 */
@Table(name = "activity_actor_value", type = DBQueueType.IMPORTANT)
public class ActivityActorValue extends MultiEntity<Long> {

    /**
     * 角色Id
     */
    @Column(pk = true, fk = true)
    private long actorId;

    /**
     * 活动类型
     */
    @Column(pk = true)
    private int activityType;

    /**
     * 活动记录
     */
    @Column
    private String record;

    public static ActivityActorValue valueOf(long actorId, int activityType, String record) {
        ActivityActorValue activityActorValue = new ActivityActorValue();
        activityActorValue.actorId = actorId;
        activityActorValue.activityType = activityType;
        activityActorValue.record = record;
        return activityActorValue;
    }

    public long getActorId() {
        return actorId;
    }

    public int getActivityType() {
        return activityType;
    }

    public String getRecord() {
        return record;
    }

    public void setRecord(String record) {
        this.record = record;
    }

    @Override
    public Long findFkId() {
        return actorId;
    }

    @Override
    public void setFkId(Long fk) {
        this.actorId = fk;
    }

    @Override
    public IdentiyKey findPkId() {
        return IdentiyKey.build(actorId, activityType);
    }

    @Override
    public void setPkId(IdentiyKey pk) {
        this.actorId = pk.getIdentifys(0, Long.class);
        this.activityType = pk.getIdentifys(1, Integer.class);
    }

}
