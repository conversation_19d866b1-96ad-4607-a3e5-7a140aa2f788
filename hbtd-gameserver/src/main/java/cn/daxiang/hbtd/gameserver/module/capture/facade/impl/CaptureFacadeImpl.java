package cn.daxiang.hbtd.gameserver.module.capture.facade.impl;

import cn.daxiang.framework.context.ApplicationInitCompleteEvent;
import cn.daxiang.framework.event.GameEvent;
import cn.daxiang.framework.event.TriggerTime;
import cn.daxiang.framework.event.annotation.Event;
import cn.daxiang.framework.event.annotation.EventOnline;
import cn.daxiang.framework.result.CollectionResult;
import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.framework.rpc.event.CaptureBattleEvent;
import cn.daxiang.framework.rpc.event.CaptureBattleReportEvent;
import cn.daxiang.framework.rpc.event.CaptureCityRefreshEvent;
import cn.daxiang.framework.rpc.event.CaptureReceiveOccupyRewardEvent;
import cn.daxiang.framework.rpc.event.CaptureRefreshEvent;
import cn.daxiang.framework.rpc.event.CaptureSettlementOccupyRewardEvent;
import cn.daxiang.framework.rpc.world.WorldCaptureRpc;
import cn.daxiang.framework.utils.DateUtils;
import cn.daxiang.framework.utils.RandomUtils;
import cn.daxiang.framework.utils.TimeConstant;
import cn.daxiang.hbtd.gameserver.core.GameBaseFacade;
import cn.daxiang.hbtd.gameserver.core.GameConfig;
import cn.daxiang.hbtd.gameserver.core.database.table.Beast;
import cn.daxiang.hbtd.gameserver.core.database.table.CaptureActor;
import cn.daxiang.hbtd.gameserver.core.database.table.Hero;
import cn.daxiang.hbtd.gameserver.core.database.table.Lineup;
import cn.daxiang.hbtd.gameserver.core.dataconfig.convert.impl.RewardObjectListConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.CaptureOwnLevelConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.CapturePlunderConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.CaptureConfigService;
import cn.daxiang.hbtd.gameserver.core.dispatch.DispatchHelper;
import cn.daxiang.hbtd.gameserver.core.event.impl.MailAddEvent;
import cn.daxiang.hbtd.gameserver.core.oss.type.OperationType;
import cn.daxiang.hbtd.gameserver.core.rpc.WorldRpcHelper;
import cn.daxiang.hbtd.gameserver.module.PbBuilder;
import cn.daxiang.hbtd.gameserver.module.battle.helper.PositionHelper;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleRoom;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleSprite;
import cn.daxiang.hbtd.gameserver.module.beast.dao.BeastDao;
import cn.daxiang.hbtd.gameserver.module.capture.dao.CaptureActorDao;
import cn.daxiang.hbtd.gameserver.module.capture.facade.CaptureFacade;
import cn.daxiang.hbtd.gameserver.module.capture.helper.CaptureHelper;
import cn.daxiang.hbtd.gameserver.module.capture.helper.CapturePushHelper;
import cn.daxiang.hbtd.gameserver.module.capture.model.CaptureRank;
import cn.daxiang.hbtd.gameserver.module.goods.helper.RewardHelper;
import cn.daxiang.hbtd.gameserver.module.hero.facade.HeroFacade;
import cn.daxiang.hbtd.gameserver.module.lineup.facade.LineupFacade;
import cn.daxiang.hbtd.gameserver.module.lineup.helper.LineupAttributeHelper;
import cn.daxiang.hbtd.gameserver.module.lineup.model.entity.BeastEntity;
import cn.daxiang.hbtd.gameserver.module.lineup.model.entity.LineupEntity;
import cn.daxiang.hbtd.gameserver.module.mail.helper.MailHelper;
import cn.daxiang.hbtd.gameserver.module.mail.type.MailTemplateType;
import cn.daxiang.hbtd.gameserver.module.prerogative.helper.PrerogativeHelper;
import cn.daxiang.hbtd.gameserver.module.prerogative.type.PrerogativeType;
import cn.daxiang.hbtd.gameserver.module.user.helper.ActorHelper;
import cn.daxiang.hbtd.gameserver.module.user.type.ActorUnlockType;
import cn.daxiang.protocol.game.BattleProtocol;
import cn.daxiang.protocol.game.CaptureProtocol;
import cn.daxiang.protocol.game.CommonProtocol;
import cn.daxiang.protocol.game.TypeProtocol;
import cn.daxiang.protocol.game.UserProtocol;
import cn.daxiang.shared.GlobalConfigKey;
import cn.daxiang.shared.event.EventKey;
import cn.daxiang.shared.module.battle.BattleParameterKey;
import cn.daxiang.shared.module.capture.Capture;
import cn.daxiang.shared.module.capture.CaptureBattleReport;
import cn.daxiang.shared.module.capture.CaptureCity;
import cn.daxiang.shared.module.lineup.BattleLineupEntity;
import cn.daxiang.shared.module.lineup.LineupAttribute;
import cn.daxiang.shared.module.lineup.SpriteAttributeType;
import cn.daxiang.shared.reward.RewardObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2022/7/8
 */
@Component
public class CaptureFacadeImpl extends GameBaseFacade implements CaptureFacade, ApplicationListener<ApplicationInitCompleteEvent> {
    private static WorldCaptureRpc WORLD_CAPTURE_RPC;
    @Autowired
    private CaptureActorDao captureActorDao;
    @Autowired
    private LineupFacade lineupFacade;
    @Autowired
    private HeroFacade heroFacade;
    @Autowired
    private BeastDao beastDao;

    @Override
    public void onApplicationEvent(ApplicationInitCompleteEvent event) {
        WORLD_CAPTURE_RPC = WorldRpcHelper.getProxy(WorldCaptureRpc.class);
    }

    @Override
    public TResult<Capture> getCapture(long actorId) {
        Result result = ActorHelper.unlock(actorId, ActorUnlockType.CAPTURE);
        if (result.isFail()) {
            return TResult.valueOf(result);
        }
        Capture capture = WORLD_CAPTURE_RPC.getCapture();
        return TResult.sucess(capture);
    }

    @Override
    public CollectionResult<CaptureCity> getCaptureCity(long actorId, int cityId) {
        Result result = ActorHelper.unlock(actorId, ActorUnlockType.CAPTURE);
        if (result.isFail()) {
            return CollectionResult.valueOf(result.statusCode);
        }
        TResult<CaptureActor> captureActorTResult = this.getCaptureActor(actorId);
        if (captureActorTResult.isFail()) {
            return CollectionResult.valueOf(captureActorTResult.statusCode);
        }
        Collection<CaptureCity> captureCityList = WORLD_CAPTURE_RPC.getCaptureCity(GameConfig.getServerType(), GameConfig.getServerId(), cityId);
        CaptureActor captureActor = captureActorTResult.item;
        if (captureActor.getCityId() != 0) {
            Optional<CaptureCity> captureCityOptional = captureCityList.stream().filter(e -> e.getId() == captureActor.getCityId()).findFirst();
            if (captureCityOptional.isPresent()) {
                if (captureCityOptional.get().getActorId() != actorId) {
                    captureActor.setCityId(0);
                    dbQueue.updateQueue(captureActor);
                    CapturePushHelper.pushCaptureActor(captureActor);
                }
            }
        }
        return CollectionResult.collection(captureCityList);
    }

    @Override
    public TResult<CaptureActor> getCaptureActor(long actorId) {
        Result result = ActorHelper.unlock(actorId, ActorUnlockType.CAPTURE);
        if (result.isFail()) {
            return TResult.valueOf(result);
        }
        CaptureActor captureActor = captureActorDao.get(actorId);
        Optional<CaptureOwnLevelConfig> captureOwnLevelConfigOptional = CaptureConfigService.getCaptureOwnLevelConfig(captureActor.getLevel());
        if (captureOwnLevelConfigOptional.isPresent()) {
            // 结算最大时间
            long settlementMaxTime = Math.min(System.currentTimeMillis(),
                DateUtils.getDelayDate(captureActor.getLastReceiveTime(), captureOwnLevelConfigOptional.get().getLimitTime(), TimeUnit.MINUTES).getTime());
            //结算奖励
            int settlementMinute = (int) ((settlementMaxTime - captureActor.getLastSettlementTime()) / TimeConstant.ONE_MINUTE_MILLISECOND);
            if (settlementMinute > 0) {
                settlementMinute = Math.min(settlementMinute, captureOwnLevelConfigOptional.get().getLimitTime());
                Collection<RewardObject> rewardList = RewardHelper.multipleRewardList(captureOwnLevelConfigOptional.get().getFixedRewardList(), settlementMinute);
                for (long i = 0; i < settlementMinute; i++) {
                    for (Map.Entry<RewardObject, Integer> entry : captureOwnLevelConfigOptional.get().getRandomRewardMap().entrySet()) {
                        if (RandomUtils.is10000Hit(entry.getValue())) {
                            rewardList.add(entry.getKey());
                        }
                    }
                }
                captureActor.settlement(settlementMinute, rewardList);
                dbQueue.updateQueue(captureActor);
            }
        }
        return TResult.sucess(captureActor);
    }

    @Override
    public Result upgrade(long actorId) {
        TResult<CaptureActor> captureActorTResult = this.getCaptureActor(actorId);
        if (captureActorTResult.isFail()) {
            return Result.valueOf(captureActorTResult.statusCode);
        }
        CaptureActor captureActor = captureActorTResult.item;
        if (CaptureConfigService.isMaxLevel(captureActor.getLevel())) {
            return Result.valueOf(INVALID_PARAM);
        }
        Optional<CaptureOwnLevelConfig> captureOwnLevelConfigOptional = CaptureConfigService.getCaptureOwnLevelConfig(captureActor.getLevel());
        if (!captureOwnLevelConfigOptional.isPresent()) {
            return Result.valueOf(CONFIG_NOT_FOUND);
        }
        CaptureOwnLevelConfig ownLevelConfig = captureOwnLevelConfigOptional.get();
        Result result = RewardHelper.decrease(actorId, ownLevelConfig.getCostList(), OperationType.CAPTURE_ACTOR_UPGRADE);
        if (result.isFail()) {
            return result;
        }
        captureActor.setLevel(captureActor.getLevel() + 1);
        captureActor.setLastAchieveTime(System.currentTimeMillis());
        dbQueue.updateQueue(captureActor);
        captureActorDao.achieveCaptureRank(actorId, captureActor.getLevel(), captureActor.getLastAchieveTime());
        CapturePushHelper.pushCaptureActor(captureActor);
        return Result.valueOf();
    }

    @Override
    public TResult<CommonProtocol.RewardResult> receiveOwn(long actorId) {
        TResult<CaptureActor> captureActorTResult = this.getCaptureActor(actorId);
        if (captureActorTResult.isFail()) {
            return TResult.valueOf(captureActorTResult.statusCode);
        }
        CaptureActor captureActor = captureActorTResult.item;
        if (captureActor.getSettledRewards().isEmpty()) {
            return TResult.valueOf(CAPTURE_CITY_RECEIVE_REWARD_ERROR);
        }
        Collection<RewardObject> rewardList = this.getPrerogative(actorId, PrerogativeType.CAPTURE_OWN_HANG_UP, captureActor.getSettledRewards());
        CommonProtocol.RewardResult rewardResult = RewardHelper.sendRewardList(actorId, rewardList, OperationType.CAPTURE_ACTOR_RECEIVE);
        captureActor.receive();
        dbQueue.updateQueue(captureActor);
        CapturePushHelper.pushCaptureActor(captureActor);
        return TResult.sucess(rewardResult);
    }

    @Override
    public Result battle(long actorId, int captureCityId, CaptureProtocol.CaptureBattleType type, Map<Integer, Integer> lineup, Map<Integer, Integer> beastMap) {
        if (captureCityId <= 0 || type == CaptureProtocol.CaptureBattleType.CAPTURE_BATTLE_NONE) {
            return Result.valueOf(INVALID_PARAM);
        }
        if (lineup.isEmpty() || lineup.size() > globalConfigService.findGlobalConfig(GlobalConfigKey.CAPTURE_BATTLE_HERO_COUNT).findInt()) {
            return Result.valueOf(INVALID_PARAM);
        }
        TResult<CaptureActor> captureActorTResult = this.getCaptureActor(actorId);
        if (captureActorTResult.isFail()) {
            return Result.valueOf(captureActorTResult.statusCode);
        }
        TResult<Lineup> lineUpResult = lineupFacade.getLineup(actorId);
        if (lineUpResult.isFail()) {
            return Result.valueOf(lineUpResult.statusCode);
        }
        //{k:roleId,value:positionId}
        Map<Integer, Integer> battleLineupMap = Maps.newHashMapWithExpectedSize(lineup.size() + beastMap.size());
        Map<Integer, Integer> battleLineupHeroSkinMap = Maps.newHashMapWithExpectedSize(lineup.size());
        //{k:positionId,value:orderId}
        for (Map.Entry<Integer, Integer> entry : lineup.entrySet()) {
            if (entry.getKey() <= 0 || entry.getKey() > PositionHelper.getPositionCount()) {
                return Result.valueOf(INVALID_PARAM);
            }
            LineupEntity lineupEntity = lineUpResult.item.getLineupEntity(entry.getValue());
            if (lineupEntity == null) {
                return Result.valueOf(INVALID_PARAM);
            }
            TResult<Hero> heroTResult = heroFacade.getHero(actorId, lineupEntity.getHeroId());
            if (heroTResult.isFail()) {
                return Result.valueOf(heroTResult.statusCode);
            }
            if (battleLineupMap.containsKey(heroTResult.item.getConfigId())) {
                return Result.valueOf(INVALID_PARAM);
            }
            battleLineupMap.put(heroTResult.item.getConfigId(), entry.getKey());
            battleLineupHeroSkinMap.put(heroTResult.item.getConfigId(), heroTResult.item.getSkinId());
        }

        //神兽阵容 crw 设计如此 功能设计者:冯程程
        //{k:positionId,value:beastId}
        for (Map.Entry<Integer, Integer> entry : beastMap.entrySet()) {
            int positionId = entry.getKey();
            int beastId = entry.getValue();
            if (positionId <= 0 || positionId > PositionHelper.getPositionCount()) {
                return Result.valueOf(INVALID_PARAM);
            }
            BeastEntity entity = lineUpResult.item.getBeastEntityByBeastId(beastId);
            BeastEntity beastEntity = entity == null ? lineUpResult.item.getResonanceBeastEntityByBeastId(beastId) : entity;
            if (beastEntity == null) {
                return Result.valueOf(INVALID_PARAM);
            }
            Beast beast = beastDao.getBeast(actorId, beastId);
            if (beast == null) {
                return Result.valueOf(BEAST_NOT_FOUND);
            }
            if (battleLineupMap.containsKey(beastId)) {
                return Result.valueOf(INVALID_PARAM);
            }
            battleLineupMap.put(beastId, -positionId);
        }

        CaptureActor captureActor = captureActorTResult.item;
        int battleTimesLimit = globalConfigService.findGlobalConfig(GlobalConfigKey.CAPTURE_BATTLE_TIMES).findInt();
        List<RewardObject> costList = globalConfigService.findGlobalObject(GlobalConfigKey.CAPTURE_EXTRA_BATTLE_TIMES_COST, RewardObjectListConfig.class).getVs();
        if (captureActor.getBattleTimes() >= battleTimesLimit) {
            Result result = RewardHelper.hasEnough(actorId, costList);
            if (result.isFail()) {
                return result;
            }
        }
        Result result = WORLD_CAPTURE_RPC.battle(GameConfig.getServerType(), GameConfig.getServerId(), actorId, captureCityId, captureActor.getCityId(), type, battleLineupMap,
            battleLineupHeroSkinMap);
        if (result.isOk()) {
            if (captureActor.getBattleTimes() >= battleTimesLimit) {
                RewardHelper.decrease(actorId, costList, OperationType.CAPTURE_BATTLE);
            } else {
                captureActor.setBattleTimes(captureActor.getBattleTimes() + 1);
                dbQueue.updateQueue(captureActor);
            }
        }
        return result;
    }

    @Override
    public void challengeRecord(long actorId, BattleRoom battleRoom) {
        TResult<CaptureActor> captureActorTResult = this.getCaptureActor(actorId);
        if (captureActorTResult.isFail()) {
            return;
        }
        CaptureActor captureActor = captureActorTResult.item;
        int captureCityId = (int) battleRoom.getParameterMap().get(BattleParameterKey.CAPTURE_CITY_ID);
        CaptureProtocol.CaptureBattleType captureBattleType = (CaptureProtocol.CaptureBattleType) battleRoom.getParameterMap().get(BattleParameterKey.CAPTURE_BATTLE_TYPE);
        boolean isReward = false;
        if (battleRoom.isWin()) {
            switch (captureBattleType) {
                case CAPTURE_BATTLE_CAPTURE:
                    isReward = captureActor.getCaptureRewardTimes() < globalConfigService.findGlobalConfig(GlobalConfigKey.CAPTURE_CAPTURE_REWARD_TIMES).findInt();
                    break;
                case CAPTURE_BATTLE_PLUNDER:
                    isReward = captureActor.getPlunderRewardTimes() < globalConfigService.findGlobalConfig(GlobalConfigKey.CAPTURE_PLUNDER_REWARD_TIMES).findInt();
                    break;
                default:
                    break;
            }
        }
        Map<Byte, Object> captureAttributes = ActorHelper.getActorAttributeMap(actorId);
        Map<Byte, Object> garrisonAttributes = (Map<Byte, Object>) battleRoom.getParameterMap().get(BattleParameterKey.CROSS_BATTLE_ATTRIBUTE);
        Map<Integer, LineupAttribute> captureLineupAttributeMap = (Map<Integer, LineupAttribute>) battleRoom.getParameterMap().get(BattleParameterKey.CAPTURE_LINEUP);
        BattleLineupEntity battleLineupEntity = (BattleLineupEntity) battleRoom.getParameterMap().get(BattleParameterKey.CROSS_BATTLE_LINEUP);
        Map<Integer, LineupAttribute> garrisonLineupAttributeMap = battleLineupEntity.getLineupAttributeMap();
        Map<Integer, Long> garrisonLineupMap = Maps.newHashMap();
        for (BattleSprite battleSprite : battleRoom.getBattleSpriteList(BattleProtocol.BattleCamp.RIGHT_CAMP)) {
            LineupAttribute lineupAttribute = garrisonLineupAttributeMap.get(battleSprite.getPositionId());
            long hp = Math.min(battleSprite.getSpriteBattle().getHP(), lineupAttribute.getAttributeMap().get(SpriteAttributeType.HP));
            hp = Math.max(0, hp);
            lineupAttribute.getAttributeMap().put(SpriteAttributeType.HP, hp);
            garrisonLineupMap.put(battleSprite.getPositionId(), hp);
        }
        captureAttributes.put((byte) TypeProtocol.ActorFieldType.ACTOR_POWER_VALUE, captureLineupAttributeMap.values().stream().mapToLong(LineupAttribute::getPower).sum());
        Collection<CommonProtocol.LineupExtraHP> lineupExtraHPList = PbBuilder.buildLineupExtraHP(garrisonLineupAttributeMap.values());
        CaptureBattleReport captureBattleReport =
            CaptureBattleReport.valueOf(captureCityId, captureBattleType, actorId, (int) captureAttributes.getOrDefault((byte) TypeProtocol.ActorFieldType.SERVER_ID_VALUE, 0),
                captureAttributes.getOrDefault((byte) TypeProtocol.ActorFieldType.ACTOR_NAME_VALUE, Strings.EMPTY).toString(),
                Long.parseLong(garrisonAttributes.getOrDefault((byte) TypeProtocol.ActorFieldType.ACTOR_ID_VALUE, 0L).toString()),
                (int) garrisonAttributes.getOrDefault((byte) TypeProtocol.ActorFieldType.SERVER_ID_VALUE, 0),
                garrisonAttributes.getOrDefault((byte) TypeProtocol.ActorFieldType.ACTOR_NAME_VALUE, Strings.EMPTY).toString(), battleRoom.isWin(), System.currentTimeMillis());
        Map<Integer, Collection<Integer>> systemSkillMaps = LineupAttributeHelper.getSystemSkillMaps(actorId);
        CollectionResult<RewardObject> battleResult =
            WORLD_CAPTURE_RPC.battleResult(GameConfig.getServerType(), GameConfig.getServerId(), actorId, captureAttributes, captureCityId, captureActor.getCityId(),
                captureLineupAttributeMap, garrisonLineupMap, isReward, captureBattleReport, captureActor.getMultipleExpireTime(), systemSkillMaps);
        Collection<RewardObject> rewardList = Collections.emptyList();
        if (battleResult.isOk()) {
            rewardList = battleResult.item;
            if (battleRoom.isWin()) {
                switch (captureBattleType) {
                    case CAPTURE_BATTLE_CAPTURE:
                        captureActor.setCityId(captureCityId);
                        if (isReward) {
                            captureActor.setCaptureRewardTimes(captureActor.getCaptureRewardTimes() + 1);
                        }
                        break;
                    case CAPTURE_BATTLE_PLUNDER:
                        if (isReward) {
                            captureActor.setPlunderRewardTimes(captureActor.getPlunderRewardTimes() + 1);
                        }
                        break;
                    default:
                        break;
                }
            }
        }
        if (rewardList.isEmpty()) {
            rewardList = globalConfigService.findGlobalObject(GlobalConfigKey.CAPTURE_BATTLE_FINAL_GUARANTEE_REWARD, RewardObjectListConfig.class).getVs();
        }
        switch (captureBattleType) {
            case CAPTURE_BATTLE_CAPTURE:
                rewardList = this.getPrerogative(actorId, PrerogativeType.CAPTURE_BATTLE_CAPTURE, rewardList);
                break;
            case CAPTURE_BATTLE_PLUNDER:
                rewardList = this.getPrerogative(actorId, PrerogativeType.CAPTURE_BATTLE_PLUNDER, rewardList);
                break;
            default:
                break;
        }
        CommonProtocol.RewardResult rewardResult = RewardHelper.sendRewardList(actorId, rewardList, OperationType.CAPTURE_BATTLE);
        Collection<BattleProtocol.BattleMember> members = Lists.newArrayList();
        CommonProtocol.ActorProfile leftActorProfile = ActorHelper.getActorProfile(captureAttributes);
        BattleProtocol.BattleMember leftBattleMember = battleRoom.getBattleMember(BattleProtocol.BattleCamp.LEFT_CAMP, leftActorProfile);
        members.add(leftBattleMember);
        CommonProtocol.ActorProfile rightActorProfile = ActorHelper.getActorProfile(garrisonAttributes);
        BattleProtocol.BattleMember rightBattleMember = battleRoom.getBattleMember(BattleProtocol.BattleCamp.RIGHT_CAMP, rightActorProfile);
        members.add(rightBattleMember);
        BattleProtocol.BattleResultResponse battleResultResponse =
            PbBuilder.buildBattleResultResponse(battleRoom.getBattleType(), members, battleRoom.getRightHistoryBattleSpriteVO(), battleRoom.getBattleReportMap(), rewardResult,
                battleRoom.getBattleStats());
        CaptureProtocol.CaptureBattleResponse captureBattleResponse =
            CaptureProtocol.CaptureBattleResponse.newBuilder().setBattle(battleResultResponse).setType(captureBattleType).addAllLineupAttributes(lineupExtraHPList).build();
        CapturePushHelper.pushBattleResult(actorId, captureBattleResponse);
        captureBattleReport.setRewards(rewardList);
        captureActor.addReport(captureBattleReport, globalConfigService.findGlobalConfig(GlobalConfigKey.CAPTURE_BATTLE_REPORT_LIMIT).findInt());
        dbQueue.updateQueue(captureActor);
        CapturePushHelper.pushCaptureActor(captureActor);
        DispatchHelper.postEvent(new CaptureBattleEvent(actorId, captureBattleType, battleRoom.isWin()));
    }

    @Override
    public TResult<CommonProtocol.RewardResult> receiveCapture(long actorId) {
        TResult<CaptureActor> captureActorTResult = this.getCaptureActor(actorId);
        if (captureActorTResult.isFail()) {
            return TResult.valueOf(captureActorTResult.statusCode);
        }
        CaptureActor captureActor = captureActorTResult.item;
        if (captureActor.getCityId() == 0) {
            return TResult.valueOf(INVALID_PARAM);
        }
        TResult<Map.Entry<Long, Collection<RewardObject>>> result =
            WORLD_CAPTURE_RPC.receiveReward(GameConfig.getServerType(), GameConfig.getServerId(), actorId, captureActor.getCityId(), captureActor.getMultipleExpireTime());
        if (result.isFail()) {
            return TResult.valueOf(result.statusCode);
        }
        Collection<RewardObject> rewardList = this.getPrerogative(actorId, PrerogativeType.CAPTURE_FIELD_HANG_UP, result.item.getValue());
        CommonProtocol.RewardResult rewardResult = RewardHelper.sendRewardList(actorId, rewardList, OperationType.CAPTURE_RECEIVE);
        DispatchHelper.postEvent(new CaptureReceiveOccupyRewardEvent(actorId, result.item.getKey()));
        return TResult.sucess(rewardResult);
    }

    @Override
    public Result multiple(long actorId) {
        TResult<CaptureActor> captureActorTResult = this.getCaptureActor(actorId);
        if (captureActorTResult.isFail()) {
            return Result.valueOf(captureActorTResult.statusCode);
        }
        CaptureActor captureActor = captureActorTResult.item;
        if (captureActor.getCityId() == 0) {
            return Result.valueOf(INVALID_PARAM);
        }
        List<RewardObject> costList = globalConfigService.findGlobalObject(GlobalConfigKey.CAPTURE_MULTIPLE_COST, RewardObjectListConfig.class).getVs();
        Result result = RewardHelper.decrease(actorId, costList, OperationType.CAPTURE_MULTIPLE);
        if (result.isFail()) {
            return result;
        }
        int minutes = globalConfigService.findGlobalConfig(GlobalConfigKey.CAPTURE_MULTIPLE_TIME).findInt();
        long multipleExpireTime = DateUtils.getDelayDate(Math.max(System.currentTimeMillis(), captureActor.getMultipleExpireTime()), minutes, TimeUnit.MINUTES).getTime();
        captureActor.setMultipleExpireTime(multipleExpireTime);
        dbQueue.updateQueue(captureActor);
        CapturePushHelper.pushCaptureActor(captureActor);
        TResult<CaptureCity> refreshResult = WORLD_CAPTURE_RPC.refreshMultipleExpireTime(GameConfig.getServerType(), GameConfig.getServerId(), actorId, captureActor.getCityId(),
            captureActor.getMultipleExpireTime());
        if (refreshResult.isOk()) {
            CapturePushHelper.pushCaptureCity(Lists.newArrayList(refreshResult.item));
        }
        return result;
    }

    @Override
    public TResult<CaptureProtocol.CaptureRankResponse> getCaptureRankResponse(long actorId, int page) {
        Collection<CaptureRank> ranks = captureActorDao.getCaptureRanks(page);
        CaptureRank bestRank = captureActorDao.getCaptureRank(actorId);
        if (bestRank == null) {
            CaptureActor captureActor = captureActorDao.get(actorId);
            bestRank = CaptureRank.valueOf(actorId, captureActor.getLevel(), captureActor.getLastAchieveTime());
        }
        ranks.add(bestRank);
        Collection<CaptureProtocol.CaptureRankVO> rankList = Lists.newArrayList();
        for (CaptureRank captureRank : ranks) {
            CaptureProtocol.CaptureRankVO rank = CaptureHelper.buildCaptureRank(captureRank.getKey(), captureRank.getRank(), captureRank.getLevel());
            rankList.add(rank);
        }
        CaptureProtocol.CaptureRankResponse response =
            CaptureProtocol.CaptureRankResponse.newBuilder().addAllRanks(rankList).setPage(page).setPages(captureActorDao.getCaptureRankPages())
                .setPraiseCount(ActorHelper.getPraiseCount(UserProtocol.RankType.CAPTURE_RANK)).build();
        return TResult.sucess(response);
    }

    @Override
    public Result giveUp(long actorId) {
        TResult<CaptureActor> captureActorTResult = this.getCaptureActor(actorId);
        if (captureActorTResult.isFail()) {
            return Result.valueOf(captureActorTResult.statusCode);
        }
        CaptureActor captureActor = captureActorTResult.item;
        if (captureActor.getCityId() == 0) {
            return Result.valueOf(CAPTURE_CITY_NOT_OCCUPY);
        }
        int times = globalConfigService.findGlobalConfig(GlobalConfigKey.CAPTURE_GIVE_UP).findInt();
        if (captureActor.getGiveUpTimes() >= times) {
            return Result.valueOf(CAPTURE_GIVE_UP_TIMES_NOT_ENOUGH);
        }
        Result result = WORLD_CAPTURE_RPC.giveUp(GameConfig.getServerType(), GameConfig.getServerId(), actorId, captureActor.getCityId(), captureActor.getMultipleExpireTime());
        if (result.isFail()) {
            return result;
        }
        captureActor.setGiveUpTimes(captureActor.getGiveUpTimes() + 1);
        dbQueue.updateQueue(captureActor);
        return Result.valueOf();
    }

    @Override
    public Result read(long actorId) {
        TResult<CaptureActor> captureActorTResult = this.getCaptureActor(actorId);
        if (captureActorTResult.isFail()) {
            return Result.valueOf(captureActorTResult.statusCode);
        }
        CaptureActor captureActor = captureActorTResult.item;
        captureActor.setOut(false);
        dbQueue.updateQueue(captureActor);
        CapturePushHelper.pushCaptureActor(captureActor);
        return Result.valueOf();
    }

    @Override
    public TResult<CommonProtocol.RewardResultResponse> wipeOut(long actorId, int times) {
        if (times <= 0 || times > GameConfig.getClientCountLimit()) {
            return TResult.valueOf(INVALID_PARAM);
        }
        TResult<CaptureActor> captureActorTResult = this.getCaptureActor(actorId);
        if (captureActorTResult.isFail()) {
            return TResult.valueOf(captureActorTResult.statusCode);
        }
        CaptureActor captureActor = captureActorTResult.item;
        //以下计算消耗道具的数量
        int battleTimesLimit = globalConfigService.findGlobalConfig(GlobalConfigKey.CAPTURE_BATTLE_TIMES).findInt();
        List<RewardObject> configCostList = globalConfigService.findGlobalObject(GlobalConfigKey.CAPTURE_EXTRA_BATTLE_TIMES_COST, RewardObjectListConfig.class).getVs();
        List<RewardObject> wipeOutCostList = globalConfigService.findGlobalObject(GlobalConfigKey.CAPTURE_SCOURING_GOODS_ID, RewardObjectListConfig.class).getVs();
        Collection<RewardObject> costList = Lists.newLinkedList();
        for (int i = 1; i <= times; i++) {
            if (captureActor.getBattleTimes() + i > battleTimesLimit) {
                costList.addAll(configCostList);
            }
            costList.addAll(wipeOutCostList);
        }
        costList = RewardHelper.groupByTypeAndId(costList);
        Result result = RewardHelper.decrease(actorId, costList, OperationType.CAPTURE_WIPE_OUT_REWARD);
        if (result.isFail()) {
            return TResult.valueOf(result);
        }
        int actorLevel = ActorHelper.getActorLevel(actorId);
        Optional<CapturePlunderConfig> capturePlunderConfigOptional = CaptureConfigService.getCapturePlunderConfig(actorLevel);
        if (!capturePlunderConfigOptional.isPresent()) {
            LOGGER.error("CapturePlunderConfig is not found!,actorLevel:{}", actorLevel);
            return TResult.valueOf(CONFIG_NOT_FOUND);
        }
        CapturePlunderConfig capturePlunderConfig = capturePlunderConfigOptional.get();
        Collection<RewardObject> rewardList = Lists.newLinkedList();
        //是否是正常奖励
        int rewardLimit = globalConfigService.findGlobalConfig(GlobalConfigKey.CAPTURE_PLUNDER_REWARD_TIMES).findInt();
        for (int i = 1; i <= times; i++) {
            boolean isNormalReward = captureActor.getPlunderRewardTimes() + i <= rewardLimit;
            if (isNormalReward) {
                rewardList.addAll(capturePlunderConfig.getNormalRewardList());
            } else {
                rewardList.addAll(capturePlunderConfig.getLowRewardList());
            }
        }
        captureActor.wiptOut(times);
        dbQueue.updateQueue(captureActor);
        rewardList = RewardHelper.groupByTypeAndId(rewardList);
        CommonProtocol.RewardResult rewardResult = RewardHelper.sendRewardList(actorId, rewardList, OperationType.CAPTURE_WIPE_OUT_REWARD);
        CapturePushHelper.pushCaptureActor(captureActor);
        DispatchHelper.postEvent(new CaptureBattleEvent(actorId, CaptureProtocol.CaptureBattleType.CAPTURE_BATTLE_PLUNDER, true, times));
        CommonProtocol.RewardResultResponse response = PbBuilder.buildRewardResultResponse(rewardResult);
        return TResult.sucess(response);
    }

    @Override
    public TResult<CommonProtocol.RewardResultResponse> receiveDailyReward(long actorId) {
        TResult<CaptureActor> captureActorTResult = this.getCaptureActor(actorId);
        if (captureActorTResult.isFail()) {
            return TResult.valueOf(captureActorTResult.statusCode);
        }
        CaptureActor captureActor = captureActorTResult.item;
        if (captureActor.getReceiveDailyReward()) {
            return TResult.valueOf(CAPTURE_DAILY_REWARD_HAVE_RECEIVED);
        }
        Optional<CaptureOwnLevelConfig> captureOwnLevelConfigOptional = CaptureConfigService.getCaptureOwnLevelConfig(captureActor.getLevel());
        if (!captureOwnLevelConfigOptional.isPresent()) {
            LOGGER.error("CaptureOwnLevelConfig is not found!,level:{}", captureActor.getLevel());
            return TResult.valueOf(CONFIG_NOT_FOUND);
        }
        CaptureOwnLevelConfig captureOwnLevelConfig = captureOwnLevelConfigOptional.get();
        captureActor.setReceiveDailyReward(true);
        dbQueue.updateQueue(captureActor);
        CommonProtocol.RewardResult rewardResult = RewardHelper.sendRewardList(actorId, captureOwnLevelConfig.getDailyRewardList(), OperationType.CAPTURE_DAILY_REWARD);
        CapturePushHelper.pushCaptureActor(captureActor);
        CommonProtocol.RewardResultResponse response = PbBuilder.buildRewardResultResponse(rewardResult);
        return TResult.sucess(response);
    }

    @EventOnline(trigger = TriggerTime.FIXED_HOUR, triggerValue = 24)
    public void onEverySecond(GameEvent e) {
        this.resetCaptureActor(e.getUniqueId(), true);
    }

    @Event(name = EventKey.ACTOR_LOGIN)
    public void onActorLogin(GameEvent e) {
        this.resetCaptureActor(e.getUniqueId(), false);
    }

    private void resetCaptureActor(long actorId, boolean isPush) {
        TResult<CaptureActor> captureActorTResult = this.getCaptureActor(actorId);
        if (captureActorTResult.isFail()) {
            return;
        }
        CaptureActor captureActor = captureActorTResult.item;
        if (DateUtils.isToday(captureActor.getLastResetTime())) {
            return;
        }
        captureActor.reset();
        dbQueue.updateQueue(captureActor);
        if (isPush) {
            CapturePushHelper.pushCaptureActor(captureActor);
        }
    }

    @Event(name = EventKey.CAPTURE_SETTLEMENT_OCCUPY_REWARD_EVENT)
    public void onCaptureSettlementOccupyRewardEvent(CaptureSettlementOccupyRewardEvent event) {
        if (!CollectionUtils.isEmpty(event.rewardList)) {
            Map<String, String> params = Maps.newHashMap();
            params.put("name", ActorHelper.getActorName(event.actorId));
            params.put("cityName", MailHelper.getTranslateFormat("CAPTURE_CITY_NAME_" + event.cityId));
            params.put("minute", String.valueOf(event.minute));
            params.put("source", MailHelper.getTranslateFormat("CAPTURE_OCCUPY_REWARD_SOURCE_" + event.type.getNumber()));
            Collection<RewardObject> rewardList = this.getPrerogative(event.actorId, PrerogativeType.CAPTURE_FIELD_HANG_UP, event.rewardList);
            DispatchHelper.postEvent(new MailAddEvent(event.actorId, MailTemplateType.CAPTURE_OCCUPY_REWARD, params, rewardList));
        }
        DispatchHelper.postEvent(new CaptureReceiveOccupyRewardEvent(event.actorId, event.minute));
        if (event.type == CaptureProtocol.CaptureOccupyRewardSourceType.CAPTURE_OCCUPY_REWARD_SOURCE_CAPTURED
            || event.type == CaptureProtocol.CaptureOccupyRewardSourceType.CAPTURE_OCCUPY_REWARD_SOURCE_SYSTEM) {
            TResult<CaptureActor> captureActorTResult = this.getCaptureActor(event.actorId);
            if (captureActorTResult.isFail()) {
                return;
            }
            CaptureActor captureActor = captureActorTResult.item;
            captureActor.setCityId(0);
            dbQueue.updateQueue(captureActor);
            CapturePushHelper.pushCaptureActor(captureActor);
        }
    }

    /**
     * 获取特权
     *
     * @param actorId
     * @param prerogativeType
     * @param rewardList
     * @return
     */
    private Collection<RewardObject> getPrerogative(long actorId, PrerogativeType prerogativeType, Collection<RewardObject> rewardList) {
        int percent = PrerogativeHelper.getIntPrerogative(actorId, prerogativeType);
        return RewardHelper.extraPercentRewardList(rewardList, percent);
    }

    @Event(name = EventKey.CAPTURE_BATTLE_REPORT_EVENT)
    public void onCaptureBattleReportEvent(CaptureBattleReportEvent event) {
        TResult<CaptureActor> captureActorTResult = this.getCaptureActor(event.actorId);
        if (captureActorTResult.isFail()) {
            return;
        }
        CaptureActor captureActor = captureActorTResult.item;
        event.battleReport.setRewards(event.rewardList == null ? Collections.emptyList() : event.rewardList);
        if (event.battleReport.isResult() && event.battleReport.getType() == CaptureProtocol.CaptureBattleType.CAPTURE_BATTLE_CAPTURE) {
            captureActor.setOut(true);
        }
        captureActor.addReport(event.battleReport, globalConfigService.findGlobalConfig(GlobalConfigKey.CAPTURE_BATTLE_REPORT_LIMIT).findInt());
        dbQueue.updateQueue(captureActor);
        CapturePushHelper.pushCaptureActor(captureActor);
    }

    @Event(name = EventKey.CAPTURE_REFRESH_EVENT)
    public void onCaptureRefreshEvent(CaptureRefreshEvent event) {
        CapturePushHelper.pushCapture(event.capture);
    }

    @Event(name = EventKey.CAPTURE_CITY_REFRESH_EVENT)
    public void onCaptureCityRefreshEvent(CaptureCityRefreshEvent event) {
        CapturePushHelper.pushCaptureCity(event.captureCityList);
    }
}
