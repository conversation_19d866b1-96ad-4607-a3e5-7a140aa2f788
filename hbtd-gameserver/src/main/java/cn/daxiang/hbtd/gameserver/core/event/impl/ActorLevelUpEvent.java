package cn.daxiang.hbtd.gameserver.core.event.impl;

import cn.daxiang.framework.event.ActorEvent;
import cn.daxiang.shared.event.EventKey;

/**
 * 角色升级事件
 *
 * <AUTHOR>
 */
public class ActorLevelUpEvent extends ActorEvent {
    /**
     * 旧的等级
     */
    public int oldLevel;
    /**
     * 新的等级
     */
    public int newLevel;

    public ActorLevelUpEvent(long actorId, int oldLevel, int newLevel) {
        super(EventKey.ACTOR_LEVEL_UP_EVENT, actorId);
        this.oldLevel = oldLevel;
        this.newLevel = newLevel;
    }
}