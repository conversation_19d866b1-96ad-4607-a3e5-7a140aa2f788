package cn.daxiang.hbtd.gameserver.core.database.table;

import cn.daxiang.framework.database.MultiEntity;
import cn.daxiang.framework.database.annotation.Column;
import cn.daxiang.framework.database.annotation.DBQueueType;
import cn.daxiang.framework.database.annotation.Table;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.framework.utils.RandomUtils;
import cn.daxiang.hbtd.gameserver.module.rotaryTable.model.RotaryTableItem;
import cn.daxiang.protocol.game.RotaryTableProtocol.RotaryTableType;
import cn.daxiang.shared.reward.RewardObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.NavigableMap;
import java.util.Optional;
import java.util.TreeMap;

/**
 * 大转盘玩法
 *
 * <AUTHOR>
 * @date 2023/12/19
 */
@Table(name = "rotary_table", type = DBQueueType.DEFAULT)
public class RotaryTable extends MultiEntity<Long> {
    /**
     * 角色ID
     */
    @Column(pk = true, fk = true)
    private long actorId;
    /**
     * 玩法类型 {@link RotaryTableType}
     */
    @Column(pk = true)
    private int type;
    /**
     * 次数
     */
    @Column
    private int times;
    /**
     * 轮数
     */
    @Column
    private int round;
    /**
     * 当前欧皇轮数
     */
    @Column
    private int luckRound;
    /**
     * 当前欧皇次数
     */
    @Column
    private int luckTimes;
    /**
     * 抽奖记录
     * <序号（1-8,1为大奖），已抽奖次数>
     */
    @Column(alias = "record_map")
    private final Map<Integer, Integer> recordMap = Maps.newHashMap();
    /**
     * 抽取概率信息
     * {抽取次数, <序号,概率>}
     */
    @Column(alias = "reward_rate")
    private TreeMap<Integer, Map<Integer, Integer>> rewardRateMap = Maps.newTreeMap();
    /**
     * 当前转盘物品配置 {@link  RotaryTableItem}
     * <序号,配置>
     */
    @Column(alias = "items")
    private Map<Integer, RotaryTableItem> itemMap = Maps.newHashMap();

    /**
     * 设置次数配置
     */
    public void setCounterMap(Map<Integer, Integer> timeByRound) {
        for (Map.Entry<Integer, Integer> entry : timeByRound.entrySet()) {
            RotaryTableItem config = itemMap.computeIfAbsent(entry.getKey(), k -> RotaryTableItem.valueOf(k, entry.getValue()));
            config.setIndex(entry.getKey());
            config.setCount(entry.getValue());
        }
    }

    /**
     * 设置奖励配置
     */
    public void setReward(Map<Integer, Collection<RewardObject>> rewardsByRound) {
        for (Map.Entry<Integer, Collection<RewardObject>> entry : rewardsByRound.entrySet()) {
            RotaryTableItem tableConfig = itemMap.get(entry.getKey());
            tableConfig.setRewards(entry.getValue());
        }
    }

    public static RotaryTable valueOf(long actorId, int type) {
        RotaryTable model = new RotaryTable();
        model.actorId = actorId;
        model.type = type;
        return model;
    }

    @Override
    public Long findFkId() {
        return this.actorId;
    }

    @Override
    public void setFkId(Long fk) {
        this.actorId = fk;
    }

    @Override
    public IdentiyKey findPkId() {
        return IdentiyKey.build(actorId, type);
    }

    @Override
    public void setPkId(IdentiyKey pk) {
        this.actorId = (long) pk.getIdentifys()[0];
        this.type = (int) pk.getIdentifys()[1];
    }

    /**
     * 通过type round count 随机一个序号
     */
    public Optional<Integer> getNewIndex(int times) {
        Collection<Integer> filter = Lists.newArrayList();
        for (Map.Entry<Integer, Integer> entry : recordMap.entrySet()) {
            RotaryTableItem tableConfig = itemMap.get(entry.getKey());
            if (entry.getValue() >= tableConfig.getCount()) {
                filter.add(entry.getKey());
            }
        }
        Integer floorKey = rewardRateMap.floorKey(times);
        if (floorKey == null) {
            floorKey = rewardRateMap.firstKey();
        }
        HashMap<Integer, Integer> newHashMap = Maps.newHashMap();
        NavigableMap<Integer, Map<Integer, Integer>> headMap = rewardRateMap.headMap(floorKey, true);
        for (Map.Entry<Integer, Map<Integer, Integer>> mapEntry : headMap.entrySet()) {
            newHashMap.putAll(mapEntry.getValue());
        }
        for (Integer index : filter) {
            newHashMap.remove(index);
        }
        return Optional.ofNullable(RandomUtils.randomByWeight(newHashMap));
    }

    /**
     * 清理记录
     */
    public void clearRecord() {
        this.times = 0;
        this.recordMap.clear();
    }

    /**
     * 判断是否抽中大奖
     */
    public boolean check() {
        return this.recordMap.getOrDefault(1, 0) > 0;
    }

    /**
     * 获取奖励
     */
    public Collection<RewardObject> getRewardList(int index) {
        RotaryTableItem tableConfig = itemMap.get(index);
        return tableConfig.getRewards();
    }

    /**
     * 是否幸运轮
     */
    public boolean isLuckRound(int luckZone) {
        int zone = round % luckZone;
        return zone == luckRound || (luckRound == luckZone && zone == 0);
    }

    /**
     * 增加记录
     */
    public void addRecord(int index) {
        recordMap.merge(index, 1, Integer::sum);
    }

    public Map<Integer, Integer> getRecordMap() {
        return recordMap;
    }

    public long getActorId() {
        return actorId;
    }

    public void setActorId(long actorId) {
        this.actorId = actorId;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getTimes() {
        return times;
    }

    public void setTimes(int times) {
        this.times = times;
    }

    public int getRound() {
        return round;
    }

    public void setRound(int round) {
        this.round = round;
    }

    public void addCounter() {
        ++times;
    }

    public void addRound() {
        ++round;
    }

    public int getLuckRound() {
        return luckRound;
    }

    public void setLuckRound(int luckRound) {
        this.luckRound = luckRound;
    }

    public TreeMap<Integer, Map<Integer, Integer>> getRewardRateMap() {
        return rewardRateMap;
    }

    public void setRewardRateMap(TreeMap<Integer, Map<Integer, Integer>> rewardRateMap) {
        this.rewardRateMap = rewardRateMap;
    }

    public Map<Integer, RotaryTableItem> getItemMap() {
        return itemMap;
    }

    public int getRemainCount() {
        int itemCount = this.itemMap.values().stream().mapToInt(RotaryTableItem::getCount).sum();
        int recordCount = this.recordMap.values().stream().mapToInt(e -> e).sum();
        return itemCount - recordCount;
    }

    public void setItemMap(Map<Integer, RotaryTableItem> itemMap) {
        this.itemMap = itemMap;
    }

    public int getLuckTimes() {
        return luckTimes;
    }

    public void setLuckTimes(int luckTimes) {
        this.luckTimes = luckTimes;
    }
}
