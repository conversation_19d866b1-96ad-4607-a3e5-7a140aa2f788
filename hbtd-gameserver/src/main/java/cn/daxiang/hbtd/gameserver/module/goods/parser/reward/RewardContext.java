package cn.daxiang.hbtd.gameserver.module.goods.parser.reward;

import cn.daxiang.protocol.game.TypeProtocol.RewardType;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class RewardContext {

    private Map<Integer, RewardParser> parserMap = Maps.newHashMap();

    public void register(RewardType type, RewardParser parser) {
        parserMap.put(type.getNumber(), parser);
    }

    public RewardParser getParser(RewardType type) {
        return parserMap.get(type.getNumber());
    }
}
