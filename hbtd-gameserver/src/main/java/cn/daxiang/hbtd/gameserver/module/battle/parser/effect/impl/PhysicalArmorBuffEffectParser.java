package cn.daxiang.hbtd.gameserver.module.battle.parser.effect.impl;

import cn.daxiang.hbtd.gameserver.core.dataconfig.model.SkillConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.SkillEffectConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.SkillConfigService;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleBuff;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleRoom;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleSprite;
import cn.daxiang.hbtd.gameserver.module.battle.model.FightReport;
import cn.daxiang.hbtd.gameserver.module.battle.parser.effect.AbstractSkillEffectParser;
import cn.daxiang.hbtd.gameserver.module.battle.type.SkillEffectType;
import cn.daxiang.hbtd.gameserver.module.hero.type.HeroSexType;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Iterator;
import java.util.Map;

@Component
public class PhysicalArmorBuffEffectParser extends AbstractSkillEffectParser {

    @Override
    public boolean execute(FightReport report, BattleSprite attacker, BattleSprite targeter, SkillConfig skillConfig, SkillEffectConfig effectConfig, BattleRoom battleRoom) {
        this.addBattleBuff(report, attacker, targeter, skillConfig, effectConfig, battleRoom);
        return true;
    }

    @Override
    protected SkillEffectType getType() {
        return SkillEffectType.PHYSICAL_ARMOR_BUFF_EFFECT;
    }

    @Override
    public long calcEffectValue(FightReport report, BattleSprite battleSprite, BattleRoom battleRoom) {
        //X1:目标的物理抗性（初始值）
        //X2:施法者攻击力（计算buff）
        //X3:目标彩金女武将人数
        //X4:目标彩金男武将人数
        //x5:目标是否存在4个（魏蜀吴群）彩金奖（true）
        long targetPhysicalArmor = battleSprite.getSpriteBattle().getPhysicalArmor();
        Collection<BattleBuff> buffList = battleSprite.getBuffList(getType());
        Map<Integer, Collection<Long>> buffValueMap = Maps.newHashMap();
        int colorWomanCount = battleRoom.getColorSpriteCount(battleSprite.getBattleCamp(), HeroSexType.WOMAN);
        int colorManCount = battleRoom.getColorSpriteCount(battleSprite.getBattleCamp(), HeroSexType.MAN);
        boolean isCollectAllCampColorHero = battleRoom.isCollectAllCampColorHero(battleSprite.getBattleCamp());
        for (Iterator<BattleBuff> iterator = buffList.iterator(); iterator.hasNext(); ) {
            BattleBuff buff = iterator.next();
            BattleSprite casterSprite = battleRoom.getBattleSprite(buff.getCastSpriteUid());
            long casterAttack = casterSprite.getAttack();
            SkillEffectConfig effectConfig = SkillConfigService.getSkillEffectConfig(buff.getEffectId());
            long buffValue = effectConfig.calcSkillEffect(targetPhysicalArmor, casterAttack, colorWomanCount, colorManCount, isCollectAllCampColorHero);
            Collection<Long> buffValues = buffValueMap.get(buff.getEffectId());
            if (buffValues == null) {
                buffValues = Lists.newArrayList();
                buffValueMap.put(buff.getEffectId(), buffValues);
            }
            buffValues.add(buffValue);
        }
        return this.getBuffValue(buffValueMap);
    }
}
