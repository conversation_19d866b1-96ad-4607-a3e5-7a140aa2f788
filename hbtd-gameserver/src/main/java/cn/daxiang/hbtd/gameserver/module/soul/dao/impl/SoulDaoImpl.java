package cn.daxiang.hbtd.gameserver.module.soul.dao.impl;

import cn.daxiang.framework.database.MultiEntity;
import cn.daxiang.framework.database.dao.MultiEntityDaoImpl;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.hbtd.gameserver.core.GameConfig;
import cn.daxiang.hbtd.gameserver.core.database.IdGenerator;
import cn.daxiang.hbtd.gameserver.core.database.UniqueId;
import cn.daxiang.hbtd.gameserver.core.database.table.Soul;
import cn.daxiang.hbtd.gameserver.core.oss.GameOssLogger;
import cn.daxiang.hbtd.gameserver.core.oss.type.OperationType;
import cn.daxiang.hbtd.gameserver.module.soul.dao.SoulDao;
import cn.daxiang.protocol.game.TypeProtocol;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;

/**
 * <AUTHOR>
 * @date 2023/1/30
 */
@Component
public class SoulDaoImpl extends MultiEntityDaoImpl implements SoulDao {
    @Autowired
    private IdGenerator idGenerator;

    @Override
    protected Class<? extends MultiEntity<?>> forClass() {
        return Soul.class;
    }

    @Override
    protected void initMaxId() {
        Long maxId = jdbc.queryForObject("select max(soulId) from soul", Long.class);
        AtomicLong base = null;
        if (maxId == null || maxId <= 0) {
            base = new AtomicLong(UniqueId.otherId(GameConfig.getServerId()));
        } else {
            base = new AtomicLong(maxId);
        }
        idGenerator.init(IdentiyKey.build(this.getClass()), base);
    }

    @Override
    public Soul getSoul(long actorId, int configId) {
        Collection<Soul> all = this.getAllSoul(actorId);
        for (Soul soul : all) {
            if (soul.getConfigId() == configId) {
                return soul;
            }
        }
        return null;
    }

    @Override
    public boolean isExistSoul(long actorId, int configId) {
        Collection<Soul> all = this.getAllSoul(actorId);
        for (Soul soul : all) {
            if (soul.getConfigId() == configId) {
                return true;
            }
        }
        return false;
    }

    @Override
    public Collection<Soul> getAllSoul(long actorId) {
        Map<IdentiyKey, Soul> all = this.getByFk(actorId);
        return all.values();
    }

    @Override
    public Soul getSoul(long actorId, long soulId) {
        return super.getMultiEnity(actorId, IdentiyKey.build(soulId));
    }

    @Override
    public Soul createSoul(long actorId, int configId) {
        long soulUid = idGenerator.increment(IdentiyKey.build(this.getClass()));
        Soul soul = Soul.valueOf(actorId, soulUid, configId);
        updateQueue(soul);
        return soul;
    }

    @Override
    public void deleteSoul(long actorId, Collection<Long> soulIds, OperationType operationType) {
        for (long soulId : soulIds) {
            Soul soul = this.getSoul(actorId, soulId);
            GameOssLogger.goodsDecrease(actorId, operationType, TypeProtocol.RewardType.SOUL, soul.getSoulId(), soul.getConfigId(), 1, 1);
            this.delete(soul);
        }

    }
}
