package cn.daxiang.hbtd.gameserver.core.dataconfig.convert.impl;

import cn.daxiang.hbtd.gameserver.core.dataconfig.convert.IConfigable;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/12/28
 */
public class StringListListConfig implements IConfigable {

    private Collection<List<String>> list = Lists.newArrayList();

    @Override
    public void buildObject(String config) {
        JSONArray jsonArray = JSONArray.parseArray(config);
        for (Object array : jsonArray) {
            List<String> stringList = JSONArray.parseArray(array.toString(), String.class);
            list.add(stringList);
        }
    }

    public Collection<List<String>> getList() {
        return list;
    }
}


