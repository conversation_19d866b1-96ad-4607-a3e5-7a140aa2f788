package cn.daxiang.hbtd.gameserver.module.story;

import cn.daxiang.framework.dispatch.DispatchType;
import cn.daxiang.framework.protocal.DataPacket;
import cn.daxiang.framework.result.CollectionResult;
import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.framework.router.annotation.Cmd;
import cn.daxiang.framework.utils.DateUtils;
import cn.daxiang.hbtd.gameserver.core.database.table.StoryChapter;
import cn.daxiang.hbtd.gameserver.core.database.table.StoryHangUp;
import cn.daxiang.hbtd.gameserver.core.router.GatewayRouterHandlerImpl;
import cn.daxiang.hbtd.gameserver.module.PbBuilder;
import cn.daxiang.hbtd.gameserver.module.story.facade.StoryFacade;
import cn.daxiang.hbtd.gameserver.module.story.facade.StoryHangUpFacade;
import cn.daxiang.hbtd.gameserver.module.story.helper.StoryHelper;
import cn.daxiang.protocol.game.CommonProtocol.IntListPacket;
import cn.daxiang.protocol.game.CommonProtocol.IntPacket;
import cn.daxiang.protocol.game.CommonProtocol.RewardResult;
import cn.daxiang.protocol.game.CommonProtocol.RewardResultResponse;
import cn.daxiang.protocol.game.ModuleProtocol.Module;
import cn.daxiang.protocol.game.StoryProtocol;
import cn.daxiang.protocol.game.StoryProtocol.ChallengeStoryRequest;
import cn.daxiang.protocol.game.StoryProtocol.GoodsQuicklySweepOutRequest;
import cn.daxiang.protocol.game.StoryProtocol.GoodsSweepOutRequest;
import cn.daxiang.protocol.game.StoryProtocol.QuicklySweepOutRequest;
import cn.daxiang.protocol.game.StoryProtocol.ReceiveChapterPassedRewardRequest;
import cn.daxiang.protocol.game.StoryProtocol.ReceiveChapterPassedRewardResponse;
import cn.daxiang.protocol.game.StoryProtocol.StoryCmd;
import cn.daxiang.protocol.game.StoryProtocol.StoryHangUpResponse;
import cn.daxiang.protocol.game.StoryProtocol.StoryInfoResponse;
import cn.daxiang.protocol.game.StoryProtocol.StoryRankRequest;
import cn.daxiang.protocol.game.StoryProtocol.StoryRankResponse;
import cn.daxiang.protocol.game.StoryProtocol.StoryRewardResponse;
import cn.daxiang.protocol.game.StoryProtocol.StoryWipeOutRequest;
import cn.daxiang.protocol.game.StoryProtocol.StoryWipeOutRewardResponse;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import io.netty.channel.Channel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashSet;

@Component
public class StoryHandler extends GatewayRouterHandlerImpl {

    @Autowired
    private StoryFacade storyFacade;
    @Autowired
    private StoryHangUpFacade storyHangUpFacade;

    @Override
    public int getModule() {
        return Module.STORY_VALUE;
    }

    @Cmd(Id = StoryCmd.GET_STORY_LIST_VALUE, dispatchType = DispatchType.ACTOR)
    public void getStoryList(Channel channel, Long actorId, DataPacket packet) {
        CollectionResult<StoryChapter> result = storyFacade.getStoryChapterList(actorId);
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        StoryInfoResponse response = StoryHelper.buildStoryInfoResponse(result.item, true);
        channelWrite(channel, packet, response);
    }

    @Cmd(Id = StoryCmd.PREPARE_STORY_VALUE, dispatchType = DispatchType.ACTOR)
    public void prepareStory(Channel channel, Long actorId, DataPacket packet) {
        ChallengeStoryRequest request = packet.getValue(ChallengeStoryRequest.parser());
        TResult<RewardResult> result = storyFacade.prepareStory(actorId, request.getStoryId(), request.getIsRoll());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        StoryRewardResponse.Builder builder = StoryRewardResponse.newBuilder();
        if (result.item != null) {
            builder.setRewardResult(result.item);
        }
        StoryRewardResponse response = builder.setStoryId(request.getStoryId()).build();
        channelWrite(channel, packet, response);
    }

    @Cmd(Id = StoryCmd.CHALLENGE_STORY_VALUE, dispatchType = DispatchType.ACTOR)
    public void playStory(Channel channel, Long actorId, DataPacket packet) {
        ChallengeStoryRequest request = packet.getValue(ChallengeStoryRequest.parser());
        Result result = storyFacade.challenge(actorId, request);
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
        }
    }

    @Cmd(Id = StoryCmd.RECEIVE_PASSED_REWARD_VALUE, dispatchType = DispatchType.ACTOR)
    public void receivePassedReward(Channel channel, Long actorId, DataPacket packet) {
        ReceiveChapterPassedRewardRequest request = packet.getValue(ReceiveChapterPassedRewardRequest.parser());
        TResult<ReceiveChapterPassedRewardResponse> result = storyFacade.receivePassedReward(actorId, request.getChapterId(), request.getStarNum());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = StoryCmd.STORY_WIPE_OUT_VALUE, dispatchType = DispatchType.ACTOR)
    public void storyWipeOut(Channel channel, Long actorId, DataPacket packet) {
        StoryWipeOutRequest request = packet.getValue(StoryWipeOutRequest.parser());
        TResult<StoryWipeOutRewardResponse> result = storyFacade.storyWipeOut(actorId, request.getStoryId(), request.getHangUpTimes());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = StoryCmd.RESET_CHALLENGE_TIMES_VALUE, dispatchType = DispatchType.ACTOR)
    public void resetChallengeTimes(Channel channel, Long actorId, DataPacket packet) {
        //        IntPacket request = packet.getValue(IntPacket.parser());
        //        TResult<ResetChallengeTimesResponse> result = storyFacade.resetChallengeTimes(actorId, request.getValue());
        //        if (result.isFail()) {
        //            channelWrite(channel, packet, result.statusCode);
        //            return;
        //        }
        //        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = StoryCmd.STORY_RANK_VALUE, dispatchType = DispatchType.ACTOR)
    public void storyRank(Channel channel, Long actorId, DataPacket packet) {
        StoryRankRequest request = packet.getValue(StoryRankRequest.parser());
        TResult<StoryRankResponse> result = storyFacade.getStoryRankResponse(actorId, request.getChapterType(), request.getPage());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = StoryCmd.GET_STORY_HANG_UP_VALUE, dispatchType = DispatchType.ACTOR)
    public void getStoryHangUp(Channel channel, Long actorId, DataPacket packet) {
        TResult<StoryHangUp> result = storyHangUpFacade.getStoryHangUp(actorId);
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        StoryHangUpResponse response = StoryHangUpResponse.newBuilder().setStoryId(result.item.getStoryId()).setLastReceiveTimes(result.item.getLastRewardTime())
            .setEliteBuyTimes(result.item.getEliteBuyTimes()).setEliteRemainChallengeTimes(result.item.getEliteRemainChallengeTimes())
            .addAllReceiveStateId(result.item.getReceiveStateIds()).setFreeRollTimes(result.item.getFreeRollTimes()).setBuyPrivilege(result.item.isBuyPrivilege())
            .setReceivePriReward(result.item.isReceivePriReward()).setReceivePriDailyReward(DateUtils.isToday(result.item.getReceivePriDailyReward()))
            .addAllReceiveIds(result.item.getReceiveStoryRewards()).build();
        channelWrite(channel, packet, response);
    }

    @Cmd(Id = StoryCmd.STORY_HANG_UP_RECEIVE_REWARD_VALUE, dispatchType = DispatchType.ACTOR)
    public void receiveReward(Channel channel, Long actorId, DataPacket packet) {
        //        TResult<RewardResult> result = storyHangUpFacade.receiveReward(actorId);
        //        if (result.isFail()) {
        //            channelWrite(channel, packet, result.statusCode);
        //            return;
        //        }
        //        RewardResultResponse response = PbBuilder.buildRewardResultResponse(result.item);
        //        channelWrite(channel, packet, response);
    }

    @Cmd(Id = StoryCmd.STORY_QUICKLY_RECEIVE_STAR_REWARD_VALUE, dispatchType = DispatchType.ACTOR)
    public void quicklyReceive(Channel channel, Long actorId, DataPacket packet) {
        IntPacket request = packet.getValue(IntPacket.parser());
        TResult<RewardResult> result = storyFacade.quicklyReceive(actorId, request.getValue());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        RewardResultResponse response = PbBuilder.buildRewardResultResponse(result.item);
        channelWrite(channel, packet, response);
    }

    @Cmd(Id = StoryCmd.STORY_QUICKLY_WIPE_OUT_VALUE, dispatchType = DispatchType.ACTOR)
    public void quicklySweepOut(Channel channel, Long actorId, DataPacket packet) {
        QuicklySweepOutRequest request = packet.getValue(QuicklySweepOutRequest.parser());
        HashSet<Integer> storyIdSet = Sets.newHashSet(request.getStoryIdList());
        TResult<StoryWipeOutRewardResponse> result =
            storyFacade.quicklyStoryWipeOut(actorId, Lists.newArrayList(storyIdSet), request.getHangUpTimes(), request.getGoodsUid(), request.getNum(), null);
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = StoryCmd.STORY_GOODS_WIPE_OUT_VALUE, dispatchType = DispatchType.ACTOR)
    public void storyGoodsWipeOut(Channel channel, Long actorId, DataPacket packet) {
        GoodsSweepOutRequest request = packet.getValue(GoodsSweepOutRequest.parser());
        TResult<StoryWipeOutRewardResponse> result =
            storyFacade.storyGoodsWipeOut(actorId, request.getStoryId(), request.getSweepOutTimes(), request.getGoodsId(), request.getExpectedNum());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = StoryCmd.BUY_ELITE_CHALLENGE_TIMES_VALUE, dispatchType = DispatchType.ACTOR)
    public void buyChallengeTimes(Channel channel, Long actorId, DataPacket packet) {
        IntPacket request = packet.getValue(IntPacket.parser());
        Result result = storyHangUpFacade.buyChallengeTimes(actorId, request.getValue());
        channelWrite(channel, packet, result.statusCode);
    }

    @Cmd(Id = StoryCmd.RECEIVE_STATE_REWARD_VALUE, dispatchType = DispatchType.ACTOR)
    public void receiveStateReward(Channel channel, Long actorId, DataPacket packet) {
        IntPacket request = packet.getValue(IntPacket.parser());
        TResult<RewardResult> result = storyHangUpFacade.receiveStateReward(actorId, request.getValue());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        RewardResultResponse response = PbBuilder.buildRewardResultResponse(result.item);
        channelWrite(channel, packet, response);
    }

    @Cmd(Id = StoryCmd.STORY_ELITE_RANK_VALUE, dispatchType = DispatchType.ACTOR)
    public void storyEliteRank(Channel channel, Long actorId, DataPacket packet) {
        IntPacket request = packet.getValue(IntPacket.parser());
        TResult<StoryProtocol.StoryEliteRankResponse> result = storyHangUpFacade.getStoryEliteResponse(actorId, request.getValue());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = StoryCmd.STORY_GOODS_QUICKLY_WIPE_OUT_VALUE, dispatchType = DispatchType.ACTOR)
    public void storyQuicklyGoodsWipeOut(Channel channel, Long actorId, DataPacket packet) {
        GoodsQuicklySweepOutRequest request = packet.getValue(GoodsQuicklySweepOutRequest.parser());
        TResult<StoryWipeOutRewardResponse> result =
            storyFacade.storyQuicklyGoodsWipeOut(actorId, request.getStoryId(), request.getSweepOutTimes(), request.getGoodsId(), request.getExpectedNum(), request.getIsUse());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = StoryCmd.RECEIVE_ROLL_PRIVILEGE_CHARGE_REWARD_VALUE, dispatchType = DispatchType.ACTOR)
    public void receiveRollPrivilegeChargeReward(Channel channel, Long actorId, DataPacket packet) {
        TResult<RewardResult> result = storyHangUpFacade.receiveRollPrivilegeChargeReward(actorId);
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        RewardResultResponse response = PbBuilder.buildRewardResultResponse(result.item);
        channelWrite(channel, packet, response);
    }

    @Cmd(Id = StoryCmd.RECEIVE_ROLL_PRIVILEGE_DAILY_REWARD_VALUE, dispatchType = DispatchType.ACTOR)
    public void receiveRollPrivilegeDailyReward(Channel channel, Long actorId, DataPacket packet) {
        TResult<RewardResult> result = storyHangUpFacade.receiveRollPrivilegeDailyReward(actorId);
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        RewardResultResponse response = PbBuilder.buildRewardResultResponse(result.item);
        channelWrite(channel, packet, response);
    }

    @Cmd(Id = StoryCmd.RECEIVE_STORY_PLAN_REWARDS_VALUE, dispatchType = DispatchType.ACTOR)
    public void receiveStoryPlanRewards(Channel channel, Long actorId, DataPacket packet) {
        IntListPacket request = packet.getValue(IntListPacket.parser());
        TResult<RewardResult> result = storyFacade.receiveStoryPlanRewards(actorId, Sets.newHashSet(request.getListList()));
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        RewardResultResponse response = PbBuilder.buildRewardResultResponse(result.item);
        channelWrite(channel, packet, response);
    }

    @Cmd(Id = StoryCmd.ROLL_STORY_VALUE, dispatchType = DispatchType.ACTOR)
    public void rollStory(Channel channel, Long actorId, DataPacket packet) {
        ChallengeStoryRequest request = packet.getValue(ChallengeStoryRequest.parser());
        TResult<RewardResult> result = storyFacade.rollStory(actorId, request.getStoryId());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        StoryRewardResponse.Builder builder = StoryRewardResponse.newBuilder();
        builder.setStoryId(request.getStoryId());
        builder.setRewardResult(result.item);
        channelWrite(channel, packet, builder.build());
    }
    @Cmd(Id = StoryCmd.STORY_ZONE_RANK_VALUE, dispatchType = DispatchType.ACTOR)
    public void storyZoneRank(Channel channel, Long actorId, DataPacket packet) {
        StoryRankRequest request = packet.getValue(StoryRankRequest.parser());
        TResult<StoryRankResponse> result = storyFacade.getStoryZoneRankResponse(actorId, request.getChapterType(), request.getPage());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }
}
