package cn.daxiang.hbtd.gameserver.module.activity.facade.impl;

import cn.daxiang.framework.result.TResult;
import cn.daxiang.hbtd.gameserver.core.GameBaseFacade;
import cn.daxiang.hbtd.gameserver.core.database.table.ActivityActorValue;
import cn.daxiang.hbtd.gameserver.core.database.table.ActivityGlobal;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.ActivityOpenConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.ActivityOpenConfigService;
import cn.daxiang.hbtd.gameserver.module.activity.dao.ActivityActorValueDao;
import cn.daxiang.hbtd.gameserver.module.activity.dao.ActivityGlobalDao;
import cn.daxiang.hbtd.gameserver.module.activity.facade.ActivityFacade;
import cn.daxiang.hbtd.gameserver.module.activity.helper.ActivityPushHelper;
import cn.daxiang.hbtd.gameserver.module.activity.parser.ActivityContext;
import cn.daxiang.hbtd.gameserver.module.activity.parser.ActivityParser;
import cn.daxiang.hbtd.gameserver.module.activity.type.ActivityStatus;
import cn.daxiang.hbtd.gameserver.module.activity.type.ActivityType;
import cn.daxiang.hbtd.gameserver.module.user.helper.ActorHelper;
import cn.daxiang.protocol.game.ActivityProtocol;
import cn.daxiang.protocol.game.ActivityProtocol.ActivityActionResponse;
import cn.daxiang.protocol.game.ActivityProtocol.ActivityInfoResponse;
import com.google.protobuf.ByteString;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;

@Component
public class ActivityFacadeImpl extends GameBaseFacade implements ActivityFacade {

    @Autowired
    private ActivityContext context;
    @Autowired
    private ActivityGlobalDao activityGlobalDao;
    @Autowired
    private ActivityActorValueDao activityActorValueDao;

    @Override
    public TResult<ActivityInfoResponse> getActivityInfo(long actorId) {
        ActivityInfoResponse.Builder response = ActivityInfoResponse.newBuilder();
        for (ActivityType activityType : ActivityType.values()) {
            Collection<ActivityOpenConfig> activityOpenConfigList = ActivityOpenConfigService.getByType(activityType);
            long currentTimeMillis = System.currentTimeMillis();
            for (ActivityOpenConfig config : activityOpenConfigList) {
                if (config.isUnopened(currentTimeMillis)) {
                    continue;
                }
                int activityId = config.getId();
                if (!isActivityOpen(activityId)) {
                    continue;
                }
                TResult<ActivityParser> parserResult = this.getActivityParser(actorId, activityId);
                if (parserResult.isFail()) {
                    continue;
                }
                TResult<ByteString> recordResult = parserResult.item.getRecord2Client(actorId, activityId);
                if (recordResult.isFail()) {
                    continue;
                }
                TResult<ByteString> globalResult = parserResult.item.getGlobal2Client(actorId, activityId);
                ActivityProtocol.ActivityInfo.Builder builder = ActivityProtocol.ActivityInfo.newBuilder();
                builder.setActivityId(activityId);
                if (globalResult.item != null) {
                    builder.setGlobal(globalResult.item);
                }
                if (recordResult.item != null) {
                    builder.setRecord(recordResult.item);
                }
                ActivityProtocol.ActivityInfo activityInfo = builder.build();
                response.putActivityInfo(activityId, activityInfo);
            }
        }
        ActivityInfoResponse activityInfoResponse = response.build();
        return TResult.sucess(activityInfoResponse);
    }

    @Override
    public TResult<ActivityInfoResponse> getActivityInfo(long actorId, int activityId) {
        TResult<ActivityParser> parserResult = this.getActivityParser(actorId, activityId);
        if (parserResult.isFail()) {
            return TResult.valueOf(parserResult.statusCode);
        }
        TResult<ByteString> recordResult = parserResult.item.getRecord2Client(actorId, activityId);
        if (recordResult.isFail()) {
            return TResult.valueOf(recordResult.statusCode);
        }
        TResult<ByteString> globalResult = parserResult.item.getGlobal2Client(actorId, activityId);
        ActivityProtocol.ActivityInfo.Builder builder = ActivityProtocol.ActivityInfo.newBuilder();
        builder.setActivityId(activityId);
        if (globalResult.item != null) {
            builder.setGlobal(globalResult.item);
        }
        if (recordResult.item != null) {
            builder.setRecord(recordResult.item);
        }
        ActivityProtocol.ActivityInfo activityInfo = builder.build();
        ActivityInfoResponse response = ActivityInfoResponse.newBuilder().putActivityInfo(activityId, activityInfo).build();
        return TResult.sucess(response);
    }

    /**
     * 获取活动解析器
     *
     * @param actorId
     * @param activityId
     * @return
     */
    private TResult<ActivityParser> getActivityParser(long actorId, int activityId) {
        ActivityOpenConfig activityOpenConfig = ActivityOpenConfigService.getActivityConfig(activityId);
        if (activityOpenConfig == null) {
            LOGGER.error("ActivityOpenConfig not found, id:{}", activityId);
            return TResult.valueOf(CONFIG_NOT_FOUND);
        }
        if (!activityOpenConfig.isReached(ActorHelper.getActorLevel(actorId), ActorHelper.getVipLevel(actorId))) {
            return TResult.valueOf(ACTIVITY_LIMIT_BY_LEVEL_OR_VIP_LEVEL);
        }

        if (!isActivityOpen(activityId)) {
            LOGGER.info("ActivityId:{}", activityId);
            return TResult.valueOf(ACTIVITY_STATUS_ERROR);
        }

        ActivityParser parser = context.getParser(ActivityType.getType(activityOpenConfig.getActivityType()));
        if (parser == null) {
            LOGGER.error("ActivityParser not found, type:{}", activityOpenConfig.getActivityType());
            return TResult.valueOf(CONFIG_ERROR);
        }
        return TResult.sucess(parser);
    }

    @Override
    public TResult<ActivityActionResponse> action(long actorId, int activityId, int id, byte[] value) {
        TResult<ActivityParser> parserResult = this.getActivityParser(actorId, activityId);
        if (parserResult.isFail()) {
            return TResult.valueOf(parserResult.statusCode);
        }
        TResult<ActivityActionResponse> result = parserResult.item.action(actorId, activityId, id, value);
        if (result.isOk()) {
            TResult<ActivityInfoResponse> activityResult = this.getActivityInfo(actorId, activityId);
            if (activityResult.isOk()) {
                ActivityPushHelper.pushActivity(actorId, activityResult.item);
            }
        }
        return result;
    }

    @Override
    public boolean isActivityOpen(int activityId) {
        ActivityGlobal activityGlobal = activityGlobalDao.getActivityGlobal(activityId);
        if (activityGlobal == null || activityGlobal.getStatus() == ActivityStatus.CLOSE.getId()) {
            return false;
        }
        return true;
    }

    @Override
    public TResult<ActivityActorValue> getActivityActorValue(long actorId, int activityType) {
        ActivityActorValue activityActorValue = activityActorValueDao.getActivityActorValue(actorId, activityType);
        if (activityActorValue == null) {
            return TResult.fail();
        }
        return TResult.sucess(activityActorValue);
    }

    @Override
    public void updateActivityActorValue(long actorId, int activityType, String record) {
        activityActorValueDao.updateActivityActorValue(actorId, activityType, record);
    }
}
