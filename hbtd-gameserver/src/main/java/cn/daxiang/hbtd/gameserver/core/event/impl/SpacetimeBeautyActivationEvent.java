package cn.daxiang.hbtd.gameserver.core.event.impl;

import cn.daxiang.framework.event.ActorEvent;
import cn.daxiang.hbtd.gameserver.core.oss.type.OperationType;
import cn.daxiang.shared.event.EventKey;

/**
 * 时空红颜合成激活事件
 *
 * @author: <PERSON>
 * @date: 2023/8/18 11:29
 * @Description:
 */
public class SpacetimeBeautyActivationEvent extends ActorEvent {
    /**
     * 时空红颜配置Id
     */
    public int configId;
    /**
     * 获取途径
     */
    public OperationType operationType;

    public SpacetimeBeautyActivationEvent(long actorId, int configId, OperationType operationType) {
        super(EventKey.SPACETIME_BEAUTY_ACTIVATION_EVENT, actorId);
        this.configId = configId;
        this.operationType = operationType;
    }
}
