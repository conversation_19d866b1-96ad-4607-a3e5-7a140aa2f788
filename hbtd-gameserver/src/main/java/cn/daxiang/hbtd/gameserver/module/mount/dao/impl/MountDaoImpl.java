package cn.daxiang.hbtd.gameserver.module.mount.dao.impl;

import cn.daxiang.framework.database.MultiEntity;
import cn.daxiang.framework.database.dao.MultiEntityDaoImpl;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.hbtd.gameserver.core.database.table.Mount;
import cn.daxiang.hbtd.gameserver.module.mount.dao.MountDao;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;

@Component
public class MountDaoImpl extends MultiEntityDaoImpl implements MountDao {
    @Override
    protected Class<? extends MultiEntity<?>> forClass() {
        return Mount.class;
    }

    @Override
    protected void initMaxId() {

    }

    @Override
    public Collection<Mount> getMountList(long actorId) {
        Map<IdentiyKey, Mount> entityMap = this.getByFk(actorId);
        return entityMap.values();
    }

    @Override
    public Mount getMount(long actorId, int id) {
        return this.getMultiEnity(actorId, IdentiyKey.build(actorId, id));
    }

    @Override
    public Mount creatMount(long actorId, int cid) {
        Mount mount = Mount.creatMount(actorId, cid);
        this.updateQueue(mount);
        return mount;
    }

}
