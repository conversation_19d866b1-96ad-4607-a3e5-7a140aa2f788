package cn.daxiang.hbtd.gameserver.module.activity.type;

/**
 * <AUTHOR>
 * @date 2022/7/21
 */
public enum Activity56PriceType {
    /**
     * 1-元宝购买
     */
    DIAMOND_BUY(1),
    /**
     * 2-直购
     */
    DIRECT_PURCHASING_BUY(2),

    /**
     * 0.none
     */
    NONE(0);

    private int id;

    private Activity56PriceType(int id) {
        this.id = id;
    }

    public static Activity56PriceType getType(int id) {
        for (Activity56PriceType type : Activity56PriceType.values()) {
            if (type.id == id) {
                return type;
            }
        }
        return NONE;
    }

    public int getId() {
        return id;
    }
}
