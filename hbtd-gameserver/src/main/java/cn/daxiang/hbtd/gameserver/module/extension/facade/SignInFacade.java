package cn.daxiang.hbtd.gameserver.module.extension.facade;

import cn.daxiang.framework.result.TResult;
import cn.daxiang.hbtd.gameserver.core.database.table.SignIn;
import cn.daxiang.protocol.game.CommonProtocol;

import java.util.Collection;

public interface SignInFacade {
    /**
     * 获取签到
     *
     * @param actorId
     * @return
     */
    TResult<SignIn> getSignIn(long actorId);

    /**
     * 签到
     *
     * @param actorId
     * @return
     */
    TResult<CommonProtocol.RewardResult> signIn(long actorId);

    /**
     * 领取累积签到签到
     *
     * @param actorId
     * @return
     */
    TResult<CommonProtocol.RewardResult> recieveTotalSignIn(long actorId, Collection<Integer> configIds);
}
