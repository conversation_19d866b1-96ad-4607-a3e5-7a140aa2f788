package cn.daxiang.hbtd.gameserver.core.event.impl;

import cn.daxiang.framework.event.ActorEvent;
import cn.daxiang.shared.event.EventKey;

/**
 * 无尽试炼（原沙场演武）
 */
public class BattlefieldDrillChallengeEvent extends ActorEvent {
    /**
     * 无尽试炼ID
     */
    public int id;
    /**
     * 最高ID
     */
    public int maxId;

    public BattlefieldDrillChallengeEvent(long actorId, int id, int maxId) {
        super(EventKey.BATTLEFIELD_DRILL_CHALLENGE_EVENT, actorId);
        this.id = id;
        this.maxId = maxId;
    }
}
