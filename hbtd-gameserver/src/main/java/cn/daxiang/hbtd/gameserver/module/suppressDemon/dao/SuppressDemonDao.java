package cn.daxiang.hbtd.gameserver.module.suppressDemon.dao;

import cn.daxiang.hbtd.gameserver.core.database.table.SuppressDemon;
import cn.daxiang.hbtd.gameserver.module.suppressDemon.model.SuppressDemonRank;
import cn.daxiang.hbtd.gameserver.module.suppressDemon.model.entity.SuppressDemonEntity;

import java.util.Collection;

/**
 * <AUTHOR>
 */
public interface SuppressDemonDao {
    /**
     * 获取镇妖台信息
     *
     * @param actorId
     * @return
     */
    SuppressDemon getSuppressDemon(long actorId);

    /**
     * 获取镇妖台排名
     *
     * @param actorId
     * @param id
     * @return
     */
    SuppressDemonRank getSuppressDemonRank(long actorId, int id);

    /**
     * 获取镇妖台排名
     *
     * @param id
     * @return
     */
    Collection<SuppressDemonRank> getRankList(int id);

    /**
     * 参与排名
     *
     * @param actorId
     * @param entity
     */
    void achieveRank(long actorId, SuppressDemonEntity entity);

    /**
     * 清空排名
     */
    void cleanRank();
}
