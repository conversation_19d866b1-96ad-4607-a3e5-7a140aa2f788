package cn.daxiang.hbtd.gameserver.module.chess.dao;

import cn.daxiang.hbtd.gameserver.core.database.table.Chess;
import cn.daxiang.hbtd.gameserver.module.chess.model.ChessRank;

import java.util.Collection;

/**
 * <AUTHOR>
 * @date 2023/4/25
 */
public interface ChessDao {
    /**
     * 获取珍珑棋局信息
     *
     * @param actorId
     * @return
     */
    Chess getChess(long actorId);

    /**
     * 珍珑棋局排行更新
     *
     * @param actorId
     * @param num     通关数
     */
    void achieveChessRank(long actorId, int num, long time);

    /**
     * 获取所有的排行
     *
     * @return
     */
    Collection<ChessRank> getAllRanks();

    /**
     * 清除排行榜
     */
    void cleanRank();
}
