package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.dataconfig.annotation.FieldIgnore;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.shared.reward.RewardObject;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;

import java.util.Collection;

/**
 * 活动54盈余奖励配置表
 *
 * <AUTHOR>
 * @date 2024/1/11
 */
@DataFile(fileName = "activity_54_extra_reward_config")
public class Activity54ExtraRewardConfig implements ModelAdapter {
    /**
     * 活动DATA
     */
    private int data;
    /**
     * 消耗 [type,id,count]
     */
    private String cost;
    /**
     * 奖励 [[type,id,count],[type,id,count]]
     */
    private String rewards;

    /**
     * 花费列表
     */
    @FieldIgnore
    private RewardObject costRewardObject;
    /**
     * 奖励列表
     */
    @FieldIgnore
    private Collection<RewardObject> rewardList = Lists.newArrayList();

    @Override
    public void initialize() {
        costRewardObject = RewardObject.valueOf(JSONArray.parseArray(cost));
        for (Object rewardsItem : JSONArray.parseArray(rewards)) {
            JSONArray rewardArray = JSONArray.parseArray(rewardsItem.toString());
            RewardObject rewardObject = RewardObject.valueOf(rewardArray);
            rewardList.add(rewardObject);
        }
    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(data);
    }

    public int getData() {
        return data;
    }

    public RewardObject getCostRewardObject() {
        return costRewardObject;
    }

    public Collection<RewardObject> getRewardList() {
        return rewardList;
    }
}
