package cn.daxiang.hbtd.gameserver.core.dataconfig.service;

import cn.daxiang.framework.dataconfig.ConfigServiceAdapter;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.BeautyAptitudeConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.BeautyLikabilityConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.BeautyScriptConfig;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;
import java.util.Optional;
import java.util.TreeMap;

/**
 * @Author: Gary
 * @Date 2022-10-29 14:07
 * @Description:
 */
@Component
public class BeautyConfigService extends ConfigServiceAdapter {
    /**
     * key:beautyId,value:{key:level,value:config}
     */
    private static Map<Integer, TreeMap<Integer, BeautyLikabilityConfig>> BEAUTY_LIKABILITY_CONFIG_MAP = Maps.newHashMap();

    /**
     * key:scriptId,value:{key:textId,value:config}
     */
    private static Map<Integer, TreeMap<Integer, BeautyScriptConfig>> BEAUTY_SCRIPT_CONFIG_MAP = Maps.newHashMap();

    /**
     * key:beautyId,value:{key:type,value:{key:value,value:coefficient}}
     */
    private static Map<Integer, Map<Integer, TreeMap<Integer, Integer>>> BEAUTY_APTITUDE_COEFFICIENT_CONFIG_MAP = Maps.newHashMap();

    public static Optional<TreeMap<Integer, BeautyLikabilityConfig>> getLikabilityConfigByBeautyId(int beautyId) {
        return Optional.ofNullable(BEAUTY_LIKABILITY_CONFIG_MAP.get(beautyId));
    }

    public static Optional<TreeMap<Integer, BeautyScriptConfig>> getScriptConfigByScriptId(int scriptId) {
        return Optional.ofNullable(BEAUTY_SCRIPT_CONFIG_MAP.get(scriptId));
    }

    public static Optional<Integer> getCoefficient(int beautyId, int type, int value) {
        Map<Integer, TreeMap<Integer, Integer>> typeMap = BEAUTY_APTITUDE_COEFFICIENT_CONFIG_MAP.get(beautyId);
        if (value == 0) {
            return Optional.empty();
        }
        if (typeMap == null) {
            return Optional.empty();
        }
        TreeMap<Integer, Integer> valueMap = typeMap.get(type);
        if (valueMap == null) {
            return Optional.empty();
        }
        if (valueMap.ceilingEntry(value) == null) {
            return Optional.ofNullable(valueMap.lastEntry().getValue());
        }
        return Optional.ofNullable(valueMap.ceilingEntry(value).getValue());
    }

    protected void initialize() {
        Collection<BeautyLikabilityConfig> configs = dataConfig.listAll(this, BeautyLikabilityConfig.class);
        for (BeautyLikabilityConfig config : configs) {
            TreeMap<Integer, BeautyLikabilityConfig> treeMap = BEAUTY_LIKABILITY_CONFIG_MAP.computeIfAbsent(config.getBeautyId(), x -> Maps.newTreeMap());
            treeMap.put(config.getLikabilityLevel(), config);
        }
        Collection<BeautyScriptConfig> beautyScriptConfigs = dataConfig.listAll(this, BeautyScriptConfig.class);
        for (BeautyScriptConfig config : beautyScriptConfigs) {
            TreeMap<Integer, BeautyScriptConfig> treeMap = BEAUTY_SCRIPT_CONFIG_MAP.computeIfAbsent(config.getScriptId(), x -> Maps.newTreeMap());
            treeMap.put(config.getTextId(), config);
        }
        Collection<BeautyAptitudeConfig> aptitudeConfigs = dataConfig.listAll(this, BeautyAptitudeConfig.class);
        for (BeautyAptitudeConfig config : aptitudeConfigs) {
            Map<Integer, TreeMap<Integer, Integer>> typeMap = BEAUTY_APTITUDE_COEFFICIENT_CONFIG_MAP.computeIfAbsent(config.getBeautyId(), x -> Maps.newHashMap());
            TreeMap<Integer, Integer> valueMap = typeMap.computeIfAbsent(config.getType(), x -> Maps.newTreeMap());
            valueMap.put(config.getValue(), config.getCoefficient());
        }
    }

    @Override
    protected void clean() {
        BEAUTY_LIKABILITY_CONFIG_MAP.clear();
        BEAUTY_SCRIPT_CONFIG_MAP.clear();
        BEAUTY_APTITUDE_COEFFICIENT_CONFIG_MAP.clear();
    }
}
