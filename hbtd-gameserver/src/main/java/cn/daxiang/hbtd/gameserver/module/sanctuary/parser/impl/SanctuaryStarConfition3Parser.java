package cn.daxiang.hbtd.gameserver.module.sanctuary.parser.impl;

import cn.daxiang.hbtd.gameserver.core.dataconfig.model.SanctuaryClearanceConditionConfig;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleMember;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleRoom;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleSprite;
import cn.daxiang.hbtd.gameserver.module.sanctuary.parser.AbstractSanctuaryStarConditionParser;
import cn.daxiang.hbtd.gameserver.module.sanctuary.type.SanctuaryStarConditionType;
import cn.daxiang.protocol.game.BattleProtocol;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/9/15
 */
@Component
public class SanctuaryStarConfition3Parser extends AbstractSanctuaryStarConditionParser {
    @Override
    protected SanctuaryStarConditionType getType() {
        return SanctuaryStarConditionType.STAR_CONDITION_3;
    }

    @Override
    public boolean meet(<PERSON>Room battleRoom, SanctuaryClearanceConditionConfig conditionConfig) {
        if (!battleRoom.isWin()) {
            return false;
        }
        BattleMember battleMember = battleRoom.getBattleMember(BattleProtocol.BattleCamp.LEFT_CAMP);
        int deadNum = 0;
        for (BattleSprite battleSprite : battleMember.getSpriteMap().values()) {
            if (battleSprite.getHPPercent() == 0) {
                deadNum++;
            }
        }
        return deadNum <= conditionConfig.getParameter1();
    }
}
