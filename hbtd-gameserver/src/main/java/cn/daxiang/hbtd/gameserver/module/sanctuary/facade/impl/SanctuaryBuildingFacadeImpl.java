package cn.daxiang.hbtd.gameserver.module.sanctuary.facade.impl;

import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.framework.result.CollectionResult;
import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.framework.rpc.RpcCall;
import cn.daxiang.framework.rpc.world.WorldSanctuaryRpc;
import cn.daxiang.framework.utils.FormulaUtils;
import cn.daxiang.hbtd.gameserver.core.GameBaseFacade;
import cn.daxiang.hbtd.gameserver.core.GameConfig;
import cn.daxiang.hbtd.gameserver.core.database.table.SanctuaryBuilding;
import cn.daxiang.hbtd.gameserver.core.database.table.SanctuaryEquipment;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.SanctuaryBuildConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.SanctuaryBuildEquipmentConfig;
import cn.daxiang.hbtd.gameserver.core.dispatch.DispatchHelper;
import cn.daxiang.hbtd.gameserver.core.event.impl.SanctuaryBuildLevelTaskEvent;
import cn.daxiang.hbtd.gameserver.core.event.impl.SanctuaryShanHeValueTaskEvent;
import cn.daxiang.hbtd.gameserver.core.oss.type.OperationType;
import cn.daxiang.hbtd.gameserver.core.rpc.WorldRpcHelper;
import cn.daxiang.hbtd.gameserver.module.goods.helper.RewardHelper;
import cn.daxiang.hbtd.gameserver.module.sanctuary.dao.SanctuaryBuildingDao;
import cn.daxiang.hbtd.gameserver.module.sanctuary.facade.SanctuaryActorFacade;
import cn.daxiang.hbtd.gameserver.module.sanctuary.facade.SanctuaryBuildingFacade;
import cn.daxiang.hbtd.gameserver.module.sanctuary.helper.SanctuaryHelper;
import cn.daxiang.hbtd.gameserver.module.sanctuary.helper.SanctuaryPushHelper;
import cn.daxiang.hbtd.gameserver.module.sanctuary.model.CampBuildingInfo;
import cn.daxiang.hbtd.gameserver.module.sanctuaryEquipment.facade.SanctuaryEquipmentFacade;
import cn.daxiang.hbtd.gameserver.module.user.helper.ActorHelper;
import cn.daxiang.hbtd.gameserver.module.user.type.ActorUnlockType;
import cn.daxiang.protocol.game.SanctuaryProtocol.SanctuaryBuildingType;
import cn.daxiang.shared.GlobalConfigKey;
import cn.daxiang.shared.module.lineup.SpriteAttributeType;
import cn.daxiang.shared.module.sanctuary.SanctuaryRegionState;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/8/28
 */
@Component
public class SanctuaryBuildingFacadeImpl extends GameBaseFacade implements SanctuaryBuildingFacade {
    @Autowired
    private SanctuaryActorFacade sanctuaryActorFacade;
    @Autowired
    private SanctuaryBuildingDao sanctuaryBuildingDao;
    @Autowired
    private SanctuaryEquipmentFacade sanctuaryEquipmentFacade;

    @Override
    public TResult<SanctuaryBuilding> getSanctuaryBuilding(long actorId) {
        Result unlock = ActorHelper.unlock(actorId, ActorUnlockType.SANCTUARY);
        if (unlock.isFail()) {
            return TResult.valueOf(unlock);
        }
        TResult<SanctuaryRegionState> stateTResult = sanctuaryActorFacade.getSanctuaryRegionState();
        if (stateTResult.isFail()) {
            return TResult.valueOf(stateTResult.statusCode);
        }
        SanctuaryBuilding sanctuaryBuilding = sanctuaryBuildingDao.getSanctuaryBuilding(actorId);
        return TResult.sucess(sanctuaryBuilding);
    }

    @Override
    public Result synthesizeAndEquip(long actorId, SanctuaryBuildingType type, int position, int configId) {
        TResult<SanctuaryRegionState> stateTResult = sanctuaryActorFacade.getSanctuaryRegionState();
        if (stateTResult.isFail()) {
            return Result.valueOf(stateTResult.statusCode);
        }
        if (type == SanctuaryBuildingType.UNRECOGNIZED || type == SanctuaryBuildingType.SANCTUARY_BUILDING_NONE || type == SanctuaryBuildingType.SANCTUARY_PUBLIC) {
            return Result.valueOf(INVALID_PARAM);
        }
        TResult<SanctuaryBuilding> sanctuaryBuildingTResult = getSanctuaryBuilding(actorId);
        if (sanctuaryBuildingTResult.isFail()) {
            return Result.valueOf(sanctuaryBuildingTResult.statusCode);
        }
        SanctuaryBuilding sanctuaryBuilding = sanctuaryBuildingTResult.item;
        SanctuaryBuildEquipmentConfig buildEquipmentConfig = globalConfigService.findConfig(configId, SanctuaryBuildEquipmentConfig.class);
        if (buildEquipmentConfig == null) {
            return Result.valueOf(CONFIG_ERROR);
        }
        if (position != buildEquipmentConfig.getType()) {
            return Result.valueOf(INVALID_PARAM);
        }
        CampBuildingInfo campBuildingInfo = sanctuaryBuilding.getBuildingMap().get(type.getNumber());
        if (campBuildingInfo.getEquipmentMap().keySet().contains(position)) {
            return Result.valueOf(SANCTUARY_BUILDING_EQUIP_POSITION_ERROR);
        }
        SanctuaryBuildConfig buildingConfig =
            globalConfigService.findConfig(IdentiyKey.build(stateTResult.item.getSeasonId(), type.getNumber(), campBuildingInfo.getStage()), SanctuaryBuildConfig.class);
        if (buildingConfig == null) {
            return Result.valueOf(CONFIG_NOT_FOUND);
        }
        if (!buildingConfig.getEquipIdList().contains(configId)) {
            return Result.valueOf(SANCTUARY_BUILDING_EQUIPMENT_CANNOT_EQUIP);
        }
        Result decrease = RewardHelper.decrease(actorId, buildEquipmentConfig.getCostList(), OperationType.SANCTUARY_BUILDING_EQUIPMENT_SYNTHESIS);
        if (decrease.isFail()) {
            return decrease;
        }
        //创建一件装备
        Map<Integer, Long> data = Maps.newHashMap();
        data.put(configId, 1L);
        CollectionResult<SanctuaryEquipment> sanctuaryEquipmentListResult =
            sanctuaryEquipmentFacade.createSanctuaryEquipment(actorId, data, OperationType.SANCTUARY_BUILDING_EQUIPMENT_SYNTHESIS);
        SanctuaryEquipment sanctuaryEquipment = sanctuaryEquipmentListResult.item.stream().findFirst().get();
        campBuildingInfo.equip(position, sanctuaryEquipment.getEquipmentId());
        this.refreshValue(stateTResult.item.getSeasonId(), sanctuaryBuilding);
        dbQueue.updateQueue(sanctuaryBuilding);
        SanctuaryPushHelper.pushSanctuaryBuilding(actorId, sanctuaryBuilding);
        return Result.valueOf();
    }

    @Override
    public Result equip(long actorId, SanctuaryBuildingType type, int position, long sanctuaryEquipmentId) {
        TResult<SanctuaryRegionState> stateTResult = sanctuaryActorFacade.getSanctuaryRegionState();
        if (stateTResult.isFail()) {
            return Result.valueOf(stateTResult.statusCode);
        }
        if (type == SanctuaryBuildingType.UNRECOGNIZED || type == SanctuaryBuildingType.SANCTUARY_BUILDING_NONE || type == SanctuaryBuildingType.SANCTUARY_PUBLIC) {
            return Result.valueOf(INVALID_PARAM);
        }
        TResult<SanctuaryBuilding> sanctuaryBuildingTResult = getSanctuaryBuilding(actorId);
        if (sanctuaryBuildingTResult.isFail()) {
            return Result.valueOf(sanctuaryBuildingTResult.statusCode);
        }
        SanctuaryBuilding sanctuaryBuilding = sanctuaryBuildingTResult.item;
        TResult<SanctuaryEquipment> sanctuaryEquipmentTResult = sanctuaryEquipmentFacade.getSanctuaryEquipment(actorId, sanctuaryEquipmentId);
        if (sanctuaryEquipmentTResult.isFail()) {
            return Result.valueOf(sanctuaryEquipmentTResult.statusCode);
        }
        SanctuaryEquipment sanctuaryEquipment = sanctuaryEquipmentTResult.item;
        CampBuildingInfo campBuildingInfo = sanctuaryBuilding.getBuildingMap().get(type.getNumber());
        if (campBuildingInfo.getEquipmentMap().keySet().contains(position)) {
            return Result.valueOf(SANCTUARY_BUILDING_EQUIP_POSITION_ERROR);
        }
        SanctuaryBuildConfig buildingConfig =
            globalConfigService.findConfig(IdentiyKey.build(stateTResult.item.getSeasonId(), type.getNumber(), campBuildingInfo.getStage()), SanctuaryBuildConfig.class);
        if (buildingConfig == null) {
            return Result.valueOf(CONFIG_NOT_FOUND);
        }
        if (!buildingConfig.getEquipIdList().contains(sanctuaryEquipment.getConfigId())) {
            return Result.valueOf(SANCTUARY_BUILDING_EQUIPMENT_CANNOT_EQUIP);
        }
        SanctuaryBuildEquipmentConfig buildEquipmentConfig = globalConfigService.findConfig(sanctuaryEquipment.getConfigId(), SanctuaryBuildEquipmentConfig.class);
        if (buildEquipmentConfig == null) {
            return Result.valueOf(CONFIG_ERROR);
        }
        if (position != buildEquipmentConfig.getType()) {
            return Result.valueOf(INVALID_PARAM);
        }
        campBuildingInfo.equip(position, sanctuaryEquipmentId);
        this.refreshValue(stateTResult.item.getSeasonId(), sanctuaryBuilding);
        dbQueue.updateQueue(sanctuaryBuilding);
        SanctuaryPushHelper.pushSanctuaryBuilding(actorId, sanctuaryBuilding);
        return Result.valueOf();
    }

    @Override
    public Result mainLevelUp(long actorId) {
        TResult<SanctuaryBuilding> sanctuaryBuildingTResult = getSanctuaryBuilding(actorId);
        if (sanctuaryBuildingTResult.isFail()) {
            return Result.valueOf(sanctuaryBuildingTResult.statusCode);
        }
        SanctuaryBuilding sanctuaryBuilding = sanctuaryBuildingTResult.item;
        TResult<SanctuaryRegionState> stateTResult = sanctuaryActorFacade.getSanctuaryRegionState();
        if (stateTResult.isFail()) {
            return Result.valueOf(stateTResult.statusCode);
        }
        int publicBuildingType = SanctuaryBuildingType.SANCTUARY_PUBLIC_VALUE;
        SanctuaryBuildConfig buildingConfig =
            globalConfigService.findConfig(IdentiyKey.build(stateTResult.item.getSeasonId(), publicBuildingType, sanctuaryBuilding.getLevel()), SanctuaryBuildConfig.class);
        if (buildingConfig == null) {
            return Result.valueOf(CONFIG_NOT_FOUND);
        }
        if (buildingConfig.getCostList().isEmpty()) {
            return Result.valueOf(SANCTUARY_BUILDING_LEVEL_MAX);
        }
        Result decrease = RewardHelper.decrease(actorId, buildingConfig.getCostList(), OperationType.SANCTUARY_BUILDING_LEVEL_UP);
        if (decrease.isFail()) {
            return decrease;
        }
        sanctuaryBuilding.setLevel(sanctuaryBuilding.getLevel() + 1);
        this.refreshValue(stateTResult.item.getSeasonId(), sanctuaryBuilding);
        dbQueue.updateQueue(sanctuaryBuilding);
        SanctuaryPushHelper.pushSanctuaryBuilding(actorId, sanctuaryBuilding);
        DispatchHelper.postEvent(new SanctuaryBuildLevelTaskEvent(actorId, publicBuildingType, sanctuaryBuilding.getLevel()));
        return Result.valueOf();
    }

    @Override
    public Result campLevelUp(long actorId, int campId) {
        SanctuaryBuildingType buildingType = SanctuaryBuildingType.forNumber(campId);
        if (buildingType == SanctuaryBuildingType.UNRECOGNIZED || buildingType == SanctuaryBuildingType.SANCTUARY_BUILDING_NONE
            || buildingType == SanctuaryBuildingType.SANCTUARY_PUBLIC) {
            return Result.valueOf(INVALID_PARAM);
        }
        TResult<SanctuaryBuilding> sanctuaryBuildingTResult = getSanctuaryBuilding(actorId);
        if (sanctuaryBuildingTResult.isFail()) {
            return Result.valueOf(sanctuaryBuildingTResult.statusCode);
        }
        SanctuaryBuilding sanctuaryBuilding = sanctuaryBuildingTResult.item;
        TResult<SanctuaryRegionState> stateTResult = sanctuaryActorFacade.getSanctuaryRegionState();
        if (stateTResult.isFail()) {
            return Result.valueOf(stateTResult.statusCode);
        }
        CampBuildingInfo campBuildingInfo = sanctuaryBuilding.getBuildingMap().get(campId);
        SanctuaryBuildConfig buildingConfig =
            globalConfigService.findConfig(IdentiyKey.build(stateTResult.item.getSeasonId(), campId, campBuildingInfo.getStage()), SanctuaryBuildConfig.class);
        if (buildingConfig == null) {
            return Result.valueOf(CONFIG_NOT_FOUND);
        }
        if (buildingConfig.getCostList().isEmpty()) {
            return Result.valueOf(SANCTUARY_BUILDING_LEVEL_MAX);
        }
        //需要删除的建筑装备唯一ID
        Collection<Long> deleltIdSet = Sets.newHashSet();
        //将建筑穿戴的装备配置Id remove出 升级需要消耗的建筑装备配置ID列表得方式来计算，穿戴的装备是否够消耗，如果升级需要消耗的建筑装备配置ID列表最后为empty，说明满足否则不满足
        for (Long sanctuaryEquipmentId : campBuildingInfo.getEquipmentMap().values()) {
            deleltIdSet.add(sanctuaryEquipmentId);
        }
        if (deleltIdSet.size() != buildingConfig.getEquipIdList().size()) {
            return Result.valueOf(SANCTUARY_BUILDING_CANNOT_LEVEL_UP);
        }
        sanctuaryEquipmentFacade.deleteSanctuaryEquipment(actorId, deleltIdSet, OperationType.SANCTUARY_BUILDING_LEVEL_UP);
        campBuildingInfo.levelUp();
        this.refreshValue(stateTResult.item.getSeasonId(), sanctuaryBuilding);
        dbQueue.updateQueue(sanctuaryBuilding);
        SanctuaryPushHelper.pushSanctuaryBuilding(actorId, sanctuaryBuilding);
        DispatchHelper.postEvent(new SanctuaryBuildLevelTaskEvent(actorId, campId, campBuildingInfo.getStage()));
        return Result.valueOf();
    }

    @Override
    public void cleanSanctuaryBuilding(long actorId) {
        SanctuaryBuilding sanctuaryBuilding = sanctuaryBuildingDao.getExsitSanctuaryBuilding(actorId);
        if (sanctuaryBuilding != null) {
            dbQueue.deleteQueue(sanctuaryBuilding);
        }
    }

    /**
     * 刷新山河值
     *
     * @param sanctuaryBuilding
     */
    private void refreshValue(int seasonId, SanctuaryBuilding sanctuaryBuilding) {
        long actorId = sanctuaryBuilding.getActorId();
        //X1：主建筑攻击 X2：主建筑生命 X3：主建筑物防 X4：主建筑法防 X5：阵营建筑攻击 …… X8：阵营建筑法防 X9-X12：灭魏/蜀/吴/群 X13-16：抗
        long x1, x2, x3, x4, x5, x6, x7, x8, x9, x10, x11, x12, x13, x14, x15, x16;
        //公共建筑属性
        Collection<Integer> publicBuildingSpecialIdList = SanctuaryHelper.getPublicBuildingSpecialIdList(sanctuaryBuilding, seasonId);
        Map<SpriteAttributeType, Long> publicBuildingAttributeMap = SanctuaryHelper.getPublicBuildingAttributeMap(sanctuaryBuilding, seasonId, publicBuildingSpecialIdList);
        x1 = publicBuildingAttributeMap.getOrDefault(SpriteAttributeType.ATK, 0L);
        x2 = publicBuildingAttributeMap.getOrDefault(SpriteAttributeType.HP, 0L);
        x3 = publicBuildingAttributeMap.getOrDefault(SpriteAttributeType.PHYSICAL_ARMOR, 0L);
        x4 = publicBuildingAttributeMap.getOrDefault(SpriteAttributeType.SPELL_ARMOR, 0L);

        x9 = publicBuildingAttributeMap.getOrDefault(SpriteAttributeType.RESTRAINT_WEI_DAMAGE_BONUS, 0L);
        x10 = publicBuildingAttributeMap.getOrDefault(SpriteAttributeType.RESTRAINT_SHU_DAMAGE_BONUS, 0L);
        x11 = publicBuildingAttributeMap.getOrDefault(SpriteAttributeType.RESTRAINT_WU_DAMAGE_BONUS, 0L);
        x12 = publicBuildingAttributeMap.getOrDefault(SpriteAttributeType.RESTRAINT_QUN_DAMAGE_BONUS, 0L);
        x13 = publicBuildingAttributeMap.getOrDefault(SpriteAttributeType.INDULGE_WEI_DAMAGE_REDUCTION, 0L);
        x14 = publicBuildingAttributeMap.getOrDefault(SpriteAttributeType.INDULGE_SHU_DAMAGE_REDUCTION, 0L);
        x15 = publicBuildingAttributeMap.getOrDefault(SpriteAttributeType.INDULGE_WU_DAMAGE_REDUCTION, 0L);
        x16 = publicBuildingAttributeMap.getOrDefault(SpriteAttributeType.INDULGE_QUN_DAMAGE_REDUCTION, 0L);
        //阵营建筑属性
        Collection<Integer> campBuildingSpecialIdList = SanctuaryHelper.getCampBuildingSpecialIdList(sanctuaryBuilding, seasonId);
        Map<SpriteAttributeType, Long> campBuildingBaseAttributeMap =
            SanctuaryHelper.getCampBuildingBaseAttributeMap(actorId, sanctuaryBuilding, seasonId, campBuildingSpecialIdList);
        x5 = campBuildingBaseAttributeMap.getOrDefault(SpriteAttributeType.ATK, 0L);
        x6 = campBuildingBaseAttributeMap.getOrDefault(SpriteAttributeType.HP, 0L);
        x7 = campBuildingBaseAttributeMap.getOrDefault(SpriteAttributeType.PHYSICAL_ARMOR, 0L);
        x8 = campBuildingBaseAttributeMap.getOrDefault(SpriteAttributeType.SPELL_ARMOR, 0L);

        x9 += campBuildingBaseAttributeMap.getOrDefault(SpriteAttributeType.RESTRAINT_WEI_DAMAGE_BONUS, 0L);
        x10 += campBuildingBaseAttributeMap.getOrDefault(SpriteAttributeType.RESTRAINT_SHU_DAMAGE_BONUS, 0L);
        x11 += campBuildingBaseAttributeMap.getOrDefault(SpriteAttributeType.RESTRAINT_WU_DAMAGE_BONUS, 0L);
        x12 += campBuildingBaseAttributeMap.getOrDefault(SpriteAttributeType.RESTRAINT_QUN_DAMAGE_BONUS, 0L);
        x13 += campBuildingBaseAttributeMap.getOrDefault(SpriteAttributeType.INDULGE_WEI_DAMAGE_REDUCTION, 0L);
        x14 += campBuildingBaseAttributeMap.getOrDefault(SpriteAttributeType.INDULGE_SHU_DAMAGE_REDUCTION, 0L);
        x15 += campBuildingBaseAttributeMap.getOrDefault(SpriteAttributeType.INDULGE_WU_DAMAGE_REDUCTION, 0L);
        x16 += campBuildingBaseAttributeMap.getOrDefault(SpriteAttributeType.INDULGE_QUN_DAMAGE_REDUCTION, 0L);

        String shanHeValueExpr = globalConfigService.findGlobalConfig(GlobalConfigKey.SANCTUARY_SHANHEPOWER_EXPR).getValue();
        long shanHeValue = FormulaUtils.executeRoundingLong(shanHeValueExpr, x1, x2, x3, x4, x5, x6, x7, x8, x9, x10, x11, x12, x13, x14, x15, x16);
        sanctuaryBuilding.setValue(shanHeValue);
        Map<Byte, Object> actorAttributeMap = ActorHelper.getActorAttributeMap(actorId);
        WorldRpcHelper.asynCall(actorId, WorldSanctuaryRpc.class, new RpcCall<WorldSanctuaryRpc>() {
            @Override
            public void run(WorldSanctuaryRpc rpcProxy) {
                rpcProxy.refreshValueRank(GameConfig.getServerType(), GameConfig.getServerId(), actorId, actorAttributeMap, shanHeValue);
            }
        });
        DispatchHelper.postEvent(new SanctuaryShanHeValueTaskEvent(sanctuaryBuilding.getActorId(), shanHeValue));
    }
}
