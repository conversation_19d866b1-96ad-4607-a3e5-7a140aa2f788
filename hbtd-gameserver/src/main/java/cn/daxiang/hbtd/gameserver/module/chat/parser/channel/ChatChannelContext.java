package cn.daxiang.hbtd.gameserver.module.chat.parser.channel;

import cn.daxiang.protocol.game.TypeProtocol.ChatChannelType;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class ChatChannelContext {

    private Map<ChatChannelType, ChatChannelParser> parserMap = Maps.newHashMap();

    public void register(ChatChannelType type, ChatChannelParser parser) {
        parserMap.put(type, parser);
    }

    public ChatChannelParser getParser(ChatChannelType type) {
        return parserMap.get(type);
    }
}