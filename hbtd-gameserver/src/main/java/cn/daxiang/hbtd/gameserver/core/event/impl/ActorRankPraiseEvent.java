package cn.daxiang.hbtd.gameserver.core.event.impl;

import cn.daxiang.framework.event.ActorEvent;
import cn.daxiang.hbtd.gameserver.module.user.type.RankType;
import cn.daxiang.shared.event.EventKey;

/**
 * 点赞事件
 *
 * <AUTHOR>
 */
public class ActorRankPraiseEvent extends ActorEvent {
    /**
     * 点赞排行榜
     */
    public RankType rankType;

    public ActorRankPraiseEvent(long actorId, RankType rankType) {
        super(EventKey.ACTOR_RANK_PRAISE_EVENT, actorId);
        this.rankType = rankType;
    }
}