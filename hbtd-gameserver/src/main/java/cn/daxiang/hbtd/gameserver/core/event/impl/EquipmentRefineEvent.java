package cn.daxiang.hbtd.gameserver.core.event.impl;

import cn.daxiang.framework.event.ActorEvent;
import cn.daxiang.hbtd.gameserver.core.database.table.Equipment;
import cn.daxiang.shared.event.EventKey;

import java.util.Collection;

/**
 * 装备精炼事件
 *
 * <AUTHOR>
 */
public class EquipmentRefineEvent extends ActorEvent {
    /**
     * 装备列表
     */
    public Collection<Equipment> equipmentList;
    /**
     * 精炼次数
     */
    public int refineTimes;
    /**
     * 精炼升级次数
     */
    public int refineLevelTimes;

    public EquipmentRefineEvent(long actorId, Collection<Equipment> equipmentList, int refineTimes, int refineLevelTimes) {
        super(EventKey.EQUIPMENT_REFINE_EVENT, actorId);
        this.equipmentList = equipmentList;
        this.refineTimes = refineTimes;
        this.refineLevelTimes = refineLevelTimes;
    }
}
