package cn.daxiang.hbtd.gameserver.module.prerogative.facade;

import cn.daxiang.framework.result.CollectionResult;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.hbtd.gameserver.core.database.table.Prerogative;
import cn.daxiang.hbtd.gameserver.core.oss.type.OperationType;
import cn.daxiang.hbtd.gameserver.module.prerogative.model.entity.PrerogativeEntity;
import cn.daxiang.hbtd.gameserver.module.prerogative.type.PrerogativeType;

import java.util.Collection;

public interface PrerogativeFacade {
    /**
     * 获取特权信息
     *
     * @param actorId
     * @return
     */
    TResult<Prerogative> getPrerogative(long actorId);

    /**
     * 根据类型获取特权信息
     *
     * @param actorId
     * @param type
     * @return
     */
    CollectionResult<PrerogativeEntity> getPrerogativeEntity(long actorId, PrerogativeType type);

    /**
     * 添加特权
     *
     * @param actorId
     * @param ids
     * @param operationType
     */
    CollectionResult<PrerogativeEntity> addPrerogative(long actorId, Collection<Integer> ids, OperationType operationType);
}
