package cn.daxiang.hbtd.gameserver.core.event.impl;

import cn.daxiang.framework.event.ActorDelayEvent;
import cn.daxiang.shared.event.EventKey;
import com.google.common.base.Objects;

/**
 * 名将塔恢复次数事件
 *
 * @Author: Gary
 * @Date 2022-10-20 18:06
 * @Description:
 */
public class TowerRecoverTimesEvent extends ActorDelayEvent {
    public TowerRecoverTimesEvent(long actorId) {
        super(EventKey.TOWER_RECOVER_TIMES_EVENT, actorId);
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(actorId, name);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        TowerRecoverTimesEvent other = (TowerRecoverTimesEvent) obj;
        if (actorId != other.actorId)
            return false;
        if (name != other.name)
            return false;
        return true;
    }
}
