package cn.daxiang.hbtd.gameserver.core.dataconfig.service;

import cn.daxiang.framework.dataconfig.ConfigServiceAdapter;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.FunctionAtlasConfig;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;
import java.util.TreeMap;

/**
 * <AUTHOR>
 */
@Component
public class FunctionAtlasConfigService extends ConfigServiceAdapter {
    /**
     * key:level,value:Collection<FunctionAtlasConfig>
     */
    private static TreeMap<Integer, Collection<FunctionAtlasConfig>> FUNCTION_ATLAS_LEVEL_ID_LIST_MAP = Maps.newTreeMap();

    /**
     * 获取可展示的功能手册的functionIds
     * (初始化调用)
     *
     * @param actorLevel
     * @return
     */
    public static Collection<FunctionAtlasConfig> getFunctionAtlasShowList(int actorLevel) {
        Collection<FunctionAtlasConfig> configs = Lists.newArrayList();
        Integer key = FUNCTION_ATLAS_LEVEL_ID_LIST_MAP.floorKey(actorLevel);
        if (key == null) {
            return configs;
        }
        for (Map.Entry<Integer, Collection<FunctionAtlasConfig>> entry : FUNCTION_ATLAS_LEVEL_ID_LIST_MAP.entrySet()) {
            if (entry.getKey() > key) {
                break;
            }
            configs.addAll(entry.getValue());
        }
        return configs;
    }

    /**
     * 获取该等级下刚好可展示的functionIds
     * (玩家升级的时候调用)
     *
     * @param newLevel
     * @return
     */

    public static Collection<FunctionAtlasConfig> getFunctionAtlasList(int newLevel) {
        Collection<FunctionAtlasConfig> configs = Lists.newArrayList();
        Integer newKey = FUNCTION_ATLAS_LEVEL_ID_LIST_MAP.floorKey(newLevel);
        for (Map.Entry<Integer, Collection<FunctionAtlasConfig>> entry : FUNCTION_ATLAS_LEVEL_ID_LIST_MAP.entrySet()) {
            if (entry.getKey() > newKey) {
                break;
            }
            configs.addAll(entry.getValue());
        }
        return configs;
    }

    @Override
    protected void initialize() {
        Collection<FunctionAtlasConfig> FunctionAtlasConfigList = dataConfig.listAll(this, FunctionAtlasConfig.class);
        for (FunctionAtlasConfig config : FunctionAtlasConfigList) {
            Collection<FunctionAtlasConfig> functionIdList = FUNCTION_ATLAS_LEVEL_ID_LIST_MAP.get(config.getShowLevel());
            if (functionIdList == null) {
                functionIdList = Lists.newArrayList();
                FUNCTION_ATLAS_LEVEL_ID_LIST_MAP.put(config.getShowLevel(), functionIdList);
            }
            functionIdList.add(config);
        }
    }

    @Override
    protected void clean() {
        FUNCTION_ATLAS_LEVEL_ID_LIST_MAP.clear();
    }
}
