package cn.daxiang.hbtd.gameserver.module.runes.helper;

import cn.daxiang.framework.protocal.DataPacket;
import cn.daxiang.hbtd.gameserver.core.database.table.Runes;
import cn.daxiang.hbtd.gameserver.core.database.table.RunesLineup;
import cn.daxiang.hbtd.gameserver.core.netty.channel.PlayerChannel;
import cn.daxiang.protocol.game.CommonProtocol;
import cn.daxiang.protocol.game.ModuleProtocol.Module;
import cn.daxiang.protocol.game.RunesProtocol;
import cn.daxiang.protocol.game.RunesProtocol.RunesCmd;

import java.util.Collection;

/**
 * <AUTHOR>
 * @date 2022/12/21
 */
public class RunesPushHelper {
    /**
     * 推送兵符信息
     * <pre>
     * 推送:{@code RunesInfoResponse}
     * </pre>
     */
    public static void pushRunesList(long actorId, Collection<Runes> runesList) {
        RunesProtocol.RunesInfoResponse response = RunesHelper.buildRunesInfoResponse(runesList);
        DataPacket packet = DataPacket.valueOf(Module.RUNES_VALUE, RunesCmd.PUSH_RUNES_INFO_VALUE, response);
        PlayerChannel.push(actorId, packet);
    }
    /**
     * 推送灵石信息
     * <pre>
     * 推送:{@code GemstoneInfoResponse}
     * </pre>
     */
    //    public static void pushGemstoneList(long actorId, Collection<Runes> runesList) {
    //        RunesProtocol.RunesInfoResponse response = RunesHelper.buildRunesInfoResponse(runesList);
    //        DataPacket packet = DataPacket.valueOf(Module.RUNES_VALUE, RunesCmd.PUSH_GEMSTONE_INFO_VALUE, response);
    //        PlayerChannel.push(actorId, packet);
    //    }

    /**
     * 推送已穿戴兵符信息
     * <pre>
     * 推送:{@code RunesLineupInfoResponse}
     * </pre>
     */
    public static void pushRunesLineup(long actorId, RunesLineup runesLineup) {
        RunesProtocol.RunesLineupInfoResponse response = RunesHelper.buildRunesLineupInfoResponse(runesLineup);
        DataPacket packet = DataPacket.valueOf(Module.RUNES_VALUE, RunesCmd.PUSH_RUNES_LINEUP_INFO_VALUE, response);
        PlayerChannel.push(actorId, packet);
    }

    /**
     * 推送删除的runesId集合
     * <pre>
     * 推送:{@code LongListPacket}
     * </pre>
     */
    public static void pushdeleteRunesIdList(long actorId, Collection<Long> deleteIdList) {
        CommonProtocol.LongListPacket response = CommonProtocol.LongListPacket.newBuilder().addAllList(deleteIdList).build();
        DataPacket packet = DataPacket.valueOf(Module.RUNES_VALUE, RunesCmd.PUSH_DELETE_RUNES_INFO_VALUE, response);
        PlayerChannel.push(actorId, packet);
    }
}
