package cn.daxiang.hbtd.gameserver.module.user.helper;

import cn.daxiang.framework.protocal.DataPacket;
import cn.daxiang.hbtd.gameserver.core.database.table.Actor;
import cn.daxiang.hbtd.gameserver.core.database.table.Charge;
import cn.daxiang.hbtd.gameserver.core.netty.channel.PlayerChannel;
import cn.daxiang.hbtd.gameserver.module.PbBuilder;
import cn.daxiang.protocol.game.CommonProtocol.IntPacket;
import cn.daxiang.protocol.game.ModuleProtocol.Module;
import cn.daxiang.protocol.game.TypeProtocol.ActorFieldType;
import cn.daxiang.protocol.game.UserProtocol;
import cn.daxiang.protocol.game.UserProtocol.ActorChargeResponse;
import cn.daxiang.protocol.game.UserProtocol.ActorLoginResponse;
import cn.daxiang.protocol.game.UserProtocol.UserCmd;
import com.google.common.collect.Lists;

import java.util.Collection;
import java.util.Map;
import java.util.Set;

/**
 * 角色推送帮助类
 *
 * <AUTHOR>
 */
public class ActorPushHelper {
    /**
     * 推送角色属性
     *
     * @param actorId
     * @param list
     */
    public static void pushActorAttribute(long actorId, Set<ActorFieldType> list) {
        ActorFieldType[] strings = new ActorFieldType[list.size()];
        list.toArray(strings);
        pushActorAttribute(actorId, strings);
    }

    /**
     * 推送角色属性
     *
     * @param actorId
     * @param ActorFieldTypes
     */
    public static void pushActorAttribute(long actorId, ActorFieldType... ActorFieldTypes) {
        Actor actor = ActorHelper.getActor(actorId);
        if (actor == null) {
            return;
        }
        ActorLoginResponse actorAttributeMap = PbBuilder.buildActorLoginResponse(actor, ActorFieldTypes);
        DataPacket packet = DataPacket.valueOf(Module.USER_VALUE, UserCmd.PUSH_ACTOR_ATTRIBUTE_VALUE, actorAttributeMap);
        PlayerChannel.push(actorId, packet);
    }

    /**
     * 推送角色充值
     *
     * @param actorId
     * @param charges
     */
    public static void pushActorCharge(long actorId, Collection<Charge> charges, Map<String, Integer> chargeOrderMap) {
        Collection<UserProtocol.Charge> list = Lists.newArrayListWithCapacity(charges.size());
        for (Charge charge : charges) {
            list.add(PbBuilder.buildCharge(charge));
        }
        ActorChargeResponse response = UserProtocol.ActorChargeResponse.newBuilder().addAllCharges(list).putAllChargeOrderMap(chargeOrderMap).build();
        DataPacket packet = DataPacket.valueOf(Module.USER_VALUE, UserCmd.PUSH_ACTOR_CHARGE_VALUE, response);
        PlayerChannel.push(actorId, packet);
    }

    /**
     * 离线被献花数量
     *
     * @param actorId
     * @param count
     */
    public static void pushActorOfflineFlowered(long actorId, int count) {
        UserProtocol.ActorOfflinePraisedResponse response = UserProtocol.ActorOfflinePraisedResponse.newBuilder().setCount(count).build();
        DataPacket packet = DataPacket.valueOf(Module.USER_VALUE, UserCmd.PUSH_ACTOR_OFFLINE_PRAISED_VALUE, response);
        PlayerChannel.push(actorId, packet);
    }

    /**
     * 推送客户端版本更新消息
     */
    public static void pushClientVersionUpdateValue(int version) {
        IntPacket intPacket = IntPacket.newBuilder().setValue(version).build();
        DataPacket packet = DataPacket.valueOf(Module.USER_VALUE, UserCmd.PUSH_CLIENT_VERSION_UPDATE_VALUE, intPacket);
        PlayerChannel.pushAllOnline(packet);
    }
}
