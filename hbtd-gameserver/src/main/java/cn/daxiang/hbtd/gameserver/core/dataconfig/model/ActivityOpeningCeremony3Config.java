package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.dataconfig.annotation.FieldIgnore;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.shared.reward.RewardObject;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;

import java.util.Collection;

/**
 * 活动-19-群雄逐鹿
 *
 * <AUTHOR>
 * @date 2022/6/9
 */
@DataFile(fileName = "activity_opening_ceremony_3_config")
public class ActivityOpeningCeremony3Config implements ModelAdapter {
    /**
     * 活动DATA
     */
    private int data;
    /**
     * 唯一ID
     */
    private int id;
    /**
     * 1-战力排名
     * 2-主线排名
     * 3-战力达标
     * 4-主线进度达标
     */
    private int type;
    /**
     * 排名
     */
    private long rank;
    /**
     * type 3-4类型 目标
     */
    private long target;
    /**
     * 奖励
     */
    private String rewards;

    @FieldIgnore
    private Collection<RewardObject> rewardList = Lists.newArrayList();

    @Override
    public void initialize() {
        JSONArray rewardArray = JSONArray.parseArray(rewards);
        for (Object reward : rewardArray) {
            JSONArray array = JSONArray.parseArray(reward.toString());
            RewardObject rewardObject = RewardObject.valueOf(array);
            rewardList.add(rewardObject);
        }
    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(data, id);
    }

    public int getData() {
        return data;
    }

    public int getId() {
        return id;
    }

    public int getType() {
        return type;
    }

    public long getRank() {
        return rank;
    }

    public long getTarget() {
        return target;
    }

    public Collection<RewardObject> getRewardList() {
        return rewardList;
    }
}
