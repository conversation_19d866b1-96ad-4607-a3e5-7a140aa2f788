package cn.daxiang.hbtd.gameserver.module.activity.model.entity.record;

import com.google.common.collect.Lists;

import java.util.List;

/**
 * 任务战令
 * HBTD\doc\策划文档\已评审文档\beta5.3商业化\任务战令-MG.xlsx
 *
 * <AUTHOR>
 * @date 2022/5/7
 */
public class ActivityRecord54 {
    /**
     * 是否激活高级战令
     */
    private boolean isActive;
    /**
     * 普通战令已领取列表
     */
    private List<Integer> normalReceiveList = Lists.newCopyOnWriteArrayList();
    /**
     * 高级战令已领取列表
     */
    private List<Integer> supremeReceiveList = Lists.newCopyOnWriteArrayList();

    /**
     * 满级后-累计获得盈余奖励数量
     */
    private int receivesTimes;

    public boolean isActive() {
        return isActive;
    }

    public void setActive(boolean active) {
        isActive = active;
    }

    public List<Integer> getNormalReceiveList() {
        return normalReceiveList;
    }

    public void setNormalReceiveList(List<Integer> normalReceiveList) {
        this.normalReceiveList = normalReceiveList;
    }

    public List<Integer> getSupremeReceiveList() {
        return supremeReceiveList;
    }

    public void setSupremeReceiveList(List<Integer> supremeReceiveList) {
        this.supremeReceiveList = supremeReceiveList;
    }

    public void activate() {
        this.isActive = true;
    }

    public void receiveNormalReward(int level) {
        this.normalReceiveList.add(level);
    }

    public void receiveSupremeReward(int level) {
        this.supremeReceiveList.add(level);
    }

    public int getReceivesTimes() {
        return receivesTimes;
    }

    public void setReceivesTimes(int receivesTimes) {
        this.receivesTimes = receivesTimes;
    }

    public void addReceivesTimes(int times) {
        this.receivesTimes += times;
    }

    /**
     * 普通战令是否领取过
     *
     * @param configId configId
     * @return true = 已领取
     */
    public boolean isNormalReceive(int configId) {
        return normalReceiveList.contains(configId);
    }

    /**
     * 高级战令是否领取过
     *
     * @param configId configId
     * @return true = 已领取
     */
    public boolean isSupremeReceive(int configId) {
        return supremeReceiveList.contains(configId);
    }
}
