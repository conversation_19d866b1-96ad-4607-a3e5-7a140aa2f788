package cn.daxiang.hbtd.gameserver.module.thousandDraws.helper;

import cn.daxiang.hbtd.gameserver.core.database.table.ThousandDraws;
import cn.daxiang.protocol.game.ThousanddrawsProtocol.ThousandDrawsResponse;
import cn.daxiang.protocol.game.ThousanddrawsProtocol.ThousandDrawsRewardResponse;

import java.util.Collection;

public class ThousandDrawsHelper {

    public static ThousandDrawsResponse buildThousandDrawsResponse(ThousandDraws thousandDraws) {
        ThousandDrawsResponse.Builder builder = ThousandDrawsResponse.newBuilder();
        builder.setId(thousandDraws.getTempId());
        builder.setTimes(thousandDraws.getTimes());
        builder.setReceiveReward(thousandDraws.isReceiveReward());
        builder.putAllTempRewardMap(thousandDraws.getTempRewardMap());
        return builder.build();
    }

    public static ThousandDrawsRewardResponse buildThousandDrawsRewardResponse(int showPoolConfigId, Collection<Integer> poolConfigIdList) {
        ThousandDrawsRewardResponse.Builder builder = ThousandDrawsRewardResponse.newBuilder();
        builder.setShowId(showPoolConfigId);
        builder.addAllIdList(poolConfigIdList);
        ThousandDrawsRewardResponse response = builder.build();
        return response;
    }

}
