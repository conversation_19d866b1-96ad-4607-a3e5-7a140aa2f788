package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.dataconfig.annotation.FieldIgnore;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.shared.reward.RewardObject;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;

import java.util.Collection;

/**
 * 36、登陆有礼
 *
 * <AUTHOR>
 * @date 2023/3/21
 */
@DataFile(fileName = "activity_36_sign_config")
public class Activity36SignConfig implements ModelAdapter {
    /**
     * 活动data
     */
    private int data;
    /**
     * 签到天数
     */
    private int days;
    /**
     * 签到普通奖励
     */
    private String signReward;
    /**
     * 签到特权奖励
     */
    private String privilegeReward;
    /**
     * 登陆有礼特权礼包价格ID，只读取每期活动第一个配置
     */
    private int chargeId;
    /**
     * 普通奖励列表
     */
    @FieldIgnore
    private Collection<RewardObject> signRewardList = Lists.newLinkedList();
    /**
     * 特权奖励列表
     */
    @FieldIgnore
    private Collection<RewardObject> privilegeRewardList = Lists.newLinkedList();

    @Override
    public void initialize() {
        JSONArray rewardsArray = JSONArray.parseArray(signReward);
        for (Object rewardsItem : rewardsArray) {
            JSONArray rewardArray = JSONArray.parseArray(rewardsItem.toString());
            RewardObject rewardObject = RewardObject.valueOf(rewardArray);
            signRewardList.add(rewardObject);
        }
        JSONArray costsArray = JSONArray.parseArray(privilegeReward);
        for (Object costsItem : costsArray) {
            JSONArray costArray = JSONArray.parseArray(costsItem.toString());
            RewardObject rewardObject = RewardObject.valueOf(costArray);
            privilegeRewardList.add(rewardObject);
        }
    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(data, days);
    }

    public int getData() {
        return data;
    }

    public int getDays() {
        return days;
    }

    public Collection<RewardObject> getSignRewardList() {
        return signRewardList;
    }

    public Collection<RewardObject> getPrivilegeRewardList() {
        return privilegeRewardList;
    }

    public int getChargeId() {
        return chargeId;
    }
}
