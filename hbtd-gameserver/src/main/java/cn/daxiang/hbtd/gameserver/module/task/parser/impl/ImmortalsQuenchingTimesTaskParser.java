package cn.daxiang.hbtd.gameserver.module.task.parser.impl;

import cn.daxiang.hbtd.gameserver.core.database.table.Task;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.TaskConfig;
import cn.daxiang.hbtd.gameserver.core.event.impl.ImmortalsQuenchingEvent;
import cn.daxiang.hbtd.gameserver.module.task.parser.AbstractTaskParser;
import cn.daxiang.hbtd.gameserver.module.task.type.TaskConditionType;
import cn.daxiang.protocol.game.TaskProtocol;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/7/14
 */
@Component
public class ImmortalsQuenchingTimesTaskParser extends AbstractTaskParser<ImmortalsQuenchingEvent> {
    @Override
    public void init(Task task, TaskConfig taskConfig) {
        if (task.getValue() >= taskConfig.getValue()) {
            task.setStatus(TaskProtocol.TaskStatus.TASK_FINISH);
        }
    }

    @Override
    protected TaskConditionType getType() {
        return TaskConditionType.IMMORTALS_QUENCHING_TIMES;
    }

    @Override
    protected boolean parseCondition(ImmortalsQuenchingEvent event, Task task, TaskConfig taskConfig) {
        task.setValue(task.getValue() + event.quenchingLevelTimes);
        if (task.getValue() >= taskConfig.getValue()) {
            task.setStatus(TaskProtocol.TaskStatus.TASK_FINISH);
        }
        return true;
    }

}
