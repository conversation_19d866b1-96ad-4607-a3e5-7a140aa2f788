package cn.daxiang.hbtd.gameserver.module.extension.helper;

import cn.daxiang.framework.result.TResult;
import cn.daxiang.framework.utils.DateUtils;
import cn.daxiang.framework.utils.ObjectReference;
import cn.daxiang.hbtd.gameserver.core.database.table.Actor;
import cn.daxiang.hbtd.gameserver.core.database.table.DailyGift;
import cn.daxiang.hbtd.gameserver.core.database.table.DrawEnergy;
import cn.daxiang.hbtd.gameserver.core.database.table.FirstRecharge;
import cn.daxiang.hbtd.gameserver.core.database.table.Fund;
import cn.daxiang.hbtd.gameserver.core.database.table.HeroRecommend;
import cn.daxiang.hbtd.gameserver.core.database.table.RankTargetActor;
import cn.daxiang.hbtd.gameserver.core.database.table.RankTargetGlobal;
import cn.daxiang.hbtd.gameserver.core.database.table.ThousandGacha;
import cn.daxiang.hbtd.gameserver.core.database.table.VipCard;
import cn.daxiang.hbtd.gameserver.core.database.table.WeekGift;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.DrawEnergyConfigService;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.RankRewardConfigService;
import cn.daxiang.hbtd.gameserver.module.PbBuilder;
import cn.daxiang.hbtd.gameserver.module.extension.dao.RankTargetGlobalDao;
import cn.daxiang.hbtd.gameserver.module.extension.facade.FundFacade;
import cn.daxiang.hbtd.gameserver.module.extension.facade.RankTargetFacade;
import cn.daxiang.hbtd.gameserver.module.extension.facade.VipCardFacade;
import cn.daxiang.hbtd.gameserver.module.extension.model.ChargeReturnInfo;
import cn.daxiang.hbtd.gameserver.module.extension.model.FundEntity;
import cn.daxiang.hbtd.gameserver.module.extension.model.LimitedTimeChargeEntity;
import cn.daxiang.hbtd.gameserver.module.extension.model.RankTargetInfo;
import cn.daxiang.hbtd.gameserver.module.user.helper.ActorHelper;
import cn.daxiang.hbtd.gameserver.module.user.model.entity.VipCardEntity;
import cn.daxiang.hbtd.gameserver.module.user.type.VipCardType;
import cn.daxiang.protocol.game.CommonProtocol;
import cn.daxiang.protocol.game.ExtensionProtocol;
import cn.daxiang.protocol.game.ExtensionProtocol.ChargeReturnResponse.Builder;
import cn.daxiang.protocol.game.ExtensionProtocol.HeroLineupRecommendResponse;
import cn.daxiang.protocol.game.ExtensionProtocol.ThousandGachaResponse;
import cn.daxiang.protocol.game.ExtensionProtocol.VipCardResponse;
import cn.daxiang.protocol.game.ExtensionProtocol.VipCardVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Collection;
import java.util.Map;

@Component
public class ExtensionHelper {
    private static ObjectReference<ExtensionHelper> ref = new ObjectReference<>();
    @Autowired
    FundFacade fundFacade;
    @Autowired
    VipCardFacade vipCardFacade;
    @Autowired
    RankTargetGlobalDao rankTargetGlobalDao;
    @Autowired
    RankTargetFacade rankTargetFacade;

    public static ExtensionProtocol.FundResponse buildFundResponse(Fund fund) {
        ExtensionProtocol.FundResponse.Builder builder = ExtensionProtocol.FundResponse.newBuilder();
        for (Map.Entry<Integer, FundEntity> entry : fund.getFundInfoMap().entrySet()) {
            builder.putEntities(entry.getKey(), buildFundEntity(entry.getKey(), entry.getValue()));
        }
        return builder.build();
    }

    public static VipCardResponse buildVipCardResponse(VipCard vipCard) {
        VipCardResponse.Builder builder = VipCardResponse.newBuilder();
        for (VipCardEntity entity : vipCard.getVipCardMap().values()) {
            builder.addVo(buildVipCardVO(entity));
        }
        builder.putAllReceiveInfo(vipCard.getReceiveInfoMap());
        builder.addAllReceiveScoreInfo(vipCard.getReceiveScoreInfoList());
        builder.putAllCumulativeRecharge(vipCard.getCumulativeRechargeMap());
        return builder.build();
    }

    public static VipCardVO buildVipCardVO(VipCardEntity entity) {
        VipCardVO.Builder builder = VipCardVO.newBuilder();
        builder.setType(entity.getType().getId());
        builder.setCreateTime(entity.getCreateTime());
        builder.setExpirationTime(entity.getExpirationTime());
        return builder.build();
    }

    public static ExtensionProtocol.FirstRechargeResponse buildFirstRechargeResponse(FirstRecharge firstRecharge) {
        ExtensionProtocol.FirstRechargeResponse.Builder builder = ExtensionProtocol.FirstRechargeResponse.newBuilder();
        long currentTimeMillis = System.currentTimeMillis();
        for (Map.Entry<Integer, Long> entry : firstRecharge.getFirstRechargeMap().entrySet()) {
            int day = DateUtils.getRemainDays(currentTimeMillis, entry.getValue()) + 1;
            builder.putFirstRechargeIndo(entry.getKey(), day);
        }
        builder.addAllReceive(firstRecharge.getReceiveList());
        return builder.build();
    }

    public static ExtensionProtocol.LimitedTimeChargeResponse buildLimitedTimeChargeResponse(Collection<LimitedTimeChargeEntity> list) {
        ExtensionProtocol.LimitedTimeChargeResponse.Builder builder = ExtensionProtocol.LimitedTimeChargeResponse.newBuilder();
        for (LimitedTimeChargeEntity entity : list) {
            builder.addEntities(buildLimitedTimeChargeEntity(entity));
        }
        return builder.build();
    }

    public static ExtensionProtocol.LimitedTimeChargeEntity buildLimitedTimeChargeEntity(LimitedTimeChargeEntity entity) {
        ExtensionProtocol.LimitedTimeChargeEntity.Builder builder = ExtensionProtocol.LimitedTimeChargeEntity.newBuilder();
        builder.setId(entity.getId());
        builder.setChargeState(entity.getChargeState());
        builder.setLimitedTime(entity.getLimitedTime());
        return builder.build();
    }

    public static ExtensionProtocol.FundResponse buildFundResponse(int type, Fund fund) {
        ExtensionProtocol.FundResponse.Builder builder = ExtensionProtocol.FundResponse.newBuilder();
        builder.putEntities(type, buildFundEntity(type, fund.getFundInfoMap().get(type)));
        return builder.build();
    }

    public static ExtensionProtocol.FundEntity buildFundEntity(int type, FundEntity entity) {
        ExtensionProtocol.FundEntity.Builder builder = ExtensionProtocol.FundEntity.newBuilder();
        builder.putAllIsActivate(entity.getActivateMap());
        for (Map.Entry<Integer, Collection<Integer>> entry : entity.getNormalReceiveMap().entrySet()) {
            CommonProtocol.IntListPacket intListPacket = CommonProtocol.IntListPacket.newBuilder().addAllList(entry.getValue()).build();
            builder.putNormalReceives(entry.getKey(), intListPacket);
        }
        for (Map.Entry<Integer, Collection<Integer>> entry : entity.getLuxuryReceiveMap().entrySet()) {
            CommonProtocol.IntListPacket intListPacket = CommonProtocol.IntListPacket.newBuilder().addAllList(entry.getValue()).build();
            builder.putLuxuryReceives(entry.getKey(), intListPacket);
        }
        int times = ref.get().fundFacade.getFundBuyTimes(type);
        builder.setBuyTimes(times);
        builder.addAllWelfareReceives(entity.getWelfareReceiveList());
        return builder.build();
    }

    /**
     * 月卡是否有效
     *
     * @param actorId
     * @param type
     * @return
     */
    public static boolean isVipCard(long actorId, VipCardType type) {
        TResult<VipCard> result = ref.get().vipCardFacade.getVipCard(actorId);
        if (result.isFail()) {
            return false;
        }
        Map<Integer, VipCardEntity> vipCardMap = result.item.getVipCardMap();
        if (!vipCardMap.containsKey(type.getId())) {
            return false;
        }
        VipCardEntity vipCardEntity = vipCardMap.get(type.getId());
        if (vipCardEntity.getExpirationTime() < System.currentTimeMillis()) {
            return false;
        }
        return true;
    }

    public static ExtensionProtocol.EnergyPrivilegeResponse buildEnergyPrivilegeResponse(Actor actor, DrawEnergy drawEnergy) {
        ExtensionProtocol.EnergyPrivilegeResponse.Builder builder = ExtensionProtocol.EnergyPrivilegeResponse.newBuilder();
        builder.setEnergyPools(actor.getEnergyPool());
        int energyReceive = Math.max(0, DrawEnergyConfigService.getMaxTimes() - drawEnergy.getYesterdayReceiveTimes());
        builder.setEnergyReceive(energyReceive);
        builder.setEnergyBuy(drawEnergy.getYesterdayBuyTimes());
        builder.setVipLevel(drawEnergy.getVipLevel());
        return builder.build();
    }

    public static ExtensionProtocol.DailyGiftInfoResponse buildDailyGiftInfoResponse(DailyGift dailyGift) {
        ExtensionProtocol.DailyGiftInfoResponse.Builder builder = ExtensionProtocol.DailyGiftInfoResponse.newBuilder();
        builder.addAllReceiveIds(dailyGift.getReceiveIds());
        builder.putAllReceiveMap(dailyGift.getReceiveMap());
        builder.setLastBuyTime(dailyGift.getLastBuyTime());
        builder.addAllDrawRecords(dailyGift.getDrawRecords());
        builder.setData(dailyGift.getData());
        builder.setRemainDrawTimes(dailyGift.getRemainDrawTimes());
        builder.setIsDraw(dailyGift.isDraw());
        return builder.build();
    }

    public static ExtensionProtocol.WeekGiftInfoResponse buildWeekGiftInfoResponse(int week, WeekGift weekGift) {
        ExtensionProtocol.WeekGiftInfoResponse.Builder builder = ExtensionProtocol.WeekGiftInfoResponse.newBuilder();
        builder.setWeek(week);
        builder.addAllReceive(weekGift.getReceiveReward());
        return builder.build();
    }

    public static ExtensionProtocol.RankTargetResponse buildRankTargetResponse(RankTargetActor rankTargetActor) {
        ExtensionProtocol.RankTargetResponse.Builder builder = ExtensionProtocol.RankTargetResponse.newBuilder();
        for (Integer type : RankRewardConfigService.getAllType()) {
            RankTargetGlobal rankTargetGlobal = ref.get().rankTargetGlobalDao.get(type);
            RankTargetInfo info = rankTargetActor.getInfo(type);
            builder.putRankReward(type, buildRankRewardInfo(info, rankTargetGlobal));
        }
        return builder.build();
    }

    public static ExtensionProtocol.RankRewardInfo buildRankRewardInfo(RankTargetInfo info, RankTargetGlobal rankTargetGlobal) {
        ExtensionProtocol.RankRewardInfo.Builder builder = ExtensionProtocol.RankRewardInfo.newBuilder();
        for (Map.Entry<Integer, Long> entry : rankTargetGlobal.getEntityMap().entrySet()) {
            boolean receive = false;
            if (info != null && info.getState(entry.getKey()) != null) {
                receive = info.getState(entry.getKey()).getReceive();
            }
            builder.addState(buildRankRewardState(entry.getKey(), receive, entry.getValue()));
        }
        return builder.build();
    }

    public static ExtensionProtocol.RankRewardState buildRankRewardState(int configId, boolean receive, long rankActorId) {
        ExtensionProtocol.RankRewardState.Builder builder = ExtensionProtocol.RankRewardState.newBuilder();
        builder.setConfigId(configId);
        builder.setReceive(receive);
        if (ActorHelper.isExist(rankActorId)) {
            builder.setActorProfile(ActorHelper.getActorProfile(rankActorId));
        } else {
            builder.setActorProfile(ref.get().rankTargetFacade.changeActorProfile(configId));
        }
        return builder.build();
    }

    public static ExtensionProtocol.ChargeReturnResponse buildChargeReturnResponse(ChargeReturnInfo chargeReturn) {
        Builder builder = ExtensionProtocol.ChargeReturnResponse.newBuilder();
        builder.setCharge(chargeReturn.getCharge());
        builder.setActorId(chargeReturn.getActorId());
        builder.setServerId(chargeReturn.getServerId());
        builder.setRewards(PbBuilder.buildRewardObjectList(chargeReturn.getRewardList()));
        builder.setLoginDay(chargeReturn.getLoginDay());
        return builder.build();
    }

    public static ExtensionProtocol.ThousandGachaResponse buildThousandGachaResponse(ThousandGacha thousandGacha) {
        ThousandGachaResponse.Builder builder = ExtensionProtocol.ThousandGachaResponse.newBuilder();
        builder.setDay(thousandGacha.getDay());
        builder.setReceive(thousandGacha.isReceive());
        return builder.build();
    }

    public static ExtensionProtocol.HeroLineupRecommendResponse buildHeroLineupRecommendResponse(HeroRecommend heroLineupRecommend) {
        HeroLineupRecommendResponse.Builder builder = ExtensionProtocol.HeroLineupRecommendResponse.newBuilder();
        builder.addAllReceive(heroLineupRecommend.getReceiveList());
        return builder.build();
    }

    @PostConstruct
    protected void init() {
        ref.set(this);
    }
}
