package cn.daxiang.hbtd.gameserver.module.system.parser.impl;

import cn.daxiang.hbtd.gameserver.core.GameConfig;
import cn.daxiang.hbtd.gameserver.core.database.UniqueId;
import cn.daxiang.hbtd.gameserver.module.system.parser.AbstractSettingKeyParser;
import cn.daxiang.protocol.game.TypeProtocol;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/8/4
 */
@Component
public class SystemMailMaxIdSettingParser extends AbstractSettingKeyParser {

    @Override
    protected TypeProtocol.SettingType getKey() {
        return TypeProtocol.SettingType.SYSTEM_MAIL_MAX_ID;
    }

    @Override
    public String initValue() {
        return String.valueOf(UniqueId.otherId(GameConfig.getServerId()));
    }

    @Override
    public Object parserValue(String value) {
        return Long.valueOf(value);
    }

    @Override
    public String toString(Object value) {
        return value.toString();
    }
}
