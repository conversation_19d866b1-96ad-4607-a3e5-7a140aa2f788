package cn.daxiang.hbtd.gameserver.module.teamOnlineTD.facade;

import cn.daxiang.framework.result.TResult;
import cn.daxiang.hbtd.gameserver.core.database.table.TeamOnlineTD;
import cn.daxiang.protocol.game.CommonProtocol.RewardResult;

/**
 * <AUTHOR>
 * @date 2020/12/26
 */
public interface TeamOnlineTDFacade {

    /**
     * 获取组队在线TD信息
     *
     * @param actorId
     * @return
     */
    TResult<TeamOnlineTD> getTeamOnlineTD(long actorId);

    /**
     * 领取关卡首次通关奖励
     *
     * @param actorId
     * @param id
     * @return
     */
    TResult<RewardResult> receivePassReward(long actorId, int id);

    /**
     * 战斗胜利
     *
     * @param actorId
     * @param id
     * @param wave
     * @param isDouble
     * @return
     */
    TResult<RewardResult> battle(long actorId, int id, int wave, boolean isDouble);
}
