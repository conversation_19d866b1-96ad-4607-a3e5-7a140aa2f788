package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.dataconfig.annotation.FieldIgnore;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.shared.reward.RewardObject;
import com.alibaba.fastjson.JSONArray;

/**
 * 百工作坊-道具回收表
 *
 * <AUTHOR>
 * @date 2022/8/27
 */
@DataFile(fileName = "item_recycle_config")
public class ItemRecycleConfig implements ModelAdapter {
    /**
     * 道具的id
     */
    private int itemID;
    /**
     * 单个物品可贡献的热度值
     */
    private int hotValue;
    /**
     * 回收物品可获得的补偿
     */
    private String feedback;
    /**
     * 返回的奖励
     */
    @FieldIgnore
    private RewardObject backReward;

    @Override
    public void initialize() {
        JSONArray array = JSONArray.parseArray(feedback);
        backReward = RewardObject.valueOf(array);
    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(itemID);
    }

    public int getItemID() {
        return itemID;
    }

    public int getHotValue() {
        return hotValue;
    }

    public RewardObject getBackReward(int num) {
        RewardObject rewardObject = RewardObject.valueOf(backReward.getType(), backReward.getId(), backReward.getCount() * num);
        return rewardObject;
    }
}
