package cn.daxiang.hbtd.gameserver.module.task.parser.impl;

import cn.daxiang.hbtd.gameserver.core.database.table.Task;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.TaskConfig;
import cn.daxiang.hbtd.gameserver.core.event.impl.HeroManualAchievementLevelUpEvent;
import cn.daxiang.hbtd.gameserver.module.task.parser.AbstractTaskParser;
import cn.daxiang.hbtd.gameserver.module.task.type.TaskConditionType;
import cn.daxiang.protocol.game.TaskProtocol;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/7/13
 */
@Component
public class HeroManualAchievementLevelUpTimesTaskParser extends AbstractTaskParser<HeroManualAchievementLevelUpEvent> {
    @Override
    public void init(Task task, TaskConfig taskConfig) {
        if (task.getValue() >= taskConfig.getValue()) {
            task.setStatus(TaskProtocol.TaskStatus.TASK_FINISH);
        }
    }

    @Override
    protected TaskConditionType getType() {
        return TaskConditionType.HERO_MANUAL_ACHIEVEMENT_LEVEL_UP_TIMES;
    }

    @Override
    protected boolean parseCondition(HeroManualAchievementLevelUpEvent event, Task task, TaskConfig taskConfig) {
        int addLevel = event.newLevel - event.oldLevel;
        task.setValue(task.getValue() + addLevel);
        if (task.getValue() >= taskConfig.getValue()) {
            task.setStatus(TaskProtocol.TaskStatus.TASK_FINISH);
        }
        return true;
    }

}
