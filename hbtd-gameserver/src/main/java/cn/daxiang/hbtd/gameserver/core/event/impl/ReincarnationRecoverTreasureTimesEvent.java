package cn.daxiang.hbtd.gameserver.core.event.impl;

import cn.daxiang.framework.event.ActorDelayEvent;
import cn.daxiang.shared.event.EventKey;
import com.google.common.base.Objects;

/**
 * 轮回战场恢复宝藏次数
 *
 * <AUTHOR>
 * @date 2023/12/23
 */
public class ReincarnationRecoverTreasureTimesEvent extends ActorDelayEvent {

    public ReincarnationRecoverTreasureTimesEvent(long actorId) {
        super(EventKey.REINCARNATION_RECOVER_TREASURE_TIMES_EVENT, actorId);
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(actorId, name);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        ReincarnationRecoverTreasureTimesEvent other = (ReincarnationRecoverTreasureTimesEvent) obj;
        if (actorId != other.actorId)
            return false;
        if (name != other.name)
            return false;
        return true;
    }
}
