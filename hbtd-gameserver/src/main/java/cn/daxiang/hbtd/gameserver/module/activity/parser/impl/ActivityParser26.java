package cn.daxiang.hbtd.gameserver.module.activity.parser.impl;

import cn.daxiang.framework.event.GameEvent;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.framework.utils.DateUtils;
import cn.daxiang.hbtd.gameserver.core.GameConfig;
import cn.daxiang.hbtd.gameserver.core.database.table.ActivityRecord;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.Activity26TokenStoreConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.ActivityOpenConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.Activity26ConfigService;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.ActivityDeleteGoodsConfigService;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.ActivityOpenConfigService;
import cn.daxiang.hbtd.gameserver.core.dispatch.DispatchHelper;
import cn.daxiang.hbtd.gameserver.core.event.impl.GoodsDeleteEvent;
import cn.daxiang.hbtd.gameserver.core.oss.type.OperationType;
import cn.daxiang.hbtd.gameserver.module.activity.model.entity.record.ActivityRecord26;
import cn.daxiang.hbtd.gameserver.module.activity.parser.AbstractActivityParser;
import cn.daxiang.hbtd.gameserver.module.activity.type.ActivityType;
import cn.daxiang.hbtd.gameserver.module.goods.facade.GoodsFacade;
import cn.daxiang.hbtd.gameserver.module.goods.helper.RewardHelper;
import cn.daxiang.protocol.game.ActivityInfoProtocol;
import cn.daxiang.protocol.game.CommonProtocol;
import cn.daxiang.shared.reward.RewardObject;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.protobuf.ByteString;
import com.google.protobuf.InvalidProtocolBufferException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Set;

import static cn.daxiang.shared.GameModuleStatusCodeConstant.ACTIVITY_REWARD_TIMES_LIMIT;
import static cn.daxiang.shared.GameModuleStatusCodeConstant.CONFIG_ERROR;
import static cn.daxiang.shared.GameModuleStatusCodeConstant.CONFIG_NOT_FOUND;
import static cn.daxiang.shared.GameModuleStatusCodeConstant.INVALID_PARAM;

/**
 * 天赐神将-代币商店
 */
@Component
public class ActivityParser26 extends AbstractActivityParser {
    @Autowired
    GoodsFacade goodsFacade;

    @Override
    public void registerEvent(Set<String> eventSet) {
        eventSet.addAll(getType().getEventNames());
    }

    @Override
    public TResult<CommonProtocol.RewardResult> receiveReward(long actorId, int activityId, int id, byte[] value) {
        ActivityOpenConfig activityOpenConfig = ActivityOpenConfigService.getActivityConfig(activityId);
        if (activityOpenConfig == null) {
            LOGGER.error("ActivityOpenConfig not found, id:{}", activityId);
            return TResult.valueOf(CONFIG_NOT_FOUND);
        }

        Activity26TokenStoreConfig config = globalConfigService.findConfig(IdentiyKey.build(activityOpenConfig.getData(), id), Activity26TokenStoreConfig.class);
        if (config == null) {
            return TResult.valueOf(CONFIG_ERROR);
        }
        int times = 1;
        try {
            times = CommonProtocol.IntPacket.parseFrom(value).getValue();
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("receiveReward byte value error");
            throw new RuntimeException(e);
        }
        if (times < 0 || times > GameConfig.getClientCountLimit()) {
            return TResult.valueOf(INVALID_PARAM);
        }
        ActivityRecord26 record;
        ActivityRecord activityRecord = activityRecordDao.getActivityRecord(actorId, activityId);
        if (activityRecord == null) {
            record = new ActivityRecord26();
            activityRecord = activityRecordDao.create(actorId, activityId, JSON.toJSONString(record));
        } else {
            record = JSON.parseObject(activityRecord.getRecord(), ActivityRecord26.class);
        }

        Integer buyTimes = record.getBuyTimes().getOrDefault(id, 0);
        if (buyTimes + times > config.getTimes()) {
            return TResult.valueOf(ACTIVITY_REWARD_TIMES_LIMIT);
        }
        Collection<RewardObject> costList = Lists.newLinkedList();
        Collection<RewardObject> rewardList = Lists.newLinkedList();
        for (int i = 0; i < times; i++) {
            costList.addAll(config.getCostList());
            rewardList.addAll(config.getRewardList());
        }
        rewardList = RewardHelper.groupByTypeAndId(rewardList);
        Result result = RewardHelper.decrease(actorId, costList, OperationType.ACTIVITY_TYPE_26);
        if (result.isFail()) {
            return TResult.valueOf(result);
        }
        record.buyTimes(id, times);
        CommonProtocol.RewardResult rewardResult = RewardHelper.sendRewardList(actorId, rewardList, OperationType.ACTIVITY_TYPE_26);
        activityRecord.setRecord(JSON.toJSONString(record));
        dbQueue.updateQueue(activityRecord);
        return TResult.sucess(rewardResult);

    }

    @Override
    public void onEvent(GameEvent event) {

    }

    @Override
    protected ActivityType getType() {
        return ActivityType.ACTIVITY_TYPE_26;
    }

    @Override
    protected void onActivityOpen(Collection<ActivityOpenConfig> openActivityIds) {

    }

    @Override
    protected void onActivityEnd(Collection<ActivityOpenConfig> endActivityIds) {
        for (ActivityOpenConfig activityOpenConfig : endActivityIds) {
            // 删除物品
            Collection<Integer> goodsIdList = ActivityDeleteGoodsConfigService.getGoodsIdList(activityOpenConfig.getActivityType());
            if (!goodsIdList.isEmpty()) {
                Collection<Long> goodsActorIds = goodsFacade.getActorIdsByGoodsId(goodsIdList);
                for (Long actorId : goodsActorIds) {
                    DispatchHelper.postEvent(new GoodsDeleteEvent(actorId, goodsIdList, OperationType.ACTIVITY_TYPE_26));
                }
            }
        }
    }

    @Override
    public TResult<ByteString> getRecord2Client(long actorId, int activityId) {
        ActivityOpenConfig activityOpenConfig = ActivityOpenConfigService.getActivityConfig(activityId);
        if (activityOpenConfig == null) {
            LOGGER.error("ActivityOpenConfig not found, id:{}", activityId);
            return TResult.valueOf(CONFIG_NOT_FOUND);
        }
        ActivityRecord activityRecord = activityRecordDao.getActivityRecord(actorId, activityId);
        ActivityInfoProtocol.Activity26Record.Builder builder = ActivityInfoProtocol.Activity26Record.newBuilder();
        if (activityRecord == null) {
            ActivityRecord26 record26 = ActivityRecord26.valueOf();
            activityRecord = activityRecordDao.create(actorId, activityId, JSON.toJSONString(activityRecord));
            activityRecord.setRecord(JSON.toJSONString(record26));
            dbQueue.updateQueue(activityRecord);
        }
        ActivityRecord26 record26 = JSON.parseObject(activityRecord.getRecord(), ActivityRecord26.class);
        if (!DateUtils.isToday(record26.getResetTime())) {
            Collection<Integer> list = Activity26ConfigService.getActivity26EverydayConfigId(activityOpenConfig.getData());
            if (list == null) {
                LOGGER.info("Activity26TokenStoreConfig type:1 not found data:{}", activityOpenConfig.getData());
            } else {
                record26.reset(list);
                activityRecord.setRecord(JSON.toJSONString(record26));
                dbQueue.updateQueue(activityRecord);
            }
        }

        builder.putAllBuyTimes(record26.getBuyTimes());
        return TResult.sucess(builder.build().toByteString());
    }

    @Override
    public TResult<ByteString> getGlobal2Client(long actorId, int activityId) {
        return TResult.fail();
    }

}
