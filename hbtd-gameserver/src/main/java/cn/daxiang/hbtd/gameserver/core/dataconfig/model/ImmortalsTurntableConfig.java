package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.identity.IdentiyKey;

/**
 * @author: <PERSON>
 * @date: 2023/2/17 9:39
 * @Description:
 */
@DataFile(fileName = "immortals_turntable_config")
public class ImmortalsTurntableConfig implements ModelAdapter {
    /**
     * 英雄Id
     */
    private int heroId;

    /**
     * 奖池id
     */
    private int poolId;

    @Override
    public void initialize() {

    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(heroId);
    }

    public int getHeroId() {
        return heroId;
    }

    public int getPoolId() {
        return poolId;
    }
}
