package cn.daxiang.hbtd.gameserver.module.activity.parser.impl;

import cn.daxiang.framework.event.GameEvent;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.hbtd.gameserver.core.database.table.ActivityGlobal;
import cn.daxiang.hbtd.gameserver.core.database.table.ActivityRecord;
import cn.daxiang.hbtd.gameserver.core.database.table.ImmortalsManual;
import cn.daxiang.hbtd.gameserver.core.dataconfig.convert.impl.IntLongMapConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.convert.impl.IntMapConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.ActivityOpenConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.ActivityOpeningCeremony3Config;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.ActivityOpenConfigService;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.ActivityOpeningCeremony3ConfigService;
import cn.daxiang.hbtd.gameserver.core.dispatch.DispatchHelper;
import cn.daxiang.hbtd.gameserver.core.event.impl.ArsenalChallengeEvent;
import cn.daxiang.hbtd.gameserver.core.event.impl.MailAddEvent;
import cn.daxiang.hbtd.gameserver.core.oss.type.OperationType;
import cn.daxiang.hbtd.gameserver.module.activity.model.Global19Rank;
import cn.daxiang.hbtd.gameserver.module.activity.model.entity.global.ActivityGlobal19;
import cn.daxiang.hbtd.gameserver.module.activity.model.entity.record.ActivityRecord19;
import cn.daxiang.hbtd.gameserver.module.activity.parser.AbstractActivityParser;
import cn.daxiang.hbtd.gameserver.module.activity.type.Activity19RankType;
import cn.daxiang.hbtd.gameserver.module.activity.type.Activity19TargetType;
import cn.daxiang.hbtd.gameserver.module.activity.type.ActivityType;
import cn.daxiang.hbtd.gameserver.module.goods.helper.RewardHelper;
import cn.daxiang.hbtd.gameserver.module.immortalsManual.dao.ImmortalsManualDao;
import cn.daxiang.hbtd.gameserver.module.immortalsManual.facade.ImmortalsManualFacade;
import cn.daxiang.hbtd.gameserver.module.immortalsManual.model.ImmortalsManualRank;
import cn.daxiang.hbtd.gameserver.module.mail.type.MailTemplateType;
import cn.daxiang.hbtd.gameserver.module.user.helper.ActorHelper;
import cn.daxiang.protocol.game.ActivityInfoProtocol;
import cn.daxiang.protocol.game.CommonProtocol;
import cn.daxiang.shared.GlobalConfigKey;
import cn.daxiang.shared.reward.RewardObject;
import cn.daxiang.shared.type.QualityType;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.protobuf.ByteString;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;

import static cn.daxiang.shared.GameModuleStatusCodeConstant.ACTIVITY_NOT_FINISH_FOR_RECEIVED;
import static cn.daxiang.shared.GameModuleStatusCodeConstant.ACTIVITY_REWARD_HAD_RECEIVED;
import static cn.daxiang.shared.GameModuleStatusCodeConstant.ACTIVITY_STATUS_ERROR;
import static cn.daxiang.shared.GameModuleStatusCodeConstant.CONFIG_NOT_FOUND;
import static cn.daxiang.shared.GameModuleStatusCodeConstant.INVALID_PARAM;

/**
 * 群雄逐鹿3
 *
 * <AUTHOR>
 * @date 2022/8/30
 */
@Component
public class ActivityParser19 extends AbstractActivityParser {
    @Autowired
    private ImmortalsManualFacade immortalsManualFacade;
    @Autowired
    private ImmortalsManualDao immortalsManualDao;

    @Override
    public TResult<CommonProtocol.RewardResult> receiveReward(long actorId, int activityId, int id, byte[] value) {
        ActivityOpenConfig activityOpenConfig = ActivityOpenConfigService.getActivityConfig(activityId);
        if (activityOpenConfig == null) {
            LOGGER.error("ActivityOpenConfig not found, id:{}", activityId);
            return TResult.valueOf(CONFIG_NOT_FOUND);
        }
        ActivityOpeningCeremony3Config config = globalConfigService.findConfig(IdentiyKey.build(activityOpenConfig.getData(), id), ActivityOpeningCeremony3Config.class);
        if (config == null) {
            LOGGER.error("ActivityOpeningCeremony3Config not found, activityId:{} data:{} id:{}", activityId, activityOpenConfig.getData(), id);
            return TResult.valueOf(CONFIG_NOT_FOUND);
        }

        if (config.getType() != Activity19TargetType.IMMORTALS_MANUAL_VALUE_TARGET.getId() && config.getType() != Activity19TargetType.ARSENAL_CHALLENGE_TIMES.getId()) {
            LOGGER.error("id error, activityId:{} data:{} id:{}", activityId, activityOpenConfig.getData(), id);
            return TResult.valueOf(CONFIG_NOT_FOUND);
        }

        ActivityGlobal activityGlobal = activityGlobalDao.getActivityGlobal(activityId);
        if (activityGlobal == null) {
            return TResult.valueOf(INVALID_PARAM);
        }
        if (System.currentTimeMillis() > activityGlobal.getShowTime() + 1000) {
            return TResult.valueOf(ACTIVITY_STATUS_ERROR);
        }

        ActivityRecord19 activityRecord;
        ActivityRecord record = activityRecordDao.getActivityRecord(actorId, activityId);
        if (record == null) {
            activityRecord = new ActivityRecord19();
            record = activityRecordDao.create(actorId, activityId, JSON.toJSONString(activityRecord));
        } else {
            activityRecord = JSON.parseObject(record.getRecord(), ActivityRecord19.class);
        }

        if (activityRecord.getReceives().contains(id)) {
            return TResult.valueOf(ACTIVITY_REWARD_HAD_RECEIVED);
        }

        if (config.getType() == Activity19TargetType.IMMORTALS_MANUAL_VALUE_TARGET.getId()) {
            int immortalsManualValue = 0;
            TResult<ImmortalsManual> immortalsManual = immortalsManualFacade.getImmortalsManual(actorId);
            if (immortalsManual.isOk()) {
                immortalsManualValue = immortalsManual.item.getManualValue();
            }
            if (immortalsManualValue < config.getTarget()) {
                return TResult.valueOf(ACTIVITY_NOT_FINISH_FOR_RECEIVED);
            }
        }

        if (config.getType() == Activity19TargetType.ARSENAL_CHALLENGE_TIMES.getId()) {
            int arsenalChallengeTimes = activityRecord.getArsenalChallengeTimes();
            if (arsenalChallengeTimes < config.getTarget()) {
                return TResult.valueOf(ACTIVITY_NOT_FINISH_FOR_RECEIVED);
            }
        }

        activityRecord.addReceives(id);
        record.setRecord(JSON.toJSONString(activityRecord));
        dbQueue.updateQueue(record);

        CommonProtocol.RewardResult result = RewardHelper.sendRewardList(actorId, config.getRewardList(), OperationType.ACTIVITY_TYPE_19);
        return TResult.sucess(result);
    }

    @Override
    public void registerEvent(Set<String> eventSet) {
        eventSet.addAll(getType().getEventNames());
    }

    @Override
    public void onEvent(GameEvent event) {
        ArsenalChallengeEvent arsenalChallengeEvent = event.convert();
        Collection<ActivityOpenConfig> configList = ActivityOpenConfigService.getActivityOpenConfigList(getType());
        if (configList.isEmpty()) {
            return;
        }
        if (arsenalChallengeEvent.quality < QualityType.EPIC.getId()) {
            return;
        }
        long actorId = arsenalChallengeEvent.getActorId();
        for (ActivityOpenConfig activityOpenConfig : configList) {
            int activityId = activityOpenConfig.getId();
            if (isActivityOpen(activityId) == false) {
                continue;
            }
            ActivityRecord19 activityRecord;
            ActivityRecord record = activityRecordDao.getActivityRecord(actorId, activityId);
            if (record == null) {
                activityRecord = new ActivityRecord19();
                record = activityRecordDao.create(actorId, activityId, JSON.toJSONString(activityRecord));
            } else {
                activityRecord = JSON.parseObject(record.getRecord(), ActivityRecord19.class);
            }

            activityRecord.addArsenalChallengeTimes();
            record.setRecord(JSON.toJSONString(activityRecord));
            dbQueue.updateQueue(record);
            this.pushActivity(actorId, activityId, activityOpenConfig.getActivityType());
        }
    }

    @Override
    protected ActivityType getType() {
        return ActivityType.ACTIVITY_TYPE_19;
    }

    @Override
    protected void onActivityOpen(Collection<ActivityOpenConfig> openActivityIds) {

    }

    @Override
    protected void onActivityEnd(Collection<ActivityOpenConfig> endActivityIds) {

    }

    @Override
    protected void onActivityShow(Collection<ActivityOpenConfig> endActivityIds) {
        for (ActivityOpenConfig activityOpenConfig : endActivityIds) {
            sendRankReward(activityOpenConfig.getId(), activityOpenConfig.getData());
        }
    }

    @Override
    public TResult<ByteString> getRecord2Client(long actorId, int activityId) {
        ActivityInfoProtocol.Activity19Record.Builder builder = ActivityInfoProtocol.Activity19Record.newBuilder();
        TResult<ImmortalsManual> immortalsManual = immortalsManualFacade.getImmortalsManual(actorId);
        if (immortalsManual.isOk()) {
            int immortalsManualValue = immortalsManual.item.getManualValue();
            builder.setImmortalsManualValue(immortalsManualValue);
        }
        ActivityRecord19 record;
        ActivityRecord activityRecord = activityRecordDao.getActivityRecord(actorId, activityId);
        if (activityRecord != null) {
            record = JSON.parseObject(activityRecord.getRecord(), ActivityRecord19.class);
            builder.setArsenalChallengeTimes(record.getArsenalChallengeTimes());
            builder.addAllReceives(record.getReceives());
        }
        return TResult.sucess(builder.build().toByteString());
    }

    @Override
    public TResult<ByteString> getGlobal2Client(long actorId, int activityId) {
        ActivityOpenConfig activityOpenConfig = ActivityOpenConfigService.getActivityConfig(activityId);
        if (activityOpenConfig == null) {
            return TResult.fail();
        }

        ActivityGlobal activityGlobal = activityGlobalDao.getActivityGlobal(activityId);
        if (activityGlobal == null) {
            return TResult.fail();
        }

        ActivityInfoProtocol.Activity19Global.Builder builder = ActivityInfoProtocol.Activity19Global.newBuilder();
        for (Activity19RankType rankType : Activity19RankType.values()) {
            Collection<Global19Rank> rankList;
            if (rankType == Activity19RankType.NONE) {
                continue;
            }
            if (System.currentTimeMillis() > activityGlobal.getShowTime()) {
                ActivityGlobal19 global = JSON.parseObject(activityGlobal.getGlobalRecord(), ActivityGlobal19.class);
                if (global == null) {
                    continue;
                }
                rankList = global.getGlobal19RankMap().get(rankType.getId());
            } else {
                rankList = getGlobal19RankMap(rankType);
                long rank = 0;
                long value = 0;
                switch (rankType) {
                    case IMMORTALS_MANUAL_VALUE_RANK:
                        ImmortalsManualRank immortalsManualRank = immortalsManualDao.getImmortalsManualRank(actorId);
                        if (immortalsManualRank != null) {
                            rank = immortalsManualRank.getRank();
                            value = immortalsManualRank.getManualValue();
                        }
                        break;
                    default:
                        break;
                }
                Global19Rank global19Rank = Global19Rank.valueOf(actorId, rank, value);
                rankList.add(global19Rank);
            }

            for (Global19Rank global19Rank : rankList) {
                ActivityInfoProtocol.Activity19Rank.Builder rankBuilder = ActivityInfoProtocol.Activity19Rank.newBuilder();
                rankBuilder.setActorProfile(ActorHelper.getActorProfile(global19Rank.getActorId()));
                rankBuilder.setRank(global19Rank.getRank());
                rankBuilder.setValue(global19Rank.getValue());
                if (rankType == Activity19RankType.IMMORTALS_MANUAL_VALUE_RANK) {
                    builder.addImmortalsManualValueRank(rankBuilder.build());
                }
            }
        }

        return TResult.sucess(builder.build().toByteString());
    }

    private Collection<Global19Rank> getGlobal19RankMap(Activity19RankType rankType) {
        IntMapConfig saveLimitMap = globalConfigService.findGlobalObject(GlobalConfigKey.ACTIVITY_12_RANK_NUM_LIMIT, IntMapConfig.class);
        IntLongMapConfig rankLimitMap = globalConfigService.findGlobalObject(GlobalConfigKey.ACTIVITY_12_OPENING_CEREMONY_MIN_LIMIT, IntLongMapConfig.class);
        int limit = saveLimitMap.getMap().get(rankType.getId());
        long valueLimit = rankLimitMap.getMap().get(rankType.getId());
        Collection<Global19Rank> rankList = Lists.newArrayList();
        for (int i = 1; i <= limit; i++) {
            long rankActorId;
            long value;
            switch (rankType) {
                case IMMORTALS_MANUAL_VALUE_RANK:
                    ImmortalsManualRank immortalsManualRank = immortalsManualDao.getImmortalsManualRankByRank(i);
                    if (immortalsManualRank == null) {
                        continue;
                    }
                    rankActorId = immortalsManualRank.getKey();
                    value = immortalsManualRank.getManualValue();
                    break;
                default:
                    return rankList;
            }
            // 未达到上榜条件,不上榜
            if (value < valueLimit) {
                break;
            }
            Global19Rank global19Rank = Global19Rank.valueOf(rankActorId, i, value);
            rankList.add(global19Rank);
        }
        return rankList;
    }

    private void sendRankReward(int activityId, int data) {
        // 排行榜上角色会存数据库展示时间展示
        for (Activity19RankType rankType : Activity19RankType.values()) {
            if (rankType == Activity19RankType.NONE) {
                continue;
            }
            TreeMap<Long, ActivityOpeningCeremony3Config> powerRankConfigMap = ActivityOpeningCeremony3ConfigService.getRankConfigMap(data, rankType);
            if (powerRankConfigMap == null) {
                continue;
            }

            // 获取排行结算
            ActivityGlobal activityGlobal = activityGlobalDao.getActivityGlobal(activityId);
            ActivityGlobal19 global = JSON.parseObject(activityGlobal.getGlobalRecord(), ActivityGlobal19.class);
            if (global == null) {
                global = new ActivityGlobal19();
            }
            if (global.getGlobal19RankMap().keySet().contains(rankType.getId())) {
                // 已发放
                continue;
            }

            Collection<Global19Rank> rankList = this.getGlobal19RankMap(rankType);
            global.writeRecord(rankType.getId(), rankList);
            activityGlobal.setGlobalRcord(JSON.toJSONString(global));
            dbQueue.updateQueue(activityGlobal);
            for (Global19Rank global19Rank : rankList) {
                long actorId = global19Rank.getActorId();
                if (ActorHelper.isRobot(actorId)) {
                    continue;
                }
                long rank = global19Rank.getRank();

                Map.Entry<Long, ActivityOpeningCeremony3Config> entity = powerRankConfigMap.ceilingEntry(rank);
                if (entity == null) {
                    LOGGER.error("ActivityOpeningCeremony3Config isEmpty rank:{}", rank);
                    continue;
                }
                Collection<RewardObject> rewardList = entity.getValue().getRewardList();
                if (rewardList.isEmpty()) {
                    LOGGER.error("rewardList isEmpty rank:{}", rank);
                    continue;
                }
                sendRankActivityRewardMail(actorId, rankType, rank, rewardList);
            }
        }
        // 超过排行榜角色，直接发奖，不存数据库不展示
        for (Activity19RankType rankType : Activity19RankType.values()) {
            if (rankType == Activity19RankType.NONE) {
                continue;
            }
            IntMapConfig limitMap = globalConfigService.findGlobalObject(GlobalConfigKey.ACTIVITY_12_RANK_NUM_LIMIT, IntMapConfig.class);
            int initRank = limitMap.getMap().get(rankType.getId()) + 1;

            IntLongMapConfig rankLimitMap = globalConfigService.findGlobalObject(GlobalConfigKey.ACTIVITY_12_OPENING_CEREMONY_MIN_LIMIT, IntLongMapConfig.class);

            TreeMap<Long, ActivityOpeningCeremony3Config> rankConfigMap = ActivityOpeningCeremony3ConfigService.getRankConfigMap(data, rankType);
            if (rankConfigMap == null) {
                continue;
            }
            Map.Entry<Long, ActivityOpeningCeremony3Config> powerRankConfigEntry = rankConfigMap.lastEntry();
            ActivityOpeningCeremony3Config ActivityOpeningCeremony3Config = powerRankConfigEntry.getValue();
            long maxRank = ActivityOpeningCeremony3Config.getRank();

            for (int i = initRank; i <= maxRank; i++) {
                long rankActorId = 0;
                long rankValue = 0;
                switch (rankType) {
                    case IMMORTALS_MANUAL_VALUE_RANK:
                        ImmortalsManualRank immortalsManualRank = immortalsManualDao.getImmortalsManualRankByRank(i);
                        if (immortalsManualRank == null) {
                            continue;
                        }
                        rankActorId = immortalsManualRank.getKey();
                        rankValue = immortalsManualRank.getManualValue();
                        break;
                    default:
                        break;
                }
                // 未达到上榜要求不发邮件
                long power = rankLimitMap.getMap().get(rankType.getId());
                if (rankValue < power) {
                    break;
                }
                if (ActorHelper.isRobot(rankActorId)) {
                    continue;
                }
                Map.Entry<Long, ActivityOpeningCeremony3Config> entity = rankConfigMap.ceilingEntry((long) i);
                if (entity == null || entity.getValue() == null) {
                    LOGGER.error("ActivityOpeningCeremony3Config isEmpty rank:{}", i);
                    continue;
                }
                if (entity.getValue().getRewardList().isEmpty()) {
                    LOGGER.error("rewardList isEmpty rank:{}", i);
                    continue;
                }
                sendRankActivityRewardMail(rankActorId, rankType, i, entity.getValue().getRewardList());
            }
        }
    }

    private void sendRankActivityRewardMail(long actorId, Activity19RankType rankType, long rank, Collection<RewardObject> rewards) {
        MailTemplateType mailTemplateType = null;
        if (rankType == Activity19RankType.IMMORTALS_MANUAL_VALUE_RANK) {
            mailTemplateType = MailTemplateType.ACTIVITY_19_MANUAL_VALUE_RANK;
        }
        Map<String, String> params = Maps.newHashMap();
        params.put("name", ActorHelper.getActorName(actorId));
        params.put("rank", String.valueOf(rank));
        MailAddEvent mailAddEvent = new MailAddEvent(actorId, mailTemplateType, params, rewards);
        DispatchHelper.postEvent(mailAddEvent);
        LOGGER.info("Send {} complete, rankType:{}, actorId:{}", mailTemplateType, rankType, actorId);
    }
}
