package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.dataconfig.annotation.FieldIgnore;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.framework.utils.StringUtils;
import cn.daxiang.shared.reward.RewardObject;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * 河图收集表
 */
@DataFile(fileName = "artifact_gather_config")
public class ArtifactGatherConfig implements ModelAdapter {
    /**
     * id
     */
    private int starID;
    /**
     * 星宿类型
     * 1=白虎
     * 2=朱雀
     * 3=青龙
     * 4=玄武
     * 5=麒麟
     */
    private int type;
    /**
     * 激活消耗
     */
    private String activeCost;
    /**
     * 特殊属性配置---读特殊属性表
     */
    private String specialEffectId;
    /**
     * 解锁条件，填主线关卡配置id
     */
    private int condition;
    /**
     * 全激活奖励属性，配置在每个星宿组rank为1的星宿上
     */
    private String completeEffectID;
    /**
     * 消耗列表
     */
    @FieldIgnore
    private List<RewardObject> costList = Lists.newArrayList();

    @FieldIgnore
    private List<Integer> specialEffectIds = Lists.newArrayList();
    @FieldIgnore
    private List<Integer> completeEffectIDs = Lists.newArrayList();

    @Override
    public void initialize() {
        JSONArray costArray = JSONArray.parseArray(activeCost);
        RewardObject rewardObject = RewardObject.valueOf(costArray);
        costList.add(rewardObject);
        if (StringUtils.isNotBlank(specialEffectId)) {
            specialEffectIds.addAll(JSONArray.parseArray(specialEffectId, Integer.class));
        }
        completeEffectIDs.addAll(JSONArray.parseArray(completeEffectID, Integer.class));
    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(starID);
    }

    public int getStarID() {
        return starID;
    }

    public int getType() {
        return type;
    }

    public List<RewardObject> getCostList() {
        return costList;
    }

    public List<Integer> getSpecialEffectIds() {
        return specialEffectIds;
    }

    public int getCondition() {
        return condition;
    }

    public List<Integer> getCompleteEffectIDs() {
        return completeEffectIDs;
    }
}
