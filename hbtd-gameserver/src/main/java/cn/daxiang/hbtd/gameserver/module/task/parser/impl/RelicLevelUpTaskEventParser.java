package cn.daxiang.hbtd.gameserver.module.task.parser.impl;

import cn.daxiang.framework.utils.FormulaUtils;
import cn.daxiang.hbtd.gameserver.core.database.table.RelicLineup;
import cn.daxiang.hbtd.gameserver.core.database.table.Task;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.TaskConfig;
import cn.daxiang.hbtd.gameserver.core.event.impl.RelicLevelUpEvent;
import cn.daxiang.hbtd.gameserver.module.relic.dao.RelicLineupDao;
import cn.daxiang.hbtd.gameserver.module.relic.model.RelicInfo;
import cn.daxiang.hbtd.gameserver.module.task.parser.AbstractTaskParser;
import cn.daxiang.hbtd.gameserver.module.task.type.TaskConditionType;
import cn.daxiang.protocol.game.TaskProtocol;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;

/**
 * @author: Gary
 * @date: 2023/2/6 10:38
 * @Description:
 */
@Component
public class RelicLevelUpTaskEventParser extends AbstractTaskParser<RelicLevelUpEvent> {
    @Autowired
    private RelicLineupDao relicLineupDao;

    @Override
    protected TaskConditionType getType() {
        return TaskConditionType.RELIC_LEVEL;
    }

    @Override
    protected boolean parseCondition(RelicLevelUpEvent event, Task task, TaskConfig taskConfig) {
        if (!FormulaUtils.executeBool(taskConfig.getCondition(), event.relicLevel)) {
            return false;
        }
        parser(task, taskConfig);
        return true;
    }

    @Override
    public void init(Task task, TaskConfig taskConfig) {
        task.setValue(0);
        parser(task, taskConfig);
    }

    private void parser(Task task, TaskConfig taskConfig) {
        RelicLineup relicLineup = relicLineupDao.getRelicLineup(task.getActorId());
        int value = 0;
        for (Collection<RelicInfo> relicInfos : relicLineup.getRelicLineupMap().values()) {
            for (RelicInfo relicInfo : relicInfos) {
                if (!FormulaUtils.executeBool(taskConfig.getCondition(), relicInfo.getLevel())) {
                    continue;
                }
                value++;
                if (value >= taskConfig.getValue()) {
                    break;
                }
            }
        }
        if (task.getValue() < value) {
            task.setValue(value);
        }
        if (task.getValue() >= taskConfig.getValue()) {
            task.setStatus(TaskProtocol.TaskStatus.TASK_FINISH);
        }
    }

}
