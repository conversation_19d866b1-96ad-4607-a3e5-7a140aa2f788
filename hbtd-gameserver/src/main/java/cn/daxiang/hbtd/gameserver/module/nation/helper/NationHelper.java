package cn.daxiang.hbtd.gameserver.module.nation.helper;

import cn.daxiang.dto.request.ChangeNationHeaderRequest;
import cn.daxiang.dto.response.NationMemberInfoResponse;
import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.framework.utils.ObjectReference;
import cn.daxiang.framework.utils.StringUtils;
import cn.daxiang.hbtd.gameserver.core.database.table.NationActor;
import cn.daxiang.hbtd.gameserver.module.PbBuilder;
import cn.daxiang.hbtd.gameserver.module.nation.facade.NationFacade;
import cn.daxiang.protocol.game.NationProtocol;
import cn.daxiang.shared.module.nation.Nation;
import cn.daxiang.shared.module.nation.NationDungeonActorDamageRankVO;
import cn.daxiang.shared.module.nation.NationDungeonNationDamageRankVO;
import cn.daxiang.shared.module.nation.NationPowerRankVO;
import cn.daxiang.shared.module.nation.NationSimpleVO;
import cn.daxiang.shared.module.nation.entity.BoxInfo;
import cn.daxiang.shared.module.nation.entity.NationApplyEntity;
import cn.daxiang.shared.module.nation.entity.NationDonateEntity;
import cn.daxiang.shared.module.nation.entity.NationDungeonActorDamageRankResponse;
import cn.daxiang.shared.module.nation.entity.NationDungeonNationDamageRankResponse;
import cn.daxiang.shared.module.nation.entity.NationMemberEntity;
import cn.daxiang.shared.module.nation.entity.NationPowerRankResponse;
import cn.daxiang.shared.module.nation.entity.NationRecordEntity;
import cn.daxiang.shared.module.nation.entity.NationRecycleEntity;
import cn.daxiang.shared.module.nation.entity.StoryBoxInfo;
import cn.daxiang.shared.type.NationLimitType;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Collection;
import java.util.Collections;
import java.util.Map;
import java.util.Set;

@Component
public class NationHelper {
    private static final ObjectReference<NationHelper> ref = new ObjectReference<>();

    private static final Map<Long, String> NATION_NAME_MAP = Maps.newConcurrentMap();

    @Autowired
    private NationFacade nationFacade;

    /**
     * 获取国家ID
     *
     * @param actorId
     * @return
     */
    public static long getNationId(long actorId) {
        if (actorId < 0) {
            return 0;
        }
        TResult<NationActor> nationActor = ref.get().nationFacade.getNationActor(actorId);
        return nationActor.item.getNationId();
    }

    /**
     * 获取国家等级
     *
     * @param actorId
     * @return
     */
    public static int getNationLevel(long actorId) {
        TResult<Nation> nationResult = ref.get().nationFacade.getNationByActorId(actorId);
        if (nationResult.isFail()) {
            return 0;
        }
        return nationResult.item.getLevel();
    }

    /**
     * 获取国家名称
     *
     * @param actorId
     * @return
     */
    public static String getNationName(long actorId) {
        if (actorId < 0) {
            return "";
        }
        TResult<NationActor> nationActorResult = ref.get().nationFacade.getNationActor(actorId);
        if (nationActorResult.isFail()) {
            return "";
        }
        long nationId = nationActorResult.item.getNationId();
        if (nationId == 0) {
            return "";
        }
        String nationName = NATION_NAME_MAP.get(nationId);
        if (nationName != null) {
            return nationName;
        } else {
            nationName = getNationNameByNationId(nationId);
            if (StringUtils.isBlank(nationName)) {
                return "";
            }
            NATION_NAME_MAP.put(nationId, nationName);
        }
        return nationName;
    }

    /**
     * 清除缓存军团名字
     *
     * @param nationId
     */
    public static void cleanNationName(long nationId) {
        NATION_NAME_MAP.remove(nationId);
    }

    /**
     * 获取国家旗帜id
     *
     * @param actorId
     * @return
     */

    public static int getNationFlagId(long actorId) {
        TResult<Nation> nationResult = ref.get().nationFacade.getNationByActorId(actorId);
        if (nationResult.isFail()) {
            return 0;
        }
        return nationResult.item.getFlagId();
    }

    /**
     * 获取国家内的所有玩家ID
     *
     * @param nationId
     * @return
     */
    public static Collection<Long> getNationMerberIds(long nationId) {
        Collection<Long> list = Lists.newArrayList();
        TResult<Nation> nationResult = ref.get().nationFacade.getNation(nationId);
        if (nationResult.isFail()) {
            return Collections.emptyList();
        }
        list.addAll(nationResult.item.getMemberIds());
        return list;
    }

    /**
     * 获取国家创建时间
     *
     * @param nationId
     * @return
     */
    public static long getNationCreateTime(long nationId) {
        TResult<Nation> nationResult = ref.get().nationFacade.getNation(nationId);
        if (nationResult.isFail()) {
            return 0;
        }
        return nationResult.item.getCreateTime();
    }

    /**
     * 根据国家ID获取国家名称
     *
     * @param
     * @return
     */
    public static String getNationNameByNationId(long nationId) {
        TResult<Nation> nationResult = ref.get().nationFacade.getNation(nationId);
        if (nationResult.isFail()) {
            return "";
        }
        return nationResult.item.getName();
    }

    /**
     * 获取玩家加入国家时间
     *
     * @param
     * @return
     */
    public static long getNationActorJoinTime(long actorId) {
        TResult<NationActor> result = ref.get().nationFacade.getNationActor(actorId);
        if (result.isFail()) {
            return 0;
        }
        return result.item.getJoinTime();
    }

    public static Collection<NationProtocol.NationDonateEntity> buildNationDonateEntityList(Collection<NationDonateEntity> entityList) {
        Collection<NationProtocol.NationDonateEntity> nationDonateEntities = Lists.newLinkedList();
        for (NationDonateEntity entity : entityList) {
            NationProtocol.NationDonateEntity.Builder builder = NationProtocol.NationDonateEntity.newBuilder();
            builder.setActorProfile(PbBuilder.buildActorProfile(entity.getAttributes()));
            builder.setExp(entity.getExp());
            builder.setTime(entity.getTime());
            nationDonateEntities.add(builder.build());
        }

        return nationDonateEntities;
    }

    public static NationProtocol.NationMemberResponse buildNationMemberResponse(Collection<NationMemberEntity> entityList) {
        NationProtocol.NationMemberResponse.Builder builder = NationProtocol.NationMemberResponse.newBuilder();
        for (NationMemberEntity entity : entityList) {
            NationProtocol.NationMemberEntity nationMemberEntity = NationProtocol.NationMemberEntity.newBuilder().setTargeter(PbBuilder.buildActorProfile(entity.getAttributes()))
                .setMemberType(NationProtocol.NationMemberType.forNumber(entity.getMemberType().getId())).setBuildScore(entity.getBuildScore())
                .setBuildTotalScore(entity.getBuildTotalScore()).build();
            builder.addMembers(nationMemberEntity);
        }
        return builder.build();
    }

    public static NationProtocol.NationRecordResponse buildNationRecordResponse(Collection<NationRecordEntity> entityList) {
        NationProtocol.NationRecordResponse.Builder builder = NationProtocol.NationRecordResponse.newBuilder();
        for (NationRecordEntity entity : entityList) {
            NationProtocol.NationRecordEntity nationMemberEntity =
                NationProtocol.NationRecordEntity.newBuilder().setRecordType(NationProtocol.NationRecordType.forNumber(entity.getRecordType().getId()))
                    .setKing(PbBuilder.buildActorProfile(entity.getKing())).setTargeter(PbBuilder.buildActorProfile(entity.getTargeter()))
                    .setMemberType(NationProtocol.NationMemberType.forNumber(entity.getMemberType().getId())).setTime(entity.getTime()).setExtra(entity.getExtra()).build();
            builder.addRecords(nationMemberEntity);
        }
        return builder.build();
    }

    public static NationProtocol.NationActorResponse buildNationActorResponse(NationActor nationActor) {
        NationProtocol.NationActorResponse.Builder builder = NationProtocol.NationActorResponse.newBuilder();
        builder.setMemberType(nationActor.getType());
        builder.addAllAppliedList(nationActor.getAppliedList());
        builder.putAllSciTechmap(nationActor.getSciTechMap());
        builder.addAllDonateIds(nationActor.getDonateIdList());
        builder.addAllDonateReceiveList(nationActor.getDonateReceiveList());
        builder.setNationDungeonTimes(nationActor.getNationDungeonTimes());
        builder.setNationDungeonBuyTimes(nationActor.getNationDungeonBuyTimes());
        builder.addAllStoryReceives(nationActor.getStoryReceiveList());
        builder.addAllChapterReceives(nationActor.getChapterReceiveList());
        builder.setLastCreateTime(nationActor.getLastCreateTime());
        builder.setLastExitTime(nationActor.getLastExitTime());
        builder.addAllRecycleReceive(nationActor.getRecycleReceiveList());
        builder.setBuildRunesTimes(nationActor.getBuildRunesTimes());
        return builder.build();
    }

    public static NationProtocol.NationInfoResponse buildNationInfoResponse(Nation nation) {
        NationProtocol.NationInfoResponse.Builder builder = NationProtocol.NationInfoResponse.newBuilder();
        builder.setVo(buildNationVO(nation, Maps.newHashMap()));
        builder.setExp(nation.getExp());
        builder.setNotice(nation.getNotice());
        builder.setAppointNextTime(nation.getNextAppointTime());
        builder.setNoticeNextTime(nation.getNextNoticeTime());
        builder.setRenameNextTime(nation.getNextRenameTime());
        for (NationApplyEntity entity : nation.getApplyList()) {
            NationProtocol.ApplyList applyList =
                NationProtocol.ApplyList.newBuilder().setApplyTime(entity.getApplyTime()).setActorProfile(PbBuilder.buildActorProfile(entity.getApplyActor())).build();
            builder.addApplyList(applyList);
        }
        builder.setKickOffCount(nation.getKickOffCount());
        builder.setDonateProgress(nation.getDonateProgress());
        builder.setDayDonateMaxExp(nation.getDayDonateMaxExp());
        builder.addAllDonateList(buildNationDonateEntityList(nation.getDonateList()));
        builder.setMailNextTime(nation.getNextMailTime());
        builder.setRecruitDeclaration(nation.getRecruitDeclaration());
        builder.setRecruitNextTime(nation.getRecruitNextTime());
        for (Map.Entry<Integer, StoryBoxInfo> entry : nation.getAllBoxStates().entrySet()) {
            builder.putAllBoxStates(entry.getKey(), buildStoryBoxInfo(entry.getValue()));
        }
        builder.setRecycleProgress(nation.getRecycleValue());
        NationRecycleEntity nationRecycleEntity = null;
        for (NationRecycleEntity entity : nation.getRecycleMap().values()) {
            if (nationRecycleEntity == null) {
                nationRecycleEntity = entity;
            }
            if (entity.getRecycleValue() > nationRecycleEntity.getRecycleValue()) {
                nationRecycleEntity = entity;
            }
        }
        if (nationRecycleEntity != null) {
            builder.setRecycleMVP(PbBuilder.buildActorProfile(nationRecycleEntity.getAttributes()));
        }
        return builder.build();
    }

    public static NationProtocol.storyBoxInfo buildStoryBoxInfo(StoryBoxInfo info) {
        NationProtocol.storyBoxInfo.Builder builder = NationProtocol.storyBoxInfo.newBuilder();
        for (Map.Entry<Integer, BoxInfo> entry : info.getBoxStates().entrySet()) {
            builder.putBoxStates(entry.getKey(), buildBoxInfo(entry.getValue()));
        }
        return builder.build();
    }

    public static NationProtocol.boxInfo buildBoxInfo(BoxInfo info) {
        NationProtocol.boxInfo.Builder builder = NationProtocol.boxInfo.newBuilder();
        builder.setActorProfile(PbBuilder.buildActorProfile(info.getActorAttributes()));
        builder.setReward(PbBuilder.buildRewardObject(info.getRewardObject()));
        return builder.build();
    }

    public static NationProtocol.NationVO buildNationVO(Nation nation, Map<Integer, Integer> actorCount) {
        NationProtocol.NationVO.Builder builder = NationProtocol.NationVO.newBuilder();
        builder.setSimpleVO(buildNationSimpleVO(nation));
        builder.setLimit(buildNationLimitEntity(nation));
        builder.putAllActorCount(actorCount);
        return builder.build();
    }

    public static NationProtocol.NationLimitEntity buildNationLimitEntity(Nation nation) {
        NationProtocol.NationLimitEntity.Builder builder = NationProtocol.NationLimitEntity.newBuilder();
        int type = nation.getLimitEntity().getType().getId();
        builder.setType(NationProtocol.NationLimitType.forNumber(type));
        if (nation.getLimitEntity().getType() == NationLimitType.REFUSE_IN) {
            builder.setValue(0);
        } else {
            builder.setValue((int) nation.getLimitEntity().getValue());
        }
        return builder.build();
    }

    public static NationProtocol.NationSimpleVO buildNationSimpleVO(Nation nation) {
        NationProtocol.NationSimpleVO.Builder builder = NationProtocol.NationSimpleVO.newBuilder();
        long nationId = nation.getNationId();
        builder.setId(nationId);
        builder.setName(nation.getName());
        builder.setFlagId(nation.getFlagId());
        builder.setActorProfile(PbBuilder.buildActorProfile(nation.getKingAttributeMap()));
        builder.setLevel(nation.getLevel());
        builder.setMemberCount(nation.getMemberMap().size());
        builder.setYesterdayLevel(nation.getYesterdayLevel());
        builder.setNationChapterId(nation.getChapterId());
        builder.setNationPower(nation.getPower());
        builder.setNationInitChapterId(nation.getInitChapterId());
        builder.setNotice(nation.getNotice());
        return builder.build();
    }

    public static NationProtocol.NationSimpleVO buildNationSimpleVO(NationSimpleVO nationSimpleVO) {
        NationProtocol.NationSimpleVO.Builder builder = NationProtocol.NationSimpleVO.newBuilder();
        builder.setId(nationSimpleVO.getId());
        builder.setName(nationSimpleVO.getName());
        builder.setFlagId(nationSimpleVO.getFlagId());
        builder.setActorProfile(PbBuilder.buildActorProfile(nationSimpleVO.getAttributes()));
        builder.setLevel(nationSimpleVO.getLevel());
        builder.setMemberCount(nationSimpleVO.getMemberCount());
        builder.setYesterdayLevel(nationSimpleVO.getYesterdayLevel());
        builder.setNationChapterId(nationSimpleVO.getChapterId());
        builder.setNationPower(nationSimpleVO.getNationPower());
        builder.setNationInitChapterId(nationSimpleVO.getInitChapterId());
        return builder.build();
    }

    public static NationProtocol.NationPowerRankResponse buildNationPowerRankResponse(NationPowerRankResponse response) {
        NationProtocol.NationPowerRankResponse.Builder builder = NationProtocol.NationPowerRankResponse.newBuilder();
        for (NationPowerRankVO vo : response.getRanks()) {
            builder.addRanks(buildNationPowerRankVO(vo));
        }
        builder.setPage(response.getPage());
        builder.setPages(response.getPages());
        return builder.build();
    }

    public static NationProtocol.NationPowerRankVO buildNationPowerRankVO(NationPowerRankVO vo) {
        NationProtocol.NationPowerRankVO.Builder builder = NationProtocol.NationPowerRankVO.newBuilder();
        builder.setSimpleVO(buildNationSimpleVO(vo.getSimpleVO()));
        builder.setPower(vo.getPower());
        builder.setRank(vo.getRank());
        return builder.build();
    }

    public static NationProtocol.NationDungeonDamageRankResponse buildNationDungeonDamageRankResponse(NationDungeonNationDamageRankResponse response) {
        NationProtocol.NationDungeonDamageRankResponse.Builder builder = NationProtocol.NationDungeonDamageRankResponse.newBuilder();
        for (NationDungeonNationDamageRankVO vo : response.getRanks()) {
            builder.addRanks(buildNationDungeonDamageRankVO(vo));
        }
        builder.setPage(response.getPage());
        builder.setPages(response.getPages());
        return builder.build();
    }

    public static NationProtocol.NationDungeonDamageRankVO buildNationDungeonDamageRankVO(NationDungeonNationDamageRankVO vo) {
        NationProtocol.NationDungeonDamageRankVO.Builder builder = NationProtocol.NationDungeonDamageRankVO.newBuilder();
        builder.setSimpleVO(buildNationSimpleVO(vo.getSimpleVO()));
        builder.setRank(vo.getRank());
        builder.setDamage(vo.getDamage());
        return builder.build();
    }

    public static NationProtocol.NationStoryDamageRankResponse buildNationStoryDamageRankResponse(NationDungeonActorDamageRankResponse response) {
        NationProtocol.NationStoryDamageRankResponse.Builder builder = NationProtocol.NationStoryDamageRankResponse.newBuilder();
        if (response.getRanks() != null) {
            for (NationDungeonActorDamageRankVO vo : response.getRanks()) {
                builder.addRanks(buildNationStoryDamageRankVO(vo));
            }
        }
        return builder.build();
    }

    public static NationProtocol.NationStoryDamageRankVO buildNationStoryDamageRankVO(NationDungeonActorDamageRankVO vo) {
        NationProtocol.NationStoryDamageRankVO.Builder builder = NationProtocol.NationStoryDamageRankVO.newBuilder();
        builder.setActorProfile(PbBuilder.buildActorProfile(vo.getActorAttributes()));
        builder.setDamage(vo.getDamage());
        builder.setRank(vo.getRank());
        return builder.build();
    }

    public static Result updateNotice(long kingId, String notice) {
        return ref.get().nationFacade.notice(kingId, notice);
    }

    public static Collection<Nation> getNationList() {
        return ref.get().nationFacade.getNationList();
    }

    public static Set<String> getAllNation() {
        return ref.get().nationFacade.getAllNationNames();
    }

    public static TResult<NationMemberInfoResponse> getNationMemberByName(String name) {
        return ref.get().nationFacade.getNationMemberByName(name);
    }

    public static Result changeNationHeader(ChangeNationHeaderRequest request) {
        TResult<Nation> nationResult = ref.get().nationFacade.getNation(request.getNationId());
        if (nationResult.isFail()) {
            return Result.valueOf(nationResult.statusCode);
        }
        long kingId = nationResult.item.getKingId();
        return ref.get().nationFacade.transfer(kingId, request.getActorId(), false);
    }

    public static Result changeDeclaration(long nationId, String notice) {
        return ref.get().nationFacade.changeDeclaration(nationId, notice);
    }

    @PostConstruct
    protected void init() {
        ref.set(this);
    }
}