package cn.daxiang.hbtd.gameserver.module.runes.model;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/12/21
 */
public class RunesExtra {
    /**
     * 属性类型
     */
    private int type;
    /**
     * 属性值
     */
    private int value;

    public static RunesExtra valueOf(int type, int value) {
        RunesExtra model = new RunesExtra();
        model.type = type;
        model.value = value;
        return model;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;
        RunesExtra that = (RunesExtra) o;
        return type == that.type && value == that.value;
    }

    @Override
    public int hashCode() {
        return Objects.hash(type, value);
    }
}
