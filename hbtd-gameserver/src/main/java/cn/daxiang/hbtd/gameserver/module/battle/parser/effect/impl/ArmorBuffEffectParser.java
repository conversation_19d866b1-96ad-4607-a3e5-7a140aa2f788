package cn.daxiang.hbtd.gameserver.module.battle.parser.effect.impl;

import cn.daxiang.hbtd.gameserver.core.dataconfig.model.SkillConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.SkillEffectConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.SkillConfigService;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleBuff;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleRoom;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleSprite;
import cn.daxiang.hbtd.gameserver.module.battle.model.FightReport;
import cn.daxiang.hbtd.gameserver.module.battle.parser.effect.AbstractSkillEffectParser;
import cn.daxiang.hbtd.gameserver.module.battle.type.SkillEffectType;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Iterator;
import java.util.Map;

@Component
public class ArmorBuffEffectParser extends AbstractSkillEffectParser {

    @Override
    public boolean execute(FightReport report, BattleSprite attacker, BattleSprite targeter, SkillConfig skillConfig, SkillEffectConfig effectConfig, BattleRoom battleRoom) {
        this.addBattleBuff(report, attacker, targeter, skillConfig, effectConfig, battleRoom);
        return true;
    }

    @Override
    protected SkillEffectType getType() {
        return SkillEffectType.ARMOR_BUFF_EFFECT;
    }

    @Override
    public long calcEffectValue(FightReport report, BattleSprite battleSprite, BattleRoom battleRoom) {
        //X1:目标的物理抗性（初始值）
        //X2:目标的魔法抗性（初始值）
        //X3:施法者攻击力（计算buff）
        long targetPhysicalArmor = battleSprite.getSpriteBattle().getPhysicalArmor();
        long targetSpellArmor = battleSprite.getSpriteBattle().getSpellArmor();
        Collection<BattleBuff> buffList = battleSprite.getBuffList(getType());
        Map<Integer, Collection<Long>> buffValueMap = Maps.newHashMap();
        for (Iterator<BattleBuff> iterator = buffList.iterator(); iterator.hasNext(); ) {
            BattleBuff buff = iterator.next();
            BattleSprite casterSprite = battleRoom.getBattleSprite(buff.getCastSpriteUid());
            long casterAttack = casterSprite.getAttack();
            SkillEffectConfig effectConfig = SkillConfigService.getSkillEffectConfig(buff.getEffectId());
            long buffValue = effectConfig.calcSkillEffect(targetPhysicalArmor, targetSpellArmor, casterAttack);
            Collection<Long> buffValues = buffValueMap.get(buff.getEffectId());
            if (buffValues == null) {
                buffValues = Lists.newArrayList();
                buffValueMap.put(buff.getEffectId(), buffValues);
            }
            buffValues.add(buffValue);
        }
        return this.getBuffValue(buffValueMap);
    }
}
