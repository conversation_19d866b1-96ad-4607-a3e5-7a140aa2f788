package cn.daxiang.hbtd.gameserver.core.event.impl;

import cn.daxiang.framework.event.ActorEvent;
import cn.daxiang.shared.event.EventKey;

/**
 * 平定天下完成事件
 *
 * @author: <PERSON>
 * @date: 2023/3/15 16:25
 * @Description:
 */
public class ReputationEventFinishEvent extends ActorEvent {
    /**
     * 增加的历史名气值
     */
    public long addReputationVale;
    /**
     * 事件难度
     */
    public int eventDegree;

    public ReputationEventFinishEvent(long actorId, long addReputationVale, int eventDegree) {
        super(EventKey.REPUTATION_EVENT_FINISH_EVENT, actorId);
        this.addReputationVale = addReputationVale;
        this.eventDegree = eventDegree;
    }
}
