package cn.daxiang.hbtd.gameserver.module.activity.model.entity.record;

import cn.daxiang.hbtd.gameserver.module.goods.helper.RewardHelper;
import cn.daxiang.shared.reward.RewardObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.Collection;
import java.util.Map;

/**
 * 天官赐福玩家记录
 *
 * @Author: Gary
 * @Date 2022-10-09 11:30
 * @Description:
 */
public class ActivityRecord21 {
    /**
     * 积分
     */
    private int score;
    /**
     * 最大数
     */
    private int maxNumber;
    /**
     * 今日购买次数
     * key:id value:购买次数
     */
    private Map<Integer, Integer> todayBuyMap = Maps.newHashMap();
    /**
     * 购买礼包详情
     * [id, Integer]
     */
    private Map<Integer, Integer> buyMap = Maps.newHashMap();
    /**
     * 领取礼包详情
     * [id, Integer]
     */
    private Map<Integer, Integer> receiveMap = Maps.newHashMap();
    /**
     * 已获取奖励列表
     */
    private Collection<RewardObject> rewardList = Lists.newArrayList();
    /**
     * 充值、直冲记录 最后一次重置时间
     */
    private long lastRestTime;

    public static ActivityRecord21 valueOf() {
        ActivityRecord21 model = new ActivityRecord21();
        model.lastRestTime = System.currentTimeMillis();
        return model;
    }

    /**
     * 充值购买次数重置
     */
    public void reset() {
        this.todayBuyMap.clear();
        this.lastRestTime = System.currentTimeMillis();
    }

    public void addScore(int score) {
        this.score += score;
    }

    public void addRewardList(Collection<RewardObject> rewardList) {
        this.rewardList.addAll(rewardList);
        this.rewardList = RewardHelper.groupByTypeAndId(this.rewardList);
    }

    public boolean isReceive(int id) {
        return this.receiveMap.getOrDefault(id, 0) >= this.buyMap.getOrDefault(id, 0);
    }

    public int buyCount(int id) {
        return this.buyMap.getOrDefault(id, 0);
    }

    public int receiveCount(int id) {
        return this.receiveMap.getOrDefault(id, 0);
    }

    public void buyReward(int id) {
        this.buyMap.merge(id, 1, Integer::sum);
        this.todayBuyMap.merge(id, 1, Integer::sum);
    }

    public void receiveReward(int id) {
        this.receiveMap.merge(id, 1, Integer::sum);
    }

    public int getScore() {
        return score;
    }

    public void setScore(int score) {
        this.score = score;
    }

    public int getMaxNumber() {
        return maxNumber;
    }

    public void setMaxNumber(int maxNumber) {
        this.maxNumber = maxNumber;
    }

    public Map<Integer, Integer> getBuyMap() {
        return buyMap;
    }

    public void setBuyMap(Map<Integer, Integer> buyMap) {
        this.buyMap = buyMap;
    }

    public Map<Integer, Integer> getReceiveMap() {
        return receiveMap;
    }

    public void setReceiveMap(Map<Integer, Integer> receiveMap) {
        this.receiveMap = receiveMap;
    }

    public Collection<RewardObject> getRewardList() {
        return rewardList;
    }

    public void setRewardList(Collection<RewardObject> rewardList) {
        this.rewardList = rewardList;
    }

    public long getLastRestTime() {
        return lastRestTime;
    }

    public void setLastRestTime(long lastRestTime) {
        this.lastRestTime = lastRestTime;
    }

    public Map<Integer, Integer> getTodayBuyMap() {
        return todayBuyMap;
    }

    public void setTodayBuyMap(Map<Integer, Integer> todayBuyMap) {
        this.todayBuyMap = todayBuyMap;
    }

    public int getDayTimes(int num) {
        return this.todayBuyMap.getOrDefault(num, 0);
    }
}
