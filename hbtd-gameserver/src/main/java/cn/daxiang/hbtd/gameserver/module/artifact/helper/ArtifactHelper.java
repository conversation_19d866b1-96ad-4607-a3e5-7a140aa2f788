package cn.daxiang.hbtd.gameserver.module.artifact.helper;

import cn.daxiang.hbtd.gameserver.core.database.table.Artifact;
import cn.daxiang.protocol.game.ArtifactProtocol;
import cn.daxiang.protocol.game.CommonProtocol;

import java.util.Collection;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class ArtifactHelper {

    public static ArtifactProtocol.ArtifactResponse buildArtifactResponse(Artifact artifact) {
        ArtifactProtocol.ArtifactResponse.Builder builder = ArtifactProtocol.ArtifactResponse.newBuilder();
        for (Map.Entry<Integer, Collection<Integer>> entry : artifact.getActivateInfoMap().entrySet()) {
            CommonProtocol.IntListPacket intListPacket = CommonProtocol.IntListPacket.newBuilder().addAllList(entry.getValue()).build();
            builder.putActivateInfo(entry.getKey(), intListPacket);
        }
        builder.setLevel(artifact.getLevel());
        builder.setProgress(artifact.getProgress());
        return builder.build();
    }
}
