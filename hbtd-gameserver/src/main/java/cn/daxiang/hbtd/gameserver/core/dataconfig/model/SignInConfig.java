package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.dataconfig.annotation.FieldIgnore;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.shared.reward.RewardObject;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * 签到奖励表
 */
@DataFile(fileName = "sign_in_config")
public class SignInConfig implements ModelAdapter {
    /**
     * 期
     */
    private int stage;
    /**
     * 天数
     */
    private int days;

    /**
     * 奖励([[rewardType,id,num],[rewardType,id,num]])
     * {@code RewardObject}
     */
    private String reward;

    /**
     * -1:代表没有双倍,0:所有都是双倍,其他代表所需要的VIP等级
     */
    private int doubleVip;
    /**
     * 阶段奖励
     */
    private String stageReward;

    @FieldIgnore
    private List<RewardObject> rewardList = Lists.newArrayList();

    @FieldIgnore
    private List<RewardObject> stageRewardList = Lists.newArrayList();

    @Override
    public void initialize() {
        JSONArray rewardArray = JSON.parseArray(reward);
        for (Object rewardItem : rewardArray) {
            JSONArray itemArray = JSON.parseArray(rewardItem.toString());
            RewardObject rewardObject = RewardObject.valueOf(itemArray);
            rewardList.add(rewardObject);
        }
        JSONArray stageRewardArray = JSON.parseArray(stageReward);
        for (Object rewardItem : stageRewardArray) {
            JSONArray itemArray = JSON.parseArray(rewardItem.toString());
            RewardObject rewardObject = RewardObject.valueOf(itemArray);
            stageRewardList.add(rewardObject);
        }
    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(stage, days);
    }

    public int getStage() {
        return stage;
    }

    public int getDays() {
        return days;
    }

    public List<RewardObject> getReward() {
        return rewardList;
    }

    public int getDoubleVip() {
        return doubleVip;
    }

    public List<RewardObject> getDoubleVipReward(int vipLevel) {
        List<RewardObject> list = Lists.newArrayList();
        if (doubleVip < 0) {
            return this.rewardList;
        } else {
            if (vipLevel >= doubleVip) {
                for (RewardObject rewardObject : rewardList) {
                    list.add(RewardObject.valueOf(rewardObject.getType(), rewardObject.getId(), rewardObject.getCount() * 2));
                }
                return list;
            } else {
                return this.rewardList;
            }
        }
    }

    /**
     * 获取累积签到奖励
     *
     * @return
     */
    public List<RewardObject> getStageRewardList() {
        return stageRewardList;
    }
}
