package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.dataconfig.annotation.FieldIgnore;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.protocol.game.TypeProtocol.RewardType;
import cn.daxiang.shared.reward.RewardObject;
import com.google.common.collect.Lists;

import java.util.Collection;

/**
 * 坐骑基础表
 * <AUTHOR>
 * @date 2025/3/14
 */
@DataFile(fileName = "mount_config")
public class MountConfig implements ModelAdapter {
    /**
     * 坐骑Id
     */
    private int id;
    /**
     * 品质
     */
    private int quality;
    /**
     * 坐骑碎片道具ID
     */
    private int fragmentId;
    /**
     * 合成所需碎片数量
     */
    private int fragmentCount;
    /**
     * 重复获得转化碎片数量
     */
    private int transformedCount;
    /**
     * 是否为心愿武将（特权才可以选择）
     */
    private boolean wish;
    /**
     * 星级解锁，展示到主城的设置，配置为0则判定解锁，配置1-5则判定星级
     */
    private int showStar;

    /**
     * 合成坐骑消耗
     */
    @FieldIgnore
    private Collection<RewardObject> fragmentCostList = Lists.newArrayList();
    /**
     * 重复获得转化
     */
    @FieldIgnore
    private Collection<RewardObject> transformedList = Lists.newArrayList();

    @Override
    public void initialize() {
        fragmentCostList.add(RewardObject.valueOf(RewardType.GOODS_VALUE, fragmentId, fragmentCount));
        transformedList.add(RewardObject.valueOf(RewardType.GOODS_VALUE, fragmentId, transformedCount));
    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(id);
    }

    public int getId() {
        return id;
    }

    public int getQuality() {
        return quality;
    }

    public boolean isWish() {
        return wish;
    }

    public int getFragmentId() {
        return fragmentId;
    }

    public Collection<RewardObject> getFragmentCostList() {
        return fragmentCostList;
    }

    public Collection<RewardObject> getTransformedList() {
        return transformedList;
    }

    public int getShowStar() {
        return showStar;
    }
}
