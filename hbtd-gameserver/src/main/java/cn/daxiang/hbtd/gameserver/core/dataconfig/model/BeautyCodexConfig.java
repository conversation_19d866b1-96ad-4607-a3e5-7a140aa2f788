package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.dataconfig.annotation.FieldIgnore;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.shared.reward.RewardObject;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;

import java.util.Collection;

/**
 * 时空红颜小游戏图鉴配置
 *
 * @author: Gary
 * @date: 2023/9/4 11:47
 * @Description:
 */
@DataFile(fileName = "beauty_codex_config")
public class BeautyCodexConfig implements ModelAdapter {
    /**
     * id
     */
    private int id;
    /**
     * 玩法类型
     */
    private int playType;
    /**
     * 图鉴奖励[[类型,id,数量],[类型,id,数量]]
     */
    private String value;
    @FieldIgnore
    private Collection<RewardObject> rewardList = Lists.newArrayList();

    @Override
    public void initialize() {

        JSONArray rewardArray = JSONArray.parseArray(value);
        for (Object rewardItem : rewardArray) {
            JSONArray array = JSONArray.parseArray(rewardItem.toString());
            RewardObject rewardObject = RewardObject.valueOf(array);
            rewardList.add(rewardObject);
        }
    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(id);
    }

    public int getId() {
        return id;
    }

    public int getPlayType() {
        return playType;
    }

    public Collection<RewardObject> getRewardList() {
        return rewardList;
    }
}
