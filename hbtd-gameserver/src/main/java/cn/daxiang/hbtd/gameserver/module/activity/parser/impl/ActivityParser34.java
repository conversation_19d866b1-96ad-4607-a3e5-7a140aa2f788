package cn.daxiang.hbtd.gameserver.module.activity.parser.impl;

import cn.daxiang.framework.context.ApplicationInitCompleteEvent;
import cn.daxiang.framework.event.GameEvent;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.framework.utils.DateUtils;
import cn.daxiang.hbtd.gameserver.core.database.table.ActivityRecord;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.Activity34DailyRechargeConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.ActivityOpenConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.Activity34ConfigService;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.ActivityOpenConfigService;
import cn.daxiang.hbtd.gameserver.core.dispatch.DispatchHelper;
import cn.daxiang.hbtd.gameserver.core.event.impl.ActorRechargeEvent;
import cn.daxiang.hbtd.gameserver.core.event.impl.MailAddEvent;
import cn.daxiang.hbtd.gameserver.core.netty.channel.PlayerChannel;
import cn.daxiang.hbtd.gameserver.core.oss.type.OperationType;
import cn.daxiang.hbtd.gameserver.module.activity.model.entity.record.ActivityRecord34;
import cn.daxiang.hbtd.gameserver.module.activity.parser.AbstractActivityParser;
import cn.daxiang.hbtd.gameserver.module.activity.type.ActivityType;
import cn.daxiang.hbtd.gameserver.module.goods.helper.RewardHelper;
import cn.daxiang.hbtd.gameserver.module.mail.type.MailTemplateType;
import cn.daxiang.hbtd.gameserver.module.user.helper.ActorHelper;
import cn.daxiang.protocol.game.ActivityInfoProtocol;
import cn.daxiang.protocol.game.CommonProtocol;
import cn.daxiang.shared.reward.RewardObject;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.protobuf.ByteString;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;

import static cn.daxiang.shared.GameModuleStatusCodeConstant.ACTIVITY_NOT_FINISH_FOR_RECEIVED;
import static cn.daxiang.shared.GameModuleStatusCodeConstant.ACTIVITY_REWARD_HAD_RECEIVED;
import static cn.daxiang.shared.GameModuleStatusCodeConstant.ACTIVITY_UPDATE_DAY_ERROR;
import static cn.daxiang.shared.GameModuleStatusCodeConstant.CONFIG_NOT_FOUND;

/**
 * <AUTHOR>
 * @date 2023/2/16
 */
@Component
public class ActivityParser34 extends AbstractActivityParser implements ApplicationListener<ApplicationInitCompleteEvent> {
    @Override
    public void onApplicationEvent(ApplicationInitCompleteEvent event) {
        schedule.addFixedTime(new Runnable() {
            @Override
            public void run() {
                Collection<ActivityOpenConfig> configList = ActivityOpenConfigService.getActivityOpenConfigList(getType());
                if (configList.isEmpty()) {
                    return;
                }
                for (ActivityOpenConfig activityOpenConfig : configList) {
                    if (!isActivityOpen(activityOpenConfig.getId())) {
                        continue;
                    }
                    Set<Long> onlineActorList = PlayerChannel.onlineActorList();
                    for (Long actorId : onlineActorList) {
                        pushOnlineActivity(activityOpenConfig.getActivityType(), activityOpenConfig.getId(), actorId);
                    }
                }
            }
        }, 24);
    }

    @Override
    public void registerEvent(Set<String> eventSet) {
        eventSet.addAll(getType().getEventNames());
    }

    @Override
    public TResult<CommonProtocol.RewardResult> receiveReward(long actorId, int activityId, int id, byte[] value) {
        ActivityOpenConfig activityOpenConfig = ActivityOpenConfigService.getActivityConfig(activityId);
        if (activityOpenConfig == null) {
            LOGGER.error("ActivityOpenConfig not found, id:{}", activityId);
            return TResult.valueOf(CONFIG_NOT_FOUND);
        }
        TreeMap<Integer, Activity34DailyRechargeConfig> configMap = Activity34ConfigService.getActivity34DailyRechargeConfigMap(activityOpenConfig.getData());
        if (configMap == null) {
            LOGGER.error("Activity34DailyRechargeConfig is null activityId:{} data:{}", activityOpenConfig.getId(), activityOpenConfig.getData());
            return TResult.valueOf(CONFIG_NOT_FOUND);
        }
        ActivityRecord record = activityRecordDao.getActivityRecord(actorId, activityId);
        ActivityRecord34 activityRecord = JSON.parseObject(record.getRecord(), ActivityRecord34.class);
        Collection<RewardObject> rewardList = Lists.newLinkedList();
        if (id == -1) {
            if (activityRecord.getReceiveDailyReward()) {
                return TResult.valueOf(ACTIVITY_REWARD_HAD_RECEIVED);
            }
            activityRecord.setReceiveDailyReward(true);
            Activity34DailyRechargeConfig config = configMap.get(configMap.firstKey());
            rewardList.addAll(config.getDailyRewardList());
        } else {
            Activity34DailyRechargeConfig config = configMap.get(id);
            if (config == null) {
                LOGGER.error("Activity34DailyRechargeConfig not found, activityId:{} data:{} id:{}", activityId, activityOpenConfig.getData(), id);
                return TResult.valueOf(CONFIG_NOT_FOUND);
            }
            int activityOpenDay = ActivityOpenConfigService.getActivityOpenDay(activityId);
            if (id > activityOpenDay) {
                return TResult.valueOf(ACTIVITY_UPDATE_DAY_ERROR);
            }

            if (activityRecord.getReceivedList().contains(id)) {
                return TResult.valueOf(ACTIVITY_REWARD_HAD_RECEIVED);
            }
            int recordId = activityRecord.getId();
            // 是否满足条件
            if (recordId < config.getId() || (recordId == config.getId() && !activityRecord.getBuy())) {
                return TResult.valueOf(ACTIVITY_NOT_FINISH_FOR_RECEIVED);
            }
            if (config.getId() == 0 && (recordId != configMap.lastKey() || !activityRecord.getBuy())) {
                return TResult.valueOf(ACTIVITY_NOT_FINISH_FOR_RECEIVED);
            }
            activityRecord.addReceived(id);
            rewardList.addAll(config.getRewardList());
        }
        record.setRecord(JSON.toJSONString(activityRecord));
        dbQueue.updateQueue(record);
        CommonProtocol.RewardResult result = RewardHelper.sendRewardList(actorId, rewardList, OperationType.ACTIVITY_TYPE_34);
        return TResult.sucess(result);
    }

    @Override
    public void onEvent(GameEvent event) {
        Collection<ActivityOpenConfig> configList = ActivityOpenConfigService.getActivityOpenConfigList(getType());
        if (configList.isEmpty()) {
            return;
        }
        ActorRechargeEvent actorRechargeEvent = event.convert();
        long actorId = actorRechargeEvent.getActorId();
        for (ActivityOpenConfig activityOpenConfig : configList) {
            int activityId = activityOpenConfig.getId();
            if (isActivityOpen(activityId) == false) {
                continue;
            }

            TreeMap<Integer, Activity34DailyRechargeConfig> configMap = Activity34ConfigService.getActivity34DailyRechargeConfigMap(activityOpenConfig.getData());
            if (configMap == null) {
                LOGGER.error("Activity34DailyRechargeConfig is null activityId:{} data:{}", activityOpenConfig.getId(), activityOpenConfig.getData());
                continue;
            }
            reset(actorId, activityId);
            ActivityRecord34 activityRecord;
            ActivityRecord record = activityRecordDao.getActivityRecord(actorId, activityId);
            if (record == null) {
                activityRecord = new ActivityRecord34();
                record = activityRecordDao.create(actorId, activityId, JSON.toJSONString(activityRecord));
            } else {
                activityRecord = JSON.parseObject(record.getRecord(), ActivityRecord34.class);
            }
            if (activityRecord.getId() == 0) {
                activityRecord.setId(1);
            }
            Activity34DailyRechargeConfig config = configMap.get(activityRecord.getId());
            if (config == null) {
                LOGGER.error("Activity34DailyRechargeConfig is null activityId:{} data:{} id:{}", activityOpenConfig.getId(), activityOpenConfig.getData(), activityRecord.getId());
                continue;
            }
            if (activityRecord.getBuy() || config.getChargeId() != actorRechargeEvent.getChargeId()) {
                continue;
            }
            activityRecord.setBuy(true);
            activityRecord.setResetTime(System.currentTimeMillis());
            record.setRecord(JSON.toJSONString(activityRecord));
            dbQueue.updateQueue(record);
            this.pushActivity(actorId, activityId, activityOpenConfig.getActivityType());
        }
    }

    @Override
    protected ActivityType getType() {
        return ActivityType.ACTIVITY_TYPE_34;
    }

    @Override
    protected void onActivityOpen(Collection<ActivityOpenConfig> openActivityIds) {

    }

    @Override
    protected void onActivityEnd(Collection<ActivityOpenConfig> endActivityIds) {
        // 发送未领取的奖励邮件
        for (ActivityOpenConfig activityOpenConfig : endActivityIds) {
            int activityId = activityOpenConfig.getId();
            List<Long> actorIds = activityRecordDao.getActorIds(activityId);
            for (Long actorId : actorIds) {
                rewardClear(actorId, activityOpenConfig);
            }
        }
    }

    @Override
    public TResult<ByteString> getRecord2Client(long actorId, int activityId) {
        reset(actorId, activityId);
        ActivityRecord record = activityRecordDao.getActivityRecord(actorId, activityId);
        ActivityInfoProtocol.Activity34Record.Builder builder = ActivityInfoProtocol.Activity34Record.newBuilder();
        if (record != null) {
            ActivityRecord34 activityRecord = JSON.parseObject(record.getRecord(), ActivityRecord34.class);
            builder.setRechargeDay(activityRecord.getId());
            builder.addAllReceives(activityRecord.getReceivedList());
            builder.setReceiveDailyReward(activityRecord.getReceiveDailyReward());
            builder.setIsBuy(activityRecord.getBuy());
        }
        return TResult.sucess(builder.build().toByteString());
    }

    private void reset(long actorId, int activityId) {
        ActivityOpenConfig activityOpenConfig = ActivityOpenConfigService.getActivityConfig(activityId);
        if (activityOpenConfig == null) {
            LOGGER.error("ActivityOpenConfig not found, id:{}", activityId);
            return;
        }
        TreeMap<Integer, Activity34DailyRechargeConfig> configMap = Activity34ConfigService.getActivity34DailyRechargeConfigMap(activityOpenConfig.getData());
        if (configMap == null) {
            LOGGER.error("Activity34DailyRechargeConfig is null activityId:{} data:{}", activityOpenConfig.getId(), activityOpenConfig.getData());
            return;
        }
        ActivityRecord34 activityRecord;
        ActivityRecord record = activityRecordDao.getActivityRecord(actorId, activityId);
        if (record == null) {
            activityRecord = new ActivityRecord34();
            record = activityRecordDao.create(actorId, activityId, JSON.toJSONString(activityRecord));
        } else {
            activityRecord = JSON.parseObject(record.getRecord(), ActivityRecord34.class);
        }
        if (activityRecord.getId() == 0) {
            activityRecord.setId(1);
        }
        boolean buy = activityRecord.getBuy();
        // 当天不重置
        if (DateUtils.isToday(activityRecord.getResetTime())) {
            return;
        }
        //没有购买礼包 或者 最后一个礼包买了以后都只重置时间和每日奖励
        if (!buy || Activity34ConfigService.getMaxId(activityOpenConfig.getData()) == activityRecord.getId()) {
            activityRecord.setReceiveDailyReward(false);
            activityRecord.setResetTime(System.currentTimeMillis());
        } else {
            int nextId = activityRecord.getId() + 1;
            activityRecord.reset(nextId);
        }
        record.setRecord(JSON.toJSONString(activityRecord));
        dbQueue.updateQueue(record);
    }

    @Override
    public TResult<ByteString> getGlobal2Client(long actorId, int activityId) {
        return TResult.fail();
    }

    private void rewardClear(Long actorId, ActivityOpenConfig activityOpenConfig) {
        ActivityRecord record = activityRecordDao.getActivityRecord(actorId, activityOpenConfig.getId());
        if (record == null) {
            return;
        }
        ActivityRecord34 activityRecord = JSON.parseObject(record.getRecord(), ActivityRecord34.class);
        if (activityRecord.getBuy() == false && activityRecord.getId() == 1) {
            return;
        }
        Collection<RewardObject> activityRewards = Lists.newArrayList();

        Map<Integer, Activity34DailyRechargeConfig> configMap = Activity34ConfigService.getActivity34DailyRechargeConfigMap(activityOpenConfig.getData());
        if (configMap == null) {
            LOGGER.error("Activity34DailyRechargeConfig is null data:{}", activityOpenConfig.getData());
            return;
        }

        for (Activity34DailyRechargeConfig config : configMap.values()) {
            // 是否领取过
            if (activityRecord.getReceivedList().contains(config.getId())) {
                continue;
            }
            int recordId = activityRecord.getId();
            // 是否满足条件
            if (config.getId() == 0 && (recordId != Activity34ConfigService.getMaxId(activityOpenConfig.getData()) || !activityRecord.getBuy())) {
                continue;
            }
            if (recordId < config.getId() || (recordId == config.getId() && !activityRecord.getBuy())) {
                continue;
            }
            activityRecord.addReceived(config.getId());
            activityRewards.addAll(config.getRewardList());
        }

        if (activityRewards.isEmpty()) {
            return;
        }
        record.setRecord(JSON.toJSONString(activityRecord));
        dbQueue.updateQueue(record);
        sendRewardMail(actorId, activityRewards);
    }

    private void sendRewardMail(Long actorId, Collection<RewardObject> rewards) {
        Map<String, String> params = Maps.newHashMap();
        params.put("name", ActorHelper.getActorName(actorId));
        MailAddEvent mailAddEvent = new MailAddEvent(actorId, MailTemplateType.ACTIVITY_34_RECHARGE_UNCLAIMED_REWARD, params, rewards);
        DispatchHelper.postEvent(mailAddEvent);
        LOGGER.info("Send ACTIVITY_34_RESET_EVENT Reward complete, actorId:{}", actorId);
    }

    private void pushOnlineActivity(int activityType, int activityId, long actorId) {
        ActivityRecord record = activityRecordDao.getActivityRecord(actorId, activityId);
        if (record == null) {
            return;
        }
        TResult<ByteString> recordResult = this.getRecord2Client(actorId, activityId);
        if (recordResult.isFail()) {
            return;
        }
        TResult<ByteString> globalResult = this.getGlobal2Client(actorId, activityId);
        pushActivity(actorId, activityId, activityType, globalResult.item, recordResult.item);
    }
}
