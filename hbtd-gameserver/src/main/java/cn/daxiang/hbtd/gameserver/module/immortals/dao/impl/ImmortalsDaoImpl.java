package cn.daxiang.hbtd.gameserver.module.immortals.dao.impl;

import cn.daxiang.framework.database.MultiEntity;
import cn.daxiang.framework.database.dao.MultiEntityDaoImpl;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.hbtd.gameserver.core.GameConfig;
import cn.daxiang.hbtd.gameserver.core.database.IdGenerator;
import cn.daxiang.hbtd.gameserver.core.database.UniqueId;
import cn.daxiang.hbtd.gameserver.core.database.table.Immortals;
import cn.daxiang.hbtd.gameserver.core.oss.GameOssLogger;
import cn.daxiang.hbtd.gameserver.core.oss.type.OperationType;
import cn.daxiang.hbtd.gameserver.module.immortals.dao.ImmortalsDao;
import cn.daxiang.protocol.game.TypeProtocol;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;

/**
 * <AUTHOR>
 * @date 2022/7/1
 */
@Component
public class ImmortalsDaoImpl extends MultiEntityDaoImpl implements ImmortalsDao {
    @Autowired
    private IdGenerator idGenerator;

    @Override
    protected Class<? extends MultiEntity<?>> forClass() {
        return Immortals.class;
    }

    @Override
    protected void initMaxId() {
        Long maxId = jdbc.queryForObject("select max(immortalsId) from immortals", Long.class);
        AtomicLong base = null;
        if (maxId == null || maxId <= 0) {
            base = new AtomicLong(UniqueId.otherId(GameConfig.getServerId()));
        } else {
            base = new AtomicLong(maxId);
        }
        idGenerator.init(IdentiyKey.build(this.getClass()), base);
    }

    @Override
    public Collection<Immortals> getImmortalsList(long actorId) {
        Map<IdentiyKey, Immortals> immortalsMap = this.getByFk(actorId);
        return immortalsMap.values();
    }

    @Override
    public Immortals getImmortals(long actorId, long immortalsId) {
        return this.getMultiEnity(actorId, IdentiyKey.build(immortalsId));
    }

    @Override
    public Immortals createImmortals(long actorId, int configId, int quenchingLevel) {
        long immortalsId = this.idGenerator.increment(IdentiyKey.build(this.getClass()));
        Immortals equipment = Immortals.valueOf(actorId, immortalsId, configId, quenchingLevel);
        updateQueue(equipment);
        return equipment;
    }

    @Override
    public void deleteImmortals(long actorId, Collection<Long> immortalsIds, OperationType operationType) {
        for (Long immortalsId : immortalsIds) {
            Immortals immortals = this.getImmortals(actorId, immortalsId);
            GameOssLogger.goodsDecrease(actorId, operationType, TypeProtocol.RewardType.IMMORTALS, immortalsId, immortals.getConfigId(), 1, 1);
            this.delete(immortals);
        }
    }

    @Override
    public Immortals getImmortalsByConfigId(long actorId, int configId) {
        Map<IdentiyKey, Immortals> immortalsMap = this.getByFk(actorId);
        if (immortalsMap == null) {
            return null;
        }
        for (Immortals immortals : immortalsMap.values()) {
            if (immortals.getConfigId() == configId) {
                return immortals;
            }
        }
        return null;
    }
}
