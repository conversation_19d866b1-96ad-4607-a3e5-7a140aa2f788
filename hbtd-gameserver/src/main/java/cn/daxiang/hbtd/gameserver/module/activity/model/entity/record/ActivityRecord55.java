package cn.daxiang.hbtd.gameserver.module.activity.model.entity.record;

import com.google.common.collect.Lists;

import java.util.Collection;

/**
 * 合战锦标赛个人数据记录
 *
 * <AUTHOR>
 * @date 2024/1/13
 */
public class ActivityRecord55 {
    /**
     * 挑战完成次数
     */
    private int times;
    /**
     * 是否激活高级战令
     */
    private boolean active;
    /**
     * 最高难度
     */
    public int difficulty;
    /**
     * 最高难度下最小击杀耗时(ms)
     */
    public int time;
    /**
     * 普通战令已领取列表
     */
    private Collection<Integer> normalReceives = Lists.newArrayList();
    /**
     * 高级战令已领取列表
     */
    private Collection<Integer> supremeReceives = Lists.newArrayList();

    public int getTimes() {
        return times;
    }

    public void setTimes(int times) {
        this.times = times;
    }

    public void recordTimes() {
        this.times++;
    }

    public boolean isActive() {
        return active;
    }

    public void setActive(boolean active) {
        this.active = active;
    }

    public Collection<Integer> getNormalReceives() {
        return normalReceives;
    }

    public void setNormalReceives(Collection<Integer> normalReceives) {
        this.normalReceives = normalReceives;
    }

    public boolean isReceiveNormal(int id) {
        return this.normalReceives.contains(id);
    }

    public void receiveNormal(int id) {
        this.normalReceives.add(id);
    }

    public Collection<Integer> getSupremeReceives() {
        return supremeReceives;
    }

    public void setSupremeReceives(Collection<Integer> supremeReceives) {
        this.supremeReceives = supremeReceives;
    }

    public boolean isReceiveSupreme(int id) {
        return this.supremeReceives.contains(id);
    }

    public void receiveSupreme(int id) {
        this.supremeReceives.add(id);
    }

    public int getDifficulty() {
        return difficulty;
    }

    public void setDifficulty(int difficulty) {
        this.difficulty = difficulty;
    }

    public int getTime() {
        return time;
    }

    public void setTime(int time) {
        this.time = time;
    }

    public void refreshDifficulty(int difficulty, int time) {
        if (difficulty > this.difficulty || (difficulty == this.difficulty && time < this.time)) {
            this.difficulty = difficulty;
            this.time = time;
        }
    }
}
