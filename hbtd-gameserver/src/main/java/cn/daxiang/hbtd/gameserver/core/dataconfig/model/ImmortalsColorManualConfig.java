package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.identity.IdentiyKey;

/**
 * 专武幻彩图鉴
 *
 * <AUTHOR>
 * @date 2025/02/14
 */
@DataFile(fileName = "immortals_color_manual_config")
public class ImmortalsColorManualConfig implements ModelAdapter {
    /**
     * 专武配置ID
     */
    private int immortalsId;
    /**
     * 特殊属性效果配置id
     */
    private int specialEffectId;

    @Override
    public void initialize() {

    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(immortalsId);
    }

    public int getImmortalsId() {
        return immortalsId;
    }

    public int getSpecialEffectId() {
        return specialEffectId;
    }
}
