package cn.daxiang.hbtd.gameserver.module.battle.parser.seek.impl;

import cn.daxiang.hbtd.gameserver.core.dataconfig.model.SkillSeekConfig;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleRoom;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleSprite;
import cn.daxiang.hbtd.gameserver.module.battle.parser.seek.AbstractSkillSeekParser;
import cn.daxiang.hbtd.gameserver.module.battle.type.SkillSeekType;
import cn.daxiang.hbtd.gameserver.module.hero.type.HeroSexType;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class ManSkillSeekParser extends AbstractSkillSeekParser {

    @Override
    protected SkillSeekType getType() {
        return SkillSeekType.MAN;
    }

    @Override
    public List<BattleSprite> seekSprites(BattleSprite attacker, BattleRoom battleRoom, SkillSeekConfig skillSeekConfig) {
        List<BattleSprite> seekSprites = Lists.newArrayList();
        List<BattleSprite> targetSprites = this.getTargetSpriteList(attacker, battleRoom, skillSeekConfig);

        int count = 0;
        for (BattleSprite targetSprite : targetSprites) {
            if (count >= skillSeekConfig.getTargetCount()) {
                break;
            }
            if (targetSprite.getHeroSexType() == HeroSexType.MAN) {
                seekSprites.add(targetSprite);
                count++;
            }
        }
        return seekSprites;
    }

}
