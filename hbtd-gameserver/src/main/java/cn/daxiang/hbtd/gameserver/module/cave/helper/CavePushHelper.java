package cn.daxiang.hbtd.gameserver.module.cave.helper;

import cn.daxiang.framework.protocal.DataPacket;
import cn.daxiang.hbtd.gameserver.core.database.table.Cave;
import cn.daxiang.hbtd.gameserver.core.netty.channel.PlayerChannel;
import cn.daxiang.hbtd.gameserver.module.user.helper.ActorHelper;
import cn.daxiang.protocol.game.CaveProtocol;
import cn.daxiang.protocol.game.CommonProtocol;
import cn.daxiang.protocol.game.ModuleProtocol;
import cn.daxiang.protocol.game.TypeProtocol;
import cn.daxiang.shared.module.team.Team;
import cn.daxiang.shared.module.team.TeamExtHardCave;

import java.util.Map;

public class CavePushHelper {
    /**
     * 推送勇闯魔窟信息
     *
     * @param actorId
     * @param cave
     */
    public static void pushCave(long actorId, Cave cave) {
        CaveProtocol.CaveInfoResponse response = CaveHelper.buildCaveInfoResponse(cave);
        DataPacket packet = DataPacket.valueOf(ModuleProtocol.Module.CAVE_VALUE, CaveProtocol.CaveCmd.PUSH_CAVE_VALUE, response);
        PlayerChannel.push(actorId, packet);
    }

    /**
     * 推送单人闯关战斗结果
     *
     * @param actorId
     * @param response
     */
    public static void pushSingleBattleResult(long actorId, CaveProtocol.CaveSingleBattleResultResponse response) {
        DataPacket packet = DataPacket.valueOf(ModuleProtocol.Module.CAVE_VALUE, CaveProtocol.CaveCmd.PUSH_CAVE_SINGLE_BATTLE_RESULT_VALUE, response);
        PlayerChannel.push(actorId, packet);
    }

    /**
     * 推送队伍信息
     *
     * @param actorId
     * @param team
     */
    public static void pushTeam(long actorId, Team team) {
        CaveProtocol.CaveTeamInfoResponse response = CaveHelper.buildTeam(team);
        DataPacket packet = DataPacket.valueOf(ModuleProtocol.Module.CAVE_VALUE, CaveProtocol.CaveCmd.CAVE_PUSH_TEAM_INFO_VALUE, response);
        PlayerChannel.push(actorId, packet);
    }

    /**
     * 推送队伍解散
     *
     * @param actorId
     */
    public static void pushTeamDisband(long actorId) {
        CommonProtocol.Response response = CommonProtocol.Response.newBuilder().build();
        DataPacket packet = DataPacket.valueOf(ModuleProtocol.Module.CAVE_VALUE, CaveProtocol.CaveCmd.CAVE_PUSH_TEAM_DISBAND_VALUE, response);
        PlayerChannel.push(actorId, packet);
    }

    /**
     * 推送跳过战斗
     *
     * @param actorId
     */
    public static void pushTeamSkipBattle(long actorId) {
        CommonProtocol.Response response = CommonProtocol.Response.newBuilder().build();
        DataPacket packet = DataPacket.valueOf(ModuleProtocol.Module.CAVE_VALUE, CaveProtocol.CaveCmd.PUSH_TEAM_SKIP_BATTLE_VALUE, response);
        PlayerChannel.push(actorId, packet);
    }

    /**
     * 推送踢出队伍
     *
     * @param actorId
     */
    public static void pushTeamKickOutEvent(long actorId) {
        CommonProtocol.Response response = CommonProtocol.Response.newBuilder().build();
        DataPacket packet = DataPacket.valueOf(ModuleProtocol.Module.CAVE_VALUE, CaveProtocol.CaveCmd.CAVE_PUSH_TEAM_KICK_OUT_VALUE, response);
        PlayerChannel.push(actorId, packet);
    }

    /**
     * 推送组队邀请
     *
     * @param actorId
     * @param team
     */
    public static void pushTeamInvite(long actorId, Team team) {
        CommonProtocol.ActorProfile actorProfile = null;
        for (Map<Byte, Object> entry : team.getAttributeMap().values()) {
            long teamActorId = (long) entry.get((byte) TypeProtocol.ActorFieldType.ACTOR_ID_VALUE);
            if (team.getLeaderActorId() == teamActorId) {
                actorProfile = ActorHelper.getActorProfile(entry);
            }
        }
        CaveProtocol.CaveTeamInviteResponse.Builder builder = CaveProtocol.CaveTeamInviteResponse.newBuilder();
        TeamExtHardCave extHardCave = (TeamExtHardCave) team.getExt();
        builder.setConfigId(extHardCave.getConfigId());
        builder.setTeamType(CaveProtocol.CaveTeamType.CAVE_HARD_TEAM);
        builder.setActorProfile(actorProfile);
        DataPacket packet = DataPacket.valueOf(ModuleProtocol.Module.CAVE_VALUE, CaveProtocol.CaveCmd.CAVE_PUSH_TEAM_INVITE_VALUE, builder.build());
        PlayerChannel.push(actorId, packet);
    }

    /**
     * 推送魔将组队战斗结果
     *
     * @param actorId
     * @param response
     */
    public static void pushGroupBattleResult(long actorId, CaveProtocol.CaveGroupNormalBattleResultResponse response) {
        DataPacket packet = DataPacket.valueOf(ModuleProtocol.Module.CAVE_VALUE, CaveProtocol.CaveCmd.PUSH_CAVE_GROUP_NORMAL_BATTLE_RESULT_VALUE, response);
        PlayerChannel.push(actorId, packet);
    }

    /**
     * 推送天魔组队战斗结果
     *
     * @param actorId
     * @param response
     */
    public static void pushGroupBattleResult(long actorId, CaveProtocol.CaveGroupHardBattleResultResponse response) {
        DataPacket packet = DataPacket.valueOf(ModuleProtocol.Module.CAVE_VALUE, CaveProtocol.CaveCmd.PUSH_CAVE_GROUP_HARD_BATTLE_RESULT_VALUE, response);
        PlayerChannel.push(actorId, packet);
    }
}
