package cn.daxiang.hbtd.gameserver.module.chat.parser.info;

import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;

@Component
public class ChatInfoContext {
    /**
     * key:eventName,value:Collection<ChatInfoParser>
     */
    private Map<String, Collection<ChatInfoParser>> PARSER_MAP = new HashMap<>();

    public void register(Collection<String> eventNames, ChatInfoParser parser) {
        for (String eventName : eventNames) {
            Collection<ChatInfoParser> parsers = PARSER_MAP.get(eventName);
            if (parsers == null) {
                parsers = new HashSet<>();
                PARSER_MAP.put(eventName, parsers);
            }
            parsers.add(parser);
        }
    }

    public Collection<ChatInfoParser> getParser(String eventName) {
        return PARSER_MAP.get(eventName);
    }

    public Collection<String> getEvents() {
        return PARSER_MAP.keySet();
    }
}