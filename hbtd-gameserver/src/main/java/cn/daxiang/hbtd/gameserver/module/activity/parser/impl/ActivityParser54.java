package cn.daxiang.hbtd.gameserver.module.activity.parser.impl;

import cn.daxiang.framework.event.GameEvent;
import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.framework.utils.NumberUtils;
import cn.daxiang.framework.utils.RandomUtils;
import cn.daxiang.framework.utils.TimeConstant;
import cn.daxiang.hbtd.gameserver.core.database.table.ActivityGlobal;
import cn.daxiang.hbtd.gameserver.core.database.table.ActivityRecord;
import cn.daxiang.hbtd.gameserver.core.dataconfig.convert.impl.IntListConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.Activity54ExtraRewardConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.ActivityOpenConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.ActivityTaskOrderConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.ActivityDeleteGoodsConfigService;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.ActivityOpenConfigService;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.ActivityTaskOrderConfigService;
import cn.daxiang.hbtd.gameserver.core.dispatch.DispatchHelper;
import cn.daxiang.hbtd.gameserver.core.event.impl.ActorRechargeEvent;
import cn.daxiang.hbtd.gameserver.core.event.impl.GoodsDeleteEvent;
import cn.daxiang.hbtd.gameserver.core.event.impl.MailAddEvent;
import cn.daxiang.hbtd.gameserver.core.oss.type.OperationType;
import cn.daxiang.hbtd.gameserver.module.activity.model.entity.record.ActivityRecord54;
import cn.daxiang.hbtd.gameserver.module.activity.parser.AbstractActivityParser;
import cn.daxiang.hbtd.gameserver.module.activity.type.ActivityType;
import cn.daxiang.hbtd.gameserver.module.goods.facade.GoodsFacade;
import cn.daxiang.hbtd.gameserver.module.goods.helper.RewardHelper;
import cn.daxiang.hbtd.gameserver.module.mail.type.MailTemplateType;
import cn.daxiang.hbtd.gameserver.module.user.helper.ActorHelper;
import cn.daxiang.hbtd.gameserver.module.user.type.ChargeType;
import cn.daxiang.protocol.game.ActivityInfoProtocol;
import cn.daxiang.protocol.game.CommonProtocol;
import cn.daxiang.protocol.game.TypeProtocol.ResourceId;
import cn.daxiang.protocol.game.TypeProtocol.RewardType;
import cn.daxiang.shared.GlobalConfigKey;
import cn.daxiang.shared.reward.RewardObject;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.protobuf.ByteString;
import com.google.protobuf.InvalidProtocolBufferException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static cn.daxiang.shared.GameModuleStatusCodeConstant.ACTIVITY4_BUY_LEVEL_LOCKED;
import static cn.daxiang.shared.GameModuleStatusCodeConstant.ACTIVITY4_CAN_NOT_BUY_LEVEL;
import static cn.daxiang.shared.GameModuleStatusCodeConstant.ACTIVITY4_CAN_NOT_RECEIVE;
import static cn.daxiang.shared.GameModuleStatusCodeConstant.ACTIVITY4_LEVEL_NOT_ENOUGH;
import static cn.daxiang.shared.GameModuleStatusCodeConstant.ACTIVITY4_NOT_ACTIVATE;
import static cn.daxiang.shared.GameModuleStatusCodeConstant.ACTIVITY4_REWARDS_HAS_RECEIVED;
import static cn.daxiang.shared.GameModuleStatusCodeConstant.ACTIVITY_STATUS_ERROR;
import static cn.daxiang.shared.GameModuleStatusCodeConstant.CONFIG_NOT_FOUND;
import static cn.daxiang.shared.GameModuleStatusCodeConstant.INVALID_PARAM;

/**
 * 任务战令(新)
 *
 * <AUTHOR>
 * @date 2024/1/11
 */
@Component
public class ActivityParser54 extends AbstractActivityParser {
    @Autowired
    private GoodsFacade goodsFacade;

    @Override
    public void registerEvent(Set<String> eventSet) {
        eventSet.addAll(getType().getEventNames());
    }

    @Override
    public TResult<CommonProtocol.RewardResult> receiveReward(long actorId, int activityId, int id, byte[] value) {
        int requestValue;
        try {
            requestValue = ActivityInfoProtocol.Activity54RequestValue.parseFrom(value).getValue();
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("receiveReward byte value error activityId:{}, actorId:{}, id:{}", activityId, actorId, id);
            return TResult.valueOf(CONFIG_NOT_FOUND);
        }
        ActivityOpenConfig activityOpenConfig = ActivityOpenConfigService.getActivityConfig(activityId);
        if (activityOpenConfig == null) {
            LOGGER.error("ActivityOpenConfig not found,activityId:{}", activityId);
            return TResult.valueOf(CONFIG_NOT_FOUND);
        }
        int data = activityOpenConfig.getData();
        if (!isActivityOpen(activityId)) {
            return TResult.valueOf(ACTIVITY_STATUS_ERROR);
        }
        ActivityRecord activityRecord = activityRecordDao.getActivityRecord(actorId, activityId);
        ActivityRecord54 record;
        if (activityRecord == null) {
            record = new ActivityRecord54();
            activityRecord = activityRecordDao.create(actorId, activityId, JSON.toJSONString(record));
        } else {
            record = JSON.parseObject(activityRecord.getRecord(), ActivityRecord54.class);
        }
        int goodsId = ActivityTaskOrderConfigService.getGoodsId(data);
        int goodsNum = goodsFacade.getGoodsNum(actorId, goodsId);
        //根据玩家当前的所拥有的道具数量得到玩家的当前所在等级的配置
        ActivityTaskOrderConfig activityTaskOrderConfig = ActivityTaskOrderConfigService.getActivityTaskOrderConfigByGoodsNum(data, goodsNum);
        if (activityTaskOrderConfig == null) {
            LOGGER.info("activityTaskOrderConfig not found,data:{},goodsNum:{}", data, goodsNum);
            return TResult.valueOf(CONFIG_NOT_FOUND);
        }
        Collection<RewardObject> rewardList = Lists.newArrayList();
        ActivityTaskOrderConfig taskOrderConfig = null;

        switch (id) {
            case 1:
                ActivityTaskOrderConfig maxLevelActivityTaskOrderConfig = ActivityTaskOrderConfigService.getMaxLevelActivityTaskOrderConfig(data);
                if (maxLevelActivityTaskOrderConfig == null) {
                    LOGGER.error("activityTaskOrderConfig not found,data:{}", data);
                    return TResult.valueOf(CONFIG_NOT_FOUND);
                }
                Integer initLevel = ActivityTaskOrderConfigService.getInitLevel(data);
                if (initLevel == null) {
                    return TResult.valueOf(CONFIG_NOT_FOUND);
                }
                for (int i = initLevel; i <= activityTaskOrderConfig.getLevel(); i++) {
                    taskOrderConfig = ActivityTaskOrderConfigService.getActivityTaskOrderConfigByLevel(data, i);
                    if (taskOrderConfig == null) {
                        LOGGER.error("activityTaskOrderConfig not found,data:{},level:{}", data, i);
                        continue;
                    }
                    if (record.isNormalReceive(i)) {
                        continue;
                    }
                    record.receiveNormalReward(i);
                    rewardList.addAll(taskOrderConfig.getNormalRewardList());
                }
                if (record.isActive()) {
                    for (int i = initLevel; i <= activityTaskOrderConfig.getLevel(); i++) {
                        taskOrderConfig = ActivityTaskOrderConfigService.getActivityTaskOrderConfigByLevel(data, i);
                        if (taskOrderConfig == null) {
                            LOGGER.error("activityTaskOrderConfig not found,data:{},level:{}", data, i);
                            continue;
                        }
                        if (record.isSupremeReceive(i)) {
                            continue;
                        }
                        record.receiveSupremeReward(i);
                        rewardList.addAll(taskOrderConfig.getSupremeRewardList());
                    }
                    // 领取盈余奖励
                    int surplusRewardTimes = getSurplusRewardTimes(data, activityTaskOrderConfig.getLevel(), goodsNum);
                    if (surplusRewardTimes > 0) {
                        Activity54ExtraRewardConfig extraRewardConfig = globalConfigService.findConfig(data, Activity54ExtraRewardConfig.class);
                        RewardObject cost = extraRewardConfig.getCostRewardObject();
                        Collection<RewardObject> costReward = Lists.newArrayList(RewardObject.valueOf(cost.getType(), cost.getId(), cost.getCount() * surplusRewardTimes));
                        RewardHelper.decrease(actorId, costReward, OperationType.ACTIVITY_TYPE_54);
                        rewardList.addAll(RewardHelper.multipleRewardList(extraRewardConfig.getRewardList(), surplusRewardTimes));
                        record.addReceivesTimes(surplusRewardTimes);
                    }
                }
                break;
            case 2:
                ActivityGlobal activityGlobal = activityGlobalDao.getActivityGlobal(activityId);
                int countdownDays = globalConfigService.findGlobalConfig(GlobalConfigKey.TASK_ORDER_BUY_DISPLAY_COUNTDOWN_DAYS).findInt();
                long countdownMillis = countdownDays * TimeConstant.ONE_DAY_MILLISECOND;
                if (System.currentTimeMillis() < activityGlobal.getCloseTime() - countdownMillis) {
                    return TResult.valueOf(ACTIVITY4_BUY_LEVEL_LOCKED);
                }
                if (activityTaskOrderConfig.getPrice() == 0) {
                    return TResult.valueOf(ACTIVITY4_CAN_NOT_BUY_LEVEL);
                }
                List<Integer> buyLevelList = globalConfigService.findGlobalObject(GlobalConfigKey.TASK_ORDER_CAN_BUY_LEVEL_LIST, IntListConfig.class).getVs();
                if (!buyLevelList.contains(requestValue)) {
                    return TResult.valueOf(INVALID_PARAM);
                }
                Integer maxLevel = ActivityTaskOrderConfigService.getMaxLevel(data);
                if (maxLevel == null) {
                    return TResult.valueOf(CONFIG_NOT_FOUND);
                }
                int upLevel = activityTaskOrderConfig.getLevel() + requestValue > maxLevel ? maxLevel - activityTaskOrderConfig.getLevel() : requestValue;
                int nextLevel = 0;
                int price = 0;
                for (int i = 0; i < upLevel; i++) {
                    ActivityTaskOrderConfig levelTaskOrderConfig = ActivityTaskOrderConfigService.getActivityTaskOrderConfigByLevel(data, activityTaskOrderConfig.getLevel() + i);
                    if (levelTaskOrderConfig != null) {
                        nextLevel = levelTaskOrderConfig.getLevel() + 1;
                        price += levelTaskOrderConfig.getPrice();
                    }
                }
                if (price <= 0) {
                    return TResult.valueOf(INVALID_PARAM);
                }
                ActivityTaskOrderConfig nextTaskOrderConfig = ActivityTaskOrderConfigService.getActivityTaskOrderConfigByLevel(data, nextLevel);
                if (nextTaskOrderConfig == null) {
                    return TResult.valueOf(CONFIG_NOT_FOUND);
                }
                int discount = globalConfigService.findGlobalConfig(GlobalConfigKey.TASK_ORDER_BUY_LEVEL_DISCOUNT).findInt();
                //购买一级的时候不打折
                if (requestValue == 1) {
                    discount = RandomUtils.TEN_THOUSAND;
                }
                long discountPrice = NumberUtils.getFloorPercentValue(price, discount).longValue();
                Result decreaseResult = RewardHelper.decrease(actorId, Lists.newArrayList(RewardObject.valueOf(RewardType.RESOURCE_VALUE, ResourceId.DIAMOND_VALUE, discountPrice)),
                    OperationType.ACTIVITY_TYPE_54);
                if (decreaseResult.isFail()) {
                    return TResult.valueOf(decreaseResult);
                }
                long needNum = nextTaskOrderConfig.getNeedNumObject().getCount() - activityTaskOrderConfig.getNeedNumObject().getCount();
                rewardList.add(RewardObject.valueOf(nextTaskOrderConfig.getNeedNumObject().getType(), nextTaskOrderConfig.getNeedNumObject().getId(), needNum));
                break;
            case 3:
                if (requestValue > activityTaskOrderConfig.getLevel()) {
                    return TResult.valueOf(ACTIVITY4_LEVEL_NOT_ENOUGH);
                }
                if (record.isNormalReceive(requestValue)) {
                    return TResult.valueOf(ACTIVITY4_REWARDS_HAS_RECEIVED);
                }
                taskOrderConfig = ActivityTaskOrderConfigService.getActivityTaskOrderConfigByLevel(data, requestValue);
                if (taskOrderConfig == null) {
                    LOGGER.error("activityTaskOrderConfig not found,data:{},level:{}", data, requestValue);
                    return TResult.valueOf(CONFIG_NOT_FOUND);
                }
                record.receiveNormalReward(requestValue);
                rewardList.addAll(taskOrderConfig.getNormalRewardList());
                break;
            case 4:
                if (!record.isActive()) {
                    return TResult.valueOf(ACTIVITY4_NOT_ACTIVATE);
                }
                if (requestValue > activityTaskOrderConfig.getLevel()) {
                    return TResult.valueOf(ACTIVITY4_LEVEL_NOT_ENOUGH);
                }
                if (record.isSupremeReceive(requestValue)) {
                    return TResult.valueOf(ACTIVITY4_REWARDS_HAS_RECEIVED);
                }
                taskOrderConfig = ActivityTaskOrderConfigService.getActivityTaskOrderConfigByLevel(data, requestValue);
                if (taskOrderConfig == null) {
                    LOGGER.error("activityTaskOrderConfig not found,data:{},level:{}", data, requestValue);
                    return TResult.valueOf(CONFIG_NOT_FOUND);
                }
                record.receiveSupremeReward(requestValue);
                rewardList.addAll(taskOrderConfig.getSupremeRewardList());
                break;
            default:
                return TResult.valueOf(INVALID_PARAM);
        }
        if (rewardList.isEmpty()) {
            return TResult.valueOf(ACTIVITY4_CAN_NOT_RECEIVE);
        }
        CommonProtocol.RewardResult rewardResult = RewardHelper.sendRewardList(actorId, rewardList, OperationType.ACTIVITY_TYPE_54);
        activityRecord.setRecord(JSON.toJSONString(record));
        dbQueue.updateQueue(activityRecord);
        return TResult.sucess(rewardResult);
    }

    @Override
    public void onEvent(GameEvent event) {
        Collection<ActivityOpenConfig> configList = ActivityOpenConfigService.getActivityOpenConfigList(getType());
        if (configList.isEmpty()) {
            return;
        }
        ActorRechargeEvent actorRechargeEvent = event.convert();
        if (actorRechargeEvent.getChargeType() != ChargeType.CHARGE_DIRECT_PURCHASING) {
            return;
        }
        long actorId = actorRechargeEvent.getActorId();
        int chargeId = actorRechargeEvent.getChargeId();
        for (ActivityOpenConfig activityOpenConfig : configList) {
            int activityId = activityOpenConfig.getId();
            if (!isActivityOpen(activityId)) {
                continue;
            }
            Integer chargeIdByData = ActivityTaskOrderConfigService.getChargeIdByData(activityOpenConfig.getData());
            if (chargeId != chargeIdByData) {
                continue;
            }
            ActivityRecord54 activityRecord;
            ActivityRecord record = activityRecordDao.getActivityRecord(actorId, activityId);
            if (record == null) {
                activityRecord = new ActivityRecord54();
                record = activityRecordDao.create(actorId, activityId, JSON.toJSONString(activityRecord));
            } else {
                activityRecord = JSON.parseObject(record.getRecord(), ActivityRecord54.class);
            }
            activityRecord.activate();
            record.setRecord(JSON.toJSONString(activityRecord));
            dbQueue.updateQueue(record);
            this.pushActivity(actorId, activityId, activityOpenConfig.getActivityType());
        }
    }

    @Override
    protected ActivityType getType() {
        return ActivityType.ACTIVITY_TYPE_54;
    }

    @Override
    protected void onActivityOpen(Collection<ActivityOpenConfig> openActivityIds) {

    }

    @Override
    protected void onActivityEnd(Collection<ActivityOpenConfig> endActivityIds) {
        for (ActivityOpenConfig activityOpenConfig : endActivityIds) {
            // 未领取补发邮件
            int activityId = activityOpenConfig.getId();
            List<Long> activityActorIds = activityRecordDao.getActorIds(activityId);
            for (Long actorId : activityActorIds) {
                rewardClear(actorId, activityOpenConfig);
            }
            // 删除物品
            Collection<Integer> goodsIdList = ActivityDeleteGoodsConfigService.getGoodsIdList(activityOpenConfig.getActivityType());
            if (!goodsIdList.isEmpty()) {
                Collection<Long> goodsActorIds = goodsFacade.getActorIdsByGoodsId(goodsIdList);
                for (Long actorId : goodsActorIds) {
                    DispatchHelper.postEvent(new GoodsDeleteEvent(actorId, goodsIdList, OperationType.ACTIVITY_TYPE_54));
                }
            }
        }
    }

    @Override
    public TResult<ByteString> getRecord2Client(long actorId, int activityId) {
        ActivityRecord activityRecord = activityRecordDao.getActivityRecord(actorId, activityId);
        ActivityInfoProtocol.Activity54Record.Builder builder = ActivityInfoProtocol.Activity54Record.newBuilder();
        if (activityRecord == null) {
            activityRecord = activityRecordDao.create(actorId, activityId, JSON.toJSONString(new ActivityRecord54()));
        }
        ActivityRecord54 record54 = JSON.parseObject(activityRecord.getRecord(), ActivityRecord54.class);
        builder.setIsActivate(record54.isActive());
        builder.addAllNormalReceives(record54.getNormalReceiveList());
        builder.addAllSupremeReceives(record54.getSupremeReceiveList());
        builder.setReceivesTimes(record54.getReceivesTimes());
        return TResult.sucess(builder.build().toByteString());
    }

    @Override
    public TResult<ByteString> getGlobal2Client(long actorId, int activityId) {
        return TResult.fail();
    }

    private void rewardClear(Long actorId, ActivityOpenConfig activityOpenConfig) {
        ActivityRecord record = activityRecordDao.getActivityRecord(actorId, activityOpenConfig.getId());
        if (record == null) {
            return;
        }
        ActivityRecord54 activityRecord = JSON.parseObject(record.getRecord(), ActivityRecord54.class);
        int data = activityOpenConfig.getData();
        int goodsId = ActivityTaskOrderConfigService.getGoodsId(data);
        int goodsNum = goodsFacade.getGoodsNum(actorId, goodsId);
        //根据玩家当前的所拥有的道具数量得到玩家的当前所在等级的配置
        ActivityTaskOrderConfig activityTaskOrderConfig = ActivityTaskOrderConfigService.getActivityTaskOrderConfigByGoodsNum(data, goodsNum);
        int level = 0;
        if (activityTaskOrderConfig != null) {
            level = activityTaskOrderConfig.getLevel();
        }
        Collection<RewardObject> activityRewards = Lists.newArrayList();
        ActivityTaskOrderConfig taskOrderConfig = null;

        for (int i = 1; i <= level; i++) {
            taskOrderConfig = ActivityTaskOrderConfigService.getActivityTaskOrderConfigByLevel(data, i);
            if (taskOrderConfig == null) {
                LOGGER.error("activityTaskOrderConfig not found,data:{},level:{}", data, i);
                continue;
            }
            if (activityRecord.isNormalReceive(i)) {
                continue;
            }
            activityRewards.addAll(taskOrderConfig.getNormalRewardList());
        }
        if (activityRecord.isActive()) {
            Integer maxLevel = ActivityTaskOrderConfigService.getMaxLevel(data);
            if (maxLevel == null) {
                LOGGER.error("ActivityTaskOrderConfigService ActivityTaskOrderConfig not found,data:{} ", data);
                maxLevel = 0;
            }
            for (int i = 1; i <= maxLevel; i++) {
                taskOrderConfig = ActivityTaskOrderConfigService.getActivityTaskOrderConfigByLevel(data, i);
                if (taskOrderConfig == null) {
                    LOGGER.error("activityTaskOrderConfig not found,data:{},level:{}", data, i);
                    continue;
                }
                if (activityRecord.isSupremeReceive(i)) {
                    continue;
                }
                activityRewards.addAll(taskOrderConfig.getSupremeRewardList());
            }
            int surplusRewardTimes = getSurplusRewardTimes(data, level, goodsNum);
            if (surplusRewardTimes > 0) {
                Activity54ExtraRewardConfig extraRewardConfig = globalConfigService.findConfig(data, Activity54ExtraRewardConfig.class);
                activityRewards.addAll(RewardHelper.multipleRewardList(extraRewardConfig.getRewardList(), surplusRewardTimes));
            }
        }
        if (activityRewards.isEmpty()) {
            return;
        }
        activityRewards = RewardHelper.groupByTypeAndId(activityRewards);
        sendRewardMail(actorId, activityRewards);
    }

    private void sendRewardMail(Long actorId, Collection<RewardObject> rewards) {
        Map<String, String> params = Maps.newHashMap();
        params.put("name", ActorHelper.getActorName(actorId));
        MailAddEvent mailAddEvent = new MailAddEvent(actorId, MailTemplateType.ACTIVITY_4_TASK_ORDER_REWARD, params, rewards);
        DispatchHelper.postEvent(mailAddEvent);
        LOGGER.info("Send TASK_ORDER_ACTIVITY_RESET_EVENT Reward complete mailTemplateType:{}, actorId:{}", MailTemplateType.ACTIVITY_4_TASK_ORDER_REWARD, actorId);
    }

    /**
     * 获取盈余奖励次数
     *
     * @param data
     * @param currentLevel
     * @param goodsNum
     * @return
     */
    public int getSurplusRewardTimes(int data, int currentLevel, long goodsNum) {
        ActivityTaskOrderConfig maxLevelActivityTaskOrderConfig = ActivityTaskOrderConfigService.getMaxLevelActivityTaskOrderConfig(data);
        if (maxLevelActivityTaskOrderConfig != null) {
            if (currentLevel >= maxLevelActivityTaskOrderConfig.getLevel()) {
                Activity54ExtraRewardConfig extraRewardConfig = globalConfigService.findConfig(data, Activity54ExtraRewardConfig.class);
                if (extraRewardConfig != null) {
                    RewardObject costReward = extraRewardConfig.getCostRewardObject();
                    long getTimes = (goodsNum - maxLevelActivityTaskOrderConfig.getNeedNumObject().getCount()) / costReward.getCount();
                    return (int) getTimes;
                }
            }
        }
        return 0;
    }
}
