package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.dataconfig.annotation.FieldIgnore;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.shared.reward.RewardObject;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * 星布棋局-直购礼包
 *
 * <AUTHOR>
 * @date 2023/12/28
 */
@DataFile(fileName = "activity_53_charge_reward_config")
public class Activity53ChargeRewardConfig implements ModelAdapter {

    /**
     * data
     */
    private int data;
    /**
     * 唯一Id
     */
    private int id;
    /**
     * 充值ID
     */
    private int chargeId;
    /**
     * 次数
     */
    private int times;

    /**
     * 1-每日限购 * 2-本轮限购
     */
    private int type;

    /**
     * 消耗列表([[rewardType,id,num],[rewardType,id,num]])
     * {@code RewardType}
     * 没有消耗请配空数组
     */
    private String cost;
    /**
     * 奖励([[rewardType,id,num],[rewardType,id,num]])
     * {@code RewardType}
     */
    private String rewards;
    @FieldIgnore
    private List<RewardObject> rewardList = Lists.newArrayList();
    @FieldIgnore
    private List<RewardObject> costList = Lists.newArrayList();

    @Override
    public void initialize() {
        JSONArray rewardsArray = JSONArray.parseArray(rewards);
        for (Object rewardsItem : rewardsArray) {
            JSONArray rewardArray = JSONArray.parseArray(rewardsItem.toString());
            RewardObject rewardObject = RewardObject.valueOf(rewardArray);
            rewardList.add(rewardObject);
        }
        JSONArray costArray = JSON.parseArray(cost);
        for (Object rewardsItem : costArray) {
            JSONArray rewardArray = JSONArray.parseArray(rewardsItem.toString());
            RewardObject rewardObject = RewardObject.valueOf(rewardArray);
            costList.add(rewardObject);
        }
    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(data, id);
    }

    public int getData() {
        return data;
    }

    public int getId() {
        return id;
    }

    public int getChargeId() {
        return chargeId;
    }

    public int getTimes() {
        return times;
    }

    public int getType() {
        return type;
    }

    public List<RewardObject> getRewardList() {
        return rewardList;
    }

    public List<RewardObject> getCostList() {
        return costList;
    }
}
