package cn.daxiang.hbtd.gameserver.module.recruit.facade.impl;

import cn.daxiang.dto.model.RecruitRecordVO;
import cn.daxiang.framework.dispatch.DispatchType;
import cn.daxiang.framework.event.GameEvent;
import cn.daxiang.framework.event.TriggerTime;
import cn.daxiang.framework.event.annotation.Event;
import cn.daxiang.framework.event.annotation.EventOnline;
import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.framework.rpc.RpcCall;
import cn.daxiang.framework.rpc.RpcCallback;
import cn.daxiang.framework.rpc.world.WorldRecruitRpc;
import cn.daxiang.framework.utils.DateUtils;
import cn.daxiang.hbtd.gameserver.core.GameBaseFacade;
import cn.daxiang.hbtd.gameserver.core.GameConfig;
import cn.daxiang.hbtd.gameserver.core.database.table.Recruit;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.RecruitWishListConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.RewardPoolConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.SummonGiftConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.RecruitConfigService;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.RewardConfigService;
import cn.daxiang.hbtd.gameserver.core.dispatch.DispatchHelper;
import cn.daxiang.hbtd.gameserver.core.event.impl.ActorRechargeEvent;
import cn.daxiang.hbtd.gameserver.core.event.impl.RecruitGachaEvent;
import cn.daxiang.hbtd.gameserver.core.oss.type.OperationType;
import cn.daxiang.hbtd.gameserver.core.rpc.WorldRpcHelper;
import cn.daxiang.hbtd.gameserver.module.PbBuilder;
import cn.daxiang.hbtd.gameserver.module.goods.helper.RewardHelper;
import cn.daxiang.hbtd.gameserver.module.recruit.dao.RecruitDao;
import cn.daxiang.hbtd.gameserver.module.recruit.facade.RecruitFacade;
import cn.daxiang.hbtd.gameserver.module.recruit.helper.RecruitHelper;
import cn.daxiang.hbtd.gameserver.module.recruit.helper.RecruitPushHelper;
import cn.daxiang.hbtd.gameserver.module.recruit.type.RecruitGiftType;
import cn.daxiang.hbtd.gameserver.module.system.helper.SettingsHelper;
import cn.daxiang.hbtd.gameserver.module.user.helper.ActorHelper;
import cn.daxiang.hbtd.gameserver.module.user.type.ActorUnlockType;
import cn.daxiang.hbtd.gameserver.module.user.type.ChargeType;
import cn.daxiang.protocol.game.CommonProtocol;
import cn.daxiang.protocol.game.RecruitProtocol;
import cn.daxiang.shared.GlobalConfigKey;
import cn.daxiang.shared.event.EventKey;
import cn.daxiang.shared.reward.RewardObject;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Collection;
import java.util.Map;

/**
 * 名将招募
 *
 * <AUTHOR>
 * @date 2022/6/14
 */
@Component
public class RecruitFacadeImpl extends GameBaseFacade implements RecruitFacade {
    @Autowired
    RecruitDao recruitDao;

    @PostConstruct
    public void init() {

    }

    @Override
    public TResult<Recruit> getRecruit(long actorId) {
        Result unlock = ActorHelper.unlock(actorId, ActorUnlockType.RECRUIT);
        if (unlock.isFail()) {
            return TResult.valueOf(unlock);
        }
        Recruit recruit = recruitDao.getRecruit(actorId);
        return TResult.sucess(recruit);
    }

    @Override
    public TResult<CommonProtocol.IntPacket> chooseWishFamousHero(long actorId, int heroId) {
        TResult<Recruit> result = getRecruit(actorId);
        if (result.isFail()) {
            return TResult.valueOf(result.statusCode);
        }
        Recruit recruit = result.item;
        RecruitWishListConfig recruitWishListConfig = RecruitConfigService.getRecruitWishListConfig(heroId);
        if (recruitWishListConfig == null) {
            LOGGER.error("RecruitWishListConfig is not found! heroId:{}", heroId);
            return TResult.valueOf(CONFIG_NOT_FOUND);
        }
        if (recruitWishListConfig.getOffsetTime() > SettingsHelper.getServerStartDays()) {
            return TResult.valueOf(HERO_ID_NOT_FOUND);
        }
        recruit.setWishHeroId(heroId);
        dbQueue.updateQueue(recruit);
        CommonProtocol.IntPacket response = CommonProtocol.IntPacket.newBuilder().setValue(heroId).build();
        return TResult.sucess(response);
    }

    @Override
    public TResult<RecruitProtocol.RecruitResultResponse> recruitFamousHero(long actorId, int times) {
        int gachaTimesLimit = globalConfigService.findGlobalConfig(GlobalConfigKey.ONCE_GACHA_LIMIT).findInt();
        if (times <= 0 || times > gachaTimesLimit) {
            return TResult.valueOf(INVALID_PARAM);
        }
        TResult<Recruit> result = getRecruit(actorId);
        if (result.isFail()) {
            return TResult.valueOf(result.statusCode);
        }
        Recruit recruit = result.item;
        RecruitWishListConfig config = RecruitConfigService.getRecruitWishListConfig(recruit.getWishHeroId());
        if (config == null) {
            LOGGER.error("RecruitWishListConfig is not found! heroId:{}", recruit.getWishHeroId());
            return TResult.valueOf(HERO_ID_NOT_FOUND);
        }
        if (config.getOffsetTime() > SettingsHelper.getServerStartDays()) {
            return TResult.valueOf(HERO_ID_NOT_FOUND);
        }
        String cost = globalConfigService.findGlobalConfig(GlobalConfigKey.RECRUIT_COST).getValue();
        RewardObject costObject = RewardObject.valueOf(JSON.parseArray(cost));
        costObject.setCount(times);
        Result decrease = RewardHelper.decrease(actorId, Lists.newArrayList(costObject), OperationType.RECRUIT_COST);
        if (decrease.isFail()) {
            return TResult.valueOf(decrease);
        }

        Map<Integer, Collection<RewardObject>> gachaMap = Maps.newLinkedHashMap();
        int totalTimes = recruit.getTotalTimes();
        Collection<RecruitRecordVO> recordVOList = Lists.newArrayList();
        int serverId = GameConfig.getServerId();
        String actorName = ActorHelper.getActorName(actorId);
        for (int i = 0; i < times; i++) {
            totalTimes++;
            if (config.isPrize(totalTimes)) {
                totalTimes = 0;
                RewardHelper.sendRewardList(actorId, Lists.newArrayList(config.getRewardObject()), OperationType.RECRUIT_REWARD);
                gachaMap.put(i, Lists.newArrayList(config.getRewardObject()));
                if (config.getDes() != null && !"".equals(config.getDes())) {
                    recordVOList.add(RecruitRecordVO.valueOf(serverId, actorName, Lists.newArrayList(config.getRewardObject()), config.getDes()));
                }
            } else {
                RewardPoolConfig rewardPoolConfig = RewardConfigService.getRewardPoolConfig(config.getPool());
                for (RewardObject rewardObject : rewardPoolConfig.getRewardList()) {
                    if (rewardObject.equals(config.getRewardObject())) {
                        totalTimes = 0;
                        break;
                    }
                }
                RewardHelper.sendRewardList(actorId, rewardPoolConfig.getRewardList(), OperationType.RECRUIT_REWARD);
                gachaMap.put(i, Lists.newArrayList(rewardPoolConfig.getRewardList()));
                if (rewardPoolConfig.getDes() != null && !"".equals(rewardPoolConfig.getDes())) {
                    recordVOList.add(RecruitRecordVO.valueOf(serverId, actorName, Lists.newArrayList(rewardPoolConfig.getRewardList()), rewardPoolConfig.getDes()));
                }
            }

        }
        if (!recordVOList.isEmpty()) {
            WorldRpcHelper.asynCall(GameConfig.getServerId(), WorldRecruitRpc.class, new RpcCall<WorldRecruitRpc>() {
                @Override
                public void run(WorldRecruitRpc rpcProxy) {
                    rpcProxy.setRecruitRecord(GameConfig.getServerType(), serverId, recordVOList);
                }
            }, new RpcCallback() {

                @Override
                public int getDispatchType() {
                    return DispatchType.ACTOR;
                }

                @SuppressWarnings("unchecked")
                @Override
                public void completed(Object result) {
                    RecruitPushHelper.pushRecruitRecord(actorId, recordVOList);
                }
            });
        }
        recruit.setTimes(recruit.getTimes() + times);
        recruit.setTotalTimes(totalTimes);
        dbQueue.updateQueue(recruit);
        RecruitPushHelper.pushRecruit(actorId, recruit);
        DispatchHelper.postEvent(new RecruitGachaEvent(actorId, times));
        RecruitProtocol.RecruitResultResponse.Builder builder = RecruitProtocol.RecruitResultResponse.newBuilder();
        for (Map.Entry<Integer, Collection<RewardObject>> entry : gachaMap.entrySet()) {
            builder.putRewardObjectMap(entry.getKey(), PbBuilder.buildRewardObjectList(entry.getValue()));
        }
        return TResult.sucess(builder.build());
    }

    @Override
    public TResult<CommonProtocol.RewardResultResponse> buyGoods(long actorId, int id) {
        SummonGiftConfig summonGiftConfig = globalConfigService.findConfig(id, SummonGiftConfig.class);
        if (summonGiftConfig.getType() != RecruitGiftType.GOODS_BUY.getId()) {
            return TResult.valueOf(INVALID_PARAM);
        }
        Result unlock = ActorHelper.unlock(actorId, ActorUnlockType.RECRUIT, 2);
        if (unlock.isFail()) {
            return TResult.valueOf(unlock);
        }
        Recruit recruit = recruitDao.getRecruit(actorId);
        Integer buyTimes = recruit.getBuyTimesMap().getOrDefault(id, 0);
        if (buyTimes >= summonGiftConfig.getDailyTimes()) {
            return TResult.valueOf(RECRUIT_REWARD_HAD_RECEIVE);
        }
        Result result = RewardHelper.decrease(actorId, summonGiftConfig.getCostList(), OperationType.RECRUIT_BUY_GOODS);
        if (result.isFail()) {
            return TResult.valueOf(result);
        }
        recruit.addBuyTimes(id);
        dbQueue.updateQueue(recruit);
        RecruitPushHelper.pushRecruit(actorId, recruit);
        CommonProtocol.RewardResult rewardResult = RewardHelper.sendRewardList(actorId, summonGiftConfig.getRewardList(), OperationType.RECRUIT_BUY_GOODS);
        CommonProtocol.RewardResultResponse.Builder builder = CommonProtocol.RewardResultResponse.newBuilder();
        builder.setRewardResult(rewardResult);
        return TResult.sucess(builder.build());
    }

    @Override
    public TResult<CommonProtocol.RewardResultResponse> receiveGoods(long actorId, int id) {
        SummonGiftConfig summonGiftConfig = globalConfigService.findConfig(id, SummonGiftConfig.class);
        if (summonGiftConfig.getType() != RecruitGiftType.CHARGE_BUY.getId()) {
            return TResult.valueOf(INVALID_PARAM);
        }
        Result unlock = ActorHelper.unlock(actorId, ActorUnlockType.RECRUIT, 2);
        if (unlock.isFail()) {
            return TResult.valueOf(unlock);
        }
        Recruit recruit = recruitDao.getRecruit(actorId);
        Integer activatesTimes = recruit.getActivatesMap().getOrDefault(id, 0);
        if (activatesTimes <= 0) {
            return TResult.valueOf(RECRUIT_REWARD_HAD_RECEIVE);
        }
        recruit.receiveGoods(id);
        dbQueue.updateQueue(recruit);
        RecruitPushHelper.pushRecruit(actorId, recruit);
        CommonProtocol.RewardResult rewardResult = RewardHelper.sendRewardList(actorId, summonGiftConfig.getRewardList(), OperationType.RECRUIT_BUY_GOODS);
        CommonProtocol.RewardResultResponse.Builder builder = CommonProtocol.RewardResultResponse.newBuilder();
        builder.setRewardResult(rewardResult);
        return TResult.sucess(builder.build());
    }

    @Override
    public TResult<RecruitProtocol.RecruitRecordResponse> getRecruitRecords(long actorId) {
        WorldRecruitRpc proxy = WorldRpcHelper.getProxy(WorldRecruitRpc.class);
        Collection<RecruitRecordVO> recruitRecordList = proxy.getRecruitRecord(GameConfig.getServerType(), GameConfig.getServerId());
        RecruitProtocol.RecruitRecordResponse response = RecruitHelper.buildRecruitRecordResponse(recruitRecordList);
        return TResult.sucess(response);
    }

    @EventOnline(trigger = TriggerTime.FIXED_HOUR, triggerValue = 24)
    public void onEverySecond(GameEvent e) {
        this.reset(e.getUniqueId(), true);
    }

    @Event(name = EventKey.ACTOR_LOGIN)
    public void onActorLogin(GameEvent e) {
        this.reset(e.getUniqueId(), false);
    }

    @Event(name = EventKey.ACTOR_RECHARGE_EVENT)
    public void onActorRechargeDisposeEvent(ActorRechargeEvent e) {
        ActorRechargeEvent event = e.convert();
        if (event.getChargeType() != ChargeType.CHARGE_DIRECT_PURCHASING) {
            return;
        }
        SummonGiftConfig summonGiftConfig = RecruitConfigService.getSummonGiftConfig(event.getChargeId());
        if (summonGiftConfig == null) {
            return;
        }
        Recruit recruit = recruitDao.getRecruit(event.getActorId());
        recruit.buyRechargeGift(summonGiftConfig.getId());
        dbQueue.updateQueue(recruit);
        RecruitPushHelper.pushRecruit(event.getActorId(), recruit);
    }

    private void reset(long actorId, boolean isPush) {
        Recruit recruit = recruitDao.getRecruit(actorId);
        if (DateUtils.isToday(recruit.getLastResetTime())) {
            return;
        }
        recruit.reset();
        dbQueue.updateQueue(recruit);
        if (isPush) {
            RecruitPushHelper.pushRecruit(actorId, recruit);
        }
    }

}
