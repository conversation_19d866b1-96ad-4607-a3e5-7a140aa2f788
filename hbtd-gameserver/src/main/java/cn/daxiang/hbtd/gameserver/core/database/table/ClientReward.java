package cn.daxiang.hbtd.gameserver.core.database.table;

import cn.daxiang.framework.database.SingleEntity;
import cn.daxiang.framework.database.annotation.Column;
import cn.daxiang.framework.database.annotation.DBQueueType;
import cn.daxiang.framework.database.annotation.Table;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.protocol.game.ClientrewardProtocol.ClientRewardType;
import com.google.common.collect.Lists;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @author: Gary
 * @date: 2024/3/20 17:06
 * @Description:
 */
@Table(name = "client_reward", type = DBQueueType.IMPORTANT)
public class ClientReward extends SingleEntity<Long> {
    /**
     * 角色ID
     */
    @Column(pk = true)
    private long actorId;
    /**
     * 已领取信息
     * {key:type，value:List<Id>}
     * {@link ClientRewardType}
     */
    @Column(alias = "receives")
    private ConcurrentHashMap<Integer, List<Integer>> receiveMap = new ConcurrentHashMap<>();

    @Override
    public List<IdentiyKey> keyLists() {
        return Lists.newArrayList(findPkId());
    }

    @Override
    public IdentiyKey findPkId() {
        return IdentiyKey.build(actorId);
    }

    @Override
    public void setPkId(IdentiyKey pk) {
        this.actorId = pk.getFirstLongId();
    }

    public long getActorId() {
        return actorId;
    }

    public void setActorId(long actorId) {
        this.actorId = actorId;
    }

    public ConcurrentHashMap<Integer, List<Integer>> getReceiveMap() {
        return receiveMap;
    }

    public void setReceiveMap(ConcurrentHashMap<Integer, List<Integer>> receiveMap) {
        this.receiveMap = receiveMap;
    }

    /**
     * 是否已领取
     *
     * @param type
     * @param id
     * @return
     */
    public boolean isReceive(ClientRewardType type, int id) {
        List<Integer> receiveIds = receiveMap.getOrDefault(type.getNumber(), Collections.emptyList());
        return receiveIds.contains(id);
    }

    /**
     * 领取奖励
     *
     * @param type
     * @param ids
     */
    public void receive(ClientRewardType type, Collection<Integer> ids) {
        List<Integer> receiveIds = receiveMap.computeIfAbsent(type.getNumber(), x -> Lists.newArrayList());
        receiveIds.addAll(ids);
    }

    /**
     * 获取领取列表
     *
     * @param type
     * @return
     */
    public Collection<Integer> getReceiveIds(ClientRewardType type) {
        return receiveMap.getOrDefault(type.getNumber(), Collections.emptyList());
    }
}
