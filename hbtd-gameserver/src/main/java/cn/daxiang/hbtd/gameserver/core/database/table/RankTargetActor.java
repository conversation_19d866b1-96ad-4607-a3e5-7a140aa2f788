package cn.daxiang.hbtd.gameserver.core.database.table;

import cn.daxiang.framework.database.SingleEntity;
import cn.daxiang.framework.database.annotation.Column;
import cn.daxiang.framework.database.annotation.DBQueueType;
import cn.daxiang.framework.database.annotation.Table;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.hbtd.gameserver.module.extension.model.RankTargetInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/7/4
 */
@Table(name = "rank_target_actor", type = DBQueueType.IMPORTANT)
public class RankTargetActor extends SingleEntity<Long> {
    /**
     * 角色ID
     */
    @Column(pk = true)
    private long actorId;
    /**
     * key:type,value:RankRewardInfo
     */
    @Column(alias = "infos")
    private Map<Integer, RankTargetInfo> infoMap = Maps.newConcurrentMap();

    @Override
    public IdentiyKey findPkId() {
        return IdentiyKey.build(actorId);
    }

    @Override
    public void setPkId(IdentiyKey pk) {
        this.actorId = pk.getFirstLongId();
    }

    @Override
    public List<IdentiyKey> keyLists() {
        return Lists.newArrayList(findPkId());
    }

    public long getActorId() {
        return actorId;
    }

    public void putInfo(int type, RankTargetInfo info) {
        this.infoMap.put(type, info);
    }

    public RankTargetInfo getInfo(int type) {
        return this.infoMap.get(type);
    }
}
