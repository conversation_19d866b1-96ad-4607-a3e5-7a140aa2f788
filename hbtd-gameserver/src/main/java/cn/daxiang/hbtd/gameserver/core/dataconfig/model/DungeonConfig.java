package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.dataconfig.annotation.FieldIgnore;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.shared.reward.RewardObject;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;

import java.util.Collection;
import java.util.List;

/**
 * 副本配置
 *
 * <AUTHOR>
 */
@DataFile(fileName = "dungeon_config")
public class DungeonConfig implements ModelAdapter {
    /**
     * 副本ID
     */
    private int configId;
    /**
     * 副本类型
     * {@code DungeonType}
     */
    private int type;
    /**
     * 上一关ID(最好是顺序的,我直接判断当前副本ID大于等于这个就可以打)
     */
    private int preId;
    /**
     * 周几开放
     */
    private String openWeek;
    /**
     * 周几开放时间范围
     */
    @FieldIgnore
    private List<Integer> openWeekList = Lists.newArrayList();
    /**
     * 条件解锁
     */
    private int unlockId;
    /**
     * 奖励类型
     * {@code RewardType}
     */
    private int rewardType;
    /**
     * 奖励ID
     */
    private int rewardId;
    /**
     * 奖励数量最小值
     */
    private long rewardCountMin;
    /**
     * 奖励数量最大值
     */
    private long rewardCountMax;
    /**
     * 战斗ID
     */
    private int battleId;

    @Override
    public void initialize() {
        JSONArray weekArray = JSONArray.parseArray(openWeek);
        for (int i = 0; i < weekArray.size(); i++) {
            Integer oneOfWeek = weekArray.getIntValue(i);
            openWeekList.add(oneOfWeek);
        }
    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(type, configId);
    }

    public int getConfigId() {
        return configId;
    }

    public int getType() {
        return type;
    }

    public int getPreId() {
        return preId;
    }

    public int getUnlockId() {
        return unlockId;
    }

    public long getRewardCountMin() {
        return rewardCountMin;
    }

    public long getRewardCountMax() {
        return rewardCountMax;
    }

    public Collection<RewardObject> getRewardList(long rewardNum) {
        return Lists.newArrayList(RewardObject.valueOf(rewardType, rewardId, rewardNum));
    }

    public Collection<Integer> getOnWeekList() {
        return openWeekList;
    }

    public int getBattleId() {
        return battleId;
    }
}
