package cn.daxiang.hbtd.gameserver.module.extension.parser.fund.impl;

import cn.daxiang.framework.result.CollectionResult;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.hbtd.gameserver.core.database.table.Fund;
import cn.daxiang.hbtd.gameserver.core.database.table.Reputation;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.FundRewardConfig;
import cn.daxiang.hbtd.gameserver.module.extension.model.FundEntity;
import cn.daxiang.hbtd.gameserver.module.extension.parser.fund.AbstractFundParser;
import cn.daxiang.hbtd.gameserver.module.extension.type.FundType;
import cn.daxiang.hbtd.gameserver.module.reputation.facade.ReputationFacade;
import cn.daxiang.shared.reward.RewardObject;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;

import static cn.daxiang.shared.GameModuleStatusCodeConstant.FUND_REWARD_CAN_NOT_RECEIVED;

/**
 * @author: Gary
 * @date: 2023/3/14 14:15
 * @Description:
 */
@Component
public class ReputationFundParser extends AbstractFundParser {
    @Autowired
    private ReputationFacade reputationFacade;

    @Override
    protected FundType getType() {
        return FundType.REPUTATION;
    }

    @Override
    public CollectionResult<RewardObject> parser(long actorId, Collection<FundRewardConfig> configs, Fund fund, boolean isLuxury) {
        Collection<RewardObject> rewardList = Lists.newArrayList();
        TResult<Reputation> tResult = reputationFacade.getReputationInfo(actorId);
        if (tResult.isFail()) {
            return CollectionResult.valueOf(tResult.statusCode);
        }
        Reputation reputation = tResult.item;
        int sum = reputation.getEventTypeTimesMap().values().stream().mapToInt(i -> i).sum();
        FundEntity entity = null;
        Collection<Integer> recieveList;
        for (FundRewardConfig config : configs) {
            entity = fund.getFundInfoMap().get(config.getType().getId());
            recieveList = entity.getFundReceiveList(isLuxury, config.getStage());
            if (recieveList.contains(config.getId())) {
                continue;
            }
            if (config.getMinLimit() <= sum) {
                rewardList.addAll(config.getRewardList(isLuxury));
                entity.receiveFund(config.getStage(), config.getId(), isLuxury);
            } else {
                break;
            }
        }
        if (rewardList.isEmpty()) {
            return CollectionResult.valueOf(FUND_REWARD_CAN_NOT_RECEIVED);
        }
        return CollectionResult.collection(rewardList);
    }
}
