package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.dataconfig.annotation.FieldIgnore;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.shared.reward.RewardObject;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * 活动-33-节日活动-登录有礼配置
 *
 * <AUTHOR>
 * @date 2023/2/18
 */
@DataFile(fileName = "activity_33_sign_config")
public class Activity33SignConfig implements ModelAdapter {

    /**
     * data
     */
    private int data;
    /**
     * 天数
     */
    private int day;
    /**
     * 签到奖励
     */
    private String rewards;
    /**
     * 补签消耗
     */
    private String retroactive;
    /**
     * 领取每日礼包的单日累充金额需求
     */
    private long charge;
    /**
     * 每日礼包奖励
     */
    private String chargeRewards;

    @FieldIgnore
    private List<RewardObject> rewardList = Lists.newArrayList();

    @FieldIgnore
    private List<RewardObject> chargeRewardsList = Lists.newArrayList();

    @FieldIgnore
    private List<RewardObject> retroactiveRewardsList = Lists.newArrayList();

    @Override
    public void initialize() {
        JSONArray rewardsArray = JSONArray.parseArray(rewards);
        for (Object rewardsItem : rewardsArray) {
            JSONArray rewardArray = JSONArray.parseArray(rewardsItem.toString());
            RewardObject rewardObject = RewardObject.valueOf(rewardArray);
            rewardList.add(rewardObject);
        }

        JSONArray chargeRewardsArray = JSONArray.parseArray(chargeRewards);
        for (Object rewardsItem : chargeRewardsArray) {
            JSONArray rewardArray = JSONArray.parseArray(rewardsItem.toString());
            RewardObject rewardObject = RewardObject.valueOf(rewardArray);
            chargeRewardsList.add(rewardObject);
        }

        JSONArray retroactiveRewardsArray = JSONArray.parseArray(retroactive);
        for (Object rewardsItem : retroactiveRewardsArray) {
            JSONArray rewardArray = JSONArray.parseArray(rewardsItem.toString());
            RewardObject rewardObject = RewardObject.valueOf(rewardArray);
            retroactiveRewardsList.add(rewardObject);
        }
    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(data, day);
    }

    public int getData() {
        return data;
    }

    public int getDay() {
        return day;
    }

    public long getCharge() {
        return charge;
    }

    public List<RewardObject> getRewardList() {
        return rewardList;
    }

    public List<RewardObject> getChargeRewardsList() {
        return chargeRewardsList;
    }

    public List<RewardObject> getRetroactiveRewardsList() {
        return retroactiveRewardsList;
    }
}
