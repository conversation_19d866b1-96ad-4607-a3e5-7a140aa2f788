package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.dataconfig.annotation.FieldIgnore;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.shared.reward.RewardObject;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;

import java.util.Collection;

/**
 * @author: <PERSON>
 * @date: 2023/6/2 11:50
 * @Description:
 */
@DataFile(fileName = "charge_gift_config")
public class ChargeGiftConfig implements ModelAdapter {
    /**
     * 唯一Id
     */
    private int id;
    /**
     * 玩法类型
     */
    private int type;
    /**
     * 充值Id
     */
    private int chargeId;
    /**
     * 消耗列表<br />[[类型,id,数量],[类型,id,数量]]<br />没有则填[]
     */
    private String costs;
    /**
     * 奖励列表<br />[[类型,id,数量],[类型,id,数量]]
     */
    private String rewards;
    /**
     * 限购次数
     */
    private int limit;

    @FieldIgnore
    private Collection<RewardObject> rewardList = Lists.newArrayList();
    @FieldIgnore
    private Collection<RewardObject> costList = Lists.newArrayList();

    @Override
    public void initialize() {
        JSONArray rewardArray = JSONArray.parseArray(rewards);
        for (Object rewardItem : rewardArray) {
            JSONArray array = JSONArray.parseArray(rewardItem.toString());
            RewardObject rewardObject = RewardObject.valueOf(array);
            rewardList.add(rewardObject);
        }
        JSONArray costArray = JSONArray.parseArray(costs);
        for (Object costItem : costArray) {
            JSONArray array = JSONArray.parseArray(costItem.toString());
            RewardObject rewardObject = RewardObject.valueOf(array);
            costList.add(rewardObject);
        }
    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(id);
    }

    public int getId() {
        return id;
    }

    public int getType() {
        return type;
    }

    public int getChargeId() {
        return chargeId;
    }

    public int getLimit() {
        return limit;
    }

    public Collection<RewardObject> getRewardList() {
        return rewardList;
    }

    public Collection<RewardObject> getCostList() {
        return costList;
    }
}
