package cn.daxiang.hbtd.gameserver.module.soul.type;

import cn.daxiang.hbtd.gameserver.module.user.type.ActorUnlockType;

/**
 * 武魂类型
 *
 * <AUTHOR>
 * @date 2023/2/1
 */
public enum SoulType {
    NONE(0, ActorUnlockType.NONE, 0, 0),
    /**
     * 属性类武魂
     */
    SOUL_TYPE_1(1, ActorUnlockType.SOUL_TYPE_1, 0, 6),
    /**
     * 回合技能类武魂
     */
    SOUL_TYPE_2(2, ActorUnlockType.SOUL_TYPE_2, 4, 1),
    /**
     * 塔防技能类武魂
     */
    SOUL_TYPE_3(3, ActorUnlockType.SOUL_TYPE_3, 4, 1),
    ;
    private int id;
    /**
     * 武魂类型对应的解锁类型
     */
    private ActorUnlockType unlockType;
    /**
     * 英雄品质限制
     */
    private int qualityLimit;
    /**
     * 英雄品质限制孔数
     */
    private int limit;

    private SoulType(int id, ActorUnlockType unlockType, int qualityLimit, int limit) {
        this.id = id;
        this.unlockType = unlockType;
        this.qualityLimit = qualityLimit;
        this.limit = limit;
    }

    public static SoulType getType(int id) {
        for (SoulType currencyType : SoulType.values()) {
            if (currencyType.id == id) {
                return currencyType;
            }
        }
        return NONE;
    }

    public int getId() {
        return id;
    }

    public ActorUnlockType getUnlockType() {
        return unlockType;
    }

    public int getQualityLimit() {
        return qualityLimit;
    }

    public int getLimit() {
        return limit;
    }
}
