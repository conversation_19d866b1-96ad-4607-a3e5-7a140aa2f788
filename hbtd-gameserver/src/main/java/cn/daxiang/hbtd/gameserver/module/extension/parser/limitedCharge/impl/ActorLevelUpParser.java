package cn.daxiang.hbtd.gameserver.module.extension.parser.limitedCharge.impl;

import cn.daxiang.framework.result.TResult;
import cn.daxiang.framework.utils.FormulaUtils;
import cn.daxiang.hbtd.gameserver.core.database.table.LimitedTimeCharge;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.LimitedTimeChargeConfig;
import cn.daxiang.hbtd.gameserver.core.event.impl.ActorLevelUpEvent;
import cn.daxiang.hbtd.gameserver.module.extension.model.LimitedTimeChargeEntity;
import cn.daxiang.hbtd.gameserver.module.extension.parser.limitedCharge.AbstractLimitedChargeParser;
import cn.daxiang.hbtd.gameserver.module.extension.type.LimitedTimeChargeType;
import cn.daxiang.hbtd.gameserver.module.user.helper.ActorHelper;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/6/10
 */
@Component
public class ActorLevelUpParser extends AbstractLimitedChargeParser<ActorLevelUpEvent> {
    @Override
    protected LimitedTimeChargeType getType() {
        return LimitedTimeChargeType.LEVEL_UP;
    }

    @Override
    protected TResult<LimitedTimeChargeEntity> parseCondition(ActorLevelUpEvent event, LimitedTimeChargeConfig config, LimitedTimeCharge limitedTimeCharge) {
        int actorVipLevel = ActorHelper.getVipLevel(event.getActorId());
        long actorMaxPower = ActorHelper.getActorMaxPower(event.getActorId());
        for (int i = event.oldLevel + 1; i <= event.newLevel; i++) {
            if (FormulaUtils.executeBool(config.getCondition(), i, actorVipLevel, actorMaxPower)) {
                return TResult.sucess(limitedTimeCharge.addEntity(config));
            }
        }
        return TResult.fail();
    }
}
