package cn.daxiang.hbtd.gameserver.module.battle.parser.effect.impl;

import cn.daxiang.hbtd.gameserver.core.dataconfig.model.SkillConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.SkillEffectConfig;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleRoom;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleSprite;
import cn.daxiang.hbtd.gameserver.module.battle.model.FightReport;
import cn.daxiang.hbtd.gameserver.module.battle.parser.effect.AbstractSkillEffectParser;
import cn.daxiang.hbtd.gameserver.module.battle.type.SkillEffectType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/10/18
 */
@Component
public class HPMaAbsoluteValuexEffectParser extends AbstractSkillEffectParser {

    @Override
    public boolean execute(FightReport report, BattleSprite attacker, BattleSprite targeter, SkillConfig skillConfig, SkillEffectConfig effectConfig, BattleRoom battleRoom) {
        if (targeter.isDead()) {
            return false;
        }
        int value = (int) effectConfig.calcSkillEffect();
        long hpMax = targeter.getSpriteBattle().getHPMax() + value;
        hpMax = Math.max(-targeter.getSpriteBattle().getHPMax() + 1, hpMax);
        targeter.getSpriteBattle().initHP(hpMax);
        return true;
    }

    @Override
    protected SkillEffectType getType() {
        return SkillEffectType.HP_MAX_ABSOLUTE_VALUE_EFFECT;
    }
}

