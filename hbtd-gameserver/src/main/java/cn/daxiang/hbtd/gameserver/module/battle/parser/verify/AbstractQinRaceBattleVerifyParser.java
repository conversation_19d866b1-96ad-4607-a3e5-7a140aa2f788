package cn.daxiang.hbtd.gameserver.module.battle.parser.verify;

import cn.daxiang.framework.result.TResult;
import cn.daxiang.hbtd.gameserver.module.qinrace.facade.QinRaceFacade;
import cn.daxiang.hbtd.gameserver.module.qinrace.helper.QinRacePushHelper;
import cn.daxiang.protocol.game.CommonProtocol;
import cn.daxiang.protocol.game.QinraceProtocol;
import cn.daxiang.shared.type.PVEVerifyParameterKey;
import com.google.protobuf.InvalidProtocolBufferException;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/6/27
 */
public abstract class AbstractQinRaceBattleVerifyParser extends AbstractBattleVerifyParser {
    @Autowired
    private QinRaceFacade qinRaceFacade;

    @Override
    protected String getClientData(byte[] data) throws InvalidProtocolBufferException {
        QinraceProtocol.QinraceBattleRequest request = QinraceProtocol.QinraceBattleRequest.parseFrom(data);
        return request.getCheck();
    }

    @Override
    protected String getBattleRequest(byte[] data) throws InvalidProtocolBufferException {
        QinraceProtocol.QinraceBattleRequest request = QinraceProtocol.QinraceBattleRequest.parseFrom(data);
        return request.toString();
    }

    @Override
    protected void verifyResult(long actorId, Map<PVEVerifyParameterKey, Object> parameter, boolean result) throws InvalidProtocolBufferException {
        QinraceProtocol.QinraceBattleRequest request = QinraceProtocol.QinraceBattleRequest.parseFrom((byte[]) parameter.get(PVEVerifyParameterKey.BATTLE_VERIFY_REQUEST));
        TResult<QinraceProtocol.QinraceBattleRespones> battleResult = qinRaceFacade.verifyResult(actorId, request, result);
        QinRacePushHelper.pushQinRaceBattleResult(actorId, battleResult);
    }
}
