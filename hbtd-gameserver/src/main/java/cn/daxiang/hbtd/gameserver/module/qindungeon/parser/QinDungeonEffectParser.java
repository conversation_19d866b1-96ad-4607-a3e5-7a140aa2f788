package cn.daxiang.hbtd.gameserver.module.qindungeon.parser;

import cn.daxiang.framework.result.TResult;
import cn.daxiang.hbtd.gameserver.core.database.table.QinDungeon;
import cn.daxiang.hbtd.gameserver.core.database.table.QinRace;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.QinDungeonRougueEffectConfig;

/**
 * <AUTHOR>
 * @date 2022/8/3
 */
public interface QinDungeonEffectParser {
    /**
     * @param qinDungeon
     * @param config
     */
    TResult<String> parser(QinDungeon qinDungeon, QinDungeonRougueEffectConfig config);

    /**
     * @param qinRace
     * @param config
     */
    TResult<String> parser(QinRace qinRace, QinDungeonRougueEffectConfig config);
}
