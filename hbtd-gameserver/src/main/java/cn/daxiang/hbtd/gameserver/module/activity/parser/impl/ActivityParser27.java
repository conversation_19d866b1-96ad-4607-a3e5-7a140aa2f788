package cn.daxiang.hbtd.gameserver.module.activity.parser.impl;

import cn.daxiang.framework.context.ApplicationInitCompleteEvent;
import cn.daxiang.framework.event.GameEvent;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.framework.utils.DateUtils;
import cn.daxiang.hbtd.gameserver.core.database.table.ActivityRecord;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.Activity27GiftStoreConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.ActivityOpenConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.Activity27ConfigService;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.ActivityDeleteGoodsConfigService;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.ActivityOpenConfigService;
import cn.daxiang.hbtd.gameserver.core.dispatch.DispatchHelper;
import cn.daxiang.hbtd.gameserver.core.event.impl.ActorRechargeEvent;
import cn.daxiang.hbtd.gameserver.core.event.impl.MailAddEvent;
import cn.daxiang.hbtd.gameserver.core.netty.channel.PlayerChannel;
import cn.daxiang.hbtd.gameserver.core.oss.type.OperationType;
import cn.daxiang.hbtd.gameserver.module.activity.model.entity.record.ActivityRecord27;
import cn.daxiang.hbtd.gameserver.module.activity.parser.AbstractActivityParser;
import cn.daxiang.hbtd.gameserver.module.activity.type.Activity27PriceType;
import cn.daxiang.hbtd.gameserver.module.activity.type.Activity27Type;
import cn.daxiang.hbtd.gameserver.module.activity.type.ActivityType;
import cn.daxiang.hbtd.gameserver.module.goods.helper.RewardHelper;
import cn.daxiang.hbtd.gameserver.module.mail.type.MailTemplateType;
import cn.daxiang.hbtd.gameserver.module.user.helper.ActorHelper;
import cn.daxiang.hbtd.gameserver.module.user.type.ChargeType;
import cn.daxiang.protocol.game.ActivityInfoProtocol;
import cn.daxiang.protocol.game.CommonProtocol;
import cn.daxiang.protocol.game.TypeProtocol;
import cn.daxiang.shared.reward.RewardObject;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.protobuf.ByteString;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static cn.daxiang.shared.GameModuleStatusCodeConstant.ACTIVITY_REWARD_HAD_RECEIVED;
import static cn.daxiang.shared.GameModuleStatusCodeConstant.CONFIG_ERROR;
import static cn.daxiang.shared.GameModuleStatusCodeConstant.CONFIG_NOT_FOUND;

/**
 * 天赐神将-礼包商店
 */
@Component
public class ActivityParser27 extends AbstractActivityParser implements ApplicationListener<ApplicationInitCompleteEvent> {

    @Override
    public void onApplicationEvent(ApplicationInitCompleteEvent event) {
        schedule.addFixedTime(new Runnable() {
            @Override
            public void run() {
                Collection<ActivityOpenConfig> configList = ActivityOpenConfigService.getActivityOpenConfigList(getType());
                if (configList.isEmpty()) {
                    return;
                }
                for (ActivityOpenConfig activityOpenConfig : configList) {
                    if (!isActivityOpen(activityOpenConfig.getId())) {
                        continue;
                    }
                    Set<Long> onlineActorList = PlayerChannel.onlineActorList();
                    for (Long actorId : onlineActorList) {
                        pushActivity(actorId, activityOpenConfig.getId(), activityOpenConfig.getActivityType());
                    }
                }
            }
        }, 24);
    }

    @Override
    public void registerEvent(Set<String> eventSet) {
        eventSet.addAll(getType().getEventNames());
    }

    @Override
    public TResult<CommonProtocol.RewardResult> receiveReward(long actorId, int activityId, int id, byte[] value) {
        ActivityOpenConfig activityOpenConfig = ActivityOpenConfigService.getActivityConfig(activityId);
        if (activityOpenConfig == null) {
            LOGGER.error("ActivityOpenConfig not found, id:{}", activityId);
            return TResult.valueOf(CONFIG_NOT_FOUND);
        }
        Map<Integer, Activity27GiftStoreConfig> configMap = Activity27ConfigService.getActivity27GiftStoreConfigMap(activityOpenConfig.getData());
        if (configMap == null) {
            LOGGER.error("ActivityLimitedTimeSaleConfig is null activityId:{} data:{}", activityOpenConfig.getId(), activityOpenConfig.getData());
            return TResult.valueOf(CONFIG_NOT_FOUND);
        }

        Activity27GiftStoreConfig config = globalConfigService.findConfig(IdentiyKey.build(activityOpenConfig.getData(), id), Activity27GiftStoreConfig.class);
        if (config == null) {
            LOGGER.error("ActivityLimitedTimeSaleConfig not found, activityId:{} data:{} id:{}", activityId, activityOpenConfig.getData(), id);
            return TResult.valueOf(CONFIG_NOT_FOUND);
        }

        ActivityRecord27 activityRecord;
        ActivityRecord record = activityRecordDao.getActivityRecord(actorId, activityId);
        if (record == null) {
            activityRecord = new ActivityRecord27();
            record = activityRecordDao.create(actorId, activityId, JSON.toJSONString(activityRecord));
        } else {
            activityRecord = JSON.parseObject(record.getRecord(), ActivityRecord27.class);
        }

        int receivesTimes = activityRecord.getReceives().getOrDefault(id, 0);
        if (config.getType() == Activity27PriceType.DIRECT_PURCHASING_BUY.getId()) {
            // 直购
            int buyTimes = activityRecord.getBuyTimes().getOrDefault(id, 0);
            if (receivesTimes >= buyTimes) {
                return TResult.valueOf(ACTIVITY_REWARD_HAD_RECEIVED);
            }
        } else if (config.getType() == Activity27PriceType.DIAMOND_BUY.getId()) {
            // 元宝购买
            if (receivesTimes >= config.getTimes()) {
                return TResult.valueOf(ACTIVITY_REWARD_HAD_RECEIVED);
            }
            Result result = RewardHelper.decrease(actorId, config.getCostList(), OperationType.ACTIVITY_TYPE_27);
            if (result.isFail()) {
                return TResult.valueOf(result);
            }
        } else {
            LOGGER.error("Activity27GiftStoreConfig error not find Type:{} id:{}", config.getType(), id);
            return TResult.valueOf(CONFIG_ERROR);
        }
        activityRecord.receives(id, 1);
        record.setRecord(JSON.toJSONString(activityRecord));
        dbQueue.updateQueue(record);
        CommonProtocol.RewardResult result = RewardHelper.sendRewardList(actorId, config.getRewardList(), OperationType.ACTIVITY_TYPE_27);
        return TResult.sucess(result);
    }

    @Override
    public void onEvent(GameEvent event) {
        Collection<ActivityOpenConfig> configList = ActivityOpenConfigService.getActivityOpenConfigList(getType());
        if (configList.isEmpty()) {
            return;
        }
        ActorRechargeEvent actorRechargeEvent = event.convert();
        if (actorRechargeEvent.getChargeType() != ChargeType.CHARGE_DIRECT_PURCHASING) {
            return;
        }
        long actorId = actorRechargeEvent.getActorId();
        for (ActivityOpenConfig activityOpenConfig : configList) {
            int activityId = activityOpenConfig.getId();
            if (isActivityOpen(activityId) == false) {
                continue;
            }
            Map<Integer, Activity27GiftStoreConfig> configMap = Activity27ConfigService.getActivity27GiftStoreConfigMap(activityOpenConfig.getData());
            if (configMap == null) {
                LOGGER.error("ActivityLimitedTimeSaleConfig is null activityId:{} data:{}", activityOpenConfig.getId(), activityOpenConfig.getData());
                return;
            }
            reset(actorId, activityId);
            ActivityRecord27 activityRecord;
            ActivityRecord record = activityRecordDao.getActivityRecord(actorId, activityId);
            if (record == null) {
                activityRecord = new ActivityRecord27();
                record = activityRecordDao.create(actorId, activityId, JSON.toJSONString(activityRecord));
            } else {
                activityRecord = JSON.parseObject(record.getRecord(), ActivityRecord27.class);
            }
            Activity27GiftStoreConfig config = configMap.get(actorRechargeEvent.getChargeId());
            if (config == null) {
                return;
            }

            activityRecord.buyTimes(config.getId(), 1);
            record.setRecord(JSON.toJSONString(activityRecord));
            dbQueue.updateQueue(record);
            this.pushActivity(actorId, activityId, activityOpenConfig.getActivityType());
        }
    }

    @Override
    protected ActivityType getType() {
        return ActivityType.ACTIVITY_TYPE_27;
    }

    @Override
    protected void onActivityOpen(Collection<ActivityOpenConfig> openActivityIds) {

    }

    @Override
    protected void onActivityEnd(Collection<ActivityOpenConfig> endActivityIds) {
        // 发送未领取的奖励邮件
        for (ActivityOpenConfig activityOpenConfig : endActivityIds) {
            int activityId = activityOpenConfig.getId();
            List<Long> actorIds = activityRecordDao.getActorIds(activityId);
            for (Long actorId : actorIds) {
                rewardClear(actorId, activityOpenConfig);
            }
        }
    }

    @Override
    public TResult<ByteString> getRecord2Client(long actorId, int activityId) {

        reset(actorId, activityId);
        ActivityRecord record = activityRecordDao.getActivityRecord(actorId, activityId);
        ActivityInfoProtocol.Activity13Record.Builder builder = ActivityInfoProtocol.Activity13Record.newBuilder();
        if (record != null) {
            ActivityRecord27 activityRecord = JSON.parseObject(record.getRecord(), ActivityRecord27.class);
            builder.putAllDayBuyTimes(activityRecord.getDayBuyTimes());
            builder.putAllBuyTimes(activityRecord.getBuyTimes());
            builder.putAllReceives(activityRecord.getReceives());
        }
        return TResult.sucess(builder.build().toByteString());
    }

    @Override
    public TResult<ByteString> getGlobal2Client(long actorId, int activityId) {
        return TResult.fail();
    }

    private void rewardClear(Long actorId, ActivityOpenConfig activityOpenConfig) {
        ActivityRecord record = activityRecordDao.getActivityRecord(actorId, activityOpenConfig.getId());
        if (record == null) {
            return;
        }
        ActivityRecord27 activityRecord = JSON.parseObject(record.getRecord(), ActivityRecord27.class);
        if (activityRecord.getBuyTimes().isEmpty()) {
            return;
        }
        Collection<RewardObject> activityRewards = Lists.newArrayList();

        Map<Integer, Activity27GiftStoreConfig> configMap = Activity27ConfigService.getActivity27GiftStoreConfigMap(activityOpenConfig.getData());
        if (configMap == null) {
            LOGGER.error("Activity27GiftStoreConfig is null activityId:{} data:{}", activityOpenConfig.getId(), activityOpenConfig.getData());
            return;
        }

        for (Activity27GiftStoreConfig config : configMap.values()) {
            if (config.getType() != Activity27PriceType.DIRECT_PURCHASING_BUY.getId()) {
                continue;
            }
            int receivesTimes = activityRecord.getReceives().getOrDefault(config.getId(), 0);
            int buyTimes = activityRecord.getBuyTimes().getOrDefault(config.getId(), 0);
            if (buyTimes > receivesTimes) {
                int times = buyTimes - receivesTimes;
                activityRecord.receives(config.getId(), times);
                for (int i = 0; i < times; i++) {
                    activityRewards.addAll(config.getRewardList());
                }
            }
        }

        // 活动结束补发购买奖励时，不补发神将招募令、神将币等物品
        Collection<Integer> goodsIdList = ActivityDeleteGoodsConfigService.getGoodsIdList(activityOpenConfig.getActivityType());
        activityRewards = RewardHelper.groupByTypeAndId(activityRewards);
        Iterator<RewardObject> iterator = activityRewards.iterator();
        while (iterator.hasNext()) {
            RewardObject next = iterator.next();
            if (next.getType() == TypeProtocol.RewardType.GOODS.getNumber() && goodsIdList.contains(next.getId())) {
                iterator.remove();
            }
        }

        if (activityRewards.isEmpty()) {
            return;
        }
        record.setRecord(JSON.toJSONString(activityRecord));
        dbQueue.updateQueue(record);
        sendRewardMail(actorId, activityRewards);
    }

    private void sendRewardMail(Long actorId, Collection<RewardObject> rewards) {
        Map<String, String> params = Maps.newHashMap();
        params.put("name", ActorHelper.getActorName(actorId));
        MailAddEvent mailAddEvent = new MailAddEvent(actorId, MailTemplateType.ACTIVITY_27_RECHARGE_UNCLAIMED_REWARD, params, rewards);
        DispatchHelper.postEvent(mailAddEvent);
        LOGGER.info("Send ACTIVITY_27 mail Reward complete, actorId:{}", actorId);
    }

    /**
     * 每天重置购买次数
     */
    private void reset(long actorId, int activityId) {
        ActivityOpenConfig activityConfig = ActivityOpenConfigService.getActivityConfig(activityId);
        if (activityConfig == null) {
            return;
        }
        ActivityRecord record = activityRecordDao.getActivityRecord(actorId, activityId);
        if (record != null) {
            ActivityRecord27 activityRecord = JSON.parseObject(record.getRecord(), ActivityRecord27.class);
            if (!DateUtils.isToday(activityRecord.getResetTime())) {
                List<Activity27GiftStoreConfig> configList =
                    Activity27ConfigService.getActivity27GiftStoreConfig(activityConfig.getData(), Activity27Type.REFRESH_EVERY_DAY.getId());
                List<Activity27GiftStoreConfig> resetList = Lists.newArrayList();
                for (Activity27GiftStoreConfig config : configList) {
                    if (config.getType() == Activity27PriceType.DIAMOND_BUY.getId()) {
                        resetList.add(config);
                    }
                }
                activityRecord.reset(resetList);
                record.setRecord(JSON.toJSONString(activityRecord));
                dbQueue.updateQueue(record);
            }
        }
    }
}
