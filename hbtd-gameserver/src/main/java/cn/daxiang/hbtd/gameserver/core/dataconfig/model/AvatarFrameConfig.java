package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.identity.IdentiyKey;

/**
 * 头像框表
 */
@DataFile(fileName = "avatar_frame_config")
public class AvatarFrameConfig implements ModelAdapter {
    /**
     * 头像框ID
     */
    private int id;

    @Override
    public void initialize() {
    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(id);
    }

    public int getId() {
        return id;
    }
}
