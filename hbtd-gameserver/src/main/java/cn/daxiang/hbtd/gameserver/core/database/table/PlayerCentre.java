package cn.daxiang.hbtd.gameserver.core.database.table;

import cn.daxiang.framework.database.SingleEntity;
import cn.daxiang.framework.database.annotation.Column;
import cn.daxiang.framework.database.annotation.DBQueueType;
import cn.daxiang.framework.database.annotation.Table;
import cn.daxiang.framework.identity.IdentiyKey;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * 玩家中心
 *
 * <AUTHOR>
 * @date 2022/8/26
 */
@Table(name = "player_centre", type = DBQueueType.IMPORTANT)
public class PlayerCentre extends SingleEntity<Long> {
    /**
     * 角色ID
     */
    @Column(pk = true)
    private long actorId;
    /**
     * 是否领取防骗指南奖励
     */
    @Column
    private boolean antiFraudReward;
    /**
     * 每日反馈次数
     */
    @Column
    private int feedbackTimes;
    /**
     * 最后一次重置时间
     */
    @Column
    private long lastResetTime;

    @Override
    public IdentiyKey findPkId() {
        return IdentiyKey.build(actorId);
    }

    @Override
    public void setPkId(IdentiyKey pk) {
        this.actorId = pk.getFirstLongId();
    }

    @Override
    public List<IdentiyKey> keyLists() {
        return Lists.newArrayList(findPkId());
    }

    public long getActorId() {
        return actorId;
    }

    public boolean isAntiFraudReward() {
        return antiFraudReward;
    }

    public void setAntiFraudReward(boolean antiFraudReward) {
        this.antiFraudReward = antiFraudReward;
    }

    public int getFeedbackTimes() {
        return feedbackTimes;
    }

    public long getLastResetTime() {
        return lastResetTime;
    }

    public void feedBack() {
        this.feedbackTimes++;
    }

    public void reset() {
        this.feedbackTimes = 0;
        this.lastResetTime = System.currentTimeMillis();
    }
}
