package cn.daxiang.hbtd.gameserver.module.task.parser.impl;

import cn.daxiang.hbtd.gameserver.core.database.table.Hero;
import cn.daxiang.hbtd.gameserver.core.database.table.Task;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.TaskConfig;
import cn.daxiang.hbtd.gameserver.core.event.impl.HeroFosterActivateEvent;
import cn.daxiang.hbtd.gameserver.module.hero.facade.HeroFacade;
import cn.daxiang.hbtd.gameserver.module.task.parser.AbstractTaskParser;
import cn.daxiang.hbtd.gameserver.module.task.type.TaskConditionType;
import cn.daxiang.protocol.game.TaskProtocol;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;

/**
 * x个英雄培养到x阶段   （满的阶段）
 */
@Component
public class HeroFosterStageTaskParser extends AbstractTaskParser<HeroFosterActivateEvent> {
    @Autowired
    HeroFacade heroFacade;

    @Override
    public void init(Task task, TaskConfig taskConfig) {
        long actorId = task.getActorId();
        Collection<Hero> heroList = heroFacade.getHeroList(actorId);
        if (heroList.isEmpty()) {
            return;
        }
        Integer limit = Integer.valueOf(taskConfig.getCondition());
        int value = 0;
        for (Hero hero : heroList) {
            if (hero.getFosterLevel() <= limit) {
                continue;
            }
            value++;
            if (value >= taskConfig.getValue()) {
                break;
            }
        }
        if (task.getValue() < value) {
            task.setValue(value);
        }
        if (task.getValue() >= taskConfig.getValue()) {
            task.setStatus(TaskProtocol.TaskStatus.TASK_FINISH);
        }
    }

    @Override
    protected TaskConditionType getType() {
        return TaskConditionType.HERO_FOSTER_STAGE;
    }

    @Override
    protected boolean parseCondition(HeroFosterActivateEvent event, Task task, TaskConfig taskConfig) {
        //因为我这里每次都是重新拉数据过来算，为了减少计算量所以增加判断当任务完成时不再计算。 一般情况下都是不加这个判断的
        if (task.getStatus() == TaskProtocol.TaskStatus.TASK_FINISH) {
            return false;
        }
        init(task, taskConfig);
        return true;
    }

}
