package cn.daxiang.hbtd.gameserver.module.battle.parser.effect.impl;

import cn.daxiang.hbtd.gameserver.core.dataconfig.model.SkillConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.SkillEffectConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.SkillConfigService;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleBuff;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleRoom;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleSprite;
import cn.daxiang.hbtd.gameserver.module.battle.model.FightReport;
import cn.daxiang.hbtd.gameserver.module.battle.parser.effect.AbstractSkillEffectParser;
import cn.daxiang.hbtd.gameserver.module.battle.type.SkillEffectType;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Iterator;
import java.util.Map;

/**
 * 伤害提升被动buff（攻击时生效）
 *
 * <AUTHOR>
 * @date 2020/3/26
 */
@Component
public class DamageBonusPassiveBuffEffectParser extends AbstractSkillEffectParser {
    @Override
    protected SkillEffectType getType() {
        return SkillEffectType.DAMAGE_BONUS_PASSIVE_BUFF_EFFECT;
    }

    @Override
    public boolean execute(FightReport report, BattleSprite attacker, BattleSprite targeter, SkillConfig skillConfig, SkillEffectConfig effectConfig, BattleRoom battleRoom) {
        this.addBattleBuff(report, attacker, targeter, skillConfig, effectConfig, battleRoom);
        return true;
    }

    @Override
    public long calcEffectValue(BattleSprite battleSprite, BattleSprite targetSprite) {
        //      * x1：带有buff的角色攻击力
        //      * x2：带有buff的角色攻击对象的攻击力
        //      * x3：带有buff的角色当前生命值（绝对值）
        //      * x4：带有buff的角色攻击对象的生命值（绝对值）
        long attack = battleSprite.getAttack();
        long targetAttack = targetSprite.getAttack();
        long hp = battleSprite.getSpriteBattle().getHP();
        long targetHP = targetSprite.getSpriteBattle().getHP();
        Collection<BattleBuff> buffList = battleSprite.getBuffList(getType());
        Map<Integer, Collection<Long>> buffValueMap = Maps.newHashMap();
        for (Iterator<BattleBuff> iterator = buffList.iterator(); iterator.hasNext(); ) {
            BattleBuff buff = iterator.next();
            SkillEffectConfig effectConfig = SkillConfigService.getSkillEffectConfig(buff.getEffectId());
            long buffValue = effectConfig.calcSkillEffect(attack, targetAttack, hp, targetHP);
            Collection<Long> buffValues = buffValueMap.get(buff.getEffectId());
            if (buffValues == null) {
                buffValues = Lists.newArrayList();
                buffValueMap.put(buff.getEffectId(), buffValues);
            }
            buffValues.add(buffValue);
        }
        return this.getBuffValue(buffValueMap);
    }

}
