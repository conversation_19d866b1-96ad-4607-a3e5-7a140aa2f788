package cn.daxiang.hbtd.gameserver.module.immortals.facade.impl;

import cn.daxiang.framework.event.GameEvent;
import cn.daxiang.framework.event.TriggerTime;
import cn.daxiang.framework.event.annotation.Event;
import cn.daxiang.framework.event.annotation.EventOnline;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.framework.result.CollectionResult;
import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.framework.rpc.world.WorldImmortalsTurntableRpc;
import cn.daxiang.framework.utils.DateUtils;
import cn.daxiang.framework.utils.FormulaUtils;
import cn.daxiang.hbtd.gameserver.core.GameBaseFacade;
import cn.daxiang.hbtd.gameserver.core.GameConfig;
import cn.daxiang.hbtd.gameserver.core.database.table.Actor;
import cn.daxiang.hbtd.gameserver.core.database.table.Goods;
import cn.daxiang.hbtd.gameserver.core.database.table.Hero;
import cn.daxiang.hbtd.gameserver.core.database.table.Immortals;
import cn.daxiang.hbtd.gameserver.core.database.table.ImmortalsTurntable;
import cn.daxiang.hbtd.gameserver.core.dataconfig.convert.impl.IntListConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.convert.impl.RewardObjectMapConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.GoodsConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.ImmortalsColorConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.ImmortalsConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.ImmortalsQuenchingLevelConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.ImmortalsStarConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.ImmortalsTurntableChargeRewardConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.ImmortalsTurntableConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.ImmortalsTurntableStageRewardConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.RewardPoolConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.HeroConfigService;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.ImmortalsConfigService;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.ImmortalsTurntableConfigService;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.RewardConfigService;
import cn.daxiang.hbtd.gameserver.core.dispatch.DispatchHelper;
import cn.daxiang.hbtd.gameserver.core.event.impl.ActorLoginEvent;
import cn.daxiang.hbtd.gameserver.core.event.impl.ActorRechargeEvent;
import cn.daxiang.hbtd.gameserver.core.event.impl.ImmortalsColorLevelUpEvent;
import cn.daxiang.hbtd.gameserver.core.event.impl.ImmortalsQuenchingEvent;
import cn.daxiang.hbtd.gameserver.core.event.impl.ImmortalsStarUpEvent;
import cn.daxiang.hbtd.gameserver.core.event.impl.ImmortalsTurntableGachaEvent;
import cn.daxiang.hbtd.gameserver.core.event.impl.ImmortalsTurntableWinningEvent;
import cn.daxiang.hbtd.gameserver.core.event.impl.LineupPowerRefreshEvent;
import cn.daxiang.hbtd.gameserver.core.oss.GameOssLogger;
import cn.daxiang.hbtd.gameserver.core.oss.type.OperationType;
import cn.daxiang.hbtd.gameserver.core.rpc.WorldRpcHelper;
import cn.daxiang.hbtd.gameserver.module.PbBuilder;
import cn.daxiang.hbtd.gameserver.module.goods.facade.GoodsFacade;
import cn.daxiang.hbtd.gameserver.module.goods.helper.RewardHelper;
import cn.daxiang.hbtd.gameserver.module.goods.type.GoodsType;
import cn.daxiang.hbtd.gameserver.module.hero.facade.HeroFacade;
import cn.daxiang.hbtd.gameserver.module.immortals.dao.ImmortalsDao;
import cn.daxiang.hbtd.gameserver.module.immortals.dao.ImmortalsTurntableDao;
import cn.daxiang.hbtd.gameserver.module.immortals.facade.ImmortalsFacade;
import cn.daxiang.hbtd.gameserver.module.immortals.helper.ImmortalsHelper;
import cn.daxiang.hbtd.gameserver.module.immortals.helper.ImmortalsPushHelper;
import cn.daxiang.hbtd.gameserver.module.lineup.helper.LineupHelper;
import cn.daxiang.hbtd.gameserver.module.user.helper.ActorHelper;
import cn.daxiang.hbtd.gameserver.module.user.helper.ActorPushHelper;
import cn.daxiang.hbtd.gameserver.module.user.type.ActorUnlockType;
import cn.daxiang.protocol.game.CommonProtocol;
import cn.daxiang.protocol.game.CommonProtocol.RewardResult;
import cn.daxiang.protocol.game.ImmortalsProtocol;
import cn.daxiang.protocol.game.TypeProtocol.ActorFieldType;
import cn.daxiang.protocol.game.TypeProtocol.RewardType;
import cn.daxiang.shared.GlobalConfigKey;
import cn.daxiang.shared.event.EventKey;
import cn.daxiang.shared.module.immortalsTurntable.ImmortalsTurntableRecordEntity;
import cn.daxiang.shared.reward.RewardObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.TreeMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @date 2022/7/1
 */
@Component
public class ImmortalsFacadeImpl extends GameBaseFacade implements ImmortalsFacade {
    @Autowired
    private ImmortalsDao immortalsDao;
    @Autowired
    private GoodsFacade goodsFacade;
    @Autowired
    private ImmortalsTurntableDao immortalsTurntableDao;
    @Autowired
    private HeroFacade heroFacade;

    @Override
    public TResult<Immortals> getImmortals(long actorId, long immortalsId) {
        Immortals immortals = immortalsDao.getImmortals(actorId, immortalsId);
        if (immortals == null) {
            return TResult.valueOf(IMMORTALS_NOT_FOUND);
        }
        return TResult.sucess(immortals);
    }

    @Override
    public CollectionResult<Immortals> getImmortalsList(long actorId) {
        Collection<Immortals> immortalsList = immortalsDao.getImmortalsList(actorId);
        return CollectionResult.collection(immortalsList);
    }

    @Override
    public TResult<Immortals> getImmortalsByConfigId(long actorId, int configId) {
        Immortals immortals = immortalsDao.getImmortalsByConfigId(actorId, configId);
        if (immortals == null) {
            return TResult.valueOf(HERO_NOT_FOUND);
        }
        return TResult.sucess(immortals);
    }

    @Override
    public CollectionResult<Immortals> createImmortals(long actorId, Map<Integer, Long> data, OperationType operationType) {
        int quenchingLevel = globalConfigService.findGlobalConfig(GlobalConfigKey.IMMORTALS_INIT_QUENCHING_LEVEL).findInt();
        Collection<Immortals> immortalsList = Lists.newArrayList();
        for (Map.Entry<Integer, Long> entry : data.entrySet()) {
            int id = entry.getKey();
            long count = entry.getValue();
            ImmortalsConfig immortalsConfig = globalConfigService.findConfig(IdentiyKey.build(id), ImmortalsConfig.class);
            if (immortalsConfig == null) {
                LOGGER.error("ImmortalsConfig not found,id:{}", id);
                continue;
            }
            for (long i = 0; i < count; i++) {
                Immortals immortals = immortalsDao.createImmortals(actorId, id, quenchingLevel);
                GameOssLogger.goodsAdd(actorId, operationType, RewardType.IMMORTALS, immortals.getImmortalsId(), immortals.getConfigId(), 1, 1);
                immortalsList.add(immortals);
            }
        }
        ImmortalsPushHelper.pushImmortalsList(actorId, immortalsList);
        return CollectionResult.collection(immortalsList);
    }

    //计算专武淬炼所需要消耗的物品Map
    private TResult<Map<Long, Integer>> countGoodsMap(long actorId, int level, Immortals immortals) {
        if (level <= 0) {
            return TResult.valueOf(INVALID_PARAM);
        }
        //升级所需经验
        int needExp = -immortals.getQuenchingExp();
        for (int i = 0; i < level; i++) {
            ImmortalsQuenchingLevelConfig config = globalConfigService.findConfig(IdentiyKey.build(immortals.getQuenchingLevel() + i), ImmortalsQuenchingLevelConfig.class);
            //配置的nextLevel为0，代表已经是满级了，所以退出循环
            if (config.getNextLevel() == 0) {
                break;
            }
            needExp += config.getNeedExp();
        }
        Map<Long, Integer> goodsMap = Maps.newHashMap();
        if (needExp < 0) {
            return TResult.sucess(goodsMap);
        }
        List<Integer> goodsIds = globalConfigService.findGlobalObject(GlobalConfigKey.IMMORTALS_QUENCHING_GOODS_ID_LIST, IntListConfig.class).getVs();
        for (Integer goodsId : goodsIds) {
            List<Goods> goodsList = goodsFacade.getGoodsList(actorId, goodsId);
            Collections.sort(goodsList);
            for (Goods goods : goodsList) {
                long goodsUid = goods.getGoodsUid();
                GoodsConfig goodsConfig = globalConfigService.findConfig(goodsId, GoodsConfig.class);
                if (goodsConfig == null) {
                    return TResult.valueOf(CONFIG_NOT_FOUND);
                }
                if (goodsConfig.getGoodsType() != GoodsType.RESOURCE) {
                    return TResult.valueOf(GOODS_TYPE_ERROR);
                }
                //需要物品的个数
                int needNum = (int) Math.ceil((double) needExp / goodsConfig.calcEffectValue());
                if (needNum <= goods.getNum()) {
                    goodsMap.put(goodsUid, needNum);
                    return TResult.sucess(goodsMap);
                } else {
                    needExp -= goodsConfig.calcEffectValue() * goods.getNum();
                    goodsMap.put(goodsUid, goods.getNum());
                }
            }
        }
        return TResult.sucess(goodsMap);
    }

    @Override
    public Result quenching(long actorId, Map<Long, Integer> times) {
        Result res = ActorHelper.unlock(actorId, ActorUnlockType.IMMORTALS, 2);
        if (res.isFail()) {
            return res;
        }
        if (!LineupHelper.immortalsAllInLineup(actorId, times.keySet())) {
            return Result.valueOf(IMMORTALS_CAN_NOT_QUENCHING);
        }
        int multiples = globalConfigService.findGlobalConfig(GlobalConfigKey.IMMORTALS_QUENCHING_ACTOR_LEVEL_MULTIPLES).findInt();
        int actorLevel = ActorHelper.getActorLevel(actorId);
        Collection<Immortals> immortalsList = Lists.newArrayList();
        int quenchingLevelTimes = 0;
        int quenchingTimes = 0;
        for (Map.Entry<Long, Integer> entry : times.entrySet()) {
            TResult<Immortals> immortalsResult = this.getImmortals(actorId, entry.getKey());
            if (immortalsResult.isFail()) {
                continue;
            }
            Immortals immortals = immortalsResult.item;
            int oldLevel = immortals.getQuenchingLevel();
            int limitLevel = actorLevel * multiples - oldLevel;
            if (limitLevel == 0) {
                continue;
            }
            //计算专武淬炼所需消耗的物品
            TResult<Map<Long, Integer>> goodsMapTResult = countGoodsMap(actorId, Math.min(entry.getValue(), limitLevel), immortals);
            if (goodsMapTResult.isFail()) {
                continue;
            }
            Map<Long, Integer> goodsMap = goodsMapTResult.item;
            quenchingTimes++;
            TResult<Immortals> refineResult = this.quenching(actorId, immortals, goodsMap, immortals.getQuenchingLevel() + limitLevel);
            if (refineResult.isFail()) {
                return Result.valueOf(refineResult.statusCode);
            }
            immortalsList.add(immortalsResult.item);
            quenchingLevelTimes += immortals.getQuenchingLevel() - oldLevel;

        }
        if (!immortalsList.isEmpty()) {
            ImmortalsPushHelper.pushImmortalsList(actorId, immortalsList);
            DispatchHelper.postEvent(new ImmortalsQuenchingEvent(actorId, immortalsList, quenchingTimes, quenchingLevelTimes));
            if (LineupHelper.immortalsInLineup(actorId, times.keySet())) {
                DispatchHelper.postEvent(new LineupPowerRefreshEvent(actorId));
            }
        } else {
            return Result.valueOf(IMMORTALS_QUENCHING_ERROR);
        }
        return Result.valueOf();
    }

    private TResult<Immortals> quenching(long actorId, Immortals immortals, Map<Long, Integer> goodsMap, int levelLimit) {

        //物品提供的经验总值
        int addExp = 0;
        for (Map.Entry<Long, Integer> goodsEntry : goodsMap.entrySet()) {
            Result result = goodsFacade.hasEnoughGoods(actorId, goodsEntry.getKey(), goodsEntry.getValue());
            if (result.isFail()) {
                return TResult.valueOf(result);
            }
            TResult<Goods> goodsResult = goodsFacade.getGoods(actorId, goodsEntry.getKey());
            if (goodsResult.isFail()) {
                return TResult.valueOf(GOODS_NOT_FOUND);
            }
            GoodsConfig goodsConfig = globalConfigService.findConfig(goodsResult.item.getGoodsId(), GoodsConfig.class);
            if (goodsConfig == null) {
                return TResult.valueOf(CONFIG_NOT_FOUND);
            }
            addExp += goodsConfig.calcEffectValue() * goodsEntry.getValue();
        }
        goodsMap.forEach((k, v) -> goodsFacade.decreaseGoods(actorId, k, v, OperationType.IMMORTAL_QUENCHING_COST));
        //当前专武的淬炼等级
        int quenchingLevel = immortals.getQuenchingLevel();
        //当前专武的淬炼的多余经验
        int quenchingExp = immortals.getQuenchingExp();

        ImmortalsQuenchingLevelConfig quenchingConfig = null;
        do {
            quenchingConfig = globalConfigService.findConfig(quenchingLevel, ImmortalsQuenchingLevelConfig.class);
            if (quenchingConfig == null) {
                LOGGER.error("ImmortalsQuenchingLevelConfig not found,quenchingLevel:{}", immortals.getQuenchingLevel());
                break;
            }
            if (quenchingConfig.getNeedExp() <= quenchingExp + addExp) {
                if (quenchingConfig.getNextLevel() == 0 || quenchingLevel >= levelLimit) {
                    quenchingExp = (int) addExp;
                    break;
                } else {
                    addExp -= quenchingConfig.getNeedExp() - quenchingExp;
                    quenchingLevel++;
                    quenchingExp = 0;
                }
            } else {
                quenchingExp += addExp;
                addExp = 0;
            }
        } while (addExp > 0);
        if (quenchingLevel == immortals.getQuenchingLevel() && quenchingExp == immortals.getQuenchingExp()) {
            return TResult.valueOf(GOODS_NOT_ENOUGH);
        }
        immortals.quenching(quenchingLevel, quenchingExp);
        dbQueue.updateQueue(immortals);
        return TResult.sucess(immortals);
    }

    @Override
    public Result starUp(long actorId, long immortalsId) {
        Result res = ActorHelper.unlock(actorId, ActorUnlockType.IMMORTALS, 3);
        if (res.isFail()) {
            return res;
        }
        TResult<Immortals> immortalsResult = this.getImmortals(actorId, immortalsId);
        if (immortalsResult.isFail()) {
            return Result.valueOf(immortalsResult.statusCode);
        }
        Immortals immortals = immortalsResult.item;
        int oldLevel = immortals.getStarLevel();
        ImmortalsConfig immortalsConfig = globalConfigService.findConfig(immortals.getConfigId(), ImmortalsConfig.class);
        if (immortalsConfig == null) {
            LOGGER.error("ImmortalsConfig not found,id:{}", immortals.getConfigId());
            return Result.valueOf(CONFIG_NOT_FOUND);
        }
        ImmortalsStarConfig nextConfig = globalConfigService.findConfig(IdentiyKey.build(immortals.getConfigId(), immortals.getStarLevel() + 1), ImmortalsStarConfig.class);
        if (nextConfig == null) {
            return Result.valueOf(IMMORTALS_STAR_LEVEL_MAX);
        }
        ImmortalsStarConfig config = globalConfigService.findConfig(IdentiyKey.build(immortals.getConfigId(), immortals.getStarLevel()), ImmortalsStarConfig.class);
        if (config == null) {
            LOGGER.error("ImmortalsStarConfig is not found!actorId:{},configId:{},starLevel:{}", actorId, immortals.getConfigId(), immortals.getStarLevel());
            return Result.valueOf(CONFIG_NOT_FOUND);
        }
        Result decrease =
            RewardHelper.decrease(actorId, Lists.newArrayList(RewardObject.valueOf(RewardType.GOODS_VALUE, immortalsConfig.getFragmentId(), config.getFragmentCount())),
                OperationType.IMMORTAL_NIRVANA_COST);
        if (decrease.isFail()) {
            return decrease;
        }
        immortals.addStarLevel();
        dbQueue.updateQueue(immortals);
        ImmortalsPushHelper.pushImmortalsList(actorId, Lists.newArrayList(immortals));
        DispatchHelper.postEvent(new ImmortalsStarUpEvent(actorId, immortalsConfig.getQuality(), immortals.getConfigId(), oldLevel, immortals.getStarLevel()));
        if (LineupHelper.immortalsInLineup(actorId, Lists.newArrayList(immortals.getImmortalsId()))) {
            DispatchHelper.postEvent(new LineupPowerRefreshEvent(actorId));
        }
        return Result.valueOf();
    }

    @Override
    public TResult<ImmortalsTurntable> getImmortalsTurntableInfo(long actorId) {
        Result res = ActorHelper.unlock(actorId, ActorUnlockType.HEGEMONY, 4);
        if (res.isFail()) {
            return TResult.valueOf(res.statusCode);
        }
        ImmortalsTurntable immortalsTurntable = immortalsTurntableDao.getImmortalsTurntable(actorId);
        return TResult.sucess(immortalsTurntable);
    }

    @Override
    public TResult<ImmortalsProtocol.TurntableRewardRecordResponse> getImmortalsTurntableRewardRecordInfo(long actorId) {
        List<ImmortalsTurntableRecordEntity> recordEntities =
            WorldRpcHelper.getProxy(WorldImmortalsTurntableRpc.class).getImmortalsTurntableRewardRecord(GameConfig.getServerType(), GameConfig.getServerId());
        return TResult.sucess(ImmortalsHelper.buildTurntableRewardRecordResponse(recordEntities));
    }

    @Override
    public Result changeHeroPool(long actorId, int heroId) {
        TResult<ImmortalsTurntable> tResult = getImmortalsTurntableInfo(actorId);
        if (tResult.isFail()) {
            return Result.valueOf(tResult.statusCode);
        }
        ImmortalsTurntable immortalsTurntable = tResult.item;
        Optional<ImmortalsTurntableConfig> optional = ImmortalsTurntableConfigService.getImmortalsTurntableConfig(heroId);
        if (!optional.isPresent()) {
            return Result.valueOf(CONFIG_NOT_FOUND);
        }
        immortalsTurntable.setHeroId(heroId);
        dbQueue.updateQueue(immortalsTurntable);
        ImmortalsProtocol.ImmortalsTurntableInfoResponse response = ImmortalsHelper.buildImmortalsTurntableInfoResponse(immortalsTurntable);
        ImmortalsPushHelper.pushImmortalsTurntableInfo(actorId, response);
        return Result.valueOf();
    }

    @Override
    public TResult<CommonProtocol.RewardObjectList> immortalsTurntableGacha(long actorId, int times) {
        TResult<ImmortalsTurntable> tResult = getImmortalsTurntableInfo(actorId);
        if (tResult.isFail()) {
            return TResult.valueOf(tResult.statusCode);
        }
        ImmortalsTurntable immortalsTurntable = tResult.item;
        int heroId = immortalsTurntable.getHeroId();
        ImmortalsTurntableConfig config = globalConfigService.findConfig(IdentiyKey.build(heroId), ImmortalsTurntableConfig.class);
        if (config == null) {
            return TResult.valueOf(CONFIG_NOT_FOUND);
        }
        Optional<TreeMap<Integer, ImmortalsTurntableStageRewardConfig>> optional = ImmortalsTurntableConfigService.getStageRewardConfigs();
        if (!optional.isPresent()) {
            return TResult.valueOf(CONFIG_NOT_FOUND);
        }
        if (immortalsTurntable.getTimes() >= optional.get().lastEntry().getValue().getConditionTimes()) {
            return TResult.valueOf(IMMORTALS_CON_NOT_GACHA);
        }
        RewardObjectMapConfig costConfig = globalConfigService.findGlobalObject(GlobalConfigKey.IMMORTALS_TURNTABLE_GACHA_COST, RewardObjectMapConfig.class);
        RewardObject cost = costConfig.getRewardObjectByKey(times);
        if (cost == null) {
            return TResult.valueOf(CONFIG_ERROR);
        }
        Result decrease = RewardHelper.decrease(actorId, Lists.newArrayList(cost), OperationType.IMMORTALS_TURNTABLE);
        if (decrease.isFail()) {
            return TResult.valueOf(decrease.statusCode);
        }
        int poolId = config.getPoolId();
        Collection<RewardObject> rewards = Lists.newArrayList();
        Collection<RewardObject> messageList = Lists.newArrayList();
        for (int i = 0; i < times; i++) {
            RewardPoolConfig rewardPoolConfig = RewardConfigService.getRewardPoolConfig(poolId);
            if (rewardPoolConfig == null) {
                return TResult.valueOf(CONFIG_NOT_FOUND);
            }
            List<RewardObject> rewardList = rewardPoolConfig.getRewardList();
            if (rewardPoolConfig.getDes() != null && !"".equals(rewardPoolConfig.getDes())) {
                Map<Byte, Object> attributeMap = ActorHelper.getActorAttributeMap(actorId);
                int limit = globalConfigService.findGlobalConfig(GlobalConfigKey.IMMORTALS_TURNTABLE_REWARD_RECORD_LIMIT).findInt();
                WorldRpcHelper.getProxy(WorldImmortalsTurntableRpc.class)
                    .refreshImmortalsTurntableRewardRecord(GameConfig.getServerType(), GameConfig.getServerId(), attributeMap, rewardList, rewardPoolConfig.getDes(), limit);
                for (RewardObject rewardObject : rewardList) {
                    DispatchHelper.postEvent(new ImmortalsTurntableWinningEvent(actorId, rewardObject.getType(), rewardObject.getId(), rewardObject.getCount()));
                }
            }
            rewards.addAll(rewardList);
        }
        RewardHelper.sendRewardList(actorId, rewards, OperationType.IMMORTALS_TURNTABLE);
        immortalsTurntable.addTimes(times);
        dbQueue.updateQueue(immortalsTurntable);
        DispatchHelper.postEvent(new ImmortalsTurntableGachaEvent(actorId, times));
        ImmortalsProtocol.ImmortalsTurntableInfoResponse response = ImmortalsHelper.buildImmortalsTurntableInfoResponse(immortalsTurntable);
        ImmortalsPushHelper.pushImmortalsTurntableInfo(actorId, response);
        return TResult.sucess(PbBuilder.buildRewardObjectList(rewards));
    }

    @Override
    public TResult<CommonProtocol.RewardResult> receiveChargeReward(long actorId, int configId) {
        TResult<ImmortalsTurntable> tResult = getImmortalsTurntableInfo(actorId);
        if (tResult.isFail()) {
            return TResult.valueOf(tResult.statusCode);
        }
        ImmortalsTurntable immortalsTurntable = tResult.item;
        ImmortalsTurntableChargeRewardConfig chargeRewardConfig = globalConfigService.findConfig(IdentiyKey.build(configId), ImmortalsTurntableChargeRewardConfig.class);
        if (chargeRewardConfig == null) {
            return TResult.valueOf(CONFIG_NOT_FOUND);
        }
        if (chargeRewardConfig.getChargeId() != 0) {
            if (immortalsTurntable.isReceive(configId)) {
                return TResult.valueOf(ACTIVITY_REWARD_TIMES_LIMIT);
            }
        } else {
            if (chargeRewardConfig.getTimes() <= immortalsTurntable.getDayTimes(configId)) {
                return TResult.valueOf(ACTIVITY_REWARD_TIMES_LIMIT);
            }
        }
        if (!chargeRewardConfig.getCostList().isEmpty()) {
            Result result = RewardHelper.decrease(actorId, chargeRewardConfig.getCostList(), OperationType.IMMORTALS_TURNTABLE);
            if (result.isFail()) {
                return TResult.valueOf(result.statusCode);
            }
            immortalsTurntable.buyReward(configId);
        }
        immortalsTurntable.receiveReward(configId);
        dbQueue.updateQueue(immortalsTurntable);
        ImmortalsProtocol.ImmortalsTurntableInfoResponse response = ImmortalsHelper.buildImmortalsTurntableInfoResponse(immortalsTurntable);
        ImmortalsPushHelper.pushImmortalsTurntableInfo(actorId, response);
        return TResult.sucess(RewardHelper.sendRewardList(actorId, chargeRewardConfig.getRewardList(), OperationType.IMMORTALS_TURNTABLE));
    }

    @Override
    public TResult<CommonProtocol.RewardResult> receiveStageReward(long actorId, int configId) {
        TResult<ImmortalsTurntable> tResult = getImmortalsTurntableInfo(actorId);
        if (tResult.isFail()) {
            return TResult.valueOf(tResult.statusCode);
        }
        ImmortalsTurntable immortalsTurntable = tResult.item;
        ImmortalsTurntableStageRewardConfig stageRewardConfig = globalConfigService.findConfig(IdentiyKey.build(configId), ImmortalsTurntableStageRewardConfig.class);
        if (stageRewardConfig == null) {
            return TResult.valueOf(CONFIG_NOT_FOUND);
        }
        if (immortalsTurntable.getTimes() < stageRewardConfig.getConditionTimes()) {
            return TResult.valueOf(ACTIVITY_NOT_FINISH_FOR_RECEIVED);
        }
        if (immortalsTurntable.isReceiveStage(configId)) {
            return TResult.valueOf(ACTIVITY_REWARD_HAD_RECEIVED);
        }
        Optional<TreeMap<Integer, ImmortalsTurntableStageRewardConfig>> optional = ImmortalsTurntableConfigService.getStageRewardConfigs();
        if (!optional.isPresent()) {
            return TResult.valueOf(CONFIG_NOT_FOUND);
        }
        immortalsTurntable.receiveStage(configId);
        if (immortalsTurntable.getStageIds().containsAll(optional.get().keySet())) {
            int conditionTimes = optional.get().lastEntry().getValue().getConditionTimes();
            immortalsTurntable.resetStage(conditionTimes);
        }
        dbQueue.updateQueue(immortalsTurntable);
        ImmortalsProtocol.ImmortalsTurntableInfoResponse response = ImmortalsHelper.buildImmortalsTurntableInfoResponse(immortalsTurntable);
        ImmortalsPushHelper.pushImmortalsTurntableInfo(actorId, response);
        return TResult.sucess(RewardHelper.sendRewardList(actorId, stageRewardConfig.getRewardList(), OperationType.IMMORTALS_TURNTABLE));
    }

    @Override
    public long getImmortalsScore(long actorId, long immortalsId, long heroId) {
        TResult<Immortals> immortalsResult = this.getImmortals(actorId, immortalsId);
        if (immortalsResult.isFail()) {
            return 0l;
        }
        Immortals immortals = immortalsResult.item;
        ImmortalsConfig immortalsConfig = globalConfigService.findConfig(immortals.getConfigId(), ImmortalsConfig.class);
        if (immortalsConfig == null) {
            LOGGER.error("ImmortalsConfig not found,id:{}", immortals.getConfigId());
            return 0l;
        }
        //是否为自身专武
        boolean selfImmortals = false;
        if (immortalsConfig.getHeroId() == heroId) {
            selfImmortals = true;
        }
        return FormulaUtils.executeRoundingLong(immortalsConfig.getScoreExpr(), immortals.getQuenchingLevel(), immortals.getStarLevel(), selfImmortals, immortals.getColorLevel());
    }

    @Override
    public Result inherit(long actorId, long beInheritedImmortalsId, long inheritImmortalsId) {
        TResult<Immortals> beInheritedResult = this.getImmortals(actorId, beInheritedImmortalsId);
        if (beInheritedResult.isFail()) {
            return Result.valueOf(beInheritedResult.statusCode);
        }
        Immortals beInheritedImmortals = beInheritedResult.item;
        TResult<Immortals> inheritResult = this.getImmortals(actorId, inheritImmortalsId);
        if (inheritResult.isFail()) {
            return Result.valueOf(inheritResult.statusCode);
        }
        Immortals inheritImmortals = inheritResult.item;
        //继承专武淬炼等级
        int quenchingLevel = inheritImmortals.getQuenchingLevel();
        int quenchingExp = inheritImmortals.getQuenchingExp();
        inheritImmortals.setQuenchingLevel(beInheritedImmortals.getQuenchingLevel());
        inheritImmortals.setQuenchingExp(beInheritedImmortals.getQuenchingExp());
        beInheritedImmortals.setQuenchingLevel(quenchingLevel);
        beInheritedImmortals.setQuenchingExp(quenchingExp);
        dbQueue.updateQueue(inheritImmortals);
        dbQueue.updateQueue(beInheritedImmortals);
        ImmortalsPushHelper.pushImmortalsList(actorId, Lists.newArrayList(inheritImmortals, beInheritedImmortals));
        return Result.valueOf();
    }

    @Override
    public TResult<CommonProtocol.RewardResultResponse> immortalsSynthesis(long actorId, int configId) {
        ImmortalsConfig immortalsConfig = globalConfigService.findConfig(configId, ImmortalsConfig.class);
        if (immortalsConfig == null) {
            LOGGER.error("ImmortalsConfig not found,id:{}", configId);
            return TResult.valueOf(CONFIG_NOT_FOUND);
        }

        TResult<Immortals> immortalsTResult = getImmortalsByConfigId(actorId, configId);
        if (immortalsTResult.isOk()) {
            return TResult.valueOf(IMMORTALS_IS_EXIST);
        }
        RewardObject rewardObject = RewardObject.valueOf(RewardType.GOODS.getNumber(), immortalsConfig.getFragmentId(), immortalsConfig.getInitNumber());
        List<RewardObject> fragmentList = Lists.newArrayList(rewardObject);
        Result result = RewardHelper.decrease(actorId, fragmentList, OperationType.HERO_SYNTHESIZE);
        if (result.isFail()) {
            return TResult.valueOf(result);
        }
        CommonProtocol.RewardResult rewardResult =
            RewardHelper.sendRewardList(actorId, Lists.newArrayList(RewardObject.valueOf(RewardType.IMMORTALS_VALUE, configId, 1)), OperationType.IMMORTALS_SYNTHESIZE);
        CommonProtocol.RewardResultResponse response = PbBuilder.buildRewardResultResponse(rewardResult);
        return TResult.sucess(response);
    }

    @Override
    public TResult<CommonProtocol.RewardResultResponse> fragmentDecompose(long actorId, Map<Integer, Integer> goodsMap) {
        Map<Integer, Integer> decomposeMap = Maps.newHashMap();
        for (Map.Entry<Integer, Integer> entry : goodsMap.entrySet()) {
            ImmortalsConfig immortalsConfig = ImmortalsConfigService.getImmortalsConfigByFragment(entry.getKey());
            if (immortalsConfig == null) {
                continue;
            }
            TResult<Immortals> immortalsTResult = getImmortalsByConfigId(actorId, immortalsConfig.getConfigId());
            if (immortalsTResult.isFail()) {
                continue;
            }
            int immortalsMaxStarLevel = ImmortalsConfigService.getImmortalsMaxStarLevel(immortalsConfig.getConfigId());
            if (immortalsMaxStarLevel > immortalsTResult.item.getStarLevel()) {
                continue;
            }
            decomposeMap.put(entry.getKey(), entry.getValue());
        }
        TResult<CommonProtocol.RewardResultResponse> decomposeResult = goodsFacade.decompose(actorId, decomposeMap);
        return decomposeResult;
    }

    @Override
    public Result colorLevelUp(long actorId, long immortalsId) {
        Result res = ActorHelper.unlock(actorId, ActorUnlockType.IMMORTALS_COLOR);
        if (res.isFail()) {
            return Result.valueOf(res.statusCode);
        }
        TResult<Immortals> immortalsResult = getImmortals(actorId, immortalsId);
        if (immortalsResult.isFail()) {
            return Result.valueOf(immortalsResult.statusCode);
        }
        Immortals immortals = immortalsResult.item;
        ImmortalsConfig immortalsConfig = globalConfigService.findConfig(immortals.getConfigId(), ImmortalsConfig.class);
        if (immortalsConfig == null) {
            return Result.valueOf(CONFIG_NOT_FOUND);
        }
        TResult<Hero> heroResult = heroFacade.getHeroByConfigId(actorId, immortalsConfig.getHeroId());
        if (heroResult.isFail()) {
            return Result.valueOf(heroResult.statusCode);
        }
        if (heroResult.item.getHeroColorLevel() <= 0) {
            return Result.valueOf(IMMORTALS_COLOR_HERO_NOT_ENOUGH);
        }
        int immortalsMaxStarLevel = ImmortalsConfigService.getImmortalsMaxStarLevel(immortals.getConfigId());
        if (immortals.getStarLevel() < immortalsMaxStarLevel) {
            return Result.valueOf(IMMORTALS_COLOR_HERO_START_NOT_ENOUGH);
        }
        int nextColorLevel = immortals.getColorLevel() + 1;
        ImmortalsColorConfig colorConfig = globalConfigService.findConfig(IdentiyKey.build(immortals.getConfigId(), nextColorLevel), ImmortalsColorConfig.class);
        if (colorConfig == null) {
            return Result.valueOf(CONFIG_NOT_FOUND);
        }
        Collection<Immortals> immortalsList = immortalsDao.getImmortalsList(actorId);
        AtomicInteger count = new AtomicInteger();
        immortalsList.forEach(x -> {
            if (x.getColorLevel() >= colorConfig.getLevelLimit()) {
                count.getAndIncrement();
            }
        });
        if (colorConfig.getCountLimit() > 0 && colorConfig.getCountLimit() > count.get()) {
            return Result.valueOf(IMMORTALS_COLOR_CONDITION_NOT_ENOUGH);
        }
        Result result = RewardHelper.decrease(actorId, colorConfig.getCostList(), OperationType.IMMORTALS_COLOR);
        if (result.isFail()) {
            return Result.valueOf(result.statusCode);
        }
        immortals.setColorLevel(nextColorLevel);
        dbQueue.updateQueue(immortals);
        DispatchHelper.postEvent(new ImmortalsColorLevelUpEvent(immortals.getActorId(), immortals.getConfigId(), immortals.getColorLevel() - 1));
        ImmortalsPushHelper.pushImmortalsList(actorId, Lists.newArrayList(immortals));
        DispatchHelper.postEvent(new LineupPowerRefreshEvent(actorId));
        return Result.valueOf();
    }

    @Override
    public TResult<RewardResult> colorRebirth(long actorId, long immortalsId) {
        TResult<Immortals> immortalsResult = getImmortals(actorId, immortalsId);
        if (immortalsResult.isFail()) {
            return TResult.valueOf(immortalsResult.statusCode);
        }
        Immortals immortals = immortalsResult.item;
        if (immortals.getColorLevel() <= 1) {
            return TResult.valueOf(IMMORTALS_COLOR_REBIRTH_ERROR);
        }
        Actor actor = ActorHelper.getActor(actorId);
        if (actor == null) {
            return TResult.valueOf(ACTOR_NOT_FOUND);
        }
        int timesLimit = globalConfigService.findGlobalConfig(GlobalConfigKey.IMMORTALS_COLOR_REBIRTH_TIMES_LIMIT).findInt();
        if (actor.getImmortalsColorRebirthTimes() >= timesLimit) {
            RewardObjectMapConfig costConfig = globalConfigService.findGlobalObject(GlobalConfigKey.IMMORTALS_COLOR_REBIRTH_COST, RewardObjectMapConfig.class);
            RewardObject costReward = costConfig.getRewardObject(immortals.getColorLevel());
            Result result = RewardHelper.decrease(actorId, Lists.newArrayList(costReward), OperationType.IMMORTALS_COLOR_REBIRTH);
            if (result.isFail()) {
                return TResult.valueOf(result);
            }
        }
        actor.addImmortalsColorRebirthTimes();
        Collection<RewardObject> returnList = Lists.newArrayList();
        // 初始0，激活为1
        for (int i = 2; i <= immortals.getColorLevel(); i++) {
            ImmortalsColorConfig heroColorConfig = globalConfigService.findConfig(IdentiyKey.build(immortals.getConfigId(), i), ImmortalsColorConfig.class);
            if (heroColorConfig == null) {
                return TResult.valueOf(CONFIG_NOT_FOUND);
            }
            returnList.addAll(heroColorConfig.getCostList());
        }
        immortals.setColorLevel(1);
        dbQueue.updateQueue(immortals, actor);
        RewardResult rewardResult = RewardHelper.sendRewardList(actorId, returnList, OperationType.IMMORTALS_COLOR_REBIRTH);
        ImmortalsPushHelper.pushImmortalsList(actorId, Lists.newArrayList(immortals));
        DispatchHelper.postEvent(new LineupPowerRefreshEvent(actorId));
        ActorPushHelper.pushActorAttribute(actorId, ActorFieldType.IMMORTALS_COLOR_REBIRTH_TIMES);
        return TResult.sucess(rewardResult);
    }

    @Event(name = EventKey.ACTOR_RECHARGE_EVENT)
    public void onActorRechargeDisposeEvent(ActorRechargeEvent e) {
        ActorRechargeEvent event = e.convert();
        Optional<ImmortalsTurntableChargeRewardConfig> optional = ImmortalsTurntableConfigService.getImmortalsTurntableChargeRewardConfig(e.getChargeId());
        if (!optional.isPresent()) {
            return;
        }
        TResult<ImmortalsTurntable> tResult = getImmortalsTurntableInfo(e.actorId);
        if (tResult.isFail()) {
            return;
        }
        ImmortalsTurntable immortalsTurntable = tResult.item;
        int buyTimes = tResult.item.getDayBuyTimes().getOrDefault(optional.get().getId(), 0);
        if (buyTimes >= optional.get().getTimes()) {
            LOGGER.info("recharge repeat actorId:{}, activityType:{}, chargeId:{}", event.actorId, -13, event.getChargeId());
            return;
        }
        immortalsTurntable.buyReward(optional.get().getId());
        dbQueue.updateQueue(immortalsTurntable);
        ImmortalsProtocol.ImmortalsTurntableInfoResponse response = ImmortalsHelper.buildImmortalsTurntableInfoResponse(immortalsTurntable);
        ImmortalsPushHelper.pushImmortalsTurntableInfo(event.getActorId(), response);
    }

    @EventOnline(trigger = TriggerTime.FIXED_HOUR, triggerValue = 24)
    public void onFixedHour(GameEvent e) {
        long actorId = e.getUniqueId();
        this.reset(actorId, true);
    }

    @Event(name = EventKey.ACTOR_LOGIN)
    public void onActorLogin(ActorLoginEvent event) {
        this.reset(event.actorId, false);
    }

    public void reset(long actorId, boolean push) {
        TResult<ImmortalsTurntable> tResult = getImmortalsTurntableInfo(actorId);
        if (tResult.isFail()) {
            return;
        }
        ImmortalsTurntable immortalsTurntable = tResult.item;
        if (!DateUtils.isToday(immortalsTurntable.getLastRestTime())) {
            immortalsTurntable.dailyRest();
            dbQueue.updateQueue(immortalsTurntable);
        }
        if (push) {
            ImmortalsProtocol.ImmortalsTurntableInfoResponse response = ImmortalsHelper.buildImmortalsTurntableInfoResponse(immortalsTurntable);
            ImmortalsPushHelper.pushImmortalsTurntableInfo(actorId, response);
        }

    }
}
