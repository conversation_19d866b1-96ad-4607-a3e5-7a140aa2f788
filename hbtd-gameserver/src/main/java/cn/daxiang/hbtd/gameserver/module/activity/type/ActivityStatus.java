package cn.daxiang.hbtd.gameserver.module.activity.type;

/**
 * 活动状态
 *
 * <AUTHOR>
 */
public enum ActivityStatus {
    /**
     * 1.未开始
     */
    NOT_START(1),
    /**
     * 2.进行中
     */
    RUNNING(2),
    /**
     * 3.已结束
     */
    CLOSE(3),
    /**
     * 4.展示状态
     */
    SHOW(4),

    NONE(0);

    private int id;

    private ActivityStatus(int id) {
        this.id = id;
    }

    public static ActivityStatus getById(int id) {
        for (ActivityStatus key : values()) {
            if (id == key.getId()) {
                return key;
            }
        }
        return NONE;
    }

    public int getId() {
        return id;
    }

    @Override
    public String toString() {
        return String.valueOf(id);
    }
}