package cn.daxiang.hbtd.gameserver.module.compare.parser.impl;

import cn.daxiang.framework.result.TResult;
import cn.daxiang.hbtd.gameserver.core.database.table.ImmortalsManual;
import cn.daxiang.hbtd.gameserver.module.compare.parser.AbstractCompareParser;
import cn.daxiang.hbtd.gameserver.module.immortalsManual.facade.ImmortalsManualFacade;
import cn.daxiang.hbtd.gameserver.module.immortalsManual.helper.ImmortalsManualHelper;
import cn.daxiang.hbtd.gameserver.module.user.helper.ActorHelper;
import cn.daxiang.protocol.game.CompareProtocol;
import cn.daxiang.protocol.game.ImmortalsmanualProtocol;
import com.google.protobuf.InvalidProtocolBufferException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/3/16
 */
@Component
public class CompareImmortalsManualParser extends AbstractCompareParser<CompareProtocol.CompareImmortalsManualInfo, CompareProtocol.CompareImmortalsManualResponse> {
    @Autowired
    private ImmortalsManualFacade immortalsManualFacade;

    @Override
    protected Integer getCmdId() {
        return CompareProtocol.CompareCmd.COMPARE_IMMORTALS_MANUAL_VALUE;
    }

    @Override
    protected CompareProtocol.CompareImmortalsManualInfo parseCompareInfo(long actorId) {
        CompareProtocol.CompareImmortalsManualInfo.Builder builder = CompareProtocol.CompareImmortalsManualInfo.newBuilder();
        builder.setMyProfile(ActorHelper.getActorProfile(actorId));
        TResult<ImmortalsManual> result = immortalsManualFacade.getImmortalsManual(actorId);
        if (result.isOk()) {
            int immortalsManualValue = result.item.getManualValue();
            ImmortalsmanualProtocol.ImmortalsManualResponse response = ImmortalsManualHelper.buildImmortalsManualResponse(result.item, immortalsManualValue);
            builder.setMyHeroManual(response);
        }
        return builder.build();
    }

    @Override
    protected CompareProtocol.CompareImmortalsManualResponse parseCompareResponse(CompareProtocol.CompareImmortalsManualInfo myInfo, byte[] hisInfo) {
        CompareProtocol.CompareImmortalsManualInfo hisResponse = null;
        try {
            hisResponse = CompareProtocol.CompareImmortalsManualInfo.parseFrom(hisInfo);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("{}", e);
        }
        if (hisResponse == null) {
            return null;
        }
        CompareProtocol.CompareImmortalsManualResponse.Builder builder = CompareProtocol.CompareImmortalsManualResponse.newBuilder();
        builder.setMyHeroManual(myInfo);
        builder.setHisHeroManual(hisResponse);
        return builder.build();
    }

    @Override
    protected CompareProtocol.CompareImmortalsManualResponse parseCompareResponse(long myActorId, long hisActorId) {
        CompareProtocol.CompareImmortalsManualResponse.Builder builder = CompareProtocol.CompareImmortalsManualResponse.newBuilder();
        builder.setMyHeroManual(this.parseCompareInfo(myActorId));
        builder.setHisHeroManual(this.parseCompareInfo(hisActorId));
        return builder.build();
    }
}
