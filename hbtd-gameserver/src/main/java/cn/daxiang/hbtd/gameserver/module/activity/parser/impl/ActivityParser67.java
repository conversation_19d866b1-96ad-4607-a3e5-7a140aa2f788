package cn.daxiang.hbtd.gameserver.module.activity.parser.impl;

import cn.daxiang.framework.event.GameEvent;
import cn.daxiang.framework.event.TriggerTime;
import cn.daxiang.framework.event.annotation.Event;
import cn.daxiang.framework.event.annotation.EventOnline;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.framework.utils.DateUtils;
import cn.daxiang.hbtd.gameserver.core.database.table.ActivityRecord;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.Activity67GiftConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.ActivityOpenConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.Activity67GiftConfigService;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.ActivityOpenConfigService;
import cn.daxiang.hbtd.gameserver.core.dispatch.DispatchHelper;
import cn.daxiang.hbtd.gameserver.core.event.impl.ActorLoginEvent;
import cn.daxiang.hbtd.gameserver.core.event.impl.ActorRechargeEvent;
import cn.daxiang.hbtd.gameserver.core.event.impl.MailAddEvent;
import cn.daxiang.hbtd.gameserver.core.oss.type.OperationType;
import cn.daxiang.hbtd.gameserver.module.activity.model.entity.record.ActivityRecord67;
import cn.daxiang.hbtd.gameserver.module.activity.parser.AbstractActivityParser;
import cn.daxiang.hbtd.gameserver.module.activity.type.ActivityType;
import cn.daxiang.hbtd.gameserver.module.goods.helper.RewardHelper;
import cn.daxiang.hbtd.gameserver.module.mail.helper.MailHelper;
import cn.daxiang.hbtd.gameserver.module.mail.type.MailTemplateType;
import cn.daxiang.hbtd.gameserver.module.user.helper.ActorHelper;
import cn.daxiang.hbtd.gameserver.module.user.type.ChargeType;
import cn.daxiang.protocol.game.ActivityInfoProtocol;
import cn.daxiang.protocol.game.CommonProtocol;
import cn.daxiang.shared.event.EventKey;
import cn.daxiang.shared.reward.RewardObject;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.protobuf.ByteString;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static cn.daxiang.shared.GameModuleStatusCodeConstant.ACTIVITY_REWARD_HAD_RECEIVED;
import static cn.daxiang.shared.GameModuleStatusCodeConstant.CONFIG_NOT_FOUND;

/**
 * 活动-67-充值-尊享礼包
 */
@Component
public class ActivityParser67  extends AbstractActivityParser {

    @Override
    public void registerEvent(Set<String> eventSet) {
        eventSet.addAll(getType().getEventNames());
    }

    @Override
    public TResult<CommonProtocol.RewardResult> receiveReward(long actorId, int activityId, int id, byte[] value) {
        ActivityOpenConfig activityOpenConfig = ActivityOpenConfigService.getActivityConfig(activityId);
        if (activityOpenConfig == null) {
            LOGGER.error("ActivityOpenConfig not found, id:{}", activityId);
            return TResult.valueOf(CONFIG_NOT_FOUND);
        }
        Activity67GiftConfig config = globalConfigService.findConfig(IdentiyKey.build(activityOpenConfig.getData(), id), Activity67GiftConfig.class);
        if (config == null) {
            LOGGER.error("Activity67GiftConfig not found, activityId:{} data:{} id:{}", activityId, activityOpenConfig.getData(), id);
            return TResult.valueOf(CONFIG_NOT_FOUND);
        }

        ActivityRecord67 activityRecord;
        ActivityRecord record = activityRecordDao.getActivityRecord(actorId, activityId);
        if (record == null) {
            activityRecord = ActivityRecord67.valueOf();
            record = activityRecordDao.create(actorId, activityId, JSON.toJSONString(activityRecord));
        } else {
            activityRecord = JSON.parseObject(record.getRecord(), ActivityRecord67.class);
        }

        int receivesTimes = activityRecord.getReceives().getOrDefault(id, 0);
        // chargeId为0时 可以免费领取一次
        if (config.getChargeId() == 0) {
            if (receivesTimes >= config.getTimes()) {
                return TResult.valueOf(ACTIVITY_REWARD_HAD_RECEIVED);
            }
        } else {
            int buyTimes = activityRecord.getBuyTimes().getOrDefault(id, 0);
            if (receivesTimes >= buyTimes) {
                return TResult.valueOf(ACTIVITY_REWARD_HAD_RECEIVED);
            }
        }

        activityRecord.receives(id, 1);
        record.setRecord(JSON.toJSONString(activityRecord));
        dbQueue.updateQueue(record);

        CommonProtocol.RewardResult result = RewardHelper.sendRewardList(actorId, config.getRewardList(), OperationType.ACTIVITY_TYPE_67);
        return TResult.sucess(result);
    }

    @Override
    public void onEvent(GameEvent event) {
        Collection<ActivityOpenConfig> configList = ActivityOpenConfigService.getActivityOpenConfigList(getType());
        if (configList.isEmpty()) {
            return;
        }
        ActorRechargeEvent actorRechargeEvent = event.convert();
        if (actorRechargeEvent.getChargeType() != ChargeType.CHARGE_DIRECT_PURCHASING) {
            return;
        }
        long actorId = actorRechargeEvent.getActorId();
        for (ActivityOpenConfig activityOpenConfig : configList) {
            int activityId = activityOpenConfig.getId();
            if (!isActivityOpen(activityId)) {
                continue;
            }
            Map<Integer, Activity67GiftConfig> configMap = Activity67GiftConfigService.getActivity67GiftConfigMap(activityOpenConfig.getData());
            if (configMap == null) {
                LOGGER.error("Activity67GiftConfig is null activityId:{} data:{}", activityOpenConfig.getId(), activityOpenConfig.getData());
                continue;
            }
            ActivityRecord67 activityRecord;
            ActivityRecord record = activityRecordDao.getActivityRecord(actorId, activityId);
            if (record == null) {
                activityRecord = ActivityRecord67.valueOf();
                record = activityRecordDao.create(actorId, activityId, JSON.toJSONString(activityRecord));
            } else {
                activityRecord = JSON.parseObject(record.getRecord(), ActivityRecord67.class);
            }
            Activity67GiftConfig config = configMap.get(actorRechargeEvent.getChargeId());
            if (config == null) {
                continue;
            }
            // 增加购买次数
            activityRecord.buyTimes(config.getId(), 1);
            record.setRecord(JSON.toJSONString(activityRecord));
            dbQueue.updateQueue(record);
            this.pushActivity(actorId, activityId, activityOpenConfig.getActivityType());
        }
    }

    @Override
    protected ActivityType getType() {
        return ActivityType.ACTIVITY_TYPE_67;
    }

    @Override
    protected void onActivityOpen(Collection<ActivityOpenConfig> openActivityIds) {

    }

    @Override
    protected void onActivityEnd(Collection<ActivityOpenConfig> endActivityIds) {
        this.onActivityEnd(endActivityIds, MailTemplateType.ACTIVITY_END_REWARD);
    }

    protected void onActivityEnd(Collection<ActivityOpenConfig> endActivityIds, MailTemplateType mailTemplateType) {
        // 发送未领取的奖励邮件
        for (ActivityOpenConfig activityOpenConfig : endActivityIds) {
            int activityId = activityOpenConfig.getId();
            List<Long> actorIds = activityRecordDao.getActorIds(activityId);
            for (Long actorId : actorIds) {
                rewardClear(actorId, activityOpenConfig, mailTemplateType);
            }
        }
    }

    @Override
    public TResult<ByteString> getRecord2Client(long actorId, int activityId) {
        ActivityRecord record = activityRecordDao.getActivityRecord(actorId, activityId);
        ActivityInfoProtocol.Activity67Record.Builder builder = ActivityInfoProtocol.Activity67Record.newBuilder();
        if (record != null) {
            ActivityRecord67 activityRecord = JSON.parseObject(record.getRecord(), ActivityRecord67.class);
            builder.putAllDailyBuyTimes(activityRecord.getDailyBuyTimes());
            builder.putAllBuyTimes(activityRecord.getBuyTimes());
            builder.putAllReceives(activityRecord.getReceives());
        }
        return TResult.sucess(builder.build().toByteString());
    }

    @Override
    public TResult<ByteString> getGlobal2Client(long actorId, int activityId) {
        return TResult.fail();
    }

    private void rewardClear(Long actorId, ActivityOpenConfig activityOpenConfig, MailTemplateType mailTemplateType) {
        ActivityRecord record = activityRecordDao.getActivityRecord(actorId, activityOpenConfig.getId());
        if (record == null) {
            return;
        }
        ActivityRecord67 activityRecord = JSON.parseObject(record.getRecord(), ActivityRecord67.class);
        if (activityRecord.getBuyTimes().isEmpty()) {
            return;
        }
        Collection<RewardObject> activityRewards = Lists.newArrayList();

        Map<Integer, Activity67GiftConfig> configMap = Activity67GiftConfigService.getActivity67GiftConfigMap(activityOpenConfig.getData());
        if (configMap == null) {
            LOGGER.error("Activity67GiftConfig is null activityId:{} data:{}", activityOpenConfig.getId(), activityOpenConfig.getData());
            return;
        }

        for (Activity67GiftConfig config : configMap.values()) {
            if (config.getChargeId() == 0) {
                continue;
            }
            int receivesTimes = activityRecord.getReceives().getOrDefault(config.getId(), 0);
            int buyTimes = activityRecord.getBuyTimes().getOrDefault(config.getId(), 0);
            if (buyTimes > receivesTimes) {
                int times = buyTimes - receivesTimes;
                activityRecord.receives(config.getId(), times);
                for (int i = 0; i < times; i++) {
                    activityRewards.addAll(config.getRewardList());
                }
            }
        }

        if (activityRewards.isEmpty()) {
            return;
        }
        record.setRecord(JSON.toJSONString(activityRecord));
        dbQueue.updateQueue(record);
        sendRewardMail(actorId, activityRewards, mailTemplateType);
    }

    private void sendRewardMail(Long actorId, Collection<RewardObject> rewards, MailTemplateType mailTemplateType) {
        Map<String, String> params = Maps.newHashMap();
        params.put("name", ActorHelper.getActorName(actorId));
        params.put("activityType", MailHelper.getTranslateFormat("ACTIVITY_END_NOT_RECEIVE_REWARD_" + getType().getId()));
        MailAddEvent mailAddEvent = new MailAddEvent(actorId, mailTemplateType, params, rewards);
        DispatchHelper.postEvent(mailAddEvent);
        LOGGER.info("Send Reward complete, ActivityType:{} ,mailTemplateTypeId:{}, actorId:{}", getType(), mailTemplateType.getId(), actorId);
    }


    @EventOnline(trigger = TriggerTime.FIXED_HOUR, triggerValue = 24)
    public void onFixedHour(GameEvent e) {
        this.reset(e.getUniqueId(), true);
    }

    @Event(name = EventKey.ACTOR_LOGIN)
    public void onActorLogin(ActorLoginEvent event) {
        this.reset(event.actorId, false);
    }

    public void reset(long actorId, boolean push) {
        Collection<ActivityOpenConfig> configList = ActivityOpenConfigService.getActivityOpenConfigList(getType());
        for (ActivityOpenConfig config : configList) {
            int activityId = config.getId();
            if (!isActivityOpen(activityId)) {
                continue;
            }
            ActivityRecord activityRecord = activityRecordDao.getActivityRecord(actorId, config.getId());
            if (activityRecord != null) {
                ActivityRecord67 record67 = JSON.parseObject(activityRecord.getRecord(), ActivityRecord67.class);
                if (!DateUtils.isToday(record67.getLastRestTime())) {
                    Collection<Integer> freeId = Lists.newArrayList();
                    Map<Integer, Activity67GiftConfig> activity67GiftConfigMap = Activity67GiftConfigService.getActivity67GiftConfigMap(config.getData());
                    for (Integer chargeId : activity67GiftConfigMap.keySet()) {
                        if (chargeId == 0) {
                            Activity67GiftConfig activity67GiftConfig = activity67GiftConfigMap.get(chargeId);
                            if (activity67GiftConfig != null) {
                                freeId.add(activity67GiftConfig.getId());
                            }
                        }
                    }
                    record67.reset(freeId);
                    activityRecord.setRecord(JSON.toJSONString(record67));
                    dbQueue.updateQueue(activityRecord);
                }
            }
            if (push) {
                pushActivity(actorId, activityId, getType().getId());
            }
        }
    }

}

