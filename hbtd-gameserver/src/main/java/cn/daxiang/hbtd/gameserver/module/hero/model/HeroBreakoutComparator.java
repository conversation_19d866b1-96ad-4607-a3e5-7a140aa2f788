package cn.daxiang.hbtd.gameserver.module.hero.model;

import cn.daxiang.hbtd.gameserver.core.database.table.Hero;

import java.util.Comparator;

/**
 * <AUTHOR>
 * @date 2020/1/15
 */
public class HeroBreakoutComparator implements Comparator<Hero> {

    @Override
    public int compare(Hero o1, Hero o2) {
        if (o1.getBreakoutLevel() > o2.getBreakoutLevel()) {
            return -1;
        } else if (o1.getBreakoutLevel() == o2.getBreakoutLevel()) {
            return o1.getHeroId() > o2.getHeroId() ? -1 : 1;
        } else {
            return 1;
        }
    }
}
