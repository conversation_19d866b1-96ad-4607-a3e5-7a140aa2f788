package cn.daxiang.hbtd.gameserver.core.dataconfig.convert.impl;

import cn.daxiang.hbtd.gameserver.core.dataconfig.convert.IConfigable;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Maps;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/11/17
 */
public class IntStringMapConfig implements IConfigable {
    private final Map<Integer, String> map = Maps.newHashMap();

    @Override
    public void buildObject(String config) {
        JSONArray array = JSONArray.parseArray(config);
        for (Object item : array) {
            JSONArray valueArray = JSONArray.parseArray(item.toString());
            map.put(valueArray.getIntValue(0), valueArray.getString(1));
        }
    }

    public Map<Integer, String> getMap() {
        return map;
    }
}