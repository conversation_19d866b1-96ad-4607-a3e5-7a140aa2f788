package cn.daxiang.hbtd.gameserver.core.dataconfig.service;

import cn.daxiang.framework.dataconfig.ConfigServiceAdapter;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.ActivityGoodsManageConfig;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Component
public class ActivityDeleteGoodsConfigService extends ConfigServiceAdapter {
    /**
     * 合服活动删除物品map
     * key:activityType ,value:[goodsId,goodsId]
     */
    private static Map<Integer, Collection<Integer>> ACTIVITY_CLOSE_DELETE_GOODS_MAP = Maps.newHashMap();
    /**
     * 物品对应活动类型Map
     * key:goodsId,value:[activityType,activityType]
     */
    private final static Map<Integer, Collection<Integer>> GOODS_ACTIVITY_TYPES_MAP = Maps.newHashMap();

    public static Map<Integer, Collection<Integer>> getGoodsActivityTypesMap() {
        return GOODS_ACTIVITY_TYPES_MAP;
    }

    public static Collection<Integer> getGoodsIdList(int type) {
        if (ACTIVITY_CLOSE_DELETE_GOODS_MAP.containsKey(type)) {
            return ACTIVITY_CLOSE_DELETE_GOODS_MAP.get(type);
        }
        return Collections.emptyList();
    }

    @Override
    protected void initialize() {
        Collection<ActivityGoodsManageConfig> list = dataConfig.listAll(this, ActivityGoodsManageConfig.class);
        for (ActivityGoodsManageConfig config : list) {
            ACTIVITY_CLOSE_DELETE_GOODS_MAP.put(config.getActivityType(), config.getGoodsIdList());
            for (Integer goodsId : config.getGoodsIdList()) {
                Collection<Integer> activityTypes = GOODS_ACTIVITY_TYPES_MAP.computeIfAbsent(goodsId, k -> Sets.newHashSet());
                activityTypes.add(config.getActivityType());
            }
        }
    }

    @Override
    protected void clean() {
        ACTIVITY_CLOSE_DELETE_GOODS_MAP.clear();
        GOODS_ACTIVITY_TYPES_MAP.clear();
    }
}
