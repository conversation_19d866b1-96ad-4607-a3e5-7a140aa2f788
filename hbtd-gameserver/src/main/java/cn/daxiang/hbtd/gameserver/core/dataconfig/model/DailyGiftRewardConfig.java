package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.dataconfig.annotation.FieldIgnore;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.shared.reward.RewardObject;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;

import java.util.Collection;

/**
 * 每日特惠礼包配置表
 *
 * @Author: Gary
 * @Date 2022-10-14 14:11
 * @Description:
 */
@DataFile(fileName = "daily_gift_reward_config")
public class DailyGiftRewardConfig implements ModelAdapter {
    /**
     * 唯一Id
     */
    private int id;
    /**
     * 充值Id
     */
    private int chargeId;
    /**
     * 奖励列表
     */
    private String reward;
    /**
     * 是否为打包购买
     */
    private boolean isPackage;

    @FieldIgnore
    private Collection<RewardObject> rewardList = Lists.newArrayList();

    @Override
    public void initialize() {
        JSONArray rewardsArray = JSONObject.parseArray(reward);
        for (Object reward : rewardsArray) {
            JSONArray rewardArray = JSONObject.parseArray(reward.toString());
            RewardObject rewardObject = RewardObject.valueOf(rewardArray);
            rewardList.add(rewardObject);
        }
    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(id);
    }

    public int getId() {
        return id;
    }

    public int getChargeId() {
        return chargeId;
    }

    public Collection<RewardObject> getRewardList() {
        return rewardList;
    }

    public boolean isPackage() {
        return isPackage;
    }
}
