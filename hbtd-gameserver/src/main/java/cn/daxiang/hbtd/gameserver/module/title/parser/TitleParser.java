package cn.daxiang.hbtd.gameserver.module.title.parser;

import cn.daxiang.framework.event.GameEvent;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.TitleConfig;
import cn.daxiang.hbtd.gameserver.module.title.model.TitleEntity;

/**
 * 称号解析
 *
 * <AUTHOR>
 */
public interface TitleParser {

    /**
     * 解析
     *
     * @param event
     * @param titleEntity
     * @param titleConfig
     * @return
     */
    boolean parser(GameEvent event, TitleEntity titleEntity, TitleConfig titleConfig);
}
