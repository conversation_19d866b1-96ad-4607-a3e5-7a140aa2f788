package cn.daxiang.hbtd.gameserver.module.activity.model.entity.record;

import com.google.common.collect.Lists;

import java.util.List;

/**
 * 天天充值（连续充值的优化变成新加一个活动）
 * HBTD\doc\策划文档\已评审文档\beta6.2\连续充值优化-keen.xlsx
 *
 * <AUTHOR>
 * @date 2023/2/16
 */
public class ActivityRecord34 {
    /**
     * 当前进度
     */
    private int id;
    /**
     * 重置时间
     */
    private long resetTime;
    /**
     * 今日是否购买礼包
     */
    private boolean buy;
    /**
     * 已领取id列表
     */
    private List<Integer> receivedList = Lists.newArrayList();
    /**
     * 每日奖励是否领取
     */
    private boolean receiveDailyReward;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public long getResetTime() {
        return resetTime;
    }

    public void setResetTime(long resetTime) {
        this.resetTime = resetTime;
    }

    public boolean getBuy() {
        return buy;
    }

    public void setBuy(boolean buy) {
        this.buy = buy;
    }

    public List<Integer> getReceivedList() {
        return receivedList;
    }

    public void setReceivedList(List<Integer> receivedList) {
        this.receivedList = receivedList;
    }

    public boolean getReceiveDailyReward() {
        return receiveDailyReward;
    }

    public void setReceiveDailyReward(boolean receiveDailyReward) {
        this.receiveDailyReward = receiveDailyReward;
    }

    /**
     * 领取奖励
     *
     * @param id
     */
    public void addReceived(int id) {
        this.receivedList.add(id);
    }

    /**
     * 每日重置
     *
     * @param id
     */
    public void reset(int id) {
        this.id = id;
        this.buy = false;
        this.resetTime = System.currentTimeMillis();
        this.receiveDailyReward = false;
    }
}
