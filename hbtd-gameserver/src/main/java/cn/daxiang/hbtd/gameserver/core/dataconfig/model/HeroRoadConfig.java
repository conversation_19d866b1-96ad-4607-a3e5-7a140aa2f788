package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.identity.IdentiyKey;

/**
 * 英雄之路
 */
@DataFile(fileName = "hero_road_config")
public class HeroRoadConfig implements ModelAdapter {
    /**
     * 传记ID
     */
    private int storyId;
    /**
     * 难度等级
     */
    private int difficultyLevel;
    /**
     * 角色解锁ID
     */
    private int unlockId;

    @Override
    public void initialize() {

    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(storyId, difficultyLevel);
    }

    public int getStoryId() {
        return storyId;
    }

    public int getDifficultyLevel() {
        return difficultyLevel;
    }

    public int getUnlockId() {
        return unlockId;
    }

}