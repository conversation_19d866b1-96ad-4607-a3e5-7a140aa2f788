package cn.daxiang.hbtd.gameserver.module.activity.parser.impl;

import cn.daxiang.framework.result.TResult;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.ActivityOpenConfig;
import cn.daxiang.hbtd.gameserver.core.oss.type.OperationType;
import cn.daxiang.hbtd.gameserver.module.activity.parser.AbstractActivityParser14;
import cn.daxiang.hbtd.gameserver.module.activity.type.ActivityType;
import cn.daxiang.protocol.game.CommonProtocol.RewardResult;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;

/**
 * 招兵买马 同 [14,52]
 *
 * <AUTHOR>
 * @date 2022/6/6
 */
@Component
public class ActivityParser14 extends AbstractActivityParser14 {
    @Override
    public TResult<RewardResult> receiveReward(long actorId, int activityId, int id, byte[] value) {
        return this.receiveReward(actorId, activityId, id, value, OperationType.ACTIVITY_TYPE_14);
    }

    @Override
    protected ActivityType getType() {
        return ActivityType.ACTIVITY_TYPE_14;
    }

    @Override
    protected void onActivityOpen(Collection<ActivityOpenConfig> openActivityIds) {
        for (ActivityOpenConfig openConfig : openActivityIds) {
            List<Long> actorIds = activityRecordDao.getActorIds(openConfig.getId());
            for (Long actorId : actorIds) {
                activityRecordDao.delete(actorId, openConfig.getId());
            }
        }
    }
}
