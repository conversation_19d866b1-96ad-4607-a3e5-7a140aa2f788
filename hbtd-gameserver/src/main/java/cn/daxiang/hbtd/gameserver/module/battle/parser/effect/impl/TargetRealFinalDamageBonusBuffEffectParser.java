package cn.daxiang.hbtd.gameserver.module.battle.parser.effect.impl;

import cn.daxiang.hbtd.gameserver.core.dataconfig.model.SkillConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.SkillEffectConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.SkillConfigService;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleBuff;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleRoom;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleSprite;
import cn.daxiang.hbtd.gameserver.module.battle.model.FightReport;
import cn.daxiang.hbtd.gameserver.module.battle.parser.effect.AbstractSkillEffectParser;
import cn.daxiang.hbtd.gameserver.module.battle.type.SkillEffectType;
import cn.daxiang.hbtd.gameserver.module.hero.type.HeroSexType;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 目标最最终伤害降低万分比buff
 *
 * @author: Gary
 * @date: 2023/9/18 11:39
 * @Description:
 */
@Component
public class TargetRealFinalDamageBonusBuffEffectParser extends AbstractSkillEffectParser {
    @Override
    protected SkillEffectType getType() {
        return SkillEffectType.TARGET_REAL_FINAL_DAMAGE_BONUS_BUFF_EFFECT;
    }

    @Override
    public boolean execute(FightReport report, BattleSprite attacker, BattleSprite targeter, SkillConfig skillConfig, SkillEffectConfig effectConfig, BattleRoom battleRoom) {
        this.addBattleBuff(report, attacker, targeter, skillConfig, effectConfig, battleRoom);
        return true;
    }

    @Override
    public long calcEffectValue(FightReport report, BattleSprite battleSprite, BattleRoom battleRoom) {
        Collection<BattleBuff> buffList = battleSprite.getBuffList(getType());
        Map<Integer, Collection<Long>> buffValueMap = Maps.newHashMap();
        /**
         * x1:性别
         * x2:施加该buff者的怒气技释放次数
         * x3:施加该buff方的女性武将数量
         */
        for (BattleBuff buff : buffList) {
            SkillEffectConfig effectConfig = SkillConfigService.getSkillEffectConfig(buff.getEffectId());
            BattleSprite castBuffSprite = battleRoom.getBattleSprite(buff.getCastSpriteUid());
            int castTimes = 0;
            long womanCount = 0;
            if (castBuffSprite != null) {
                castTimes = castBuffSprite.getCastTimes();
                List<BattleSprite> battleSpriteList = battleRoom.getBattleSpriteList(castBuffSprite.getBattleCamp());
                womanCount = battleSpriteList.stream().filter(x -> x.getHeroSexType() == HeroSexType.WOMAN).count();
            }
            long buffValue = effectConfig.calcSkillEffect(battleSprite.getHeroSexType().getId(), castTimes, womanCount);
            Collection<Long> buffValues = buffValueMap.computeIfAbsent(buff.getEffectId(), k -> Lists.newArrayList());
            buffValues.add(buffValue);
        }
        return this.getBuffValue(buffValueMap);
    }
}
