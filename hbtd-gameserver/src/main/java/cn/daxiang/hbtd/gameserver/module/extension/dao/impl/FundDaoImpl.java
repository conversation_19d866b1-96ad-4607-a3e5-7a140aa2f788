package cn.daxiang.hbtd.gameserver.module.extension.dao.impl;

import cn.daxiang.framework.database.SingleEntity;
import cn.daxiang.framework.database.dao.SingleEntityDaoImpl;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.hbtd.gameserver.core.database.table.Fund;
import cn.daxiang.hbtd.gameserver.module.extension.dao.FundDao;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/6/15
 */
@Component
public class FundDaoImpl extends SingleEntityDaoImpl implements FundDao {

    @Override
    protected Class<? extends SingleEntity<?>> forClass() {
        return Fund.class;
    }

    @Override
    protected void initMaxId() {

    }

    @Override
    public Fund getFund(long actorId) {
        Fund table = this.get(IdentiyKey.build(actorId));
        return table;
    }
}
