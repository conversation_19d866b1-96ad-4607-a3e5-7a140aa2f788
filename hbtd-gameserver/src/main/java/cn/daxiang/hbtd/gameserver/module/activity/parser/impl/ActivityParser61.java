package cn.daxiang.hbtd.gameserver.module.activity.parser.impl;

import cn.daxiang.framework.event.GameEvent;
import cn.daxiang.framework.event.TriggerTime;
import cn.daxiang.framework.event.annotation.Event;
import cn.daxiang.framework.event.annotation.EventOnline;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.framework.utils.DateUtils;
import cn.daxiang.framework.utils.RandomUtils;
import cn.daxiang.hbtd.gameserver.core.database.table.ActivityRecord;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.Activity61GiftConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.ActivityOpenConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.Activity61ConfigService;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.ActivityOpenConfigService;
import cn.daxiang.hbtd.gameserver.core.event.impl.ActorRechargeEvent;
import cn.daxiang.hbtd.gameserver.core.oss.type.OperationType;
import cn.daxiang.hbtd.gameserver.module.activity.model.entity.record.ActivityRecord61;
import cn.daxiang.hbtd.gameserver.module.activity.parser.AbstractActivityParser;
import cn.daxiang.hbtd.gameserver.module.activity.type.ActivityType;
import cn.daxiang.hbtd.gameserver.module.goods.helper.RewardHelper;
import cn.daxiang.hbtd.gameserver.module.mail.type.MailTemplateType;
import cn.daxiang.protocol.game.ActivityInfoProtocol;
import cn.daxiang.protocol.game.CommonProtocol.RewardResult;
import cn.daxiang.shared.event.EventKey;
import cn.daxiang.shared.reward.RewardObject;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.protobuf.ByteString;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import static cn.daxiang.shared.GameModuleStatusCodeConstant.ACTIVITY_NOT_FINISH_FOR_RECEIVED;
import static cn.daxiang.shared.GameModuleStatusCodeConstant.ACTIVITY_REWARD_HAD_RECEIVED;
import static cn.daxiang.shared.GameModuleStatusCodeConstant.CONFIG_NOT_FOUND;
import static cn.daxiang.shared.GameModuleStatusCodeConstant.INVALID_PARAM;

/**
 * 活动61 每日首充
 *
 * @author: Gary
 * @date: 2024/4/22 15:46
 * @Description:
 */
@Component
public class ActivityParser61 extends AbstractActivityParser {
    @Override
    protected ActivityType getType() {
        return ActivityType.ACTIVITY_TYPE_61;
    }

    @Override
    protected void onActivityOpen(Collection<ActivityOpenConfig> openActivityIds) {

    }

    @Override
    protected void onActivityEnd(Collection<ActivityOpenConfig> endActivityIds) {
        for (ActivityOpenConfig activityOpenConfig : endActivityIds) {
            int activityId = activityOpenConfig.getId();
            List<Long> actorIds = activityRecordDao.getActorIds(activityId);
            for (Long actorId : actorIds) {
                ActivityRecord record = this.getActivityRecord(actorId, activityId);
                ActivityRecord61 activityRecord = JSON.parseObject(record.getRecord(), ActivityRecord61.class);
                rewardClear(actorId, activityRecord, activityOpenConfig);
            }
        }
    }

    /**
     * @param actorId
     * @param activityId
     * @param id
     * @param value
     * @return
     */
    @Override
    public TResult<RewardResult> receiveReward(long actorId, int activityId, int id, byte[] value) {
        ActivityOpenConfig activityOpenConfig = ActivityOpenConfigService.getActivityConfig(activityId);
        if (activityOpenConfig == null) {
            LOGGER.error("ActivityOpenConfig not found, id:{}", activityId);
            return TResult.valueOf(CONFIG_NOT_FOUND);
        }
        ActivityRecord record = getActivityRecord(actorId, activityId);
        ActivityRecord61 activityRecord = JSON.parseObject(record.getRecord(), ActivityRecord61.class);
        Activity61GiftConfig giftConfig = dataConfig.getConfig(IdentiyKey.build(activityOpenConfig.getData(), id), Activity61GiftConfig.class);
        if (giftConfig == null) {
            return TResult.valueOf(CONFIG_NOT_FOUND);
        }
        if (giftConfig.getId() < 0) {
            return TResult.valueOf(INVALID_PARAM);
        }
        if (giftConfig.getType() == 1) {
            if (!activityRecord.isBuyPreferentialGift(id) && giftConfig.getChargeId() > 0) {
                return TResult.valueOf(ACTIVITY_NOT_FINISH_FOR_RECEIVED);
            }
            if (activityRecord.isReceivePreferentialGift(id)) {
                return TResult.valueOf(ACTIVITY_REWARD_HAD_RECEIVED);
            }
            activityRecord.addReceivePreferentialGift(id);
        } else if (giftConfig.getType() == 2) {
            if (activityRecord.isReceiveChargeReward(id)) {
                return TResult.valueOf(ACTIVITY_REWARD_HAD_RECEIVED);
            }
            if (activityRecord.getDailyCharge() < (long) giftConfig.getCharge() * RandomUtils.HUNDRED) {
                return TResult.valueOf(ACTIVITY_NOT_FINISH_FOR_RECEIVED);
            }
            activityRecord.addReceiveCharge(id);
        } else {
            return TResult.valueOf(INVALID_PARAM);
        }
        List<RewardObject> rewardList = giftConfig.getRewardList();
        record.setRecord(JSON.toJSONString(activityRecord));
        dbQueue.updateQueue(record);
        return TResult.sucess(RewardHelper.sendRewardList(actorId, rewardList, OperationType.ACTIVITY_TYPE_61));
    }

    @Override
    public TResult<ByteString> getRecord2Client(long actorId, int activityId) {
        ActivityRecord record = getActivityRecord(actorId, activityId);
        ActivityInfoProtocol.Activity61Record.Builder builder = ActivityInfoProtocol.Activity61Record.newBuilder();
        ActivityRecord61 activityRecord = JSON.parseObject(record.getRecord(), ActivityRecord61.class);
        builder.setDailyCharge(activityRecord.getDailyCharge());
        builder.addAllBuyGiftIds(activityRecord.getBuyGiftIds());
        builder.addAllReceiveIds(activityRecord.getReceives());
        builder.addAllReceiveCharge(activityRecord.getReceiveCharge());
        return TResult.sucess(builder.build().toByteString());
    }

    private ActivityRecord getActivityRecord(long actorId, int activityId) {
        ActivityRecord record = activityRecordDao.getActivityRecord(actorId, activityId);
        if (record == null) {
            ActivityRecord61 activityRecord61 = new ActivityRecord61();
            activityRecord61.dailyRest();
            record = activityRecordDao.create(actorId, activityId, JSON.toJSONString(activityRecord61));
            dbQueue.updateQueue(record);
        }
        return record;
    }

    @EventOnline(trigger = TriggerTime.FIXED_HOUR, triggerValue = 24)
    public void onEvery0Hour(GameEvent e) {
        this.reset(e.getUniqueId(), true);
    }

    @Event(name = EventKey.ACTOR_LOGIN)
    public void onActorLogin(GameEvent e) {
        reset(e.getUniqueId(), false);
    }

    /**
     * 每天重置购买次数
     */
    private void reset(long actorId, boolean isPush) {
        Collection<ActivityOpenConfig> activityOpenConfigList = ActivityOpenConfigService.getActivityOpenConfigList(getType());
        for (ActivityOpenConfig activityOpenConfig : activityOpenConfigList) {
            ActivityRecord record = this.getActivityRecord(actorId, activityOpenConfig.getId());
            ActivityRecord61 activityRecord = JSON.parseObject(record.getRecord(), ActivityRecord61.class);
            if (!DateUtils.isToday(activityRecord.getResetTime())) {
                rewardClear(record.findFkId(), activityRecord, activityOpenConfig);
                activityRecord.dailyRest();
                record.setRecord(JSON.toJSONString(activityRecord));
                dbQueue.updateQueue(record);
            }
            if (isPush) {
                pushActivity(actorId, activityOpenConfig.getId(), getType().getId());
            }
        }
    }

    /**
     * 奖励补发
     *
     * @param actorId
     * @param activityRecord
     * @param activityConfig
     */
    private void rewardClear(long actorId, ActivityRecord61 activityRecord, ActivityOpenConfig activityConfig) {
        Collection<RewardObject> rewards = Lists.newArrayList();
        Optional<Collection<Activity61GiftConfig>> giftConfigsOptional = Activity61ConfigService.getAllActivity61PreferentialGiftConfigs(activityConfig.getData());
        if (giftConfigsOptional.isPresent()) {
            for (Activity61GiftConfig config : giftConfigsOptional.get()) {
                if (config.getChargeId() > 0 && activityRecord.isBuyPreferentialGift(config.getId()) && !activityRecord.isReceivePreferentialGift(config.getId())) {
                    rewards.addAll(config.getRewardList());
                    activityRecord.addReceivePreferentialGift(config.getId());
                }
            }
        }
        Optional<Collection<Activity61GiftConfig>> chargeRewardsConfigsOptional = Activity61ConfigService.getActivity61ChargeRewardsConfigs(activityConfig.getData());
        if (chargeRewardsConfigsOptional.isPresent()) {
            for (Activity61GiftConfig config : chargeRewardsConfigsOptional.get()) {
                if (activityRecord.getDailyCharge() >= (long) config.getCharge() * RandomUtils.HUNDRED && !activityRecord.isReceiveChargeReward(config.getId())) {
                    rewards.addAll(config.getRewardList());
                    activityRecord.addReceiveCharge(config.getId());
                }
            }
        }
        if (!rewards.isEmpty()) {
            sendReward(rewards, actorId, MailTemplateType.ACTIVITY_END_REWARD);
        }
    }

    @Override
    public TResult<ByteString> getGlobal2Client(long actorId, int activityId) {
        return TResult.fail();
    }

    @Override
    public void registerEvent(Set<String> eventSet) {
        eventSet.addAll(getType().getEventNames());
    }

    @Override
    public void onEvent(GameEvent event) {
        Collection<ActivityOpenConfig> configList = ActivityOpenConfigService.getActivityOpenConfigList(getType());
        if (configList.isEmpty()) {
            return;
        }
        ActorRechargeEvent actorRechargeEvent = event.convert();
        long actorId = actorRechargeEvent.getActorId();
        for (ActivityOpenConfig activityOpenConfig : configList) {
            int activityId = activityOpenConfig.getId();
            int data = activityOpenConfig.getData();
            if (!isActivityOpen(activityId)) {
                continue;
            }
            ActivityRecord record = getActivityRecord(actorId, activityId);
            ActivityRecord61 activityRecord = JSON.parseObject(record.getRecord(), ActivityRecord61.class);
            //累充记录
            activityRecord.addDailyCharge(actorRechargeEvent.getRecharge());
            Optional<Activity61GiftConfig> configOptional = Activity61ConfigService.getActivity61PreferentialGiftConfig(data, actorRechargeEvent.getChargeId());
            if (configOptional.isPresent()) {
                Activity61GiftConfig giftConfig = configOptional.get();
                //购买礼包
                if (giftConfig.getId() < 0) {
                    Optional<Collection<Activity61GiftConfig>> allConfigOptional = Activity61ConfigService.getAllActivity61PreferentialGiftConfigs(data);
                    if (allConfigOptional.isPresent()) {
                        for (Activity61GiftConfig config : allConfigOptional.get()) {
                            if (config.getId() > 0 && config.getChargeId() > 0) {
                                activityRecord.buy(config.getId());
                            }
                        }
                    }
                } else {
                    activityRecord.buy(giftConfig.getId());
                }
            }
            record.setRecord(JSON.toJSONString(activityRecord));
            dbQueue.updateQueue(record);
            this.pushActivity(actorId, activityId, activityOpenConfig.getActivityType());
        }
    }
}
