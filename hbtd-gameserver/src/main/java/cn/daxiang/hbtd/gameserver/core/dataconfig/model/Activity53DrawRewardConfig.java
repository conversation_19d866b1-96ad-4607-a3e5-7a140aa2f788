package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.dataconfig.annotation.FieldIgnore;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.shared.reward.RewardObject;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;

import java.util.Collection;

/**
 * 星布棋局-祈福求签奖励配置表
 *
 * <AUTHOR>
 * @date 2023/12/28
 */
@DataFile(fileName = "activity_53_draw_reward_config")
public class Activity53DrawRewardConfig implements ModelAdapter {
    /**
     * 活动DATA
     */
    private int data;
    /**
     * 配置ID
     */
    private int id;
    /**
     * 求签祈福奖池 [[rewardType,id,num],[rewardType,id,num]]
     */
    private String rewards;
    /**
     * 随机权重
     */
    private int rate;

    @FieldIgnore
    private Collection<RewardObject> rewardList = Lists.newArrayList();

    @Override
    public void initialize() {
        JSONArray rewardObjectArray = JSONObject.parseArray(rewards);
        for (Object reward : rewardObjectArray) {
            JSONArray rewardArray = JSONObject.parseArray(reward.toString());
            rewardList.add(RewardObject.valueOf(rewardArray));
        }
    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(data, id);
    }

    public int getData() {
        return data;
    }

    public int getId() {
        return id;
    }

    public int getRate() {
        return rate;
    }

    public Collection<RewardObject> getRewardList() {
        return rewardList;
    }
}
