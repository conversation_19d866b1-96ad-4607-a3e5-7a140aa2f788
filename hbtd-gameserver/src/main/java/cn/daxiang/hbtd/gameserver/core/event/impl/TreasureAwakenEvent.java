package cn.daxiang.hbtd.gameserver.core.event.impl;

import cn.daxiang.framework.event.ActorEvent;
import cn.daxiang.shared.event.EventKey;

/**
 * 宝物觉醒事件
 */
public class TreasureAwakenEvent extends ActorEvent {
    /**
     * 宝物ID
     */
    public long treasureId;
    /**
     * 宝物配置ID
     */
    public int configId;
    /**
     * 宝物精炼节点等级
     */
    public int awakenNodeLevel;

    public TreasureAwakenEvent(long actorId, long treasureId, int configId, int awakenNodeLevel) {
        super(EventKey.TREASURE_AWAKEN_EVENT, actorId);
        this.treasureId = treasureId;
        this.configId = configId;
        this.awakenNodeLevel = awakenNodeLevel;
    }
}
