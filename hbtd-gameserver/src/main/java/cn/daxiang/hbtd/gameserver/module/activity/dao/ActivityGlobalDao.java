package cn.daxiang.hbtd.gameserver.module.activity.dao;

import cn.daxiang.hbtd.gameserver.core.database.table.ActivityGlobal;

public interface ActivityGlobalDao {
    /**
     * 获取活动公共数据
     *
     * @param activityId
     * @return
     */
    ActivityGlobal getActivityGlobal(int activityId);

    /**
     * 创建活动公共数据
     *
     * @param activityId
     * @param openTime
     * @param closeTime
     * @param showTime
     * @return
     */
    ActivityGlobal createActivityGlobal(int activityId, long openTime, long closeTime, long showTime);
}
