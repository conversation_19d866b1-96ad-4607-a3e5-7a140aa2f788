package cn.daxiang.hbtd.gameserver.module.battle.parser.effect.impl;

import cn.daxiang.hbtd.gameserver.core.dataconfig.model.SkillConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.SkillEffectConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.SkillConfigService;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleBuff;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleRoom;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleSprite;
import cn.daxiang.hbtd.gameserver.module.battle.model.FightReport;
import cn.daxiang.hbtd.gameserver.module.battle.parser.effect.AbstractSkillEffectParser;
import cn.daxiang.hbtd.gameserver.module.battle.type.SkillEffectType;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Iterator;
import java.util.Map;
/**
 * 183.反震伤害提升万分比
 *
 * @author: Gary
 * @date: 2023/11/14 11:01
 * @Description:
 */
@Component
public class CounterShockBonusBuffEffectParser extends AbstractSkillEffectParser {
    @Override
    protected SkillEffectType getType() {
        return SkillEffectType.COUNTER_SHOCK_BONUS_BUFF_EFFECT;
    }

    @Override
    public boolean execute(FightReport report, BattleSprite attacker, BattleSprite targeter, SkillConfig skillConfig, SkillEffectConfig effectConfig, BattleRoom battleRoom) {
        this.addBattleBuff(report, attacker, targeter, skillConfig, effectConfig, battleRoom);
        return true;
    }

    @Override
    public long calcEffectValue(FightReport report, BattleSprite battleSprite, BattleRoom battleRoom) {
        Collection<BattleBuff> buffList = battleSprite.getBuffList(getType());
        Map<Integer, Collection<Long>> buffValueMap = Maps.newHashMap();
        for (Iterator<BattleBuff> iterator = buffList.iterator(); iterator.hasNext(); ) {
            BattleBuff buff = iterator.next();
            SkillEffectConfig effectConfig = SkillConfigService.getSkillEffectConfig(buff.getEffectId());
            long buffValue = effectConfig.calcSkillEffect();
            Collection<Long> buffValues = buffValueMap.get(buff.getEffectId());
            if (buffValues == null) {
                buffValues = Lists.newArrayList();
                buffValueMap.put(buff.getEffectId(), buffValues);
            }
            buffValues.add(buffValue);
        }
        return this.getBuffValue(buffValueMap);
    }
}
