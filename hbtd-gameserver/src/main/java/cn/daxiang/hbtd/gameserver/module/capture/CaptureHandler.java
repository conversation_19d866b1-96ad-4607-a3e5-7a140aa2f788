package cn.daxiang.hbtd.gameserver.module.capture;

import cn.daxiang.framework.dispatch.DispatchType;
import cn.daxiang.framework.protocal.DataPacket;
import cn.daxiang.framework.result.CollectionResult;
import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.framework.router.annotation.Cmd;
import cn.daxiang.hbtd.gameserver.core.database.table.CaptureActor;
import cn.daxiang.hbtd.gameserver.core.router.GatewayRouterHandlerImpl;
import cn.daxiang.hbtd.gameserver.module.capture.facade.CaptureFacade;
import cn.daxiang.hbtd.gameserver.module.capture.helper.CaptureHelper;
import cn.daxiang.protocol.game.CaptureProtocol;
import cn.daxiang.protocol.game.CommonProtocol;
import cn.daxiang.protocol.game.ModuleProtocol;
import cn.daxiang.shared.module.capture.Capture;
import cn.daxiang.shared.module.capture.CaptureCity;
import io.netty.channel.Channel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/7/1
 */
@Component
public class CaptureHandler extends GatewayRouterHandlerImpl {
    @Autowired
    private CaptureFacade captureFacade;

    @Override
    public int getModule() {
        return ModuleProtocol.Module.CAPTURE_VALUE;
    }

    @Cmd(Id = CaptureProtocol.CaptureCmd.CAPTURE_INFO_VALUE, dispatchType = DispatchType.ACTOR)
    public void getCapture(Channel channel, Long actorId, DataPacket packet) {
        TResult<Capture> result = captureFacade.getCapture(actorId);
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, CaptureHelper.buildCaptureResponse(result.item));
    }

    @Cmd(Id = CaptureProtocol.CaptureCmd.CAPTURE_CITY_LIST_VALUE, dispatchType = DispatchType.ACTOR)
    public void getCaptureCity(Channel channel, Long actorId, DataPacket packet) {
        CommonProtocol.IntPacket request = packet.getValue(CommonProtocol.IntPacket.parser());
        CollectionResult<CaptureCity> result = captureFacade.getCaptureCity(actorId, request.getValue());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, CaptureHelper.buildCaptureCityResponse(result.item));
    }

    @Cmd(Id = CaptureProtocol.CaptureCmd.CAPTURE_ACTOR_VALUE, dispatchType = DispatchType.ACTOR)
    public void getCaptureActor(Channel channel, Long actorId, DataPacket packet) {
        TResult<CaptureActor> result = captureFacade.getCaptureActor(actorId);
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, CaptureHelper.buildCaptureActorResponse(result.item));
    }

    @Cmd(Id = CaptureProtocol.CaptureCmd.CAPTURE_ACTOR_UPGRADE_VALUE, dispatchType = DispatchType.ACTOR)
    public void upgrade(Channel channel, Long actorId, DataPacket packet) {
        Result result = captureFacade.upgrade(actorId);
        channelWrite(channel, packet, result.statusCode);
    }

    @Cmd(Id = CaptureProtocol.CaptureCmd.CAPTURE_ACTOR_RECEIVE_VALUE, dispatchType = DispatchType.ACTOR)
    public void receiveOwn(Channel channel, Long actorId, DataPacket packet) {
        TResult<CommonProtocol.RewardResult> result = captureFacade.receiveOwn(actorId);
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, CaptureHelper.buildCaptureRewardResultResponse(result.item));
    }

    @Cmd(Id = CaptureProtocol.CaptureCmd.CAPTURE_BATTLE_VALUE, dispatchType = DispatchType.ACTOR)
    public void battle(Channel channel, Long actorId, DataPacket packet) {
        CaptureProtocol.CaptureBattleRequest request = packet.getValue(CaptureProtocol.CaptureBattleRequest.parser());
        Result result = captureFacade.battle(actorId, request.getCityId(), request.getType(), request.getLineupMap(), request.getBeastsMap());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
        }
    }

    @Cmd(Id = CaptureProtocol.CaptureCmd.CAPTURE_RECEIVE_VALUE, dispatchType = DispatchType.ACTOR)
    public void receiveCapture(Channel channel, Long actorId, DataPacket packet) {
        TResult<CommonProtocol.RewardResult> result = captureFacade.receiveCapture(actorId);
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, CaptureHelper.buildCaptureRewardResultResponse(result.item));
    }

    @Cmd(Id = CaptureProtocol.CaptureCmd.CAPTURE_MULTIPLE_VALUE, dispatchType = DispatchType.ACTOR)
    public void multiple(Channel channel, Long actorId, DataPacket packet) {
        Result result = captureFacade.multiple(actorId);
        channelWrite(channel, packet, result.statusCode);
    }

    @Cmd(Id = CaptureProtocol.CaptureCmd.CAPTURE_RANK_VALUE, dispatchType = DispatchType.ACTOR)
    public void captureRank(Channel channel, Long actorId, DataPacket packet) {
        CommonProtocol.IntPacket request = packet.getValue(CommonProtocol.IntPacket.parser());
        TResult<CaptureProtocol.CaptureRankResponse> result = captureFacade.getCaptureRankResponse(actorId, request.getValue());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = CaptureProtocol.CaptureCmd.CAPTURE_GIVE_UP_VALUE, dispatchType = DispatchType.ACTOR)
    public void giveUp(Channel channel, Long actorId, DataPacket packet) {
        Result result = captureFacade.giveUp(actorId);
        channelWrite(channel, packet, result.statusCode);
    }

    @Cmd(Id = CaptureProtocol.CaptureCmd.CAPTURE_READ_VALUE, dispatchType = DispatchType.ACTOR)
    public void read(Channel channel, Long actorId, DataPacket packet) {
        Result result = captureFacade.read(actorId);
        channelWrite(channel, packet, result.statusCode);
    }

    @Cmd(Id = CaptureProtocol.CaptureCmd.CAPTURE_WIPE_OUT_VALUE, dispatchType = DispatchType.ACTOR)
    public void wipeOut(Channel channel, Long actorId, DataPacket packet) {
        CommonProtocol.IntPacket request = packet.getValue(CommonProtocol.IntPacket.parser());
        TResult<CommonProtocol.RewardResultResponse> result = captureFacade.wipeOut(actorId, request.getValue());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = CaptureProtocol.CaptureCmd.CAPTURE_RECEIVE_DAILY_REWARD_VALUE, dispatchType = DispatchType.ACTOR)
    public void receiveDailyReward(Channel channel, Long actorId, DataPacket packet) {
        TResult<CommonProtocol.RewardResultResponse> result = captureFacade.receiveDailyReward(actorId);
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }
}
