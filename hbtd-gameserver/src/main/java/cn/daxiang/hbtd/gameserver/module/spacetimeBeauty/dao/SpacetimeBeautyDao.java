package cn.daxiang.hbtd.gameserver.module.spacetimeBeauty.dao;

import cn.daxiang.hbtd.gameserver.core.database.table.SpacetimeBeauty;

import java.util.Collection;

/**
 * @author: <PERSON>
 * @date: 2023/8/16 16:47
 * @Description:
 */
public interface SpacetimeBeautyDao {
    /**
     * 获取时空红颜列表
     *
     * @param actorId
     * @return
     */
    Collection<SpacetimeBeauty> getSpacetimeBeautyList(long actorId);

    /**
     * 获取某个时空红颜
     *
     * @param actorId
     * @param configId
     * @return
     */
    SpacetimeBeauty getSpacetimeBeauty(long actorId, int configId);

    /**
     * 创建某个时空红颜
     *
     * @param actorId
     * @param configId
     * @return
     */
    SpacetimeBeauty creatSpacetimeBeauty(long actorId, int configId);
}
