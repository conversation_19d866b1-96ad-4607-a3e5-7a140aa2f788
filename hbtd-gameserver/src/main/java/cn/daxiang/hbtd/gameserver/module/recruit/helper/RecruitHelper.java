package cn.daxiang.hbtd.gameserver.module.recruit.helper;

import cn.daxiang.dto.model.RecruitRecordVO;
import cn.daxiang.hbtd.gameserver.core.database.table.Recruit;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.RecruitConfigService;
import cn.daxiang.hbtd.gameserver.module.PbBuilder;
import cn.daxiang.protocol.game.RecruitProtocol;
import cn.daxiang.protocol.game.RecruitProtocol.RecruitFamousHeroResponse;

import java.util.Collection;

/**
 * <AUTHOR>
 * @date 2022/6/14
 */
public class RecruitHelper {
    public static RecruitFamousHeroResponse buildRecruitFamousHeroResponse(Recruit recruit) {
        RecruitFamousHeroResponse.Builder builder = RecruitFamousHeroResponse.newBuilder();
        builder.addAllHeroPool(RecruitConfigService.getHeroIds());
        builder.setHeroId(recruit.getWishHeroId());
        builder.setTimes(recruit.getTimes());
        builder.setTotalTimes(recruit.getTotalTimes());
        builder.putAllBuyTimes(recruit.getBuyTimesMap());
        builder.putAllActivates(recruit.getActivatesMap());
        return builder.build();
    }

    public static RecruitProtocol.RecruitRecordResponse buildRecruitRecordResponse(Collection<RecruitRecordVO> voList) {
        RecruitProtocol.RecruitRecordResponse.Builder builder = RecruitProtocol.RecruitRecordResponse.newBuilder();
        for (RecruitRecordVO vo : voList) {
            builder.addRecord(buildRecruitRecord(vo));
        }
        return builder.build();
    }

    public static RecruitProtocol.RecruitRecord buildRecruitRecord(RecruitRecordVO vo) {
        RecruitProtocol.RecruitRecord.Builder builder = RecruitProtocol.RecruitRecord.newBuilder();
        builder.setServerId(vo.getServerId());
        builder.setName(vo.getName());
        builder.setRewardList(PbBuilder.buildRewardObjectList(vo.getRewardList()));
        builder.setDes(vo.getDes());
        return builder.build();
    }
}
