package cn.daxiang.hbtd.gameserver.module.battlefieldDrill.dao.impl;

import cn.daxiang.framework.database.SingleEntity;
import cn.daxiang.framework.database.dao.SingleEntityDaoImpl;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.framework.utils.DateUtils;
import cn.daxiang.framework.utils.StopWatch;
import cn.daxiang.framework.utils.rank.cache.ConsistentLadderRankCache;
import cn.daxiang.hbtd.gameserver.core.database.GameInit;
import cn.daxiang.hbtd.gameserver.core.database.InitIndex;
import cn.daxiang.hbtd.gameserver.core.database.table.BattlefieldDrill;
import cn.daxiang.hbtd.gameserver.module.battlefieldDrill.dao.BattlefieldDrillDao;
import cn.daxiang.hbtd.gameserver.module.battlefieldDrill.model.BattlefieldDrillRank;
import cn.daxiang.hbtd.gameserver.module.battlefieldDrill.model.HonourRank;
import cn.daxiang.hbtd.gameserver.module.battlefieldDrill.model.entity.BattlefieldDrillFirstPassData;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

@Component
public class BattlefieldDrillDaoImpl extends SingleEntityDaoImpl implements BattlefieldDrillDao, GameInit {
    private final static int PAGE_SIZE = 50;

    private final static int HONOUR_PAGE_SIZE = 10;
    /**
     * 沙场演兵排行Map
     */
    private ConsistentLadderRankCache<Long, BattlefieldDrillRank> BATTLEFIELD_DRILL_RANK;

    private TreeMap<Integer, ConsistentLadderRankCache<Long, HonourRank>> HONOUR_RANK = Maps.newTreeMap();

    @Override
    protected Class<? extends SingleEntity<?>> forClass() {
        return BattlefieldDrill.class;
    }

    @Override
    protected void initMaxId() {

    }

    @Override
    public void gameInit() {
        BATTLEFIELD_DRILL_RANK = new ConsistentLadderRankCache<>(PAGE_SIZE, Short.MAX_VALUE);
        StopWatch sw = new StopWatch(true);
        int pageIndex = 0;
        while (true) {
            int limitBegin = pageIndex * jdbc.PAGE_SIZE;
            int limitEnd = jdbc.PAGE_SIZE;
            String sql = "select actorId,id,achieveTime  from battlefield_drill where id > 0 and lastResetTime > ? limit ?";
            LinkedHashMap<String, Object> paramMaps = new LinkedHashMap<>();
            paramMaps.put("lastResetTime", DateUtils.getTodayStart().getTimeInMillis());
            if (limitBegin > 0) {
                sql += " , ?";
                paramMaps.put("limitBegin", limitBegin);
            }
            paramMaps.put("limitEnd", limitEnd);
            List<BattlefieldDrillRank> list = jdbc.query(sql, paramMaps.values().toArray(), new BattlefieldDrillRank());
            if (list.isEmpty()) {
                break;
            }
            for (BattlefieldDrillRank rank : list) {
                BATTLEFIELD_DRILL_RANK.achieve(rank);
            }
            pageIndex++;
        }
        sw.stop();
        LOGGER.info("battlefield drill  rank loading complete! total:[{}] record. time:{}ms", BATTLEFIELD_DRILL_RANK.findSize(), sw.runTime());
        initHonourRank();
    }

    private void initHonourRank() {
        StopWatch sw = new StopWatch(true);
        int pageIndex = 0;
        while (true) {
            LinkedHashMap<String, Object> paramMaps = new LinkedHashMap<>();
            List<BattlefieldDrill> list = jdbc.getList(BattlefieldDrill.class, paramMaps, pageIndex * jdbc.PAGE_SIZE, jdbc.PAGE_SIZE);
            if (list.size() < 1) {
                break;
            }
            for (BattlefieldDrill battlefieldDrill : list) {
                if (battlefieldDrill.getId() == 0) {
                    continue;
                }
                for (Map.Entry<Integer, BattlefieldDrillFirstPassData> entry : battlefieldDrill.getFirstPassData().entrySet()) {
                    ConsistentLadderRankCache<Long, HonourRank> honourLadderRankCache = HONOUR_RANK.get(entry.getKey());
                    if (honourLadderRankCache == null) {
                        honourLadderRankCache = new ConsistentLadderRankCache<>(HONOUR_PAGE_SIZE, HONOUR_PAGE_SIZE);
                        HONOUR_RANK.put(entry.getKey(), honourLadderRankCache);
                    }
                    honourLadderRankCache.achieve(HonourRank.valueOf(battlefieldDrill.getActorId(), entry.getValue().getPower(), entry.getValue().getAchieveTime()));
                }

            }
            pageIndex++;
        }

        sw.stop();
        LOGGER.info("actor loading complete! record. time:{}ms", sw.runTime());
    }

    @Override
    public InitIndex index() {
        return InitIndex.INTI_SECOND;
    }

    @Override
    public BattlefieldDrill getBattlefieldDrill(long actorId) {
        BattlefieldDrill table = this.get(IdentiyKey.build(actorId));
        if (table.newEntity()) {
            table.setNewEntity(false);
            table.reset(0);
            this.updateQueue(table);
        }
        return table;
    }

    @Override
    public BattlefieldDrillRank getBattlefieldDrillRank(long actorId) {
        return BATTLEFIELD_DRILL_RANK.find(actorId);
    }

    @Override
    public BattlefieldDrillRank getBattlefieldDrillRankByRank(long rank) {
        return BATTLEFIELD_DRILL_RANK.findByRank(rank);
    }

    @Override
    public void achieveRank(BattlefieldDrill ancientCorridor) {
        BattlefieldDrillRank rank = BATTLEFIELD_DRILL_RANK.find(ancientCorridor.getActorId());
        if (rank == null) {
            rank = BattlefieldDrillRank.valueOf(ancientCorridor.getActorId(), ancientCorridor.getId(), ancientCorridor.getAchieveTime());
        } else {
            rank.reset(ancientCorridor.getId(), ancientCorridor.getAchieveTime());
        }
        BATTLEFIELD_DRILL_RANK.achieve(rank);
    }

    @Override
    public Collection<BattlefieldDrillRank> getRanks() {
        return BATTLEFIELD_DRILL_RANK.findRanks(1);
    }

    @Override
    public Collection<BattlefieldDrillRank> getAllRanks() {
        return BATTLEFIELD_DRILL_RANK.findAll();
    }

    @Override
    public int getRankSize() {
        return BATTLEFIELD_DRILL_RANK.findSize();
    }


    @Override
    public void cleanRank() {
        BATTLEFIELD_DRILL_RANK.cleanRank();
    }

    @Override
    public void achieveHonourRank(long actorId, int id, long power, long achieveTime) {
        ConsistentLadderRankCache<Long, HonourRank> ladderRankCache = HONOUR_RANK.get(id);
        if (ladderRankCache == null) {
            ladderRankCache = new ConsistentLadderRankCache<>(HONOUR_PAGE_SIZE, HONOUR_PAGE_SIZE);
            HONOUR_RANK.put(id, ladderRankCache);
        }

        HonourRank rank = ladderRankCache.find(actorId);
        if (rank == null) {
            rank = HonourRank.valueOf(actorId, power, achieveTime);
        } else {
            rank.reset(power, achieveTime);
        }
        ladderRankCache.achieve(rank);
    }

    @Override
    public Collection<HonourRank> getHonourRanks(int id) {
        ConsistentLadderRankCache<Long, HonourRank> ladderRankCache = HONOUR_RANK.get(id);
        if (ladderRankCache == null) {
            return Lists.newArrayList();
        }
        return ladderRankCache.findRanks(1);
    }

    @Override
    public Map<Integer, Collection<HonourRank>> getHonourRanks() {
        Map<Integer, Collection<HonourRank>> map = Maps.newHashMap();
        for (Map.Entry<Integer, ConsistentLadderRankCache<Long, HonourRank>> entry : HONOUR_RANK.entrySet()) {
            if (entry.getValue() == null) {
                continue;
            }
            map.put(entry.getKey(), entry.getValue().findRanks(1));
        }

        return map;
    }
}
