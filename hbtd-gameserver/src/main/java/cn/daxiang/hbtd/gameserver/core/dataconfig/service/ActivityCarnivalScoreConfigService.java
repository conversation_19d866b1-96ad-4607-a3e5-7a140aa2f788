package cn.daxiang.hbtd.gameserver.core.dataconfig.service;

import cn.daxiang.framework.dataconfig.ConfigServiceAdapter;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.ActivityCarnivalScoreConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.CarnivalTypeConfig;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Collections;
import java.util.Map;
import java.util.TreeMap;

/**
 * 新服狂欢
 */
@Component
public class ActivityCarnivalScoreConfigService extends ConfigServiceAdapter {

    private static Map<Integer, TreeMap<Integer, ActivityCarnivalScoreConfig>> ACTIVITY_CARNIVAL_SCORE_CONFIG_MAP = Maps.newHashMap();

    /**
     * key:data,value:Collection<taskId>
     */
    private static final Map<Integer, Collection<Integer>> ACTIVITY_1_TASK_CONFIG_MAP = Maps.newHashMap();

    public static Map<Integer, ActivityCarnivalScoreConfig> getActivityCarnivalScoreConfigMap(int data) {
        Map<Integer, ActivityCarnivalScoreConfig> map = ACTIVITY_CARNIVAL_SCORE_CONFIG_MAP.get(data);
        return map;

    }

    @Override
    protected void initialize() {
        Collection<ActivityCarnivalScoreConfig> list = dataConfig.listAll(this, ActivityCarnivalScoreConfig.class);

        for (ActivityCarnivalScoreConfig config : list) {
            TreeMap<Integer, ActivityCarnivalScoreConfig> configMap = ACTIVITY_CARNIVAL_SCORE_CONFIG_MAP.get(config.getData());
            if (configMap == null) {
                configMap = Maps.newTreeMap();
                ACTIVITY_CARNIVAL_SCORE_CONFIG_MAP.put(config.getData(), configMap);
            }
            configMap.put(config.getScore(), config);
        }

        Collection<CarnivalTypeConfig> taskConfigs = dataConfig.listAll(this, CarnivalTypeConfig.class);
        for (CarnivalTypeConfig taskConfig : taskConfigs) {
            Collection<Integer> taskIdList = ACTIVITY_1_TASK_CONFIG_MAP.computeIfAbsent(taskConfig.getData(), k -> Sets.newHashSet());
            taskIdList.addAll(taskConfig.getTaskIdList());
        }
    }

    @Override
    protected void clean() {
        ACTIVITY_CARNIVAL_SCORE_CONFIG_MAP.clear();
        ACTIVITY_1_TASK_CONFIG_MAP.clear();
    }

    public static boolean isContainsTask(int data, int taskId) {
        Collection<Integer> taskIdList = ACTIVITY_1_TASK_CONFIG_MAP.getOrDefault(data, Collections.emptyList());
        return taskIdList.contains(taskId);
    }
}