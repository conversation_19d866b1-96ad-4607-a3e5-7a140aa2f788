package cn.daxiang.hbtd.gameserver.module.arena.facade.impl;

import cn.daxiang.framework.context.ApplicationInitCompleteEvent;
import cn.daxiang.framework.event.ActorEvent;
import cn.daxiang.framework.event.GameEvent;
import cn.daxiang.framework.event.TriggerTime;
import cn.daxiang.framework.event.annotation.Event;
import cn.daxiang.framework.event.annotation.EventOnline;
import cn.daxiang.framework.result.CollectionResult;
import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.framework.utils.DateUtils;
import cn.daxiang.framework.utils.TimeConstant;
import cn.daxiang.framework.utils.lock.ChainLock;
import cn.daxiang.framework.utils.lock.LockUtils;
import cn.daxiang.hbtd.gameserver.core.GameBaseFacade;
import cn.daxiang.hbtd.gameserver.core.GameConfig;
import cn.daxiang.hbtd.gameserver.core.database.table.Arena;
import cn.daxiang.hbtd.gameserver.core.dataconfig.convert.impl.RewardObjectMapConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.ArenaDailyChallengeRewardConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.ArenaHistoricalRankRewardConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.ArenaRankRewardConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.RewardPoolConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.TitleConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.ArenaConfigService;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.RewardConfigService;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.TitleConfigService;
import cn.daxiang.hbtd.gameserver.core.dispatch.DispatchHelper;
import cn.daxiang.hbtd.gameserver.core.event.impl.ArenaChallengeEvent;
import cn.daxiang.hbtd.gameserver.core.event.impl.MailAddEvent;
import cn.daxiang.hbtd.gameserver.core.event.impl.TitleCreateEvent;
import cn.daxiang.hbtd.gameserver.core.netty.channel.PlayerChannel;
import cn.daxiang.hbtd.gameserver.core.oss.GameOssLogger;
import cn.daxiang.hbtd.gameserver.core.oss.type.OperationType;
import cn.daxiang.hbtd.gameserver.module.PbBuilder;
import cn.daxiang.hbtd.gameserver.module.arena.dao.ArenaDao;
import cn.daxiang.hbtd.gameserver.module.arena.facade.ArenaFacade;
import cn.daxiang.hbtd.gameserver.module.arena.helper.ArenaPushHelper;
import cn.daxiang.hbtd.gameserver.module.arena.model.ArenaRank;
import cn.daxiang.hbtd.gameserver.module.arena.model.config.ArenaMatch;
import cn.daxiang.hbtd.gameserver.module.arena.model.config.ArenaMatchNumber;
import cn.daxiang.hbtd.gameserver.module.arena.model.entity.ArenaReportEntity;
import cn.daxiang.hbtd.gameserver.module.battle.parser.battle.BattleContext;
import cn.daxiang.hbtd.gameserver.module.battle.parser.battle.BattleParser;
import cn.daxiang.hbtd.gameserver.module.goods.helper.RewardHelper;
import cn.daxiang.hbtd.gameserver.module.mail.type.MailTemplateType;
import cn.daxiang.hbtd.gameserver.module.prerogative.helper.PrerogativeHelper;
import cn.daxiang.hbtd.gameserver.module.prerogative.type.PrerogativeType;
import cn.daxiang.hbtd.gameserver.module.title.type.TitleConditionType;
import cn.daxiang.hbtd.gameserver.module.user.helper.ActorHelper;
import cn.daxiang.hbtd.gameserver.module.user.helper.ActorPushHelper;
import cn.daxiang.hbtd.gameserver.module.user.type.ActorUnlockType;
import cn.daxiang.protocol.game.ArenaProtocol;
import cn.daxiang.protocol.game.ArenaProtocol.ArenaRankResponse;
import cn.daxiang.protocol.game.ArenaProtocol.ArenaReport;
import cn.daxiang.protocol.game.CommonProtocol;
import cn.daxiang.protocol.game.CommonProtocol.ActorProfile;
import cn.daxiang.protocol.game.CommonProtocol.RewardResult;
import cn.daxiang.protocol.game.TypeProtocol.ActorFieldType;
import cn.daxiang.protocol.game.UserProtocol;
import cn.daxiang.shared.GlobalConfigKey;
import cn.daxiang.shared.event.EventKey;
import cn.daxiang.shared.module.battle.BattleParameterKey;
import cn.daxiang.shared.module.battle.BattleType;
import cn.daxiang.shared.reward.RewardObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Component
public class ArenaFacadeImpl extends GameBaseFacade implements ArenaFacade, ApplicationListener<ApplicationInitCompleteEvent> {
    /**
     * 玩家对手缓存列表Map{key:actorId,value:Collection<rank>}
     */
    private static Map<Long, Collection<Long>> ARENA_RANKLIST_CACHE = Maps.newConcurrentMap();
    /**
     * 正在战斗中的Map{key:actorId,value:rank}
     */
    private static Set<Long> ARENA_BATTLING_LIST = Sets.newConcurrentHashSet();
    @Autowired
    private ArenaDao arenaDao;
    @Autowired
    private BattleContext battleContext;

    private void sendArenaRankReward() {
        // key:rank,value:actorId
        String date = DateUtils.SDF_SHORT_DATE.get().format(new Date());
        Map<Long, TitleConfig> arenaRankTitleMap = Maps.newHashMap();
        Collection<TitleConfig> titleConfigList = TitleConfigService.getTitleConfigList(TitleConditionType.ARENA_RANK);
        for (TitleConfig titleConfig : titleConfigList) {
            arenaRankTitleMap.put(titleConfig.getValue(), titleConfig);
        }
        for (Map.Entry<Long, Long> entry : arenaDao.getArenaRankMap().entrySet()) {
            TitleConfig titleConfig = arenaRankTitleMap.get(entry.getKey());
            if (!ActorHelper.isRobot(entry.getValue()) && titleConfig != null) {
                DispatchHelper.postEvent(new TitleCreateEvent(entry.getValue(), titleConfig));
            }
            ArenaRankRewardConfig config = ArenaConfigService.getArenaRankRewardConfig(entry.getKey());
            if (config == null || config.getRewardList().isEmpty()) {
                break;
            }
            Map<String, String> params = Maps.newHashMap();
            params.put("rank", String.valueOf(entry.getKey()));
            params.put("date", date);
            params.put("name", ActorHelper.getActorName(entry.getValue()));
            MailAddEvent event = new MailAddEvent(entry.getValue(), MailTemplateType.ARENA_ERVER_DAY_RANK_REWARD, params, config.getRewardList());
            DispatchHelper.postEvent(event);
            LOGGER.debug("SendArenaRankReward actorId:{},params:{}", entry.getValue(), params);
            if (DateUtils.getChineseDayOfWeek() == 7 && entry.getKey() == 1) {
                DispatchHelper.postEvent(new ActorEvent(EventKey.ARENA_WEEK_RANK_FIRST_EVENT, entry.getValue()) {
                });
            }
        }
        LOGGER.info("SendArenaRankReward Completed !");
    }

    //删除过期的战报
    private void removeOverTimeReportRecord() {
        // key:rank,value:actorId
        for (Long actorId : arenaDao.getArenaRankMap().values()) {
            if (ActorHelper.isRobot(actorId)) {
                continue;
            }
            TResult<Arena> result = this.getArena(actorId);
            if (result.isFail()) {
                continue;
            }
            Collection<Long> battleReplayIdList = Lists.newArrayList();
            Arena arena = result.item;
            if (arena.getReportList().isEmpty()) {
                continue;
            }
            long nowTime = System.currentTimeMillis();
            //战报存储的时间限制（单位是小时）
            long limitHour = globalConfigService.findGlobalConfig(GlobalConfigKey.ARENA_REPORT_TIME_LIMIT).findLong();
            //战报存储的时间限制转化为毫秒单位
            long limitMilliSecond = limitHour * TimeConstant.ONE_HOUR_MILLISECOND;
            Iterator<ArenaReportEntity> iterator = arena.getReportList().iterator();
            while (iterator.hasNext()) {
                ArenaReportEntity entity = iterator.next();
                if (nowTime >= entity.getTime() + limitMilliSecond) {
                    long battleReplayId = entity.getBattleReplyId();
                    battleReplayIdList.add(battleReplayId);
                    iterator.remove();
                }
            }
            dbQueue.updateQueue(arena);
            //            DispatchHelper.postEvent(new BattleReplayReportDeleteEvent(actorId, battleReplayIdList));
        }
        LOGGER.info("DeleteArenaOverTimeReport Completed !");
    }

    @Override
    public TResult<Arena> getArena(long actorId) {
        Result unlockResult = ActorHelper.unlock(actorId, ActorUnlockType.ARENA);
        if (unlockResult.isFail()) {
            return TResult.valueOf(unlockResult.statusCode);
        }
        Arena arena = arenaDao.getArena(actorId);
        return TResult.sucess(arena);
    }

    @Override
    public long getArena1stId() {
        return arenaDao.getArenaRank(1).getActorId();
    }

    @Override
    public CollectionResult<ArenaProtocol.ArenaRank> getArenaRankList(long actorId, boolean isRefresh) {
        TResult<Arena> arenaResult = this.getArena(actorId);
        if (arenaResult.isFail()) {
            return CollectionResult.valueOf(arenaResult.statusCode);
        }
        Collection<Long> ranks;
        //缓存中存在直接在缓存中取，缓存中没有则调用getRankList生成一个
        //如果isRefresh为true,无论缓存中是否存在值都重新生成玩家对手，并加入缓存
        if (isRefresh || !ARENA_RANKLIST_CACHE.containsKey(actorId)) {
            ranks = this.getRankList(arenaResult.item);
            ARENA_RANKLIST_CACHE.put(actorId, ranks);
        } else {
            ranks = ARENA_RANKLIST_CACHE.get(actorId);
        }
        for (long i = 1; i <= 10; i++) {
            if (ranks.contains(i)) {
                continue;
            }
            ranks.add(i);
        }
        Collection<ArenaProtocol.ArenaRank> rankList = Lists.newArrayListWithCapacity(ranks.size());
        for (long rank : ranks) {
            ArenaRank arenaRank = arenaDao.getArenaRank(rank);
            if (arenaRank == null) {
                continue;
            }
            ActorProfile actorProfile = ActorHelper.getActorProfile(arenaRank.getActorId());
            ArenaProtocol.ArenaRank.Builder builder = ArenaProtocol.ArenaRank.newBuilder();
            builder.setActorProfile(actorProfile);
            builder.setRank(rank);
            String declaration = ActorHelper.getDeclaration(arenaRank.getActorId());
            builder.setDeclaration(declaration);
            rankList.add(builder.build());
        }
        return CollectionResult.collection(rankList);
    }

    private Collection<Long> getRankList(Arena arena) {
        long lastRank = arenaDao.getLastRank();
        ArenaMatchNumber matchNumber = globalConfigService.findGlobalObject(GlobalConfigKey.ARENA_MATCH_NUMBER, ArenaMatchNumber.class);
        ArenaMatch match = globalConfigService.findGlobalObject(GlobalConfigKey.ARENA_MATCH, ArenaMatch.class);
        List<Long> ranks = match.findMatch(arena.getRank(), lastRank, matchNumber.getUpNumber(), matchNumber.getDownNumber());
        return ranks;
    }

    @Override
    public Result challenge(long actorId, long opponent) {
        TResult<Arena> arenaResult = this.getArena(actorId);
        if (arenaResult.isFail()) {
            return Result.valueOf(arenaResult.statusCode);
        }
        if (ARENA_BATTLING_LIST.contains(actorId)) {
            return Result.valueOf(ARENA_SELF_BATTLING);
        }
        if (ARENA_BATTLING_LIST.contains(opponent)) {
            return Result.valueOf(ARENA_OPPONENT_BATTLING);
        }
        Arena arena = arenaResult.item;
        long targetRank = arenaDao.getArenaRankByActorId(opponent);
        if (targetRank == 0) {
            return Result.valueOf(ARENA_CHALLENGE_RANK_ERROR);
        }
        // 测试取消屏蔽
        ArenaMatch match = globalConfigService.findGlobalObject(GlobalConfigKey.ARENA_MATCH, ArenaMatch.class);
        if (!match.inRange(arena.getRank(), targetRank)) {
            return Result.valueOf(ARENA_CHALLENGE_RANK_ERROR);
        }
        int playTimes = globalConfigService.findGlobalConfig(GlobalConfigKey.ARENA_PLAY_TIMES).findInt();
        if (arena.getPlayTimes() >= playTimes + arena.getBuyTimes()) {
            return Result.valueOf(ARENA_PLAY_TIMES_NOT_ENOUGH);
        }
        arena.play();
        dbQueue.updateQueue(arena);
        BattleParser parser = battleContext.getParser(BattleType.ARENA);
        Map<BattleParameterKey, Object> parameterMap = Maps.newHashMap();
        parameterMap.put(BattleParameterKey.TARGET_ACTOR_ID, opponent);
        ArenaPushHelper.pushArenaInfo(actorId, arena);
        return parser.fight(actorId, parameterMap);
    }

    @Override
    public Result buyTimes(long actorId, int buyTimes) {
        TResult<Arena> arenaResult = this.getArena(actorId);
        if (arenaResult.isFail()) {
            return Result.valueOf(arenaResult.statusCode);
        }
        Arena arena = arenaResult.item;
        if (buyTimes <= 0 || buyTimes > GameConfig.getClientCountLimit()) {
            return Result.valueOf(INVALID_PARAM);
        }
        int timesLimit = PrerogativeHelper.getIntPrerogative(actorId, PrerogativeType.ARENA_CHALLENGE_BUY_TIMES);
        if (arena.getBuyTimes() + buyTimes > timesLimit) {
            return Result.valueOf(ARENA_BUY_TIMES_ERROR);
        }

        RewardObjectMapConfig costConfig = globalConfigService.findGlobalObject(GlobalConfigKey.ARENA_RESET_COST, RewardObjectMapConfig.class);
        Collection<RewardObject> costList = Lists.newArrayList();
        for (int i = arena.getBuyTimes() + 1; i <= arena.getBuyTimes() + buyTimes; i++) {
            costList.add(costConfig.getRewardObject(i));
        }
        Result decreaseResult = RewardHelper.decrease(actorId, costList, OperationType.ARENA_RESET_TIMES);
        if (decreaseResult.isFail()) {
            return decreaseResult;
        }
        arena.buyTimes(buyTimes);
        dbQueue.updateQueue(arena);
        ArenaPushHelper.pushArenaInfo(actorId, arena);
        return Result.valueOf();
    }

    @Override
    public void recordChallengeResult(long actorId, long opponent, boolean isWin, long battleReplayId, long createTime) {
        int reportLimit = globalConfigService.findGlobalConfig(GlobalConfigKey.ARENA_REPORT_NUMBER_LIMIT).findInt();
        Arena arena = arenaDao.getArena(actorId);
        Arena opponentArena = arenaDao.getArena(opponent);
        long rank = arena.getRank();
        long opponentRank = opponentArena.getRank();
        ArenaReportEntity reportEntity = ArenaReportEntity.valueOf(actorId, opponent, isWin ? actorId : opponent, createTime, rank, opponentRank, battleReplayId);
        boolean isRefreshMaxRank = false;
        ChainLock lock = LockUtils.getLock(arena, opponentArena);
        try {
            lock.lock();
            boolean isRefrsh = false;
            Long actorBattleReplayId = arena.addReport(reportEntity, reportLimit);
            //            if (actorBattleReplayId != null) {
            //抛出战斗回放删除事件
            //                DispatchHelper.postEvent(new BattleReplayReportDeleteEvent(actorId, Lists.newArrayList(actorBattleReplayId), false));
            //            }
            //抛出战斗回放添加引用事件
            //            DispatchHelper.postEvent(new BattleReplayReportAddCitationEvent(actorId, Lists.newArrayList(reportEntity.getBattleReplyId())));
            //如果对手为机器人则不计战报
            if (!ActorHelper.isRobot(opponent)) {
                Long opponentBattleReplayId = opponentArena.addReport(reportEntity, reportLimit);
                //                if (opponentBattleReplayId != null) {
                //抛出战斗回放删除事件
                //                    DispatchHelper.postEvent(new BattleReplayReportDeleteEvent(actorId, Lists.newArrayList(opponentBattleReplayId), false));
                //                }
                //抛出战斗回放添加引用事件
                //                DispatchHelper.postEvent(new BattleReplayReportAddCitationEvent(actorId, Lists.newArrayList(reportEntity.getBattleReplyId())));
            }
            if (isWin && rank > opponentRank) {
                arenaDao.swapRank(rank, opponentRank);
                isRefreshMaxRank = arena.setRank(opponentRank);
                opponentArena.setRank(rank);
                isRefrsh = true;
                //如果败方不是机器人也需要刷新败方的对手列表
                if (!ActorHelper.isRobot(opponent)) {
                    CollectionResult<ArenaProtocol.ArenaRank> opponentRankListResult = this.getArenaRankList(opponent, true);
                    ArenaPushHelper.pushArenaInfo(opponent, opponentArena, opponentRankListResult.item);
                }
                GameOssLogger.arena(actorId, arena.getRank());
            }

            CollectionResult<ArenaProtocol.ArenaRank> rankListResult = this.getArenaRankList(actorId, isRefrsh);
            ArenaPushHelper.pushArenaInfo(actorId, arena, rankListResult.item);
        } finally {
            lock.unlock();

        }
        if (isRefreshMaxRank) {
            ActorPushHelper.pushActorAttribute(actorId, ActorFieldType.ARENA_MAX_RANK);
        }
        dbQueue.updateQueue(arena, opponentArena);
        ARENA_BATTLING_LIST.remove(actorId);
        ARENA_BATTLING_LIST.remove(opponent);

        this.pushNewReport(actorId);
        this.pushNewReport(opponent);
    }

    public void pushNewReport(long actorId) {
        if (ActorHelper.isRobot(actorId)) {
            return;
        }
        if (!PlayerChannel.isOnline(actorId)) {
            return;
        }
        TResult<Arena> arenaResult = this.getArena(actorId);
        if (arenaResult.isFail()) {
            return;
        }
        Arena arena = arenaResult.item;
        Collection<ArenaReportEntity> reportList = arena.getReportList();
        boolean push = false;
        for (ArenaReportEntity entity : reportList) {
            if (entity.getTime() > arena.getLastPushTime()) {
                push = true;
                break;
            }
        }
        if (push) {
            arena.setLastPushTime(System.currentTimeMillis());
            dbQueue.updateQueue(arena);
            ArenaPushHelper.pushNewReport(actorId);
        }
    }

    @Override
    public CollectionResult<ArenaReport> getReportList(long actorId) {
        TResult<Arena> arenaResult = this.getArena(actorId);
        if (arenaResult.isFail()) {
            return CollectionResult.valueOf(arenaResult.statusCode);
        }
        Collection<ArenaReportEntity> reports;
        Arena arena = arenaResult.item;
        reports = arena.getReportList();
        Collection<ArenaReport> reportList = Lists.newArrayListWithCapacity(reports.size());
        for (ArenaReportEntity entity : reports) {
            ActorProfile challenge = ActorHelper.getActorProfile(entity.getChallenge());
            ActorProfile opponent = ActorHelper.getActorProfile(entity.getOpponent());

            ArenaReport.Builder builder = ArenaReport.newBuilder();
            builder.setChallenge(challenge);
            builder.setOpponent(opponent);
            builder.setChallengeRank(entity.getChallengeRank());
            builder.setWinner(entity.getWinner());
            builder.setTime(entity.getTime());
            builder.setOpponentRank(entity.getOpponentRank());
            builder.setBattleReplayId(entity.getBattleReplyId());
            builder.setPower(ActorHelper.getActorPower(actorId));
            ArenaReport arenaReport = builder.build();
            reportList.add(arenaReport);
        }
        return CollectionResult.collection(reportList);
    }

    private void resetArena(long actorId) {
        TResult<Arena> arenaResult = this.getArena(actorId);
        if (arenaResult.isFail()) {
            return;
        }
        Arena arena = arenaResult.item;
        if (DateUtils.isToday(arena.getLastResetTime())) {
            return;
        }
        arena.reset();
        dbQueue.updateQueue(arena);
        ArenaPushHelper.pushArenaInfo(actorId, arena);
    }

    @EventOnline(trigger = TriggerTime.FIXED_HOUR, triggerValue = 24)
    public void onEverySecond(GameEvent e) {
        this.resetArena(e.getUniqueId());
    }

    @Event(name = EventKey.ACTOR_LOGIN)
    public void onActorLogin(GameEvent e) {
        long actorId = e.getUniqueId();
        this.resetArena(actorId);
        this.pushNewReport(actorId);
    }

    @Override
    public TResult<ArenaRankResponse> getArenaRankResponse(long actorId, int page) {
        Collection<ArenaRank> ranks = arenaDao.getArenaRanks(page);
        ArenaRank arenaRank = arenaDao.findLineRank(actorId);
        if (arenaRank != null) {
            ranks.add(arenaRank);
        }
        Collection<ArenaProtocol.ArenaRank> rankList = Lists.newArrayList();
        for (ArenaRank rank : ranks) {
            ActorProfile actorProfile = ActorHelper.getActorProfile(rank.getActorId());
            ArenaProtocol.ArenaRank.Builder builder = ArenaProtocol.ArenaRank.newBuilder();
            builder.setActorProfile(actorProfile);
            builder.setRank(rank.getRank());
            rankList.add(builder.build());
        }
        int pages = arenaDao.getArenaRankPages();
        ArenaRankResponse.Builder builder = ArenaRankResponse.newBuilder();
        builder.addAllRanks(rankList);
        builder.setPage(page);
        builder.setPages(pages);
        builder.setPraiseCount(ActorHelper.getPraiseCount(UserProtocol.RankType.ARENA_RANK));
        ArenaRankResponse response = builder.build();
        return TResult.sucess(response);
    }

    @Override
    public TResult<ArenaRank> getArenaRank(long rank) {
        ArenaRank arenaRank = arenaDao.getArenaRank(rank);
        if (arenaRank == null) {
            return TResult.fail();
        }
        return TResult.sucess(arenaRank);
    }

    @Override
    public TResult<ArenaProtocol.ArenaWipeOutResponse> wipeOut(long actorId, long opponent) {
        TResult<Arena> arenaResult = this.getArena(actorId);
        if (arenaResult.isFail()) {
            return TResult.valueOf(arenaResult.statusCode);
        }
        Arena arena = arenaResult.item;
        long targetRank = arenaDao.getArenaRankByActorId(opponent);
        if (targetRank == 0) {
            return TResult.valueOf(ARENA_CHALLENGE_RANK_ERROR);
        }
        int playTimes = globalConfigService.findGlobalConfig(GlobalConfigKey.ARENA_PLAY_TIMES).findInt();
        if (arena.getPlayTimes() >= playTimes + arena.getBuyTimes()) {
            return TResult.valueOf(ARENA_PLAY_TIMES_NOT_ENOUGH);
        }
        //剩余次数
        int remainingTimes = playTimes + arena.getBuyTimes() - arena.getPlayTimes();
        if (remainingTimes > 5) {
            remainingTimes = 5;
        }
        Collection<RewardObject> rewardList = Lists.newLinkedList();
        ArenaProtocol.ArenaWipeOutResponse.Builder builder = ArenaProtocol.ArenaWipeOutResponse.newBuilder();
        int poolId = globalConfigService.findGlobalConfig(GlobalConfigKey.ARENA_BATTLE_WIN_REWARD_POOL_ID).findInt();
        for (int i = 1; i <= remainingTimes; i++) {
            RewardPoolConfig config = RewardConfigService.getRewardPoolConfig(poolId);
            if (config == null) {
                LOGGER.error("RewardPoolConfig is not found,poolId:{}", poolId);
                return TResult.valueOf(CONFIG_NOT_FOUND);
            }
            rewardList.addAll(config.getRewardList());
            builder.putRewards(i, PbBuilder.buildRewardObjectList(config.getRewardList()));
        }
        if (arena.getRank() > targetRank) {
            return TResult.valueOf(ARENA_CHALLENGE_RANK_ERROR);
        }
        arena.wipeOut(remainingTimes);
        RewardResult rewardResult = RewardHelper.sendRewardList(actorId, rewardList, OperationType.ARENA_BATTLE);
        dbQueue.updateQueue(arena);
        DispatchHelper.postEvent(new ArenaChallengeEvent(actorId, opponent, true, targetRank, arena.getMaxRank(), remainingTimes));
        ArenaPushHelper.pushArenaInfo(actorId, arena);
        return TResult.sucess(builder.build());
    }

    @Override
    public long getLastRank() {
        return arenaDao.getLastRank();
    }

    @Override
    public TResult<CommonProtocol.RewardResultResponse> historicalRankReward(long actorId, int configId) {
        TResult<Arena> arenaResult = this.getArena(actorId);
        if (arenaResult.isFail()) {
            return TResult.valueOf(arenaResult.statusCode);
        }
        Arena arena = arenaResult.item;
        ArenaHistoricalRankRewardConfig rewardConfig = globalConfigService.findConfig(configId, ArenaHistoricalRankRewardConfig.class);
        if (rewardConfig == null) {
            return TResult.valueOf(CONFIG_NOT_FOUND);
        }
        if (arena.getMaxRank() > rewardConfig.getRank()) {
            return TResult.valueOf(ARENA_RANK_NOT_ENOUGH);
        }
        if (arena.getReceiveHistoricalRankRewardList().contains(configId)) {
            return TResult.valueOf(ARENA_HISTORICAL_RANK_RECORD_RECEIVED);
        }
        RewardResult rewardResult = RewardHelper.sendRewardList(actorId, rewardConfig.getRankRewardList(), OperationType.ARENA_HISTORICAL_RANK_REWARD);
        arena.receiveHistoricalRankReward(configId);
        dbQueue.updateQueue(arena);
        ArenaPushHelper.pushArenaInfo(actorId, arena);
        CommonProtocol.RewardResultResponse response = PbBuilder.buildRewardResultResponse(rewardResult);
        return TResult.sucess(response);
    }

    @Override
    public TResult<CommonProtocol.RewardResultResponse> dailyReward(long actorId, Set<Integer> configIds) {
        TResult<Arena> arenaResult = this.getArena(actorId);
        if (arenaResult.isFail()) {
            return TResult.valueOf(arenaResult.statusCode);
        }
        Arena arena = arenaResult.item;

        Collection<RewardObject> rewardList = Lists.newArrayList();
        for (int configId : configIds) {
            ArenaDailyChallengeRewardConfig rewardConfig = globalConfigService.findConfig(configId, ArenaDailyChallengeRewardConfig.class);
            if (rewardConfig == null) {
                return TResult.valueOf(CONFIG_NOT_FOUND);
            }
            if (arena.getPlayDailyTotalTimes() < rewardConfig.getTimes()) {
                return TResult.valueOf(ARENA_DAILY_PLAY_TOTAL_TIMES_NOT_ENOUGH);
            }
            if (arena.getReceiveDailyRewardList().contains(configId)) {
                return TResult.valueOf(ARENA_DAILY_PLAY_TOTAL_RECORD_RECEIVED);
            }
            rewardList.addAll(rewardConfig.getDailyRewardList());
        }
        arena.getReceiveDailyRewardList().addAll(configIds);
        RewardResult rewardResult = RewardHelper.sendRewardList(actorId, rewardList, OperationType.ARENA_DAILY_PLAY_TOTAL_RECORD);
        dbQueue.updateQueue(arena);
        ArenaPushHelper.pushArenaInfo(actorId, arena);
        CommonProtocol.RewardResultResponse response = PbBuilder.buildRewardResultResponse(rewardResult);
        return TResult.sucess(response);
    }

    @Override
    public Result changeOpponent(long actorId) {
        TResult<Arena> arenaResult = this.getArena(actorId);
        if (arenaResult.isFail()) {
            return Result.valueOf(arenaResult.statusCode);
        }
        Arena arena = arenaResult.item;
        //测试取消屏蔽
        int changTimes = globalConfigService.findGlobalConfig(GlobalConfigKey.FREE_NUMBER_OF_ARENA_SUBSTITUTIONS).findInt();
        if (arena.getChangeTimes() >= changTimes) {
            int buyTimes = arena.getChangeTimes() - changTimes;
            RewardObjectMapConfig costConfig = globalConfigService.findGlobalObject(GlobalConfigKey.ARENA_REPLACEMENT_COST, RewardObjectMapConfig.class);
            Collection<RewardObject> costList = Lists.newArrayList(costConfig.getRewardObject(buyTimes + 1));
            Result decreaseResult = RewardHelper.decrease(actorId, costList, OperationType.ARENA_BUY_CHANGE_OPPONENT_TIMES);
            if (decreaseResult.isFail()) {
                return decreaseResult;
            }
        }
        arena.changeOpponent();
        dbQueue.updateQueue(arena);
        CollectionResult<ArenaProtocol.ArenaRank> collectionResult = this.getArenaRankList(actorId, true);
        ArenaPushHelper.pushArenaInfo(actorId, arena, collectionResult.item);
        return Result.valueOf();
    }

    @Override
    public void onApplicationEvent(ApplicationInitCompleteEvent event) {
        Integer sendTime = globalConfigService.findGlobalConfig(GlobalConfigKey.ARENA_SEND_RANK_TIME).findInt();
        if (sendTime == null) {
            sendTime = 22;
        }
        schedule.addFixedTime(new Runnable() {

            @Override
            public void run() {
                sendArenaRankReward();
            }
        }, sendTime);
        int minute = GameConfig.getServerId() % 60;
        schedule.addFixedTime(new Runnable() {

            @Override
            public void run() {
                removeOverTimeReportRecord();
            }
        }, 3, minute, 0);
    }
}
