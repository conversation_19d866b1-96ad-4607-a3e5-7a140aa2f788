package cn.daxiang.hbtd.gameserver.module.qindungeon.facade;

import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.hbtd.gameserver.core.database.table.QinDungeon;
import cn.daxiang.protocol.game.CommonProtocol;
import cn.daxiang.protocol.game.QindungeonProtocol;

/**
 * <AUTHOR>
 * @date 2020/8/4
 */
public interface QinDungeonFacade {
    /**
     * 获取远古棋局信息
     *
     * @param actorId
     * @return
     */
    TResult<QinDungeon> getQinDungeon(long actorId);

    /**
     * 获取特性id
     *
     * @return
     */
    int getFeatureId();

    /**
     * 获取远古棋局排名列表
     *
     * @param actorId
     * @return
     */
    TResult<QindungeonProtocol.QindungeonRankResponse> getQinDungeonRankList(long actorId);

    /**
     * 刷新出售区
     *
     * @param actorId
     * @return
     */
    Result refreshSale(long actorId);

    /**
     * 锁定出售区
     *
     * @param actorId
     * @return
     */
    Result lockSale(long actorId);

    /**
     * 购买棋子
     *
     * @param actorId
     * @param orderId
     * @return
     */
    Result buy(long actorId, int orderId);

    /**
     * 出售棋子
     *
     * @param actorId
     * @param orderId
     * @return
     */
    Result sell(long actorId, int orderId);

    /**
     * 升级
     *
     * @param actorId
     * @return
     */
    Result levelUp(long actorId);

    /**
     * 准备
     *
     * @param actorId
     * @return
     */
    Result prepare(long actorId);

    /**
     * 挑战
     *
     * @param actorId
     * @param hp
     * @param check
     * @return
     */
    TResult<QindungeonProtocol.QindungeonBattleRespones> battle(long actorId, int hp, String check);

    /**
     * 结算
     *
     * @param actorId
     * @return
     */
    TResult<QindungeonProtocol.QindungeonBattleRespones> settle(long actorId);

    /**
     * 扫荡
     *
     * @param actorId
     * @return
     */
    TResult<CommonProtocol.RewardResult> wipeOut(long actorId);

    /**
     * 领取段位奖励
     *
     * @param actorId
     * @return
     */
    TResult<CommonProtocol.RewardResult> receiveDanReward(long actorId, int dan);

    /**
     * 交换位置
     *
     * @param actorId
     * @param from
     * @param to
     * @return
     */
    Result swapOrder(long actorId, int from, int to);

    /**
     * 获取远古棋局战区排行榜排名列表
     *
     * @param actorId
     * @return
     */
    TResult<QindungeonProtocol.QindungeonRankResponse> getQinDungeonWarZoneRankList(long actorId);

    /**
     * 选择效果ID
     *
     * @param actorId
     * @param configId
     * @return
     */
    TResult<CommonProtocol.IntPacket> chooseEffectId(long actorId, int configId);

    /**
     * 使用幸运星（复制棋子）
     *
     * @param actorId
     * @param orderId
     * @return
     */
    Result useLuckStar(long actorId, int orderId);

    /**
     * 获得当前积分在排行榜中超过多少人
     *
     * @param actorId
     * @return
     */
    int getRatio(long actorId);

}
