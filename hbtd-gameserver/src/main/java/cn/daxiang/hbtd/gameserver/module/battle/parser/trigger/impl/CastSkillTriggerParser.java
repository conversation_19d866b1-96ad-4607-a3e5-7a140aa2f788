package cn.daxiang.hbtd.gameserver.module.battle.parser.trigger.impl;

import cn.daxiang.hbtd.gameserver.core.dataconfig.model.SkillConfig;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleRoom;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleSprite;
import cn.daxiang.hbtd.gameserver.module.battle.parser.trigger.AbstractSkillTriggerParser;
import cn.daxiang.hbtd.gameserver.module.battle.type.SkillTriggerType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2019/11/19
 */
@Component
public class CastSkillTriggerParser extends AbstractSkillTriggerParser {
    @Override
    protected SkillTriggerType getType() {
        return SkillTriggerType.CAST;
    }

    @Override
    public boolean trigger(BattleRoom battleRoom, BattleSprite battleSprite, BattleSprite caster, BattleSprite target, long triggerValue, SkillConfig triggerSkill,
        SkillConfig skillConfig) {
        //记技能次数
        battleSprite.addCastTimes();
        return true;
    }
}
