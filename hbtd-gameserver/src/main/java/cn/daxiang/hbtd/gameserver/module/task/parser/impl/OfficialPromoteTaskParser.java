package cn.daxiang.hbtd.gameserver.module.task.parser.impl;

import cn.daxiang.framework.result.TResult;
import cn.daxiang.hbtd.gameserver.core.database.table.Official;
import cn.daxiang.hbtd.gameserver.core.database.table.Task;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.TaskConfig;
import cn.daxiang.hbtd.gameserver.core.event.impl.OfficialPromoteEvent;
import cn.daxiang.hbtd.gameserver.module.official.facade.OfficialFacade;
import cn.daxiang.hbtd.gameserver.module.task.parser.AbstractTaskParser;
import cn.daxiang.hbtd.gameserver.module.task.type.TaskConditionType;
import cn.daxiang.protocol.game.TaskProtocol;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/7/14
 */
@Component
public class OfficialPromoteTaskParser extends AbstractTaskParser<OfficialPromoteEvent> {
    @Autowired
    private OfficialFacade officialFacade;

    @Override
    public void init(Task task, TaskConfig taskConfig) {
        parser(task, taskConfig);
    }

    private void parser(Task task, TaskConfig taskConfig) {
        TResult<Official> result = officialFacade.getOfficialInfo(task.getActorId());
        if (result.isFail()) {
            return;
        }
        task.setValue(result.item.getMaxLevel());
        if (task.getValue() >= taskConfig.getValue()) {
            task.setStatus(TaskProtocol.TaskStatus.TASK_FINISH);
        }
    }

    @Override
    protected TaskConditionType getType() {
        return TaskConditionType.OFFICIAL_PROMOTE;
    }

    @Override
    protected boolean parseCondition(OfficialPromoteEvent event, Task task, TaskConfig taskConfig) {
        if (task.getValue() >= event.level) {
            return false;
        }
        task.setValue(event.level);
        if (task.getValue() >= taskConfig.getValue()) {
            task.setStatus(TaskProtocol.TaskStatus.TASK_FINISH);
        }
        return true;
    }

}
