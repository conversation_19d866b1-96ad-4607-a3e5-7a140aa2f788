package cn.daxiang.hbtd.gameserver.module.battle.parser.battle.impl;

import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.rpc.RpcCall;
import cn.daxiang.framework.rpc.world.WorldBattleRpc;
import cn.daxiang.hbtd.gameserver.core.GameConfig;
import cn.daxiang.hbtd.gameserver.core.rpc.WorldRpcHelper;
import cn.daxiang.hbtd.gameserver.module.PbBuilder;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleMember;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleRoom;
import cn.daxiang.hbtd.gameserver.module.battle.parser.battle.AbstractBattleParser;
import cn.daxiang.hbtd.gameserver.module.user.helper.ActorHelper;
import cn.daxiang.protocol.game.BattleProtocol;
import cn.daxiang.protocol.game.CommonProtocol;
import cn.daxiang.protocol.game.TypeProtocol.ActorFieldType;
import cn.daxiang.shared.module.battle.BattleParameterKey;
import cn.daxiang.shared.module.battle.BattleType;
import cn.daxiang.shared.module.lineup.BattleLineupEntity;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;

/**
 * 切磋比试战斗解析
 *
 * <AUTHOR>
 * @date 2020/06/03
 */
@Component
public class CompeteBattleParser extends AbstractBattleParser {

    @Override
    public Result fight(long actorId, Map<BattleParameterKey, Object> parameterMap) {
        long targetActorId = actorId;
        Map<Byte, Object> attributes = (Map<Byte, Object>) parameterMap.get(BattleParameterKey.CROSS_BATTLE_ATTRIBUTE);
        actorId = Long.parseLong(attributes.get((byte) ActorFieldType.ACTOR_ID_VALUE).toString());
        long power = Long.parseLong(attributes.get((byte) ActorFieldType.ACTOR_POWER_VALUE).toString());
        BattleLineupEntity battleLineupEntity = (BattleLineupEntity) parameterMap.get(BattleParameterKey.CROSS_BATTLE_LINEUP);
        BattleMember rightMember = this.createBattleMember(targetActorId, BattleProtocol.BattleCamp.RIGHT_CAMP, getType());
        int officialLevel = (int) attributes.get((byte) ActorFieldType.ACTOR_OFFICIAL_VALUE);
        BattleMember leftMember = this.createBattleMember(actorId, power, officialLevel, BattleProtocol.BattleCamp.LEFT_CAMP, battleLineupEntity, getType());
        BattleRoom battleRoom = this.createBattleRoom(leftMember, rightMember, parameterMap);
        battleRoomFacade.createBattleRoom(battleRoom);
        return Result.valueOf();
    }

    @Override
    public void battleEnd(long actorId, BattleRoom battleRoom) {
        Map<Byte, Object> attributes = (Map<Byte, Object>) battleRoom.getParameterMap().get(BattleParameterKey.CROSS_BATTLE_ATTRIBUTE);
        long offensiveId = Long.parseLong(attributes.get((byte) ActorFieldType.ACTOR_ID_VALUE).toString());
        int offensiveServerId = (int) attributes.get((byte) ActorFieldType.SERVER_ID_VALUE);

        Collection<BattleProtocol.BattleMember> members = Lists.newArrayList();
        CommonProtocol.ActorProfile leftActorProfile = ActorHelper.getActorProfile(attributes);
        BattleProtocol.BattleMember leftBattleMember = battleRoom.getBattleMember(BattleProtocol.BattleCamp.LEFT_CAMP, leftActorProfile);
        members.add(leftBattleMember);
        CommonProtocol.ActorProfile rightActorProfile = ActorHelper.getActorProfile(battleRoom.getTargeter());
        BattleProtocol.BattleMember rightBattleMember = battleRoom.getBattleMember(BattleProtocol.BattleCamp.RIGHT_CAMP, rightActorProfile);
        members.add(rightBattleMember);

        CommonProtocol.RewardResult rewardResult = CommonProtocol.RewardResult.newBuilder().build();
        BattleProtocol.BattleResultResponse response =
            PbBuilder.buildBattleResultResponse(battleRoom.getBattleType(), members, battleRoom.getRightHistoryBattleSpriteVO(), battleRoom.getBattleReportMap(), rewardResult,
                battleRoom.getBattleStats());

        byte[] finalResponseBytes = response.toByteArray();
        WorldRpcHelper.asynCall(actorId, WorldBattleRpc.class, new RpcCall<WorldBattleRpc>() {
            @Override
            public void run(WorldBattleRpc rpcProxy) {
                rpcProxy.pushCompeteResult(GameConfig.getServerType(), offensiveServerId, offensiveId, battleRoom.isWin(), finalResponseBytes, battleRoom.getParameterMap());
            }
        });
    }

    @Override
    protected BattleType getType() {
        return BattleType.COMPETE;
    }
}