package cn.daxiang.hbtd.gameserver.module.friend.model.entity;

import java.util.HashMap;
import java.util.Map;

/**
 * 好友实体
 *
 * <AUTHOR>
 */
public class FriendEntity {
    /**
     * 角色属性Map
     * key:{@code ActorKey},value:Value
     */
    private Map<Byte, Object> attributes = new HashMap<>();
    /**
     * 是否在线
     */
    private boolean isOnline;
    /**
     * 最后在线时间
     */
    private long lastLogoutTime;
    /**
     * 亲密度
     */
    private int intimacy;

    public static FriendEntity valueOf(Map<Byte, Object> attributes, boolean isOnline, long lastLogoutTime) {
        FriendEntity vo = new FriendEntity();
        vo.attributes = attributes;
        vo.isOnline = isOnline;
        vo.lastLogoutTime = lastLogoutTime;
        return vo;
    }

    public static FriendEntity valueOf(Map<Byte, Object> attributes) {
        FriendEntity vo = new FriendEntity();
        vo.attributes = attributes;
        return vo;
    }

    public Map<Byte, Object> getAttributes() {
        return attributes;
    }

    public void setAttributes(Map<Byte, Object> attributes) {
        this.attributes = attributes;
    }

    public boolean getIsOnline() {
        return isOnline;
    }

    public void setIsOnline(boolean online) {
        isOnline = online;
    }

    public long getLastLogoutTime() {
        return lastLogoutTime;
    }

    public void setLastLogoutTime(long lastLogoutTime) {
        this.lastLogoutTime = lastLogoutTime;
    }

    public int getIntimacy() {
        return intimacy;
    }

    public void setIntimacy(int intimacy) {
        this.intimacy = intimacy;
    }

    public void refresh(Map<Byte, Object> attributes, boolean isOnline, long lastLogoutTime, boolean isSelfUpdate) {
        if (isSelfUpdate) {
            this.isOnline = isOnline;
            this.lastLogoutTime = lastLogoutTime;
        }
        this.attributes = attributes;
    }
}

