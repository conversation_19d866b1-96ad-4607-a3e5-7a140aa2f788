package cn.daxiang.hbtd.gameserver.module.runes.dao.dao;

import cn.daxiang.framework.database.MultiEntity;
import cn.daxiang.framework.database.dao.MultiEntityDaoImpl;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.hbtd.gameserver.core.GameConfig;
import cn.daxiang.hbtd.gameserver.core.database.IdGenerator;
import cn.daxiang.hbtd.gameserver.core.database.UniqueId;
import cn.daxiang.hbtd.gameserver.core.database.table.Runes;
import cn.daxiang.hbtd.gameserver.module.runes.dao.RunesDao;
import cn.daxiang.hbtd.gameserver.module.runes.model.RunesExtra;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;

/**
 * <AUTHOR>
 * @date 2022/12/21
 */
@Component
public class RunesDaoImpl extends MultiEntityDaoImpl implements RunesDao {
    @Autowired
    private IdGenerator idGenerator;

    @Override
    protected Class<? extends MultiEntity<?>> forClass() {
        return Runes.class;
    }

    @Override
    protected void initMaxId() {
        Long maxId = jdbc.queryForObject("select max(runesUid) from runes", Long.class);
        AtomicLong base = null;
        if (maxId == null || maxId <= 0) {
            base = new AtomicLong(UniqueId.otherId(GameConfig.getServerId()));
        } else {
            base = new AtomicLong(maxId);
        }
        idGenerator.init(IdentiyKey.build(this.getClass()), base);
    }

    @Override
    public Runes getRunesByConfigId(long actorId, int configId) {
        Collection<Runes> all = this.getAllRunes(actorId);
        for (Runes runes : all) {
            if (runes.getConfigId() == configId) {
                return runes;
            }
        }
        return null;
    }

    @Override
    public Collection<Runes> getAllRunes(long actorId) {
        Map<IdentiyKey, Runes> all = this.getByFk(actorId);
        return all.values();
    }

    @Override
    public Runes getRunes(long actorId, long runesUid) {
        return super.getMultiEnity(actorId, IdentiyKey.build(runesUid));
    }

    @Override
    public Runes createRunes(long actorId, int configId, int campId, long num) {
        long runesUid = idGenerator.increment(IdentiyKey.build(this.getClass()));
        Runes runes = Runes.valueOf(actorId, runesUid, configId, campId, num);
        updateQueue(runes);
        return runes;
    }

    @Override
    public Runes appraiseRunes(long actorId, int configId, int campId, RunesExtra extra) {
        long runesUid = idGenerator.increment(IdentiyKey.build(this.getClass()));
        Runes runes = Runes.valueOf(actorId, runesUid, configId, campId, 1l);
        runes.appraiseRunes(extra);
        updateQueue(runes);
        return runes;
    }

    @Override
    public long getCampRunesNum(long actorId, int camp) {
        long num = 0;
        Collection<Runes> all = this.getAllRunes(actorId);
        for (Runes runes : all) {
            if (runes.getCampId() == camp && runes.isHasAppraised()) {
                num++;
            }
        }
        return num;
    }

    @Override
    public void deleteRunes(long actorId, long runesUid) {
        Runes runes = this.getRunes(actorId, runesUid);
        this.delete(runes);
    }
}
