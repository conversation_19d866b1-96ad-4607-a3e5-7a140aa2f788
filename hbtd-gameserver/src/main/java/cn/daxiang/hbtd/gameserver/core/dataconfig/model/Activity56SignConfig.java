package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.dataconfig.annotation.FieldIgnore;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.shared.reward.RewardObject;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;

import java.util.Collection;

/**
 * 活动-56-枪出如龙-每日签到表
 *
 * <AUTHOR>
 * @date 2024/1/15
 */
@DataFile(fileName = "activity_56_sign_config")
public class Activity56SignConfig implements ModelAdapter {
    /**
     * 活动DATA
     */
    private int data;
    /**
     * 活动签到天配置
     */
    private int days;
    /**
     * 活动签到奖励
     * [[rewardType,id,num],[rewardType,id,num]]
     */
    private String reward;

    /**
     * 活动签到奖励列表
     */
    @FieldIgnore
    private Collection<RewardObject> rewardList = Lists.newArrayList();

    @Override
    public void initialize() {
        JSONArray rewardArray = JSONArray.parseArray(reward);
        for (Object reward : rewardArray) {
            JSONArray array = JSONArray.parseArray(reward.toString());
            RewardObject rewardObject = RewardObject.valueOf(array);
            rewardList.add(rewardObject);
        }
    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(data, days);
    }

    public int getData() {
        return data;
    }

    public int getDays() {
        return days;
    }

    public Collection<RewardObject> getRewardList() {
        return rewardList;
    }
}
