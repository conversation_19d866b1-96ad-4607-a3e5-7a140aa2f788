package cn.daxiang.hbtd.gameserver.module.battle.parser.verify.impl;

import cn.daxiang.framework.result.TResult;
import cn.daxiang.hbtd.gameserver.module.battle.parser.verify.AbstractBattleVerifyParser;
import cn.daxiang.hbtd.gameserver.module.suppressDemon.facade.SuppressDemonFacade;
import cn.daxiang.hbtd.gameserver.module.suppressDemon.helper.SuppressDemonPushHelper;
import cn.daxiang.protocol.game.CommonProtocol;
import cn.daxiang.protocol.game.SuppressdemonProtocol;
import cn.daxiang.shared.type.PVEVerifyParameterKey;
import cn.daxiang.shared.type.PVEVerifyType;
import com.google.protobuf.InvalidProtocolBufferException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/4/27
 */
@Component
public class SuppressDemonBattleVerifyParser extends AbstractBattleVerifyParser {
    @Autowired
    private SuppressDemonFacade suppressDemonFacade;

    @Override
    protected PVEVerifyType getType() {
        return PVEVerifyType.SUPPRESSDEMON;
    }

    @Override
    protected String getClientData(byte[] data) throws InvalidProtocolBufferException {
        SuppressdemonProtocol.SuppressDemonChallengeRequest request = SuppressdemonProtocol.SuppressDemonChallengeRequest.parseFrom(data);
        return request.getCheck();
    }

    @Override
    protected String getBattleRequest(byte[] data) throws InvalidProtocolBufferException {
        SuppressdemonProtocol.SuppressDemonChallengeRequest request = SuppressdemonProtocol.SuppressDemonChallengeRequest.parseFrom(data);
        return request.toString();
    }

    @Override
    protected void verifyResult(long actorId, Map<PVEVerifyParameterKey, Object> parameter, boolean result) throws InvalidProtocolBufferException {
        SuppressdemonProtocol.SuppressDemonChallengeRequest request =
            SuppressdemonProtocol.SuppressDemonChallengeRequest.parseFrom((byte[]) parameter.get(PVEVerifyParameterKey.BATTLE_VERIFY_REQUEST));
        TResult<CommonProtocol.RewardResult> battleResult = suppressDemonFacade.verifyResult(actorId, request, result);
        SuppressDemonPushHelper.pushSuppressDemonBattleResult(actorId, battleResult);
    }
}
