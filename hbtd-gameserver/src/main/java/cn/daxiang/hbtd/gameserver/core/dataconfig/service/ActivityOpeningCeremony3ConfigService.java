package cn.daxiang.hbtd.gameserver.core.dataconfig.service;

import cn.daxiang.framework.dataconfig.ConfigServiceAdapter;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.ActivityOpeningCeremony3Config;
import cn.daxiang.hbtd.gameserver.module.activity.type.Activity19RankType;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;
import java.util.TreeMap;

/**
 * <AUTHOR>
 * @date 2022/6/9
 */
@Component
public class ActivityOpeningCeremony3ConfigService extends ConfigServiceAdapter {

    private static Map<Integer, TreeMap<Long, ActivityOpeningCeremony3Config>> IMMORTALS_VALUE_RANK_CONFIG_MAP = Maps.newHashMap();

    public static TreeMap<Long, ActivityOpeningCeremony3Config> getRankConfigMap(int data, Activity19RankType rankType) {
        if (rankType == Activity19RankType.IMMORTALS_MANUAL_VALUE_RANK) {
            return IMMORTALS_VALUE_RANK_CONFIG_MAP.get(data);
        }
        return null;
    }

    @Override
    protected void initialize() {
        Collection<ActivityOpeningCeremony3Config> activityOpeningCeremony3Config = dataConfig.listAll(this, ActivityOpeningCeremony3Config.class);

        for (ActivityOpeningCeremony3Config config : activityOpeningCeremony3Config) {
            if (config.getType() == Activity19RankType.IMMORTALS_MANUAL_VALUE_RANK.getId()) {
                TreeMap<Long, ActivityOpeningCeremony3Config> configMap = IMMORTALS_VALUE_RANK_CONFIG_MAP.get(config.getData());
                if (configMap == null) {
                    configMap = Maps.newTreeMap();
                    IMMORTALS_VALUE_RANK_CONFIG_MAP.put(config.getData(), configMap);
                }
                configMap.put(config.getRank(), config);
            }
        }
    }

    @Override
    protected void clean() {
        IMMORTALS_VALUE_RANK_CONFIG_MAP.clear();
    }

}
