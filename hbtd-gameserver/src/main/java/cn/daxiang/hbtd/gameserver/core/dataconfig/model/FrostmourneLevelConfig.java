package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.dataconfig.annotation.FieldIgnore;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.shared.module.lineup.SpriteAttributeType;
import cn.daxiang.shared.reward.RewardObject;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/11/11
 */
@DataFile(fileName = "frostmourne_level_config")
public class FrostmourneLevelConfig implements ModelAdapter {
    /**
     * 神兵配置ID
     */
    private int id;
    /**
     * 等级
     */
    private int level;
    /**
     * 属性（取单条）
     */
    private String attributes;
    /**
     * 消耗
     */
    private String cost;
    /**
     * 下一级
     */
    private int nextLevel;
    /**
     * 基础消耗列表
     */
    @FieldIgnore
    private List<RewardObject> costList = Lists.newArrayList();
    /**
     * 属性Map
     * key:SpriteAttributeType,value:upValue
     */
    @FieldIgnore
    private Map<SpriteAttributeType, Long> attributeMap = Maps.newHashMap();

    @Override
    public void initialize() {
        JSONArray costArray = JSONArray.parseArray(cost);
        for (Object item : costArray) {
            JSONArray rewardArray = JSONArray.parseArray(item.toString());
            RewardObject rewardObject = RewardObject.valueOf(rewardArray);
            costList.add(rewardObject);
        }

        JSONArray attributesArray = JSONArray.parseArray(attributes);
        for (Object attributeItem : attributesArray) {
            JSONArray attributeArray = JSONArray.parseArray(attributeItem.toString());
            attributeMap.put(SpriteAttributeType.getType(attributeArray.getIntValue(0)), attributeArray.getLong(1));
        }
    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(id, level);
    }

    public int getId() {
        return id;
    }

    public int getLevel() {
        return level;
    }

    public int getNextLevel() {
        return nextLevel;
    }

    public List<RewardObject> getCostList() {
        return costList;
    }

    public Map<SpriteAttributeType, Long> getAttributeMap() {
        return attributeMap;
    }
}
