package cn.daxiang.hbtd.gameserver.module.chat.parser.info.impl;

import cn.daxiang.framework.event.GameEvent;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.FrostmourneConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.SystemMsgConfig;
import cn.daxiang.hbtd.gameserver.core.event.impl.FrostmourneGetEvent;
import cn.daxiang.hbtd.gameserver.module.chat.helper.ChatHelper;
import cn.daxiang.hbtd.gameserver.module.chat.model.ChatInfoReceiver;
import cn.daxiang.hbtd.gameserver.module.chat.parser.info.AbstractChatInfoParser;
import cn.daxiang.protocol.game.ChatInfoProtocol;
import cn.daxiang.protocol.game.ChatProtocol;
import cn.daxiang.protocol.game.TypeProtocol;
import cn.daxiang.shared.event.EventKey;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * @author: lester
 * @date: 2023/11/14 14:54
 * @Description:
 */
@Component
public class FrostmourneGetChatInfoParser extends AbstractChatInfoParser {
    @Override
    protected List<String> getEventName() {
        return Lists.newArrayList(EventKey.FROSTMOURNE_GET_EVENT);
    }

    @Override
    public Map<ChatInfoReceiver, ChatProtocol.ChatInfo> parse(GameEvent e) {
        FrostmourneGetEvent event = e.convert();
        SystemMsgConfig msgConfig = globalConfigService.findConfig(IdentiyKey.build(TypeProtocol.ChatInfoType.FROSTMOURNE_GET_CHAT_INFO_VALUE), SystemMsgConfig.class);
        int operationTypeId = event.operationType.getId();
        Map<Integer, Integer> frostmourneMap = Maps.newHashMap();
        for (Map.Entry<Integer, Long> entry : event.frostmourneMap.entrySet()) {
            Integer configId = entry.getKey();
            long num = entry.getValue();
            FrostmourneConfig frostmourneConfig = globalConfigService.findConfig(IdentiyKey.build(configId), FrostmourneConfig.class);
            if (frostmourneConfig == null) {
                continue;
            }
            if (msgConfig.execute(configId)) {
                frostmourneMap.put(configId, (int) num);
            }
        }
        if (frostmourneMap.isEmpty()) {
            return Collections.emptyMap();
        }

        Map<ChatInfoReceiver, ChatProtocol.ChatInfo> chatMap = Maps.newHashMap();
        ChatInfoProtocol.FrostmourneGetInfo.Builder builder = ChatInfoProtocol.FrostmourneGetInfo.newBuilder();
        builder.putAllFrostmourne(frostmourneMap);
        builder.setOperationType(operationTypeId);
        Collection<Long> actorIds = Lists.newLinkedList();
        actorIds.add(event.actorId);
        ChatProtocol.ChatInfo chatInfo = ChatHelper.buildChatInfo(TypeProtocol.ChatInfoType.FROSTMOURNE_GET_CHAT_INFO, event.getActorId(), builder.build().toByteArray(), actorIds);
        chatMap.put(ChatInfoReceiver.valueOf(TypeProtocol.ChatChannelType.CHAT_CHANNEL_TYPE_SYSTEM), chatInfo);
        return chatMap;
    }
}
