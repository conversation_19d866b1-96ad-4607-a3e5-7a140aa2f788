package cn.daxiang.hbtd.gameserver.module.chat.parser.info.impl;

import cn.daxiang.framework.event.GameEvent;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.SystemMsgConfig;
import cn.daxiang.hbtd.gameserver.core.event.impl.EquipmentEnhanceEvent;
import cn.daxiang.hbtd.gameserver.module.chat.helper.ChatHelper;
import cn.daxiang.hbtd.gameserver.module.chat.model.ChatInfoReceiver;
import cn.daxiang.hbtd.gameserver.module.chat.parser.info.AbstractChatInfoParser;
import cn.daxiang.protocol.game.ChatInfoProtocol;
import cn.daxiang.protocol.game.ChatProtocol;
import cn.daxiang.protocol.game.TypeProtocol;
import cn.daxiang.protocol.game.TypeProtocol.ChatInfoType;
import cn.daxiang.shared.event.EventKey;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * @author: Gary
 * @date: 2023/5/4 14:54
 * @Description:
 */
@Component
public class EquipmentEnhanceChatInfoParser extends AbstractChatInfoParser {
    @Override
    protected List<String> getEventName() {
        return Lists.newArrayList(EventKey.EQUIPMENT_ENHANCE_EVENT);
    }

    @Override
    public Map<ChatInfoReceiver, ChatProtocol.ChatInfo> parse(GameEvent e) {
        EquipmentEnhanceEvent event = e.convert();
        SystemMsgConfig msgConfig = globalConfigService.findConfig(IdentiyKey.build(ChatInfoType.EQUIPMENT_ENHANCE_INFO_VALUE), SystemMsgConfig.class);
        int configId = event.configId;
        int enhanceLevel = event.enhanceLevel;
        if (!msgConfig.execute(enhanceLevel)) {
            return Collections.emptyMap();
        }
        Map<ChatInfoReceiver, ChatProtocol.ChatInfo> chatMap = Maps.newHashMap();
        ChatInfoProtocol.EquipmentEnhanceChatInfo.Builder builder = ChatInfoProtocol.EquipmentEnhanceChatInfo.newBuilder();
        builder.setConfigId(configId);
        builder.setEnhanceLevel(enhanceLevel);
        Collection<Long> actorIds = Lists.newLinkedList();
        actorIds.add(event.actorId);
        ChatProtocol.ChatInfo chatInfo = ChatHelper.buildChatInfo(ChatInfoType.EQUIPMENT_ENHANCE_INFO, event.getActorId(), builder.build().toByteArray(), actorIds);
        chatMap.put(ChatInfoReceiver.valueOf(TypeProtocol.ChatChannelType.CHAT_CHANNEL_TYPE_SYSTEM), chatInfo);
        return chatMap;
    }
}
