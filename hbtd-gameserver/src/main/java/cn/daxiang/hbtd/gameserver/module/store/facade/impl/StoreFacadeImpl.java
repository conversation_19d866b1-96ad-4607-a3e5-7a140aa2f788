package cn.daxiang.hbtd.gameserver.module.store.facade.impl;

import cn.daxiang.framework.event.ActorDelayEvent;
import cn.daxiang.framework.event.GameEvent;
import cn.daxiang.framework.event.annotation.Event;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.framework.result.CollectionResult;
import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.hbtd.gameserver.core.GameBaseFacade;
import cn.daxiang.hbtd.gameserver.core.GameConfig;
import cn.daxiang.hbtd.gameserver.core.database.table.Actor;
import cn.daxiang.hbtd.gameserver.core.database.table.Store;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.StoreGoodsConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.StoreRefreshConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.StoreConfigService;
import cn.daxiang.hbtd.gameserver.core.dispatch.DispatchHelper;
import cn.daxiang.hbtd.gameserver.core.event.impl.StoreProductBuyEvent;
import cn.daxiang.hbtd.gameserver.core.event.impl.StoreRecoverRefreshTimesEvent;
import cn.daxiang.hbtd.gameserver.core.event.impl.StoreResetEvent;
import cn.daxiang.hbtd.gameserver.core.netty.channel.PlayerChannel;
import cn.daxiang.hbtd.gameserver.core.oss.GameOssLogger;
import cn.daxiang.hbtd.gameserver.core.oss.type.OperationType;
import cn.daxiang.hbtd.gameserver.module.goods.helper.RewardHelper;
import cn.daxiang.hbtd.gameserver.module.store.dao.StoreDao;
import cn.daxiang.hbtd.gameserver.module.store.facade.StoreFacade;
import cn.daxiang.hbtd.gameserver.module.store.helper.StoreGoodsPushHelper;
import cn.daxiang.hbtd.gameserver.module.store.model.StoreItemEntity;
import cn.daxiang.hbtd.gameserver.module.store.type.StoreRefreshType;
import cn.daxiang.hbtd.gameserver.module.user.helper.ActorHelper;
import cn.daxiang.hbtd.gameserver.module.user.type.ActorUnlockType;
import cn.daxiang.protocol.game.CommonProtocol;
import cn.daxiang.protocol.game.StoreProtocol.StoreType;
import cn.daxiang.shared.event.EventKey;
import cn.daxiang.shared.reward.RewardObject;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;

@Component
public class StoreFacadeImpl extends GameBaseFacade implements StoreFacade {

    @Autowired
    private StoreDao storeDao;

    @Override
    public CollectionResult<Store> getStoreMapByActorId(long actorId) {
        Collection<Store> storeList = storeDao.getStoreList(actorId);
        return CollectionResult.collection(storeList);
    }

    @Override
    public TResult<Store> getStoreItems(long actorId, StoreType storeType) {
        Store store = storeDao.getStore(actorId, storeType);
        return TResult.sucess(store);
    }

    @Override
    public TResult<CommonProtocol.RewardResult> buyProductsFromStore(long actorId, StoreType storeType, Map<Integer, Integer> productMap) {
        for (Integer num : productMap.values()) {
            if (num <= 0 || num > GameConfig.getClientCountLimit()) {
                return TResult.valueOf(INVALID_PARAM);
            }
        }
        // 获取角色配置，判断角色等级是否足够开启商店
        Result unlockResult = ActorHelper.unlock(actorId, ActorUnlockType.STORE, storeType.getNumber());
        if (unlockResult.isFail()) {
            return TResult.valueOf(unlockResult);
        }
        StoreRefreshConfig refreshConfig = StoreConfigService.getStoreRefreshConfig(storeType);
        if (refreshConfig == null) {
            LOGGER.error("StoreRefreshConfig not found, storeType:{}", storeType);
            return TResult.valueOf(CONFIG_NOT_FOUND);
        }
        Store store = getStoreItems(actorId, storeType).item;
        Collection<RewardObject> allCostList = Lists.newArrayList();
        Collection<RewardObject> totalRewardList = Lists.newArrayList();
        for (Map.Entry<Integer, Integer> entry : productMap.entrySet()) {
            int productId = entry.getKey();
            int num = entry.getValue();
            if (refreshConfig.getStoreRefreshType() != StoreRefreshType.NONE && !store.getStoreItemMap().containsKey(productId)) {
                return TResult.valueOf(STORE_BUY_TIMES_NOT_ENOUGH);
            }
            // 获取商店配置
            StoreGoodsConfig storeGoodsConfig = globalConfigService.findConfig(IdentiyKey.build(storeType.getNumber(), productId), StoreGoodsConfig.class);
            if (storeGoodsConfig == null) {
                return TResult.valueOf(CONFIG_NOT_FOUND);
            }
            if (storeGoodsConfig.getUnlockId() != 0) {
                Result productUnlockResult = ActorHelper.unlock(actorId, storeGoodsConfig.getUnlockId());
                if (productUnlockResult.isFail()) {
                    return TResult.valueOf(productUnlockResult);
                }
            }
            // 已经购买的次数
            StoreItemEntity itemEntity = store.getStoreItemEntity(productId);
            int buyCount = itemEntity.getBuyCount();
            int timesLimit = storeGoodsConfig.getTimes(ActorHelper.getVipLevel(actorId));
            // 购买限制:
            if (timesLimit != 0 && timesLimit < buyCount + num) {
                return TResult.valueOf(STORE_BUY_TIMES_NOT_ENOUGH);
            }
            // 购买num个所需要的花费
            for (int i = 1; i <= num; i++) {
                Collection<RewardObject> costList = storeGoodsConfig.getCost(buyCount + i);
                allCostList.addAll(costList);
                totalRewardList.add(storeGoodsConfig.getRewardObject());
            }
        }
        //这里特殊处理一下，免费购买的情况
        allCostList = RewardHelper.deleteRewardNumZero(allCostList);
        // 扣除花费
        Result result = RewardHelper.decrease(actorId, allCostList, OperationType.STORE_BUY);
        if (result.isFail()) {
            return TResult.valueOf(result);
        }
        Collection<StoreItemEntity> items = Lists.newArrayList();
        // 记录购买次数
        Actor actor = ActorHelper.getActor(actorId);
        for (Map.Entry<Integer, Integer> entry : productMap.entrySet()) {
            // 已经购买的次数
            StoreItemEntity itemEntity = store.getStoreItemEntity(entry.getKey());
            itemEntity.buy(entry.getValue());
            items.add(itemEntity);
            GameOssLogger.storeBuy(actor, entry.getKey(), itemEntity.getProductId(), itemEntity.getBuyCount());
        }
        // 推送商品
        StoreGoodsPushHelper.pushStoreItemList(actorId, storeType, items);
        dbQueue.updateQueue(store);
        CommonProtocol.RewardResult rewardResult = RewardHelper.sendRewardList(actorId, totalRewardList, OperationType.STORE_BUY);
        DispatchHelper.postEvent(new StoreProductBuyEvent(actorId, productMap, storeType));
        return TResult.sucess(rewardResult);
    }

    @Override
    public Result refreshStore(long actorId, StoreType storeType) {
        Result unlockResult = ActorHelper.unlock(actorId, ActorUnlockType.STORE, storeType.getNumber());
        if (unlockResult.isFail()) {
            return unlockResult;
        }
        StoreRefreshConfig refreshConfig = StoreConfigService.getStoreRefreshConfig(storeType);
        if (refreshConfig == null) {
            LOGGER.error("StoreRefreshConfig not found, storeType:{}", storeType);
            return Result.valueOf(CONFIG_NOT_FOUND);
        }
        if (refreshConfig.getStoreRefreshType() == StoreRefreshType.NONE || refreshConfig.getStoreRefreshType() == StoreRefreshType.AUTOMATIC) {
            return Result.valueOf(INVALID_PARAM);
        }
        Store store = getStoreItems(actorId, storeType).item;
        if (store.getFreeTimes() > 0) {
            //            store.freeRefresh(refreshConfig.getFreeMaxTimes(), refreshConfig.getRecoverTime(store.getRecoverTimes()));
            this.addRecoverTimeTask(store);
        } else {
            Collection<RewardObject> costList = refreshConfig.getCostList(store.getPaymentTimes() + 1);
            Result costResult = RewardHelper.decrease(actorId, costList, OperationType.STORE_REFRESH);
            if (costResult.isFail()) {
                return costResult;
            }
            store.paymentRefresh();
        }
        Result result = this.refreshStore(store, refreshConfig, true);
        return result;
    }

    @Event(name = EventKey.ACTOR_LOGIN)
    public void onActorLogin(GameEvent e) {
        for (StoreType storeType : StoreType.values()) {
            if (storeType == StoreType.STORE_TYPE_NONE || storeType == StoreType.UNRECOGNIZED) {
                continue;
            }
            this.resetStore(e.getUniqueId(), storeType, false);
        }
    }

    @Event(name = EventKey.STORE_RESET_EVENT)
    public void onStoreResetEvent(StoreResetEvent e) {
        if (PlayerChannel.isOnline(e.getUniqueId())) {
            this.resetStore(e.getActorId(), e.storeType, true);
        }
    }

    @Event(name = EventKey.STORE_RECOVER_REFRESH_TIMES_EVENT)
    public void onStoreRecoverRefreshTimesEvent(StoreRecoverRefreshTimesEvent e) {
        if (PlayerChannel.isOnline(e.getUniqueId())) {
            Store store = getStoreItems(e.getActorId(), e.storeType).item;
            StoreRefreshConfig refreshConfig = StoreConfigService.getStoreRefreshConfig(store.getStoreType());
            if (refreshConfig == null) {
                LOGGER.error("StoreRefreshConfig not found, storeType:{}", store.getStoreType());
                return;
            }
            this.recoverTimes(store, refreshConfig, true);
        }
    }

    private Result refreshStore(Store store, StoreRefreshConfig refreshConfig, boolean isPush) {
        int actorLevel = ActorHelper.getActorLevel(store.getActorId());
        Collection<Integer> productIds = StoreConfigService.getRandomProductIds(store.getStoreType(), actorLevel, refreshConfig.getRefreshItemMap());
        if (productIds.isEmpty()) {
            LOGGER.error("Store Refresh error, storeType:{},actorLevel:{},count:{}", store.getStoreType(), actorLevel, refreshConfig.getRefreshItemMap());
            return Result.valueOf(CONFIG_ERROR);
        }
        store.refresh(productIds);
        if (isPush) {
            dbQueue.updateQueue(store);
            StoreGoodsPushHelper.pushStore(store);
        }
        return Result.valueOf();
    }

    private void resetStore(long actorId, StoreType storeType, boolean isPush) {
        Store store = getStoreItems(actorId, storeType).item;
        StoreRefreshConfig refreshConfig = StoreConfigService.getStoreRefreshConfig(storeType);
        if (refreshConfig == null || refreshConfig.getStoreRefreshType() == StoreRefreshType.NONE) {
            return;
        }
        if (store.getNextResetTime() <= System.currentTimeMillis()) {
            switch (refreshConfig.getStoreRefreshType()) {
                case MANUAL:
                    // 只有初始化的时候才会刷新
                    if (store.getStoreItemMap().isEmpty()) {
                        this.refreshStore(store, refreshConfig, false);
                    }
                    break;
                case AUTOMATIC:
                    this.refreshStore(store, refreshConfig, false);
                    break;
                case RESET_MANUAL:
                    this.refreshStore(store, refreshConfig, false);
                    //                    store.resetFreeTimes(refreshConfig.getFreeMaxTimes());
                default:
                    break;
            }
            store.reset(refreshConfig.getNextRefreshTime());
            dbQueue.updateQueue(store);
            if (isPush) {
                StoreGoodsPushHelper.pushStore(store);
            }
        }
        if (!isPush) {
            this.recoverTimes(store, refreshConfig, false);
        }
        this.addResetTask(store);
    }

    private void recoverTimes(Store store, StoreRefreshConfig refreshConfig, boolean isPush) {
        //        if (store.getFreeTimes() >= refreshConfig.getFreeMaxTimes()) {
        //            return;
        //        }
        //        for (int i = store.getFreeTimes(); i < refreshConfig.getFreeMaxTimes(); i++) {
        //            if (store.getNextRecoverTime() == 0 || System.currentTimeMillis() < store.getNextRecoverTime()) {
        //                break;
        //            }
        //            long recoverTime = refreshConfig.getRecoverTime(store.getRecoverTimes() + 1);
        //            recoverTime += store.getNextRecoverTime();
        //            store.recoverTimes(refreshConfig.getFreeMaxTimes(), store.getNextRecoverTime(), recoverTime);
        //        }
        dbQueue.updateQueue(store);
        if (isPush) {
            StoreGoodsPushHelper.pushStore(store);
        }
        this.addRecoverTimeTask(store);
    }

    private void addRecoverTimeTask(Store store) {
        if (store.getNextRecoverTime() > 0) {
            ActorDelayEvent event = new StoreRecoverRefreshTimesEvent(store.getActorId(), store.getStoreType());
            schedule.addDelayTask(event, new Runnable() {
                @Override
                public void run() {
                    DispatchHelper.postEvent(event);
                }
            }, store.getNextRecoverTime());
        }
    }

    private void addResetTask(Store store) {
        StoreResetEvent event = new StoreResetEvent(store.getActorId(), store.getStoreType());
        schedule.addDelayTask(event, new Runnable() {
            @Override
            public void run() {
                DispatchHelper.postEvent(event);
            }
        }, store.getNextResetTime());
    }
}
