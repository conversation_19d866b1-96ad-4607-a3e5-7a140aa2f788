package cn.daxiang.hbtd.gameserver.module.task.parser.impl;

import cn.daxiang.framework.result.TResult;
import cn.daxiang.framework.utils.DateUtils;
import cn.daxiang.framework.utils.FormulaUtils;
import cn.daxiang.hbtd.gameserver.core.database.table.QinRace;
import cn.daxiang.hbtd.gameserver.core.database.table.Task;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.TaskConfig;
import cn.daxiang.hbtd.gameserver.core.event.impl.QinRacePassEvent;
import cn.daxiang.hbtd.gameserver.module.qinrace.facade.QinRaceFacade;
import cn.daxiang.hbtd.gameserver.module.task.parser.AbstractTaskParser;
import cn.daxiang.hbtd.gameserver.module.task.type.TaskConditionType;
import cn.daxiang.protocol.game.TaskProtocol;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/6/29
 */
@Component
public class QinRacePassTimesTaskParser extends AbstractTaskParser<QinRacePassEvent> {
    @Autowired
    private QinRaceFacade qinRaceFacade;

    @Override
    public void init(Task task, TaskConfig taskConfig) {
        TResult<QinRace> qinRaceTResult = qinRaceFacade.getQinRace(task.getActorId());
        if (qinRaceTResult.isFail()) {
            return;
        }
        QinRace qinRace = qinRaceTResult.item;
        if (task.getValue() == 0 && qinRace.getLastWeekResetTime() > DateUtils.getWeekMonday0AM()) {
            task.setValue(qinRace.getPlayTimes());
        }
        if (task.getValue() >= taskConfig.getValue()) {
            task.setStatus(TaskProtocol.TaskStatus.TASK_FINISH);
        }
    }

    @Override
    protected TaskConditionType getType() {
        return TaskConditionType.QINRACE_TIMES;
    }

    @Override
    protected boolean parseCondition(QinRacePassEvent event, Task task, TaskConfig taskConfig) {
        if (!FormulaUtils.executeBool(taskConfig.getCondition(), event.type)) {
            return false;
        }
        task.setValue(task.getValue() + 1);
        if (task.getValue() >= taskConfig.getValue()) {
            task.setStatus(TaskProtocol.TaskStatus.TASK_FINISH);
        }
        return true;
    }

}
