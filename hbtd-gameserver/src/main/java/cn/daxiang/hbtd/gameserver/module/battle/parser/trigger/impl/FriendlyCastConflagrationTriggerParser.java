package cn.daxiang.hbtd.gameserver.module.battle.parser.trigger.impl;

import cn.daxiang.hbtd.gameserver.core.dataconfig.model.SkillConfig;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleBuff;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleRoom;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleSprite;
import cn.daxiang.hbtd.gameserver.module.battle.parser.trigger.AbstractSkillTriggerParser;
import cn.daxiang.hbtd.gameserver.module.battle.type.SkillEffectType;
import cn.daxiang.hbtd.gameserver.module.battle.type.SkillTriggerType;
import org.springframework.stereotype.Component;

/**
 * 29.友方添加业火后
 *
 * @author: Gary
 * @date: 2023/11/15 17:23
 * @Description:
 */
@Component
public class FriendlyCastConflagrationTriggerParser extends AbstractSkillTriggerParser {
    @Override
    protected SkillTriggerType getType() {
        return SkillTriggerType.FRIENDLY_CAST_CONFLAGRATION;
    }

    @Override
    public boolean trigger(BattleRoom battleRoom, BattleSprite battleSprite, BattleSprite caster, BattleSprite target, long triggerValue, SkillConfig triggerSkill,
        SkillConfig skillConfig) {
        /**
         * X1	添加者是否是自身
         * X2	添加者是否是队友不包括自身
         * X3	添加者给累积给目标添加的业火层数
         * x4   添加业火的触发类型
         */
        boolean isMyself = caster.getSpriteId() == battleSprite.getSpriteId();
        boolean isMyFriend = caster.getBattleCamp() == battleSprite.getBattleCamp() && !isMyself;
        int layers =
            target.getBuffList(SkillEffectType.CONFLAGRATION_BUFF_EFFECT).stream().filter(x -> x.getCastSpriteUid() == caster.getRoleId()).mapToInt(BattleBuff::getLayers).sum();
        return skillConfig.isTrigger(isMyself, isMyFriend, layers, skillConfig.getTriggerType());
    }
}
