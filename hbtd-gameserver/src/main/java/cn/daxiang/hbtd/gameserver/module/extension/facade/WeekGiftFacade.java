package cn.daxiang.hbtd.gameserver.module.extension.facade;

import cn.daxiang.framework.result.TResult;
import cn.daxiang.hbtd.gameserver.core.database.table.WeekGift;
import cn.daxiang.protocol.game.CommonProtocol;

public interface WeekGiftFacade {
    /**
     * 获取新周礼包领取状态
     *
     * @param actorId
     * @return
     */
    TResult<WeekGift> getWeekGift(long actorId);

    /**
     * 领取奖励
     *
     * @param actorId
     * @return
     */
    TResult<CommonProtocol.RewardResult> receive(long actorId);

    /**
     * 获取间隔时间周
     *
     * @return
     */
    int getWeek();
}
