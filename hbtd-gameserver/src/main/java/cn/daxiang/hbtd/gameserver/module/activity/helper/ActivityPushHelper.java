package cn.daxiang.hbtd.gameserver.module.activity.helper;

import cn.daxiang.framework.protocal.DataPacket;
import cn.daxiang.framework.result.Result;
import cn.daxiang.hbtd.gameserver.core.netty.channel.PlayerChannel;
import cn.daxiang.protocol.game.ActivityProtocol.ActivityCmd;
import cn.daxiang.protocol.game.ActivityProtocol.ActivityConfigResponse;
import cn.daxiang.protocol.game.ActivityProtocol.ActivityInfoResponse;
import cn.daxiang.protocol.game.ModuleProtocol.Module;

public class ActivityPushHelper {
    /**
     * 推送活动信息
     *
     * @param actorId
     * @param response
     */
    public static void pushActivity(long actorId, ActivityInfoResponse response) {
        DataPacket packet = DataPacket.valueOf(Module.ACTIVITY_VALUE, ActivityCmd.PUSH_ACTIVITY_INFO_VALUE, response);
        PlayerChannel.push(actorId, packet);
    }

    /**
     * 推送所有在线用户活动信息
     *
     * @param response
     */
    public static void pushOnlineActivity(ActivityInfoResponse response) {
        DataPacket packet = DataPacket.valueOf(Module.ACTIVITY_VALUE, ActivityCmd.PUSH_ACTIVITY_INFO_VALUE, response);
        PlayerChannel.pushAllOnline(packet);
    }

    /**
     * 推送动作异常信息
     *
     * @param actorId
     * @param result
     */
    public static void pushActivityStat(long actorId, Result result) {
        DataPacket packet = DataPacket.valueOf(Module.ACTIVITY_VALUE, ActivityCmd.ACTIVITY_ACTION_VALUE, result.statusCode);
        PlayerChannel.push(actorId, packet);
    }

    /**
     * 推送活动列表
     *
     * @param actorId
     * @param response
     */
    public static void pushActivityConfig(long actorId, ActivityConfigResponse response) {
        DataPacket packet = DataPacket.valueOf(Module.ACTIVITY_VALUE, ActivityCmd.PUSH_ACTIVITY_CONFIG_VALUE, response);
        PlayerChannel.push(actorId, packet);
    }

}
