package cn.daxiang.hbtd.gameserver.module.battle.parser.trigger;

import cn.daxiang.hbtd.gameserver.module.battle.type.SkillTriggerType;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.PostConstruct;

/**
 * <AUTHOR>
 * @date 2019/11/19
 */

public abstract class AbstractSkillTriggerParser implements SkillTriggerParser {

    @Autowired
    private SkillTriggerContext context;

    @PostConstruct
    private void initialize() {
        context.register(getType(), this);
    }

    protected abstract SkillTriggerType getType();
}
