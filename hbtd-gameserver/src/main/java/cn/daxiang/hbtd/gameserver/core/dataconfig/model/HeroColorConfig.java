package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.dataconfig.annotation.FieldIgnore;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.shared.reward.RewardObject;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * 英雄幻彩配置
 *
 * <AUTHOR>
 * @date 2024/12/24
 */
@DataFile(fileName = "hero_color_config")
public class HeroColorConfig implements ModelAdapter {
    /**
     * 英雄类型
     */
    private int roleType;
    /**
     * 0:未激活 1.已激活 (客户端显示-1)
     */
    private int level;
    /**
     * 消耗([[rewardType,id,num],[rewardType,id,num]])
     * {@code RewardObject}
     */
    private String cost;
    /**
     * 升级限制([level,num])
     */
    private String costLimit;
    /**
     * 特殊属性
     */
    private int specialEffectIds;

    /**
     * 特殊属性
     */
    private int specialEffectIds2;

    /**
     * pvp技能
     */
    private int pvpSkill;
    /**
     * 消耗列表
     */
    @FieldIgnore
    private List<RewardObject> costList = Lists.newArrayList();
    /**
     * 升级限制（等级）
     */
    @FieldIgnore
    private int levelLimit;
    /**
     * 升级限时（数量）
     */
    @FieldIgnore
    private int countLimit;

    @FieldIgnore
    private List<Integer> specialEffectIdList = Lists.newArrayList();

    @Override
    public void initialize() {
        JSONArray costArray = JSONArray.parseArray(cost);
        for (Object costItem : costArray) {
            JSONArray rewardArray = JSONArray.parseArray(costItem.toString());
            RewardObject rewardObject = RewardObject.valueOf(rewardArray);
            costList.add(rewardObject);
        }
        if (specialEffectIds > 0) {
            specialEffectIdList.add(specialEffectIds);
        }
        if (specialEffectIds2 > 0) {
            specialEffectIdList.add(specialEffectIds2);
        }
        JSONArray limitArray = JSON.parseArray(costLimit);
        if (!limitArray.isEmpty()) {
            levelLimit = limitArray.getIntValue(0);
            countLimit = limitArray.getIntValue(1);
        }
    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(roleType, level);
    }

    public int getRoleType() {
        return roleType;
    }

    public int getLevel() {
        return level;
    }

    public int getPvpSkill() {
        return pvpSkill;
    }

    public List<RewardObject> getCostList() {
        return costList;
    }

    public List<Integer> getSpecialEffectIdList() {
        return specialEffectIdList;
    }

    public int getLevelLimit() {
        return levelLimit;
    }

    public int getCountLimit() {
        return countLimit;
    }

}
