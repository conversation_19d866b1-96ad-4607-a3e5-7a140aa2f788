package cn.daxiang.hbtd.gameserver.module.battle.parser.trigger.impl;

import cn.daxiang.hbtd.gameserver.core.dataconfig.model.SkillConfig;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleRoom;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleSprite;
import cn.daxiang.hbtd.gameserver.module.battle.parser.trigger.AbstractSkillTriggerParser;
import cn.daxiang.hbtd.gameserver.module.battle.type.SkillTriggerType;
import org.springframework.stereotype.Component;

/**
 * 35.扣除记录的累积损失护盾值时触发
 *
 * @author: Gary
 * @date: 2023/11/16 12:00
 * @Description:
 */
@Component
public class ClearShieldWithstandTriggerParser extends AbstractSkillTriggerParser {
    @Override
    protected SkillTriggerType getType() {
        return SkillTriggerType.CLEAR_SHIELD_WITHSTAND;
    }

    @Override
    public boolean trigger(<PERSON>Room battleRoom, BattleSprite battleSprite, BattleSprite caster, BattleSprite target, long triggerValue, SkillConfig triggerSkill,
        SkillConfig skillConfig) {
        return skillConfig.isTrigger(triggerValue);
    }
}
