package cn.daxiang.hbtd.gameserver.module.chat.parser.info.impl;

import cn.daxiang.framework.event.GameEvent;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.framework.rpc.event.GVGBrawlingTriggerMultipleEvent;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.SystemMsgConfig;
import cn.daxiang.hbtd.gameserver.module.chat.helper.ChatHelper;
import cn.daxiang.hbtd.gameserver.module.chat.model.ChatInfoReceiver;
import cn.daxiang.hbtd.gameserver.module.chat.parser.info.AbstractChatInfoParser;
import cn.daxiang.protocol.game.ChatInfoProtocol.GVGBrawlingMultipleInfo;
import cn.daxiang.protocol.game.ChatInfoProtocol.GVGBrawlingMultipleInfo.Builder;
import cn.daxiang.protocol.game.ChatProtocol;
import cn.daxiang.protocol.game.ChatProtocol.ChatInfo;
import cn.daxiang.protocol.game.TypeProtocol;
import cn.daxiang.protocol.game.TypeProtocol.ChatInfoType;
import cn.daxiang.shared.event.EventKey;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 王城乱斗跑马灯
 *
 * @author: Gary
 * @date: 2023/11/27 16:29
 * @Description:
 */
@Component
public class GVGBrawlingTriggerMultipleChatInfoParser extends AbstractChatInfoParser {
    @Override
    protected List<String> getEventName() {
        return Lists.newArrayList(EventKey.GVG_BRAWLING_TRIGGER_MULTIPLE_EVENT);
    }

    @Override
    public Map<ChatInfoReceiver, ChatInfo> parse(GameEvent e) {
        GVGBrawlingTriggerMultipleEvent event = e.convert();
        SystemMsgConfig config = globalConfigService.findConfig(IdentiyKey.build(TypeProtocol.ChatInfoType.GVG_BRAWLING_MULTIPLE_INFO_VALUE), SystemMsgConfig.class);
        if (config == null) {
            return Collections.emptyMap();
        }
        if (!config.execute(event.multiple)) {
            return Collections.emptyMap();
        }
        Map<ChatInfoReceiver, ChatProtocol.ChatInfo> chatMap = Maps.newHashMap();
        Builder builder = GVGBrawlingMultipleInfo.newBuilder();
        builder.setActorName(event.actorName);
        builder.setMultiple(event.multiple);
        ChatProtocol.ChatInfo chatInfo = ChatHelper.buildChatInfo(ChatInfoType.GVG_BRAWLING_MULTIPLE_INFO, 0, builder.build().toByteArray(), Lists.newLinkedList());
        chatMap.put(ChatInfoReceiver.valueOf(TypeProtocol.ChatChannelType.CHAT_CHANNEL_TYPE_SYSTEM), chatInfo);
        return chatMap;
    }
}
