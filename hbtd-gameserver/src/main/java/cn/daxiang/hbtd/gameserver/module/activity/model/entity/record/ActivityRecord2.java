package cn.daxiang.hbtd.gameserver.module.activity.model.entity.record;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;

import java.util.Map;
import java.util.Set;

/**
 * 推荐-推荐礼包
 * HBTD\doc\策划文档\已评审文档\beta5.5\活动-群雄逐鹿-keen.xlsx
 */
public class ActivityRecord2 {

    /**
     * 达成目标值 key:type,value:达成目标值
     */
    private Map<Integer, Long> targetValueMap = Maps.newHashMap();

    /**
     * 个人奖励领取记录
     */
    private Set<Integer> receives = Sets.newHashSet();

    public Map<Integer, Long> getTargetValueMap() {
        return targetValueMap;
    }

    public void setTargetValueMap(Map<Integer, Long> targetValueMap) {
        this.targetValueMap = targetValueMap;
    }

    public Set<Integer> getReceives() {
        return receives;
    }

    public void setReceives(Set<Integer> receives) {
        this.receives = receives;
    }

    public void addReceives(int id) {
        this.receives.add(id);
    }

}