package cn.daxiang.hbtd.gameserver.module.dungeon.facade.impl;

import cn.daxiang.framework.event.GameEvent;
import cn.daxiang.framework.event.TriggerTime;
import cn.daxiang.framework.event.annotation.Event;
import cn.daxiang.framework.event.annotation.EventOnline;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.framework.utils.DateUtils;
import cn.daxiang.hbtd.gameserver.core.GameBaseFacade;
import cn.daxiang.hbtd.gameserver.core.database.table.Dungeon;
import cn.daxiang.hbtd.gameserver.core.dataconfig.convert.impl.IntMapConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.DungeonConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.PVEBattleConfigService;
import cn.daxiang.hbtd.gameserver.core.dispatch.DispatchHelper;
import cn.daxiang.hbtd.gameserver.core.event.impl.DungeonChallengeEvent;
import cn.daxiang.hbtd.gameserver.core.oss.GameOssLogger;
import cn.daxiang.hbtd.gameserver.core.oss.type.OperationType;
import cn.daxiang.hbtd.gameserver.module.battle.parser.verify.BattleVerifyContext;
import cn.daxiang.hbtd.gameserver.module.battle.parser.verify.BattleVerifyParser;
import cn.daxiang.hbtd.gameserver.module.dungeon.dao.DungeonDao;
import cn.daxiang.hbtd.gameserver.module.dungeon.facade.DungeonFacade;
import cn.daxiang.hbtd.gameserver.module.dungeon.helper.DungeonHelper;
import cn.daxiang.hbtd.gameserver.module.dungeon.helper.DungeonPushHelper;
import cn.daxiang.hbtd.gameserver.module.dungeon.model.entity.DungeonEntity;
import cn.daxiang.hbtd.gameserver.module.goods.helper.RewardHelper;
import cn.daxiang.hbtd.gameserver.module.lineup.helper.LineupHelper;
import cn.daxiang.hbtd.gameserver.module.user.helper.ActorHelper;
import cn.daxiang.hbtd.gameserver.module.user.type.ActorUnlockType;
import cn.daxiang.protocol.game.CommonProtocol;
import cn.daxiang.protocol.game.DungeonProtocol;
import cn.daxiang.protocol.game.DungeonProtocol.DungeonType;
import cn.daxiang.shared.GlobalConfigKey;
import cn.daxiang.shared.event.EventKey;
import cn.daxiang.shared.reward.RewardObject;
import cn.daxiang.shared.type.PVEVerifyParameterKey;
import cn.daxiang.shared.type.PVEVerifyType;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Component
public class DungeonFacadeImpl extends GameBaseFacade implements DungeonFacade {

    @Autowired
    private DungeonDao dungeonDao;
    @Autowired
    private BattleVerifyContext battleCheckContext;

    @Override
    public TResult<Dungeon> getDungeon(long actorId, DungeonType type) {
        if (type == DungeonType.DUNGEON_TYPE_NONE || type == DungeonType.UNRECOGNIZED) {
            return TResult.valueOf(DUNGEON_TYPE_ERROR);
        }
        Dungeon dungeon = dungeonDao.getDungeon(actorId, type);
        return TResult.sucess(dungeon);

    }

    private Result checkChallengeTimes(DungeonType type, int playTimes) {
        Map<Integer, Integer> playTimesMap = globalConfigService.findGlobalObject(GlobalConfigKey.DAILY_DUNGEON_TIMES, IntMapConfig.class).getMap();
        if (playTimes >= playTimesMap.get(type.getNumber())) {
            return Result.valueOf(DUNGEON_CHALLENGE_TIMES_NOT_ENOUGH);
        }
        return Result.valueOf();
    }

    @Override
    public Result prepare(long actorId, int type, int dungeonId) {
        // 获取角色配置，判断角色等级是否足够开启副本
        Result unlockResult = ActorHelper.unlock(actorId, ActorUnlockType.DUNGEON_VALUE, type);
        if (unlockResult.isFail()) {
            return unlockResult;
        }
        TResult<Dungeon> dungeonResult = this.getDungeon(actorId, DungeonType.forNumber(type));
        if (dungeonResult.isFail()) {
            return Result.valueOf(dungeonResult.statusCode);
        }
        Dungeon dungeon = dungeonResult.item;
        DungeonConfig config = globalConfigService.findConfig(IdentiyKey.build(type, dungeonId), DungeonConfig.class);
        if (config == null) {
            LOGGER.error("DungeonConfig not found, type:{}, id:{}", type, dungeonId);
            return Result.valueOf(CONFIG_NOT_FOUND);
        }
        Result limitResult = ActorHelper.unlock(actorId, config.getUnlockId());
        if (limitResult.isFail()) {
            return limitResult;
        }
        if (!config.getOnWeekList().contains(DateUtils.getChineseDayOfWeek())) {
            return Result.valueOf(CONFIG_NOT_FOUND);
        }
        if (config.getPreId() != 0 && dungeon.getDungeonEntity(config.getPreId()) == null) {
            return Result.valueOf(DUNGEON_CHALLENGE_ID_ERROR);
        }
        //是不是首次通关
        boolean firstChallenge = dungeon.getDungeonEntity(dungeonId) == null ? true : false;
        Result checkResult = this.checkChallengeTimes(DungeonType.forNumber(type), dungeon.getChallengeTimes());
        if (!firstChallenge && checkResult.isFail()) {
            return checkResult;
        }
        Optional<List<Integer>> waveOptional = PVEBattleConfigService.getWaveId(config.getBattleId());
        if (!waveOptional.isPresent()) {
            LOGGER.error("PveBattleConfig not found, dungeonType:{},dungeonId:{}, battleId:{}", config.getType(), config.getConfigId(), config.getBattleId());
            return Result.valueOf(STORY_CAN_NOT_PLAY);
        }
        dungeon.prepare(dungeonId);
        return Result.valueOf();
    }

    @Override
    public Result challenge(long actorId, DungeonProtocol.DungeonChallengeRequest request) {
        int type = request.getType();
        int dungeonId = request.getConfigId();
        int rewardNum = request.getRewardNum();
        DungeonConfig config = globalConfigService.findConfig(IdentiyKey.build(type, dungeonId), DungeonConfig.class);
        if (config == null) {
            LOGGER.error("DungeonConfig not found, type:{}, id:{}", type, dungeonId);
            return Result.valueOf(CONFIG_NOT_FOUND);
        }
        if (rewardNum > config.getRewardCountMax()) {
            return Result.valueOf(INVALID_PARAM);
        }
        Optional<List<Integer>> waveOptional = PVEBattleConfigService.getWaveId(config.getBattleId());
        if (!waveOptional.isPresent()) {
            LOGGER.error("PveBattleConfig not found, dungeonType:{},dungeonId:{}, battleId:{}", config.getType(), config.getConfigId(), config.getBattleId());
            return Result.valueOf(STORY_CAN_NOT_PLAY);
        }
        TResult<Dungeon> dungeonResult = this.getDungeon(actorId, DungeonType.forNumber(type));
        if (dungeonResult.isFail()) {
            return Result.valueOf(dungeonResult.statusCode);
        }
        Dungeon dungeon = dungeonResult.item;
        if (dungeon.getPrepareId() != dungeonId) {
            return Result.valueOf(DUNGEON_CHALLENGE_ID_ERROR);
        }
        //是不是首次通关
        boolean firstChallenge = dungeon.getDungeonEntity(dungeonId) == null ? true : false;
        Result checkResult = this.checkChallengeTimes(DungeonType.forNumber(type), dungeon.getChallengeTimes());
        if (!firstChallenge && checkResult.isFail()) {
            return checkResult;
        }
        BattleVerifyParser battleCheckParser = battleCheckContext.getParser(PVEVerifyType.DUNGEON);
        Map<PVEVerifyParameterKey, Object> parameter = Maps.newHashMap();
        parameter.put(PVEVerifyParameterKey.BATTLE_VERIFY_REQUEST, request.toByteArray());
        parameter.put(PVEVerifyParameterKey.WAVE_ID, waveOptional.get().get(0));
        parameter.put(PVEVerifyParameterKey.TIME, dungeon.getBattleTime());
        parameter.put(PVEVerifyParameterKey.POWER, LineupHelper.getLineupPower(actorId));
        parameter.put(PVEVerifyParameterKey.BEAST_POWER, LineupHelper.getBeastLineupPower(actorId));
        battleCheckParser.verify(actorId, parameter);
        return Result.valueOf();
    }

    @Override
    public TResult<CommonProtocol.RewardResult> sweep(long actorId, int type, int dungeonId) {
        TResult<Dungeon> dungeonResult = this.getDungeon(actorId, DungeonType.forNumber(type));
        if (dungeonResult.isFail()) {
            return TResult.valueOf(dungeonResult.statusCode);
        }
        Dungeon dungeon = dungeonResult.item;
        DungeonConfig config = globalConfigService.findConfig(IdentiyKey.build(type, dungeonId), DungeonConfig.class);
        if (config == null) {
            LOGGER.error("dungeonConfig not found, type:{}, id:{}", type, dungeonId);
            return TResult.valueOf(CONFIG_NOT_FOUND);
        }
        DungeonEntity entity = dungeon.getDungeonEntity(dungeonId);
        if (entity == null) {
            return TResult.valueOf(DUNGEON_CHALLENGE_ID_ERROR);
        }
        if (entity.getRewardNum() < config.getRewardCountMax()) {
            return TResult.valueOf(DUNGEON_CANNOT_SWEEP);
        }
        Map<Integer, Integer> playTimesMap = globalConfigService.findGlobalObject(GlobalConfigKey.DAILY_DUNGEON_TIMES, IntMapConfig.class).getMap();
        int configPlayTimes = playTimesMap.get(type);
        if (dungeon.getChallengeTimes() >= configPlayTimes) {
            return TResult.valueOf(DUNGEON_CHALLENGE_TIMES_NOT_ENOUGH);
        }
        //        boolean isDouble = false;
        //        int day = globalConfigService.findGlobalConfig(GlobalConfigKey.DUNGEON_DOUBLE_REWARD_TIME).findInt();
        //        MapJsonArrayConfig doubleRewardConfig = globalConfigService.findGlobalObject(GlobalConfigKey.DUNGEON_DOUBLE_REWARD_CONFIG, MapJsonArrayConfig.class);
        //        if (SettingsHelper.getServerStartDays() >= day) {
        //            Integer dayOfWeek = DateUtils.getDayOfWeek() - 1;
        //            if (dayOfWeek == 0) {
        //                dayOfWeek = 7;
        //            }
        //            if (doubleRewardConfig.getCache().get(dayOfWeek).contains(type.getId())) {
        //                isDouble = true;
        //            }
        //        }
        Collection<RewardObject> rewardList = config.getRewardList(config.getRewardCountMax());
        //            if (isDouble) {
        //                rewardList.addAll(rewardList);
        //            }
        DispatchHelper.postEvent(new DungeonChallengeEvent(actorId, DungeonType.forNumber(type), dungeonId, 1));
        dungeon.sweep(1);
        dbQueue.updateQueue(dungeon);
        CommonProtocol.RewardResult rewardResult = RewardHelper.sendRewardList(actorId, rewardList, OperationType.DUNGEON_SWEEP);
        DungeonPushHelper.pushDungeon(actorId, dungeon);
        GameOssLogger.dungeon(actorId, type, dungeonId, config.getRewardCountMax(), 1);
        return TResult.sucess(rewardResult);
    }

    @Override
    public TResult<CommonProtocol.RewardResult> quicklySweep(long actorId) {
        Result unlock = ActorHelper.unlock(actorId, ActorUnlockType.DUNGEON_VALUE, 0);
        if (unlock.isFail()) {
            return TResult.valueOf(unlock);
        }
        Collection<RewardObject> rewardList = Lists.newLinkedList();
        Map<Integer, Integer> playTimesMap = globalConfigService.findGlobalObject(GlobalConfigKey.DAILY_DUNGEON_TIMES, IntMapConfig.class).getMap();
        for (DungeonType dungeonType : DungeonType.values()) {
            if (dungeonType == DungeonType.DUNGEON_TYPE_NONE || dungeonType == DungeonType.UNRECOGNIZED) {
                continue;
            }
            int type = dungeonType.getNumber();
            TResult<Dungeon> dungeonResult = this.getDungeon(actorId, DungeonType.forNumber(type));
            if (dungeonResult.isFail()) {
                continue;
            }
            Dungeon dungeon = dungeonResult.item;
            //最大的满足条件的副本配置
            DungeonConfig maxConfig = null;
            for (Map.Entry<Integer, DungeonEntity> entry : dungeon.getDungeonMap().entrySet()) {
                DungeonConfig config = globalConfigService.findConfig(IdentiyKey.build(type, entry.getKey()), DungeonConfig.class);
                if (config == null) {
                    continue;
                }
                DungeonEntity entity = entry.getValue();
                //如果最大的满足条件的副本配置不为null，并且最大的满足条件的副本配置的配置id大于当前配置的配置id就continue，以此来找到最大的配置
                if (maxConfig != null && maxConfig.getConfigId() > config.getConfigId()) {
                    continue;
                }
                if (entity.getRewardNum() >= config.getRewardCountMax()) {
                    maxConfig = config;
                }
            }
            //判断是否有满足条件的maxConfig
            if (maxConfig == null) {
                continue;
            }
            int configPlayTimes = playTimesMap.get(type);
            if (dungeon.getChallengeTimes() >= configPlayTimes) {
                continue;
            }
            int times = configPlayTimes - dungeon.getChallengeTimes();
            rewardList.addAll(maxConfig.getRewardList(maxConfig.getRewardCountMax() * times));
            DispatchHelper.postEvent(new DungeonChallengeEvent(actorId, DungeonType.forNumber(type), maxConfig.getConfigId(), times));
            dungeon.sweep(times);
            dbQueue.updateQueue(dungeon);
            DungeonPushHelper.pushDungeon(actorId, dungeon);
            GameOssLogger.dungeon(actorId, type, maxConfig.getConfigId(), maxConfig.getRewardCountMax() * times, 1);
        }
        if (rewardList.isEmpty()) {
            return TResult.valueOf(DUNGEON_CHALLENGE_TIMES_NOT_ENOUGH);
        }
        CommonProtocol.RewardResult rewardResult = RewardHelper.sendRewardList(actorId, rewardList, OperationType.DUNGEON_SWEEP);
        return TResult.sucess(rewardResult);
    }

    @Override
    public TResult<CommonProtocol.RewardResult> verifyResult(long actorId, DungeonProtocol.DungeonChallengeRequest request, boolean result) {
        int type = request.getType();
        TResult<Dungeon> dungeonResult = this.getDungeon(actorId, DungeonType.forNumber(type));
        if (dungeonResult.isFail()) {
            return TResult.valueOf(dungeonResult.statusCode);
        }
        Dungeon dungeon = dungeonResult.item;
        dungeon.resetPrepare();
        if (result) {
            return TResult.valueOf(BATTLE_PVE_VERIFY_CHEAT);
        }
        int dungeonId = request.getConfigId();
        int rewardNum = request.getRewardNum();
        DungeonConfig config = globalConfigService.findConfig(IdentiyKey.build(type, dungeonId), DungeonConfig.class);
        if (config == null) {
            LOGGER.error("DungeonConfig not found, type:{}, id:{}", type, dungeonId);
            return TResult.valueOf(CONFIG_NOT_FOUND);
        }
        if (rewardNum < config.getRewardCountMin()) {
            rewardNum = (int) config.getRewardCountMin();
        }
        dungeon.challenge(dungeonId, rewardNum);
        dbQueue.updateQueue(dungeon);
        DispatchHelper.postEvent(new DungeonChallengeEvent(actorId, DungeonType.forNumber(type), dungeonId, 1));
        DungeonPushHelper.pushDungeon(actorId, dungeon);
        Collection<RewardObject> rewardList = config.getRewardList(rewardNum);
        GameOssLogger.dungeon(actorId, type, dungeonId, rewardNum, 0);
        CommonProtocol.RewardResult rewardResult = RewardHelper.sendRewardList(actorId, rewardList, OperationType.DUNGEON_BATTLE);
        return TResult.sucess(rewardResult);
    }

    /**
     * 重置副本信息
     *
     * @param actorId
     * @param isPush
     */
    private void resetDungeon(long actorId, boolean isPush) {
        Collection<DungeonProtocol.Dungeon> voList = Lists.newArrayList();
        for (DungeonType type : DungeonType.values()) {
            if (type == DungeonType.DUNGEON_TYPE_NONE || type == DungeonType.UNRECOGNIZED) {
                continue;
            }
            Dungeon dungeon = dungeonDao.getDungeon(actorId, type);
            if (!DateUtils.isToday(dungeon.getLastResetTime())) {
                dungeon.reset();
                dbQueue.updateQueue(dungeon);
                voList.add(DungeonHelper.buildDungeonPb(dungeon));
            }
        }
        if (!voList.isEmpty() && isPush) {
            DungeonPushHelper.pushDungeon(actorId, voList);
        }
    }

    @EventOnline(trigger = TriggerTime.FIXED_HOUR, triggerValue = 24)
    public void onEverySecond(GameEvent e) {
        this.resetDungeon(e.getUniqueId(), true);
    }

    @Event(name = EventKey.ACTOR_LOGIN)
    public void onActorLogin(GameEvent e) {
        this.resetDungeon(e.getUniqueId(), false);
    }
}
