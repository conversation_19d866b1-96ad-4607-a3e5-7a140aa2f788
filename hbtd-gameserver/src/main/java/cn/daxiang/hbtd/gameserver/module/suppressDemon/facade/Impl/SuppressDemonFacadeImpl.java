package cn.daxiang.hbtd.gameserver.module.suppressDemon.facade.Impl;

import cn.daxiang.framework.context.ApplicationInitCompleteEvent;
import cn.daxiang.framework.event.GameEvent;
import cn.daxiang.framework.event.TriggerTime;
import cn.daxiang.framework.event.annotation.Event;
import cn.daxiang.framework.event.annotation.EventOnline;
import cn.daxiang.framework.extend.KeyValue;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.framework.utils.DateUtils;
import cn.daxiang.framework.utils.NumberUtils;
import cn.daxiang.framework.utils.TimeConstant;
import cn.daxiang.hbtd.gameserver.core.GameBaseFacade;
import cn.daxiang.hbtd.gameserver.core.GameConfig;
import cn.daxiang.hbtd.gameserver.core.database.table.SuppressDemon;
import cn.daxiang.hbtd.gameserver.core.dataconfig.convert.impl.RewardObjectMapConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.SuppressDemonChallengeRewardsConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.SuppressDemonConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.SuppressDemonRankConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.SuppressDemonConfigService;
import cn.daxiang.hbtd.gameserver.core.dispatch.DispatchHelper;
import cn.daxiang.hbtd.gameserver.core.event.impl.MailAddEvent;
import cn.daxiang.hbtd.gameserver.core.event.impl.SuppressDemonChallengeEvent;
import cn.daxiang.hbtd.gameserver.core.event.impl.SuppressDemonKillEvent;
import cn.daxiang.hbtd.gameserver.core.oss.GameOssLogger;
import cn.daxiang.hbtd.gameserver.core.oss.type.OperationType;
import cn.daxiang.hbtd.gameserver.module.PbBuilder;
import cn.daxiang.hbtd.gameserver.module.battle.parser.verify.BattleVerifyContext;
import cn.daxiang.hbtd.gameserver.module.battle.parser.verify.BattleVerifyParser;
import cn.daxiang.hbtd.gameserver.module.goods.helper.RewardHelper;
import cn.daxiang.hbtd.gameserver.module.lineup.helper.LineupHelper;
import cn.daxiang.hbtd.gameserver.module.mail.type.MailTemplateType;
import cn.daxiang.hbtd.gameserver.module.prerogative.helper.PrerogativeHelper;
import cn.daxiang.hbtd.gameserver.module.prerogative.type.PrerogativeType;
import cn.daxiang.hbtd.gameserver.module.suppressDemon.dao.SuppressDemonDao;
import cn.daxiang.hbtd.gameserver.module.suppressDemon.facade.SuppressDemonFacade;
import cn.daxiang.hbtd.gameserver.module.suppressDemon.helper.SuppressDemonPushHelper;
import cn.daxiang.hbtd.gameserver.module.suppressDemon.model.SuppressDemonRank;
import cn.daxiang.hbtd.gameserver.module.suppressDemon.model.entity.SuppressDemonEntity;
import cn.daxiang.hbtd.gameserver.module.user.helper.ActorHelper;
import cn.daxiang.hbtd.gameserver.module.user.type.ActorUnlockType;
import cn.daxiang.protocol.game.CommonProtocol;
import cn.daxiang.protocol.game.SuppressdemonProtocol;
import cn.daxiang.shared.GlobalConfigKey;
import cn.daxiang.shared.event.EventKey;
import cn.daxiang.shared.reward.RewardObject;
import cn.daxiang.shared.type.PVEVerifyParameterKey;
import cn.daxiang.shared.type.PVEVerifyType;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
public class SuppressDemonFacadeImpl extends GameBaseFacade implements SuppressDemonFacade, ApplicationListener<ApplicationInitCompleteEvent> {
    @Autowired
    private SuppressDemonDao suppressDemonDao;
    @Autowired
    private BattleVerifyContext battleCheckContext;

    @Override
    public void onApplicationEvent(ApplicationInitCompleteEvent event) {
        schedule.addFixedTime(new Runnable() {
            @Override
            public void run() {
                sendRankReward();
            }
        }, 24);
    }

    private void sendRankReward() {
        // key:rank,value:actorId
        String date = DateUtils.SDF_SHORT_DATE.get().format(new Date());
        for (int id : SuppressDemonConfigService.getSuppressDemonIDList()) {
            for (SuppressDemonRank rank : suppressDemonDao.getRankList(id)) {
                SuppressDemonRankConfig config = SuppressDemonConfigService.getSuppressDemonRankConfig(id, rank.getRank());
                if (config == null || config.getRewardList().isEmpty()) {
                    continue;
                }
                long actorId = rank.getKey();
                String name = "";
                SuppressDemonConfig suppressDemonConfig = SuppressDemonConfigService.getSuppressDemonConfig(id);
                if (suppressDemonConfig != null) {
                    name = suppressDemonConfig.getName();
                }
                Map<String, String> params = Maps.newHashMap();
                params.put("rank", String.valueOf(rank.getRank()));
                params.put("date", date);
                params.put("bossName", name);
                params.put("name", ActorHelper.getActorName(actorId));
                MailAddEvent event = new MailAddEvent(actorId, MailTemplateType.SUPPRESS_DEMON_EVERY_DAY_RANK_REWARD, params, config.getRewardList());
                DispatchHelper.postEvent(event);
            }
        }
        suppressDemonDao.cleanRank();
        LOGGER.info("SendSuppressDemonRankReward Completed !");
    }

    @Override
    public TResult<SuppressDemon> getSuppressDemon(long actorId) {
        Result result = ActorHelper.unlock(actorId, ActorUnlockType.SUPPRESS_DEMON);
        if (result.isFail()) {
            return TResult.valueOf(result);
        }
        SuppressDemon suppressDemon = suppressDemonDao.getSuppressDemon(actorId);
        return TResult.sucess(suppressDemon);
    }

    @Override
    public TResult<SuppressdemonProtocol.SuppressDemonRankResponse> getSuppressDemonRank(long actorId, int id) {
        TResult<SuppressDemon> suppressDemonTResult = this.getSuppressDemon(actorId);
        if (suppressDemonTResult.isFail()) {
            return TResult.valueOf(suppressDemonTResult.statusCode);
        }
        SuppressDemon suppressDemon = suppressDemonTResult.item;
        Collection<SuppressDemonRank> rankList = suppressDemonDao.getRankList(id);
        SuppressDemonRank suppressDemonRank = suppressDemonDao.getSuppressDemonRank(actorId, id);
        if (suppressDemonRank == null) {
            SuppressDemonEntity entity = suppressDemon.getSuppressDemonEntity(id);
            if (entity == null) {
                suppressDemonRank = new SuppressDemonRank();
                suppressDemonRank.setKey(actorId);
            } else {
                suppressDemonRank = SuppressDemonRank.valueOf(suppressDemon.getActorId(), entity.getDamage(), entity.getTime(), entity.getAchieveTime());
            }
        }
        rankList.add(suppressDemonRank);
        Collection<SuppressdemonProtocol.SuppressDemonRankVO> rankVOList = Lists.newArrayListWithCapacity(rankList.size());
        for (SuppressDemonRank rank : rankList) {
            SuppressdemonProtocol.SuppressDemonRankVO rankVO =
                SuppressdemonProtocol.SuppressDemonRankVO.newBuilder().setActorProfile(ActorHelper.getActorProfile(rank.getKey())).setRank(rank.getRank())
                    .setDamage(rank.getDamage()).setTime(rank.getTime()).build();
            rankVOList.add(rankVO);
        }
        SuppressdemonProtocol.SuppressDemonRankResponse response = SuppressdemonProtocol.SuppressDemonRankResponse.newBuilder().setId(id).addAllRanks(rankVOList).build();
        return TResult.sucess(response);
    }

    @Override
    public Result prepare(long actorId, int id) {
        TResult<SuppressDemon> suppressDemonTResult = this.getSuppressDemon(actorId);
        if (suppressDemonTResult.isFail()) {
            return Result.valueOf(suppressDemonTResult.statusCode);
        }
        SuppressDemon suppressDemon = suppressDemonTResult.item;
        SuppressDemonConfig config = SuppressDemonConfigService.getSuppressDemonConfig(id);
        if (config == null) {
            LOGGER.error("SuppressDemonConfig not found, id:{}", id);
            return Result.valueOf(CONFIG_NOT_FOUND);
        }
        long maxPower = ActorHelper.getActorMaxPower(actorId);
        if (maxPower < config.getMaxPowerMin() || (SuppressDemonConfigService.getSuppressDemonNextId(suppressDemon.getMaxId()) != 0
            && SuppressDemonConfigService.getSuppressDemonNextId(suppressDemon.getMaxId()) < id)) {
            return Result.valueOf(SUPPRESS_DEMON_ID_ERROR);
        }
        int freeTimes = globalConfigService.findGlobalConfig(GlobalConfigKey.SUPPRESS_DEMON_TIMES).findInt();
        int challengeTimes = suppressDemon.getChallengeTimes();
        int buyTimes = suppressDemon.getBuyTimes();
        if (challengeTimes >= freeTimes + buyTimes) {
            return Result.valueOf(SUPPRESS_DEMON_CHALLENGE_TIMES_NOT_ENOUGH);
        }
        suppressDemon.prepare(id);
        return Result.valueOf();
    }

    @Override
    public Result challenge(long actorId, SuppressdemonProtocol.SuppressDemonChallengeRequest request) {
        int id = request.getId();
        long leftHP = request.getLeftHP();
        long time = request.getTime();
        if (leftHP < 0 || time < 0 || (leftHP > 0 && time > 0) || (leftHP == 0 && time == 0)) {
            return Result.valueOf(INVALID_PARAM);
        }
        TResult<SuppressDemon> suppressDemonTResult = this.getSuppressDemon(actorId);
        if (suppressDemonTResult.isFail()) {
            return Result.valueOf(suppressDemonTResult.statusCode);
        }
        SuppressDemon suppressDemon = suppressDemonTResult.item;
        if (suppressDemon.getBattleTime() * 2 + TimeConstant.ONE_SECOND_MILLISECOND < time) {
            return Result.valueOf(INVALID_PARAM);
        }
        if (suppressDemon.getPrepareId() != id) {
            return Result.valueOf(SUPPRESS_DEMON_ID_ERROR);
        }
        SuppressDemonConfig config = SuppressDemonConfigService.getSuppressDemonConfig(id);
        if (config == null) {
            LOGGER.error("SuppressDemonConfig not found, id:{}", id);
            return Result.valueOf(CONFIG_NOT_FOUND);
        }
        int freeTimes = globalConfigService.findGlobalConfig(GlobalConfigKey.SUPPRESS_DEMON_TIMES).findInt();
        int challengeTimes = suppressDemon.getChallengeTimes();
        int buyTimes = suppressDemon.getBuyTimes();
        if (challengeTimes >= freeTimes + buyTimes) {
            return Result.valueOf(SUPPRESS_DEMON_CHALLENGE_TIMES_NOT_ENOUGH);
        }
        KeyValue<Integer, Integer> challengeRewardKey = config.getChallengeRewardKey(leftHP, time);
        SuppressDemonChallengeRewardsConfig challengeRewardsConfig =
            globalConfigService.findConfig(IdentiyKey.build(challengeRewardKey.getKey(), challengeRewardKey.getValue()), SuppressDemonChallengeRewardsConfig.class);
        if (challengeRewardsConfig == null) {
            LOGGER.error("SuppressDemonChallengeRewardsConfig not found, id:{}, rank:{} leftHP:{}, time:{}", challengeRewardKey.getKey(), challengeRewardKey.getValue(), leftHP,
                time);
            return Result.valueOf(CONFIG_NOT_FOUND);
        }
        BattleVerifyParser battleCheckParser = battleCheckContext.getParser(PVEVerifyType.SUPPRESSDEMON);
        Map<PVEVerifyParameterKey, Object> parameter = Maps.newHashMap();
        parameter.put(PVEVerifyParameterKey.BATTLE_VERIFY_REQUEST, request.toByteArray());
        parameter.put(PVEVerifyParameterKey.POWER, LineupHelper.getLineupPower(actorId));
        parameter.put(PVEVerifyParameterKey.BEAST_POWER, LineupHelper.getBeastLineupPower(actorId));
        battleCheckParser.verify(actorId, parameter);
        return Result.valueOf();
    }

    @Override
    public Result buyTimes(long actorId, int times) {
        if (times <= 0 || times > GameConfig.getClientCountLimit()) {
            return Result.valueOf(INVALID_PARAM);
        }
        TResult<SuppressDemon> suppressDemonTResult = this.getSuppressDemon(actorId);
        if (suppressDemonTResult.isFail()) {
            return Result.valueOf(suppressDemonTResult.statusCode);
        }
        SuppressDemon suppressDemon = suppressDemonTResult.item;
        int timesLimit = PrerogativeHelper.getIntPrerogative(actorId, PrerogativeType.SUPPRESS_DEMON_TIMES);
        if (suppressDemon.getBuyTimes() + times > timesLimit) {
            return Result.valueOf(SUPPRESS_DEMON_BUY_TIMES_ERROR);
        }
        Collection<RewardObject> costList = Lists.newArrayList();
        RewardObjectMapConfig costConfig = globalConfigService.findGlobalObject(GlobalConfigKey.SUPPRESS_DEMON_BUY_TIMES_COST, RewardObjectMapConfig.class);
        for (int i = 1; i <= times; i++) {
            costList.add(costConfig.getRewardObject(suppressDemon.getBuyTimes() + i));
        }
        Result result = RewardHelper.decrease(actorId, costList, OperationType.SUPPRESS_DEMON_BUY_TIMES);
        if (result.isFail()) {
            return result;
        }
        suppressDemon.buyTimes(times);
        dbQueue.updateQueue(suppressDemon);
        SuppressDemonPushHelper.pushSuppressDemon(suppressDemon);
        return Result.valueOf();
    }

    @Override
    public TResult<CommonProtocol.RewardResult> verifyResult(long actorId, SuppressdemonProtocol.SuppressDemonChallengeRequest request, boolean result) {
        TResult<SuppressDemon> suppressDemonTResult = this.getSuppressDemon(actorId);
        SuppressDemon suppressDemon = suppressDemonTResult.item;
        suppressDemon.resetPrepare();
        if (result) {
            return TResult.valueOf(BATTLE_PVE_VERIFY_CHEAT);
        }
        int id = request.getId();
        long leftHP = request.getLeftHP();
        long time = request.getTime();
        SuppressDemonConfig config = SuppressDemonConfigService.getSuppressDemonConfig(id);
        KeyValue<Integer, Integer> challengeRewardKey = config.getChallengeRewardKey(leftHP, time);
        SuppressDemonChallengeRewardsConfig challengeRewardsConfig =
            globalConfigService.findConfig(IdentiyKey.build(challengeRewardKey.getKey(), challengeRewardKey.getValue()), SuppressDemonChallengeRewardsConfig.class);
        if (challengeRewardsConfig == null) {
            LOGGER.error("SuppressDemonChallengeRewardsConfig not found, id:{}, rank:{} leftHP:{}, time:{}", challengeRewardKey.getKey(), challengeRewardKey.getValue(), leftHP,
                time);
            return TResult.valueOf(CONFIG_NOT_FOUND);
        }
        long damage = Math.max(config.getBossHP() - leftHP, 0);
        if (suppressDemon.getEntityMap().containsKey(id) || suppressDemon.getMaxId() < id || (suppressDemon.getMaxId() == id
            && config.getMaxPowerMax() > ActorHelper.getActorMaxPower(actorId))) {
            SuppressDemonEntity entity = suppressDemon.challenge(id, damage, time, true);
            suppressDemonDao.achieveRank(actorId, entity);
        } else {
            suppressDemon.challenge(id, damage, time, false);
        }
        dbQueue.updateQueue(suppressDemon);
        DispatchHelper.postEvent(new SuppressDemonChallengeEvent(actorId));
        SuppressDemonPushHelper.pushSuppressDemon(suppressDemon);
        CommonProtocol.RewardResult rewardResult = RewardHelper.sendRewardList(actorId, challengeRewardsConfig.getChallengeRewardList(), OperationType.SUPPRESS_DEMON_CHALLENGE);
        // 如果击杀直接发奖励不会返回给客户端奖励结果,客户端自己查配置展示
        if (leftHP == 0) {
            RewardHelper.sendRewardList(actorId, config.getKillRewardList(), OperationType.SUPPRESS_DEMON_CHALLENGE);
            DispatchHelper.postEvent(new SuppressDemonKillEvent(actorId, id));
        }
        GameOssLogger.suppressDemon(actorId, id, NumberUtils.getValuePercent(damage, config.getBossHP()), time);
        return TResult.sucess(rewardResult);
    }

    @Override
    public TResult<CommonProtocol.RewardResultResponse> wipeOut(long actorId, int id) {
        Result result = ActorHelper.unlock(actorId, ActorUnlockType.SUPPRESS_DEMON, 2);
        if (result.isFail()) {
            return TResult.valueOf(result);
        }
        TResult<SuppressDemon> suppressDemonTResult = this.getSuppressDemon(actorId);
        if (suppressDemonTResult.isFail()) {
            return TResult.valueOf(suppressDemonTResult.statusCode);
        }
        SuppressDemon suppressDemon = suppressDemonTResult.item;
        SuppressDemonConfig config = SuppressDemonConfigService.getSuppressDemonConfig(id);
        if (config == null) {
            LOGGER.error("SuppressDemonConfig not found, id:{}", id);
            return TResult.valueOf(CONFIG_NOT_FOUND);
        }
        long maxPower = ActorHelper.getActorMaxPower(actorId);
        if (maxPower < config.getMaxPowerMin()) {
            return TResult.valueOf(SUPPRESS_DEMON_ID_ERROR);
        }
        int freeTimes = globalConfigService.findGlobalConfig(GlobalConfigKey.SUPPRESS_DEMON_TIMES).findInt();
        int challengeTimes = suppressDemon.getChallengeTimes();
        int buyTimes = suppressDemon.getBuyTimes();
        if (challengeTimes >= freeTimes + buyTimes) {
            return TResult.valueOf(SUPPRESS_DEMON_CHALLENGE_TIMES_NOT_ENOUGH);
        }
        if (id > suppressDemon.getMaxId()) {
            return TResult.valueOf(SUPPRESS_DEMON_CAN_NOT_WIPT_OUT);
        }
        SuppressDemonEntity historyEntity = suppressDemon.getHistoryEntity();
        if (id == suppressDemon.getMaxId() && maxPower < config.getMaxPowerMax()) {
            suppressDemon.challenge(id, historyEntity.getDamage(), historyEntity.getTime(), true);
            suppressDemonDao.achieveRank(actorId, historyEntity);
        } else {
            suppressDemon.challenge(id, historyEntity.getDamage(), historyEntity.getTime(), false);
        }
        dbQueue.updateQueue(suppressDemon);
        DispatchHelper.postEvent(new SuppressDemonChallengeEvent(actorId));
        SuppressDemonPushHelper.pushSuppressDemon(suppressDemon);
        KeyValue<Integer, Integer> challengeRewardKey = config.getChallengeRewardKey(0, historyEntity.getTime());
        SuppressDemonChallengeRewardsConfig challengeRewardsConfig =
            globalConfigService.findConfig(IdentiyKey.build(challengeRewardKey.getKey(), challengeRewardKey.getValue()), SuppressDemonChallengeRewardsConfig.class);
        Collection<RewardObject> rewardList = Lists.newArrayList();
        rewardList.addAll(challengeRewardsConfig.getChallengeRewardList());
        rewardList.addAll(config.getKillRewardList());
        CommonProtocol.RewardResult rewardResult = RewardHelper.sendRewardList(actorId, rewardList, OperationType.SUPPRESS_DEMON_CHALLENGE);
        DispatchHelper.postEvent(new SuppressDemonKillEvent(actorId, id));
        CommonProtocol.RewardResultResponse rewardResultResponse = PbBuilder.buildRewardResultResponse(rewardResult);
        return TResult.sucess(rewardResultResponse);
    }

    /**
     * 重置镇妖台信息
     *
     * @param actorId
     * @param isPush
     */
    private void resetSuppressDemon(long actorId, boolean isPush) {
        Result result = ActorHelper.unlock(actorId, ActorUnlockType.SUPPRESS_DEMON);
        if (result.isFail()) {
            return;
        }
        SuppressDemon suppressDemon = suppressDemonDao.getSuppressDemon(actorId);
        if (DateUtils.isToday(suppressDemon.getLastResetTime())) {
            return;
        }
        suppressDemon.reset();
        dbQueue.updateQueue(suppressDemon);
        if (isPush) {
            SuppressDemonPushHelper.pushSuppressDemon(suppressDemon);
        }
    }

    @EventOnline(trigger = TriggerTime.FIXED_HOUR, triggerValue = 24)
    public void onEverySecond(GameEvent e) {
        this.resetSuppressDemon(e.getUniqueId(), true);
    }

    @Event(name = EventKey.ACTOR_LOGIN)
    public void onActorLogin(GameEvent e) {
        this.resetSuppressDemon(e.getUniqueId(), false);
    }

}
