package cn.daxiang.hbtd.gameserver.core.database.table;

import cn.daxiang.framework.database.SingleEntity;
import cn.daxiang.framework.database.annotation.Column;
import cn.daxiang.framework.database.annotation.DBQueueType;
import cn.daxiang.framework.database.annotation.Table;
import cn.daxiang.framework.identity.IdentiyKey;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 英雄皮肤
 *
 * <AUTHOR>
 * @date 2024/11/26
 */
@Table(name = "hero_skin", type = DBQueueType.IMPORTANT)
public class HeroSkin extends SingleEntity<Long> {
    @Column(pk = true)
    private long actorId;

    @Column(alias = "skinIds")
    private Set<Integer> skinIds = Sets.newHashSet();
    /**
     * 累计购买数量
     * {id:count,id:count,id:count}
     */
    @Column(alias = "buyTimes")
    private Map<Integer, Integer> buyTimes = Maps.newHashMap();

    /**
     * 累计已领奖励数量
     * {id:count,id:count,id:count}
     */
    @Column(alias = "receives")
    private Map<Integer, Integer> receives = Maps.newHashMap();

    /**
     * 皮肤碎片
     * key:skinId value:[fragmentId]
     */
    @Column(alias = "skinFragment")
    private Map<Integer, Set<Integer>> skinFragmentMap = Maps.newHashMap();

    @Override
    public IdentiyKey findPkId() {
        return IdentiyKey.build(actorId);
    }

    @Override
    public void setPkId(IdentiyKey pk) {
        this.actorId = pk.getFirstLongId();
    }

    @Override
    public List<IdentiyKey> keyLists() {
        return Lists.newArrayList(findPkId());
    }

    public long getActorId() {
        return actorId;
    }

    public Set<Integer> getSkinIds() {
        return skinIds;
    }

    public void setSkinIds(Set<Integer> skinIds) {
        this.skinIds = skinIds;
    }

    public void add(Integer skinId) {
        this.skinIds.add(skinId);
    }

    public Map<Integer, Integer> getBuyTimes() {
        return buyTimes;
    }

    public int getBuyTimes(int configId) {
        return this.buyTimes.getOrDefault(configId, 0);
    }

    public void setBuyTimes(Map<Integer, Integer> buyTimes) {
        this.buyTimes = buyTimes;
    }

    public Map<Integer, Integer> getReceives() {
        return receives;
    }

    public void setReceives(Map<Integer, Integer> receives) {
        this.receives = receives;
    }

    public boolean isReceiveGift(int id) {
        return this.receives.getOrDefault(id, 0) >= this.buyTimes.getOrDefault(id, 0);
    }

    public void receiveGift(int configId) {
        this.receives.merge(configId, 1, Integer::sum);
    }

    public void buyReward(int id) {
        this.buyTimes.merge(id, 1, Integer::sum);
    }

    public Map<Integer, Set<Integer>> getSkinFragmentMap() {
        return skinFragmentMap;
    }

    public void setSkinFragmentMap(Map<Integer, Set<Integer>> skinFragmentMap) {
        this.skinFragmentMap = skinFragmentMap;
    }

    /**
     * 添加皮肤碎片
     *
     * @param skinId
     * @param fragmentId
     */
    public void addSkinFragment(int skinId, int fragmentId) {
        if (this.skinIds.contains(skinId)) {
            return;
        }
        skinFragmentMap.computeIfAbsent(skinId, x -> Sets.newHashSet()).add(fragmentId);
    }

    public int getSkinFragmentNum(int skinId) {
        if (!skinFragmentMap.containsKey(skinId)) {
            return 0;
        }
        return skinFragmentMap.get(skinId).size();
    }
}
