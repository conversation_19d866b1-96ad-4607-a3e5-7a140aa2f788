package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.dataconfig.annotation.FieldIgnore;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.shared.reward.RewardObject;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;

import java.util.Collection;

/**
 * 魔窟转盘配置表
 *
 * <AUTHOR>
 * @date 2022/10/28
 */
@DataFile(fileName = "cave_turntable_config")
public class CaveTurntableConfig implements ModelAdapter {
    /**
     * 角色等级
     */
    private int level;
    /**
     * 配置Id
     */
    private int id;
    /**
     * 奖励
     */
    private String reward;
    /**
     * 权重
     */
    private int weight;

    /**
     * 奖励列表
     */
    @FieldIgnore
    private Collection<RewardObject> rewardList = Lists.newArrayList();

    @Override
    public void initialize() {
        JSONArray rewardArray = JSONArray.parseArray(reward);
        RewardObject rewardObject = RewardObject.valueOf(rewardArray);
        rewardList.add(rewardObject);
    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(level, id);
    }

    public int getLevel() {
        return level;
    }

    public int getId() {
        return id;
    }

    public int getWeight() {
        return weight;
    }

    public Collection<RewardObject> getRewardList() {
        return rewardList;
    }
}
