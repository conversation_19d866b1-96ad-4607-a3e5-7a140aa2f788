package cn.daxiang.hbtd.gameserver.module.heroRoad.model;

import cn.daxiang.framework.utils.rank.AbstractRank;
import cn.daxiang.framework.utils.rank.LadderRank;

import cn.daxiang.shared.module.heroRoad.HeroRoadLineupInfo;
import com.google.common.collect.Lists;

import java.util.Collection;

/**
 * 英雄之路通关记录阵容排行
 */
public class HeroRoadRank extends LadderRank<Long> {
    /**
     * 奖励数量
     */
    private int rewardTimes;
    /**
     * 官职
     */
    private int official;
    /**
     * 写入时间
     */
    private long writeTime;
    /**
     * 布阵信息
     */
    private Collection<Collection<HeroRoadLineupInfo>> lineup = Lists.newArrayList();

    public static HeroRoadRank valueOf(long actorId, int rewardTimes, int official, long writeTime, Collection<Collection<HeroRoadLineupInfo>> lineup) {
        HeroRoadRank rank = new HeroRoadRank();
        rank.key = actorId;
        rank.rewardTimes = rewardTimes;
        rank.official = official;
        rank.writeTime = writeTime;
        rank.lineup = lineup;
        return rank;
    }

    @Override
    public boolean outstrip(AbstractRank<Long> rank) {
        HeroRoadRank powerRank = (HeroRoadRank) rank;
        if (this.rewardTimes > powerRank.getRewardTimes()) {
            return true;
        } else if (this.rewardTimes < powerRank.getRewardTimes()) {
            return false;
        } else {
            if (this.official > powerRank.getOfficial()) {
                return false;
            } else if (this.official < powerRank.getOfficial()) {
                return true;
            } else {
                if (this.writeTime > powerRank.getWriteTime()) {
                    return false;
                } else if (this.writeTime < powerRank.getWriteTime()) {
                    return true;
                } else {
                    return this.key < powerRank.getKey();
                }
            }
        }
    }

    @Override
    public void achieveRank(LadderRank<Long> rank) {
        HeroRoadRank heroRoadRank = (HeroRoadRank) rank;
        this.rewardTimes = heroRoadRank.rewardTimes;
        this.official = heroRoadRank.official;
        this.writeTime = heroRoadRank.writeTime;
        this.lineup = heroRoadRank.lineup;
    }

    @Override
    public AbstractRank<Long> copy() {
        HeroRoadRank rank = HeroRoadRank.valueOf(this.key, this.rewardTimes, this.official, this.writeTime, this.lineup);
        this.copyRank(rank);
        return rank;
    }

    public int getRewardTimes() {
        return rewardTimes;
    }

    public int getOfficial() {
        return official;
    }

    public long getWriteTime() {
        return writeTime;
    }

    public Collection<Collection<HeroRoadLineupInfo>> getLineup() {
        return lineup;
    }
}
