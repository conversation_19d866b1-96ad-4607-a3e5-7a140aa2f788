package cn.daxiang.hbtd.gameserver.module.activity.model.entity.global;

import cn.daxiang.hbtd.gameserver.module.activity.model.Global12Rank;
import com.google.common.collect.Maps;

import java.util.Collection;
import java.util.Map;

/**
 * 群雄逐鹿
 *
 * <AUTHOR>
 * @date 2022/6/8
 */
public class ActivityGlobal12 {

    /**
     * 群雄逐鹿活动数据记录
     */
    private Map<Integer, Collection<Global12Rank>> global12RankMap = Maps.newHashMap();

    public static ActivityGlobal12 valueOf(Map<Integer, Collection<Global12Rank>> global12RankMap) {
        ActivityGlobal12 global12 = new ActivityGlobal12();
        global12.global12RankMap = global12RankMap;
        return global12;
    }

    public Map<Integer, Collection<Global12Rank>> getGlobal12RankMap() {
        return global12RankMap;
    }

    public void setGlobal12RankMap(Map<Integer, Collection<Global12Rank>> global12RankMap) {
        this.global12RankMap = global12RankMap;
    }

    public void writeRecord(int rushRankType, Collection<Global12Rank> global12Rank) {
        global12RankMap.put(rushRankType, global12Rank);
    }

}
