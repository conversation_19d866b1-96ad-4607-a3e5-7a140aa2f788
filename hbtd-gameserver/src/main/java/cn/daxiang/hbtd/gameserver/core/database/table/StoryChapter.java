package cn.daxiang.hbtd.gameserver.core.database.table;

import cn.daxiang.framework.database.MultiEntity;
import cn.daxiang.framework.database.annotation.Column;
import cn.daxiang.framework.database.annotation.DBQueueType;
import cn.daxiang.framework.database.annotation.Table;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.hbtd.gameserver.module.story.model.entity.StoryEntity;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentMap;

/**
 * 关卡章节表
 *
 * <AUTHOR>
 */
@Table(name = "story_chapter", type = DBQueueType.IMPORTANT)
public class StoryChapter extends MultiEntity<Long> {
    /**
     * 角色ID
     */
    @Column(pk = true, fk = true)
    private long actorId;
    /**
     * 章节ID
     */
    @Column(pk = true)
    private int chapterId;
    /**
     * 章节类型
     */
    @Column
    private int chapterType;
    /**
     * 关卡信息
     * key:storyId,value:StoryEntity
     */
    @Column(alias = "stories")
    private ConcurrentMap<Integer, StoryEntity> storyMap = Maps.newConcurrentMap();
    /**
     * 最高关卡ID
     */
    @Column
    private int highestStoryId;
    /**
     * 通关最高关卡的时间
     */
    @Column
    private long passedHighestTime;
    /**
     * 获取总星数
     */
    @Column
    private int maxStarNum;
    /**
     * 最后一次刷新最大星数时间
     */
    @Column
    private long refreshMaxStarTime;
    /**
     * 奖励领取List:领取星数
     */
    @Column(alias = "receiveReward")
    private List<Integer> receiveRewardList = Lists.newArrayList();
    /**
     * 剩余章节挑战次数
     */
    @Column
    private int remainChallengeTimes;
    /**
     * 重置时间
     */
    @Column
    private long lastResetTime;

    public static StoryChapter valueOf(long actorId, int chapterId) {
        StoryChapter storyChapter = new StoryChapter();
        storyChapter.actorId = actorId;
        storyChapter.chapterId = chapterId;
        return storyChapter;
    }

    @Override
    public Long findFkId() {
        return actorId;
    }

    @Override
    public void setFkId(Long fk) {
        this.actorId = fk;
    }

    @Override
    public IdentiyKey findPkId() {
        return IdentiyKey.build(actorId, chapterId);
    }

    @Override
    public void setPkId(IdentiyKey pk) {
        this.actorId = pk.getIdentifys(0, Long.class);
        this.chapterId = pk.getIdentifys(1, Integer.class);
    }

    public long getActorId() {
        return actorId;
    }

    public void setActorId(long actorId) {
        this.actorId = actorId;
    }

    public int getChapterId() {
        return chapterId;
    }

    public void setChapterId(int chapterId) {
        this.chapterId = chapterId;
    }

    public int getMaxStarNum() {
        return maxStarNum;
    }

    public void setMaxStarNum(int maxStarNum) {
        this.maxStarNum = maxStarNum;
    }

    public long getRefreshMaxStarTime() {
        return refreshMaxStarTime;
    }

    public void setRefreshMaxStarTime(long refreshMaxStarTime) {
        this.refreshMaxStarTime = refreshMaxStarTime;
    }

    public StoryEntity getStoryEntity(int storyId) {
        return storyMap.get(storyId);
    }

    public void passedStory(int storyId, int starNum) {
        StoryEntity storyEntity = this.getStoryEntity(storyId);
        //        int challengeTimes = storyEntity.getRemainChallengeTimes();
        //        storyEntity.setRemainChallengeTimes(challengeTimes - 1);
        if (storyEntity.getStarNum() < starNum) {
            this.maxStarNum = this.maxStarNum - storyEntity.getStarNum() + starNum;
            this.refreshMaxStarTime = System.currentTimeMillis();
            storyEntity.setStarNum(starNum);
            storyEntity.setRefreshStarTime(this.refreshMaxStarTime);
        }
        if (this.highestStoryId < storyId) {
            this.highestStoryId = storyId;
            this.passedHighestTime = storyEntity.getRefreshStarTime();
        }
    }

    public int getChapterType() {
        return chapterType;
    }

    public void setChapterType(int chapterType) {
        this.chapterType = chapterType;
    }

    public int getHighestStoryId() {
        return highestStoryId;
    }

    public long getPassedHighestTime() {
        return passedHighestTime;
    }

    public void addStoryEntity(StoryEntity storyEntity) {
        this.storyMap.put(storyEntity.getStoryId(), storyEntity);
    }

    public Map<Integer, StoryEntity> getStoryMap() {
        return storyMap;
    }

    public List<Integer> getReceiveRewardList() {
        return receiveRewardList;
    }

    public void setReceiveRewardList(List<Integer> receiveRewardList) {
        this.receiveRewardList = receiveRewardList;
    }

    public boolean isRefreshStar(int storyId, int addStarNum) {
        StoryEntity storyEntity = this.getStoryEntity(storyId);
        if (storyEntity == null) {
            return false;
        }
        return storyEntity.getStarNum() < addStarNum;
    }

    public void receive(int starNum) {
        this.receiveRewardList.add(starNum);
    }

    public void reset(int storyId, int challengeTimes) {
        StoryEntity storyEntity = this.getStoryEntity(storyId);
        storyEntity.reset(challengeTimes);
    }

    /**
     * 重置章节挑战、购买次数
     *
     * @param challengeTimes
     */
    public void resetChapter(int challengeTimes) {
        this.remainChallengeTimes = challengeTimes;
        this.lastResetTime = System.currentTimeMillis();
    }

    public int getRemainChallengeTimes() {
        return remainChallengeTimes;
    }

    public void decreaseRemainChallengeTimes(int times) {
        this.remainChallengeTimes = this.remainChallengeTimes - times;
        if (this.remainChallengeTimes < 0) {
            this.remainChallengeTimes = 0;
        }
    }

    public long getLastResetTime() {
        return lastResetTime;
    }

}
