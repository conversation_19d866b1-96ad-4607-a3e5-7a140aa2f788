package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.identity.IdentiyKey;

/**
 * 怪物表
 *
 * <AUTHOR>
 */
@DataFile(fileName = "monster_config")
public class MonsterConfig implements ModelAdapter {
    /**
     * 怪物ID
     */
    private int id;
    /**
     * 怪物类型
     */
    private int type;
    /**
     * 怪物数值
     */
    private int capabilityId;

    @Override
    public void initialize() {
    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(id);
    }

    public int getMonsterId() {
        return id;
    }

    public int getType() {
        return type;
    }

    public int getCapabilityId() {
        return capabilityId;
    }
}
