package cn.daxiang.hbtd.gameserver.module.qindungeon.parser.impl;

import cn.daxiang.framework.result.TResult;
import cn.daxiang.framework.utils.FormulaUtils;
import cn.daxiang.hbtd.gameserver.core.database.table.QinDungeon;
import cn.daxiang.hbtd.gameserver.core.database.table.QinRace;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.QinDungeonRougueEffectConfig;
import cn.daxiang.hbtd.gameserver.module.qindungeon.parser.AbstractQinDungeonEffectParser;
import cn.daxiang.protocol.game.QindungeonProtocol;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/8/3
 */
@Component
public class SuperStartEffectParser extends AbstractQinDungeonEffectParser {
    @Override
    protected QindungeonProtocol.QinDungeonEffectType getType() {
        return QindungeonProtocol.QinDungeonEffectType.QINDUNGEON_SUPER_START;
    }

    @Override
    public TResult<String> parser(QinDungeon qinDungeon, QinDungeonRougueEffectConfig config) {
        int result = FormulaUtils.executeInt(config.getCondition(), qinDungeon.getFloor());
        qinDungeon.setCurrency(qinDungeon.getCurrency() + result);
        //        LOGGER.error("SuperStartEffectParser EXPR:[{}], result:{},returnString:{},floor:{}", config.getCondition(), result, returnString(result), qinDungeon.getFloor());
        return TResult.sucess(returnString(result));
    }

    @Override
    public TResult<String> parser(QinRace qinRace, QinDungeonRougueEffectConfig config) {
        int result = FormulaUtils.executeInt(config.getCondition(), qinRace.getFloor());
        qinRace.setCurrency(qinRace.getCurrency() + result);
        return TResult.sucess(returnString(result));
    }
}
