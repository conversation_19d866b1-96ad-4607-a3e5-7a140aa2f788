package cn.daxiang.hbtd.gameserver.core.event.impl;

import cn.daxiang.framework.event.ActorEvent;
import cn.daxiang.shared.event.EventKey;

/**
 * 上古珍宝激活事件
 *
 * <AUTHOR>
 * @date 2022/7/12
 */
public class AntiqueActivationEvent extends ActorEvent {
    /**
     * 上古珍宝配置ID
     */
    public int configId;

    public AntiqueActivationEvent(long actorId, int configId) {
        super(EventKey.ANTIQUE_ACTIVATION_EVENT, actorId);
        this.configId = configId;
    }

}
