package cn.daxiang.hbtd.gameserver.core.database.table;

import cn.daxiang.framework.database.SingleEntity;
import cn.daxiang.framework.database.annotation.Column;
import cn.daxiang.framework.database.annotation.DBQueueType;
import cn.daxiang.framework.database.annotation.Table;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.TitleConfig;
import cn.daxiang.hbtd.gameserver.module.title.model.TitleEntity;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.Collection;
import java.util.Comparator;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * 称号
 *
 * <AUTHOR>
 * @date 2020/2/5
 */
@Table(name = "title", type = DBQueueType.IMPORTANT)
public class Title extends SingleEntity<Long> {
    /**
     * 角色ID
     */
    @Column(pk = true)
    private long actorId;
    /**
     * 称号ID
     */
    @Column
    private int titleId;
    /**
     * 默认称号
     */
    @Column(alias = "defaultTitles")
    private Map<Integer, TitleEntity> defaultTitles = Maps.newHashMap();
    /**
     * 称号
     */
    @Column(alias = "timeLimitTitles")
    private Map<Integer, TitleEntity> timeLimitTitles = Maps.newHashMap();
    /**
     * 上一次重置时间
     */
    @Column
    private long lastResetTime;

    @Override
    public List<IdentiyKey> keyLists() {
        return Lists.newArrayList(findPkId());
    }

    @Override
    public IdentiyKey findPkId() {
        return IdentiyKey.build(actorId);
    }

    @Override
    public void setPkId(IdentiyKey pk) {
        this.actorId = pk.getFirstLongId();
    }

    public void init(Collection<TitleConfig> titleConfigList) {
        for (TitleConfig titleConfig : titleConfigList) {
            this.defaultTitles.put(titleConfig.getTitleId(), TitleEntity.init(titleConfig.getTitleId()));
        }
        this.lastResetTime = System.currentTimeMillis();
    }

    public long getActorId() {
        return actorId;
    }

    public int getTitleId() {
        return titleId;
    }

    public void setTitleId(int titleId) {
        this.titleId = titleId;
    }

    public Map<Integer, TitleEntity> getDefaultTitles() {
        return defaultTitles;
    }

    public Map<Integer, TitleEntity> getTimeLimitTitles() {
        return timeLimitTitles;
    }

    public TitleEntity getTitleEntity(int titleId) {
        TitleEntity titleEntity = defaultTitles.get(titleId);
        if (titleEntity == null) {
            titleEntity = timeLimitTitles.get(titleId);
        }
        return titleEntity;
    }

    public long getLastResetTime() {
        return lastResetTime;
    }

    public void setLastResetTime(long lastResetTime) {
        this.lastResetTime = lastResetTime;
    }

    public long getExpirationTime() {
        if (timeLimitTitles.isEmpty()) {
            return 0L;
        }
        long expirationTime = timeLimitTitles.values().stream().sorted(Comparator.comparing(TitleEntity::getExpirationTime)).findFirst().get().getExpirationTime();
        return expirationTime;
    }

    public boolean clean() {
        boolean refresh = false;
        for (Iterator<TitleEntity> iterator = timeLimitTitles.values().iterator(); iterator.hasNext(); ) {
            TitleEntity entity = iterator.next();
            if (entity.getExpirationTime() < System.currentTimeMillis()) {
                iterator.remove();
                refresh = true;
            }
        }
        return refresh;
    }

    public Collection<TitleEntity> refresh(Collection<TitleConfig> titleConfigList) {
        Collection<TitleEntity> titleList = Lists.newArrayList();
        for (TitleConfig titleConfig : titleConfigList) {
            if (!this.defaultTitles.containsKey(titleConfig.getTitleId())) {
                TitleEntity titleEntity = TitleEntity.init(titleConfig.getTitleId());
                this.defaultTitles.put(titleEntity.getTitleId(), titleEntity);
                titleList.add(titleEntity);
            }
        }
        return titleList;
    }
}
