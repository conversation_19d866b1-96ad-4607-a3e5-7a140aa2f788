package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.dataconfig.annotation.FieldIgnore;
import cn.daxiang.framework.identity.IdentiyKey;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;

import java.util.Collection;

/**
 * 巅峰竞技场养成组表
 *
 * <AUTHOR>
 * @date 2025/3/25
 */
@DataFile(fileName = "cross_cup_up_group_config")
public class CrossCupUpGroupConfig implements ModelAdapter {
    /**
     * 唯一ID
     */
    private int id;
    /**
     * 养成模块
     */
    private String groupId;
    /**
     * 权重
     */
    private int weight;

    @FieldIgnore
    public Collection<Integer> groupIdList = Lists.newArrayList();

    @Override
    public void initialize() {
        JSONArray array = JSONArray.parseArray(groupId);
        groupIdList.addAll(array.toJavaList(Integer.class));
    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(id);
    }

    public int getId() {
        return id;
    }

    public int getWeight() {
        return weight;
    }

    public Collection<Integer> getGroupIdList() {
        return groupIdList;
    }
}
