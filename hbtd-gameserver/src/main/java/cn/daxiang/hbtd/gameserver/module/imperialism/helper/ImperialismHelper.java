package cn.daxiang.hbtd.gameserver.module.imperialism.helper;

import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.framework.utils.ObjectReference;
import cn.daxiang.hbtd.gameserver.core.database.table.Imperialism;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.ImperialismCityConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.ImperialismPalaceFormationConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.MonsterGroupConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.GlobalConfigService;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.ImperialismConfigService;
import cn.daxiang.hbtd.gameserver.module.PbBuilder;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleRoom;
import cn.daxiang.hbtd.gameserver.module.user.helper.ActorHelper;
import cn.daxiang.protocol.game.BattleProtocol;
import cn.daxiang.protocol.game.BattleProtocol.BattleMember;
import cn.daxiang.protocol.game.BattleProtocol.BattleResultResponse;
import cn.daxiang.protocol.game.CommonProtocol;
import cn.daxiang.protocol.game.ImperialismProtocol.ImperialismActorResponse;
import cn.daxiang.protocol.game.ImperialismProtocol.ImperialismRankType;
import cn.daxiang.shared.module.battle.BattleParameterKey;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Collection;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/11/16
 */
@Component
public class ImperialismHelper {
    private static final ObjectReference<ImperialismHelper> ref = new ObjectReference<>();
    @Autowired
    protected GlobalConfigService globalConfigService;

    @PostConstruct
    public void init() {
        ref.set(this);
    }

    public static BattleResultResponse buildBattleResultResponse(BattleRoom battleRoom) {
        Collection<BattleMember> members = Lists.newArrayList();
        ImperialismRankType rankType = (ImperialismRankType) battleRoom.getParameterMap().get(BattleParameterKey.IMPERIALISM_TARGET_RANK_TYPE);
        CommonProtocol.ActorProfile leftActorProfile;
        CommonProtocol.ActorProfile rightActorProfile;
        Long targetId = (Long) battleRoom.getParameterMap().get(BattleParameterKey.TARGET_ACTOR_ID);
        if (targetId == null) {
            leftActorProfile = ActorHelper.getActorProfile(battleRoom.getAttacker());
            long rank = (long) battleRoom.getParameterMap().get(BattleParameterKey.IMPERIALISM_TARGET_RANK);
            int formationId;
            switch (rankType) {
                case RANK_TYPE_SERVER:
                case RANK_TYPE_ZONE:
                    Optional<ImperialismCityConfig> optional = ImperialismConfigService.getCityConfig(rankType, rank);
                    formationId = optional.get().getFormationId();
                    break;
                default:
                    int season = (int) battleRoom.getParameterMap().get(BattleParameterKey.IMPERIALISM_SEASON);
                    Optional<ImperialismPalaceFormationConfig> palaceFormationConfigOptional = ImperialismConfigService.getImperialismPalaceFormationConfig(season, rank);
                    formationId = palaceFormationConfigOptional.get().getFormationId();
                    break;
            }
            MonsterGroupConfig groupConfig = ref.get().globalConfigService.findConfig(IdentiyKey.build(formationId), MonsterGroupConfig.class);
            rightActorProfile = ActorHelper.getActorProfile(groupConfig.getAttributes());

        } else {
            if (rankType == ImperialismRankType.RANK_TYPE_SERVER) {
                leftActorProfile = ActorHelper.getActorProfile(battleRoom.getAttacker());
            } else {
                Map<Byte, Object> attributes = (Map<Byte, Object>) battleRoom.getParameterMap().get(BattleParameterKey.CROSS_BATTLE_ATTRIBUTE);
                leftActorProfile = ActorHelper.getActorProfile(attributes);
            }
            rightActorProfile = ActorHelper.getActorProfile(battleRoom.getTargeter());
        }
        BattleProtocol.BattleMember leftBattleMember = battleRoom.getBattleMember(BattleProtocol.BattleCamp.LEFT_CAMP, leftActorProfile);
        members.add(leftBattleMember);
        BattleProtocol.BattleMember rightBattleMember = battleRoom.getBattleMember(BattleProtocol.BattleCamp.RIGHT_CAMP, rightActorProfile);
        members.add(rightBattleMember);
        return PbBuilder.buildBattleResultResponse(battleRoom.getBattleType(), members, battleRoom.getRightHistoryBattleSpriteVO(), battleRoom.getBattleReportMap(), null,
            battleRoom.getBattleStats());
    }

    public static ImperialismActorResponse buildImperialismActorResponse(Imperialism imperialism) {
        ImperialismActorResponse.Builder builder = ImperialismActorResponse.newBuilder();
        builder.setStatueLevel(imperialism.getStatueLevel());
        builder.setType(ImperialismRankType.forNumber(imperialism.getType()));
        builder.setRank(imperialism.getRank());
        builder.setChallengeTimes(imperialism.getChallengeTimes());
        builder.setBuyTimes(imperialism.getBuyTimes());
        builder.setPalaceChallengeTimes(imperialism.getPalaceChallengeTimes());
        builder.setPalaceBuyTimes(imperialism.getPalaceBuyTimes());
        builder.addAllReceiveList(imperialism.getReceiveList());
        builder.setPrerogative(imperialism.isPrerogative());
        builder.setPrerogativeDisposable(imperialism.isPrerogativeDisposable());
        builder.setPrerogativeDaily(imperialism.isPrerogativeDaily());
        builder.putAllHighestRanks(imperialism.getHighestRanks());
        builder.setRecruitTimes(imperialism.getRecruitTimes());
        return builder.build();
    }
}
