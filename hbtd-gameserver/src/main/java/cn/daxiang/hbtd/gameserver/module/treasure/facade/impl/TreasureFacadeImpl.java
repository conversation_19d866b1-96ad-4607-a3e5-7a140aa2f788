package cn.daxiang.hbtd.gameserver.module.treasure.facade.impl;

import cn.daxiang.framework.extend.KeyValue;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.framework.result.CollectionResult;
import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.framework.utils.FormulaUtils;
import cn.daxiang.hbtd.gameserver.core.GameBaseFacade;
import cn.daxiang.hbtd.gameserver.core.GameConfig;
import cn.daxiang.hbtd.gameserver.core.database.table.Goods;
import cn.daxiang.hbtd.gameserver.core.database.table.Treasure;
import cn.daxiang.hbtd.gameserver.core.dataconfig.convert.impl.IntListConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.GoodsConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.TreasureAwakenConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.TreasureConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.TreasureGlyphsConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.TreasureLevelConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.TreasureRefineConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.TreasureConfigService;
import cn.daxiang.hbtd.gameserver.core.dispatch.DispatchHelper;
import cn.daxiang.hbtd.gameserver.core.event.impl.LineupPowerRefreshEvent;
import cn.daxiang.hbtd.gameserver.core.event.impl.TreasureAddExpEvent;
import cn.daxiang.hbtd.gameserver.core.event.impl.TreasureAwakenEvent;
import cn.daxiang.hbtd.gameserver.core.event.impl.TreasureGetEvent;
import cn.daxiang.hbtd.gameserver.core.event.impl.TreasureGlyphsEvent;
import cn.daxiang.hbtd.gameserver.core.event.impl.TreasureRefineEvent;
import cn.daxiang.hbtd.gameserver.core.event.impl.TreasureScoreChangeEvent;
import cn.daxiang.hbtd.gameserver.core.event.impl.TreasureSynthesizeEvent;
import cn.daxiang.hbtd.gameserver.core.oss.GameOssLogger;
import cn.daxiang.hbtd.gameserver.core.oss.type.OperationType;
import cn.daxiang.hbtd.gameserver.module.goods.facade.GoodsFacade;
import cn.daxiang.hbtd.gameserver.module.goods.helper.RewardHelper;
import cn.daxiang.hbtd.gameserver.module.goods.type.GoodsSubType;
import cn.daxiang.hbtd.gameserver.module.goods.type.GoodsType;
import cn.daxiang.hbtd.gameserver.module.lineup.facade.LineupFacade;
import cn.daxiang.hbtd.gameserver.module.lineup.helper.LineupHelper;
import cn.daxiang.hbtd.gameserver.module.treasure.dao.TreasureDao;
import cn.daxiang.hbtd.gameserver.module.treasure.facade.TreasureFacade;
import cn.daxiang.hbtd.gameserver.module.treasure.helper.TreasurePushHelper;
import cn.daxiang.hbtd.gameserver.module.user.helper.ActorHelper;
import cn.daxiang.hbtd.gameserver.module.user.type.ActorUnlockType;
import cn.daxiang.protocol.game.CommonProtocol.RewardResult;
import cn.daxiang.protocol.game.TypeProtocol.RewardType;
import cn.daxiang.shared.GlobalConfigKey;
import cn.daxiang.shared.reward.RewardObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.TreeMap;

@Component
public class TreasureFacadeImpl extends GameBaseFacade implements TreasureFacade {

    @Autowired
    private TreasureDao treasureDao;
    @Autowired
    private GoodsFacade goodsFacade;
    @Autowired
    private LineupFacade lineupFacade;

    @Override
    public TResult<Treasure> getTreasure(long actorId, long treasureId) {
        Treasure treasure = treasureDao.getTreasure(actorId, treasureId);
        if (treasure == null) {
            return TResult.valueOf(TREASURE_NOT_FOUND);
        }
        return TResult.sucess(treasure);
    }

    @Override
    public CollectionResult<Treasure> getTreasures(long actorId) {
        Collection<Treasure> treasureList = treasureDao.getTreasures(actorId);
        return CollectionResult.collection(treasureList);
    }

    @Override
    public CollectionResult<Treasure> createTreasure(long actorId, Map<Integer, Long> data, OperationType operationType) {
        int level = globalConfigService.findGlobalConfig(GlobalConfigKey.TREASURE_INIT_LEVEL).findInt();
        Collection<Treasure> treasures = Lists.newArrayList();
        for (Entry<Integer, Long> entry : data.entrySet()) {
            int id = entry.getKey();
            long count = entry.getValue();
            TreasureConfig treasureConfig = globalConfigService.findConfig(id, TreasureConfig.class);
            if (treasureConfig == null) {
                LOGGER.error("TreasureConfig not found, id:{}", id);
                continue;
            }
            for (long i = 0; i < count; i++) {
                Treasure treasure = treasureDao.createTreasure(actorId, id, level);
                treasure.setScore(countTreasureScore(treasure));
                GameOssLogger.goodsAdd(actorId, operationType, RewardType.TREASURE, treasure.getTreasureId(), treasure.getConfigId(), 1, 1);
                treasures.add(treasure);
            }
            DispatchHelper.postEvent(new TreasureGetEvent(actorId, id, treasureConfig.getQualityType().getId(), count, operationType));
        }
        TreasurePushHelper.pushTreasure(actorId, treasures);
        return CollectionResult.collection(treasures);
    }

    @Override
    public Result addExp(long actorId, long treasureId, int level) {
        Result result = ActorHelper.unlock(actorId, ActorUnlockType.TREASURE);
        if (result.isFail()) {
            return Result.valueOf(result.statusCode);
        }
        Treasure treasure = treasureDao.getTreasure(actorId, treasureId);
        if (treasure == null) {
            return Result.valueOf(TREASURE_NOT_FOUND);
        }
        int treasureMaxLevel = TreasureConfigService.getTreasureMaxLevel(treasure.getConfigId());
        // 宝物可升等级最大等级
        int maxLevel = treasureMaxLevel - treasure.getLevel();
        if (level > maxLevel) {
            level = maxLevel;
        }

        TreasureConfig treasureConfig = globalConfigService.findConfig(treasure.getConfigId(), TreasureConfig.class);
        if (treasureConfig == null) {
            return Result.valueOf(CONFIG_NOT_FOUND);
        }

        //计算升级所需消耗的物品
        TResult<Map<Long, Integer>> goodsMapResult = this.countGoodsMap(actorId, level, treasure);
        if (goodsMapResult.isFail()) {
            return Result.valueOf(goodsMapResult.statusCode);
        }
        // 检查增加经验所用物品信息
        TResult<Long> checkGoodsResult = this.checkExpGoods(actorId, goodsMapResult.item);
        if (checkGoodsResult.isFail()) {
            return Result.valueOf(checkGoodsResult.statusCode);
        }
        int oldLevel = treasure.getLevel();
        int oldExp = treasure.getExp();
        // 宝物增加经验
        KeyValue<Integer, Integer> addResult = this.addExp(treasure.getConfigId(), treasure.getLevel(), treasure.getExp(), checkGoodsResult.item);
        if (addResult.getKey() == oldLevel && addResult.getValue() == oldExp) {
            return Result.valueOf(GOODS_NOT_ENOUGH);
        }
        // 扣除物品
        goodsMapResult.item.forEach((k, v) -> goodsFacade.decreaseGoods(actorId, k, v, OperationType.TREASURE_ADD_EXP));
        treasure.levelUp(addResult.getKey(), addResult.getValue());
        treasure.setScore(countTreasureScore(treasure));
        dbQueue.updateQueue(treasure);
        TreasurePushHelper.pushTreasure(actorId, Lists.newArrayList(treasure));
        if (oldLevel < treasure.getLevel()) {
            this.refreshPower(actorId, treasureId);
        }
        DispatchHelper.postEvent(new TreasureAddExpEvent(actorId, treasureId, treasureConfig.getQualityType().getId(), oldLevel, treasure.getLevel()));
        return Result.valueOf();
    }

    private TResult<Map<Long, Integer>> countGoodsMap(long actorId, int level, Treasure treasure) {
        if (level <= 0 || level > GameConfig.getClientCountLimit()) {
            return TResult.valueOf(INVALID_PARAM);
        }
        //升级所需经验
        int needExp = -treasure.getExp();
        for (int i = 0; i < level; i++) {
            TreasureLevelConfig treasureLevelConfig = globalConfigService.findConfig(IdentiyKey.build(treasure.getConfigId(), treasure.getLevel() + i), TreasureLevelConfig.class);
            TreasureLevelConfig treasureNextLevelConfig =
                globalConfigService.findConfig(IdentiyKey.build(treasure.getConfigId(), treasureLevelConfig.getNextLevel()), TreasureLevelConfig.class);
            //找不到配置的时候说明宝物已经是满级了
            if (treasureNextLevelConfig == null) {
                break;
            }
            needExp += treasureLevelConfig.getNeedExp();
        }
        Map<Long, Integer> goodsMap = Maps.newHashMap();
        if (needExp < 0) {
            return TResult.sucess(goodsMap);
        }
        List<Integer> goodsIds = globalConfigService.findGlobalObject(GlobalConfigKey.TREASURE_EXP_ID_LIST, IntListConfig.class).getVs();
        for (Integer goodsId : goodsIds) {
            List<Goods> goodsList = goodsFacade.getGoodsList(actorId, goodsId);
            Collections.sort(goodsList);
            for (Goods goods : goodsList) {
                long goodsUid = goods.getGoodsUid();
                GoodsConfig goodsConfig = globalConfigService.findConfig(goodsId, GoodsConfig.class);
                if (goodsConfig == null) {
                    return TResult.valueOf(CONFIG_NOT_FOUND);
                }
                if (goodsConfig.getGoodsType() != GoodsType.RESOURCE || goodsConfig.getGoodsSubType() != GoodsSubType.TREASURE_UPGRADE) {
                    return TResult.valueOf(GOODS_TYPE_ERROR);
                }
                //需要物品的个数
                int needNum = (int) Math.ceil((double) needExp / goodsConfig.calcEffectValue());
                if (needNum <= goods.getNum()) {
                    goodsMap.put(goodsUid, needNum);
                    return TResult.sucess(goodsMap);
                } else {
                    needExp -= goodsConfig.calcEffectValue() * goods.getNum();
                    goodsMap.put(goodsUid, goods.getNum());
                }
            }
        }
        return TResult.sucess(goodsMap);
    }

    /**
     * 检查经验物品
     *
     * @param actorId
     * @param goodsMap
     * @return
     */
    private TResult<Long> checkExpGoods(long actorId, Map<Long, Integer> goodsMap) {
        long exp = 0;
        for (Entry<Long, Integer> entry : goodsMap.entrySet()) {
            Result result = goodsFacade.hasEnoughGoods(actorId, entry.getKey(), entry.getValue());
            if (result.isFail()) {
                return TResult.valueOf(result);
            }
            TResult<Goods> goodsResult = goodsFacade.getGoods(actorId, entry.getKey());
            GoodsConfig goodsConfig = globalConfigService.findConfig(goodsResult.item.getGoodsId(), GoodsConfig.class);
            if (goodsConfig == null) {
                return TResult.valueOf(CONFIG_NOT_FOUND);
            }
            if (goodsConfig.getGoodsType() != GoodsType.RESOURCE || goodsConfig.getGoodsSubType() != GoodsSubType.TREASURE_UPGRADE) {
                return TResult.valueOf(GOODS_TYPE_ERROR);
            }
            exp += goodsConfig.calcEffectValue() * entry.getValue();
        }
        return TResult.sucess(exp);
    }

    /**
     * 增加经验
     *
     * @param treasureId
     * @param level
     * @param exp
     * @param addExp
     * @return
     */
    public KeyValue<Integer, Integer> addExp(int treasureId, int level, int exp, long addExp) {
        int levelLimit = TreasureConfigService.getTreasureMaxLevel(treasureId);
        if (levelLimit == 0) {
            LOGGER.error("TreasureLevelConfig not found, treasureId:{}", treasureId);
        }
        KeyValue<Integer, Integer> keyValue = new KeyValue<>();
        do {
            TreasureLevelConfig levelConfig = globalConfigService.findConfig(IdentiyKey.build(treasureId, level), TreasureLevelConfig.class);
            if (levelConfig == null) {
                LOGGER.error("TreasureLevelConfig not found, treasureId:{}, level:{}", treasureId, level);
                break;
            }
            if (levelConfig.getNeedExp() <= exp + addExp) {
                if (levelConfig.getNextLevel() == 0 || level >= levelLimit) {
                    exp = (int) addExp;
                    break;
                } else {
                    addExp -= levelConfig.getNeedExp() - exp;
                    level++;
                    exp = 0;
                }
            } else {
                exp += addExp;
                addExp = 0;
            }
        } while (addExp > 0);
        keyValue.setKey(level);
        keyValue.setValue(exp);
        return keyValue;
    }

    @Override
    public Result refine(long actorId, long treasureId) {
        // 宝物开启解锁
        Result unlock = ActorHelper.unlock(actorId, ActorUnlockType.TREASURE);
        if (unlock.isFail()) {
            return unlock;
        }
        // 宝物精炼开启解锁
        unlock = ActorHelper.unlock(actorId, ActorUnlockType.TREASURE_EVOLVE);
        if (unlock.isFail()) {
            return unlock;
        }
        Treasure treasure = treasureDao.getTreasure(actorId, treasureId);
        if (treasure == null) {
            return Result.valueOf(TREASURE_NOT_FOUND);
        }
        TreasureConfig treasureConfig = globalConfigService.findConfig(treasure.getConfigId(), TreasureConfig.class);
        if (treasureConfig == null) {
            return Result.valueOf(CONFIG_NOT_FOUND);
        }
        int evolveLevel = treasure.getEvolveLevel() + 1;
        TreasureRefineConfig evolveNextConfig = globalConfigService.findConfig(IdentiyKey.build(treasure.getConfigId(), evolveLevel), TreasureRefineConfig.class);
        if (evolveNextConfig == null) {
            LOGGER.error("TreasureRefineConfig is null configId:{} evolveLevel:{}", treasure.getConfigId(), evolveLevel);
            return Result.valueOf(CONFIG_NOT_FOUND);
        }
        TreasureRefineConfig evolveConfig = globalConfigService.findConfig(IdentiyKey.build(treasure.getConfigId(), treasure.getEvolveLevel()), TreasureRefineConfig.class);
        if (evolveConfig == null) {
            LOGGER.error("TreasureRefineConfig is null configId:{} evolveLevel:{}", treasure.getConfigId(), treasure.getEvolveLevel());
            return Result.valueOf(CONFIG_NOT_FOUND);
        }
        Result result = RewardHelper.decrease(actorId, evolveConfig.getCostList(), OperationType.TREASURE_EVOLVE);
        if (result.isFail()) {
            return result;
        }
        treasure.setEvolveLevel(evolveLevel);
        treasure.setScore(countTreasureScore(treasure));
        dbQueue.updateQueue(treasure);
        this.refreshPower(actorId, treasureId);
        TreasurePushHelper.pushTreasure(actorId, Lists.newArrayList(treasure));
        DispatchHelper.postEvent(new TreasureRefineEvent(actorId, treasureId, treasureConfig.getId(), treasureConfig.getQualityType().getId(), evolveLevel));
        return Result.valueOf();
    }

    /**
     * 刷新战力
     *
     * @param actorId
     * @param treasureId
     */
    private void refreshPower(long actorId, long treasureId) {
        if (LineupHelper.treasureInLineup(actorId, Lists.newArrayList(treasureId))) {
            DispatchHelper.postEvent(new LineupPowerRefreshEvent(actorId));
            DispatchHelper.postEvent(new TreasureScoreChangeEvent(actorId));
            lineupFacade.achieveTreasureRank(actorId);
        }
    }

    @Override
    public Result glyphs(long actorId, Map<Long, Integer> glyphs) {
        Result unlock = ActorHelper.unlock(actorId, ActorUnlockType.TREASURE_GLYPHS);
        if (unlock.isFail()) {
            return unlock;
        }
        Collection<Treasure> treasureList = Lists.newArrayList();
        //总的雕纹升级次数
        int glyphsLevelTotalTimes = 0;
        for (Entry<Long, Integer> entry : glyphs.entrySet()) {
            TResult<Treasure> treasureResult = this.getTreasure(actorId, entry.getKey());
            if (treasureResult.isFail()) {
                continue;
            }
            Treasure treasure = treasureResult.item;
            TreasureConfig treasureConfig = globalConfigService.findConfig(treasure.getConfigId(), TreasureConfig.class);
            if (treasureConfig == null) {
                continue;
            }
            //升几级扣几次
            for (int i = 1; i <= entry.getValue(); i++) {
                TreasureGlyphsConfig treasureNextGlyphsConfig = globalConfigService.findConfig(
                    IdentiyKey.build(treasureConfig.getType(), treasureConfig.getQuality(), treasureConfig.getAptitude(), treasure.getGlyphsLevel() + i),
                    TreasureGlyphsConfig.class);
                if (treasureNextGlyphsConfig == null) {
                    break;
                }
                TreasureGlyphsConfig treasureGlyphsConfig = globalConfigService.findConfig(
                    IdentiyKey.build(treasureConfig.getType(), treasureConfig.getQuality(), treasureConfig.getAptitude(), treasure.getGlyphsLevel() + i - 1),
                    TreasureGlyphsConfig.class);
                Result result = RewardHelper.decrease(actorId, treasureGlyphsConfig.getCostList(), OperationType.TREASURE_GLYPHS);
                if (result.isFail()) {
                    break;
                }
                treasure.glyphsLevelUp();
                treasureList.add(treasure);
                glyphsLevelTotalTimes++;
            }
            treasure.setScore(countTreasureScore(treasure));
            dbQueue.updateQueue(treasure);

        }
        if (!treasureList.isEmpty()) {
            TreasurePushHelper.pushTreasure(actorId, treasureList);
            DispatchHelper.postEvent(new TreasureGlyphsEvent(actorId, treasureList, glyphsLevelTotalTimes));
            if (LineupHelper.treasureInLineup(actorId, glyphs.keySet())) {
                DispatchHelper.postEvent(new LineupPowerRefreshEvent(actorId));
                DispatchHelper.postEvent(new TreasureScoreChangeEvent(actorId));
                lineupFacade.achieveTreasureRank(actorId);
            }
            return Result.valueOf();
        } else {
            return Result.valueOf(TREASURE_GLYPHS_LEVEL_UP_ERROR);
        }
    }

    /**
     * 删除宝物列表
     *
     * @param actorId
     * @param treasureIds
     * @param operationType
     */
    private void deleteTreasure(long actorId, Collection<Long> treasureIds, OperationType operationType) {
        treasureDao.deleteTreasure(actorId, treasureIds, operationType);
        TreasurePushHelper.pushTreasureDelete(actorId, treasureIds);
    }

    @Override
    public TResult<RewardResult> synthesize(long actorId, int treasureId, int count) {
        if (count <= 0 || count > GameConfig.getClientCountLimit()) {
            return TResult.valueOf(INVALID_PARAM);
        }
        Result unlock = ActorHelper.unlock(actorId, ActorUnlockType.TREASURE);
        if (unlock.isFail()) {
            return TResult.valueOf(unlock);
        }
        TreasureConfig treasureConfig = globalConfigService.findConfig(treasureId, TreasureConfig.class);
        if (treasureConfig == null) {
            return TResult.valueOf(CONFIG_NOT_FOUND);
        }
        Collection<RewardObject> costList = Lists.newArrayList();
        for (int i = 0; i < count; i++) {
            costList.addAll(treasureConfig.getFragmentList());
        }
        Result result = RewardHelper.decrease(actorId, costList, OperationType.TREASURE_SYNTHESIZE);
        if (result.isFail()) {
            return TResult.valueOf(result);
        }
        RewardResult rewardResult =
            RewardHelper.sendRewardList(actorId, Lists.newArrayList(RewardObject.valueOf(RewardType.TREASURE.getNumber(), treasureId, count)), OperationType.TREASURE_SYNTHESIZE);
        DispatchHelper.postEvent(new TreasureSynthesizeEvent(actorId, treasureConfig.getQualityType().getId(), count));
        return TResult.sucess(rewardResult);
    }

    @Override
    public Result batchAddExp(long actorId, Map<Long, Map<Integer, Integer>> reinforcesMap) {
        Result res = ActorHelper.unlock(actorId, ActorUnlockType.TREASURE);
        if (res.isFail()) {
            return res;
        }
        Collection<Treasure> treasureList = Lists.newArrayList();
        for (Entry<Long, Map<Integer, Integer>> entry : reinforcesMap.entrySet()) {
            TResult<Treasure> treasureResult = this.getTreasure(actorId, entry.getKey());
            if (treasureResult.isFail()) {
                continue;
            }
            Treasure treasure = treasureResult.item;
            int oldLevel = treasure.getLevel();
            int oldExp = treasure.getExp();
            Map<Long, Integer> goodsMap = Maps.newHashMap();
            boolean goodsEnough = true;
            //根据客户端传过来的消耗物品配置Id和数量，转换为物品的唯一Id和数量
            for (Entry<Integer, Integer> goodsEntry : entry.getValue().entrySet()) {
                int clientGoodsNum = goodsEntry.getValue();
                List<Goods> goodsList = goodsFacade.getGoodsList(actorId, goodsEntry.getKey());
                for (Goods goods : goodsList) {
                    if (clientGoodsNum > goods.getNum()) {
                        goodsMap.put(goods.getGoodsUid(), goods.getNum());
                        clientGoodsNum -= goods.getNum();
                    } else {
                        goodsMap.put(goods.getGoodsUid(), clientGoodsNum);
                        clientGoodsNum = 0;
                        break;
                    }
                }
                if (clientGoodsNum > 0) {
                    goodsEnough = false;
                }
            }
            if (!goodsEnough) {
                continue;
            }
            // 检查增加经验所用物品信息
            TResult<Long> checkGoodsResult = this.checkExpGoods(actorId, goodsMap);
            if (checkGoodsResult.isFail()) {
                continue;
            }
            // 宝物增加经验
            KeyValue<Integer, Integer> addResult = this.addExp(treasure.getConfigId(), treasure.getLevel(), treasure.getExp(), checkGoodsResult.item);
            if (addResult.getKey() == oldLevel && addResult.getValue() == oldExp) {
                continue;
            }
            // 扣除物品
            goodsMap.forEach((k, v) -> goodsFacade.decreaseGoods(actorId, k, v, OperationType.TREASURE_ADD_EXP));
            treasure.levelUp(addResult.getKey(), addResult.getValue());
            dbQueue.updateQueue(treasure);
            treasureList.add(treasureResult.item);
            TreasureConfig config = globalConfigService.findConfig(treasure.getConfigId(), TreasureConfig.class);
            DispatchHelper.postEvent(new TreasureAddExpEvent(actorId, treasure.getTreasureId(), config.getQuality(), oldLevel, treasure.getLevel()));

        }
        if (!treasureList.isEmpty()) {
            TreasurePushHelper.pushTreasure(actorId, treasureList);
            if (LineupHelper.treasureInLineup(actorId, reinforcesMap.keySet())) {
                DispatchHelper.postEvent(new LineupPowerRefreshEvent(actorId));
                DispatchHelper.postEvent(new TreasureScoreChangeEvent(actorId));
                lineupFacade.achieveTreasureRank(actorId);
            }
            return Result.valueOf();
        } else {
            return Result.fail();
        }
    }

    @Override
    public Result awaken(long actorId, long treasureId) {
        Treasure treasure = treasureDao.getTreasure(actorId, treasureId);
        if (treasure == null) {
            return Result.valueOf(TREASURE_NOT_FOUND);
        }
        Result result = this.treasureAwakenProcess(actorId, treasure, treasure.getAwakenNodeLevel() + 1);
        if (result.isFail()) {
            return result;
        }
        return Result.valueOf();
    }

    @Override
    public TResult<RewardResult> treasureRebirth(long actorId, Collection<Long> treasureIds) {
        CollectionResult<RewardObject> collectionResult = treasureRebirthPreview(actorId, treasureIds);
        if (collectionResult.isFail()) {
            return TResult.valueOf(collectionResult.statusCode);
        }
        Collection<Long> deleteTreasureList = Lists.newArrayList();
        for (long treasureId : treasureIds) {
            TResult<Treasure> tResult = getTreasure(actorId, treasureId);
            if (tResult.isFail()) {
                continue;
            }
            deleteTreasureList.add(treasureId);
        }
        this.deleteTreasure(actorId, deleteTreasureList, OperationType.TREASURE_REBIRTH);
        RewardResult result = RewardHelper.sendRewardList(actorId, collectionResult.item, OperationType.TREASURE_REBIRTH);
        return TResult.sucess(result);
    }

    @Override
    public CollectionResult<RewardObject> treasureRebirthPreview(long actorId, Collection<Long> treasureIds) {
        Result res = ActorHelper.unlock(actorId, ActorUnlockType.REBIRTH, 3);
        if (res.isFail()) {
            return CollectionResult.valueOf(res.statusCode);
        }
        List<RewardObject> costList = Lists.newArrayList();
        for (long treasureId : treasureIds) {
            TResult<Treasure> result = getTreasure(actorId, treasureId);
            if (result.isFail()) {
                continue;
            }
            Treasure treasure = result.item;
            // 强化
            CollectionResult<RewardObject> collectionResult = rebirthTreasureLevel(treasure);
            if (collectionResult.isFail()) {
                return CollectionResult.valueOf(result.statusCode);
            }
            costList.addAll(collectionResult.item);

            // 精炼
            for (int i = 0; i < treasure.getEvolveLevel(); i++) {
                TreasureRefineConfig refineConfig = globalConfigService.findConfig(IdentiyKey.build(treasure.getConfigId(), i), TreasureRefineConfig.class);
                if (refineConfig == null) {
                    return CollectionResult.valueOf(CONFIG_NOT_FOUND);
                }
                costList.addAll(refineConfig.getCostList());
            }
            TreasureConfig treasureConfig = globalConfigService.findConfig(treasure.getConfigId(), TreasureConfig.class);
            // 雕纹
            for (int i = 0; i < treasure.getGlyphsLevel(); i++) {
                TreasureGlyphsConfig glyphsConfig =
                    globalConfigService.findConfig(IdentiyKey.build(treasureConfig.getType(), treasureConfig.getQuality(), treasureConfig.getAptitude(), i),
                        TreasureGlyphsConfig.class);
                if (glyphsConfig == null) {
                    return CollectionResult.valueOf(CONFIG_NOT_FOUND);
                }
                costList.addAll(glyphsConfig.getCostList());
            }

            // 宝物觉醒
            for (int i = 0; i < treasure.getAwakenNodeLevel(); i++) {
                TreasureAwakenConfig treasureAwakenConfig = globalConfigService.findConfig(IdentiyKey.build(treasure.getConfigId(), i), TreasureAwakenConfig.class);
                if (treasureAwakenConfig == null) {
                    return CollectionResult.valueOf(CONFIG_NOT_FOUND);
                }
                costList.addAll(treasureAwakenConfig.getCostList());
            }

            // 宝物重生自动转换成碎片
            costList.addAll(treasureConfig.getFragmentList());
        }
        if (costList.isEmpty()) {
            return CollectionResult.valueOf(TREASURE_CANNOT_REBIRTH);
        }
        return CollectionResult.collection(RewardHelper.groupByTypeAndId(costList));
    }

    /**
     * 重生宝物强化等级后返回材料
     *
     * @param treasure
     * @return
     */
    private CollectionResult<RewardObject> rebirthTreasureLevel(Treasure treasure) {
        long totalExp = treasure.getExp();
        int treasureInitLevel = globalConfigService.findGlobalConfig(GlobalConfigKey.TREASURE_INIT_LEVEL).findInt();
        for (int i = treasureInitLevel; i < treasure.getLevel(); i++) {
            TreasureLevelConfig treasureLevelConfig = globalConfigService.findConfig(IdentiyKey.build(treasure.getConfigId(), i), TreasureLevelConfig.class);
            if (treasureLevelConfig == null) {
                LOGGER.error("TreasureLevelConfig is not found ,level : {}", i);
                return CollectionResult.valueOf(CONFIG_NOT_FOUND);
            }
            totalExp += treasureLevelConfig.getNeedExp();
        }
        return CollectionResult.collection(getReturnRewardListByExp(totalExp));
    }

    private Collection<RewardObject> getReturnRewardListByExp(long totalExp) {
        Collection<RewardObject> rewardList = Lists.newArrayList();
        //key:exp,value:GoodsConfig (从大到小排序key)
        TreeMap<Integer, Integer> expGoodsmap = Maps.newTreeMap(Collections.reverseOrder());
        List<Integer> goodsIds = globalConfigService.findGlobalObject(GlobalConfigKey.TREASURE_EXP_ID_LIST, IntListConfig.class).getVs();
        for (Integer goodsId : goodsIds) {
            GoodsConfig goodsConfig = globalConfigService.findConfig(goodsId, GoodsConfig.class);
            expGoodsmap.put(goodsConfig.calcEffectValue(), goodsId);
        }
        for (Map.Entry<Integer, Integer> entry : expGoodsmap.entrySet()) {
            long expGoodsNum = totalExp / entry.getKey();
            if (expGoodsNum == 0L) {
                continue;
            }
            rewardList.add(RewardObject.valueOf(RewardType.GOODS.getNumber(), entry.getValue(), expGoodsNum));
            totalExp = totalExp % entry.getKey();
        }
        return rewardList;
    }

    @Override
    public TResult<Collection<RewardResult>> quickSynthesize(long actorId, Map<Integer, Integer> synthesizeMap) {
        Collection<RewardResult> rewardResultList = Lists.newArrayList();
        for (Entry<Integer, Integer> entry : synthesizeMap.entrySet()) {
            Integer treasureId = entry.getKey();
            Integer count = entry.getValue();
            if (count <= 0 || count > GameConfig.getClientCountLimit()) {
                return TResult.valueOf(INVALID_PARAM);
            }
            Result unlock = ActorHelper.unlock(actorId, ActorUnlockType.TREASURE);
            if (unlock.isFail()) {
                return TResult.valueOf(unlock);
            }
            TreasureConfig treasureConfig = globalConfigService.findConfig(treasureId, TreasureConfig.class);
            if (treasureConfig == null) {
                return TResult.valueOf(CONFIG_NOT_FOUND);
            }
            Collection<RewardObject> costList = Lists.newArrayList();
            for (int i = 0; i < count; i++) {
                costList.addAll(treasureConfig.getFragmentList());
            }
            Result result = RewardHelper.decrease(actorId, costList, OperationType.TREASURE_SYNTHESIZE);
            if (result.isFail()) {
                return TResult.valueOf(result);
            }
            RewardResult rewardResult = RewardHelper.sendRewardList(actorId, Lists.newArrayList(RewardObject.valueOf(RewardType.TREASURE.getNumber(), treasureId, count)),
                OperationType.TREASURE_SYNTHESIZE);
            rewardResultList.add(rewardResult);
            DispatchHelper.postEvent(new TreasureSynthesizeEvent(actorId, treasureConfig.getQualityType().getId(), count));
        }
        return TResult.sucess(rewardResultList);
    }

    @Override
    public Result deleteTreasure(long actorId, long treasureId, OperationType operationType) {
        Collection<Long> treasureIds = Lists.newArrayList(treasureId);
        this.deleteTreasure(actorId, treasureIds, operationType);
        return Result.valueOf();
    }

    @Override
    public long getTreasuresScore(long actorId, Collection<Long> treasureIds) {
        long totalTreasuresScore = 0;
        for (Long treasureId : treasureIds) {
            TResult<Treasure> treasureTResult = getTreasure(actorId, treasureId);
            if (treasureTResult.isFail()) {
                continue;
            }
            Treasure treasure = treasureTResult.item;
            totalTreasuresScore += treasure.getScore();
        }
        return totalTreasuresScore;
    }

    @Override
    public Collection<Integer> getTreasureAwakenResonanceSpecialIdList(long actorId, Collection<Long> treasureIds) {
        if (treasureIds.size() != 2) {
            return Collections.emptyList();
        }

        Collection<Integer> awakenLevel = Lists.newArrayList();
        for (Long treasureId : treasureIds) {
            TResult<Treasure> treasureResult = getTreasure(actorId, treasureId);
            if (treasureResult.isFail()) {
                continue;
            }
            Treasure treasure = treasureResult.item;
            TreasureConfig treasureConfig = globalConfigService.findConfig(IdentiyKey.build(treasure.getConfigId()), TreasureConfig.class);
            int qualityLimit = globalConfigService.findGlobalConfig(GlobalConfigKey.TREASURE_SUIT_CONDITION).findInt();
            // 品质不符，直接返回
            if (treasureConfig.getQuality() < qualityLimit) {
                return Collections.emptyList();
            }
            TreasureAwakenConfig treasureAwakenConfig =
                globalConfigService.findConfig(IdentiyKey.build(treasure.getConfigId(), treasure.getAwakenNodeLevel()), TreasureAwakenConfig.class);
            awakenLevel.add(treasureAwakenConfig.getAwakenLevel());
        }
        Integer minAwakenLevel = Collections.min(awakenLevel);
        Collection<Integer> treasureResonanceConfigs = TreasureConfigService.getTreasureResonanceSpecialEffectIdList(minAwakenLevel);
        return treasureResonanceConfigs;
    }

    @Override
    public Result treasureQuickAwaken(long actorId, long treasureId, int level) {
        Treasure treasure = treasureDao.getTreasure(actorId, treasureId);
        if (treasure == null) {
            return Result.valueOf(TREASURE_NOT_FOUND);
        }
        if (treasure.getAwakenNodeLevel() >= level) {
            return Result.valueOf(INVALID_PARAM);
        }
        Result result = this.treasureAwakenProcess(actorId, treasure, level);
        if (result.isFail()) {
            return result;
        }
        return Result.valueOf();
    }

    private Result treasureAwakenProcess(long actorId, Treasure treasure, int level) {
        Result unlock = ActorHelper.unlock(actorId, ActorUnlockType.TREASURE_AWAKEN);
        if (unlock.isFail()) {
            return Result.valueOf(unlock.statusCode);
        }
        long treasureId = treasure.getTreasureId();
        Collection<RewardObject> costList = Lists.newArrayList();
        for (int i = treasure.getAwakenNodeLevel(); i < level; i++) {
            TreasureAwakenConfig treasureAwakenConfig = globalConfigService.findConfig(IdentiyKey.build(treasure.getConfigId(), i), TreasureAwakenConfig.class);
            if (treasureAwakenConfig == null) {
                return Result.valueOf(CONFIG_NOT_FOUND);
            }
            if (treasureAwakenConfig.getCostList().isEmpty()) {
                return Result.valueOf(CONFIG_ERROR);
            }
            if (globalConfigService.findConfig(IdentiyKey.build(treasure.getConfigId(), i + 1), TreasureAwakenConfig.class) == null) {
                return Result.valueOf(CONFIG_ERROR);
            }
            costList.addAll(treasureAwakenConfig.getCostList());
        }
        Result result = RewardHelper.decrease(actorId, costList, OperationType.TREASURE_AWAKEN);
        if (result.isFail()) {
            return Result.valueOf(result.statusCode);
        }
        treasure.setAwakenNodeLevel(level);
        treasure.setScore(countTreasureScore(treasure));
        dbQueue.updateQueue(treasure);
        TreasurePushHelper.pushTreasure(actorId, Lists.newArrayList(treasure));
        DispatchHelper.postEvent(new TreasureAwakenEvent(actorId, treasureId, treasure.getConfigId(), level));
        this.refreshPower(actorId, treasureId);
        return Result.valueOf();
    }

    @Override
    public TResult<RewardResult> inherit(long actorId, long beInheritedTreasureId, long inheritedTreasureId) {
        TResult<Treasure> beInheritedResult = this.getTreasure(actorId, beInheritedTreasureId);
        if (beInheritedResult.isFail()) {
            return TResult.valueOf(beInheritedResult.statusCode);
        }
        TResult<Treasure> inheritResult = this.getTreasure(actorId, inheritedTreasureId);
        if (inheritResult.isFail()) {
            return TResult.valueOf(inheritResult.statusCode);
        }

        Treasure beInheritedTreasure = beInheritedResult.item;
        Treasure inheritTreasure = inheritResult.item;
        int initLevel = globalConfigService.findGlobalConfig(GlobalConfigKey.TREASURE_INIT_LEVEL).findInt();
        if (inheritTreasure.getLevel() != initLevel || inheritTreasure.getExp() > 0 || inheritTreasure.getEvolveLevel() != 0 || inheritTreasure.getGlyphsLevel() != 0) {
            return TResult.valueOf(INVALID_PARAM);
        }

        Collection<RewardObject> rewardList = Lists.newArrayList();
        //--------------------------------------------宝物强化继承-------------------------------------------------------------------------------------------------------------------------
        /**先计算出来被继承的宝物的总经验*/
        int beInheritedLevel = beInheritedTreasure.getLevel();
        long beInheritedTotalExp = beInheritedTreasure.getExp();
        for (int i = initLevel; i < beInheritedLevel; i++) {
            TreasureLevelConfig treasureLevelConfig = globalConfigService.findConfig(IdentiyKey.build(beInheritedTreasure.getConfigId(), i), TreasureLevelConfig.class);
            if (treasureLevelConfig == null) {
                LOGGER.error("TreasureLevelConfig is not found ,configId : {},level : {}", beInheritedTreasure.getConfigId(), i);
                return TResult.valueOf(CONFIG_NOT_FOUND);
            }
            beInheritedTotalExp += treasureLevelConfig.getNeedExp();
        }
        /**再根据被继承宝物的总经验给继承宝物升级，如果继承宝物满级了，就返还经验道具*/
        //继承宝物的等级
        int inheritedLevel = initLevel;
        //继承宝物的经验
        int inheritedExp = 0;
        TreasureLevelConfig treasureLevelConfig = globalConfigService.findConfig(IdentiyKey.build(inheritTreasure.getConfigId(), inheritedLevel), TreasureLevelConfig.class);
        if (treasureLevelConfig == null) {
            LOGGER.error("TreasureLevelConfig is not found ,configId : {},level : {}", inheritTreasure.getConfigId(), inheritedLevel);
            return TResult.valueOf(CONFIG_NOT_FOUND);
        }
        //升级需要的经验
        int needExp = treasureLevelConfig.getNeedExp();
        while (beInheritedTotalExp >= needExp) {
            //当前等级下一级的配置
            treasureLevelConfig = globalConfigService.findConfig(IdentiyKey.build(inheritTreasure.getConfigId(), inheritedLevel + 1), TreasureLevelConfig.class);
            //找不到配置说明已经达到顶级
            if (treasureLevelConfig == null) {
                break;
            }
            //减去升级所需的经验
            beInheritedTotalExp -= needExp;
            //等级加一
            inheritedLevel++;
            //所需要经验变成下一级升级所需要的经验
            needExp = treasureLevelConfig.getNeedExp();
        }
        int treasureMaxLevel = TreasureConfigService.getTreasureMaxLevel(inheritTreasure.getConfigId());
        //继承宝物满级了,经验没消耗完作为经验道具返还。没有满级经验没消耗完就存到继承宝物的经验上
        if (treasureMaxLevel == inheritedLevel && beInheritedTotalExp > 0) {
            rewardList.addAll(getReturnRewardListByExp(beInheritedTotalExp));
        } else if (beInheritedTotalExp > 0) {
            inheritedExp = (int) beInheritedTotalExp;
        }
        //--------------------------------------------宝物强化继承-------------------------------------------------------------------------------------------------------------------------

        //--------------------------------------------宝物精炼继承-------------------------------------------------------------------------------------------------------------------------
        /**先计算出来被继承的宝物的总消耗道具*/
        int beInheritedEvolveLevel = beInheritedTreasure.getEvolveLevel();
        Collection<RewardObject> beInheritedEvolveCost = Lists.newLinkedList();
        for (int i = 0; i < beInheritedEvolveLevel; i++) {
            TreasureRefineConfig treasureRefineConfig = globalConfigService.findConfig(IdentiyKey.build(beInheritedTreasure.getConfigId(), i), TreasureRefineConfig.class);
            if (treasureRefineConfig == null) {
                LOGGER.error("TreasureRefineConfig is not found,configId : {} ,level : {}", beInheritedTreasure.getConfigId(), i);
                return TResult.valueOf(CONFIG_NOT_FOUND);
            }
            beInheritedEvolveCost.addAll(treasureRefineConfig.getCostList());
        }
        beInheritedEvolveCost = RewardHelper.groupByTypeAndId(beInheritedEvolveCost);
        //key:type+","+id,value:num
        Map<String, Long> beInheritedEvolveCostMap = Maps.newHashMap();
        for (RewardObject rewardObject : beInheritedEvolveCost) {
            String key = rewardObject.getType() + "," + rewardObject.getId();
            beInheritedEvolveCostMap.put(key, rewardObject.getCount());
        }
        /**再根据被继承宝物的总经验给继承宝物升级，如果继承宝物满级了，就返还经验道具*/
        //继承宝物的精炼等级
        int inheritedEvolveLevel = 0;
        TreasureRefineConfig treasureRefineConfig =
            globalConfigService.findConfig(IdentiyKey.build(inheritTreasure.getConfigId(), inheritedEvolveLevel), TreasureRefineConfig.class);
        if (treasureRefineConfig == null) {
            LOGGER.error("TreasureRefineConfig is not found ,configId : {},evolveLevel : {}", inheritTreasure.getConfigId(), inheritedEvolveLevel);
            return TResult.valueOf(CONFIG_NOT_FOUND);
        }
        boolean breakLoop = false;
        Collection<RewardObject> costList = treasureRefineConfig.getCostList();
        while (true) {
            //当前等级下一级的配置
            treasureRefineConfig = globalConfigService.findConfig(IdentiyKey.build(inheritTreasure.getConfigId(), inheritedEvolveLevel + 1), TreasureRefineConfig.class);
            //找不到配置说明已经达到顶级
            if (treasureRefineConfig == null) {
                break;
            }
            //先判断是否所有物品都充足
            for (RewardObject costObject : costList) {
                String key = costObject.getType() + "," + costObject.getId();
                long totalNUm = beInheritedEvolveCostMap.getOrDefault(key, 0L);
                totalNUm -= costObject.getCount();
                if (totalNUm < 0) {
                    breakLoop = true;
                    break;
                }
            }
            if (breakLoop) {
                break;
            }
            //再扣除物品
            for (RewardObject costObject : costList) {
                String key = costObject.getType() + "," + costObject.getId();
                beInheritedEvolveCostMap.merge(key, -costObject.getCount(), Long::sum);
            }
            //继承宝物的精炼等级+1
            inheritedEvolveLevel++;
            //所需要消耗变成下一级升级所需要的消耗
            costList = treasureRefineConfig.getCostList();
        }
        //返还未消耗完的道具
        for (Entry<String, Long> entry : beInheritedEvolveCostMap.entrySet()) {
            if (entry.getValue() <= 0) {
                continue;
            }
            String[] split = entry.getKey().split(",");
            int type = Integer.parseInt(split[0]);
            int id = Integer.parseInt(split[1]);
            rewardList.add(RewardObject.valueOf(type, id, entry.getValue()));
        }
        //--------------------------------------------宝物精炼继承-------------------------------------------------------------------------------------------------------------------------

        //--------------------------------------------宝物雕文继承-------------------------------------------------------------------------------------------------------------------------
        /** 因为雕文的消耗每个品质的相同等级都是一样的，这里只需要注意高品质被低品质继承后返还材料的问题（另需注意如果以后策划把不同品质消耗配置得不一样了，就会有问题）*/
        int beInheritedGlyphsLevel = beInheritedTreasure.getGlyphsLevel();
        int inheritedGlyphsLevel = 0;

        TreasureConfig beInheritedConfig = globalConfigService.findConfig(IdentiyKey.build(beInheritedTreasure.getConfigId()), TreasureConfig.class);
        if (beInheritedConfig == null) {
            LOGGER.error("TreasureConfig is not found ,configId : {}", beInheritedTreasure.getConfigId());
            return TResult.valueOf(CONFIG_NOT_FOUND);
        }
        TreasureConfig inheritConfig = globalConfigService.findConfig(IdentiyKey.build(inheritTreasure.getConfigId()), TreasureConfig.class);
        if (inheritConfig == null) {
            LOGGER.error("TreasureConfig is not found ,configId : {}", inheritTreasure.getConfigId());
            return TResult.valueOf(CONFIG_NOT_FOUND);
        }
        //用被继承的宝物的雕文等级赋值给i,将i作为参数去寻找继承宝物的雕文配置。 如果找到了，就将i赋值给继承宝物的雕文等级并且跳出循环，没有找到就将被继承的宝物i-1等级所在配置所需要的消耗放入rewardList,再将i--进行下一次循环。
        for (int i = beInheritedGlyphsLevel; i > 0; i--) {
            TreasureGlyphsConfig inheritedGlyphsConfig =
                globalConfigService.findConfig(IdentiyKey.build(inheritConfig.getType(), inheritConfig.getQuality(), inheritConfig.getAptitude(), i), TreasureGlyphsConfig.class);
            if (inheritedGlyphsConfig != null) {
                inheritedGlyphsLevel = i;
                break;
            } else {
                TreasureGlyphsConfig beInheritedGlyphsConfig =
                    globalConfigService.findConfig(IdentiyKey.build(beInheritedConfig.getType(), beInheritedConfig.getQuality(), beInheritedConfig.getAptitude(), i - 1),
                        TreasureGlyphsConfig.class);
                if (beInheritedGlyphsConfig == null) {
                    LOGGER.error("TreasureGlyphsConfig is not found ,type:{},quality:{},aptitude:{},glyphsLevel:{}", beInheritedConfig.getType(), beInheritedConfig.getQuality(),
                        beInheritedConfig.getAptitude(), i - 1);
                    return TResult.valueOf(CONFIG_NOT_FOUND);
                }
                rewardList.addAll(beInheritedGlyphsConfig.getCostList());
            }
        }
        //--------------------------------------------宝物雕文继承-------------------------------------------------------------------------------------------------------------------------

        //--------------------------------------------被继承宝物觉醒返还-------------------------------------------------------------------------------------------------------------------------
        for (int i = beInheritedTreasure.getAwakenNodeLevel() - 1; i >= 0; i--) {
            TreasureAwakenConfig treasureAwakenConfig = globalConfigService.findConfig(IdentiyKey.build(beInheritedTreasure.getConfigId(), i), TreasureAwakenConfig.class);
            if (treasureAwakenConfig == null) {
                LOGGER.error("TreasureAwakenConfig is not found ,configId : {},awakenLevel:{}", beInheritedTreasure.getConfigId(), i);
                return TResult.valueOf(CONFIG_NOT_FOUND);
            }
            rewardList.addAll(treasureAwakenConfig.getCostList());
        }
        //--------------------------------------------被继承宝物觉醒返还-------------------------------------------------------------------------------------------------------------------------
        inheritTreasure.levelUp(inheritedLevel, inheritedExp);
        beInheritedTreasure.levelUp(initLevel, 0);
        inheritTreasure.setEvolveLevel(inheritedEvolveLevel);
        beInheritedTreasure.setEvolveLevel(0);
        inheritTreasure.setGlyphsLevel(inheritedGlyphsLevel);
        beInheritedTreasure.setGlyphsLevel(0);
        beInheritedTreasure.setAwakenNodeLevel(0);
        inheritTreasure.setScore(countTreasureScore(inheritTreasure));
        beInheritedTreasure.setScore(countTreasureScore(beInheritedTreasure));
        dbQueue.updateQueue(inheritTreasure, beInheritedTreasure);
        TreasurePushHelper.pushTreasure(actorId, Lists.newArrayList(inheritTreasure, beInheritedTreasure));
        if (!rewardList.isEmpty()) {
            RewardResult rewardResult = RewardHelper.sendRewardList(actorId, rewardList, OperationType.TREASURE_INHERIT);
            return TResult.sucess(rewardResult);
        }
        return TResult.sucess(RewardResult.newBuilder().build());
    }

    /**
     * 计算宝物评分
     *
     * @param treasure
     * @return
     */
    private long countTreasureScore(Treasure treasure) {
        TreasureConfig treasureConfig = globalConfigService.findConfig(IdentiyKey.build(treasure.getConfigId()), TreasureConfig.class);
        if (treasureConfig == null) {
            return 0L;
        }
        long result = FormulaUtils.executeRoundingLong(treasureConfig.getScoreExpr(), treasure.getLevel(), treasure.getEvolveLevel(), treasure.getGlyphsLevel(),
            treasure.getAwakenNodeLevel());
        String strengthComparison = globalConfigService.findGlobalConfig(GlobalConfigKey.STRENGTH_COMPARISON).getValue();
        return FormulaUtils.executeRoundingLong(strengthComparison, result);
    }
}
