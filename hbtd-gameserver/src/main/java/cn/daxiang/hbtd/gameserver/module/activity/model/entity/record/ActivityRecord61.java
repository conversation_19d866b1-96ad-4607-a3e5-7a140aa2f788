package cn.daxiang.hbtd.gameserver.module.activity.model.entity.record;

import com.google.common.collect.Sets;

import java.util.Set;

/**
 * 每日首充活动个人记录
 *
 * @author: <PERSON>
 * @date: 2024/4/22 15:29
 * @Description:
 */
public class ActivityRecord61 {
    /**
     * 特惠礼包购买列表
     */
    private Set<Integer> buyGiftIds = Sets.newHashSet();

    /**
     * 特惠礼包领取列表
     */
    private Set<Integer> receives = Sets.newHashSet();

    /**
     * 当天充值额度
     * 单位:分
     */
    private long dailyCharge;

    /**
     * 已领取的累充奖励
     */
    private Set<Integer> receiveCharge = Sets.newHashSet();

    /**
     * 最后一次重置时间
     */
    private long resetTime;

    public Set<Integer> getBuyGiftIds() {
        return buyGiftIds;
    }

    public void setBuyGiftIds(Set<Integer> buyGiftIds) {
        this.buyGiftIds = buyGiftIds;
    }

    public Set<Integer> getReceives() {
        return receives;
    }

    public void setReceives(Set<Integer> receives) {
        this.receives = receives;
    }

    public long getDailyCharge() {
        return dailyCharge;
    }

    public void setDailyCharge(long dailyCharge) {
        this.dailyCharge = dailyCharge;
    }

    public Set<Integer> getReceiveCharge() {
        return receiveCharge;
    }

    public void setReceiveCharge(Set<Integer> receiveCharge) {
        this.receiveCharge = receiveCharge;
    }

    public long getResetTime() {
        return resetTime;
    }

    public void setResetTime(long resetTime) {
        this.resetTime = resetTime;
    }

    /**
     * 每日重置
     */
    public void dailyRest() {
        buyGiftIds.clear();
        receives.clear();
        dailyCharge = 0;
        receiveCharge.clear();
        resetTime = System.currentTimeMillis();
    }

    /**
     * 增加购买次数
     *
     * @param id
     */
    public void buy(int id) {
        this.buyGiftIds.add(id);
    }

    /**
     * 增加累充值
     *
     * @param recharge
     */
    public void addDailyCharge(long recharge) {
        this.dailyCharge += recharge;
    }

    /**
     * 特惠礼包是否已领取
     *
     * @param id
     * @return
     */
    public boolean isReceivePreferentialGift(int id) {
        return this.receives.contains(id);
    }

    /**
     * 获取特惠礼包是否已购买
     *
     * @param id
     * @return
     */
    public boolean isBuyPreferentialGift(int id) {
        return this.buyGiftIds.contains(id);
    }

    /**
     * 累充礼包是否已领取
     *
     * @param id
     * @return
     */
    public boolean isReceiveChargeReward(int id) {
        return receiveCharge.contains(id);
    }

    /**
     * 领取累充奖励
     *
     * @param cid
     */
    public void addReceiveCharge(int cid) {
        receiveCharge.add(cid);
    }

    public void addReceivePreferentialGift(int id) {
        this.receives.add(id);
    }
}
