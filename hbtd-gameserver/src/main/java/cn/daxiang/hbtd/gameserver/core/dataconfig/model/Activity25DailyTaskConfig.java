package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.dataconfig.annotation.FieldIgnore;
import cn.daxiang.framework.identity.IdentiyKey;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/27
 */
@DataFile(fileName = "activity_25_daily_task_config")
public class Activity25DailyTaskConfig implements ModelAdapter {
    /**
     * DATA
     */
    private int data;
    /**
     * 任务列表
     */
    private String taskGroup;
    /**
     * 1-日常 * 2-成就
     */
    private int type;

    @FieldIgnore
    private List<Integer> taskIdList = Lists.newArrayList();

    @Override
    public void initialize() {
        JSONArray array = JSONArray.parseArray(taskGroup);
        for (Object taskId : array) {
            taskIdList.add(Integer.valueOf(taskId.toString()));
        }
    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(data, type);
    }

    public int getData() {
        return data;
    }

    public int getType() {
        return type;
    }

    public List<Integer> getTaskIdList() {
        return taskIdList;
    }
}
