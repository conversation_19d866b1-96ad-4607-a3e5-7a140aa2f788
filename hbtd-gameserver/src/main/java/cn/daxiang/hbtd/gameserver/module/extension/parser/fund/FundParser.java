package cn.daxiang.hbtd.gameserver.module.extension.parser.fund;

import cn.daxiang.framework.result.CollectionResult;
import cn.daxiang.hbtd.gameserver.core.database.table.Fund;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.FundRewardConfig;
import cn.daxiang.shared.reward.RewardObject;

import java.util.Collection;

/**
 * <AUTHOR>
 * @date 2022/6/14
 */
public interface FundParser {
    /**
     * @param actorId
     * @param configs
     * @param fund
     * @param isLuxury 是否是领取豪华奖励
     * @return
     */
    CollectionResult<RewardObject> parser(long actorId, Collection<FundRewardConfig> configs, Fund fund, boolean isLuxury);
}
