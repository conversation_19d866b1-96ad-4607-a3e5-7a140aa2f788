package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.dataconfig.annotation.FieldIgnore;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.shared.reward.RewardObject;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;

import java.util.Collection;

/**
 * <AUTHOR>
 * @date 2025/2/19
 */
@DataFile(fileName = "activity_70_sign_config")
public class Activity70SignConfig implements ModelAdapter {
    /**
     * 活动DATA
     */
    private int data;
    /**
     * 登录天数
     */
    private int days;
    /**
     * 奖励
     */
    private String rewards;

    @FieldIgnore
    private final Collection<RewardObject> rewardList = Lists.newArrayList();

    @Override
    public void initialize() {
        JSONArray rewardsArray = JSONObject.parseArray(rewards);
        for (Object reward : rewardsArray) {
            JSONArray rewardArray = JSONObject.parseArray(reward.toString());
            rewardList.add(RewardObject.valueOf(rewardArray));
        }
    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(data, days);
    }

    public int getData() {
        return data;
    }

    public int getDays() {
        return days;
    }

    public Collection<RewardObject> getRewardList() {
        return rewardList;
    }
}
