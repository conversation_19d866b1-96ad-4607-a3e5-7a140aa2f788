package cn.daxiang.hbtd.gameserver.module.goods.parser.effect.impl;

import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.GoodsConfig;
import cn.daxiang.hbtd.gameserver.core.oss.type.OperationType;
import cn.daxiang.hbtd.gameserver.module.goods.parser.effect.AbstractGoodsEffectParser;
import cn.daxiang.hbtd.gameserver.module.goods.type.GoodsEffectType;
import cn.daxiang.hbtd.gameserver.module.user.facade.ActorFacade;
import cn.daxiang.protocol.game.CommonProtocol.RewardResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 增加VIP经验
 *
 * <AUTHOR>
 * @date 2019/9/7
 */
@Component
public class AddVipExpGoodsEffectParser extends AbstractGoodsEffectParser {
    @Autowired
    private ActorFacade actorFacade;

    @Override
    public TResult<RewardResult> execute(long actorId, long num, GoodsConfig goodsConfig, byte[] value, OperationType operationType) {
        long recharge = goodsConfig.calcEffectValue() * num;
        Result result = actorFacade.addVipExp(actorId, recharge, recharge, operationType);
        if (result.isFail()) {
            return TResult.valueOf(result.statusCode);
        }
        RewardResult rewardResult = RewardResult.newBuilder().build();
        return TResult.sucess(rewardResult);
    }

    @Override
    protected GoodsEffectType getType() {
        return GoodsEffectType.ADD_VIP_EXP;
    }
}
