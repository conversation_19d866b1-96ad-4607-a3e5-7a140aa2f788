package cn.daxiang.hbtd.gameserver.module.arsenal.facade.impl;

import cn.daxiang.framework.event.GameEvent;
import cn.daxiang.framework.event.TriggerTime;
import cn.daxiang.framework.event.annotation.Event;
import cn.daxiang.framework.event.annotation.EventOnline;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.framework.rpc.RpcCall;
import cn.daxiang.framework.rpc.event.ArsenalBattleEndEvent;
import cn.daxiang.framework.rpc.world.WorldArsenalRpc;
import cn.daxiang.framework.rpc.world.WorldTeamRpc;
import cn.daxiang.framework.utils.DateUtils;
import cn.daxiang.framework.utils.FormulaUtils;
import cn.daxiang.framework.utils.NumberUtils;
import cn.daxiang.framework.utils.RandomUtils;
import cn.daxiang.framework.utils.TimeConstant;
import cn.daxiang.hbtd.gameserver.core.GameBaseFacade;
import cn.daxiang.hbtd.gameserver.core.GameConfig;
import cn.daxiang.hbtd.gameserver.core.database.table.Arsenal;
import cn.daxiang.hbtd.gameserver.core.dataconfig.convert.impl.IntMapConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.convert.impl.MapIntListConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.convert.impl.RewardObjectListConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.ArsenalMonsterConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.ArsenalSingleRewardConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.HeroConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.MonsterCapabilityConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.MonsterGroupConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.ArsenalConfigService;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.HeroConfigService;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.MonsterConfigService;
import cn.daxiang.hbtd.gameserver.core.dispatch.DispatchHelper;
import cn.daxiang.hbtd.gameserver.core.event.impl.ArsenalChallengeEvent;
import cn.daxiang.hbtd.gameserver.core.event.impl.ArsenalRefreshEvent;
import cn.daxiang.hbtd.gameserver.core.event.impl.ArsenalTokenReceiveEvent;
import cn.daxiang.hbtd.gameserver.core.event.impl.TeamOneClickInvitationEvent;
import cn.daxiang.hbtd.gameserver.core.oss.GameOssLogger;
import cn.daxiang.hbtd.gameserver.core.oss.type.OperationType;
import cn.daxiang.hbtd.gameserver.core.rpc.WorldRpcHelper;
import cn.daxiang.hbtd.gameserver.module.PbBuilder;
import cn.daxiang.hbtd.gameserver.module.arsenal.dao.ArsenalDao;
import cn.daxiang.hbtd.gameserver.module.arsenal.facade.ArsenalFacade;
import cn.daxiang.hbtd.gameserver.module.arsenal.helper.ArsenalHelper;
import cn.daxiang.hbtd.gameserver.module.arsenal.helper.ArsenalPushHelper;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleRoom;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleSprite;
import cn.daxiang.hbtd.gameserver.module.battle.parser.battle.BattleContext;
import cn.daxiang.hbtd.gameserver.module.battle.parser.battle.BattleParser;
import cn.daxiang.hbtd.gameserver.module.goods.helper.RewardHelper;
import cn.daxiang.hbtd.gameserver.module.team.TeamFacade;
import cn.daxiang.hbtd.gameserver.module.user.helper.ActorHelper;
import cn.daxiang.hbtd.gameserver.module.user.type.ActorUnlockType;
import cn.daxiang.protocol.game.ArsenalProtocol.ArsenalBattleResultResponse;
import cn.daxiang.protocol.game.ArsenalProtocol.TeamInfoResponse;
import cn.daxiang.protocol.game.ArsenalProtocol.TeamListResponse;
import cn.daxiang.protocol.game.ArsenalProtocol.TokenRewardResultResponse;
import cn.daxiang.protocol.game.BattleProtocol;
import cn.daxiang.protocol.game.BattleProtocol.BattleResultResponse;
import cn.daxiang.protocol.game.CommonProtocol;
import cn.daxiang.protocol.game.CommonProtocol.RewardResult;
import cn.daxiang.protocol.game.CommonProtocol.RewardResultResponse;
import cn.daxiang.protocol.game.TypeProtocol.ActorFieldType;
import cn.daxiang.protocol.game.TypeProtocol.ResourceId;
import cn.daxiang.protocol.game.TypeProtocol.RewardType;
import cn.daxiang.shared.GlobalConfigKey;
import cn.daxiang.shared.event.EventKey;
import cn.daxiang.shared.module.arsenal.ArsenalPushActor;
import cn.daxiang.shared.module.battle.BattleParameterKey;
import cn.daxiang.shared.module.battle.BattleType;
import cn.daxiang.shared.module.lineup.SpriteAttributeType;
import cn.daxiang.shared.module.team.Team;
import cn.daxiang.shared.module.team.TeamExtArsenal;
import cn.daxiang.shared.reward.RewardObject;
import cn.daxiang.shared.type.TeamType;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.protobuf.InvalidProtocolBufferException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Component
public class ArsenalFacadeImpl extends GameBaseFacade implements ArsenalFacade {

    /**
     * 怪物列表数量
     */
    private static int MONSTER_COUNT = 3;
    /**
     * 组队人数
     */
    private static int TEAM_ACTOR_TIMES = 3;
    /**
     * 信物容器最大数
     */
    private static int TOKEN_TIMES_LIMIT = 9;
    private static Map<Long, List<byte[]>> BATTLE_RESULT_RESPONSE_MAP = Maps.newConcurrentMap();
    @Autowired
    private ArsenalDao arsenalDao;
    @Autowired
    private BattleContext battleContext;
    @Autowired
    private TeamFacade teamFacade;

    @Override
    public TResult<Arsenal> getArsenal(long actorId) {
        Arsenal arsenal = arsenalDao.getArsenal(actorId);
        if (arsenal.getMonsterMap().isEmpty()) {
            this.refreshMonster(arsenal);
        }
        return TResult.sucess(arsenal);
    }

    @Override
    public TResult<TeamInfoResponse> teamUp(long actorId, int position) {
        Result unlock = ActorHelper.unlock(actorId, ActorUnlockType.ARSENAL);
        if (unlock.isFail()) {
            return TResult.valueOf(unlock);
        }
        Arsenal arsenal = arsenalDao.getArsenal(actorId);

        int playTimes = globalConfigService.findGlobalConfig(GlobalConfigKey.ARSENAL_DAILY_PLAY_TIMES).findInt();

        if (arsenal.getPlayTimes() >= playTimes) {
            return TResult.valueOf(ARSENAL_PLAY_TIMES_NOT_ENOUGH);
        }
        if (arsenal.getHeroTokenMap().size() >= TOKEN_TIMES_LIMIT) {
            return TResult.valueOf(ARSENAL_TOKEN_FULL);
        }
        Integer heroId = arsenal.getMonsterMap().get(position);
        if (heroId == null) {
            return TResult.valueOf(INVALID_PARAM);
        }
        ArsenalMonsterConfig config = globalConfigService.findConfig(heroId, ArsenalMonsterConfig.class);
        if (config == null) {
            return TResult.valueOf(CONFIG_NOT_FOUND);
        }
        int actorLevel = ActorHelper.getActorLevel(actorId);
        long monsterGroupPower = getMonsterGroupPower(config.getMonsterGroup(), actorLevel);
        TeamExtArsenal ext = TeamExtArsenal.valueOf(position, heroId, actorLevel, monsterGroupPower);
        TResult<Team> tResult = teamFacade.create(actorId, TeamType.ARSENAL, ext, TEAM_ACTOR_TIMES);
        if (tResult.isFail()) {
            return TResult.valueOf(tResult.statusCode);
        }
        TeamInfoResponse response = ArsenalHelper.buildTeam(tResult.item);
        return TResult.sucess(response);
    }

    @Override
    public TResult<TeamInfoResponse> setPassword(long actorId, String password) {
        TResult<Team> tResult = teamFacade.setPassword(actorId, password);
        if (tResult.isFail()) {
            return TResult.valueOf(tResult.statusCode);
        }
        TeamInfoResponse response = ArsenalHelper.buildTeam(tResult.item);
        return TResult.sucess(response);
    }

    @Override
    public Result teamInvite(long actorId, long leaderActorId, int targetServerId, long targetActorId) {
        Result unlock = ActorHelper.unlock(actorId, ActorUnlockType.ARSENAL);
        if (unlock.isFail()) {
            return unlock;
        }
        Result result = teamFacade.invite(actorId, leaderActorId, targetServerId, targetActorId);
        return result;
    }

    @Override
    public Result teamQuit(long actorId) {
        Result result = teamFacade.quit(actorId);
        return result;
    }

    @Override
    public Result teamKickOut(long actorId, long targetActorId) {
        Result result = teamFacade.kickOut(actorId, targetActorId);
        return result;
    }

    @Override
    public Result joinTeam(long actorId, long targetId, String password) {
        Result unlock = ActorHelper.unlock(actorId, ActorUnlockType.ARSENAL);
        if (unlock.isFail()) {
            return unlock;
        }
        TResult<Arsenal> tResult = getArsenal(actorId);
        if (tResult.isFail()) {
            return Result.valueOf(tResult.statusCode);
        }
        Arsenal arsenal = tResult.item;
        int timesLimit = globalConfigService.findGlobalConfig(GlobalConfigKey.ARSENAL_DAILY_PLAY_TIMES).findInt();
        if (arsenal.getPlayTimes() >= timesLimit) {
            return Result.valueOf(ARSENAL_PLAY_TIMES_NOT_ENOUGH);
        }
        if (arsenal.getHeroTokenMap().size() >= TOKEN_TIMES_LIMIT) {
            return Result.valueOf(ARSENAL_TOKEN_FULL);
        }
        Result result = teamFacade.join(actorId, targetId, password, TeamType.ARSENAL);
        return result;
    }

    @Override
    public TResult<TeamListResponse> teamList(long actorId) {
        Collection<Team> list = teamFacade.getList(actorId, TeamType.ARSENAL);
        if (list.isEmpty()) {
            return TResult.sucess(TeamListResponse.newBuilder().build());
        }
        TeamListResponse.Builder response = TeamListResponse.newBuilder();
        for (Team team : list) {
            TeamInfoResponse teamInfoResponse = ArsenalHelper.buildTeam(team);
            response.addTeams(teamInfoResponse);
        }
        return TResult.sucess(response.build());
    }

    @Override
    public Result monsterCommonRefresh(long actorId) {
        Result unlock = ActorHelper.unlock(actorId, ActorUnlockType.ARSENAL);
        if (unlock.isFail()) {
            return unlock;
        }
        // 检查免费 扣除物品 消耗元宝
        TResult<Arsenal> tResult = getArsenal(actorId);
        if (tResult.isFail()) {
            return Result.valueOf(tResult.statusCode);
        }
        Arsenal arsenal = tResult.item;
        //1.免费次数
        if (System.currentTimeMillis() > arsenal.getFreeRefreshTime()) {
            int minute = globalConfigService.findGlobalConfig(GlobalConfigKey.FREE_REFRESH_INTERVAL_TIME).findInt();
            long freeRefreshTime = System.currentTimeMillis() + (minute * TimeConstant.ONE_MINUTE_MILLISECOND);
            arsenal.setFreeRefreshTime(freeRefreshTime);
            this.refreshMonster(arsenal);
            ArsenalPushHelper.pushArsenal(actorId, arsenal);
            return Result.valueOf();
        }

        //2.消耗道具
        Collection<RewardObject> costGoodsReward = globalConfigService.findGlobalObject(GlobalConfigKey.ARSENAL_CONSUMPTION_COUPONS, RewardObjectListConfig.class).getVs();
        Result goodsResult = RewardHelper.decrease(actorId, costGoodsReward, OperationType.ARSENAL_REFRESH);
        if (goodsResult.isOk()) {
            this.refreshMonster(arsenal);
            ArsenalPushHelper.pushArsenal(actorId, arsenal);
            return Result.valueOf();
        }

        //3.消耗元宝
        int costTimes = globalConfigService.findGlobalConfig(GlobalConfigKey.ARSENAL_REFRESH_NORMAL_COST).findInt();
        RewardObject rewardObject = RewardObject.valueOf(RewardType.RESOURCE_VALUE, ResourceId.DIAMOND_VALUE, costTimes);
        ArrayList<RewardObject> rewardObjects = Lists.newArrayList(rewardObject);
        Result result = RewardHelper.decrease(actorId, rewardObjects, OperationType.ARSENAL_REFRESH);
        if (result.isFail()) {
            return Result.valueOf();
        }
        this.refreshMonster(arsenal);
        ArsenalPushHelper.pushArsenal(actorId, arsenal);
        return Result.valueOf();
    }

    @Override
    public Result monsterAppointRefresh(long actorId, int camp, int quality) {
        Result unlock = ActorHelper.unlock(actorId, ActorUnlockType.ARSENAL);
        if (unlock.isFail()) {
            return unlock;
        }
        // 指定刷新
        TResult<Arsenal> tResult = getArsenal(actorId);
        if (tResult.isFail()) {
            return Result.valueOf(tResult.statusCode);
        }
        int costTimes = globalConfigService.findGlobalConfig(GlobalConfigKey.ARSENAL_REFRESH_CAMP_COST).findInt();

        IntMapConfig mapConfig = globalConfigService.findGlobalObject(GlobalConfigKey.ARSENAL_REFRESH_QUALITY_COST, IntMapConfig.class);
        Integer qualityCostTimes = mapConfig.getMap().get(quality);
        if (qualityCostTimes == null) {
            return Result.valueOf(CONFIG_NOT_FOUND);
        }

        costTimes += qualityCostTimes;
        RewardObject rewardObject = RewardObject.valueOf(RewardType.RESOURCE_VALUE, ResourceId.DIAMOND_VALUE, costTimes);
        ArrayList<RewardObject> rewardObjects = Lists.newArrayList(rewardObject);
        Result result = RewardHelper.decrease(actorId, rewardObjects, OperationType.ARSENAL_REFRESH);
        if (result.isFail()) {
            return Result.valueOf();
        }

        this.refreshMonsterAppoint(actorId, camp, quality);
        ArsenalPushHelper.pushArsenal(actorId, tResult.item);
        DispatchHelper.postEvent(new ArsenalRefreshEvent(actorId, quality, 1));
        return Result.valueOf();
    }

    @Override
    public TResult<TokenRewardResultResponse> tokenReceiveReward(long actorId) {
        Result unlock = ActorHelper.unlock(actorId, ActorUnlockType.ARSENAL);
        if (unlock.isFail()) {
            return TResult.valueOf(unlock.statusCode);
        }
        TResult<Arsenal> tResult = getArsenal(actorId);
        if (tResult.isFail()) {
            return TResult.valueOf(tResult.statusCode);
        }

        Arsenal arsenal = tResult.item;

        if (arsenal.getHeroTokenMap().size() < TOKEN_TIMES_LIMIT) {
            return TResult.valueOf(INVALID_PARAM);
        }
        Collection<RewardObject> rewardList = Lists.newArrayList();
        Map<Integer, Collection<RewardObject>> tokenRewardMap = Maps.newHashMap();

        for (Map.Entry<Integer, List<Integer>> entry : getTokenLocationMap().entrySet()) {
            tokenRewardMap.put(entry.getKey(), getTokenReward(arsenal, entry.getValue()));
        }
        for (Collection<RewardObject> rewardObjects : tokenRewardMap.values()) {
            rewardList.addAll(rewardObjects);
        }

        arsenal.getHeroTokenMap().clear();
        arsenal.addDailyAwardedList(rewardList);
        dbQueue.updateQueue(arsenal);
        RewardHelper.sendRewardList(actorId, rewardList, OperationType.ARSENAL_TOKEN_RECEIVE_REWARD);
        DispatchHelper.postEvent(new ArsenalTokenReceiveEvent(actorId));
        // 领取奖励
        ArsenalPushHelper.pushArsenal(actorId, arsenal);
        TokenRewardResultResponse.Builder builder = TokenRewardResultResponse.newBuilder();
        for (Map.Entry<Integer, Collection<RewardObject>> entry : tokenRewardMap.entrySet()) {
            if (!entry.getValue().isEmpty()) {
                builder.putTokenRewards(entry.getKey(), PbBuilder.buildRewardObjectList(entry.getValue()));
            }
        }
        return TResult.sucess(builder.build());
    }

    /**
     * 获取信物品中奖对应的位置
     */
    private Map<Integer, List<Integer>> getTokenLocationMap() {
        Map<Integer, List<Integer>> map = Maps.newTreeMap();
        map.put(1, Lists.newArrayList(1, 2, 3));
        map.put(2, Lists.newArrayList(4, 5, 6));
        map.put(3, Lists.newArrayList(7, 8, 9));
        map.put(4, Lists.newArrayList(1, 4, 7));
        map.put(5, Lists.newArrayList(2, 5, 8));
        map.put(6, Lists.newArrayList(3, 6, 9));
        return map;
    }

    @Override
    public Result teamSwapPosition(long actorId, int currentPosition, int targetPosition) {
        Result result = teamFacade.swapPosition(actorId, currentPosition, targetPosition);
        return result;
    }

    private int getScore(Arsenal arsenal) {
        IntMapConfig scoreMap = globalConfigService.findGlobalObject(GlobalConfigKey.ARSENAL_QUALITY_SCORE, IntMapConfig.class);
        if (scoreMap == null) {
            LOGGER.error("ARSENAL_QUALITY_SCORE config error");
            return 0;
        }
        Integer tScore = 0;
        for (int position = 0; position <= TOKEN_TIMES_LIMIT; position++) {
            Integer heroId = arsenal.getHeroTokenMap().get(position);
            if (heroId == null) {
                continue;
            }
            HeroConfig heroConfig = HeroConfigService.getHeroConfigByHeroId(heroId);
            if (heroConfig == null) {
                continue;
            }
            Integer score = scoreMap.getMap().get(heroConfig.getQuality());
            if (score == null) {
                return 0;
            }
            tScore += score;
        }
        return tScore;
    }

    private Collection<RewardObject> getTokenReward(Arsenal arsenal, List<Integer> positionList) {
        Collection<RewardObject> rewardObjects = Lists.newArrayList();
        Set<Integer> heroIdSet = Sets.newHashSet();
        List<Integer> qualityList = Lists.newArrayList();
        for (Integer position : positionList) {
            int heroId = arsenal.getHeroTokenMap().get(position);
            heroIdSet.add(heroId);
            HeroConfig heroConfig = HeroConfigService.getHeroConfigByHeroId(heroId);
            qualityList.add(heroConfig.getQuality());
        }
        if (heroIdSet.size() == 1) {
            ArsenalMonsterConfig config = globalConfigService.findConfig(heroIdSet.iterator().next(), ArsenalMonsterConfig.class);
            if (config == null) {
                return Collections.emptyList();
            }
            rewardObjects.addAll(config.getTokenRewardList());
        }
        Collections.sort(qualityList);

        MapIntListConfig config = globalConfigService.findGlobalObject(GlobalConfigKey.ARSENAL_TOKEN_QUALITY_REWARD, MapIntListConfig.class);
        Collection<Integer> rewardGoodsIdList = config.getRewardListByKey(qualityList.get(0));
        Integer goodsId = RandomUtils.randomHit(rewardGoodsIdList);
        rewardObjects.add(RewardObject.valueOf(RewardType.GOODS_VALUE, goodsId, 2));

        return rewardObjects;
    }

    @Override
    public TResult<RewardResultResponse> oneClickChallenge(long actorId) {
        Result unlock = ActorHelper.unlock(actorId, ActorUnlockType.ARSENAL);
        if (unlock.isFail()) {
            return TResult.valueOf(unlock.statusCode);
        }
        TResult<Arsenal> tResult = getArsenal(actorId);
        if (tResult.isFail()) {
            return TResult.valueOf(tResult.statusCode);
        }
        Arsenal arsenal = tResult.item;
        int remainingTimes = globalConfigService.findGlobalConfig(GlobalConfigKey.ARSENAL_DAILY_PLAY_TIMES).findInt() - arsenal.getPlayTimes();
        if (remainingTimes <= 0) {
            return TResult.valueOf(ARSENAL_PLAY_TIMES_NOT_ENOUGH);
        }
        int tokenTimes = TOKEN_TIMES_LIMIT - arsenal.getHeroTokenMap().size();
        if (remainingTimes < tokenTimes) {
            tokenTimes = remainingTimes;
        }
        if (tokenTimes <= 0) {
            return TResult.valueOf(ARSENAL_PLAY_TIMES_NOT_ENOUGH);
        }

        int costTimes = globalConfigService.findGlobalConfig(GlobalConfigKey.ARSENAL_ONE_KEY_CHALLENGE_COST_SINGLE).findInt() * tokenTimes;
        RewardObject rewardObject = RewardObject.valueOf(RewardType.RESOURCE_VALUE, ResourceId.DIAMOND_VALUE, costTimes);
        ArrayList<RewardObject> rewardObjects = Lists.newArrayList(rewardObject);

        Result result = RewardHelper.decrease(actorId, rewardObjects, OperationType.ARSENAL_ONE_CLICK_CHALLENGE);
        if (result.isFail()) {
            return TResult.valueOf(result);
        }

        int quality = globalConfigService.findGlobalConfig(GlobalConfigKey.ARSENAL_ONE_CLICK_TOKEN_QUALITY).findInt();

        int actorLevel = ActorHelper.getActorLevel(actorId);
        Collection<RewardObject> rewardList = Lists.newArrayList();
        for (int i = 0; i < tokenTimes; i++) {
            ArsenalMonsterConfig config = ArsenalConfigService.getArsenalMonsterConfig(quality);
            arsenal.getHeroTokenMap().put(arsenal.getHeroTokenMap().size() + 1, config.getHeroId());
            arsenal.addPlayTimes();

            ArsenalSingleRewardConfig rewardConfig = ArsenalConfigService.getArsenalSingleRewardConfig(config.getQuality(), actorLevel);
            rewardList.addAll(rewardConfig.getRewardList());
        }
        arsenal.addDailyAwardedList(rewardList);
        dbQueue.updateQueue(arsenal);
        ArsenalPushHelper.pushArsenal(actorId, arsenal);
        DispatchHelper.postEvent(new ArsenalChallengeEvent(actorId, quality, tokenTimes));
        RewardResult rewardResult = RewardHelper.sendRewardList(actorId, rewardList, OperationType.ARSENAL_ONE_CLICK_CHALLENGE);
        RewardResultResponse response = PbBuilder.buildRewardResultResponse(rewardResult);
        return TResult.sucess(response);
    }

    @Override
    public Result challenge(long actorId) {
        Result unlock = ActorHelper.unlock(actorId, ActorUnlockType.ARSENAL);
        if (unlock.isFail()) {
            return unlock;
        }

        TResult<Team> tResult = teamFacade.challenge(actorId);
        if (tResult.isFail()) {
            return Result.valueOf(tResult.statusCode);
        }

        BATTLE_RESULT_RESPONSE_MAP.remove(actorId);
        Team team = tResult.item;
        TeamExtArsenal extArsenal = (TeamExtArsenal) tResult.item.getExt();
        ArsenalMonsterConfig config = globalConfigService.findConfig(extArsenal.getHeroId(), ArsenalMonsterConfig.class);
        String expr1 = globalConfigService.findGlobalConfig(GlobalConfigKey.ARSENAL_MONSTER_ATTRIBUTES_EXPR1).getValue();
        String expr2 = globalConfigService.findGlobalConfig(GlobalConfigKey.ARSENAL_MONSTER_ATTRIBUTES_EXPR2).getValue();
        int level = ActorHelper.getActorLevel(actorId);
        int location = team.getAttributeMap().keySet().stream().reduce(Integer::min).get();
        Map<BattleParameterKey, Object> parameterMap = Maps.newHashMap();
        parameterMap.put(BattleParameterKey.TEAM_LOCATION_ID, location);
        parameterMap.put(BattleParameterKey.TEAM_INFO, team);
        parameterMap.put(BattleParameterKey.MONSTER_GROUP_ID, config.getMonsterGroup());
        parameterMap.put(BattleParameterKey.ARSENAL_EXPR1, expr1);
        parameterMap.put(BattleParameterKey.ARSENAL_EXPR2, expr2);
        parameterMap.put(BattleParameterKey.ACTOR_LEVEL, level);
        BattleParser parser = battleContext.getParser(BattleType.ARSENAL);
        Result result = parser.fight(actorId, parameterMap);
        return result;
    }

    @Override
    public void challengeRecord(long actorId, BattleRoom battleRoom) {
        boolean isWin = battleRoom.isWin();

        // 队员人数大于1人，并且未赢则进入第二场
        Team team = (Team) battleRoom.getParameterMap().get(BattleParameterKey.TEAM_INFO);
        int teamLocationId = (int) battleRoom.getParameterMap().get(BattleParameterKey.TEAM_LOCATION_ID);

        Map<BattleParameterKey, Object> parameterMap = Maps.newHashMap();
        parameterMap.putAll(battleRoom.getParameterMap());

        Map<Integer, Integer> leftHp = Maps.newHashMap();
        Map<Integer, Integer> leftRage = Maps.newHashMap();
        for (BattleSprite battleSprite : battleRoom.getBattleSpriteList(BattleProtocol.BattleCamp.RIGHT_CAMP)) {
            // 剩余血量万份比
            int hpPercent = NumberUtils.getValuePercent(battleSprite.getSpriteBattle().getHP(), battleSprite.getSpriteBattle().getHPMax());
            if (battleSprite.getSpriteBattle().getHP() > 0 && hpPercent == 0) {
                hpPercent = 1;
            }
            leftHp.put(battleSprite.getPositionId(), hpPercent);
            leftRage.put(battleSprite.getPositionId(), battleSprite.getSpriteBattle().getRage());
        }
        parameterMap.put(BattleParameterKey.MONSTER_HP_TTPERCENT, leftHp);
        parameterMap.put(BattleParameterKey.MONSTER_RAGE, leftRage);
        int monsterGroupId = (int) battleRoom.getParameterMap().get(BattleParameterKey.MONSTER_GROUP_ID);
        MonsterGroupConfig groupConfig = globalConfigService.findConfig(IdentiyKey.build(monsterGroupId), MonsterGroupConfig.class);

        Collection<BattleProtocol.BattleMember> members = Lists.newArrayList();
        Map<Byte, Object> attribute = team.getAttributeMap().get(teamLocationId);
        CommonProtocol.ActorProfile leftActorProfile = ActorHelper.getActorProfile(attribute);
        BattleProtocol.BattleMember leftBattleMember = battleRoom.getBattleMember(BattleProtocol.BattleCamp.LEFT_CAMP, leftActorProfile);
        members.add(leftBattleMember);

        CommonProtocol.ActorProfile rightActorProfile = ActorHelper.getActorProfile(groupConfig.getAttributes());
        BattleProtocol.BattleMember rightBattleMember = battleRoom.getBattleMember(BattleProtocol.BattleCamp.RIGHT_CAMP, rightActorProfile);
        members.add(rightBattleMember);

        BattleResultResponse response =
            PbBuilder.buildBattleResultResponse(battleRoom.getBattleType(), members, battleRoom.getRightHistoryBattleSpriteVO(), battleRoom.getBattleReportMap(), null,
                battleRoom.getBattleStats());

        List<byte[]> list = BATTLE_RESULT_RESPONSE_MAP.get(actorId);
        if (list == null) {
            list = Lists.newLinkedList();
            BATTLE_RESULT_RESPONSE_MAP.put(actorId, list);
        }
        list.add(response.toByteArray());

        if (team.getAttributeMap().size() > 1 && !isWin) {
            int nextTeamLocationId = 0;
            for (int i = teamLocationId + 1; i <= TEAM_ACTOR_TIMES; i++) {
                if (team.getAttributeMap().containsKey(i)) {
                    nextTeamLocationId = i;
                    break;
                }
            }

            if (nextTeamLocationId > 0) {
                parameterMap.put(BattleParameterKey.TEAM_LOCATION_ID, nextTeamLocationId);
                BattleParser parser = battleContext.getParser(BattleType.ARSENAL);
                parser.fight(actorId, parameterMap);
                return;
            }
        }

        List<byte[]> battleResultResponses = BATTLE_RESULT_RESPONSE_MAP.get(actorId);
        // 队长战斗结果直接发送
        onArsenalBattleEndEvent(new ArsenalBattleEndEvent(actorId, isWin, team, battleResultResponses));

        if (team.getAttributeMap().size() == 1) {
            WorldRpcHelper.asynCall(GameConfig.getServerId(), WorldTeamRpc.class, new RpcCall<WorldTeamRpc>() {
                @Override
                public void run(WorldTeamRpc rpcProxy) {
                    rpcProxy.actorLogout(GameConfig.getServerType(), GameConfig.getServerId(), actorId);
                }
            });
            BATTLE_RESULT_RESPONSE_MAP.remove(actorId);
            return;
        }
        // 战斗结果发给队友
        List<ArsenalPushActor> pushActors = Lists.newArrayList();
        for (Map<Byte, Object> attributeMap : team.getAttributeMap().values()) {
            int targetServerId = (int) attributeMap.get((byte) ActorFieldType.SERVER_ID_VALUE);
            long targetActorId = Long.parseLong(attributeMap.get((byte) ActorFieldType.ACTOR_ID_VALUE).toString());
            if (team.getLeaderActorId() == targetActorId) {
                continue;
            }
            pushActors.add(ArsenalPushActor.valueOf(targetServerId, targetActorId));
        }
        WorldRpcHelper.asynCall(GameConfig.getServerId(), WorldArsenalRpc.class, new RpcCall<WorldArsenalRpc>() {
            @Override
            public void run(WorldArsenalRpc rpcProxy) {
                rpcProxy.pushCompeteResult(GameConfig.getServerType(), pushActors, isWin, team, battleResultResponses);
            }
        });
        BATTLE_RESULT_RESPONSE_MAP.remove(actorId);
    }

    @Override
    public Result teamOneClickInvitation(long actorId) {
        TResult<Team> result = teamFacade.oneClickInvitation(actorId);
        if (result.isFail()) {
            return Result.valueOf(result.statusCode);
        }

        Team team = result.item;
        if (team.getLeaderActorId() != actorId) {
            return Result.valueOf();
        }
        DispatchHelper.postEvent(new TeamOneClickInvitationEvent(actorId, team));
        return Result.valueOf();
    }

    @Event(name = EventKey.ARSENAL_BATTLE_END_EVENT)
    public void onArsenalBattleEndEvent(ArsenalBattleEndEvent event) {
        long actorId = event.actorId;
        ArsenalBattleResultResponse.Builder response = ArsenalBattleResultResponse.newBuilder();
        response.setIsWin(event.win);
        TResult<Arsenal> tResult = getArsenal(actorId);
        if (tResult.isFail()) {
            LOGGER.error("onArsenalBattleEndEvent getArsenal statusCode:{}", tResult.statusCode);
            return;
        }
        Arsenal arsenal = tResult.item;
        int limit = globalConfigService.findGlobalConfig(GlobalConfigKey.ARSENAL_RECENT_FRIENDS_LIMIT).findInt();
        if (event.team.getAttributeMap().size() > 1) {
            for (Map<Byte, Object> attribute : event.team.getAttributeMap().values()) {
                if (Long.parseLong(attribute.get((byte) ActorFieldType.ACTOR_ID_VALUE).toString()) == actorId) {
                    continue;
                }
                arsenal.addLastTeamActor(attribute, limit);
            }
            dbQueue.updateQueue(arsenal);
        }

        TeamExtArsenal extArsenal = (TeamExtArsenal) event.team.getExt();
        HeroConfig config = HeroConfigService.getHeroConfigByHeroId(extArsenal.getHeroId());
        if (config == null) {
            LOGGER.error("HeroConfig is null heroId:{}", extArsenal.getHeroId());
            return;
        }

        int size = event.team.getAttributeMap().size();

        if (event.win) {
            int playTimes = globalConfigService.findGlobalConfig(GlobalConfigKey.ARSENAL_DAILY_PLAY_TIMES).findInt();
            if (arsenal.getPlayTimes() >= playTimes) {
                LOGGER.error("onArsenalBattleEndEvent playTimes {}>{}", arsenal.getPlayTimes(), playTimes);
                return;
            }

            ArsenalSingleRewardConfig rewardConfig = ArsenalConfigService.getArsenalSingleRewardConfig(config.getQuality(), extArsenal.getLevel());
            if (rewardConfig == null) {
                LOGGER.error("ArsenalSingleRewardConfig is null quality:{},level:{}", config.getQuality(), extArsenal.getLevel());
                return;
            }
            arsenal.addPlayTimes();
            int id = arsenal.getHeroTokenMap().size() + 1;
            if (id > TOKEN_TIMES_LIMIT) {
                LOGGER.error("onArsenalBattleEndEvent TOKEN_TIMES_LIMIT {}", arsenal.getHeroTokenMap().size());
                return;
            }
            arsenal.getHeroTokenMap().put(id, extArsenal.getHeroId());
            Collection<RewardObject> passRewardList = Lists.newArrayList();
            // 多人组队奖励根据系数增加
            IntMapConfig rewardCoefficientConfig = globalConfigService.findGlobalObject(GlobalConfigKey.ARSENAL_TEAM_PASS_REWARD_COEFFICIENT, IntMapConfig.class);
            Integer percent = rewardCoefficientConfig.getMap().get(size);
            if (percent != null) {
                for (RewardObject rewardObject : rewardConfig.getRewardList()) {
                    long count = NumberUtils.getFloorPercentLongValue(rewardObject.getCount(), percent);
                    passRewardList.add(RewardObject.valueOf(rewardObject.getType(), rewardObject.getId(), rewardObject.getCount() + count));
                }
            } else {
                passRewardList.addAll(rewardConfig.getRewardList());
            }
            arsenal.addDailyAwardedList(passRewardList);
            RewardResult rewardResult = RewardHelper.sendRewardList(actorId, passRewardList, OperationType.ARSENAL_BATTLE);
            response.setRewardResult(rewardResult);
            if (event.team.getLeaderActorId() == event.actorId) {
                refreshMonster(actorId, extArsenal.getPosition());
            }
            dbQueue.updateQueue(arsenal);
            ArsenalPushHelper.pushArsenal(actorId, arsenal);
            DispatchHelper.postEvent(new ArsenalChallengeEvent(actorId, config.getQuality(), 1));
            GameOssLogger.arsenal(actorId, size, config.getQuality(), config.getRoleId(), 1);
        } else {
            GameOssLogger.arsenal(actorId, size, config.getQuality(), config.getRoleId(), 0);
        }

        response.setTeamInfo(ArsenalHelper.buildTeam(event.team));
        for (byte[] bytes : event.response) {
            try {
                response.addBattles(BattleResultResponse.parseFrom(bytes));
            } catch (InvalidProtocolBufferException e) {
                LOGGER.error("onArsenalBattleEndEvent", e);
            }
        }
        ArsenalPushHelper.pushBattleResult(actorId, response.build());
    }

    @EventOnline(trigger = TriggerTime.FIXED_HOUR, triggerValue = 24)
    public void onEvery0Hour(GameEvent e) {
        this.reset(e.getUniqueId(), true);
    }

    @Event(name = EventKey.ACTOR_LOGIN)
    public void onActorLogin(GameEvent e) {
        this.reset(e.getUniqueId(), false);
    }

    /**
     * 每日重置
     *
     * @param actorId
     * @param isPush
     */
    private void reset(long actorId, boolean isPush) {
        Result unlock = ActorHelper.unlock(actorId, ActorUnlockType.ARSENAL);
        if (unlock.isFail()) {
            return;
        }
        Arsenal arsenal = arsenalDao.getArsenal(actorId);
        if (!DateUtils.isToday(arsenal.getLastResetTime())) {
            arsenal.reset();
            dbQueue.updateQueue(arsenal);
            if (isPush) {
                ArsenalPushHelper.pushArsenal(actorId, arsenal);
            }
        }
    }

    /**
     * 普通刷新
     *
     * @param arsenal
     */
    private void refreshMonster(Arsenal arsenal) {
        IntMapConfig intMapConfig = globalConfigService.findGlobalObject(GlobalConfigKey.ARSENAL_MONSTER_RANDOM, IntMapConfig.class);
        Map<Integer, Integer> map = intMapConfig.getMap();
        for (int i = 1; i <= MONSTER_COUNT; i++) {
            Integer quality = RandomUtils.randomByWeight(map);
            ArsenalMonsterConfig config = ArsenalConfigService.getArsenalMonsterConfig(quality);
            arsenal.getMonsterMap().put(i, config.getHeroId());
        }
        dbQueue.updateQueue(arsenal);
    }

    /**
     * 刷新指定位置
     *
     * @param actorId
     * @param position
     */
    private void refreshMonster(long actorId, int position) {
        TResult<Arsenal> tResult = getArsenal(actorId);
        if (tResult.isFail()) {
            return;
        }
        Arsenal arsenal = tResult.item;
        IntMapConfig intMapConfig = globalConfigService.findGlobalObject(GlobalConfigKey.ARSENAL_MONSTER_RANDOM, IntMapConfig.class);
        Map<Integer, Integer> map = intMapConfig.getMap();
        Integer quality = RandomUtils.randomByWeight(map);
        ArsenalMonsterConfig config = ArsenalConfigService.getArsenalMonsterConfig(quality);
        arsenal.getMonsterMap().put(position, config.getHeroId());
        dbQueue.updateQueue(arsenal);
    }

    /**
     * 指定刷新
     *
     * @param actorId
     * @param camp
     * @param quality
     */
    private void refreshMonsterAppoint(long actorId, int camp, int quality) {
        TResult<Arsenal> tResult = getArsenal(actorId);
        if (tResult.isFail()) {
            LOGGER.error("refreshMonsterAppoint fail:{}", tResult.statusCode);
            return;
        }
        Arsenal arsenal = tResult.item;
        ArsenalMonsterConfig arsenalMonsterConfig = ArsenalConfigService.getArsenalMonsterConfig(camp, quality);
        if (arsenalMonsterConfig == null) {
            LOGGER.error("ArsenalMonsterConfig is null refreshMonsterAppoint camp:{} quality:{}", camp, quality);
            return;
        }
        List<Integer> heroIds = Lists.newArrayList();
        heroIds.add(arsenalMonsterConfig.getHeroId());
        IntMapConfig intMapConfig = globalConfigService.findGlobalObject(GlobalConfigKey.ARSENAL_MONSTER_RANDOM, IntMapConfig.class);
        Map<Integer, Integer> map = intMapConfig.getMap();
        for (int i = 2; i <= MONSTER_COUNT; i++) {
            Integer randomQuality = RandomUtils.randomByWeight(map);
            ArsenalMonsterConfig config = ArsenalConfigService.getArsenalMonsterConfig(randomQuality);
            heroIds.add(config.getHeroId());
        }
        Collections.shuffle(heroIds);
        int i = 1;
        for (Integer hoerId : heroIds) {
            arsenal.getMonsterMap().put(i, hoerId);
            i++;
        }
        dbQueue.updateQueue(arsenal);
    }

    private long getMonsterGroupPower(int monsterGroupId, int level) {
        MonsterGroupConfig config = globalConfigService.findConfig(monsterGroupId, MonsterGroupConfig.class);
        long power = 0;
        for (Integer monsterId : config.getPositionMonsterMap().values()) {
            power += getMonsterPower(monsterId, level);
        }
        return power;
    }

    private long getMonsterPower(int monsterId, int level) {
        String powerExpr = globalConfigService.findGlobalConfig(GlobalConfigKey.FIGHT_POWER_EXPRESSION).getValue();
        MonsterCapabilityConfig monsterCapabilityConfig = MonsterConfigService.getMonsterCapabilityConfig(monsterId);
        String expr1 = globalConfigService.findGlobalConfig(GlobalConfigKey.ARSENAL_MONSTER_ATTRIBUTES_EXPR1).getValue();
        String expr2 = globalConfigService.findGlobalConfig(GlobalConfigKey.ARSENAL_MONSTER_ATTRIBUTES_EXPR2).getValue();
        Map<SpriteAttributeType, Long> attributeMap = monsterCapabilityConfig.getAttributeMap(expr1, expr2, level);

        List<Long> attributeList = Lists.newLinkedList();
        for (SpriteAttributeType attributeType : SpriteAttributeType.values()) {
            if (attributeType.getId() >= 99) {
                break;
            }
            attributeList.add(attributeMap.getOrDefault(attributeType, 0L));
        }
        long power = FormulaUtils.executeRoundingLong(powerExpr, attributeList.toArray(new Long[attributeList.size()]));
        return power;
    }

}
