package cn.daxiang.hbtd.gameserver.module.user.model.entity;

import cn.daxiang.hbtd.gameserver.module.user.type.VipCardType;

/**
 * VIP卡实体
 *
 * <AUTHOR>
 */
public class VipCardEntity {
    /**
     * VIP卡类型 {@code VipCardType}
     */
    private VipCardType type;
    /**
     * 创建时间
     */
    private long createTime;
    /**
     * 结束时间
     */
    private long expirationTime;

    public static VipCardEntity valueOf(int type) {
        VipCardEntity entity = new VipCardEntity();
        entity.type = VipCardType.getType(type);
        entity.createTime = System.currentTimeMillis();
        return entity;
    }

    public VipCardType getType() {
        return type;
    }

    public void setType(int type) {
        this.type = VipCardType.getType(type);
    }

    public long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(long createTime) {
        this.createTime = createTime;
    }

    public long getExpirationTime() {
        return expirationTime;
    }

    public void setExpirationTime(long expirationTime) {
        this.expirationTime = expirationTime;
    }
}
