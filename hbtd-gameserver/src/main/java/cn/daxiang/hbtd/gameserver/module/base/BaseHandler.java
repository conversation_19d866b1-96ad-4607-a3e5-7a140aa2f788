package cn.daxiang.hbtd.gameserver.module.base;

import cn.daxiang.framework.dispatch.DispatchType;
import cn.daxiang.framework.protocal.DataPacket;
import cn.daxiang.framework.router.annotation.Cmd;
import cn.daxiang.hbtd.gameserver.core.netty.channel.ChannelKey;
import cn.daxiang.hbtd.gameserver.core.router.GatewayRouterHandlerImpl;
import cn.daxiang.protocol.game.BaseProtocol.BaseCmd;
import cn.daxiang.protocol.game.BaseProtocol.CommonHeartbeatResponse;
import cn.daxiang.protocol.game.ModuleProtocol.Module;
import io.netty.channel.Channel;
import org.springframework.stereotype.Component;

@Component
public class BaseHandler extends GatewayRouterHandlerImpl {

    @Override
    public int getModule() {
        return Module.BASE_VALUE;
    }

    @Cmd(Id = BaseCmd.HEART_BEAT_VALUE, dispatchType = DispatchType.ACTOR, CheckActorLogin = false)
    public void heartBeat(Channel channel, Long actorId, DataPacket packet) {
        if (!channel.hasAttr(ChannelKey.PLATFORM_UID)) {
            return;
        }

        CommonHeartbeatResponse response = CommonHeartbeatResponse.newBuilder().setTime(System.currentTimeMillis()).build();
        channelWrite(channel, packet, response);
    }

}
