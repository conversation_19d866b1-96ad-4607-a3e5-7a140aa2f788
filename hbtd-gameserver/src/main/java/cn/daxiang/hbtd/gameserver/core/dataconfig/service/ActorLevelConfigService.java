package cn.daxiang.hbtd.gameserver.core.dataconfig.service;

import cn.daxiang.framework.dataconfig.ConfigServiceAdapter;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.ActorLevelConfig;
import cn.daxiang.protocol.game.TypeProtocol.ActorFieldType;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

import static cn.daxiang.shared.GameModuleStatusCodeConstant.CONFIG_NOT_FOUND;

@Component
public class ActorLevelConfigService extends ConfigServiceAdapter {

    private static Map<Integer, ActorLevelConfig> ACTOR_LEVEL_CONFIG_MAP = new HashMap<>();

    private static int ACTOR_MAX_LEVEL = 0;

    public static TResult<ActorLevelConfig> getActorLevelConfig(int level) {
        ActorLevelConfig levelConfig = ACTOR_LEVEL_CONFIG_MAP.get(level);
        if (levelConfig == null) {
            LOGGER.error("ActorLevelConfig not found, level:{}", level);
            return TResult.valueOf(CONFIG_NOT_FOUND);
        }
        return TResult.sucess(levelConfig);
    }

    public static boolean isMaxLevel(int level, long exp) {
        TResult<ActorLevelConfig> result = getActorLevelConfig(level);
        if (result.isFail()) {
            return true;
        }
        ActorLevelConfig levelConfig = result.item;
        if (levelConfig.getNextLevel() != 0) {
            return false;
        }
        return levelConfig.getNeedExp() <= exp;
    }

    public static int getMaxLevel() {
        return ACTOR_MAX_LEVEL;
    }

    public static Map<ActorFieldType, Number> addExp(int level, long exp, long addExp) {
        Map<ActorFieldType, Number> attributeMaps = Maps.newHashMap();
        while (addExp > 0) {
            TResult<ActorLevelConfig> result = getActorLevelConfig(level);
            if (result.isFail()) {
                break;
            }
            ActorLevelConfig levelConfig = result.item;
            if (levelConfig.getNeedExp() <= exp + addExp) {
                if (levelConfig.getNextLevel() == 0) {
                    exp = levelConfig.getNeedExp();
                    addExp = 0;
                } else {
                    addExp -= levelConfig.getNeedExp() - exp;
                    level++;
                    exp = 0;
                }
            } else {
                exp += addExp;
                addExp = 0;
            }
        }
        attributeMaps.put(ActorFieldType.ACTOR_LEVEL, level);
        attributeMaps.put(ActorFieldType.ACTOR_EXP, exp);
        return attributeMaps;
    }

    @Override
    protected void initialize() {
        Collection<ActorLevelConfig> levelConfigList = dataConfig.listAll(this, ActorLevelConfig.class);
        for (ActorLevelConfig actorLevelConfig : levelConfigList) {
            ACTOR_LEVEL_CONFIG_MAP.put(actorLevelConfig.getLevel(), actorLevelConfig);
            if (actorLevelConfig.getNextLevel() == 0) {
                ACTOR_MAX_LEVEL = actorLevelConfig.getLevel();
            }
        }
    }

    @Override
    protected void clean() {
        ACTOR_MAX_LEVEL = 0;
        ACTOR_LEVEL_CONFIG_MAP.clear();
    }

}
