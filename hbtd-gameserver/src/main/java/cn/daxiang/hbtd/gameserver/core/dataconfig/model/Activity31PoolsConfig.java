package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.identity.IdentiyKey;

/**
 * 摘星揽月奖励配置表
 *
 * @Author: Gary
 * @Date 2022-08-12 17:31
 * @Description:
 */
@DataFile(fileName = "activity_31_pools_config")
public class Activity31PoolsConfig implements ModelAdapter {
    /**
     * data
     */
    private int data;
    /**
     * 对应召唤表Id
     * {@link GachaConfig#getId()}
     */
    private int id;

    @Override
    public void initialize() {

    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(data);
    }

    public int getData() {
        return data;
    }

    public int getId() {
        return id;
    }
}
