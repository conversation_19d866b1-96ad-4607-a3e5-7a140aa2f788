package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.dataconfig.annotation.FieldIgnore;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.shared.reward.RewardObject;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;

import java.util.Collection;

/**
 * <AUTHOR>
 */
@DataFile(fileName = "suppress_demon_challengeRewards_config")
public class SuppressDemonChallengeRewardsConfig implements ModelAdapter {
    /**
     * 镇妖台id
     */
    private int id;
    /**
     * 奖励挡位
     */
    private int rank;
    /**
     * 奖励
     */
    private String challengeRewards;
    @FieldIgnore
    private Collection<RewardObject> challengeRewardList = Lists.newArrayList();

    @Override
    public void initialize() {
        JSONArray killRewardArray = JSONArray.parseArray(challengeRewards);
        for (Object reward : killRewardArray) {
            JSONArray itemArray = JSONArray.parseArray(reward.toString());
            RewardObject rewardObject = RewardObject.valueOf(itemArray);
            challengeRewardList.add(rewardObject);
        }
    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(id, rank);
    }

    public int getId() {
        return id;
    }

    public int getRank() {
        return rank;
    }

    public Collection<RewardObject> getChallengeRewardList() {
        return challengeRewardList;
    }
}
