package cn.daxiang.hbtd.gameserver.module.gacha.facade.impl;

import cn.daxiang.framework.event.GameEvent;
import cn.daxiang.framework.event.TriggerTime;
import cn.daxiang.framework.event.annotation.Event;
import cn.daxiang.framework.event.annotation.EventOnline;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.framework.utils.DateUtils;
import cn.daxiang.framework.utils.RandomUtils;
import cn.daxiang.hbtd.gameserver.core.GameBaseFacade;
import cn.daxiang.hbtd.gameserver.core.database.table.Gacha;
import cn.daxiang.hbtd.gameserver.core.database.table.GachaHero;
import cn.daxiang.hbtd.gameserver.core.dataconfig.convert.impl.IntMapConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.convert.impl.MapJsonArrayConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.GachaConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.HeroConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.RewardPoolConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.ActivityOpenConfigService;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.HeroConfigService;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.RewardConfigService;
import cn.daxiang.hbtd.gameserver.core.dispatch.DispatchHelper;
import cn.daxiang.hbtd.gameserver.core.event.impl.GachaEvent;
import cn.daxiang.hbtd.gameserver.core.oss.type.OperationType;
import cn.daxiang.hbtd.gameserver.module.PbBuilder;
import cn.daxiang.hbtd.gameserver.module.activity.type.ActivityType;
import cn.daxiang.hbtd.gameserver.module.gacha.dao.GachaDao;
import cn.daxiang.hbtd.gameserver.module.gacha.dao.GachaHeroDao;
import cn.daxiang.hbtd.gameserver.module.gacha.facade.GachaFacade;
import cn.daxiang.hbtd.gameserver.module.gacha.helper.GachaPushHelper;
import cn.daxiang.hbtd.gameserver.module.gacha.model.entity.GachaEntity;
import cn.daxiang.hbtd.gameserver.module.gacha.type.GachaType;
import cn.daxiang.hbtd.gameserver.module.goods.helper.RewardHelper;
import cn.daxiang.hbtd.gameserver.module.user.helper.ActorHelper;
import cn.daxiang.hbtd.gameserver.module.user.type.ActorUnlockType;
import cn.daxiang.protocol.game.GachaProtocol;
import cn.daxiang.protocol.game.GachaProtocol.GachaResultResponse;
import cn.daxiang.protocol.game.TypeProtocol;
import cn.daxiang.shared.GlobalConfigKey;
import cn.daxiang.shared.event.EventKey;
import cn.daxiang.shared.reward.RewardObject;
import cn.daxiang.shared.reward.RewardType;
import cn.daxiang.shared.type.QualityType;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.TreeMap;

/**
 * <AUTHOR>
 */
@Component
public class GachaFacadeImpl extends GameBaseFacade implements GachaFacade {
    /**
     * 心愿单最大英雄数量
     */
    private static int WISH_HERO_LIMIT = 2;
    @Autowired
    private GachaDao gachaDao;
    @Autowired
    private GachaHeroDao gachaHeroDao;

    @Override
    public TResult<Gacha> getGacha(long actorId) {
        Gacha gacha = gachaDao.getGacha(actorId);
        return TResult.sucess(gacha);
    }

    @Override
    public TResult<GachaResultResponse> gacha(long actorId, int gachaId, int times, boolean useGoods) {
        Result result = ActorHelper.unlock(actorId, ActorUnlockType.GACHA, gachaId);
        if (result.isFail()) {
            return TResult.valueOf(result);
        }
        int gachaTimesLimit = globalConfigService.findGlobalConfig(GlobalConfigKey.ONCE_GACHA_LIMIT).findInt();
        if (times <= 0 || times > gachaTimesLimit) {
            return TResult.valueOf(INVALID_PARAM);
        }
        GachaConfig gachaConfig = globalConfigService.findConfig(IdentiyKey.build(gachaId), GachaConfig.class);
        if (gachaConfig == null) {
            return TResult.valueOf(CONFIG_NOT_FOUND);
        }
        if (gachaConfig.getActivityType() != ActivityType.NONE && ActivityOpenConfigService.getActivityOpenConfigList(gachaConfig.getActivityType()).isEmpty()) {
            return TResult.valueOf(INVALID_PARAM);
        }
        Gacha gacha = gachaDao.getGacha(actorId);
        GachaEntity gachaEntity = gacha.getGachaEntity(gachaId, gachaConfig.hasFreeTime());

        GachaHero gachaHero = gachaHeroDao.getGachaHero(actorId);

        float disCount = gachaConfig.getDisCount();
        Collection<GachaEntity> gachaList = Lists.newArrayList(gachaEntity);
        // 检查是否可以抽奖
        if (gachaConfig.hasFreeTime() && gachaEntity.isFree() && times == 1 && !useGoods) {
            gachaEntity.setNextFreeTime(gachaConfig.getNextFreeTime());
        } else {
            Collection<RewardObject> costList = Lists.newArrayList();
            for (int i = 0; i < times; i++) {
                if (useGoods) {
                    costList.addAll(gachaConfig.getGoodsCostList());
                } else {
                    costList.addAll(gachaConfig.getCostList());
                }
            }
            // 打折后价格(使用物品和抽一次不打折)
            if (!useGoods && gachaConfig.getDisCount() != 1 && times != 1) {
                costList = RewardHelper.groupByTypeAndId(costList);
                Collection<RewardObject> disCountCostList = Lists.newArrayList();
                for (RewardObject rewardObject : costList) {
                    long count = (long) (rewardObject.getCount() * disCount);
                    RewardObject reward = RewardObject.valueOf(rewardObject.getType(), rewardObject.getId(), count);
                    disCountCostList.add(reward);
                }
                costList.clear();
                costList.addAll(disCountCostList);
            }
            if (costList.isEmpty()) {
                return TResult.valueOf(INVALID_PARAM);
            }
            Result costResult = RewardHelper.decrease(actorId, costList, gachaConfig.getGachaType().getOperationType());
            if (costResult.isFail()) {
                return TResult.valueOf(costResult);
            }
        }
        Map<Integer, Collection<RewardObject>> gachaMap = Maps.newLinkedHashMap();
        Collection<RewardObject> gachaRewardList = Lists.newArrayList();

        int rewardPoolId = 0;
        for (int i = 1; i <= times; i++) {
            Collection<RewardObject> rewardList = Lists.newArrayList(gachaConfig.getBaseRewardList());
            for (int baseRewardPoolId : gachaConfig.getBaseRewardPoolList()) {
                RewardPoolConfig rewardPoolConfig = RewardConfigService.getRewardPoolConfig(baseRewardPoolId);
                if (rewardPoolConfig == null) {
                    return TResult.valueOf(CONFIG_NOT_FOUND);
                }
                rewardList.addAll(rewardPoolConfig.getRewardList());
            }
            int gachaTimes = gachaEntity.getTimes() + 1;
            int totalTimes = gachaEntity.getTotalTimes() + 1;

            Optional<Integer> poolId = gachaConfig.getRewardPoolId(gachaTimes, totalTimes);
            if (!poolId.isPresent()) {
                return TResult.valueOf(CONFIG_ERROR);
            }
            rewardPoolId = poolId.get();
            RewardPoolConfig rewardPoolConfig = RewardConfigService.getRewardPoolConfig(rewardPoolId);
            if (rewardPoolConfig == null) {
                return TResult.valueOf(CONFIG_NOT_FOUND);
            }
            rewardList.addAll(wishRewardList(gachaConfig, gachaEntity, gachaHero, rewardPoolConfig.getRewardList()));
            gachaMap.put(i, rewardList);
            gachaRewardList.addAll(rewardList);
            boolean isReset = gachaConfig.isReset(gachaTimes);
            gachaEntity.gacha(isReset);
        }

        if (gachaConfig.getGachaType() == GachaType.DIAMOND_HERO) {
            int blessMaxValue = globalConfigService.findGlobalConfig(GlobalConfigKey.BLESS_MAX_VALUE).findInt();
            gachaHero.addBlessValue(times, blessMaxValue);
            dbQueue.updateQueue(gachaHero);
            GachaPushHelper.pushGachaHero(actorId, gachaHero);
        }

        RewardHelper.sendRewardList(actorId, gachaRewardList, gachaConfig.getGachaType().getOperationType());
        dbQueue.updateQueue(gacha);

        DispatchHelper.postEvent(new GachaEvent(actorId, gachaId, times));

        GachaPushHelper.pushGachaInfo(actorId, gachaList);
        GachaResultResponse.Builder builder = GachaResultResponse.newBuilder();
        for (Map.Entry<Integer, Collection<RewardObject>> entry : gachaMap.entrySet()) {
            builder.putRewardObjectMap(entry.getKey(), PbBuilder.buildRewardObjectList(entry.getValue()));
        }

        return TResult.sucess(builder.build());
    }

    @Override
    public TResult<GachaHero> getGachaHero(long actorId) {
        GachaHero gachaHero = gachaHeroDao.getGachaHero(actorId);
        return TResult.sucess(gachaHero);
    }

    /**
     * 心愿单加成后返回奖励结果
     */
    private List<RewardObject> wishRewardList(GachaConfig gachaConfig, GachaEntity gachaEntity, GachaHero gachaHero, List<RewardObject> gachaRewardList) {
        Set<Integer> wishHeroIdList = gachaHero.getWishHeroIdList();
        int wishlistEnableTimes = globalConfigService.findGlobalConfig(GlobalConfigKey.WISHLIST_ENABLE_TIMES).findInt();
        if (gachaEntity.getTotalTimes() < wishlistEnableTimes || gachaConfig.getGachaType() != GachaType.DIAMOND_HERO || wishHeroIdList.isEmpty()) {
            return gachaRewardList;
        }

        // key:英雄品质 value:替换概率
        Map<Integer, Integer> qualityPercentageMap = Maps.newHashMap();
        // key:英雄品质 value:替换英雄ID
        Map<Integer, List<Integer>> qualityHeroIdMap = Maps.newHashMap();

        IntMapConfig globalObject = globalConfigService.findGlobalObject(GlobalConfigKey.WISHLIST_ODDS_IMPROVE, IntMapConfig.class);
        Map<Integer, Integer> wishPercentageMap = globalObject.getMap();
        if (wishHeroIdList.size() > 0) {
            for (Integer heroId : wishHeroIdList) {
                // 英雄是否已替换过
                if (gachaHero.getWishRecordList().contains(heroId)) {
                    continue;
                }
                HeroConfig heroConfig = HeroConfigService.getHeroConfigByHeroId(heroId);
                if (heroConfig == null) {
                    LOGGER.error("HeroConfig is null heroId:{}", heroId);
                    continue;
                }
                // 品质3 品质4 替换概率，如果品质一样的2个英雄概率相加
                if (wishPercentageMap.containsKey(heroConfig.getQuality())) {
                    int wishPercentage = wishPercentageMap.get(heroConfig.getQuality());
                    qualityPercentageMap.put(heroConfig.getQuality(), qualityPercentageMap.getOrDefault(heroConfig.getQuality(), 0) + wishPercentage);
                    List<Integer> heroIdList = qualityHeroIdMap.computeIfAbsent(heroConfig.getQuality(), v -> new ArrayList<>());
                    heroIdList.add(heroConfig.getRoleId());
                }
            }
        }

        if (qualityHeroIdMap.isEmpty()) {
            return gachaRewardList;
        }

        List<RewardObject> rewardObjects = Lists.newArrayList();
        for (RewardObject rewardObject : gachaRewardList) {
            if (rewardObject.getType() == RewardType.HERO.getId()) {
                HeroConfig heroConfig = HeroConfigService.getHeroConfigByHeroId(rewardObject.getId());
                Integer wishPercentage = qualityPercentageMap.getOrDefault(heroConfig.getQuality(), 0);
                if (wishPercentage <= 0) {
                    rewardObjects.add(rewardObject);
                    continue;
                }
                // 判断是否需要替换
                if (RandomUtils.is10000Hit(wishPercentage)) {
                    List<Integer> heroIds = qualityHeroIdMap.get(heroConfig.getQuality());
                    Integer heroId = RandomUtils.randomHit(heroIds);
                    if (heroId == null) {
                        rewardObjects.add(rewardObject);
                        continue;
                    }
                    if (gachaHero.getWishRecordList().contains(heroId)) {
                        rewardObjects.add(rewardObject);
                        continue;
                    }

                    gachaHero.getWishRecordList().add(heroId);
                    int percentage = Math.max(0, wishPercentage - wishPercentageMap.getOrDefault(heroConfig.getQuality(), 0));
                    qualityPercentageMap.put(heroConfig.getQuality(), percentage);
                    rewardObjects.add(RewardObject.valueOf(rewardObject.getType(), heroId, rewardObject.getCount()));
                    continue;
                } else {
                    // 运气随机出的心愿英雄也算名额
                    List<Integer> heroIds = qualityHeroIdMap.get(heroConfig.getQuality());
                    if (heroIds == null || !heroIds.contains(rewardObject.getId())) {
                        rewardObjects.add(rewardObject);
                        continue;
                    }

                    if (gachaHero.getWishRecordList().contains(rewardObject.getId())) {
                        rewardObjects.add(rewardObject);
                        continue;
                    }

                    gachaHero.getWishRecordList().add(rewardObject.getId());
                    int percentage = Math.max(0, wishPercentage - wishPercentageMap.getOrDefault(heroConfig.getQuality(), 0));
                    qualityPercentageMap.put(heroConfig.getQuality(), percentage);
                    rewardObjects.add(RewardObject.valueOf(rewardObject.getType(), rewardObject.getId(), rewardObject.getCount()));
                    continue;
                }
            }
            rewardObjects.add(rewardObject);
        }
        return rewardObjects;
    }

    @Override
    public Result wishChoice(long actorId, Set<Integer> heroIds) {
        Result result = ActorHelper.unlock(actorId, ActorUnlockType.GACHA, GachaType.DIAMOND_HERO.getId());
        if (result.isFail()) {
            return result;
        }

        Gacha gacha = gachaDao.getGacha(actorId);
        GachaEntity gachaEntity = gacha.getGachaMap().get(GachaType.DIAMOND_HERO.getId());
        if (gachaEntity == null) {
            LOGGER.error("GachaEntity not exist GachaType.DIAMOND_HERO:{}", GachaType.DIAMOND_HERO.getId());
            return Result.valueOf(INVALID_PARAM);
        }
        int wishlistEnableTimes = globalConfigService.findGlobalConfig(GlobalConfigKey.WISHLIST_ENABLE_TIMES).findInt();
        if (gachaEntity.getTotalTimes() < wishlistEnableTimes) {
            return Result.valueOf(INVALID_PARAM);
        }

        GachaHero gachaHero = gachaHeroDao.getGachaHero(actorId);
        if (heroIds.size() > WISH_HERO_LIMIT) {
            return Result.valueOf(INVALID_PARAM);
        }

        for (Integer heroId : heroIds) {
            HeroConfig heroConfig = HeroConfigService.getHeroConfigByHeroId(heroId);
            if (heroConfig == null) {
                return Result.valueOf(CONFIG_NOT_FOUND);
            }
            if (heroConfig.getInWishlist() != 1) {
                return Result.valueOf(UNDER_CONSTRUCTION);
            }
        }

        gachaHero.getWishHeroIdList().clear();
        if (!heroIds.isEmpty()) {
            gachaHero.getWishHeroIdList().addAll(heroIds);
        }
        dbQueue.updateQueue(gachaHero);
        GachaPushHelper.pushGachaHero(actorId, gachaHero);
        return Result.valueOf();
    }

    @Override
    public Result blessChoice(long actorId, int heroId) {
        Result result = ActorHelper.unlock(actorId, ActorUnlockType.GACHA, GachaType.DIAMOND_HERO.getId());
        if (result.isFail()) {
            return result;
        }

        Gacha gacha = gachaDao.getGacha(actorId);
        GachaEntity gachaEntity = gacha.getGachaMap().get(GachaType.DIAMOND_HERO.getId());
        if (gachaEntity == null) {
            LOGGER.error("GachaEntity not exist GachaType.DIAMOND_HERO:{}", GachaType.DIAMOND_HERO.getId());
            return Result.valueOf(INVALID_PARAM);
        }
        int blessEnableTimes = globalConfigService.findGlobalConfig(GlobalConfigKey.BLESS_ENABLE_TIMES).findInt();
        if (gachaEntity.getTotalTimes() < blessEnableTimes) {
            return Result.valueOf(INVALID_PARAM);
        }

        GachaHero gachaHero = gachaHeroDao.getGachaHero(actorId);
        HeroConfig heroConfig = HeroConfigService.getHeroConfigByHeroId(heroId);
        if (heroConfig == null) {
            return Result.valueOf(CONFIG_NOT_FOUND);
        }
        if (heroConfig.getInBlesslist() != 1) {
            return Result.valueOf(UNDER_CONSTRUCTION);
        }
        gachaHero.setBlessHeroId(heroId);
        dbQueue.updateQueue(gachaHero);
        GachaPushHelper.pushGachaHero(actorId, gachaHero);
        return Result.valueOf();
    }

    @Override
    public TResult<GachaProtocol.GachaResultResponse> blessReceive(long actorId) {
        GachaHero gachaHero = gachaHeroDao.getGachaHero(actorId);
        if (gachaHero.getBlessHeroId() == 0) {
            return TResult.valueOf(INVALID_PARAM);
        }
        HeroConfig heroConfig = HeroConfigService.getHeroConfigByHeroId(gachaHero.getBlessHeroId());
        if (heroConfig == null) {
            return TResult.valueOf(CONFIG_NOT_FOUND);
        }
        if (heroConfig.getInBlesslist() != 1) {
            return TResult.valueOf(GACHA_BLESS_HERO_LIMIT);
        }
        int quality = heroConfig.getQuality();
        MapJsonArrayConfig mapIntListConfig = globalConfigService.findGlobalObject(GlobalConfigKey.BLESS_AQUIRE_VALUE, MapJsonArrayConfig.class);
        Map<Integer, JSONArray> cache = mapIntListConfig.getCache();
        JSONArray jsonArray = cache.get(quality);
        if (jsonArray == null) {
            LOGGER.error("GlobalConfigKey BLESS_AQUIRE_VALUE error quality:{}", quality);
            return TResult.valueOf(CONFIG_NOT_FOUND);
        }
        List<Integer> blessValues = jsonArray.toJavaList(Integer.class);
        Collections.sort(blessValues);
        TreeMap<Integer, Integer> blessValuesMap = Maps.newTreeMap();
        int i = 1;
        for (Integer blessValue : blessValues) {
            blessValuesMap.put(i, blessValue);
            i++;
        }
        int blessTimes = 0;
        switch (QualityType.getType(quality)) {
            case RARE:
                blessTimes = gachaHero.getBlessTimes();
                break;
            case EPIC:
                blessTimes = gachaHero.getEpicBlessTimes();
                break;
        }
        Map.Entry<Integer, Integer> entry = blessValuesMap.floorEntry(blessTimes + 1);
        if (entry == null) {
            LOGGER.error("GlobalConfigKey BLESS_AQUIRE_VALUE error quality:{} blessTimes:{}", quality, blessTimes);
            return TResult.valueOf(CONFIG_NOT_FOUND);
        }
        Integer costBlessValue = entry.getValue();
        if (costBlessValue > gachaHero.getBlessValue()) {
            return TResult.valueOf(GACHA_BLESS_VALUE_NOT_ENOUGH);
        }
        gachaHero.setBlessValue(gachaHero.getBlessValue() - costBlessValue);
        gachaHero.addBlessTimes(quality);
        dbQueue.updateQueue(gachaHero);
        Collection<RewardObject> rewardObjects = Lists.newArrayList(RewardObject.valueOf(TypeProtocol.RewardType.HERO_VALUE, gachaHero.getBlessHeroId(), 1));
        RewardHelper.sendRewardList(actorId, rewardObjects, OperationType.BLESS_RECEIVE);
        GachaResultResponse.Builder builder = GachaResultResponse.newBuilder();
        builder.putRewardObjectMap(1, PbBuilder.buildRewardObjectList(rewardObjects));
        GachaPushHelper.pushGachaHero(actorId, gachaHero);
        return TResult.sucess(builder.build());
    }

    @EventOnline(trigger = TriggerTime.FIXED_HOUR, triggerValue = 24)
    public void onEvery0Hour(GameEvent e) {
        this.resetGacha(e.getUniqueId(), true);
    }

    @Event(name = EventKey.ACTOR_LOGIN)
    public void onActorLogin(GameEvent e) {
        this.resetGacha(e.getUniqueId(), false);
    }

    private void resetGacha(long actorId, boolean isPush) {
        Gacha gacha = gachaDao.getGacha(actorId);
        if (!DateUtils.isToday(gacha.getLastResetTime())) {
            for (GachaEntity gachaEntity : gacha.getGachaMap().values()) {
                gachaEntity.setDayTimes(0);
                gacha.setLastResetTime(System.currentTimeMillis());
            }
            dbQueue.updateQueue(gacha);
        }
        if (isPush) {
            GachaPushHelper.pushGachaInfo(actorId, gacha.getGachaMap().values());
        }
    }
}
