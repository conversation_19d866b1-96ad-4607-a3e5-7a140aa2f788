package cn.daxiang.hbtd.gameserver.module.title.facade.impl;

import cn.daxiang.framework.event.ActorDelayEvent;
import cn.daxiang.framework.event.GameEvent;
import cn.daxiang.framework.event.OnEventListener;
import cn.daxiang.framework.event.TriggerTime;
import cn.daxiang.framework.event.annotation.Event;
import cn.daxiang.framework.event.annotation.EventOnline;
import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.utils.DateUtils;
import cn.daxiang.hbtd.gameserver.core.GameBaseFacade;
import cn.daxiang.hbtd.gameserver.core.database.table.Title;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.TitleConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.TitleConfigService;
import cn.daxiang.hbtd.gameserver.core.dispatch.DispatchHelper;
import cn.daxiang.hbtd.gameserver.core.event.impl.LineupPowerRefreshEvent;
import cn.daxiang.hbtd.gameserver.core.event.impl.TitleCreateEvent;
import cn.daxiang.hbtd.gameserver.core.event.impl.TitleExpirationEvent;
import cn.daxiang.hbtd.gameserver.module.title.dao.TitleDao;
import cn.daxiang.hbtd.gameserver.module.title.facade.TitleFacade;
import cn.daxiang.hbtd.gameserver.module.title.helper.TitlePushHelper;
import cn.daxiang.hbtd.gameserver.module.title.model.TitleEntity;
import cn.daxiang.hbtd.gameserver.module.title.parser.TitleContext;
import cn.daxiang.hbtd.gameserver.module.title.parser.TitleParser;
import cn.daxiang.hbtd.gameserver.module.title.type.TitleConditionType;
import cn.daxiang.hbtd.gameserver.module.title.type.TitleStatus;
import cn.daxiang.hbtd.gameserver.module.title.type.TitleType;
import cn.daxiang.hbtd.gameserver.module.user.helper.ActorPushHelper;
import cn.daxiang.protocol.game.TypeProtocol;
import cn.daxiang.shared.event.EventKey;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Collections;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2020/2/5
 */
@Component
public class TitleFacadeImpl extends GameBaseFacade implements TitleFacade, OnEventListener {
    @Autowired
    private TitleDao titleDao;
    @Autowired
    private TitleContext titleContext;

    @Override
    public Title getTitle(long actorId) {
        return titleDao.getTitle(actorId);
    }

    @Override
    public Result createTitle(long actorId, TitleConfig titleConfig) {
        Title title = this.getTitle(actorId);
        Collection<TitleEntity> defaultTitles = Lists.newArrayList();
        switch (titleConfig.getTitleType()) {
            case TIME_LIMIT:
                if (titleConfig.getTitleConditionType() != TitleConditionType.NONE) {
                    Collection<TitleConfig> titleConfigList = TitleConfigService.getTitleConfigList(titleConfig.getTitleConditionType());
                    for (TitleConfig config : titleConfigList) {
                        title.getTimeLimitTitles().remove(config.getTitleId());
                    }
                }
                TitleEntity limitEntity = TitleEntity.valueOf(titleConfig.getTitleId(), titleConfig.getExpirationTime());
                title.getTimeLimitTitles().put(limitEntity.getTitleId(), limitEntity);
                this.checkTitleId(title);
                this.addDelayTask(title);
                break;
            case GOODS:
                TitleEntity goodsEntity = title.getTitleEntity(titleConfig.getTitleId());
                if (goodsEntity == null) {
                    goodsEntity = TitleEntity.valueOf(titleConfig.getTitleId());
                    title.getDefaultTitles().put(goodsEntity.getTitleId(), goodsEntity);
                } else {
                    goodsEntity.setTimes(goodsEntity.getTimes() + 1);
                }
                defaultTitles.add(goodsEntity);
                break;
            default:
                TitleEntity titleEntity = TitleEntity.valueOf(titleConfig.getTitleId());
                title.getDefaultTitles().put(titleEntity.getTitleId(), titleEntity);
                defaultTitles.add(titleEntity);
                break;
        }
        dbQueue.updateQueue(title);
        DispatchHelper.postEvent(new LineupPowerRefreshEvent(title.getActorId()));
        TitlePushHelper.pushTitle(actorId, defaultTitles, title.getTimeLimitTitles().values());
        return Result.valueOf();
    }

    @Override
    public Result changeTitle(long actorId, int titleId) {
        Title title = this.getTitle(actorId);
        if (titleId != 0) {
            TitleEntity titleEntity = title.getTitleEntity(titleId);
            if (titleEntity == null) {
                return Result.valueOf(TITLE_NOT_FOUND);
            }
        }
        title.setTitleId(titleId);
        dbQueue.updateQueue(title);
        ActorPushHelper.pushActorAttribute(actorId, TypeProtocol.ActorFieldType.TITLE_ID);
        return Result.valueOf();
    }

    @Event(name = EventKey.TITLE_CREATE_EVENT)
    public void onTitleCreateEvent(TitleCreateEvent e) {
        this.createTitle(e.actorId, e.titleConfig);
    }

    @Event(name = EventKey.TITLE_EXPIRATION_EVENT)
    public void onTitleExpirationEvent(GameEvent e) {
        Title title = this.getTitle(e.getUniqueId());
        title.clean();
        this.checkTitleId(title);
        DispatchHelper.postEvent(new LineupPowerRefreshEvent(title.getActorId()));
        dbQueue.updateQueue(title);
        this.addDelayTask(title);
        TitlePushHelper.pushTitle(title.getActorId(), Collections.emptyList(), title.getTimeLimitTitles().values());
    }

    private void checkTitleId(Title title) {
        TitleEntity titleEntity = title.getTitleEntity(title.getTitleId());
        if (titleEntity == null) {
            title.setTitleId(0);
            ActorPushHelper.pushActorAttribute(title.getActorId(), TypeProtocol.ActorFieldType.TITLE_ID);
        }
    }

    @EventOnline(trigger = TriggerTime.FIXED_HOUR, triggerValue = 24)
    public void onFixedHour(GameEvent e) {
        this.reset(e.getUniqueId(), true);
    }

    @Event(name = EventKey.ACTOR_LOGIN)
    public void onLogin(GameEvent e) {
        this.reset(e.getUniqueId(), false);
    }

    private void reset(long actorId, boolean isPush) {
        Title title = this.getTitle(actorId);
        if (DateUtils.isToday(title.getLastResetTime())) {
            return;
        }
        Collection<TitleEntity> defaultTitleList = Lists.newArrayList();
        Collection<TitleEntity> refreshList = title.refresh(TitleConfigService.getTitleConfigList(TitleType.ACHIEVE));
        defaultTitleList.addAll(refreshList);
        for (TitleEntity titleEntity : title.getDefaultTitles().values()) {
            if (titleEntity.getStatus() == TitleStatus.FINISH) {
                continue;
            }
            TitleConfig titleConfig = TitleConfigService.getTitleConfig(titleEntity.getTitleId());
            if (titleConfig == null || !titleConfig.getTitleConditionType().isDaily()) {
                continue;
            }
            titleEntity.setValue(0L);
            defaultTitleList.add(titleEntity);
        }
        title.setLastResetTime(System.currentTimeMillis());
        dbQueue.updateQueue(title);
        if (isPush) {
            TitlePushHelper.pushTitle(title.getActorId(), defaultTitleList, title.getTimeLimitTitles().values());
        }
    }

    private void addDelayTask(Title title) {
        long expirationTime = title.getExpirationTime();
        if (expirationTime > 0) {
            ActorDelayEvent event = new TitleExpirationEvent(title.getActorId());
            schedule.addDelayTask(event, new Runnable() {
                @Override
                public void run() {
                    DispatchHelper.postEvent(event);
                }
            }, expirationTime);
        }
    }

    @Override
    public void onEvent(GameEvent event) {
        Collection<TitleEntity> refreshTitleList = Lists.newArrayList();
        long actorId = event.getUniqueId();
        Title title = this.getTitle(actorId);
        Collection<TitleConditionType> types = titleContext.getTypes(event.name);
        if (types != null) {
            for (TitleConditionType conditionType : types) {
                TitleParser parser = titleContext.getParser(conditionType);
                if (parser == null) {
                    LOGGER.error("TitleParser not found, conditionType:{}", conditionType);
                    continue;
                }
                Collection<TitleConfig> titleConfigList = TitleConfigService.getTitleConfigList(conditionType);
                if (titleConfigList == null) {
                    continue;
                }
                for (TitleConfig titleConfig : titleConfigList) {
                    TitleEntity titleEntity = title.getDefaultTitles().get(titleConfig.getTitleId());
                    if (titleEntity == null || titleEntity.getStatus() != TitleStatus.PROGRESS) {
                        continue;
                    }
                    if (parser.parser(event, titleEntity, titleConfig)) {
                        refreshTitleList.add(titleEntity);
                    }
                }
            }
        }
        if (!refreshTitleList.isEmpty()) {
            if (refreshTitleList.stream().filter(e -> e.getStatus() == TitleStatus.FINISH).count() > 0) {
                DispatchHelper.postEvent(new LineupPowerRefreshEvent(title.getActorId()));
            }
            dbQueue.updateQueue(title);
            TitlePushHelper.pushTitle(title.getActorId(), refreshTitleList, title.getTimeLimitTitles().values());
        }
    }

    @Override
    public void registerEvent(Set<String> eventSet) {
        eventSet.addAll(titleContext.getEvents());
    }
}
