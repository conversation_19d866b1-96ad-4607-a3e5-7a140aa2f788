package cn.daxiang.hbtd.gameserver.module.runes.helper;

import cn.daxiang.hbtd.gameserver.core.database.table.Runes;
import cn.daxiang.hbtd.gameserver.core.database.table.RunesLineup;
import cn.daxiang.hbtd.gameserver.module.runes.model.RunesExtra;
import cn.daxiang.hbtd.gameserver.module.runes.model.RunesLineupEntity;
import cn.daxiang.protocol.game.CommonProtocol;
import cn.daxiang.protocol.game.RunesProtocol;
import cn.daxiang.protocol.game.RunesProtocol.RunesInfoResponse;

import java.util.Collection;
import java.util.Map;

/**
 * @Author: Gary
 * @Date 2022-12-01 10:21
 * @Description:
 */
public class RunesHelper {
    //    public static GemstoneInfoResponse buildGemstoneInfoResponse(Collection<Gemstone> gemstone) {
    //        GemstoneInfoResponse.Builder gemstoneInfoResponseBuilder = GemstoneInfoResponse.newBuilder();
    //        gemstoneInfoResponseBuilder.addAllGemstone(gemstone);
    //        return gemstoneInfoResponseBuilder.build();
    //    }
    public static RunesInfoResponse buildRunesInfoResponse(Collection<Runes> runesList) {
        RunesInfoResponse.Builder builder = RunesInfoResponse.newBuilder();
        for (Runes runes : runesList) {
            builder.addRunes(buildRunes(runes));
        }
        return builder.build();
    }

    public static CommonProtocol.Runes buildRunes(Runes runes) {
        CommonProtocol.Runes.Builder builder = CommonProtocol.Runes.newBuilder();
        builder.setRunesId(runes.getRunesUid());
        builder.setConfigId(runes.getConfigId());
        for (RunesExtra runesExtra : runes.getRunesExtraList()) {
            builder.addExtra(buildRunesExtra(runesExtra));
        }
        builder.setIsAppraise(runes.isHasAppraised());
        builder.setNum(runes.getNum());
        return builder.build();
    }

    public static CommonProtocol.RunesExtra buildRunesExtra(RunesExtra runesExtra) {
        CommonProtocol.RunesExtra.Builder builder = CommonProtocol.RunesExtra.newBuilder();
        builder.setType(runesExtra.getType());
        builder.setValue(runesExtra.getValue());
        return builder.build();
    }

    public static RunesProtocol.RunesLineupInfoResponse buildRunesLineupInfoResponse(RunesLineup runesLineup) {
        RunesProtocol.RunesLineupInfoResponse.Builder builder = RunesProtocol.RunesLineupInfoResponse.newBuilder();
        for (Map.Entry<Integer, RunesLineupEntity> entry : runesLineup.getRunesLineupMap().entrySet()) {
            builder.putRunesLineup(entry.getKey(), buildRunesLineupEntity(entry.getValue()));
        }
        return builder.build();
    }

    public static RunesProtocol.RunesLineupEntity buildRunesLineupEntity(RunesLineupEntity entity) {
        RunesProtocol.RunesLineupEntity.Builder runesLineupEntityBuilder = RunesProtocol.RunesLineupEntity.newBuilder();
        runesLineupEntityBuilder.setFosterLevel(entity.getFosterLevel());
        runesLineupEntityBuilder.putAllFosterAttribute(entity.getFosterAttributeMap());
        runesLineupEntityBuilder.setRune(entity.getRuneId());
        //        runesLineupEntityBuilder.putAllGemstone(gemstone);
        return runesLineupEntityBuilder.build();
    }

}
