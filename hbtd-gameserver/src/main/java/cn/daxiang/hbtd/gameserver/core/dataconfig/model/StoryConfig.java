package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.dataconfig.annotation.FieldIgnore;
import cn.daxiang.framework.extend.KeyValue;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.framework.utils.RandomUtils;
import cn.daxiang.protocol.game.TypeProtocol.ResourceId;
import cn.daxiang.protocol.game.TypeProtocol.RewardType;
import cn.daxiang.shared.reward.RewardObject;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 关卡配置
 *
 * <AUTHOR>
 */
@DataFile(fileName = "story_config")
public class StoryConfig implements ModelAdapter {
    /**
     * 关卡ID
     * 出怪GateId
     * {@code PveProduceMonsterConfig.gateId}
     */
    private int storyId;
    /**
     * 章节ID
     */
    private int chapterId;
    /**
     * 章节类型
     * {@code StoryChapterType}
     */
    private int chapterType;
    /**
     * 关卡类型
     * {@code StoryType}
     */
    private int storyType;
    /**
     * 上一关卡ID
     */
    private int preStoryId;
    /**
     * 下一关（gm工具）
     */
    private int nextStoryId;
    /**
     * 等级限制
     */
    private int levelLimit;
    /**
     * 推荐战力
     */
    private long rollPower;
    /**
     * 挑战次数
     */
    private int challengeTimes;
    /**
     * 基础获得金币(这里分开配置是前端要求方便显示)
     */
    private int baseDropGold;
    /**
     * 基础获得经验
     */
    private int baseDropEXP;
    /**
     * 随机掉落奖励(根据权重必出)
     * [[[[[rewardType,id,num],[rewardType,id,num]],weight],[[[rewardType,id,num],[rewardType,id,num]],weight]],[[[[rewardType,id,num],[rewardType,id,num]],weight],[[[rewardType,id,num],[rewardType,id,num]],weight]]]
     */
    private String randomDropWeight;
    /**
     * 随机掉落奖励(根据概率有可能出,基数10000)
     * [[[[[rewardType,id,num],[rewardType,id,num]],probability],[[[rewardType,id,num],[rewardType,id,num]],probability]],[[[[rewardType,id,num],[rewardType,id,num]],probability],[[[rewardType,id,num],[rewardType,id,num]],probability]]]
     */
    private String randomDropProbability;

    /**
     * 首胜奖励([[rewardType,id,num],[rewardType,id,num]])
     * {@code RewardType}
     */
    private String firstdrop;
    /**
     * 战斗ID
     */
    private int battleId;
    /**
     * 碾压战力
     */
    private long passPower;
    /**
     * 防作弊验证，通关必须达到战力
     */
    private long mustPower;

    @FieldIgnore
    private List<RewardObject> baseDropRewardList = Lists.newArrayList();
    @FieldIgnore
    private Map<Collection<RewardObject>, Integer> randomDropWeightMap = Maps.newHashMap();
    @FieldIgnore
    private Collection<KeyValue<Collection<RewardObject>, Integer>> randomDropProbabilityList = Lists.newArrayList();
    @FieldIgnore
    private List<RewardObject> firstRewardList = Lists.newArrayList();

    public static void main(String[] args) {
        // [[[[[[rewardType,id,num],[rewardType,id,num]],weight],[[[rewardType,id,num],[rewardType,id,num]],weight]]],[[[[rewardType,id,num],[rewardType,id,num]],weight],[[[rewardType,id,num],
        // [rewardType,id,num]],weight]]]
        Collection<Map<Collection<RewardObject>, Integer>> randomRewardList = Lists.newArrayList();
        // [[[rewardType,id,num],[rewardType,id,num]],weight]
        // [[[[[11,1,1],[12,2,2]],1000],[[[11,1,11],[12,2,12]],2000]
        // [[[[[11,1,1],[12,1,2]],1000],[[[11,1,11],[12,1,12]],1000]],[[[[13,1,3],[14,1,4]],1000],[[[13,1,13],[14,2,14]],1000]]]
        // [[[[[11,1,11],[12,2,12]],2000],[[[11,1,11],[12,2,12]],2000]],[[[[11,1,11],[12,2,12]],2000],[[[11,1,11],[12,2,12]],2000]]]
        // [[[[[rewardType,id,num],[rewardType,id,num]],weight],[[[rewardType,id,num],[rewardType,id,num]],weight]],[[[[rewardType,id,num],[rewardType,id,num]],weight],[[[rewardType,id,num],
        // [rewardType,id,num]],weight]]]
        String text = "[[[[[11,1,1],[12,1,2]],1000],[[[11,1,11],[12,1,12]],1000]],[[[[13,1,3],[14,1,4]],1000],[[[13,1,13],[14,2,14]],1000]]]";
        text = "[[[[[11,1,11],[12,2,12]],2000],[[[11,1,11],[12,2,12]],2000]],[[[[11,1,11],[12,2,12]],2000],[[[11,1,11],[12,2,12]],2000]]]";
        JSONArray randomDropArray = JSONArray.parseArray(text);
        for (Object randomDropItem : randomDropArray) {
            JSONArray randomArray = JSONArray.parseArray(randomDropItem.toString());
            Map<Collection<RewardObject>, Integer> randomRewardMap = Maps.newHashMap();
            for (Object randomItem : randomArray) {
                JSONArray array = JSONArray.parseArray(randomItem.toString());
                JSONArray rewardArray = JSONArray.parseArray(array.getString(0));
                Collection<RewardObject> rewardList = Lists.newArrayListWithCapacity(rewardArray.size());
                for (Object rewardItem : rewardArray) {
                    JSONArray rewardItemArray = JSONArray.parseArray(rewardItem.toString());
                    RewardObject rewardObject = RewardObject.valueOf(rewardItemArray);
                    rewardList.add(rewardObject);
                }
                randomRewardMap.put(rewardList, array.getInteger(1));
            }
            randomRewardList.add(randomRewardMap);
        }
    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(storyId);
    }

    public int getStoryId() {
        return storyId;
    }

    public int getChapterId() {
        return chapterId;
    }

    public int getPreStoryId() {
        return preStoryId;
    }

    public int getNextStoryId() {
        return nextStoryId;
    }

    public int getChapterType() {
        return chapterType;
    }

    public int getStoryType() {
        return storyType;
    }

    public long getRollPower() {
        return rollPower;
    }

    public List<RewardObject> getBossRewards() {
        List<RewardObject> rewards = Lists.newArrayList();
        rewards.addAll(baseDropRewardList);
        if (!randomDropWeightMap.isEmpty()) {
            Collection<RewardObject> weightList = RandomUtils.randomByWeight(randomDropWeightMap);
            rewards.addAll(weightList);
        }
        for (KeyValue<Collection<RewardObject>, Integer> entry : randomDropProbabilityList) {
            if (RandomUtils.is10000Hit(entry.getValue())) {
                rewards.addAll(entry.getKey());
            }
        }
        return rewards;
    }

    public int getChallengeTimes() {
        return challengeTimes;
    }

    public int getBaseDropGold() {
        return baseDropGold;
    }

    public int getBaseDropEXP() {
        return baseDropEXP;
    }

    public int getLevelLimit() {
        return levelLimit;
    }

    public List<RewardObject> getFirstRewardList() {
        return firstRewardList;
    }

    @Override
    public void initialize() {
        if (baseDropGold > 0) {
            RewardObject gold = RewardObject.valueOf(RewardType.RESOURCE.getNumber(), ResourceId.GOLD.getNumber(), baseDropGold);
            baseDropRewardList.add(gold);
            firstRewardList.add(gold);
        }
        if (baseDropEXP > 0) {
            RewardObject exp = RewardObject.valueOf(RewardType.RESOURCE.getNumber(), ResourceId.EXP.getNumber(), baseDropEXP);
            baseDropRewardList.add(exp);
            firstRewardList.add(exp);
        }

        if (!"".equals(randomDropWeight)) {
            JSONArray randomDropWeightArray = JSONArray.parseArray(randomDropWeight);
            if (!randomDropWeightArray.isEmpty()) {
                for (Object randomDropItem : randomDropWeightArray) {
                    JSONArray randomArray = JSONArray.parseArray(randomDropItem.toString());
                    for (Object randomItem : randomArray) {
                        JSONArray array = JSONArray.parseArray(randomItem.toString());
                        JSONArray rewardArray = JSONArray.parseArray(array.getString(0));
                        Collection<RewardObject> rewardList = Lists.newArrayListWithCapacity(rewardArray.size());
                        for (Object rewardItem : rewardArray) {
                            JSONArray rewardItemArray = JSONArray.parseArray(rewardItem.toString());
                            RewardObject rewardObject = RewardObject.valueOf(rewardItemArray);
                            rewardList.add(rewardObject);
                        }
                        randomDropWeightMap.put(rewardList, array.getInteger(1));
                    }
                }
            }
        }
        if (!"".equals(randomDropProbability)) {
            JSONArray randomDropProbabilityArray = JSONArray.parseArray(randomDropProbability);
            if (!randomDropProbabilityArray.isEmpty()) {
                for (Object randomDropItem : randomDropProbabilityArray) {
                    JSONArray randomArray = JSONArray.parseArray(randomDropItem.toString());

                    for (Object randomItem : randomArray) {
                        JSONArray array = JSONArray.parseArray(randomItem.toString());
                        JSONArray rewardArray = JSONArray.parseArray(array.getString(0));
                        Collection<RewardObject> rewardList = Lists.newArrayListWithCapacity(rewardArray.size());
                        for (Object rewardItem : rewardArray) {
                            JSONArray rewardItemArray = JSONArray.parseArray(rewardItem.toString());
                            RewardObject rewardObject = RewardObject.valueOf(rewardItemArray);
                            rewardList.add(rewardObject);
                        }
                        randomDropProbabilityList.add(new KeyValue<>(rewardList, array.getInteger(1)));
                    }
                }
            }
        }
        if (!"".equals(firstdrop)) {
            JSONArray firstRewardArray = JSONArray.parseArray(firstdrop);
            for (Object firstRewardItem : firstRewardArray) {
                JSONArray rewardArray = JSONArray.parseArray(firstRewardItem.toString());
                RewardObject rewardObject = RewardObject.valueOf(rewardArray);
                firstRewardList.add(rewardObject);
            }
        }
    }

    public long getPassPower() {
        return passPower;
    }

    public int getBattleId() {
        return battleId;
    }

    public long getMustPower() {
        return mustPower;
    }
}
