package cn.daxiang.hbtd.gameserver.core.dataconfig.service;

import cn.daxiang.framework.dataconfig.ConfigServiceAdapter;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.CrossCupBetConfig;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.TreeMap;

/**
 * <AUTHOR>
 * @date 2020/2/12
 */
@Component
public class CrossCupConfigService extends ConfigServiceAdapter {
    /**
     * key:state,value:CrossCupBetConfig
     */
    private static TreeMap<Integer, CrossCupBetConfig> CROSS_CUP_BET_CONFIG_MAP = Maps.newTreeMap();

    public static CrossCupBetConfig getCrossCupBetConfig(int state) {
        return CROSS_CUP_BET_CONFIG_MAP.get(state);
    }

    @Override
    protected void initialize() {
        Collection<CrossCupBetConfig> listAll = dataConfig.listAll(this, CrossCupBetConfig.class);
        for (CrossCupBetConfig config : listAll) {
            CROSS_CUP_BET_CONFIG_MAP.put(config.getState(), config);
        }
    }

    @Override
    protected void clean() {
        CROSS_CUP_BET_CONFIG_MAP.clear();
    }
}
