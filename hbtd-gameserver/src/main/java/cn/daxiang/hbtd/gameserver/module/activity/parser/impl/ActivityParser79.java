package cn.daxiang.hbtd.gameserver.module.activity.parser.impl;

import cn.daxiang.framework.event.GameEvent;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.hbtd.gameserver.core.database.table.ActivityGlobal;
import cn.daxiang.hbtd.gameserver.core.database.table.ActivityRecord;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.Activity69CreateConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.Activity71OrderConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.ActivityOpenConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.Activity71ConfigService;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.ActivityOpenConfigService;
import cn.daxiang.hbtd.gameserver.core.dispatch.DispatchHelper;
import cn.daxiang.hbtd.gameserver.core.event.impl.ActorRechargeEvent;
import cn.daxiang.hbtd.gameserver.core.event.impl.MailAddEvent;
import cn.daxiang.hbtd.gameserver.core.oss.type.OperationType;
import cn.daxiang.hbtd.gameserver.module.activity.model.entity.record.ActivityRecord71;
import cn.daxiang.hbtd.gameserver.module.activity.parser.AbstractActivityParser;
import cn.daxiang.hbtd.gameserver.module.activity.type.ActivityStatus;
import cn.daxiang.hbtd.gameserver.module.activity.type.ActivityType;
import cn.daxiang.hbtd.gameserver.module.goods.helper.RewardHelper;
import cn.daxiang.hbtd.gameserver.module.mail.type.MailTemplateType;
import cn.daxiang.hbtd.gameserver.module.user.helper.ActorHelper;
import cn.daxiang.hbtd.gameserver.module.user.type.ChargeType;
import cn.daxiang.protocol.game.ActivityInfoProtocol;
import cn.daxiang.protocol.game.ActivityInfoProtocol.Activity71RequestValue;
import cn.daxiang.protocol.game.CommonProtocol.RewardResult;
import cn.daxiang.shared.reward.RewardObject;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.protobuf.ByteString;
import com.google.protobuf.InvalidProtocolBufferException;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;

import static cn.daxiang.shared.GameModuleStatusCodeConstant.ACTIVITY_NOT_FINISH_FOR_RECEIVED;
import static cn.daxiang.shared.GameModuleStatusCodeConstant.ACTIVITY_REWARD_HAD_RECEIVED;
import static cn.daxiang.shared.GameModuleStatusCodeConstant.CONFIG_NOT_FOUND;
import static cn.daxiang.shared.GameModuleStatusCodeConstant.INVALID_PARAM;

/**
 * 霓裳罗衣战令
 *
 * <AUTHOR>
 * @date 2025/5/22
 */
@Component
public class ActivityParser79 extends AbstractActivityParser {
    @Override
    protected ActivityType getType() {
        return ActivityType.ACTIVITY_TYPE_79;
    }

    @Override
    public TResult<RewardResult> receiveReward(long actorId, int activityId, int id, byte[] value) {
        //获取活动配置
        ActivityOpenConfig activityOpenConfig = ActivityOpenConfigService.getActivityConfig(activityId);
        if (activityOpenConfig == null) {
            LOGGER.error("ActivityOpenConfig not found,activityId:{}", activityId);
            return TResult.valueOf(CONFIG_NOT_FOUND);
        }
        int data = activityOpenConfig.getData();

        int openDays = ActivityOpenConfigService.getActivityOpenDay(activityId);
        // 活动个人数据
        ActivityRecord record = activityRecordDao.getActivityRecord(actorId, activityId);
        ActivityRecord71 record71;
        if (record == null) {
            record71 = new ActivityRecord71();
            record = activityRecordDao.create(actorId, activityId, JSON.toJSONString(record71));
        }
        record71 = JSON.parseObject(record.getRecord(), ActivityRecord71.class);
        Collection<RewardObject> rewardList = Lists.newArrayList();
        TreeMap<Integer, Activity71OrderConfig> configsMap = Activity71ConfigService.getOrderConfigsMap(data);
        if (configsMap == null) {
            return TResult.valueOf(CONFIG_NOT_FOUND);
        }
        if (id == 0) {
            for (Activity71OrderConfig config : configsMap.values()) {
                if (openDays < config.getDays()) {
                    continue;
                }
                if (!record71.getNormalReceives().contains(config.getDays())) {
                    rewardList.addAll(config.getNormalRewardList());
                    record71.addNormalReceives(config.getDays());
                }
                if (!record71.getMediumReceives().contains(config.getDays()) && record71.isActiveMedium()) {
                    rewardList.addAll(config.getMediumRewardList());
                    record71.addMediumReceives(config.getDays());
                }
                if (!record71.getSupremeReceives().contains(config.getDays()) && record71.isActiveSupreme()) {
                    rewardList.addAll(config.getSupremeRewardList());
                    record71.addSupremeReceives(config.getDays());
                }
            }
        } else {
            Activity71OrderConfig config = configsMap.get(id);
            if (config == null) {
                return TResult.valueOf(CONFIG_NOT_FOUND);
            }
            Activity71RequestValue request;
            try {
                request = Activity71RequestValue.parseFrom(value);
            } catch (InvalidProtocolBufferException e) {
                return TResult.valueOf(INVALID_PARAM);
            }
            if (openDays < config.getDays()) {
                return TResult.valueOf(ACTIVITY_NOT_FINISH_FOR_RECEIVED);
            }
            if (request.getTypeId() == 1) {
                if (record71.getNormalReceives().contains(id)) {
                    return TResult.valueOf(ACTIVITY_REWARD_HAD_RECEIVED);
                }
                rewardList.addAll(config.getNormalRewardList());
                record71.addNormalReceives(id);
            } else if (request.getTypeId() == 2 && record71.isActiveMedium()) {
                if (record71.getMediumReceives().contains(id)) {
                    return TResult.valueOf(ACTIVITY_REWARD_HAD_RECEIVED);
                }
                rewardList.addAll(config.getMediumRewardList());
                record71.addMediumReceives(id);
            } else if (request.getTypeId() == 3 && record71.isActiveSupreme()) {
                if (record71.getSupremeReceives().contains(id)) {
                    return TResult.valueOf(ACTIVITY_REWARD_HAD_RECEIVED);
                }
                rewardList.addAll(config.getSupremeRewardList());
                record71.addSupremeReceives(id);
            }
        }
        if (rewardList.isEmpty()) {
            return TResult.valueOf(ACTIVITY_REWARD_HAD_RECEIVED);
        }
        RewardResult result = RewardHelper.sendRewardList(actorId, rewardList, OperationType.ACTIVITY_TYPE_71);
        record.setRecord(JSON.toJSONString(record71));
        dbQueue.updateQueue(record);
        return TResult.sucess(result);
    }

    @Override
    protected void onActivityOpen(Collection<ActivityOpenConfig> openActivityIds) {

    }

    @Override
    protected void onActivityEnd(Collection<ActivityOpenConfig> endActivityIds) {
        for (ActivityOpenConfig activityOpenConfig : endActivityIds) {
            int activityId = activityOpenConfig.getId();
            int data = activityOpenConfig.getData();
            TreeMap<Integer, Activity71OrderConfig> configsMap = Activity71ConfigService.getOrderConfigsMap(data);
            if (configsMap == null) {
                continue;
            }
            Collection<Long> activityActorIds = activityRecordDao.getActorIds(activityId);
            for (Long actorId : activityActorIds) {
                ActivityRecord record = activityRecordDao.getActivityRecord(actorId, activityOpenConfig.getId());
                if (record == null) {
                    continue;
                }
                ActivityRecord71 record71 = JSONObject.parseObject(record.getRecord(), ActivityRecord71.class);
                if (!record71.isActiveMedium() && !record71.isActiveSupreme()) {
                    continue;
                }
                Collection<RewardObject> rewardList = Lists.newArrayList();
                for (Activity71OrderConfig config : configsMap.values()) {
                    if (!record71.getMediumReceives().contains(config.getDays()) && record71.isActiveMedium()) {
                        rewardList.addAll(config.getMediumRewardList());
                    }
                    if (!record71.getSupremeReceives().contains(config.getDays()) && record71.isActiveSupreme()) {
                        rewardList.addAll(config.getSupremeRewardList());
                    }
                }
                if (rewardList.isEmpty()) {
                    continue;
                }
                rewardList = RewardHelper.groupByTypeAndId(rewardList);
                // 不补发宝藏探秘消耗物品
                Activity69CreateConfig baseConfig = globalConfigService.findConfig(activityOpenConfig.getData(), Activity69CreateConfig.class);
                if (baseConfig != null) {
                    rewardList.removeIf(next -> baseConfig.getCostList().stream().anyMatch(cost -> cost.getId() == next.getId()));
                }
                sendRewardMail(actorId, MailTemplateType.ACTIVITY_79_RECHARGE_UNCLAIMED_REWARD, rewardList);
            }
        }
    }

    /**
     * 未领取战令补发
     *
     * @param actorId
     * @param mailTemplateType
     * @param rewards
     */
    private void sendRewardMail(Long actorId, MailTemplateType mailTemplateType, Collection<RewardObject> rewards) {
        if (rewards.isEmpty()) {
            return;
        }
        Map<String, String> params = Maps.newHashMap();
        params.put("name", ActorHelper.getActorName(actorId));
        MailAddEvent mailAddEvent = new MailAddEvent(actorId, mailTemplateType, params, rewards);
        DispatchHelper.postEvent(mailAddEvent);
        LOGGER.info("Send Activity79 Reward complete mailTemplateType:{}, actorId:{}", mailTemplateType, actorId);
    }

    @Override
    public TResult<ByteString> getRecord2Client(long actorId, int activityId) {
        ActivityRecord activityRecord = activityRecordDao.getActivityRecord(actorId, activityId);
        ActivityInfoProtocol.Activity71Record.Builder builder = ActivityInfoProtocol.Activity71Record.newBuilder();
        if (activityRecord == null) {
            ActivityOpenConfig activityOpenConfig = ActivityOpenConfigService.getActivityConfig(activityId);
            if (activityOpenConfig == null) {
                LOGGER.error("ActivityOpenConfig not found,activityId:{}", activityId);
                return TResult.valueOf(CONFIG_NOT_FOUND);
            }
            ActivityRecord71 record71 = new ActivityRecord71();
            activityRecord = activityRecordDao.create(actorId, activityId, JSONObject.toJSONString(record71));
            activityRecord.setRecord(JSONObject.toJSONString(record71));
            dbQueue.updateQueue(activityRecord);
        }
        int openDays = ActivityOpenConfigService.getActivityOpenDay(activityId);
        ActivityRecord71 record71 = JSONObject.parseObject(activityRecord.getRecord(), ActivityRecord71.class);
        builder.setDays(openDays);
        builder.setIsActiveMedium(record71.isActiveMedium());
        builder.setIsActiveSupreme(record71.isActiveSupreme());
        builder.addAllNormalReceives(record71.getNormalReceives());
        builder.addAllMediumReceives(record71.getMediumReceives());
        builder.addAllSupremeReceives(record71.getSupremeReceives());

        return TResult.sucess(builder.build().toByteString());
    }

    @Override
    public TResult<ByteString> getGlobal2Client(long actorId, int activityId) {
        return TResult.fail();
    }

    @Override
    public void registerEvent(Set<String> eventSet) {
        eventSet.addAll(getType().getEventNames());
    }

    @Override
    public void onEvent(GameEvent event) {
        Collection<ActivityOpenConfig> configList = ActivityOpenConfigService.getActivityOpenConfigList(getType());
        if (configList.isEmpty()) {
            return;
        }
        ActorRechargeEvent actorRechargeEvent = event.convert();
        if (actorRechargeEvent.getChargeType() != ChargeType.CHARGE_DIRECT_PURCHASING) {
            return;
        }
        long actorId = actorRechargeEvent.getActorId();
        for (ActivityOpenConfig activityOpenConfig : configList) {
            int activityId = activityOpenConfig.getId();
            if (!isActivityOpen(activityId)) {
                continue;
            }
            boolean isMedium = Activity71ConfigService.isMediumCharge(actorRechargeEvent.getChargeId());
            boolean isSupreme = Activity71ConfigService.isSupremeCharge(actorRechargeEvent.getChargeId());
            if (!isMedium && !isSupreme) {
                continue;
            }
            ActivityRecord activityRecord = activityRecordDao.getActivityRecord(actorId, activityId);
            if (activityRecord == null) {
                ActivityRecord71 record71 = new ActivityRecord71();
                activityRecord = activityRecordDao.create(actorId, activityId, JSONObject.toJSONString(record71));
                activityRecord.setRecord(JSONObject.toJSONString(record71));
                dbQueue.updateQueue(activityRecord);
            }
            ActivityRecord71 record71 = JSONObject.parseObject(activityRecord.getRecord(), ActivityRecord71.class);
            if (isMedium) {
                record71.setActiveMedium(isMedium);
            }
            if (isSupreme) {
                record71.setActiveSupreme(isSupreme);
            }
            activityRecord.setRecord(JSONObject.toJSONString(record71));
            dbQueue.updateQueue(activityRecord);
            this.pushActivity(actorId, activityId, activityOpenConfig.getActivityType());
        }
    }
}
