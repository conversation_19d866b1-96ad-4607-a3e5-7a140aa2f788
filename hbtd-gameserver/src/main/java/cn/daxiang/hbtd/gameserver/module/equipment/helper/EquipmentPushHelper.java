package cn.daxiang.hbtd.gameserver.module.equipment.helper;

import cn.daxiang.framework.protocal.DataPacket;
import cn.daxiang.hbtd.gameserver.core.database.table.Equipment;
import cn.daxiang.hbtd.gameserver.core.netty.channel.PlayerChannel;
import cn.daxiang.protocol.game.EquipmentProtocol.EquipmentCmd;
import cn.daxiang.protocol.game.EquipmentProtocol.EquipmentDeleteResponse;
import cn.daxiang.protocol.game.EquipmentProtocol.EquipmentInfoResponse;
import cn.daxiang.protocol.game.ModuleProtocol.Module;

import java.util.Collection;

/**
 * 装备信息推送帮助类
 *
 * <AUTHOR>
 */
public class EquipmentPushHelper {
    /**
     * 推送装备列表
     *
     * @param actorId
     * @param equipmentList
     */
    public static void pushEquipmentList(long actorId, Collection<Equipment> equipmentList) {
        EquipmentInfoResponse.Builder builder = EquipmentInfoResponse.newBuilder();
        for (Equipment equipment : equipmentList) {
            builder.addEquipment(EquipmentHelper.buildEquipment(equipment));
        }
        EquipmentInfoResponse response = builder.build();
        DataPacket packet = DataPacket.valueOf(Module.EQUIPMENT_VALUE, EquipmentCmd.PUSH_EQUIPMENT_LIST_VALUE, response);
        PlayerChannel.push(actorId, packet);
    }

    /**
     * 推送删除装备列表
     *
     * @param actorId
     * @param equipmentIds
     */
    public static void pushEquipmentDelete(long actorId, Collection<Long> equipmentIds) {
        EquipmentDeleteResponse.Builder builder = EquipmentDeleteResponse.newBuilder();
        builder.addAllEquipmentId(equipmentIds);
        EquipmentDeleteResponse response = builder.build();
        DataPacket packet = DataPacket.valueOf(Module.EQUIPMENT_VALUE, EquipmentCmd.PUSH_EQUIPMENT_DELETE_LIST_VALUE, response);
        PlayerChannel.push(actorId, packet);
    }
}
