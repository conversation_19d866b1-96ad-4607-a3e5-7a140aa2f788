package cn.daxiang.hbtd.gameserver.module.activity.parser;

import cn.daxiang.framework.event.GameEvent;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.hbtd.gameserver.core.database.table.ActivityRecord;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.ActivityCumulativeRechargeConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.ActivityOpenConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.ActivityCumulativeRechargeConfigService;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.ActivityOpenConfigService;
import cn.daxiang.hbtd.gameserver.core.dispatch.DispatchHelper;
import cn.daxiang.hbtd.gameserver.core.event.impl.ActorRechargeEvent;
import cn.daxiang.hbtd.gameserver.core.event.impl.MailAddEvent;
import cn.daxiang.hbtd.gameserver.core.oss.type.OperationType;
import cn.daxiang.hbtd.gameserver.module.activity.model.entity.record.ActivityRecord9;
import cn.daxiang.hbtd.gameserver.module.goods.helper.RewardHelper;
import cn.daxiang.hbtd.gameserver.module.mail.type.MailTemplateType;
import cn.daxiang.hbtd.gameserver.module.user.helper.ActorHelper;
import cn.daxiang.protocol.game.ActivityInfoProtocol;
import cn.daxiang.protocol.game.CommonProtocol;
import cn.daxiang.shared.reward.RewardObject;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.protobuf.ByteString;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static cn.daxiang.shared.GameModuleStatusCodeConstant.ACTIVITY_DATA_ERROR;
import static cn.daxiang.shared.GameModuleStatusCodeConstant.ACTIVITY_NOT_FINISH_FOR_RECEIVED;
import static cn.daxiang.shared.GameModuleStatusCodeConstant.ACTIVITY_REWARD_HAD_RECEIVED;
import static cn.daxiang.shared.GameModuleStatusCodeConstant.CONFIG_NOT_FOUND;

/**
 * 累计充值 相同类型 [9, 41, 42, 43, 44, 45]
 *
 * <AUTHOR>
 */
public abstract class AbstractActivityParser9 extends AbstractActivityParser {
    @Override
    public void registerEvent(Set<String> eventSet) {
        eventSet.addAll(getType().getEventNames());
    }

    protected TResult<CommonProtocol.RewardResult> receiveReward(long actorId, int activityId, int id, byte[] value, OperationType operationType) {
        ActivityOpenConfig activityOpenConfig = ActivityOpenConfigService.getActivityConfig(activityId);
        if (activityOpenConfig == null) {
            LOGGER.error("ActivityOpenConfig not found, id:{}", activityId);
            return TResult.valueOf(CONFIG_NOT_FOUND);
        }
        Map<Integer, ActivityCumulativeRechargeConfig> configMap = ActivityCumulativeRechargeConfigService.getActivityCumulativeRechargeConfigMap(activityOpenConfig.getData());
        if (configMap == null) {
            LOGGER.error("ActivityDailyPreferentialConfig is null activityId:{} data:{}", activityOpenConfig.getId(), activityOpenConfig.getData());
            return TResult.valueOf(CONFIG_NOT_FOUND);
        }

        ActivityCumulativeRechargeConfig config = globalConfigService.findConfig(IdentiyKey.build(activityOpenConfig.getData(), id), ActivityCumulativeRechargeConfig.class);
        if (config == null) {
            LOGGER.error("ActivityDailyPreferentialConfig not found, activityId:{} data:{} id:{}", activityId, activityOpenConfig.getData(), id);
            return TResult.valueOf(CONFIG_NOT_FOUND);
        }

        ActivityRecord record = activityRecordDao.getActivityRecord(actorId, activityId);
        if (record == null) {
            return TResult.valueOf(ACTIVITY_DATA_ERROR);
        }
        ActivityRecord9 activityRecord = JSON.parseObject(record.getRecord(), ActivityRecord9.class);
        if (activityRecord.getReceivedList().contains(id)) {
            return TResult.valueOf(ACTIVITY_REWARD_HAD_RECEIVED);
        }

        long recharge = activityRecord.getRecharge();
        if (recharge < config.getCumulative()) {
            return TResult.valueOf(ACTIVITY_NOT_FINISH_FOR_RECEIVED);
        }

        activityRecord.addReceived(id);
        record.setRecord(JSON.toJSONString(activityRecord));
        dbQueue.updateQueue(record);
        CommonProtocol.RewardResult result = RewardHelper.sendRewardList(actorId, config.getRewardList(), operationType);
        return TResult.sucess(result);
    }

    @Override
    public void onEvent(GameEvent event) {
        Collection<ActivityOpenConfig> configList = ActivityOpenConfigService.getActivityOpenConfigList(getType());
        if (configList.isEmpty()) {
            return;
        }
        ActorRechargeEvent actorRechargeEvent = event.convert();
        // 判断充值是否计入累充
        if (!actorRechargeEvent.isCount()) {
            return;
        }
        long actorId = actorRechargeEvent.getActorId();
        for (ActivityOpenConfig activityOpenConfig : configList) {
            int activityId = activityOpenConfig.getId();
            if (isActivityOpen(activityId) == false) {
                continue;
            }
            Map<Integer, ActivityCumulativeRechargeConfig> configMap = ActivityCumulativeRechargeConfigService.getActivityCumulativeRechargeConfigMap(activityOpenConfig.getData());
            if (configMap == null) {
                LOGGER.error("ActivityDailyPreferentialConfig is null activityId:{} data:{}", activityOpenConfig.getId(), activityOpenConfig.getData());
                return;
            }
            ActivityRecord9 activityRecord;
            ActivityRecord record = activityRecordDao.getActivityRecord(actorId, activityId);
            if (record == null) {
                activityRecord = new ActivityRecord9();
                record = activityRecordDao.create(actorId, activityId, JSON.toJSONString(activityRecord));
            } else {
                activityRecord = JSON.parseObject(record.getRecord(), ActivityRecord9.class);
            }

            activityRecord.addRecharge(actorRechargeEvent.getRecharge());
            record.setRecord(JSON.toJSONString(activityRecord));
            dbQueue.updateQueue(record);
            this.pushActivity(actorId, activityId, activityOpenConfig.getActivityType());
        }
    }

    @Override
    protected void onActivityOpen(Collection<ActivityOpenConfig> openActivityIds) {

    }

    protected void onActivityEnd(Collection<ActivityOpenConfig> endActivityIds, MailTemplateType mailTemplateType) {
        // 发送未领取的奖励邮件
        for (ActivityOpenConfig activityOpenConfig : endActivityIds) {
            int activityId = activityOpenConfig.getId();
            List<Long> actorIds = activityRecordDao.getActorIds(activityId);
            for (Long actorId : actorIds) {
                rewardClear(actorId, activityOpenConfig, mailTemplateType);
            }
        }
    }

    @Override
    public TResult<ByteString> getRecord2Client(long actorId, int activityId) {
        ActivityRecord record = activityRecordDao.getActivityRecord(actorId, activityId);
        ActivityInfoProtocol.Activity9Record.Builder builder = ActivityInfoProtocol.Activity9Record.newBuilder();
        if (record != null) {
            ActivityRecord9 activityRecord = JSON.parseObject(record.getRecord(), ActivityRecord9.class);
            builder.setRecharge(activityRecord.getRecharge());
            builder.addAllReceives(activityRecord.getReceivedList());
        }
        return TResult.sucess(builder.build().toByteString());
    }

    @Override
    public TResult<ByteString> getGlobal2Client(long actorId, int activityId) {
        return TResult.fail();
    }

    private void rewardClear(Long actorId, ActivityOpenConfig activityOpenConfig, MailTemplateType mailTemplateType) {
        ActivityRecord record = activityRecordDao.getActivityRecord(actorId, activityOpenConfig.getId());
        if (record == null) {
            return;
        }
        ActivityRecord9 activityRecord = JSON.parseObject(record.getRecord(), ActivityRecord9.class);
        if (activityRecord.getRecharge() <= 0) {
            return;
        }
        Collection<RewardObject> activityRewards = Lists.newArrayList();

        Map<Integer, ActivityCumulativeRechargeConfig> configMap = ActivityCumulativeRechargeConfigService.getActivityCumulativeRechargeConfigMap(activityOpenConfig.getData());
        if (configMap == null) {
            LOGGER.error("ActivityCumulativeRechargeConfig is null data:{}", activityOpenConfig.getData());
            return;
        }

        for (ActivityCumulativeRechargeConfig config : configMap.values()) {
            // 是否领取过
            if (activityRecord.getReceivedList().contains(config.getId())) {
                continue;
            }
            // 是否满足条件
            if (activityRecord.getRecharge() < config.getCumulative()) {
                continue;
            }
            activityRecord.addReceived(config.getId());
            activityRewards.addAll(config.getRewardList());
        }

        if (activityRewards.isEmpty()) {
            return;
        }
        record.setRecord(JSON.toJSONString(activityRecord));
        dbQueue.updateQueue(record);
        sendRewardMail(actorId, activityRewards, mailTemplateType);
    }

    private void sendRewardMail(Long actorId, Collection<RewardObject> rewards, MailTemplateType mailTemplateType) {
        Map<String, String> params = Maps.newHashMap();
        params.put("name", ActorHelper.getActorName(actorId));
        MailAddEvent mailAddEvent = new MailAddEvent(actorId, mailTemplateType, params, rewards);
        DispatchHelper.postEvent(mailAddEvent);
        LOGGER.info("Send Reward complete, ActivityType:{} ,mailTemplateTypeId:{}, actorId:{}", getType(), mailTemplateType.getId(), actorId);
    }
}
