package cn.daxiang.hbtd.gameserver.module.activity.model.entity.record;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.common.collect.Lists;

import java.util.Collection;
import java.util.Map;

/**
 * 招兵买马
 * HBTD\doc\策划文档\已评审文档\beta5.5\活动_招兵买马-vien.xlsx
 *
 * <AUTHOR>
 * @date 2022/6/6
 */
public class ActivityRecord14 {
    /**
     * key:type,value:当前类型的积累的活动值
     */
    private Map<Integer, Integer> valueMap;
    /**
     * 已领取列表
     */
    private Collection<Integer> receiveList = Lists.newArrayList();

    public static ActivityRecord14 valueOf(Map<Integer, Integer> value) {
        ActivityRecord14 record = new ActivityRecord14();
        record.valueMap = value;
        return record;
    }

    public boolean recieve(int id) {
        boolean isSuccess = false;
        if (!receiveList.contains(id)) {
            this.receiveList.add(id);
            isSuccess = true;
        }
        return isSuccess;
    }

    public Map<Integer, Integer> getValueMap() {
        return valueMap;
    }

    public void setValueMap(Map<Integer, Integer> valueMap) {
        this.valueMap = valueMap;
    }

    public Collection<Integer> getReceiveList() {
        return receiveList;
    }

    public void setReceiveList(Collection<Integer> receiveList) {
        this.receiveList = receiveList;
    }

    @JSONField(serialize = false)
    public Integer getValue(int type) {
        Integer value = valueMap.get(type);
        if (value == null) {
            value = 0;
            valueMap.put(type, 0);
        }
        return value;
    }

    @JSONField(serialize = false)
    public void putValue(int type, int value) {
        valueMap.put(type, value);
    }
}
