package cn.daxiang.hbtd.gameserver.module.chat.parser.info.impl;

import cn.daxiang.framework.event.GameEvent;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.SystemMsgConfig;
import cn.daxiang.hbtd.gameserver.core.event.impl.ImmortalsColorLevelUpEvent;
import cn.daxiang.hbtd.gameserver.module.chat.helper.ChatHelper;
import cn.daxiang.hbtd.gameserver.module.chat.model.ChatInfoReceiver;
import cn.daxiang.hbtd.gameserver.module.chat.parser.info.AbstractChatInfoParser;
import cn.daxiang.protocol.game.ChatInfoProtocol.ImmortalsColorLeveUpChatInfo;
import cn.daxiang.protocol.game.ChatProtocol.ChatInfo;
import cn.daxiang.protocol.game.TypeProtocol.ChatChannelType;
import cn.daxiang.protocol.game.TypeProtocol.ChatInfoType;
import cn.daxiang.shared.event.EventKey;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Component
public class ImmortalsColorLevelUpChatInfoParser extends AbstractChatInfoParser {

    @Override
    public Map<ChatInfoReceiver, ChatInfo> parse(GameEvent e) {
        SystemMsgConfig msgConfig = globalConfigService.findConfig(IdentiyKey.build(ChatInfoType.IMMORTALS_COLOR_LEVE_UP_CHAT_INFO_VALUE), SystemMsgConfig.class);
        ImmortalsColorLevelUpEvent event = e.convert();
        if (event.level == 0) {
            return Collections.emptyMap();
        }
        if (!msgConfig.execute(event.level)) {
            return Collections.emptyMap();
        }
        Map<ChatInfoReceiver, ChatInfo> chatMap = Maps.newHashMap();
        ImmortalsColorLeveUpChatInfo colorLeveUpChatInfo = ImmortalsColorLeveUpChatInfo.newBuilder().setImmortalsId(event.immortalsId).setImmortalsColorLevel(event.level).build();
        Collection<Long> actorIds = Lists.newLinkedList();
        actorIds.add(event.actorId);
        ChatInfo chatInfo = ChatHelper.buildChatInfo(ChatInfoType.IMMORTALS_COLOR_LEVE_UP_CHAT_INFO, event.getActorId(), colorLeveUpChatInfo.toByteArray(), actorIds);
        chatMap.put(ChatInfoReceiver.valueOf(ChatChannelType.CHAT_CHANNEL_TYPE_SYSTEM), chatInfo);
        return chatMap;
    }

    @Override
    protected List<String> getEventName() {
        return Lists.newArrayList(EventKey.IMMORTALS_COLOR_LEVEL_UP_EVENT);
    }

}
