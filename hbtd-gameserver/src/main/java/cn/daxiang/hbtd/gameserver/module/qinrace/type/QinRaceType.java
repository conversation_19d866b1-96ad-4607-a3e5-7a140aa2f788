package cn.daxiang.hbtd.gameserver.module.qinrace.type;

import cn.daxiang.hbtd.gameserver.module.qinrace.model.QinRace1Record;
import cn.daxiang.hbtd.gameserver.module.qinrace.model.QinRace2Record;
import cn.daxiang.hbtd.gameserver.module.qinrace.model.QinRace3Record;
import cn.daxiang.hbtd.gameserver.module.title.type.TitleConditionType;
import cn.daxiang.shared.GlobalConfigKey;
import cn.daxiang.shared.type.PVEVerifyType;

/**
 * 挑战玩法类型
 *
 * <AUTHOR>
 * @date 2023/6/15
 */
public enum QinRaceType {
    /**
     * 0.指的段位模式玩法
     */
    NONE(0, true, null, null, null, null, null),
    /**
     * 1.无限火力
     */
    QINRACE_INFINITE_FIREPOWER(1, false, QinRace1Record.class, GlobalConfigKey.QIN_RACE_FIRE_END_HP, GlobalConfigKey.QIN_RACE_FIRE_INIT_CURRENCY,
        TitleConditionType.QINRACE_TYPE_1_RANK, PVEVerifyType.QIN_RACE_1),
    /**
     * 2.生存挑战
     */
    QINRACE_SURVIVAL_CHALLENGE(2, false, QinRace2Record.class, GlobalConfigKey.QIN_RACE_SURVIVAL_END_HP, GlobalConfigKey.QIN_RACE_SURVIVAL_INIT_CURRENCY,
        TitleConditionType.QINRACE_TYPE_2_RANK, PVEVerifyType.QIN_RACE_2),
    /**
     * 3.大富翁
     */
    QINRACE_RICH(3, true, QinRace3Record.class, GlobalConfigKey.QIN_RACE_RICH_END_HP, GlobalConfigKey.QIN_RACE_RICH_INIT_CURRENCY, TitleConditionType.QINRACE_TYPE_3_RANK,
        PVEVerifyType.QIN_RACE_3),
    /**
     * 4.火力全开
     */
    QINRACE_FULL(4, false, QinRace3Record.class, GlobalConfigKey.QIN_RACE_FULL_END_HP, GlobalConfigKey.QIN_RACE_FULL_INIT_CURRENCY, TitleConditionType.QINRACE_TYPE_4_RANK,
        PVEVerifyType.QIN_RACE_4),
    ;
    private int id;
    /**
     * 玩法是否支持玩家自己升级
     */
    private boolean isLevelUp;
    /**
     * 保存实体类的class
     */
    private Class clazz;
    /**
     * 初始血量配置
     */
    private GlobalConfigKey initHp;
    /**
     * 初始货币配置
     */
    private GlobalConfigKey initCurrency;
    /**
     * 称号类型
     */
    private TitleConditionType titleConditionType;
    /**
     * PVE验证类型
     */
    private PVEVerifyType pveVerifyType;

    private QinRaceType(int id, boolean isUp, Class clazz, GlobalConfigKey initHp, GlobalConfigKey initCurrency, TitleConditionType titleConditionType,
        PVEVerifyType pveVerifyType) {
        this.id = id;
        this.isLevelUp = isUp;
        this.clazz = clazz;
        this.initHp = initHp;
        this.initCurrency = initCurrency;
        this.titleConditionType = titleConditionType;
        this.pveVerifyType = pveVerifyType;
    }

    public static QinRaceType getType(int id) {
        for (QinRaceType type : QinRaceType.values()) {
            if (type.id == id) {
                return type;
            }
        }
        return NONE;
    }

    public int getId() {
        return id;
    }

    public boolean isLevelUp() {
        return isLevelUp;
    }

    public Class getClazz() {
        return clazz;
    }

    public GlobalConfigKey getInitHp() {
        return initHp;
    }

    public GlobalConfigKey getInitCurrency() {
        return initCurrency;
    }

    public TitleConditionType getTitleConditionType() {
        return titleConditionType;
    }

    public PVEVerifyType getPveVerifyType() {
        return pveVerifyType;
    }
}
