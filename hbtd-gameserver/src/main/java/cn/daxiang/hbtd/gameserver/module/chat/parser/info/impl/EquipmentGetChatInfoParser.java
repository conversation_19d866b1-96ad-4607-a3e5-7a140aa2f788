package cn.daxiang.hbtd.gameserver.module.chat.parser.info.impl;

import cn.daxiang.framework.event.GameEvent;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.SystemMsgConfig;
import cn.daxiang.hbtd.gameserver.core.event.impl.EquipmentGetEvent;
import cn.daxiang.hbtd.gameserver.module.chat.helper.ChatHelper;
import cn.daxiang.hbtd.gameserver.module.chat.model.ChatInfoReceiver;
import cn.daxiang.hbtd.gameserver.module.chat.parser.info.AbstractChatInfoParser;
import cn.daxiang.protocol.game.ChatInfoProtocol;
import cn.daxiang.protocol.game.ChatProtocol;
import cn.daxiang.protocol.game.TypeProtocol;
import cn.daxiang.shared.event.EventKey;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Component
public class EquipmentGetChatInfoParser extends AbstractChatInfoParser {
    @Override
    protected List<String> getEventName() {
        return Lists.newArrayList(EventKey.EQUIPMENT_GET_EVENT);
    }

    @Override
    public Map<ChatInfoReceiver, ChatProtocol.ChatInfo> parse(GameEvent e) {
        EquipmentGetEvent event = e.convert();
        SystemMsgConfig config = globalConfigService.findConfig(IdentiyKey.build(TypeProtocol.ChatInfoType.EQUIPMENT_GET_CHAT_INFO_VALUE), SystemMsgConfig.class);

        if (config == null || !config.execute(event.operationType.getId(), event.configId, event.quality)) {
            return Collections.emptyMap();
        }

        Map<ChatInfoReceiver, ChatProtocol.ChatInfo> chatMap = Maps.newHashMap();
        ChatInfoProtocol.EquipmentGetChatInfo equipmentGetChatInfo =
            ChatInfoProtocol.EquipmentGetChatInfo.newBuilder().setConfigId(event.configId).setCount(event.count).setOperationType(event.operationType.getId()).build();

        Collection<Long> actorIds = Lists.newLinkedList();
        actorIds.add(event.actorId);
        ChatProtocol.ChatInfo chatInfo =
            ChatHelper.buildChatInfo(TypeProtocol.ChatInfoType.EQUIPMENT_GET_CHAT_INFO, event.getActorId(), equipmentGetChatInfo.toByteArray(), actorIds);
        chatMap.put(ChatInfoReceiver.valueOf(TypeProtocol.ChatChannelType.CHAT_CHANNEL_TYPE_SYSTEM), chatInfo);
        return chatMap;
    }
}
