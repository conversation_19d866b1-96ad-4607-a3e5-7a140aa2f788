package cn.daxiang.hbtd.gameserver.module.activity.facade;

import cn.daxiang.framework.result.TResult;
import cn.daxiang.hbtd.gameserver.core.database.table.ActivityActorValue;
import cn.daxiang.protocol.game.ActivityProtocol.ActivityActionResponse;
import cn.daxiang.protocol.game.ActivityProtocol.ActivityInfoResponse;

public interface ActivityFacade {
    /**
     * 获取活动信息
     *
     * @param actorId
     * @return
     */
    TResult<ActivityInfoResponse> getActivityInfo(long actorId);

    /**
     * 获取单个活动信息
     *
     * @param actorId
     * @param activityId
     * @return
     */
    TResult<ActivityInfoResponse> getActivityInfo(long actorId, int activityId);

    /**
     * 执行动作
     *
     * @param actorId
     * @param activityId
     * @param id
     * @param value
     * @return
     */
    TResult<ActivityActionResponse> action(long actorId, int activityId, int id, byte[] value);

    /**
     * 判断活动是否开启
     *
     * @param activityId
     * @return
     */
    boolean isActivityOpen(int activityId);

    /**
     * 获取活动留存记录
     * @param actorId
     * @param activityType
     * @return
     */
    TResult<ActivityActorValue> getActivityActorValue(long actorId, int activityType);

    /**
     * 更新活动留存记录
     */
    void updateActivityActorValue(long actorId, int activityType, String record);


}
