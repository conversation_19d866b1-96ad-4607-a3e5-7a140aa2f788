package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.dataconfig.annotation.FieldIgnore;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.framework.utils.RandomUtils;
import cn.daxiang.shared.module.lineup.SpriteAttributeType;
import cn.daxiang.shared.reward.RewardObject;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Maps;

import java.util.Map;

/**
 * 已鉴定兵符表
 *
 * <AUTHOR>
 * @date 2022/12/21
 */
@DataFile(fileName = "runes_identify_config")
public class RunesIdentifyConfig implements ModelAdapter {
    /**
     * 配置Id
     */
    private int id;
    /**
     * 阵营
     */
    private int camp;
    /**
     * 阶段
     */
    private int stage;
    /**
     * 品质
     */
    private int quality;
    /**
     * 条件角色等级
     */
    private int condition;
    /**
     * 基础属性
     */
    private String base_effect;
    /**
     * 基础分
     */
    private int base_score;
    /**
     * 鉴定权重
     */
    private String add_effect_weight;
    /**
     * 分解产物
     */
    private String decompose;
    /**
     * 计算评分表达式
     */
    private String scoreExpr;
    /**
     * 基础属性数值加成Map
     * key:SpriteAttributeType,value:upValue
     */
    @FieldIgnore
    private Map<SpriteAttributeType, Long> attributeMap = Maps.newHashMap();
    /**
     * 鉴定随机属性
     */
    @FieldIgnore
    private Map<Integer, Integer> weightMap = Maps.newHashMap();
    /**
     * 分解产物
     */
    @FieldIgnore
    private RewardObject decomposeReward;

    @Override
    public void initialize() {
        JSONArray attributeArray = JSON.parseArray(base_effect);
        for (Object attributeItem : attributeArray) {
            JSONArray array = JSONArray.parseArray(attributeItem.toString());
            attributeMap.put(SpriteAttributeType.getType(array.getIntValue(0)), array.getLong(1));
        }

        JSONArray weightArray = JSON.parseArray(add_effect_weight);
        for (Object weightItem : weightArray) {
            JSONArray array = JSONArray.parseArray(weightItem.toString());
            weightMap.put(array.getIntValue(0), array.getIntValue(1));
        }

        JSONArray costArray = JSONArray.parseArray(decompose);
        decomposeReward = RewardObject.valueOf(costArray);
    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(id);
    }

    public int getId() {
        return id;
    }

    public int getCamp() {
        return camp;
    }

    public int getStage() {
        return stage;
    }

    public int getQuality() {
        return quality;
    }

    public int getCondition() {
        return condition;
    }

    public int getBaseScore() {
        return base_score;
    }

    public Map<SpriteAttributeType, Long> getAttributeMap() {
        return attributeMap;
    }

    public RewardObject getDecomposeReward() {
        return decomposeReward;
    }

    public int randomExtraType() {
        Integer extraType = RandomUtils.randomByWeight(weightMap);
        return extraType;
    }

    public String getScoreExpr() {
        return scoreExpr;
    }
}
