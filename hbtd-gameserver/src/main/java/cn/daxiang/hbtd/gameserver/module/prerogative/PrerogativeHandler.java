package cn.daxiang.hbtd.gameserver.module.prerogative;

import cn.daxiang.framework.dispatch.DispatchType;
import cn.daxiang.framework.protocal.DataPacket;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.framework.router.annotation.Cmd;
import cn.daxiang.hbtd.gameserver.core.database.table.Prerogative;
import cn.daxiang.hbtd.gameserver.core.router.GatewayRouterHandlerImpl;
import cn.daxiang.hbtd.gameserver.module.PbBuilder;
import cn.daxiang.hbtd.gameserver.module.prerogative.facade.PrerogativeFacade;
import cn.daxiang.hbtd.gameserver.module.prerogative.model.entity.PrerogativeEntity;
import cn.daxiang.protocol.game.ModuleProtocol.Module;
import cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeCmd;
import cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeInfoResponse;
import cn.daxiang.protocol.game.PrerogativeProtocol.PrerogativeList;
import io.netty.channel.Channel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Map.Entry;

@Component
public class PrerogativeHandler extends GatewayRouterHandlerImpl {

    @Autowired
    private PrerogativeFacade prerogativeFacade;

    @Override
    public int getModule() {
        return Module.PREROGATIVE_VALUE;
    }

    @Cmd(Id = PrerogativeCmd.GET_PREROGATIVE_LIST_VALUE, dispatchType = DispatchType.ACTOR)
    public void getPrerogativeList(Channel channel, Long actorId, DataPacket packet) {
        TResult<Prerogative> result = prerogativeFacade.getPrerogative(actorId);
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }

        PrerogativeInfoResponse.Builder builder = PrerogativeInfoResponse.newBuilder();
        for (Entry<Integer, Map<Integer, PrerogativeEntity>> entry : result.item.getPrerogativeMap().entrySet()) {
            PrerogativeList.Builder list = PrerogativeList.newBuilder();
            for (PrerogativeEntity entity : entry.getValue().values()) {
                list.addPrerogatives(PbBuilder.buildPrerogative(entity));
            }
            PrerogativeList prerogativeList = list.build();
            builder.putPrerogativeMap(entry.getKey(), prerogativeList);
        }
        PrerogativeInfoResponse response = builder.build();
        channelWrite(channel, packet, response);
    }
}