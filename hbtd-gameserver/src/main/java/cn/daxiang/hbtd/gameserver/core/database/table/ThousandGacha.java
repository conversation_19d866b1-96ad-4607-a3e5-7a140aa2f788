package cn.daxiang.hbtd.gameserver.core.database.table;

import cn.daxiang.framework.database.SingleEntity;
import cn.daxiang.framework.database.annotation.Column;
import cn.daxiang.framework.database.annotation.DBQueueType;
import cn.daxiang.framework.database.annotation.Table;
import cn.daxiang.framework.identity.IdentiyKey;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * 1000抽活动
 *
 * <AUTHOR>
 * @date 2023/11/6
 */
@Table(name = "thousand_gacha", type = DBQueueType.IMPORTANT)
public class ThousandGacha extends SingleEntity<Long> {
    /**
     * 角色ID
     */
    @Column(pk = true)
    private long actorId;
    /**
     * 第几天
     */
    @Column
    private int day;
    /**
     * 领取状态
     */
    @Column
    private boolean receive;
    /**
     * 重置时间
     */
    @Column
    private long lastResetTime;

    @Override
    public IdentiyKey findPkId() {
        return IdentiyKey.build(actorId);
    }

    @Override
    public void setPkId(IdentiyKey pk) {
        this.actorId = pk.getFirstLongId();
    }

    @Override
    public List<IdentiyKey> keyLists() {
        return Lists.newArrayList(findPkId());
    }

    public long getActorId() {
        return actorId;
    }

    public void setActorId(long actorId) {
        this.actorId = actorId;
    }

    public int getDay() {
        return day;
    }

    public void setDay(int day) {
        this.day = day;
    }

    public boolean isReceive() {
        return receive;
    }

    public void setReceive(boolean receive) {
        this.receive = receive;
    }

    public long getLastResetTime() {
        return lastResetTime;
    }

    public void setLastResetTime(long lastResetTime) {
        this.lastResetTime = lastResetTime;
    }

    public void reset() {
        this.day += 1;
        this.receive = false;
        this.lastResetTime = System.currentTimeMillis();
    }

    public void addReceives() {
        this.receive = true;
        this.lastResetTime = System.currentTimeMillis();
    }
}
