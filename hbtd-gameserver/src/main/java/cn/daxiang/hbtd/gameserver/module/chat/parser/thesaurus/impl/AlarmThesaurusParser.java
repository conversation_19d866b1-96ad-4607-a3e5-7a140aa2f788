package cn.daxiang.hbtd.gameserver.module.chat.parser.thesaurus.impl;

import cn.daxiang.framework.result.TResult;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.ThesaurusConfigService;
import cn.daxiang.hbtd.gameserver.core.oss.GameOssLogger;
import cn.daxiang.hbtd.gameserver.module.chat.channel.ChatCacheContext;
import cn.daxiang.hbtd.gameserver.module.chat.helper.ChatHelper;
import cn.daxiang.hbtd.gameserver.module.chat.parser.thesaurus.AbstractThesaurusParser;
import cn.daxiang.hbtd.gameserver.module.system.helper.SystemHelper;
import cn.daxiang.protocol.game.ChatProtocol.ChatMessage;
import cn.daxiang.protocol.game.TypeProtocol.ChatChannelType;
import cn.daxiang.shared.module.chat.type.ThesaurusType;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Comparator;
import java.util.Optional;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2022/5/26
 */
@Component
public class AlarmThesaurusParser extends AbstractThesaurusParser {
    private static final String FORMAT = "【频道】：%s\n【触发】：%s\n【次数】：%s\n【发言】：%s\n ----------------【历史】----------------\n%s";

    @Override
    protected ThesaurusType getType() {
        return ThesaurusType.ALARM;
    }

    @Override
    public void match(long actorId, ChatChannelType channelType, String message) {
        Optional<Pattern> optional = ThesaurusConfigService.match(getType(), message);
        if (!optional.isPresent()) {
            return;
        }
        TResult<Integer> result = ChatHelper.addAlarm(actorId);
        StringBuilder historyChat = new StringBuilder();
        Collection<ChatMessage> historyChatList = ChatCacheContext.getHistoryChat(actorId);
        historyChatList.stream().sorted(Comparator.comparing(ChatMessage::getTime).reversed()).limit(3L)
            .forEach(e -> historyChat.append(e.getChatInfo().getValue().toStringUtf8()).append("\n"));
        SystemHelper.operations(actorId, String.format(FORMAT, channelType, optional.get(), result.item, message, historyChat));
        GameOssLogger.thesaurusMatch(actorId, getType(), optional.get().toString(), message);
    }

}
