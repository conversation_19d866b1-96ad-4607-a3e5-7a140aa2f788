package cn.daxiang.hbtd.gameserver.module.activity.type;

/**
 * <AUTHOR>
 * @date 2022/7/21
 */
public enum Activity27PriceType {
    /**
     * 1-元购购买
     */
    DIAMOND_BUY(1),
    /**
     * 2-直购
     */
    DIRECT_PURCHASING_BUY(2),

    /**
     * 0.none
     */
    NONE(0);

    private int id;

    private Activity27PriceType(int id) {
        this.id = id;
    }

    public static Activity27PriceType getType(int id) {
        for (Activity27PriceType type : Activity27PriceType.values()) {
            if (type.id == id) {
                return type;
            }
        }
        return NONE;
    }

    public int getId() {
        return id;
    }
}
