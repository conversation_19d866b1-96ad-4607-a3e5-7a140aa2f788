package cn.daxiang.hbtd.gameserver.module.battle.parser.trigger.impl;

import cn.daxiang.hbtd.gameserver.core.dataconfig.model.SkillConfig;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleRoom;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleSprite;
import cn.daxiang.hbtd.gameserver.module.battle.parser.trigger.AbstractSkillTriggerParser;
import cn.daxiang.hbtd.gameserver.module.battle.type.SkillTriggerType;
import org.springframework.stereotype.Component;

/**
 * 敌方释放技能时触发
 *
 * <AUTHOR>
 * @date 2023/8/2
 */
@Component
public class EnemyHeroCastSkillTriggerParser extends AbstractSkillTriggerParser {
    @Override
    protected SkillTriggerType getType() {
        return SkillTriggerType.ENEMY_HERO_CAST;
    }

    @Override
    public boolean trigger(BattleRoom battleRoom, BattleSprite battleSprite, BattleSprite caster, BattleSprite target, long triggerValue, SkillConfig triggerSkill,
        SkillConfig skillConfig) {
        int totalTimes = caster.getCastTimes();
        return skillConfig.isTrigger(totalTimes);
    }
}
