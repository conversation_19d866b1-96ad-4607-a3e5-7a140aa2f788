package cn.daxiang.hbtd.gameserver.core.database.table;

import cn.daxiang.framework.database.SingleEntity;
import cn.daxiang.framework.database.annotation.Column;
import cn.daxiang.framework.database.annotation.DBQueueType;
import cn.daxiang.framework.database.annotation.Table;
import cn.daxiang.framework.identity.IdentiyKey;
import com.google.common.collect.Lists;

import java.util.Collection;
import java.util.List;

/**
 * 群雄争霸个人信息
 *
 * <AUTHOR>
 * @date 2022/10/15
 */
@Table(name = "hegemony_actor", type = DBQueueType.IMPORTANT)
public class HegemonyActor extends SingleEntity<Long> {
    /**
     * 角色ID
     */
    @Column(pk = true)
    private long actorId;
    /**
     * 分数
     */
    @Column
    private int score;
    /**
     * 段位
     */
    @Column
    private int dan;
    /**
     * 本赛季历史最高段位
     */
    @Column
    private int maxDan;
    /**
     * 每日匹配次数
     */
    @Column
    private int dayBattleTimes;
    /**
     * 今日剩余挑战次数
     */
    @Column
    private int surplusTimes;
    /**
     * 连败次数
     */
    @Column
    private int defeatTimes;
    /**
     * 每日购买次数
     */
    @Column
    private int buytimes;
    /**
     * 每日胜利次数（主动匹配胜利次数）
     */
    @Column
    private int dayVictoryTimes;
    /**
     * 每日奖励领取列表
     */
    @Column(alias = "dayRewardReceives")
    private Collection<Integer> dayRewardReceiveList = Lists.newArrayList();
    /**
     * 赛季匹配次数
     */
    @Column
    private int seasonBattleTimes;
    /**
     * 赛季胜利次数
     */
    @Column
    private int seasonVictoryTimes;
    /**
     * 赛季段位奖励领取列表
     */
    @Column(alias = "seasonReceives")
    private Collection<Integer> seasonReceiveList = Lists.newArrayList();
    /**
     * 连胜次数
     */
    @Column
    private int winStreak;
    /**
     * 上一赛季排名
     */
    @Column
    private int preRank;
    /**
     * 上一次排行榜刷新时间
     */
    @Column
    private long lastUpdateTime;
    /**
     * 上一次日重置时间
     */
    @Column
    private long lastDayResetTime;
    /**
     * 上一次赛季重置时间
     */
    @Column
    private long lastSeasonResetTime;
    /**
     * 是否推送过新赛季
     */
    @Column
    private boolean pushNewSeason;

    @Override
    public List<IdentiyKey> keyLists() {
        return Lists.newArrayList(findPkId());
    }

    @Override
    public IdentiyKey findPkId() {
        return IdentiyKey.build(actorId);
    }

    @Override
    public void setPkId(IdentiyKey pk) {
        this.actorId = pk.getFirstLongId();
    }

    public long getActorId() {
        return actorId;
    }

    public int getScore() {
        return score;
    }

    public void setScore(int score) {
        this.score = score;
    }

    public int getDan() {
        return dan;
    }

    public void setDan(int dan) {
        this.dan = dan;
    }

    public int getMaxDan() {
        return maxDan;
    }

    public void setMaxDan(int maxDan) {
        this.maxDan = maxDan;
    }

    public int getDayBattleTimes() {
        return dayBattleTimes;
    }

    public void setDayBattleTimes(int dayBattleTimes) {
        this.dayBattleTimes = dayBattleTimes;
    }

    public int getSurplusTimes() {
        return surplusTimes;
    }

    public void setSurplusTimes(int surplusTimes) {
        this.surplusTimes = surplusTimes;
    }

    public int getDefeatTimes() {
        return defeatTimes;
    }

    public void setDefeatTimes(int defeatTimes) {
        this.defeatTimes = defeatTimes;
    }

    public int getBuytimes() {
        return buytimes;
    }

    public void setBuytimes(int buytimes) {
        this.buytimes = buytimes;
    }

    public int getDayVictoryTimes() {
        return dayVictoryTimes;
    }

    public void setDayVictoryTimes(int dayVictoryTimes) {
        this.dayVictoryTimes = dayVictoryTimes;
    }

    public Collection<Integer> getDayRewardReceiveList() {
        return dayRewardReceiveList;
    }

    public void setDayRewardReceiveList(Collection<Integer> dayRewardReceiveList) {
        this.dayRewardReceiveList = dayRewardReceiveList;
    }

    public int getSeasonBattleTimes() {
        return seasonBattleTimes;
    }

    public void setSeasonBattleTimes(int seasonBattleTimes) {
        this.seasonBattleTimes = seasonBattleTimes;
    }

    public int getSeasonVictoryTimes() {
        return seasonVictoryTimes;
    }

    public void setSeasonVictoryTimes(int seasonVictoryTimes) {
        this.seasonVictoryTimes = seasonVictoryTimes;
    }

    public Collection<Integer> getSeasonReceiveList() {
        return seasonReceiveList;
    }

    public void setSeasonReceiveList(Collection<Integer> seasonReceiveList) {
        this.seasonReceiveList = seasonReceiveList;
    }

    public int getWinStreak() {
        return winStreak;
    }

    public void setWinStreak(int winStreak) {
        this.winStreak = winStreak;
    }

    public int getPreRank() {
        return preRank;
    }

    public void setPreRank(int preRank) {
        this.preRank = preRank;
    }

    public long getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(long lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public long getLastDayResetTime() {
        return lastDayResetTime;
    }

    public void setLastDayResetTime(long lastDayResetTime) {
        this.lastDayResetTime = lastDayResetTime;
    }

    public long getLastSeasonResetTime() {
        return lastSeasonResetTime;
    }

    public void setLastSeasonResetTime(long lastSeasonResetTime) {
        this.lastSeasonResetTime = lastSeasonResetTime;
    }

    public boolean isPushNewSeason() {
        return pushNewSeason;
    }

    public void setPushNewSeason(boolean pushNewSeason) {
        this.pushNewSeason = pushNewSeason;
    }

    public void resetDay(int surplusTimes, int storeLimit) {
        this.dayBattleTimes = 0;
        this.surplusTimes = Math.min(Math.max(this.surplusTimes, surplusTimes), storeLimit);
        this.buytimes = 0;
        this.dayVictoryTimes = 0;
        this.dayRewardReceiveList.clear();
        this.lastDayResetTime = System.currentTimeMillis();
    }

    public void resetSeason(int baseScore, int dan) {
        this.score = baseScore;
        this.dan = dan;
        this.maxDan = dan;
        this.defeatTimes = 0;
        this.seasonBattleTimes = 0;
        this.seasonVictoryTimes = 0;
        this.seasonReceiveList.clear();
        this.winStreak = 0;
        this.lastSeasonResetTime = System.currentTimeMillis();
        this.pushNewSeason = false;
    }

    public void refresh(int score, boolean isOffensive, boolean isVictory, int dan) {
        this.score = score;
        this.dan = dan;
        this.maxDan = Math.max(this.maxDan, dan);
        if (isOffensive) {
            this.dayBattleTimes++;
            this.surplusTimes--;
            this.seasonBattleTimes++;
            if (isVictory) {
                this.dayVictoryTimes++;
                this.seasonVictoryTimes++;
                this.winStreak++;
                this.defeatTimes = 0;
            } else {
                this.winStreak = 0;
                this.defeatTimes++;
            }
        }
    }

    public void buyTimes(int addTimes) {
        this.buytimes += addTimes;
        this.surplusTimes += addTimes;
    }

    /**
     * 日常奖励领取
     *
     * @param configId
     */
    public void receiveDailyReward(Collection<Integer> configIds) {
        this.dayRewardReceiveList.addAll(configIds);
    }

    /**
     * 赛季段位奖励
     *
     * @param configId
     */
    public void receiveDanReward(int configId) {
        this.seasonReceiveList.add(configId);
    }
}
