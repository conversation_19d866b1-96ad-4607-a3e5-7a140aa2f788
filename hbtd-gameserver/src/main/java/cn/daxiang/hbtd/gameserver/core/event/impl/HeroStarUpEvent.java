package cn.daxiang.hbtd.gameserver.core.event.impl;

import cn.daxiang.framework.event.ActorEvent;
import cn.daxiang.shared.event.EventKey;

/**
 * <AUTHOR>
 */
public class HeroStarUpEvent extends ActorEvent {
    /**
     * 英雄配置ID
     */
    public int configId;
    /**
     * 英雄品质
     */
    public int quality;
    /**
     * 英雄资质
     */
    public int aptitude;
    /**
     * 星级
     */
    public int starLevel;
    /**
     * 升星次数
     */
    public int starUpTimes;

    public HeroStarUpEvent(long actorId, int configId, int quality, int aptitude, int starLevel, int starUpTimes) {
        super(EventKey.HERO_STAR_UP_EVENT, actorId);
        this.configId = configId;
        this.quality = quality;
        this.aptitude = aptitude;
        this.starLevel = starLevel;
        this.starUpTimes = starUpTimes;
    }
}
