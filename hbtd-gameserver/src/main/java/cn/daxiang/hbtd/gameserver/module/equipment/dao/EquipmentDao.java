package cn.daxiang.hbtd.gameserver.module.equipment.dao;

import cn.daxiang.hbtd.gameserver.core.database.table.Equipment;
import cn.daxiang.hbtd.gameserver.core.oss.type.OperationType;

import java.util.Collection;

public interface EquipmentDao {

    /**
     * 获取装备列表
     *
     * @param actorId
     * @return
     */
    Collection<Equipment> getEquipmentList(long actorId);

    /**
     * 获取装备
     *
     * @param actorId
     * @param equipmentId
     * @return
     */
    Equipment getEquipment(long actorId, long equipmentId);

    /**
     * 创建装备
     *
     * @param actorId
     * @param configId
     * @param reinforceLevel
     * @return
     */
    Equipment createEquipment(long actorId, int configId, int reinforceLevel);

    /**
     * 删除装备列表
     *
     * @param actorId
     * @param equipmentIds
     */
    void deleteEquipment(long actorId, Collection<Long> equipmentIds, OperationType operationType);
}
