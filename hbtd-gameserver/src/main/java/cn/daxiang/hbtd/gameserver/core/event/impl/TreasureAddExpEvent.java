package cn.daxiang.hbtd.gameserver.core.event.impl;

import cn.daxiang.framework.event.ActorEvent;
import cn.daxiang.shared.event.EventKey;

/**
 * 宝物增加经验事件
 *
 * <AUTHOR>
 */
public class TreasureAddExpEvent extends ActorEvent {
    /**
     * 宝物ID
     */
    public long treasureId;
    /**
     * 宝物品质
     */
    public int quality;
    /**
     * 旧的等级
     */
    public int oldLevel;
    /**
     * 新的等级
     */
    public int newLevel;

    public TreasureAddExpEvent(long actorId, long treasureId, int quality, int oldLevel, int newLevel) {
        super(EventKey.TREASURE_ADD_EXP_EVENT, actorId);
        this.treasureId = treasureId;
        this.quality = quality;
        this.oldLevel = oldLevel;
        this.newLevel = newLevel;
    }
}
