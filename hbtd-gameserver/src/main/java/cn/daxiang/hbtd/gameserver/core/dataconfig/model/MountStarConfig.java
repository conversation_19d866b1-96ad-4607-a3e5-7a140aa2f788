package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.dataconfig.annotation.FieldIgnore;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.shared.module.lineup.SpriteAttributeType;
import cn.daxiang.shared.reward.RewardObject;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.Collection;
import java.util.Map;

/**
 * 坐骑升星表
 *
 * <AUTHOR>
 * @date 2024/4/8
 */
@DataFile(fileName = "mount_star_config")
public class MountStarConfig implements ModelAdapter {
    /**
     * 坐骑Id
     */
    private int mountId;
    /**
     * 星级
     */
    private int starLevel;
    /**
     * 消耗列表
     */
    private String cost;
    /**
     * 属性 读当前属性，即代表0级获得也具有属性
     * [[SpriteAttributeType,value],[SpriteAttributeType,value]]
     */
    private String attributes;
    /**
     * 坐骑技能ID
     */
    private String skillId;
    /**
     * 速度属性 读当前属性，即代表0级获得也具有属性
     * [[SpriteAttributeType,value],[SpriteAttributeType,value]]
     */
    private String speed;

    @FieldIgnore
    private Collection<RewardObject> costReward = Lists.newArrayList();
    /**
     * 属性Map
     * key:SpriteAttributeType,value:upValue
     */
    @FieldIgnore
    private Map<SpriteAttributeType, Long> attributeMap = Maps.newHashMap();
    /**
     * 所有技能列表
     */
    @FieldIgnore
    private Collection<Integer> skillIdList = Lists.newArrayList();
    /**
     * 速度属性Map
     * key:SpriteAttributeType,value:upValue
     */
    @FieldIgnore
    private Map<SpriteAttributeType, Long> speedAttributeMap = Maps.newHashMap();

    @Override
    public void initialize() {
        JSONArray rewardArray = JSONArray.parseArray(cost);
        for (Object reward : rewardArray) {
            JSONArray array = JSONArray.parseArray(reward.toString());
            RewardObject rewardObject = RewardObject.valueOf(array);
            costReward.add(rewardObject);
        }

        JSONArray attributesArray = JSONArray.parseArray(attributes);
        for (Object attributeItem : attributesArray) {
            JSONArray attributeArray = JSONArray.parseArray(attributeItem.toString());
            SpriteAttributeType spriteAttributeType = SpriteAttributeType.getType(attributeArray.getIntValue(0));
            if (spriteAttributeType.isTType()) {
                continue;
            }
            attributeMap.put(spriteAttributeType, attributeArray.getLong(1));
        }

        JSONArray skillIdArray = JSONArray.parseArray(skillId);
        for (Object id : skillIdArray) {
            skillIdList.add(Integer.parseInt(id.toString()));
        }

        JSONArray speedAttributesArray = JSONArray.parseArray(speed);
        for (Object attributeItem : speedAttributesArray) {
            JSONArray attributeArray = JSONArray.parseArray(attributeItem.toString());
            SpriteAttributeType spriteAttributeType = SpriteAttributeType.getType(attributeArray.getIntValue(0));
            if (spriteAttributeType.isTType()) {
                continue;
            }
            speedAttributeMap.put(spriteAttributeType, attributeArray.getLong(1));
        }
    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(mountId, starLevel);
    }

    public int getMountId() {
        return mountId;
    }

    public int getStarLevel() {
        return starLevel;
    }

    public Collection<RewardObject> getCostReward() {
        return costReward;
    }

    public Map<SpriteAttributeType, Long> getAttributeMap() {
        return attributeMap;
    }

    public Collection<Integer> getSkillIdList() {
        return skillIdList;
    }

    public Map<SpriteAttributeType, Long> getSpeedAttributeMap() {
        return speedAttributeMap;
    }
}
