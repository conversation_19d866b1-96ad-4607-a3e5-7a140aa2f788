package cn.daxiang.hbtd.gameserver.module.activity.type;

/**
 * <AUTHOR>
 * @date 2022/10/13
 */
public enum Activity2Type {
    /**
     * 0.none
     */
    NONE(0, false),
    /**
     * 1-新区战神(战力)
     */
    POWER_RANK(1, true),
    /**
     * 2闯关达人 (关卡)
     */
    STORY_RANK(2, true),
    /**
     * 3-战力达标
     */
    POWER_TARGET(3, false),
    /**
     * 4-主线星数达标
     */
    STORY_STAR(4, false),
    /**
     * 5-装备评分排行
     */
    EQUIPMENT_SCORE_RANK(5, true),
    /**
     * 6.装备评分达标
     */
    EQUIPMENT_SCORE_TARGET(6, false),
    /**
     * 7-宝物评分排行
     */
    TREASURE_SCORE_RANK(7, true),
    /**
     * 8-宝物评分达标
     */
    TREASURE_SCORE_TARGET(8, false),
    /**
     * 9-精英副本星数达标
     */
    ELITE_STORY_STAR_TARGET(9, false),
    /**
     * 10-名将塔通关层数达标
     */
    TOWER_LAYER_TARGET(10, false),
    /**
     * 11-专武图鉴值
     */
    IMMORTALS_MANUAL_VALUE_RANK(11, true),
    /**
     * 12-专武图鉴值达标
     */
    IMMORTALS_MANUAL_VALUE_TARGET(12, false),
    /**
     * 13-活动期间藏兵阁挑战金将次数达标
     */
    ARSENAL_CHALLENGE_TIMES(13, false),
    /**
     * 14.武将招募次数达标
     */
    HERO_RECRUIT_TIME_TARGET(14, false),
    /**
     * 15.名将招募次数达标
     */
    GREAT_HERO_RECRUIT_TIME_TARGET(15, false),
    /**
     * 16-活动期间获取的平定四方声望值进行排名
     */
    REPUTATION_RANK(16, true),
    /**
     * 17-活动期间完成的平定四方蓝色及以上品质事件X次
     */
    REPUTATION_POOR_EVENT_TARGET(17, false),
    /**
     * 18-活动期间完成的平定四方紫色及以上品质事件X次
     */
    REPUTATION_COMMON_EVENT_TARGET(18, false),
    /**
     * 19-活动期间完成的平定四方橙色及以上品质事件X次
     */
    REPUTATION_UNCOMMON_EVENT_TARGET(19, false),
    /**
     * 20-活动期间完成的平定四方红色及以上品质事件X次
     */
    REPUTATION_RARE_EVENT_TARGET(20, false),
    /**
     * 21-活动期间完成的平定四方金色及以上品质事件X次
     */
    REPUTATION_EPIC_EVENT_TARGET(21, false),
    ;

    private int id;

    private boolean isRankType;

    private Activity2Type(int id, boolean isRankType) {
        this.id = id;
        this.isRankType = isRankType;
    }

    public static Activity2Type getType(int id) {
        for (Activity2Type type : Activity2Type.values()) {
            if (type.id == id) {
                return type;
            }
        }
        return NONE;
    }

    public int getId() {
        return id;
    }

    public boolean isRankType() {
        return isRankType;
    }

    @Override
    public String toString() {
        return String.valueOf(id);
    }
}
