package cn.daxiang.hbtd.gameserver.core.dataconfig.convert;

import cn.daxiang.hbtd.gameserver.core.dataconfig.convert.callback.ConvertStringToMapCallback;
import com.google.common.collect.Maps;

import java.util.Map;
import java.util.TreeMap;

public abstract class TreeMapConfig<K extends Comparable<K>, V> implements IConfigable {

    protected TreeMap<K, V> cache = Maps.newTreeMap();

    @Override
    public void buildObject(final String config) {
        Map<K, V> map = StringConvert.convertToObjecTreeMap(new ConvertStringToMapCallback<K, V>() {

            @Override
            public String getTokenizerString() {
                return config;
            }

            @Override
            public int getSize() {
                return fromSize();
            }

            @Override
            public void fromArray(Map<K, V> map, String[] array) {
                map.put(fromKey(array), fromValue(array));
            }
        });
        this.cache.putAll(map);
    }

    /**
     * default is the first one
     *
     * @param key
     * @return
     */
    protected V findAscAchieve(K key) {
        V v = cache.firstEntry().getValue();
        return findAchieve(key, v);
    }

    /**
     * default is the last one
     *
     * @param key
     * @return
     */
    protected V findDescAchieve(K key) {
        V v = cache.lastEntry().getValue();
        return findAchieve(key, v);
    }

    /**
     * default is null
     *
     * @param key
     * @return
     */
    protected V findNullableAchieve(K key) {
        return findAchieve(key, null);
    }

    protected V findAchieve(K key, V defaultValue) {
        for (Map.Entry<K, V> entry : cache.entrySet()) {
            if (key.compareTo(entry.getKey()) >= 0) {
                defaultValue = entry.getValue();
            } else {
                break;
            }
        }
        return defaultValue;
    }

    protected int fromSize() {
        return 2;
    }

    public Map<K, V> getCache() {
        return this.cache;
    }

    public TreeMap<K, V> getTreeMap() {
        return this.cache;
    }

    protected abstract K fromKey(String[] array);

    protected abstract V fromValue(String[] array);

}
