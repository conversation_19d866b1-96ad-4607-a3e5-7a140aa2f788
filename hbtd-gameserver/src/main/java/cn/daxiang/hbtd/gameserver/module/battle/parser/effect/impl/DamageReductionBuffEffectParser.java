package cn.daxiang.hbtd.gameserver.module.battle.parser.effect.impl;

import cn.daxiang.hbtd.gameserver.core.dataconfig.model.SkillConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.SkillEffectConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.SkillConfigService;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleBuff;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleRoom;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleSprite;
import cn.daxiang.hbtd.gameserver.module.battle.model.FightReport;
import cn.daxiang.hbtd.gameserver.module.battle.parser.effect.AbstractSkillEffectParser;
import cn.daxiang.hbtd.gameserver.module.battle.type.SkillEffectType;
import cn.daxiang.shared.module.lineup.SpriteCampType;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class DamageReductionBuffEffectParser extends AbstractSkillEffectParser {

    @Override
    public boolean execute(FightReport report, BattleSprite attacker, BattleSprite targeter, SkillConfig skillConfig, SkillEffectConfig effectConfig, BattleRoom battleRoom) {
        this.addBattleBuff(report, attacker, targeter, skillConfig, effectConfig, battleRoom);
        return true;
    }

    @Override
    protected SkillEffectType getType() {
        return SkillEffectType.DAMAGE_REDUCTION_BUFF_EFFECT;
    }

    public static void main(String[] args) {
        ArrayList<Integer> list = Lists.newArrayList(1, 2, 39, 78, 45, 649);
        System.out.println(list.stream().filter(x -> x > 38).collect(Collectors.toList()));
    }

    @Override
    public long calcEffectValue(FightReport report, BattleSprite battleSprite, BattleRoom battleRoom) {
        /**
         * X1：受击方英雄剩余血量（万分比）
         * X2：攻击方英雄剩余血量（万分比）
         * X3：受击方英雄剩余血量（绝对值）
         * X4：攻击方英雄剩余血量（绝对值）
         * x5:目标是否中毒
         * x6:施法者在阵容中的位置
         * x7: 当前回合数
         * x8: 我方是否同时存在魏蜀吴群4个阵营的英雄(包括已死亡)
         */
        int targetHPPercent = battleSprite.getHPPercent();
        long targetHP = battleSprite.getSpriteBattle().getHP();
        boolean targetHasPoisonBuff = battleSprite.hasBuffType(SkillEffectType.POISON_BUFF_EFFECT);
        Map<Integer, BattleSprite> battleSpriteMap = battleRoom.getBattleMember(battleSprite.getBattleCamp()).getSpriteMap();
        boolean haveAllCommonCap =
            battleSpriteMap.values().stream().filter(x -> SpriteCampType.isCommonCamp(x.getSpriteBattle().getSpriteCamp())).map(x -> x.getSpriteBattle().getSpriteCamp().getId())
                .collect(Collectors.toSet()).size() >= 4;
        Collection<BattleBuff> buffList = battleSprite.getBuffList(getType());
        Map<Integer, Collection<Long>> buffValueMap = Maps.newHashMap();
        int round = battleRoom.getRound();
        for (Iterator<BattleBuff> iterator = buffList.iterator(); iterator.hasNext(); ) {
            BattleBuff buff = iterator.next();
            BattleSprite casterSprite = battleRoom.getBattleSprite(buff.getCastSpriteUid());
            int casterHPPercent = casterSprite.getHPPercent();
            long casterHP = casterSprite.getSpriteBattle().getHP();
            int positionId = casterSprite.getPositionId();
            SkillEffectConfig effectConfig = SkillConfigService.getSkillEffectConfig(buff.getEffectId());
            long buffValue = effectConfig.calcSkillEffect(targetHPPercent, casterHPPercent, targetHP, casterHP, targetHasPoisonBuff, positionId, round, haveAllCommonCap);
            Collection<Long> buffValues = buffValueMap.get(buff.getEffectId());
            if (buffValues == null) {
                buffValues = Lists.newArrayList();
                buffValueMap.put(buff.getEffectId(), buffValues);
            }
            buffValues.add(buffValue);
        }
        return this.getBuffValue(buffValueMap);
    }

}
