package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.dataconfig.annotation.FieldIgnore;
import cn.daxiang.framework.identity.IdentiyKey;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;

import java.util.Collection;

/**
 * <AUTHOR>
 * @date 2024/11/26
 */
@DataFile(fileName = "hero_skin_config")
public class HeroSkinConfig implements ModelAdapter {
    /**
     * 英雄皮肤配置ID
     */
    private int id;
    /**
     * 英雄配置ID
     */
    private int heroId;
    /**
     * 特殊效果Id列表
     * [特殊效果Id,特殊效果Id,特殊效果Id]
     */
    private String specialIds;
    /**
     * 发放头像ID
     */
    private int avatarId;
    /**
     * 发放形象ID
     */
    private int showId;
    /**
     * 皮肤碎片ID列表
     */
    private String skinFragmentGroupId;

    @FieldIgnore
    private Collection<Integer> specialIdList = Lists.newArrayList();

    @FieldIgnore
    private Collection<Integer> skinFragmentIdList = Lists.newArrayList();

    @Override
    public void initialize() {
        JSONArray array = JSONArray.parseArray(specialIds);
        for (Object skill : array) {
            this.specialIdList.add(Integer.valueOf(skill.toString()));
        }

        JSONArray fragmentArray = JSONArray.parseArray(skinFragmentGroupId);
        if (fragmentArray != null) {
            for (Object fragmentId : fragmentArray) {
                this.skinFragmentIdList.add(Integer.valueOf(fragmentId.toString()));
            }
        }
    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(id);
    }

    public int getId() {
        return id;
    }

    public int getHeroId() {
        return heroId;
    }

    public int getAvatarId() {
        return avatarId;
    }

    public Collection<Integer> getSpecialIdList() {
        return specialIdList;
    }

    public int getShowId() {
        return showId;
    }

    public Collection<Integer> getSkinFragmentIdList() {
        return skinFragmentIdList;
    }
}
