package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.dataconfig.annotation.FieldIgnore;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.shared.reward.RewardObject;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;

import java.util.Collection;

/**
 * @author: <PERSON>
 * @date: 2023/6/1 16:05
 * @Description:
 */
@DataFile(fileName = "beast_gacha_pool_reward_config")
public class BeastGachaPoolRewardConfig implements ModelAdapter {
    /**
     * 唯一Id
     */
    private int id;

    /**
     * 奖池Id
     */
    private int poolId;

    /**
     * 奖励列表
     */
    private String rewards;

    /**
     * 道具品质
     */
    private int quality;

    /**
     * 权重
     */
    private int weight;

    /**
     * 描述
     */
    private String des;

    /**
     * 幸运值
     */
    private int lucky;
    @FieldIgnore
    private Collection<RewardObject> rewardList = Lists.newArrayList();

    @Override
    public void initialize() {
        JSONArray rewardArray = JSONArray.parseArray(rewards);
        for (Object rewardItem : rewardArray) {
            JSONArray array = JSONArray.parseArray(rewardItem.toString());
            RewardObject rewardObject = RewardObject.valueOf(array);
            rewardList.add(rewardObject);
        }
    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(id);
    }

    public int getId() {
        return id;
    }

    public int getPoolId() {
        return poolId;
    }

    public int getQuality() {
        return quality;
    }

    public int getWeight() {
        return weight;
    }

    public int getLucky() {
        return lucky;
    }

    public String getDes() {
        return des;
    }

    public Collection<RewardObject> getRewardList() {
        return rewardList;
    }
}
