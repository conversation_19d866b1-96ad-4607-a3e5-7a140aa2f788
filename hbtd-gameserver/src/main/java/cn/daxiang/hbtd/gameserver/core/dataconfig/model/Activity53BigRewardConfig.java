package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.identity.IdentiyKey;

/**
 * 星布棋局-棋局大奖抽奖配置表
 *
 * <AUTHOR>
 * @date 2023/12/28
 */
@DataFile(fileName = "activity_53_big_reward_config")
public class Activity53BigRewardConfig implements ModelAdapter {
    /**
     * data
     */
    private int data;

    /**
     * 棋局：第几局
     */
    private int floor;

    /**
     * 本局的棋子数量
     * 当抽取次数到达本局次数，直接抽中大奖
     */
    private int count;

    /**
     * 棋盘ID
     */
    private int chessboardId;

    /**
     * 本轮多少抽开始加入奖池
     */
    private int limit;

    /**
     * 权值
     * 万分比
     */
    private int rate;

    /**
     * 奖励组id
     */
    private int group;

    public int getData() {
        return data;
    }

    public int getFloor() {
        return floor;
    }

    public int getCount() {
        return count;
    }

    public int getChessboardId() {
        return chessboardId;
    }

    public int getLimit() {
        return limit;
    }

    public int getRate() {
        return rate;
    }

    public int getGroup() {
        return group;
    }

    @Override
    public void initialize() {

    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(data, floor);
    }
}
