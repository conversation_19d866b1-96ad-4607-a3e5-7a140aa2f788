package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.identity.IdentiyKey;

/**
 * 神器配置表
 *
 * @author: <PERSON>
 * @date: 2023/1/31 19:47
 * @Description:
 */
@DataFile(fileName = "relic_config")
public class RelicConfig implements ModelAdapter {
    /**
     * id
     */
    private int id;
    /**
     * 品质
     */
    private int quality;
    /**
     * 分组
     */
    private int group;
    /**
     * 槽位
     */
    private int rank;
    /**
     * 经验
     */
    private int exp;
    /**
     * 分数计算表示式
     */
    private String scoreExpr;

    @Override
    public void initialize() {

    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(id);
    }

    public int getId() {
        return id;
    }

    public int getQuality() {
        return quality;
    }

    public int getGroup() {
        return group;
    }

    public int getRank() {
        return rank;
    }

    public int getExp() {
        return exp;
    }

    public String getScoreExpr() {
        return scoreExpr;
    }
}
