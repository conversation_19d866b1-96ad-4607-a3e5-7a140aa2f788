package cn.daxiang.hbtd.gameserver.module.battle.parser.effect.impl;

import cn.daxiang.hbtd.gameserver.core.dataconfig.model.SkillConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.SkillEffectConfig;
import cn.daxiang.hbtd.gameserver.module.battle.helper.FightHelper;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleBuff;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleRoom;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleSprite;
import cn.daxiang.hbtd.gameserver.module.battle.model.FightReport;
import cn.daxiang.hbtd.gameserver.module.battle.parser.effect.AbstractSkillEffectParser;
import cn.daxiang.hbtd.gameserver.module.battle.type.SkillEffectType;
import cn.daxiang.hbtd.gameserver.module.battle.type.SkillTriggerType;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;

/**
 * 印记层数buff
 *
 * @author: Gary
 * @date: 2023/11/13 16:34
 * @Description:
 */
@Component
public class ImprintBuffEffectParser extends AbstractSkillEffectParser {
    @Override
    protected SkillEffectType getType() {
        return SkillEffectType.IMPRINT_BUFF_EFFECT;
    }

    @Override
    public boolean execute(FightReport report, BattleSprite attacker, BattleSprite targeter, SkillConfig skillConfig, SkillEffectConfig effectConfig, BattleRoom battleRoom) {
        /**
         * 层数计算参数:
         * X1: 目标身上的印记buff层数
         * X2: buff添加者身上的印记buff层数
         */
        Collection<BattleBuff> targeterBuffList = targeter.getBuffList(SkillEffectType.IMPRINT_BUFF_EFFECT);
        int targeterLayers = targeterBuffList.stream().mapToInt(BattleBuff::getLayers).sum();
        Collection<BattleBuff> attackerBuffList = targeter.getBuffList(SkillEffectType.IMPRINT_BUFF_EFFECT);
        int attackerLayers = attackerBuffList.stream().mapToInt(BattleBuff::getLayers).sum();
        int layers = effectConfig.calcBuffLayers(targeterLayers, attackerLayers);
        this.addBattleBuff(report, attacker, targeter, skillConfig, effectConfig, battleRoom, layers);
        //我方任意武将印记层数变化后
        List<BattleSprite> spriteList = Lists.newArrayList(battleRoom.getBattleSpriteList(targeter.getBattleCamp()));
        for (BattleSprite sprite : spriteList) {
            FightHelper.processSkill(battleRoom, sprite, attacker, targeter, layers, skillConfig, SkillTriggerType.IMPRINT_CHANGE_LAYERS, null);
        }
        return true;
    }
}
