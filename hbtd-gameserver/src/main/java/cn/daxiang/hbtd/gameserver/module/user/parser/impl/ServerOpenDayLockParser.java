package cn.daxiang.hbtd.gameserver.module.user.parser.impl;

import cn.daxiang.framework.result.Result;
import cn.daxiang.hbtd.gameserver.module.system.helper.SettingsHelper;
import cn.daxiang.hbtd.gameserver.module.user.parser.AbstractActorLockParser;
import cn.daxiang.hbtd.gameserver.module.user.type.ActorLockType;
import org.springframework.stereotype.Component;

import static cn.daxiang.shared.GameModuleStatusCodeConstant.UNDER_CONSTRUCTION;

@Component
public class ServerOpenDayLockParser extends AbstractActorLockParser {

    @Override
    public Result unlock(long actorId, int condition) {
        if (SettingsHelper.getServerStartDays() < condition) {
            return Result.valueOf(UNDER_CONSTRUCTION);
        }
        return Result.valueOf();
    }

    @Override
    protected ActorLockType getType() {
        return ActorLockType.SERVER_OPEN_DAY;
    }

}
