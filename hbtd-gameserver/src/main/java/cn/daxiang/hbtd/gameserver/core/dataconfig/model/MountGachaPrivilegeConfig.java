package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.dataconfig.annotation.FieldIgnore;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.shared.reward.RewardObject;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.Collection;
import java.util.Map;

/**
 * @author: Cary
 * @date: 2025/3/17 16:51
 * @Description:
 */
@DataFile(fileName = "mount_gacha_privilege_config")
public class MountGachaPrivilegeConfig implements ModelAdapter {
    /**
     * 特权类型
     */
    private int type;
    /**
     * 充值Id
     */
    private int chargeId;
    /**
     * 每日奖励
     */
    private String dailyRewards;
    /**
     * 额外奖励
     */
    private String otherRewards;
    /**
     * 购买奖励  仅首次领取附带
     */
    private String baseReward;

    @FieldIgnore
    private Collection<RewardObject> dailyRewardList = Lists.newArrayList();

    @FieldIgnore
    private Collection<RewardObject> baseRewardList = Lists.newArrayList();
    @FieldIgnore
    private Map<Integer, Collection<RewardObject>> otherRewardMap = Maps.newHashMap();

    @Override
    public void initialize() {
        JSONArray dailyRewardArray = JSONArray.parseArray(dailyRewards);
        for (Object reward : dailyRewardArray) {
            JSONArray array = JSONArray.parseArray(reward.toString());
            RewardObject rewardObject = RewardObject.valueOf(array);
            dailyRewardList.add(rewardObject);
        }
        JSONArray baseRewardArray = JSONArray.parseArray(baseReward);
        for (Object reward : baseRewardArray) {
            JSONArray array = JSONArray.parseArray(reward.toString());
            RewardObject rewardObject = RewardObject.valueOf(array);
            baseRewardList.add(rewardObject);
        }

        JSONArray valueArray = JSONArray.parseArray(otherRewards);
        JSONArray rewardListArray = JSONArray.parseArray(valueArray.getString(1));
        Collection<RewardObject> rewardList = Lists.newArrayListWithCapacity(rewardListArray.size());
        for (Object rewardListItem : rewardListArray) {
            JSONArray rewardArray = JSONArray.parseArray(rewardListItem.toString());
            RewardObject rewardObject = RewardObject.valueOf(rewardArray);
            rewardList.add(rewardObject);
        }
        otherRewardMap.put(valueArray.getInteger(0), rewardList);

    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(type);
    }

    public int getType() {
        return type;
    }

    public int getChargeId() {
        return chargeId;
    }

    public Collection<RewardObject> getDailyRewardList() {
        return dailyRewardList;
    }

    public Map<Integer, Collection<RewardObject>> getOtherRewardMap() {
        return otherRewardMap;
    }

    public Collection<RewardObject> getBaseRewardList() {
        return baseRewardList;
    }
}
