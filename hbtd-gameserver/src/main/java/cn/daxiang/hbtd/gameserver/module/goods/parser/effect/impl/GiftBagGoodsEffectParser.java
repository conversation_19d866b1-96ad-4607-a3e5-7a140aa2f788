package cn.daxiang.hbtd.gameserver.module.goods.parser.effect.impl;

import cn.daxiang.framework.result.TResult;
import cn.daxiang.framework.utils.RandomUtils;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.GoodsBagNoticeConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.GoodsConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.GlobalConfigService;
import cn.daxiang.hbtd.gameserver.core.dispatch.DispatchHelper;
import cn.daxiang.hbtd.gameserver.core.event.impl.GoodsBagNoticeEvent;
import cn.daxiang.hbtd.gameserver.core.event.impl.GoodsBagUseEvent;
import cn.daxiang.hbtd.gameserver.core.oss.type.OperationType;
import cn.daxiang.hbtd.gameserver.module.goods.helper.RewardHelper;
import cn.daxiang.hbtd.gameserver.module.goods.parser.effect.AbstractGoodsEffectParser;
import cn.daxiang.hbtd.gameserver.module.goods.type.GoodsEffectType;
import cn.daxiang.protocol.game.CommonProtocol.RewardResult;
import cn.daxiang.shared.reward.RewardObject;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import static cn.daxiang.shared.GameModuleStatusCodeConstant.CONFIG_ERROR;

@Component
public class GiftBagGoodsEffectParser extends AbstractGoodsEffectParser {
    @Autowired
    private GlobalConfigService globalConfigService;

    public static void main(String[] args) {
        List<RewardObject> rewardList = Lists.newArrayList();
        String expr = "[2,[[[[11,1,1],[11,2,2]],10000],[[[11,1,3],[11,2,5]],2000],[[[11,1,11],[11,2,15]],2000],[[[11,1,23],[11,2,25]],2000]]]";
        Map<Collection<RewardObject>, Integer> rewardMap = Maps.newHashMap();
        JSONArray jsonArray = JSONArray.parseArray(expr);
        int rewardCount = jsonArray.getIntValue(0);
        JSONArray bagArray = JSONArray.parseArray(jsonArray.getString(1));
        for (Object bagItem : bagArray) {
            JSONArray randomArray = JSONArray.parseArray(bagItem.toString());
            JSONArray rewardArray = JSONArray.parseArray(randomArray.getString(0));
            Collection<RewardObject> randomRewardList = Lists.newArrayListWithCapacity(rewardArray.size());
            for (Object rewardItem : rewardArray) {
                JSONArray array = JSONArray.parseArray(rewardItem.toString());
                RewardObject rewardObject = RewardObject.valueOf(array.getIntValue(0), array.getIntValue(1), array.getIntValue(2));
                randomRewardList.add(rewardObject);
            }
            rewardMap.put(randomRewardList, randomArray.getInteger(1));
        }
        for (int j = 0; j < rewardCount; j++) {
            Collection<RewardObject> randomRewardList = RandomUtils.randomByWeight(rewardMap);
            rewardMap.remove(randomRewardList);
            rewardList.addAll(randomRewardList);
        }
        System.err.println(rewardMap);
        System.err.println(rewardList);
    }

    @Override
    public TResult<RewardResult> execute(long actorId, long num, GoodsConfig goodsConfig, byte[] value, OperationType operationType) {
        // [num,[[[[rewardType,id,num],[rewardType,id,num]],weight],[[[rewardType,id,num],[rewardType,id,num]],weight]]]
        List<RewardObject> rewardList = Lists.newArrayList();
        List<RewardObject> noticeRewardList = Lists.newArrayList();
        GoodsBagNoticeConfig noticeConfig = globalConfigService.findConfig(goodsConfig.findKey(), GoodsBagNoticeConfig.class);
        for (int i = 1; i <= num; i++) {
            Map<Collection<RewardObject>, Integer> rewardMap = Maps.newHashMap();
            JSONArray jsonArray = JSONArray.parseArray(goodsConfig.getGoodsEffectExpr());
            int rewardCount = jsonArray.getIntValue(0);
            JSONArray bagArray = JSONArray.parseArray(jsonArray.getString(1));
            for (Object bagItem : bagArray) {
                JSONArray randomArray = JSONArray.parseArray(bagItem.toString());
                JSONArray rewardArray = JSONArray.parseArray(randomArray.getString(0));
                Collection<RewardObject> randomRewardList = Lists.newArrayListWithCapacity(rewardArray.size());
                for (Object rewardItem : rewardArray) {
                    JSONArray array = JSONArray.parseArray(rewardItem.toString());
                    RewardObject rewardObject = RewardObject.valueOf(array.getIntValue(0), array.getIntValue(1), array.getIntValue(2));
                    randomRewardList.add(rewardObject);
                }
                rewardMap.put(randomRewardList, randomArray.getInteger(1));
            }
            if (rewardCount > rewardMap.size()) {
                LOGGER.error("GoodsEffectExpr error, goodsId:{},effectExpr:{}", goodsConfig.getGoodsId(), goodsConfig.getGoodsEffectExpr());
                return TResult.valueOf(CONFIG_ERROR);
            }
            for (int j = 0; j < rewardCount; j++) {
                Collection<RewardObject> randomRewardList = RandomUtils.randomByWeight(rewardMap);
                rewardMap.remove(randomRewardList);
                rewardList.addAll(randomRewardList);
                if (noticeConfig != null) {
                    for (RewardObject rewardObject : randomRewardList) {
                        if (noticeConfig.contains(rewardObject)) {
                            noticeRewardList.add(rewardObject);
                        }
                    }
                }
            }
        }
        if (!noticeRewardList.isEmpty()) {
            DispatchHelper.postEvent(new GoodsBagNoticeEvent(actorId, goodsConfig.getGoodsId(), noticeRewardList));
        }
        DispatchHelper.postEvent(new GoodsBagUseEvent(actorId, goodsConfig.getGoodsId(), num));
        RewardResult result = RewardHelper.sendRewardList(actorId, rewardList, operationType);
        return TResult.sucess(result);
    }

    @Override
    protected GoodsEffectType getType() {
        return GoodsEffectType.GIFT_BAG;
    }
}