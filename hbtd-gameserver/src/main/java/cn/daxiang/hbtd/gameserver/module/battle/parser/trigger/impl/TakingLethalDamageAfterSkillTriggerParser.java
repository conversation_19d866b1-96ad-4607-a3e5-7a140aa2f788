package cn.daxiang.hbtd.gameserver.module.battle.parser.trigger.impl;

import cn.daxiang.hbtd.gameserver.core.dataconfig.model.SkillConfig;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleRoom;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleSprite;
import cn.daxiang.hbtd.gameserver.module.battle.parser.trigger.AbstractSkillTriggerParser;
import cn.daxiang.hbtd.gameserver.module.battle.type.SkillTriggerType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/2/20
 */

@Component
public class TakingLethalDamageAfterSkillTriggerParser extends AbstractSkillTriggerParser {
    @Override
    protected SkillTriggerType getType() {
        return SkillTriggerType.TAKING_LETHAL_DAMAGE_AFTER;
    }

    @Override
    public boolean trigger(BattleRoom battleRoom, BattleSprite battleSprite, BattleSprite caster, BattleSprite triggerTarget, long triggerValue, SkillConfig triggerSkill,
        SkillConfig skillConfig) {
        /**
         * x1:是否为自身
         * x2:是否为队友（自身除外）
         */
        boolean isSelf = battleSprite.getSpriteId() == triggerTarget.getSpriteId();
        boolean isFriendly = !isSelf && battleSprite.getBattleCamp() == triggerTarget.getBattleCamp();
        return skillConfig.isTrigger(isSelf, isFriendly);
    }
}
