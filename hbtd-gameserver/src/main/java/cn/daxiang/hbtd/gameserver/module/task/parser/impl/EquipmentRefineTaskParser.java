package cn.daxiang.hbtd.gameserver.module.task.parser.impl;

import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.framework.result.CollectionResult;
import cn.daxiang.framework.utils.FormulaUtils;
import cn.daxiang.hbtd.gameserver.core.database.table.Equipment;
import cn.daxiang.hbtd.gameserver.core.database.table.Task;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.EquipmentConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.TaskConfig;
import cn.daxiang.hbtd.gameserver.core.event.impl.EquipmentRefineEvent;
import cn.daxiang.hbtd.gameserver.module.equipment.facade.EquipmentFacade;
import cn.daxiang.hbtd.gameserver.module.task.parser.AbstractTaskParser;
import cn.daxiang.hbtd.gameserver.module.task.type.TaskConditionType;
import cn.daxiang.protocol.game.TaskProtocol;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class EquipmentRefineTaskParser extends AbstractTaskParser<EquipmentRefineEvent> {
    @Autowired
    private EquipmentFacade equipmentFacade;

    @Override
    public void init(Task task, TaskConfig taskConfig) {
        task.setValue(0);
        parser(task, taskConfig);
    }

    private void parser(Task task, TaskConfig taskConfig) {
        int value = 0;
        CollectionResult<Equipment> equipmentResult = equipmentFacade.getEquipmentList(task.getActorId());
        for (Equipment equipment : equipmentResult.item) {
            EquipmentConfig equipmentConfig = globalConfigService.findConfig(equipment.getConfigId(), EquipmentConfig.class);
            if (!FormulaUtils.executeBool(taskConfig.getCondition(), equipmentConfig.getQuality(), equipment.getRefineLevel())) {
                continue;
            }
            value += 1;
            if (value >= taskConfig.getValue()) {
                break;
            }
        }
        if (task.getValue() < value) {
            task.setValue(value);
        }
        if (task.getValue() >= taskConfig.getValue()) {
            task.setStatus(TaskProtocol.TaskStatus.TASK_FINISH);
        }
    }

    @Override
    protected TaskConditionType getType() {
        return TaskConditionType.EQUIPMENT_REFINE;
    }

    @Override
    protected boolean parseCondition(EquipmentRefineEvent event, Task task, TaskConfig taskConfig) {
        for (Equipment equipment : event.equipmentList) {
            EquipmentConfig equipmentConfig = globalConfigService.findConfig(IdentiyKey.build(equipment.getConfigId()), EquipmentConfig.class);
            if (FormulaUtils.executeBool(taskConfig.getCondition(), equipmentConfig.getQuality(), equipment.getRefineLevel())) {
                parser(task, taskConfig);
                return true;
            }
        }
        return true;
    }
}
