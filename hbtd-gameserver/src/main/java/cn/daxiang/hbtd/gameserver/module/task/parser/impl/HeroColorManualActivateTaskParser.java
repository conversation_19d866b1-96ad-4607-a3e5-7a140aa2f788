package cn.daxiang.hbtd.gameserver.module.task.parser.impl;

import cn.daxiang.hbtd.gameserver.core.database.table.HeroManual;
import cn.daxiang.hbtd.gameserver.core.database.table.Task;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.TaskConfig;
import cn.daxiang.hbtd.gameserver.core.event.impl.HeroColorManualActivateEvent;
import cn.daxiang.hbtd.gameserver.module.heroManual.dao.HeroManualDao;
import cn.daxiang.hbtd.gameserver.module.task.parser.AbstractTaskParser;
import cn.daxiang.hbtd.gameserver.module.task.type.TaskConditionType;
import cn.daxiang.protocol.game.TaskProtocol;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 武将彩金数量任务
 *
 * @Author: Cary
 * @Date 2024-5-27
 */
@Component
public class HeroColorManualActivateTaskParser extends AbstractTaskParser<HeroColorManualActivateEvent> {

    @Autowired
    private HeroManualDao heroManualDao;

    @Override
    public void init(Task task, TaskConfig taskConfig) {
        HeroManual heroManual = heroManualDao.getHeroManual(task.getActorId());
        task.setValue(heroManual.getColorManualList().size());
        if (task.getValue() >= taskConfig.getValue()) {
            task.setStatus(TaskProtocol.TaskStatus.TASK_FINISH);
        }
    }

    @Override
    protected TaskConditionType getType() {
        return TaskConditionType.HERO_COLOR_MANUAL_ACTIVATE;
    }

    @Override
    protected boolean parseCondition(HeroColorManualActivateEvent e, Task task, TaskConfig taskConfig) {
        init(task, taskConfig);
        return true;
    }

}

