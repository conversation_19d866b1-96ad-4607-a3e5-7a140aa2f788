package cn.daxiang.hbtd.gameserver.module.task.parser.impl;

import cn.daxiang.framework.event.ActorEvent;
import cn.daxiang.framework.result.CollectionResult;
import cn.daxiang.framework.utils.FormulaUtils;
import cn.daxiang.hbtd.gameserver.core.database.table.Immortals;
import cn.daxiang.hbtd.gameserver.core.database.table.Task;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.TaskConfig;
import cn.daxiang.hbtd.gameserver.module.immortals.facade.ImmortalsFacade;
import cn.daxiang.hbtd.gameserver.module.task.parser.AbstractTaskParser;
import cn.daxiang.hbtd.gameserver.module.task.type.TaskConditionType;
import cn.daxiang.protocol.game.TaskProtocol;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;

/**
 * <AUTHOR>
 * @date 2022/7/14
 */
@Component
public class ImmortalsStarUpTaskParser extends AbstractTaskParser<ActorEvent> {
    @Autowired
    private ImmortalsFacade immortalsFacade;

    @Override
    public void init(Task task, TaskConfig taskConfig) {
        task.setValue(0);
        parser(task, taskConfig);
    }

    private void parser(Task task, TaskConfig taskConfig) {
        CollectionResult<Immortals> result = immortalsFacade.getImmortalsList(task.getActorId());
        if (result.isFail()) {
            return;
        }
        int value = 0;
        Collection<Immortals> immortalsList = result.item;
        for (Immortals immortals : immortalsList) {
            if (!FormulaUtils.executeBool(taskConfig.getCondition(), immortals.getStarLevel())) {
                continue;
            }
            value += 1;
            if (value >= taskConfig.getValue()) {
                break;
            }
        }
        if (task.getValue() < value) {
            task.setValue(value);
        }
        if (task.getValue() >= taskConfig.getValue()) {
            task.setStatus(TaskProtocol.TaskStatus.TASK_FINISH);
        }
    }

    @Override
    protected TaskConditionType getType() {
        return TaskConditionType.IMMORTALS_STAR_UP;
    }

    @Override
    protected boolean parseCondition(ActorEvent event, Task task, TaskConfig taskConfig) {
        parser(task, taskConfig);
        return true;
    }

}
