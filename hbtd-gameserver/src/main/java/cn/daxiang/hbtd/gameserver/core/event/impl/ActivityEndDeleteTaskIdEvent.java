package cn.daxiang.hbtd.gameserver.core.event.impl;

import cn.daxiang.framework.event.ActorEvent;
import cn.daxiang.hbtd.gameserver.module.task.type.TaskType;
import cn.daxiang.shared.event.EventKey;

import java.util.Collection;

/**
 * 活动结束删除任务事件
 * 更具任务类型 和 任务Ids
 *
 * <AUTHOR>
 * @date 2024/5/24
 */
public class ActivityEndDeleteTaskIdEvent extends ActorEvent {
    /**
     * 任务类型
     */
    private TaskType taskType;

    /**
     * 任务Id
     */
    private Collection<Integer> taskIds;

    public ActivityEndDeleteTaskIdEvent(long actorId, TaskType taskType, Collection<Integer> taskIds) {
        super(EventKey.ACTIVITY_END_DELETE_TASK_ID_EVENT, actorId);
        this.taskType = taskType;
        this.taskIds = taskIds;
    }

    public TaskType getTaskType() {
        return taskType;
    }

    public Collection<Integer> getTaskIds() {
        return taskIds;
    }
}