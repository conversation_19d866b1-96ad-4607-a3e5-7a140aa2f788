package cn.daxiang.hbtd.gameserver.module.chat.helper;

import java.util.concurrent.atomic.AtomicLong;

public class GenerateChatMessageHelper {
    private static AtomicLong MSG_ID_GENERATER = new AtomicLong();

    public static long generateMsgId() {
        long id = MSG_ID_GENERATER.incrementAndGet();
        if (id == Long.MAX_VALUE) {
            MSG_ID_GENERATER.set(1);
        }
        return System.nanoTime() + id;
    }
}
