package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.dataconfig.annotation.FieldIgnore;
import cn.daxiang.framework.extend.KeyValue;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.framework.utils.RandomUtils;
import cn.daxiang.shared.reward.RewardObject;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.Collection;
import java.util.Map;
import java.util.TreeMap;

/**
 * 勇闯魔窟魔将配置
 */
@DataFile(fileName = "cave_normal_challenge_config")
public class CaveNormalChallengeConfig implements ModelAdapter {
    /**
     * ID
     */
    private int stage;
    /**
     * 单人挑战奖励
     * [[rewardType,id,num],[rewardType,id,num]]
     */
    private String rewards;
    /**
     * 单人挑战天门怪物组
     */
    private int monsterGroupOne;
    /**
     * 单人挑战地门怪物组
     */
    private int monsterGroupTwo;
    /**
     * 单人挑战人门怪物组
     */
    private int monsterGroupThree;
    /**
     * 奖励池ID列表(默认次数为-1,其余次数为固定奖励,暂时只能配一个.如果需要弄固定次数的在说,这个次数是循环的,多个次数需要累计)
     * [[times,poolId],[times,poolId]]
     */
    private String rewardPools;
    /**
     * 奖励池ID列表(默认次数为-1,其余次数为固定奖励,暂时只能配一个.如果需要弄固定次数的在说,这个次数是循环的,多个次数需要累计)
     * <p>
     * [[times,poolId],[times,poolId]]
     */
    private String recycleRewardPools;
    /**
     * 组队怪物组
     */
    private int monsterGroup;
    /**
     * [触发天魔万分比,[[天魔type，权重],[天魔type，权重]]]，触发后按权重抽取触发哪个天魔
     */
    private String hardChallenge;
    /**
     * 助战奖励上限
     * 周收益上限:本级上限
     * 首通增加=下级-上级
     */
    private int limit;
    @FieldIgnore
    private Collection<RewardObject> singleRewardList = Lists.newArrayList();
    /**
     * 门的orderId对应monsterGroupIdmap
     */
    @FieldIgnore
    private Map<Integer, Integer> doorMap = Maps.newHashMap();
    /**
     * 奖励池Map
     * key: poolId,value: times
     */
    @FieldIgnore
    private TreeMap<Integer, Integer> rewardPoolMap = Maps.newTreeMap();
    @FieldIgnore
    private Map<Integer, Integer> recycleRewardPoolMap = Maps.newHashMap();
    @FieldIgnore
    private KeyValue<Integer, Map<Integer, Integer>> hardChallengeKV = new KeyValue<>();

    @Override
    public void initialize() {
        JSONArray rewardArray = JSONArray.parseArray(rewards);
        for (Object rewardItem : rewardArray) {
            JSONArray array = JSONArray.parseArray(rewardItem.toString());
            RewardObject rewardObject = RewardObject.valueOf(array);
            singleRewardList.add(rewardObject);
        }
        doorMap.put(0, monsterGroupOne);
        doorMap.put(1, monsterGroupTwo);
        doorMap.put(2, monsterGroupThree);

        JSONArray rewardPoolArray = JSONArray.parseArray(rewardPools);
        for (Object rewardPoolItem : rewardPoolArray) {
            JSONArray itemArray = JSONArray.parseArray(rewardPoolItem.toString());
            rewardPoolMap.put(itemArray.getInteger(1), itemArray.getInteger(0));
        }

        JSONArray recycleRewardPoolArray = JSONArray.parseArray(recycleRewardPools);
        for (Object recycleRewardPoolItem : recycleRewardPoolArray) {
            JSONArray itemArray = JSONArray.parseArray(recycleRewardPoolItem.toString());
            recycleRewardPoolMap.put(itemArray.getInteger(0), itemArray.getInteger(1));
        }

        JSONArray hardChallengeArray = JSONArray.parseArray(hardChallenge);
        hardChallengeKV.setKey(hardChallengeArray.getIntValue(0));
        hardChallengeKV.setValue(Maps.newHashMap());
        JSONArray rateArray = JSONArray.parseArray(hardChallengeArray.getString(1));
        for (Object rateItem : rateArray) {
            JSONArray itemArray = JSONArray.parseArray(rateItem.toString());
            hardChallengeKV.getValue().put(itemArray.getIntValue(0), itemArray.getIntValue(1));
        }
    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(stage);
    }

    public int getStage() {
        return stage;
    }

    public int getMonsterGroupOne() {
        return monsterGroupOne;
    }

    public int getMonsterGroupTwo() {
        return monsterGroupTwo;
    }

    public int getMonsterGroupThree() {
        return monsterGroupThree;
    }

    /**
     * 魔将挑战怪物组ID
     *
     * @return
     */
    public int getGroupMonsterGroup() {
        return monsterGroup;
    }

    public Collection<RewardObject> getSingleRewardList() {
        return singleRewardList;
    }

    /**
     * 单人挑战根据门id获取对应怪物组
     *
     * @param door
     * @return
     */
    public int getSingleMonsterGroup(int door) {
        return doorMap.getOrDefault(door, 0);
    }

    /**
     * 基础奖励池Map
     * key:times,value:poolId
     *
     * @return
     */
    public Map<Integer, Integer> getRewardPoolMap() {
        return rewardPoolMap;
    }

    /**
     * 循环奖励池
     * key:times,value:poolId
     *
     * @return
     */
    public Map<Integer, Integer> getRecycleRewardPoolMap() {
        return recycleRewardPoolMap;
    }

    public int getLimit() {
        return limit;
    }

    /**
     * 触发天魔返回天魔配置信息
     * 未触发天魔则返回 0；
     *
     * @return
     */
    public int triggerHardChallenge() {
        boolean isTrigger = RandomUtils.is10000Hit(hardChallengeKV.getKey());
        if (isTrigger) {
            Map<Integer, Integer> triggerMap = hardChallengeKV.getValue();
            Integer triggerKey = RandomUtils.randomByWeight(triggerMap);
            return triggerKey;
        } else {
            return 0;
        }

    }
}
