package cn.daxiang.hbtd.gameserver.module.reputation.dao.impl;

import cn.daxiang.framework.database.SingleEntity;
import cn.daxiang.framework.database.dao.SingleEntityDaoImpl;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.framework.utils.StopWatch;
import cn.daxiang.framework.utils.rank.cache.ConsistentLadderRankCache;
import cn.daxiang.hbtd.gameserver.core.database.GameInit;
import cn.daxiang.hbtd.gameserver.core.database.InitIndex;
import cn.daxiang.hbtd.gameserver.core.database.table.Reputation;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.ReputationEventConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.ReputationLevelConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.GlobalConfigService;
import cn.daxiang.hbtd.gameserver.module.reputation.dao.ReputationDao;
import cn.daxiang.hbtd.gameserver.module.reputation.facade.ReputationFacade;
import cn.daxiang.hbtd.gameserver.module.reputation.model.EventInfoEntity;
import cn.daxiang.hbtd.gameserver.module.reputation.model.ReputationRank;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * @author: Gary
 * @date: 2023/3/11 17:15
 * @Description:
 */
@Component
public class ReputationDaoImpl extends SingleEntityDaoImpl implements ReputationDao, GameInit {
    private final static int RANK_LIMIT = 100;
    /**
     * 平定天下排行榜
     * key:actorId,value:ReputationRank
     */
    private final static ConsistentLadderRankCache<Long, ReputationRank> REPUTATION_RANK_CACHE = new ConsistentLadderRankCache<>(RANK_LIMIT, RANK_LIMIT);
    @Autowired
    private ReputationFacade reputationFacade;
    @Autowired
    private GlobalConfigService globalConfigService;

    @Override
    protected Class<? extends SingleEntity<?>> forClass() {
        return Reputation.class;
    }

    @Override
    protected void initMaxId() {

    }

    @Override
    public Reputation getReputation(long actorId) {
        Reputation entity = this.get(IdentiyKey.build(actorId));
        if (entity.newEntity()) {
            entity.setLevel(1);
            entity.setNewEntity(false);
            ReputationLevelConfig config = globalConfigService.findConfig(IdentiyKey.build(1), ReputationLevelConfig.class);
            ReputationEventConfig eventConfig = globalConfigService.findConfig(IdentiyKey.build(config.getUrgentEventId()), ReputationEventConfig.class);
            if (eventConfig != null) {
                TResult<EventInfoEntity> tResult = reputationFacade.creatEventInfoEntity(eventConfig, 1);
                if (tResult.isOk()) {
                    entity.setUrgentEvent(tResult.item);
                }
            }
            dbQueue.updateQueue(entity);
        }
        return entity;
    }

    @Override
    public Collection<ReputationRank> getReputationRanks() {
        return REPUTATION_RANK_CACHE.findRanks(1);
    }

    @Override
    public void achieveReputationRank(long actorId, int level, long reputationVale, long lastUpgradeTime) {
        if (reputationVale <= 0) {
            return;
        }
        ReputationRank rank = REPUTATION_RANK_CACHE.find(actorId);
        if (rank == null) {
            rank = ReputationRank.valueOf(actorId, level, reputationVale, lastUpgradeTime);
        } else {
            rank.update(level, reputationVale, lastUpgradeTime);
        }
        REPUTATION_RANK_CACHE.achieve(rank);
    }

    @Override
    public ReputationRank getReputationRankByActorId(long actorId) {
        return REPUTATION_RANK_CACHE.find(actorId);
    }

    @Override
    public ReputationRank getReputationRankByRank(int rank) {
        return REPUTATION_RANK_CACHE.findByRank(rank);
    }

    @Override
    public void gameInit() {
        StopWatch sw = new StopWatch(true);
        int pageIndex = 0;
        while (true) {
            LinkedHashMap<String, Object> paramMaps = new LinkedHashMap<>();
            List<Reputation> list = jdbc.getList(Reputation.class, paramMaps, pageIndex * jdbc.PAGE_SIZE, jdbc.PAGE_SIZE);
            if (list.size() < 1) {
                break;
            }
            for (Reputation reputation : list) {
                long actorId = reputation.getActorId();
                if (actorId < 0 || reputation.getHistoryReputationVale() <= 0) {
                    continue;
                }
                ReputationRank rank =
                    ReputationRank.valueOf(reputation.getActorId(), reputation.getLevel(), reputation.getHistoryReputationVale(), reputation.getLastUpgradeTime());
                REPUTATION_RANK_CACHE.achieve(rank);
            }
            pageIndex++;
        }
        sw.stop();
        LOGGER.info("Reputation rank loading complete! total:[{}] record. time:{}ms", REPUTATION_RANK_CACHE.findSize(), sw.runTime());
    }

    @Override
    public InitIndex index() {
        return InitIndex.INTI_FOUR;
    }
}
