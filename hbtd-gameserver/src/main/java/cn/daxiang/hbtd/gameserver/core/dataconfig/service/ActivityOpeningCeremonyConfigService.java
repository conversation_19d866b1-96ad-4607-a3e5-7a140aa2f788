package cn.daxiang.hbtd.gameserver.core.dataconfig.service;

import cn.daxiang.framework.dataconfig.ConfigServiceAdapter;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.ActivityOpeningCeremonyConfig;
import cn.daxiang.hbtd.gameserver.module.activity.type.Activity12RankType;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;
import java.util.TreeMap;

/**
 * <AUTHOR>
 * @date 2022/6/9
 */
@Component
public class ActivityOpeningCeremonyConfigService extends ConfigServiceAdapter {

    private static Map<Integer, TreeMap<Long, ActivityOpeningCeremonyConfig>> POWER_RANK_CONFIG_MAP = Maps.newHashMap();

    private static Map<Integer, TreeMap<Long, ActivityOpeningCeremonyConfig>> STORY_RANK_CONFIG_MAP = Maps.newHashMap();

    public static TreeMap<Long, ActivityOpeningCeremonyConfig> getRankConfigMap(int data, Activity12RankType rankType) {
        if (rankType == Activity12RankType.POWER_RANK) {
            return POWER_RANK_CONFIG_MAP.get(data);
        } else if (rankType == Activity12RankType.STORY_RANK) {
            return STORY_RANK_CONFIG_MAP.get(data);
        }
        return null;
    }

    @Override
    protected void initialize() {
        Collection<ActivityOpeningCeremonyConfig> activityOpeningCeremonyConfig = dataConfig.listAll(this, ActivityOpeningCeremonyConfig.class);

        for (ActivityOpeningCeremonyConfig config : activityOpeningCeremonyConfig) {
            if (config.getType() == Activity12RankType.POWER_RANK.getId()) {
                TreeMap<Long, ActivityOpeningCeremonyConfig> configMap = POWER_RANK_CONFIG_MAP.get(config.getData());
                if (configMap == null) {
                    configMap = Maps.newTreeMap();
                    POWER_RANK_CONFIG_MAP.put(config.getData(), configMap);
                }
                configMap.put(config.getRank(), config);
            }

            if (config.getType() == Activity12RankType.STORY_RANK.getId()) {
                TreeMap<Long, ActivityOpeningCeremonyConfig> configMap = STORY_RANK_CONFIG_MAP.get(config.getData());
                if (configMap == null) {
                    configMap = Maps.newTreeMap();
                    STORY_RANK_CONFIG_MAP.put(config.getData(), configMap);
                }
                configMap.put(config.getRank(), config);
            }
        }
    }

    @Override
    protected void clean() {
        POWER_RANK_CONFIG_MAP.clear();
        STORY_RANK_CONFIG_MAP.clear();
    }

}
