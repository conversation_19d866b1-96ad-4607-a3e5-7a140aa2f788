package cn.daxiang.hbtd.gameserver.core.event.impl;

import cn.daxiang.framework.event.ActorEvent;
import cn.daxiang.shared.event.EventKey;

/**
 * 装备进阶事件
 *
 * @author: <PERSON>
 * @date: 2024/1/09
 * @Description:
 */
public class EquipmentEnhanceEvent extends ActorEvent {
    /**
     * 装备配置Id
     */
    public int configId;

    /**
     * 进阶等级
     */
    public int enhanceLevel;

    public EquipmentEnhanceEvent(long actorId, int configId, int enhanceLevel) {
        super(EventKey.EQUIPMENT_ENHANCE_EVENT, actorId);
        this.configId = configId;
        this.enhanceLevel = enhanceLevel;
    }
}
