package cn.daxiang.hbtd.gameserver.core.event.impl;

import cn.daxiang.framework.event.ActorEvent;
import cn.daxiang.hbtd.gameserver.module.task.type.TaskType;
import cn.daxiang.shared.event.EventKey;

/**
 * 任务完成事件
 */
public class TaskCompleteEvent extends ActorEvent {

    public int active;

    public TaskType type;

    public TaskCompleteEvent(long actorId, TaskType type, int active) {
        super(EventKey.TASK_COMPLETE_EVENT, actorId);
        this.type = type;
        this.active = active;
    }

}
