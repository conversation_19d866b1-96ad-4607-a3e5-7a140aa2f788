package cn.daxiang.hbtd.gameserver.module.title.facade;

import cn.daxiang.framework.result.Result;
import cn.daxiang.hbtd.gameserver.core.database.table.Title;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.TitleConfig;

/**
 * <AUTHOR>
 * @date 2020/2/5
 */
public interface TitleFacade {

    /**
     * 获取称号信息
     *
     * @param actorId
     * @return
     */
    Title getTitle(long actorId);

    /**
     * 创建称号
     *
     * @param actorId
     * @param titleConfig
     * @return
     */
    Result createTitle(long actorId, TitleConfig titleConfig);

    /**
     * 更换称号
     *
     * @param actorId
     * @param titleId
     * @return
     */
    Result changeTitle(long actorId, int titleId);
}
