package cn.daxiang.hbtd.gameserver.core.event.impl;

import cn.daxiang.framework.event.ActorEvent;
import cn.daxiang.shared.event.EventKey;

/**
 * 珍珑宝库转盘抽奖
 */
public class ChessGachaEvent extends ActorEvent {

    /**
     * 抽奖次数
     */
    public int times;

    public ChessGachaEvent(long actorId, int times) {
        super(EventKey.CHESS_GACHA_EVENT, actorId);
        this.times = times;
    }
}
