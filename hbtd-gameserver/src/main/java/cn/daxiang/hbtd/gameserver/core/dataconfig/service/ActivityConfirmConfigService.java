package cn.daxiang.hbtd.gameserver.core.dataconfig.service;

import cn.daxiang.framework.dataconfig.ConfigServiceAdapter;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.ConfirmGiftConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.ConfirmRelevanceConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.ConfirmStoreGoodsConfig;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;

/**
 * 活动15-过关斩将
 *
 * <AUTHOR>
 * @date 2022/8/3
 */
@Component
public class ActivityConfirmConfigService extends ConfigServiceAdapter {
    /**
     * 过关斩将活动关联表
     */
    private static Map<Integer, ConfirmRelevanceConfig> CONFIRM_RELEVANCE_CONFIG_MAP = Maps.newHashMap();
    /**
     * 过关斩将礼包配置表
     */
    private static Map<Integer, Map<Integer, ConfirmGiftConfig>> CONFIRM_GIFT_CONFIG_MAP = Maps.newHashMap();
    /**
     * 过关斩将商店表
     */
    private static Map<Integer, Map<Integer, ConfirmStoreGoodsConfig>> CONFIRM_STORE_GOODS_CONFIG_MAP = Maps.newHashMap();

    public static ConfirmRelevanceConfig getConfirmRelevanceConfig(int data) {
        return CONFIRM_RELEVANCE_CONFIG_MAP.get(data);
    }

    /**
     * 根据活动DATA找到对应礼包配置
     *
     * @param data
     * @return
     */
    public static Map<Integer, ConfirmGiftConfig> getConfirmGiftConfigMap(int data) {
        ConfirmRelevanceConfig config = CONFIRM_RELEVANCE_CONFIG_MAP.get(data);
        if (config == null) {
            return null;
        }
        Map<Integer, ConfirmGiftConfig> map = CONFIRM_GIFT_CONFIG_MAP.get(config.getGiftData());
        return map;
    }

    /**
     * 获取礼包直购配置
     *
     * @param data
     * @param chargeId
     * @return
     */
    public static ConfirmGiftConfig getConfirmGiftConfig(int data, int chargeId) {
        ConfirmRelevanceConfig config = CONFIRM_RELEVANCE_CONFIG_MAP.get(data);
        if (config == null) {
            return null;
        }
        Map<Integer, ConfirmGiftConfig> map = CONFIRM_GIFT_CONFIG_MAP.get(config.getGiftData());
        if (map == null) {
            return null;
        }
        for (ConfirmGiftConfig confirmGiftConfig : map.values()) {
            if (confirmGiftConfig.getChargeId() == chargeId) {
                return confirmGiftConfig;
            }
        }
        return null;
    }

    /**
     * 根据活动DATA找到对应商店配置
     *
     * @param data
     * @return
     */
    public static Map<Integer, ConfirmStoreGoodsConfig> getConfirmStoreGoodsConfigMap(int data) {
        ConfirmRelevanceConfig config = CONFIRM_RELEVANCE_CONFIG_MAP.get(data);
        if (config == null) {
            return null;
        }
        Map<Integer, ConfirmStoreGoodsConfig> map = CONFIRM_STORE_GOODS_CONFIG_MAP.get(config.getStoreData());
        return map;
    }

    @Override
    protected void initialize() {
        Collection<ConfirmRelevanceConfig> confirmRelevanceConfigList = dataConfig.listAll(this, ConfirmRelevanceConfig.class);
        for (ConfirmRelevanceConfig config : confirmRelevanceConfigList) {
            CONFIRM_RELEVANCE_CONFIG_MAP.put(config.getData(), config);
        }

        Collection<ConfirmGiftConfig> confirmGiftConfigList = dataConfig.listAll(this, ConfirmGiftConfig.class);
        for (ConfirmGiftConfig config : confirmGiftConfigList) {
            Map<Integer, ConfirmGiftConfig> configMap = CONFIRM_GIFT_CONFIG_MAP.get(config.getGiftData());
            if (configMap == null) {
                configMap = Maps.newTreeMap();
                CONFIRM_GIFT_CONFIG_MAP.put(config.getGiftData(), configMap);
            }
            configMap.put(config.getId(), config);
        }

        Collection<ConfirmStoreGoodsConfig> confirmStoreGoodsConfigList = dataConfig.listAll(this, ConfirmStoreGoodsConfig.class);
        for (ConfirmStoreGoodsConfig config : confirmStoreGoodsConfigList) {
            Map<Integer, ConfirmStoreGoodsConfig> configMap = CONFIRM_STORE_GOODS_CONFIG_MAP.get(config.getStoreData());
            if (configMap == null) {
                configMap = Maps.newTreeMap();
                CONFIRM_STORE_GOODS_CONFIG_MAP.put(config.getStoreData(), configMap);
            }
            configMap.put(config.getId(), config);
        }
    }

    @Override
    protected void clean() {
        CONFIRM_RELEVANCE_CONFIG_MAP.clear();
        CONFIRM_GIFT_CONFIG_MAP.clear();
        CONFIRM_STORE_GOODS_CONFIG_MAP.clear();
    }

}
