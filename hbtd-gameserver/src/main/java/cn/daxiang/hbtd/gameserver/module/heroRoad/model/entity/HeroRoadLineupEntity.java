package cn.daxiang.hbtd.gameserver.module.heroRoad.model.entity;

import cn.daxiang.shared.module.heroRoad.HeroRoadLineupInfo;
import com.google.common.collect.Lists;

import java.util.Collection;

/**
 * <AUTHOR>
 * @date 2023/7/18
 */
public class HeroRoadLineupEntity {
    /**
     * 获取奖励数
     */
    private int rewardTimes;
    /**
     * 官职
     */
    private int official;
    /**
     * 写入时间
     */
    private long writeTime;
    /**
     * 布阵信息
     */
    private Collection<Collection<HeroRoadLineupInfo>> lineup = Lists.newArrayList();

    public static HeroRoadLineupEntity valueOf(int official, int rewardTimes, long writeTime, Collection<Collection<HeroRoadLineupInfo>> lineup) {
        HeroRoadLineupEntity model = new HeroRoadLineupEntity();
        model.setRewardTimes(rewardTimes);
        model.setOfficial(official);
        model.setWriteTime(writeTime);
        model.setLineup(lineup);
        return model;
    }

    public int getRewardTimes() {
        return rewardTimes;
    }

    public void setRewardTimes(int rewardTimes) {
        this.rewardTimes = rewardTimes;
    }

    public int getOfficial() {
        return official;
    }

    public void setOfficial(int official) {
        this.official = official;
    }

    public long getWriteTime() {
        return writeTime;
    }

    public void setWriteTime(long writeTime) {
        this.writeTime = writeTime;
    }

    public Collection<Collection<HeroRoadLineupInfo>> getLineup() {
        return lineup;
    }

    public void setLineup(Collection<Collection<HeroRoadLineupInfo>> lineup) {
        this.lineup = lineup;
    }
}
