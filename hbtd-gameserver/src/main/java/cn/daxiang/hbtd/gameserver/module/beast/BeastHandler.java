package cn.daxiang.hbtd.gameserver.module.beast;

import cn.daxiang.framework.dispatch.DispatchType;
import cn.daxiang.framework.protocal.DataPacket;
import cn.daxiang.framework.result.CollectionResult;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.framework.router.annotation.Cmd;
import cn.daxiang.hbtd.gameserver.core.database.table.Beast;
import cn.daxiang.hbtd.gameserver.core.router.GatewayRouterHandlerImpl;
import cn.daxiang.hbtd.gameserver.module.beast.facade.BeastFacade;
import cn.daxiang.hbtd.gameserver.module.beast.helper.BeastHelper;
import cn.daxiang.protocol.game.BeastProtocol;
import cn.daxiang.protocol.game.CommonProtocol;
import cn.daxiang.protocol.game.ModuleProtocol;
import io.netty.channel.Channel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;

/**
 * @author: Gary
 * @date: 2023/4/26 11:09
 * @Description:
 */
@Component
public class BeastHandler extends GatewayRouterHandlerImpl {
    @Autowired
    private BeastFacade beastFacade;

    @Override
    public int getModule() {
        return ModuleProtocol.Module.BEAST_VALUE;
    }

    @Cmd(Id = BeastProtocol.BeastCmd.GET_BEAST_BAG_INFO_VALUE, dispatchType = DispatchType.ACTOR)
    public void getBeastBagInfo(Channel channel, Long actorId, DataPacket packet) {
        CollectionResult<Beast> result = beastFacade.getBeastBagInfo(actorId);
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        Collection<Beast> beasts = result.item;
        BeastProtocol.BeastBagInfoResponse beastBagInfoResponse = BeastHelper.buildBeastBagInfoResponse(beasts);
        channelWrite(channel, packet, beastBagInfoResponse);
    }

    @Cmd(Id = BeastProtocol.BeastCmd.BEAST_ACTIVATE_VALUE, dispatchType = DispatchType.ACTOR)
    public void beastActivate(Channel channel, Long actorId, DataPacket packet) {
        CommonProtocol.IntPacket request = packet.getValue(CommonProtocol.IntPacket.parser());
        TResult<CommonProtocol.RewardResult> result = beastFacade.beastActivate(actorId, request.getValue());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = BeastProtocol.BeastCmd.BEAST_LEVE_UP_VALUE, dispatchType = DispatchType.ACTOR)
    public void beastLeveUp(Channel channel, Long actorId, DataPacket packet) {
        BeastProtocol.BeastAddExpRequest request = packet.getValue(BeastProtocol.BeastAddExpRequest.parser());
        TResult<BeastProtocol.BeastBagInfoResponse> result = beastFacade.beastLeveUp(actorId, request.getConfigId(), request.getLevel());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = BeastProtocol.BeastCmd.BEAST_STAR_UP_VALUE, dispatchType = DispatchType.ACTOR)
    public void beastStarUp(Channel channel, Long actorId, DataPacket packet) {
        CommonProtocol.IntPacket request = packet.getValue(CommonProtocol.IntPacket.parser());
        TResult<BeastProtocol.BeastBagInfoResponse> result = beastFacade.beastStarUp(actorId, request.getValue());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = BeastProtocol.BeastCmd.GET_BEAST_MANUAL_INFO_VALUE, dispatchType = DispatchType.ACTOR)
    public void getBeastManualInfo(Channel channel, Long actorId, DataPacket packet) {
        TResult<BeastProtocol.BeastManualResponse> result = beastFacade.getBeastManualInfo(actorId);
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = BeastProtocol.BeastCmd.BEAST_MANUAL_ACTIVATE_VALUE, dispatchType = DispatchType.ACTOR)
    public void beastManualActivate(Channel channel, Long actorId, DataPacket packet) {
        CommonProtocol.IntPacket request = packet.getValue(CommonProtocol.IntPacket.parser());
        TResult<BeastProtocol.BeastManualResponse> result = beastFacade.beastManualActivate(actorId, request.getValue());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = BeastProtocol.BeastCmd.BEAST_MANUAL_RECEIVE_STAGE_REWARD_VALUE, dispatchType = DispatchType.ACTOR)
    public void beastManualReceiveStageReward(Channel channel, Long actorId, DataPacket packet) {
        CommonProtocol.IntPacket request = packet.getValue(CommonProtocol.IntPacket.parser());
        TResult<CommonProtocol.RewardResult> result = beastFacade.beastManualReceiveStageReward(actorId, request.getValue());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = BeastProtocol.BeastCmd.BEAST_MANUAL_RECEIVE_RESONANCE_REWARD_VALUE, dispatchType = DispatchType.ACTOR)
    public void beastManualReceiveResonanceReward(Channel channel, Long actorId, DataPacket packet) {
        CommonProtocol.IntPacket request = packet.getValue(CommonProtocol.IntPacket.parser());
        TResult<CommonProtocol.RewardResult> result = beastFacade.beastManualReceiveResonanceReward(actorId, request.getValue());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = BeastProtocol.BeastCmd.GET_BEAST_GACHA_INFO_VALUE, dispatchType = DispatchType.ACTOR)
    public void getBeastGachaInfo(Channel channel, Long actorId, DataPacket packet) {
        TResult<BeastProtocol.BeastGachaInfoResponse> result = beastFacade.getBeastGachaInfo(actorId);
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = BeastProtocol.BeastCmd.BEAST_GACHA_CHANGE_REWARD_POOL_VALUE, dispatchType = DispatchType.ACTOR)
    public void beastGachaChangeRewardPool(Channel channel, Long actorId, DataPacket packet) {
        CommonProtocol.IntPacket request = packet.getValue(CommonProtocol.IntPacket.parser());
        TResult<BeastProtocol.BeastGachaInfoResponse> result = beastFacade.beastGachaChangeRewardPool(actorId, request.getValue());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = BeastProtocol.BeastCmd.BEAST_GACHA_VALUE, dispatchType = DispatchType.ACTOR)
    public void beastGacha(Channel channel, Long actorId, DataPacket packet) {
        CommonProtocol.IntPacket request = packet.getValue(CommonProtocol.IntPacket.parser());
        TResult<BeastProtocol.BeastGachaResultResponse> result = beastFacade.beastGacha(actorId, request.getValue());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = BeastProtocol.BeastCmd.GET_BEAST_GACHA_REPORT_INFO_VALUE, dispatchType = DispatchType.ACTOR)
    public void getBeastGachaReportInfo(Channel channel, Long actorId, DataPacket packet) {
        TResult<BeastProtocol.BeastGachaReportResponse> result = beastFacade.getBeastGachaReportInfo(actorId);
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = BeastProtocol.BeastCmd.RECEIVE_FEEDBACK_REWARD_VALUE, dispatchType = DispatchType.ACTOR)
    public void receiveFeedbackReward(Channel channel, Long actorId, DataPacket packet) {
        CommonProtocol.IntPacket request = packet.getValue(CommonProtocol.IntPacket.parser());
        TResult<CommonProtocol.RewardResult> result = beastFacade.receiveFeedbackReward(actorId, request.getValue());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = BeastProtocol.BeastCmd.RECEIVE_BEAST_GACHA_PRIVILEGE_REWARD_VALUE, dispatchType = DispatchType.ACTOR)
    public void receiveBeastGachaPrivilegeReward(Channel channel, Long actorId, DataPacket packet) {
        CommonProtocol.IntPacket request = packet.getValue(CommonProtocol.IntPacket.parser());
        TResult<CommonProtocol.RewardResult> result = beastFacade.receiveBeastGachaPrivilegeReward(actorId, request.getValue());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = BeastProtocol.BeastCmd.RECEIVE_BEAST_GACHA_CHARGE_REWARD_VALUE, dispatchType = DispatchType.ACTOR)
    public void receiveBeastGachaChargeReward(Channel channel, Long actorId, DataPacket packet) {
        CommonProtocol.IntPacket request = packet.getValue(CommonProtocol.IntPacket.parser());
        TResult<CommonProtocol.RewardResult> result = beastFacade.receiveBeastGachaChargeReward(actorId, request.getValue());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = BeastProtocol.BeastCmd.GET_BEAST_TOTEM_INFO_VALUE, dispatchType = DispatchType.ACTOR)
    public void getBeastTotemInfo(Channel channel, Long actorId, DataPacket packet) {
        TResult<BeastProtocol.BeastTotemInfoResponse> result = beastFacade.getBeastTotemInfo(actorId);
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = BeastProtocol.BeastCmd.BEAST_TOTEM_LEVEL_UP_VALUE, dispatchType = DispatchType.ACTOR)
    public void beastTotemLevelUp(Channel channel, Long actorId, DataPacket packet) {
        CommonProtocol.KeyValuePacket request = packet.getValue(CommonProtocol.KeyValuePacket.parser());
        TResult<BeastProtocol.BeastTotemInfoResponse> result = beastFacade.beastTotemLevelUp(actorId, request.getKey(), request.getValue());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = BeastProtocol.BeastCmd.BEAST_TOTEM_ADVANCED_VALUE, dispatchType = DispatchType.ACTOR)
    public void beastTotemAdvanced(Channel channel, Long actorId, DataPacket packet) {
        CommonProtocol.IntPacket request = packet.getValue(CommonProtocol.IntPacket.parser());
        TResult<BeastProtocol.BeastTotemInfoResponse> result = beastFacade.beastTotemAdvanced(actorId, request.getValue());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = BeastProtocol.BeastCmd.BEAST_TOTEM_QUICKLY_LEVEL_UP_VALUE, dispatchType = DispatchType.ACTOR)
    public void beastTotemQuicklyLevelUp(Channel channel, Long actorId, DataPacket packet) {
        CommonProtocol.IntPacket request = packet.getValue(CommonProtocol.IntPacket.parser());
        TResult<BeastProtocol.BeastTotemInfoResponse> result = beastFacade.beastTotemQuicklyLevelUp(actorId, request.getValue());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

}
