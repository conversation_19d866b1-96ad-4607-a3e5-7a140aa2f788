package cn.daxiang.hbtd.gameserver.core.dataconfig.convert.impl;

import cn.daxiang.hbtd.gameserver.core.dataconfig.convert.HashMapConfig;

public class IntMapConfig extends HashMapConfig<Integer, Integer> {

    @Override
    protected int fromSize() {
        return 2;
    }

    @Override
    protected Integer fromKey(String[] array) {
        return Integer.valueOf(array[0]);
    }

    @Override
    protected Integer fromValue(String[] array) {
        return Integer.valueOf(array[1]);
    }
}
