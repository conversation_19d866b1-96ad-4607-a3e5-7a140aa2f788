package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.dataconfig.annotation.FieldIgnore;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.shared.reward.RewardObject;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;

import java.util.Collection;

/**
 * 过关斩将商店表
 *
 * <AUTHOR>
 * @date 2022/8/3
 */
@DataFile(fileName = "confirm_store_goods_config")
public class ConfirmStoreGoodsConfig implements ModelAdapter {
    /**
     * 根据活动DATA和活动关联表对应的ID
     */
    private int storeData;
    /**
     * 唯一ID
     */
    private int id;
    /**
     * 价格
     */
    private String price;
    /**
     * 限购次数
     */
    private int times;
    /**
     * 购买商品
     */
    private String rewards;

    @FieldIgnore
    private Collection<RewardObject> priceList = Lists.newArrayList();

    @FieldIgnore
    private Collection<RewardObject> rewardList = Lists.newArrayList();

    @Override
    public void initialize() {
        JSONArray priceArray = JSONArray.parseArray(price);
        for (Object item : priceArray) {
            JSONArray rewardArray = JSONArray.parseArray(item.toString());
            RewardObject rewardObject = RewardObject.valueOf(rewardArray);
            priceList.add(rewardObject);
        }

        JSONArray array = JSONArray.parseArray(rewards);
        for (Object item : array) {
            JSONArray rewardArray = JSONArray.parseArray(item.toString());
            RewardObject rewardObject = RewardObject.valueOf(rewardArray);
            rewardList.add(rewardObject);
        }
    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(storeData, id);
    }

    public int getStoreData() {
        return storeData;
    }

    public int getId() {
        return id;
    }

    public int getTimes() {
        return times;
    }

    public Collection<RewardObject> getPriceList() {
        return priceList;
    }

    public Collection<RewardObject> getRewardList() {
        return rewardList;
    }
}
