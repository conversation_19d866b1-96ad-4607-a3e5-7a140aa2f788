package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.identity.IdentiyKey;

/**
 * 神器配置表
 *
 * @author: <PERSON>
 * @date: 2023/10/10 15:33
 * @Description:
 */
@DataFile(fileName = "hallows_config")
public class HallowsConfig implements ModelAdapter {
    /**
     * 神器配置ID
     */
    private int id;
    /**
     * 品质
     */
    private int quality;
    /**
     * 羁绊组
     */
    private int group;
    /**
     * 排序
     */
    private int rank;
    /**
     * 实力比较积分计算公式
     * x1:神器等级
     * x2:神器羁绊组等级
     */
    private String scoreExpr;

    @Override
    public void initialize() {

    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(id);
    }

    public int getId() {
        return id;
    }

    public int getQuality() {
        return quality;
    }

    public int getGroup() {
        return group;
    }

    public int getRank() {
        return rank;
    }

    public String getScoreExpr() {
        return scoreExpr;
    }
}
