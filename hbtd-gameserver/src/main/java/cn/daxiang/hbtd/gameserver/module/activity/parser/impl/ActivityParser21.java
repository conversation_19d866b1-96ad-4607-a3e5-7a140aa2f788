package cn.daxiang.hbtd.gameserver.module.activity.parser.impl;

import cn.daxiang.framework.dispatch.DispatchType;
import cn.daxiang.framework.event.GameEvent;
import cn.daxiang.framework.event.TriggerTime;
import cn.daxiang.framework.event.annotation.Event;
import cn.daxiang.framework.event.annotation.EventOnline;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.framework.rpc.RpcCallback;
import cn.daxiang.framework.rpc.world.WorldActivityRpc;
import cn.daxiang.framework.utils.DateUtils;
import cn.daxiang.hbtd.gameserver.core.GameConfig;
import cn.daxiang.hbtd.gameserver.core.database.table.ActivityGlobal;
import cn.daxiang.hbtd.gameserver.core.database.table.ActivityRecord;
import cn.daxiang.hbtd.gameserver.core.database.table.Actor;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.Activity21ChargeRewardConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.Activity21NumberRewardConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.Activity21NumberWeightConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.Activity21RankConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.ActivityOpenConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.TaskConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.Activity21ConfigService;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.ActivityDeleteGoodsConfigService;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.ActivityOpenConfigService;
import cn.daxiang.hbtd.gameserver.core.dispatch.DispatchHelper;
import cn.daxiang.hbtd.gameserver.core.event.impl.ActivityEndDeleteTaskEvent;
import cn.daxiang.hbtd.gameserver.core.event.impl.ActivityLuckyNumberEvent;
import cn.daxiang.hbtd.gameserver.core.event.impl.ActorLoginEvent;
import cn.daxiang.hbtd.gameserver.core.event.impl.ActorRechargeEvent;
import cn.daxiang.hbtd.gameserver.core.event.impl.GoodsDeleteEvent;
import cn.daxiang.hbtd.gameserver.core.event.impl.MailAddEvent;
import cn.daxiang.hbtd.gameserver.core.oss.type.OperationType;
import cn.daxiang.hbtd.gameserver.core.rpc.WorldRpcHelper;
import cn.daxiang.hbtd.gameserver.module.PbBuilder;
import cn.daxiang.hbtd.gameserver.module.activity.model.entity.record.ActivityRecord21;
import cn.daxiang.hbtd.gameserver.module.activity.parser.AbstractActivityParser;
import cn.daxiang.hbtd.gameserver.module.activity.type.Activity21RankType;
import cn.daxiang.hbtd.gameserver.module.activity.type.ActivityStatus;
import cn.daxiang.hbtd.gameserver.module.activity.type.ActivityType;
import cn.daxiang.hbtd.gameserver.module.goods.helper.RewardHelper;
import cn.daxiang.hbtd.gameserver.module.mail.type.MailTemplateType;
import cn.daxiang.hbtd.gameserver.module.task.type.TaskType;
import cn.daxiang.hbtd.gameserver.module.user.helper.ActorHelper;
import cn.daxiang.protocol.game.ActivityInfoProtocol;
import cn.daxiang.protocol.game.ActivityInfoProtocol.Activity21ReportEntity;
import cn.daxiang.protocol.game.ActivityProtocol;
import cn.daxiang.protocol.game.CommonProtocol;
import cn.daxiang.protocol.game.TypeProtocol;
import cn.daxiang.shared.GlobalConfigKey;
import cn.daxiang.shared.event.EventKey;
import cn.daxiang.shared.module.activity.Activity21ReportRecord;
import cn.daxiang.shared.module.activity.ActivityScoreRankVO;
import cn.daxiang.shared.reward.RewardObject;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.protobuf.ByteString;
import com.google.protobuf.InvalidProtocolBufferException;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import static cn.daxiang.shared.GameModuleStatusCodeConstant.ACTIVITY_STATUS_ERROR;
import static cn.daxiang.shared.GameModuleStatusCodeConstant.CONFIG_NOT_FOUND;
import static cn.daxiang.shared.GameModuleStatusCodeConstant.INVALID_PARAM;
import static cn.daxiang.shared.GameModuleStatusCodeConstant.RECRUIT_REWARD_HAD_RECEIVE;

/**
 * 天官赐福
 *
 * @Author: Gary
 * @Date 2022-10-09 11:29
 * @Description:
 */
@Component
public class ActivityParser21 extends AbstractActivityParser {
    @Override
    public void registerEvent(Set<String> eventSet) {
        eventSet.addAll(getType().getEventNames());
    }

    @Override
    public void onEvent(GameEvent event) {
        Collection<ActivityOpenConfig> configList = ActivityOpenConfigService.getActivityOpenConfigList(getType());
        if (configList.isEmpty()) {
            return;
        }
        for (ActivityOpenConfig activityOpenConfig : configList) {
            int activityId = activityOpenConfig.getId();
            int data = activityOpenConfig.getData();
            //获取活动数据
            ActivityGlobal activityGlobal = activityGlobalDao.getActivityGlobal(activityOpenConfig.getId());
            //活动状态未展示时间不执行
            if (activityGlobal.getStatus() == ActivityStatus.SHOW.getId()) {
                continue;
            }
            //玩家充值
            if (EventKey.ACTOR_RECHARGE_EVENT.equals(event.name)) {
                ActorRechargeEvent rechargeEvent = event.convert();
                //获取活动个人数据
                long actorId = rechargeEvent.getActorId();
                int chargeId = rechargeEvent.getChargeId();
                //活动个人数据
                ActivityRecord activityRecord = activityRecordDao.getActivityRecord(actorId, activityId);
                ActivityRecord21 record21;
                if (activityRecord == null) {
                    record21 = ActivityRecord21.valueOf();
                    activityRecord = activityRecordDao.create(actorId, activityId, JSON.toJSONString(record21));
                } else {
                    record21 = JSON.parseObject(activityRecord.getRecord(), ActivityRecord21.class);
                }
                Optional<Activity21ChargeRewardConfig> optional = Activity21ConfigService.activity21ChargeRewardConfig(data, chargeId);
                if (!optional.isPresent()) {
                    return;
                }
                Activity21ChargeRewardConfig config = optional.get();
                record21.buyReward(config.getId());
                activityRecord.setRecord(JSON.toJSONString(record21));
                dbQueue.updateQueue(activityRecord);
                this.pushActivity(actorId, activityId, activityOpenConfig.getActivityType());
            }
        }
    }

    @Override
    protected ActivityType getType() {
        return ActivityType.ACTIVITY_TYPE_21;
    }

    @Override
    protected void onActivityOpen(Collection<ActivityOpenConfig> openActivityIds) {
        for (ActivityOpenConfig openConfig : openActivityIds) {
            int activityId = openConfig.getId();
            WorldRpcHelper.getProxy(WorldActivityRpc.class).activity21Open(GameConfig.getServerType(), activityId, getActivityOpenDate(activityId));
        }
    }

    @Override
    protected void onActivityShow(Collection<ActivityOpenConfig> endActivityIds) {
        for (ActivityOpenConfig openConfig : endActivityIds) {
            int activityId = openConfig.getId();
            int data = openConfig.getData();
            Optional<Integer> scoreRankOptional = Activity21ConfigService.getRankLimitCount(data, Activity21RankType.SCORE_RANK.getId());
            Optional<Integer> luckyRankOptional = Activity21ConfigService.getRankLimitCount(data, Activity21RankType.LUCKY_RANK.getId());
            if (!scoreRankOptional.isPresent() || !luckyRankOptional.isPresent()) {
                continue;
            }
            int scoreRankLimit = scoreRankOptional.get();
            int luckyRankLimit = luckyRankOptional.get();
            Collection<ActivityScoreRankVO> scoreRankList = WorldRpcHelper.getProxy(WorldActivityRpc.class)
                .getActivity21ScoreRankList(GameConfig.getServerType(), 0L, activityId, getActivityOpenDate(activityId), scoreRankLimit);
            Collection<ActivityScoreRankVO> luckyRankList = WorldRpcHelper.getProxy(WorldActivityRpc.class)
                .getActivity21LuckyRankList(GameConfig.getServerType(), 0L, activityId, getActivityOpenDate(activityId), luckyRankLimit);
            sendLuckyRankRewards(openConfig, luckyRankList);
            sendScoreRankRewards(openConfig, scoreRankList);
            sendAttendRewards(openConfig, scoreRankList, luckyRankList);
        }
    }

    @Override
    public boolean taskOpenCondition(TaskConfig taskConfig) {
        for (ActivityOpenConfig activityConfig : ActivityOpenConfigService.getActivityOpenConfigList(getType())) {
            if (Activity21ConfigService.isContainsTask(activityConfig.getData(), taskConfig.getTaskId())) {
                return true;
            }
        }
        return false;
    }

    private void sendAttendRewards(ActivityOpenConfig openConfig, Collection<ActivityScoreRankVO> scoreRankList, Collection<ActivityScoreRankVO> luckyRankList) {
        int attendLimit = globalConfigService.findGlobalConfig(GlobalConfigKey.ACTIVITY_21_RANK_LIMIT_SCORE).findInt();
        Optional<Activity21RankConfig> scoreOptional = Activity21ConfigService.getRankConfig(openConfig.getData(), Activity21RankType.SCORE_RANK.getId(), 0L);
        if (!scoreOptional.isPresent()) {
            LOGGER.error("Activity21RankConfig not found, data:{},  rank:{}", openConfig.getData(), 0);
            return;
        }
        Optional<Activity21RankConfig> luckyOptional = Activity21ConfigService.getRankConfig(openConfig.getData(), Activity21RankType.LUCKY_RANK.getId(), 0L);
        if (!luckyOptional.isPresent()) {
            LOGGER.error("Activity21RankConfig not found, data:{},  rank:{}", openConfig.getData(), 0);
            return;
        }
        List<Long> actorIds = activityRecordDao.getActorIds(openConfig.getId());
        for (Long actorId : actorIds) {
            ActivityRecord activityRecord = activityRecordDao.getActivityRecord(actorId, openConfig.getId());
            if (activityRecord == null) {
                continue;
            }
            ActivityRecord21 record21 = JSON.parseObject(activityRecord.getRecord(), ActivityRecord21.class);
            if (record21.getScore() >= attendLimit) {
                Map<String, String> params = Maps.newHashMap();
                params.put("name", ActorHelper.getActorName(actorId));
                if (scoreRankList.stream().noneMatch(x -> Long.parseLong(x.getAttributes().get((byte) TypeProtocol.ActorFieldType.ACTOR_ID_VALUE).toString()) == actorId)) {
                    Collection<RewardObject> rewards = Lists.newArrayList();
                    rewards.addAll(scoreOptional.get().getRewardList());
                    DispatchHelper.postEvent(new MailAddEvent(actorId, MailTemplateType.ACTIVITY_21_SCORE_ATTEND_REWARD, params, rewards));
                }
            }
            if (record21.getMaxNumber() > 0) {
                Map<String, String> params = Maps.newHashMap();
                params.put("name", ActorHelper.getActorName(actorId));
                if (luckyRankList.stream().noneMatch(x -> Long.parseLong(x.getAttributes().get((byte) TypeProtocol.ActorFieldType.ACTOR_ID_VALUE).toString()) == actorId)) {
                    Collection<RewardObject> rewards = Lists.newArrayList();
                    rewards.addAll(luckyOptional.get().getRewardList());
                    DispatchHelper.postEvent(new MailAddEvent(actorId, MailTemplateType.ACTIVITY_21_LUCKY_ATTEND_REWARD, params, rewards));
                }
            }
        }
    }

    private void sendLuckyRankRewards(ActivityOpenConfig activityOpenConfig, Collection<ActivityScoreRankVO> rankList) {
        for (ActivityScoreRankVO rank : rankList) {
            long actorId = Long.parseLong(rank.getAttributes().get((byte) TypeProtocol.ActorFieldType.ACTOR_ID_VALUE).toString());
            Actor actor = ActorHelper.getActor(actorId);
            if (actor != null) {
                Optional<Activity21RankConfig> optional =
                    Activity21ConfigService.getRankConfig(activityOpenConfig.getData(), Activity21RankType.LUCKY_RANK.getId(), rank.getRank());
                if (!optional.isPresent()) {
                    LOGGER.error("Activity21RankConfig not found, data:{},  rank:{}", activityOpenConfig.getData(), rank.getRank());
                    continue;
                }
                Collection<RewardObject> rewards = Lists.newArrayList();
                rewards.addAll(optional.get().getRewardList());
                Map<String, String> params = Maps.newHashMap();
                params.put("name", ActorHelper.getActorName(actorId));
                params.put("rank", String.valueOf(rank.getRank()));
                DispatchHelper.postEvent(new MailAddEvent(actorId, MailTemplateType.ACTIVITY_21_LUCKY_RANK_REWARD, params, rewards));
            }
        }
    }

    private void sendScoreRankRewards(ActivityOpenConfig activityOpenConfig, Collection<ActivityScoreRankVO> rankList) {
        for (ActivityScoreRankVO rank : rankList) {
            long actorId = Long.parseLong(rank.getAttributes().get((byte) TypeProtocol.ActorFieldType.ACTOR_ID_VALUE).toString());
            Actor actor = ActorHelper.getActor(actorId);
            if (actor != null) {
                Optional<Activity21RankConfig> optional =
                    Activity21ConfigService.getRankConfig(activityOpenConfig.getData(), Activity21RankType.SCORE_RANK.getId(), rank.getRank());
                if (!optional.isPresent()) {
                    LOGGER.error("Activity21RankConfig not found, data:{},  rank:{}", activityOpenConfig.getData(), rank.getRank());
                    continue;
                }
                Collection<RewardObject> rewards = Lists.newArrayList();
                rewards.addAll(optional.get().getRewardList());
                Map<String, String> params = Maps.newHashMap();
                params.put("name", ActorHelper.getActorName(actorId));
                params.put("rank", String.valueOf(rank.getRank()));
                DispatchHelper.postEvent(new MailAddEvent(actorId, MailTemplateType.ACTIVITY_21_SCORE_RANK_REWARD, params, rewards));
            }
        }
    }

    @Override
    protected void onActivityEnd(Collection<ActivityOpenConfig> endActivityIds) {
        for (ActivityOpenConfig activityOpenConfig : endActivityIds) {
            //充值补发
            List<Long> activityActorIds = activityRecordDao.getActorIds(activityOpenConfig.getId());
            for (Long actorId : activityActorIds) {
                rewardClear(actorId, activityOpenConfig);
            }
            //获取该任务类型的玩家列表
            TaskType taskType = TaskType.getType(getType());
            Collection<Long> actorIds = taskFacade.getActorIdsByTaskType(taskType);
            //活动结束删除活动任务
            for (Long actorId : actorIds) {
                DispatchHelper.postEvent(new ActivityEndDeleteTaskEvent(actorId, taskType));
            }
            //获取需删除的道具
            Collection<Integer> goodsIdList = ActivityDeleteGoodsConfigService.getGoodsIdList(activityOpenConfig.getActivityType());
            if (goodsIdList != null) {
                //获取该道具ID的玩家列表
                Collection<Long> goodsActorIds = goodsFacade.getActorIdsByGoodsId(goodsIdList);
                //活动结束删除活动道具
                for (Long actorId : goodsActorIds) {
                    DispatchHelper.postEvent(new GoodsDeleteEvent(actorId, goodsIdList, OperationType.ACTIVITY_TYPE_21));
                }
            }
        }
    }

    @EventOnline(trigger = TriggerTime.FIXED_HOUR, triggerValue = 24)
    public void onFixedHour(GameEvent e) {
        this.reset(e.getUniqueId(), true);
    }

    @Event(name = EventKey.ACTOR_LOGIN)
    public void onActorLogin(ActorLoginEvent event) {
        this.reset(event.actorId, false);
    }

    public void reset(long actorId, boolean push) {
        Collection<ActivityOpenConfig> configList = ActivityOpenConfigService.getActivityOpenConfigList(getType());
        for (ActivityOpenConfig config : configList) {
            int activityId = config.getId();
            if (isActivityOpen(activityId) == false) {
                continue;
            }
            ActivityGlobal activityGlobal = activityGlobalDao.getActivityGlobal(activityId);
            if (activityGlobal.getStatus() == ActivityStatus.SHOW.getId()) {
                continue;
            }
            ActivityRecord activityRecord = activityRecordDao.getActivityRecord(actorId, config.getId());
            if (activityRecord != null) {
                ActivityRecord21 record21 = JSON.parseObject(activityRecord.getRecord(), ActivityRecord21.class);
                if (!DateUtils.isToday(record21.getLastRestTime())) {
                    record21.reset();
                    activityRecord.setRecord(JSON.toJSONString(record21));
                    dbQueue.updateQueue(activityRecord);
                }
            }
            if (push) {
                asyncPushActivity(actorId, activityId, Activity21RankType.NONE);
            }
        }
    }

    private void rewardClear(long actorId, ActivityOpenConfig config) {
        ActivityRecord activityRecord = activityRecordDao.getActivityRecord(actorId, config.getId());
        if (activityRecord != null) {
            ActivityRecord21 record21 = JSON.parseObject(activityRecord.getRecord(), ActivityRecord21.class);
            List<RewardObject> rewards = Lists.newArrayList();
            record21.getBuyMap().forEach((k, v) -> {
                Activity21ChargeRewardConfig rewardConfig = globalConfigService.findConfig(IdentiyKey.build(config.getData(), k), Activity21ChargeRewardConfig.class);
                if (rewardConfig == null) {
                    LOGGER.error("Activity21ChargeRewardConfig not found,data:{},id:{}", config.getData(), k);
                    return;
                }
                if (v > record21.receiveCount(k)) {
                    int num = v - record21.receiveCount(k);
                    rewards.addAll(RewardHelper.multipleRewardList(rewardConfig.getRewardList(), num));
                }
            });
            Collection<Integer> goodsIdList = ActivityDeleteGoodsConfigService.getGoodsIdList(config.getActivityType());
            rewards.removeIf(x -> goodsIdList.contains(x.getId()));
            sendReward(rewards, actorId, MailTemplateType.ACTIVITY_21_RECHARGE_REWARD);
        }
    }

    /**
     * 1.摇奖
     * 2.领取礼包
     * 3.获取排行榜
     */
    @Override
    public TResult<ActivityProtocol.ActivityActionResponse> action(long actorId, int activityId, int id, byte[] value) {
        ActivityOpenConfig activityOpenConfig = ActivityOpenConfigService.getActivityConfig(activityId);
        if (activityOpenConfig == null) {
            LOGGER.error("ActivityOpenConfig not found,activityId:{}", activityId);
            return TResult.valueOf(CONFIG_NOT_FOUND);
        }
        int num;
        try {
            num = ActivityInfoProtocol.Activity21RequestValue.parseFrom(value).getNum();
            if (num <= 0 || num > GameConfig.getClientCountLimit()) {
                return TResult.valueOf(INVALID_PARAM);
            }
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("receiveReward byte value error");
            throw new RuntimeException(e);
        }
        int data = activityOpenConfig.getData();
        ActivityRecord activityRecord = activityRecordDao.getActivityRecord(actorId, activityId);
        ActivityRecord21 record21;
        if (activityRecord == null) {
            record21 = ActivityRecord21.valueOf();
            activityRecord = activityRecordDao.create(actorId, activityId, JSON.toJSONString(record21));
        } else {
            record21 = JSON.parseObject(activityRecord.getRecord(), ActivityRecord21.class);
        }
        Collection<RewardObject> rewardList = Lists.newArrayList();
        ActivityGlobal activityGlobal = activityGlobalDao.getActivityGlobal(activityOpenConfig.getId());
        ActivityProtocol.Activity21DrawResponse.Builder drawResponseBuilder = ActivityProtocol.Activity21DrawResponse.newBuilder();
        Activity21RankType type = Activity21RankType.NONE;
        switch (id) {
            case 1:
                if (activityGlobal.getStatus() == ActivityStatus.SHOW.getId()) {
                    return TResult.valueOf(ACTIVITY_STATUS_ERROR);
                }
                Optional<Collection<RewardObject>> optional = Activity21ConfigService.getCostListByData(data);
                if (!optional.isPresent()) {
                    LOGGER.error("activity21 cost config not found,data:{}", data);
                    return TResult.valueOf(CONFIG_NOT_FOUND);
                }
                Collection<RewardObject> singleCost = optional.get();
                Collection<RewardObject> costList = RewardHelper.multipleRewardList(singleCost, num);
                Result costResult = RewardHelper.decrease(actorId, costList, OperationType.ACTIVITY_TYPE_21);
                if (costResult.isFail()) {
                    return TResult.valueOf(costResult.statusCode);
                }
                Optional<Map<Integer, Activity21NumberWeightConfig>> weightOptional = Activity21ConfigService.getNumberWeightConfigByData(data);
                if (!weightOptional.isPresent()) {
                    LOGGER.error("Activity21NumberWeightConfig not found,data:{}", data);
                    return TResult.valueOf(CONFIG_NOT_FOUND);
                }
                Optional<List<Activity21NumberRewardConfig>> rewardOptional = Activity21ConfigService.getRewardConfigs(data);
                if (!rewardOptional.isPresent()) {
                    LOGGER.error("Activity21NumberRewardConfig not found,data:{}", data);
                    return TResult.valueOf(CONFIG_NOT_FOUND);
                }
                int reportLimit = globalConfigService.findGlobalConfig(GlobalConfigKey.ACTIVITY_21_REPORT_LIMIT).findInt();
                Map<Integer, Activity21NumberWeightConfig> configMap = weightOptional.get();
                List<Activity21NumberRewardConfig> rewardConfigs = rewardOptional.get();
                int maxNum = 0;
                for (int i = 0; i < num; i++) {
                    ActivityProtocol.Activity21Result.Builder resultBuilder = ActivityProtocol.Activity21Result.newBuilder();
                    StringBuilder number = new StringBuilder();
                    for (Map.Entry<Integer, Activity21NumberWeightConfig> entry : configMap.entrySet()) {
                        number.append(entry.getValue().getRandomNumber());
                    }
                    boolean isTrigger = false;
                    int addScore = 0;
                    Collection<RewardObject> singleReward = Lists.newArrayList();
                    for (Activity21NumberRewardConfig rewardConfig : rewardConfigs) {
                        if (rewardConfig.isMatch(number.toString())) {
                            if (rewardConfig.isTrigger()) {
                                isTrigger = true;
                                ActivityInfoProtocol.Activity21ReportEntity.Builder builder = ActivityInfoProtocol.Activity21ReportEntity.newBuilder();
                                builder.setActorProfile(ActorHelper.getActorProfile(actorId));
                                builder.setNum(Integer.parseInt(number.toString()));
                                builder.setRewardList(PbBuilder.buildRewardObjectList(rewardConfig.getRewardList()));
                                WorldRpcHelper.getProxy(WorldActivityRpc.class)
                                    .refreshActivity21ReportList(GameConfig.getServerType(), activityId, getActivityOpenDate(activityId), ActorHelper.getActorAttributeMap(actorId),
                                        Integer.parseInt(number.toString()), rewardConfig.getRewardList(), reportLimit);
                                DispatchHelper.postEvent(new ActivityLuckyNumberEvent(actorId, Integer.parseInt(number.toString()), rewardConfig.getRewardList()));
                            }
                            addScore += rewardConfig.getScore();
                            rewardList.addAll(rewardConfig.getRewardList());
                            singleReward.addAll(rewardConfig.getRewardList());
                            record21.addScore(rewardConfig.getScore());
                        }
                    }
                    if (Integer.parseInt(number.toString()) > maxNum) {
                        maxNum = Integer.parseInt(number.toString());
                    }
                    resultBuilder.setNumber(Integer.parseInt(number.toString()));
                    resultBuilder.setTrigger(isTrigger);
                    resultBuilder.setScore(addScore);
                    resultBuilder.setRewardObject(PbBuilder.buildRewardObjectList(singleReward));
                    drawResponseBuilder.addList(resultBuilder);
                }
                if (maxNum > record21.getMaxNumber()) {
                    record21.setMaxNumber(maxNum);
                    WorldRpcHelper.getProxy(WorldActivityRpc.class)
                        .refreshActivity21LuckyRank(GameConfig.getServerType(), activityId, getActivityOpenDate(activityId), ActorHelper.getActorAttributeMap(actorId),
                            record21.getMaxNumber());
                }
                record21.addRewardList(rewardList);
                int limitScore = globalConfigService.findGlobalConfig(GlobalConfigKey.ACTIVITY_21_RANK_LIMIT_SCORE).findInt();
                if (record21.getScore() >= limitScore) {
                    WorldRpcHelper.getProxy(WorldActivityRpc.class)
                        .refreshActivity21ScoreRank(GameConfig.getServerType(), activityId, getActivityOpenDate(activityId), ActorHelper.getActorAttributeMap(actorId),
                            record21.getScore());
                }
                break;
            case 2:
                if (activityGlobal.getStatus() == ActivityStatus.SHOW.getId()) {
                    return TResult.valueOf(ACTIVITY_STATUS_ERROR);
                }
                Activity21ChargeRewardConfig config = globalConfigService.findConfig(IdentiyKey.build(data, num), Activity21ChargeRewardConfig.class);
                if (config == null) {
                    LOGGER.error("Activity21ChargeRewardConfig not found,data:{},id:{}", data, num);
                    return TResult.valueOf(CONFIG_NOT_FOUND);
                }
                if (config.getChargeId() != 0) {
                    if (record21.isReceive(num)) {
                        return TResult.valueOf(RECRUIT_REWARD_HAD_RECEIVE);
                    }
                } else {
                    if (config.getTimes() <= record21.getDayTimes(num)) {
                        return TResult.valueOf(RECRUIT_REWARD_HAD_RECEIVE);
                    }
                }
                Collection<RewardObject> costs = config.getCostList();
                if (!costs.isEmpty()) {
                    Result decrease = RewardHelper.decrease(actorId, costs, OperationType.ACTIVITY_TYPE_21);
                    if (decrease.isFail()) {
                        return TResult.valueOf(decrease.statusCode);
                    }
                    record21.buyReward(num);
                }
                rewardList = config.getRewardList();
                record21.receiveReward(num);
                break;
            case 3:
                type = Activity21RankType.getById(num);
                if (type == Activity21RankType.NONE) {
                    return TResult.valueOf(INVALID_PARAM);
                }
                break;
        }
        activityRecord.setRecord(JSON.toJSONString(record21));
        dbQueue.updateQueue(activityRecord);
        CommonProtocol.RewardResult rewardResult = RewardHelper.sendRewardList(actorId, rewardList, OperationType.ACTIVITY_TYPE_21);
        ActivityProtocol.ActivityActionResponse.Builder builder = ActivityProtocol.ActivityActionResponse.newBuilder();
        builder.setActivityId(activityId);
        builder.setRewardResult(rewardResult);
        builder.setValue(drawResponseBuilder.build().toByteString());
        this.asyncPushActivity(actorId, activityId, type);
        return TResult.sucess(builder.build());
    }

    /**
     * 异步推送
     *
     * @param actorId
     * @param activityId
     */
    private void asyncPushActivity(long actorId, int activityId, Activity21RankType type) {
        ActivityOpenConfig activityOpenConfig = ActivityOpenConfigService.getActivityConfig(activityId);
        if (activityOpenConfig == null) {
            LOGGER.error("ActivityOpenConfig not found,activityId:{}", activityId);
            return;
        }
        Map<Integer, Integer> configMap = globalConfigService.findGlobalConfig(GlobalConfigKey.ACTIVITY_21_RANK_NUM_LIMIT).getIntMap();
        Optional<Integer> optional = Activity21ConfigService.getRankLimitCount(activityOpenConfig.getData(), type.getId());
        ByteString byteStringRecord = getByteStringRecord(actorId, activityId);
        WorldRpcHelper.asynCall(actorId, WorldActivityRpc.class, rpcProxy -> {
            if (type == Activity21RankType.LUCKY_RANK) {
                if (!optional.isPresent()) {
                    LOGGER.error("Activity21 rank limit config not found,activityId:{}", activityId);
                    return;
                }
                rpcProxy.getActivity21LuckyRankList(GameConfig.getServerType(), actorId, activityId, getActivityOpenDate(activityId), configMap.get(type.getId()));
            } else if (type == Activity21RankType.SCORE_RANK) {
                if (!optional.isPresent()) {
                    LOGGER.error("Activity21 rank limit config not found,activityId:{}", activityId);
                    return;
                }
                rpcProxy.getActivity21ScoreRankList(GameConfig.getServerType(), actorId, activityId, getActivityOpenDate(activityId), configMap.get(type.getId()));
            } else {
                rpcProxy.getActivity21ReportList(GameConfig.getServerType(), activityId, getActivityOpenDate(activityId));
            }
        }, new RpcCallback() {
            @Override
            public int getDispatchType() {
                return DispatchType.ACTOR;
            }

            @SuppressWarnings("unchecked")
            @Override
            public void completed(Object result) {
                if (type == Activity21RankType.SCORE_RANK) {
                    ActivityInfoProtocol.Activity21Global.Builder builder = ActivityInfoProtocol.Activity21Global.newBuilder();
                    for (ActivityScoreRankVO rankVO : (Collection<ActivityScoreRankVO>) result) {
                        ActivityInfoProtocol.ActivityRank.Builder rankBuilder = ActivityInfoProtocol.ActivityRank.newBuilder();
                        rankBuilder.setActorProfile(PbBuilder.buildActorProfile(rankVO.getAttributes()));
                        rankBuilder.setRank(rankVO.getRank());
                        rankBuilder.setValue(rankVO.getScore());
                        builder.addScoreRank(rankBuilder);
                    }
                    pushActivity(actorId, activityId, activityOpenConfig.getActivityType(), builder.build().toByteString(), byteStringRecord);
                } else if (type == Activity21RankType.LUCKY_RANK) {
                    ActivityInfoProtocol.Activity21Global.Builder builder = ActivityInfoProtocol.Activity21Global.newBuilder();
                    for (ActivityScoreRankVO rankVO : (Collection<ActivityScoreRankVO>) result) {
                        ActivityInfoProtocol.ActivityRank.Builder rankBuilder = ActivityInfoProtocol.ActivityRank.newBuilder();
                        rankBuilder.setActorProfile(PbBuilder.buildActorProfile(rankVO.getAttributes()));
                        rankBuilder.setRank(rankVO.getRank());
                        rankBuilder.setValue(rankVO.getScore());
                        builder.addLuckRank(rankBuilder);
                    }
                    pushActivity(actorId, activityId, activityOpenConfig.getActivityType(), builder.build().toByteString(), byteStringRecord);
                } else {
                    ActivityInfoProtocol.Activity21Global.Builder builder = ActivityInfoProtocol.Activity21Global.newBuilder();
                    for (Activity21ReportRecord reportRecord : (Collection<Activity21ReportRecord>) result) {
                        Activity21ReportEntity.Builder recordBuild = Activity21ReportEntity.newBuilder();
                        recordBuild.setActorProfile(PbBuilder.buildActorProfile(reportRecord.getAttributes()));
                        recordBuild.setRewardList(PbBuilder.buildRewardObjectList(reportRecord.getRewards()));
                        recordBuild.setNum(reportRecord.getNum());
                        builder.addReportList(recordBuild);
                    }
                    pushActivity(actorId, activityId, activityOpenConfig.getActivityType(), builder.build().toByteString(), byteStringRecord);
                }
            }
        });
    }

    private ByteString getByteStringRecord(long actorId, int activityId) {
        ActivityRecord record = activityRecordDao.getActivityRecord(actorId, activityId);
        int day = ActivityOpenConfigService.getActivityOpenDay(activityId);
        ActivityInfoProtocol.Activity21RecordVO.Builder builder = ActivityInfoProtocol.Activity21RecordVO.newBuilder();
        if (record != null) {
            ActivityRecord21 record21 = JSON.parseObject(record.getRecord(), ActivityRecord21.class);
            builder.setScore(record21.getScore());
            builder.setMaxNumber(record21.getMaxNumber());
            builder.putAllBuyMap(record21.getBuyMap());
            builder.putAllReceiveMap(record21.getReceiveMap());
            builder.putAllTodayBuyMap(record21.getTodayBuyMap());
            builder.setRewardList(PbBuilder.buildRewardObjectList(record21.getRewardList()));
        }
        builder.setDay(day);
        return builder.build().toByteString();
    }

    @Override
    public TResult<ByteString> getRecord2Client(long actorId, int activityId) {
        asyncPushActivity(actorId, activityId, Activity21RankType.NONE);
        return TResult.fail();
    }

    @Override
    public TResult<ByteString> getGlobal2Client(long actorId, int activityId) {
        return TResult.fail();
    }
}
