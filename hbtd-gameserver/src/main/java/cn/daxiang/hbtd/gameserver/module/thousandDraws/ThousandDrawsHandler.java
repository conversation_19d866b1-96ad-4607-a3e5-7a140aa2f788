package cn.daxiang.hbtd.gameserver.module.thousandDraws;

import cn.daxiang.framework.dispatch.DispatchType;
import cn.daxiang.framework.protocal.DataPacket;
import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.framework.router.annotation.Cmd;
import cn.daxiang.hbtd.gameserver.core.database.table.ThousandDraws;
import cn.daxiang.hbtd.gameserver.core.router.GatewayRouterHandlerImpl;
import cn.daxiang.hbtd.gameserver.module.PbBuilder;
import cn.daxiang.hbtd.gameserver.module.thousandDraws.facade.ThousandDrawsFacade;
import cn.daxiang.hbtd.gameserver.module.thousandDraws.helper.ThousandDrawsHelper;
import cn.daxiang.protocol.game.CommonProtocol;
import cn.daxiang.protocol.game.CommonProtocol.IntPacket;
import cn.daxiang.protocol.game.CommonProtocol.RewardObjectList;
import cn.daxiang.protocol.game.CommonProtocol.RewardResult;
import cn.daxiang.protocol.game.ModuleProtocol.Module;
import cn.daxiang.protocol.game.ThousanddrawsProtocol.ThousandDrawsResponse;
import cn.daxiang.protocol.game.ThousanddrawsProtocol.ThousandDrawsRewardResponse;
import cn.daxiang.protocol.game.ThousanddrawsProtocol.ThousanddrawsCmd;
import io.netty.channel.Channel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/8/26
 */
@Component
public class ThousandDrawsHandler extends GatewayRouterHandlerImpl {

    @Autowired
    private ThousandDrawsFacade thousandDrawsFacade;

    @Override
    public int getModule() {
        return Module.THOUSANDDRAWS_VALUE;
    }

    @Cmd(Id = ThousanddrawsCmd.THOUSAND_DRAWS_GET_VALUE, dispatchType = DispatchType.ACTOR)
    public void getThousandDraws(Channel channel, Long actorId, DataPacket packet) {
        TResult<ThousandDraws> result = thousandDrawsFacade.getThousandDraws(actorId);
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        ThousandDrawsResponse response = ThousandDrawsHelper.buildThousandDrawsResponse(result.item);
        channelWrite(channel, packet, response);
    }

    @Cmd(Id = ThousanddrawsCmd.THOUSAND_DRAWS_VALUE, dispatchType = DispatchType.ACTOR)
    public void thousandDraws(Channel channel, Long actorId, DataPacket packet) {
        TResult<ThousandDrawsRewardResponse> result = thousandDrawsFacade.thousandDraws(actorId);
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = ThousanddrawsCmd.THOUSAND_DRAWS_REPLACE_VALUE, dispatchType = DispatchType.ACTOR)
    public void thousandDrawsReplace(Channel channel, Long actorId, DataPacket packet) {
        IntPacket request = packet.getValue(IntPacket.parser());
        Result result = thousandDrawsFacade.replace(actorId, request.getValue());
        channelWrite(channel, packet, result.statusCode);
    }

    @Cmd(Id = ThousanddrawsCmd.THOUSAND_DRAWS_RECEIVE_VALUE, dispatchType = DispatchType.ACTOR)
    public void receive(Channel channel, Long actorId, DataPacket packet) {
        IntPacket request = packet.getValue(IntPacket.parser());
        TResult<RewardObjectList> result = thousandDrawsFacade.receive(actorId, request.getValue());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }
}
