package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.identity.IdentiyKey;

/**
 * 归藏秘境挂机道具表
 *
 * <AUTHOR>
 * @date 2023/9/14
 */
@DataFile(fileName = "sanctuary_hangup_goods_config")
public class SanctuaryHangupGoodsConfig implements ModelAdapter {
    /**
     * 道具ID，可在道具表中用相同ID获得道具的图标之类信息
     */
    private int id;
    /**
     * 相当于挂机时间-分钟
     */
    private int time;
    /**
     * 挂机收益一般为按基础收益+当前关卡提升收益进行计算，上限关卡则表示该道具最多能按第几关计算收益
     * 配置为storyId
     */
    private int limitStory;

    @Override
    public void initialize() {

    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(id);
    }

    public int getId() {
        return id;
    }

    public int getTime() {
        return time;
    }

    public int getLimitStory() {
        return limitStory;
    }
}
