package cn.daxiang.hbtd.gameserver.module.battle.parser.battle.impl;

import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.hbtd.gameserver.core.database.table.TreasureSnatch;
import cn.daxiang.hbtd.gameserver.module.PbBuilder;
import cn.daxiang.hbtd.gameserver.module.battle.helper.BattlePushHelper;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleMember;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleRoom;
import cn.daxiang.hbtd.gameserver.module.battle.parser.battle.AbstractBattleParser;
import cn.daxiang.hbtd.gameserver.module.treasure.facade.TreasureSnatchFacade;
import cn.daxiang.hbtd.gameserver.module.treasure.type.TreasureSnatchRecordState;
import cn.daxiang.protocol.game.BattleProtocol.BattleCamp;
import cn.daxiang.protocol.game.BattleProtocol.BattleResultResponse;
import cn.daxiang.protocol.game.CommonProtocol.RewardResult;
import cn.daxiang.shared.GlobalConfigKey;
import cn.daxiang.shared.module.battle.BattleParameterKey;
import cn.daxiang.shared.module.battle.BattleType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class TreasureSnatchBattleParser extends AbstractBattleParser {
    @Autowired
    private TreasureSnatchFacade treasureSnatchFacade;

    @Override
    public Result fight(long actorId, Map<BattleParameterKey, Object> parameterMap) {
        long targetActorId = (long) parameterMap.get(BattleParameterKey.TARGET_ACTOR_ID);
        TreasureSnatchRecordState state = (TreasureSnatchRecordState) parameterMap.get(BattleParameterKey.TREASURE_SNATCH_TYPE);
        BattleMember leftMember = this.createBattleMember(actorId, BattleCamp.LEFT_CAMP, getType());
        BattleMember rightMember;
        TreasureSnatch treasureSnatch = treasureSnatchFacade.getTreasureSnatch(actorId);
        if (state == TreasureSnatchRecordState.REVENGE || treasureSnatch.isInitialize()) {
            rightMember = this.createBattleMember(targetActorId, BattleCamp.RIGHT_CAMP, getType());
        } else {
            int monsterGroupId = globalConfigService.findGlobalConfig(GlobalConfigKey.TREASURE_SNATCH_INITIALIZE_MONSTER_GROUP_ID).findInt();
            TResult<BattleMember> monsterMemberResult = this.createMonsterMember(targetActorId, monsterGroupId);
            if (monsterMemberResult.isFail()) {
                return Result.valueOf(monsterMemberResult.statusCode);
            }
            rightMember = monsterMemberResult.item;
            rightMember.setActorId(targetActorId);
        }
        BattleRoom battleRoom = this.createBattleRoom(leftMember, rightMember, parameterMap);
        battleRoomFacade.createBattleRoom(battleRoom);
        return Result.valueOf();
    }

    @Override
    public void battleEnd(long actorId, BattleRoom battleRoom) {
        boolean isWin = battleRoom.isWin();
        long targetId = (long) battleRoom.getParameterMap().get(BattleParameterKey.TARGET_ACTOR_ID);
        int fragmentId = (int) battleRoom.getParameterMap().get(BattleParameterKey.FRAGMENT_ID);
        TreasureSnatchRecordState state = (TreasureSnatchRecordState) battleRoom.getParameterMap().get(BattleParameterKey.TREASURE_SNATCH_TYPE);
        long time = (long) battleRoom.getParameterMap().get(BattleParameterKey.TREASURE_SNATCH_TIME);
        RewardResult rewardResult = treasureSnatchFacade.recordChallengeResult(actorId, fragmentId, targetId, state, time, isWin);
        BattleResultResponse response =
            PbBuilder.buildBattleResultResponse(battleRoom.getBattleType(), battleRoom.getMemberVOMap().values(), battleRoom.getRightHistoryBattleSpriteVO(),
                battleRoom.getBattleReportMap(), rewardResult, battleRoom.getBattleStats());
        BattlePushHelper.pushBattleResult(actorId, response);
    }

    @Override
    protected BattleType getType() {
        return BattleType.TREASURE_SNATCH;
    }
}