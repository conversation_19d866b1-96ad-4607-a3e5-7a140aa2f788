package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.dataconfig.annotation.FieldIgnore;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.shared.reward.RewardObject;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * 战力达标活动
 *
 * <AUTHOR>
 * @date 2023/11/10
 */
@DataFile(fileName = "activity_50_power_config")
public class Activity50PowerConfig implements ModelAdapter {
    /**
     * 活动DATA
     */
    private int data;
    /**
     * 唯一ID
     */
    private int id;
    /**
     * 达标战力
     */
    private long power;
    /**
     *
     */
    private String rewards;

    @FieldIgnore
    private List<RewardObject> rewardList = Lists.newArrayList();

    @Override
    public void initialize() {
        JSONArray rewardsArray = JSONArray.parseArray(rewards);
        for (Object rewardsItem : rewardsArray) {
            JSONArray rewardArray = JSONArray.parseArray(rewardsItem.toString());
            RewardObject rewardObject = RewardObject.valueOf(rewardArray);
            rewardList.add(rewardObject);
        }
    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(data, id);
    }

    public int getData() {
        return data;
    }

    public int getId() {
        return id;
    }

    public long getPower() {
        return power;
    }

    public List<RewardObject> getRewardList() {
        return rewardList;
    }

}
