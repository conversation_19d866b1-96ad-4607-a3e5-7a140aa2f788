package cn.daxiang.hbtd.gameserver.module.battle.parser.verify.impl;

import cn.daxiang.framework.result.TResult;
import cn.daxiang.hbtd.gameserver.module.battle.parser.verify.AbstractBattleVerifyParser;
import cn.daxiang.hbtd.gameserver.module.battlefieldDrill.facade.BattlefieldDrillFacade;
import cn.daxiang.hbtd.gameserver.module.battlefieldDrill.helper.BattlefieldDrillPushHelper;
import cn.daxiang.protocol.game.BattlefielddrillProtocol;
import cn.daxiang.protocol.game.CommonProtocol;
import cn.daxiang.shared.type.PVEVerifyParameterKey;
import cn.daxiang.shared.type.PVEVerifyType;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Maps;
import com.google.protobuf.InvalidProtocolBufferException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/4/27
 */
@Component
public class BattlefieldDrillBattleVerifyParser extends AbstractBattleVerifyParser {
    @Autowired
    private BattlefieldDrillFacade ancientCorridorFacade;

    public static void main(String[] args) {
        //[{上阵英雄ID:战力,上阵英雄ID:战力},[[英雄ID,技能ID,伤害,是否暴击(0:false.1:true)],[英雄ID,技能ID,伤害,是否暴击(0:false.1:true)]]]
        String data = "[{1:100,2:200},[[1,2,3,4],[2,2,3,4]]]";
        JSONArray jsonArray = JSON.parseArray(data);
        Map<Integer, Long> serverPowerMap = Maps.newHashMap();
        serverPowerMap.put(1, 100L);
        serverPowerMap.put(2, 200L);
        Map<Integer, Long> clientPowerMap = jsonArray.getJSONObject(0).toJavaObject(new TypeReference<Map<Integer, Long>>() {
        });
        System.err.println(clientPowerMap);
        for (Map.Entry<Integer, Long> entry : clientPowerMap.entrySet()) {
            int heroId = entry.getKey();
            long clientPower = entry.getValue();
            long serverPower = serverPowerMap.getOrDefault(heroId, 0L);
            System.err.println(heroId + ":" + clientPower + ":" + serverPower);
        }
        Collection<JSONArray> damageList = jsonArray.getJSONArray(1).toJavaList(JSONArray.class);
        System.err.println(damageList);
        for (JSONArray array : damageList) {
            int heroId = array.getIntValue(0);
            int skillId = array.getIntValue(1);
            long damage = array.getLongValue(2);
            int isCrit = array.getIntValue(3);
            System.err.println(heroId + ":" + skillId + ":" + damage + ":" + isCrit);
        }

    }

    @Override
    protected PVEVerifyType getType() {
        return PVEVerifyType.BATTLEFIELDDRILL;
    }

    @Override
    protected String getClientData(byte[] data) throws InvalidProtocolBufferException {
        BattlefielddrillProtocol.BattlefieldDrillChallengeRequest request = BattlefielddrillProtocol.BattlefieldDrillChallengeRequest.parseFrom(data);
        return request.getCheck();
    }

    @Override
    protected String getBattleRequest(byte[] data) throws InvalidProtocolBufferException {
        BattlefielddrillProtocol.BattlefieldDrillChallengeRequest request = BattlefielddrillProtocol.BattlefieldDrillChallengeRequest.parseFrom(data);
        return request.toString();
    }

    @Override
    protected void verifyResult(long actorId, Map<PVEVerifyParameterKey, Object> parameter, boolean result) throws InvalidProtocolBufferException {
        BattlefielddrillProtocol.BattlefieldDrillChallengeRequest request =
            BattlefielddrillProtocol.BattlefieldDrillChallengeRequest.parseFrom((byte[]) parameter.get(PVEVerifyParameterKey.BATTLE_VERIFY_REQUEST));
        TResult<CommonProtocol.RewardResult> battleResult = ancientCorridorFacade.verifyResult(actorId, request, result);
        BattlefieldDrillPushHelper.pushBattlefieldDrillBattleResult(actorId, battleResult);
    }
}
