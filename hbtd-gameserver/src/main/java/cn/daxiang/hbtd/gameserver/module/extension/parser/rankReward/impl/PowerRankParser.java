package cn.daxiang.hbtd.gameserver.module.extension.parser.rankReward.impl;

import cn.daxiang.hbtd.gameserver.core.database.table.RankTargetGlobal;
import cn.daxiang.hbtd.gameserver.module.extension.parser.rankReward.AbstractRankRewardParser;
import cn.daxiang.hbtd.gameserver.module.extension.type.RankRewardType;
import cn.daxiang.hbtd.gameserver.module.user.dao.ActorDao;
import cn.daxiang.hbtd.gameserver.module.user.helper.ActorHelper;
import cn.daxiang.hbtd.gameserver.module.user.model.PowerRank;
import cn.daxiang.protocol.game.CommonProtocol;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/4
 */
@Component
public class PowerRankParser extends AbstractRankRewardParser {
    @Autowired
    ActorDao actorDao;

    @Override
    protected RankRewardType getType() {
        return RankRewardType.POWER_RANK;
    }

    @Override
    public CommonProtocol.ActorProfile parser(int configId, RankTargetGlobal rankTargetGlobal) {
        List<PowerRank> ranks = actorDao.findSettleRank(1);
        long firstRankActorId = ranks.get(0).getKey();
        rankTargetGlobal.putRankActorId(configId, firstRankActorId);
        return ActorHelper.getActorProfile(firstRankActorId);
    }

}
