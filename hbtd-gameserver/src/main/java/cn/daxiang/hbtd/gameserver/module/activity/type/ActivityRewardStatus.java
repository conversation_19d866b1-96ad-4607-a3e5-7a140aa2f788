package cn.daxiang.hbtd.gameserver.module.activity.type;

/**
 * 活动奖励状态
 *
 * <AUTHOR>
 */
public enum ActivityRewardStatus {
    /**
     * 1.完成
     */
    FINISH(1),

    /**
     * 2.已领取
     */
    RECEIVED(2),

    /**
     * 0.未完成
     */
    NONE(0);

    private int id;

    ActivityRewardStatus(int id) {
        this.id = id;
    }

    public static ActivityRewardStatus getType(int id) {
        for (ActivityRewardStatus type : ActivityRewardStatus.values()) {
            if (type.id == id) {
                return type;
            }
        }
        return NONE;
    }

    public int getId() {
        return id;
    }

    @Override
    public String toString() {
        return String.valueOf(id);
    }
}
