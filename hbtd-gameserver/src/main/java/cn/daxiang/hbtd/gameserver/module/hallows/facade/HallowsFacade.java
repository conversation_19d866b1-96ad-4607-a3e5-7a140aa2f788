package cn.daxiang.hbtd.gameserver.module.hallows.facade;

import cn.daxiang.framework.result.CollectionResult;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.hbtd.gameserver.core.database.table.Hallows;
import cn.daxiang.hbtd.gameserver.core.oss.type.OperationType;
import cn.daxiang.protocol.game.CommonProtocol.RewardResult;
import cn.daxiang.protocol.game.HallowsProtocol.HallowsGachaInfoResponse;
import cn.daxiang.protocol.game.HallowsProtocol.HallowsListResponse;

import java.util.List;
import java.util.Map;

/**
 * @author: Gary
 * @date: 2023/10/10 14:09
 * @Description:
 */
public interface HallowsFacade {
    /**
     * 获取神器背包列表
     *
     * @param actorId
     * @return
     */
    CollectionResult<Hallows> getHallowsInfoList(long actorId);

    /**
     * 神器升级
     *
     * @param actorId
     * @param configIds
     * @return
     */
    TResult<HallowsListResponse> hallowsLevelUp(long actorId, List<Integer> configIds);

    /**
     * 获取神器召唤信息
     *
     * @param actorId
     * @return
     */
    TResult<HallowsGachaInfoResponse> getHallowsGachaInfo(long actorId);

    /**
     * 神器召唤抽奖
     *
     * @param actorId
     * @param times
     * @return
     */
    TResult<RewardResult> hallowsGacha(long actorId, int times);

    /**
     * 神器召唤充值礼包领取
     *
     * @param actorId
     * @param configId
     * @return
     */
    TResult<RewardResult> hallowsGachaChargeRewardReceive(long actorId, int configId);

    /**
     * 获取单个神器信息
     *
     * @param actorId
     * @param configId
     * @return
     */
    TResult<Hallows> getHallowsInfo(long actorId, int configId);

    /**
     * 获取神器积分
     *
     * @param actorId
     * @return
     */
    long getHallowsScore(long actorId);

    /**
     * 神器添加
     *
     * @param actorId
     * @param rewardMap
     * @param operationType
     */
    void addHallows(long actorId, Map<Integer, Long> rewardMap, OperationType operationType);
}
