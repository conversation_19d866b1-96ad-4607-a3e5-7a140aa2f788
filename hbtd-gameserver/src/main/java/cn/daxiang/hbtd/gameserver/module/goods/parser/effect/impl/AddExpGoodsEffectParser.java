package cn.daxiang.hbtd.gameserver.module.goods.parser.effect.impl;

import cn.daxiang.framework.result.TResult;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.GoodsConfig;
import cn.daxiang.hbtd.gameserver.core.oss.type.OperationType;
import cn.daxiang.hbtd.gameserver.module.goods.helper.RewardHelper;
import cn.daxiang.hbtd.gameserver.module.goods.parser.effect.AbstractGoodsEffectParser;
import cn.daxiang.hbtd.gameserver.module.goods.type.GoodsEffectType;
import cn.daxiang.protocol.game.CommonProtocol.RewardResult;
import cn.daxiang.protocol.game.TypeProtocol.ResourceId;
import cn.daxiang.protocol.game.TypeProtocol.RewardType;
import cn.daxiang.shared.reward.RewardObject;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

@Component
public class AddExpGoodsEffectParser extends AbstractGoodsEffectParser {

    @Override
    public TResult<RewardResult> execute(long actorId, long num, GoodsConfig goodsConfig, byte[] value, OperationType operationType) {
        int exp = goodsConfig.calcEffectValue();
        exp *= num;
        RewardResult rewardResult =
            RewardHelper.sendRewardList(actorId, Lists.newArrayList(RewardObject.valueOf(RewardType.RESOURCE.getNumber(), ResourceId.EXP.getNumber(), exp)), operationType);
        return TResult.sucess(rewardResult);
    }

    @Override
    protected GoodsEffectType getType() {
        return GoodsEffectType.ADD_EXP;
    }

}