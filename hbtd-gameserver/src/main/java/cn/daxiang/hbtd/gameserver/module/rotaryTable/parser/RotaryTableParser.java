package cn.daxiang.hbtd.gameserver.module.rotaryTable.parser;

import cn.daxiang.framework.result.TResult;
import cn.daxiang.hbtd.gameserver.core.database.table.RotaryTable;

import java.util.Collection;

/**
 * <AUTHOR>
 * @date 2023/12/22
 */
public interface RotaryTableParser {

    /**
     * 获取大转盘信息
     *
     * @param actorId
     * @return
     */
    TResult<RotaryTable> getRotaryTable(long actorId);

    /**
     * 转动转盘
     *
     * @param rotaryTable
     * @param count
     * @return
     */
    TResult<Collection<Integer>> action(RotaryTable rotaryTable, int count);

    /**
     * 开始新的一轮
     *
     * @param rotaryTable
     */
    void newRoundAction(RotaryTable rotaryTable);
}
