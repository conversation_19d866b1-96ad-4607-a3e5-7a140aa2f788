package cn.daxiang.hbtd.gameserver.module.escort.facade;

import cn.daxiang.framework.result.CollectionResult;
import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.hbtd.gameserver.core.database.table.EscortActor;
import cn.daxiang.protocol.game.BattleProtocol;
import cn.daxiang.protocol.game.CommonProtocol;
import cn.daxiang.protocol.game.EscortProtocol;
import cn.daxiang.shared.module.escort.Escort;
import cn.daxiang.shared.module.escort.EscortCart;
import cn.daxiang.shared.module.escort.EscortState;

import java.util.Collection;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/2/2
 */
public interface EscortFacade {

    /**
     * 获取攻城略地信息
     *
     * @param actorId
     * @return
     */
    TResult<Escort> getEscort(long actorId);

    /**
     * 获取押镖州郡信息
     *
     * @param actorId
     * @return
     */
    CollectionResult<EscortState> getEscortStateList(long actorId);

    /**
     * 获取押镖个人信息
     *
     * @param actorId
     * @return
     */
    TResult<EscortActor> getEscortActor(long actorId);

    /**
     * 获取镖车列表
     *
     * @param actorId
     * @param stateId
     * @param actorIdList
     * @return
     */
    CollectionResult<EscortCart> getEscortCartList(long actorId, int stateId, Collection<Long> actorIdList);

    /**
     * 获取个人镖车信息
     *
     * @param actorId
     * @return
     */
    TResult<EscortCart> getEscortCart(long actorId);

    /**
     * 刷新个人镖车
     *
     * @param actorId
     * @return
     */
    TResult<EscortCart> getRefreshEscortCart(long actorId);

    /**
     * 开始押镖
     *
     * @param actorId
     * @return
     */
    TResult<EscortCart> escortStart(long actorId);

    /**
     * 劫镖
     *
     * @param actorId
     * @param targetActorId
     * @return
     */
    Result robberyEscortCart(long actorId, long targetActorId);

    /**
     * 请求护镖
     *
     * @param actorId
     * @param targetServerId
     * @param targetActorId
     * @return
     */
    Result begEscort(long actorId, int targetServerId, long targetActorId);

    /**
     * 帮助护镖
     *
     * @param actorId
     * @param targetServerId
     * @param targetActorId
     * @return
     */
    Result helpEscort(long actorId, int targetServerId, long targetActorId);

    /**
     * @param actorId
     * @param targetActorIdList
     */
    CollectionResult<Long> getEscortEscortedList(long actorId, Collection<Long> targetActorIdList);

    /**
     * @param actorId
     * @param targetActorIdList
     */
    TResult<Map<Long, Integer>> getEscortNoDepartureList(long actorId, Collection<Long> targetActorIdList);

    /**
     * 获取录像
     *
     * @param actorId
     * @param battleId
     */
    TResult<BattleProtocol.BattleResultResponse> getBattle(long actorId, long battleId);

    /**
     * 领取任务奖励
     *
     * @param actorId
     * @param configId
     * @return
     */
    TResult<CommonProtocol.RewardResultResponse> getTaskReward(long actorId, int configId);

    /**
     * 完成押镖领奖
     *
     * @param actorId
     * @return
     */
    TResult<CommonProtocol.RewardResultResponse> escortCompleted(long actorId);

    /**
     * 获取GVG军团加成
     *
     * @param actorId
     * @return
     */
    TResult<CommonProtocol.LongPacket> getGVGNationRank(long actorId);

    /**
     * 押镖排行
     *
     * @param actorId
     * @return
     */
    TResult<EscortProtocol.EscortRankResponse> getEscortRank(long actorId);

    /**
     * 劫镖排行
     *
     * @param actorId
     * @return
     */
    TResult<EscortProtocol.EscortRobbingRankResponse> getEscortRobbingRank(long actorId);

    /**
     * 护镖排行
     *
     * @param actorId
     * @return
     */
    TResult<EscortProtocol.EscortHelpRankResponse> getEscortHelpRank(long actorId);

    /**
     * 红点状态
     *
     * @param actorId
     * @return
     */
    Result haveRedDot(long actorId);

    /**
     * 定时押运时间选择
     *
     * @param actorId
     * @param typeId
     * @return
     */
    Result fixedTime(long actorId, int typeId);

    /**
     * 购买高品质飙车
     *
     * @param actorId
     * @param configId
     * @return
     */
    Result buyEscortCart(long actorId, int configId);
}
