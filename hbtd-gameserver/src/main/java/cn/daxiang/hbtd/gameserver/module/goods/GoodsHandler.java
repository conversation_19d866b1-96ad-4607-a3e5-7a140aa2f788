package cn.daxiang.hbtd.gameserver.module.goods;

import cn.daxiang.framework.dispatch.DispatchType;
import cn.daxiang.framework.protocal.DataPacket;
import cn.daxiang.framework.result.CollectionResult;
import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.framework.router.annotation.Cmd;
import cn.daxiang.hbtd.gameserver.core.database.table.Goods;
import cn.daxiang.hbtd.gameserver.core.router.GatewayRouterHandlerImpl;
import cn.daxiang.hbtd.gameserver.module.goods.facade.GoodsFacade;
import cn.daxiang.protocol.game.CommonProtocol;
import cn.daxiang.protocol.game.CommonProtocol.RewardResultResponse;
import cn.daxiang.protocol.game.GoodsProtocol.ConvertFragmentRequest;
import cn.daxiang.protocol.game.GoodsProtocol.DecomposeRequest;
import cn.daxiang.protocol.game.GoodsProtocol.GoodsCmd;
import cn.daxiang.protocol.game.GoodsProtocol.GoodsListResponse;
import cn.daxiang.protocol.game.GoodsProtocol.SynthesisRequest;
import cn.daxiang.protocol.game.GoodsProtocol.TestRewardRequest;
import cn.daxiang.protocol.game.GoodsProtocol.UseGoodsRequest;
import cn.daxiang.protocol.game.GoodsProtocol.UseGoodsResponse;
import cn.daxiang.protocol.game.ModuleProtocol.Module;
import io.netty.channel.Channel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class GoodsHandler extends GatewayRouterHandlerImpl {

    @Autowired
    private GoodsFacade goodsFacade;

    @Override
    public int getModule() {
        return Module.GOODS_VALUE;
    }

    @Cmd(Id = GoodsCmd.GET_GOODS_LIST_VALUE, dispatchType = DispatchType.ACTOR)
    public void getGoodsList(Channel channel, Long actorId, DataPacket packet) {
        CollectionResult<Goods> result = goodsFacade.getGoodsList(actorId);
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }

        GoodsListResponse.Builder builder = GoodsListResponse.newBuilder();
        for (Goods goods : result.item) {
            CommonProtocol.Goods.Builder goodsBuilder =
                CommonProtocol.Goods.newBuilder().setGoodsId(goods.getGoodsId()).setGoodsUid(goods.getGoodsUid()).setNum(goods.getNum()).setTimes(goods.getTimes());
            builder.addGoods(goodsBuilder);
        }
        GoodsListResponse response = builder.build();
        channelWrite(channel, packet, response);
    }

    @Cmd(Id = GoodsCmd.USE_GOODS_VALUE, dispatchType = DispatchType.ACTOR)
    public void useGoods(Channel channel, Long actorId, DataPacket packet) {
        UseGoodsRequest request = packet.getValue(UseGoodsRequest.parser());
        TResult<UseGoodsResponse> result = goodsFacade.useGoods(actorId, request.getGoodsUid(), request.getNum(), request.getValue().toByteArray());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }

        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = GoodsCmd.CONVERT_FRAGMENT_VALUE, dispatchType = DispatchType.ACTOR)
    public void convertFragment(Channel channel, Long actorId, DataPacket packet) {
        ConvertFragmentRequest request = packet.getValue(ConvertFragmentRequest.parser());
        TResult<RewardResultResponse> result = goodsFacade.convertFragment(actorId, request.getFragmentid(), request.getGoodsid(), request.getCount());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = GoodsCmd.DECOMPOSE_VALUE, dispatchType = DispatchType.ACTOR)
    public void decompose(Channel channel, Long actorId, DataPacket packet) {
        DecomposeRequest request = packet.getValue(DecomposeRequest.parser());
        TResult<RewardResultResponse> result = goodsFacade.decompose(actorId, request.getGoodsMap());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = GoodsCmd.SYNTHESIS_VALUE, dispatchType = DispatchType.ACTOR)
    public void synthesis(Channel channel, Long actorId, DataPacket packet) {
        SynthesisRequest request = packet.getValue(SynthesisRequest.parser());
        TResult<RewardResultResponse> result = goodsFacade.synthesis(actorId, request.getGoodsId(), request.getNum());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = GoodsCmd.TEST_REWARD_VALUE, dispatchType = DispatchType.ACTOR)
    public void testReward(Channel channel, Long actorId, DataPacket packet) {
        TestRewardRequest request = packet.getValue(TestRewardRequest.parser());
        Result result = goodsFacade.testReward(actorId, request.getType(), request.getId(), request.getNum());
        channelWrite(channel, packet, result.statusCode);
    }
}
