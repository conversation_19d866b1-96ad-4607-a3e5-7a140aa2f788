package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.dataconfig.annotation.FieldIgnore;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.shared.module.lineup.SpriteAttributeType;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Maps;

import java.util.Map;

/**
 * @author: Gary
 * @date: 2023/4/26 14:30
 * @Description:
 */
@DataFile(fileName = "beast_star_config")
public class BeastStarConfig implements ModelAdapter {

    /**
     * 神兽Id
     */
    private int id;
    /**
     * 星级
     */
    private int star;
    /**
     * 图鉴值(当前)
     */
    private int collectValue;
    /**
     * 全体英雄增加的属性(当前)
     */
    private String heroAttributes;
    /**
     * 神兽增加的属性(累加)
     */
    private String beastAttributes;
    /**
     * 回合技能
     */
    private int pvpSkill;
    /**
     * 塔防技能
     */
    private int pveSkill;
    /**
     * 消耗的碎片数量(当前)
     */
    private int fragmentCount;

    /**
     * 神兽基础属性Map 神兽自身
     * key:SpriteAttributeType,value:value
     */
    @FieldIgnore
    private Map<SpriteAttributeType, Long> beastAttributeMap = Maps.newHashMap();

    /**
     * 图鉴属性Map  全体英雄
     * key:SpriteAttributeType,value:value
     */
    @FieldIgnore
    private Map<SpriteAttributeType, Long> heroAttributeMap = Maps.newHashMap();
    /**
     * 图鉴属性Map 属性万分比Map
     * key:SpriteAttributeType,value:ttpercent
     */
    @FieldIgnore
    private Map<SpriteAttributeType, Integer> heroAttributeTTPercentMap = Maps.newHashMap();

    @Override
    public void initialize() {
        JSONArray attributesArray = JSONArray.parseArray(heroAttributes);
        for (Object attributeItem : attributesArray) {
            JSONArray attributeArray = JSONArray.parseArray(attributeItem.toString());
            if (attributeArray.getIntValue(0) > 100) {
                heroAttributeTTPercentMap.put(SpriteAttributeType.getType(attributeArray.getIntValue(0) - 100), attributeArray.getInteger(1));
            } else {
                heroAttributeMap.put(SpriteAttributeType.getType(attributeArray.getIntValue(0)), attributeArray.getLong(1));
            }
        }
        JSONArray beastAttributesArray = JSONArray.parseArray(beastAttributes);
        for (Object attributeItem : beastAttributesArray) {
            JSONArray attributeArray = JSONArray.parseArray(attributeItem.toString());
            beastAttributeMap.put(SpriteAttributeType.getType(attributeArray.getIntValue(0)), attributeArray.getLong(1));
        }
    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(id, star);
    }

    public int getId() {
        return id;
    }

    public int getStar() {
        return star;
    }

    public int getCollectValue() {
        return collectValue;
    }

    public int getPvpSkill() {
        return pvpSkill;
    }

    public int getPveSkill() {
        return pveSkill;
    }

    public int getFragmentCount() {
        return fragmentCount;
    }

    public Map<SpriteAttributeType, Long> getBeastAttributeMap() {
        return beastAttributeMap;
    }

    public Map<SpriteAttributeType, Long> getHeroAttributeMap() {
        return heroAttributeMap;
    }

    public Map<SpriteAttributeType, Integer> getHeroAttributeTTPercentMap() {
        return heroAttributeTTPercentMap;
    }
}
