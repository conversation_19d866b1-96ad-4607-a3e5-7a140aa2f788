package cn.daxiang.hbtd.gameserver.module.goods.parser.reward.impl;

import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.framework.result.CollectionResult;
import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.hbtd.gameserver.core.database.table.Beast;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.BeastConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.GlobalConfigService;
import cn.daxiang.hbtd.gameserver.core.dispatch.DispatchHelper;
import cn.daxiang.hbtd.gameserver.core.event.impl.BeastGetEvent;
import cn.daxiang.hbtd.gameserver.core.oss.type.OperationType;
import cn.daxiang.hbtd.gameserver.module.beast.facade.BeastFacade;
import cn.daxiang.hbtd.gameserver.module.goods.helper.RewardHelper;
import cn.daxiang.hbtd.gameserver.module.goods.parser.reward.AbstractRewardParser;
import cn.daxiang.protocol.game.CommonProtocol;
import cn.daxiang.protocol.game.TypeProtocol;
import cn.daxiang.shared.StatusCode;
import cn.daxiang.shared.reward.RewardObject;
import cn.daxiang.shared.reward.RewardType;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Iterator;
import java.util.Map;

/**
 * @author: Gary
 * @date: 2023/4/26 17:27
 * @Description:
 */
@Component
public class BeastRewardParser extends AbstractRewardParser {
    @Autowired
    private BeastFacade beastFacade;
    @Autowired
    private GlobalConfigService globalConfigService;

    @Override
    protected TypeProtocol.RewardType getType() {
        return TypeProtocol.RewardType.BEAST;
    }

    @Override
    public TResult<CommonProtocol.RewardResult> send(long actorId, Map<Integer, Long> rewardMap, OperationType operationType) {
        CommonProtocol.RewardResult.Builder rewardResult = CommonProtocol.RewardResult.newBuilder();
        Collection<Beast> beastList = beastFacade.getBeastBagInfo(actorId).item;
        DispatchHelper.postEvent(new BeastGetEvent(actorId, Maps.newHashMap(rewardMap), operationType));
        Collection<RewardObject> rewardList = Lists.newArrayList();
        Iterator<Map.Entry<Integer, Long>> iter = rewardMap.entrySet().iterator();
        while (iter.hasNext()) {
            Map.Entry<Integer, Long> next = iter.next();
            int configId = next.getKey();
            BeastConfig config = globalConfigService.findConfig(IdentiyKey.build(configId), BeastConfig.class);
            if (config == null) {
                LOGGER.error("BeastConfig is not found, id:{}", configId);
                iter.remove();
                continue;
            }
            Collection<RewardObject> rewards = Lists.newArrayList(RewardObject.valueOf(RewardType.GOODS.getId(), config.getFragmentId(), config.getFragmentCount()));
            //神兽已存在转碎片
            if (beastList.stream().anyMatch(x -> x.getConfigId() == configId)) {
                rewardList.addAll(RewardHelper.multipleRewardList(rewards, next.getValue()));
                iter.remove();
                continue;
            }
            //神兽不存在,但发送数量大于1,创建神兽,其他转碎片
            if (next.getValue() > 1L) {
                rewardList.addAll(RewardHelper.multipleRewardList(rewards, next.getValue() - 1));
                next.setValue(1L);
            }
        }
        //需要返还的碎片不为空
        if (rewardList.size() != 0) {
            CommonProtocol.RewardResult goodsResult = RewardHelper.sendRewardList(actorId, rewardList, operationType);
            rewardResult.putAllGoods(goodsResult.getGoodsMap());
        }
        //需创建的神兽
        if (rewardMap.size() != 0) {
            CollectionResult<Beast> result = beastFacade.creatBeats(actorId, rewardMap, operationType);
            if (result.isOk()) {
                Map<Integer, Long> countMap = Maps.newHashMap();
                for (Beast beast : result.item) {
                    countMap.merge(beast.getConfigId(), 1L, Long::sum);
                }
                rewardResult.putAllBeasts(countMap);
            }
        }
        return TResult.sucess(rewardResult.build());
    }

    @Override
    public Result decrease(long actorId, Map<Integer, Long> rewardMap, OperationType operationType) {
        return Result.valueOf(StatusCode.NO_RESULTS);
    }

    @Override
    public Result hasEnough(long actorId, Map<Integer, Long> rewardMap) {
        LOGGER.error("{}", new RuntimeException(String.format("reward hasEnough error,data:%s", rewardMap)));
        return Result.valueOf(StatusCode.NO_RESULTS);
    }
}
