package cn.daxiang.hbtd.gameserver.module.battle.parser.trigger.impl;

import cn.daxiang.hbtd.gameserver.core.dataconfig.model.SkillConfig;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleRoom;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleSprite;
import cn.daxiang.hbtd.gameserver.module.battle.parser.trigger.AbstractSkillTriggerParser;
import cn.daxiang.hbtd.gameserver.module.battle.type.SkillTriggerType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/2/20
 */
@Component
public class BattleStartSkillTriggerParser extends AbstractSkillTriggerParser {
    @Override
    protected SkillTriggerType getType() {
        return SkillTriggerType.BATTLE_START;
    }

    @Override
    public boolean trigger(BattleRoom battleRoom, BattleSprite battleSprite, BattleSprite caster, BattleSprite triggerTarget, long triggerValue, SkillConfig triggerSkill,
        SkillConfig skillConfig) {
        return skillConfig.isTrigger();
    }
}
