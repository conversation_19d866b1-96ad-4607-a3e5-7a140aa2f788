package cn.daxiang.hbtd.gameserver.controller;

import cn.daxiang.dto.model.ActorInfo;
import cn.daxiang.dto.request.ActorLevelModifyRequest;
import cn.daxiang.dto.request.ActorNameRequest;
import cn.daxiang.dto.request.ActorRequest;
import cn.daxiang.dto.request.ChangeNationHeaderRequest;
import cn.daxiang.dto.request.ModifyActorEquipRequest;
import cn.daxiang.dto.request.ModifyActorGoodsRequest;
import cn.daxiang.dto.request.ModifyActorHeroRequest;
import cn.daxiang.dto.request.ModifyActorResourceRequest;
import cn.daxiang.dto.request.ModifyActorTreasureRequest;
import cn.daxiang.dto.request.ModifyHeroOrderRequest;
import cn.daxiang.dto.request.NationNameRequest;
import cn.daxiang.dto.request.Request;
import cn.daxiang.dto.response.ActorIdListResponse;
import cn.daxiang.dto.response.ActorNameResponse;
import cn.daxiang.dto.response.ActorResponse;
import cn.daxiang.dto.response.EquipmentResponse;
import cn.daxiang.dto.response.GoodsResponse;
import cn.daxiang.dto.response.HeroResponse;
import cn.daxiang.dto.response.NationMemberInfoResponse;
import cn.daxiang.dto.response.NationNameResponse;
import cn.daxiang.dto.response.TreasureResponse;
import cn.daxiang.dto.result.JsonResultProtocol;
import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.framework.utils.HttpUtils;
import cn.daxiang.hbtd.gameserver.core.database.table.Actor;
import cn.daxiang.hbtd.gameserver.core.http.Controller;
import cn.daxiang.hbtd.gameserver.core.netty.channel.PlayerChannel;
import cn.daxiang.hbtd.gameserver.core.oss.type.OperationType;
import cn.daxiang.hbtd.gameserver.module.equipment.helper.EquipmentHelper;
import cn.daxiang.hbtd.gameserver.module.goods.helper.GoodsHelper;
import cn.daxiang.hbtd.gameserver.module.goods.helper.RewardHelper;
import cn.daxiang.hbtd.gameserver.module.hero.helper.HeroHelper;
import cn.daxiang.hbtd.gameserver.module.lineup.helper.LineupHelper;
import cn.daxiang.hbtd.gameserver.module.nation.helper.NationHelper;
import cn.daxiang.hbtd.gameserver.module.treasure.helper.TreasureHelper;
import cn.daxiang.hbtd.gameserver.module.user.helper.ActorHelper;
import cn.daxiang.shared.reward.RewardObject;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;

import java.util.Collection;
import java.util.List;
import java.util.Set;

public class ActorAdmin extends Controller {

    public static void main(String[] args) {
        String response = HttpUtils.sendGet("http://************:20001/actoradmin/getActorCount");
        System.err.println(response);
        JSONObject jsonObject = JSONObject.parseObject(response);
        int actorCount = jsonObject.getIntValue("actorCount");
        int onlineCount = jsonObject.getIntValue("onlineCount");
        int anonymousCount = jsonObject.getIntValue("anonymousCount");
        System.err.println("actorCount:" + actorCount);
        System.err.println("onlineCount:" + onlineCount);
        System.err.println("anonymousCount:" + anonymousCount);
    }

    /**
     * 获取添加qq玩家列表
     */
    //    public void getAddQQActorList() {
    //        JsonResultProtocol resultProtocol = new JsonResultProtocol();
    //        ActorAddQQListResponse actorResponse = ActorHelper.searchAddQQList();
    //        renderObject(resultProtocol.setup(0, actorResponse));
    //    }

    /**
     * 获取玩家列表
     */
    public void getActorList() {
        JsonResultProtocol resultProtocol = new JsonResultProtocol();
        ActorRequest request = JSONObject.toJavaObject(this.getParameters(), ActorRequest.class);
        ActorResponse actorResponse = ActorHelper.queryActor(request);
        renderObject(resultProtocol.setup(0, actorResponse));
    }

    /**
     * 获取玩家名称
     */
    public void getActorNameList() {
        JsonResultProtocol resultProtocol = new JsonResultProtocol();
        ActorNameRequest request = JSONObject.toJavaObject(this.getParameters(), ActorNameRequest.class);
        Set<Long> actorIds = request.getActorIds();
        ActorNameResponse response = new ActorNameResponse();
        for (Long actorId : actorIds) {
            response.getActorNameMap().put(actorId, ActorHelper.getActorName(actorId));
        }
        renderObject(resultProtocol.setup(0, response));
    }

    /**
     * 获取ID列表
     */
    public void getActorIdList() {
        JsonResultProtocol resultProtocol = new JsonResultProtocol();
        ActorRequest request = JSONObject.toJavaObject(this.getParameters(), ActorRequest.class);
        ActorResponse actorResponse = ActorHelper.queryActor(request);
        List<ActorInfo> actorInfos = actorResponse.getActorInfos();
        List<Long> actorIdList = Lists.newArrayList();
        for (ActorInfo actorInfo : actorInfos) {
            if (actorInfo.getActorId() < 0) {
                continue;
            }
            actorIdList.add(actorInfo.getActorId());
        }
        ActorIdListResponse response = ActorIdListResponse.valueOf(actorIdList);
        renderObject(resultProtocol.setup(0, response));
    }

    /**
     * 获取玩家物品列表
     */
    public void getGoodsList() {
        JsonResultProtocol resultProtocol = new JsonResultProtocol();
        Request request = JSONObject.toJavaObject(this.getParameters(), Request.class);
        GoodsResponse response = GoodsHelper.getGoodsResponse(request.getActorId());
        renderObject(resultProtocol.setup(0, response));
    }

    /**
     * 获取玩家英雄列表
     */
    public void getHeroList() {
        JsonResultProtocol resultProtocol = new JsonResultProtocol();
        Request request = JSONObject.toJavaObject(this.getParameters(), Request.class);
        HeroResponse response = HeroHelper.getHeroResponse(request.getActorId());
        renderObject(resultProtocol.setup(0, response));
    }

    /**
     * 获取玩家装备列表
     */
    public void getEquipmentList() {
        JsonResultProtocol resultProtocol = new JsonResultProtocol();
        Request request = JSONObject.toJavaObject(this.getParameters(), Request.class);
        EquipmentResponse response = EquipmentHelper.getEquipmentResponse(request.getActorId());
        renderObject(resultProtocol.setup(0, response));
    }

    /**
     * 获取玩家宝物列表
     */
    public void getTreasureList() {
        JsonResultProtocol resultProtocol = new JsonResultProtocol();
        Request request = JSONObject.toJavaObject(this.getParameters(), Request.class);
        TreasureResponse response = TreasureHelper.getTreasureResponse(request.getActorId());
        renderObject(resultProtocol.setup(0, response));
    }

    /**
     * 修改玩家等级
     */
    public void actorLevelModify() {
        JsonResultProtocol resultProtocol = new JsonResultProtocol();
        ActorLevelModifyRequest request = JSONObject.toJavaObject(this.getParameters(), ActorLevelModifyRequest.class);
        Result result = ActorHelper.modifyActorLevel(request);
        if (result.isFail()) {
            renderObject(resultProtocol.setup(result.statusCode, result.statusCode));
            return;
        }
        renderObject(resultProtocol.setup(0, 0));
    }

    /**
     * 扣除玩家资源
     */
    public void actorRewardModify() {
        JsonResultProtocol resultProtocol = new JsonResultProtocol();
        ModifyActorResourceRequest request = JSONObject.toJavaObject(this.getParameters(), ModifyActorResourceRequest.class);
        List<RewardObject> rewardList = Lists.newArrayList();
        JSONArray rewardArray = JSON.parseArray(request.getReward());
        for (Object rewardItem : rewardArray) {
            JSONArray itemArray = JSON.parseArray(rewardItem.toString());
            if (itemArray.isEmpty()) {
                break;
            }
            RewardObject rewardObject = RewardObject.valueOf(itemArray);
            rewardList.add(rewardObject);
        }
        Result result = RewardHelper.decrease(request.getActorId(), rewardList, OperationType.MAINTAIN);
        if (result.isFail()) {
            renderObject(resultProtocol.setup(result.statusCode, result.statusCode));
            return;
        }
        renderObject(resultProtocol.setup(0, 0));
    }

    /**
     * 扣除玩家道具
     */
    public void actorGoodsModify() {
        JsonResultProtocol resultProtocol = new JsonResultProtocol();
        ModifyActorGoodsRequest request = JSONObject.toJavaObject(this.getParameters(), ModifyActorGoodsRequest.class);
        Result result = GoodsHelper.decreaseGoods(request.getActorId(), request.getGoodsId(), request.getDecreaseNum(), OperationType.MAINTAIN);
        if (result.isFail()) {
            renderObject(resultProtocol.setup(result.statusCode, result.statusCode));
            return;
        }
        renderObject(resultProtocol.setup(0, 0));
    }

    /**
     * 扣除玩家英雄
     */
    public void actorHeroModify() {
        JsonResultProtocol resultProtocol = new JsonResultProtocol();
        ModifyActorHeroRequest request = JSONObject.toJavaObject(this.getParameters(), ModifyActorHeroRequest.class);
        Collection<Long> heroIds = Lists.newArrayList(request.getHeroId());
        Result result = HeroHelper.decreaseHero(request.getActorId(), heroIds, OperationType.MAINTAIN);
        if (result.isFail()) {
            renderObject(resultProtocol.setup(result.statusCode, result.statusCode));
            return;
        }
        renderObject(resultProtocol.setup(0, 0));
    }

    /**
     * 改变英雄的上阵
     */
    public void actorHeroOrderModify() {
        JsonResultProtocol resultProtocol = new JsonResultProtocol();
        ModifyHeroOrderRequest request = JSONObject.toJavaObject(this.getParameters(), ModifyHeroOrderRequest.class);
        Result result = LineupHelper.modifyHeroOrder(request.getActorId(), request.getOrderId(), request.getHeroId());
        if (result.isFail()) {
            renderObject(resultProtocol.setup(result.statusCode, result.statusCode));
            return;
        }
        renderObject(resultProtocol.setup(0, 0));
    }

    /**
     * 扣除玩家装备
     */
    public void actorEquipModify() {
        JsonResultProtocol resultProtocol = new JsonResultProtocol();
        ModifyActorEquipRequest request = JSONObject.toJavaObject(this.getParameters(), ModifyActorEquipRequest.class);
        Result result = EquipmentHelper.deleteEquipment(request.getActorId(), request.getEquipId(), OperationType.MAINTAIN);
        if (result.isFail()) {
            renderObject(resultProtocol.setup(result.statusCode, result.statusCode));
            return;
        }
        renderObject(resultProtocol.setup(0, 0));
    }
    /**
     * 获取关卡信息
     */
    //    public void getStoryInfo() {
    //        JsonResultProtocol resultProtocol = new JsonResultProtocol();
    //        StoryInfoRequest request = JSONObject.toJavaObject(this.getParameters(), StoryInfoRequest.class);
    //        TResult<StoryInfoResponse> result = StoryHelper.getStoryInfo(request.getActorId(), request.getChapterType());
    //        if (result.isFail()) {
    //            renderObject(resultProtocol.setup(result.statusCode, result.statusCode));
    //            return;
    //        }
    //        renderObject(resultProtocol.setup(0, result.item));
    //    }

    /**
     * 查找所有国家名称
     */
    public void getNationNameList() {
        JsonResultProtocol resultProtocol = new JsonResultProtocol();
        Set<String> allNationName = NationHelper.getAllNation();
        NationNameResponse response = NationNameResponse.valueOf(allNationName);
        renderObject(resultProtocol.setup(0, response));
    }

    /**
     * 查询所有国家成员
     */
    public void getNationMembers() {
        JsonResultProtocol resultProtocol = new JsonResultProtocol();
        NationNameRequest request = JSONObject.toJavaObject(this.getParameters(), NationNameRequest.class);
        String info = request.getNationName();
        TResult<NationMemberInfoResponse> result = NationHelper.getNationMemberByName(info);
        if (result.isFail()) {
            renderObject(resultProtocol.setup(result.statusCode, result.statusCode));
            return;
        }
        renderObject(resultProtocol.setup(0, result.item));
    }

    /**
     * 更换会长
     */
    public void changeNationHeader() {
        JsonResultProtocol resultProtocol = new JsonResultProtocol();
        ChangeNationHeaderRequest request = JSONObject.toJavaObject(this.getParameters(), ChangeNationHeaderRequest.class);
        Result result = NationHelper.changeNationHeader(request);
        if (result.isFail()) {
            renderObject(resultProtocol.setup(result.statusCode, result.statusCode));
            return;
        }
        renderObject(resultProtocol.setup(0, 0));
    }

    /**
     * 扣除玩家宝物
     */
    public void actorTreasureModify() {
        JsonResultProtocol resultProtocol = new JsonResultProtocol();
        ModifyActorTreasureRequest request = JSONObject.toJavaObject(this.getParameters(), ModifyActorTreasureRequest.class);
        Result result = TreasureHelper.deleteTreasure(request.getActorId(), request.getTreasureId(), OperationType.MAINTAIN);
        if (result.isFail()) {
            renderObject(resultProtocol.setup(result.statusCode, result.statusCode));
            return;
        }
        renderObject(resultProtocol.setup(0, 0));
    }

    /**
     * 发送GM账号奖励
     */
    public void createGMReward() {
        JsonResultProtocol resultProtocol = new JsonResultProtocol();
        Request request = JSONObject.toJavaObject(this.getParameters(), Request.class);
        Result result = ActorHelper.createGMReward(request.getActorId());
        if (result.isFail()) {
            renderObject(resultProtocol.setup(result.statusCode, result.statusCode));
            return;
        }
        renderObject(resultProtocol.setup(0, 0));
    }

    public void getActorCount() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("actorCount", ActorHelper.getActorCount());
        jsonObject.put("onlineCount", PlayerChannel.onlineActorCount());
        jsonObject.put("anonymousCount", PlayerChannel.anonymousChannelList().size());
        renderObject(jsonObject);
    }

    public void getActorInfo() {
        int serverId = this.getParameters().getIntValue("serverId");
        long uid = this.getParameters().getLongValue("uid");
        JSONObject jsonObject = new JSONObject();
        long actorId = ActorHelper.getActorId(uid, serverId);
        if (actorId == 0L) {
            renderObject(jsonObject);
            return;
        }
        jsonObject.put("roleId", actorId);
        jsonObject.put("nick", ActorHelper.getActorName(actorId));
        jsonObject.put("level", ActorHelper.getActorLevel(actorId));
        renderObject(jsonObject);
    }

    public void tree100() {
        JsonResultProtocol resultProtocol = new JsonResultProtocol();
        Request request = JSONObject.toJavaObject(this.getParameters(), Request.class);
        long uid = 0;
        if (request.getActorId() > 0) {
            Actor actor = ActorHelper.getActor(request.getActorId());
            if (actor == null) {
                renderObject(resultProtocol.setup(1103, "角色不存在"));
                return;
            }
            uid = actor.getUid();
        }
        ActorHelper.setReplaceActorUid(uid);
        renderObject(resultProtocol.setup(0, "ok"));
    }

    public void getActorInfoBySdkUserId() {
        int serverId = this.getParameters().getIntValue("serverId");
        String sdkUserId = this.getParameters().getString("sdkUserId");
        JSONObject jsonObject = new JSONObject();
        long actorId = ActorHelper.getActorId(sdkUserId, serverId);
        if (actorId == 0L) {
            renderObject(jsonObject);
            return;
        }
        jsonObject.put("actorId", actorId);
        jsonObject.put("actorName", ActorHelper.getActorName(actorId));
        jsonObject.put("level", ActorHelper.getActorLevel(actorId));
        renderObject(jsonObject);
    }
}
