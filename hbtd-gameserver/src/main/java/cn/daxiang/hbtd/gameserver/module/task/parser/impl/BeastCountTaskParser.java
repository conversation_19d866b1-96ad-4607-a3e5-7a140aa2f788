package cn.daxiang.hbtd.gameserver.module.task.parser.impl;

import cn.daxiang.hbtd.gameserver.core.database.table.Beast;
import cn.daxiang.hbtd.gameserver.core.database.table.Task;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.TaskConfig;
import cn.daxiang.hbtd.gameserver.core.event.impl.BeastCreatEvent;
import cn.daxiang.hbtd.gameserver.module.beast.dao.BeastDao;
import cn.daxiang.hbtd.gameserver.module.task.parser.AbstractTaskParser;
import cn.daxiang.hbtd.gameserver.module.task.type.TaskConditionType;
import cn.daxiang.protocol.game.TaskProtocol;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;

/**
 * @author: Gary
 * @date: 2023/5/4 14:42
 * @Description:
 */
@Component
public class BeastCountTaskParser extends AbstractTaskParser<BeastCreatEvent> {
    @Autowired
    private BeastDao beastDao;

    @Override
    protected TaskConditionType getType() {
        return TaskConditionType.BEAST_COUNT;
    }

    @Override
    protected boolean parseCondition(BeastCreatEvent event, Task task, TaskConfig taskConfig) {
        parser(task, taskConfig);
        return true;
    }

    @Override
    public void init(Task task, TaskConfig taskConfig) {
        task.setValue(0);
        parser(task, taskConfig);
    }

    private void parser(Task task, TaskConfig taskConfig) {
        Collection<Beast> beastList = beastDao.getBeastList(task.getActorId());
        int value = beastList.size();
        if (task.getValue() < value) {
            task.setValue(value);
        }
        if (task.getValue() >= taskConfig.getValue()) {
            task.setStatus(TaskProtocol.TaskStatus.TASK_FINISH);
        }
    }
}
