package cn.daxiang.hbtd.gameserver.module.goods.parser.reward.impl;

import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.hbtd.gameserver.core.oss.type.OperationType;
import cn.daxiang.hbtd.gameserver.module.goods.parser.reward.AbstractRewardParser;
import cn.daxiang.hbtd.gameserver.module.heroSkin.facade.HeroSkinFacade;
import cn.daxiang.protocol.game.CommonProtocol.RewardResult;
import cn.daxiang.protocol.game.TypeProtocol.RewardType;
import cn.daxiang.shared.StatusCode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;

@Component
public class HeroSkinFragmentRewardParser extends AbstractRewardParser {
    @Autowired
    private HeroSkinFacade heroSkinFacade;

    @Override
    public TResult<RewardResult> send(long actorId, Map<Integer, Long> rewardMap, OperationType operationType) {
        RewardResult.Builder rewardResult = RewardResult.newBuilder();
        TResult<Collection<Integer>> result = heroSkinFacade.addHeroSkinFragment(actorId, rewardMap, operationType);
        if (result.isOk()) {
            rewardResult.addAllHeroSkinFragmentIds(result.item);
        }
        return TResult.sucess(rewardResult.build());
    }

    @Override
    public Result decrease(long actorId, Map<Integer, Long> rewardMap, OperationType operationType) {
        LOGGER.error("{}", new RuntimeException(String.format("reward decrease error,data:%s", rewardMap)));
        return Result.valueOf(StatusCode.NO_RESULTS);
    }

    @Override
    public Result hasEnough(long actorId, Map<Integer, Long> rewardMap) {
        LOGGER.error("{}", new RuntimeException(String.format("reward hasEnough error,data:%s", rewardMap)));
        return Result.valueOf(StatusCode.NO_RESULTS);
    }

    @Override
    protected RewardType getType() {
        return RewardType.HERO_SKIN_FRAGMENT;
    }

}
