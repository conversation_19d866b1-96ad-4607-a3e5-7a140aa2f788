package cn.daxiang.hbtd.gameserver.module.activity.model.entity.record;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;

import java.util.Collection;
import java.util.Map;

/**
 * 新服狂欢活动记录
 *
 * <AUTHOR>
 * @date 2019/11/5
 */
public class CarnivalRecord {
    /**
     * 新服狂欢优惠购买情况
     * {key:id,value:times}
     */
    private Map<Integer, Integer> discountMap = Maps.newHashMap();
    /**
     * 新服狂欢领取积分奖励列表
     */
    private Collection<Integer> scoreList = Sets.newHashSet();

    public Map<Integer, Integer> getDiscountMap() {
        return discountMap;
    }

    public void setDiscountMap(Map<Integer, Integer> discountMap) {
        this.discountMap = discountMap;
    }

    public Collection<Integer> getScoreList() {
        return scoreList;
    }

    public void setScoreList(Collection<Integer> scoreList) {
        this.scoreList = scoreList;
    }

    /**
     * 领取七日狂欢积分奖励
     *
     * @param score
     */
    public void receiveScore(int score) {
        this.scoreList.add(score);
    }

    /**
     * 购买七日狂欢折扣商品
     *
     * @param id
     */
    public void buyDiscount(int id) {
        Integer times = this.discountMap.get(id);
        if (times == null) {
            times = 0;
        }
        this.discountMap.put(id, ++times);
    }
}
