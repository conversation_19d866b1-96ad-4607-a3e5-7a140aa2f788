package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.dataconfig.annotation.FieldIgnore;
import cn.daxiang.framework.identity.IdentiyKey;
import com.alibaba.fastjson.JSONArray;

import java.util.List;

/**
 * 英雄标签名称配置表
 * <AUTHOR>
 * @date 2023/12/20
 */
@DataFile(fileName = "hero_tag_name_config")
public class HeroTagNameConfig implements ModelAdapter {
    /**
     * 英雄ID
     */
    private int tagId;
    /**
     * 标签ID
     */
    private String tagName;

    @Override
    public void initialize() {

    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(tagId);
    }

    public int getTagId() {
        return tagId;
    }

    public String getTagName() {
        return tagName;
    }
}
