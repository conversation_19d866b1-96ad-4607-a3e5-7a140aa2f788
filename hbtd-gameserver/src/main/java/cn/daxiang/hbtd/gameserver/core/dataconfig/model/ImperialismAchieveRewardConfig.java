package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.dataconfig.annotation.FieldIgnore;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.shared.reward.RewardObject;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;

import java.util.Collection;

/**
 * 决战皇城成就奖励配置表
 *
 * <AUTHOR>
 * @date 2023/11/14
 */
@DataFile(fileName = "imperialism_achieve_reward_config")
public class ImperialismAchieveRewardConfig implements ModelAdapter {
    /**
     * 成就Id
     * id是唯一的
     */
    private int id;
    /**
     * 排行榜类型
     * 1.本服
     * 2.小战区
     * 3.皇城战区
     */
    private int type;
    /**
     * 历史最高排名
     */
    private long rank;
    /**
     * 奖励
     * [[rewardType,id,num],[rewardType,id,num]]
     */
    private String rewards;

    @FieldIgnore
    private Collection<RewardObject> rewardList = Lists.newArrayList();

    @Override
    public void initialize() {
        JSONArray rewardArray = JSONArray.parseArray(rewards);
        for (Object rewardItem : rewardArray) {
            JSONArray array = JSONArray.parseArray(rewardItem.toString());
            RewardObject rewardObject = RewardObject.valueOf(array);
            rewardList.add(rewardObject);
        }
    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(id);
    }

    public int getId() {
        return id;
    }

    public int getType() {
        return type;
    }

    public long getRank() {
        return rank;
    }

    public Collection<RewardObject> getRewardList() {
        return rewardList;
    }
}