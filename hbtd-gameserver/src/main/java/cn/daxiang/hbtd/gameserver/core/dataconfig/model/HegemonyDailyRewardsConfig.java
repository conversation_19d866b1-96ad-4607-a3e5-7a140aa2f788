package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.dataconfig.annotation.FieldIgnore;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.shared.reward.RewardObject;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;

import java.util.Collection;

/**
 * 群雄争霸日常奖励表
 *
 * <AUTHOR>
 * @date 2022/10/19
 */
@DataFile(fileName = "hegemony_daily_rewards_config")
public class HegemonyDailyRewardsConfig implements ModelAdapter {
    /**
     * 用于排序和作为唯一标识
     */
    private int id;
    /**
     * type=1时value为主动挑战次数；
     * type=2时value为主动挑战胜利次数
     */
    private int type;
    /**
     * 限制值
     */
    private int value;
    /**
     * 奖励
     */
    private String rewards;
    @FieldIgnore
    private Collection<RewardObject> rewardList = Lists.newArrayList();

    @Override
    public void initialize() {
        JSONArray costArray = JSONArray.parseArray(rewards);
        for (Object costItem : costArray) {
            JSONArray rewardArray = JSONArray.parseArray(costItem.toString());
            RewardObject rewardObject = RewardObject.valueOf(rewardArray);
            rewardList.add(rewardObject);
        }
    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(id);
    }

    public int getId() {
        return id;
    }

    public int getType() {
        return type;
    }

    public int getValue() {
        return value;
    }

    public Collection<RewardObject> getRewardList() {
        return rewardList;
    }
}
