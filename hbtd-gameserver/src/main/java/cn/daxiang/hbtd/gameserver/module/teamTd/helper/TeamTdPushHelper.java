package cn.daxiang.hbtd.gameserver.module.teamTd.helper;

import cn.daxiang.framework.protocal.DataPacket;
import cn.daxiang.framework.rpc.event.TeamTdBattleStartEvent;
import cn.daxiang.hbtd.gameserver.core.netty.channel.PlayerChannel;
import cn.daxiang.protocol.game.CommonProtocol.RewardResult;
import cn.daxiang.protocol.game.ModuleProtocol.Module;
import cn.daxiang.protocol.game.TeamtdProtocol;
import cn.daxiang.protocol.game.TeamtdProtocol.TeamtdCmd;
import cn.daxiang.protocol.game.TypeProtocol;
import cn.daxiang.shared.module.teamTd.model.TeamTd;
import cn.daxiang.shared.module.teamTd.type.TeamTdParameterKey;
import cn.daxiang.shared.module.teamTd.type.TeamTdType;

import java.util.Collection;
import java.util.Map;

public class TeamTdPushHelper {

    public static void pushTeamList(TeamTd team, Collection<Long> actorIds) {
        TeamtdProtocol.TeamTdListResponse.Builder builder = TeamtdProtocol.TeamTdListResponse.newBuilder();
        builder.setTeamTdTypeValue(team.getType().getId());
        builder.setTypeValue(team.getTypeValue());
        builder.addTeamTds(TeamTdHelper.buildTeamTd(team));
        TeamtdProtocol.TeamTdListResponse response = builder.build();

        DataPacket packet = DataPacket.valueOf(Module.TEAMTD_VALUE, TeamtdCmd.PUSH_TEAM_TD_LIST_VALUE, response);
        PlayerChannel.push(actorIds, packet);
    }

    public static void pushTeam(TeamTd team, Collection<Long> actorIds) {
        TeamtdProtocol.TeamTdListResponse.Builder builder = TeamtdProtocol.TeamTdListResponse.newBuilder();
        builder.setTeamTdType(TypeProtocol.TeamTdType.forNumber(team.getType().getId()));
        builder.setTypeValue(team.getTypeValue());
        builder.addTeamTds(TeamTdHelper.buildTeamTd(team));
        TeamtdProtocol.TeamTdListResponse response = builder.build();
        DataPacket packet = DataPacket.valueOf(Module.TEAMTD_VALUE, TeamtdCmd.PUSH_TEAM_TD_VALUE, response);
        PlayerChannel.push(actorIds, packet);
    }

    public static void pushTeamList(TeamTdType type, int typeValue, Collection<TeamTd> teamList, long actorId) {
        TeamtdProtocol.TeamTdListResponse.Builder builder = TeamtdProtocol.TeamTdListResponse.newBuilder();
        builder.setTeamTdType(TypeProtocol.TeamTdType.forNumber(type.getId()));
        builder.setTypeValue(typeValue);
        for (TeamTd teamTd : teamList) {
            builder.addTeamTds(TeamTdHelper.buildTeamTd(teamTd));
        }
        TeamtdProtocol.TeamTdListResponse response = builder.build();
        DataPacket packet = DataPacket.valueOf(Module.TEAMTD_VALUE, TeamtdCmd.PUSH_TEAM_TD_LIST_VALUE, response);
        PlayerChannel.push(actorId, packet);
    }

    public static void pushTeamDisband(TeamTdType type, int typeValue, long id, Collection<Long> actorIds) {
        TeamtdProtocol.TeamTdDisbandResponse.Builder builder = TeamtdProtocol.TeamTdDisbandResponse.newBuilder();
        builder.setTeamTdTypeValue(type.getId());
        builder.setTypeValue(typeValue);
        builder.setId(id);
        TeamtdProtocol.TeamTdDisbandResponse response = builder.build();
        DataPacket packet = DataPacket.valueOf(Module.TEAMTD_VALUE, TeamtdCmd.PUSH_TEAM_TD_DISBAND_VALUE, response);
        PlayerChannel.push(actorIds, packet);
    }

    public static void pushTeamStart(long actorId, TeamTdBattleStartEvent event) {
        TeamtdProtocol.TeamTdBattleStartResponse.Builder builder = TeamtdProtocol.TeamTdBattleStartResponse.newBuilder();
        builder.setBsId(event.bsId);
        builder.setTeamTdTypeValue(event.type.getId());
        builder.setRoomId(event.roomId);
        builder.setToken(event.token);
        builder.setBattleHost(event.battleHost);
        builder.setBattlePort(event.battlePort);
        TeamtdProtocol.TeamTdBattleStartResponse response = builder.build();
        DataPacket packet = DataPacket.valueOf(Module.TEAMTD_VALUE, TeamtdCmd.PUSH_TEAM_TD_BATTLE_START_VALUE, response);
        PlayerChannel.push(actorId, packet);
    }

    public static void pushTeamBattleResult(long actorId, TeamTdType type, int value, Map<TeamTdParameterKey, Object> parameter, RewardResult rewardResult, boolean isCheat) {
        TeamtdProtocol.TeamTdBattleResultResponse.Builder builder = TeamtdProtocol.TeamTdBattleResultResponse.newBuilder();
        builder.setTeamTdTypeValue(type.getId());
        builder.setTypeValue(value);
        for (Map.Entry<TeamTdParameterKey, Object> entry : parameter.entrySet()) {
            builder.putParameter(entry.getKey().getId(), entry.getValue().toString());
        }
        builder.setRewardResult(rewardResult);
        builder.setIsCheat(isCheat);
        TeamtdProtocol.TeamTdBattleResultResponse response = builder.build();
        DataPacket packet = DataPacket.valueOf(Module.TEAMTD_VALUE, TeamtdCmd.PUSH_TEAM_TD_BATTLE_RESULT_VALUE, response);
        PlayerChannel.push(actorId, packet);
    }
}
