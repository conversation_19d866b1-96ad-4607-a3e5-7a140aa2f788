package cn.daxiang.hbtd.gameserver.module.activity.type;

/**
 * <AUTHOR>
 * @date 2024/5/20
 */
public enum Activity68GachaType {
    /**
     * 免费
     */
    FREE(0),
    /**
     * 使用道具
     */
    GOODS(1),
    /**
     * 使用元宝
     */
    DIAMOND(2);

    private final int type;

    Activity68GachaType(int type) {
        this.type = type;
    }

    public static Activity68GachaType getType(int type) {
        for (Activity68GachaType gachaTimesType : Activity68GachaType.values()) {
            if (gachaTimesType.type == type) {
                return gachaTimesType;
            }
        }
        return null;
    }

    public int getType() {
        return type;
    }
}
