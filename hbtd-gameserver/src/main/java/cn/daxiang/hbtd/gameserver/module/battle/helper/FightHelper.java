package cn.daxiang.hbtd.gameserver.module.battle.helper;

import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.framework.utils.NumberUtils;
import cn.daxiang.framework.utils.ObjectReference;
import cn.daxiang.framework.utils.RandomUtils;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.SkillConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.SkillEffectConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.SkillSeekConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.GlobalConfigService;
import cn.daxiang.hbtd.gameserver.core.dataconfig.service.SkillConfigService;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleBuff;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleMember;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleRoom;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleSkill;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleSprite;
import cn.daxiang.hbtd.gameserver.module.battle.model.FightEffect;
import cn.daxiang.hbtd.gameserver.module.battle.model.FightReport;
import cn.daxiang.hbtd.gameserver.module.battle.model.SpriteBattle;
import cn.daxiang.hbtd.gameserver.module.battle.parser.effect.SkillEffectContext;
import cn.daxiang.hbtd.gameserver.module.battle.parser.effect.SkillEffectParser;
import cn.daxiang.hbtd.gameserver.module.battle.parser.seek.SkillSeekContext;
import cn.daxiang.hbtd.gameserver.module.battle.parser.seek.SkillSeekParser;
import cn.daxiang.hbtd.gameserver.module.battle.parser.trigger.SkillTriggerContext;
import cn.daxiang.hbtd.gameserver.module.battle.parser.trigger.SkillTriggerParser;
import cn.daxiang.hbtd.gameserver.module.battle.type.SkillEffectType;
import cn.daxiang.hbtd.gameserver.module.battle.type.SkillTriggerType;
import cn.daxiang.hbtd.gameserver.module.battle.type.SkillType;
import cn.daxiang.protocol.game.BattleProtocol;
import cn.daxiang.protocol.game.BattleProtocol.SpriteType;
import cn.daxiang.shared.GlobalConfigKey;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Collection;
import java.util.Comparator;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
public class FightHelper {
    private static final Logger LOGGER = LoggerFactory.getLogger(FightHelper.class);
    private static ObjectReference<FightHelper> ref = new ObjectReference<>();

    @Autowired
    private SkillEffectContext effectContext;
    @Autowired
    private SkillSeekContext seekContext;
    @Autowired
    private SkillTriggerContext skillTriggerContext;
    @Autowired
    private GlobalConfigService globalConfigService;

    public static void action(BattleRoom battleRoom, BattleSprite battleSprite) {
        if (battleSprite.isDead()) {
            return;
        }
        //        FightReport fightReport = FightReport.valueOf(battleSprite.getSpriteId(),battleRoom.getRound());
        //        rageRecover(battleSprite,fightReport);
        // 如果有超级强控
        if (battleSprite.hasBuffType(SkillEffectType.SUPER_STUN_BUFF_EFFECT)) {
            return;
        }
        // 如果有击晕buff直接退出
        if (battleSprite.hasBuffType(SkillEffectType.STUN_BUFF_EFFECT) && !battleSprite.hasBuffType(SkillEffectType.IMMUNE_CONTROL_BUFF_EFFECT)) {
            return;
        }
        // 眩晕并且怒气满就直接return;
        SkillTriggerType skillTriggerType;
        SpriteBattle spriteBattle = battleSprite.getSpriteBattle();
        if (spriteBattle.isRageFull() && (!battleSprite.hasBuffType(SkillEffectType.SILENCE_BUFF_EFFECT) || battleSprite.hasBuffType(SkillEffectType.IMMUNE_CONTROL_BUFF_EFFECT))) {
            skillTriggerType = SkillTriggerType.CAST;
        } else {
            skillTriggerType = SkillTriggerType.NORMAL_ATTACK;
        }
        processSkill(battleRoom, battleSprite, skillTriggerType);
        if (battleSprite.getSpriteType() != BattleProtocol.SpriteType.SPRITE_BEAST && skillTriggerType == SkillTriggerType.CAST) {
            BattleProtocol.BattleCamp enemyCamp;
            if (battleSprite.getBattleCamp() == BattleProtocol.BattleCamp.LEFT_CAMP) {
                enemyCamp = BattleProtocol.BattleCamp.RIGHT_CAMP;
            } else {
                enemyCamp = BattleProtocol.BattleCamp.LEFT_CAMP;
            }
            //敌方英雄释放大招时
            for (BattleSprite enemySprite : battleRoom.getAliveSprite(enemyCamp)) {
                processSkill(battleRoom, enemySprite, battleSprite, null, null, SkillTriggerType.ENEMY_HERO_CAST);
            }
            //友方英雄释放大招时（除自己）
            for (BattleSprite friendlySprite : battleRoom.getAliveSprite(battleSprite.getBattleCamp())) {
                if (friendlySprite.getSpriteId() == battleSprite.getSpriteId()) {
                    continue;
                }
                processSkill(battleRoom, friendlySprite, null, battleSprite, null, SkillTriggerType.FRIENDLY_HERO_CAST);
            }
        }
    }

    /**
     * 战斗红颜技
     *
     * @param battleRoom
     * @param battleSprite
     */
    public static void spacetimeBeautyAction(BattleRoom battleRoom, BattleSprite battleSprite) {
        if (battleSprite.isDead()) {
            return;
        }
        if (battleSprite.getSpriteBattle().getSpacetimeBeautyId() == 0) {
            return;
        }
        int beautySkillCD = battleSprite.getSpriteBattle().getBeautySkillCD();
        if (beautySkillCD == 0) {
            return;
        }
        if (battleSprite.getBeautySkillRoundCD() != 0) {
            return;
        }
        if (!battleSprite.isSpiritualEnough()) {
            return;
        }
        battleSprite.processBeautySkill();
        processSkill(battleRoom, battleSprite, SkillTriggerType.SPACETIME_BEAUTY_SKILL);
    }

    /**
     * 是否受到致命伤害并可以触发技能
     *
     * @param battleSprite
     * @param damage
     * @return
     */
    public static boolean isTakingLethalDamage(BattleSprite battleSprite, long damage) {
        if (battleSprite.getSpriteBattle().getHP() > damage) {
            return false;
        }
        SkillTriggerParser parser = ref.get().skillTriggerContext.getParser(SkillTriggerType.TAKING_LETHAL_DAMAGE);
        if (parser == null) {
            LOGGER.error("SkillTriggerParser not found,triggerType:{}", SkillTriggerType.TAKING_LETHAL_DAMAGE);
            return false;
        }
        Collection<BattleSkill> skillList = battleSprite.getSkillList(SkillTriggerType.TAKING_LETHAL_DAMAGE);
        for (BattleSkill battleSkill : skillList) {
            SkillConfig skillConfig = SkillConfigService.getSkillConfig(battleSkill.getSkillId());
            if (skillConfig == null) {
                LOGGER.error("SkillConfig not found,skillId:{}", battleSkill.getSkillId());
                continue;
            }
            if (skillConfig.getTimes() > 0 && battleSkill.getTimes() >= skillConfig.getTimes()) {
                continue;
            }
            if (skillConfig.getRoundTimes() > 0 && battleSkill.getRoundTimes() >= skillConfig.getRoundTimes()) {
                continue;
            }
            boolean isTrigger = parser.trigger(null, battleSprite, null, null, 0L, null, skillConfig);
            if (isTrigger) {
                return true;
            }
        }
        return false;
    }

    public static void processSkill(BattleRoom battleRoom, SkillTriggerType triggerType) {
        for (BattleSprite battleSprite : battleRoom.getRoomSprites().values()) {
            processSkill(battleRoom, battleSprite, triggerType);
        }
    }

    public static void processSkill(BattleRoom battleRoom, BattleSprite battleSprite, SkillTriggerType triggerType) {
        processSkill(battleRoom, battleSprite, null, null, null, triggerType);
    }

    public static void processSkillByTarget(BattleRoom battleRoom, BattleSprite target, SkillTriggerType triggerType) {
        for (BattleSprite battleSprite : battleRoom.getRoomSprites().values()) {
            processSkill(battleRoom, battleSprite, null, target, null, triggerType);
        }
    }

    public static void processSkillByTriggerSkill(BattleRoom battleRoom, BattleSprite caster, BattleSprite target, SkillConfig triggerSkill, SkillTriggerType triggerType) {
        processSkillByTrigger(battleRoom, caster, target, 0L, triggerSkill, triggerType);
    }

    public static void processSkillByTrigger(BattleRoom battleRoom, BattleSprite caster, BattleSprite triggerTarget, long triggerValue, SkillConfig triggerSkill,
        SkillTriggerType triggerType) {
        processSkillByTrigger(battleRoom, caster, triggerTarget, triggerValue, triggerSkill, triggerType, null);
    }

    public static void processSkillByTrigger(BattleRoom battleRoom, BattleSprite caster, BattleSprite triggerTarget, long triggerValue, SkillConfig triggerSkill,
        SkillTriggerType triggerType, BattleSprite excludeSprite) {
        for (BattleSprite battleSprite : battleRoom.getRoomSprites().values()) {
            if (battleSprite.equals(excludeSprite)) {
                continue;
            }
            processSkill(battleRoom, battleSprite, caster, triggerTarget, triggerValue, triggerSkill, triggerType, null);
        }
    }

    public static void processSkill(BattleRoom battleRoom, BattleSprite battleSprite, BattleSprite caster, BattleSprite triggerTarget, SkillConfig triggerSkill,
        SkillTriggerType triggerType) {
        processSkill(battleRoom, battleSprite, caster, triggerTarget, 0L, triggerSkill, triggerType, null);
    }

    public static void processSkillByTriggerSkillExcludeTriggerTarget(BattleRoom battleRoom, BattleSprite caster, BattleSprite triggerTarget, SkillConfig triggerSkill,
        SkillTriggerType triggerType, BattleSprite excludeSprite) {
        processSkillByTrigger(battleRoom, caster, triggerTarget, 0L, triggerSkill, triggerType, excludeSprite);
    }

    public static void processSkillBySkillConfig(BattleRoom battleRoom, BattleSprite battleSprite, SkillTriggerType triggerType, SkillConfig processSkillConfig) {
        processSkill(battleRoom, battleSprite, null, null, 0, null, triggerType, processSkillConfig);
    }

    public static void processSkill(BattleRoom battleRoom, BattleSprite battleSprite, BattleSprite caster, BattleSprite target, long triggerValue, SkillConfig triggerSkill,
        SkillTriggerType triggerType, SkillConfig processSkillConfig) {
        SkillTriggerParser parser = ref.get().skillTriggerContext.getParser(triggerType);
        if (parser == null) {
            LOGGER.error("SkillTriggerParser not found,triggerType:{}", triggerType);
            return;
        }
        //获取所有触发的技能
        Collection<BattleSkill> skillList = Lists.newArrayList();
        if (processSkillConfig != null) {
            skillList.add(BattleSkill.valueOf(processSkillConfig.getSkillId()));
        } else {
            skillList = battleSprite.getSkillList(triggerType);
            //判断是否触发普攻2
            if (triggerType == SkillTriggerType.NORMAL_ATTACK && battleSprite.getSkillList(triggerType).size() > 1) {
                skillList = Lists.newArrayList();
                List<BattleSkill> list = Lists.newArrayList(battleSprite.getSkillList(triggerType));
                int buffValue = (int) BuffHelper.getBuffValue(battleSprite, SkillEffectType.TRIGGER_SKILL_PROBABILITY_BONUS_BUFF_EFFECT);
                if (RandomUtils.is10000Hit(battleSprite.getNormalAttack2Percent() + buffValue)) {
                    skillList.add(list.get(1));
                } else {
                    skillList.add(list.get(0));
                }
            }
        }
        // 英雄全死后系统精灵不再释放技能
        if (battleSprite.getSpriteType() == SpriteType.SYSTEM) {
            BattleMember battleMember = battleRoom.getBattleMember(battleSprite.getBattleCamp());
            if (battleMember.isLose()) {
                return;
            }
        }
        if (battleSprite.isDead()) {
            if (!triggerType.isIgnoreDead()) {
                return;
            }
            if (target != null && !battleSprite.equals(target)) {
                return;
            }
        }

        //根据技能配置分组,组为0的全执行,组非0的根据order自然排序取第一个  全部取出后根据order为List再排序
        Map<Integer, List<BattleSkill>> skillGroupMap = Maps.newHashMap();
        for (BattleSkill battleSkill : skillList) {
            SkillConfig skillConfig = SkillConfigService.getSkillConfig(battleSkill.getSkillId());
            if (skillConfig == null) {
                LOGGER.error("SkillConfig not found,skillId:{}", battleSkill.getSkillId());
                continue;
            }
            if (skillConfig.getTimes() > 0 && battleSkill.getTimes() >= skillConfig.getTimes()) {
                continue;
            }
            if (skillConfig.getRoundTimes() > 0 && battleSkill.getRoundTimes() >= skillConfig.getRoundTimes()) {
                continue;
            }
            if (triggerType != SkillTriggerType.TAKING_LETHAL_DAMAGE) {
                boolean isTrigger = parser.trigger(battleRoom, battleSprite, caster, target, triggerValue, triggerSkill, skillConfig);
                if (!isTrigger) {
                    continue;
                }
            }
            List<BattleSkill> battleSkills = skillGroupMap.computeIfAbsent(skillConfig.getGroup(), x -> Lists.newArrayList());
            battleSkills.add(battleSkill);
        }
        //组0全添加,组非0的根据order自然排序取第一个
        Collection<BattleSkill> operateSkillList = Lists.newArrayList();
        for (Entry<Integer, List<BattleSkill>> entry : skillGroupMap.entrySet()) {
            if (entry.getKey() == 0) {
                operateSkillList.addAll(entry.getValue());
            } else {
                Optional<BattleSkill> skillOptional = entry.getValue().stream().min(Comparator.comparing(x -> {
                    SkillConfig skillConfig = SkillConfigService.getSkillConfig(x.getSkillId());
                    return skillConfig.getOrderId();
                }));
                skillOptional.ifPresent(operateSkillList::add);
            }
        }
        //根据order为List再排序
        List<BattleSkill> finalSkill = operateSkillList.stream().sorted(Comparator.comparing(x -> {
            SkillConfig skillConfig = SkillConfigService.getSkillConfig(x.getSkillId());
            return skillConfig.getOrderId();
        })).collect(Collectors.toList());

        //如果添加的技能的技能触发类型和被添加的技能的触发类型一样,或者移除的技能的技能触发类型和被移除的技能的触发类型一样,就会报以下错误
        //java.util.ConcurrentModificationException: null at java.util.ArrayList
        //原因是在遍历skillList时去添加同触发类型的技能,相当于skillList.add(),故报错.
        for (BattleSkill battleSkill : finalSkill) {
            SkillConfig skillConfig = SkillConfigService.getSkillConfig(battleSkill.getSkillId());
            FightReport fightReport = FightReport.valueOf(battleSprite.getSpriteId(), skillConfig.getSkillId(), battleRoom.getRound());
            battleRoom.addFightReport(fightReport);
            battleSkill.addTimes();
            //触发混乱buff
            long buffValue = BuffHelper.getBuffValue(battleSprite, SkillEffectType.CONFUSION_BUFF_EFFECT);
            boolean hit = RandomUtils.is10000Hit((int) buffValue);
            if (hit) {
                battleSprite.setConfusion(true);
                fightReport.addFightEffect(FightEffect.confusion(battleSprite.getSpriteId()));
            }
            if (triggerType == SkillTriggerType.NORMAL_ATTACK) {
                attackRage(battleRoom, battleSprite, fightReport);
                attackSpiritual(battleSprite, fightReport);
            } else if (triggerType == SkillTriggerType.CAST) {
                int spilledRage = battleSprite.getSpriteBattle().getRage() - battleSprite.getSpriteBattle().getRageLimit();
                battleSprite.getSpriteBattle().setSpilledRage(Math.max(spilledRage, 0));
                fightReport.addFightEffect(FightEffect.roundRage(battleSprite, -battleSprite.getSpriteBattle().getRage()));
                FightHelper.processSkill(battleRoom, battleSprite, null, null, null, SkillTriggerType.RAGE_CHANGE);
                attackSpiritual(battleSprite, fightReport);
            }
            boolean effectResult = processSkillEffect(battleRoom, battleSprite, target, fightReport, triggerValue);
            FightHelper.processDead(battleRoom, fightReport, skillConfig);
            if (!effectResult) {
                battleSkill.decreaseTimes();
                continue;
            }

            if (triggerType == SkillTriggerType.CAST) {
                //我方任意武将释放大招后
                List<BattleSprite> battleSpriteList = Lists.newArrayList(battleRoom.getBattleSpriteList(battleSprite.getBattleCamp()));
                battleSpriteList.addAll(battleRoom.getPetBattleSpriteList(battleSprite.getBattleCamp()));
                for (BattleSprite sprite : battleSpriteList) {
                    processSkill(battleRoom, sprite, null, null, skillConfig, SkillTriggerType.FRIENDLY_HERO_CAST_AFTER);
                }
            }

            switch (triggerType) {
                case NORMAL_ATTACK:
                case CAST:
                    //某个战斗精灵行动之后
                    processSkill(battleRoom, battleSprite, null, null, skillConfig, SkillTriggerType.ACTION_AFTER);
                    //我方任意武将释放普攻/大招后
                    List<BattleSprite> battleSpriteList = Lists.newArrayList(battleRoom.getBattleSpriteList(battleSprite.getBattleCamp()));
                    battleSpriteList.addAll(battleRoom.getPetBattleSpriteList(battleSprite.getBattleCamp()));
                    for (BattleSprite sprite : battleSpriteList) {
                        processSkill(battleRoom, sprite, null, null, skillConfig, SkillTriggerType.FRIENDLY_HERO_ACTION_AFTER);
                    }
                    //敌方任意武将将释放普攻/大招后
                    BattleProtocol.BattleCamp opposite = FightHelper.BattleCampOpposite(battleSprite.getBattleCamp());
                    List<BattleSprite> enemyBattleSpriteList = Lists.newArrayList(battleRoom.getBattleSpriteList(opposite));
                    enemyBattleSpriteList.addAll(battleRoom.getPetBattleSpriteList(opposite));
                    for (BattleSprite sprite : enemyBattleSpriteList) {
                        processSkill(battleRoom, sprite, null, null, skillConfig, SkillTriggerType.ENEMY_HERO_ACTION_AFTER);
                    }
                    break;
                case TAKING_LETHAL_DAMAGE:
                    processSkillByTarget(battleRoom, battleSprite, SkillTriggerType.TAKING_LETHAL_DAMAGE_AFTER);
                    break;
                case SPACETIME_BEAUTY_SKILL:
                    //负uid为红颜出手
                    fightReport.setAttackerUid(-battleSprite.getSpriteId());
                default:
                    break;
            }
            //使用技能后的处理
            //移除致命一击的buff
            if (battleSprite.getFatalStrikeTimes() > 0) {
                Collection<BattleBuff> buffList = battleSprite.getBuffList(SkillEffectType.FATAL_STRIKE_BUFF_EFFECT);
                for (Iterator<BattleBuff> iterator = buffList.iterator(); iterator.hasNext(); ) {
                    BattleBuff buff = iterator.next();
                    iterator.remove();
                    fightReport.addFightEffect(FightEffect.removeBuff(battleSprite.getSpriteId(), buff));
                }
            }
            //清除本次技能的混乱标记
            battleSprite.setConfusion(false);
        }
    }

    /**
     * 回合结束恢复怒气
     *
     * @param battleRoom
     */
    public static void rageRecover(BattleRoom battleRoom) {
        FightReport fightReport = FightReport.valueOf(0, battleRoom.getRound());
        for (BattleSprite battleSprite : battleRoom.getRoomSprites().values()) {
            if (battleSprite.isDead()) {
                continue;
            }
            //回怒万分比buff
            int rage = battleSprite.getSpriteBattle().getRageRecover();
            long buffValue = BuffHelper.getBuffValue(battleSprite, SkillEffectType.RAGE_BONUS_BUFF_EFFECT);
            if (buffValue < 0) {
                if (!battleSprite.hasBuffType(SkillEffectType.IMMUNE_RAGE_BONUS_BUFF_EFFECT)) {
                    rage += NumberUtils.getPercentLongValue(rage, (int) buffValue);
                }
            } else {
                //todo 策划需求,暂没有效果  若要有效果需把判断放在FightEffect.roundRage里
                if (!battleSprite.hasBuffType(SkillEffectType.FORBID_RAGE_BUFF_EFFECT)) {
                    rage += NumberUtils.getPercentLongValue(rage, (int) buffValue);
                } else {
                    rage = 0;
                }
            }
            fightReport.addFightEffect(FightEffect.roundRage(battleSprite, rage));
            FightHelper.processSkill(battleRoom, battleSprite, null, null, null, SkillTriggerType.RAGE_CHANGE);
        }
        battleRoom.addFightReport(fightReport);
    }

    /**
     * 回合结束回复灵力值
     * todo 后续添加给客户端的战报在添加FightEffect
     *
     * @param battleRoom
     */
    public static void recoverSpiritual(BattleRoom battleRoom) {
        FightReport fightReport = FightReport.valueOf(0, battleRoom.getRound());
        for (BattleSprite battleSprite : battleRoom.getRoomSprites().values()) {
            int spiritualRecover = battleSprite.getSpriteBattle().getSpiritualRecover();
            long buffValue = BuffHelper.getBuffValue(battleSprite, SkillEffectType.SPIRITUAL_RECOVER_BONUS_BUFF_EFFECT);
            spiritualRecover += NumberUtils.getPercentLongValue(spiritualRecover, (int) buffValue);
            int spiritual = battleSprite.getSpriteBattle().getSpiritual();
            spiritual += spiritualRecover;
            spiritual = Math.min(spiritual, battleSprite.getSpriteBattle().getSpiritualMaxLimit());
            battleSprite.getSpriteBattle().setSpiritual(spiritual);
        }
    }

    /**
     * 普攻恢复怒气
     *
     * @param battleRoom
     * @param battleSprite
     * @param fightReport
     */
    public static void attackRage(BattleRoom battleRoom, BattleSprite battleSprite, FightReport fightReport) {
        if (battleSprite.getSpriteBattle().getRage() < battleSprite.getSpriteBattle().getRageMaxLimit()) {
            int rage = battleSprite.getSpriteBattle().getAtackRage();
            //回怒万分比buff
            long buffValue = BuffHelper.getBuffValue(battleSprite, SkillEffectType.RAGE_BONUS_BUFF_EFFECT);
            if (buffValue < 0) {
                if (!battleSprite.hasBuffType(SkillEffectType.IMMUNE_RAGE_BONUS_BUFF_EFFECT)) {
                    rage += NumberUtils.getPercentLongValue(rage, (int) buffValue);
                }
            } else {
                //todo 策划需求,暂没有效果  若要有效果需把判断放在FightEffect.roundRage里
                if (!battleSprite.hasBuffType(SkillEffectType.FORBID_RAGE_BUFF_EFFECT)) {
                    rage += NumberUtils.getPercentLongValue(rage, (int) buffValue);
                } else {
                    rage = 0;
                }
            }
            fightReport.addFightEffect(FightEffect.roundRage(battleSprite, rage));
            FightHelper.processSkill(battleRoom, battleSprite, null, null, null, SkillTriggerType.RAGE_CHANGE);
        }
    }

    /**
     * 普攻/大招回复灵力值
     * todo 后续添加给客户端的战报在添加FightEffect
     *
     * @param battleSprite
     * @param fightReport
     */
    public static void attackSpiritual(BattleSprite battleSprite, FightReport fightReport) {
        int spiritualAttack = battleSprite.getSpriteBattle().getSpiritualAttack();
        long buffValue = BuffHelper.getBuffValue(battleSprite, SkillEffectType.SPIRITUAL_RECOVER_BONUS_BUFF_EFFECT);
        spiritualAttack += NumberUtils.getPercentLongValue(spiritualAttack, (int) buffValue);
        int spiritual = battleSprite.getSpriteBattle().getSpiritual();
        spiritual += spiritualAttack;
        spiritual = Math.min(spiritual, battleSprite.getSpriteBattle().getSpiritualMaxLimit());
        battleSprite.getSpriteBattle().setSpiritual(spiritual);
    }

    /**
     * 被攻击回复怒气
     *
     * @param battleRoom
     * @param battleSprite
     * @param fightReport
     */
    public static void passiveRage(BattleRoom battleRoom, BattleSprite battleSprite, FightReport fightReport) {
        // BOSS被攻击不回复怒气
        if (battleSprite.getSpriteBattle().isBoss()) {
            return;
        }
        if (battleSprite.getSpriteBattle().getRage() < battleSprite.getSpriteBattle().getRageMaxLimit()) {
            int rage = battleSprite.getSpriteBattle().getPassiveRage();
            //回怒万分比buff
            long buffValue = BuffHelper.getBuffValue(battleSprite, SkillEffectType.RAGE_BONUS_BUFF_EFFECT);
            if (buffValue < 0) {
                if (!battleSprite.hasBuffType(SkillEffectType.IMMUNE_RAGE_BONUS_BUFF_EFFECT)) {
                    rage += NumberUtils.getPercentLongValue(rage, (int) buffValue);
                }
            } else {
                //todo 策划需求,暂没有效果  若要有效果需把判断放在FightEffect.roundRage里
                if (!battleSprite.hasBuffType(SkillEffectType.FORBID_RAGE_BUFF_EFFECT)) {
                    rage += NumberUtils.getPercentLongValue(rage, (int) buffValue);
                } else {
                    rage = 0;
                }
            }
            fightReport.addFightEffect(FightEffect.roundRage(battleSprite, rage));
            FightHelper.processSkill(battleRoom, battleSprite, null, null, null, SkillTriggerType.RAGE_CHANGE);
        }
    }

    /**
     * 受击回复灵力值
     * todo 后续添加给客户端的战报在添加FightEffect
     *
     * @param battleSprite
     * @param fightReport
     */
    public static void passiveSpiritual(BattleSprite battleSprite, FightReport fightReport) {
        int spiritualPassive = battleSprite.getSpriteBattle().getSpiritualPassive();
        long buffValue = BuffHelper.getBuffValue(battleSprite, SkillEffectType.SPIRITUAL_RECOVER_BONUS_BUFF_EFFECT);
        spiritualPassive += NumberUtils.getPercentLongValue(spiritualPassive, (int) buffValue);
        int spiritual = battleSprite.getSpriteBattle().getSpiritual();
        spiritual += spiritualPassive;
        spiritual = Math.min(spiritual, battleSprite.getSpriteBattle().getSpiritualMaxLimit());
        battleSprite.getSpriteBattle().setSpiritual(spiritual);
    }

    /**
     * 击杀敌方恢复怒气
     *
     * @param battleRoom
     * @param battleSprite
     * @param fightReport
     */
    public static void struckRage(BattleRoom battleRoom, BattleSprite battleSprite, FightReport fightReport) {
        if (battleSprite.getSpriteBattle().getRage() < battleSprite.getSpriteBattle().getRageMaxLimit()) {
            int rage = battleSprite.getSpriteBattle().getStruckRage();
            if (rage == 0) {
                return;
            }
            if (battleSprite.getSpriteBattle().getRage() + rage > battleSprite.getSpriteBattle().getRageMaxLimit()) {
                rage = battleSprite.getSpriteBattle().getRageMaxLimit() - battleSprite.getSpriteBattle().getRage();
            }
            //回怒万分比buff
            long buffValue = BuffHelper.getBuffValue(battleSprite, SkillEffectType.RAGE_BONUS_BUFF_EFFECT);
            if (buffValue < 0) {
                if (!battleSprite.hasBuffType(SkillEffectType.IMMUNE_RAGE_BONUS_BUFF_EFFECT)) {
                    rage += NumberUtils.getPercentLongValue(rage, (int) buffValue);
                }
            } else {
                //todo 策划需求,暂没有效果  若要有效果需把判断放在FightEffect.deathRoundRage
                if (!battleSprite.hasBuffType(SkillEffectType.FORBID_RAGE_BUFF_EFFECT)) {
                    rage += NumberUtils.getPercentLongValue(rage, (int) buffValue);
                } else {
                    rage = 0;
                }
            }
            fightReport.addFightEffect(FightEffect.deathRoundRage(battleSprite, rage));
            FightHelper.processSkill(battleRoom, battleSprite, null, null, null, SkillTriggerType.RAGE_CHANGE);
        }
    }

    /**
     * 敌方死亡恢复怒气
     *
     * @param battleRoom
     * @param battleSprite
     * @param fightReport
     */
    public static void enemyDiedRage(BattleRoom battleRoom, BattleSprite battleSprite, FightReport fightReport) {
        if (battleSprite.getSpriteBattle().getRage() < battleSprite.getSpriteBattle().getRageMaxLimit()) {
            int rage = battleSprite.getSpriteBattle().getEnemyDiedRage();
            if (rage == 0) {
                return;
            }
            if (battleSprite.getSpriteBattle().getHP() == 0) {
                return;
            }
            if (battleSprite.getSpriteBattle().getRage() + rage > battleSprite.getSpriteBattle().getRageMaxLimit()) {
                rage = battleSprite.getSpriteBattle().getRageMaxLimit() - battleSprite.getSpriteBattle().getRage();
            }
            //回怒万分比buff
            long buffValue = BuffHelper.getBuffValue(battleSprite, SkillEffectType.RAGE_BONUS_BUFF_EFFECT);
            if (buffValue < 0) {
                if (!battleSprite.hasBuffType(SkillEffectType.IMMUNE_RAGE_BONUS_BUFF_EFFECT)) {
                    rage += NumberUtils.getPercentLongValue(rage, (int) buffValue);
                }
            } else {
                //todo 策划需求,暂没有效果  若要有效果需把判断放在FightEffect.deathRoundRage
                if (!battleSprite.hasBuffType(SkillEffectType.FORBID_RAGE_BUFF_EFFECT)) {
                    rage += NumberUtils.getPercentLongValue(rage, (int) buffValue);
                } else {
                    rage = 0;
                }
            }
            fightReport.addFightEffect(FightEffect.deathRoundRage(battleSprite, rage));
            FightHelper.processSkill(battleRoom, battleSprite, null, null, null, SkillTriggerType.RAGE_CHANGE);
        }
    }

    /**
     * 友军死亡恢复怒气
     *
     * @param battleRoom
     * @param battleSprite
     * @param fightReport
     */
    public static void teammateDiedRage(BattleRoom battleRoom, BattleSprite battleSprite, FightReport fightReport) {
        if (battleSprite.getSpriteBattle().getRage() < battleSprite.getSpriteBattle().getRageMaxLimit()) {
            int rage = battleSprite.getSpriteBattle().getTeammateDiedRage();
            if (rage == 0) {
                return;
            }
            if (battleSprite.getSpriteBattle().getHP() == 0) {
                return;
            }
            //回怒万分比buff
            long buffValue = BuffHelper.getBuffValue(battleSprite, SkillEffectType.RAGE_BONUS_BUFF_EFFECT);
            if (buffValue < 0) {
                if (!battleSprite.hasBuffType(SkillEffectType.IMMUNE_RAGE_BONUS_BUFF_EFFECT)) {
                    rage += NumberUtils.getPercentLongValue(rage, (int) buffValue);
                }
            } else {
                //todo 策划需求,暂没有效果  若要有效果需把判断放在FightEffect.deathRoundRage
                if (battleSprite.hasBuffType(SkillEffectType.FORBID_RAGE_BUFF_EFFECT)) {
                    rage += NumberUtils.getPercentLongValue(rage, (int) buffValue);
                } else {
                    rage = 0;
                }
            }
            fightReport.addFightEffect(FightEffect.deathRoundRage(battleSprite, rage));
            FightHelper.processSkill(battleRoom, battleSprite, null, null, null, SkillTriggerType.RAGE_CHANGE);
        }
    }

    /**
     * 掉血怒气恢复
     *
     * @param battleRoom
     * @param targeter
     * @param startHpPercent
     * @param endHpPercent
     * @param report
     */
    public static void bloodLossRage(BattleRoom battleRoom, BattleSprite targeter, int startHpPercent, int endHpPercent, FightReport report) {
        if (startHpPercent > endHpPercent) {
            int interval = ref.get().globalConfigService.findGlobalConfig(GlobalConfigKey.PVP_RAGE_RECOVER_INTERVAL).findInt();
            // 百分之百特殊处理
            int startHp = (startHpPercent == 10000 ? 9999 : startHpPercent) / (interval * RandomUtils.HUNDRED);
            int endHp = endHpPercent / (interval * RandomUtils.HUNDRED);
            int times = startHp - endHp;
            if (times > 0) {
                int value = ref.get().globalConfigService.findGlobalConfig(GlobalConfigKey.PVP_RAGE_RECOVER_VALUE).findInt() * times;
                //回怒万分比buff
                long buffValue = BuffHelper.getBuffValue(targeter, SkillEffectType.RAGE_BONUS_BUFF_EFFECT);
                if (buffValue < 0) {
                    if (!targeter.hasBuffType(SkillEffectType.IMMUNE_RAGE_BONUS_BUFF_EFFECT)) {
                        value += NumberUtils.getPercentLongValue(value, (int) buffValue);
                    }
                } else {
                    //todo 策划需求,暂没有效果  若要有效果需把判断放在FightEffect.roundRage里
                    if (!targeter.hasBuffType(SkillEffectType.FORBID_RAGE_BUFF_EFFECT)) {
                        value += NumberUtils.getPercentLongValue(value, (int) buffValue);
                    } else {
                        value = 0;
                    }
                }
                report.addFightEffect(FightEffect.roundRage(targeter, value));
                FightHelper.processSkill(battleRoom, targeter, null, null, null, SkillTriggerType.RAGE_CHANGE);
            }
        }
    }

    /**
     * 处理技能效果
     *
     * @param battleRoom
     * @param attacker
     * @param fightReport
     * @param triggerValue
     */
    private static boolean processSkillEffect(BattleRoom battleRoom, BattleSprite attacker, BattleSprite triggerTarget, FightReport fightReport, long triggerValue) {
        SkillConfig skillConfig = SkillConfigService.getSkillConfig(fightReport.getSkillId());
        Map<BattleSprite, List<SkillEffectConfig>> spriteEffectMap = getBattleEffectMap(battleRoom, attacker, fightReport.getSkillId(), triggerTarget);
        if (spriteEffectMap.isEmpty()) {
            return false;
        }
        fightReport.setDamageType(skillConfig.getDamageType());
        for (Entry<BattleSprite, List<SkillEffectConfig>> entry : spriteEffectMap.entrySet()) {
            BattleSprite targeter = entry.getKey();
            if (targeter.hasBuffType(SkillEffectType.IMMUNE_CAST_BUFF_EFFECT) && skillConfig.getTriggerType() == SkillTriggerType.CAST) {
                targeter.setLastDamageSpriteId(attacker.getSpriteId());
                strikeBack(battleRoom, targeter, attacker, skillConfig, entry.getValue());
                fightReport.addFightEffect(FightEffect.immune(targeter, entry.getValue().iterator().next().getEffectId()));
                continue;
            }
            if (!attacker.getBattleCamp().equals(targeter.getBattleCamp())) {
                if (attacker.hasBuffType(SkillEffectType.MISS_NORMAL_ATTACK_BUFF_EFFECT) && (skillConfig.getTriggerType() == SkillTriggerType.NORMAL_ATTACK
                    || skillConfig.getTriggerType() == SkillTriggerType.STRIKE_BACK)) {
                    evadeEffect(battleRoom, attacker, targeter, fightReport, skillConfig, entry.getValue(), targeter.getSpriteBattle().getEvadeTimes());
                    continue;
                }
                if (!attacker.hasBuffType(SkillEffectType.IGNORE_EVADE_BUFF_EFFECT) && !skillConfig.isIgnoreEvade()) {
                    // 计算是否命中,如果未命中直接跳出循环
                    int accuracy = attacker.getAccuracy();
                    int evade = targeter.getEvade();
                    if (accuracy < evade) {
                        int evadeTimes = targeter.getSpriteBattle().getEvadeTimes();
                        //pvp最大闪避概率
                        int maxEvadePercent = ref.get().globalConfigService.findGlobalConfig(GlobalConfigKey.PVP_MAX_EVADE_PERCENT).findInt();
                        int evadePercent = (int) (Math.min(maxEvadePercent, evade - accuracy) * (Math.pow(0.8, Math.min(evadeTimes, 5))));
                        if (RandomUtils.is10000Hit(evadePercent)) {
                            evadeEffect(battleRoom, attacker, targeter, fightReport, skillConfig, entry.getValue(), evadeTimes);
                            continue;
                        }
                    }
                }
            }
            targeter.getSpriteBattle().setEvadeTimes(0);
            for (SkillEffectConfig effectConfig : entry.getValue()) {
                if (targeter.hasBuffType(SkillEffectType.IMMUNE_EFFECT_BUFF_EFFECT)) {
                    if (!effectConfig.isBuffState()) {
                        fightReport.addFightEffect(FightEffect.immune(targeter, effectConfig.getEffectId()));
                        continue;
                    }
                }
                boolean effectResult = executeEffect(fightReport, attacker, targeter, skillConfig, effectConfig, battleRoom, triggerValue);
                if (!effectResult) {
                    break;
                }
            }
        }
        return true;
    }

    private static void evadeEffect(BattleRoom battleRoom, BattleSprite attacker, BattleSprite targeter, FightReport fightReport, SkillConfig skillConfig,
        List<SkillEffectConfig> effectConfigList, int evadeTimes) {
        targeter.setLastDamageSpriteId(attacker.getSpriteId());
        targeter.getSpriteBattle().setEvadeTimes(evadeTimes + 1);
        fightReport.addFightEffect(FightEffect.miss(targeter, effectConfigList.iterator().next().getEffectId()));
        strikeBack(battleRoom, targeter, attacker, skillConfig, effectConfigList);
        //闪避时触发技能(带目标)
        processSkillByTarget(battleRoom, targeter, SkillTriggerType.EVADE);
    }

    private static void strikeBack(BattleRoom battleRoom, BattleSprite battleSprite, BattleSprite attacker, SkillConfig skillConfig, List<SkillEffectConfig> effectConfigList) {
        if (effectConfigList.stream().anyMatch(e -> e.getEffectType() == SkillEffectType.DAMAGE_EFFECT)) {
            //反击
            FightHelper.processSkill(battleRoom, battleSprite, attacker, battleSprite, skillConfig, SkillTriggerType.STRIKE_BACK);
            //反击必中
            FightHelper.processSkill(battleRoom, battleSprite, attacker, battleSprite, skillConfig, SkillTriggerType.MUST_HIT_STRIKE_BACK);
        }
    }

    /**
     * 执行效果
     *
     * @param report
     * @param attacker
     * @param targeter
     * @param skillConfig
     * @param effectConfig
     * @param battleRoom
     * @param triggerValue
     */
    private static boolean executeEffect(FightReport report, BattleSprite attacker, BattleSprite targeter, SkillConfig skillConfig, SkillEffectConfig effectConfig,
        BattleRoom battleRoom, long triggerValue) {
        SkillEffectType skillEffectType = effectConfig.getEffectType();
        SkillEffectParser effectParser = ref.get().effectContext.getParser(skillEffectType);
        if (effectParser == null) {
            LOGGER.error("SkillEffectParser not found,effectId:{},skillEffectType:{}", effectConfig.getEffectId(), skillEffectType.getId());
            return false;
        }
        boolean effectResult = effectParser.execute(report, attacker, targeter, skillConfig, effectConfig, battleRoom, triggerValue);
        return effectResult;
    }

    private static Map<BattleSprite, List<SkillEffectConfig>> getBattleEffectMap(BattleRoom battleRoom, BattleSprite attacker, int skillId, BattleSprite triggerTarget) {
        Map<BattleSprite, List<SkillEffectConfig>> spriteEffectMap = Maps.newLinkedHashMap();
        Map<SkillSeekConfig, List<SkillEffectConfig>> effectMap = SkillConfigService.getEffectMap(skillId);
        for (Entry<SkillSeekConfig, List<SkillEffectConfig>> entry : effectMap.entrySet()) {
            SkillSeekConfig seekConfig = entry.getKey();
            SkillSeekParser seekParser = ref.get().seekContext.getParser(seekConfig.getSeekType());
            if (seekParser == null) {
                LOGGER.error("SkillSeekParser not found,seekId:{},SeekType:{}", seekConfig.getSeekId(), seekConfig.getSeekType());
                continue;
            }
            if (entry.getValue().isEmpty()) {
                LOGGER.error("SkillEffectList is empty, skillId:{},seekId:{}", skillId, seekConfig.getSeekId());
                continue;
            }
            List<BattleSprite> targeterList = seekParser.seek(attacker, battleRoom, seekConfig, triggerTarget);
            if (targeterList.isEmpty()) {
                continue;
            }
            for (BattleSprite battleSprite : targeterList) {
                List<SkillEffectConfig> effectList = spriteEffectMap.get(battleSprite);
                if (effectList == null) {
                    effectList = Lists.newArrayList();
                    spriteEffectMap.put(battleSprite, effectList);
                }
                int targetHPPercent = battleSprite.getHPPercent();
                boolean hasStunBuff = battleSprite.hasBuffType(SkillEffectType.STUN_BUFF_EFFECT) || battleSprite.hasBuffType(SkillEffectType.SUPER_STUN_BUFF_EFFECT);
                for (SkillEffectConfig config : entry.getValue()) {
                    int randomProbability = 0;
                    if (config.getEffectType() == SkillEffectType.STUN_BUFF_EFFECT || config.getEffectType() == SkillEffectType.SUPER_STUN_BUFF_EFFECT) {
                        int controlProbability = attacker.getControlProbability();
                        int exemptionProbability = battleSprite.getExemptionProbability();
                        randomProbability = Math.min(10000, Math.max(-10000, controlProbability - exemptionProbability));
                    }
                    int random = RandomUtils.nextInt(1 - randomProbability, RandomUtils.TEN_THOUSAND - randomProbability);
                    int imprintLayers = attacker.getBuffList(SkillEffectType.IMPRINT_BUFF_EFFECT).stream().mapToInt(BattleBuff::getLayers).sum();
                    if (config.isTrigger(targetHPPercent, random, hasStunBuff, imprintLayers, attacker.getEightGateId())) {
                        effectList.add(config);
                    }
                }
            }
        }
        spriteEffectMap.values().removeIf(List::isEmpty);
        return spriteEffectMap;
    }

    /**
     * 获取敌对阵营
     *
     * @param camp
     * @return
     */
    public static BattleProtocol.BattleCamp BattleCampOpposite(BattleProtocol.BattleCamp camp) {
        return camp == BattleProtocol.BattleCamp.LEFT_CAMP ? BattleProtocol.BattleCamp.RIGHT_CAMP : BattleProtocol.BattleCamp.LEFT_CAMP;
    }

    @PostConstruct
    void init() {
        ref.set(this);
    }

    /**
     * 处理死亡信息
     *
     * @param battleRoom
     * @param report
     * @param skillConfig
     */
    public static void processDead(BattleRoom battleRoom, FightReport report, SkillConfig skillConfig) {
        for (Map.Entry<Long, Map<Long, Long>> entry : report.getSpriteDamageMap().entrySet()) {
            BattleSprite attacker = battleRoom.getBattleSprite(entry.getKey());
            for (Long spriteId : entry.getValue().keySet()) {
                BattleSprite injuredSprite = battleRoom.getBattleSprite(spriteId);
                if (injuredSprite.isDead()) {
                    injuredSprite.getSpriteBattle().setDiedRound(battleRoom.getRound());
                    SpriteBattle attackerSpriteBattle = attacker.getSpriteBattle();
                    attackerSpriteBattle.kill(injuredSprite);
                    if ((skillConfig != null && skillConfig.getSkillsType() == SkillType.CAST) && attacker.hasBuffType(SkillEffectType.CAST_KILL_RAGE_EFFECT)) {
                        report.addFightEffect(FightEffect.deathRoundRage(attacker, attackerSpriteBattle.getRageLimit()));
                    }
                    //首先处理排除死亡者之外得所有精灵得死亡时触发技能最后再处理死亡者本身
                    FightHelper.processSkillByTriggerSkillExcludeTriggerTarget(battleRoom, attacker, injuredSprite, skillConfig, SkillTriggerType.DEAD, injuredSprite);
                    FightHelper.deadDispel(battleRoom, injuredSprite, report);
                    FightHelper.deadRageRecover(battleRoom, injuredSprite, report);
                    //死亡时触发技能
                    FightHelper.processSkill(battleRoom, injuredSprite, attacker, injuredSprite, entry.getValue().getOrDefault(spriteId, 0L), skillConfig, SkillTriggerType.DEAD,
                        null);
                }
            }
        }
    }

    /**
     * 死亡驱散buff (1.清除所有deBuff 2.清除可清除的buff)
     *
     * @param battleRoom
     * @param battleSprite
     * @param report
     */
    public static void deadDispel(BattleRoom battleRoom, BattleSprite battleSprite, FightReport report) {
        for (Iterator<Map<IdentiyKey, BattleBuff>> iterator = battleSprite.getBuffMap().values().iterator(); iterator.hasNext(); ) {
            Map<IdentiyKey, BattleBuff> bufferMap = iterator.next();
            for (Iterator<BattleBuff> bufferIterator = bufferMap.values().iterator(); bufferIterator.hasNext(); ) {
                BattleBuff buff = bufferIterator.next();
                if (buff.isDebuff()) {
                    report.addFightEffect(FightEffect.removeBuff(battleSprite.getSpriteId(), buff));
                    bufferIterator.remove();
                    continue;
                }
                if (buff.isDispel()) {
                    report.addFightEffect(FightEffect.removeBuff(battleSprite.getSpriteId(), buff));
                    bufferIterator.remove();
                }
            }
        }
        //驱散附体红颜给出去的所有buff
        for (BattleMember battleMember : battleRoom.getMemberMap().values()) {
            for (BattleSprite sprite : battleMember.getSpriteMap().values()) {
                for (Map<IdentiyKey, BattleBuff> buffMap : sprite.getBuffMap().values()) {
                    Iterator<BattleBuff> buffIterator = buffMap.values().iterator();
                    while (buffIterator.hasNext()) {
                        BattleBuff buff = buffIterator.next();
                        if (buff.getCastSpriteUid() == -battleSprite.getSpriteId()) {
                            report.addFightEffect(FightEffect.removeBuff(sprite.getSpriteId(), buff));
                            buffIterator.remove();
                        }
                    }
                }
            }
        }
    }

    /**
     * 有人死亡时触发怒气回复
     *
     * @param battleRoom
     * @param battleSprite
     * @param report
     */
    private static void deadRageRecover(BattleRoom battleRoom, BattleSprite battleSprite, FightReport report) {
        for (BattleSprite sprite : battleRoom.getRoomSprites().values()) {
            if (sprite.isDead()) {
                continue;
            }
            if (sprite.getSpriteId() == battleSprite.getSpriteId()) {
                continue;
            }
            if (sprite.getBattleCamp() == battleSprite.getBattleCamp()) {
                FightHelper.teammateDiedRage(battleRoom, sprite, report);
            } else {
                if (report.getAttackerUid() == sprite.getSpriteId()) {
                    FightHelper.struckRage(battleRoom, sprite, report);
                } else {
                    FightHelper.enemyDiedRage(battleRoom, sprite, report);
                }
            }
        }
    }

}
