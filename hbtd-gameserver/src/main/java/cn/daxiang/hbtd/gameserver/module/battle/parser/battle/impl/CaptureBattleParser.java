package cn.daxiang.hbtd.gameserver.module.battle.parser.battle.impl;

import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.utils.FormulaUtils;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleMember;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleRoom;
import cn.daxiang.hbtd.gameserver.module.battle.parser.battle.AbstractBattleParser;
import cn.daxiang.hbtd.gameserver.module.capture.facade.CaptureFacade;
import cn.daxiang.hbtd.gameserver.module.lineup.helper.LineupAttributeHelper;
import cn.daxiang.hbtd.gameserver.module.official.helper.OfficialHelper;
import cn.daxiang.protocol.game.BattleProtocol;
import cn.daxiang.protocol.game.TypeProtocol;
import cn.daxiang.shared.GlobalConfigKey;
import cn.daxiang.shared.module.battle.BattleParameterKey;
import cn.daxiang.shared.module.battle.BattleType;
import cn.daxiang.shared.module.lineup.BattleLineupEntity;
import cn.daxiang.shared.module.lineup.LineupAttribute;
import cn.daxiang.shared.module.lineup.SpriteAttributeType;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/7/5
 */
@Component
public class CaptureBattleParser extends AbstractBattleParser {

    @Autowired
    private CaptureFacade captureFacade;

    @Override
    protected BattleType getType() {
        return BattleType.CAPTURE;
    }

    @Override
    public Result fight(long actorId, Map<BattleParameterKey, Object> parameterMap) {
        String powerExpr = globalConfigService.findGlobalConfig(GlobalConfigKey.FIGHT_POWER_EXPRESSION).getValue();
        String beastPowerExpr = globalConfigService.findGlobalConfig(GlobalConfigKey.BEAST_POWER_EXPR).getValue();
        Map<Integer, Integer> captureLineup = (Map<Integer, Integer>) parameterMap.get(BattleParameterKey.CAPTURE_LINEUP);
        Map<Integer, Integer> captureLineupHeroSkin = (Map<Integer, Integer>) parameterMap.get(BattleParameterKey.CAPTURE_LINEUP_HERO_SKIN);
        Map<Integer, LineupAttribute> lineupAttributeMap = LineupAttributeHelper.getLineupAttributes(actorId);
        Map<Integer, Collection<Integer>> leftSysSkillMaps = LineupAttributeHelper.getSystemSkillMaps(actorId);
        Map<Integer, LineupAttribute> captureLineupAttributeMap = Maps.newHashMapWithExpectedSize(captureLineup.size());
        //上阵信息
        for (LineupAttribute lineupAttribute : lineupAttributeMap.values()) {
            if (!captureLineup.containsKey(lineupAttribute.getRoleId())) {
                continue;
            }
            int positionId = captureLineup.get(lineupAttribute.getRoleId());
            Collection<Long> attributeList = SpriteAttributeType.getAttributeList(lineupAttribute.getAttributeMap());
            long power = positionId < 0 ? FormulaUtils.executeRoundingLong(beastPowerExpr, attributeList.toArray(new Long[attributeList.size()])) :
                FormulaUtils.executeRoundingLong(powerExpr, attributeList.toArray(new Long[attributeList.size()]));
            lineupAttribute.setPositionId(positionId);
            lineupAttribute.setPower(power);
            lineupAttribute.getAttributeMap().put(SpriteAttributeType.HP_INIT_MAX, lineupAttribute.getAttributeMap().getOrDefault(SpriteAttributeType.HP, 1L));
            Integer heroSkinId = captureLineupHeroSkin.getOrDefault(lineupAttribute.getRoleId(), 0);
            lineupAttribute.setHeroSkinId(heroSkinId);
            captureLineupAttributeMap.put(positionId, lineupAttribute);
        }
        //神兽共鸣信息
        if (captureLineupAttributeMap.size() < captureLineup.size()) {
            Map<Integer, LineupAttribute> resonanceBeastLineupAttributes = LineupAttributeHelper.getResonanceBeastLineupAttributes(actorId);
            for (LineupAttribute lineupAttribute : resonanceBeastLineupAttributes.values()) {
                if (!captureLineup.containsKey(lineupAttribute.getRoleId())) {
                    continue;
                }
                int positionId = captureLineup.get(lineupAttribute.getRoleId());
                Collection<Long> attributeList = SpriteAttributeType.getAttributeList(lineupAttribute.getAttributeMap());
                long power = FormulaUtils.executeRoundingLong(beastPowerExpr, attributeList.toArray(new Long[attributeList.size()]));
                lineupAttribute.setPositionId(positionId);
                lineupAttribute.setPower(power);
                lineupAttribute.getAttributeMap().put(SpriteAttributeType.HP_INIT_MAX, lineupAttribute.getAttributeMap().getOrDefault(SpriteAttributeType.HP, 1L));
                captureLineupAttributeMap.put(positionId, lineupAttribute);
            }
        }
        parameterMap.put(BattleParameterKey.CAPTURE_LINEUP, captureLineupAttributeMap);
        Map<Byte, Object> attributes = (Map<Byte, Object>) parameterMap.get(BattleParameterKey.CROSS_BATTLE_ATTRIBUTE);
        int officialLevel = OfficialHelper.getOfficialLevel(actorId);
        if (!attributes.containsKey((byte) TypeProtocol.ActorFieldType.SERVER_ID_VALUE)) {
            officialLevel = 0;
        }
        BattleLineupEntity leftBattleLineupEntity = BattleLineupEntity.valueOf(captureLineupAttributeMap, leftSysSkillMaps);
        BattleMember leftMember = this.createBattleMember(actorId, officialLevel, BattleProtocol.BattleCamp.LEFT_CAMP, leftBattleLineupEntity, getType());
        long targetActorId = Long.parseLong(attributes.getOrDefault((byte) TypeProtocol.ActorFieldType.ACTOR_ID_VALUE, 0L).toString());
        long targetPower = Long.parseLong(attributes.getOrDefault((byte) TypeProtocol.ActorFieldType.ACTOR_POWER_VALUE, 0L).toString());
        int targetOfficialLevel = (int) attributes.getOrDefault((byte) TypeProtocol.ActorFieldType.ACTOR_OFFICIAL_VALUE, 0);
        //        Map<Integer, LineupAttribute> targetLineupAttributeMap = (Map<Integer, LineupAttribute>) parameterMap.get(BattleParameterKey.CROSS_BATTLE_LINEUP);
        BattleLineupEntity rightBattleLineupEntity = (BattleLineupEntity) parameterMap.get(BattleParameterKey.CROSS_BATTLE_LINEUP);
        BattleMember rightMember =
            this.createBattleMember(targetActorId, targetPower, targetOfficialLevel, BattleProtocol.BattleCamp.RIGHT_CAMP, rightBattleLineupEntity, getType());
        BattleRoom battleRoom = this.createBattleRoom(leftMember, rightMember, parameterMap);
        battleRoomFacade.createBattleRoom(battleRoom);
        return Result.valueOf();
    }

    @Override
    public void battleEnd(long actorId, BattleRoom battleRoom) {
        captureFacade.challengeRecord(actorId, battleRoom);
    }
}
