package cn.daxiang.hbtd.gameserver.module.mount;

import cn.daxiang.framework.dispatch.DispatchType;
import cn.daxiang.framework.protocal.DataPacket;
import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.framework.router.annotation.Cmd;
import cn.daxiang.hbtd.gameserver.core.database.table.Mount;
import cn.daxiang.hbtd.gameserver.core.router.GatewayRouterHandlerImpl;
import cn.daxiang.hbtd.gameserver.module.PbBuilder;
import cn.daxiang.hbtd.gameserver.module.mount.facade.MountFacade;
import cn.daxiang.hbtd.gameserver.module.mount.helper.MountPBHelper;
import cn.daxiang.hbtd.gameserver.module.mount.type.MountPrerogativeType;
import cn.daxiang.protocol.game.CommonProtocol;
import cn.daxiang.protocol.game.CommonProtocol.IntPacket;
import cn.daxiang.protocol.game.CommonProtocol.RewardResult;
import cn.daxiang.protocol.game.CommonProtocol.RewardResultResponse;
import cn.daxiang.protocol.game.ModuleProtocol.Module;
import cn.daxiang.protocol.game.MountProtocol.MountCmd;
import cn.daxiang.protocol.game.MountProtocol.MountGachaRequest;
import cn.daxiang.protocol.game.MountProtocol.MountGachaResultResponse;
import cn.daxiang.protocol.game.MountProtocol.MountInfoResponse;
import cn.daxiang.protocol.game.MountProtocol.MountListResponse;
import io.netty.channel.Channel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;

/**
 * 坐骑
 *
 * <AUTHOR>
 * @date 2024/4/9
 */
@Component
public class MountHandler extends GatewayRouterHandlerImpl {
    @Autowired
    private MountFacade mountFacade;

    @Override
    public int getModule() {
        return Module.MOUNT_VALUE;
    }

    @Cmd(Id = MountCmd.GET_MOUNT_INFO_VALUE, dispatchType = DispatchType.ACTOR)
    public void getMountInfos(Channel channel, Long actorId, DataPacket packet) {
        TResult<MountInfoResponse> result = mountFacade.getMountInfos(actorId);
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = MountCmd.GET_MOUNT_LIST_VALUE, dispatchType = DispatchType.ACTOR)
    public void getMountList(Channel channel, Long actorId, DataPacket packet) {
        Collection<Mount> mountList = mountFacade.getMountList(actorId);
        MountListResponse response = MountPBHelper.buildMountListResponse(mountList);
        channelWrite(channel, packet, response);
    }

    @Cmd(Id = MountCmd.MOUNT_ACTIVATION_VALUE, dispatchType = DispatchType.ACTOR)
    public void MountActivation(Channel channel, Long actorId, DataPacket packet) {
        IntPacket request = packet.getValue(IntPacket.parser());
        Result result = mountFacade.mountActivation(actorId, request.getValue());
        channelWrite(channel, packet, result.statusCode);
    }

    @Cmd(Id = MountCmd.MOUNT_STAR_UP_VALUE, dispatchType = DispatchType.ACTOR)
    public void mountStarUp(Channel channel, Long actorId, DataPacket packet) {
        IntPacket request = packet.getValue(IntPacket.parser());
        Result result = mountFacade.mountStarUp(actorId, request.getValue());
        channelWrite(channel, packet, result.statusCode);
    }

    @Cmd(Id = MountCmd.MOUNT_PLATFORM_UPGRADE_VALUE, dispatchType = DispatchType.ACTOR)
    public void mountPlatformUpgrade(Channel channel, Long actorId, DataPacket packet) {
        Result result = mountFacade.mountPlatformUpgrade(actorId);
        channelWrite(channel, packet, result.statusCode);
    }

    @Cmd(Id = MountCmd.MOUNT_INTEGRAL_REWARD_VALUE, dispatchType = DispatchType.ACTOR)
    public void mountIntegralReward(Channel channel, Long actorId, DataPacket packet) {
        IntPacket request = packet.getValue(IntPacket.parser());
        TResult<RewardResult> result = mountFacade.mountIntegralReward(actorId, request.getValue());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        RewardResultResponse response = PbBuilder.buildRewardResultResponse(result.item);
        channelWrite(channel, packet, response);
    }

    @Cmd(Id = MountCmd.MOUNT_PRIVILEGE_REWARD_RECEIVE_VALUE, dispatchType = DispatchType.ACTOR)
    public void mountPrivilegeRewardReceive(Channel channel, Long actorId, DataPacket packet) {
        IntPacket request = packet.getValue(IntPacket.parser());
        MountPrerogativeType type = MountPrerogativeType.getType(request.getValue());
        TResult<RewardResult> result = mountFacade.mountPrivilegeRewardReceive(actorId, type);
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = MountCmd.MOUNT_PRIVILEGE_CHOOSE_WISH_VALUE, dispatchType = DispatchType.ACTOR)
    public void mountPrivilegeChooseWish(Channel channel, Long actorId, DataPacket packet) {
        IntPacket request = packet.getValue(IntPacket.parser());
        Result result = mountFacade.mountPrivilegeChooseWish(actorId, request.getValue());
        channelWrite(channel, packet, result.statusCode);
    }

    @Cmd(Id = MountCmd.MOUNT_GACHA_VALUE, dispatchType = DispatchType.ACTOR)
    public void mountGacha(Channel channel, Long actorId, DataPacket packet) {
        MountGachaRequest request = packet.getValue(MountGachaRequest.parser());
        TResult<MountGachaResultResponse> result = mountFacade.mountGacha(actorId, request.getTimes(), request.getUseGoods());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = MountCmd.RECEIVE_MOUNT_GACHA_CHARGE_REWARD_VALUE, dispatchType = DispatchType.ACTOR)
    public void receiveMountGachaChargeReward(Channel channel, Long actorId, DataPacket packet) {
        IntPacket request = packet.getValue(IntPacket.parser());
        TResult<RewardResult> result = mountFacade.receiveMountGachaChargeReward(actorId, request.getValue());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

}
