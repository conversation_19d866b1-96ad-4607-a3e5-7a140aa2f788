package cn.daxiang.hbtd.gameserver.core.event.impl;

import cn.daxiang.framework.event.ActorEvent;
import cn.daxiang.shared.event.EventKey;

/**
 * 宝物合成事件
 *
 * <AUTHOR>
 */
public class TreasureSynthesizeEvent extends ActorEvent {
    /**
     * 品质
     */
    public int quality;
    /**
     * 数量
     */
    public int count;

    public TreasureSynthesizeEvent(long actorId, int quality, int count) {
        super(EventKey.TREASURE_SYNTHESIZE_EVENT, actorId);
        this.quality = quality;
        this.count = count;
    }
}
