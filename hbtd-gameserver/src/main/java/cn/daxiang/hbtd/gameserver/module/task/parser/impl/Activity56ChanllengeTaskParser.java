package cn.daxiang.hbtd.gameserver.module.task.parser.impl;

import cn.daxiang.hbtd.gameserver.core.database.table.Task;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.TaskConfig;
import cn.daxiang.hbtd.gameserver.core.event.impl.Activity56ChallengeEvent;
import cn.daxiang.hbtd.gameserver.module.task.parser.AbstractTaskParser;
import cn.daxiang.hbtd.gameserver.module.task.type.TaskConditionType;
import cn.daxiang.protocol.game.TaskProtocol;
import org.springframework.stereotype.Component;

/**
 * 活动56强出如龙-试炼任务
 */
@Component
public class Activity56ChanllengeTaskParser extends AbstractTaskParser<Activity56ChallengeEvent> {
    @Override
    public void init(Task task, TaskConfig taskConfig) {
        if (task.getValue() >= taskConfig.getValue()) {
            task.setStatus(TaskProtocol.TaskStatus.TASK_FINISH);
        }
    }

    @Override
    protected TaskConditionType getType() {
        return TaskConditionType.ACTIVITY_56_CHALLENGE;
    }

    @Override
    protected boolean parseCondition(Activity56ChallengeEvent event, Task task, TaskConfig taskConfig) {
        if (event.chapterId >= (int) taskConfig.getValue()) {
            task.setValue(event.chapterId);
            task.setStatus(TaskProtocol.TaskStatus.TASK_FINISH);
            return true;
        }
        return false;
    }

}
