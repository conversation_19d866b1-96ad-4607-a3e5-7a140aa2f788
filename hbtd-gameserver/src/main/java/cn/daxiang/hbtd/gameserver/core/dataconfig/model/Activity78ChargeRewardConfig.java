package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.dataconfig.annotation.FieldIgnore;
import cn.daxiang.framework.identity.IdentiyKey;
import cn.daxiang.shared.reward.RewardObject;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * 活动-33-节日活动-直购礼包配置
 */
@DataFile(fileName = "activity_78_charge_reward_config")
public class Activity78ChargeRewardConfig implements ModelAdapter {
    /**
     * 唯一Id
     */
    private int id;
    /**
     * data
     */
    private int data;
    /**
     * 充值Id
     */
    private int chargeId;
    /**
     * 限购次数
     */
    private int times;
    /**
     * 消耗列表([[rewardType,id,num],[rewardType,id,num]])
     * {@code RewardType}
     */
    private String cost;
    /**
     * 奖励([[rewardType,id,num],[rewardType,id,num]])
     * {@code RewardType}
     */
    private String rewards;
    /**
     * 重置类型
     * 1-每日限购 * 2-本轮限购
     */
    private int type;
    @FieldIgnore
    private List<RewardObject> rewardList = Lists.newArrayList();
    @FieldIgnore
    private List<RewardObject> costList = Lists.newArrayList();

    @Override
    public void initialize() {
        JSONArray rewardsArray = JSONArray.parseArray(rewards);
        for (Object rewardsItem : rewardsArray) {
            JSONArray rewardArray = JSONArray.parseArray(rewardsItem.toString());
            RewardObject rewardObject = RewardObject.valueOf(rewardArray);
            rewardList.add(rewardObject);
        }
        JSONArray costsArray = JSONArray.parseArray(cost);
        for (Object costsItem : costsArray) {
            JSONArray costArray = JSONArray.parseArray(costsItem.toString());
            RewardObject rewardObject = RewardObject.valueOf(costArray);
            costList.add(rewardObject);
        }
    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(data, id);
    }

    public int getData() {
        return data;
    }

    public int getChargeId() {
        return chargeId;
    }

    public int getTimes() {
        return times;
    }

    public List<RewardObject> getRewardList() {
        return rewardList;
    }

    public List<RewardObject> getCostList() {
        return costList;
    }

    public int getId() {
        return id;
    }

    public int getType() {
        return type;
    }
}
