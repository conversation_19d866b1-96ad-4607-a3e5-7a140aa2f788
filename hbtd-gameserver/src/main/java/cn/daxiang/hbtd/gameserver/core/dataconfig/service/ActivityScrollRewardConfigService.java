package cn.daxiang.hbtd.gameserver.core.dataconfig.service;

import cn.daxiang.framework.dataconfig.ConfigServiceAdapter;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.ActivityScrollRewardConfig;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;
import java.util.Optional;

/**
 * @Author: Gary
 * @Date 2022-09-17 15:54
 * @Description:
 */
@Component
public class ActivityScrollRewardConfigService extends ConfigServiceAdapter {

    /**
     * key:data,value:
     * {
     * key:closeDay{@link ActivityScrollRewardConfig#getCloseTime()}
     * value:Collection<id>
     * }
     */
    private static Map<Integer, Map<Integer, Collection<Integer>>> ACTIVITY_END_SCROLL_CONFIG_MAP = Maps.newHashMap();

    public static Optional<Collection<Integer>> getlastScrollByDay(int data, int day) {
        Map<Integer, Collection<Integer>> map = ACTIVITY_END_SCROLL_CONFIG_MAP.get(data);
        if (map == null) {
            return Optional.empty();
        }
        return Optional.ofNullable(map.get(day));
    }

    @Override
    protected void initialize() {
        Collection<ActivityScrollRewardConfig> configs = dataConfig.listAll(this, ActivityScrollRewardConfig.class);
        for (ActivityScrollRewardConfig config : configs) {
            Map<Integer, Collection<Integer>> map = ACTIVITY_END_SCROLL_CONFIG_MAP.computeIfAbsent(config.getData(), x -> Maps.newTreeMap());
            Collection<Integer> collection = map.computeIfAbsent(config.getCloseTime(), x -> Lists.newArrayList());
            collection.add(config.getId());
        }
    }

    @Override
    protected void clean() {
        ACTIVITY_END_SCROLL_CONFIG_MAP.clear();
    }
}
