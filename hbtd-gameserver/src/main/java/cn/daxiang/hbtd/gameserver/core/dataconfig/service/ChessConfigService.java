package cn.daxiang.hbtd.gameserver.core.dataconfig.service;

import cn.daxiang.framework.dataconfig.ConfigServiceAdapter;
import cn.daxiang.framework.utils.RandomUtils;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.ChessChargeRewardConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.ChessChestConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.ChessExpressionConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.ChessFeaturesConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.ChessFormationPoolConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.ChessStoryConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.ChessStoryRewardConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.ChessWishListConfig;
import cn.daxiang.shared.module.chess.model.entity.ChessMonster;
import cn.daxiang.shared.module.chess.model.entity.MonsterLineup;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Collections;
import java.util.Map;
import java.util.Optional;
import java.util.TreeMap;

/**
 * <AUTHOR>
 * @date 2023/5/4
 */
@Component
public class ChessConfigService extends ConfigServiceAdapter {
    /**
     * key:circulate,value:{key:storyId,value:ChessStoryConfig}
     */
    private static TreeMap<Integer, Map<Integer, ChessStoryConfig>> CHESS_STORY_CONFIG_MAP = Maps.newTreeMap();
    /**
     * key: circulate,value:{key:row value:{storyId...}}
     */
    private static Map<Integer, Map<Integer, Collection<Integer>>> CIRCULATE_ROW_STORY_ID_MAP = Maps.newHashMap();
    /**
     * key: circulate,value:{key:column value:{storyId...}}
     */
    private static Map<Integer, Map<Integer, Collection<Integer>>> CIRCULATE_COLUMN_STORY_ID_MAP = Maps.newHashMap();
    /**
     * key:id.value:{key:level,value:ChessChestConfig}
     */
    private static Map<Integer, TreeMap<Integer, ChessChestConfig>> CHESS_CHEST_CONFIG_MAP = Maps.newHashMap();
    /**
     * key:id.value:{key:level,value:ChessStoryRewardConfig}
     */
    private static Map<Integer, TreeMap<Integer, ChessStoryRewardConfig>> CHESS_STORY_REWARD_CONFIG_MAP = Maps.newHashMap();
    /**
     * key:featuresGroup ,value:{key:skillId,value:weight}
     */
    private static Map<Integer, Map<Integer, Integer>> CHESS_FEATURE_POOL_ID_MAP = Maps.newHashMap();
    /**
     * key:formationId,value:ChessFormationPoolConfig
     */
    private static Map<Integer, ChessFormationPoolConfig> CHESS_FORMATION_POOL_CONFIG_MAP = Maps.newHashMap();
    /**
     * key:id,value:expression表达式
     */
    private static Map<Integer, String> CHESS_EXPRESSION_CONFIG_MAP = Maps.newHashMap();
    /**
     * key:configId, value:ChessWishListConfig
     */
    private static TreeMap<Integer, ChessWishListConfig> CHESS_WISH_LIST_CONFIG_MAP = Maps.newTreeMap();
    /**
     * key:chargeId
     * value:{@link ChessChargeRewardConfig}
     */
    private final static Map<Integer, ChessChargeRewardConfig> CHESS_TURNTABLE_CHARGE_REWARD_CONFIG_MAP = Maps.newHashMap();

    @Override
    protected void initialize() {
        Collection<ChessStoryConfig> chessStoryConfigs = dataConfig.listAll(this, ChessStoryConfig.class);
        chessStoryConfigs.forEach(x -> {
            Map<Integer, ChessStoryConfig> chessStoryConfigMap = CHESS_STORY_CONFIG_MAP.computeIfAbsent(x.getCirculate(), y -> Maps.newHashMap());
            chessStoryConfigMap.put(x.getStoryId(), x);

            Map<Integer, Collection<Integer>> rowStoryIdMap = CIRCULATE_ROW_STORY_ID_MAP.computeIfAbsent(x.getCirculate(), y -> Maps.newHashMap());
            Collection<Integer> rowStoryIdSet = rowStoryIdMap.computeIfAbsent(x.getRow(), y -> Sets.newHashSet());
            rowStoryIdSet.add(x.getStoryId());

            Map<Integer, Collection<Integer>> columnStoryIdMap = CIRCULATE_COLUMN_STORY_ID_MAP.computeIfAbsent(x.getCirculate(), y -> Maps.newHashMap());
            Collection<Integer> columnStoryIdSet = columnStoryIdMap.computeIfAbsent(x.getColumn(), y -> Sets.newHashSet());
            columnStoryIdSet.add(x.getStoryId());
        });

        Collection<ChessChestConfig> chessChestConfigs = dataConfig.listAll(this, ChessChestConfig.class);
        chessChestConfigs.forEach(x -> {
            TreeMap<Integer, ChessChestConfig> chessMap = CHESS_CHEST_CONFIG_MAP.computeIfAbsent(x.getId(), y -> Maps.newTreeMap());
            chessMap.put(x.getLevel(), x);
        });

        Collection<ChessStoryRewardConfig> chessStoryRewardConfigs = dataConfig.listAll(this, ChessStoryRewardConfig.class);
        chessStoryRewardConfigs.forEach(x -> {
            TreeMap<Integer, ChessStoryRewardConfig> storyRewardMap = CHESS_STORY_REWARD_CONFIG_MAP.computeIfAbsent(x.getStoryId(), y -> Maps.newTreeMap());
            storyRewardMap.put(x.getLevel(), x);
        });

        Collection<ChessFeaturesConfig> chessFeaturesConfigs = dataConfig.listAll(this, ChessFeaturesConfig.class);
        chessFeaturesConfigs.forEach(x -> {
            Map<Integer, Integer> skillIdMap = CHESS_FEATURE_POOL_ID_MAP.computeIfAbsent(x.getFeaturesGroup(), y -> Maps.newHashMap());
            skillIdMap.put(x.getSkillId(), x.getWeight());
        });

        Collection<ChessFormationPoolConfig> chessFormationPoolConfigs = dataConfig.listAll(this, ChessFormationPoolConfig.class);
        chessFormationPoolConfigs.forEach(x -> {
            CHESS_FORMATION_POOL_CONFIG_MAP.put(x.getFormationId(), x);
        });

        Collection<ChessExpressionConfig> chessExpressionConfigs = dataConfig.listAll(this, ChessExpressionConfig.class);
        chessExpressionConfigs.forEach(x -> {
            CHESS_EXPRESSION_CONFIG_MAP.put(x.getId(), x.getExpression());
        });

        Collection<ChessWishListConfig> chessWishListConfigs = dataConfig.listAll(this, ChessWishListConfig.class);
        chessWishListConfigs.forEach(x -> {
            CHESS_WISH_LIST_CONFIG_MAP.put(x.getId(), x);
        });

        Collection<ChessChargeRewardConfig> chargeRewardConfigs = this.dataConfig.listAll(this, ChessChargeRewardConfig.class);
        for (ChessChargeRewardConfig config : chargeRewardConfigs) {
            if (config.getChargeId() > 0) {
                CHESS_TURNTABLE_CHARGE_REWARD_CONFIG_MAP.put(config.getChargeId(), config);
            }
        }
    }

    @Override
    protected void clean() {
        CHESS_STORY_CONFIG_MAP.clear();
        CIRCULATE_ROW_STORY_ID_MAP.clear();
        CIRCULATE_COLUMN_STORY_ID_MAP.clear();
        CHESS_CHEST_CONFIG_MAP.clear();
        CHESS_STORY_REWARD_CONFIG_MAP.clear();
        CHESS_FEATURE_POOL_ID_MAP.clear();
        CHESS_FORMATION_POOL_CONFIG_MAP.clear();
        CHESS_EXPRESSION_CONFIG_MAP.clear();
        CHESS_WISH_LIST_CONFIG_MAP.clear();
        CHESS_TURNTABLE_CHARGE_REWARD_CONFIG_MAP.clear();
    }

    /**
     * 得到宝箱所需要的通关Id列表
     *
     * @param circulate
     * @param type
     * @param rowcolum
     * @return
     */
    public static Collection<Integer> getStoryIdList(int circulate, int type, int rowcolum) {
        Collection<Integer> storyIds = Lists.newArrayList();
        Map<Integer, Collection<Integer>> rowStorIdMap = CIRCULATE_ROW_STORY_ID_MAP.get(circulate);
        if (rowStorIdMap == null) {
            return storyIds;
        }
        switch (type) {
            case 0:
                rowStorIdMap.values().forEach(x -> {
                    storyIds.addAll(x);
                });
                break;
            case 1:
                storyIds.addAll(rowStorIdMap.getOrDefault(rowcolum, Collections.emptyList()));
                break;
            case 2:
                Map<Integer, Collection<Integer>> columnStorIdMap = CIRCULATE_COLUMN_STORY_ID_MAP.get(circulate);
                if (columnStorIdMap == null) {
                    return storyIds;
                }
                storyIds.addAll(columnStorIdMap.getOrDefault(rowcolum, Collections.emptyList()));
                break;
        }
        return storyIds;
    }

    public static ChessChestConfig getChessChestConfig(int id, int level) {
        TreeMap<Integer, ChessChestConfig> chestMap = CHESS_CHEST_CONFIG_MAP.get(id);
        if (chestMap == null) {
            return null;
        }
        Integer floorKey = chestMap.floorKey(level);
        if (floorKey == null) {
            return null;
        }
        return chestMap.get(floorKey);
    }

    public static ChessStoryRewardConfig getChessStoryRewardConfig(int storyId, int level) {
        TreeMap<Integer, ChessStoryRewardConfig> chestMap = CHESS_STORY_REWARD_CONFIG_MAP.get(storyId);
        if (chestMap == null) {
            return null;
        }
        Integer floorKey = chestMap.floorKey(level);
        if (floorKey == null) {
            return null;
        }
        return chestMap.get(floorKey);
    }

    public static MonsterLineup getMonsterLineup(int circulate, int storyId, int formationId) {
        Map<Integer, ChessStoryConfig> chessStoryConfigMap = CHESS_STORY_CONFIG_MAP.get(circulate);
        if (chessStoryConfigMap == null) {
            return null;
        }
        ChessStoryConfig chessStoryConfig = chessStoryConfigMap.get(storyId);
        if (chessStoryConfig == null) {
            return null;
        }
        Collection<Integer> featureIdList = getFeatureIdList(chessStoryConfig.getFeaturesPoolIdList());
        //key:positionId,value:ChessMonster
        Map<Integer, ChessMonster> chessMonsterMap = Maps.newHashMap();
        ChessFormationPoolConfig chessFormationPoolConfig = CHESS_FORMATION_POOL_CONFIG_MAP.get(formationId);
        if (chessFormationPoolConfig == null) {
            return null;
        }
        Collection<Integer> filterIdList = Lists.newArrayList();
        switch (chessFormationPoolConfig.getType()) {
            case 1:
                Integer randomHit = RandomUtils.randomHit(chessFormationPoolConfig.getFormationMap().values());
                Optional<Integer> randomRoleId1 = HeroConfigService.getRandomRoleId(filterIdList, CHESS_EXPRESSION_CONFIG_MAP.get(randomHit));
                chessFormationPoolConfig.getFormationMap().forEach((position, id) -> {
                    chessMonsterMap.put(position, ChessMonster.valueOf(randomRoleId1.get()));
                });
                break;
            case 2:
                chessFormationPoolConfig.getFormationMap().forEach((position, id) -> {
                    Optional<Integer> randomRoleId2 = HeroConfigService.getRandomRoleId(filterIdList, CHESS_EXPRESSION_CONFIG_MAP.get(id));
                    filterIdList.add(randomRoleId2.get());
                    chessMonsterMap.put(position, ChessMonster.valueOf(randomRoleId2.get()));
                });
                break;
            case 3:
                chessFormationPoolConfig.getFormationMap().forEach((position, id) -> {
                    Optional<Integer> randomRoleId3 = HeroConfigService.getRandomRoleId(filterIdList, CHESS_EXPRESSION_CONFIG_MAP.get(id));
                    chessMonsterMap.put(position, ChessMonster.valueOf(randomRoleId3.get()));
                });
                break;
        }

        MonsterLineup monsterLineup = MonsterLineup.valueOf(chessMonsterMap, featureIdList, formationId);
        return monsterLineup;
    }

    private static Collection<Integer> getFeatureIdList(Collection<Integer> featuresPoolIdList) {
        Collection<Integer> featureIdList = Lists.newArrayList();
        Map<Integer, Integer> randomMap = Maps.newHashMap();
        featuresPoolIdList.forEach(x -> {
            CHESS_FEATURE_POOL_ID_MAP.get(x).forEach((k, v) -> {
                if (!featureIdList.contains(k)) {
                    randomMap.put(k, v);
                }
            });
            Integer random = RandomUtils.randomByWeight(randomMap);
            if (random != null) {
                featureIdList.add(random);
            }
            randomMap.clear();
        });
        return featureIdList;
    }

    public static ChessWishListConfig getFirstChessWishListConfig() {
        Map.Entry<Integer, ChessWishListConfig> entryMap = CHESS_WISH_LIST_CONFIG_MAP.firstEntry();
        if (entryMap == null) {
            return null;
        }
        return entryMap.getValue();
    }

    /**
     * 获取充值礼包配置
     *
     * @param chargeId
     * @return
     */
    public static Optional<ChessChargeRewardConfig> getChessChargeRewardConfig(int chargeId) {
        return Optional.ofNullable(CHESS_TURNTABLE_CHARGE_REWARD_CONFIG_MAP.get(chargeId));
    }

}
