package cn.daxiang.hbtd.gameserver.core.dataconfig.service;

import cn.daxiang.framework.dataconfig.ConfigServiceAdapter;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.Activity31ChargeRewardConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.Activity31RankConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.Activity31TargetRewardConfig;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.Activity31TaskConfig;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Collections;
import java.util.Map;
import java.util.Optional;
import java.util.TreeMap;

/**
 * @Author: Gary
 * @Date 2022-08-15 9:52
 * @Description:
 */
@Component
public class Activity31ConfigService extends ConfigServiceAdapter {
    /**
     * key:data,value:{key:cycle,Collection{id}}
     */
    private static final Map<Integer, Map<Integer, Collection<Activity31TargetRewardConfig>>> ACTIVITY_31_TARGET_GROUP_CONFIG = Maps.newHashMap();
    /**
     * key:data,value:{key:chargeId,value:config}
     */
    private static final Map<Integer, Map<Integer, Activity31ChargeRewardConfig>> ACTIVITY_31_CHARGE_REWARD_CONFIG = Maps.newHashMap();
    /**
     * key:data
     * value:{key:rank,value:config}
     */
    private static final Map<Integer, TreeMap<Long, Activity31RankConfig>> ACTIVITY_31_RANK_CONFIG = Maps.newHashMap();

    /**
     * 所有任务 key:data value:taskIdList
     */
    private static final Map<Integer, Collection<Integer>> ACTIVITY_31_TASK_CONFIG_MAP = Maps.newHashMap();

    /**
     * 获取轮次任务列表
     *
     * @param data
     * @param cycle
     * @return
     */
    public static Optional<Collection<Activity31TargetRewardConfig>> getGroupIdsByCycle(int data, int cycle) {
        Map<Integer, Collection<Activity31TargetRewardConfig>> map = ACTIVITY_31_TARGET_GROUP_CONFIG.get(data);
        if (map == null) {
            return Optional.empty();
        }
        return Optional.ofNullable(map.get(cycle));
    }

    public static Optional<Activity31ChargeRewardConfig> getActivity31ChargeRewardConfig(int data, int chargeId) {
        Map<Integer, Activity31ChargeRewardConfig> map = ACTIVITY_31_CHARGE_REWARD_CONFIG.get(data);
        if (map == null) {
            return Optional.empty();
        }
        return Optional.ofNullable(map.get(chargeId));
    }

    /**
     * 获取排行配置
     *
     * @param data
     * @param rank
     * @return
     */
    public static Optional<Activity31RankConfig> getRankConfig(int data, long rank) {
        TreeMap<Long, Activity31RankConfig> configTreeMap = ACTIVITY_31_RANK_CONFIG.get(data);
        if (configTreeMap == null) {
            return Optional.empty();
        }
        Long key = configTreeMap.ceilingKey(rank);
        if (key == null) {
            return Optional.empty();
        }
        return Optional.ofNullable(configTreeMap.get(key));
    }

    /**
     * 获取排行榜人数限制
     *
     * @param data data
     * @return 排名
     */
    public static Optional<Activity31RankConfig> getLastRankConfig(int data) {
        TreeMap<Long, Activity31RankConfig> configTreeMap = ACTIVITY_31_RANK_CONFIG.get(data);
        if (configTreeMap == null) {
            return Optional.empty();
        }
        return Optional.ofNullable(configTreeMap.lastEntry().getValue());
    }

    public static boolean isContainsTask(int data, int taskId) {
        Collection<Integer> taskIdList = ACTIVITY_31_TASK_CONFIG_MAP.getOrDefault(data, Collections.emptyList());
        return taskIdList.contains(taskId);
    }

    @Override
    protected void initialize() {
        Collection<Activity31TargetRewardConfig> targetRewardConfigs = dataConfig.listAll(this, Activity31TargetRewardConfig.class);
        for (Activity31TargetRewardConfig config : targetRewardConfigs) {
            if (config.getRank() != 0) {
                Map<Integer, Collection<Activity31TargetRewardConfig>> map = ACTIVITY_31_TARGET_GROUP_CONFIG.computeIfAbsent(config.getData(), x -> Maps.newHashMap());
                Collection<Activity31TargetRewardConfig> collection = map.computeIfAbsent(config.getGroup(), x -> Lists.newArrayList());
                collection.add(config);
            }
        }
        Collection<Activity31ChargeRewardConfig> chargeRewardConfigs = dataConfig.listAll(this, Activity31ChargeRewardConfig.class);
        for (Activity31ChargeRewardConfig config : chargeRewardConfigs) {
            if (config.getChargeId() != 0) {
                Map<Integer, Activity31ChargeRewardConfig> map = ACTIVITY_31_CHARGE_REWARD_CONFIG.computeIfAbsent(config.getData(), x -> Maps.newHashMap());
                map.put(config.getChargeId(), config);
            }
        }
        Collection<Activity31RankConfig> rankConfigs = dataConfig.listAll(this, Activity31RankConfig.class);
        for (Activity31RankConfig config : rankConfigs) {
            TreeMap<Long, Activity31RankConfig> map = ACTIVITY_31_RANK_CONFIG.computeIfAbsent(config.getData(), x -> Maps.newTreeMap());
            map.put(config.getRank(), config);
        }
        Collection<Activity31TaskConfig> taskConfigs = dataConfig.listAll(this, Activity31TaskConfig.class);
        for (Activity31TaskConfig config : taskConfigs) {
            Collection<Integer> taskIdList = ACTIVITY_31_TASK_CONFIG_MAP.computeIfAbsent(config.getData(), k -> Sets.newHashSet());
            taskIdList.addAll(config.getTaskIdList());
        }
    }

    @Override
    protected void clean() {
        ACTIVITY_31_TARGET_GROUP_CONFIG.clear();
        ACTIVITY_31_CHARGE_REWARD_CONFIG.clear();
        ACTIVITY_31_RANK_CONFIG.clear();
        ACTIVITY_31_TASK_CONFIG_MAP.clear();
    }
}
