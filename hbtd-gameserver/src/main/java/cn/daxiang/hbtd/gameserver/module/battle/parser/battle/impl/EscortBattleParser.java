package cn.daxiang.hbtd.gameserver.module.battle.parser.battle.impl;

import cn.daxiang.framework.result.Result;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleMember;
import cn.daxiang.hbtd.gameserver.module.battle.model.BattleRoom;
import cn.daxiang.hbtd.gameserver.module.battle.parser.battle.AbstractBattleParser;
import cn.daxiang.hbtd.gameserver.module.escort.facade.impl.EscortFacadeImpl;
import cn.daxiang.protocol.game.BattleProtocol;
import cn.daxiang.protocol.game.TypeProtocol.ActorFieldType;
import cn.daxiang.shared.module.battle.BattleParameterKey;
import cn.daxiang.shared.module.battle.BattleType;
import cn.daxiang.shared.module.lineup.BattleLineupEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 押镖
 */
@Component
public class EscortBattleParser extends AbstractBattleParser {

    @Autowired
    private EscortFacadeImpl escortFacadeImpl;

    @Override
    public Result fight(long actorId, Map<BattleParameterKey, Object> parameterMap) {
        // 护镖人是否死亡
        boolean isGuardDeath = (boolean) parameterMap.get(BattleParameterKey.ESCORT_BATTLE_GUARD_DEATH);
        BattleMember rightMember;
        //护镖人玩家属性
        Map<Byte, Object> guardAttributes = (Map<Byte, Object>) parameterMap.get(BattleParameterKey.ESCORT_BATTLE_GUARD_ATTRIBUTE);
        BattleLineupEntity guardBattleLineupEntity = (BattleLineupEntity) parameterMap.get(BattleParameterKey.ESCORT_BATTLE_GUARD_LINEUP);
        if (!isGuardDeath && !guardAttributes.isEmpty() && !guardBattleLineupEntity.getLineupAttributeMap().isEmpty()) {
            long power = Long.parseLong(guardAttributes.get((byte) ActorFieldType.ACTOR_POWER_VALUE).toString());
            int officialLevel = (int) guardAttributes.get((byte) ActorFieldType.ACTOR_OFFICIAL_VALUE);
            long targetActorId = Long.parseLong(guardAttributes.get((byte) ActorFieldType.ACTOR_ID_VALUE).toString());
            rightMember = this.createBattleMember(targetActorId, power, officialLevel, BattleProtocol.BattleCamp.RIGHT_CAMP, guardBattleLineupEntity, getType());
        } else {
            Map<Byte, Object> attributes = (Map<Byte, Object>) parameterMap.get(BattleParameterKey.CROSS_BATTLE_ATTRIBUTE);
            BattleLineupEntity battleLineupEntity = (BattleLineupEntity) parameterMap.get(BattleParameterKey.CROSS_BATTLE_LINEUP);
            long power = Long.parseLong(attributes.get((byte) ActorFieldType.ACTOR_POWER_VALUE).toString());
            int officialLevel = (int) attributes.get((byte) ActorFieldType.ACTOR_OFFICIAL_VALUE);
            long targetActorId = Long.parseLong(attributes.get((byte) ActorFieldType.ACTOR_ID_VALUE).toString());
            rightMember = this.createBattleMember(targetActorId, power, officialLevel, BattleProtocol.BattleCamp.RIGHT_CAMP, battleLineupEntity, getType());
        }
        BattleMember leftMember = this.createBattleMember(actorId, BattleProtocol.BattleCamp.LEFT_CAMP, getType());
        BattleRoom battleRoom = this.createBattleRoom(leftMember, rightMember, parameterMap);
        battleRoomFacade.createBattleRoom(battleRoom);
        return Result.valueOf();
    }

    @Override
    public void battleEnd(long actorId, BattleRoom battleRoom) {
        escortFacadeImpl.robberyBattleEnd(actorId, battleRoom);
    }

    @Override
    protected BattleType getType() {
        return BattleType.ESCORT;
    }
}