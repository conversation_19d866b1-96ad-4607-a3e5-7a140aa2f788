package cn.daxiang.hbtd.gameserver.module.hegemony.model;

import cn.daxiang.framework.utils.rank.AbstractRank;
import cn.daxiang.framework.utils.rank.LadderRank;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * <AUTHOR>
 * @date 2022/10/15
 */
public class HegemonyRank extends LadderRank<Long> implements RowMapper<HegemonyRank> {
    /**
     * 积分
     */
    private int score;
    /**
     * 段位
     */
    private int dan;
    /**
     * 上一次排名更新时间
     */
    private long lastUpdateTime;

    public static HegemonyRank valueOf(long actorId, int score, int dan, long lastUpdateTime) {
        HegemonyRank rank = new HegemonyRank();
        rank.key = actorId;
        rank.score = score;
        rank.dan = dan;
        rank.lastUpdateTime = lastUpdateTime;
        return rank;
    }

    public int getDan() {
        return dan;
    }

    public void setDan(int dan) {
        this.dan = dan;
    }

    public long getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(long lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public int getScore() {
        return score;
    }

    public void setScore(int score) {
        this.score = score;
    }

    public void refresh(int score, int dan, long power) {
        this.score = score;
        this.dan = dan;
        this.lastUpdateTime = power;
    }

    @Override
    public boolean outstrip(AbstractRank<Long> rank) {
        HegemonyRank duelRank = (HegemonyRank) rank;
        if (this.score > duelRank.getScore()) {
            return true;
        } else if (this.score < duelRank.getScore()) {
            return false;
        } else {
            if (this.lastUpdateTime < duelRank.getLastUpdateTime()) {
                return true;
            } else if (this.lastUpdateTime > duelRank.getLastUpdateTime()) {
                return false;
            } else {
                return this.key < rank.getKey();
            }
        }
    }

    @Override
    public void achieveRank(LadderRank<Long> rank) {
        HegemonyRank hegemonyRank = (HegemonyRank) rank;
        this.score = hegemonyRank.getScore();
        this.dan = hegemonyRank.getDan();
        this.lastUpdateTime = hegemonyRank.getLastUpdateTime();
    }

    @Override
    public AbstractRank<Long> copy() {
        HegemonyRank rank = HegemonyRank.valueOf(this.key, this.score, this.dan, this.lastUpdateTime);
        this.copyRank(rank);
        return rank;
    }

    @Override
    public HegemonyRank mapRow(ResultSet rs, int rowNum) throws SQLException {
        HegemonyRank rank = new HegemonyRank();
        rank.key = rs.getLong("actorId");
        rank.score = rs.getInt("score");
        rank.dan = rs.getInt("dan");
        rank.lastUpdateTime = rs.getLong("lastUpdateTime");
        return rank;
    }
}
