package cn.daxiang.hbtd.gameserver.core.dataconfig.model;

import cn.daxiang.framework.dataconfig.ModelAdapter;
import cn.daxiang.framework.dataconfig.annotation.DataFile;
import cn.daxiang.framework.dataconfig.annotation.FieldIgnore;
import cn.daxiang.framework.identity.IdentiyKey;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;

import java.util.Collection;

/**
 * 八门突破消耗组配置表
 *
 * @Author: Gary
 * @Date 2022-10-22 10:59
 * @Description:
 */
@DataFile(fileName = "eight_gate_cost_group_config")
public class EightGateCostGroupConfig implements ModelAdapter {
    /**
     * 唯一组Id
     */
    private int groupId;
    /**
     * 碎片列表
     * [碎片配置Id,碎片配置Id]
     */
    private String items;

    @FieldIgnore
    private Collection<Integer> itemList = Lists.newArrayList();

    @Override
    public void initialize() {
        itemList.addAll(JSON.parseArray(items, Integer.class));
    }

    @Override
    public IdentiyKey findKey() {
        return IdentiyKey.build(groupId);
    }

    public int getGroupId() {
        return groupId;
    }

    public Collection<Integer> getItemList() {
        return itemList;
    }
}
