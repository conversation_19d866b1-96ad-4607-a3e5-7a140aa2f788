package cn.daxiang.hbtd.gameserver.module.heroManual.helper;

import cn.daxiang.framework.protocal.DataPacket;
import cn.daxiang.hbtd.gameserver.core.database.table.HeroManual;
import cn.daxiang.hbtd.gameserver.core.netty.channel.PlayerChannel;
import cn.daxiang.protocol.game.HeromanualProtocol;
import cn.daxiang.protocol.game.ModuleProtocol;

public class HeroManualPushHelper {

    public static void pushHeroManual(long actorId, int heroManualValue, HeroManual heroManual) {
        HeromanualProtocol.HeroManualResponse response = HeroManualHelper.buildHeroManualResponse(heroManualValue, heroManual);
        DataPacket packet = DataPacket.valueOf(ModuleProtocol.Module.HEROMANUAL_VALUE, HeromanualProtocol.HeromanualCmd.PUSH_HERO_MANUAL_VALUE, response);
        PlayerChannel.push(actorId, packet);
    }

}
