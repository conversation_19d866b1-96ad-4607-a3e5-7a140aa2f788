package cn.daxiang.hbtd.gameserver.module.invite.helper;

import cn.daxiang.framework.protocal.DataPacket;
import cn.daxiang.framework.result.Result;
import cn.daxiang.hbtd.gameserver.core.database.table.Invite;
import cn.daxiang.hbtd.gameserver.core.netty.channel.PlayerChannel;
import cn.daxiang.hbtd.gameserver.module.PbBuilder;
import cn.daxiang.protocol.game.CommonProtocol.RewardResult;
import cn.daxiang.protocol.game.CommonProtocol.RewardResultResponse;
import cn.daxiang.protocol.game.InviteProtocol.InviteCmd;
import cn.daxiang.protocol.game.InviteProtocol.InviteInfoResponse;
import cn.daxiang.protocol.game.ModuleProtocol.Module;

public class InvitePushHelper {
    /**
     * 推送邀请码信息
     *
     * @param actorId
     */
    public static void pushInviteInfo(long actorId, Invite invite) {
        InviteInfoResponse response = InviteHelper.buildInviteInfoResponse(invite);
        DataPacket packet = DataPacket.valueOf(Module.INVITE_VALUE, InviteCmd.PUSH_INVITE_INFO_VALUE, response);
        PlayerChannel.push(actorId, packet);
    }

    /**
     * 推送绑定好友结果
     *
     * @param actorId
     */
    public static void pushInviteInfoResult(long actorId, Result result) {
        DataPacket packet = DataPacket.valueOf(Module.INVITE_VALUE, InviteCmd.PUSH_BIND_MASTER_REWARD_VALUE, result.statusCode);
        PlayerChannel.push(actorId, packet);
    }

    /**
     * 推送绑定好友结果
     *
     * @param actorId
     */
    public static void pushInviteInfoResult(long actorId, RewardResult rewardResult) {
        RewardResultResponse response = PbBuilder.buildRewardResultResponse(rewardResult);
        DataPacket packet = DataPacket.valueOf(Module.INVITE_VALUE, InviteCmd.PUSH_BIND_MASTER_REWARD_VALUE, response);
        PlayerChannel.push(actorId, packet);
    }

}
