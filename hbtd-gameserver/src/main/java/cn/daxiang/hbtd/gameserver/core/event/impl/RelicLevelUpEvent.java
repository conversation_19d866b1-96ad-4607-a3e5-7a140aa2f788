package cn.daxiang.hbtd.gameserver.core.event.impl;

import cn.daxiang.framework.event.ActorEvent;
import cn.daxiang.shared.event.EventKey;

/**
 * @author: <PERSON>
 * @date: 2023/2/6 10:46
 * @Description:
 */
public class RelicLevelUpEvent extends ActorEvent {
    /**
     * 品质组
     */
    public int group;
    /**
     * 神器等级
     */
    public int relicLevel;

    public RelicLevelUpEvent(long actorId, int group, int relicLevel) {
        super(EventKey.RELIC_LEVEL_UP_EVENT, actorId);
        this.relicLevel = relicLevel;
        this.group = group;
    }
}
