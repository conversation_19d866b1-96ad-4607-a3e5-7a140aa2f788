package cn.daxiang.hbtd.gameserver.module.extension.helper;

import cn.daxiang.framework.protocal.DataPacket;
import cn.daxiang.hbtd.gameserver.core.database.table.DailyGift;
import cn.daxiang.hbtd.gameserver.core.database.table.DrawEnergy;
import cn.daxiang.hbtd.gameserver.core.database.table.FirstRecharge;
import cn.daxiang.hbtd.gameserver.core.database.table.Fund;
import cn.daxiang.hbtd.gameserver.core.database.table.HeroRecommend;
import cn.daxiang.hbtd.gameserver.core.database.table.PlayerCentre;
import cn.daxiang.hbtd.gameserver.core.database.table.RankTargetActor;
import cn.daxiang.hbtd.gameserver.core.database.table.SignIn;
import cn.daxiang.hbtd.gameserver.core.database.table.ThousandGacha;
import cn.daxiang.hbtd.gameserver.core.database.table.VipCard;
import cn.daxiang.hbtd.gameserver.core.database.table.VipGiftBag;
import cn.daxiang.hbtd.gameserver.core.database.table.WeekGift;
import cn.daxiang.hbtd.gameserver.core.netty.channel.PlayerChannel;
import cn.daxiang.hbtd.gameserver.module.extension.model.ChargeReturnInfo;
import cn.daxiang.hbtd.gameserver.module.extension.model.LimitedTimeChargeEntity;
import cn.daxiang.hbtd.gameserver.module.extension.model.SignInEntity;
import cn.daxiang.protocol.game.CommonProtocol;
import cn.daxiang.protocol.game.CommonProtocol.IntListPacket;
import cn.daxiang.protocol.game.ExtensionProtocol;
import cn.daxiang.protocol.game.ExtensionProtocol.ExtensionCmd;
import cn.daxiang.protocol.game.ExtensionProtocol.HeroLineupRecommendResponse;
import cn.daxiang.protocol.game.ExtensionProtocol.SignInResponse;
import cn.daxiang.protocol.game.ExtensionProtocol.ThousandGachaResponse;
import cn.daxiang.protocol.game.ExtensionProtocol.VipCardResponse;
import cn.daxiang.protocol.game.ExtensionProtocol.VipGiftBagResponse;
import cn.daxiang.protocol.game.ModuleProtocol.Module;

import java.util.Collection;
import java.util.Map;

public class ExtensionPushHelper {
    /**
     * 推送每日体力领取情况
     *
     * @param actorId
     * @param drawEnergy
     */
    public static void pushDrawEnergy(Long actorId, DrawEnergy drawEnergy) {
        IntListPacket response = IntListPacket.newBuilder().addAllList(drawEnergy.getReceiveList()).build();
        DataPacket packet = DataPacket.valueOf(Module.EXTENSION_VALUE, ExtensionCmd.PUSH_DRAW_ENERGY_INFO_VALUE, response);
        PlayerChannel.push(actorId, packet);
    }

    /**
     * 推送签到
     *
     * @param signIn
     */
    public static void pushSignIn(Long actorId, SignIn signIn) {
        SignInEntity entity = signIn.getSignInEntity();
        SignInResponse response = SignInResponse.newBuilder().setDays(entity.getDays()).setStage(entity.getStage()).setLastSignInTime(entity.getLastSignInTime())
            .addAllReceiveReward(entity.getRecieveList()).build();
        DataPacket packet = DataPacket.valueOf(Module.EXTENSION_VALUE, ExtensionCmd.PUSH_SIGN_IN_VALUE, response);
        PlayerChannel.push(actorId, packet);
    }

    /**
     * 推送VipCard信息
     *
     * @param actorId
     * @param vipCard
     */
    public static void pushVipCard(Long actorId, VipCard vipCard) {
        VipCardResponse response = ExtensionHelper.buildVipCardResponse(vipCard);
        DataPacket packet = DataPacket.valueOf(Module.EXTENSION_VALUE, ExtensionCmd.PUSH_VIP_CARD_INFO_VALUE, response);
        PlayerChannel.push(actorId, packet);
    }

    /**
     * 推送VIP礼包
     *
     * @param vipGiftBag
     */
    public static void pushVipGiftBag(long actorId, VipGiftBag vipGiftBag) {
        ExtensionProtocol.VipGiftBagResponse.Builder builder = ExtensionProtocol.VipGiftBagResponse.newBuilder();
        builder.addAllReceives(vipGiftBag.getGiftBagList());
        VipGiftBagResponse response = builder.build();
        DataPacket packet = DataPacket.valueOf(Module.EXTENSION_VALUE, ExtensionCmd.PUSH_VIP_GIFT_BAG_INFO_VALUE, response);
        PlayerChannel.push(actorId, packet);
    }

    /**
     * 推送多档首充
     *
     * @param firstRecharge
     */
    public static void pushFirstRecharge(long actorId, FirstRecharge firstRecharge) {
        ExtensionProtocol.FirstRechargeResponse response = ExtensionHelper.buildFirstRechargeResponse(firstRecharge);
        DataPacket packet = DataPacket.valueOf(Module.EXTENSION_VALUE, ExtensionCmd.PUSH_FIRST_RECHARGE_INFO_VALUE, response);
        PlayerChannel.push(actorId, packet);
    }

    /**
     * 推送限时充值信息
     *
     * @param actorId
     * @param list
     */
    public static void pushLimitedTimeCharge(long actorId, Collection<LimitedTimeChargeEntity> list) {
        ExtensionProtocol.LimitedTimeChargeResponse response = ExtensionHelper.buildLimitedTimeChargeResponse(list);
        DataPacket packet = DataPacket.valueOf(Module.EXTENSION_VALUE, ExtensionCmd.PUSH_LIMITE_TIME_CHARGE_INFO_VALUE, response);
        PlayerChannel.push(actorId, packet);
    }

    /**
     * 推送基金信息（约定不推全量）
     *
     * @param actorId
     * @param fund
     */
    public static void pushFund(long actorId, int type, Fund fund) {
        ExtensionProtocol.FundResponse response = ExtensionHelper.buildFundResponse(type, fund);
        DataPacket packet = DataPacket.valueOf(Module.EXTENSION_VALUE, ExtensionCmd.PUSH_FUND_INFO_VALUE, response);
        PlayerChannel.push(actorId, packet);
    }

    /**
     * 推送基金购买次数信息（全民福利次数信息）
     *
     * @param actorId
     * @param buyTimesMap
     */
    public static void pushFundBuyTimes(long actorId, Map<Integer, Integer> buyTimesMap) {
        CommonProtocol.IntMap response = CommonProtocol.IntMap.newBuilder().putAllValue(buyTimesMap).build();
        DataPacket packet = DataPacket.valueOf(Module.EXTENSION_VALUE, ExtensionCmd.PUSH_FUND_BUY_TIMES_INFO_VALUE, response);
        PlayerChannel.push(actorId, packet);
    }

    /**
     * 推送体力特选信息
     *
     * @param actorId
     * @param response
     */
    public static void pushEnergyPrivilegeInfo(long actorId, ExtensionProtocol.EnergyPrivilegeResponse response) {
        DataPacket packet = DataPacket.valueOf(Module.EXTENSION_VALUE, ExtensionCmd.PUSH_ENERGY_PRIVILEGE_INFO_VALUE, response);
        PlayerChannel.push(actorId, packet);
    }

    /**
     * 推送玩家中心信息
     *
     * @param playerCentre
     */
    public static void pushPlayerCentre(PlayerCentre playerCentre) {
        ExtensionProtocol.PlayerCentreResponse response =
            ExtensionProtocol.PlayerCentreResponse.newBuilder().setAntiFraudReward(playerCentre.isAntiFraudReward()).setFeedbackTimes(playerCentre.getFeedbackTimes()).build();
        DataPacket packet = DataPacket.valueOf(Module.EXTENSION_VALUE, ExtensionCmd.PUSH_PLAYER_CENTRE_INFO_VALUE, response);
        PlayerChannel.push(playerCentre.getActorId(), packet);
    }

    public static void pushDailyGiftInfo(DailyGift dailyGift) {
        ExtensionProtocol.DailyGiftInfoResponse response = ExtensionHelper.buildDailyGiftInfoResponse(dailyGift);
        DataPacket packet = DataPacket.valueOf(Module.EXTENSION_VALUE, ExtensionCmd.PUSH_DAILY_GIFT_INFO_VALUE, response);
        PlayerChannel.push(dailyGift.getActorId(), packet);
    }

    public static void pushWeekGift(Long actorId, int week, WeekGift weekGift) {
        ExtensionProtocol.WeekGiftInfoResponse response = ExtensionHelper.buildWeekGiftInfoResponse(week, weekGift);
        DataPacket packet = DataPacket.valueOf(Module.EXTENSION_VALUE, ExtensionCmd.PUSH_WEEK_GIFT_INFO_VALUE, response);
        PlayerChannel.push(actorId, packet);
    }

    public static void pushRankTarget(long actorId, RankTargetActor rankTargetActor) {
        ExtensionProtocol.RankTargetResponse response = ExtensionHelper.buildRankTargetResponse(rankTargetActor);
        DataPacket packet = DataPacket.valueOf(Module.EXTENSION_VALUE, ExtensionCmd.PUSH_RANK_TARGET_INFO_VALUE, response);
        PlayerChannel.push(actorId, packet);
    }

    public static void pushDirectPurchaseReward(long actorId, CommonProtocol.RewardResult rewardResult) {
        CommonProtocol.RewardResultResponse response = CommonProtocol.RewardResultResponse.newBuilder().setRewardResult(rewardResult).build();
        DataPacket packet = DataPacket.valueOf(Module.EXTENSION_VALUE, ExtensionCmd.PUSH_DIRECT_PURCHASE_REWARD_VALUE, response);
        PlayerChannel.push(actorId, packet);
    }

    public static void pushChargeReturnResponse(long actorId, ChargeReturnInfo chargeReturn) {
        ExtensionProtocol.ChargeReturnResponse response = ExtensionHelper.buildChargeReturnResponse(chargeReturn);
        DataPacket packet = DataPacket.valueOf(Module.EXTENSION_VALUE, ExtensionCmd.PUSH_CHARGE_RETURN_INFO_VALUE, response);
        PlayerChannel.push(actorId, packet);
    }

    public static void pushThousandGachaResponse(long actorId, ThousandGacha thousandGacha){
        ThousandGachaResponse response = ExtensionHelper.buildThousandGachaResponse(thousandGacha);
        DataPacket packet = DataPacket.valueOf(Module.EXTENSION_VALUE, ExtensionCmd.PUSH_THOUSAND_GACHA_INFO_VALUE, response);
        PlayerChannel.push(actorId, packet);
    }

    public static void pushHeroLineupRecommendResponse(long actorId, HeroRecommend heroLineupRecommend){
        HeroLineupRecommendResponse response = ExtensionHelper.buildHeroLineupRecommendResponse(heroLineupRecommend);
        DataPacket packet = DataPacket.valueOf(Module.EXTENSION_VALUE, ExtensionCmd.PUSH_HERO_LINEUP_RECOMMEND_INFO_VALUE, response);
        PlayerChannel.push(actorId, packet);
    }
}