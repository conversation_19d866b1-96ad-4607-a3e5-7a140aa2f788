package cn.daxiang.hbtd.gameserver.module.activity.model.entity.record;

import cn.daxiang.hbtd.gameserver.module.activity.model.entity.record.entity.Activity68GiftEntity;
import com.google.common.collect.Maps;

import java.util.Map;

/**
 * 心愿招募-招募
 * <AUTHOR>
 * @date 2024/5/17
 */
public class ActivityRecord68 {

    /**
     * 选择的心愿大奖ID
     */
    private int selectId;

    /**
     * 总抽奖次数
     */
    private int totalGachaTimes;
    /**
     * 心愿大奖抽奖次数
     * 如若概率/必中 抽中心愿大奖，则清0
     */
    private int bigRewardGachaTimes;

    /**
     * 今日使用元宝已招募次数
     */
    private int diamondGachaTimes;

    /**
     * 已使用免费招募次数
     */
    private boolean usedFreeTime;

    /**
     * 每日重置时间
     * 重置免费招募、元宝招募次数、心愿助力礼包每日限购次数
     */
    private long lastResetTime;

    /**
     * 心愿礼物
     */
    private Activity68GiftEntity giftEntity = new Activity68GiftEntity();

    /**
     * 心愿助力 今日购买次数
     * key:Activity68HelpRewardConfig.id value:购买次数
     */
    private Map<Integer, Integer> helpRewardDayBuyTimes = Maps.newHashMap();

    /**
     * 心愿助力
     * key:Activity68HelpRewardConfig.id   value: 已购次数
     */
    private Map<Integer, Integer> helpRewardBuyTimes = Maps.newHashMap();

    /**
     * 心愿助力
     * key:Activity68HelpRewardConfig.id   value: 已领取次数
     */
    private Map<Integer, Integer> helpRewardReceiveTimes = Maps.newHashMap();

    public static ActivityRecord68 valueOf(){
        ActivityRecord68 activityRecord68 = new ActivityRecord68();
        activityRecord68.lastResetTime = System.currentTimeMillis();
        return activityRecord68;
    }


    public int getSelectId() {
        return selectId;
    }

    public void setSelectId(int selectId) {
        this.selectId = selectId;
    }

    public int getBigRewardGachaTimes() {
        return bigRewardGachaTimes;
    }

    public void setBigRewardGachaTimes(int bigRewardGachaTimes) {
        this.bigRewardGachaTimes = bigRewardGachaTimes;
    }

    public int getDiamondGachaTimes() {
        return diamondGachaTimes;
    }

    public void setDiamondGachaTimes(int diamondGachaTimes) {
        this.diamondGachaTimes = diamondGachaTimes;
    }

    public boolean isUsedFreeTime() {
        return usedFreeTime;
    }

    public void setUsedFreeTime(boolean usedFreeTime) {
        this.usedFreeTime = usedFreeTime;
    }

    public long getLastResetTime() {
        return lastResetTime;
    }

    public void setLastResetTime(long lastResetTime) {
        this.lastResetTime = lastResetTime;
    }

    public Activity68GiftEntity getGiftEntity() {
        return giftEntity;
    }

    public void setGiftEntity(Activity68GiftEntity giftEntity) {
        this.giftEntity = giftEntity;
    }

    public Map<Integer, Integer> getHelpRewardBuyTimes() {
        return helpRewardBuyTimes;
    }

    public void setHelpRewardBuyTimes(Map<Integer, Integer> helpRewardBuyTimes) {
        this.helpRewardBuyTimes = helpRewardBuyTimes;
    }

    public Map<Integer, Integer> getHelpRewardReceiveTimes() {
        return helpRewardReceiveTimes;
    }

    public void setHelpRewardReceiveTimes(Map<Integer, Integer> helpRewardReceiveTimes) {
        this.helpRewardReceiveTimes = helpRewardReceiveTimes;
    }

    public Map<Integer, Integer> getHelpRewardDayBuyTimes() {
        return helpRewardDayBuyTimes;
    }

    public void setHelpRewardDayBuyTimes(Map<Integer, Integer> helpRewardDayBuyTimes) {
        this.helpRewardDayBuyTimes = helpRewardDayBuyTimes;
    }

    public int getTotalGachaTimes() {
        return totalGachaTimes;
    }

    public void setTotalGachaTimes(int totalGachaTimes) {
        this.totalGachaTimes = totalGachaTimes;
    }

    public void addDiamondGachaTimes(int times) {
        diamondGachaTimes += times;
    }

    public void addBigRewardGachaTimes(int times) {
        bigRewardGachaTimes += times;
        totalGachaTimes += times;
    }

    public boolean receiveNormalGiftReward(int receiveDay) {
        if (giftEntity.getNormalReceiveList().contains(receiveDay)) {
            return false;
        }
        giftEntity.receiveNormalReward(receiveDay);
        return true;
    }

    public boolean receivetSupremeGiftReward(int receiveDay) {
        if (giftEntity.getSupremeReceiveList().contains(receiveDay)) {
            return false;
        }
        giftEntity.receiveSupremeReward(receiveDay);
        return true;
    }

    public void activeGiftReward() {
        giftEntity.setActive(true);
    }

    public void mergeHelpRewardBuyTimes(int id) {
        helpRewardBuyTimes.merge(id, 1, Integer::sum);
    }

    public void mergeHelpRewardDayBuyTimes(int id) {
        helpRewardDayBuyTimes.merge(id, 1, Integer::sum);
    }

    public void addHelpRewardReceiveTimes(int id) {
        helpRewardReceiveTimes.merge(id, 1, Integer::sum);
    }



    public void resetTime() {
        usedFreeTime = false;
        diamondGachaTimes = 0;
        helpRewardDayBuyTimes.clear();
        lastResetTime = System.currentTimeMillis();
    }

    /**
     * 抽中心愿，次数清0
     */
    public void clearBigRewardGachaTimes() {
        bigRewardGachaTimes = 0;
    }

}
