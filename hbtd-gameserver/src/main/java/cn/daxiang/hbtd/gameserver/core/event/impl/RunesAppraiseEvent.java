package cn.daxiang.hbtd.gameserver.core.event.impl;

import cn.daxiang.framework.event.ActorEvent;
import cn.daxiang.shared.event.EventKey;

import java.util.Map;

/**
 * 兵符鉴定事件
 *
 * <AUTHOR>
 * @date 2023/2/17
 */
public class RunesAppraiseEvent extends ActorEvent {
    /**
     * 鉴定兵符Map{key:configId,value:num}
     */
    public Map<Integer, Integer> appraiseMap;

    public RunesAppraiseEvent(long actorId, Map<Integer, Integer> appraiseMap) {
        super(EventKey.RUNES_APPRAISE_EVENT, actorId);
        this.appraiseMap = appraiseMap;
    }
}
