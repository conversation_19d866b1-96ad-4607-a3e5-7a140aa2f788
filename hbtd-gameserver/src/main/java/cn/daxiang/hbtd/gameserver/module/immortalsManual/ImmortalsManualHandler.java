package cn.daxiang.hbtd.gameserver.module.immortalsManual;

import cn.daxiang.framework.dispatch.DispatchType;
import cn.daxiang.framework.protocal.DataPacket;
import cn.daxiang.framework.result.Result;
import cn.daxiang.framework.result.TResult;
import cn.daxiang.framework.router.annotation.Cmd;
import cn.daxiang.hbtd.gameserver.core.database.table.ImmortalsManual;
import cn.daxiang.hbtd.gameserver.core.router.GatewayRouterHandlerImpl;
import cn.daxiang.hbtd.gameserver.module.immortalsManual.facade.ImmortalsManualFacade;
import cn.daxiang.hbtd.gameserver.module.immortalsManual.helper.ImmortalsManualHelper;
import cn.daxiang.protocol.game.CommonProtocol;
import cn.daxiang.protocol.game.CommonProtocol.LongPacket;
import cn.daxiang.protocol.game.ImmortalsmanualProtocol;
import cn.daxiang.protocol.game.ModuleProtocol;
import io.netty.channel.Channel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/7/4
 */
@Component
public class ImmortalsManualHandler extends GatewayRouterHandlerImpl {

    @Autowired
    private ImmortalsManualFacade immortalsManualFacade;

    @Override
    public int getModule() {
        return ModuleProtocol.Module.IMMORTALSMANUAL_VALUE;
    }

    @Cmd(Id = ImmortalsmanualProtocol.ImmortalsManualCmd.IMMORTALS_MANUAL_LIST_VALUE, dispatchType = DispatchType.ACTOR)
    public void getImmortalsManualList(Channel channel, Long actorId, DataPacket packet) {
        TResult<ImmortalsManual> result = immortalsManualFacade.getImmortalsManual(actorId);
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        int manualValue = result.item.getManualValue();
        ImmortalsmanualProtocol.ImmortalsManualResponse response = ImmortalsManualHelper.buildImmortalsManualResponse(result.item, manualValue);
        channelWrite(channel, packet, response);
    }

    @Cmd(Id = ImmortalsmanualProtocol.ImmortalsManualCmd.IMMORTALS_SKIN_MANUAL_ACTIVATION_VALUE, dispatchType = DispatchType.ACTOR)
    public void getImmortalsSkinManualActivation(Channel channel, Long actorId, DataPacket packet) {
        CommonProtocol.IntPacket request = packet.getValue(CommonProtocol.IntPacket.parser());
        Result result = immortalsManualFacade.immortalsSkinManualActivation(actorId, request.getValue());
        channelWrite(channel, packet, result.statusCode);
    }

    @Cmd(Id = ImmortalsmanualProtocol.ImmortalsManualCmd.IMMORTALS_MANUAL_RANK_VALUE, dispatchType = DispatchType.ACTOR)
    public void getImmortalsManualRank(Channel channel, Long actorId, DataPacket packet) {
        CommonProtocol.IntPacket request = packet.getValue(CommonProtocol.IntPacket.parser());
        TResult<ImmortalsmanualProtocol.ImmortalsManualRankResponse> result = immortalsManualFacade.getImmortalsRankResponse(actorId, request.getValue());
        if (result.isFail()) {
            channelWrite(channel, packet, result.statusCode);
            return;
        }
        channelWrite(channel, packet, result.item);
    }

    @Cmd(Id = ImmortalsmanualProtocol.ImmortalsManualCmd.IMMORTALS_MANUAL_UPGRADE_VALUE, dispatchType = DispatchType.ACTOR)
    public void immortalsManualUpgrade(Channel channel, Long actorId, DataPacket packet) {
        Result result = immortalsManualFacade.immortalsManualUpgrade(actorId);
        channelWrite(channel, packet, result.statusCode);
    }

    @Cmd(Id = ImmortalsmanualProtocol.ImmortalsManualCmd.IMMORTALS_COLOR_MANUAL_ACTIVATE_VALUE, dispatchType = DispatchType.ACTOR)
    public void colorManualActivate(Channel channel, Long actorId, DataPacket packet) {
        LongPacket request = packet.getValue(LongPacket.parser());
        Result result = immortalsManualFacade.colorManualActivate(actorId, request.getValue());
        channelWrite(channel, packet, result.statusCode);
    }
}
