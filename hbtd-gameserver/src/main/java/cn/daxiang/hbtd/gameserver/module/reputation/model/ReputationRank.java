package cn.daxiang.hbtd.gameserver.module.reputation.model;

import cn.daxiang.framework.utils.rank.AbstractRank;
import cn.daxiang.framework.utils.rank.LadderRank;

/**
 * @author: <PERSON>
 * @date: 2023/4/21 15:40
 * @Description:
 */
public class ReputationRank extends LadderRank<Long> {
    /**
     * 等级
     */
    private int level;
    /**
     * 历史名气值
     */
    private long reputationVale;
    /**
     * 达成时间
     */
    private long achieveTime;

    public static ReputationRank valueOf(long actorId, int level, long reputationVale, long lastUpgradeTime) {
        ReputationRank model = new ReputationRank();
        model.key = actorId;
        model.level = level;
        model.achieveTime = lastUpgradeTime;
        model.reputationVale = reputationVale;
        return model;
    }

    @Override
    public AbstractRank<Long> copy() {
        ReputationRank reputationRank = ReputationRank.valueOf(this.key, this.level, this.reputationVale, this.achieveTime);
        this.copyRank(reputationRank);
        return reputationRank;
    }

    @Override
    public boolean outstrip(AbstractRank<Long> rank) {
        ReputationRank reputationRank = (ReputationRank) rank;
        if (this.level > reputationRank.getLevel()) {
            return true;
        } else if (this.level < reputationRank.getLevel()) {
            return false;
        } else {
            if (this.reputationVale > reputationRank.getReputationVale()) {
                return true;
            } else if (this.reputationVale < reputationRank.getReputationVale()) {
                return false;
            } else {
                return this.achieveTime < reputationRank.getAchieveTime();
            }
        }
    }

    @Override
    public void achieveRank(LadderRank<Long> rank) {
        ReputationRank ReputationRank = (ReputationRank) rank;
        this.level = ReputationRank.level;
        this.achieveTime = ReputationRank.achieveTime;
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public long getReputationVale() {
        return reputationVale;
    }

    public void setReputationVale(long reputationVale) {
        this.reputationVale = reputationVale;
    }

    public long getAchieveTime() {
        return achieveTime;
    }

    public void setAchieveTime(long achieveTime) {
        this.achieveTime = achieveTime;
    }

    public void update(int level, long reputationVale, long lastUpgradeTime) {
        this.level = level;
        this.reputationVale = reputationVale;
        this.achieveTime = lastUpgradeTime;
    }
}
