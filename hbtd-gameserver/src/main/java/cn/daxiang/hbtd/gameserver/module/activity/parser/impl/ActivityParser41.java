package cn.daxiang.hbtd.gameserver.module.activity.parser.impl;

import cn.daxiang.framework.result.TResult;
import cn.daxiang.hbtd.gameserver.core.dataconfig.model.ActivityOpenConfig;
import cn.daxiang.hbtd.gameserver.core.oss.type.OperationType;
import cn.daxiang.hbtd.gameserver.module.activity.parser.AbstractActivityParser9;
import cn.daxiang.hbtd.gameserver.module.activity.type.ActivityType;
import cn.daxiang.hbtd.gameserver.module.mail.type.MailTemplateType;
import cn.daxiang.protocol.game.CommonProtocol;
import org.springframework.stereotype.Component;

import java.util.Collection;

/**
 * 活动-41-累计充值
 *
 * <AUTHOR>
 */
@Component
public class ActivityParser41 extends AbstractActivityParser9 {

    @Override
    public TResult<CommonProtocol.RewardResult> receiveReward(long actorId, int activityId, int id, byte[] value) {
        return this.receiveReward(actorId, activityId, id, value, OperationType.ACTIVITY_TYPE_41);
    }

    @Override
    protected ActivityType getType() {
        return ActivityType.ACTIVITY_TYPE_41;
    }

    @Override
    protected void onActivityEnd(Collection<ActivityOpenConfig> endActivityIds) {
        this.onActivityEnd(endActivityIds, MailTemplateType.ACTIVITY_41_RECHARGE_UNCLAIMED_REWARD);
    }

}
