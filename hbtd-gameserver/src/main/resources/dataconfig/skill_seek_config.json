[{"currentRound": "false", "seekType": 1, "deadStatus": "false", "targetType": 1, "targetCount": 1, "seekId": 1}, {"currentRound": "false", "seekType": 1, "deadStatus": "false", "targetType": 2, "targetCount": 1, "seekId": 2}, {"currentRound": "false", "seekType": 2, "deadStatus": "false", "targetType": 2, "targetCount": 1, "seekId": 3}, {"currentRound": "false", "seekType": 2, "deadStatus": "false", "targetType": 2, "targetCount": 2, "seekId": 4}, {"currentRound": "false", "seekType": 2, "deadStatus": "false", "targetType": 2, "targetCount": 3, "seekId": 5}, {"currentRound": "false", "seekType": 2, "deadStatus": "false", "targetType": 2, "targetCount": 4, "seekId": 6}, {"currentRound": "false", "seekType": 2, "deadStatus": "false", "targetType": 2, "targetCount": 5, "seekId": 7}, {"currentRound": "false", "seekType": 2, "deadStatus": "false", "targetType": 2, "targetCount": 6, "seekId": 8}, {"currentRound": "false", "seekType": 2, "deadStatus": "false", "targetType": 2, "targetCount": 6, "seekId": 9}, {"currentRound": "false", "seekType": 3, "deadStatus": "false", "targetType": 2, "targetCount": 1, "seekId": 10}, {"currentRound": "false", "seekType": 3, "deadStatus": "false", "targetType": 2, "targetCount": 2, "seekId": 11}, {"currentRound": "false", "seekType": 3, "deadStatus": "false", "targetType": 2, "targetCount": 3, "seekId": 12}, {"currentRound": "false", "seekType": 3, "deadStatus": "false", "targetType": 2, "targetCount": 3, "seekId": 13}, {"currentRound": "false", "seekType": 4, "deadStatus": "false", "targetType": 2, "targetCount": 1, "seekId": 14}, {"currentRound": "false", "seekType": 4, "deadStatus": "false", "targetType": 2, "targetCount": 2, "seekId": 15}, {"currentRound": "false", "seekType": 4, "deadStatus": "false", "targetType": 2, "targetCount": 3, "seekId": 16}, {"currentRound": "false", "seekType": 4, "deadStatus": "false", "targetType": 2, "targetCount": 3, "seekId": 17}, {"currentRound": "false", "seekType": 5, "deadStatus": "false", "targetType": 2, "targetCount": 1, "seekId": 18}, {"currentRound": "false", "seekType": 5, "deadStatus": "false", "targetType": 2, "targetCount": 2, "seekId": 19}, {"currentRound": "false", "seekType": 5, "deadStatus": "false", "targetType": 2, "targetCount": 3, "seekId": 20}, {"currentRound": "false", "seekType": 6, "deadStatus": "false", "targetType": 2, "targetCount": 1, "seekId": 21}, {"currentRound": "false", "seekType": 6, "deadStatus": "false", "targetType": 2, "targetCount": 2, "seekId": 22}, {"currentRound": "false", "seekType": 6, "deadStatus": "false", "targetType": 2, "targetCount": 3, "seekId": 23}, {"currentRound": "false", "seekType": 7, "deadStatus": "false", "targetType": 2, "targetCount": 1, "seekId": 24}, {"currentRound": "false", "seekType": 7, "deadStatus": "false", "targetType": 2, "targetCount": 2, "seekId": 25}, {"currentRound": "false", "seekType": 7, "deadStatus": "false", "targetType": 2, "targetCount": 3, "seekId": 26}, {"currentRound": "false", "seekType": 8, "deadStatus": "false", "targetType": 2, "targetCount": 1, "seekId": 27}, {"currentRound": "false", "seekType": 8, "deadStatus": "false", "targetType": 2, "targetCount": 2, "seekId": 28}, {"currentRound": "false", "seekType": 8, "deadStatus": "false", "targetType": 2, "targetCount": 3, "seekId": 29}, {"currentRound": "false", "seekType": 9, "deadStatus": "false", "targetType": 2, "targetCount": 1, "seekId": 30}, {"currentRound": "false", "seekType": 9, "deadStatus": "false", "targetType": 2, "targetCount": 2, "seekId": 31}, {"currentRound": "false", "seekType": 9, "deadStatus": "false", "targetType": 2, "targetCount": 3, "seekId": 32}, {"currentRound": "false", "seekType": 10, "deadStatus": "false", "targetType": 2, "targetCount": 1, "seekId": 33}, {"currentRound": "false", "seekType": 10, "deadStatus": "false", "targetType": 2, "targetCount": 2, "seekId": 34}, {"currentRound": "false", "seekType": 10, "deadStatus": "false", "targetType": 2, "targetCount": 3, "seekId": 35}, {"currentRound": "false", "seekType": 11, "deadStatus": "false", "targetType": 2, "targetCount": 1, "seekId": 36}, {"currentRound": "false", "seekType": 11, "deadStatus": "false", "targetType": 2, "targetCount": 2, "seekId": 37}, {"currentRound": "false", "seekType": 11, "deadStatus": "false", "targetType": 2, "targetCount": 3, "seekId": 38}, {"currentRound": "false", "seekType": 12, "deadStatus": "false", "targetType": 2, "targetCount": 1, "seekId": 39}, {"currentRound": "false", "seekType": 12, "deadStatus": "false", "targetType": 2, "targetCount": 2, "seekId": 40}, {"currentRound": "false", "seekType": 12, "deadStatus": "false", "targetType": 2, "targetCount": 3, "seekId": 41}, {"currentRound": "false", "seekType": 13, "deadStatus": "false", "targetType": 2, "targetCount": 1, "seekId": 42}, {"currentRound": "false", "seekType": 13, "deadStatus": "false", "targetType": 2, "targetCount": 2, "seekId": 43}, {"currentRound": "false", "seekType": 13, "deadStatus": "false", "targetType": 2, "targetCount": 3, "seekId": 44}, {"currentRound": "false", "seekType": 14, "deadStatus": "false", "targetType": 2, "targetCount": 1, "seekId": 45}, {"currentRound": "false", "seekType": 14, "deadStatus": "false", "targetType": 2, "targetCount": 2, "seekId": 46}, {"currentRound": "false", "seekType": 14, "deadStatus": "false", "targetType": 2, "targetCount": 3, "seekId": 47}, {"currentRound": "false", "seekType": 15, "deadStatus": "false", "targetType": 2, "targetCount": 1, "seekId": 48}, {"currentRound": "false", "seekType": 15, "deadStatus": "false", "targetType": 2, "targetCount": 2, "seekId": 49}, {"currentRound": "false", "seekType": 15, "deadStatus": "false", "targetType": 2, "targetCount": 3, "seekId": 50}, {"currentRound": "false", "seekType": 16, "deadStatus": "false", "targetType": 2, "targetCount": 1, "seekId": 51}, {"currentRound": "false", "seekType": 16, "deadStatus": "false", "targetType": 2, "targetCount": 2, "seekId": 52}, {"currentRound": "false", "seekType": 16, "deadStatus": "false", "targetType": 2, "targetCount": 3, "seekId": 53}, {"currentRound": "false", "seekType": 17, "deadStatus": "false", "targetType": 2, "targetCount": 1, "seekId": 54}, {"currentRound": "false", "seekType": 17, "deadStatus": "false", "targetType": 2, "targetCount": 2, "seekId": 55}, {"currentRound": "false", "seekType": 17, "deadStatus": "false", "targetType": 2, "targetCount": 3, "seekId": 56}, {"currentRound": "false", "seekType": 18, "deadStatus": "false", "targetType": 2, "targetCount": 2, "seekId": 57}, {"currentRound": "false", "seekType": 2, "deadStatus": "false", "targetType": 3, "targetCount": 1, "seekId": 58}, {"currentRound": "false", "seekType": 2, "deadStatus": "false", "targetType": 3, "targetCount": 2, "seekId": 59}, {"currentRound": "false", "seekType": 2, "deadStatus": "false", "targetType": 3, "targetCount": 3, "seekId": 60}, {"currentRound": "false", "seekType": 2, "deadStatus": "false", "targetType": 3, "targetCount": 4, "seekId": 61}, {"currentRound": "false", "seekType": 2, "deadStatus": "false", "targetType": 3, "targetCount": 5, "seekId": 62}, {"currentRound": "false", "seekType": 2, "deadStatus": "false", "targetType": 3, "targetCount": 6, "seekId": 63}, {"currentRound": "false", "seekType": 2, "deadStatus": "false", "targetType": 3, "targetCount": 6, "seekId": 64}, {"currentRound": "false", "seekType": 3, "deadStatus": "false", "targetType": 3, "targetCount": 1, "seekId": 65}, {"currentRound": "false", "seekType": 3, "deadStatus": "false", "targetType": 3, "targetCount": 2, "seekId": 66}, {"currentRound": "false", "seekType": 3, "deadStatus": "false", "targetType": 3, "targetCount": 3, "seekId": 67}, {"currentRound": "false", "seekType": 3, "deadStatus": "false", "targetType": 3, "targetCount": 3, "seekId": 68}, {"currentRound": "false", "seekType": 4, "deadStatus": "false", "targetType": 3, "targetCount": 1, "seekId": 69}, {"currentRound": "false", "seekType": 4, "deadStatus": "false", "targetType": 3, "targetCount": 2, "seekId": 70}, {"currentRound": "false", "seekType": 4, "deadStatus": "false", "targetType": 3, "targetCount": 3, "seekId": 71}, {"currentRound": "false", "seekType": 4, "deadStatus": "false", "targetType": 3, "targetCount": 3, "seekId": 72}, {"currentRound": "false", "seekType": 5, "deadStatus": "false", "targetType": 3, "targetCount": 1, "seekId": 73}, {"currentRound": "false", "seekType": 5, "deadStatus": "false", "targetType": 3, "targetCount": 2, "seekId": 74}, {"currentRound": "false", "seekType": 5, "deadStatus": "false", "targetType": 3, "targetCount": 3, "seekId": 75}, {"currentRound": "false", "seekType": 6, "deadStatus": "false", "targetType": 3, "targetCount": 1, "seekId": 76}, {"currentRound": "false", "seekType": 6, "deadStatus": "false", "targetType": 3, "targetCount": 2, "seekId": 77}, {"currentRound": "false", "seekType": 6, "deadStatus": "false", "targetType": 3, "targetCount": 3, "seekId": 78}, {"currentRound": "false", "seekType": 7, "deadStatus": "false", "targetType": 3, "targetCount": 1, "seekId": 79}, {"currentRound": "false", "seekType": 7, "deadStatus": "false", "targetType": 3, "targetCount": 2, "seekId": 80}, {"currentRound": "false", "seekType": 7, "deadStatus": "false", "targetType": 3, "targetCount": 3, "seekId": 81}, {"currentRound": "false", "seekType": 8, "deadStatus": "false", "targetType": 3, "targetCount": 1, "seekId": 82}, {"currentRound": "false", "seekType": 8, "deadStatus": "false", "targetType": 3, "targetCount": 2, "seekId": 83}, {"currentRound": "false", "seekType": 8, "deadStatus": "false", "targetType": 3, "targetCount": 3, "seekId": 84}, {"currentRound": "false", "seekType": 9, "deadStatus": "false", "targetType": 3, "targetCount": 1, "seekId": 85}, {"currentRound": "false", "seekType": 9, "deadStatus": "false", "targetType": 3, "targetCount": 2, "seekId": 86}, {"currentRound": "false", "seekType": 9, "deadStatus": "false", "targetType": 3, "targetCount": 3, "seekId": 87}, {"currentRound": "false", "seekType": 10, "deadStatus": "false", "targetType": 3, "targetCount": 1, "seekId": 88}, {"currentRound": "false", "seekType": 10, "deadStatus": "false", "targetType": 3, "targetCount": 2, "seekId": 89}, {"currentRound": "false", "seekType": 10, "deadStatus": "false", "targetType": 3, "targetCount": 3, "seekId": 90}, {"currentRound": "false", "seekType": 11, "deadStatus": "false", "targetType": 3, "targetCount": 1, "seekId": 91}, {"currentRound": "false", "seekType": 11, "deadStatus": "false", "targetType": 3, "targetCount": 2, "seekId": 92}, {"currentRound": "false", "seekType": 11, "deadStatus": "false", "targetType": 3, "targetCount": 3, "seekId": 93}, {"currentRound": "false", "seekType": 12, "deadStatus": "false", "targetType": 3, "targetCount": 1, "seekId": 94}, {"currentRound": "false", "seekType": 12, "deadStatus": "false", "targetType": 3, "targetCount": 2, "seekId": 95}, {"currentRound": "false", "seekType": 12, "deadStatus": "false", "targetType": 3, "targetCount": 3, "seekId": 96}, {"currentRound": "false", "seekType": 13, "deadStatus": "false", "targetType": 3, "targetCount": 1, "seekId": 97}, {"currentRound": "false", "seekType": 13, "deadStatus": "false", "targetType": 3, "targetCount": 2, "seekId": 98}, {"currentRound": "false", "seekType": 13, "deadStatus": "false", "targetType": 3, "targetCount": 3, "seekId": 99}, {"currentRound": "false", "seekType": 14, "deadStatus": "false", "targetType": 3, "targetCount": 1, "seekId": 100}, {"currentRound": "false", "seekType": 14, "deadStatus": "false", "targetType": 3, "targetCount": 2, "seekId": 101}, {"currentRound": "false", "seekType": 14, "deadStatus": "false", "targetType": 3, "targetCount": 3, "seekId": 102}, {"currentRound": "false", "seekType": 15, "deadStatus": "false", "targetType": 3, "targetCount": 1, "seekId": 103}, {"currentRound": "false", "seekType": 15, "deadStatus": "false", "targetType": 3, "targetCount": 2, "seekId": 104}, {"currentRound": "false", "seekType": 15, "deadStatus": "false", "targetType": 3, "targetCount": 3, "seekId": 105}, {"currentRound": "false", "seekType": 16, "deadStatus": "false", "targetType": 3, "targetCount": 1, "seekId": 106}, {"currentRound": "false", "seekType": 16, "deadStatus": "false", "targetType": 3, "targetCount": 2, "seekId": 107}, {"currentRound": "false", "seekType": 16, "deadStatus": "false", "targetType": 3, "targetCount": 3, "seekId": 108}, {"currentRound": "false", "seekType": 17, "deadStatus": "false", "targetType": 3, "targetCount": 1, "seekId": 109}, {"currentRound": "false", "seekType": 17, "deadStatus": "false", "targetType": 3, "targetCount": 2, "seekId": 110}, {"currentRound": "false", "seekType": 17, "deadStatus": "false", "targetType": 3, "targetCount": 3, "seekId": 111}, {"currentRound": "false", "seekType": 2, "deadStatus": "false", "targetType": 4, "targetCount": 1, "seekId": 112}, {"currentRound": "false", "seekType": 2, "deadStatus": "false", "targetType": 4, "targetCount": 2, "seekId": 113}, {"currentRound": "false", "seekType": 2, "deadStatus": "false", "targetType": 4, "targetCount": 3, "seekId": 114}, {"currentRound": "false", "seekType": 2, "deadStatus": "false", "targetType": 4, "targetCount": 4, "seekId": 115}, {"currentRound": "false", "seekType": 2, "deadStatus": "false", "targetType": 4, "targetCount": 5, "seekId": 116}, {"currentRound": "false", "seekType": 2, "deadStatus": "false", "targetType": 4, "targetCount": 6, "seekId": 117}, {"currentRound": "false", "seekType": 3, "deadStatus": "false", "targetType": 4, "targetCount": 1, "seekId": 118}, {"currentRound": "false", "seekType": 3, "deadStatus": "false", "targetType": 4, "targetCount": 2, "seekId": 119}, {"currentRound": "false", "seekType": 3, "deadStatus": "false", "targetType": 4, "targetCount": 3, "seekId": 120}, {"currentRound": "false", "seekType": 3, "deadStatus": "false", "targetType": 4, "targetCount": 3, "seekId": 121}, {"currentRound": "false", "seekType": 4, "deadStatus": "false", "targetType": 4, "targetCount": 1, "seekId": 122}, {"currentRound": "false", "seekType": 4, "deadStatus": "false", "targetType": 4, "targetCount": 2, "seekId": 123}, {"currentRound": "false", "seekType": 4, "deadStatus": "false", "targetType": 4, "targetCount": 3, "seekId": 124}, {"currentRound": "false", "seekType": 4, "deadStatus": "false", "targetType": 4, "targetCount": 3, "seekId": 125}, {"currentRound": "false", "seekType": 5, "deadStatus": "false", "targetType": 4, "targetCount": 1, "seekId": 126}, {"currentRound": "false", "seekType": 5, "deadStatus": "false", "targetType": 4, "targetCount": 2, "seekId": 127}, {"currentRound": "false", "seekType": 5, "deadStatus": "false", "targetType": 4, "targetCount": 3, "seekId": 128}, {"currentRound": "false", "seekType": 6, "deadStatus": "false", "targetType": 4, "targetCount": 1, "seekId": 129}, {"currentRound": "false", "seekType": 6, "deadStatus": "false", "targetType": 4, "targetCount": 2, "seekId": 130}, {"currentRound": "false", "seekType": 6, "deadStatus": "false", "targetType": 4, "targetCount": 3, "seekId": 131}, {"currentRound": "false", "seekType": 7, "deadStatus": "false", "targetType": 4, "targetCount": 1, "seekId": 132}, {"currentRound": "false", "seekType": 7, "deadStatus": "false", "targetType": 4, "targetCount": 2, "seekId": 133}, {"currentRound": "false", "seekType": 7, "deadStatus": "false", "targetType": 4, "targetCount": 3, "seekId": 134}, {"currentRound": "false", "seekType": 8, "deadStatus": "false", "targetType": 4, "targetCount": 1, "seekId": 135}, {"currentRound": "false", "seekType": 8, "deadStatus": "false", "targetType": 4, "targetCount": 2, "seekId": 136}, {"currentRound": "false", "seekType": 8, "deadStatus": "false", "targetType": 4, "targetCount": 3, "seekId": 137}, {"currentRound": "false", "seekType": 9, "deadStatus": "false", "targetType": 4, "targetCount": 1, "seekId": 138}, {"currentRound": "false", "seekType": 9, "deadStatus": "false", "targetType": 4, "targetCount": 2, "seekId": 139}, {"currentRound": "false", "seekType": 9, "deadStatus": "false", "targetType": 4, "targetCount": 3, "seekId": 140}, {"currentRound": "false", "seekType": 10, "deadStatus": "false", "targetType": 4, "targetCount": 1, "seekId": 141}, {"currentRound": "false", "seekType": 10, "deadStatus": "false", "targetType": 4, "targetCount": 2, "seekId": 142}, {"currentRound": "false", "seekType": 10, "deadStatus": "false", "targetType": 4, "targetCount": 3, "seekId": 143}, {"currentRound": "false", "seekType": 11, "deadStatus": "false", "targetType": 4, "targetCount": 1, "seekId": 144}, {"currentRound": "false", "seekType": 11, "deadStatus": "false", "targetType": 4, "targetCount": 2, "seekId": 145}, {"currentRound": "false", "seekType": 11, "deadStatus": "false", "targetType": 4, "targetCount": 3, "seekId": 146}, {"currentRound": "false", "seekType": 12, "deadStatus": "false", "targetType": 4, "targetCount": 1, "seekId": 147}, {"currentRound": "false", "seekType": 12, "deadStatus": "false", "targetType": 4, "targetCount": 2, "seekId": 148}, {"currentRound": "false", "seekType": 12, "deadStatus": "false", "targetType": 4, "targetCount": 3, "seekId": 149}, {"currentRound": "false", "seekType": 13, "deadStatus": "false", "targetType": 4, "targetCount": 1, "seekId": 150}, {"currentRound": "false", "seekType": 13, "deadStatus": "false", "targetType": 4, "targetCount": 2, "seekId": 151}, {"currentRound": "false", "seekType": 13, "deadStatus": "false", "targetType": 4, "targetCount": 3, "seekId": 152}, {"currentRound": "false", "seekType": 14, "deadStatus": "false", "targetType": 4, "targetCount": 1, "seekId": 153}, {"currentRound": "false", "seekType": 14, "deadStatus": "false", "targetType": 4, "targetCount": 2, "seekId": 154}, {"currentRound": "false", "seekType": 14, "deadStatus": "false", "targetType": 4, "targetCount": 3, "seekId": 155}, {"currentRound": "false", "seekType": 15, "deadStatus": "false", "targetType": 4, "targetCount": 1, "seekId": 156}, {"currentRound": "false", "seekType": 15, "deadStatus": "false", "targetType": 4, "targetCount": 2, "seekId": 157}, {"currentRound": "false", "seekType": 15, "deadStatus": "false", "targetType": 4, "targetCount": 3, "seekId": 158}, {"currentRound": "false", "seekType": 16, "deadStatus": "false", "targetType": 4, "targetCount": 1, "seekId": 159}, {"currentRound": "false", "seekType": 16, "deadStatus": "false", "targetType": 4, "targetCount": 2, "seekId": 160}, {"currentRound": "false", "seekType": 16, "deadStatus": "false", "targetType": 4, "targetCount": 3, "seekId": 161}, {"currentRound": "false", "seekType": 17, "deadStatus": "false", "targetType": 4, "targetCount": 1, "seekId": 162}, {"currentRound": "false", "seekType": 17, "deadStatus": "false", "targetType": 4, "targetCount": 2, "seekId": 163}, {"currentRound": "false", "seekType": 17, "deadStatus": "false", "targetType": 4, "targetCount": 3, "seekId": 164}, {"currentRound": "false", "seekType": 2, "deadStatus": "false", "targetType": 3, "targetCount": 1, "seekId": 165}, {"currentRound": "false", "seekType": 2, "deadStatus": "false", "targetType": 3, "targetCount": 2, "seekId": 166}, {"currentRound": "false", "seekType": 2, "deadStatus": "false", "targetType": 3, "targetCount": 3, "seekId": 167}, {"currentRound": "false", "seekType": 2, "deadStatus": "false", "targetType": 3, "targetCount": 4, "seekId": 168}, {"currentRound": "false", "seekType": 2, "deadStatus": "false", "targetType": 5, "targetCount": 5, "seekId": 169}, {"currentRound": "false", "seekType": 2, "deadStatus": "false", "targetType": 5, "targetCount": 6, "seekId": 170}, {"currentRound": "false", "seekType": 3, "deadStatus": "false", "targetType": 5, "targetCount": 1, "seekId": 171}, {"currentRound": "false", "seekType": 3, "deadStatus": "false", "targetType": 5, "targetCount": 2, "seekId": 172}, {"currentRound": "false", "seekType": 3, "deadStatus": "false", "targetType": 5, "targetCount": 3, "seekId": 173}, {"currentRound": "false", "seekType": 3, "deadStatus": "false", "targetType": 5, "targetCount": 4, "seekId": 174}, {"currentRound": "false", "seekType": 4, "deadStatus": "false", "targetType": 5, "targetCount": 1, "seekId": 175}, {"currentRound": "false", "seekType": 4, "deadStatus": "false", "targetType": 5, "targetCount": 2, "seekId": 176}, {"currentRound": "false", "seekType": 4, "deadStatus": "false", "targetType": 5, "targetCount": 3, "seekId": 177}, {"currentRound": "false", "seekType": 4, "deadStatus": "false", "targetType": 5, "targetCount": 4, "seekId": 178}, {"currentRound": "false", "seekType": 5, "deadStatus": "false", "targetType": 5, "targetCount": 1, "seekId": 179}, {"currentRound": "false", "seekType": 5, "deadStatus": "false", "targetType": 5, "targetCount": 2, "seekId": 180}, {"currentRound": "false", "seekType": 5, "deadStatus": "false", "targetType": 5, "targetCount": 3, "seekId": 181}, {"currentRound": "false", "seekType": 6, "deadStatus": "false", "targetType": 5, "targetCount": 1, "seekId": 182}, {"currentRound": "false", "seekType": 6, "deadStatus": "false", "targetType": 5, "targetCount": 2, "seekId": 183}, {"currentRound": "false", "seekType": 6, "deadStatus": "false", "targetType": 5, "targetCount": 3, "seekId": 184}, {"currentRound": "false", "seekType": 7, "deadStatus": "false", "targetType": 5, "targetCount": 1, "seekId": 185}, {"currentRound": "false", "seekType": 7, "deadStatus": "false", "targetType": 5, "targetCount": 2, "seekId": 186}, {"currentRound": "false", "seekType": 7, "deadStatus": "false", "targetType": 5, "targetCount": 3, "seekId": 187}, {"currentRound": "false", "seekType": 8, "deadStatus": "false", "targetType": 5, "targetCount": 1, "seekId": 188}, {"currentRound": "false", "seekType": 8, "deadStatus": "false", "targetType": 5, "targetCount": 2, "seekId": 189}, {"currentRound": "false", "seekType": 8, "deadStatus": "false", "targetType": 5, "targetCount": 3, "seekId": 190}, {"currentRound": "false", "seekType": 9, "deadStatus": "false", "targetType": 5, "targetCount": 1, "seekId": 191}, {"currentRound": "false", "seekType": 9, "deadStatus": "false", "targetType": 5, "targetCount": 2, "seekId": 192}, {"currentRound": "false", "seekType": 9, "deadStatus": "false", "targetType": 5, "targetCount": 3, "seekId": 193}, {"currentRound": "false", "seekType": 10, "deadStatus": "false", "targetType": 5, "targetCount": 1, "seekId": 194}, {"currentRound": "false", "seekType": 10, "deadStatus": "false", "targetType": 5, "targetCount": 2, "seekId": 195}, {"currentRound": "false", "seekType": 10, "deadStatus": "false", "targetType": 5, "targetCount": 3, "seekId": 196}, {"currentRound": "false", "seekType": 11, "deadStatus": "false", "targetType": 5, "targetCount": 1, "seekId": 197}, {"currentRound": "false", "seekType": 11, "deadStatus": "false", "targetType": 5, "targetCount": 2, "seekId": 198}, {"currentRound": "false", "seekType": 11, "deadStatus": "false", "targetType": 5, "targetCount": 3, "seekId": 199}, {"currentRound": "false", "seekType": 12, "deadStatus": "false", "targetType": 5, "targetCount": 1, "seekId": 200}, {"currentRound": "false", "seekType": 12, "deadStatus": "false", "targetType": 5, "targetCount": 2, "seekId": 201}, {"currentRound": "false", "seekType": 12, "deadStatus": "false", "targetType": 5, "targetCount": 3, "seekId": 202}, {"currentRound": "false", "seekType": 13, "deadStatus": "false", "targetType": 5, "targetCount": 1, "seekId": 203}, {"currentRound": "false", "seekType": 13, "deadStatus": "false", "targetType": 5, "targetCount": 2, "seekId": 204}, {"currentRound": "false", "seekType": 13, "deadStatus": "false", "targetType": 5, "targetCount": 3, "seekId": 205}, {"currentRound": "false", "seekType": 14, "deadStatus": "false", "targetType": 5, "targetCount": 1, "seekId": 206}, {"currentRound": "false", "seekType": 14, "deadStatus": "false", "targetType": 5, "targetCount": 2, "seekId": 207}, {"currentRound": "false", "seekType": 14, "deadStatus": "false", "targetType": 5, "targetCount": 3, "seekId": 208}, {"currentRound": "false", "seekType": 15, "deadStatus": "false", "targetType": 5, "targetCount": 1, "seekId": 209}, {"currentRound": "false", "seekType": 15, "deadStatus": "false", "targetType": 5, "targetCount": 2, "seekId": 210}, {"currentRound": "false", "seekType": 15, "deadStatus": "false", "targetType": 5, "targetCount": 3, "seekId": 211}, {"currentRound": "false", "seekType": 16, "deadStatus": "false", "targetType": 5, "targetCount": 1, "seekId": 212}, {"currentRound": "false", "seekType": 16, "deadStatus": "false", "targetType": 5, "targetCount": 2, "seekId": 213}, {"currentRound": "false", "seekType": 16, "deadStatus": "false", "targetType": 5, "targetCount": 3, "seekId": 214}, {"currentRound": "false", "seekType": 17, "deadStatus": "false", "targetType": 5, "targetCount": 1, "seekId": 215}, {"currentRound": "false", "seekType": 17, "deadStatus": "false", "targetType": 5, "targetCount": 2, "seekId": 216}, {"currentRound": "false", "seekType": 17, "deadStatus": "false", "targetType": 5, "targetCount": 3, "seekId": 217}, {"currentRound": "false", "seekType": 2, "deadStatus": "true", "targetType": 3, "targetCount": 1, "seekId": 218}, {"currentRound": "false", "seekType": 2, "deadStatus": "true", "targetType": 3, "targetCount": 2, "seekId": 219}, {"currentRound": "false", "seekType": 2, "deadStatus": "true", "targetType": 3, "targetCount": 3, "seekId": 220}, {"currentRound": "false", "seekType": 2, "deadStatus": "true", "targetType": 3, "targetCount": 4, "seekId": 221}, {"currentRound": "true", "seekType": 2, "deadStatus": "true", "targetType": 3, "targetCount": 5, "seekId": 222}, {"currentRound": "false", "seekType": 2, "deadStatus": "true", "targetType": 3, "targetCount": 6, "seekId": 223}, {"currentRound": "false", "seekType": 2, "deadStatus": "true", "targetType": 3, "targetCount": 6, "seekId": 224}, {"currentRound": "false", "seekType": 9, "deadStatus": "true", "targetType": 3, "targetCount": 1, "seekId": 225}, {"currentRound": "false", "seekType": 9, "deadStatus": "true", "targetType": 3, "targetCount": 2, "seekId": 226}, {"currentRound": "false", "seekType": 9, "deadStatus": "true", "targetType": 3, "targetCount": 3, "seekId": 227}, {"currentRound": "false", "seekType": 11, "deadStatus": "true", "targetType": 3, "targetCount": 1, "seekId": 228}, {"currentRound": "false", "seekType": 11, "deadStatus": "true", "targetType": 3, "targetCount": 2, "seekId": 229}, {"currentRound": "false", "seekType": 11, "deadStatus": "true", "targetType": 3, "targetCount": 3, "seekId": 230}, {"currentRound": "false", "seekType": 13, "deadStatus": "true", "targetType": 3, "targetCount": 1, "seekId": 231}, {"currentRound": "false", "seekType": 13, "deadStatus": "true", "targetType": 3, "targetCount": 2, "seekId": 232}, {"currentRound": "false", "seekType": 13, "deadStatus": "true", "targetType": 3, "targetCount": 3, "seekId": 233}, {"currentRound": "false", "seekType": 19, "deadStatus": "true", "targetType": 2, "targetCount": 1, "seekId": 234}, {"currentRound": "false", "seekType": 9, "deadStatus": "false", "targetType": 3, "targetCount": 4, "seekId": 235}, {"currentRound": "false", "seekType": 20, "deadStatus": "false", "targetType": 2, "targetCount": 2, "seekId": 236}, {"currentRound": "false", "seekType": 21, "deadStatus": "false", "targetType": 3, "targetCount": 6, "seekId": 237}, {"currentRound": "false", "seekType": 22, "deadStatus": "false", "targetType": 3, "targetCount": 6, "seekId": 238}, {"currentRound": "false", "seekType": 21, "deadStatus": "false", "targetType": 2, "targetCount": 6, "seekId": 239}, {"currentRound": "false", "seekType": 22, "deadStatus": "false", "targetType": 2, "targetCount": 6, "seekId": 240}, {"currentRound": "false", "seekType": 23, "deadStatus": "false", "targetType": 2, "targetCount": 1, "seekId": 241}, {"currentRound": "false", "seekType": 23, "deadStatus": "false", "targetType": 3, "targetCount": 1, "seekId": 242}, {"currentRound": "false", "seekType": 24, "deadStatus": "false", "targetType": 2, "targetCount": 1, "seekId": 243}, {"currentRound": "false", "seekType": 24, "deadStatus": "false", "targetType": 3, "targetCount": 1, "seekId": 244}, {"currentRound": "false", "seekType": 1, "deadStatus": "true", "targetType": 1, "targetCount": 1, "seekId": 245}, {"currentRound": "false", "seekType": 25, "deadStatus": "false", "targetType": 3, "targetCount": 6, "seekId": 246}, {"currentRound": "false", "seekType": 25, "deadStatus": "false", "targetType": 2, "targetCount": 6, "seekId": 247}, {"currentRound": "false", "seekType": 26, "deadStatus": "false", "targetType": 3, "targetCount": 6, "seekId": 248}, {"currentRound": "false", "seekType": 26, "deadStatus": "false", "targetType": 2, "targetCount": 6, "seekId": 249}, {"currentRound": "false", "seekType": 6, "deadStatus": "false", "targetType": 2, "targetCount": 4, "seekId": 250}, {"currentRound": "false", "seekType": 6, "deadStatus": "false", "targetType": 3, "targetCount": 4, "seekId": 251}, {"currentRound": "false", "seekType": 27, "deadStatus": "false", "targetType": 2, "targetCount": 2, "seekId": 252}, {"currentRound": "false", "seekType": 28, "deadStatus": "false", "targetType": 2, "targetCount": 2, "seekId": 253}, {"currentRound": "false", "seekType": 29, "deadStatus": "false", "targetType": 2, "targetCount": 1, "seekId": 254}, {"currentRound": "false", "seekType": 29, "deadStatus": "false", "targetType": 2, "targetCount": 2, "seekId": 255}, {"currentRound": "false", "seekType": 29, "deadStatus": "false", "targetType": 2, "targetCount": 3, "seekId": 256}, {"currentRound": "false", "seekType": 30, "deadStatus": "false", "targetType": 2, "targetCount": 6, "seekId": 257}, {"currentRound": "false", "seekType": 2, "deadStatus": "false", "targetType": 5, "targetCount": 1, "seekId": 258}, {"currentRound": "false", "seekType": 38, "deadStatus": "false", "targetType": 2, "targetCount": 2, "seekId": 259}, {"currentRound": "false", "seekType": 40, "deadStatus": "false", "targetType": 2, "targetCount": 1, "seekId": 260}, {"currentRound": "false", "seekType": 41, "deadStatus": "false", "targetType": 2, "targetCount": 1, "seekId": 261}, {"currentRound": "false", "seekType": 34, "deadStatus": "false", "targetType": 3, "targetCount": 6, "seekId": 262}, {"currentRound": "false", "seekType": 35, "deadStatus": "false", "targetType": 3, "targetCount": 6, "seekId": 263}, {"currentRound": "false", "seekType": 36, "deadStatus": "false", "targetType": 3, "targetCount": 6, "seekId": 264}, {"currentRound": "false", "seekType": 34, "deadStatus": "false", "targetType": 5, "targetCount": 6, "seekId": 265}, {"currentRound": "false", "seekType": 37, "deadStatus": "false", "targetType": 2, "targetCount": 1, "seekId": 266}, {"currentRound": "false", "seekType": 35, "deadStatus": "false", "targetType": 5, "targetCount": 6, "seekId": 267}, {"currentRound": "false", "seekType": 42, "deadStatus": "false", "targetType": 3, "targetCount": 6, "seekId": 268}, {"currentRound": "false", "seekType": 43, "deadStatus": "false", "targetType": 3, "targetCount": 6, "seekId": 269}, {"currentRound": "false", "seekType": 44, "deadStatus": "false", "targetType": 3, "targetCount": 6, "seekId": 270}, {"currentRound": "false", "seekType": 45, "deadStatus": "false", "targetType": 3, "targetCount": 6, "seekId": 271}, {"currentRound": "false", "seekType": 30, "deadStatus": "false", "targetType": 3, "targetCount": 6, "seekId": 272}, {"currentRound": "false", "seekType": 31, "deadStatus": "false", "targetType": 3, "targetCount": 6, "seekId": 273}, {"currentRound": "false", "seekType": 32, "deadStatus": "false", "targetType": 3, "targetCount": 6, "seekId": 274}, {"currentRound": "false", "seekType": 1, "deadStatus": "false", "targetType": 6, "targetCount": 1, "seekId": 999}]