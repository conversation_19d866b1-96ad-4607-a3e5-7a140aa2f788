[{"isAnd": "true", "condition": [[101, 1]], "orderId": 1, "id": 10101, "type": 1, "rewards": []}, {"isAnd": "true", "condition": [[101, 2]], "orderId": 2, "id": 10102, "type": 1, "rewards": []}, {"isAnd": "true", "condition": [[101, 3]], "orderId": 3, "id": 10103, "type": 1, "rewards": []}, {"isAnd": "true", "condition": [[101, 6]], "orderId": 4, "id": 10104, "type": 1, "rewards": []}, {"isAnd": "true", "condition": [[101, 10]], "orderId": 5, "id": 10105, "type": 1, "rewards": []}, {"isAnd": "true", "condition": [[101, 12]], "orderId": 6, "id": 10106, "type": 1, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 7, "id": 10107, "type": 1, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 8, "id": 10108, "type": 1, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 9, "id": 10109, "type": 1, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 10, "id": 10110, "type": 1, "rewards": []}, {"isAnd": "true", "condition": [[101, 21]], "orderId": 1, "id": 10201, "type": 2, "rewards": []}, {"isAnd": "false", "condition": [[101, 95], [106, 30]], "orderId": 1, "id": 10301, "type": 3, "rewards": []}, {"isAnd": "false", "condition": [[101, 95], [106, 30]], "orderId": 2, "id": 10302, "type": 3, "rewards": []}, {"isAnd": "false", "condition": [[101, 95], [106, 30]], "orderId": 3, "id": 10303, "type": 3, "rewards": []}, {"isAnd": "true", "condition": [[101, 70]], "orderId": 0, "id": 10400, "type": 4, "rewards": []}, {"isAnd": "true", "condition": [[101, 26]], "orderId": 1, "id": 10401, "type": 4, "rewards": []}, {"isAnd": "true", "condition": [[101, 30]], "orderId": 2, "id": 10402, "type": 4, "rewards": []}, {"isAnd": "true", "condition": [[101, 40]], "orderId": 3, "id": 10403, "type": 4, "rewards": []}, {"isAnd": "true", "condition": [[101, 50]], "orderId": 4, "id": 10404, "type": 4, "rewards": []}, {"isAnd": "true", "condition": [[101, 75]], "orderId": 5, "id": 10405, "type": 4, "rewards": []}, {"isAnd": "true", "condition": [[106, 22], [101, 75]], "orderId": 6, "id": 10406, "type": 4, "rewards": []}, {"isAnd": "true", "condition": [[106, 75], [101, 95]], "orderId": 7, "id": 10407, "type": 4, "rewards": []}, {"isAnd": "true", "condition": [[101, 26]], "orderId": 1, "id": 10501, "type": 5, "rewards": []}, {"isAnd": "true", "condition": [[101, 18]], "orderId": 1, "id": 10601, "type": 6, "rewards": []}, {"isAnd": "true", "condition": [[101, 26]], "orderId": 1, "id": 10701, "type": 7, "rewards": []}, {"isAnd": "true", "condition": [[101, 999]], "orderId": 1, "id": 10801, "type": 8, "rewards": []}, {"isAnd": "true", "condition": [[101, 20]], "orderId": 1, "id": 10901, "type": 9, "rewards": []}, {"isAnd": "true", "condition": [[101, 20]], "orderId": 1, "id": 11001, "type": 10, "rewards": []}, {"isAnd": "true", "condition": [[104, 3]], "orderId": 2, "id": 11002, "type": 10, "rewards": []}, {"isAnd": "true", "condition": [[104, 3]], "orderId": 3, "id": 11003, "type": 10, "rewards": []}, {"isAnd": "true", "condition": [[101, 15]], "orderId": 1, "id": 11101, "type": 11, "rewards": []}, {"isAnd": "true", "condition": [[101, 70]], "orderId": 2, "id": 11102, "type": 11, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 1, "id": 11201, "type": 12, "rewards": []}, {"isAnd": "true", "condition": [[101, 18]], "orderId": 2, "id": 11202, "type": 12, "rewards": []}, {"isAnd": "true", "condition": [[106, 2], [101, 30]], "orderId": 3, "id": 11203, "type": 12, "rewards": []}, {"isAnd": "true", "condition": [[113, 51]], "orderId": 4, "id": 11204, "type": 12, "rewards": []}, {"isAnd": "true", "condition": [[101, 999]], "orderId": 5, "id": 11205, "type": 12, "rewards": []}, {"isAnd": "true", "condition": [[106, 6], [101, 40]], "orderId": 6, "id": 11206, "type": 12, "rewards": []}, {"isAnd": "true", "condition": [[106, 4], [101, 35]], "orderId": 7, "id": 11207, "type": 12, "rewards": []}, {"isAnd": "true", "condition": [[101, 20]], "orderId": 8, "id": 11208, "type": 12, "rewards": []}, {"isAnd": "true", "condition": [[101, 999]], "orderId": 9, "id": 11209, "type": 12, "rewards": []}, {"isAnd": "true", "condition": [[106, 15], [101, 75]], "orderId": 10, "id": 11210, "type": 12, "rewards": []}, {"isAnd": "true", "condition": [[106, 8], [101, 60]], "orderId": 11, "id": 11211, "type": 12, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 100, "id": 11212, "type": 12, "rewards": []}, {"isAnd": "true", "condition": [[106, 13], [101, 18]], "orderId": 12, "id": 11213, "type": 12, "rewards": []}, {"isAnd": "true", "condition": [[106, 22], [101, 75]], "orderId": 13, "id": 11214, "type": 12, "rewards": []}, {"isAnd": "true", "condition": [[106, 8], [101, 40]], "orderId": 14, "id": 11215, "type": 12, "rewards": []}, {"isAnd": "true", "condition": [[101, 12]], "orderId": 15, "id": 11216, "type": 12, "rewards": []}, {"isAnd": "true", "condition": [[106, 2], [101, 35]], "orderId": 1, "id": 11301, "type": 13, "rewards": []}, {"isAnd": "true", "condition": [[101, 45], [112, 2]], "orderId": 2, "id": 11302, "type": 13, "rewards": []}, {"isAnd": "true", "condition": [[106, 6], [101, 40]], "orderId": 3, "id": 11303, "type": 13, "rewards": []}, {"isAnd": "true", "condition": [[112, 16], [101, 40]], "orderId": 4, "id": 11304, "type": 13, "rewards": []}, {"isAnd": "true", "condition": [[106, 4], [101, 35]], "orderId": 1, "id": 11401, "type": 14, "rewards": []}, {"isAnd": "true", "condition": [[101, 18]], "orderId": 1, "id": 11501, "type": 15, "rewards": []}, {"isAnd": "true", "condition": [[101, 25]], "orderId": 2, "id": 11502, "type": 15, "rewards": []}, {"isAnd": "true", "condition": [[101, 23]], "orderId": 1, "id": 11601, "type": 16, "rewards": []}, {"isAnd": "false", "condition": [[101, 1]], "orderId": 2, "id": 11602, "type": 16, "rewards": []}, {"isAnd": "false", "condition": [[101, 1]], "orderId": 3, "id": 11603, "type": 16, "rewards": []}, {"isAnd": "true", "condition": [[101, 35]], "orderId": 4, "id": 11604, "type": 16, "rewards": []}, {"isAnd": "true", "condition": [[101, 37]], "orderId": 1, "id": 11701, "type": 17, "rewards": []}, {"isAnd": "true", "condition": [[101, 100]], "orderId": 1, "id": 11801, "type": 18, "rewards": []}, {"isAnd": "true", "condition": [[101, 5]], "orderId": 1, "id": 11901, "type": 19, "rewards": []}, {"isAnd": "false", "condition": [[101, 5]], "orderId": 2, "id": 11902, "type": 19, "rewards": []}, {"isAnd": "true", "condition": [[101, 70]], "orderId": 3, "id": 11903, "type": 19, "rewards": []}, {"isAnd": "true", "condition": [[101, 37]], "orderId": 1, "id": 12001, "type": 20, "rewards": []}, {"isAnd": "true", "condition": [[106, 12]], "orderId": 1, "id": 12101, "type": 21, "rewards": []}, {"isAnd": "true", "condition": [[101, 36]], "orderId": 1, "id": 12201, "type": 22, "rewards": []}, {"isAnd": "true", "condition": [[106, 6]], "orderId": 1, "id": 12301, "type": 23, "rewards": []}, {"isAnd": "true", "condition": [[101, 29]], "orderId": 1, "id": 12401, "type": 24, "rewards": []}, {"isAnd": "true", "condition": [[107, 110303]], "orderId": 1, "id": 12501, "type": 25, "rewards": []}, {"isAnd": "true", "condition": [[101, 8]], "orderId": 1, "id": 12601, "type": 26, "rewards": []}, {"isAnd": "true", "condition": [[101, 100]], "orderId": 1, "id": 12701, "type": 27, "rewards": []}, {"isAnd": "true", "condition": [[101, 100]], "orderId": 1, "id": 12801, "type": 28, "rewards": []}, {"isAnd": "true", "condition": [[101, 40]], "orderId": 1, "id": 12901, "type": 29, "rewards": []}, {"isAnd": "true", "condition": [[106, 4]], "orderId": 1, "id": 13001, "type": 30, "rewards": []}, {"isAnd": "true", "condition": [[101, 32]], "orderId": 1, "id": 13101, "type": 31, "rewards": []}, {"isAnd": "true", "condition": [[101, 32]], "orderId": 2, "id": 13102, "type": 31, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 2, "id": 13202, "type": 32, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 3, "id": 13203, "type": 32, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 4, "id": 13204, "type": 32, "rewards": []}, {"isAnd": "false", "condition": [[101, 1]], "orderId": 5, "id": 13205, "type": 32, "rewards": []}, {"isAnd": "false", "condition": [[101, 1]], "orderId": 6, "id": 13206, "type": 32, "rewards": []}, {"isAnd": "false", "condition": [[101, 1]], "orderId": 7, "id": 13207, "type": 32, "rewards": []}, {"isAnd": "true", "condition": [[106, 5], [101, 40]], "orderId": 1, "id": 13301, "type": 33, "rewards": []}, {"isAnd": "true", "condition": [[106, 8], [101, 40]], "orderId": 2, "id": 13302, "type": 33, "rewards": []}, {"isAnd": "true", "condition": [[101, 22]], "orderId": 1, "id": 13401, "type": 34, "rewards": []}, {"isAnd": "true", "condition": [[101, 23]], "orderId": 1, "id": 13501, "type": 35, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 1, "id": 13601, "type": 36, "rewards": []}, {"isAnd": "true", "condition": [[101, 37]], "orderId": 1, "id": 13701, "type": 37, "rewards": []}, {"isAnd": "true", "condition": [[101, 16]], "orderId": 1, "id": 13801, "type": 38, "rewards": []}, {"isAnd": "true", "condition": [[101, 4]], "orderId": 2, "id": 13802, "type": 38, "rewards": []}, {"isAnd": "true", "condition": [[101, 30]], "orderId": 1, "id": 13901, "type": 39, "rewards": []}, {"isAnd": "true", "condition": [[101, 30]], "orderId": 2, "id": 13902, "type": 39, "rewards": []}, {"isAnd": "true", "condition": [[101, 35]], "orderId": 3, "id": 13903, "type": 39, "rewards": []}, {"isAnd": "true", "condition": [[101, 40]], "orderId": 4, "id": 13904, "type": 39, "rewards": []}, {"isAnd": "true", "condition": [[101, 45]], "orderId": 5, "id": 13905, "type": 39, "rewards": []}, {"isAnd": "true", "condition": [[101, 50]], "orderId": 6, "id": 13906, "type": 39, "rewards": []}, {"isAnd": "false", "condition": [[101, 70], [102, 4]], "orderId": 7, "id": 13907, "type": 39, "rewards": []}, {"isAnd": "false", "condition": [[101, 80], [102, 6]], "orderId": 8, "id": 13908, "type": 39, "rewards": []}, {"isAnd": "false", "condition": [[101, 90], [102, 8]], "orderId": 9, "id": 13909, "type": 39, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 1, "id": 14001, "type": 40, "rewards": []}, {"isAnd": "true", "condition": [[101, 10]], "orderId": 2, "id": 14002, "type": 40, "rewards": []}, {"isAnd": "true", "condition": [[101, 40]], "orderId": 3, "id": 14003, "type": 40, "rewards": []}, {"isAnd": "true", "condition": [[106, 10]], "orderId": 4, "id": 14004, "type": 40, "rewards": []}, {"isAnd": "true", "condition": [[101, 60]], "orderId": 5, "id": 14005, "type": 40, "rewards": []}, {"isAnd": "true", "condition": [[101, 100]], "orderId": 6, "id": 14006, "type": 40, "rewards": []}, {"isAnd": "true", "condition": [[106, 975], [101, 900]], "orderId": 7, "id": 14007, "type": 40, "rewards": []}, {"isAnd": "true", "condition": [[101, 15]], "orderId": 1, "id": 14101, "type": 41, "rewards": []}, {"isAnd": "true", "condition": [[101, 15]], "orderId": 2, "id": 14102, "type": 41, "rewards": []}, {"isAnd": "true", "condition": [[101, 999]], "orderId": 3, "id": 14103, "type": 41, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 4, "id": 14104, "type": 41, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 5, "id": 14105, "type": 41, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 6, "id": 14106, "type": 41, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 7, "id": 14107, "type": 41, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 8, "id": 14108, "type": 41, "rewards": []}, {"isAnd": "true", "condition": [[101, 70]], "orderId": 9, "id": 14109, "type": 41, "rewards": []}, {"isAnd": "true", "condition": [[101, 15]], "orderId": 10, "id": 14110, "type": 41, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 11, "id": 14111, "type": 41, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 12, "id": 14112, "type": 41, "rewards": []}, {"isAnd": "true", "condition": [[101, 70]], "orderId": 13, "id": 14113, "type": 41, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 14, "id": 14114, "type": 41, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 15, "id": 14115, "type": 41, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 16, "id": 14116, "type": 41, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 17, "id": 14117, "type": 41, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 18, "id": 14118, "type": 41, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 20, "id": 14120, "type": 41, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 21, "id": 14121, "type": 41, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 22, "id": 14122, "type": 41, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 23, "id": 14123, "type": 41, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 24, "id": 14124, "type": 41, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 25, "id": 14125, "type": 41, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 26, "id": 14126, "type": 41, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 27, "id": 14127, "type": 41, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 28, "id": 14128, "type": 41, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 29, "id": 14129, "type": 41, "rewards": []}, {"isAnd": "true", "condition": [[101, 35]], "orderId": 1, "id": 14201, "type": 42, "rewards": []}, {"isAnd": "true", "condition": [[106, 999]], "orderId": 2, "id": 14202, "type": 42, "rewards": []}, {"isAnd": "true", "condition": [[101, 4]], "orderId": 1, "id": 14301, "type": 43, "rewards": []}, {"isAnd": "true", "condition": [[107, 110505]], "orderId": 1, "id": 14401, "type": 44, "rewards": []}, {"isAnd": "true", "condition": [[107, 110505]], "orderId": 19, "id": 14402, "type": 130, "rewards": []}, {"isAnd": "true", "condition": [[107, 110201]], "orderId": 1, "id": 14501, "type": 45, "rewards": []}, {"isAnd": "true", "condition": [[101, 75]], "orderId": 1, "id": 14601, "type": 46, "rewards": []}, {"isAnd": "true", "condition": [[101, 75]], "orderId": 2, "id": 14602, "type": 46, "rewards": []}, {"isAnd": "true", "condition": [[106, 10], [101, 60]], "orderId": 1, "id": 14701, "type": 47, "rewards": []}, {"isAnd": "true", "condition": [[104, 2]], "orderId": 1, "id": 14801, "type": 48, "rewards": []}, {"isAnd": "false", "condition": [[101, 50], [102, 3]], "orderId": 2, "id": 14802, "type": 48, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 1, "id": 14901, "type": 49, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 2, "id": 14902, "type": 49, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 3, "id": 14903, "type": 49, "rewards": []}, {"isAnd": "true", "condition": [[107, 110505]], "orderId": 1, "id": 15001, "type": 50, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 2, "id": 15002, "type": 50, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 3, "id": 15003, "type": 50, "rewards": []}, {"isAnd": "true", "condition": [[101, 8]], "orderId": 1, "id": 15101, "type": 51, "rewards": []}, {"isAnd": "true", "condition": [[104, 2]], "orderId": 1, "id": 10001, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[104, 3]], "orderId": 2, "id": 10002, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[104, 4]], "orderId": 3, "id": 10003, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[104, 5]], "orderId": 4, "id": 10004, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[107, 110305]], "orderId": 1, "id": 10005, "type": 101, "rewards": []}, {"isAnd": "true", "condition": [[107, 110205]], "orderId": 1, "id": 10006, "type": 102, "rewards": []}, {"isAnd": "true", "condition": [[107, 110103]], "orderId": 1, "id": 10007, "type": 103, "rewards": []}, {"isAnd": "true", "condition": [[101, 10]], "orderId": 1, "id": 10008, "type": 104, "rewards": []}, {"isAnd": "true", "condition": [[101, 6]], "orderId": 1, "id": 10009, "type": 105, "rewards": []}, {"isAnd": "true", "condition": [[101, 30]], "orderId": 1, "id": 10010, "type": 1000, "rewards": []}, {"isAnd": "true", "condition": [[101, 30]], "orderId": 2, "id": 10011, "type": 1000, "rewards": []}, {"isAnd": "true", "condition": [[101, 30]], "orderId": 3, "id": 10012, "type": 1000, "rewards": []}, {"isAnd": "true", "condition": [[101, 30]], "orderId": 4, "id": 10013, "type": 1000, "rewards": []}, {"isAnd": "true", "condition": [[101, 30]], "orderId": 5, "id": 11014, "type": 1000, "rewards": []}, {"isAnd": "true", "condition": [[101, 30]], "orderId": 6, "id": 11015, "type": 1000, "rewards": []}, {"isAnd": "true", "condition": [[101, 30]], "orderId": 7, "id": 11016, "type": 1000, "rewards": []}, {"isAnd": "true", "condition": [[101, 30]], "orderId": 8, "id": 11017, "type": 1000, "rewards": []}, {"isAnd": "true", "condition": [[102, 3]], "orderId": 1, "id": 10014, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[102, 2], [107, 110305]], "orderId": 1, "id": 10015, "type": 106, "rewards": []}, {"isAnd": "true", "condition": [[101, 20]], "orderId": 1, "id": 10016, "type": 107, "rewards": []}, {"isAnd": "true", "condition": [[113, 51]], "orderId": 1, "id": 10017, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[103, 50]], "orderId": 1, "id": 10018, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[103, 100]], "orderId": 1, "id": 10019, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[106, 8]], "orderId": 1, "id": 10020, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[112, 11]], "orderId": 1, "id": 10021, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[112, 21]], "orderId": 1, "id": 10022, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[112, 36]], "orderId": 1, "id": 10023, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[113, 51]], "orderId": 1, "id": 10024, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[106, 12]], "orderId": 1, "id": 10025, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[106, 14]], "orderId": 1, "id": 10026, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[116, 100]], "orderId": 1, "id": 10027, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[116, 30]], "orderId": 1, "id": 10028, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[116, 60]], "orderId": 1, "id": 10029, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[103, 400]], "orderId": 1, "id": 10030, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[103, 300]], "orderId": 1, "id": 10031, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[103, 200]], "orderId": 1, "id": 10032, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[103, 150]], "orderId": 1, "id": 10033, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[103, 100]], "orderId": 1, "id": 10034, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[103, 50]], "orderId": 1, "id": 10035, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[103, 30]], "orderId": 1, "id": 10036, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[103, 10]], "orderId": 1, "id": 10037, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[113, 61]], "orderId": 1, "id": 10038, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[113, 71]], "orderId": 1, "id": 10039, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[113, 81]], "orderId": 1, "id": 10040, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[113, 51]], "orderId": 1, "id": 10041, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[113, 71]], "orderId": 1, "id": 10042, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[113, 81]], "orderId": 1, "id": 10043, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[113, 91]], "orderId": 1, "id": 10044, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[113, 101]], "orderId": 1, "id": 10045, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[113, 101]], "orderId": 1, "id": 10046, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[106, 8]], "orderId": 1, "id": 10047, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[113, 121]], "orderId": 1, "id": 10068, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[113, 141]], "orderId": 1, "id": 10069, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[113, 161]], "orderId": 1, "id": 10070, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[113, 181]], "orderId": 1, "id": 10071, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[113, 201]], "orderId": 1, "id": 10072, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[113, 251]], "orderId": 1, "id": 10073, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[113, 301]], "orderId": 1, "id": 10074, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[113, 351]], "orderId": 1, "id": 10075, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[113, 401]], "orderId": 1, "id": 10076, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[113, 451]], "orderId": 1, "id": 10077, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[113, 501]], "orderId": 1, "id": 10078, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[113, 551]], "orderId": 1, "id": 10079, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[113, 601]], "orderId": 1, "id": 10080, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[113, 651]], "orderId": 1, "id": 10081, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[113, 701]], "orderId": 1, "id": 10082, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[113, 751]], "orderId": 1, "id": 10083, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[113, 801]], "orderId": 1, "id": 10084, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[113, 851]], "orderId": 1, "id": 10085, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[113, 901]], "orderId": 1, "id": 10086, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[113, 951]], "orderId": 1, "id": 10087, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[113, 1001]], "orderId": 1, "id": 10088, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[103, 100]], "orderId": 1, "id": 10048, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[103, 80]], "orderId": 1, "id": 10049, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[103, 50]], "orderId": 1, "id": 10050, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[104, 3]], "orderId": 1, "id": 10051, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[104, 4]], "orderId": 2, "id": 10052, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[116, 40]], "orderId": 1, "id": 10053, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[116, 80]], "orderId": 1, "id": 10054, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[116, 120]], "orderId": 1, "id": 10055, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[116, 120]], "orderId": 1, "id": 10056, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[101, 70]], "orderId": 2, "id": 10057, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[101, 80]], "orderId": 2, "id": 10058, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[101, 90]], "orderId": 2, "id": 10059, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[101, 90]], "orderId": 1, "id": 10063, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[101, 85]], "orderId": 1, "id": 10064, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[106, 22], [101, 75]], "orderId": 1, "id": 10065, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[106, 22], [101, 75]], "orderId": 1, "id": 10066, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[104, 5]], "orderId": 3, "id": 10067, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[116, 280]], "orderId": 1, "id": 10089, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[112, 51]], "orderId": 1, "id": 10090, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[116, 380]], "orderId": 1, "id": 10091, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[113, 1051]], "orderId": 1, "id": 10092, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[113, 1101]], "orderId": 1, "id": 10093, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[113, 1151]], "orderId": 1, "id": 10094, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[113, 1201]], "orderId": 1, "id": 10095, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[113, 1251]], "orderId": 1, "id": 10096, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[113, 1301]], "orderId": 1, "id": 10097, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[113, 1351]], "orderId": 1, "id": 10098, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[113, 1401]], "orderId": 1, "id": 10099, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[113, 1451]], "orderId": 1, "id": 10100, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[113, 1501]], "orderId": 1, "id": 100101, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[113, 1551]], "orderId": 1, "id": 100102, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[113, 1601]], "orderId": 1, "id": 100103, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[113, 1651]], "orderId": 1, "id": 100104, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[113, 1701]], "orderId": 1, "id": 100105, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[113, 1751]], "orderId": 1, "id": 100106, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[113, 1801]], "orderId": 1, "id": 100107, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[113, 1851]], "orderId": 1, "id": 100108, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[113, 1901]], "orderId": 1, "id": 100109, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[113, 1951]], "orderId": 1, "id": 100110, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[113, 2001]], "orderId": 1, "id": 100111, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[106, 3]], "orderId": 1, "id": 101101, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[106, 4]], "orderId": 1, "id": 101102, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[106, 6]], "orderId": 1, "id": 101103, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[106, 8]], "orderId": 1, "id": 101104, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[106, 10]], "orderId": 1, "id": 101105, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[106, 15]], "orderId": 1, "id": 101106, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[106, 22]], "orderId": 1, "id": 101107, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[102, 4]], "orderId": 1, "id": 101108, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[102, 5]], "orderId": 1, "id": 101109, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[102, 6]], "orderId": 1, "id": 101110, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[102, 7]], "orderId": 1, "id": 101111, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 1, "id": 15201, "type": 52, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 2, "id": 15202, "type": 52, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 3, "id": 15203, "type": 52, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 4, "id": 15204, "type": 52, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 5, "id": 15205, "type": 52, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 6, "id": 15206, "type": 52, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 7, "id": 15207, "type": 52, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 8, "id": 15208, "type": 52, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 9, "id": 15209, "type": 52, "rewards": []}, {"isAnd": "true", "condition": [[101, 22]], "orderId": 10, "id": 15210, "type": 52, "rewards": []}, {"isAnd": "true", "condition": [[101, 10]], "orderId": 1, "id": 15301, "type": 53, "rewards": []}, {"isAnd": "true", "condition": [[101, 40]], "orderId": 2, "id": 15302, "type": 53, "rewards": []}, {"isAnd": "true", "condition": [[101, 40]], "orderId": 0, "id": 15303, "type": 53, "rewards": []}, {"isAnd": "true", "condition": [[101, 40], [106, 8]], "orderId": 1, "id": 15401, "type": 54, "rewards": []}, {"isAnd": "true", "condition": [[101, 40], [106, 8]], "orderId": 2, "id": 15402, "type": 54, "rewards": []}, {"isAnd": "true", "condition": [[107, 110405]], "orderId": 1, "id": 15500, "type": 55, "rewards": []}, {"isAnd": "true", "condition": [[101, 12]], "orderId": 1, "id": 15601, "type": 56, "rewards": []}, {"isAnd": "false", "condition": [[101, 74], [102, 7]], "orderId": 1, "id": 15701, "type": 57, "rewards": []}, {"isAnd": "false", "condition": [[101, 78], [102, 7]], "orderId": 2, "id": 15702, "type": 57, "rewards": []}, {"isAnd": "true", "condition": [[106, 8], [101, 50]], "orderId": 1, "id": 15801, "type": 58, "rewards": []}, {"isAnd": "true", "condition": [[106, 8], [101, 50]], "orderId": 2, "id": 15802, "type": 58, "rewards": []}, {"isAnd": "true", "condition": [[106, 8], [101, 50]], "orderId": 3, "id": 15803, "type": 58, "rewards": []}, {"isAnd": "true", "condition": [[106, 8], [101, 50]], "orderId": 1, "id": 15901, "type": 59, "rewards": []}, {"isAnd": "true", "condition": [[101, 21]], "orderId": 1, "id": 16001, "type": 60, "rewards": []}, {"isAnd": "true", "condition": [[114, 0]], "orderId": 1, "id": 20000, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[114, 1]], "orderId": 2, "id": 20001, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[114, 2]], "orderId": 3, "id": 20002, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[114, 3]], "orderId": 4, "id": 20003, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[114, 4]], "orderId": 5, "id": 20004, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[114, 5]], "orderId": 6, "id": 20005, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[114, 6]], "orderId": 7, "id": 20006, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[114, 7]], "orderId": 8, "id": 20007, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[114, 8]], "orderId": 9, "id": 20008, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[114, 9]], "orderId": 10, "id": 20009, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[114, 10]], "orderId": 11, "id": 20010, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[114, 11]], "orderId": 12, "id": 20011, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[114, 12]], "orderId": 13, "id": 20012, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[114, 13]], "orderId": 14, "id": 20013, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[114, 14]], "orderId": 15, "id": 20014, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[114, 15]], "orderId": 16, "id": 20015, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[114, 16]], "orderId": 17, "id": 20016, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[114, 17]], "orderId": 18, "id": 20017, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[114, 18]], "orderId": 19, "id": 20018, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[114, 19]], "orderId": 20, "id": 20019, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[114, 20]], "orderId": 21, "id": 20020, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[114, 21]], "orderId": 22, "id": 20021, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[114, 22]], "orderId": 23, "id": 20022, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[114, 23]], "orderId": 24, "id": 20023, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[114, 24]], "orderId": 25, "id": 20024, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[114, 25]], "orderId": 26, "id": 20025, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[114, 26]], "orderId": 27, "id": 20026, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[114, 27]], "orderId": 28, "id": 20027, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[114, 28]], "orderId": 29, "id": 20028, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[114, 29]], "orderId": 30, "id": 20029, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[114, 30]], "orderId": 31, "id": 20030, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[114, 31]], "orderId": 32, "id": 20031, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[114, 32]], "orderId": 33, "id": 20032, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[114, 33]], "orderId": 34, "id": 20033, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[114, 34]], "orderId": 35, "id": 20034, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[114, 35]], "orderId": 36, "id": 20035, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[114, 36]], "orderId": 37, "id": 20036, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[106, 15], [101, 75]], "orderId": 1, "id": 16101, "type": 61, "rewards": []}, {"isAnd": "true", "condition": [[101, 70]], "orderId": 2, "id": 16102, "type": 61, "rewards": []}, {"isAnd": "true", "condition": [[101, 72]], "orderId": 3, "id": 16103, "type": 61, "rewards": []}, {"isAnd": "true", "condition": [[101, 75]], "orderId": 4, "id": 16104, "type": 61, "rewards": []}, {"isAnd": "true", "condition": [[101, 78]], "orderId": 5, "id": 16105, "type": 61, "rewards": []}, {"isAnd": "true", "condition": [[101, 80]], "orderId": 6, "id": 16106, "type": 61, "rewards": []}, {"isAnd": "true", "condition": [[101, 85]], "orderId": 7, "id": 16107, "type": 61, "rewards": []}, {"isAnd": "true", "condition": [[101, 90]], "orderId": 8, "id": 16108, "type": 61, "rewards": []}, {"isAnd": "true", "condition": [[101, 95]], "orderId": 9, "id": 16109, "type": 61, "rewards": []}, {"isAnd": "true", "condition": [[101, 100]], "orderId": 10, "id": 16110, "type": 61, "rewards": []}, {"isAnd": "true", "condition": [[107, 110305]], "orderId": 1, "id": 30000, "type": 113, "rewards": []}, {"isAnd": "true", "condition": [[101, 15]], "orderId": 1, "id": 16201, "type": 62, "rewards": []}, {"isAnd": "true", "condition": [[101, 300]], "orderId": 1, "id": 16301, "type": 63, "rewards": []}, {"isAnd": "true", "condition": [[107, 110505]], "orderId": 1, "id": 16401, "type": 64, "rewards": []}, {"isAnd": "true", "condition": [[101, 10]], "orderId": 2, "id": 16402, "type": 64, "rewards": []}, {"isAnd": "true", "condition": [[101, 10]], "orderId": 3, "id": 16403, "type": 64, "rewards": []}, {"isAnd": "true", "condition": [[101, 10]], "orderId": 4, "id": 16404, "type": 64, "rewards": []}, {"isAnd": "false", "condition": [[101, 70]], "orderId": 5, "id": 16405, "type": 64, "rewards": []}, {"isAnd": "true", "condition": [[107, 110405]], "orderId": 1, "id": 16501, "type": 65, "rewards": []}, {"isAnd": "true", "condition": [[106, 9999]], "orderId": 1, "id": 16600, "type": 66, "rewards": []}, {"isAnd": "true", "condition": [[106, 8]], "orderId": 1, "id": 166001, "type": 66, "rewards": []}, {"isAnd": "true", "condition": [[106, 14]], "orderId": 1, "id": 166002, "type": 66, "rewards": []}, {"isAnd": "true", "condition": [[106, 24]], "orderId": 1, "id": 166003, "type": 66, "rewards": []}, {"isAnd": "true", "condition": [[106, 2]], "orderId": 1, "id": 166004, "type": 66, "rewards": []}, {"isAnd": "true", "condition": [[106, 6]], "orderId": 1, "id": 166005, "type": 66, "rewards": []}, {"isAnd": "true", "condition": [[106, 10]], "orderId": 1, "id": 166006, "type": 66, "rewards": []}, {"isAnd": "true", "condition": [[106, 4]], "orderId": 1, "id": 166007, "type": 66, "rewards": []}, {"isAnd": "true", "condition": [[106, 20]], "orderId": 1, "id": 166008, "type": 66, "rewards": []}, {"isAnd": "true", "condition": [[106, 46]], "orderId": 1, "id": 166009, "type": 66, "rewards": []}, {"isAnd": "true", "condition": [[106, 39]], "orderId": 1, "id": 166010, "type": 66, "rewards": []}, {"isAnd": "true", "condition": [[106, 60]], "orderId": 1, "id": 166011, "type": 66, "rewards": []}, {"isAnd": "true", "condition": [[106, 65]], "orderId": 1, "id": 166012, "type": 66, "rewards": []}, {"isAnd": "true", "condition": [[106, 50]], "orderId": 1, "id": 166013, "type": 66, "rewards": []}, {"isAnd": "true", "condition": [[106, 70]], "orderId": 1, "id": 166014, "type": 66, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 1, "id": 16604, "type": 66, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 1, "id": 16605, "type": 66, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 1, "id": 16606, "type": 66, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 1, "id": 16607, "type": 66, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 1, "id": 16608, "type": 66, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 1, "id": 16609, "type": 66, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 1, "id": 16610, "type": 66, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 1, "id": 16611, "type": 66, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 1, "id": 16612, "type": 66, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 1, "id": 16613, "type": 66, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 1, "id": 16614, "type": 66, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 1, "id": 16615, "type": 66, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 1, "id": 16616, "type": 66, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 1, "id": 16617, "type": 66, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 1, "id": 16618, "type": 66, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 1, "id": 16619, "type": 66, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 1, "id": 16620, "type": 66, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 1, "id": 16621, "type": 66, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 1, "id": 16622, "type": 66, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 1, "id": 16623, "type": 66, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 1, "id": 16624, "type": 66, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 1, "id": 16625, "type": 66, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 1, "id": 16626, "type": 66, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 1, "id": 16627, "type": 66, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 1, "id": 16628, "type": 66, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 1, "id": 16629, "type": 66, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 1, "id": 16630, "type": 66, "rewards": []}, {"isAnd": "true", "condition": [[101, 98]], "orderId": 1, "id": 16631, "type": 66, "rewards": []}, {"isAnd": "true", "condition": [[106, 999]], "orderId": 1, "id": 16701, "type": 67, "rewards": []}, {"isAnd": "true", "condition": [[106, 8], [101, 60]], "orderId": 1, "id": 16801, "type": 68, "rewards": []}, {"isAnd": "true", "condition": [[107, 130608]], "orderId": 1, "id": 16901, "type": 69, "rewards": []}, {"isAnd": "true", "condition": [[101, 15]], "orderId": 1, "id": 16911, "type": 114, "rewards": []}, {"isAnd": "true", "condition": [[101, 10]], "orderId": 1, "id": 17001, "type": 70, "rewards": []}, {"isAnd": "true", "condition": [[101, 15]], "orderId": 1, "id": 17101, "type": 71, "rewards": []}, {"isAnd": "true", "condition": [[101, 15]], "orderId": 1, "id": 17201, "type": 72, "rewards": []}, {"isAnd": "true", "condition": [[106, 3], [101, 30]], "orderId": 1, "id": 17301, "type": 73, "rewards": []}, {"isAnd": "true", "condition": [[101, 35]], "orderId": 2, "id": 17302, "type": 73, "rewards": []}, {"isAnd": "true", "condition": [[101, 32]], "orderId": 3, "id": 17303, "type": 73, "rewards": []}, {"isAnd": "true", "condition": [[106, 8], [101, 50]], "orderId": 4, "id": 17304, "type": 73, "rewards": []}, {"isAnd": "false", "condition": [[101, 85], [106, 30], [117, 10]], "orderId": 1, "id": 17401, "type": 74, "rewards": []}, {"isAnd": "false", "condition": [[101, 80]], "orderId": 1, "id": 17402, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[107, 120105]], "orderId": 1, "id": 17403, "type": 115, "rewards": []}, {"isAnd": "false", "condition": [[101, 90]], "orderId": 1, "id": 17404, "type": 0, "rewards": []}, {"isAnd": "false", "condition": [[101, 32]], "orderId": 1, "id": 17405, "type": 0, "rewards": []}, {"isAnd": "false", "condition": [[101, 50]], "orderId": 1, "id": 17406, "type": 0, "rewards": []}, {"isAnd": "false", "condition": [[101, 100]], "orderId": 1, "id": 17407, "type": 0, "rewards": []}, {"isAnd": "false", "condition": [[101, 155]], "orderId": 1, "id": 17408, "type": 0, "rewards": []}, {"isAnd": "false", "condition": [[101, 20]], "orderId": 1, "id": 31601, "type": 116, "rewards": []}, {"isAnd": "true", "condition": [[101, 5]], "orderId": 1, "id": 17501, "type": 75, "rewards": []}, {"isAnd": "false", "condition": [[102, 1], [101, 20]], "orderId": 2, "id": 17502, "type": 75, "rewards": []}, {"isAnd": "true", "condition": [[101, 50], [104, 3]], "orderId": 1, "id": 17601, "type": 76, "rewards": []}, {"isAnd": "true", "condition": [[101, 50]], "orderId": 1, "id": 17701, "type": 77, "rewards": []}, {"isAnd": "true", "condition": [[106, 3], [101, 30]], "orderId": 1, "id": 17801, "type": 78, "rewards": []}, {"isAnd": "true", "condition": [[101, 190]], "orderId": 2, "id": 17802, "type": 78, "rewards": []}, {"isAnd": "true", "condition": [[113, 51]], "orderId": 3, "id": 17803, "type": 78, "rewards": []}, {"isAnd": "true", "condition": [[101, 999]], "orderId": 4, "id": 17804, "type": 78, "rewards": []}, {"isAnd": "true", "condition": [[106, 12], [101, 40]], "orderId": 5, "id": 17805, "type": 78, "rewards": []}, {"isAnd": "true", "condition": [[106, 8], [101, 60]], "orderId": 6, "id": 17806, "type": 78, "rewards": []}, {"isAnd": "true", "condition": [[106, 15], [101, 18]], "orderId": 7, "id": 17807, "type": 78, "rewards": []}, {"isAnd": "true", "condition": [[106, 5], [101, 40]], "orderId": 8, "id": 17808, "type": 78, "rewards": []}, {"isAnd": "false", "condition": [[101, 50], [104, 3]], "orderId": 1, "id": 17901, "type": 79, "rewards": []}, {"isAnd": "false", "condition": [[101, 80], [102, 7]], "orderId": 1, "id": 18001, "type": 80, "rewards": []}, {"isAnd": "false", "condition": [[101, 83], [102, 7]], "orderId": 2, "id": 18002, "type": 80, "rewards": []}, {"isAnd": "true", "condition": [[107, 110205]], "orderId": 1, "id": 10060, "type": 117, "rewards": []}, {"isAnd": "true", "condition": [[107, 110205]], "orderId": 1, "id": 10061, "type": 118, "rewards": []}, {"isAnd": "true", "condition": [[106, 35]], "orderId": 1, "id": 10062, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 1, "id": 18101, "type": 81, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 1, "id": 18201, "type": 82, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 2, "id": 18202, "type": 82, "rewards": []}, {"isAnd": "true", "condition": [[101, 1]], "orderId": 1, "id": 18301, "type": 83, "rewards": []}, {"isAnd": "true", "condition": [[101, 62]], "orderId": 2, "id": 18302, "type": 83, "rewards": []}, {"isAnd": "true", "condition": [[101, 65]], "orderId": 3, "id": 18303, "type": 83, "rewards": []}, {"isAnd": "true", "condition": [[101, 67]], "orderId": 4, "id": 18304, "type": 83, "rewards": []}, {"isAnd": "true", "condition": [[101, 60]], "orderId": 1, "id": 18401, "type": 84, "rewards": []}, {"isAnd": "true", "condition": [[101, 70]], "orderId": 2, "id": 18402, "type": 84, "rewards": []}, {"isAnd": "true", "condition": [[101, 80]], "orderId": 3, "id": 18403, "type": 84, "rewards": []}, {"isAnd": "true", "condition": [[101, 90]], "orderId": 4, "id": 18404, "type": 84, "rewards": []}, {"isAnd": "true", "condition": [[101, 100]], "orderId": 5, "id": 18405, "type": 84, "rewards": []}, {"isAnd": "true", "condition": [[101, 999]], "orderId": 6, "id": 18406, "type": 84, "rewards": []}, {"isAnd": "true", "condition": [[101, 999]], "orderId": 7, "id": 18407, "type": 84, "rewards": []}, {"isAnd": "true", "condition": [[101, 999]], "orderId": 8, "id": 18408, "type": 84, "rewards": []}, {"isAnd": "true", "condition": [[101, 999]], "orderId": 9, "id": 18409, "type": 84, "rewards": []}, {"isAnd": "true", "condition": [[121, 22], [101, 75]], "orderId": 1, "id": 18501, "type": 85, "rewards": []}, {"isAnd": "true", "condition": [[121, 22], [101, 75]], "orderId": 2, "id": 18502, "type": 85, "rewards": []}, {"isAnd": "true", "condition": [[121, 22], [101, 75]], "orderId": 3, "id": 18503, "type": 85, "rewards": []}, {"isAnd": "true", "condition": [[121, 22], [101, 75]], "orderId": 4, "id": 18504, "type": 85, "rewards": []}, {"isAnd": "true", "condition": [[106, 10], [101, 60]], "orderId": 1, "id": 18601, "type": 86, "rewards": []}, {"isAnd": "true", "condition": [[106, 10], [101, 70]], "orderId": 2, "id": 18602, "type": 86, "rewards": []}, {"isAnd": "true", "condition": [[101, 80]], "orderId": 3, "id": 18603, "type": 86, "rewards": []}, {"isAnd": "true", "condition": [[101, 90]], "orderId": 4, "id": 18604, "type": 86, "rewards": []}, {"isAnd": "true", "condition": [[101, 100]], "orderId": 5, "id": 18605, "type": 86, "rewards": []}, {"isAnd": "true", "condition": [[101, 110]], "orderId": 6, "id": 18606, "type": 86, "rewards": []}, {"isAnd": "true", "condition": [[106, 15], [101, 70]], "orderId": 1, "id": 18701, "type": 87, "rewards": []}, {"isAnd": "false", "condition": [[101, 100], [102, 12]], "orderId": 2, "id": 18702, "type": 87, "rewards": []}, {"isAnd": "true", "condition": [[106, 10], [101, 60]], "orderId": 1, "id": 18703, "type": 88, "rewards": []}, {"isAnd": "false", "condition": [[101, 75]], "orderId": 2, "id": 18704, "type": 88, "rewards": []}, {"isAnd": "true", "condition": [[101, 999]], "orderId": 1, "id": 18705, "type": 89, "rewards": []}, {"isAnd": "false", "condition": [[101, 999]], "orderId": 2, "id": 18706, "type": 89, "rewards": []}, {"isAnd": "true", "condition": [[101, 75]], "orderId": 1, "id": 18707, "type": 90, "rewards": []}, {"isAnd": "true", "condition": [[101, 75]], "orderId": 2, "id": 18708, "type": 90, "rewards": []}, {"isAnd": "true", "condition": [[101, 75]], "orderId": 3, "id": 18709, "type": 90, "rewards": []}, {"isAnd": "false", "condition": [[101, 85], [118, 1]], "orderId": 4, "id": 18710, "type": 90, "rewards": []}, {"isAnd": "true", "condition": [[106, 18], [101, 75]], "orderId": 1, "id": 18711, "type": 91, "rewards": []}, {"isAnd": "true", "condition": [[106, 18], [101, 75]], "orderId": 2, "id": 19101, "type": 91, "rewards": []}, {"isAnd": "true", "condition": [[101, 15]], "orderId": 1, "id": 18712, "type": 119, "rewards": []}, {"isAnd": "true", "condition": [[101, 50]], "orderId": 1, "id": 112001, "type": 120, "rewards": []}, {"isAnd": "true", "condition": [[106, 10], [101, 60], [107, 120101]], "orderId": 1, "id": 18713, "type": 92, "rewards": []}, {"isAnd": "true", "condition": [[119, 2]], "orderId": 2, "id": 18714, "type": 92, "rewards": []}, {"isAnd": "true", "condition": [[119, 2]], "orderId": 3, "id": 18715, "type": 92, "rewards": []}, {"isAnd": "true", "condition": [[106, 1]], "orderId": 4, "id": 18717, "type": 92, "rewards": []}, {"isAnd": "true", "condition": [[119, 7]], "orderId": 5, "id": 18718, "type": 92, "rewards": []}, {"isAnd": "true", "condition": [[101, 28]], "orderId": 1, "id": 18716, "type": 121, "rewards": []}, {"isAnd": "true", "condition": [[106, 22], [101, 75]], "orderId": 0, "id": 122000, "type": 122, "rewards": []}, {"isAnd": "true", "condition": [[106, 22], [101, 75]], "orderId": 1, "id": 122001, "type": 122, "rewards": []}, {"isAnd": "true", "condition": [[106, 22], [101, 75]], "orderId": 2, "id": 122002, "type": 122, "rewards": []}, {"isAnd": "true", "condition": [[106, 22], [101, 75]], "orderId": 3, "id": 122003, "type": 122, "rewards": []}, {"isAnd": "true", "condition": [[106, 22], [101, 75]], "orderId": 4, "id": 122004, "type": 122, "rewards": []}, {"isAnd": "true", "condition": [[106, 24], [101, 78]], "orderId": 5, "id": 122005, "type": 122, "rewards": []}, {"isAnd": "true", "condition": [[106, 22], [101, 75]], "orderId": 1, "id": 123001, "type": 123, "rewards": []}, {"isAnd": "true", "condition": [[106, 24], [101, 78]], "orderId": 2, "id": 123002, "type": 123, "rewards": []}, {"isAnd": "true", "condition": [[106, 13], [101, 70]], "orderId": 1, "id": 19301, "type": 93, "rewards": []}, {"isAnd": "true", "condition": [[106, 13], [101, 70]], "orderId": 2, "id": 1930101, "type": 93, "rewards": []}, {"isAnd": "true", "condition": [[119, 5]], "orderId": 1, "id": 124001, "type": 124, "rewards": []}, {"isAnd": "true", "condition": [[119, 5]], "orderId": 1, "id": 125001, "type": 125, "rewards": []}, {"isAnd": "true", "condition": [[119, 5]], "orderId": 2, "id": 125101, "type": 125, "rewards": []}, {"isAnd": "true", "condition": [[119, 5]], "orderId": 3, "id": 125201, "type": 125, "rewards": []}, {"isAnd": "true", "condition": [[107, 140208]], "orderId": 1, "id": 125002, "type": 126, "rewards": []}, {"isAnd": "true", "condition": [[101, 82]], "orderId": 1, "id": 127001, "type": 127, "rewards": []}, {"isAnd": "true", "condition": [[101, 82]], "orderId": 2, "id": 127002, "type": 127, "rewards": []}, {"isAnd": "true", "condition": [[101, 84]], "orderId": 3, "id": 127003, "type": 127, "rewards": []}, {"isAnd": "true", "condition": [[101, 86]], "orderId": 4, "id": 127004, "type": 127, "rewards": []}, {"isAnd": "true", "condition": [[101, 88]], "orderId": 5, "id": 127005, "type": 127, "rewards": []}, {"isAnd": "true", "condition": [[101, 90]], "orderId": 6, "id": 127006, "type": 127, "rewards": []}, {"isAnd": "true", "condition": [[106, 22], [101, 75]], "orderId": 1, "id": 128001, "type": 128, "rewards": []}, {"isAnd": "true", "condition": [[106, 2], [101, 30]], "orderId": 1, "id": 129001, "type": 129, "rewards": []}, {"isAnd": "true", "condition": [[106, 2], [101, 30]], "orderId": 2, "id": 129002, "type": 129, "rewards": []}, {"isAnd": "true", "condition": [[106, 15]], "orderId": 3, "id": 129003, "type": 129, "rewards": []}, {"isAnd": "true", "condition": [[106, 2], [101, 30]], "orderId": 1, "id": 130001, "type": 130, "rewards": []}, {"isAnd": "true", "condition": [[114, 8]], "orderId": 2, "id": 130002, "type": 130, "rewards": []}, {"isAnd": "true", "condition": [[114, 12]], "orderId": 3, "id": 130003, "type": 130, "rewards": []}, {"isAnd": "true", "condition": [[114, 16]], "orderId": 4, "id": 130004, "type": 130, "rewards": []}, {"isAnd": "true", "condition": [[114, 20]], "orderId": 5, "id": 130005, "type": 130, "rewards": []}, {"isAnd": "true", "condition": [[114, 24]], "orderId": 6, "id": 130006, "type": 130, "rewards": []}, {"isAnd": "true", "condition": [[114, 6]], "orderId": 7, "id": 130007, "type": 130, "rewards": []}, {"isAnd": "true", "condition": [[114, 10]], "orderId": 8, "id": 130008, "type": 130, "rewards": []}, {"isAnd": "true", "condition": [[114, 14]], "orderId": 9, "id": 130009, "type": 130, "rewards": []}, {"isAnd": "true", "condition": [[114, 18]], "orderId": 10, "id": 130010, "type": 130, "rewards": []}, {"isAnd": "true", "condition": [[114, 22]], "orderId": 11, "id": 130011, "type": 130, "rewards": []}, {"isAnd": "true", "condition": [[114, 26]], "orderId": 12, "id": 130012, "type": 130, "rewards": []}, {"isAnd": "true", "condition": [[114, 18]], "orderId": 13, "id": 130013, "type": 130, "rewards": []}, {"isAnd": "true", "condition": [[114, 18]], "orderId": 14, "id": 130014, "type": 130, "rewards": []}, {"isAnd": "true", "condition": [[114, 18]], "orderId": 15, "id": 130015, "type": 130, "rewards": []}, {"isAnd": "true", "condition": [[114, 22]], "orderId": 16, "id": 130016, "type": 130, "rewards": []}, {"isAnd": "true", "condition": [[114, 26]], "orderId": 17, "id": 130017, "type": 130, "rewards": []}, {"isAnd": "true", "condition": [[114, 30]], "orderId": 18, "id": 130018, "type": 130, "rewards": []}, {"isAnd": "true", "condition": [[106, 15], [101, 75]], "orderId": 1, "id": 131001, "type": 131, "rewards": []}, {"isAnd": "true", "condition": [[106, 15], [101, 75]], "orderId": 2, "id": 131002, "type": 131, "rewards": []}, {"isAnd": "true", "condition": [[101, 58]], "orderId": 1, "id": 1001001, "type": 1001, "rewards": []}, {"isAnd": "true", "condition": [[101, 60]], "orderId": 1, "id": 1002001, "type": 1002, "rewards": []}, {"isAnd": "true", "condition": [[106, 15], [101, 75]], "orderId": 1, "id": 1003001, "type": 1003, "rewards": []}, {"isAnd": "false", "condition": [[122, 3], [102, 3]], "orderId": 1, "id": 1004001, "type": 1004, "rewards": []}, {"isAnd": "true", "condition": [[106, 8], [101, 40]], "orderId": 1, "id": 1005001, "type": 1005, "rewards": []}, {"isAnd": "true", "condition": [[101, 80], [106, 31]], "orderId": 1, "id": 132001, "type": 132, "rewards": []}, {"isAnd": "true", "condition": [[101, 10]], "orderId": 1, "id": 133001, "type": 133, "rewards": []}, {"isAnd": "true", "condition": [[106, 50], [101, 90]], "orderId": 1, "id": 134001, "type": 134, "rewards": [[11, 226202, 10]]}, {"isAnd": "true", "condition": [[106, 50], [101, 90]], "orderId": 2, "id": 134002, "type": 134, "rewards": []}, {"isAnd": "true", "condition": [[120, 100]], "orderId": 1, "id": 135001, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[106, 60], [101, 60]], "orderId": 1, "id": 135002, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[106, 60], [101, 60]], "orderId": 1, "id": 135003, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[106, 60], [101, 60]], "orderId": 1, "id": 135004, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[107, 110305]], "orderId": 1, "id": 136001, "type": 136, "rewards": []}, {"isAnd": "true", "condition": [[106, 75], [101, 95]], "orderId": 1, "id": 137001, "type": 137, "rewards": []}, {"isAnd": "true", "condition": [[106, 75], [101, 95]], "orderId": 2, "id": 137002, "type": 137, "rewards": []}, {"isAnd": "true", "condition": [[106, 65], [101, 999]], "orderId": 3, "id": 137003, "type": 137, "rewards": []}, {"isAnd": "true", "condition": [[106, 75], [101, 95]], "orderId": 4, "id": 137004, "type": 137, "rewards": []}, {"isAnd": "true", "condition": [[121, 20]], "orderId": 1, "id": 138001, "type": 138, "rewards": []}, {"isAnd": "true", "condition": [[106, 8], [101, 40]], "orderId": 1, "id": 139001, "type": 139, "rewards": []}, {"isAnd": "true", "condition": [[106, 8], [101, 40]], "orderId": 2, "id": 139002, "type": 139, "rewards": []}, {"isAnd": "true", "condition": [[122, 2]], "orderId": 3, "id": 139003, "type": 139, "rewards": []}, {"isAnd": "true", "condition": [[122, 5]], "orderId": 4, "id": 139004, "type": 139, "rewards": []}, {"isAnd": "true", "condition": [[122, 3]], "orderId": 5, "id": 139005, "type": 139, "rewards": []}, {"isAnd": "true", "condition": [[122, 4]], "orderId": 6, "id": 139006, "type": 139, "rewards": []}, {"isAnd": "true", "condition": [[122, 6]], "orderId": 7, "id": 139007, "type": 139, "rewards": []}, {"isAnd": "true", "condition": [[101, 999]], "orderId": 8, "id": 139008, "type": 139, "rewards": []}, {"isAnd": "true", "condition": [[121, 28]], "orderId": 1, "id": 140001, "type": 140, "rewards": []}, {"isAnd": "true", "condition": [[101, 15]], "orderId": 1, "id": 100141, "type": 141, "rewards": []}, {"isAnd": "true", "condition": [[101, 98]], "orderId": 1, "id": 142101, "type": 142, "rewards": []}, {"isAnd": "true", "condition": [[101, 26]], "orderId": 1, "id": 142001, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[101, 30]], "orderId": 2, "id": 142002, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[101, 40]], "orderId": 3, "id": 142003, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[101, 50]], "orderId": 4, "id": 142004, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[101, 60]], "orderId": 5, "id": 142005, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[101, 70]], "orderId": 6, "id": 142006, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[101, 80]], "orderId": 7, "id": 142007, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[101, 30]], "orderId": 8, "id": 142008, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[101, 30]], "orderId": 9, "id": 142009, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[101, 40]], "orderId": 10, "id": 142010, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[101, 50]], "orderId": 11, "id": 142011, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[101, 60]], "orderId": 12, "id": 142012, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[101, 70]], "orderId": 13, "id": 142013, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[101, 80]], "orderId": 14, "id": 142014, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[101, 40]], "orderId": 15, "id": 142015, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[101, 50]], "orderId": 16, "id": 142016, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[101, 60]], "orderId": 17, "id": 142017, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[101, 70]], "orderId": 18, "id": 142018, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[101, 80]], "orderId": 19, "id": 142019, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[101, 90]], "orderId": 20, "id": 142020, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[101, 100]], "orderId": 21, "id": 142021, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[101, 50]], "orderId": 22, "id": 142022, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[101, 60]], "orderId": 23, "id": 142023, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[101, 70]], "orderId": 24, "id": 142024, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[101, 80]], "orderId": 25, "id": 142025, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[101, 90]], "orderId": 26, "id": 142026, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[101, 100]], "orderId": 27, "id": 142027, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[101, 110]], "orderId": 28, "id": 142028, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[101, 75]], "orderId": 29, "id": 142029, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[101, 80]], "orderId": 30, "id": 142030, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[101, 90]], "orderId": 31, "id": 142031, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[101, 100]], "orderId": 32, "id": 142032, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[101, 110]], "orderId": 33, "id": 142033, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[101, 120]], "orderId": 34, "id": 142034, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[101, 130]], "orderId": 35, "id": 142035, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[101, 75]], "orderId": 36, "id": 142036, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[101, 90]], "orderId": 37, "id": 142037, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[101, 100]], "orderId": 38, "id": 142038, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[101, 110]], "orderId": 39, "id": 142039, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[101, 120]], "orderId": 40, "id": 142040, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[101, 105]], "orderId": 41, "id": 142041, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[101, 110]], "orderId": 42, "id": 142042, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[101, 120]], "orderId": 43, "id": 142043, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[101, 130]], "orderId": 44, "id": 142044, "type": 0, "rewards": []}, {"isAnd": "true", "condition": [[106, 65], [101, 98]], "orderId": 1, "id": 143001, "type": 143, "rewards": []}, {"isAnd": "true", "condition": [[101, 188]], "orderId": 1, "id": 1001114, "type": 1008, "rewards": []}, {"isAnd": "true", "condition": [[101, 12]], "orderId": 1, "id": 1009001, "type": 1009, "rewards": []}, {"isAnd": "false", "condition": [[101, 95], [106, 60]], "orderId": 1, "id": 145001, "type": 145, "rewards": []}, {"isAnd": "false", "condition": [[101, 95], [106, 60]], "orderId": 2, "id": 145002, "type": 145, "rewards": []}, {"isAnd": "false", "condition": [[101, 95], [106, 60]], "orderId": 3, "id": 145003, "type": 145, "rewards": []}, {"isAnd": "false", "condition": [[101, 105], [106, 80]], "orderId": 1, "id": 146001, "type": 146, "rewards": []}, {"isAnd": "false", "condition": [[101, 105], [106, 80]], "orderId": 2, "id": 146002, "type": 146, "rewards": []}, {"isAnd": "false", "condition": [[106, 85], [101, 102]], "orderId": 1, "id": 147001, "type": 147, "rewards": []}, {"isAnd": "false", "condition": [[106, 85], [101, 102]], "orderId": 1, "id": 148001, "type": 148, "rewards": []}, {"isAnd": "true", "condition": [[101, 999]], "orderId": 1, "id": 1000112, "type": 1006, "rewards": []}, {"isAnd": "true", "condition": [[101, 999]], "orderId": 1, "id": 1000113, "type": 1007, "rewards": []}]