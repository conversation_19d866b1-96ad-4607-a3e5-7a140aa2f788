[{"rewardProb": [[10, 8000], [9, 7000], [8, 5000], [7, 4000], [6, 3000], [0, 1500]], "id": 1, "codexWeight": [[136, 1000], [137, 1000], [138, 1000], [139, 1000], [140, 1000], [141, 1000], [142, 1000], [143, 1000], [144, 1000], [145, 1000]]}, {"rewardProb": [[10, 8000], [9, 7000], [8, 5000], [7, 4000], [6, 3000], [0, 1500]], "id": 2, "codexWeight": [[136, 1000], [137, 1000], [138, 1000], [139, 1000], [140, 1000], [141, 1000], [142, 1000], [143, 1000], [144, 1000], [145, 1000]]}, {"rewardProb": [[10, 8000], [9, 7000], [8, 5000], [7, 4000], [6, 3000], [0, 1500]], "id": 3, "codexWeight": [[136, 1000], [137, 1000], [138, 1000], [139, 1000], [140, 1000], [141, 1000], [142, 1000], [143, 1000], [144, 1000], [145, 1000]]}, {"rewardProb": [[10, 8000], [9, 7000], [8, 5000], [7, 4000], [6, 3000], [0, 1500]], "id": 4, "codexWeight": [[136, 1000], [137, 1000], [138, 1000], [139, 1000], [140, 1000], [141, 1000], [142, 1000], [143, 1000], [144, 1000], [145, 1000]]}, {"rewardProb": [[10, 8000], [9, 7000], [8, 5000], [7, 4000], [6, 3000], [0, 1500]], "id": 5, "codexWeight": [[136, 1000], [137, 1000], [138, 1000], [139, 1000], [140, 1000], [141, 1000], [142, 1000], [143, 1000], [144, 1000], [145, 1000]]}, {"rewardProb": [[10, 8000], [9, 7000], [8, 5000], [7, 4000], [6, 3000], [0, 1500]], "id": 6, "codexWeight": [[136, 1000], [137, 1000], [138, 1000], [139, 1000], [140, 1000], [146, 1000], [147, 1000], [148, 1000], [149, 1000], [150, 1000]]}, {"rewardProb": [[10, 8000], [9, 7000], [8, 5000], [7, 4000], [6, 3000], [0, 1500]], "id": 7, "codexWeight": [[136, 1000], [137, 1000], [138, 1000], [139, 1000], [140, 1000], [146, 1000], [147, 1000], [148, 1000], [149, 1000], [150, 1000]]}, {"rewardProb": [[10, 8000], [9, 7000], [8, 5000], [7, 4000], [6, 3000], [0, 1500]], "id": 8, "codexWeight": [[136, 1000], [137, 1000], [138, 1000], [139, 1000], [140, 1000], [151, 1000], [152, 1000], [153, 1000], [154, 1000], [155, 1000]]}, {"rewardProb": [[10, 8000], [9, 7000], [8, 5000], [7, 4000], [6, 3000], [0, 1500]], "id": 9, "codexWeight": [[136, 1000], [137, 1000], [138, 1000], [139, 1000], [140, 1000], [151, 1000], [152, 1000], [153, 1000], [154, 1000], [155, 1000]]}, {"rewardProb": [[10, 8000], [9, 7000], [8, 5000], [7, 4000], [6, 3000], [0, 1500]], "id": 10, "codexWeight": [[141, 1000], [142, 1000], [143, 1000], [144, 1000], [145, 1000], [156, 1000], [157, 1000], [158, 1000], [159, 1000], [160, 1000]]}, {"rewardProb": [[10, 8000], [9, 7000], [8, 5000], [7, 4000], [6, 3000], [0, 1500]], "id": 11, "codexWeight": [[141, 1000], [142, 1000], [143, 1000], [144, 1000], [145, 1000], [156, 1000], [157, 1000], [158, 1000], [159, 1000], [160, 1000]]}, {"rewardProb": [[10, 8000], [9, 7000], [8, 5000], [7, 4000], [6, 3000], [0, 1500]], "id": 12, "codexWeight": [[151, 1000], [152, 1000], [153, 1000], [154, 1000], [155, 1000], [161, 1000], [162, 1000], [163, 1000], [164, 1000], [165, 1000]]}, {"rewardProb": [[10, 8000], [9, 7000], [8, 5000], [7, 4000], [6, 3000], [0, 1500]], "id": 13, "codexWeight": [[151, 1000], [152, 1000], [153, 1000], [154, 1000], [155, 1000], [161, 1000], [162, 1000], [163, 1000], [164, 1000], [165, 1000]]}, {"rewardProb": [[10, 8000], [9, 7000], [8, 5000], [7, 4000], [6, 3000], [0, 1500]], "id": 14, "codexWeight": [[156, 1000], [157, 1000], [158, 1000], [159, 1000], [160, 1000], [166, 1000], [167, 1000], [168, 1000], [169, 1000], [170, 1000]]}, {"rewardProb": [[10, 8000], [9, 7000], [8, 5000], [7, 4000], [6, 3000], [0, 1500]], "id": 15, "codexWeight": [[156, 1000], [157, 1000], [158, 1000], [159, 1000], [160, 1000], [166, 1000], [167, 1000], [168, 1000], [169, 1000], [170, 1000]]}, {"rewardProb": [[10, 8000], [9, 7000], [8, 5000], [7, 4000], [6, 3000], [0, 1500]], "id": 16, "codexWeight": [[160, 1000], [161, 1000], [162, 1000], [163, 1000], [164, 1000], [165, 1000], [166, 1000], [167, 1000], [168, 1000], [169, 1000]]}, {"rewardProb": [[10, 8000], [9, 7000], [8, 5000], [7, 4000], [6, 3000], [0, 1500]], "id": 17, "codexWeight": [[160, 1000], [161, 1000], [162, 1000], [163, 1000], [164, 1000], [165, 1000], [166, 1000], [167, 1000], [168, 1000], [169, 1000]]}, {"rewardProb": [[10, 8000], [9, 7000], [8, 5000], [7, 4000], [6, 3000], [0, 1500]], "id": 18, "codexWeight": [[165, 1000], [166, 1000], [167, 1000], [168, 1000], [169, 1000], [171, 1000], [172, 1000], [173, 1000], [174, 1000], [175, 1000]]}, {"rewardProb": [[10, 8000], [9, 7000], [8, 5000], [7, 4000], [6, 3000], [0, 1500]], "id": 19, "codexWeight": [[165, 1000], [166, 1000], [167, 1000], [168, 1000], [169, 1000], [171, 1000], [172, 1000], [173, 1000], [174, 1000], [175, 1000]]}, {"rewardProb": [[10, 8000], [9, 7000], [8, 5000], [7, 4000], [6, 3000], [0, 1500]], "id": 20, "codexWeight": [[175, 1000], [176, 1000], [177, 1000], [178, 1000], [179, 1000], [180, 1000], [181, 1000], [182, 1000], [183, 1000], [184, 1000]]}, {"rewardProb": [[10, 8000], [9, 7000], [8, 5000], [7, 4000], [6, 3000], [0, 1500]], "id": 21, "codexWeight": [[175, 1000], [176, 1000], [177, 1000], [178, 1000], [179, 1000], [180, 1000], [181, 1000], [182, 1000], [183, 1000], [184, 1000]]}, {"rewardProb": [[10, 8000], [9, 7000], [8, 5000], [7, 4000], [6, 3000], [0, 1500]], "id": 22, "codexWeight": [[185, 1000], [186, 1000], [187, 1000], [188, 1000], [189, 1000], [186, 1000], [187, 1000], [188, 1000], [189, 1000], [190, 1000]]}, {"rewardProb": [[10, 8000], [9, 7000], [8, 5000], [7, 4000], [6, 3000], [0, 1500]], "id": 23, "codexWeight": [[185, 1000], [186, 1000], [187, 1000], [188, 1000], [189, 1000], [186, 1000], [187, 1000], [188, 1000], [189, 1000], [190, 1000]]}, {"rewardProb": [[10, 8000], [9, 7000], [8, 5000], [7, 4000], [6, 3000], [0, 1500]], "id": 24, "codexWeight": [[185, 1000], [186, 1000], [187, 1000], [188, 1000], [189, 1000], [190, 1000], [191, 1000], [192, 1000], [193, 1000], [194, 1000]]}, {"rewardProb": [[10, 8000], [9, 7000], [8, 5000], [7, 4000], [6, 3000], [0, 1500]], "id": 25, "codexWeight": [[185, 1000], [186, 1000], [187, 1000], [188, 1000], [189, 1000], [190, 1000], [191, 1000], [192, 1000], [193, 1000], [194, 1000]]}, {"rewardProb": [[10, 8000], [9, 7000], [8, 5000], [7, 4000], [6, 3000], [0, 1500]], "id": 26, "codexWeight": [[195, 1000], [194, 1000], [193, 1000], [192, 1000], [191, 1000], [190, 1000], [189, 1000], [188, 1000], [187, 1000], [186, 1000]]}, {"rewardProb": [[10, 8000], [9, 7000], [8, 5000], [7, 4000], [6, 3000], [0, 1500]], "id": 27, "codexWeight": [[195, 1000], [194, 1000], [193, 1000], [192, 1000], [191, 1000], [190, 1000], [189, 1000], [188, 1000], [187, 1000], [186, 1000]]}]