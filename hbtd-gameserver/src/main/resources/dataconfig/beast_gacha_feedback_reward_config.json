[{"costs": [[10, 1, 1500]], "condition": 2, "id": 1, "type": 1, "rewards": [[11, 380030, 5], [11, 116104, 2]]}, {"costs": [[10, 1, 1500]], "condition": 4, "id": 2, "type": 1, "rewards": [[11, 380030, 5], [11, 116104, 2]]}, {"costs": [[10, 1, 1500]], "condition": 6, "id": 3, "type": 1, "rewards": [[11, 380030, 5], [11, 116104, 2]]}, {"costs": [[10, 1, 1500]], "condition": 8, "id": 4, "type": 1, "rewards": [[11, 380030, 5], [11, 116104, 2]]}, {"costs": [[10, 1, 1500]], "condition": 10, "id": 5, "type": 1, "rewards": [[11, 380030, 5], [11, 116104, 2]]}, {"costs": [[10, 1, 1500]], "condition": 15, "id": 6, "type": 1, "rewards": [[11, 380030, 5], [11, 116104, 2]]}, {"costs": [[10, 1, 1500]], "condition": 20, "id": 7, "type": 1, "rewards": [[11, 380030, 5], [11, 116104, 2]]}, {"costs": [[10, 1, 1500]], "condition": 25, "id": 8, "type": 1, "rewards": [[11, 380030, 5], [11, 116104, 2]]}, {"costs": [[10, 1, 1500]], "condition": 30, "id": 9, "type": 1, "rewards": [[11, 380030, 5], [11, 116104, 2]]}, {"costs": [[10, 1, 1500]], "condition": 35, "id": 10, "type": 1, "rewards": [[11, 380030, 5], [11, 116104, 2]]}, {"costs": [[10, 1, 1500]], "condition": 40, "id": 11, "type": 1, "rewards": [[11, 380030, 5], [11, 116104, 2]]}, {"costs": [[10, 1, 1500]], "condition": 45, "id": 12, "type": 1, "rewards": [[11, 380030, 5], [11, 116104, 2]]}, {"costs": [[10, 1, 5500]], "condition": 50, "id": 13, "type": 1, "rewards": [[11, 380030, 5], [11, 116104, 2], [11, 641317, 1]]}, {"costs": [[10, 1, 3250]], "condition": 60, "id": 14, "type": 1, "rewards": [[11, 380030, 10], [11, 116104, 5]]}, {"costs": [[10, 1, 3250]], "condition": 70, "id": 15, "type": 1, "rewards": [[11, 380030, 10], [11, 116104, 5]]}, {"costs": [[10, 1, 3250]], "condition": 80, "id": 16, "type": 1, "rewards": [[11, 380030, 10], [11, 116104, 5]]}, {"costs": [[10, 1, 3250]], "condition": 90, "id": 17, "type": 1, "rewards": [[11, 380030, 10], [11, 116104, 5]]}, {"costs": [[10, 1, 7250]], "condition": 100, "id": 18, "type": 1, "rewards": [[11, 380030, 10], [11, 116104, 5], [11, 641317, 1]]}, {"costs": [[10, 1, 6500]], "condition": 110, "id": 19, "type": 1, "rewards": [[11, 380030, 20], [11, 116104, 10]]}, {"costs": [[10, 1, 6500]], "condition": 120, "id": 20, "type": 1, "rewards": [[11, 380030, 20], [11, 116104, 10]]}, {"costs": [[10, 1, 6500]], "condition": 130, "id": 21, "type": 1, "rewards": [[11, 380030, 20], [11, 116104, 10]]}, {"costs": [[10, 1, 6500]], "condition": 140, "id": 22, "type": 1, "rewards": [[11, 380030, 20], [11, 116104, 10]]}, {"costs": [[10, 1, 10500]], "condition": 150, "id": 23, "type": 1, "rewards": [[11, 380030, 20], [11, 116104, 10], [11, 641317, 1]]}, {"costs": [], "condition": 20, "id": 24, "type": 2, "rewards": [[11, 380030, 10]]}, {"costs": [], "condition": 40, "id": 25, "type": 2, "rewards": [[11, 380030, 10]]}, {"costs": [], "condition": 60, "id": 26, "type": 2, "rewards": [[11, 380030, 10]]}, {"costs": [], "condition": 80, "id": 27, "type": 2, "rewards": [[11, 380030, 10]]}, {"costs": [], "condition": 100, "id": 28, "type": 2, "rewards": [[11, 380030, 10], [11, 25046, 1]]}, {"costs": [], "condition": 500, "id": 29, "type": 3, "rewards": [[11, 380030, 10]]}, {"costs": [], "condition": 550, "id": 30, "type": 3, "rewards": [[11, 380030, 10]]}, {"costs": [], "condition": 600, "id": 31, "type": 3, "rewards": [[11, 380030, 10]]}, {"costs": [], "condition": 700, "id": 32, "type": 3, "rewards": [[11, 380030, 20], [11, 25045, 1]]}, {"costs": [], "condition": 780, "id": 33, "type": 3, "rewards": [[11, 380030, 50], [11, 25044, 1]]}, {"costs": [], "condition": 250, "id": 34, "type": 4, "rewards": [[11, 380030, 10]]}, {"costs": [], "condition": 200, "id": 35, "type": 4, "rewards": [[11, 380030, 10]]}, {"costs": [], "condition": 180, "id": 36, "type": 4, "rewards": [[11, 380030, 10]]}, {"costs": [], "condition": 150, "id": 37, "type": 4, "rewards": [[11, 380030, 20], [11, 25043, 1]]}, {"costs": [], "condition": 120, "id": 38, "type": 4, "rewards": [[11, 380030, 50], [11, 25042, 1]]}]