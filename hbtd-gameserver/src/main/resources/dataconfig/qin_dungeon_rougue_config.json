[{"effectId": [[11, 2500], [12, 2500], [13, 2500], [14, 2500]], "floor": 3, "type": 0, "danMax": 1}, {"effectId": [[101, 2000], [102, 2000], [103, 2000], [104, 1000], [105, 1000], [106, 1000], [107, 1000]], "floor": 7, "type": 0, "danMax": 1}, {"effectId": [[11, 2500], [12, 2500], [13, 2500], [14, 2500]], "floor": 3, "type": 0, "danMax": 2}, {"effectId": [[101, 2000], [102, 2000], [103, 2000], [104, 1000], [105, 1000], [106, 1000], [107, 1000]], "floor": 7, "type": 0, "danMax": 2}, {"effectId": [[1, 1250], [2, 1250], [3, 1250], [4, 1250], [5, 1250], [6, 1250], [7, 1250], [8, 1250]], "floor": 3, "type": 0, "danMax": 3}, {"effectId": [[101, 2000], [102, 2000], [103, 2000], [104, 1000], [105, 1000], [106, 1000], [107, 1000]], "floor": 7, "type": 0, "danMax": 3}, {"effectId": [[108, 2000], [109, 2000], [110, 2000], [111, 1000], [112, 1000], [113, 1000], [114, 1000]], "floor": 11, "type": 0, "danMax": 3}, {"effectId": [[115, 2000], [116, 2000], [117, 2000], [118, 1000], [119, 1000], [120, 1000], [121, 1000]], "floor": 15, "type": 0, "danMax": 3}, {"effectId": [[9, 2500], [122, 2500], [123, 2500], [124, 2500]], "floor": 19, "type": 0, "danMax": 3}, {"effectId": [[1, 1250], [2, 1250], [3, 1250], [4, 1250], [5, 1250], [6, 1250], [7, 1250], [8, 1250]], "floor": 3, "type": 0, "danMax": 4}, {"effectId": [[101, 2000], [102, 2000], [103, 2000], [104, 1000], [105, 1000], [106, 1000], [107, 1000]], "floor": 7, "type": 0, "danMax": 4}, {"effectId": [[108, 2000], [109, 2000], [110, 2000], [111, 1000], [112, 1000], [113, 1000], [114, 1000]], "floor": 11, "type": 0, "danMax": 4}, {"effectId": [[115, 2000], [116, 2000], [117, 2000], [118, 1000], [119, 1000], [120, 1000], [121, 1000]], "floor": 15, "type": 0, "danMax": 4}, {"effectId": [[9, 2500], [122, 2500], [123, 2500], [124, 2500]], "floor": 19, "type": 0, "danMax": 4}, {"effectId": [[1, 1250], [2, 1250], [3, 1250], [4, 1250], [5, 1250], [6, 1250], [7, 1250], [8, 1250]], "floor": 3, "type": 0, "danMax": 5}, {"effectId": [[101, 2000], [102, 2000], [103, 2000], [104, 1000], [105, 1000], [106, 1000], [107, 1000]], "floor": 7, "type": 0, "danMax": 5}, {"effectId": [[108, 2000], [109, 2000], [110, 2000], [111, 1000], [112, 1000], [113, 1000], [114, 1000]], "floor": 11, "type": 0, "danMax": 5}, {"effectId": [[115, 2000], [116, 2000], [117, 2000], [118, 1000], [119, 1000], [120, 1000], [121, 1000]], "floor": 15, "type": 0, "danMax": 5}, {"effectId": [[9, 2500], [122, 2500], [123, 2500], [124, 2500]], "floor": 19, "type": 0, "danMax": 5}, {"effectId": [[1, 1250], [2, 1250], [3, 1250], [4, 1250], [5, 1250], [6, 1250], [7, 1250], [8, 1250]], "floor": 3, "type": 0, "danMax": 6}, {"effectId": [[101, 2000], [102, 2000], [103, 2000], [104, 1000], [105, 1000], [106, 1000], [107, 1000]], "floor": 7, "type": 0, "danMax": 6}, {"effectId": [[108, 2000], [109, 2000], [110, 2000], [111, 1000], [112, 1000], [113, 1000], [114, 1000]], "floor": 11, "type": 0, "danMax": 6}, {"effectId": [[115, 2000], [116, 2000], [117, 2000], [118, 1000], [119, 1000], [120, 1000], [121, 1000]], "floor": 15, "type": 0, "danMax": 6}, {"effectId": [[9, 2500], [122, 2500], [123, 2500], [124, 2500]], "floor": 19, "type": 0, "danMax": 6}, {"effectId": [[1, 1250], [2, 1250], [3, 1250], [4, 1250], [5, 1250], [6, 1250], [7, 1250], [8, 1250]], "floor": 3, "type": 0, "danMax": 7}, {"effectId": [[101, 2000], [102, 2000], [103, 2000], [104, 1000], [105, 1000], [106, 1000], [107, 1000]], "floor": 7, "type": 0, "danMax": 7}, {"effectId": [[108, 2000], [109, 2000], [110, 2000], [111, 1000], [112, 1000], [113, 1000], [114, 1000]], "floor": 11, "type": 0, "danMax": 7}, {"effectId": [[115, 2000], [116, 2000], [117, 2000], [118, 1000], [119, 1000], [120, 1000], [121, 1000]], "floor": 15, "type": 0, "danMax": 7}, {"effectId": [[9, 2500], [122, 2500], [123, 2500], [124, 2500]], "floor": 19, "type": 0, "danMax": 7}, {"effectId": [[1, 1250], [2, 1250], [3, 1250], [4, 1250], [5, 1250], [6, 1250], [7, 1250], [8, 1250]], "floor": 3, "type": 0, "danMax": 8}, {"effectId": [[101, 2000], [102, 2000], [103, 2000], [104, 1000], [105, 1000], [106, 1000], [107, 1000]], "floor": 7, "type": 0, "danMax": 8}, {"effectId": [[108, 2000], [109, 2000], [110, 2000], [111, 1000], [112, 1000], [113, 1000], [114, 1000]], "floor": 11, "type": 0, "danMax": 8}, {"effectId": [[115, 2000], [116, 2000], [117, 2000], [118, 1000], [119, 1000], [120, 1000], [121, 1000]], "floor": 15, "type": 0, "danMax": 8}, {"effectId": [[9, 2500], [122, 2500], [123, 2500], [124, 2500]], "floor": 19, "type": 0, "danMax": 8}, {"effectId": [[1, 1250], [2, 1250], [3, 1250], [4, 1250], [5, 1250], [6, 1250], [7, 1250], [8, 1250]], "floor": 3, "type": 0, "danMax": 9}, {"effectId": [[101, 2000], [102, 2000], [103, 2000], [104, 1000], [105, 1000], [106, 1000], [107, 1000]], "floor": 7, "type": 0, "danMax": 9}, {"effectId": [[108, 2000], [109, 2000], [110, 2000], [111, 1000], [112, 1000], [113, 1000], [114, 1000]], "floor": 11, "type": 0, "danMax": 9}, {"effectId": [[115, 2000], [116, 2000], [117, 2000], [118, 1000], [119, 1000], [120, 1000], [121, 1000]], "floor": 15, "type": 0, "danMax": 9}, {"effectId": [[9, 2500], [122, 2500], [123, 2500], [124, 2500]], "floor": 19, "type": 0, "danMax": 9}, {"effectId": [[1, 1250], [2, 1250], [3, 1250], [4, 1250], [5, 1250], [6, 1250], [7, 1250], [8, 1250]], "floor": 3, "type": 0, "danMax": 10}, {"effectId": [[101, 2000], [102, 2000], [103, 2000], [104, 1000], [105, 1000], [106, 1000], [107, 1000]], "floor": 7, "type": 0, "danMax": 10}, {"effectId": [[108, 2000], [109, 2000], [110, 2000], [111, 1000], [112, 1000], [113, 1000], [114, 1000]], "floor": 11, "type": 0, "danMax": 10}, {"effectId": [[115, 2000], [116, 2000], [117, 2000], [118, 1000], [119, 1000], [120, 1000], [121, 1000]], "floor": 15, "type": 0, "danMax": 10}, {"effectId": [[9, 2500], [122, 2500], [123, 2500], [124, 2500]], "floor": 19, "type": 0, "danMax": 10}, {"effectId": [[1, 1250], [2, 1250], [3, 1250], [4, 1250], [5, 1250], [6, 1250], [7, 1250], [8, 1250]], "floor": 3, "type": 0, "danMax": 11}, {"effectId": [[101, 2000], [102, 2000], [103, 2000], [104, 1000], [105, 1000], [106, 1000], [107, 1000]], "floor": 7, "type": 0, "danMax": 11}, {"effectId": [[108, 2000], [109, 2000], [110, 2000], [111, 1000], [112, 1000], [113, 1000], [114, 1000]], "floor": 11, "type": 0, "danMax": 11}, {"effectId": [[115, 2000], [116, 2000], [117, 2000], [118, 1000], [119, 1000], [120, 1000], [121, 1000]], "floor": 15, "type": 0, "danMax": 11}, {"effectId": [[9, 2500], [122, 2500], [123, 2500], [124, 2500]], "floor": 19, "type": 0, "danMax": 11}, {"effectId": [[1, 1250], [2, 1250], [3, 1250], [4, 1250], [5, 1250], [6, 1250], [7, 1250], [8, 1250]], "floor": 3, "type": 0, "danMax": 12}, {"effectId": [[101, 2000], [102, 2000], [103, 2000], [104, 1000], [105, 1000], [106, 1000], [107, 1000]], "floor": 7, "type": 0, "danMax": 12}, {"effectId": [[108, 2000], [109, 2000], [110, 2000], [111, 1000], [112, 1000], [113, 1000], [114, 1000]], "floor": 11, "type": 0, "danMax": 12}, {"effectId": [[115, 2000], [116, 2000], [117, 2000], [118, 1000], [119, 1000], [120, 1000], [121, 1000]], "floor": 15, "type": 0, "danMax": 12}, {"effectId": [[9, 2500], [122, 2500], [123, 2500], [124, 2500]], "floor": 19, "type": 0, "danMax": 12}, {"effectId": [[1, 1250], [2, 1250], [3, 1250], [4, 1250], [5, 1250], [6, 1250], [7, 1250], [8, 1250]], "floor": 3, "type": 0, "danMax": 13}, {"effectId": [[101, 2000], [102, 2000], [103, 2000], [104, 1000], [105, 1000], [106, 1000], [107, 1000]], "floor": 7, "type": 0, "danMax": 13}, {"effectId": [[108, 2000], [109, 2000], [110, 2000], [111, 1000], [112, 1000], [113, 1000], [114, 1000]], "floor": 11, "type": 0, "danMax": 13}, {"effectId": [[115, 2000], [116, 2000], [117, 2000], [118, 1000], [119, 1000], [120, 1000], [121, 1000]], "floor": 15, "type": 0, "danMax": 13}, {"effectId": [[9, 2500], [122, 2500], [123, 2500], [124, 2500]], "floor": 19, "type": 0, "danMax": 13}, {"effectId": [[1, 1250], [2, 1250], [3, 1250], [4, 1250], [5, 1250], [6, 1250], [7, 1250], [8, 1250]], "floor": 3, "type": 0, "danMax": 14}, {"effectId": [[101, 2000], [102, 2000], [103, 2000], [104, 1000], [105, 1000], [106, 1000], [107, 1000]], "floor": 7, "type": 0, "danMax": 14}, {"effectId": [[108, 2000], [109, 2000], [110, 2000], [111, 1000], [112, 1000], [113, 1000], [114, 1000]], "floor": 11, "type": 0, "danMax": 14}, {"effectId": [[115, 2000], [116, 2000], [117, 2000], [118, 1000], [119, 1000], [120, 1000], [121, 1000]], "floor": 15, "type": 0, "danMax": 14}, {"effectId": [[9, 2500], [122, 2500], [123, 2500], [124, 2500]], "floor": 19, "type": 0, "danMax": 14}, {"effectId": [[1, 1250], [2, 1250], [3, 1250], [4, 1250], [5, 1250], [6, 1250], [7, 1250], [8, 1250]], "floor": 3, "type": 0, "danMax": 15}, {"effectId": [[101, 2000], [102, 2000], [103, 2000], [104, 1000], [105, 1000], [106, 1000], [107, 1000]], "floor": 7, "type": 0, "danMax": 15}, {"effectId": [[108, 2000], [109, 2000], [110, 2000], [111, 1000], [112, 1000], [113, 1000], [114, 1000]], "floor": 11, "type": 0, "danMax": 15}, {"effectId": [[115, 2000], [116, 2000], [117, 2000], [118, 1000], [119, 1000], [120, 1000], [121, 1000]], "floor": 15, "type": 0, "danMax": 15}, {"effectId": [[9, 2500], [122, 2500], [123, 2500], [124, 2500]], "floor": 19, "type": 0, "danMax": 15}, {"effectId": [[1, 1250], [2, 1250], [3, 1250], [4, 1250], [5, 1250], [6, 1250], [7, 1250], [8, 1250]], "floor": 3, "type": 0, "danMax": 16}, {"effectId": [[101, 2000], [102, 2000], [103, 2000], [104, 1000], [105, 1000], [106, 1000], [107, 1000]], "floor": 7, "type": 0, "danMax": 16}, {"effectId": [[108, 2000], [109, 2000], [110, 2000], [111, 1000], [112, 1000], [113, 1000], [114, 1000]], "floor": 11, "type": 0, "danMax": 16}, {"effectId": [[115, 2000], [116, 2000], [117, 2000], [118, 1000], [119, 1000], [120, 1000], [121, 1000]], "floor": 15, "type": 0, "danMax": 16}, {"effectId": [[9, 2500], [122, 2500], [123, 2500], [124, 2500]], "floor": 19, "type": 0, "danMax": 16}, {"effectId": [[1, 1250], [2, 1250], [3, 1250], [4, 1250], [5, 1250], [6, 1250], [7, 1250], [8, 1250]], "floor": 3, "type": 0, "danMax": 17}, {"effectId": [[101, 2000], [102, 2000], [103, 2000], [104, 1000], [105, 1000], [106, 1000], [107, 1000]], "floor": 7, "type": 0, "danMax": 17}, {"effectId": [[108, 2000], [109, 2000], [110, 2000], [111, 1000], [112, 1000], [113, 1000], [114, 1000]], "floor": 11, "type": 0, "danMax": 17}, {"effectId": [[115, 2000], [116, 2000], [117, 2000], [118, 1000], [119, 1000], [120, 1000], [121, 1000]], "floor": 15, "type": 0, "danMax": 17}, {"effectId": [[9, 2500], [122, 2500], [123, 2500], [124, 2500]], "floor": 19, "type": 0, "danMax": 17}, {"effectId": [[1, 1250], [2, 1250], [3, 1250], [4, 1250], [5, 1250], [6, 1250], [7, 1250], [8, 1250]], "floor": 3, "type": 0, "danMax": 18}, {"effectId": [[101, 2000], [102, 2000], [103, 2000], [104, 1000], [105, 1000], [106, 1000], [107, 1000]], "floor": 7, "type": 0, "danMax": 18}, {"effectId": [[108, 2000], [109, 2000], [110, 2000], [111, 1000], [112, 1000], [113, 1000], [114, 1000]], "floor": 11, "type": 0, "danMax": 18}, {"effectId": [[115, 2000], [116, 2000], [117, 2000], [118, 1000], [119, 1000], [120, 1000], [121, 1000]], "floor": 15, "type": 0, "danMax": 18}, {"effectId": [[9, 2500], [122, 2500], [123, 2500], [124, 2500]], "floor": 19, "type": 0, "danMax": 18}, {"effectId": [[1, 1250], [2, 1250], [3, 1250], [4, 1250], [5, 1250], [6, 1250], [7, 1250], [8, 1250]], "floor": 3, "type": 0, "danMax": 19}, {"effectId": [[101, 2000], [102, 2000], [103, 2000], [104, 1000], [105, 1000], [106, 1000], [107, 1000]], "floor": 7, "type": 0, "danMax": 19}, {"effectId": [[108, 2000], [109, 2000], [110, 2000], [111, 1000], [112, 1000], [113, 1000], [114, 1000]], "floor": 11, "type": 0, "danMax": 19}, {"effectId": [[115, 2000], [116, 2000], [117, 2000], [118, 1000], [119, 1000], [120, 1000], [121, 1000]], "floor": 15, "type": 0, "danMax": 19}, {"effectId": [[9, 2500], [122, 2500], [123, 2500], [124, 2500]], "floor": 19, "type": 0, "danMax": 19}, {"effectId": [[125, 2500], [126, 2500], [127, 2500], [128, 2500]], "floor": 23, "type": 0, "danMax": 19}, {"effectId": [[125, 2500], [126, 2500], [127, 2500], [128, 2500]], "floor": 2, "type": 1, "danMax": 0}]