[{"dan": 1, "powerBase": 100000, "powerArea": [0, 30000], "scoreBouns": [0, 30000], "scorePowerBase": 150000, "danMax": 1, "danReward": [[10, 1, 20], [10, 7, 80], [11, 631101, 2]], "sweepReward": [[10, 7, 1250], [11, 114101, 177]]}, {"dan": 2, "powerBase": 100000, "powerArea": [0, 30000], "scoreBouns": [0, 30000], "scorePowerBase": 150000, "danMax": 1, "danReward": [[10, 1, 30], [10, 7, 120], [11, 631101, 2]], "sweepReward": [[10, 7, 1250], [11, 114101, 177]]}, {"dan": 3, "powerBase": 100000, "powerArea": [0, 30000], "scoreBouns": [0, 30000], "scorePowerBase": 150000, "danMax": 1, "danReward": [[10, 1, 40], [10, 7, 160], [11, 631101, 2]], "sweepReward": [[10, 7, 1250], [11, 114101, 177]]}, {"dan": 4, "powerBase": 100000, "powerArea": [0, 30000], "scoreBouns": [0, 30000], "scorePowerBase": 150000, "danMax": 1, "danReward": [[10, 1, 50], [10, 7, 200], [11, 631101, 2]], "sweepReward": [[10, 7, 1250], [11, 114101, 177]]}, {"dan": 5, "powerBase": 100000, "powerArea": [0, 30000], "scoreBouns": [0, 30000], "scorePowerBase": 150000, "danMax": 1, "danReward": [[10, 1, 60], [10, 7, 240], [11, 631101, 2]], "sweepReward": [[10, 7, 1250], [11, 114101, 177]]}, {"dan": 6, "powerBase": 100000, "powerArea": [5000, 60000], "scoreBouns": [0, 30000], "scorePowerBase": 300000, "danMax": 2, "danReward": [[10, 1, 70], [10, 7, 280], [11, 631101, 4]], "sweepReward": [[10, 7, 1650], [11, 114101, 250]]}, {"dan": 7, "powerBase": 100000, "powerArea": [5000, 60000], "scoreBouns": [0, 30000], "scorePowerBase": 300000, "danMax": 2, "danReward": [[10, 1, 80], [10, 7, 320], [11, 631101, 4]], "sweepReward": [[10, 7, 1650], [11, 114101, 250]]}, {"dan": 8, "powerBase": 100000, "powerArea": [5000, 60000], "scoreBouns": [0, 30000], "scorePowerBase": 300000, "danMax": 2, "danReward": [[10, 1, 90], [10, 7, 360], [11, 631101, 4]], "sweepReward": [[10, 7, 1650], [11, 114101, 250]]}, {"dan": 9, "powerBase": 100000, "powerArea": [5000, 60000], "scoreBouns": [0, 30000], "scorePowerBase": 300000, "danMax": 2, "danReward": [[10, 1, 100], [10, 7, 400], [11, 631101, 4]], "sweepReward": [[10, 7, 1650], [11, 114101, 250]]}, {"dan": 10, "powerBase": 100000, "powerArea": [5000, 60000], "scoreBouns": [0, 30000], "scorePowerBase": 300000, "danMax": 2, "danReward": [[10, 1, 110], [10, 7, 440], [11, 631101, 4]], "sweepReward": [[10, 7, 1650], [11, 114101, 250]]}, {"dan": 11, "powerBase": 100000, "powerArea": [15000, 120000], "scoreBouns": [0, 30000], "scorePowerBase": 600000, "danMax": 3, "danReward": [[10, 1, 120], [10, 7, 480], [11, 631201, 2]], "sweepReward": [[10, 7, 2050], [11, 114101, 322]]}, {"dan": 12, "powerBase": 100000, "powerArea": [15000, 120000], "scoreBouns": [0, 30000], "scorePowerBase": 600000, "danMax": 3, "danReward": [[10, 1, 130], [10, 7, 520], [11, 631201, 2]], "sweepReward": [[10, 7, 2050], [11, 114101, 322]]}, {"dan": 13, "powerBase": 100000, "powerArea": [15000, 120000], "scoreBouns": [0, 30000], "scorePowerBase": 600000, "danMax": 3, "danReward": [[10, 1, 140], [10, 7, 560], [11, 631201, 2]], "sweepReward": [[10, 7, 2050], [11, 114101, 322]]}, {"dan": 14, "powerBase": 100000, "powerArea": [15000, 120000], "scoreBouns": [0, 30000], "scorePowerBase": 600000, "danMax": 3, "danReward": [[10, 1, 150], [10, 7, 600], [11, 631201, 2]], "sweepReward": [[10, 7, 2050], [11, 114101, 322]]}, {"dan": 15, "powerBase": 100000, "powerArea": [15000, 120000], "scoreBouns": [0, 30000], "scorePowerBase": 600000, "danMax": 3, "danReward": [[10, 1, 160], [10, 7, 640], [11, 631201, 2]], "sweepReward": [[10, 7, 2050], [11, 114101, 322]]}, {"dan": 16, "powerBase": 100000, "powerArea": [35000, 240000], "scoreBouns": [0, 30000], "scorePowerBase": 1200000, "danMax": 4, "danReward": [[10, 1, 170], [10, 7, 680], [11, 631201, 4]], "sweepReward": [[10, 7, 2450], [11, 114101, 395]]}, {"dan": 17, "powerBase": 100000, "powerArea": [35000, 240000], "scoreBouns": [0, 30000], "scorePowerBase": 1200000, "danMax": 4, "danReward": [[10, 1, 180], [10, 7, 720], [11, 631201, 4]], "sweepReward": [[10, 7, 2450], [11, 114101, 395]]}, {"dan": 18, "powerBase": 100000, "powerArea": [35000, 240000], "scoreBouns": [0, 30000], "scorePowerBase": 1200000, "danMax": 4, "danReward": [[10, 1, 190], [10, 7, 760], [11, 631201, 4]], "sweepReward": [[10, 7, 2450], [11, 114101, 395]]}, {"dan": 19, "powerBase": 100000, "powerArea": [35000, 240000], "scoreBouns": [0, 30000], "scorePowerBase": 1200000, "danMax": 4, "danReward": [[10, 1, 200], [10, 7, 800], [11, 631201, 4]], "sweepReward": [[10, 7, 2450], [11, 114101, 395]]}, {"dan": 20, "powerBase": 100000, "powerArea": [35000, 240000], "scoreBouns": [0, 30000], "scorePowerBase": 1200000, "danMax": 4, "danReward": [[10, 1, 210], [10, 7, 840], [11, 631201, 4]], "sweepReward": [[10, 7, 2450], [11, 114101, 395]]}, {"dan": 21, "powerBase": 100000, "powerArea": [75000, 480000], "scoreBouns": [0, 20000], "scorePowerBase": 2400000, "danMax": 5, "danReward": [[10, 1, 220], [10, 7, 880], [11, 631201, 6]], "sweepReward": [[10, 7, 2850], [11, 114101, 448]]}, {"dan": 22, "powerBase": 100000, "powerArea": [75000, 480000], "scoreBouns": [0, 20000], "scorePowerBase": 2400000, "danMax": 5, "danReward": [[10, 1, 230], [10, 7, 920], [11, 631201, 6]], "sweepReward": [[10, 7, 2850], [11, 114101, 448]]}, {"dan": 23, "powerBase": 100000, "powerArea": [75000, 480000], "scoreBouns": [0, 20000], "scorePowerBase": 2400000, "danMax": 5, "danReward": [[10, 1, 240], [10, 7, 960], [11, 631201, 6]], "sweepReward": [[10, 7, 2850], [11, 114101, 448]]}, {"dan": 24, "powerBase": 100000, "powerArea": [75000, 480000], "scoreBouns": [0, 20000], "scorePowerBase": 2400000, "danMax": 5, "danReward": [[10, 1, 250], [10, 7, 1000], [11, 631201, 6]], "sweepReward": [[10, 7, 2850], [11, 114101, 448]]}, {"dan": 25, "powerBase": 100000, "powerArea": [75000, 480000], "scoreBouns": [0, 20000], "scorePowerBase": 2400000, "danMax": 5, "danReward": [[10, 1, 260], [10, 7, 1040], [11, 631201, 6]], "sweepReward": [[10, 7, 2850], [11, 114101, 448]]}, {"dan": 26, "powerBase": 100000, "powerArea": [155000, 960000], "scoreBouns": [0, 20000], "scorePowerBase": 4800000, "danMax": 6, "danReward": [[10, 1, 270], [10, 7, 1080], [11, 631201, 8]], "sweepReward": [[10, 7, 3250], [11, 114101, 520]]}, {"dan": 27, "powerBase": 100000, "powerArea": [155000, 960000], "scoreBouns": [0, 20000], "scorePowerBase": 4800000, "danMax": 6, "danReward": [[10, 1, 280], [10, 7, 1120], [11, 631201, 8]], "sweepReward": [[10, 7, 3250], [11, 114101, 520]]}, {"dan": 28, "powerBase": 100000, "powerArea": [155000, 960000], "scoreBouns": [0, 20000], "scorePowerBase": 4800000, "danMax": 6, "danReward": [[10, 1, 290], [10, 7, 1160], [11, 631201, 8]], "sweepReward": [[10, 7, 3250], [11, 114101, 520]]}, {"dan": 29, "powerBase": 100000, "powerArea": [155000, 960000], "scoreBouns": [0, 20000], "scorePowerBase": 4800000, "danMax": 6, "danReward": [[10, 1, 300], [10, 7, 1200], [11, 631201, 8]], "sweepReward": [[10, 7, 3250], [11, 114101, 520]]}, {"dan": 30, "powerBase": 100000, "powerArea": [155000, 960000], "scoreBouns": [0, 20000], "scorePowerBase": 4800000, "danMax": 6, "danReward": [[10, 1, 310], [10, 7, 1240], [11, 631201, 8]], "sweepReward": [[10, 7, 3250], [11, 114101, 520]]}, {"dan": 31, "powerBase": 100000, "powerArea": [315000, 1920000], "scoreBouns": [0, 20000], "scorePowerBase": 9600000, "danMax": 7, "danReward": [[10, 1, 320], [10, 7, 1280], [11, 631301, 4]], "sweepReward": [[10, 7, 3650], [11, 114101, 593]]}, {"dan": 32, "powerBase": 100000, "powerArea": [315000, 1920000], "scoreBouns": [0, 20000], "scorePowerBase": 9600000, "danMax": 7, "danReward": [[10, 1, 330], [10, 7, 1320], [11, 631301, 4]], "sweepReward": [[10, 7, 3650], [11, 114101, 593]]}, {"dan": 33, "powerBase": 100000, "powerArea": [315000, 1920000], "scoreBouns": [0, 20000], "scorePowerBase": 9600000, "danMax": 7, "danReward": [[10, 1, 340], [10, 7, 1360], [11, 631301, 4]], "sweepReward": [[10, 7, 3650], [11, 114101, 593]]}, {"dan": 34, "powerBase": 100000, "powerArea": [315000, 1920000], "scoreBouns": [0, 20000], "scorePowerBase": 9600000, "danMax": 7, "danReward": [[10, 1, 350], [10, 7, 1400], [11, 631301, 4]], "sweepReward": [[10, 7, 3650], [11, 114101, 593]]}, {"dan": 35, "powerBase": 100000, "powerArea": [315000, 1920000], "scoreBouns": [0, 20000], "scorePowerBase": 9600000, "danMax": 7, "danReward": [[10, 1, 360], [10, 7, 1440], [11, 631301, 4]], "sweepReward": [[10, 7, 3650], [11, 114101, 593]]}, {"dan": 36, "powerBase": 100000, "powerArea": [635000, 3840000], "scoreBouns": [0, 10000], "scorePowerBase": 19200000, "danMax": 8, "danReward": [[10, 1, 370], [10, 7, 1480], [11, 631301, 8]], "sweepReward": [[10, 7, 4050], [11, 114101, 648]]}, {"dan": 37, "powerBase": 100000, "powerArea": [635000, 3840000], "scoreBouns": [0, 10000], "scorePowerBase": 19200000, "danMax": 8, "danReward": [[10, 1, 380], [10, 7, 1520], [11, 631301, 8]], "sweepReward": [[10, 7, 4050], [11, 114101, 648]]}, {"dan": 38, "powerBase": 100000, "powerArea": [635000, 3840000], "scoreBouns": [0, 10000], "scorePowerBase": 19200000, "danMax": 8, "danReward": [[10, 1, 390], [10, 7, 1560], [11, 631301, 8]], "sweepReward": [[10, 7, 4050], [11, 114101, 648]]}, {"dan": 39, "powerBase": 100000, "powerArea": [635000, 3840000], "scoreBouns": [0, 10000], "scorePowerBase": 19200000, "danMax": 8, "danReward": [[10, 1, 400], [10, 7, 1600], [11, 631301, 8]], "sweepReward": [[10, 7, 4050], [11, 114101, 648]]}, {"dan": 40, "powerBase": 100000, "powerArea": [635000, 3840000], "scoreBouns": [0, 10000], "scorePowerBase": 19200000, "danMax": 8, "danReward": [[10, 1, 410], [10, 7, 1640], [11, 631301, 8]], "sweepReward": [[10, 7, 4050], [11, 114101, 648]]}, {"dan": 41, "powerBase": 100000, "powerArea": [1275000, 7680000], "scoreBouns": [0, 10000], "scorePowerBase": 38400000, "danMax": 9, "danReward": [[10, 1, 420], [10, 7, 1680], [11, 631301, 12]], "sweepReward": [[10, 7, 4450], [11, 114101, 715]]}, {"dan": 42, "powerBase": 100000, "powerArea": [1655000, 9960000], "scoreBouns": [0, 10000], "scorePowerBase": 49800000, "danMax": 10, "danReward": [[10, 1, 430], [10, 7, 1720], [11, 631301, 12]], "sweepReward": [[10, 7, 4450], [11, 114101, 715]]}, {"dan": 43, "powerBase": 100000, "powerArea": [2040000, 12270000], "scoreBouns": [0, 10000], "scorePowerBase": 61350000, "danMax": 11, "danReward": [[10, 1, 440], [10, 7, 1760], [11, 631301, 12]], "sweepReward": [[10, 7, 4450], [11, 114101, 715]]}, {"dan": 44, "powerBase": 100000, "powerArea": [2425000, 14580000], "scoreBouns": [0, 10000], "scorePowerBase": 72900000, "danMax": 12, "danReward": [[10, 1, 450], [10, 7, 1800], [11, 631301, 12]], "sweepReward": [[10, 7, 4450], [11, 114101, 715]]}, {"dan": 45, "powerBase": 100000, "powerArea": [2810000, 16890000], "scoreBouns": [0, 10000], "scorePowerBase": 84450000, "danMax": 13, "danReward": [[10, 1, 460], [10, 7, 1840], [11, 631301, 12]], "sweepReward": [[10, 7, 4450], [11, 114101, 715]]}, {"dan": 46, "powerBase": 100000, "powerArea": [3195000, 19200000], "scoreBouns": [0, 10000], "scorePowerBase": 96000000, "danMax": 14, "danReward": [[10, 1, 470], [10, 7, 1880], [11, 631301, 12]], "sweepReward": [[10, 7, 4450], [11, 114101, 715]]}, {"dan": 47, "powerBase": 100000, "powerArea": [3575000, 21480000], "scoreBouns": [0, 10000], "scorePowerBase": 107400000, "danMax": 15, "danReward": [[10, 1, 480], [10, 7, 1920], [11, 631301, 12]], "sweepReward": [[10, 7, 4450], [11, 114101, 715]]}, {"dan": 48, "powerBase": 100000, "powerArea": [3960000, 23790000], "scoreBouns": [0, 10000], "scorePowerBase": 118950000, "danMax": 16, "danReward": [[10, 1, 490], [10, 7, 1960], [11, 631301, 12]], "sweepReward": [[10, 7, 4450], [11, 114101, 715]]}, {"dan": 49, "powerBase": 100000, "powerArea": [4345000, 26100000], "scoreBouns": [0, 10000], "scorePowerBase": 130500000, "danMax": 17, "danReward": [[10, 1, 500], [10, 7, 2000], [11, 631301, 12]], "sweepReward": [[10, 7, 4450], [11, 114101, 715]]}, {"dan": 50, "powerBase": 100000, "powerArea": [4730000, 28410000], "scoreBouns": [0, 10000], "scorePowerBase": 142050000, "danMax": 18, "danReward": [[10, 1, 510], [10, 7, 2040], [11, 631301, 12]], "sweepReward": [[10, 7, 4450], [11, 114101, 715]]}, {"dan": 51, "powerBase": 100000, "powerArea": [5115000, 30720000], "scoreBouns": [0, 10000], "scorePowerBase": 153600000, "danMax": 19, "danReward": [[10, 1, 520], [10, 7, 2040], [11, 631301, 16]], "sweepReward": [[10, 7, 4450], [11, 114101, 800]]}]