[{"cost": [[11, 137101, 10]], "level": 1, "attributes": [[1, 2000], [2, 20000], [3, 1000], [4, 1000]], "nextLevel": 2, "id": 75001}, {"cost": [[11, 137101, 10]], "level": 2, "attributes": [[1, 4000], [2, 40000], [3, 2000], [4, 2000]], "nextLevel": 3, "id": 75001}, {"cost": [[11, 137101, 10]], "level": 3, "attributes": [[1, 6000], [2, 60000], [3, 3000], [4, 3000]], "nextLevel": 4, "id": 75001}, {"cost": [[11, 137101, 10]], "level": 4, "attributes": [[1, 8000], [2, 80000], [3, 4000], [4, 4000]], "nextLevel": 5, "id": 75001}, {"cost": [[11, 137101, 10]], "level": 5, "attributes": [[1, 10000], [2, 100000], [3, 5000], [4, 5000]], "nextLevel": 6, "id": 75001}, {"cost": [[11, 137101, 20]], "level": 6, "attributes": [[1, 12200], [2, 122000], [3, 6100], [4, 6100]], "nextLevel": 7, "id": 75001}, {"cost": [[11, 137101, 20]], "level": 7, "attributes": [[1, 14400], [2, 144000], [3, 7200], [4, 7200]], "nextLevel": 8, "id": 75001}, {"cost": [[11, 137101, 20]], "level": 8, "attributes": [[1, 16600], [2, 166000], [3, 8300], [4, 8300]], "nextLevel": 9, "id": 75001}, {"cost": [[11, 137101, 20]], "level": 9, "attributes": [[1, 18800], [2, 188000], [3, 9400], [4, 9400]], "nextLevel": 10, "id": 75001}, {"cost": [[11, 137101, 20]], "level": 10, "attributes": [[1, 21000], [2, 210000], [3, 10500], [4, 10500]], "nextLevel": 11, "id": 75001}, {"cost": [[11, 137101, 30]], "level": 11, "attributes": [[1, 23400], [2, 234000], [3, 11700], [4, 11700]], "nextLevel": 12, "id": 75001}, {"cost": [[11, 137101, 30]], "level": 12, "attributes": [[1, 25800], [2, 258000], [3, 12900], [4, 12900]], "nextLevel": 13, "id": 75001}, {"cost": [[11, 137101, 30]], "level": 13, "attributes": [[1, 28200], [2, 282000], [3, 14100], [4, 14100]], "nextLevel": 14, "id": 75001}, {"cost": [[11, 137101, 30]], "level": 14, "attributes": [[1, 30600], [2, 306000], [3, 15300], [4, 15300]], "nextLevel": 15, "id": 75001}, {"cost": [[11, 137101, 30]], "level": 15, "attributes": [[1, 33000], [2, 330000], [3, 16500], [4, 16500]], "nextLevel": 16, "id": 75001}, {"cost": [[11, 137101, 40]], "level": 16, "attributes": [[1, 35600], [2, 356000], [3, 17800], [4, 17800]], "nextLevel": 17, "id": 75001}, {"cost": [[11, 137101, 40]], "level": 17, "attributes": [[1, 38200], [2, 382000], [3, 19100], [4, 19100]], "nextLevel": 18, "id": 75001}, {"cost": [[11, 137101, 40]], "level": 18, "attributes": [[1, 40800], [2, 408000], [3, 20400], [4, 20400]], "nextLevel": 19, "id": 75001}, {"cost": [[11, 137101, 40]], "level": 19, "attributes": [[1, 43400], [2, 434000], [3, 21700], [4, 21700]], "nextLevel": 20, "id": 75001}, {"cost": [[11, 137101, 40]], "level": 20, "attributes": [[1, 46000], [2, 460000], [3, 23000], [4, 23000]], "nextLevel": 21, "id": 75001}, {"cost": [[11, 137101, 50]], "level": 21, "attributes": [[1, 48800], [2, 488000], [3, 24400], [4, 24400]], "nextLevel": 22, "id": 75001}, {"cost": [[11, 137101, 50]], "level": 22, "attributes": [[1, 51600], [2, 516000], [3, 25800], [4, 25800]], "nextLevel": 23, "id": 75001}, {"cost": [[11, 137101, 50]], "level": 23, "attributes": [[1, 54400], [2, 544000], [3, 27200], [4, 27200]], "nextLevel": 24, "id": 75001}, {"cost": [[11, 137101, 50]], "level": 24, "attributes": [[1, 57200], [2, 572000], [3, 28600], [4, 28600]], "nextLevel": 25, "id": 75001}, {"cost": [[11, 137101, 50]], "level": 25, "attributes": [[1, 60000], [2, 600000], [3, 30000], [4, 30000]], "nextLevel": 26, "id": 75001}, {"cost": [[11, 137101, 60]], "level": 26, "attributes": [[1, 63000], [2, 630000], [3, 31500], [4, 31500]], "nextLevel": 27, "id": 75001}, {"cost": [[11, 137101, 60]], "level": 27, "attributes": [[1, 66000], [2, 660000], [3, 33000], [4, 33000]], "nextLevel": 28, "id": 75001}, {"cost": [[11, 137101, 60]], "level": 28, "attributes": [[1, 69000], [2, 690000], [3, 34500], [4, 34500]], "nextLevel": 29, "id": 75001}, {"cost": [[11, 137101, 60]], "level": 29, "attributes": [[1, 72000], [2, 720000], [3, 36000], [4, 36000]], "nextLevel": 30, "id": 75001}, {"cost": [[11, 137101, 60]], "level": 30, "attributes": [[1, 75000], [2, 750000], [3, 37500], [4, 37500]], "nextLevel": 31, "id": 75001}, {"cost": [[11, 137101, 70]], "level": 31, "attributes": [[1, 78200], [2, 782000], [3, 39100], [4, 39100]], "nextLevel": 32, "id": 75001}, {"cost": [[11, 137101, 70]], "level": 32, "attributes": [[1, 81400], [2, 814000], [3, 40700], [4, 40700]], "nextLevel": 33, "id": 75001}, {"cost": [[11, 137101, 70]], "level": 33, "attributes": [[1, 84600], [2, 846000], [3, 42300], [4, 42300]], "nextLevel": 34, "id": 75001}, {"cost": [[11, 137101, 70]], "level": 34, "attributes": [[1, 87800], [2, 878000], [3, 43900], [4, 43900]], "nextLevel": 35, "id": 75001}, {"cost": [[11, 137101, 70]], "level": 35, "attributes": [[1, 91000], [2, 910000], [3, 45500], [4, 45500]], "nextLevel": 36, "id": 75001}, {"cost": [[11, 137101, 80]], "level": 36, "attributes": [[1, 94400], [2, 944000], [3, 47200], [4, 47200]], "nextLevel": 37, "id": 75001}, {"cost": [[11, 137101, 80]], "level": 37, "attributes": [[1, 97800], [2, 978000], [3, 48900], [4, 48900]], "nextLevel": 38, "id": 75001}, {"cost": [[11, 137101, 80]], "level": 38, "attributes": [[1, 101200], [2, 1012000], [3, 50600], [4, 50600]], "nextLevel": 39, "id": 75001}, {"cost": [[11, 137101, 80]], "level": 39, "attributes": [[1, 104600], [2, 1046000], [3, 52300], [4, 52300]], "nextLevel": 40, "id": 75001}, {"cost": [[11, 137101, 80]], "level": 40, "attributes": [[1, 108000], [2, 1080000], [3, 54000], [4, 54000]], "nextLevel": 41, "id": 75001}, {"cost": [[11, 137101, 90]], "level": 41, "attributes": [[1, 111600], [2, 1116000], [3, 55800], [4, 55800]], "nextLevel": 42, "id": 75001}, {"cost": [[11, 137101, 90]], "level": 42, "attributes": [[1, 115200], [2, 1152000], [3, 57600], [4, 57600]], "nextLevel": 43, "id": 75001}, {"cost": [[11, 137101, 90]], "level": 43, "attributes": [[1, 118800], [2, 1188000], [3, 59400], [4, 59400]], "nextLevel": 44, "id": 75001}, {"cost": [[11, 137101, 90]], "level": 44, "attributes": [[1, 122400], [2, 1224000], [3, 61200], [4, 61200]], "nextLevel": 45, "id": 75001}, {"cost": [[11, 137101, 90]], "level": 45, "attributes": [[1, 126000], [2, 1260000], [3, 63000], [4, 63000]], "nextLevel": 46, "id": 75001}, {"cost": [[11, 137101, 100]], "level": 46, "attributes": [[1, 129800], [2, 1298000], [3, 64900], [4, 64900]], "nextLevel": 47, "id": 75001}, {"cost": [[11, 137101, 100]], "level": 47, "attributes": [[1, 133600], [2, 1336000], [3, 66800], [4, 66800]], "nextLevel": 48, "id": 75001}, {"cost": [[11, 137101, 100]], "level": 48, "attributes": [[1, 137400], [2, 1374000], [3, 68700], [4, 68700]], "nextLevel": 49, "id": 75001}, {"cost": [[11, 137101, 100]], "level": 49, "attributes": [[1, 141200], [2, 1412000], [3, 70600], [4, 70600]], "nextLevel": 50, "id": 75001}, {"cost": [[11, 137101, 100]], "level": 50, "attributes": [[1, 145000], [2, 1450000], [3, 72500], [4, 72500]], "nextLevel": 51, "id": 75001}, {"cost": [[11, 137101, 110]], "level": 51, "attributes": [[1, 149000], [2, 1490000], [3, 74500], [4, 74500]], "nextLevel": 52, "id": 75001}, {"cost": [[11, 137101, 110]], "level": 52, "attributes": [[1, 153000], [2, 1530000], [3, 76500], [4, 76500]], "nextLevel": 53, "id": 75001}, {"cost": [[11, 137101, 110]], "level": 53, "attributes": [[1, 157000], [2, 1570000], [3, 78500], [4, 78500]], "nextLevel": 54, "id": 75001}, {"cost": [[11, 137101, 110]], "level": 54, "attributes": [[1, 161000], [2, 1610000], [3, 80500], [4, 80500]], "nextLevel": 55, "id": 75001}, {"cost": [[11, 137101, 110]], "level": 55, "attributes": [[1, 165000], [2, 1650000], [3, 82500], [4, 82500]], "nextLevel": 56, "id": 75001}, {"cost": [[11, 137101, 120]], "level": 56, "attributes": [[1, 169200], [2, 1692000], [3, 84600], [4, 84600]], "nextLevel": 57, "id": 75001}, {"cost": [[11, 137101, 120]], "level": 57, "attributes": [[1, 173400], [2, 1734000], [3, 86700], [4, 86700]], "nextLevel": 58, "id": 75001}, {"cost": [[11, 137101, 120]], "level": 58, "attributes": [[1, 177600], [2, 1776000], [3, 88800], [4, 88800]], "nextLevel": 59, "id": 75001}, {"cost": [[11, 137101, 120]], "level": 59, "attributes": [[1, 181800], [2, 1818000], [3, 90900], [4, 90900]], "nextLevel": 60, "id": 75001}, {"cost": [[11, 137101, 120]], "level": 60, "attributes": [[1, 186000], [2, 1860000], [3, 93000], [4, 93000]], "nextLevel": 61, "id": 75001}, {"cost": [[11, 137101, 130]], "level": 61, "attributes": [[1, 190400], [2, 1904000], [3, 95200], [4, 95200]], "nextLevel": 62, "id": 75001}, {"cost": [[11, 137101, 130]], "level": 62, "attributes": [[1, 194800], [2, 1948000], [3, 97400], [4, 97400]], "nextLevel": 63, "id": 75001}, {"cost": [[11, 137101, 130]], "level": 63, "attributes": [[1, 199200], [2, 1992000], [3, 99600], [4, 99600]], "nextLevel": 64, "id": 75001}, {"cost": [[11, 137101, 130]], "level": 64, "attributes": [[1, 203600], [2, 2036000], [3, 101800], [4, 101800]], "nextLevel": 65, "id": 75001}, {"cost": [[11, 137101, 130]], "level": 65, "attributes": [[1, 208000], [2, 2080000], [3, 104000], [4, 104000]], "nextLevel": 66, "id": 75001}, {"cost": [[11, 137101, 140]], "level": 66, "attributes": [[1, 212600], [2, 2126000], [3, 106300], [4, 106300]], "nextLevel": 67, "id": 75001}, {"cost": [[11, 137101, 140]], "level": 67, "attributes": [[1, 217200], [2, 2172000], [3, 108600], [4, 108600]], "nextLevel": 68, "id": 75001}, {"cost": [[11, 137101, 140]], "level": 68, "attributes": [[1, 221800], [2, 2218000], [3, 110900], [4, 110900]], "nextLevel": 69, "id": 75001}, {"cost": [[11, 137101, 140]], "level": 69, "attributes": [[1, 226400], [2, 2264000], [3, 113200], [4, 113200]], "nextLevel": 70, "id": 75001}, {"cost": [[11, 137101, 140]], "level": 70, "attributes": [[1, 231000], [2, 2310000], [3, 115500], [4, 115500]], "nextLevel": 71, "id": 75001}, {"cost": [[11, 137101, 150]], "level": 71, "attributes": [[1, 235800], [2, 2358000], [3, 117900], [4, 117900]], "nextLevel": 72, "id": 75001}, {"cost": [[11, 137101, 150]], "level": 72, "attributes": [[1, 240600], [2, 2406000], [3, 120300], [4, 120300]], "nextLevel": 73, "id": 75001}, {"cost": [[11, 137101, 150]], "level": 73, "attributes": [[1, 245400], [2, 2454000], [3, 122700], [4, 122700]], "nextLevel": 74, "id": 75001}, {"cost": [[11, 137101, 150]], "level": 74, "attributes": [[1, 250200], [2, 2502000], [3, 125100], [4, 125100]], "nextLevel": 75, "id": 75001}, {"cost": [[11, 137101, 150]], "level": 75, "attributes": [[1, 255000], [2, 2550000], [3, 127500], [4, 127500]], "nextLevel": 76, "id": 75001}, {"cost": [[11, 137101, 160]], "level": 76, "attributes": [[1, 260000], [2, 2600000], [3, 130000], [4, 130000]], "nextLevel": 77, "id": 75001}, {"cost": [[11, 137101, 160]], "level": 77, "attributes": [[1, 265000], [2, 2650000], [3, 132500], [4, 132500]], "nextLevel": 78, "id": 75001}, {"cost": [[11, 137101, 160]], "level": 78, "attributes": [[1, 270000], [2, 2700000], [3, 135000], [4, 135000]], "nextLevel": 79, "id": 75001}, {"cost": [[11, 137101, 160]], "level": 79, "attributes": [[1, 275000], [2, 2750000], [3, 137500], [4, 137500]], "nextLevel": 80, "id": 75001}, {"cost": [[11, 137101, 160]], "level": 80, "attributes": [[1, 280000], [2, 2800000], [3, 140000], [4, 140000]], "nextLevel": 81, "id": 75001}, {"cost": [[11, 137101, 170]], "level": 81, "attributes": [[1, 285200], [2, 2852000], [3, 142600], [4, 142600]], "nextLevel": 82, "id": 75001}, {"cost": [[11, 137101, 170]], "level": 82, "attributes": [[1, 290400], [2, 2904000], [3, 145200], [4, 145200]], "nextLevel": 83, "id": 75001}, {"cost": [[11, 137101, 170]], "level": 83, "attributes": [[1, 295600], [2, 2956000], [3, 147800], [4, 147800]], "nextLevel": 84, "id": 75001}, {"cost": [[11, 137101, 170]], "level": 84, "attributes": [[1, 300800], [2, 3008000], [3, 150400], [4, 150400]], "nextLevel": 85, "id": 75001}, {"cost": [[11, 137101, 170]], "level": 85, "attributes": [[1, 306000], [2, 3060000], [3, 153000], [4, 153000]], "nextLevel": 86, "id": 75001}, {"cost": [[11, 137101, 180]], "level": 86, "attributes": [[1, 311400], [2, 3114000], [3, 155700], [4, 155700]], "nextLevel": 87, "id": 75001}, {"cost": [[11, 137101, 180]], "level": 87, "attributes": [[1, 316800], [2, 3168000], [3, 158400], [4, 158400]], "nextLevel": 88, "id": 75001}, {"cost": [[11, 137101, 180]], "level": 88, "attributes": [[1, 322200], [2, 3222000], [3, 161100], [4, 161100]], "nextLevel": 89, "id": 75001}, {"cost": [[11, 137101, 180]], "level": 89, "attributes": [[1, 327600], [2, 3276000], [3, 163800], [4, 163800]], "nextLevel": 90, "id": 75001}, {"cost": [[11, 137101, 180]], "level": 90, "attributes": [[1, 333000], [2, 3330000], [3, 166500], [4, 166500]], "nextLevel": 91, "id": 75001}, {"cost": [[11, 137101, 190]], "level": 91, "attributes": [[1, 338600], [2, 3386000], [3, 169300], [4, 169300]], "nextLevel": 92, "id": 75001}, {"cost": [[11, 137101, 190]], "level": 92, "attributes": [[1, 344200], [2, 3442000], [3, 172100], [4, 172100]], "nextLevel": 93, "id": 75001}, {"cost": [[11, 137101, 190]], "level": 93, "attributes": [[1, 349800], [2, 3498000], [3, 174900], [4, 174900]], "nextLevel": 94, "id": 75001}, {"cost": [[11, 137101, 190]], "level": 94, "attributes": [[1, 355400], [2, 3554000], [3, 177700], [4, 177700]], "nextLevel": 95, "id": 75001}, {"cost": [[11, 137101, 190]], "level": 95, "attributes": [[1, 361000], [2, 3610000], [3, 180500], [4, 180500]], "nextLevel": 96, "id": 75001}, {"cost": [[11, 137101, 200]], "level": 96, "attributes": [[1, 366800], [2, 3668000], [3, 183400], [4, 183400]], "nextLevel": 97, "id": 75001}, {"cost": [[11, 137101, 200]], "level": 97, "attributes": [[1, 372600], [2, 3726000], [3, 186300], [4, 186300]], "nextLevel": 98, "id": 75001}, {"cost": [[11, 137101, 200]], "level": 98, "attributes": [[1, 378400], [2, 3784000], [3, 189200], [4, 189200]], "nextLevel": 99, "id": 75001}, {"cost": [[11, 137101, 200]], "level": 99, "attributes": [[1, 384200], [2, 3842000], [3, 192100], [4, 192100]], "nextLevel": 100, "id": 75001}, {"cost": [[11, 137101, 200]], "level": 100, "attributes": [[1, 390000], [2, 3900000], [3, 195000], [4, 195000]], "nextLevel": 101, "id": 75001}, {"cost": [[11, 137101, 210]], "level": 101, "attributes": [[1, 396000], [2, 3960000], [3, 198000], [4, 198000]], "nextLevel": 102, "id": 75001}, {"cost": [[11, 137101, 210]], "level": 102, "attributes": [[1, 402000], [2, 4020000], [3, 201000], [4, 201000]], "nextLevel": 103, "id": 75001}, {"cost": [[11, 137101, 210]], "level": 103, "attributes": [[1, 408000], [2, 4080000], [3, 204000], [4, 204000]], "nextLevel": 104, "id": 75001}, {"cost": [[11, 137101, 210]], "level": 104, "attributes": [[1, 414000], [2, 4140000], [3, 207000], [4, 207000]], "nextLevel": 105, "id": 75001}, {"cost": [[11, 137101, 210]], "level": 105, "attributes": [[1, 420000], [2, 4200000], [3, 210000], [4, 210000]], "nextLevel": 106, "id": 75001}, {"cost": [[11, 137101, 220]], "level": 106, "attributes": [[1, 426200], [2, 4262000], [3, 213100], [4, 213100]], "nextLevel": 107, "id": 75001}, {"cost": [[11, 137101, 220]], "level": 107, "attributes": [[1, 432400], [2, 4324000], [3, 216200], [4, 216200]], "nextLevel": 108, "id": 75001}, {"cost": [[11, 137101, 220]], "level": 108, "attributes": [[1, 438600], [2, 4386000], [3, 219300], [4, 219300]], "nextLevel": 109, "id": 75001}, {"cost": [[11, 137101, 220]], "level": 109, "attributes": [[1, 444800], [2, 4448000], [3, 222400], [4, 222400]], "nextLevel": 110, "id": 75001}, {"cost": [[11, 137101, 220]], "level": 110, "attributes": [[1, 451000], [2, 4510000], [3, 225500], [4, 225500]], "nextLevel": 111, "id": 75001}, {"cost": [[11, 137101, 230]], "level": 111, "attributes": [[1, 457400], [2, 4574000], [3, 228700], [4, 228700]], "nextLevel": 112, "id": 75001}, {"cost": [[11, 137101, 230]], "level": 112, "attributes": [[1, 463800], [2, 4638000], [3, 231900], [4, 231900]], "nextLevel": 113, "id": 75001}, {"cost": [[11, 137101, 230]], "level": 113, "attributes": [[1, 470200], [2, 4702000], [3, 235100], [4, 235100]], "nextLevel": 114, "id": 75001}, {"cost": [[11, 137101, 230]], "level": 114, "attributes": [[1, 476600], [2, 4766000], [3, 238300], [4, 238300]], "nextLevel": 115, "id": 75001}, {"cost": [[11, 137101, 230]], "level": 115, "attributes": [[1, 483000], [2, 4830000], [3, 241500], [4, 241500]], "nextLevel": 116, "id": 75001}, {"cost": [[11, 137101, 240]], "level": 116, "attributes": [[1, 489600], [2, 4896000], [3, 244800], [4, 244800]], "nextLevel": 117, "id": 75001}, {"cost": [[11, 137101, 240]], "level": 117, "attributes": [[1, 496200], [2, 4962000], [3, 248100], [4, 248100]], "nextLevel": 118, "id": 75001}, {"cost": [[11, 137101, 240]], "level": 118, "attributes": [[1, 502800], [2, 5028000], [3, 251400], [4, 251400]], "nextLevel": 119, "id": 75001}, {"cost": [[11, 137101, 240]], "level": 119, "attributes": [[1, 509400], [2, 5094000], [3, 254700], [4, 254700]], "nextLevel": 120, "id": 75001}, {"cost": [[11, 137101, 240]], "level": 120, "attributes": [[1, 516000], [2, 5160000], [3, 258000], [4, 258000]], "nextLevel": 121, "id": 75001}, {"cost": [[11, 137101, 250]], "level": 121, "attributes": [[1, 522800], [2, 5228000], [3, 261400], [4, 261400]], "nextLevel": 122, "id": 75001}, {"cost": [[11, 137101, 250]], "level": 122, "attributes": [[1, 529600], [2, 5296000], [3, 264800], [4, 264800]], "nextLevel": 123, "id": 75001}, {"cost": [[11, 137101, 250]], "level": 123, "attributes": [[1, 536400], [2, 5364000], [3, 268200], [4, 268200]], "nextLevel": 124, "id": 75001}, {"cost": [[11, 137101, 250]], "level": 124, "attributes": [[1, 543200], [2, 5432000], [3, 271600], [4, 271600]], "nextLevel": 125, "id": 75001}, {"cost": [[11, 137101, 250]], "level": 125, "attributes": [[1, 550000], [2, 5500000], [3, 275000], [4, 275000]], "nextLevel": 126, "id": 75001}, {"cost": [[11, 137101, 260]], "level": 126, "attributes": [[1, 557000], [2, 5570000], [3, 278500], [4, 278500]], "nextLevel": 127, "id": 75001}, {"cost": [[11, 137101, 260]], "level": 127, "attributes": [[1, 564000], [2, 5640000], [3, 282000], [4, 282000]], "nextLevel": 128, "id": 75001}, {"cost": [[11, 137101, 260]], "level": 128, "attributes": [[1, 571000], [2, 5710000], [3, 285500], [4, 285500]], "nextLevel": 129, "id": 75001}, {"cost": [[11, 137101, 260]], "level": 129, "attributes": [[1, 578000], [2, 5780000], [3, 289000], [4, 289000]], "nextLevel": 130, "id": 75001}, {"cost": [[11, 137101, 260]], "level": 130, "attributes": [[1, 585000], [2, 5850000], [3, 292500], [4, 292500]], "nextLevel": 131, "id": 75001}, {"cost": [[11, 137101, 270]], "level": 131, "attributes": [[1, 592200], [2, 5922000], [3, 296100], [4, 296100]], "nextLevel": 132, "id": 75001}, {"cost": [[11, 137101, 270]], "level": 132, "attributes": [[1, 599400], [2, 5994000], [3, 299700], [4, 299700]], "nextLevel": 133, "id": 75001}, {"cost": [[11, 137101, 270]], "level": 133, "attributes": [[1, 606600], [2, 6066000], [3, 303300], [4, 303300]], "nextLevel": 134, "id": 75001}, {"cost": [[11, 137101, 270]], "level": 134, "attributes": [[1, 613800], [2, 6138000], [3, 306900], [4, 306900]], "nextLevel": 135, "id": 75001}, {"cost": [[11, 137101, 270]], "level": 135, "attributes": [[1, 621000], [2, 6210000], [3, 310500], [4, 310500]], "nextLevel": 136, "id": 75001}, {"cost": [[11, 137101, 280]], "level": 136, "attributes": [[1, 628400], [2, 6284000], [3, 314200], [4, 314200]], "nextLevel": 137, "id": 75001}, {"cost": [[11, 137101, 280]], "level": 137, "attributes": [[1, 635800], [2, 6358000], [3, 317900], [4, 317900]], "nextLevel": 138, "id": 75001}, {"cost": [[11, 137101, 280]], "level": 138, "attributes": [[1, 643200], [2, 6432000], [3, 321600], [4, 321600]], "nextLevel": 139, "id": 75001}, {"cost": [[11, 137101, 280]], "level": 139, "attributes": [[1, 650600], [2, 6506000], [3, 325300], [4, 325300]], "nextLevel": 140, "id": 75001}, {"cost": [[11, 137101, 280]], "level": 140, "attributes": [[1, 658000], [2, 6580000], [3, 329000], [4, 329000]], "nextLevel": 141, "id": 75001}, {"cost": [[11, 137101, 290]], "level": 141, "attributes": [[1, 665600], [2, 6656000], [3, 332800], [4, 332800]], "nextLevel": 142, "id": 75001}, {"cost": [[11, 137101, 290]], "level": 142, "attributes": [[1, 673200], [2, 6732000], [3, 336600], [4, 336600]], "nextLevel": 143, "id": 75001}, {"cost": [[11, 137101, 290]], "level": 143, "attributes": [[1, 680800], [2, 6808000], [3, 340400], [4, 340400]], "nextLevel": 144, "id": 75001}, {"cost": [[11, 137101, 290]], "level": 144, "attributes": [[1, 688400], [2, 6884000], [3, 344200], [4, 344200]], "nextLevel": 145, "id": 75001}, {"cost": [[11, 137101, 290]], "level": 145, "attributes": [[1, 696000], [2, 6960000], [3, 348000], [4, 348000]], "nextLevel": 146, "id": 75001}, {"cost": [[11, 137101, 300]], "level": 146, "attributes": [[1, 703800], [2, 7038000], [3, 351900], [4, 351900]], "nextLevel": 147, "id": 75001}, {"cost": [[11, 137101, 300]], "level": 147, "attributes": [[1, 711600], [2, 7116000], [3, 355800], [4, 355800]], "nextLevel": 148, "id": 75001}, {"cost": [[11, 137101, 300]], "level": 148, "attributes": [[1, 719400], [2, 7194000], [3, 359700], [4, 359700]], "nextLevel": 149, "id": 75001}, {"cost": [[11, 137101, 300]], "level": 149, "attributes": [[1, 727200], [2, 7272000], [3, 363600], [4, 363600]], "nextLevel": 150, "id": 75001}, {"cost": [[11, 137101, 300]], "level": 150, "attributes": [[1, 735000], [2, 7350000], [3, 367500], [4, 367500]], "nextLevel": 151, "id": 75001}, {"cost": [[11, 137101, 310]], "level": 151, "attributes": [[1, 743000], [2, 7430000], [3, 371500], [4, 371500]], "nextLevel": 152, "id": 75001}, {"cost": [[11, 137101, 310]], "level": 152, "attributes": [[1, 751000], [2, 7510000], [3, 375500], [4, 375500]], "nextLevel": 153, "id": 75001}, {"cost": [[11, 137101, 310]], "level": 153, "attributes": [[1, 759000], [2, 7590000], [3, 379500], [4, 379500]], "nextLevel": 154, "id": 75001}, {"cost": [[11, 137101, 310]], "level": 154, "attributes": [[1, 767000], [2, 7670000], [3, 383500], [4, 383500]], "nextLevel": 155, "id": 75001}, {"cost": [[11, 137101, 310]], "level": 155, "attributes": [[1, 775000], [2, 7750000], [3, 387500], [4, 387500]], "nextLevel": 156, "id": 75001}, {"cost": [[11, 137101, 320]], "level": 156, "attributes": [[1, 783200], [2, 7832000], [3, 391600], [4, 391600]], "nextLevel": 157, "id": 75001}, {"cost": [[11, 137101, 320]], "level": 157, "attributes": [[1, 791400], [2, 7914000], [3, 395700], [4, 395700]], "nextLevel": 158, "id": 75001}, {"cost": [[11, 137101, 320]], "level": 158, "attributes": [[1, 799600], [2, 7996000], [3, 399800], [4, 399800]], "nextLevel": 159, "id": 75001}, {"cost": [[11, 137101, 320]], "level": 159, "attributes": [[1, 807800], [2, 8078000], [3, 403900], [4, 403900]], "nextLevel": 160, "id": 75001}, {"cost": [[11, 137101, 320]], "level": 160, "attributes": [[1, 816000], [2, 8160000], [3, 408000], [4, 408000]], "nextLevel": 161, "id": 75001}, {"cost": [[11, 137101, 330]], "level": 161, "attributes": [[1, 824400], [2, 8244000], [3, 412200], [4, 412200]], "nextLevel": 162, "id": 75001}, {"cost": [[11, 137101, 330]], "level": 162, "attributes": [[1, 832800], [2, 8328000], [3, 416400], [4, 416400]], "nextLevel": 163, "id": 75001}, {"cost": [[11, 137101, 330]], "level": 163, "attributes": [[1, 841200], [2, 8412000], [3, 420600], [4, 420600]], "nextLevel": 164, "id": 75001}, {"cost": [[11, 137101, 330]], "level": 164, "attributes": [[1, 849600], [2, 8496000], [3, 424800], [4, 424800]], "nextLevel": 165, "id": 75001}, {"cost": [[11, 137101, 330]], "level": 165, "attributes": [[1, 858000], [2, 8580000], [3, 429000], [4, 429000]], "nextLevel": 166, "id": 75001}, {"cost": [[11, 137101, 340]], "level": 166, "attributes": [[1, 866600], [2, 8666000], [3, 433300], [4, 433300]], "nextLevel": 167, "id": 75001}, {"cost": [[11, 137101, 340]], "level": 167, "attributes": [[1, 875200], [2, 8752000], [3, 437600], [4, 437600]], "nextLevel": 168, "id": 75001}, {"cost": [[11, 137101, 340]], "level": 168, "attributes": [[1, 883800], [2, 8838000], [3, 441900], [4, 441900]], "nextLevel": 169, "id": 75001}, {"cost": [[11, 137101, 340]], "level": 169, "attributes": [[1, 892400], [2, 8924000], [3, 446200], [4, 446200]], "nextLevel": 170, "id": 75001}, {"cost": [[11, 137101, 340]], "level": 170, "attributes": [[1, 901000], [2, 9010000], [3, 450500], [4, 450500]], "nextLevel": 171, "id": 75001}, {"cost": [[11, 137101, 350]], "level": 171, "attributes": [[1, 909800], [2, 9098000], [3, 454900], [4, 454900]], "nextLevel": 172, "id": 75001}, {"cost": [[11, 137101, 350]], "level": 172, "attributes": [[1, 918600], [2, 9186000], [3, 459300], [4, 459300]], "nextLevel": 173, "id": 75001}, {"cost": [[11, 137101, 350]], "level": 173, "attributes": [[1, 927400], [2, 9274000], [3, 463700], [4, 463700]], "nextLevel": 174, "id": 75001}, {"cost": [[11, 137101, 350]], "level": 174, "attributes": [[1, 936200], [2, 9362000], [3, 468100], [4, 468100]], "nextLevel": 175, "id": 75001}, {"cost": [[11, 137101, 350]], "level": 175, "attributes": [[1, 945000], [2, 9450000], [3, 472500], [4, 472500]], "nextLevel": 176, "id": 75001}, {"cost": [[11, 137101, 360]], "level": 176, "attributes": [[1, 954000], [2, 9540000], [3, 477000], [4, 477000]], "nextLevel": 177, "id": 75001}, {"cost": [[11, 137101, 360]], "level": 177, "attributes": [[1, 963000], [2, 9630000], [3, 481500], [4, 481500]], "nextLevel": 178, "id": 75001}, {"cost": [[11, 137101, 360]], "level": 178, "attributes": [[1, 972000], [2, 9720000], [3, 486000], [4, 486000]], "nextLevel": 179, "id": 75001}, {"cost": [[11, 137101, 360]], "level": 179, "attributes": [[1, 981000], [2, 9810000], [3, 490500], [4, 490500]], "nextLevel": 180, "id": 75001}, {"cost": [[11, 137101, 360]], "level": 180, "attributes": [[1, 990000], [2, 9900000], [3, 495000], [4, 495000]], "nextLevel": 181, "id": 75001}, {"cost": [[11, 137101, 370]], "level": 181, "attributes": [[1, 999200], [2, 9992000], [3, 499600], [4, 499600]], "nextLevel": 182, "id": 75001}, {"cost": [[11, 137101, 370]], "level": 182, "attributes": [[1, 1008400], [2, 10084000], [3, 504200], [4, 504200]], "nextLevel": 183, "id": 75001}, {"cost": [[11, 137101, 370]], "level": 183, "attributes": [[1, 1017600], [2, 10176000], [3, 508800], [4, 508800]], "nextLevel": 184, "id": 75001}, {"cost": [[11, 137101, 370]], "level": 184, "attributes": [[1, 1026800], [2, 10268000], [3, 513400], [4, 513400]], "nextLevel": 185, "id": 75001}, {"cost": [[11, 137101, 370]], "level": 185, "attributes": [[1, 1036000], [2, 10360000], [3, 518000], [4, 518000]], "nextLevel": 186, "id": 75001}, {"cost": [[11, 137101, 380]], "level": 186, "attributes": [[1, 1045400], [2, 10454000], [3, 522700], [4, 522700]], "nextLevel": 187, "id": 75001}, {"cost": [[11, 137101, 380]], "level": 187, "attributes": [[1, 1054800], [2, 10548000], [3, 527400], [4, 527400]], "nextLevel": 188, "id": 75001}, {"cost": [[11, 137101, 380]], "level": 188, "attributes": [[1, 1064200], [2, 10642000], [3, 532100], [4, 532100]], "nextLevel": 189, "id": 75001}, {"cost": [[11, 137101, 380]], "level": 189, "attributes": [[1, 1073600], [2, 10736000], [3, 536800], [4, 536800]], "nextLevel": 190, "id": 75001}, {"cost": [[11, 137101, 380]], "level": 190, "attributes": [[1, 1083000], [2, 10830000], [3, 541500], [4, 541500]], "nextLevel": 191, "id": 75001}, {"cost": [[11, 137101, 390]], "level": 191, "attributes": [[1, 1092600], [2, 10926000], [3, 546300], [4, 546300]], "nextLevel": 192, "id": 75001}, {"cost": [[11, 137101, 390]], "level": 192, "attributes": [[1, 1102200], [2, 11022000], [3, 551100], [4, 551100]], "nextLevel": 193, "id": 75001}, {"cost": [[11, 137101, 390]], "level": 193, "attributes": [[1, 1111800], [2, 11118000], [3, 555900], [4, 555900]], "nextLevel": 194, "id": 75001}, {"cost": [[11, 137101, 390]], "level": 194, "attributes": [[1, 1121400], [2, 11214000], [3, 560700], [4, 560700]], "nextLevel": 195, "id": 75001}, {"cost": [[11, 137101, 390]], "level": 195, "attributes": [[1, 1131000], [2, 11310000], [3, 565500], [4, 565500]], "nextLevel": 196, "id": 75001}, {"cost": [[11, 137101, 400]], "level": 196, "attributes": [[1, 1140800], [2, 11408000], [3, 570400], [4, 570400]], "nextLevel": 197, "id": 75001}, {"cost": [[11, 137101, 400]], "level": 197, "attributes": [[1, 1150600], [2, 11506000], [3, 575300], [4, 575300]], "nextLevel": 198, "id": 75001}, {"cost": [[11, 137101, 400]], "level": 198, "attributes": [[1, 1160400], [2, 11604000], [3, 580200], [4, 580200]], "nextLevel": 199, "id": 75001}, {"cost": [[11, 137101, 400]], "level": 199, "attributes": [[1, 1170200], [2, 11702000], [3, 585100], [4, 585100]], "nextLevel": 200, "id": 75001}, {"cost": [[11, 137101, 400]], "level": 200, "attributes": [[1, 1180000], [2, 11800000], [3, 590000], [4, 590000]], "nextLevel": 201, "id": 75001}, {"cost": [[11, 137101, 410]], "level": 201, "attributes": [[1, 1190000], [2, 11900000], [3, 595000], [4, 595000]], "nextLevel": 202, "id": 75001}, {"cost": [[11, 137101, 410]], "level": 202, "attributes": [[1, 1200000], [2, 12000000], [3, 600000], [4, 600000]], "nextLevel": 203, "id": 75001}, {"cost": [[11, 137101, 410]], "level": 203, "attributes": [[1, 1210000], [2, 12100000], [3, 605000], [4, 605000]], "nextLevel": 204, "id": 75001}, {"cost": [[11, 137101, 410]], "level": 204, "attributes": [[1, 1220000], [2, 12200000], [3, 610000], [4, 610000]], "nextLevel": 205, "id": 75001}, {"cost": [[11, 137101, 410]], "level": 205, "attributes": [[1, 1230000], [2, 12300000], [3, 615000], [4, 615000]], "nextLevel": 206, "id": 75001}, {"cost": [[11, 137101, 420]], "level": 206, "attributes": [[1, 1240200], [2, 12402000], [3, 620100], [4, 620100]], "nextLevel": 207, "id": 75001}, {"cost": [[11, 137101, 420]], "level": 207, "attributes": [[1, 1250400], [2, 12504000], [3, 625200], [4, 625200]], "nextLevel": 208, "id": 75001}, {"cost": [[11, 137101, 420]], "level": 208, "attributes": [[1, 1260600], [2, 12606000], [3, 630300], [4, 630300]], "nextLevel": 209, "id": 75001}, {"cost": [[11, 137101, 420]], "level": 209, "attributes": [[1, 1270800], [2, 12708000], [3, 635400], [4, 635400]], "nextLevel": 210, "id": 75001}, {"cost": [[11, 137101, 420]], "level": 210, "attributes": [[1, 1281000], [2, 12810000], [3, 640500], [4, 640500]], "nextLevel": 211, "id": 75001}, {"cost": [[11, 137101, 430]], "level": 211, "attributes": [[1, 1291400], [2, 12914000], [3, 645700], [4, 645700]], "nextLevel": 212, "id": 75001}, {"cost": [[11, 137101, 430]], "level": 212, "attributes": [[1, 1301800], [2, 13018000], [3, 650900], [4, 650900]], "nextLevel": 213, "id": 75001}, {"cost": [[11, 137101, 430]], "level": 213, "attributes": [[1, 1312200], [2, 13122000], [3, 656100], [4, 656100]], "nextLevel": 214, "id": 75001}, {"cost": [[11, 137101, 430]], "level": 214, "attributes": [[1, 1322600], [2, 13226000], [3, 661300], [4, 661300]], "nextLevel": 215, "id": 75001}, {"cost": [[11, 137101, 430]], "level": 215, "attributes": [[1, 1333000], [2, 13330000], [3, 666500], [4, 666500]], "nextLevel": 216, "id": 75001}, {"cost": [[11, 137101, 440]], "level": 216, "attributes": [[1, 1343600], [2, 13436000], [3, 671800], [4, 671800]], "nextLevel": 217, "id": 75001}, {"cost": [[11, 137101, 440]], "level": 217, "attributes": [[1, 1354200], [2, 13542000], [3, 677100], [4, 677100]], "nextLevel": 218, "id": 75001}, {"cost": [[11, 137101, 440]], "level": 218, "attributes": [[1, 1364800], [2, 13648000], [3, 682400], [4, 682400]], "nextLevel": 219, "id": 75001}, {"cost": [[11, 137101, 440]], "level": 219, "attributes": [[1, 1375400], [2, 13754000], [3, 687700], [4, 687700]], "nextLevel": 220, "id": 75001}, {"cost": [[11, 137101, 440]], "level": 220, "attributes": [[1, 1386000], [2, 13860000], [3, 693000], [4, 693000]], "nextLevel": 221, "id": 75001}, {"cost": [[11, 137101, 450]], "level": 221, "attributes": [[1, 1396800], [2, 13968000], [3, 698400], [4, 698400]], "nextLevel": 222, "id": 75001}, {"cost": [[11, 137101, 450]], "level": 222, "attributes": [[1, 1407600], [2, 14076000], [3, 703800], [4, 703800]], "nextLevel": 223, "id": 75001}, {"cost": [[11, 137101, 450]], "level": 223, "attributes": [[1, 1418400], [2, 14184000], [3, 709200], [4, 709200]], "nextLevel": 224, "id": 75001}, {"cost": [[11, 137101, 450]], "level": 224, "attributes": [[1, 1429200], [2, 14292000], [3, 714600], [4, 714600]], "nextLevel": 225, "id": 75001}, {"cost": [[11, 137101, 450]], "level": 225, "attributes": [[1, 1440000], [2, 14400000], [3, 720000], [4, 720000]], "nextLevel": 226, "id": 75001}, {"cost": [[11, 137101, 460]], "level": 226, "attributes": [[1, 1451000], [2, 14510000], [3, 725500], [4, 725500]], "nextLevel": 227, "id": 75001}, {"cost": [[11, 137101, 460]], "level": 227, "attributes": [[1, 1462000], [2, 14620000], [3, 731000], [4, 731000]], "nextLevel": 228, "id": 75001}, {"cost": [[11, 137101, 460]], "level": 228, "attributes": [[1, 1473000], [2, 14730000], [3, 736500], [4, 736500]], "nextLevel": 229, "id": 75001}, {"cost": [[11, 137101, 460]], "level": 229, "attributes": [[1, 1484000], [2, 14840000], [3, 742000], [4, 742000]], "nextLevel": 230, "id": 75001}, {"cost": [[11, 137101, 460]], "level": 230, "attributes": [[1, 1495000], [2, 14950000], [3, 747500], [4, 747500]], "nextLevel": 231, "id": 75001}, {"cost": [[11, 137101, 470]], "level": 231, "attributes": [[1, 1506200], [2, 15062000], [3, 753100], [4, 753100]], "nextLevel": 232, "id": 75001}, {"cost": [[11, 137101, 470]], "level": 232, "attributes": [[1, 1517400], [2, 15174000], [3, 758700], [4, 758700]], "nextLevel": 233, "id": 75001}, {"cost": [[11, 137101, 470]], "level": 233, "attributes": [[1, 1528600], [2, 15286000], [3, 764300], [4, 764300]], "nextLevel": 234, "id": 75001}, {"cost": [[11, 137101, 470]], "level": 234, "attributes": [[1, 1539800], [2, 15398000], [3, 769900], [4, 769900]], "nextLevel": 235, "id": 75001}, {"cost": [[11, 137101, 470]], "level": 235, "attributes": [[1, 1551000], [2, 15510000], [3, 775500], [4, 775500]], "nextLevel": 236, "id": 75001}, {"cost": [[11, 137101, 480]], "level": 236, "attributes": [[1, 1562400], [2, 15624000], [3, 781200], [4, 781200]], "nextLevel": 237, "id": 75001}, {"cost": [[11, 137101, 480]], "level": 237, "attributes": [[1, 1573800], [2, 15738000], [3, 786900], [4, 786900]], "nextLevel": 238, "id": 75001}, {"cost": [[11, 137101, 480]], "level": 238, "attributes": [[1, 1585200], [2, 15852000], [3, 792600], [4, 792600]], "nextLevel": 239, "id": 75001}, {"cost": [[11, 137101, 480]], "level": 239, "attributes": [[1, 1596600], [2, 15966000], [3, 798300], [4, 798300]], "nextLevel": 240, "id": 75001}, {"cost": [[11, 137101, 480]], "level": 240, "attributes": [[1, 1608000], [2, 16080000], [3, 804000], [4, 804000]], "nextLevel": 241, "id": 75001}, {"cost": [[11, 137101, 490]], "level": 241, "attributes": [[1, 1619600], [2, 16196000], [3, 809800], [4, 809800]], "nextLevel": 242, "id": 75001}, {"cost": [[11, 137101, 490]], "level": 242, "attributes": [[1, 1631200], [2, 16312000], [3, 815600], [4, 815600]], "nextLevel": 243, "id": 75001}, {"cost": [[11, 137101, 490]], "level": 243, "attributes": [[1, 1642800], [2, 16428000], [3, 821400], [4, 821400]], "nextLevel": 244, "id": 75001}, {"cost": [[11, 137101, 490]], "level": 244, "attributes": [[1, 1654400], [2, 16544000], [3, 827200], [4, 827200]], "nextLevel": 245, "id": 75001}, {"cost": [[11, 137101, 490]], "level": 245, "attributes": [[1, 1666000], [2, 16660000], [3, 833000], [4, 833000]], "nextLevel": 246, "id": 75001}, {"cost": [[11, 137101, 500]], "level": 246, "attributes": [[1, 1677800], [2, 16778000], [3, 838900], [4, 838900]], "nextLevel": 247, "id": 75001}, {"cost": [[11, 137101, 500]], "level": 247, "attributes": [[1, 1689600], [2, 16896000], [3, 844800], [4, 844800]], "nextLevel": 248, "id": 75001}, {"cost": [[11, 137101, 500]], "level": 248, "attributes": [[1, 1701400], [2, 17014000], [3, 850700], [4, 850700]], "nextLevel": 249, "id": 75001}, {"cost": [[11, 137101, 500]], "level": 249, "attributes": [[1, 1713200], [2, 17132000], [3, 856600], [4, 856600]], "nextLevel": 250, "id": 75001}, {"cost": [[11, 137101, 500]], "level": 250, "attributes": [[1, 1725000], [2, 17250000], [3, 862500], [4, 862500]], "nextLevel": 251, "id": 75001}, {"cost": [[11, 137101, 510]], "level": 251, "attributes": [[1, 1737000], [2, 17370000], [3, 868500], [4, 868500]], "nextLevel": 252, "id": 75001}, {"cost": [[11, 137101, 510]], "level": 252, "attributes": [[1, 1749000], [2, 17490000], [3, 874500], [4, 874500]], "nextLevel": 253, "id": 75001}, {"cost": [[11, 137101, 510]], "level": 253, "attributes": [[1, 1761000], [2, 17610000], [3, 880500], [4, 880500]], "nextLevel": 254, "id": 75001}, {"cost": [[11, 137101, 510]], "level": 254, "attributes": [[1, 1773000], [2, 17730000], [3, 886500], [4, 886500]], "nextLevel": 255, "id": 75001}, {"cost": [[11, 137101, 510]], "level": 255, "attributes": [[1, 1785000], [2, 17850000], [3, 892500], [4, 892500]], "nextLevel": 256, "id": 75001}, {"cost": [[11, 137101, 520]], "level": 256, "attributes": [[1, 1797200], [2, 17972000], [3, 898600], [4, 898600]], "nextLevel": 257, "id": 75001}, {"cost": [[11, 137101, 520]], "level": 257, "attributes": [[1, 1809400], [2, 18094000], [3, 904700], [4, 904700]], "nextLevel": 258, "id": 75001}, {"cost": [[11, 137101, 520]], "level": 258, "attributes": [[1, 1821600], [2, 18216000], [3, 910800], [4, 910800]], "nextLevel": 259, "id": 75001}, {"cost": [[11, 137101, 520]], "level": 259, "attributes": [[1, 1833800], [2, 18338000], [3, 916900], [4, 916900]], "nextLevel": 260, "id": 75001}, {"cost": [[11, 137101, 520]], "level": 260, "attributes": [[1, 1846000], [2, 18460000], [3, 923000], [4, 923000]], "nextLevel": 261, "id": 75001}, {"cost": [[11, 137101, 530]], "level": 261, "attributes": [[1, 1858400], [2, 18584000], [3, 929200], [4, 929200]], "nextLevel": 262, "id": 75001}, {"cost": [[11, 137101, 530]], "level": 262, "attributes": [[1, 1870800], [2, 18708000], [3, 935400], [4, 935400]], "nextLevel": 263, "id": 75001}, {"cost": [[11, 137101, 530]], "level": 263, "attributes": [[1, 1883200], [2, 18832000], [3, 941600], [4, 941600]], "nextLevel": 264, "id": 75001}, {"cost": [[11, 137101, 530]], "level": 264, "attributes": [[1, 1895600], [2, 18956000], [3, 947800], [4, 947800]], "nextLevel": 265, "id": 75001}, {"cost": [[11, 137101, 530]], "level": 265, "attributes": [[1, 1908000], [2, 19080000], [3, 954000], [4, 954000]], "nextLevel": 266, "id": 75001}, {"cost": [[11, 137101, 540]], "level": 266, "attributes": [[1, 1920600], [2, 19206000], [3, 960300], [4, 960300]], "nextLevel": 267, "id": 75001}, {"cost": [[11, 137101, 540]], "level": 267, "attributes": [[1, 1933200], [2, 19332000], [3, 966600], [4, 966600]], "nextLevel": 268, "id": 75001}, {"cost": [[11, 137101, 540]], "level": 268, "attributes": [[1, 1945800], [2, 19458000], [3, 972900], [4, 972900]], "nextLevel": 269, "id": 75001}, {"cost": [[11, 137101, 540]], "level": 269, "attributes": [[1, 1958400], [2, 19584000], [3, 979200], [4, 979200]], "nextLevel": 270, "id": 75001}, {"cost": [[11, 137101, 540]], "level": 270, "attributes": [[1, 1971000], [2, 19710000], [3, 985500], [4, 985500]], "nextLevel": 271, "id": 75001}, {"cost": [[11, 137101, 550]], "level": 271, "attributes": [[1, 1983800], [2, 19838000], [3, 991900], [4, 991900]], "nextLevel": 272, "id": 75001}, {"cost": [[11, 137101, 550]], "level": 272, "attributes": [[1, 1996600], [2, 19966000], [3, 998300], [4, 998300]], "nextLevel": 273, "id": 75001}, {"cost": [[11, 137101, 550]], "level": 273, "attributes": [[1, 2009400], [2, 20094000], [3, 1004700], [4, 1004700]], "nextLevel": 274, "id": 75001}, {"cost": [[11, 137101, 550]], "level": 274, "attributes": [[1, 2022200], [2, 20222000], [3, 1011100], [4, 1011100]], "nextLevel": 275, "id": 75001}, {"cost": [[11, 137101, 550]], "level": 275, "attributes": [[1, 2035000], [2, 20350000], [3, 1017500], [4, 1017500]], "nextLevel": 276, "id": 75001}, {"cost": [[11, 137101, 560]], "level": 276, "attributes": [[1, 2048000], [2, 20480000], [3, 1024000], [4, 1024000]], "nextLevel": 277, "id": 75001}, {"cost": [[11, 137101, 560]], "level": 277, "attributes": [[1, 2061000], [2, 20610000], [3, 1030500], [4, 1030500]], "nextLevel": 278, "id": 75001}, {"cost": [[11, 137101, 560]], "level": 278, "attributes": [[1, 2074000], [2, 20740000], [3, 1037000], [4, 1037000]], "nextLevel": 279, "id": 75001}, {"cost": [[11, 137101, 560]], "level": 279, "attributes": [[1, 2087000], [2, 20870000], [3, 1043500], [4, 1043500]], "nextLevel": 280, "id": 75001}, {"cost": [[11, 137101, 560]], "level": 280, "attributes": [[1, 2100000], [2, 21000000], [3, 1050000], [4, 1050000]], "nextLevel": 281, "id": 75001}, {"cost": [[11, 137101, 570]], "level": 281, "attributes": [[1, 2113200], [2, 21132000], [3, 1056600], [4, 1056600]], "nextLevel": 282, "id": 75001}, {"cost": [[11, 137101, 570]], "level": 282, "attributes": [[1, 2126400], [2, 21264000], [3, 1063200], [4, 1063200]], "nextLevel": 283, "id": 75001}, {"cost": [[11, 137101, 570]], "level": 283, "attributes": [[1, 2139600], [2, 21396000], [3, 1069800], [4, 1069800]], "nextLevel": 284, "id": 75001}, {"cost": [[11, 137101, 570]], "level": 284, "attributes": [[1, 2152800], [2, 21528000], [3, 1076400], [4, 1076400]], "nextLevel": 285, "id": 75001}, {"cost": [[11, 137101, 570]], "level": 285, "attributes": [[1, 2166000], [2, 21660000], [3, 1083000], [4, 1083000]], "nextLevel": 286, "id": 75001}, {"cost": [[11, 137101, 580]], "level": 286, "attributes": [[1, 2179400], [2, 21794000], [3, 1089700], [4, 1089700]], "nextLevel": 287, "id": 75001}, {"cost": [[11, 137101, 580]], "level": 287, "attributes": [[1, 2192800], [2, 21928000], [3, 1096400], [4, 1096400]], "nextLevel": 288, "id": 75001}, {"cost": [[11, 137101, 580]], "level": 288, "attributes": [[1, 2206200], [2, 22062000], [3, 1103100], [4, 1103100]], "nextLevel": 289, "id": 75001}, {"cost": [[11, 137101, 580]], "level": 289, "attributes": [[1, 2219600], [2, 22196000], [3, 1109800], [4, 1109800]], "nextLevel": 290, "id": 75001}, {"cost": [[11, 137101, 580]], "level": 290, "attributes": [[1, 2233000], [2, 22330000], [3, 1116500], [4, 1116500]], "nextLevel": 291, "id": 75001}, {"cost": [[11, 137101, 590]], "level": 291, "attributes": [[1, 2246600], [2, 22466000], [3, 1123300], [4, 1123300]], "nextLevel": 292, "id": 75001}, {"cost": [[11, 137101, 590]], "level": 292, "attributes": [[1, 2260200], [2, 22602000], [3, 1130100], [4, 1130100]], "nextLevel": 293, "id": 75001}, {"cost": [[11, 137101, 590]], "level": 293, "attributes": [[1, 2273800], [2, 22738000], [3, 1136900], [4, 1136900]], "nextLevel": 294, "id": 75001}, {"cost": [[11, 137101, 590]], "level": 294, "attributes": [[1, 2287400], [2, 22874000], [3, 1143700], [4, 1143700]], "nextLevel": 295, "id": 75001}, {"cost": [[11, 137101, 590]], "level": 295, "attributes": [[1, 2301000], [2, 23010000], [3, 1150500], [4, 1150500]], "nextLevel": 296, "id": 75001}, {"cost": [[11, 137101, 600]], "level": 296, "attributes": [[1, 2314800], [2, 23148000], [3, 1157400], [4, 1157400]], "nextLevel": 297, "id": 75001}, {"cost": [[11, 137101, 600]], "level": 297, "attributes": [[1, 2328600], [2, 23286000], [3, 1164300], [4, 1164300]], "nextLevel": 298, "id": 75001}, {"cost": [[11, 137101, 600]], "level": 298, "attributes": [[1, 2342400], [2, 23424000], [3, 1171200], [4, 1171200]], "nextLevel": 299, "id": 75001}, {"cost": [[11, 137101, 600]], "level": 299, "attributes": [[1, 2356200], [2, 23562000], [3, 1178100], [4, 1178100]], "nextLevel": 300, "id": 75001}, {"cost": [[11, 137101, 600]], "level": 300, "attributes": [[1, 2370000], [2, 23700000], [3, 1185000], [4, 1185000]], "nextLevel": 301, "id": 75001}, {"cost": [[11, 137101, 610]], "level": 301, "attributes": [[1, 2384000], [2, 23840000], [3, 1192000], [4, 1192000]], "nextLevel": 302, "id": 75001}, {"cost": [[11, 137101, 610]], "level": 302, "attributes": [[1, 2398000], [2, 23980000], [3, 1199000], [4, 1199000]], "nextLevel": 303, "id": 75001}, {"cost": [[11, 137101, 610]], "level": 303, "attributes": [[1, 2412000], [2, 24120000], [3, 1206000], [4, 1206000]], "nextLevel": 304, "id": 75001}, {"cost": [[11, 137101, 610]], "level": 304, "attributes": [[1, 2426000], [2, 24260000], [3, 1213000], [4, 1213000]], "nextLevel": 305, "id": 75001}, {"cost": [[11, 137101, 610]], "level": 305, "attributes": [[1, 2440000], [2, 24400000], [3, 1220000], [4, 1220000]], "nextLevel": 306, "id": 75001}, {"cost": [[11, 137101, 620]], "level": 306, "attributes": [[1, 2454200], [2, 24542000], [3, 1227100], [4, 1227100]], "nextLevel": 307, "id": 75001}, {"cost": [[11, 137101, 620]], "level": 307, "attributes": [[1, 2468400], [2, 24684000], [3, 1234200], [4, 1234200]], "nextLevel": 308, "id": 75001}, {"cost": [[11, 137101, 620]], "level": 308, "attributes": [[1, 2482600], [2, 24826000], [3, 1241300], [4, 1241300]], "nextLevel": 309, "id": 75001}, {"cost": [[11, 137101, 620]], "level": 309, "attributes": [[1, 2496800], [2, 24968000], [3, 1248400], [4, 1248400]], "nextLevel": 310, "id": 75001}, {"cost": [[11, 137101, 620]], "level": 310, "attributes": [[1, 2511000], [2, 25110000], [3, 1255500], [4, 1255500]], "nextLevel": 311, "id": 75001}, {"cost": [[11, 137101, 630]], "level": 311, "attributes": [[1, 2525400], [2, 25254000], [3, 1262700], [4, 1262700]], "nextLevel": 312, "id": 75001}, {"cost": [[11, 137101, 630]], "level": 312, "attributes": [[1, 2539800], [2, 25398000], [3, 1269900], [4, 1269900]], "nextLevel": 313, "id": 75001}, {"cost": [[11, 137101, 630]], "level": 313, "attributes": [[1, 2554200], [2, 25542000], [3, 1277100], [4, 1277100]], "nextLevel": 314, "id": 75001}, {"cost": [[11, 137101, 630]], "level": 314, "attributes": [[1, 2568600], [2, 25686000], [3, 1284300], [4, 1284300]], "nextLevel": 315, "id": 75001}, {"cost": [[11, 137101, 630]], "level": 315, "attributes": [[1, 2583000], [2, 25830000], [3, 1291500], [4, 1291500]], "nextLevel": 316, "id": 75001}, {"cost": [[11, 137101, 640]], "level": 316, "attributes": [[1, 2597600], [2, 25976000], [3, 1298800], [4, 1298800]], "nextLevel": 317, "id": 75001}, {"cost": [[11, 137101, 640]], "level": 317, "attributes": [[1, 2612200], [2, 26122000], [3, 1306100], [4, 1306100]], "nextLevel": 318, "id": 75001}, {"cost": [[11, 137101, 640]], "level": 318, "attributes": [[1, 2626800], [2, 26268000], [3, 1313400], [4, 1313400]], "nextLevel": 319, "id": 75001}, {"cost": [[11, 137101, 640]], "level": 319, "attributes": [[1, 2641400], [2, 26414000], [3, 1320700], [4, 1320700]], "nextLevel": 320, "id": 75001}, {"cost": [[11, 137101, 640]], "level": 320, "attributes": [[1, 2656000], [2, 26560000], [3, 1328000], [4, 1328000]], "nextLevel": 321, "id": 75001}, {"cost": [[11, 137101, 650]], "level": 321, "attributes": [[1, 2670800], [2, 26708000], [3, 1335400], [4, 1335400]], "nextLevel": 322, "id": 75001}, {"cost": [[11, 137101, 650]], "level": 322, "attributes": [[1, 2685600], [2, 26856000], [3, 1342800], [4, 1342800]], "nextLevel": 323, "id": 75001}, {"cost": [[11, 137101, 650]], "level": 323, "attributes": [[1, 2700400], [2, 27004000], [3, 1350200], [4, 1350200]], "nextLevel": 324, "id": 75001}, {"cost": [[11, 137101, 650]], "level": 324, "attributes": [[1, 2715200], [2, 27152000], [3, 1357600], [4, 1357600]], "nextLevel": 325, "id": 75001}, {"cost": [[11, 137101, 650]], "level": 325, "attributes": [[1, 2730000], [2, 27300000], [3, 1365000], [4, 1365000]], "nextLevel": 326, "id": 75001}, {"cost": [[11, 137101, 660]], "level": 326, "attributes": [[1, 2745000], [2, 27450000], [3, 1372500], [4, 1372500]], "nextLevel": 327, "id": 75001}, {"cost": [[11, 137101, 660]], "level": 327, "attributes": [[1, 2760000], [2, 27600000], [3, 1380000], [4, 1380000]], "nextLevel": 328, "id": 75001}, {"cost": [[11, 137101, 660]], "level": 328, "attributes": [[1, 2775000], [2, 27750000], [3, 1387500], [4, 1387500]], "nextLevel": 329, "id": 75001}, {"cost": [[11, 137101, 660]], "level": 329, "attributes": [[1, 2790000], [2, 27900000], [3, 1395000], [4, 1395000]], "nextLevel": 330, "id": 75001}, {"cost": [[11, 137101, 660]], "level": 330, "attributes": [[1, 2805000], [2, 28050000], [3, 1402500], [4, 1402500]], "nextLevel": 331, "id": 75001}, {"cost": [[11, 137101, 670]], "level": 331, "attributes": [[1, 2820200], [2, 28202000], [3, 1410100], [4, 1410100]], "nextLevel": 332, "id": 75001}, {"cost": [[11, 137101, 670]], "level": 332, "attributes": [[1, 2835400], [2, 28354000], [3, 1417700], [4, 1417700]], "nextLevel": 333, "id": 75001}, {"cost": [[11, 137101, 670]], "level": 333, "attributes": [[1, 2850600], [2, 28506000], [3, 1425300], [4, 1425300]], "nextLevel": 334, "id": 75001}, {"cost": [[11, 137101, 670]], "level": 334, "attributes": [[1, 2865800], [2, 28658000], [3, 1432900], [4, 1432900]], "nextLevel": 335, "id": 75001}, {"cost": [[11, 137101, 670]], "level": 335, "attributes": [[1, 2881000], [2, 28810000], [3, 1440500], [4, 1440500]], "nextLevel": 336, "id": 75001}, {"cost": [[11, 137101, 680]], "level": 336, "attributes": [[1, 2896400], [2, 28964000], [3, 1448200], [4, 1448200]], "nextLevel": 337, "id": 75001}, {"cost": [[11, 137101, 680]], "level": 337, "attributes": [[1, 2911800], [2, 29118000], [3, 1455900], [4, 1455900]], "nextLevel": 338, "id": 75001}, {"cost": [[11, 137101, 680]], "level": 338, "attributes": [[1, 2927200], [2, 29272000], [3, 1463600], [4, 1463600]], "nextLevel": 339, "id": 75001}, {"cost": [[11, 137101, 680]], "level": 339, "attributes": [[1, 2942600], [2, 29426000], [3, 1471300], [4, 1471300]], "nextLevel": 340, "id": 75001}, {"cost": [[11, 137101, 680]], "level": 340, "attributes": [[1, 2958000], [2, 29580000], [3, 1479000], [4, 1479000]], "nextLevel": 341, "id": 75001}, {"cost": [[11, 137101, 690]], "level": 341, "attributes": [[1, 2973600], [2, 29736000], [3, 1486800], [4, 1486800]], "nextLevel": 342, "id": 75001}, {"cost": [[11, 137101, 690]], "level": 342, "attributes": [[1, 2989200], [2, 29892000], [3, 1494600], [4, 1494600]], "nextLevel": 343, "id": 75001}, {"cost": [[11, 137101, 690]], "level": 343, "attributes": [[1, 3004800], [2, 30048000], [3, 1502400], [4, 1502400]], "nextLevel": 344, "id": 75001}, {"cost": [[11, 137101, 690]], "level": 344, "attributes": [[1, 3020400], [2, 30204000], [3, 1510200], [4, 1510200]], "nextLevel": 345, "id": 75001}, {"cost": [[11, 137101, 690]], "level": 345, "attributes": [[1, 3036000], [2, 30360000], [3, 1518000], [4, 1518000]], "nextLevel": 346, "id": 75001}, {"cost": [[11, 137101, 700]], "level": 346, "attributes": [[1, 3051800], [2, 30518000], [3, 1525900], [4, 1525900]], "nextLevel": 347, "id": 75001}, {"cost": [[11, 137101, 700]], "level": 347, "attributes": [[1, 3067600], [2, 30676000], [3, 1533800], [4, 1533800]], "nextLevel": 348, "id": 75001}, {"cost": [[11, 137101, 700]], "level": 348, "attributes": [[1, 3083400], [2, 30834000], [3, 1541700], [4, 1541700]], "nextLevel": 349, "id": 75001}, {"cost": [[11, 137101, 700]], "level": 349, "attributes": [[1, 3099200], [2, 30992000], [3, 1549600], [4, 1549600]], "nextLevel": 350, "id": 75001}, {"cost": [[11, 137101, 700]], "level": 350, "attributes": [[1, 3115000], [2, 31150000], [3, 1557500], [4, 1557500]], "nextLevel": 351, "id": 75001}, {"cost": [[11, 137101, 710]], "level": 351, "attributes": [[1, 3131000], [2, 31310000], [3, 1565500], [4, 1565500]], "nextLevel": 352, "id": 75001}, {"cost": [[11, 137101, 710]], "level": 352, "attributes": [[1, 3147000], [2, 31470000], [3, 1573500], [4, 1573500]], "nextLevel": 353, "id": 75001}, {"cost": [[11, 137101, 710]], "level": 353, "attributes": [[1, 3163000], [2, 31630000], [3, 1581500], [4, 1581500]], "nextLevel": 354, "id": 75001}, {"cost": [[11, 137101, 710]], "level": 354, "attributes": [[1, 3179000], [2, 31790000], [3, 1589500], [4, 1589500]], "nextLevel": 355, "id": 75001}, {"cost": [[11, 137101, 710]], "level": 355, "attributes": [[1, 3195000], [2, 31950000], [3, 1597500], [4, 1597500]], "nextLevel": 356, "id": 75001}, {"cost": [[11, 137101, 720]], "level": 356, "attributes": [[1, 3211200], [2, 32112000], [3, 1605600], [4, 1605600]], "nextLevel": 357, "id": 75001}, {"cost": [[11, 137101, 720]], "level": 357, "attributes": [[1, 3227400], [2, 32274000], [3, 1613700], [4, 1613700]], "nextLevel": 358, "id": 75001}, {"cost": [[11, 137101, 720]], "level": 358, "attributes": [[1, 3243600], [2, 32436000], [3, 1621800], [4, 1621800]], "nextLevel": 359, "id": 75001}, {"cost": [[11, 137101, 720]], "level": 359, "attributes": [[1, 3259800], [2, 32598000], [3, 1629900], [4, 1629900]], "nextLevel": 360, "id": 75001}, {"cost": [[11, 137101, 720]], "level": 360, "attributes": [[1, 3276000], [2, 32760000], [3, 1638000], [4, 1638000]], "nextLevel": 361, "id": 75001}, {"cost": [[11, 137101, 730]], "level": 361, "attributes": [[1, 3292400], [2, 32924000], [3, 1646200], [4, 1646200]], "nextLevel": 362, "id": 75001}, {"cost": [[11, 137101, 730]], "level": 362, "attributes": [[1, 3308800], [2, 33088000], [3, 1654400], [4, 1654400]], "nextLevel": 363, "id": 75001}, {"cost": [[11, 137101, 730]], "level": 363, "attributes": [[1, 3325200], [2, 33252000], [3, 1662600], [4, 1662600]], "nextLevel": 364, "id": 75001}, {"cost": [[11, 137101, 730]], "level": 364, "attributes": [[1, 3341600], [2, 33416000], [3, 1670800], [4, 1670800]], "nextLevel": 365, "id": 75001}, {"cost": [[11, 137101, 730]], "level": 365, "attributes": [[1, 3358000], [2, 33580000], [3, 1679000], [4, 1679000]], "nextLevel": 366, "id": 75001}, {"cost": [[11, 137101, 740]], "level": 366, "attributes": [[1, 3374600], [2, 33746000], [3, 1687300], [4, 1687300]], "nextLevel": 367, "id": 75001}, {"cost": [[11, 137101, 740]], "level": 367, "attributes": [[1, 3391200], [2, 33912000], [3, 1695600], [4, 1695600]], "nextLevel": 368, "id": 75001}, {"cost": [[11, 137101, 740]], "level": 368, "attributes": [[1, 3407800], [2, 34078000], [3, 1703900], [4, 1703900]], "nextLevel": 369, "id": 75001}, {"cost": [[11, 137101, 740]], "level": 369, "attributes": [[1, 3424400], [2, 34244000], [3, 1712200], [4, 1712200]], "nextLevel": 370, "id": 75001}, {"cost": [[11, 137101, 740]], "level": 370, "attributes": [[1, 3441000], [2, 34410000], [3, 1720500], [4, 1720500]], "nextLevel": 371, "id": 75001}, {"cost": [[11, 137101, 750]], "level": 371, "attributes": [[1, 3457800], [2, 34578000], [3, 1728900], [4, 1728900]], "nextLevel": 372, "id": 75001}, {"cost": [[11, 137101, 750]], "level": 372, "attributes": [[1, 3474600], [2, 34746000], [3, 1737300], [4, 1737300]], "nextLevel": 373, "id": 75001}, {"cost": [[11, 137101, 750]], "level": 373, "attributes": [[1, 3491400], [2, 34914000], [3, 1745700], [4, 1745700]], "nextLevel": 374, "id": 75001}, {"cost": [[11, 137101, 750]], "level": 374, "attributes": [[1, 3508200], [2, 35082000], [3, 1754100], [4, 1754100]], "nextLevel": 375, "id": 75001}, {"cost": [[11, 137101, 750]], "level": 375, "attributes": [[1, 3525000], [2, 35250000], [3, 1762500], [4, 1762500]], "nextLevel": 376, "id": 75001}, {"cost": [[11, 137101, 760]], "level": 376, "attributes": [[1, 3542000], [2, 35420000], [3, 1771000], [4, 1771000]], "nextLevel": 377, "id": 75001}, {"cost": [[11, 137101, 760]], "level": 377, "attributes": [[1, 3559000], [2, 35590000], [3, 1779500], [4, 1779500]], "nextLevel": 378, "id": 75001}, {"cost": [[11, 137101, 760]], "level": 378, "attributes": [[1, 3576000], [2, 35760000], [3, 1788000], [4, 1788000]], "nextLevel": 379, "id": 75001}, {"cost": [[11, 137101, 760]], "level": 379, "attributes": [[1, 3593000], [2, 35930000], [3, 1796500], [4, 1796500]], "nextLevel": 380, "id": 75001}, {"cost": [[11, 137101, 760]], "level": 380, "attributes": [[1, 3610000], [2, 36100000], [3, 1805000], [4, 1805000]], "nextLevel": 381, "id": 75001}, {"cost": [[11, 137101, 770]], "level": 381, "attributes": [[1, 3627200], [2, 36272000], [3, 1813600], [4, 1813600]], "nextLevel": 382, "id": 75001}, {"cost": [[11, 137101, 770]], "level": 382, "attributes": [[1, 3644400], [2, 36444000], [3, 1822200], [4, 1822200]], "nextLevel": 383, "id": 75001}, {"cost": [[11, 137101, 770]], "level": 383, "attributes": [[1, 3661600], [2, 36616000], [3, 1830800], [4, 1830800]], "nextLevel": 384, "id": 75001}, {"cost": [[11, 137101, 770]], "level": 384, "attributes": [[1, 3678800], [2, 36788000], [3, 1839400], [4, 1839400]], "nextLevel": 385, "id": 75001}, {"cost": [[11, 137101, 770]], "level": 385, "attributes": [[1, 3696000], [2, 36960000], [3, 1848000], [4, 1848000]], "nextLevel": 386, "id": 75001}, {"cost": [[11, 137101, 780]], "level": 386, "attributes": [[1, 3713400], [2, 37134000], [3, 1856700], [4, 1856700]], "nextLevel": 387, "id": 75001}, {"cost": [[11, 137101, 780]], "level": 387, "attributes": [[1, 3730800], [2, 37308000], [3, 1865400], [4, 1865400]], "nextLevel": 388, "id": 75001}, {"cost": [[11, 137101, 780]], "level": 388, "attributes": [[1, 3748200], [2, 37482000], [3, 1874100], [4, 1874100]], "nextLevel": 389, "id": 75001}, {"cost": [[11, 137101, 780]], "level": 389, "attributes": [[1, 3765600], [2, 37656000], [3, 1882800], [4, 1882800]], "nextLevel": 390, "id": 75001}, {"cost": [[11, 137101, 780]], "level": 390, "attributes": [[1, 3783000], [2, 37830000], [3, 1891500], [4, 1891500]], "nextLevel": 391, "id": 75001}, {"cost": [[11, 137101, 790]], "level": 391, "attributes": [[1, 3800600], [2, 38006000], [3, 1900300], [4, 1900300]], "nextLevel": 392, "id": 75001}, {"cost": [[11, 137101, 790]], "level": 392, "attributes": [[1, 3818200], [2, 38182000], [3, 1909100], [4, 1909100]], "nextLevel": 393, "id": 75001}, {"cost": [[11, 137101, 790]], "level": 393, "attributes": [[1, 3835800], [2, 38358000], [3, 1917900], [4, 1917900]], "nextLevel": 394, "id": 75001}, {"cost": [[11, 137101, 790]], "level": 394, "attributes": [[1, 3853400], [2, 38534000], [3, 1926700], [4, 1926700]], "nextLevel": 395, "id": 75001}, {"cost": [[11, 137101, 790]], "level": 395, "attributes": [[1, 3871000], [2, 38710000], [3, 1935500], [4, 1935500]], "nextLevel": 396, "id": 75001}, {"cost": [[11, 137101, 800]], "level": 396, "attributes": [[1, 3888800], [2, 38888000], [3, 1944400], [4, 1944400]], "nextLevel": 397, "id": 75001}, {"cost": [[11, 137101, 800]], "level": 397, "attributes": [[1, 3906600], [2, 39066000], [3, 1953300], [4, 1953300]], "nextLevel": 398, "id": 75001}, {"cost": [[11, 137101, 800]], "level": 398, "attributes": [[1, 3924400], [2, 39244000], [3, 1962200], [4, 1962200]], "nextLevel": 399, "id": 75001}, {"cost": [[11, 137101, 800]], "level": 399, "attributes": [[1, 3942200], [2, 39422000], [3, 1971100], [4, 1971100]], "nextLevel": 400, "id": 75001}, {"cost": [[11, 137101, 800]], "level": 400, "attributes": [[1, 3960000], [2, 39600000], [3, 1980000], [4, 1980000]], "nextLevel": 401, "id": 75001}, {"cost": [[11, 137101, 810]], "level": 401, "attributes": [[1, 3978000], [2, 39780000], [3, 1989000], [4, 1989000]], "nextLevel": 402, "id": 75001}, {"cost": [[11, 137101, 810]], "level": 402, "attributes": [[1, 3996000], [2, 39960000], [3, 1998000], [4, 1998000]], "nextLevel": 403, "id": 75001}, {"cost": [[11, 137101, 810]], "level": 403, "attributes": [[1, 4014000], [2, 40140000], [3, 2007000], [4, 2007000]], "nextLevel": 404, "id": 75001}, {"cost": [[11, 137101, 810]], "level": 404, "attributes": [[1, 4032000], [2, 40320000], [3, 2016000], [4, 2016000]], "nextLevel": 405, "id": 75001}, {"cost": [[11, 137101, 810]], "level": 405, "attributes": [[1, 4050000], [2, 40500000], [3, 2025000], [4, 2025000]], "nextLevel": 406, "id": 75001}, {"cost": [[11, 137101, 820]], "level": 406, "attributes": [[1, 4068200], [2, 40682000], [3, 2034100], [4, 2034100]], "nextLevel": 407, "id": 75001}, {"cost": [[11, 137101, 820]], "level": 407, "attributes": [[1, 4086400], [2, 40864000], [3, 2043200], [4, 2043200]], "nextLevel": 408, "id": 75001}, {"cost": [[11, 137101, 820]], "level": 408, "attributes": [[1, 4104600], [2, 41046000], [3, 2052300], [4, 2052300]], "nextLevel": 409, "id": 75001}, {"cost": [[11, 137101, 820]], "level": 409, "attributes": [[1, 4122800], [2, 41228000], [3, 2061400], [4, 2061400]], "nextLevel": 410, "id": 75001}, {"cost": [[11, 137101, 820]], "level": 410, "attributes": [[1, 4141000], [2, 41410000], [3, 2070500], [4, 2070500]], "nextLevel": 411, "id": 75001}, {"cost": [[11, 137101, 830]], "level": 411, "attributes": [[1, 4159400], [2, 41594000], [3, 2079700], [4, 2079700]], "nextLevel": 412, "id": 75001}, {"cost": [[11, 137101, 830]], "level": 412, "attributes": [[1, 4177800], [2, 41778000], [3, 2088900], [4, 2088900]], "nextLevel": 413, "id": 75001}, {"cost": [[11, 137101, 830]], "level": 413, "attributes": [[1, 4196200], [2, 41962000], [3, 2098100], [4, 2098100]], "nextLevel": 414, "id": 75001}, {"cost": [[11, 137101, 830]], "level": 414, "attributes": [[1, 4214600], [2, 42146000], [3, 2107300], [4, 2107300]], "nextLevel": 415, "id": 75001}, {"cost": [[11, 137101, 830]], "level": 415, "attributes": [[1, 4233000], [2, 42330000], [3, 2116500], [4, 2116500]], "nextLevel": 416, "id": 75001}, {"cost": [[11, 137101, 840]], "level": 416, "attributes": [[1, 4251600], [2, 42516000], [3, 2125800], [4, 2125800]], "nextLevel": 417, "id": 75001}, {"cost": [[11, 137101, 840]], "level": 417, "attributes": [[1, 4270200], [2, 42702000], [3, 2135100], [4, 2135100]], "nextLevel": 418, "id": 75001}, {"cost": [[11, 137101, 840]], "level": 418, "attributes": [[1, 4288800], [2, 42888000], [3, 2144400], [4, 2144400]], "nextLevel": 419, "id": 75001}, {"cost": [[11, 137101, 840]], "level": 419, "attributes": [[1, 4307400], [2, 43074000], [3, 2153700], [4, 2153700]], "nextLevel": 420, "id": 75001}, {"cost": [[11, 137101, 840]], "level": 420, "attributes": [[1, 4326000], [2, 43260000], [3, 2163000], [4, 2163000]], "nextLevel": 421, "id": 75001}, {"cost": [[11, 137101, 850]], "level": 421, "attributes": [[1, 4344800], [2, 43448000], [3, 2172400], [4, 2172400]], "nextLevel": 422, "id": 75001}, {"cost": [[11, 137101, 850]], "level": 422, "attributes": [[1, 4363600], [2, 43636000], [3, 2181800], [4, 2181800]], "nextLevel": 423, "id": 75001}, {"cost": [[11, 137101, 850]], "level": 423, "attributes": [[1, 4382400], [2, 43824000], [3, 2191200], [4, 2191200]], "nextLevel": 424, "id": 75001}, {"cost": [[11, 137101, 850]], "level": 424, "attributes": [[1, 4401200], [2, 44012000], [3, 2200600], [4, 2200600]], "nextLevel": 425, "id": 75001}, {"cost": [[11, 137101, 850]], "level": 425, "attributes": [[1, 4420000], [2, 44200000], [3, 2210000], [4, 2210000]], "nextLevel": 426, "id": 75001}, {"cost": [[11, 137101, 860]], "level": 426, "attributes": [[1, 4439000], [2, 44390000], [3, 2219500], [4, 2219500]], "nextLevel": 427, "id": 75001}, {"cost": [[11, 137101, 860]], "level": 427, "attributes": [[1, 4458000], [2, 44580000], [3, 2229000], [4, 2229000]], "nextLevel": 428, "id": 75001}, {"cost": [[11, 137101, 860]], "level": 428, "attributes": [[1, 4477000], [2, 44770000], [3, 2238500], [4, 2238500]], "nextLevel": 429, "id": 75001}, {"cost": [[11, 137101, 860]], "level": 429, "attributes": [[1, 4496000], [2, 44960000], [3, 2248000], [4, 2248000]], "nextLevel": 430, "id": 75001}, {"cost": [[11, 137101, 860]], "level": 430, "attributes": [[1, 4515000], [2, 45150000], [3, 2257500], [4, 2257500]], "nextLevel": 431, "id": 75001}, {"cost": [[11, 137101, 870]], "level": 431, "attributes": [[1, 4534200], [2, 45342000], [3, 2267100], [4, 2267100]], "nextLevel": 432, "id": 75001}, {"cost": [[11, 137101, 870]], "level": 432, "attributes": [[1, 4553400], [2, 45534000], [3, 2276700], [4, 2276700]], "nextLevel": 433, "id": 75001}, {"cost": [[11, 137101, 870]], "level": 433, "attributes": [[1, 4572600], [2, 45726000], [3, 2286300], [4, 2286300]], "nextLevel": 434, "id": 75001}, {"cost": [[11, 137101, 870]], "level": 434, "attributes": [[1, 4591800], [2, 45918000], [3, 2295900], [4, 2295900]], "nextLevel": 435, "id": 75001}, {"cost": [[11, 137101, 870]], "level": 435, "attributes": [[1, 4611000], [2, 46110000], [3, 2305500], [4, 2305500]], "nextLevel": 436, "id": 75001}, {"cost": [[11, 137101, 880]], "level": 436, "attributes": [[1, 4630400], [2, 46304000], [3, 2315200], [4, 2315200]], "nextLevel": 437, "id": 75001}, {"cost": [[11, 137101, 880]], "level": 437, "attributes": [[1, 4649800], [2, 46498000], [3, 2324900], [4, 2324900]], "nextLevel": 438, "id": 75001}, {"cost": [[11, 137101, 880]], "level": 438, "attributes": [[1, 4669200], [2, 46692000], [3, 2334600], [4, 2334600]], "nextLevel": 439, "id": 75001}, {"cost": [[11, 137101, 880]], "level": 439, "attributes": [[1, 4688600], [2, 46886000], [3, 2344300], [4, 2344300]], "nextLevel": 440, "id": 75001}, {"cost": [[11, 137101, 880]], "level": 440, "attributes": [[1, 4708000], [2, 47080000], [3, 2354000], [4, 2354000]], "nextLevel": 441, "id": 75001}, {"cost": [[11, 137101, 890]], "level": 441, "attributes": [[1, 4727600], [2, 47276000], [3, 2363800], [4, 2363800]], "nextLevel": 442, "id": 75001}, {"cost": [[11, 137101, 890]], "level": 442, "attributes": [[1, 4747200], [2, 47472000], [3, 2373600], [4, 2373600]], "nextLevel": 443, "id": 75001}, {"cost": [[11, 137101, 890]], "level": 443, "attributes": [[1, 4766800], [2, 47668000], [3, 2383400], [4, 2383400]], "nextLevel": 444, "id": 75001}, {"cost": [[11, 137101, 890]], "level": 444, "attributes": [[1, 4786400], [2, 47864000], [3, 2393200], [4, 2393200]], "nextLevel": 445, "id": 75001}, {"cost": [[11, 137101, 890]], "level": 445, "attributes": [[1, 4806000], [2, 48060000], [3, 2403000], [4, 2403000]], "nextLevel": 446, "id": 75001}, {"cost": [[11, 137101, 900]], "level": 446, "attributes": [[1, 4825800], [2, 48258000], [3, 2412900], [4, 2412900]], "nextLevel": 447, "id": 75001}, {"cost": [[11, 137101, 900]], "level": 447, "attributes": [[1, 4845600], [2, 48456000], [3, 2422800], [4, 2422800]], "nextLevel": 448, "id": 75001}, {"cost": [[11, 137101, 900]], "level": 448, "attributes": [[1, 4865400], [2, 48654000], [3, 2432700], [4, 2432700]], "nextLevel": 449, "id": 75001}, {"cost": [[11, 137101, 900]], "level": 449, "attributes": [[1, 4885200], [2, 48852000], [3, 2442600], [4, 2442600]], "nextLevel": 450, "id": 75001}, {"cost": [[11, 137101, 900]], "level": 450, "attributes": [[1, 4905000], [2, 49050000], [3, 2452500], [4, 2452500]], "nextLevel": 451, "id": 75001}, {"cost": [[11, 137101, 910]], "level": 451, "attributes": [[1, 4925000], [2, 49250000], [3, 2462500], [4, 2462500]], "nextLevel": 452, "id": 75001}, {"cost": [[11, 137101, 910]], "level": 452, "attributes": [[1, 4945000], [2, 49450000], [3, 2472500], [4, 2472500]], "nextLevel": 453, "id": 75001}, {"cost": [[11, 137101, 910]], "level": 453, "attributes": [[1, 4965000], [2, 49650000], [3, 2482500], [4, 2482500]], "nextLevel": 454, "id": 75001}, {"cost": [[11, 137101, 910]], "level": 454, "attributes": [[1, 4985000], [2, 49850000], [3, 2492500], [4, 2492500]], "nextLevel": 455, "id": 75001}, {"cost": [[11, 137101, 910]], "level": 455, "attributes": [[1, 5005000], [2, 50050000], [3, 2502500], [4, 2502500]], "nextLevel": 456, "id": 75001}, {"cost": [[11, 137101, 920]], "level": 456, "attributes": [[1, 5025200], [2, 50252000], [3, 2512600], [4, 2512600]], "nextLevel": 457, "id": 75001}, {"cost": [[11, 137101, 920]], "level": 457, "attributes": [[1, 5045400], [2, 50454000], [3, 2522700], [4, 2522700]], "nextLevel": 458, "id": 75001}, {"cost": [[11, 137101, 920]], "level": 458, "attributes": [[1, 5065600], [2, 50656000], [3, 2532800], [4, 2532800]], "nextLevel": 459, "id": 75001}, {"cost": [[11, 137101, 920]], "level": 459, "attributes": [[1, 5085800], [2, 50858000], [3, 2542900], [4, 2542900]], "nextLevel": 460, "id": 75001}, {"cost": [[11, 137101, 920]], "level": 460, "attributes": [[1, 5106000], [2, 51060000], [3, 2553000], [4, 2553000]], "nextLevel": 461, "id": 75001}, {"cost": [[11, 137101, 930]], "level": 461, "attributes": [[1, 5126400], [2, 51264000], [3, 2563200], [4, 2563200]], "nextLevel": 462, "id": 75001}, {"cost": [[11, 137101, 930]], "level": 462, "attributes": [[1, 5146800], [2, 51468000], [3, 2573400], [4, 2573400]], "nextLevel": 463, "id": 75001}, {"cost": [[11, 137101, 930]], "level": 463, "attributes": [[1, 5167200], [2, 51672000], [3, 2583600], [4, 2583600]], "nextLevel": 464, "id": 75001}, {"cost": [[11, 137101, 930]], "level": 464, "attributes": [[1, 5187600], [2, 51876000], [3, 2593800], [4, 2593800]], "nextLevel": 465, "id": 75001}, {"cost": [[11, 137101, 930]], "level": 465, "attributes": [[1, 5208000], [2, 52080000], [3, 2604000], [4, 2604000]], "nextLevel": 466, "id": 75001}, {"cost": [[11, 137101, 940]], "level": 466, "attributes": [[1, 5228600], [2, 52286000], [3, 2614300], [4, 2614300]], "nextLevel": 467, "id": 75001}, {"cost": [[11, 137101, 940]], "level": 467, "attributes": [[1, 5249200], [2, 52492000], [3, 2624600], [4, 2624600]], "nextLevel": 468, "id": 75001}, {"cost": [[11, 137101, 940]], "level": 468, "attributes": [[1, 5269800], [2, 52698000], [3, 2634900], [4, 2634900]], "nextLevel": 469, "id": 75001}, {"cost": [[11, 137101, 940]], "level": 469, "attributes": [[1, 5290400], [2, 52904000], [3, 2645200], [4, 2645200]], "nextLevel": 470, "id": 75001}, {"cost": [[11, 137101, 940]], "level": 470, "attributes": [[1, 5311000], [2, 53110000], [3, 2655500], [4, 2655500]], "nextLevel": 471, "id": 75001}, {"cost": [[11, 137101, 950]], "level": 471, "attributes": [[1, 5331800], [2, 53318000], [3, 2665900], [4, 2665900]], "nextLevel": 472, "id": 75001}, {"cost": [[11, 137101, 950]], "level": 472, "attributes": [[1, 5352600], [2, 53526000], [3, 2676300], [4, 2676300]], "nextLevel": 473, "id": 75001}, {"cost": [[11, 137101, 950]], "level": 473, "attributes": [[1, 5373400], [2, 53734000], [3, 2686700], [4, 2686700]], "nextLevel": 474, "id": 75001}, {"cost": [[11, 137101, 950]], "level": 474, "attributes": [[1, 5394200], [2, 53942000], [3, 2697100], [4, 2697100]], "nextLevel": 475, "id": 75001}, {"cost": [[11, 137101, 950]], "level": 475, "attributes": [[1, 5415000], [2, 54150000], [3, 2707500], [4, 2707500]], "nextLevel": 476, "id": 75001}, {"cost": [[11, 137101, 960]], "level": 476, "attributes": [[1, 5436000], [2, 54360000], [3, 2718000], [4, 2718000]], "nextLevel": 477, "id": 75001}, {"cost": [[11, 137101, 960]], "level": 477, "attributes": [[1, 5457000], [2, 54570000], [3, 2728500], [4, 2728500]], "nextLevel": 478, "id": 75001}, {"cost": [[11, 137101, 960]], "level": 478, "attributes": [[1, 5478000], [2, 54780000], [3, 2739000], [4, 2739000]], "nextLevel": 479, "id": 75001}, {"cost": [[11, 137101, 960]], "level": 479, "attributes": [[1, 5499000], [2, 54990000], [3, 2749500], [4, 2749500]], "nextLevel": 480, "id": 75001}, {"cost": [[11, 137101, 960]], "level": 480, "attributes": [[1, 5520000], [2, 55200000], [3, 2760000], [4, 2760000]], "nextLevel": 481, "id": 75001}, {"cost": [[11, 137101, 970]], "level": 481, "attributes": [[1, 5541200], [2, 55412000], [3, 2770600], [4, 2770600]], "nextLevel": 482, "id": 75001}, {"cost": [[11, 137101, 970]], "level": 482, "attributes": [[1, 5562400], [2, 55624000], [3, 2781200], [4, 2781200]], "nextLevel": 483, "id": 75001}, {"cost": [[11, 137101, 970]], "level": 483, "attributes": [[1, 5583600], [2, 55836000], [3, 2791800], [4, 2791800]], "nextLevel": 484, "id": 75001}, {"cost": [[11, 137101, 970]], "level": 484, "attributes": [[1, 5604800], [2, 56048000], [3, 2802400], [4, 2802400]], "nextLevel": 485, "id": 75001}, {"cost": [[11, 137101, 970]], "level": 485, "attributes": [[1, 5626000], [2, 56260000], [3, 2813000], [4, 2813000]], "nextLevel": 486, "id": 75001}, {"cost": [[11, 137101, 980]], "level": 486, "attributes": [[1, 5647400], [2, 56474000], [3, 2823700], [4, 2823700]], "nextLevel": 487, "id": 75001}, {"cost": [[11, 137101, 980]], "level": 487, "attributes": [[1, 5668800], [2, 56688000], [3, 2834400], [4, 2834400]], "nextLevel": 488, "id": 75001}, {"cost": [[11, 137101, 980]], "level": 488, "attributes": [[1, 5690200], [2, 56902000], [3, 2845100], [4, 2845100]], "nextLevel": 489, "id": 75001}, {"cost": [[11, 137101, 980]], "level": 489, "attributes": [[1, 5711600], [2, 57116000], [3, 2855800], [4, 2855800]], "nextLevel": 490, "id": 75001}, {"cost": [[11, 137101, 980]], "level": 490, "attributes": [[1, 5733000], [2, 57330000], [3, 2866500], [4, 2866500]], "nextLevel": 491, "id": 75001}, {"cost": [[11, 137101, 990]], "level": 491, "attributes": [[1, 5754600], [2, 57546000], [3, 2877300], [4, 2877300]], "nextLevel": 492, "id": 75001}, {"cost": [[11, 137101, 990]], "level": 492, "attributes": [[1, 5776200], [2, 57762000], [3, 2888100], [4, 2888100]], "nextLevel": 493, "id": 75001}, {"cost": [[11, 137101, 990]], "level": 493, "attributes": [[1, 5797800], [2, 57978000], [3, 2898900], [4, 2898900]], "nextLevel": 494, "id": 75001}, {"cost": [[11, 137101, 990]], "level": 494, "attributes": [[1, 5819400], [2, 58194000], [3, 2909700], [4, 2909700]], "nextLevel": 495, "id": 75001}, {"cost": [[11, 137101, 990]], "level": 495, "attributes": [[1, 5841000], [2, 58410000], [3, 2920500], [4, 2920500]], "nextLevel": 496, "id": 75001}, {"cost": [[11, 137101, 1000]], "level": 496, "attributes": [[1, 5862800], [2, 58628000], [3, 2931400], [4, 2931400]], "nextLevel": 497, "id": 75001}, {"cost": [[11, 137101, 1000]], "level": 497, "attributes": [[1, 5884600], [2, 58846000], [3, 2942300], [4, 2942300]], "nextLevel": 498, "id": 75001}, {"cost": [[11, 137101, 1000]], "level": 498, "attributes": [[1, 5906400], [2, 59064000], [3, 2953200], [4, 2953200]], "nextLevel": 499, "id": 75001}, {"cost": [[11, 137101, 1000]], "level": 499, "attributes": [[1, 5928200], [2, 59282000], [3, 2964100], [4, 2964100]], "nextLevel": 500, "id": 75001}, {"cost": [], "level": 500, "attributes": [[1, 5950000], [2, 59500000], [3, 2975000], [4, 2975000]], "nextLevel": 0, "id": 75001}, {"cost": [[11, 137101, 10]], "level": 1, "attributes": [[1, 2000], [2, 20000], [3, 1000], [4, 1000]], "nextLevel": 2, "id": 75002}, {"cost": [[11, 137101, 10]], "level": 2, "attributes": [[1, 4000], [2, 40000], [3, 2000], [4, 2000]], "nextLevel": 3, "id": 75002}, {"cost": [[11, 137101, 10]], "level": 3, "attributes": [[1, 6000], [2, 60000], [3, 3000], [4, 3000]], "nextLevel": 4, "id": 75002}, {"cost": [[11, 137101, 10]], "level": 4, "attributes": [[1, 8000], [2, 80000], [3, 4000], [4, 4000]], "nextLevel": 5, "id": 75002}, {"cost": [[11, 137101, 10]], "level": 5, "attributes": [[1, 10000], [2, 100000], [3, 5000], [4, 5000]], "nextLevel": 6, "id": 75002}, {"cost": [[11, 137101, 20]], "level": 6, "attributes": [[1, 12200], [2, 122000], [3, 6100], [4, 6100]], "nextLevel": 7, "id": 75002}, {"cost": [[11, 137101, 20]], "level": 7, "attributes": [[1, 14400], [2, 144000], [3, 7200], [4, 7200]], "nextLevel": 8, "id": 75002}, {"cost": [[11, 137101, 20]], "level": 8, "attributes": [[1, 16600], [2, 166000], [3, 8300], [4, 8300]], "nextLevel": 9, "id": 75002}, {"cost": [[11, 137101, 20]], "level": 9, "attributes": [[1, 18800], [2, 188000], [3, 9400], [4, 9400]], "nextLevel": 10, "id": 75002}, {"cost": [[11, 137101, 20]], "level": 10, "attributes": [[1, 21000], [2, 210000], [3, 10500], [4, 10500]], "nextLevel": 11, "id": 75002}, {"cost": [[11, 137101, 30]], "level": 11, "attributes": [[1, 23400], [2, 234000], [3, 11700], [4, 11700]], "nextLevel": 12, "id": 75002}, {"cost": [[11, 137101, 30]], "level": 12, "attributes": [[1, 25800], [2, 258000], [3, 12900], [4, 12900]], "nextLevel": 13, "id": 75002}, {"cost": [[11, 137101, 30]], "level": 13, "attributes": [[1, 28200], [2, 282000], [3, 14100], [4, 14100]], "nextLevel": 14, "id": 75002}, {"cost": [[11, 137101, 30]], "level": 14, "attributes": [[1, 30600], [2, 306000], [3, 15300], [4, 15300]], "nextLevel": 15, "id": 75002}, {"cost": [[11, 137101, 30]], "level": 15, "attributes": [[1, 33000], [2, 330000], [3, 16500], [4, 16500]], "nextLevel": 16, "id": 75002}, {"cost": [[11, 137101, 40]], "level": 16, "attributes": [[1, 35600], [2, 356000], [3, 17800], [4, 17800]], "nextLevel": 17, "id": 75002}, {"cost": [[11, 137101, 40]], "level": 17, "attributes": [[1, 38200], [2, 382000], [3, 19100], [4, 19100]], "nextLevel": 18, "id": 75002}, {"cost": [[11, 137101, 40]], "level": 18, "attributes": [[1, 40800], [2, 408000], [3, 20400], [4, 20400]], "nextLevel": 19, "id": 75002}, {"cost": [[11, 137101, 40]], "level": 19, "attributes": [[1, 43400], [2, 434000], [3, 21700], [4, 21700]], "nextLevel": 20, "id": 75002}, {"cost": [[11, 137101, 40]], "level": 20, "attributes": [[1, 46000], [2, 460000], [3, 23000], [4, 23000]], "nextLevel": 21, "id": 75002}, {"cost": [[11, 137101, 50]], "level": 21, "attributes": [[1, 48800], [2, 488000], [3, 24400], [4, 24400]], "nextLevel": 22, "id": 75002}, {"cost": [[11, 137101, 50]], "level": 22, "attributes": [[1, 51600], [2, 516000], [3, 25800], [4, 25800]], "nextLevel": 23, "id": 75002}, {"cost": [[11, 137101, 50]], "level": 23, "attributes": [[1, 54400], [2, 544000], [3, 27200], [4, 27200]], "nextLevel": 24, "id": 75002}, {"cost": [[11, 137101, 50]], "level": 24, "attributes": [[1, 57200], [2, 572000], [3, 28600], [4, 28600]], "nextLevel": 25, "id": 75002}, {"cost": [[11, 137101, 50]], "level": 25, "attributes": [[1, 60000], [2, 600000], [3, 30000], [4, 30000]], "nextLevel": 26, "id": 75002}, {"cost": [[11, 137101, 60]], "level": 26, "attributes": [[1, 63000], [2, 630000], [3, 31500], [4, 31500]], "nextLevel": 27, "id": 75002}, {"cost": [[11, 137101, 60]], "level": 27, "attributes": [[1, 66000], [2, 660000], [3, 33000], [4, 33000]], "nextLevel": 28, "id": 75002}, {"cost": [[11, 137101, 60]], "level": 28, "attributes": [[1, 69000], [2, 690000], [3, 34500], [4, 34500]], "nextLevel": 29, "id": 75002}, {"cost": [[11, 137101, 60]], "level": 29, "attributes": [[1, 72000], [2, 720000], [3, 36000], [4, 36000]], "nextLevel": 30, "id": 75002}, {"cost": [[11, 137101, 60]], "level": 30, "attributes": [[1, 75000], [2, 750000], [3, 37500], [4, 37500]], "nextLevel": 31, "id": 75002}, {"cost": [[11, 137101, 70]], "level": 31, "attributes": [[1, 78200], [2, 782000], [3, 39100], [4, 39100]], "nextLevel": 32, "id": 75002}, {"cost": [[11, 137101, 70]], "level": 32, "attributes": [[1, 81400], [2, 814000], [3, 40700], [4, 40700]], "nextLevel": 33, "id": 75002}, {"cost": [[11, 137101, 70]], "level": 33, "attributes": [[1, 84600], [2, 846000], [3, 42300], [4, 42300]], "nextLevel": 34, "id": 75002}, {"cost": [[11, 137101, 70]], "level": 34, "attributes": [[1, 87800], [2, 878000], [3, 43900], [4, 43900]], "nextLevel": 35, "id": 75002}, {"cost": [[11, 137101, 70]], "level": 35, "attributes": [[1, 91000], [2, 910000], [3, 45500], [4, 45500]], "nextLevel": 36, "id": 75002}, {"cost": [[11, 137101, 80]], "level": 36, "attributes": [[1, 94400], [2, 944000], [3, 47200], [4, 47200]], "nextLevel": 37, "id": 75002}, {"cost": [[11, 137101, 80]], "level": 37, "attributes": [[1, 97800], [2, 978000], [3, 48900], [4, 48900]], "nextLevel": 38, "id": 75002}, {"cost": [[11, 137101, 80]], "level": 38, "attributes": [[1, 101200], [2, 1012000], [3, 50600], [4, 50600]], "nextLevel": 39, "id": 75002}, {"cost": [[11, 137101, 80]], "level": 39, "attributes": [[1, 104600], [2, 1046000], [3, 52300], [4, 52300]], "nextLevel": 40, "id": 75002}, {"cost": [[11, 137101, 80]], "level": 40, "attributes": [[1, 108000], [2, 1080000], [3, 54000], [4, 54000]], "nextLevel": 41, "id": 75002}, {"cost": [[11, 137101, 90]], "level": 41, "attributes": [[1, 111600], [2, 1116000], [3, 55800], [4, 55800]], "nextLevel": 42, "id": 75002}, {"cost": [[11, 137101, 90]], "level": 42, "attributes": [[1, 115200], [2, 1152000], [3, 57600], [4, 57600]], "nextLevel": 43, "id": 75002}, {"cost": [[11, 137101, 90]], "level": 43, "attributes": [[1, 118800], [2, 1188000], [3, 59400], [4, 59400]], "nextLevel": 44, "id": 75002}, {"cost": [[11, 137101, 90]], "level": 44, "attributes": [[1, 122400], [2, 1224000], [3, 61200], [4, 61200]], "nextLevel": 45, "id": 75002}, {"cost": [[11, 137101, 90]], "level": 45, "attributes": [[1, 126000], [2, 1260000], [3, 63000], [4, 63000]], "nextLevel": 46, "id": 75002}, {"cost": [[11, 137101, 100]], "level": 46, "attributes": [[1, 129800], [2, 1298000], [3, 64900], [4, 64900]], "nextLevel": 47, "id": 75002}, {"cost": [[11, 137101, 100]], "level": 47, "attributes": [[1, 133600], [2, 1336000], [3, 66800], [4, 66800]], "nextLevel": 48, "id": 75002}, {"cost": [[11, 137101, 100]], "level": 48, "attributes": [[1, 137400], [2, 1374000], [3, 68700], [4, 68700]], "nextLevel": 49, "id": 75002}, {"cost": [[11, 137101, 100]], "level": 49, "attributes": [[1, 141200], [2, 1412000], [3, 70600], [4, 70600]], "nextLevel": 50, "id": 75002}, {"cost": [[11, 137101, 100]], "level": 50, "attributes": [[1, 145000], [2, 1450000], [3, 72500], [4, 72500]], "nextLevel": 51, "id": 75002}, {"cost": [[11, 137101, 110]], "level": 51, "attributes": [[1, 149000], [2, 1490000], [3, 74500], [4, 74500]], "nextLevel": 52, "id": 75002}, {"cost": [[11, 137101, 110]], "level": 52, "attributes": [[1, 153000], [2, 1530000], [3, 76500], [4, 76500]], "nextLevel": 53, "id": 75002}, {"cost": [[11, 137101, 110]], "level": 53, "attributes": [[1, 157000], [2, 1570000], [3, 78500], [4, 78500]], "nextLevel": 54, "id": 75002}, {"cost": [[11, 137101, 110]], "level": 54, "attributes": [[1, 161000], [2, 1610000], [3, 80500], [4, 80500]], "nextLevel": 55, "id": 75002}, {"cost": [[11, 137101, 110]], "level": 55, "attributes": [[1, 165000], [2, 1650000], [3, 82500], [4, 82500]], "nextLevel": 56, "id": 75002}, {"cost": [[11, 137101, 120]], "level": 56, "attributes": [[1, 169200], [2, 1692000], [3, 84600], [4, 84600]], "nextLevel": 57, "id": 75002}, {"cost": [[11, 137101, 120]], "level": 57, "attributes": [[1, 173400], [2, 1734000], [3, 86700], [4, 86700]], "nextLevel": 58, "id": 75002}, {"cost": [[11, 137101, 120]], "level": 58, "attributes": [[1, 177600], [2, 1776000], [3, 88800], [4, 88800]], "nextLevel": 59, "id": 75002}, {"cost": [[11, 137101, 120]], "level": 59, "attributes": [[1, 181800], [2, 1818000], [3, 90900], [4, 90900]], "nextLevel": 60, "id": 75002}, {"cost": [[11, 137101, 120]], "level": 60, "attributes": [[1, 186000], [2, 1860000], [3, 93000], [4, 93000]], "nextLevel": 61, "id": 75002}, {"cost": [[11, 137101, 130]], "level": 61, "attributes": [[1, 190400], [2, 1904000], [3, 95200], [4, 95200]], "nextLevel": 62, "id": 75002}, {"cost": [[11, 137101, 130]], "level": 62, "attributes": [[1, 194800], [2, 1948000], [3, 97400], [4, 97400]], "nextLevel": 63, "id": 75002}, {"cost": [[11, 137101, 130]], "level": 63, "attributes": [[1, 199200], [2, 1992000], [3, 99600], [4, 99600]], "nextLevel": 64, "id": 75002}, {"cost": [[11, 137101, 130]], "level": 64, "attributes": [[1, 203600], [2, 2036000], [3, 101800], [4, 101800]], "nextLevel": 65, "id": 75002}, {"cost": [[11, 137101, 130]], "level": 65, "attributes": [[1, 208000], [2, 2080000], [3, 104000], [4, 104000]], "nextLevel": 66, "id": 75002}, {"cost": [[11, 137101, 140]], "level": 66, "attributes": [[1, 212600], [2, 2126000], [3, 106300], [4, 106300]], "nextLevel": 67, "id": 75002}, {"cost": [[11, 137101, 140]], "level": 67, "attributes": [[1, 217200], [2, 2172000], [3, 108600], [4, 108600]], "nextLevel": 68, "id": 75002}, {"cost": [[11, 137101, 140]], "level": 68, "attributes": [[1, 221800], [2, 2218000], [3, 110900], [4, 110900]], "nextLevel": 69, "id": 75002}, {"cost": [[11, 137101, 140]], "level": 69, "attributes": [[1, 226400], [2, 2264000], [3, 113200], [4, 113200]], "nextLevel": 70, "id": 75002}, {"cost": [[11, 137101, 140]], "level": 70, "attributes": [[1, 231000], [2, 2310000], [3, 115500], [4, 115500]], "nextLevel": 71, "id": 75002}, {"cost": [[11, 137101, 150]], "level": 71, "attributes": [[1, 235800], [2, 2358000], [3, 117900], [4, 117900]], "nextLevel": 72, "id": 75002}, {"cost": [[11, 137101, 150]], "level": 72, "attributes": [[1, 240600], [2, 2406000], [3, 120300], [4, 120300]], "nextLevel": 73, "id": 75002}, {"cost": [[11, 137101, 150]], "level": 73, "attributes": [[1, 245400], [2, 2454000], [3, 122700], [4, 122700]], "nextLevel": 74, "id": 75002}, {"cost": [[11, 137101, 150]], "level": 74, "attributes": [[1, 250200], [2, 2502000], [3, 125100], [4, 125100]], "nextLevel": 75, "id": 75002}, {"cost": [[11, 137101, 150]], "level": 75, "attributes": [[1, 255000], [2, 2550000], [3, 127500], [4, 127500]], "nextLevel": 76, "id": 75002}, {"cost": [[11, 137101, 160]], "level": 76, "attributes": [[1, 260000], [2, 2600000], [3, 130000], [4, 130000]], "nextLevel": 77, "id": 75002}, {"cost": [[11, 137101, 160]], "level": 77, "attributes": [[1, 265000], [2, 2650000], [3, 132500], [4, 132500]], "nextLevel": 78, "id": 75002}, {"cost": [[11, 137101, 160]], "level": 78, "attributes": [[1, 270000], [2, 2700000], [3, 135000], [4, 135000]], "nextLevel": 79, "id": 75002}, {"cost": [[11, 137101, 160]], "level": 79, "attributes": [[1, 275000], [2, 2750000], [3, 137500], [4, 137500]], "nextLevel": 80, "id": 75002}, {"cost": [[11, 137101, 160]], "level": 80, "attributes": [[1, 280000], [2, 2800000], [3, 140000], [4, 140000]], "nextLevel": 81, "id": 75002}, {"cost": [[11, 137101, 170]], "level": 81, "attributes": [[1, 285200], [2, 2852000], [3, 142600], [4, 142600]], "nextLevel": 82, "id": 75002}, {"cost": [[11, 137101, 170]], "level": 82, "attributes": [[1, 290400], [2, 2904000], [3, 145200], [4, 145200]], "nextLevel": 83, "id": 75002}, {"cost": [[11, 137101, 170]], "level": 83, "attributes": [[1, 295600], [2, 2956000], [3, 147800], [4, 147800]], "nextLevel": 84, "id": 75002}, {"cost": [[11, 137101, 170]], "level": 84, "attributes": [[1, 300800], [2, 3008000], [3, 150400], [4, 150400]], "nextLevel": 85, "id": 75002}, {"cost": [[11, 137101, 170]], "level": 85, "attributes": [[1, 306000], [2, 3060000], [3, 153000], [4, 153000]], "nextLevel": 86, "id": 75002}, {"cost": [[11, 137101, 180]], "level": 86, "attributes": [[1, 311400], [2, 3114000], [3, 155700], [4, 155700]], "nextLevel": 87, "id": 75002}, {"cost": [[11, 137101, 180]], "level": 87, "attributes": [[1, 316800], [2, 3168000], [3, 158400], [4, 158400]], "nextLevel": 88, "id": 75002}, {"cost": [[11, 137101, 180]], "level": 88, "attributes": [[1, 322200], [2, 3222000], [3, 161100], [4, 161100]], "nextLevel": 89, "id": 75002}, {"cost": [[11, 137101, 180]], "level": 89, "attributes": [[1, 327600], [2, 3276000], [3, 163800], [4, 163800]], "nextLevel": 90, "id": 75002}, {"cost": [[11, 137101, 180]], "level": 90, "attributes": [[1, 333000], [2, 3330000], [3, 166500], [4, 166500]], "nextLevel": 91, "id": 75002}, {"cost": [[11, 137101, 190]], "level": 91, "attributes": [[1, 338600], [2, 3386000], [3, 169300], [4, 169300]], "nextLevel": 92, "id": 75002}, {"cost": [[11, 137101, 190]], "level": 92, "attributes": [[1, 344200], [2, 3442000], [3, 172100], [4, 172100]], "nextLevel": 93, "id": 75002}, {"cost": [[11, 137101, 190]], "level": 93, "attributes": [[1, 349800], [2, 3498000], [3, 174900], [4, 174900]], "nextLevel": 94, "id": 75002}, {"cost": [[11, 137101, 190]], "level": 94, "attributes": [[1, 355400], [2, 3554000], [3, 177700], [4, 177700]], "nextLevel": 95, "id": 75002}, {"cost": [[11, 137101, 190]], "level": 95, "attributes": [[1, 361000], [2, 3610000], [3, 180500], [4, 180500]], "nextLevel": 96, "id": 75002}, {"cost": [[11, 137101, 200]], "level": 96, "attributes": [[1, 366800], [2, 3668000], [3, 183400], [4, 183400]], "nextLevel": 97, "id": 75002}, {"cost": [[11, 137101, 200]], "level": 97, "attributes": [[1, 372600], [2, 3726000], [3, 186300], [4, 186300]], "nextLevel": 98, "id": 75002}, {"cost": [[11, 137101, 200]], "level": 98, "attributes": [[1, 378400], [2, 3784000], [3, 189200], [4, 189200]], "nextLevel": 99, "id": 75002}, {"cost": [[11, 137101, 200]], "level": 99, "attributes": [[1, 384200], [2, 3842000], [3, 192100], [4, 192100]], "nextLevel": 100, "id": 75002}, {"cost": [[11, 137101, 200]], "level": 100, "attributes": [[1, 390000], [2, 3900000], [3, 195000], [4, 195000]], "nextLevel": 101, "id": 75002}, {"cost": [[11, 137101, 210]], "level": 101, "attributes": [[1, 396000], [2, 3960000], [3, 198000], [4, 198000]], "nextLevel": 102, "id": 75002}, {"cost": [[11, 137101, 210]], "level": 102, "attributes": [[1, 402000], [2, 4020000], [3, 201000], [4, 201000]], "nextLevel": 103, "id": 75002}, {"cost": [[11, 137101, 210]], "level": 103, "attributes": [[1, 408000], [2, 4080000], [3, 204000], [4, 204000]], "nextLevel": 104, "id": 75002}, {"cost": [[11, 137101, 210]], "level": 104, "attributes": [[1, 414000], [2, 4140000], [3, 207000], [4, 207000]], "nextLevel": 105, "id": 75002}, {"cost": [[11, 137101, 210]], "level": 105, "attributes": [[1, 420000], [2, 4200000], [3, 210000], [4, 210000]], "nextLevel": 106, "id": 75002}, {"cost": [[11, 137101, 220]], "level": 106, "attributes": [[1, 426200], [2, 4262000], [3, 213100], [4, 213100]], "nextLevel": 107, "id": 75002}, {"cost": [[11, 137101, 220]], "level": 107, "attributes": [[1, 432400], [2, 4324000], [3, 216200], [4, 216200]], "nextLevel": 108, "id": 75002}, {"cost": [[11, 137101, 220]], "level": 108, "attributes": [[1, 438600], [2, 4386000], [3, 219300], [4, 219300]], "nextLevel": 109, "id": 75002}, {"cost": [[11, 137101, 220]], "level": 109, "attributes": [[1, 444800], [2, 4448000], [3, 222400], [4, 222400]], "nextLevel": 110, "id": 75002}, {"cost": [[11, 137101, 220]], "level": 110, "attributes": [[1, 451000], [2, 4510000], [3, 225500], [4, 225500]], "nextLevel": 111, "id": 75002}, {"cost": [[11, 137101, 230]], "level": 111, "attributes": [[1, 457400], [2, 4574000], [3, 228700], [4, 228700]], "nextLevel": 112, "id": 75002}, {"cost": [[11, 137101, 230]], "level": 112, "attributes": [[1, 463800], [2, 4638000], [3, 231900], [4, 231900]], "nextLevel": 113, "id": 75002}, {"cost": [[11, 137101, 230]], "level": 113, "attributes": [[1, 470200], [2, 4702000], [3, 235100], [4, 235100]], "nextLevel": 114, "id": 75002}, {"cost": [[11, 137101, 230]], "level": 114, "attributes": [[1, 476600], [2, 4766000], [3, 238300], [4, 238300]], "nextLevel": 115, "id": 75002}, {"cost": [[11, 137101, 230]], "level": 115, "attributes": [[1, 483000], [2, 4830000], [3, 241500], [4, 241500]], "nextLevel": 116, "id": 75002}, {"cost": [[11, 137101, 240]], "level": 116, "attributes": [[1, 489600], [2, 4896000], [3, 244800], [4, 244800]], "nextLevel": 117, "id": 75002}, {"cost": [[11, 137101, 240]], "level": 117, "attributes": [[1, 496200], [2, 4962000], [3, 248100], [4, 248100]], "nextLevel": 118, "id": 75002}, {"cost": [[11, 137101, 240]], "level": 118, "attributes": [[1, 502800], [2, 5028000], [3, 251400], [4, 251400]], "nextLevel": 119, "id": 75002}, {"cost": [[11, 137101, 240]], "level": 119, "attributes": [[1, 509400], [2, 5094000], [3, 254700], [4, 254700]], "nextLevel": 120, "id": 75002}, {"cost": [[11, 137101, 240]], "level": 120, "attributes": [[1, 516000], [2, 5160000], [3, 258000], [4, 258000]], "nextLevel": 121, "id": 75002}, {"cost": [[11, 137101, 250]], "level": 121, "attributes": [[1, 522800], [2, 5228000], [3, 261400], [4, 261400]], "nextLevel": 122, "id": 75002}, {"cost": [[11, 137101, 250]], "level": 122, "attributes": [[1, 529600], [2, 5296000], [3, 264800], [4, 264800]], "nextLevel": 123, "id": 75002}, {"cost": [[11, 137101, 250]], "level": 123, "attributes": [[1, 536400], [2, 5364000], [3, 268200], [4, 268200]], "nextLevel": 124, "id": 75002}, {"cost": [[11, 137101, 250]], "level": 124, "attributes": [[1, 543200], [2, 5432000], [3, 271600], [4, 271600]], "nextLevel": 125, "id": 75002}, {"cost": [[11, 137101, 250]], "level": 125, "attributes": [[1, 550000], [2, 5500000], [3, 275000], [4, 275000]], "nextLevel": 126, "id": 75002}, {"cost": [[11, 137101, 260]], "level": 126, "attributes": [[1, 557000], [2, 5570000], [3, 278500], [4, 278500]], "nextLevel": 127, "id": 75002}, {"cost": [[11, 137101, 260]], "level": 127, "attributes": [[1, 564000], [2, 5640000], [3, 282000], [4, 282000]], "nextLevel": 128, "id": 75002}, {"cost": [[11, 137101, 260]], "level": 128, "attributes": [[1, 571000], [2, 5710000], [3, 285500], [4, 285500]], "nextLevel": 129, "id": 75002}, {"cost": [[11, 137101, 260]], "level": 129, "attributes": [[1, 578000], [2, 5780000], [3, 289000], [4, 289000]], "nextLevel": 130, "id": 75002}, {"cost": [[11, 137101, 260]], "level": 130, "attributes": [[1, 585000], [2, 5850000], [3, 292500], [4, 292500]], "nextLevel": 131, "id": 75002}, {"cost": [[11, 137101, 270]], "level": 131, "attributes": [[1, 592200], [2, 5922000], [3, 296100], [4, 296100]], "nextLevel": 132, "id": 75002}, {"cost": [[11, 137101, 270]], "level": 132, "attributes": [[1, 599400], [2, 5994000], [3, 299700], [4, 299700]], "nextLevel": 133, "id": 75002}, {"cost": [[11, 137101, 270]], "level": 133, "attributes": [[1, 606600], [2, 6066000], [3, 303300], [4, 303300]], "nextLevel": 134, "id": 75002}, {"cost": [[11, 137101, 270]], "level": 134, "attributes": [[1, 613800], [2, 6138000], [3, 306900], [4, 306900]], "nextLevel": 135, "id": 75002}, {"cost": [[11, 137101, 270]], "level": 135, "attributes": [[1, 621000], [2, 6210000], [3, 310500], [4, 310500]], "nextLevel": 136, "id": 75002}, {"cost": [[11, 137101, 280]], "level": 136, "attributes": [[1, 628400], [2, 6284000], [3, 314200], [4, 314200]], "nextLevel": 137, "id": 75002}, {"cost": [[11, 137101, 280]], "level": 137, "attributes": [[1, 635800], [2, 6358000], [3, 317900], [4, 317900]], "nextLevel": 138, "id": 75002}, {"cost": [[11, 137101, 280]], "level": 138, "attributes": [[1, 643200], [2, 6432000], [3, 321600], [4, 321600]], "nextLevel": 139, "id": 75002}, {"cost": [[11, 137101, 280]], "level": 139, "attributes": [[1, 650600], [2, 6506000], [3, 325300], [4, 325300]], "nextLevel": 140, "id": 75002}, {"cost": [[11, 137101, 280]], "level": 140, "attributes": [[1, 658000], [2, 6580000], [3, 329000], [4, 329000]], "nextLevel": 141, "id": 75002}, {"cost": [[11, 137101, 290]], "level": 141, "attributes": [[1, 665600], [2, 6656000], [3, 332800], [4, 332800]], "nextLevel": 142, "id": 75002}, {"cost": [[11, 137101, 290]], "level": 142, "attributes": [[1, 673200], [2, 6732000], [3, 336600], [4, 336600]], "nextLevel": 143, "id": 75002}, {"cost": [[11, 137101, 290]], "level": 143, "attributes": [[1, 680800], [2, 6808000], [3, 340400], [4, 340400]], "nextLevel": 144, "id": 75002}, {"cost": [[11, 137101, 290]], "level": 144, "attributes": [[1, 688400], [2, 6884000], [3, 344200], [4, 344200]], "nextLevel": 145, "id": 75002}, {"cost": [[11, 137101, 290]], "level": 145, "attributes": [[1, 696000], [2, 6960000], [3, 348000], [4, 348000]], "nextLevel": 146, "id": 75002}, {"cost": [[11, 137101, 300]], "level": 146, "attributes": [[1, 703800], [2, 7038000], [3, 351900], [4, 351900]], "nextLevel": 147, "id": 75002}, {"cost": [[11, 137101, 300]], "level": 147, "attributes": [[1, 711600], [2, 7116000], [3, 355800], [4, 355800]], "nextLevel": 148, "id": 75002}, {"cost": [[11, 137101, 300]], "level": 148, "attributes": [[1, 719400], [2, 7194000], [3, 359700], [4, 359700]], "nextLevel": 149, "id": 75002}, {"cost": [[11, 137101, 300]], "level": 149, "attributes": [[1, 727200], [2, 7272000], [3, 363600], [4, 363600]], "nextLevel": 150, "id": 75002}, {"cost": [[11, 137101, 300]], "level": 150, "attributes": [[1, 735000], [2, 7350000], [3, 367500], [4, 367500]], "nextLevel": 151, "id": 75002}, {"cost": [[11, 137101, 310]], "level": 151, "attributes": [[1, 743000], [2, 7430000], [3, 371500], [4, 371500]], "nextLevel": 152, "id": 75002}, {"cost": [[11, 137101, 310]], "level": 152, "attributes": [[1, 751000], [2, 7510000], [3, 375500], [4, 375500]], "nextLevel": 153, "id": 75002}, {"cost": [[11, 137101, 310]], "level": 153, "attributes": [[1, 759000], [2, 7590000], [3, 379500], [4, 379500]], "nextLevel": 154, "id": 75002}, {"cost": [[11, 137101, 310]], "level": 154, "attributes": [[1, 767000], [2, 7670000], [3, 383500], [4, 383500]], "nextLevel": 155, "id": 75002}, {"cost": [[11, 137101, 310]], "level": 155, "attributes": [[1, 775000], [2, 7750000], [3, 387500], [4, 387500]], "nextLevel": 156, "id": 75002}, {"cost": [[11, 137101, 320]], "level": 156, "attributes": [[1, 783200], [2, 7832000], [3, 391600], [4, 391600]], "nextLevel": 157, "id": 75002}, {"cost": [[11, 137101, 320]], "level": 157, "attributes": [[1, 791400], [2, 7914000], [3, 395700], [4, 395700]], "nextLevel": 158, "id": 75002}, {"cost": [[11, 137101, 320]], "level": 158, "attributes": [[1, 799600], [2, 7996000], [3, 399800], [4, 399800]], "nextLevel": 159, "id": 75002}, {"cost": [[11, 137101, 320]], "level": 159, "attributes": [[1, 807800], [2, 8078000], [3, 403900], [4, 403900]], "nextLevel": 160, "id": 75002}, {"cost": [[11, 137101, 320]], "level": 160, "attributes": [[1, 816000], [2, 8160000], [3, 408000], [4, 408000]], "nextLevel": 161, "id": 75002}, {"cost": [[11, 137101, 330]], "level": 161, "attributes": [[1, 824400], [2, 8244000], [3, 412200], [4, 412200]], "nextLevel": 162, "id": 75002}, {"cost": [[11, 137101, 330]], "level": 162, "attributes": [[1, 832800], [2, 8328000], [3, 416400], [4, 416400]], "nextLevel": 163, "id": 75002}, {"cost": [[11, 137101, 330]], "level": 163, "attributes": [[1, 841200], [2, 8412000], [3, 420600], [4, 420600]], "nextLevel": 164, "id": 75002}, {"cost": [[11, 137101, 330]], "level": 164, "attributes": [[1, 849600], [2, 8496000], [3, 424800], [4, 424800]], "nextLevel": 165, "id": 75002}, {"cost": [[11, 137101, 330]], "level": 165, "attributes": [[1, 858000], [2, 8580000], [3, 429000], [4, 429000]], "nextLevel": 166, "id": 75002}, {"cost": [[11, 137101, 340]], "level": 166, "attributes": [[1, 866600], [2, 8666000], [3, 433300], [4, 433300]], "nextLevel": 167, "id": 75002}, {"cost": [[11, 137101, 340]], "level": 167, "attributes": [[1, 875200], [2, 8752000], [3, 437600], [4, 437600]], "nextLevel": 168, "id": 75002}, {"cost": [[11, 137101, 340]], "level": 168, "attributes": [[1, 883800], [2, 8838000], [3, 441900], [4, 441900]], "nextLevel": 169, "id": 75002}, {"cost": [[11, 137101, 340]], "level": 169, "attributes": [[1, 892400], [2, 8924000], [3, 446200], [4, 446200]], "nextLevel": 170, "id": 75002}, {"cost": [[11, 137101, 340]], "level": 170, "attributes": [[1, 901000], [2, 9010000], [3, 450500], [4, 450500]], "nextLevel": 171, "id": 75002}, {"cost": [[11, 137101, 350]], "level": 171, "attributes": [[1, 909800], [2, 9098000], [3, 454900], [4, 454900]], "nextLevel": 172, "id": 75002}, {"cost": [[11, 137101, 350]], "level": 172, "attributes": [[1, 918600], [2, 9186000], [3, 459300], [4, 459300]], "nextLevel": 173, "id": 75002}, {"cost": [[11, 137101, 350]], "level": 173, "attributes": [[1, 927400], [2, 9274000], [3, 463700], [4, 463700]], "nextLevel": 174, "id": 75002}, {"cost": [[11, 137101, 350]], "level": 174, "attributes": [[1, 936200], [2, 9362000], [3, 468100], [4, 468100]], "nextLevel": 175, "id": 75002}, {"cost": [[11, 137101, 350]], "level": 175, "attributes": [[1, 945000], [2, 9450000], [3, 472500], [4, 472500]], "nextLevel": 176, "id": 75002}, {"cost": [[11, 137101, 360]], "level": 176, "attributes": [[1, 954000], [2, 9540000], [3, 477000], [4, 477000]], "nextLevel": 177, "id": 75002}, {"cost": [[11, 137101, 360]], "level": 177, "attributes": [[1, 963000], [2, 9630000], [3, 481500], [4, 481500]], "nextLevel": 178, "id": 75002}, {"cost": [[11, 137101, 360]], "level": 178, "attributes": [[1, 972000], [2, 9720000], [3, 486000], [4, 486000]], "nextLevel": 179, "id": 75002}, {"cost": [[11, 137101, 360]], "level": 179, "attributes": [[1, 981000], [2, 9810000], [3, 490500], [4, 490500]], "nextLevel": 180, "id": 75002}, {"cost": [[11, 137101, 360]], "level": 180, "attributes": [[1, 990000], [2, 9900000], [3, 495000], [4, 495000]], "nextLevel": 181, "id": 75002}, {"cost": [[11, 137101, 370]], "level": 181, "attributes": [[1, 999200], [2, 9992000], [3, 499600], [4, 499600]], "nextLevel": 182, "id": 75002}, {"cost": [[11, 137101, 370]], "level": 182, "attributes": [[1, 1008400], [2, 10084000], [3, 504200], [4, 504200]], "nextLevel": 183, "id": 75002}, {"cost": [[11, 137101, 370]], "level": 183, "attributes": [[1, 1017600], [2, 10176000], [3, 508800], [4, 508800]], "nextLevel": 184, "id": 75002}, {"cost": [[11, 137101, 370]], "level": 184, "attributes": [[1, 1026800], [2, 10268000], [3, 513400], [4, 513400]], "nextLevel": 185, "id": 75002}, {"cost": [[11, 137101, 370]], "level": 185, "attributes": [[1, 1036000], [2, 10360000], [3, 518000], [4, 518000]], "nextLevel": 186, "id": 75002}, {"cost": [[11, 137101, 380]], "level": 186, "attributes": [[1, 1045400], [2, 10454000], [3, 522700], [4, 522700]], "nextLevel": 187, "id": 75002}, {"cost": [[11, 137101, 380]], "level": 187, "attributes": [[1, 1054800], [2, 10548000], [3, 527400], [4, 527400]], "nextLevel": 188, "id": 75002}, {"cost": [[11, 137101, 380]], "level": 188, "attributes": [[1, 1064200], [2, 10642000], [3, 532100], [4, 532100]], "nextLevel": 189, "id": 75002}, {"cost": [[11, 137101, 380]], "level": 189, "attributes": [[1, 1073600], [2, 10736000], [3, 536800], [4, 536800]], "nextLevel": 190, "id": 75002}, {"cost": [[11, 137101, 380]], "level": 190, "attributes": [[1, 1083000], [2, 10830000], [3, 541500], [4, 541500]], "nextLevel": 191, "id": 75002}, {"cost": [[11, 137101, 390]], "level": 191, "attributes": [[1, 1092600], [2, 10926000], [3, 546300], [4, 546300]], "nextLevel": 192, "id": 75002}, {"cost": [[11, 137101, 390]], "level": 192, "attributes": [[1, 1102200], [2, 11022000], [3, 551100], [4, 551100]], "nextLevel": 193, "id": 75002}, {"cost": [[11, 137101, 390]], "level": 193, "attributes": [[1, 1111800], [2, 11118000], [3, 555900], [4, 555900]], "nextLevel": 194, "id": 75002}, {"cost": [[11, 137101, 390]], "level": 194, "attributes": [[1, 1121400], [2, 11214000], [3, 560700], [4, 560700]], "nextLevel": 195, "id": 75002}, {"cost": [[11, 137101, 390]], "level": 195, "attributes": [[1, 1131000], [2, 11310000], [3, 565500], [4, 565500]], "nextLevel": 196, "id": 75002}, {"cost": [[11, 137101, 400]], "level": 196, "attributes": [[1, 1140800], [2, 11408000], [3, 570400], [4, 570400]], "nextLevel": 197, "id": 75002}, {"cost": [[11, 137101, 400]], "level": 197, "attributes": [[1, 1150600], [2, 11506000], [3, 575300], [4, 575300]], "nextLevel": 198, "id": 75002}, {"cost": [[11, 137101, 400]], "level": 198, "attributes": [[1, 1160400], [2, 11604000], [3, 580200], [4, 580200]], "nextLevel": 199, "id": 75002}, {"cost": [[11, 137101, 400]], "level": 199, "attributes": [[1, 1170200], [2, 11702000], [3, 585100], [4, 585100]], "nextLevel": 200, "id": 75002}, {"cost": [[11, 137101, 400]], "level": 200, "attributes": [[1, 1180000], [2, 11800000], [3, 590000], [4, 590000]], "nextLevel": 201, "id": 75002}, {"cost": [[11, 137101, 410]], "level": 201, "attributes": [[1, 1190000], [2, 11900000], [3, 595000], [4, 595000]], "nextLevel": 202, "id": 75002}, {"cost": [[11, 137101, 410]], "level": 202, "attributes": [[1, 1200000], [2, 12000000], [3, 600000], [4, 600000]], "nextLevel": 203, "id": 75002}, {"cost": [[11, 137101, 410]], "level": 203, "attributes": [[1, 1210000], [2, 12100000], [3, 605000], [4, 605000]], "nextLevel": 204, "id": 75002}, {"cost": [[11, 137101, 410]], "level": 204, "attributes": [[1, 1220000], [2, 12200000], [3, 610000], [4, 610000]], "nextLevel": 205, "id": 75002}, {"cost": [[11, 137101, 410]], "level": 205, "attributes": [[1, 1230000], [2, 12300000], [3, 615000], [4, 615000]], "nextLevel": 206, "id": 75002}, {"cost": [[11, 137101, 420]], "level": 206, "attributes": [[1, 1240200], [2, 12402000], [3, 620100], [4, 620100]], "nextLevel": 207, "id": 75002}, {"cost": [[11, 137101, 420]], "level": 207, "attributes": [[1, 1250400], [2, 12504000], [3, 625200], [4, 625200]], "nextLevel": 208, "id": 75002}, {"cost": [[11, 137101, 420]], "level": 208, "attributes": [[1, 1260600], [2, 12606000], [3, 630300], [4, 630300]], "nextLevel": 209, "id": 75002}, {"cost": [[11, 137101, 420]], "level": 209, "attributes": [[1, 1270800], [2, 12708000], [3, 635400], [4, 635400]], "nextLevel": 210, "id": 75002}, {"cost": [[11, 137101, 420]], "level": 210, "attributes": [[1, 1281000], [2, 12810000], [3, 640500], [4, 640500]], "nextLevel": 211, "id": 75002}, {"cost": [[11, 137101, 430]], "level": 211, "attributes": [[1, 1291400], [2, 12914000], [3, 645700], [4, 645700]], "nextLevel": 212, "id": 75002}, {"cost": [[11, 137101, 430]], "level": 212, "attributes": [[1, 1301800], [2, 13018000], [3, 650900], [4, 650900]], "nextLevel": 213, "id": 75002}, {"cost": [[11, 137101, 430]], "level": 213, "attributes": [[1, 1312200], [2, 13122000], [3, 656100], [4, 656100]], "nextLevel": 214, "id": 75002}, {"cost": [[11, 137101, 430]], "level": 214, "attributes": [[1, 1322600], [2, 13226000], [3, 661300], [4, 661300]], "nextLevel": 215, "id": 75002}, {"cost": [[11, 137101, 430]], "level": 215, "attributes": [[1, 1333000], [2, 13330000], [3, 666500], [4, 666500]], "nextLevel": 216, "id": 75002}, {"cost": [[11, 137101, 440]], "level": 216, "attributes": [[1, 1343600], [2, 13436000], [3, 671800], [4, 671800]], "nextLevel": 217, "id": 75002}, {"cost": [[11, 137101, 440]], "level": 217, "attributes": [[1, 1354200], [2, 13542000], [3, 677100], [4, 677100]], "nextLevel": 218, "id": 75002}, {"cost": [[11, 137101, 440]], "level": 218, "attributes": [[1, 1364800], [2, 13648000], [3, 682400], [4, 682400]], "nextLevel": 219, "id": 75002}, {"cost": [[11, 137101, 440]], "level": 219, "attributes": [[1, 1375400], [2, 13754000], [3, 687700], [4, 687700]], "nextLevel": 220, "id": 75002}, {"cost": [[11, 137101, 440]], "level": 220, "attributes": [[1, 1386000], [2, 13860000], [3, 693000], [4, 693000]], "nextLevel": 221, "id": 75002}, {"cost": [[11, 137101, 450]], "level": 221, "attributes": [[1, 1396800], [2, 13968000], [3, 698400], [4, 698400]], "nextLevel": 222, "id": 75002}, {"cost": [[11, 137101, 450]], "level": 222, "attributes": [[1, 1407600], [2, 14076000], [3, 703800], [4, 703800]], "nextLevel": 223, "id": 75002}, {"cost": [[11, 137101, 450]], "level": 223, "attributes": [[1, 1418400], [2, 14184000], [3, 709200], [4, 709200]], "nextLevel": 224, "id": 75002}, {"cost": [[11, 137101, 450]], "level": 224, "attributes": [[1, 1429200], [2, 14292000], [3, 714600], [4, 714600]], "nextLevel": 225, "id": 75002}, {"cost": [[11, 137101, 450]], "level": 225, "attributes": [[1, 1440000], [2, 14400000], [3, 720000], [4, 720000]], "nextLevel": 226, "id": 75002}, {"cost": [[11, 137101, 460]], "level": 226, "attributes": [[1, 1451000], [2, 14510000], [3, 725500], [4, 725500]], "nextLevel": 227, "id": 75002}, {"cost": [[11, 137101, 460]], "level": 227, "attributes": [[1, 1462000], [2, 14620000], [3, 731000], [4, 731000]], "nextLevel": 228, "id": 75002}, {"cost": [[11, 137101, 460]], "level": 228, "attributes": [[1, 1473000], [2, 14730000], [3, 736500], [4, 736500]], "nextLevel": 229, "id": 75002}, {"cost": [[11, 137101, 460]], "level": 229, "attributes": [[1, 1484000], [2, 14840000], [3, 742000], [4, 742000]], "nextLevel": 230, "id": 75002}, {"cost": [[11, 137101, 460]], "level": 230, "attributes": [[1, 1495000], [2, 14950000], [3, 747500], [4, 747500]], "nextLevel": 231, "id": 75002}, {"cost": [[11, 137101, 470]], "level": 231, "attributes": [[1, 1506200], [2, 15062000], [3, 753100], [4, 753100]], "nextLevel": 232, "id": 75002}, {"cost": [[11, 137101, 470]], "level": 232, "attributes": [[1, 1517400], [2, 15174000], [3, 758700], [4, 758700]], "nextLevel": 233, "id": 75002}, {"cost": [[11, 137101, 470]], "level": 233, "attributes": [[1, 1528600], [2, 15286000], [3, 764300], [4, 764300]], "nextLevel": 234, "id": 75002}, {"cost": [[11, 137101, 470]], "level": 234, "attributes": [[1, 1539800], [2, 15398000], [3, 769900], [4, 769900]], "nextLevel": 235, "id": 75002}, {"cost": [[11, 137101, 470]], "level": 235, "attributes": [[1, 1551000], [2, 15510000], [3, 775500], [4, 775500]], "nextLevel": 236, "id": 75002}, {"cost": [[11, 137101, 480]], "level": 236, "attributes": [[1, 1562400], [2, 15624000], [3, 781200], [4, 781200]], "nextLevel": 237, "id": 75002}, {"cost": [[11, 137101, 480]], "level": 237, "attributes": [[1, 1573800], [2, 15738000], [3, 786900], [4, 786900]], "nextLevel": 238, "id": 75002}, {"cost": [[11, 137101, 480]], "level": 238, "attributes": [[1, 1585200], [2, 15852000], [3, 792600], [4, 792600]], "nextLevel": 239, "id": 75002}, {"cost": [[11, 137101, 480]], "level": 239, "attributes": [[1, 1596600], [2, 15966000], [3, 798300], [4, 798300]], "nextLevel": 240, "id": 75002}, {"cost": [[11, 137101, 480]], "level": 240, "attributes": [[1, 1608000], [2, 16080000], [3, 804000], [4, 804000]], "nextLevel": 241, "id": 75002}, {"cost": [[11, 137101, 490]], "level": 241, "attributes": [[1, 1619600], [2, 16196000], [3, 809800], [4, 809800]], "nextLevel": 242, "id": 75002}, {"cost": [[11, 137101, 490]], "level": 242, "attributes": [[1, 1631200], [2, 16312000], [3, 815600], [4, 815600]], "nextLevel": 243, "id": 75002}, {"cost": [[11, 137101, 490]], "level": 243, "attributes": [[1, 1642800], [2, 16428000], [3, 821400], [4, 821400]], "nextLevel": 244, "id": 75002}, {"cost": [[11, 137101, 490]], "level": 244, "attributes": [[1, 1654400], [2, 16544000], [3, 827200], [4, 827200]], "nextLevel": 245, "id": 75002}, {"cost": [[11, 137101, 490]], "level": 245, "attributes": [[1, 1666000], [2, 16660000], [3, 833000], [4, 833000]], "nextLevel": 246, "id": 75002}, {"cost": [[11, 137101, 500]], "level": 246, "attributes": [[1, 1677800], [2, 16778000], [3, 838900], [4, 838900]], "nextLevel": 247, "id": 75002}, {"cost": [[11, 137101, 500]], "level": 247, "attributes": [[1, 1689600], [2, 16896000], [3, 844800], [4, 844800]], "nextLevel": 248, "id": 75002}, {"cost": [[11, 137101, 500]], "level": 248, "attributes": [[1, 1701400], [2, 17014000], [3, 850700], [4, 850700]], "nextLevel": 249, "id": 75002}, {"cost": [[11, 137101, 500]], "level": 249, "attributes": [[1, 1713200], [2, 17132000], [3, 856600], [4, 856600]], "nextLevel": 250, "id": 75002}, {"cost": [[11, 137101, 500]], "level": 250, "attributes": [[1, 1725000], [2, 17250000], [3, 862500], [4, 862500]], "nextLevel": 251, "id": 75002}, {"cost": [[11, 137101, 510]], "level": 251, "attributes": [[1, 1737000], [2, 17370000], [3, 868500], [4, 868500]], "nextLevel": 252, "id": 75002}, {"cost": [[11, 137101, 510]], "level": 252, "attributes": [[1, 1749000], [2, 17490000], [3, 874500], [4, 874500]], "nextLevel": 253, "id": 75002}, {"cost": [[11, 137101, 510]], "level": 253, "attributes": [[1, 1761000], [2, 17610000], [3, 880500], [4, 880500]], "nextLevel": 254, "id": 75002}, {"cost": [[11, 137101, 510]], "level": 254, "attributes": [[1, 1773000], [2, 17730000], [3, 886500], [4, 886500]], "nextLevel": 255, "id": 75002}, {"cost": [[11, 137101, 510]], "level": 255, "attributes": [[1, 1785000], [2, 17850000], [3, 892500], [4, 892500]], "nextLevel": 256, "id": 75002}, {"cost": [[11, 137101, 520]], "level": 256, "attributes": [[1, 1797200], [2, 17972000], [3, 898600], [4, 898600]], "nextLevel": 257, "id": 75002}, {"cost": [[11, 137101, 520]], "level": 257, "attributes": [[1, 1809400], [2, 18094000], [3, 904700], [4, 904700]], "nextLevel": 258, "id": 75002}, {"cost": [[11, 137101, 520]], "level": 258, "attributes": [[1, 1821600], [2, 18216000], [3, 910800], [4, 910800]], "nextLevel": 259, "id": 75002}, {"cost": [[11, 137101, 520]], "level": 259, "attributes": [[1, 1833800], [2, 18338000], [3, 916900], [4, 916900]], "nextLevel": 260, "id": 75002}, {"cost": [[11, 137101, 520]], "level": 260, "attributes": [[1, 1846000], [2, 18460000], [3, 923000], [4, 923000]], "nextLevel": 261, "id": 75002}, {"cost": [[11, 137101, 530]], "level": 261, "attributes": [[1, 1858400], [2, 18584000], [3, 929200], [4, 929200]], "nextLevel": 262, "id": 75002}, {"cost": [[11, 137101, 530]], "level": 262, "attributes": [[1, 1870800], [2, 18708000], [3, 935400], [4, 935400]], "nextLevel": 263, "id": 75002}, {"cost": [[11, 137101, 530]], "level": 263, "attributes": [[1, 1883200], [2, 18832000], [3, 941600], [4, 941600]], "nextLevel": 264, "id": 75002}, {"cost": [[11, 137101, 530]], "level": 264, "attributes": [[1, 1895600], [2, 18956000], [3, 947800], [4, 947800]], "nextLevel": 265, "id": 75002}, {"cost": [[11, 137101, 530]], "level": 265, "attributes": [[1, 1908000], [2, 19080000], [3, 954000], [4, 954000]], "nextLevel": 266, "id": 75002}, {"cost": [[11, 137101, 540]], "level": 266, "attributes": [[1, 1920600], [2, 19206000], [3, 960300], [4, 960300]], "nextLevel": 267, "id": 75002}, {"cost": [[11, 137101, 540]], "level": 267, "attributes": [[1, 1933200], [2, 19332000], [3, 966600], [4, 966600]], "nextLevel": 268, "id": 75002}, {"cost": [[11, 137101, 540]], "level": 268, "attributes": [[1, 1945800], [2, 19458000], [3, 972900], [4, 972900]], "nextLevel": 269, "id": 75002}, {"cost": [[11, 137101, 540]], "level": 269, "attributes": [[1, 1958400], [2, 19584000], [3, 979200], [4, 979200]], "nextLevel": 270, "id": 75002}, {"cost": [[11, 137101, 540]], "level": 270, "attributes": [[1, 1971000], [2, 19710000], [3, 985500], [4, 985500]], "nextLevel": 271, "id": 75002}, {"cost": [[11, 137101, 550]], "level": 271, "attributes": [[1, 1983800], [2, 19838000], [3, 991900], [4, 991900]], "nextLevel": 272, "id": 75002}, {"cost": [[11, 137101, 550]], "level": 272, "attributes": [[1, 1996600], [2, 19966000], [3, 998300], [4, 998300]], "nextLevel": 273, "id": 75002}, {"cost": [[11, 137101, 550]], "level": 273, "attributes": [[1, 2009400], [2, 20094000], [3, 1004700], [4, 1004700]], "nextLevel": 274, "id": 75002}, {"cost": [[11, 137101, 550]], "level": 274, "attributes": [[1, 2022200], [2, 20222000], [3, 1011100], [4, 1011100]], "nextLevel": 275, "id": 75002}, {"cost": [[11, 137101, 550]], "level": 275, "attributes": [[1, 2035000], [2, 20350000], [3, 1017500], [4, 1017500]], "nextLevel": 276, "id": 75002}, {"cost": [[11, 137101, 560]], "level": 276, "attributes": [[1, 2048000], [2, 20480000], [3, 1024000], [4, 1024000]], "nextLevel": 277, "id": 75002}, {"cost": [[11, 137101, 560]], "level": 277, "attributes": [[1, 2061000], [2, 20610000], [3, 1030500], [4, 1030500]], "nextLevel": 278, "id": 75002}, {"cost": [[11, 137101, 560]], "level": 278, "attributes": [[1, 2074000], [2, 20740000], [3, 1037000], [4, 1037000]], "nextLevel": 279, "id": 75002}, {"cost": [[11, 137101, 560]], "level": 279, "attributes": [[1, 2087000], [2, 20870000], [3, 1043500], [4, 1043500]], "nextLevel": 280, "id": 75002}, {"cost": [[11, 137101, 560]], "level": 280, "attributes": [[1, 2100000], [2, 21000000], [3, 1050000], [4, 1050000]], "nextLevel": 281, "id": 75002}, {"cost": [[11, 137101, 570]], "level": 281, "attributes": [[1, 2113200], [2, 21132000], [3, 1056600], [4, 1056600]], "nextLevel": 282, "id": 75002}, {"cost": [[11, 137101, 570]], "level": 282, "attributes": [[1, 2126400], [2, 21264000], [3, 1063200], [4, 1063200]], "nextLevel": 283, "id": 75002}, {"cost": [[11, 137101, 570]], "level": 283, "attributes": [[1, 2139600], [2, 21396000], [3, 1069800], [4, 1069800]], "nextLevel": 284, "id": 75002}, {"cost": [[11, 137101, 570]], "level": 284, "attributes": [[1, 2152800], [2, 21528000], [3, 1076400], [4, 1076400]], "nextLevel": 285, "id": 75002}, {"cost": [[11, 137101, 570]], "level": 285, "attributes": [[1, 2166000], [2, 21660000], [3, 1083000], [4, 1083000]], "nextLevel": 286, "id": 75002}, {"cost": [[11, 137101, 580]], "level": 286, "attributes": [[1, 2179400], [2, 21794000], [3, 1089700], [4, 1089700]], "nextLevel": 287, "id": 75002}, {"cost": [[11, 137101, 580]], "level": 287, "attributes": [[1, 2192800], [2, 21928000], [3, 1096400], [4, 1096400]], "nextLevel": 288, "id": 75002}, {"cost": [[11, 137101, 580]], "level": 288, "attributes": [[1, 2206200], [2, 22062000], [3, 1103100], [4, 1103100]], "nextLevel": 289, "id": 75002}, {"cost": [[11, 137101, 580]], "level": 289, "attributes": [[1, 2219600], [2, 22196000], [3, 1109800], [4, 1109800]], "nextLevel": 290, "id": 75002}, {"cost": [[11, 137101, 580]], "level": 290, "attributes": [[1, 2233000], [2, 22330000], [3, 1116500], [4, 1116500]], "nextLevel": 291, "id": 75002}, {"cost": [[11, 137101, 590]], "level": 291, "attributes": [[1, 2246600], [2, 22466000], [3, 1123300], [4, 1123300]], "nextLevel": 292, "id": 75002}, {"cost": [[11, 137101, 590]], "level": 292, "attributes": [[1, 2260200], [2, 22602000], [3, 1130100], [4, 1130100]], "nextLevel": 293, "id": 75002}, {"cost": [[11, 137101, 590]], "level": 293, "attributes": [[1, 2273800], [2, 22738000], [3, 1136900], [4, 1136900]], "nextLevel": 294, "id": 75002}, {"cost": [[11, 137101, 590]], "level": 294, "attributes": [[1, 2287400], [2, 22874000], [3, 1143700], [4, 1143700]], "nextLevel": 295, "id": 75002}, {"cost": [[11, 137101, 590]], "level": 295, "attributes": [[1, 2301000], [2, 23010000], [3, 1150500], [4, 1150500]], "nextLevel": 296, "id": 75002}, {"cost": [[11, 137101, 600]], "level": 296, "attributes": [[1, 2314800], [2, 23148000], [3, 1157400], [4, 1157400]], "nextLevel": 297, "id": 75002}, {"cost": [[11, 137101, 600]], "level": 297, "attributes": [[1, 2328600], [2, 23286000], [3, 1164300], [4, 1164300]], "nextLevel": 298, "id": 75002}, {"cost": [[11, 137101, 600]], "level": 298, "attributes": [[1, 2342400], [2, 23424000], [3, 1171200], [4, 1171200]], "nextLevel": 299, "id": 75002}, {"cost": [[11, 137101, 600]], "level": 299, "attributes": [[1, 2356200], [2, 23562000], [3, 1178100], [4, 1178100]], "nextLevel": 300, "id": 75002}, {"cost": [[11, 137101, 600]], "level": 300, "attributes": [[1, 2370000], [2, 23700000], [3, 1185000], [4, 1185000]], "nextLevel": 301, "id": 75002}, {"cost": [[11, 137101, 610]], "level": 301, "attributes": [[1, 2384000], [2, 23840000], [3, 1192000], [4, 1192000]], "nextLevel": 302, "id": 75002}, {"cost": [[11, 137101, 610]], "level": 302, "attributes": [[1, 2398000], [2, 23980000], [3, 1199000], [4, 1199000]], "nextLevel": 303, "id": 75002}, {"cost": [[11, 137101, 610]], "level": 303, "attributes": [[1, 2412000], [2, 24120000], [3, 1206000], [4, 1206000]], "nextLevel": 304, "id": 75002}, {"cost": [[11, 137101, 610]], "level": 304, "attributes": [[1, 2426000], [2, 24260000], [3, 1213000], [4, 1213000]], "nextLevel": 305, "id": 75002}, {"cost": [[11, 137101, 610]], "level": 305, "attributes": [[1, 2440000], [2, 24400000], [3, 1220000], [4, 1220000]], "nextLevel": 306, "id": 75002}, {"cost": [[11, 137101, 620]], "level": 306, "attributes": [[1, 2454200], [2, 24542000], [3, 1227100], [4, 1227100]], "nextLevel": 307, "id": 75002}, {"cost": [[11, 137101, 620]], "level": 307, "attributes": [[1, 2468400], [2, 24684000], [3, 1234200], [4, 1234200]], "nextLevel": 308, "id": 75002}, {"cost": [[11, 137101, 620]], "level": 308, "attributes": [[1, 2482600], [2, 24826000], [3, 1241300], [4, 1241300]], "nextLevel": 309, "id": 75002}, {"cost": [[11, 137101, 620]], "level": 309, "attributes": [[1, 2496800], [2, 24968000], [3, 1248400], [4, 1248400]], "nextLevel": 310, "id": 75002}, {"cost": [[11, 137101, 620]], "level": 310, "attributes": [[1, 2511000], [2, 25110000], [3, 1255500], [4, 1255500]], "nextLevel": 311, "id": 75002}, {"cost": [[11, 137101, 630]], "level": 311, "attributes": [[1, 2525400], [2, 25254000], [3, 1262700], [4, 1262700]], "nextLevel": 312, "id": 75002}, {"cost": [[11, 137101, 630]], "level": 312, "attributes": [[1, 2539800], [2, 25398000], [3, 1269900], [4, 1269900]], "nextLevel": 313, "id": 75002}, {"cost": [[11, 137101, 630]], "level": 313, "attributes": [[1, 2554200], [2, 25542000], [3, 1277100], [4, 1277100]], "nextLevel": 314, "id": 75002}, {"cost": [[11, 137101, 630]], "level": 314, "attributes": [[1, 2568600], [2, 25686000], [3, 1284300], [4, 1284300]], "nextLevel": 315, "id": 75002}, {"cost": [[11, 137101, 630]], "level": 315, "attributes": [[1, 2583000], [2, 25830000], [3, 1291500], [4, 1291500]], "nextLevel": 316, "id": 75002}, {"cost": [[11, 137101, 640]], "level": 316, "attributes": [[1, 2597600], [2, 25976000], [3, 1298800], [4, 1298800]], "nextLevel": 317, "id": 75002}, {"cost": [[11, 137101, 640]], "level": 317, "attributes": [[1, 2612200], [2, 26122000], [3, 1306100], [4, 1306100]], "nextLevel": 318, "id": 75002}, {"cost": [[11, 137101, 640]], "level": 318, "attributes": [[1, 2626800], [2, 26268000], [3, 1313400], [4, 1313400]], "nextLevel": 319, "id": 75002}, {"cost": [[11, 137101, 640]], "level": 319, "attributes": [[1, 2641400], [2, 26414000], [3, 1320700], [4, 1320700]], "nextLevel": 320, "id": 75002}, {"cost": [[11, 137101, 640]], "level": 320, "attributes": [[1, 2656000], [2, 26560000], [3, 1328000], [4, 1328000]], "nextLevel": 321, "id": 75002}, {"cost": [[11, 137101, 650]], "level": 321, "attributes": [[1, 2670800], [2, 26708000], [3, 1335400], [4, 1335400]], "nextLevel": 322, "id": 75002}, {"cost": [[11, 137101, 650]], "level": 322, "attributes": [[1, 2685600], [2, 26856000], [3, 1342800], [4, 1342800]], "nextLevel": 323, "id": 75002}, {"cost": [[11, 137101, 650]], "level": 323, "attributes": [[1, 2700400], [2, 27004000], [3, 1350200], [4, 1350200]], "nextLevel": 324, "id": 75002}, {"cost": [[11, 137101, 650]], "level": 324, "attributes": [[1, 2715200], [2, 27152000], [3, 1357600], [4, 1357600]], "nextLevel": 325, "id": 75002}, {"cost": [[11, 137101, 650]], "level": 325, "attributes": [[1, 2730000], [2, 27300000], [3, 1365000], [4, 1365000]], "nextLevel": 326, "id": 75002}, {"cost": [[11, 137101, 660]], "level": 326, "attributes": [[1, 2745000], [2, 27450000], [3, 1372500], [4, 1372500]], "nextLevel": 327, "id": 75002}, {"cost": [[11, 137101, 660]], "level": 327, "attributes": [[1, 2760000], [2, 27600000], [3, 1380000], [4, 1380000]], "nextLevel": 328, "id": 75002}, {"cost": [[11, 137101, 660]], "level": 328, "attributes": [[1, 2775000], [2, 27750000], [3, 1387500], [4, 1387500]], "nextLevel": 329, "id": 75002}, {"cost": [[11, 137101, 660]], "level": 329, "attributes": [[1, 2790000], [2, 27900000], [3, 1395000], [4, 1395000]], "nextLevel": 330, "id": 75002}, {"cost": [[11, 137101, 660]], "level": 330, "attributes": [[1, 2805000], [2, 28050000], [3, 1402500], [4, 1402500]], "nextLevel": 331, "id": 75002}, {"cost": [[11, 137101, 670]], "level": 331, "attributes": [[1, 2820200], [2, 28202000], [3, 1410100], [4, 1410100]], "nextLevel": 332, "id": 75002}, {"cost": [[11, 137101, 670]], "level": 332, "attributes": [[1, 2835400], [2, 28354000], [3, 1417700], [4, 1417700]], "nextLevel": 333, "id": 75002}, {"cost": [[11, 137101, 670]], "level": 333, "attributes": [[1, 2850600], [2, 28506000], [3, 1425300], [4, 1425300]], "nextLevel": 334, "id": 75002}, {"cost": [[11, 137101, 670]], "level": 334, "attributes": [[1, 2865800], [2, 28658000], [3, 1432900], [4, 1432900]], "nextLevel": 335, "id": 75002}, {"cost": [[11, 137101, 670]], "level": 335, "attributes": [[1, 2881000], [2, 28810000], [3, 1440500], [4, 1440500]], "nextLevel": 336, "id": 75002}, {"cost": [[11, 137101, 680]], "level": 336, "attributes": [[1, 2896400], [2, 28964000], [3, 1448200], [4, 1448200]], "nextLevel": 337, "id": 75002}, {"cost": [[11, 137101, 680]], "level": 337, "attributes": [[1, 2911800], [2, 29118000], [3, 1455900], [4, 1455900]], "nextLevel": 338, "id": 75002}, {"cost": [[11, 137101, 680]], "level": 338, "attributes": [[1, 2927200], [2, 29272000], [3, 1463600], [4, 1463600]], "nextLevel": 339, "id": 75002}, {"cost": [[11, 137101, 680]], "level": 339, "attributes": [[1, 2942600], [2, 29426000], [3, 1471300], [4, 1471300]], "nextLevel": 340, "id": 75002}, {"cost": [[11, 137101, 680]], "level": 340, "attributes": [[1, 2958000], [2, 29580000], [3, 1479000], [4, 1479000]], "nextLevel": 341, "id": 75002}, {"cost": [[11, 137101, 690]], "level": 341, "attributes": [[1, 2973600], [2, 29736000], [3, 1486800], [4, 1486800]], "nextLevel": 342, "id": 75002}, {"cost": [[11, 137101, 690]], "level": 342, "attributes": [[1, 2989200], [2, 29892000], [3, 1494600], [4, 1494600]], "nextLevel": 343, "id": 75002}, {"cost": [[11, 137101, 690]], "level": 343, "attributes": [[1, 3004800], [2, 30048000], [3, 1502400], [4, 1502400]], "nextLevel": 344, "id": 75002}, {"cost": [[11, 137101, 690]], "level": 344, "attributes": [[1, 3020400], [2, 30204000], [3, 1510200], [4, 1510200]], "nextLevel": 345, "id": 75002}, {"cost": [[11, 137101, 690]], "level": 345, "attributes": [[1, 3036000], [2, 30360000], [3, 1518000], [4, 1518000]], "nextLevel": 346, "id": 75002}, {"cost": [[11, 137101, 700]], "level": 346, "attributes": [[1, 3051800], [2, 30518000], [3, 1525900], [4, 1525900]], "nextLevel": 347, "id": 75002}, {"cost": [[11, 137101, 700]], "level": 347, "attributes": [[1, 3067600], [2, 30676000], [3, 1533800], [4, 1533800]], "nextLevel": 348, "id": 75002}, {"cost": [[11, 137101, 700]], "level": 348, "attributes": [[1, 3083400], [2, 30834000], [3, 1541700], [4, 1541700]], "nextLevel": 349, "id": 75002}, {"cost": [[11, 137101, 700]], "level": 349, "attributes": [[1, 3099200], [2, 30992000], [3, 1549600], [4, 1549600]], "nextLevel": 350, "id": 75002}, {"cost": [[11, 137101, 700]], "level": 350, "attributes": [[1, 3115000], [2, 31150000], [3, 1557500], [4, 1557500]], "nextLevel": 351, "id": 75002}, {"cost": [[11, 137101, 710]], "level": 351, "attributes": [[1, 3131000], [2, 31310000], [3, 1565500], [4, 1565500]], "nextLevel": 352, "id": 75002}, {"cost": [[11, 137101, 710]], "level": 352, "attributes": [[1, 3147000], [2, 31470000], [3, 1573500], [4, 1573500]], "nextLevel": 353, "id": 75002}, {"cost": [[11, 137101, 710]], "level": 353, "attributes": [[1, 3163000], [2, 31630000], [3, 1581500], [4, 1581500]], "nextLevel": 354, "id": 75002}, {"cost": [[11, 137101, 710]], "level": 354, "attributes": [[1, 3179000], [2, 31790000], [3, 1589500], [4, 1589500]], "nextLevel": 355, "id": 75002}, {"cost": [[11, 137101, 710]], "level": 355, "attributes": [[1, 3195000], [2, 31950000], [3, 1597500], [4, 1597500]], "nextLevel": 356, "id": 75002}, {"cost": [[11, 137101, 720]], "level": 356, "attributes": [[1, 3211200], [2, 32112000], [3, 1605600], [4, 1605600]], "nextLevel": 357, "id": 75002}, {"cost": [[11, 137101, 720]], "level": 357, "attributes": [[1, 3227400], [2, 32274000], [3, 1613700], [4, 1613700]], "nextLevel": 358, "id": 75002}, {"cost": [[11, 137101, 720]], "level": 358, "attributes": [[1, 3243600], [2, 32436000], [3, 1621800], [4, 1621800]], "nextLevel": 359, "id": 75002}, {"cost": [[11, 137101, 720]], "level": 359, "attributes": [[1, 3259800], [2, 32598000], [3, 1629900], [4, 1629900]], "nextLevel": 360, "id": 75002}, {"cost": [[11, 137101, 720]], "level": 360, "attributes": [[1, 3276000], [2, 32760000], [3, 1638000], [4, 1638000]], "nextLevel": 361, "id": 75002}, {"cost": [[11, 137101, 730]], "level": 361, "attributes": [[1, 3292400], [2, 32924000], [3, 1646200], [4, 1646200]], "nextLevel": 362, "id": 75002}, {"cost": [[11, 137101, 730]], "level": 362, "attributes": [[1, 3308800], [2, 33088000], [3, 1654400], [4, 1654400]], "nextLevel": 363, "id": 75002}, {"cost": [[11, 137101, 730]], "level": 363, "attributes": [[1, 3325200], [2, 33252000], [3, 1662600], [4, 1662600]], "nextLevel": 364, "id": 75002}, {"cost": [[11, 137101, 730]], "level": 364, "attributes": [[1, 3341600], [2, 33416000], [3, 1670800], [4, 1670800]], "nextLevel": 365, "id": 75002}, {"cost": [[11, 137101, 730]], "level": 365, "attributes": [[1, 3358000], [2, 33580000], [3, 1679000], [4, 1679000]], "nextLevel": 366, "id": 75002}, {"cost": [[11, 137101, 740]], "level": 366, "attributes": [[1, 3374600], [2, 33746000], [3, 1687300], [4, 1687300]], "nextLevel": 367, "id": 75002}, {"cost": [[11, 137101, 740]], "level": 367, "attributes": [[1, 3391200], [2, 33912000], [3, 1695600], [4, 1695600]], "nextLevel": 368, "id": 75002}, {"cost": [[11, 137101, 740]], "level": 368, "attributes": [[1, 3407800], [2, 34078000], [3, 1703900], [4, 1703900]], "nextLevel": 369, "id": 75002}, {"cost": [[11, 137101, 740]], "level": 369, "attributes": [[1, 3424400], [2, 34244000], [3, 1712200], [4, 1712200]], "nextLevel": 370, "id": 75002}, {"cost": [[11, 137101, 740]], "level": 370, "attributes": [[1, 3441000], [2, 34410000], [3, 1720500], [4, 1720500]], "nextLevel": 371, "id": 75002}, {"cost": [[11, 137101, 750]], "level": 371, "attributes": [[1, 3457800], [2, 34578000], [3, 1728900], [4, 1728900]], "nextLevel": 372, "id": 75002}, {"cost": [[11, 137101, 750]], "level": 372, "attributes": [[1, 3474600], [2, 34746000], [3, 1737300], [4, 1737300]], "nextLevel": 373, "id": 75002}, {"cost": [[11, 137101, 750]], "level": 373, "attributes": [[1, 3491400], [2, 34914000], [3, 1745700], [4, 1745700]], "nextLevel": 374, "id": 75002}, {"cost": [[11, 137101, 750]], "level": 374, "attributes": [[1, 3508200], [2, 35082000], [3, 1754100], [4, 1754100]], "nextLevel": 375, "id": 75002}, {"cost": [[11, 137101, 750]], "level": 375, "attributes": [[1, 3525000], [2, 35250000], [3, 1762500], [4, 1762500]], "nextLevel": 376, "id": 75002}, {"cost": [[11, 137101, 760]], "level": 376, "attributes": [[1, 3542000], [2, 35420000], [3, 1771000], [4, 1771000]], "nextLevel": 377, "id": 75002}, {"cost": [[11, 137101, 760]], "level": 377, "attributes": [[1, 3559000], [2, 35590000], [3, 1779500], [4, 1779500]], "nextLevel": 378, "id": 75002}, {"cost": [[11, 137101, 760]], "level": 378, "attributes": [[1, 3576000], [2, 35760000], [3, 1788000], [4, 1788000]], "nextLevel": 379, "id": 75002}, {"cost": [[11, 137101, 760]], "level": 379, "attributes": [[1, 3593000], [2, 35930000], [3, 1796500], [4, 1796500]], "nextLevel": 380, "id": 75002}, {"cost": [[11, 137101, 760]], "level": 380, "attributes": [[1, 3610000], [2, 36100000], [3, 1805000], [4, 1805000]], "nextLevel": 381, "id": 75002}, {"cost": [[11, 137101, 770]], "level": 381, "attributes": [[1, 3627200], [2, 36272000], [3, 1813600], [4, 1813600]], "nextLevel": 382, "id": 75002}, {"cost": [[11, 137101, 770]], "level": 382, "attributes": [[1, 3644400], [2, 36444000], [3, 1822200], [4, 1822200]], "nextLevel": 383, "id": 75002}, {"cost": [[11, 137101, 770]], "level": 383, "attributes": [[1, 3661600], [2, 36616000], [3, 1830800], [4, 1830800]], "nextLevel": 384, "id": 75002}, {"cost": [[11, 137101, 770]], "level": 384, "attributes": [[1, 3678800], [2, 36788000], [3, 1839400], [4, 1839400]], "nextLevel": 385, "id": 75002}, {"cost": [[11, 137101, 770]], "level": 385, "attributes": [[1, 3696000], [2, 36960000], [3, 1848000], [4, 1848000]], "nextLevel": 386, "id": 75002}, {"cost": [[11, 137101, 780]], "level": 386, "attributes": [[1, 3713400], [2, 37134000], [3, 1856700], [4, 1856700]], "nextLevel": 387, "id": 75002}, {"cost": [[11, 137101, 780]], "level": 387, "attributes": [[1, 3730800], [2, 37308000], [3, 1865400], [4, 1865400]], "nextLevel": 388, "id": 75002}, {"cost": [[11, 137101, 780]], "level": 388, "attributes": [[1, 3748200], [2, 37482000], [3, 1874100], [4, 1874100]], "nextLevel": 389, "id": 75002}, {"cost": [[11, 137101, 780]], "level": 389, "attributes": [[1, 3765600], [2, 37656000], [3, 1882800], [4, 1882800]], "nextLevel": 390, "id": 75002}, {"cost": [[11, 137101, 780]], "level": 390, "attributes": [[1, 3783000], [2, 37830000], [3, 1891500], [4, 1891500]], "nextLevel": 391, "id": 75002}, {"cost": [[11, 137101, 790]], "level": 391, "attributes": [[1, 3800600], [2, 38006000], [3, 1900300], [4, 1900300]], "nextLevel": 392, "id": 75002}, {"cost": [[11, 137101, 790]], "level": 392, "attributes": [[1, 3818200], [2, 38182000], [3, 1909100], [4, 1909100]], "nextLevel": 393, "id": 75002}, {"cost": [[11, 137101, 790]], "level": 393, "attributes": [[1, 3835800], [2, 38358000], [3, 1917900], [4, 1917900]], "nextLevel": 394, "id": 75002}, {"cost": [[11, 137101, 790]], "level": 394, "attributes": [[1, 3853400], [2, 38534000], [3, 1926700], [4, 1926700]], "nextLevel": 395, "id": 75002}, {"cost": [[11, 137101, 790]], "level": 395, "attributes": [[1, 3871000], [2, 38710000], [3, 1935500], [4, 1935500]], "nextLevel": 396, "id": 75002}, {"cost": [[11, 137101, 800]], "level": 396, "attributes": [[1, 3888800], [2, 38888000], [3, 1944400], [4, 1944400]], "nextLevel": 397, "id": 75002}, {"cost": [[11, 137101, 800]], "level": 397, "attributes": [[1, 3906600], [2, 39066000], [3, 1953300], [4, 1953300]], "nextLevel": 398, "id": 75002}, {"cost": [[11, 137101, 800]], "level": 398, "attributes": [[1, 3924400], [2, 39244000], [3, 1962200], [4, 1962200]], "nextLevel": 399, "id": 75002}, {"cost": [[11, 137101, 800]], "level": 399, "attributes": [[1, 3942200], [2, 39422000], [3, 1971100], [4, 1971100]], "nextLevel": 400, "id": 75002}, {"cost": [[11, 137101, 800]], "level": 400, "attributes": [[1, 3960000], [2, 39600000], [3, 1980000], [4, 1980000]], "nextLevel": 401, "id": 75002}, {"cost": [[11, 137101, 810]], "level": 401, "attributes": [[1, 3978000], [2, 39780000], [3, 1989000], [4, 1989000]], "nextLevel": 402, "id": 75002}, {"cost": [[11, 137101, 810]], "level": 402, "attributes": [[1, 3996000], [2, 39960000], [3, 1998000], [4, 1998000]], "nextLevel": 403, "id": 75002}, {"cost": [[11, 137101, 810]], "level": 403, "attributes": [[1, 4014000], [2, 40140000], [3, 2007000], [4, 2007000]], "nextLevel": 404, "id": 75002}, {"cost": [[11, 137101, 810]], "level": 404, "attributes": [[1, 4032000], [2, 40320000], [3, 2016000], [4, 2016000]], "nextLevel": 405, "id": 75002}, {"cost": [[11, 137101, 810]], "level": 405, "attributes": [[1, 4050000], [2, 40500000], [3, 2025000], [4, 2025000]], "nextLevel": 406, "id": 75002}, {"cost": [[11, 137101, 820]], "level": 406, "attributes": [[1, 4068200], [2, 40682000], [3, 2034100], [4, 2034100]], "nextLevel": 407, "id": 75002}, {"cost": [[11, 137101, 820]], "level": 407, "attributes": [[1, 4086400], [2, 40864000], [3, 2043200], [4, 2043200]], "nextLevel": 408, "id": 75002}, {"cost": [[11, 137101, 820]], "level": 408, "attributes": [[1, 4104600], [2, 41046000], [3, 2052300], [4, 2052300]], "nextLevel": 409, "id": 75002}, {"cost": [[11, 137101, 820]], "level": 409, "attributes": [[1, 4122800], [2, 41228000], [3, 2061400], [4, 2061400]], "nextLevel": 410, "id": 75002}, {"cost": [[11, 137101, 820]], "level": 410, "attributes": [[1, 4141000], [2, 41410000], [3, 2070500], [4, 2070500]], "nextLevel": 411, "id": 75002}, {"cost": [[11, 137101, 830]], "level": 411, "attributes": [[1, 4159400], [2, 41594000], [3, 2079700], [4, 2079700]], "nextLevel": 412, "id": 75002}, {"cost": [[11, 137101, 830]], "level": 412, "attributes": [[1, 4177800], [2, 41778000], [3, 2088900], [4, 2088900]], "nextLevel": 413, "id": 75002}, {"cost": [[11, 137101, 830]], "level": 413, "attributes": [[1, 4196200], [2, 41962000], [3, 2098100], [4, 2098100]], "nextLevel": 414, "id": 75002}, {"cost": [[11, 137101, 830]], "level": 414, "attributes": [[1, 4214600], [2, 42146000], [3, 2107300], [4, 2107300]], "nextLevel": 415, "id": 75002}, {"cost": [[11, 137101, 830]], "level": 415, "attributes": [[1, 4233000], [2, 42330000], [3, 2116500], [4, 2116500]], "nextLevel": 416, "id": 75002}, {"cost": [[11, 137101, 840]], "level": 416, "attributes": [[1, 4251600], [2, 42516000], [3, 2125800], [4, 2125800]], "nextLevel": 417, "id": 75002}, {"cost": [[11, 137101, 840]], "level": 417, "attributes": [[1, 4270200], [2, 42702000], [3, 2135100], [4, 2135100]], "nextLevel": 418, "id": 75002}, {"cost": [[11, 137101, 840]], "level": 418, "attributes": [[1, 4288800], [2, 42888000], [3, 2144400], [4, 2144400]], "nextLevel": 419, "id": 75002}, {"cost": [[11, 137101, 840]], "level": 419, "attributes": [[1, 4307400], [2, 43074000], [3, 2153700], [4, 2153700]], "nextLevel": 420, "id": 75002}, {"cost": [[11, 137101, 840]], "level": 420, "attributes": [[1, 4326000], [2, 43260000], [3, 2163000], [4, 2163000]], "nextLevel": 421, "id": 75002}, {"cost": [[11, 137101, 850]], "level": 421, "attributes": [[1, 4344800], [2, 43448000], [3, 2172400], [4, 2172400]], "nextLevel": 422, "id": 75002}, {"cost": [[11, 137101, 850]], "level": 422, "attributes": [[1, 4363600], [2, 43636000], [3, 2181800], [4, 2181800]], "nextLevel": 423, "id": 75002}, {"cost": [[11, 137101, 850]], "level": 423, "attributes": [[1, 4382400], [2, 43824000], [3, 2191200], [4, 2191200]], "nextLevel": 424, "id": 75002}, {"cost": [[11, 137101, 850]], "level": 424, "attributes": [[1, 4401200], [2, 44012000], [3, 2200600], [4, 2200600]], "nextLevel": 425, "id": 75002}, {"cost": [[11, 137101, 850]], "level": 425, "attributes": [[1, 4420000], [2, 44200000], [3, 2210000], [4, 2210000]], "nextLevel": 426, "id": 75002}, {"cost": [[11, 137101, 860]], "level": 426, "attributes": [[1, 4439000], [2, 44390000], [3, 2219500], [4, 2219500]], "nextLevel": 427, "id": 75002}, {"cost": [[11, 137101, 860]], "level": 427, "attributes": [[1, 4458000], [2, 44580000], [3, 2229000], [4, 2229000]], "nextLevel": 428, "id": 75002}, {"cost": [[11, 137101, 860]], "level": 428, "attributes": [[1, 4477000], [2, 44770000], [3, 2238500], [4, 2238500]], "nextLevel": 429, "id": 75002}, {"cost": [[11, 137101, 860]], "level": 429, "attributes": [[1, 4496000], [2, 44960000], [3, 2248000], [4, 2248000]], "nextLevel": 430, "id": 75002}, {"cost": [[11, 137101, 860]], "level": 430, "attributes": [[1, 4515000], [2, 45150000], [3, 2257500], [4, 2257500]], "nextLevel": 431, "id": 75002}, {"cost": [[11, 137101, 870]], "level": 431, "attributes": [[1, 4534200], [2, 45342000], [3, 2267100], [4, 2267100]], "nextLevel": 432, "id": 75002}, {"cost": [[11, 137101, 870]], "level": 432, "attributes": [[1, 4553400], [2, 45534000], [3, 2276700], [4, 2276700]], "nextLevel": 433, "id": 75002}, {"cost": [[11, 137101, 870]], "level": 433, "attributes": [[1, 4572600], [2, 45726000], [3, 2286300], [4, 2286300]], "nextLevel": 434, "id": 75002}, {"cost": [[11, 137101, 870]], "level": 434, "attributes": [[1, 4591800], [2, 45918000], [3, 2295900], [4, 2295900]], "nextLevel": 435, "id": 75002}, {"cost": [[11, 137101, 870]], "level": 435, "attributes": [[1, 4611000], [2, 46110000], [3, 2305500], [4, 2305500]], "nextLevel": 436, "id": 75002}, {"cost": [[11, 137101, 880]], "level": 436, "attributes": [[1, 4630400], [2, 46304000], [3, 2315200], [4, 2315200]], "nextLevel": 437, "id": 75002}, {"cost": [[11, 137101, 880]], "level": 437, "attributes": [[1, 4649800], [2, 46498000], [3, 2324900], [4, 2324900]], "nextLevel": 438, "id": 75002}, {"cost": [[11, 137101, 880]], "level": 438, "attributes": [[1, 4669200], [2, 46692000], [3, 2334600], [4, 2334600]], "nextLevel": 439, "id": 75002}, {"cost": [[11, 137101, 880]], "level": 439, "attributes": [[1, 4688600], [2, 46886000], [3, 2344300], [4, 2344300]], "nextLevel": 440, "id": 75002}, {"cost": [[11, 137101, 880]], "level": 440, "attributes": [[1, 4708000], [2, 47080000], [3, 2354000], [4, 2354000]], "nextLevel": 441, "id": 75002}, {"cost": [[11, 137101, 890]], "level": 441, "attributes": [[1, 4727600], [2, 47276000], [3, 2363800], [4, 2363800]], "nextLevel": 442, "id": 75002}, {"cost": [[11, 137101, 890]], "level": 442, "attributes": [[1, 4747200], [2, 47472000], [3, 2373600], [4, 2373600]], "nextLevel": 443, "id": 75002}, {"cost": [[11, 137101, 890]], "level": 443, "attributes": [[1, 4766800], [2, 47668000], [3, 2383400], [4, 2383400]], "nextLevel": 444, "id": 75002}, {"cost": [[11, 137101, 890]], "level": 444, "attributes": [[1, 4786400], [2, 47864000], [3, 2393200], [4, 2393200]], "nextLevel": 445, "id": 75002}, {"cost": [[11, 137101, 890]], "level": 445, "attributes": [[1, 4806000], [2, 48060000], [3, 2403000], [4, 2403000]], "nextLevel": 446, "id": 75002}, {"cost": [[11, 137101, 900]], "level": 446, "attributes": [[1, 4825800], [2, 48258000], [3, 2412900], [4, 2412900]], "nextLevel": 447, "id": 75002}, {"cost": [[11, 137101, 900]], "level": 447, "attributes": [[1, 4845600], [2, 48456000], [3, 2422800], [4, 2422800]], "nextLevel": 448, "id": 75002}, {"cost": [[11, 137101, 900]], "level": 448, "attributes": [[1, 4865400], [2, 48654000], [3, 2432700], [4, 2432700]], "nextLevel": 449, "id": 75002}, {"cost": [[11, 137101, 900]], "level": 449, "attributes": [[1, 4885200], [2, 48852000], [3, 2442600], [4, 2442600]], "nextLevel": 450, "id": 75002}, {"cost": [[11, 137101, 900]], "level": 450, "attributes": [[1, 4905000], [2, 49050000], [3, 2452500], [4, 2452500]], "nextLevel": 451, "id": 75002}, {"cost": [[11, 137101, 910]], "level": 451, "attributes": [[1, 4925000], [2, 49250000], [3, 2462500], [4, 2462500]], "nextLevel": 452, "id": 75002}, {"cost": [[11, 137101, 910]], "level": 452, "attributes": [[1, 4945000], [2, 49450000], [3, 2472500], [4, 2472500]], "nextLevel": 453, "id": 75002}, {"cost": [[11, 137101, 910]], "level": 453, "attributes": [[1, 4965000], [2, 49650000], [3, 2482500], [4, 2482500]], "nextLevel": 454, "id": 75002}, {"cost": [[11, 137101, 910]], "level": 454, "attributes": [[1, 4985000], [2, 49850000], [3, 2492500], [4, 2492500]], "nextLevel": 455, "id": 75002}, {"cost": [[11, 137101, 910]], "level": 455, "attributes": [[1, 5005000], [2, 50050000], [3, 2502500], [4, 2502500]], "nextLevel": 456, "id": 75002}, {"cost": [[11, 137101, 920]], "level": 456, "attributes": [[1, 5025200], [2, 50252000], [3, 2512600], [4, 2512600]], "nextLevel": 457, "id": 75002}, {"cost": [[11, 137101, 920]], "level": 457, "attributes": [[1, 5045400], [2, 50454000], [3, 2522700], [4, 2522700]], "nextLevel": 458, "id": 75002}, {"cost": [[11, 137101, 920]], "level": 458, "attributes": [[1, 5065600], [2, 50656000], [3, 2532800], [4, 2532800]], "nextLevel": 459, "id": 75002}, {"cost": [[11, 137101, 920]], "level": 459, "attributes": [[1, 5085800], [2, 50858000], [3, 2542900], [4, 2542900]], "nextLevel": 460, "id": 75002}, {"cost": [[11, 137101, 920]], "level": 460, "attributes": [[1, 5106000], [2, 51060000], [3, 2553000], [4, 2553000]], "nextLevel": 461, "id": 75002}, {"cost": [[11, 137101, 930]], "level": 461, "attributes": [[1, 5126400], [2, 51264000], [3, 2563200], [4, 2563200]], "nextLevel": 462, "id": 75002}, {"cost": [[11, 137101, 930]], "level": 462, "attributes": [[1, 5146800], [2, 51468000], [3, 2573400], [4, 2573400]], "nextLevel": 463, "id": 75002}, {"cost": [[11, 137101, 930]], "level": 463, "attributes": [[1, 5167200], [2, 51672000], [3, 2583600], [4, 2583600]], "nextLevel": 464, "id": 75002}, {"cost": [[11, 137101, 930]], "level": 464, "attributes": [[1, 5187600], [2, 51876000], [3, 2593800], [4, 2593800]], "nextLevel": 465, "id": 75002}, {"cost": [[11, 137101, 930]], "level": 465, "attributes": [[1, 5208000], [2, 52080000], [3, 2604000], [4, 2604000]], "nextLevel": 466, "id": 75002}, {"cost": [[11, 137101, 940]], "level": 466, "attributes": [[1, 5228600], [2, 52286000], [3, 2614300], [4, 2614300]], "nextLevel": 467, "id": 75002}, {"cost": [[11, 137101, 940]], "level": 467, "attributes": [[1, 5249200], [2, 52492000], [3, 2624600], [4, 2624600]], "nextLevel": 468, "id": 75002}, {"cost": [[11, 137101, 940]], "level": 468, "attributes": [[1, 5269800], [2, 52698000], [3, 2634900], [4, 2634900]], "nextLevel": 469, "id": 75002}, {"cost": [[11, 137101, 940]], "level": 469, "attributes": [[1, 5290400], [2, 52904000], [3, 2645200], [4, 2645200]], "nextLevel": 470, "id": 75002}, {"cost": [[11, 137101, 940]], "level": 470, "attributes": [[1, 5311000], [2, 53110000], [3, 2655500], [4, 2655500]], "nextLevel": 471, "id": 75002}, {"cost": [[11, 137101, 950]], "level": 471, "attributes": [[1, 5331800], [2, 53318000], [3, 2665900], [4, 2665900]], "nextLevel": 472, "id": 75002}, {"cost": [[11, 137101, 950]], "level": 472, "attributes": [[1, 5352600], [2, 53526000], [3, 2676300], [4, 2676300]], "nextLevel": 473, "id": 75002}, {"cost": [[11, 137101, 950]], "level": 473, "attributes": [[1, 5373400], [2, 53734000], [3, 2686700], [4, 2686700]], "nextLevel": 474, "id": 75002}, {"cost": [[11, 137101, 950]], "level": 474, "attributes": [[1, 5394200], [2, 53942000], [3, 2697100], [4, 2697100]], "nextLevel": 475, "id": 75002}, {"cost": [[11, 137101, 950]], "level": 475, "attributes": [[1, 5415000], [2, 54150000], [3, 2707500], [4, 2707500]], "nextLevel": 476, "id": 75002}, {"cost": [[11, 137101, 960]], "level": 476, "attributes": [[1, 5436000], [2, 54360000], [3, 2718000], [4, 2718000]], "nextLevel": 477, "id": 75002}, {"cost": [[11, 137101, 960]], "level": 477, "attributes": [[1, 5457000], [2, 54570000], [3, 2728500], [4, 2728500]], "nextLevel": 478, "id": 75002}, {"cost": [[11, 137101, 960]], "level": 478, "attributes": [[1, 5478000], [2, 54780000], [3, 2739000], [4, 2739000]], "nextLevel": 479, "id": 75002}, {"cost": [[11, 137101, 960]], "level": 479, "attributes": [[1, 5499000], [2, 54990000], [3, 2749500], [4, 2749500]], "nextLevel": 480, "id": 75002}, {"cost": [[11, 137101, 960]], "level": 480, "attributes": [[1, 5520000], [2, 55200000], [3, 2760000], [4, 2760000]], "nextLevel": 481, "id": 75002}, {"cost": [[11, 137101, 970]], "level": 481, "attributes": [[1, 5541200], [2, 55412000], [3, 2770600], [4, 2770600]], "nextLevel": 482, "id": 75002}, {"cost": [[11, 137101, 970]], "level": 482, "attributes": [[1, 5562400], [2, 55624000], [3, 2781200], [4, 2781200]], "nextLevel": 483, "id": 75002}, {"cost": [[11, 137101, 970]], "level": 483, "attributes": [[1, 5583600], [2, 55836000], [3, 2791800], [4, 2791800]], "nextLevel": 484, "id": 75002}, {"cost": [[11, 137101, 970]], "level": 484, "attributes": [[1, 5604800], [2, 56048000], [3, 2802400], [4, 2802400]], "nextLevel": 485, "id": 75002}, {"cost": [[11, 137101, 970]], "level": 485, "attributes": [[1, 5626000], [2, 56260000], [3, 2813000], [4, 2813000]], "nextLevel": 486, "id": 75002}, {"cost": [[11, 137101, 980]], "level": 486, "attributes": [[1, 5647400], [2, 56474000], [3, 2823700], [4, 2823700]], "nextLevel": 487, "id": 75002}, {"cost": [[11, 137101, 980]], "level": 487, "attributes": [[1, 5668800], [2, 56688000], [3, 2834400], [4, 2834400]], "nextLevel": 488, "id": 75002}, {"cost": [[11, 137101, 980]], "level": 488, "attributes": [[1, 5690200], [2, 56902000], [3, 2845100], [4, 2845100]], "nextLevel": 489, "id": 75002}, {"cost": [[11, 137101, 980]], "level": 489, "attributes": [[1, 5711600], [2, 57116000], [3, 2855800], [4, 2855800]], "nextLevel": 490, "id": 75002}, {"cost": [[11, 137101, 980]], "level": 490, "attributes": [[1, 5733000], [2, 57330000], [3, 2866500], [4, 2866500]], "nextLevel": 491, "id": 75002}, {"cost": [[11, 137101, 990]], "level": 491, "attributes": [[1, 5754600], [2, 57546000], [3, 2877300], [4, 2877300]], "nextLevel": 492, "id": 75002}, {"cost": [[11, 137101, 990]], "level": 492, "attributes": [[1, 5776200], [2, 57762000], [3, 2888100], [4, 2888100]], "nextLevel": 493, "id": 75002}, {"cost": [[11, 137101, 990]], "level": 493, "attributes": [[1, 5797800], [2, 57978000], [3, 2898900], [4, 2898900]], "nextLevel": 494, "id": 75002}, {"cost": [[11, 137101, 990]], "level": 494, "attributes": [[1, 5819400], [2, 58194000], [3, 2909700], [4, 2909700]], "nextLevel": 495, "id": 75002}, {"cost": [[11, 137101, 990]], "level": 495, "attributes": [[1, 5841000], [2, 58410000], [3, 2920500], [4, 2920500]], "nextLevel": 496, "id": 75002}, {"cost": [[11, 137101, 1000]], "level": 496, "attributes": [[1, 5862800], [2, 58628000], [3, 2931400], [4, 2931400]], "nextLevel": 497, "id": 75002}, {"cost": [[11, 137101, 1000]], "level": 497, "attributes": [[1, 5884600], [2, 58846000], [3, 2942300], [4, 2942300]], "nextLevel": 498, "id": 75002}, {"cost": [[11, 137101, 1000]], "level": 498, "attributes": [[1, 5906400], [2, 59064000], [3, 2953200], [4, 2953200]], "nextLevel": 499, "id": 75002}, {"cost": [[11, 137101, 1000]], "level": 499, "attributes": [[1, 5928200], [2, 59282000], [3, 2964100], [4, 2964100]], "nextLevel": 500, "id": 75002}, {"cost": [], "level": 500, "attributes": [[1, 5950000], [2, 59500000], [3, 2975000], [4, 2975000]], "nextLevel": 0, "id": 75002}, {"cost": [[11, 137101, 10]], "level": 1, "attributes": [[1, 3000], [2, 30000], [3, 1500], [4, 1500]], "nextLevel": 2, "id": 76001}, {"cost": [[11, 137101, 10]], "level": 2, "attributes": [[1, 6000], [2, 60000], [3, 3000], [4, 3000]], "nextLevel": 3, "id": 76001}, {"cost": [[11, 137101, 10]], "level": 3, "attributes": [[1, 9000], [2, 90000], [3, 4500], [4, 4500]], "nextLevel": 4, "id": 76001}, {"cost": [[11, 137101, 10]], "level": 4, "attributes": [[1, 12000], [2, 120000], [3, 6000], [4, 6000]], "nextLevel": 5, "id": 76001}, {"cost": [[11, 137101, 10]], "level": 5, "attributes": [[1, 15000], [2, 150000], [3, 7500], [4, 7500]], "nextLevel": 6, "id": 76001}, {"cost": [[11, 137101, 20]], "level": 6, "attributes": [[1, 18300], [2, 183000], [3, 9150], [4, 9150]], "nextLevel": 7, "id": 76001}, {"cost": [[11, 137101, 20]], "level": 7, "attributes": [[1, 21600], [2, 216000], [3, 10800], [4, 10800]], "nextLevel": 8, "id": 76001}, {"cost": [[11, 137101, 20]], "level": 8, "attributes": [[1, 24900], [2, 249000], [3, 12450], [4, 12450]], "nextLevel": 9, "id": 76001}, {"cost": [[11, 137101, 20]], "level": 9, "attributes": [[1, 28200], [2, 282000], [3, 14100], [4, 14100]], "nextLevel": 10, "id": 76001}, {"cost": [[11, 137101, 20]], "level": 10, "attributes": [[1, 31500], [2, 315000], [3, 15750], [4, 15750]], "nextLevel": 11, "id": 76001}, {"cost": [[11, 137101, 30]], "level": 11, "attributes": [[1, 35100], [2, 351000], [3, 17550], [4, 17550]], "nextLevel": 12, "id": 76001}, {"cost": [[11, 137101, 30]], "level": 12, "attributes": [[1, 38700], [2, 387000], [3, 19350], [4, 19350]], "nextLevel": 13, "id": 76001}, {"cost": [[11, 137101, 30]], "level": 13, "attributes": [[1, 42300], [2, 423000], [3, 21150], [4, 21150]], "nextLevel": 14, "id": 76001}, {"cost": [[11, 137101, 30]], "level": 14, "attributes": [[1, 45900], [2, 459000], [3, 22950], [4, 22950]], "nextLevel": 15, "id": 76001}, {"cost": [[11, 137101, 30]], "level": 15, "attributes": [[1, 49500], [2, 495000], [3, 24750], [4, 24750]], "nextLevel": 16, "id": 76001}, {"cost": [[11, 137101, 40]], "level": 16, "attributes": [[1, 53400], [2, 534000], [3, 26700], [4, 26700]], "nextLevel": 17, "id": 76001}, {"cost": [[11, 137101, 40]], "level": 17, "attributes": [[1, 57300], [2, 573000], [3, 28650], [4, 28650]], "nextLevel": 18, "id": 76001}, {"cost": [[11, 137101, 40]], "level": 18, "attributes": [[1, 61200], [2, 612000], [3, 30600], [4, 30600]], "nextLevel": 19, "id": 76001}, {"cost": [[11, 137101, 40]], "level": 19, "attributes": [[1, 65100], [2, 651000], [3, 32550], [4, 32550]], "nextLevel": 20, "id": 76001}, {"cost": [[11, 137101, 40]], "level": 20, "attributes": [[1, 69000], [2, 690000], [3, 34500], [4, 34500]], "nextLevel": 21, "id": 76001}, {"cost": [[11, 137101, 50]], "level": 21, "attributes": [[1, 73200], [2, 732000], [3, 36600], [4, 36600]], "nextLevel": 22, "id": 76001}, {"cost": [[11, 137101, 50]], "level": 22, "attributes": [[1, 77400], [2, 774000], [3, 38700], [4, 38700]], "nextLevel": 23, "id": 76001}, {"cost": [[11, 137101, 50]], "level": 23, "attributes": [[1, 81600], [2, 816000], [3, 40800], [4, 40800]], "nextLevel": 24, "id": 76001}, {"cost": [[11, 137101, 50]], "level": 24, "attributes": [[1, 85800], [2, 858000], [3, 42900], [4, 42900]], "nextLevel": 25, "id": 76001}, {"cost": [[11, 137101, 50]], "level": 25, "attributes": [[1, 90000], [2, 900000], [3, 45000], [4, 45000]], "nextLevel": 26, "id": 76001}, {"cost": [[11, 137101, 60]], "level": 26, "attributes": [[1, 94500], [2, 945000], [3, 47250], [4, 47250]], "nextLevel": 27, "id": 76001}, {"cost": [[11, 137101, 60]], "level": 27, "attributes": [[1, 99000], [2, 990000], [3, 49500], [4, 49500]], "nextLevel": 28, "id": 76001}, {"cost": [[11, 137101, 60]], "level": 28, "attributes": [[1, 103500], [2, 1035000], [3, 51750], [4, 51750]], "nextLevel": 29, "id": 76001}, {"cost": [[11, 137101, 60]], "level": 29, "attributes": [[1, 108000], [2, 1080000], [3, 54000], [4, 54000]], "nextLevel": 30, "id": 76001}, {"cost": [[11, 137101, 60]], "level": 30, "attributes": [[1, 112500], [2, 1125000], [3, 56250], [4, 56250]], "nextLevel": 31, "id": 76001}, {"cost": [[11, 137101, 70]], "level": 31, "attributes": [[1, 117300], [2, 1173000], [3, 58650], [4, 58650]], "nextLevel": 32, "id": 76001}, {"cost": [[11, 137101, 70]], "level": 32, "attributes": [[1, 122100], [2, 1221000], [3, 61050], [4, 61050]], "nextLevel": 33, "id": 76001}, {"cost": [[11, 137101, 70]], "level": 33, "attributes": [[1, 126900], [2, 1269000], [3, 63450], [4, 63450]], "nextLevel": 34, "id": 76001}, {"cost": [[11, 137101, 70]], "level": 34, "attributes": [[1, 131700], [2, 1317000], [3, 65850], [4, 65850]], "nextLevel": 35, "id": 76001}, {"cost": [[11, 137101, 70]], "level": 35, "attributes": [[1, 136500], [2, 1365000], [3, 68250], [4, 68250]], "nextLevel": 36, "id": 76001}, {"cost": [[11, 137101, 80]], "level": 36, "attributes": [[1, 141600], [2, 1416000], [3, 70800], [4, 70800]], "nextLevel": 37, "id": 76001}, {"cost": [[11, 137101, 80]], "level": 37, "attributes": [[1, 146700], [2, 1467000], [3, 73350], [4, 73350]], "nextLevel": 38, "id": 76001}, {"cost": [[11, 137101, 80]], "level": 38, "attributes": [[1, 151800], [2, 1518000], [3, 75900], [4, 75900]], "nextLevel": 39, "id": 76001}, {"cost": [[11, 137101, 80]], "level": 39, "attributes": [[1, 156900], [2, 1569000], [3, 78450], [4, 78450]], "nextLevel": 40, "id": 76001}, {"cost": [[11, 137101, 80]], "level": 40, "attributes": [[1, 162000], [2, 1620000], [3, 81000], [4, 81000]], "nextLevel": 41, "id": 76001}, {"cost": [[11, 137101, 90]], "level": 41, "attributes": [[1, 167400], [2, 1674000], [3, 83700], [4, 83700]], "nextLevel": 42, "id": 76001}, {"cost": [[11, 137101, 90]], "level": 42, "attributes": [[1, 172800], [2, 1728000], [3, 86400], [4, 86400]], "nextLevel": 43, "id": 76001}, {"cost": [[11, 137101, 90]], "level": 43, "attributes": [[1, 178200], [2, 1782000], [3, 89100], [4, 89100]], "nextLevel": 44, "id": 76001}, {"cost": [[11, 137101, 90]], "level": 44, "attributes": [[1, 183600], [2, 1836000], [3, 91800], [4, 91800]], "nextLevel": 45, "id": 76001}, {"cost": [[11, 137101, 90]], "level": 45, "attributes": [[1, 189000], [2, 1890000], [3, 94500], [4, 94500]], "nextLevel": 46, "id": 76001}, {"cost": [[11, 137101, 100]], "level": 46, "attributes": [[1, 194700], [2, 1947000], [3, 97350], [4, 97350]], "nextLevel": 47, "id": 76001}, {"cost": [[11, 137101, 100]], "level": 47, "attributes": [[1, 200400], [2, 2004000], [3, 100200], [4, 100200]], "nextLevel": 48, "id": 76001}, {"cost": [[11, 137101, 100]], "level": 48, "attributes": [[1, 206100], [2, 2061000], [3, 103050], [4, 103050]], "nextLevel": 49, "id": 76001}, {"cost": [[11, 137101, 100]], "level": 49, "attributes": [[1, 211800], [2, 2118000], [3, 105900], [4, 105900]], "nextLevel": 50, "id": 76001}, {"cost": [[11, 137101, 100]], "level": 50, "attributes": [[1, 217500], [2, 2175000], [3, 108750], [4, 108750]], "nextLevel": 51, "id": 76001}, {"cost": [[11, 137101, 110]], "level": 51, "attributes": [[1, 223500], [2, 2235000], [3, 111750], [4, 111750]], "nextLevel": 52, "id": 76001}, {"cost": [[11, 137101, 110]], "level": 52, "attributes": [[1, 229500], [2, 2295000], [3, 114750], [4, 114750]], "nextLevel": 53, "id": 76001}, {"cost": [[11, 137101, 110]], "level": 53, "attributes": [[1, 235500], [2, 2355000], [3, 117750], [4, 117750]], "nextLevel": 54, "id": 76001}, {"cost": [[11, 137101, 110]], "level": 54, "attributes": [[1, 241500], [2, 2415000], [3, 120750], [4, 120750]], "nextLevel": 55, "id": 76001}, {"cost": [[11, 137101, 110]], "level": 55, "attributes": [[1, 247500], [2, 2475000], [3, 123750], [4, 123750]], "nextLevel": 56, "id": 76001}, {"cost": [[11, 137101, 120]], "level": 56, "attributes": [[1, 253800], [2, 2538000], [3, 126900], [4, 126900]], "nextLevel": 57, "id": 76001}, {"cost": [[11, 137101, 120]], "level": 57, "attributes": [[1, 260100], [2, 2601000], [3, 130050], [4, 130050]], "nextLevel": 58, "id": 76001}, {"cost": [[11, 137101, 120]], "level": 58, "attributes": [[1, 266400], [2, 2664000], [3, 133200], [4, 133200]], "nextLevel": 59, "id": 76001}, {"cost": [[11, 137101, 120]], "level": 59, "attributes": [[1, 272700], [2, 2727000], [3, 136350], [4, 136350]], "nextLevel": 60, "id": 76001}, {"cost": [[11, 137101, 120]], "level": 60, "attributes": [[1, 279000], [2, 2790000], [3, 139500], [4, 139500]], "nextLevel": 61, "id": 76001}, {"cost": [[11, 137101, 130]], "level": 61, "attributes": [[1, 285600], [2, 2856000], [3, 142800], [4, 142800]], "nextLevel": 62, "id": 76001}, {"cost": [[11, 137101, 130]], "level": 62, "attributes": [[1, 292200], [2, 2922000], [3, 146100], [4, 146100]], "nextLevel": 63, "id": 76001}, {"cost": [[11, 137101, 130]], "level": 63, "attributes": [[1, 298800], [2, 2988000], [3, 149400], [4, 149400]], "nextLevel": 64, "id": 76001}, {"cost": [[11, 137101, 130]], "level": 64, "attributes": [[1, 305400], [2, 3054000], [3, 152700], [4, 152700]], "nextLevel": 65, "id": 76001}, {"cost": [[11, 137101, 130]], "level": 65, "attributes": [[1, 312000], [2, 3120000], [3, 156000], [4, 156000]], "nextLevel": 66, "id": 76001}, {"cost": [[11, 137101, 140]], "level": 66, "attributes": [[1, 318900], [2, 3189000], [3, 159450], [4, 159450]], "nextLevel": 67, "id": 76001}, {"cost": [[11, 137101, 140]], "level": 67, "attributes": [[1, 325800], [2, 3258000], [3, 162900], [4, 162900]], "nextLevel": 68, "id": 76001}, {"cost": [[11, 137101, 140]], "level": 68, "attributes": [[1, 332700], [2, 3327000], [3, 166350], [4, 166350]], "nextLevel": 69, "id": 76001}, {"cost": [[11, 137101, 140]], "level": 69, "attributes": [[1, 339600], [2, 3396000], [3, 169800], [4, 169800]], "nextLevel": 70, "id": 76001}, {"cost": [[11, 137101, 140]], "level": 70, "attributes": [[1, 346500], [2, 3465000], [3, 173250], [4, 173250]], "nextLevel": 71, "id": 76001}, {"cost": [[11, 137101, 150]], "level": 71, "attributes": [[1, 353700], [2, 3537000], [3, 176850], [4, 176850]], "nextLevel": 72, "id": 76001}, {"cost": [[11, 137101, 150]], "level": 72, "attributes": [[1, 360900], [2, 3609000], [3, 180450], [4, 180450]], "nextLevel": 73, "id": 76001}, {"cost": [[11, 137101, 150]], "level": 73, "attributes": [[1, 368100], [2, 3681000], [3, 184050], [4, 184050]], "nextLevel": 74, "id": 76001}, {"cost": [[11, 137101, 150]], "level": 74, "attributes": [[1, 375300], [2, 3753000], [3, 187650], [4, 187650]], "nextLevel": 75, "id": 76001}, {"cost": [[11, 137101, 150]], "level": 75, "attributes": [[1, 382500], [2, 3825000], [3, 191250], [4, 191250]], "nextLevel": 76, "id": 76001}, {"cost": [[11, 137101, 160]], "level": 76, "attributes": [[1, 390000], [2, 3900000], [3, 195000], [4, 195000]], "nextLevel": 77, "id": 76001}, {"cost": [[11, 137101, 160]], "level": 77, "attributes": [[1, 397500], [2, 3975000], [3, 198750], [4, 198750]], "nextLevel": 78, "id": 76001}, {"cost": [[11, 137101, 160]], "level": 78, "attributes": [[1, 405000], [2, 4050000], [3, 202500], [4, 202500]], "nextLevel": 79, "id": 76001}, {"cost": [[11, 137101, 160]], "level": 79, "attributes": [[1, 412500], [2, 4125000], [3, 206250], [4, 206250]], "nextLevel": 80, "id": 76001}, {"cost": [[11, 137101, 160]], "level": 80, "attributes": [[1, 420000], [2, 4200000], [3, 210000], [4, 210000]], "nextLevel": 81, "id": 76001}, {"cost": [[11, 137101, 170]], "level": 81, "attributes": [[1, 427800], [2, 4278000], [3, 213900], [4, 213900]], "nextLevel": 82, "id": 76001}, {"cost": [[11, 137101, 170]], "level": 82, "attributes": [[1, 435600], [2, 4356000], [3, 217800], [4, 217800]], "nextLevel": 83, "id": 76001}, {"cost": [[11, 137101, 170]], "level": 83, "attributes": [[1, 443400], [2, 4434000], [3, 221700], [4, 221700]], "nextLevel": 84, "id": 76001}, {"cost": [[11, 137101, 170]], "level": 84, "attributes": [[1, 451200], [2, 4512000], [3, 225600], [4, 225600]], "nextLevel": 85, "id": 76001}, {"cost": [[11, 137101, 170]], "level": 85, "attributes": [[1, 459000], [2, 4590000], [3, 229500], [4, 229500]], "nextLevel": 86, "id": 76001}, {"cost": [[11, 137101, 180]], "level": 86, "attributes": [[1, 467100], [2, 4671000], [3, 233550], [4, 233550]], "nextLevel": 87, "id": 76001}, {"cost": [[11, 137101, 180]], "level": 87, "attributes": [[1, 475200], [2, 4752000], [3, 237600], [4, 237600]], "nextLevel": 88, "id": 76001}, {"cost": [[11, 137101, 180]], "level": 88, "attributes": [[1, 483300], [2, 4833000], [3, 241650], [4, 241650]], "nextLevel": 89, "id": 76001}, {"cost": [[11, 137101, 180]], "level": 89, "attributes": [[1, 491400], [2, 4914000], [3, 245700], [4, 245700]], "nextLevel": 90, "id": 76001}, {"cost": [[11, 137101, 180]], "level": 90, "attributes": [[1, 499500], [2, 4995000], [3, 249750], [4, 249750]], "nextLevel": 91, "id": 76001}, {"cost": [[11, 137101, 190]], "level": 91, "attributes": [[1, 507900], [2, 5079000], [3, 253950], [4, 253950]], "nextLevel": 92, "id": 76001}, {"cost": [[11, 137101, 190]], "level": 92, "attributes": [[1, 516300], [2, 5163000], [3, 258150], [4, 258150]], "nextLevel": 93, "id": 76001}, {"cost": [[11, 137101, 190]], "level": 93, "attributes": [[1, 524700], [2, 5247000], [3, 262350], [4, 262350]], "nextLevel": 94, "id": 76001}, {"cost": [[11, 137101, 190]], "level": 94, "attributes": [[1, 533100], [2, 5331000], [3, 266550], [4, 266550]], "nextLevel": 95, "id": 76001}, {"cost": [[11, 137101, 190]], "level": 95, "attributes": [[1, 541500], [2, 5415000], [3, 270750], [4, 270750]], "nextLevel": 96, "id": 76001}, {"cost": [[11, 137101, 200]], "level": 96, "attributes": [[1, 550200], [2, 5502000], [3, 275100], [4, 275100]], "nextLevel": 97, "id": 76001}, {"cost": [[11, 137101, 200]], "level": 97, "attributes": [[1, 558900], [2, 5589000], [3, 279450], [4, 279450]], "nextLevel": 98, "id": 76001}, {"cost": [[11, 137101, 200]], "level": 98, "attributes": [[1, 567600], [2, 5676000], [3, 283800], [4, 283800]], "nextLevel": 99, "id": 76001}, {"cost": [[11, 137101, 200]], "level": 99, "attributes": [[1, 576300], [2, 5763000], [3, 288150], [4, 288150]], "nextLevel": 100, "id": 76001}, {"cost": [[11, 137101, 200]], "level": 100, "attributes": [[1, 585000], [2, 5850000], [3, 292500], [4, 292500]], "nextLevel": 101, "id": 76001}, {"cost": [[11, 137101, 210]], "level": 101, "attributes": [[1, 594000], [2, 5940000], [3, 297000], [4, 297000]], "nextLevel": 102, "id": 76001}, {"cost": [[11, 137101, 210]], "level": 102, "attributes": [[1, 603000], [2, 6030000], [3, 301500], [4, 301500]], "nextLevel": 103, "id": 76001}, {"cost": [[11, 137101, 210]], "level": 103, "attributes": [[1, 612000], [2, 6120000], [3, 306000], [4, 306000]], "nextLevel": 104, "id": 76001}, {"cost": [[11, 137101, 210]], "level": 104, "attributes": [[1, 621000], [2, 6210000], [3, 310500], [4, 310500]], "nextLevel": 105, "id": 76001}, {"cost": [[11, 137101, 210]], "level": 105, "attributes": [[1, 630000], [2, 6300000], [3, 315000], [4, 315000]], "nextLevel": 106, "id": 76001}, {"cost": [[11, 137101, 220]], "level": 106, "attributes": [[1, 639300], [2, 6393000], [3, 319650], [4, 319650]], "nextLevel": 107, "id": 76001}, {"cost": [[11, 137101, 220]], "level": 107, "attributes": [[1, 648600], [2, 6486000], [3, 324300], [4, 324300]], "nextLevel": 108, "id": 76001}, {"cost": [[11, 137101, 220]], "level": 108, "attributes": [[1, 657900], [2, 6579000], [3, 328950], [4, 328950]], "nextLevel": 109, "id": 76001}, {"cost": [[11, 137101, 220]], "level": 109, "attributes": [[1, 667200], [2, 6672000], [3, 333600], [4, 333600]], "nextLevel": 110, "id": 76001}, {"cost": [[11, 137101, 220]], "level": 110, "attributes": [[1, 676500], [2, 6765000], [3, 338250], [4, 338250]], "nextLevel": 111, "id": 76001}, {"cost": [[11, 137101, 230]], "level": 111, "attributes": [[1, 686100], [2, 6861000], [3, 343050], [4, 343050]], "nextLevel": 112, "id": 76001}, {"cost": [[11, 137101, 230]], "level": 112, "attributes": [[1, 695700], [2, 6957000], [3, 347850], [4, 347850]], "nextLevel": 113, "id": 76001}, {"cost": [[11, 137101, 230]], "level": 113, "attributes": [[1, 705300], [2, 7053000], [3, 352650], [4, 352650]], "nextLevel": 114, "id": 76001}, {"cost": [[11, 137101, 230]], "level": 114, "attributes": [[1, 714900], [2, 7149000], [3, 357450], [4, 357450]], "nextLevel": 115, "id": 76001}, {"cost": [[11, 137101, 230]], "level": 115, "attributes": [[1, 724500], [2, 7245000], [3, 362250], [4, 362250]], "nextLevel": 116, "id": 76001}, {"cost": [[11, 137101, 240]], "level": 116, "attributes": [[1, 734400], [2, 7344000], [3, 367200], [4, 367200]], "nextLevel": 117, "id": 76001}, {"cost": [[11, 137101, 240]], "level": 117, "attributes": [[1, 744300], [2, 7443000], [3, 372150], [4, 372150]], "nextLevel": 118, "id": 76001}, {"cost": [[11, 137101, 240]], "level": 118, "attributes": [[1, 754200], [2, 7542000], [3, 377100], [4, 377100]], "nextLevel": 119, "id": 76001}, {"cost": [[11, 137101, 240]], "level": 119, "attributes": [[1, 764100], [2, 7641000], [3, 382050], [4, 382050]], "nextLevel": 120, "id": 76001}, {"cost": [[11, 137101, 240]], "level": 120, "attributes": [[1, 774000], [2, 7740000], [3, 387000], [4, 387000]], "nextLevel": 121, "id": 76001}, {"cost": [[11, 137101, 250]], "level": 121, "attributes": [[1, 784200], [2, 7842000], [3, 392100], [4, 392100]], "nextLevel": 122, "id": 76001}, {"cost": [[11, 137101, 250]], "level": 122, "attributes": [[1, 794400], [2, 7944000], [3, 397200], [4, 397200]], "nextLevel": 123, "id": 76001}, {"cost": [[11, 137101, 250]], "level": 123, "attributes": [[1, 804600], [2, 8046000], [3, 402300], [4, 402300]], "nextLevel": 124, "id": 76001}, {"cost": [[11, 137101, 250]], "level": 124, "attributes": [[1, 814800], [2, 8148000], [3, 407400], [4, 407400]], "nextLevel": 125, "id": 76001}, {"cost": [[11, 137101, 250]], "level": 125, "attributes": [[1, 825000], [2, 8250000], [3, 412500], [4, 412500]], "nextLevel": 126, "id": 76001}, {"cost": [[11, 137101, 260]], "level": 126, "attributes": [[1, 835500], [2, 8355000], [3, 417750], [4, 417750]], "nextLevel": 127, "id": 76001}, {"cost": [[11, 137101, 260]], "level": 127, "attributes": [[1, 846000], [2, 8460000], [3, 423000], [4, 423000]], "nextLevel": 128, "id": 76001}, {"cost": [[11, 137101, 260]], "level": 128, "attributes": [[1, 856500], [2, 8565000], [3, 428250], [4, 428250]], "nextLevel": 129, "id": 76001}, {"cost": [[11, 137101, 260]], "level": 129, "attributes": [[1, 867000], [2, 8670000], [3, 433500], [4, 433500]], "nextLevel": 130, "id": 76001}, {"cost": [[11, 137101, 260]], "level": 130, "attributes": [[1, 877500], [2, 8775000], [3, 438750], [4, 438750]], "nextLevel": 131, "id": 76001}, {"cost": [[11, 137101, 270]], "level": 131, "attributes": [[1, 888300], [2, 8883000], [3, 444150], [4, 444150]], "nextLevel": 132, "id": 76001}, {"cost": [[11, 137101, 270]], "level": 132, "attributes": [[1, 899100], [2, 8991000], [3, 449550], [4, 449550]], "nextLevel": 133, "id": 76001}, {"cost": [[11, 137101, 270]], "level": 133, "attributes": [[1, 909900], [2, 9099000], [3, 454950], [4, 454950]], "nextLevel": 134, "id": 76001}, {"cost": [[11, 137101, 270]], "level": 134, "attributes": [[1, 920700], [2, 9207000], [3, 460350], [4, 460350]], "nextLevel": 135, "id": 76001}, {"cost": [[11, 137101, 270]], "level": 135, "attributes": [[1, 931500], [2, 9315000], [3, 465750], [4, 465750]], "nextLevel": 136, "id": 76001}, {"cost": [[11, 137101, 280]], "level": 136, "attributes": [[1, 942600], [2, 9426000], [3, 471300], [4, 471300]], "nextLevel": 137, "id": 76001}, {"cost": [[11, 137101, 280]], "level": 137, "attributes": [[1, 953700], [2, 9537000], [3, 476850], [4, 476850]], "nextLevel": 138, "id": 76001}, {"cost": [[11, 137101, 280]], "level": 138, "attributes": [[1, 964800], [2, 9648000], [3, 482400], [4, 482400]], "nextLevel": 139, "id": 76001}, {"cost": [[11, 137101, 280]], "level": 139, "attributes": [[1, 975900], [2, 9759000], [3, 487950], [4, 487950]], "nextLevel": 140, "id": 76001}, {"cost": [[11, 137101, 280]], "level": 140, "attributes": [[1, 987000], [2, 9870000], [3, 493500], [4, 493500]], "nextLevel": 141, "id": 76001}, {"cost": [[11, 137101, 290]], "level": 141, "attributes": [[1, 998400], [2, 9984000], [3, 499200], [4, 499200]], "nextLevel": 142, "id": 76001}, {"cost": [[11, 137101, 290]], "level": 142, "attributes": [[1, 1009800], [2, 10098000], [3, 504900], [4, 504900]], "nextLevel": 143, "id": 76001}, {"cost": [[11, 137101, 290]], "level": 143, "attributes": [[1, 1021200], [2, 10212000], [3, 510600], [4, 510600]], "nextLevel": 144, "id": 76001}, {"cost": [[11, 137101, 290]], "level": 144, "attributes": [[1, 1032600], [2, 10326000], [3, 516300], [4, 516300]], "nextLevel": 145, "id": 76001}, {"cost": [[11, 137101, 290]], "level": 145, "attributes": [[1, 1044000], [2, 10440000], [3, 522000], [4, 522000]], "nextLevel": 146, "id": 76001}, {"cost": [[11, 137101, 300]], "level": 146, "attributes": [[1, 1055700], [2, 10557000], [3, 527850], [4, 527850]], "nextLevel": 147, "id": 76001}, {"cost": [[11, 137101, 300]], "level": 147, "attributes": [[1, 1067400], [2, 10674000], [3, 533700], [4, 533700]], "nextLevel": 148, "id": 76001}, {"cost": [[11, 137101, 300]], "level": 148, "attributes": [[1, 1079100], [2, 10791000], [3, 539550], [4, 539550]], "nextLevel": 149, "id": 76001}, {"cost": [[11, 137101, 300]], "level": 149, "attributes": [[1, 1090800], [2, 10908000], [3, 545400], [4, 545400]], "nextLevel": 150, "id": 76001}, {"cost": [[11, 137101, 300]], "level": 150, "attributes": [[1, 1102500], [2, 11025000], [3, 551250], [4, 551250]], "nextLevel": 151, "id": 76001}, {"cost": [[11, 137101, 310]], "level": 151, "attributes": [[1, 1114500], [2, 11145000], [3, 557250], [4, 557250]], "nextLevel": 152, "id": 76001}, {"cost": [[11, 137101, 310]], "level": 152, "attributes": [[1, 1126500], [2, 11265000], [3, 563250], [4, 563250]], "nextLevel": 153, "id": 76001}, {"cost": [[11, 137101, 310]], "level": 153, "attributes": [[1, 1138500], [2, 11385000], [3, 569250], [4, 569250]], "nextLevel": 154, "id": 76001}, {"cost": [[11, 137101, 310]], "level": 154, "attributes": [[1, 1150500], [2, 11505000], [3, 575250], [4, 575250]], "nextLevel": 155, "id": 76001}, {"cost": [[11, 137101, 310]], "level": 155, "attributes": [[1, 1162500], [2, 11625000], [3, 581250], [4, 581250]], "nextLevel": 156, "id": 76001}, {"cost": [[11, 137101, 320]], "level": 156, "attributes": [[1, 1174800], [2, 11748000], [3, 587400], [4, 587400]], "nextLevel": 157, "id": 76001}, {"cost": [[11, 137101, 320]], "level": 157, "attributes": [[1, 1187100], [2, 11871000], [3, 593550], [4, 593550]], "nextLevel": 158, "id": 76001}, {"cost": [[11, 137101, 320]], "level": 158, "attributes": [[1, 1199400], [2, 11994000], [3, 599700], [4, 599700]], "nextLevel": 159, "id": 76001}, {"cost": [[11, 137101, 320]], "level": 159, "attributes": [[1, 1211700], [2, 12117000], [3, 605850], [4, 605850]], "nextLevel": 160, "id": 76001}, {"cost": [[11, 137101, 320]], "level": 160, "attributes": [[1, 1224000], [2, 12240000], [3, 612000], [4, 612000]], "nextLevel": 161, "id": 76001}, {"cost": [[11, 137101, 330]], "level": 161, "attributes": [[1, 1236600], [2, 12366000], [3, 618300], [4, 618300]], "nextLevel": 162, "id": 76001}, {"cost": [[11, 137101, 330]], "level": 162, "attributes": [[1, 1249200], [2, 12492000], [3, 624600], [4, 624600]], "nextLevel": 163, "id": 76001}, {"cost": [[11, 137101, 330]], "level": 163, "attributes": [[1, 1261800], [2, 12618000], [3, 630900], [4, 630900]], "nextLevel": 164, "id": 76001}, {"cost": [[11, 137101, 330]], "level": 164, "attributes": [[1, 1274400], [2, 12744000], [3, 637200], [4, 637200]], "nextLevel": 165, "id": 76001}, {"cost": [[11, 137101, 330]], "level": 165, "attributes": [[1, 1287000], [2, 12870000], [3, 643500], [4, 643500]], "nextLevel": 166, "id": 76001}, {"cost": [[11, 137101, 340]], "level": 166, "attributes": [[1, 1299900], [2, 12999000], [3, 649950], [4, 649950]], "nextLevel": 167, "id": 76001}, {"cost": [[11, 137101, 340]], "level": 167, "attributes": [[1, 1312800], [2, 13128000], [3, 656400], [4, 656400]], "nextLevel": 168, "id": 76001}, {"cost": [[11, 137101, 340]], "level": 168, "attributes": [[1, 1325700], [2, 13257000], [3, 662850], [4, 662850]], "nextLevel": 169, "id": 76001}, {"cost": [[11, 137101, 340]], "level": 169, "attributes": [[1, 1338600], [2, 13386000], [3, 669300], [4, 669300]], "nextLevel": 170, "id": 76001}, {"cost": [[11, 137101, 340]], "level": 170, "attributes": [[1, 1351500], [2, 13515000], [3, 675750], [4, 675750]], "nextLevel": 171, "id": 76001}, {"cost": [[11, 137101, 350]], "level": 171, "attributes": [[1, 1364700], [2, 13647000], [3, 682350], [4, 682350]], "nextLevel": 172, "id": 76001}, {"cost": [[11, 137101, 350]], "level": 172, "attributes": [[1, 1377900], [2, 13779000], [3, 688950], [4, 688950]], "nextLevel": 173, "id": 76001}, {"cost": [[11, 137101, 350]], "level": 173, "attributes": [[1, 1391100], [2, 13911000], [3, 695550], [4, 695550]], "nextLevel": 174, "id": 76001}, {"cost": [[11, 137101, 350]], "level": 174, "attributes": [[1, 1404300], [2, 14043000], [3, 702150], [4, 702150]], "nextLevel": 175, "id": 76001}, {"cost": [[11, 137101, 350]], "level": 175, "attributes": [[1, 1417500], [2, 14175000], [3, 708750], [4, 708750]], "nextLevel": 176, "id": 76001}, {"cost": [[11, 137101, 360]], "level": 176, "attributes": [[1, 1431000], [2, 14310000], [3, 715500], [4, 715500]], "nextLevel": 177, "id": 76001}, {"cost": [[11, 137101, 360]], "level": 177, "attributes": [[1, 1444500], [2, 14445000], [3, 722250], [4, 722250]], "nextLevel": 178, "id": 76001}, {"cost": [[11, 137101, 360]], "level": 178, "attributes": [[1, 1458000], [2, 14580000], [3, 729000], [4, 729000]], "nextLevel": 179, "id": 76001}, {"cost": [[11, 137101, 360]], "level": 179, "attributes": [[1, 1471500], [2, 14715000], [3, 735750], [4, 735750]], "nextLevel": 180, "id": 76001}, {"cost": [[11, 137101, 360]], "level": 180, "attributes": [[1, 1485000], [2, 14850000], [3, 742500], [4, 742500]], "nextLevel": 181, "id": 76001}, {"cost": [[11, 137101, 370]], "level": 181, "attributes": [[1, 1498800], [2, 14988000], [3, 749400], [4, 749400]], "nextLevel": 182, "id": 76001}, {"cost": [[11, 137101, 370]], "level": 182, "attributes": [[1, 1512600], [2, 15126000], [3, 756300], [4, 756300]], "nextLevel": 183, "id": 76001}, {"cost": [[11, 137101, 370]], "level": 183, "attributes": [[1, 1526400], [2, 15264000], [3, 763200], [4, 763200]], "nextLevel": 184, "id": 76001}, {"cost": [[11, 137101, 370]], "level": 184, "attributes": [[1, 1540200], [2, 15402000], [3, 770100], [4, 770100]], "nextLevel": 185, "id": 76001}, {"cost": [[11, 137101, 370]], "level": 185, "attributes": [[1, 1554000], [2, 15540000], [3, 777000], [4, 777000]], "nextLevel": 186, "id": 76001}, {"cost": [[11, 137101, 380]], "level": 186, "attributes": [[1, 1568100], [2, 15681000], [3, 784050], [4, 784050]], "nextLevel": 187, "id": 76001}, {"cost": [[11, 137101, 380]], "level": 187, "attributes": [[1, 1582200], [2, 15822000], [3, 791100], [4, 791100]], "nextLevel": 188, "id": 76001}, {"cost": [[11, 137101, 380]], "level": 188, "attributes": [[1, 1596300], [2, 15963000], [3, 798150], [4, 798150]], "nextLevel": 189, "id": 76001}, {"cost": [[11, 137101, 380]], "level": 189, "attributes": [[1, 1610400], [2, 16104000], [3, 805200], [4, 805200]], "nextLevel": 190, "id": 76001}, {"cost": [[11, 137101, 380]], "level": 190, "attributes": [[1, 1624500], [2, 16245000], [3, 812250], [4, 812250]], "nextLevel": 191, "id": 76001}, {"cost": [[11, 137101, 390]], "level": 191, "attributes": [[1, 1638900], [2, 16389000], [3, 819450], [4, 819450]], "nextLevel": 192, "id": 76001}, {"cost": [[11, 137101, 390]], "level": 192, "attributes": [[1, 1653300], [2, 16533000], [3, 826650], [4, 826650]], "nextLevel": 193, "id": 76001}, {"cost": [[11, 137101, 390]], "level": 193, "attributes": [[1, 1667700], [2, 16677000], [3, 833850], [4, 833850]], "nextLevel": 194, "id": 76001}, {"cost": [[11, 137101, 390]], "level": 194, "attributes": [[1, 1682100], [2, 16821000], [3, 841050], [4, 841050]], "nextLevel": 195, "id": 76001}, {"cost": [[11, 137101, 390]], "level": 195, "attributes": [[1, 1696500], [2, 16965000], [3, 848250], [4, 848250]], "nextLevel": 196, "id": 76001}, {"cost": [[11, 137101, 400]], "level": 196, "attributes": [[1, 1711200], [2, 17112000], [3, 855600], [4, 855600]], "nextLevel": 197, "id": 76001}, {"cost": [[11, 137101, 400]], "level": 197, "attributes": [[1, 1725900], [2, 17259000], [3, 862950], [4, 862950]], "nextLevel": 198, "id": 76001}, {"cost": [[11, 137101, 400]], "level": 198, "attributes": [[1, 1740600], [2, 17406000], [3, 870300], [4, 870300]], "nextLevel": 199, "id": 76001}, {"cost": [[11, 137101, 400]], "level": 199, "attributes": [[1, 1755300], [2, 17553000], [3, 877650], [4, 877650]], "nextLevel": 200, "id": 76001}, {"cost": [[11, 137101, 400]], "level": 200, "attributes": [[1, 1770000], [2, 17700000], [3, 885000], [4, 885000]], "nextLevel": 201, "id": 76001}, {"cost": [[11, 137101, 410]], "level": 201, "attributes": [[1, 1785000], [2, 17850000], [3, 892500], [4, 892500]], "nextLevel": 202, "id": 76001}, {"cost": [[11, 137101, 410]], "level": 202, "attributes": [[1, 1800000], [2, 18000000], [3, 900000], [4, 900000]], "nextLevel": 203, "id": 76001}, {"cost": [[11, 137101, 410]], "level": 203, "attributes": [[1, 1815000], [2, 18150000], [3, 907500], [4, 907500]], "nextLevel": 204, "id": 76001}, {"cost": [[11, 137101, 410]], "level": 204, "attributes": [[1, 1830000], [2, 18300000], [3, 915000], [4, 915000]], "nextLevel": 205, "id": 76001}, {"cost": [[11, 137101, 410]], "level": 205, "attributes": [[1, 1845000], [2, 18450000], [3, 922500], [4, 922500]], "nextLevel": 206, "id": 76001}, {"cost": [[11, 137101, 420]], "level": 206, "attributes": [[1, 1860300], [2, 18603000], [3, 930150], [4, 930150]], "nextLevel": 207, "id": 76001}, {"cost": [[11, 137101, 420]], "level": 207, "attributes": [[1, 1875600], [2, 18756000], [3, 937800], [4, 937800]], "nextLevel": 208, "id": 76001}, {"cost": [[11, 137101, 420]], "level": 208, "attributes": [[1, 1890900], [2, 18909000], [3, 945450], [4, 945450]], "nextLevel": 209, "id": 76001}, {"cost": [[11, 137101, 420]], "level": 209, "attributes": [[1, 1906200], [2, 19062000], [3, 953100], [4, 953100]], "nextLevel": 210, "id": 76001}, {"cost": [[11, 137101, 420]], "level": 210, "attributes": [[1, 1921500], [2, 19215000], [3, 960750], [4, 960750]], "nextLevel": 211, "id": 76001}, {"cost": [[11, 137101, 430]], "level": 211, "attributes": [[1, 1937100], [2, 19371000], [3, 968550], [4, 968550]], "nextLevel": 212, "id": 76001}, {"cost": [[11, 137101, 430]], "level": 212, "attributes": [[1, 1952700], [2, 19527000], [3, 976350], [4, 976350]], "nextLevel": 213, "id": 76001}, {"cost": [[11, 137101, 430]], "level": 213, "attributes": [[1, 1968300], [2, 19683000], [3, 984150], [4, 984150]], "nextLevel": 214, "id": 76001}, {"cost": [[11, 137101, 430]], "level": 214, "attributes": [[1, 1983900], [2, 19839000], [3, 991950], [4, 991950]], "nextLevel": 215, "id": 76001}, {"cost": [[11, 137101, 430]], "level": 215, "attributes": [[1, 1999500], [2, 19995000], [3, 999750], [4, 999750]], "nextLevel": 216, "id": 76001}, {"cost": [[11, 137101, 440]], "level": 216, "attributes": [[1, 2015400], [2, 20154000], [3, 1007700], [4, 1007700]], "nextLevel": 217, "id": 76001}, {"cost": [[11, 137101, 440]], "level": 217, "attributes": [[1, 2031300], [2, 20313000], [3, 1015650], [4, 1015650]], "nextLevel": 218, "id": 76001}, {"cost": [[11, 137101, 440]], "level": 218, "attributes": [[1, 2047200], [2, 20472000], [3, 1023600], [4, 1023600]], "nextLevel": 219, "id": 76001}, {"cost": [[11, 137101, 440]], "level": 219, "attributes": [[1, 2063100], [2, 20631000], [3, 1031550], [4, 1031550]], "nextLevel": 220, "id": 76001}, {"cost": [[11, 137101, 440]], "level": 220, "attributes": [[1, 2079000], [2, 20790000], [3, 1039500], [4, 1039500]], "nextLevel": 221, "id": 76001}, {"cost": [[11, 137101, 450]], "level": 221, "attributes": [[1, 2095200], [2, 20952000], [3, 1047600], [4, 1047600]], "nextLevel": 222, "id": 76001}, {"cost": [[11, 137101, 450]], "level": 222, "attributes": [[1, 2111400], [2, 21114000], [3, 1055700], [4, 1055700]], "nextLevel": 223, "id": 76001}, {"cost": [[11, 137101, 450]], "level": 223, "attributes": [[1, 2127600], [2, 21276000], [3, 1063800], [4, 1063800]], "nextLevel": 224, "id": 76001}, {"cost": [[11, 137101, 450]], "level": 224, "attributes": [[1, 2143800], [2, 21438000], [3, 1071900], [4, 1071900]], "nextLevel": 225, "id": 76001}, {"cost": [[11, 137101, 450]], "level": 225, "attributes": [[1, 2160000], [2, 21600000], [3, 1080000], [4, 1080000]], "nextLevel": 226, "id": 76001}, {"cost": [[11, 137101, 460]], "level": 226, "attributes": [[1, 2176500], [2, 21765000], [3, 1088250], [4, 1088250]], "nextLevel": 227, "id": 76001}, {"cost": [[11, 137101, 460]], "level": 227, "attributes": [[1, 2193000], [2, 21930000], [3, 1096500], [4, 1096500]], "nextLevel": 228, "id": 76001}, {"cost": [[11, 137101, 460]], "level": 228, "attributes": [[1, 2209500], [2, 22095000], [3, 1104750], [4, 1104750]], "nextLevel": 229, "id": 76001}, {"cost": [[11, 137101, 460]], "level": 229, "attributes": [[1, 2226000], [2, 22260000], [3, 1113000], [4, 1113000]], "nextLevel": 230, "id": 76001}, {"cost": [[11, 137101, 460]], "level": 230, "attributes": [[1, 2242500], [2, 22425000], [3, 1121250], [4, 1121250]], "nextLevel": 231, "id": 76001}, {"cost": [[11, 137101, 470]], "level": 231, "attributes": [[1, 2259300], [2, 22593000], [3, 1129650], [4, 1129650]], "nextLevel": 232, "id": 76001}, {"cost": [[11, 137101, 470]], "level": 232, "attributes": [[1, 2276100], [2, 22761000], [3, 1138050], [4, 1138050]], "nextLevel": 233, "id": 76001}, {"cost": [[11, 137101, 470]], "level": 233, "attributes": [[1, 2292900], [2, 22929000], [3, 1146450], [4, 1146450]], "nextLevel": 234, "id": 76001}, {"cost": [[11, 137101, 470]], "level": 234, "attributes": [[1, 2309700], [2, 23097000], [3, 1154850], [4, 1154850]], "nextLevel": 235, "id": 76001}, {"cost": [[11, 137101, 470]], "level": 235, "attributes": [[1, 2326500], [2, 23265000], [3, 1163250], [4, 1163250]], "nextLevel": 236, "id": 76001}, {"cost": [[11, 137101, 480]], "level": 236, "attributes": [[1, 2343600], [2, 23436000], [3, 1171800], [4, 1171800]], "nextLevel": 237, "id": 76001}, {"cost": [[11, 137101, 480]], "level": 237, "attributes": [[1, 2360700], [2, 23607000], [3, 1180350], [4, 1180350]], "nextLevel": 238, "id": 76001}, {"cost": [[11, 137101, 480]], "level": 238, "attributes": [[1, 2377800], [2, 23778000], [3, 1188900], [4, 1188900]], "nextLevel": 239, "id": 76001}, {"cost": [[11, 137101, 480]], "level": 239, "attributes": [[1, 2394900], [2, 23949000], [3, 1197450], [4, 1197450]], "nextLevel": 240, "id": 76001}, {"cost": [[11, 137101, 480]], "level": 240, "attributes": [[1, 2412000], [2, 24120000], [3, 1206000], [4, 1206000]], "nextLevel": 241, "id": 76001}, {"cost": [[11, 137101, 490]], "level": 241, "attributes": [[1, 2429400], [2, 24294000], [3, 1214700], [4, 1214700]], "nextLevel": 242, "id": 76001}, {"cost": [[11, 137101, 490]], "level": 242, "attributes": [[1, 2446800], [2, 24468000], [3, 1223400], [4, 1223400]], "nextLevel": 243, "id": 76001}, {"cost": [[11, 137101, 490]], "level": 243, "attributes": [[1, 2464200], [2, 24642000], [3, 1232100], [4, 1232100]], "nextLevel": 244, "id": 76001}, {"cost": [[11, 137101, 490]], "level": 244, "attributes": [[1, 2481600], [2, 24816000], [3, 1240800], [4, 1240800]], "nextLevel": 245, "id": 76001}, {"cost": [[11, 137101, 490]], "level": 245, "attributes": [[1, 2499000], [2, 24990000], [3, 1249500], [4, 1249500]], "nextLevel": 246, "id": 76001}, {"cost": [[11, 137101, 500]], "level": 246, "attributes": [[1, 2516700], [2, 25167000], [3, 1258350], [4, 1258350]], "nextLevel": 247, "id": 76001}, {"cost": [[11, 137101, 500]], "level": 247, "attributes": [[1, 2534400], [2, 25344000], [3, 1267200], [4, 1267200]], "nextLevel": 248, "id": 76001}, {"cost": [[11, 137101, 500]], "level": 248, "attributes": [[1, 2552100], [2, 25521000], [3, 1276050], [4, 1276050]], "nextLevel": 249, "id": 76001}, {"cost": [[11, 137101, 500]], "level": 249, "attributes": [[1, 2569800], [2, 25698000], [3, 1284900], [4, 1284900]], "nextLevel": 250, "id": 76001}, {"cost": [[11, 137101, 500]], "level": 250, "attributes": [[1, 2587500], [2, 25875000], [3, 1293750], [4, 1293750]], "nextLevel": 251, "id": 76001}, {"cost": [[11, 137101, 510]], "level": 251, "attributes": [[1, 2605500], [2, 26055000], [3, 1302750], [4, 1302750]], "nextLevel": 252, "id": 76001}, {"cost": [[11, 137101, 510]], "level": 252, "attributes": [[1, 2623500], [2, 26235000], [3, 1311750], [4, 1311750]], "nextLevel": 253, "id": 76001}, {"cost": [[11, 137101, 510]], "level": 253, "attributes": [[1, 2641500], [2, 26415000], [3, 1320750], [4, 1320750]], "nextLevel": 254, "id": 76001}, {"cost": [[11, 137101, 510]], "level": 254, "attributes": [[1, 2659500], [2, 26595000], [3, 1329750], [4, 1329750]], "nextLevel": 255, "id": 76001}, {"cost": [[11, 137101, 510]], "level": 255, "attributes": [[1, 2677500], [2, 26775000], [3, 1338750], [4, 1338750]], "nextLevel": 256, "id": 76001}, {"cost": [[11, 137101, 520]], "level": 256, "attributes": [[1, 2695800], [2, 26958000], [3, 1347900], [4, 1347900]], "nextLevel": 257, "id": 76001}, {"cost": [[11, 137101, 520]], "level": 257, "attributes": [[1, 2714100], [2, 27141000], [3, 1357050], [4, 1357050]], "nextLevel": 258, "id": 76001}, {"cost": [[11, 137101, 520]], "level": 258, "attributes": [[1, 2732400], [2, 27324000], [3, 1366200], [4, 1366200]], "nextLevel": 259, "id": 76001}, {"cost": [[11, 137101, 520]], "level": 259, "attributes": [[1, 2750700], [2, 27507000], [3, 1375350], [4, 1375350]], "nextLevel": 260, "id": 76001}, {"cost": [[11, 137101, 520]], "level": 260, "attributes": [[1, 2769000], [2, 27690000], [3, 1384500], [4, 1384500]], "nextLevel": 261, "id": 76001}, {"cost": [[11, 137101, 530]], "level": 261, "attributes": [[1, 2787600], [2, 27876000], [3, 1393800], [4, 1393800]], "nextLevel": 262, "id": 76001}, {"cost": [[11, 137101, 530]], "level": 262, "attributes": [[1, 2806200], [2, 28062000], [3, 1403100], [4, 1403100]], "nextLevel": 263, "id": 76001}, {"cost": [[11, 137101, 530]], "level": 263, "attributes": [[1, 2824800], [2, 28248000], [3, 1412400], [4, 1412400]], "nextLevel": 264, "id": 76001}, {"cost": [[11, 137101, 530]], "level": 264, "attributes": [[1, 2843400], [2, 28434000], [3, 1421700], [4, 1421700]], "nextLevel": 265, "id": 76001}, {"cost": [[11, 137101, 530]], "level": 265, "attributes": [[1, 2862000], [2, 28620000], [3, 1431000], [4, 1431000]], "nextLevel": 266, "id": 76001}, {"cost": [[11, 137101, 540]], "level": 266, "attributes": [[1, 2880900], [2, 28809000], [3, 1440450], [4, 1440450]], "nextLevel": 267, "id": 76001}, {"cost": [[11, 137101, 540]], "level": 267, "attributes": [[1, 2899800], [2, 28998000], [3, 1449900], [4, 1449900]], "nextLevel": 268, "id": 76001}, {"cost": [[11, 137101, 540]], "level": 268, "attributes": [[1, 2918700], [2, 29187000], [3, 1459350], [4, 1459350]], "nextLevel": 269, "id": 76001}, {"cost": [[11, 137101, 540]], "level": 269, "attributes": [[1, 2937600], [2, 29376000], [3, 1468800], [4, 1468800]], "nextLevel": 270, "id": 76001}, {"cost": [[11, 137101, 540]], "level": 270, "attributes": [[1, 2956500], [2, 29565000], [3, 1478250], [4, 1478250]], "nextLevel": 271, "id": 76001}, {"cost": [[11, 137101, 550]], "level": 271, "attributes": [[1, 2975700], [2, 29757000], [3, 1487850], [4, 1487850]], "nextLevel": 272, "id": 76001}, {"cost": [[11, 137101, 550]], "level": 272, "attributes": [[1, 2994900], [2, 29949000], [3, 1497450], [4, 1497450]], "nextLevel": 273, "id": 76001}, {"cost": [[11, 137101, 550]], "level": 273, "attributes": [[1, 3014100], [2, 30141000], [3, 1507050], [4, 1507050]], "nextLevel": 274, "id": 76001}, {"cost": [[11, 137101, 550]], "level": 274, "attributes": [[1, 3033300], [2, 30333000], [3, 1516650], [4, 1516650]], "nextLevel": 275, "id": 76001}, {"cost": [[11, 137101, 550]], "level": 275, "attributes": [[1, 3052500], [2, 30525000], [3, 1526250], [4, 1526250]], "nextLevel": 276, "id": 76001}, {"cost": [[11, 137101, 560]], "level": 276, "attributes": [[1, 3072000], [2, 30720000], [3, 1536000], [4, 1536000]], "nextLevel": 277, "id": 76001}, {"cost": [[11, 137101, 560]], "level": 277, "attributes": [[1, 3091500], [2, 30915000], [3, 1545750], [4, 1545750]], "nextLevel": 278, "id": 76001}, {"cost": [[11, 137101, 560]], "level": 278, "attributes": [[1, 3111000], [2, 31110000], [3, 1555500], [4, 1555500]], "nextLevel": 279, "id": 76001}, {"cost": [[11, 137101, 560]], "level": 279, "attributes": [[1, 3130500], [2, 31305000], [3, 1565250], [4, 1565250]], "nextLevel": 280, "id": 76001}, {"cost": [[11, 137101, 560]], "level": 280, "attributes": [[1, 3150000], [2, 31500000], [3, 1575000], [4, 1575000]], "nextLevel": 281, "id": 76001}, {"cost": [[11, 137101, 570]], "level": 281, "attributes": [[1, 3169800], [2, 31698000], [3, 1584900], [4, 1584900]], "nextLevel": 282, "id": 76001}, {"cost": [[11, 137101, 570]], "level": 282, "attributes": [[1, 3189600], [2, 31896000], [3, 1594800], [4, 1594800]], "nextLevel": 283, "id": 76001}, {"cost": [[11, 137101, 570]], "level": 283, "attributes": [[1, 3209400], [2, 32094000], [3, 1604700], [4, 1604700]], "nextLevel": 284, "id": 76001}, {"cost": [[11, 137101, 570]], "level": 284, "attributes": [[1, 3229200], [2, 32292000], [3, 1614600], [4, 1614600]], "nextLevel": 285, "id": 76001}, {"cost": [[11, 137101, 570]], "level": 285, "attributes": [[1, 3249000], [2, 32490000], [3, 1624500], [4, 1624500]], "nextLevel": 286, "id": 76001}, {"cost": [[11, 137101, 580]], "level": 286, "attributes": [[1, 3269100], [2, 32691000], [3, 1634550], [4, 1634550]], "nextLevel": 287, "id": 76001}, {"cost": [[11, 137101, 580]], "level": 287, "attributes": [[1, 3289200], [2, 32892000], [3, 1644600], [4, 1644600]], "nextLevel": 288, "id": 76001}, {"cost": [[11, 137101, 580]], "level": 288, "attributes": [[1, 3309300], [2, 33093000], [3, 1654650], [4, 1654650]], "nextLevel": 289, "id": 76001}, {"cost": [[11, 137101, 580]], "level": 289, "attributes": [[1, 3329400], [2, 33294000], [3, 1664700], [4, 1664700]], "nextLevel": 290, "id": 76001}, {"cost": [[11, 137101, 580]], "level": 290, "attributes": [[1, 3349500], [2, 33495000], [3, 1674750], [4, 1674750]], "nextLevel": 291, "id": 76001}, {"cost": [[11, 137101, 590]], "level": 291, "attributes": [[1, 3369900], [2, 33699000], [3, 1684950], [4, 1684950]], "nextLevel": 292, "id": 76001}, {"cost": [[11, 137101, 590]], "level": 292, "attributes": [[1, 3390300], [2, 33903000], [3, 1695150], [4, 1695150]], "nextLevel": 293, "id": 76001}, {"cost": [[11, 137101, 590]], "level": 293, "attributes": [[1, 3410700], [2, 34107000], [3, 1705350], [4, 1705350]], "nextLevel": 294, "id": 76001}, {"cost": [[11, 137101, 590]], "level": 294, "attributes": [[1, 3431100], [2, 34311000], [3, 1715550], [4, 1715550]], "nextLevel": 295, "id": 76001}, {"cost": [[11, 137101, 590]], "level": 295, "attributes": [[1, 3451500], [2, 34515000], [3, 1725750], [4, 1725750]], "nextLevel": 296, "id": 76001}, {"cost": [[11, 137101, 600]], "level": 296, "attributes": [[1, 3472200], [2, 34722000], [3, 1736100], [4, 1736100]], "nextLevel": 297, "id": 76001}, {"cost": [[11, 137101, 600]], "level": 297, "attributes": [[1, 3492900], [2, 34929000], [3, 1746450], [4, 1746450]], "nextLevel": 298, "id": 76001}, {"cost": [[11, 137101, 600]], "level": 298, "attributes": [[1, 3513600], [2, 35136000], [3, 1756800], [4, 1756800]], "nextLevel": 299, "id": 76001}, {"cost": [[11, 137101, 600]], "level": 299, "attributes": [[1, 3534300], [2, 35343000], [3, 1767150], [4, 1767150]], "nextLevel": 300, "id": 76001}, {"cost": [[11, 137101, 600]], "level": 300, "attributes": [[1, 3555000], [2, 35550000], [3, 1777500], [4, 1777500]], "nextLevel": 301, "id": 76001}, {"cost": [[11, 137101, 610]], "level": 301, "attributes": [[1, 3576000], [2, 35760000], [3, 1788000], [4, 1788000]], "nextLevel": 302, "id": 76001}, {"cost": [[11, 137101, 610]], "level": 302, "attributes": [[1, 3597000], [2, 35970000], [3, 1798500], [4, 1798500]], "nextLevel": 303, "id": 76001}, {"cost": [[11, 137101, 610]], "level": 303, "attributes": [[1, 3618000], [2, 36180000], [3, 1809000], [4, 1809000]], "nextLevel": 304, "id": 76001}, {"cost": [[11, 137101, 610]], "level": 304, "attributes": [[1, 3639000], [2, 36390000], [3, 1819500], [4, 1819500]], "nextLevel": 305, "id": 76001}, {"cost": [[11, 137101, 610]], "level": 305, "attributes": [[1, 3660000], [2, 36600000], [3, 1830000], [4, 1830000]], "nextLevel": 306, "id": 76001}, {"cost": [[11, 137101, 620]], "level": 306, "attributes": [[1, 3681300], [2, 36813000], [3, 1840650], [4, 1840650]], "nextLevel": 307, "id": 76001}, {"cost": [[11, 137101, 620]], "level": 307, "attributes": [[1, 3702600], [2, 37026000], [3, 1851300], [4, 1851300]], "nextLevel": 308, "id": 76001}, {"cost": [[11, 137101, 620]], "level": 308, "attributes": [[1, 3723900], [2, 37239000], [3, 1861950], [4, 1861950]], "nextLevel": 309, "id": 76001}, {"cost": [[11, 137101, 620]], "level": 309, "attributes": [[1, 3745200], [2, 37452000], [3, 1872600], [4, 1872600]], "nextLevel": 310, "id": 76001}, {"cost": [[11, 137101, 620]], "level": 310, "attributes": [[1, 3766500], [2, 37665000], [3, 1883250], [4, 1883250]], "nextLevel": 311, "id": 76001}, {"cost": [[11, 137101, 630]], "level": 311, "attributes": [[1, 3788100], [2, 37881000], [3, 1894050], [4, 1894050]], "nextLevel": 312, "id": 76001}, {"cost": [[11, 137101, 630]], "level": 312, "attributes": [[1, 3809700], [2, 38097000], [3, 1904850], [4, 1904850]], "nextLevel": 313, "id": 76001}, {"cost": [[11, 137101, 630]], "level": 313, "attributes": [[1, 3831300], [2, 38313000], [3, 1915650], [4, 1915650]], "nextLevel": 314, "id": 76001}, {"cost": [[11, 137101, 630]], "level": 314, "attributes": [[1, 3852900], [2, 38529000], [3, 1926450], [4, 1926450]], "nextLevel": 315, "id": 76001}, {"cost": [[11, 137101, 630]], "level": 315, "attributes": [[1, 3874500], [2, 38745000], [3, 1937250], [4, 1937250]], "nextLevel": 316, "id": 76001}, {"cost": [[11, 137101, 640]], "level": 316, "attributes": [[1, 3896400], [2, 38964000], [3, 1948200], [4, 1948200]], "nextLevel": 317, "id": 76001}, {"cost": [[11, 137101, 640]], "level": 317, "attributes": [[1, 3918300], [2, 39183000], [3, 1959150], [4, 1959150]], "nextLevel": 318, "id": 76001}, {"cost": [[11, 137101, 640]], "level": 318, "attributes": [[1, 3940200], [2, 39402000], [3, 1970100], [4, 1970100]], "nextLevel": 319, "id": 76001}, {"cost": [[11, 137101, 640]], "level": 319, "attributes": [[1, 3962100], [2, 39621000], [3, 1981050], [4, 1981050]], "nextLevel": 320, "id": 76001}, {"cost": [[11, 137101, 640]], "level": 320, "attributes": [[1, 3984000], [2, 39840000], [3, 1992000], [4, 1992000]], "nextLevel": 321, "id": 76001}, {"cost": [[11, 137101, 650]], "level": 321, "attributes": [[1, 4006200], [2, 40062000], [3, 2003100], [4, 2003100]], "nextLevel": 322, "id": 76001}, {"cost": [[11, 137101, 650]], "level": 322, "attributes": [[1, 4028400], [2, 40284000], [3, 2014200], [4, 2014200]], "nextLevel": 323, "id": 76001}, {"cost": [[11, 137101, 650]], "level": 323, "attributes": [[1, 4050600], [2, 40506000], [3, 2025300], [4, 2025300]], "nextLevel": 324, "id": 76001}, {"cost": [[11, 137101, 650]], "level": 324, "attributes": [[1, 4072800], [2, 40728000], [3, 2036400], [4, 2036400]], "nextLevel": 325, "id": 76001}, {"cost": [[11, 137101, 650]], "level": 325, "attributes": [[1, 4095000], [2, 40950000], [3, 2047500], [4, 2047500]], "nextLevel": 326, "id": 76001}, {"cost": [[11, 137101, 660]], "level": 326, "attributes": [[1, 4117500], [2, 41175000], [3, 2058750], [4, 2058750]], "nextLevel": 327, "id": 76001}, {"cost": [[11, 137101, 660]], "level": 327, "attributes": [[1, 4140000], [2, 41400000], [3, 2070000], [4, 2070000]], "nextLevel": 328, "id": 76001}, {"cost": [[11, 137101, 660]], "level": 328, "attributes": [[1, 4162500], [2, 41625000], [3, 2081250], [4, 2081250]], "nextLevel": 329, "id": 76001}, {"cost": [[11, 137101, 660]], "level": 329, "attributes": [[1, 4185000], [2, 41850000], [3, 2092500], [4, 2092500]], "nextLevel": 330, "id": 76001}, {"cost": [[11, 137101, 660]], "level": 330, "attributes": [[1, 4207500], [2, 42075000], [3, 2103750], [4, 2103750]], "nextLevel": 331, "id": 76001}, {"cost": [[11, 137101, 670]], "level": 331, "attributes": [[1, 4230300], [2, 42303000], [3, 2115150], [4, 2115150]], "nextLevel": 332, "id": 76001}, {"cost": [[11, 137101, 670]], "level": 332, "attributes": [[1, 4253100], [2, 42531000], [3, 2126550], [4, 2126550]], "nextLevel": 333, "id": 76001}, {"cost": [[11, 137101, 670]], "level": 333, "attributes": [[1, 4275900], [2, 42759000], [3, 2137950], [4, 2137950]], "nextLevel": 334, "id": 76001}, {"cost": [[11, 137101, 670]], "level": 334, "attributes": [[1, 4298700], [2, 42987000], [3, 2149350], [4, 2149350]], "nextLevel": 335, "id": 76001}, {"cost": [[11, 137101, 670]], "level": 335, "attributes": [[1, 4321500], [2, 43215000], [3, 2160750], [4, 2160750]], "nextLevel": 336, "id": 76001}, {"cost": [[11, 137101, 680]], "level": 336, "attributes": [[1, 4344600], [2, 43446000], [3, 2172300], [4, 2172300]], "nextLevel": 337, "id": 76001}, {"cost": [[11, 137101, 680]], "level": 337, "attributes": [[1, 4367700], [2, 43677000], [3, 2183850], [4, 2183850]], "nextLevel": 338, "id": 76001}, {"cost": [[11, 137101, 680]], "level": 338, "attributes": [[1, 4390800], [2, 43908000], [3, 2195400], [4, 2195400]], "nextLevel": 339, "id": 76001}, {"cost": [[11, 137101, 680]], "level": 339, "attributes": [[1, 4413900], [2, 44139000], [3, 2206950], [4, 2206950]], "nextLevel": 340, "id": 76001}, {"cost": [[11, 137101, 680]], "level": 340, "attributes": [[1, 4437000], [2, 44370000], [3, 2218500], [4, 2218500]], "nextLevel": 341, "id": 76001}, {"cost": [[11, 137101, 690]], "level": 341, "attributes": [[1, 4460400], [2, 44604000], [3, 2230200], [4, 2230200]], "nextLevel": 342, "id": 76001}, {"cost": [[11, 137101, 690]], "level": 342, "attributes": [[1, 4483800], [2, 44838000], [3, 2241900], [4, 2241900]], "nextLevel": 343, "id": 76001}, {"cost": [[11, 137101, 690]], "level": 343, "attributes": [[1, 4507200], [2, 45072000], [3, 2253600], [4, 2253600]], "nextLevel": 344, "id": 76001}, {"cost": [[11, 137101, 690]], "level": 344, "attributes": [[1, 4530600], [2, 45306000], [3, 2265300], [4, 2265300]], "nextLevel": 345, "id": 76001}, {"cost": [[11, 137101, 690]], "level": 345, "attributes": [[1, 4554000], [2, 45540000], [3, 2277000], [4, 2277000]], "nextLevel": 346, "id": 76001}, {"cost": [[11, 137101, 700]], "level": 346, "attributes": [[1, 4577700], [2, 45777000], [3, 2288850], [4, 2288850]], "nextLevel": 347, "id": 76001}, {"cost": [[11, 137101, 700]], "level": 347, "attributes": [[1, 4601400], [2, 46014000], [3, 2300700], [4, 2300700]], "nextLevel": 348, "id": 76001}, {"cost": [[11, 137101, 700]], "level": 348, "attributes": [[1, 4625100], [2, 46251000], [3, 2312550], [4, 2312550]], "nextLevel": 349, "id": 76001}, {"cost": [[11, 137101, 700]], "level": 349, "attributes": [[1, 4648800], [2, 46488000], [3, 2324400], [4, 2324400]], "nextLevel": 350, "id": 76001}, {"cost": [[11, 137101, 700]], "level": 350, "attributes": [[1, 4672500], [2, 46725000], [3, 2336250], [4, 2336250]], "nextLevel": 351, "id": 76001}, {"cost": [[11, 137101, 710]], "level": 351, "attributes": [[1, 4696500], [2, 46965000], [3, 2348250], [4, 2348250]], "nextLevel": 352, "id": 76001}, {"cost": [[11, 137101, 710]], "level": 352, "attributes": [[1, 4720500], [2, 47205000], [3, 2360250], [4, 2360250]], "nextLevel": 353, "id": 76001}, {"cost": [[11, 137101, 710]], "level": 353, "attributes": [[1, 4744500], [2, 47445000], [3, 2372250], [4, 2372250]], "nextLevel": 354, "id": 76001}, {"cost": [[11, 137101, 710]], "level": 354, "attributes": [[1, 4768500], [2, 47685000], [3, 2384250], [4, 2384250]], "nextLevel": 355, "id": 76001}, {"cost": [[11, 137101, 710]], "level": 355, "attributes": [[1, 4792500], [2, 47925000], [3, 2396250], [4, 2396250]], "nextLevel": 356, "id": 76001}, {"cost": [[11, 137101, 720]], "level": 356, "attributes": [[1, 4816800], [2, 48168000], [3, 2408400], [4, 2408400]], "nextLevel": 357, "id": 76001}, {"cost": [[11, 137101, 720]], "level": 357, "attributes": [[1, 4841100], [2, 48411000], [3, 2420550], [4, 2420550]], "nextLevel": 358, "id": 76001}, {"cost": [[11, 137101, 720]], "level": 358, "attributes": [[1, 4865400], [2, 48654000], [3, 2432700], [4, 2432700]], "nextLevel": 359, "id": 76001}, {"cost": [[11, 137101, 720]], "level": 359, "attributes": [[1, 4889700], [2, 48897000], [3, 2444850], [4, 2444850]], "nextLevel": 360, "id": 76001}, {"cost": [[11, 137101, 720]], "level": 360, "attributes": [[1, 4914000], [2, 49140000], [3, 2457000], [4, 2457000]], "nextLevel": 361, "id": 76001}, {"cost": [[11, 137101, 730]], "level": 361, "attributes": [[1, 4938600], [2, 49386000], [3, 2469300], [4, 2469300]], "nextLevel": 362, "id": 76001}, {"cost": [[11, 137101, 730]], "level": 362, "attributes": [[1, 4963200], [2, 49632000], [3, 2481600], [4, 2481600]], "nextLevel": 363, "id": 76001}, {"cost": [[11, 137101, 730]], "level": 363, "attributes": [[1, 4987800], [2, 49878000], [3, 2493900], [4, 2493900]], "nextLevel": 364, "id": 76001}, {"cost": [[11, 137101, 730]], "level": 364, "attributes": [[1, 5012400], [2, 50124000], [3, 2506200], [4, 2506200]], "nextLevel": 365, "id": 76001}, {"cost": [[11, 137101, 730]], "level": 365, "attributes": [[1, 5037000], [2, 50370000], [3, 2518500], [4, 2518500]], "nextLevel": 366, "id": 76001}, {"cost": [[11, 137101, 740]], "level": 366, "attributes": [[1, 5061900], [2, 50619000], [3, 2530950], [4, 2530950]], "nextLevel": 367, "id": 76001}, {"cost": [[11, 137101, 740]], "level": 367, "attributes": [[1, 5086800], [2, 50868000], [3, 2543400], [4, 2543400]], "nextLevel": 368, "id": 76001}, {"cost": [[11, 137101, 740]], "level": 368, "attributes": [[1, 5111700], [2, 51117000], [3, 2555850], [4, 2555850]], "nextLevel": 369, "id": 76001}, {"cost": [[11, 137101, 740]], "level": 369, "attributes": [[1, 5136600], [2, 51366000], [3, 2568300], [4, 2568300]], "nextLevel": 370, "id": 76001}, {"cost": [[11, 137101, 740]], "level": 370, "attributes": [[1, 5161500], [2, 51615000], [3, 2580750], [4, 2580750]], "nextLevel": 371, "id": 76001}, {"cost": [[11, 137101, 750]], "level": 371, "attributes": [[1, 5186700], [2, 51867000], [3, 2593350], [4, 2593350]], "nextLevel": 372, "id": 76001}, {"cost": [[11, 137101, 750]], "level": 372, "attributes": [[1, 5211900], [2, 52119000], [3, 2605950], [4, 2605950]], "nextLevel": 373, "id": 76001}, {"cost": [[11, 137101, 750]], "level": 373, "attributes": [[1, 5237100], [2, 52371000], [3, 2618550], [4, 2618550]], "nextLevel": 374, "id": 76001}, {"cost": [[11, 137101, 750]], "level": 374, "attributes": [[1, 5262300], [2, 52623000], [3, 2631150], [4, 2631150]], "nextLevel": 375, "id": 76001}, {"cost": [[11, 137101, 750]], "level": 375, "attributes": [[1, 5287500], [2, 52875000], [3, 2643750], [4, 2643750]], "nextLevel": 376, "id": 76001}, {"cost": [[11, 137101, 760]], "level": 376, "attributes": [[1, 5313000], [2, 53130000], [3, 2656500], [4, 2656500]], "nextLevel": 377, "id": 76001}, {"cost": [[11, 137101, 760]], "level": 377, "attributes": [[1, 5338500], [2, 53385000], [3, 2669250], [4, 2669250]], "nextLevel": 378, "id": 76001}, {"cost": [[11, 137101, 760]], "level": 378, "attributes": [[1, 5364000], [2, 53640000], [3, 2682000], [4, 2682000]], "nextLevel": 379, "id": 76001}, {"cost": [[11, 137101, 760]], "level": 379, "attributes": [[1, 5389500], [2, 53895000], [3, 2694750], [4, 2694750]], "nextLevel": 380, "id": 76001}, {"cost": [[11, 137101, 760]], "level": 380, "attributes": [[1, 5415000], [2, 54150000], [3, 2707500], [4, 2707500]], "nextLevel": 381, "id": 76001}, {"cost": [[11, 137101, 770]], "level": 381, "attributes": [[1, 5440800], [2, 54408000], [3, 2720400], [4, 2720400]], "nextLevel": 382, "id": 76001}, {"cost": [[11, 137101, 770]], "level": 382, "attributes": [[1, 5466600], [2, 54666000], [3, 2733300], [4, 2733300]], "nextLevel": 383, "id": 76001}, {"cost": [[11, 137101, 770]], "level": 383, "attributes": [[1, 5492400], [2, 54924000], [3, 2746200], [4, 2746200]], "nextLevel": 384, "id": 76001}, {"cost": [[11, 137101, 770]], "level": 384, "attributes": [[1, 5518200], [2, 55182000], [3, 2759100], [4, 2759100]], "nextLevel": 385, "id": 76001}, {"cost": [[11, 137101, 770]], "level": 385, "attributes": [[1, 5544000], [2, 55440000], [3, 2772000], [4, 2772000]], "nextLevel": 386, "id": 76001}, {"cost": [[11, 137101, 780]], "level": 386, "attributes": [[1, 5570100], [2, 55701000], [3, 2785050], [4, 2785050]], "nextLevel": 387, "id": 76001}, {"cost": [[11, 137101, 780]], "level": 387, "attributes": [[1, 5596200], [2, 55962000], [3, 2798100], [4, 2798100]], "nextLevel": 388, "id": 76001}, {"cost": [[11, 137101, 780]], "level": 388, "attributes": [[1, 5622300], [2, 56223000], [3, 2811150], [4, 2811150]], "nextLevel": 389, "id": 76001}, {"cost": [[11, 137101, 780]], "level": 389, "attributes": [[1, 5648400], [2, 56484000], [3, 2824200], [4, 2824200]], "nextLevel": 390, "id": 76001}, {"cost": [[11, 137101, 780]], "level": 390, "attributes": [[1, 5674500], [2, 56745000], [3, 2837250], [4, 2837250]], "nextLevel": 391, "id": 76001}, {"cost": [[11, 137101, 790]], "level": 391, "attributes": [[1, 5700900], [2, 57009000], [3, 2850450], [4, 2850450]], "nextLevel": 392, "id": 76001}, {"cost": [[11, 137101, 790]], "level": 392, "attributes": [[1, 5727300], [2, 57273000], [3, 2863650], [4, 2863650]], "nextLevel": 393, "id": 76001}, {"cost": [[11, 137101, 790]], "level": 393, "attributes": [[1, 5753700], [2, 57537000], [3, 2876850], [4, 2876850]], "nextLevel": 394, "id": 76001}, {"cost": [[11, 137101, 790]], "level": 394, "attributes": [[1, 5780100], [2, 57801000], [3, 2890050], [4, 2890050]], "nextLevel": 395, "id": 76001}, {"cost": [[11, 137101, 790]], "level": 395, "attributes": [[1, 5806500], [2, 58065000], [3, 2903250], [4, 2903250]], "nextLevel": 396, "id": 76001}, {"cost": [[11, 137101, 800]], "level": 396, "attributes": [[1, 5833200], [2, 58332000], [3, 2916600], [4, 2916600]], "nextLevel": 397, "id": 76001}, {"cost": [[11, 137101, 800]], "level": 397, "attributes": [[1, 5859900], [2, 58599000], [3, 2929950], [4, 2929950]], "nextLevel": 398, "id": 76001}, {"cost": [[11, 137101, 800]], "level": 398, "attributes": [[1, 5886600], [2, 58866000], [3, 2943300], [4, 2943300]], "nextLevel": 399, "id": 76001}, {"cost": [[11, 137101, 800]], "level": 399, "attributes": [[1, 5913300], [2, 59133000], [3, 2956650], [4, 2956650]], "nextLevel": 400, "id": 76001}, {"cost": [[11, 137101, 800]], "level": 400, "attributes": [[1, 5940000], [2, 59400000], [3, 2970000], [4, 2970000]], "nextLevel": 401, "id": 76001}, {"cost": [[11, 137101, 810]], "level": 401, "attributes": [[1, 5967000], [2, 59670000], [3, 2983500], [4, 2983500]], "nextLevel": 402, "id": 76001}, {"cost": [[11, 137101, 810]], "level": 402, "attributes": [[1, 5994000], [2, 59940000], [3, 2997000], [4, 2997000]], "nextLevel": 403, "id": 76001}, {"cost": [[11, 137101, 810]], "level": 403, "attributes": [[1, 6021000], [2, 60210000], [3, 3010500], [4, 3010500]], "nextLevel": 404, "id": 76001}, {"cost": [[11, 137101, 810]], "level": 404, "attributes": [[1, 6048000], [2, 60480000], [3, 3024000], [4, 3024000]], "nextLevel": 405, "id": 76001}, {"cost": [[11, 137101, 810]], "level": 405, "attributes": [[1, 6075000], [2, 60750000], [3, 3037500], [4, 3037500]], "nextLevel": 406, "id": 76001}, {"cost": [[11, 137101, 820]], "level": 406, "attributes": [[1, 6102300], [2, 61023000], [3, 3051150], [4, 3051150]], "nextLevel": 407, "id": 76001}, {"cost": [[11, 137101, 820]], "level": 407, "attributes": [[1, 6129600], [2, 61296000], [3, 3064800], [4, 3064800]], "nextLevel": 408, "id": 76001}, {"cost": [[11, 137101, 820]], "level": 408, "attributes": [[1, 6156900], [2, 61569000], [3, 3078450], [4, 3078450]], "nextLevel": 409, "id": 76001}, {"cost": [[11, 137101, 820]], "level": 409, "attributes": [[1, 6184200], [2, 61842000], [3, 3092100], [4, 3092100]], "nextLevel": 410, "id": 76001}, {"cost": [[11, 137101, 820]], "level": 410, "attributes": [[1, 6211500], [2, 62115000], [3, 3105750], [4, 3105750]], "nextLevel": 411, "id": 76001}, {"cost": [[11, 137101, 830]], "level": 411, "attributes": [[1, 6239100], [2, 62391000], [3, 3119550], [4, 3119550]], "nextLevel": 412, "id": 76001}, {"cost": [[11, 137101, 830]], "level": 412, "attributes": [[1, 6266700], [2, 62667000], [3, 3133350], [4, 3133350]], "nextLevel": 413, "id": 76001}, {"cost": [[11, 137101, 830]], "level": 413, "attributes": [[1, 6294300], [2, 62943000], [3, 3147150], [4, 3147150]], "nextLevel": 414, "id": 76001}, {"cost": [[11, 137101, 830]], "level": 414, "attributes": [[1, 6321900], [2, 63219000], [3, 3160950], [4, 3160950]], "nextLevel": 415, "id": 76001}, {"cost": [[11, 137101, 830]], "level": 415, "attributes": [[1, 6349500], [2, 63495000], [3, 3174750], [4, 3174750]], "nextLevel": 416, "id": 76001}, {"cost": [[11, 137101, 840]], "level": 416, "attributes": [[1, 6377400], [2, 63774000], [3, 3188700], [4, 3188700]], "nextLevel": 417, "id": 76001}, {"cost": [[11, 137101, 840]], "level": 417, "attributes": [[1, 6405300], [2, 64053000], [3, 3202650], [4, 3202650]], "nextLevel": 418, "id": 76001}, {"cost": [[11, 137101, 840]], "level": 418, "attributes": [[1, 6433200], [2, 64332000], [3, 3216600], [4, 3216600]], "nextLevel": 419, "id": 76001}, {"cost": [[11, 137101, 840]], "level": 419, "attributes": [[1, 6461100], [2, 64611000], [3, 3230550], [4, 3230550]], "nextLevel": 420, "id": 76001}, {"cost": [[11, 137101, 840]], "level": 420, "attributes": [[1, 6489000], [2, 64890000], [3, 3244500], [4, 3244500]], "nextLevel": 421, "id": 76001}, {"cost": [[11, 137101, 850]], "level": 421, "attributes": [[1, 6517200], [2, 65172000], [3, 3258600], [4, 3258600]], "nextLevel": 422, "id": 76001}, {"cost": [[11, 137101, 850]], "level": 422, "attributes": [[1, 6545400], [2, 65454000], [3, 3272700], [4, 3272700]], "nextLevel": 423, "id": 76001}, {"cost": [[11, 137101, 850]], "level": 423, "attributes": [[1, 6573600], [2, 65736000], [3, 3286800], [4, 3286800]], "nextLevel": 424, "id": 76001}, {"cost": [[11, 137101, 850]], "level": 424, "attributes": [[1, 6601800], [2, 66018000], [3, 3300900], [4, 3300900]], "nextLevel": 425, "id": 76001}, {"cost": [[11, 137101, 850]], "level": 425, "attributes": [[1, 6630000], [2, 66300000], [3, 3315000], [4, 3315000]], "nextLevel": 426, "id": 76001}, {"cost": [[11, 137101, 860]], "level": 426, "attributes": [[1, 6658500], [2, 66585000], [3, 3329250], [4, 3329250]], "nextLevel": 427, "id": 76001}, {"cost": [[11, 137101, 860]], "level": 427, "attributes": [[1, 6687000], [2, 66870000], [3, 3343500], [4, 3343500]], "nextLevel": 428, "id": 76001}, {"cost": [[11, 137101, 860]], "level": 428, "attributes": [[1, 6715500], [2, 67155000], [3, 3357750], [4, 3357750]], "nextLevel": 429, "id": 76001}, {"cost": [[11, 137101, 860]], "level": 429, "attributes": [[1, 6744000], [2, 67440000], [3, 3372000], [4, 3372000]], "nextLevel": 430, "id": 76001}, {"cost": [[11, 137101, 860]], "level": 430, "attributes": [[1, 6772500], [2, 67725000], [3, 3386250], [4, 3386250]], "nextLevel": 431, "id": 76001}, {"cost": [[11, 137101, 870]], "level": 431, "attributes": [[1, 6801300], [2, 68013000], [3, 3400650], [4, 3400650]], "nextLevel": 432, "id": 76001}, {"cost": [[11, 137101, 870]], "level": 432, "attributes": [[1, 6830100], [2, 68301000], [3, 3415050], [4, 3415050]], "nextLevel": 433, "id": 76001}, {"cost": [[11, 137101, 870]], "level": 433, "attributes": [[1, 6858900], [2, 68589000], [3, 3429450], [4, 3429450]], "nextLevel": 434, "id": 76001}, {"cost": [[11, 137101, 870]], "level": 434, "attributes": [[1, 6887700], [2, 68877000], [3, 3443850], [4, 3443850]], "nextLevel": 435, "id": 76001}, {"cost": [[11, 137101, 870]], "level": 435, "attributes": [[1, 6916500], [2, 69165000], [3, 3458250], [4, 3458250]], "nextLevel": 436, "id": 76001}, {"cost": [[11, 137101, 880]], "level": 436, "attributes": [[1, 6945600], [2, 69456000], [3, 3472800], [4, 3472800]], "nextLevel": 437, "id": 76001}, {"cost": [[11, 137101, 880]], "level": 437, "attributes": [[1, 6974700], [2, 69747000], [3, 3487350], [4, 3487350]], "nextLevel": 438, "id": 76001}, {"cost": [[11, 137101, 880]], "level": 438, "attributes": [[1, 7003800], [2, 70038000], [3, 3501900], [4, 3501900]], "nextLevel": 439, "id": 76001}, {"cost": [[11, 137101, 880]], "level": 439, "attributes": [[1, 7032900], [2, 70329000], [3, 3516450], [4, 3516450]], "nextLevel": 440, "id": 76001}, {"cost": [[11, 137101, 880]], "level": 440, "attributes": [[1, 7062000], [2, 70620000], [3, 3531000], [4, 3531000]], "nextLevel": 441, "id": 76001}, {"cost": [[11, 137101, 890]], "level": 441, "attributes": [[1, 7091400], [2, 70914000], [3, 3545700], [4, 3545700]], "nextLevel": 442, "id": 76001}, {"cost": [[11, 137101, 890]], "level": 442, "attributes": [[1, 7120800], [2, 71208000], [3, 3560400], [4, 3560400]], "nextLevel": 443, "id": 76001}, {"cost": [[11, 137101, 890]], "level": 443, "attributes": [[1, 7150200], [2, 71502000], [3, 3575100], [4, 3575100]], "nextLevel": 444, "id": 76001}, {"cost": [[11, 137101, 890]], "level": 444, "attributes": [[1, 7179600], [2, 71796000], [3, 3589800], [4, 3589800]], "nextLevel": 445, "id": 76001}, {"cost": [[11, 137101, 890]], "level": 445, "attributes": [[1, 7209000], [2, 72090000], [3, 3604500], [4, 3604500]], "nextLevel": 446, "id": 76001}, {"cost": [[11, 137101, 900]], "level": 446, "attributes": [[1, 7238700], [2, 72387000], [3, 3619350], [4, 3619350]], "nextLevel": 447, "id": 76001}, {"cost": [[11, 137101, 900]], "level": 447, "attributes": [[1, 7268400], [2, 72684000], [3, 3634200], [4, 3634200]], "nextLevel": 448, "id": 76001}, {"cost": [[11, 137101, 900]], "level": 448, "attributes": [[1, 7298100], [2, 72981000], [3, 3649050], [4, 3649050]], "nextLevel": 449, "id": 76001}, {"cost": [[11, 137101, 900]], "level": 449, "attributes": [[1, 7327800], [2, 73278000], [3, 3663900], [4, 3663900]], "nextLevel": 450, "id": 76001}, {"cost": [[11, 137101, 900]], "level": 450, "attributes": [[1, 7357500], [2, 73575000], [3, 3678750], [4, 3678750]], "nextLevel": 451, "id": 76001}, {"cost": [[11, 137101, 910]], "level": 451, "attributes": [[1, 7387500], [2, 73875000], [3, 3693750], [4, 3693750]], "nextLevel": 452, "id": 76001}, {"cost": [[11, 137101, 910]], "level": 452, "attributes": [[1, 7417500], [2, 74175000], [3, 3708750], [4, 3708750]], "nextLevel": 453, "id": 76001}, {"cost": [[11, 137101, 910]], "level": 453, "attributes": [[1, 7447500], [2, 74475000], [3, 3723750], [4, 3723750]], "nextLevel": 454, "id": 76001}, {"cost": [[11, 137101, 910]], "level": 454, "attributes": [[1, 7477500], [2, 74775000], [3, 3738750], [4, 3738750]], "nextLevel": 455, "id": 76001}, {"cost": [[11, 137101, 910]], "level": 455, "attributes": [[1, 7507500], [2, 75075000], [3, 3753750], [4, 3753750]], "nextLevel": 456, "id": 76001}, {"cost": [[11, 137101, 920]], "level": 456, "attributes": [[1, 7537800], [2, 75378000], [3, 3768900], [4, 3768900]], "nextLevel": 457, "id": 76001}, {"cost": [[11, 137101, 920]], "level": 457, "attributes": [[1, 7568100], [2, 75681000], [3, 3784050], [4, 3784050]], "nextLevel": 458, "id": 76001}, {"cost": [[11, 137101, 920]], "level": 458, "attributes": [[1, 7598400], [2, 75984000], [3, 3799200], [4, 3799200]], "nextLevel": 459, "id": 76001}, {"cost": [[11, 137101, 920]], "level": 459, "attributes": [[1, 7628700], [2, 76287000], [3, 3814350], [4, 3814350]], "nextLevel": 460, "id": 76001}, {"cost": [[11, 137101, 920]], "level": 460, "attributes": [[1, 7659000], [2, 76590000], [3, 3829500], [4, 3829500]], "nextLevel": 461, "id": 76001}, {"cost": [[11, 137101, 930]], "level": 461, "attributes": [[1, 7689600], [2, 76896000], [3, 3844800], [4, 3844800]], "nextLevel": 462, "id": 76001}, {"cost": [[11, 137101, 930]], "level": 462, "attributes": [[1, 7720200], [2, 77202000], [3, 3860100], [4, 3860100]], "nextLevel": 463, "id": 76001}, {"cost": [[11, 137101, 930]], "level": 463, "attributes": [[1, 7750800], [2, 77508000], [3, 3875400], [4, 3875400]], "nextLevel": 464, "id": 76001}, {"cost": [[11, 137101, 930]], "level": 464, "attributes": [[1, 7781400], [2, 77814000], [3, 3890700], [4, 3890700]], "nextLevel": 465, "id": 76001}, {"cost": [[11, 137101, 930]], "level": 465, "attributes": [[1, 7812000], [2, 78120000], [3, 3906000], [4, 3906000]], "nextLevel": 466, "id": 76001}, {"cost": [[11, 137101, 940]], "level": 466, "attributes": [[1, 7842900], [2, 78429000], [3, 3921450], [4, 3921450]], "nextLevel": 467, "id": 76001}, {"cost": [[11, 137101, 940]], "level": 467, "attributes": [[1, 7873800], [2, 78738000], [3, 3936900], [4, 3936900]], "nextLevel": 468, "id": 76001}, {"cost": [[11, 137101, 940]], "level": 468, "attributes": [[1, 7904700], [2, 79047000], [3, 3952350], [4, 3952350]], "nextLevel": 469, "id": 76001}, {"cost": [[11, 137101, 940]], "level": 469, "attributes": [[1, 7935600], [2, 79356000], [3, 3967800], [4, 3967800]], "nextLevel": 470, "id": 76001}, {"cost": [[11, 137101, 940]], "level": 470, "attributes": [[1, 7966500], [2, 79665000], [3, 3983250], [4, 3983250]], "nextLevel": 471, "id": 76001}, {"cost": [[11, 137101, 950]], "level": 471, "attributes": [[1, 7997700], [2, 79977000], [3, 3998850], [4, 3998850]], "nextLevel": 472, "id": 76001}, {"cost": [[11, 137101, 950]], "level": 472, "attributes": [[1, 8028900], [2, 80289000], [3, 4014450], [4, 4014450]], "nextLevel": 473, "id": 76001}, {"cost": [[11, 137101, 950]], "level": 473, "attributes": [[1, 8060100], [2, 80601000], [3, 4030050], [4, 4030050]], "nextLevel": 474, "id": 76001}, {"cost": [[11, 137101, 950]], "level": 474, "attributes": [[1, 8091300], [2, 80913000], [3, 4045650], [4, 4045650]], "nextLevel": 475, "id": 76001}, {"cost": [[11, 137101, 950]], "level": 475, "attributes": [[1, 8122500], [2, 81225000], [3, 4061250], [4, 4061250]], "nextLevel": 476, "id": 76001}, {"cost": [[11, 137101, 960]], "level": 476, "attributes": [[1, 8154000], [2, 81540000], [3, 4077000], [4, 4077000]], "nextLevel": 477, "id": 76001}, {"cost": [[11, 137101, 960]], "level": 477, "attributes": [[1, 8185500], [2, 81855000], [3, 4092750], [4, 4092750]], "nextLevel": 478, "id": 76001}, {"cost": [[11, 137101, 960]], "level": 478, "attributes": [[1, 8217000], [2, 82170000], [3, 4108500], [4, 4108500]], "nextLevel": 479, "id": 76001}, {"cost": [[11, 137101, 960]], "level": 479, "attributes": [[1, 8248500], [2, 82485000], [3, 4124250], [4, 4124250]], "nextLevel": 480, "id": 76001}, {"cost": [[11, 137101, 960]], "level": 480, "attributes": [[1, 8280000], [2, 82800000], [3, 4140000], [4, 4140000]], "nextLevel": 481, "id": 76001}, {"cost": [[11, 137101, 970]], "level": 481, "attributes": [[1, 8311800], [2, 83118000], [3, 4155900], [4, 4155900]], "nextLevel": 482, "id": 76001}, {"cost": [[11, 137101, 970]], "level": 482, "attributes": [[1, 8343600], [2, 83436000], [3, 4171800], [4, 4171800]], "nextLevel": 483, "id": 76001}, {"cost": [[11, 137101, 970]], "level": 483, "attributes": [[1, 8375400], [2, 83754000], [3, 4187700], [4, 4187700]], "nextLevel": 484, "id": 76001}, {"cost": [[11, 137101, 970]], "level": 484, "attributes": [[1, 8407200], [2, 84072000], [3, 4203600], [4, 4203600]], "nextLevel": 485, "id": 76001}, {"cost": [[11, 137101, 970]], "level": 485, "attributes": [[1, 8439000], [2, 84390000], [3, 4219500], [4, 4219500]], "nextLevel": 486, "id": 76001}, {"cost": [[11, 137101, 980]], "level": 486, "attributes": [[1, 8471100], [2, 84711000], [3, 4235550], [4, 4235550]], "nextLevel": 487, "id": 76001}, {"cost": [[11, 137101, 980]], "level": 487, "attributes": [[1, 8503200], [2, 85032000], [3, 4251600], [4, 4251600]], "nextLevel": 488, "id": 76001}, {"cost": [[11, 137101, 980]], "level": 488, "attributes": [[1, 8535300], [2, 85353000], [3, 4267650], [4, 4267650]], "nextLevel": 489, "id": 76001}, {"cost": [[11, 137101, 980]], "level": 489, "attributes": [[1, 8567400], [2, 85674000], [3, 4283700], [4, 4283700]], "nextLevel": 490, "id": 76001}, {"cost": [[11, 137101, 980]], "level": 490, "attributes": [[1, 8599500], [2, 85995000], [3, 4299750], [4, 4299750]], "nextLevel": 491, "id": 76001}, {"cost": [[11, 137101, 990]], "level": 491, "attributes": [[1, 8631900], [2, 86319000], [3, 4315950], [4, 4315950]], "nextLevel": 492, "id": 76001}, {"cost": [[11, 137101, 990]], "level": 492, "attributes": [[1, 8664300], [2, 86643000], [3, 4332150], [4, 4332150]], "nextLevel": 493, "id": 76001}, {"cost": [[11, 137101, 990]], "level": 493, "attributes": [[1, 8696700], [2, 86967000], [3, 4348350], [4, 4348350]], "nextLevel": 494, "id": 76001}, {"cost": [[11, 137101, 990]], "level": 494, "attributes": [[1, 8729100], [2, 87291000], [3, 4364550], [4, 4364550]], "nextLevel": 495, "id": 76001}, {"cost": [[11, 137101, 990]], "level": 495, "attributes": [[1, 8761500], [2, 87615000], [3, 4380750], [4, 4380750]], "nextLevel": 496, "id": 76001}, {"cost": [[11, 137101, 1000]], "level": 496, "attributes": [[1, 8794200], [2, 87942000], [3, 4397100], [4, 4397100]], "nextLevel": 497, "id": 76001}, {"cost": [[11, 137101, 1000]], "level": 497, "attributes": [[1, 8826900], [2, 88269000], [3, 4413450], [4, 4413450]], "nextLevel": 498, "id": 76001}, {"cost": [[11, 137101, 1000]], "level": 498, "attributes": [[1, 8859600], [2, 88596000], [3, 4429800], [4, 4429800]], "nextLevel": 499, "id": 76001}, {"cost": [[11, 137101, 1000]], "level": 499, "attributes": [[1, 8892300], [2, 88923000], [3, 4446150], [4, 4446150]], "nextLevel": 500, "id": 76001}, {"cost": [], "level": 500, "attributes": [[1, 8925000], [2, 89250000], [3, 4462500], [4, 4462500]], "nextLevel": 0, "id": 76001}, {"cost": [[11, 137101, 10]], "level": 1, "attributes": [[1, 3000], [2, 30000], [3, 1500], [4, 1500]], "nextLevel": 2, "id": 76002}, {"cost": [[11, 137101, 10]], "level": 2, "attributes": [[1, 6000], [2, 60000], [3, 3000], [4, 3000]], "nextLevel": 3, "id": 76002}, {"cost": [[11, 137101, 10]], "level": 3, "attributes": [[1, 9000], [2, 90000], [3, 4500], [4, 4500]], "nextLevel": 4, "id": 76002}, {"cost": [[11, 137101, 10]], "level": 4, "attributes": [[1, 12000], [2, 120000], [3, 6000], [4, 6000]], "nextLevel": 5, "id": 76002}, {"cost": [[11, 137101, 10]], "level": 5, "attributes": [[1, 15000], [2, 150000], [3, 7500], [4, 7500]], "nextLevel": 6, "id": 76002}, {"cost": [[11, 137101, 20]], "level": 6, "attributes": [[1, 18300], [2, 183000], [3, 9150], [4, 9150]], "nextLevel": 7, "id": 76002}, {"cost": [[11, 137101, 20]], "level": 7, "attributes": [[1, 21600], [2, 216000], [3, 10800], [4, 10800]], "nextLevel": 8, "id": 76002}, {"cost": [[11, 137101, 20]], "level": 8, "attributes": [[1, 24900], [2, 249000], [3, 12450], [4, 12450]], "nextLevel": 9, "id": 76002}, {"cost": [[11, 137101, 20]], "level": 9, "attributes": [[1, 28200], [2, 282000], [3, 14100], [4, 14100]], "nextLevel": 10, "id": 76002}, {"cost": [[11, 137101, 20]], "level": 10, "attributes": [[1, 31500], [2, 315000], [3, 15750], [4, 15750]], "nextLevel": 11, "id": 76002}, {"cost": [[11, 137101, 30]], "level": 11, "attributes": [[1, 35100], [2, 351000], [3, 17550], [4, 17550]], "nextLevel": 12, "id": 76002}, {"cost": [[11, 137101, 30]], "level": 12, "attributes": [[1, 38700], [2, 387000], [3, 19350], [4, 19350]], "nextLevel": 13, "id": 76002}, {"cost": [[11, 137101, 30]], "level": 13, "attributes": [[1, 42300], [2, 423000], [3, 21150], [4, 21150]], "nextLevel": 14, "id": 76002}, {"cost": [[11, 137101, 30]], "level": 14, "attributes": [[1, 45900], [2, 459000], [3, 22950], [4, 22950]], "nextLevel": 15, "id": 76002}, {"cost": [[11, 137101, 30]], "level": 15, "attributes": [[1, 49500], [2, 495000], [3, 24750], [4, 24750]], "nextLevel": 16, "id": 76002}, {"cost": [[11, 137101, 40]], "level": 16, "attributes": [[1, 53400], [2, 534000], [3, 26700], [4, 26700]], "nextLevel": 17, "id": 76002}, {"cost": [[11, 137101, 40]], "level": 17, "attributes": [[1, 57300], [2, 573000], [3, 28650], [4, 28650]], "nextLevel": 18, "id": 76002}, {"cost": [[11, 137101, 40]], "level": 18, "attributes": [[1, 61200], [2, 612000], [3, 30600], [4, 30600]], "nextLevel": 19, "id": 76002}, {"cost": [[11, 137101, 40]], "level": 19, "attributes": [[1, 65100], [2, 651000], [3, 32550], [4, 32550]], "nextLevel": 20, "id": 76002}, {"cost": [[11, 137101, 40]], "level": 20, "attributes": [[1, 69000], [2, 690000], [3, 34500], [4, 34500]], "nextLevel": 21, "id": 76002}, {"cost": [[11, 137101, 50]], "level": 21, "attributes": [[1, 73200], [2, 732000], [3, 36600], [4, 36600]], "nextLevel": 22, "id": 76002}, {"cost": [[11, 137101, 50]], "level": 22, "attributes": [[1, 77400], [2, 774000], [3, 38700], [4, 38700]], "nextLevel": 23, "id": 76002}, {"cost": [[11, 137101, 50]], "level": 23, "attributes": [[1, 81600], [2, 816000], [3, 40800], [4, 40800]], "nextLevel": 24, "id": 76002}, {"cost": [[11, 137101, 50]], "level": 24, "attributes": [[1, 85800], [2, 858000], [3, 42900], [4, 42900]], "nextLevel": 25, "id": 76002}, {"cost": [[11, 137101, 50]], "level": 25, "attributes": [[1, 90000], [2, 900000], [3, 45000], [4, 45000]], "nextLevel": 26, "id": 76002}, {"cost": [[11, 137101, 60]], "level": 26, "attributes": [[1, 94500], [2, 945000], [3, 47250], [4, 47250]], "nextLevel": 27, "id": 76002}, {"cost": [[11, 137101, 60]], "level": 27, "attributes": [[1, 99000], [2, 990000], [3, 49500], [4, 49500]], "nextLevel": 28, "id": 76002}, {"cost": [[11, 137101, 60]], "level": 28, "attributes": [[1, 103500], [2, 1035000], [3, 51750], [4, 51750]], "nextLevel": 29, "id": 76002}, {"cost": [[11, 137101, 60]], "level": 29, "attributes": [[1, 108000], [2, 1080000], [3, 54000], [4, 54000]], "nextLevel": 30, "id": 76002}, {"cost": [[11, 137101, 60]], "level": 30, "attributes": [[1, 112500], [2, 1125000], [3, 56250], [4, 56250]], "nextLevel": 31, "id": 76002}, {"cost": [[11, 137101, 70]], "level": 31, "attributes": [[1, 117300], [2, 1173000], [3, 58650], [4, 58650]], "nextLevel": 32, "id": 76002}, {"cost": [[11, 137101, 70]], "level": 32, "attributes": [[1, 122100], [2, 1221000], [3, 61050], [4, 61050]], "nextLevel": 33, "id": 76002}, {"cost": [[11, 137101, 70]], "level": 33, "attributes": [[1, 126900], [2, 1269000], [3, 63450], [4, 63450]], "nextLevel": 34, "id": 76002}, {"cost": [[11, 137101, 70]], "level": 34, "attributes": [[1, 131700], [2, 1317000], [3, 65850], [4, 65850]], "nextLevel": 35, "id": 76002}, {"cost": [[11, 137101, 70]], "level": 35, "attributes": [[1, 136500], [2, 1365000], [3, 68250], [4, 68250]], "nextLevel": 36, "id": 76002}, {"cost": [[11, 137101, 80]], "level": 36, "attributes": [[1, 141600], [2, 1416000], [3, 70800], [4, 70800]], "nextLevel": 37, "id": 76002}, {"cost": [[11, 137101, 80]], "level": 37, "attributes": [[1, 146700], [2, 1467000], [3, 73350], [4, 73350]], "nextLevel": 38, "id": 76002}, {"cost": [[11, 137101, 80]], "level": 38, "attributes": [[1, 151800], [2, 1518000], [3, 75900], [4, 75900]], "nextLevel": 39, "id": 76002}, {"cost": [[11, 137101, 80]], "level": 39, "attributes": [[1, 156900], [2, 1569000], [3, 78450], [4, 78450]], "nextLevel": 40, "id": 76002}, {"cost": [[11, 137101, 80]], "level": 40, "attributes": [[1, 162000], [2, 1620000], [3, 81000], [4, 81000]], "nextLevel": 41, "id": 76002}, {"cost": [[11, 137101, 90]], "level": 41, "attributes": [[1, 167400], [2, 1674000], [3, 83700], [4, 83700]], "nextLevel": 42, "id": 76002}, {"cost": [[11, 137101, 90]], "level": 42, "attributes": [[1, 172800], [2, 1728000], [3, 86400], [4, 86400]], "nextLevel": 43, "id": 76002}, {"cost": [[11, 137101, 90]], "level": 43, "attributes": [[1, 178200], [2, 1782000], [3, 89100], [4, 89100]], "nextLevel": 44, "id": 76002}, {"cost": [[11, 137101, 90]], "level": 44, "attributes": [[1, 183600], [2, 1836000], [3, 91800], [4, 91800]], "nextLevel": 45, "id": 76002}, {"cost": [[11, 137101, 90]], "level": 45, "attributes": [[1, 189000], [2, 1890000], [3, 94500], [4, 94500]], "nextLevel": 46, "id": 76002}, {"cost": [[11, 137101, 100]], "level": 46, "attributes": [[1, 194700], [2, 1947000], [3, 97350], [4, 97350]], "nextLevel": 47, "id": 76002}, {"cost": [[11, 137101, 100]], "level": 47, "attributes": [[1, 200400], [2, 2004000], [3, 100200], [4, 100200]], "nextLevel": 48, "id": 76002}, {"cost": [[11, 137101, 100]], "level": 48, "attributes": [[1, 206100], [2, 2061000], [3, 103050], [4, 103050]], "nextLevel": 49, "id": 76002}, {"cost": [[11, 137101, 100]], "level": 49, "attributes": [[1, 211800], [2, 2118000], [3, 105900], [4, 105900]], "nextLevel": 50, "id": 76002}, {"cost": [[11, 137101, 100]], "level": 50, "attributes": [[1, 217500], [2, 2175000], [3, 108750], [4, 108750]], "nextLevel": 51, "id": 76002}, {"cost": [[11, 137101, 110]], "level": 51, "attributes": [[1, 223500], [2, 2235000], [3, 111750], [4, 111750]], "nextLevel": 52, "id": 76002}, {"cost": [[11, 137101, 110]], "level": 52, "attributes": [[1, 229500], [2, 2295000], [3, 114750], [4, 114750]], "nextLevel": 53, "id": 76002}, {"cost": [[11, 137101, 110]], "level": 53, "attributes": [[1, 235500], [2, 2355000], [3, 117750], [4, 117750]], "nextLevel": 54, "id": 76002}, {"cost": [[11, 137101, 110]], "level": 54, "attributes": [[1, 241500], [2, 2415000], [3, 120750], [4, 120750]], "nextLevel": 55, "id": 76002}, {"cost": [[11, 137101, 110]], "level": 55, "attributes": [[1, 247500], [2, 2475000], [3, 123750], [4, 123750]], "nextLevel": 56, "id": 76002}, {"cost": [[11, 137101, 120]], "level": 56, "attributes": [[1, 253800], [2, 2538000], [3, 126900], [4, 126900]], "nextLevel": 57, "id": 76002}, {"cost": [[11, 137101, 120]], "level": 57, "attributes": [[1, 260100], [2, 2601000], [3, 130050], [4, 130050]], "nextLevel": 58, "id": 76002}, {"cost": [[11, 137101, 120]], "level": 58, "attributes": [[1, 266400], [2, 2664000], [3, 133200], [4, 133200]], "nextLevel": 59, "id": 76002}, {"cost": [[11, 137101, 120]], "level": 59, "attributes": [[1, 272700], [2, 2727000], [3, 136350], [4, 136350]], "nextLevel": 60, "id": 76002}, {"cost": [[11, 137101, 120]], "level": 60, "attributes": [[1, 279000], [2, 2790000], [3, 139500], [4, 139500]], "nextLevel": 61, "id": 76002}, {"cost": [[11, 137101, 130]], "level": 61, "attributes": [[1, 285600], [2, 2856000], [3, 142800], [4, 142800]], "nextLevel": 62, "id": 76002}, {"cost": [[11, 137101, 130]], "level": 62, "attributes": [[1, 292200], [2, 2922000], [3, 146100], [4, 146100]], "nextLevel": 63, "id": 76002}, {"cost": [[11, 137101, 130]], "level": 63, "attributes": [[1, 298800], [2, 2988000], [3, 149400], [4, 149400]], "nextLevel": 64, "id": 76002}, {"cost": [[11, 137101, 130]], "level": 64, "attributes": [[1, 305400], [2, 3054000], [3, 152700], [4, 152700]], "nextLevel": 65, "id": 76002}, {"cost": [[11, 137101, 130]], "level": 65, "attributes": [[1, 312000], [2, 3120000], [3, 156000], [4, 156000]], "nextLevel": 66, "id": 76002}, {"cost": [[11, 137101, 140]], "level": 66, "attributes": [[1, 318900], [2, 3189000], [3, 159450], [4, 159450]], "nextLevel": 67, "id": 76002}, {"cost": [[11, 137101, 140]], "level": 67, "attributes": [[1, 325800], [2, 3258000], [3, 162900], [4, 162900]], "nextLevel": 68, "id": 76002}, {"cost": [[11, 137101, 140]], "level": 68, "attributes": [[1, 332700], [2, 3327000], [3, 166350], [4, 166350]], "nextLevel": 69, "id": 76002}, {"cost": [[11, 137101, 140]], "level": 69, "attributes": [[1, 339600], [2, 3396000], [3, 169800], [4, 169800]], "nextLevel": 70, "id": 76002}, {"cost": [[11, 137101, 140]], "level": 70, "attributes": [[1, 346500], [2, 3465000], [3, 173250], [4, 173250]], "nextLevel": 71, "id": 76002}, {"cost": [[11, 137101, 150]], "level": 71, "attributes": [[1, 353700], [2, 3537000], [3, 176850], [4, 176850]], "nextLevel": 72, "id": 76002}, {"cost": [[11, 137101, 150]], "level": 72, "attributes": [[1, 360900], [2, 3609000], [3, 180450], [4, 180450]], "nextLevel": 73, "id": 76002}, {"cost": [[11, 137101, 150]], "level": 73, "attributes": [[1, 368100], [2, 3681000], [3, 184050], [4, 184050]], "nextLevel": 74, "id": 76002}, {"cost": [[11, 137101, 150]], "level": 74, "attributes": [[1, 375300], [2, 3753000], [3, 187650], [4, 187650]], "nextLevel": 75, "id": 76002}, {"cost": [[11, 137101, 150]], "level": 75, "attributes": [[1, 382500], [2, 3825000], [3, 191250], [4, 191250]], "nextLevel": 76, "id": 76002}, {"cost": [[11, 137101, 160]], "level": 76, "attributes": [[1, 390000], [2, 3900000], [3, 195000], [4, 195000]], "nextLevel": 77, "id": 76002}, {"cost": [[11, 137101, 160]], "level": 77, "attributes": [[1, 397500], [2, 3975000], [3, 198750], [4, 198750]], "nextLevel": 78, "id": 76002}, {"cost": [[11, 137101, 160]], "level": 78, "attributes": [[1, 405000], [2, 4050000], [3, 202500], [4, 202500]], "nextLevel": 79, "id": 76002}, {"cost": [[11, 137101, 160]], "level": 79, "attributes": [[1, 412500], [2, 4125000], [3, 206250], [4, 206250]], "nextLevel": 80, "id": 76002}, {"cost": [[11, 137101, 160]], "level": 80, "attributes": [[1, 420000], [2, 4200000], [3, 210000], [4, 210000]], "nextLevel": 81, "id": 76002}, {"cost": [[11, 137101, 170]], "level": 81, "attributes": [[1, 427800], [2, 4278000], [3, 213900], [4, 213900]], "nextLevel": 82, "id": 76002}, {"cost": [[11, 137101, 170]], "level": 82, "attributes": [[1, 435600], [2, 4356000], [3, 217800], [4, 217800]], "nextLevel": 83, "id": 76002}, {"cost": [[11, 137101, 170]], "level": 83, "attributes": [[1, 443400], [2, 4434000], [3, 221700], [4, 221700]], "nextLevel": 84, "id": 76002}, {"cost": [[11, 137101, 170]], "level": 84, "attributes": [[1, 451200], [2, 4512000], [3, 225600], [4, 225600]], "nextLevel": 85, "id": 76002}, {"cost": [[11, 137101, 170]], "level": 85, "attributes": [[1, 459000], [2, 4590000], [3, 229500], [4, 229500]], "nextLevel": 86, "id": 76002}, {"cost": [[11, 137101, 180]], "level": 86, "attributes": [[1, 467100], [2, 4671000], [3, 233550], [4, 233550]], "nextLevel": 87, "id": 76002}, {"cost": [[11, 137101, 180]], "level": 87, "attributes": [[1, 475200], [2, 4752000], [3, 237600], [4, 237600]], "nextLevel": 88, "id": 76002}, {"cost": [[11, 137101, 180]], "level": 88, "attributes": [[1, 483300], [2, 4833000], [3, 241650], [4, 241650]], "nextLevel": 89, "id": 76002}, {"cost": [[11, 137101, 180]], "level": 89, "attributes": [[1, 491400], [2, 4914000], [3, 245700], [4, 245700]], "nextLevel": 90, "id": 76002}, {"cost": [[11, 137101, 180]], "level": 90, "attributes": [[1, 499500], [2, 4995000], [3, 249750], [4, 249750]], "nextLevel": 91, "id": 76002}, {"cost": [[11, 137101, 190]], "level": 91, "attributes": [[1, 507900], [2, 5079000], [3, 253950], [4, 253950]], "nextLevel": 92, "id": 76002}, {"cost": [[11, 137101, 190]], "level": 92, "attributes": [[1, 516300], [2, 5163000], [3, 258150], [4, 258150]], "nextLevel": 93, "id": 76002}, {"cost": [[11, 137101, 190]], "level": 93, "attributes": [[1, 524700], [2, 5247000], [3, 262350], [4, 262350]], "nextLevel": 94, "id": 76002}, {"cost": [[11, 137101, 190]], "level": 94, "attributes": [[1, 533100], [2, 5331000], [3, 266550], [4, 266550]], "nextLevel": 95, "id": 76002}, {"cost": [[11, 137101, 190]], "level": 95, "attributes": [[1, 541500], [2, 5415000], [3, 270750], [4, 270750]], "nextLevel": 96, "id": 76002}, {"cost": [[11, 137101, 200]], "level": 96, "attributes": [[1, 550200], [2, 5502000], [3, 275100], [4, 275100]], "nextLevel": 97, "id": 76002}, {"cost": [[11, 137101, 200]], "level": 97, "attributes": [[1, 558900], [2, 5589000], [3, 279450], [4, 279450]], "nextLevel": 98, "id": 76002}, {"cost": [[11, 137101, 200]], "level": 98, "attributes": [[1, 567600], [2, 5676000], [3, 283800], [4, 283800]], "nextLevel": 99, "id": 76002}, {"cost": [[11, 137101, 200]], "level": 99, "attributes": [[1, 576300], [2, 5763000], [3, 288150], [4, 288150]], "nextLevel": 100, "id": 76002}, {"cost": [[11, 137101, 200]], "level": 100, "attributes": [[1, 585000], [2, 5850000], [3, 292500], [4, 292500]], "nextLevel": 101, "id": 76002}, {"cost": [[11, 137101, 210]], "level": 101, "attributes": [[1, 594000], [2, 5940000], [3, 297000], [4, 297000]], "nextLevel": 102, "id": 76002}, {"cost": [[11, 137101, 210]], "level": 102, "attributes": [[1, 603000], [2, 6030000], [3, 301500], [4, 301500]], "nextLevel": 103, "id": 76002}, {"cost": [[11, 137101, 210]], "level": 103, "attributes": [[1, 612000], [2, 6120000], [3, 306000], [4, 306000]], "nextLevel": 104, "id": 76002}, {"cost": [[11, 137101, 210]], "level": 104, "attributes": [[1, 621000], [2, 6210000], [3, 310500], [4, 310500]], "nextLevel": 105, "id": 76002}, {"cost": [[11, 137101, 210]], "level": 105, "attributes": [[1, 630000], [2, 6300000], [3, 315000], [4, 315000]], "nextLevel": 106, "id": 76002}, {"cost": [[11, 137101, 220]], "level": 106, "attributes": [[1, 639300], [2, 6393000], [3, 319650], [4, 319650]], "nextLevel": 107, "id": 76002}, {"cost": [[11, 137101, 220]], "level": 107, "attributes": [[1, 648600], [2, 6486000], [3, 324300], [4, 324300]], "nextLevel": 108, "id": 76002}, {"cost": [[11, 137101, 220]], "level": 108, "attributes": [[1, 657900], [2, 6579000], [3, 328950], [4, 328950]], "nextLevel": 109, "id": 76002}, {"cost": [[11, 137101, 220]], "level": 109, "attributes": [[1, 667200], [2, 6672000], [3, 333600], [4, 333600]], "nextLevel": 110, "id": 76002}, {"cost": [[11, 137101, 220]], "level": 110, "attributes": [[1, 676500], [2, 6765000], [3, 338250], [4, 338250]], "nextLevel": 111, "id": 76002}, {"cost": [[11, 137101, 230]], "level": 111, "attributes": [[1, 686100], [2, 6861000], [3, 343050], [4, 343050]], "nextLevel": 112, "id": 76002}, {"cost": [[11, 137101, 230]], "level": 112, "attributes": [[1, 695700], [2, 6957000], [3, 347850], [4, 347850]], "nextLevel": 113, "id": 76002}, {"cost": [[11, 137101, 230]], "level": 113, "attributes": [[1, 705300], [2, 7053000], [3, 352650], [4, 352650]], "nextLevel": 114, "id": 76002}, {"cost": [[11, 137101, 230]], "level": 114, "attributes": [[1, 714900], [2, 7149000], [3, 357450], [4, 357450]], "nextLevel": 115, "id": 76002}, {"cost": [[11, 137101, 230]], "level": 115, "attributes": [[1, 724500], [2, 7245000], [3, 362250], [4, 362250]], "nextLevel": 116, "id": 76002}, {"cost": [[11, 137101, 240]], "level": 116, "attributes": [[1, 734400], [2, 7344000], [3, 367200], [4, 367200]], "nextLevel": 117, "id": 76002}, {"cost": [[11, 137101, 240]], "level": 117, "attributes": [[1, 744300], [2, 7443000], [3, 372150], [4, 372150]], "nextLevel": 118, "id": 76002}, {"cost": [[11, 137101, 240]], "level": 118, "attributes": [[1, 754200], [2, 7542000], [3, 377100], [4, 377100]], "nextLevel": 119, "id": 76002}, {"cost": [[11, 137101, 240]], "level": 119, "attributes": [[1, 764100], [2, 7641000], [3, 382050], [4, 382050]], "nextLevel": 120, "id": 76002}, {"cost": [[11, 137101, 240]], "level": 120, "attributes": [[1, 774000], [2, 7740000], [3, 387000], [4, 387000]], "nextLevel": 121, "id": 76002}, {"cost": [[11, 137101, 250]], "level": 121, "attributes": [[1, 784200], [2, 7842000], [3, 392100], [4, 392100]], "nextLevel": 122, "id": 76002}, {"cost": [[11, 137101, 250]], "level": 122, "attributes": [[1, 794400], [2, 7944000], [3, 397200], [4, 397200]], "nextLevel": 123, "id": 76002}, {"cost": [[11, 137101, 250]], "level": 123, "attributes": [[1, 804600], [2, 8046000], [3, 402300], [4, 402300]], "nextLevel": 124, "id": 76002}, {"cost": [[11, 137101, 250]], "level": 124, "attributes": [[1, 814800], [2, 8148000], [3, 407400], [4, 407400]], "nextLevel": 125, "id": 76002}, {"cost": [[11, 137101, 250]], "level": 125, "attributes": [[1, 825000], [2, 8250000], [3, 412500], [4, 412500]], "nextLevel": 126, "id": 76002}, {"cost": [[11, 137101, 260]], "level": 126, "attributes": [[1, 835500], [2, 8355000], [3, 417750], [4, 417750]], "nextLevel": 127, "id": 76002}, {"cost": [[11, 137101, 260]], "level": 127, "attributes": [[1, 846000], [2, 8460000], [3, 423000], [4, 423000]], "nextLevel": 128, "id": 76002}, {"cost": [[11, 137101, 260]], "level": 128, "attributes": [[1, 856500], [2, 8565000], [3, 428250], [4, 428250]], "nextLevel": 129, "id": 76002}, {"cost": [[11, 137101, 260]], "level": 129, "attributes": [[1, 867000], [2, 8670000], [3, 433500], [4, 433500]], "nextLevel": 130, "id": 76002}, {"cost": [[11, 137101, 260]], "level": 130, "attributes": [[1, 877500], [2, 8775000], [3, 438750], [4, 438750]], "nextLevel": 131, "id": 76002}, {"cost": [[11, 137101, 270]], "level": 131, "attributes": [[1, 888300], [2, 8883000], [3, 444150], [4, 444150]], "nextLevel": 132, "id": 76002}, {"cost": [[11, 137101, 270]], "level": 132, "attributes": [[1, 899100], [2, 8991000], [3, 449550], [4, 449550]], "nextLevel": 133, "id": 76002}, {"cost": [[11, 137101, 270]], "level": 133, "attributes": [[1, 909900], [2, 9099000], [3, 454950], [4, 454950]], "nextLevel": 134, "id": 76002}, {"cost": [[11, 137101, 270]], "level": 134, "attributes": [[1, 920700], [2, 9207000], [3, 460350], [4, 460350]], "nextLevel": 135, "id": 76002}, {"cost": [[11, 137101, 270]], "level": 135, "attributes": [[1, 931500], [2, 9315000], [3, 465750], [4, 465750]], "nextLevel": 136, "id": 76002}, {"cost": [[11, 137101, 280]], "level": 136, "attributes": [[1, 942600], [2, 9426000], [3, 471300], [4, 471300]], "nextLevel": 137, "id": 76002}, {"cost": [[11, 137101, 280]], "level": 137, "attributes": [[1, 953700], [2, 9537000], [3, 476850], [4, 476850]], "nextLevel": 138, "id": 76002}, {"cost": [[11, 137101, 280]], "level": 138, "attributes": [[1, 964800], [2, 9648000], [3, 482400], [4, 482400]], "nextLevel": 139, "id": 76002}, {"cost": [[11, 137101, 280]], "level": 139, "attributes": [[1, 975900], [2, 9759000], [3, 487950], [4, 487950]], "nextLevel": 140, "id": 76002}, {"cost": [[11, 137101, 280]], "level": 140, "attributes": [[1, 987000], [2, 9870000], [3, 493500], [4, 493500]], "nextLevel": 141, "id": 76002}, {"cost": [[11, 137101, 290]], "level": 141, "attributes": [[1, 998400], [2, 9984000], [3, 499200], [4, 499200]], "nextLevel": 142, "id": 76002}, {"cost": [[11, 137101, 290]], "level": 142, "attributes": [[1, 1009800], [2, 10098000], [3, 504900], [4, 504900]], "nextLevel": 143, "id": 76002}, {"cost": [[11, 137101, 290]], "level": 143, "attributes": [[1, 1021200], [2, 10212000], [3, 510600], [4, 510600]], "nextLevel": 144, "id": 76002}, {"cost": [[11, 137101, 290]], "level": 144, "attributes": [[1, 1032600], [2, 10326000], [3, 516300], [4, 516300]], "nextLevel": 145, "id": 76002}, {"cost": [[11, 137101, 290]], "level": 145, "attributes": [[1, 1044000], [2, 10440000], [3, 522000], [4, 522000]], "nextLevel": 146, "id": 76002}, {"cost": [[11, 137101, 300]], "level": 146, "attributes": [[1, 1055700], [2, 10557000], [3, 527850], [4, 527850]], "nextLevel": 147, "id": 76002}, {"cost": [[11, 137101, 300]], "level": 147, "attributes": [[1, 1067400], [2, 10674000], [3, 533700], [4, 533700]], "nextLevel": 148, "id": 76002}, {"cost": [[11, 137101, 300]], "level": 148, "attributes": [[1, 1079100], [2, 10791000], [3, 539550], [4, 539550]], "nextLevel": 149, "id": 76002}, {"cost": [[11, 137101, 300]], "level": 149, "attributes": [[1, 1090800], [2, 10908000], [3, 545400], [4, 545400]], "nextLevel": 150, "id": 76002}, {"cost": [[11, 137101, 300]], "level": 150, "attributes": [[1, 1102500], [2, 11025000], [3, 551250], [4, 551250]], "nextLevel": 151, "id": 76002}, {"cost": [[11, 137101, 310]], "level": 151, "attributes": [[1, 1114500], [2, 11145000], [3, 557250], [4, 557250]], "nextLevel": 152, "id": 76002}, {"cost": [[11, 137101, 310]], "level": 152, "attributes": [[1, 1126500], [2, 11265000], [3, 563250], [4, 563250]], "nextLevel": 153, "id": 76002}, {"cost": [[11, 137101, 310]], "level": 153, "attributes": [[1, 1138500], [2, 11385000], [3, 569250], [4, 569250]], "nextLevel": 154, "id": 76002}, {"cost": [[11, 137101, 310]], "level": 154, "attributes": [[1, 1150500], [2, 11505000], [3, 575250], [4, 575250]], "nextLevel": 155, "id": 76002}, {"cost": [[11, 137101, 310]], "level": 155, "attributes": [[1, 1162500], [2, 11625000], [3, 581250], [4, 581250]], "nextLevel": 156, "id": 76002}, {"cost": [[11, 137101, 320]], "level": 156, "attributes": [[1, 1174800], [2, 11748000], [3, 587400], [4, 587400]], "nextLevel": 157, "id": 76002}, {"cost": [[11, 137101, 320]], "level": 157, "attributes": [[1, 1187100], [2, 11871000], [3, 593550], [4, 593550]], "nextLevel": 158, "id": 76002}, {"cost": [[11, 137101, 320]], "level": 158, "attributes": [[1, 1199400], [2, 11994000], [3, 599700], [4, 599700]], "nextLevel": 159, "id": 76002}, {"cost": [[11, 137101, 320]], "level": 159, "attributes": [[1, 1211700], [2, 12117000], [3, 605850], [4, 605850]], "nextLevel": 160, "id": 76002}, {"cost": [[11, 137101, 320]], "level": 160, "attributes": [[1, 1224000], [2, 12240000], [3, 612000], [4, 612000]], "nextLevel": 161, "id": 76002}, {"cost": [[11, 137101, 330]], "level": 161, "attributes": [[1, 1236600], [2, 12366000], [3, 618300], [4, 618300]], "nextLevel": 162, "id": 76002}, {"cost": [[11, 137101, 330]], "level": 162, "attributes": [[1, 1249200], [2, 12492000], [3, 624600], [4, 624600]], "nextLevel": 163, "id": 76002}, {"cost": [[11, 137101, 330]], "level": 163, "attributes": [[1, 1261800], [2, 12618000], [3, 630900], [4, 630900]], "nextLevel": 164, "id": 76002}, {"cost": [[11, 137101, 330]], "level": 164, "attributes": [[1, 1274400], [2, 12744000], [3, 637200], [4, 637200]], "nextLevel": 165, "id": 76002}, {"cost": [[11, 137101, 330]], "level": 165, "attributes": [[1, 1287000], [2, 12870000], [3, 643500], [4, 643500]], "nextLevel": 166, "id": 76002}, {"cost": [[11, 137101, 340]], "level": 166, "attributes": [[1, 1299900], [2, 12999000], [3, 649950], [4, 649950]], "nextLevel": 167, "id": 76002}, {"cost": [[11, 137101, 340]], "level": 167, "attributes": [[1, 1312800], [2, 13128000], [3, 656400], [4, 656400]], "nextLevel": 168, "id": 76002}, {"cost": [[11, 137101, 340]], "level": 168, "attributes": [[1, 1325700], [2, 13257000], [3, 662850], [4, 662850]], "nextLevel": 169, "id": 76002}, {"cost": [[11, 137101, 340]], "level": 169, "attributes": [[1, 1338600], [2, 13386000], [3, 669300], [4, 669300]], "nextLevel": 170, "id": 76002}, {"cost": [[11, 137101, 340]], "level": 170, "attributes": [[1, 1351500], [2, 13515000], [3, 675750], [4, 675750]], "nextLevel": 171, "id": 76002}, {"cost": [[11, 137101, 350]], "level": 171, "attributes": [[1, 1364700], [2, 13647000], [3, 682350], [4, 682350]], "nextLevel": 172, "id": 76002}, {"cost": [[11, 137101, 350]], "level": 172, "attributes": [[1, 1377900], [2, 13779000], [3, 688950], [4, 688950]], "nextLevel": 173, "id": 76002}, {"cost": [[11, 137101, 350]], "level": 173, "attributes": [[1, 1391100], [2, 13911000], [3, 695550], [4, 695550]], "nextLevel": 174, "id": 76002}, {"cost": [[11, 137101, 350]], "level": 174, "attributes": [[1, 1404300], [2, 14043000], [3, 702150], [4, 702150]], "nextLevel": 175, "id": 76002}, {"cost": [[11, 137101, 350]], "level": 175, "attributes": [[1, 1417500], [2, 14175000], [3, 708750], [4, 708750]], "nextLevel": 176, "id": 76002}, {"cost": [[11, 137101, 360]], "level": 176, "attributes": [[1, 1431000], [2, 14310000], [3, 715500], [4, 715500]], "nextLevel": 177, "id": 76002}, {"cost": [[11, 137101, 360]], "level": 177, "attributes": [[1, 1444500], [2, 14445000], [3, 722250], [4, 722250]], "nextLevel": 178, "id": 76002}, {"cost": [[11, 137101, 360]], "level": 178, "attributes": [[1, 1458000], [2, 14580000], [3, 729000], [4, 729000]], "nextLevel": 179, "id": 76002}, {"cost": [[11, 137101, 360]], "level": 179, "attributes": [[1, 1471500], [2, 14715000], [3, 735750], [4, 735750]], "nextLevel": 180, "id": 76002}, {"cost": [[11, 137101, 360]], "level": 180, "attributes": [[1, 1485000], [2, 14850000], [3, 742500], [4, 742500]], "nextLevel": 181, "id": 76002}, {"cost": [[11, 137101, 370]], "level": 181, "attributes": [[1, 1498800], [2, 14988000], [3, 749400], [4, 749400]], "nextLevel": 182, "id": 76002}, {"cost": [[11, 137101, 370]], "level": 182, "attributes": [[1, 1512600], [2, 15126000], [3, 756300], [4, 756300]], "nextLevel": 183, "id": 76002}, {"cost": [[11, 137101, 370]], "level": 183, "attributes": [[1, 1526400], [2, 15264000], [3, 763200], [4, 763200]], "nextLevel": 184, "id": 76002}, {"cost": [[11, 137101, 370]], "level": 184, "attributes": [[1, 1540200], [2, 15402000], [3, 770100], [4, 770100]], "nextLevel": 185, "id": 76002}, {"cost": [[11, 137101, 370]], "level": 185, "attributes": [[1, 1554000], [2, 15540000], [3, 777000], [4, 777000]], "nextLevel": 186, "id": 76002}, {"cost": [[11, 137101, 380]], "level": 186, "attributes": [[1, 1568100], [2, 15681000], [3, 784050], [4, 784050]], "nextLevel": 187, "id": 76002}, {"cost": [[11, 137101, 380]], "level": 187, "attributes": [[1, 1582200], [2, 15822000], [3, 791100], [4, 791100]], "nextLevel": 188, "id": 76002}, {"cost": [[11, 137101, 380]], "level": 188, "attributes": [[1, 1596300], [2, 15963000], [3, 798150], [4, 798150]], "nextLevel": 189, "id": 76002}, {"cost": [[11, 137101, 380]], "level": 189, "attributes": [[1, 1610400], [2, 16104000], [3, 805200], [4, 805200]], "nextLevel": 190, "id": 76002}, {"cost": [[11, 137101, 380]], "level": 190, "attributes": [[1, 1624500], [2, 16245000], [3, 812250], [4, 812250]], "nextLevel": 191, "id": 76002}, {"cost": [[11, 137101, 390]], "level": 191, "attributes": [[1, 1638900], [2, 16389000], [3, 819450], [4, 819450]], "nextLevel": 192, "id": 76002}, {"cost": [[11, 137101, 390]], "level": 192, "attributes": [[1, 1653300], [2, 16533000], [3, 826650], [4, 826650]], "nextLevel": 193, "id": 76002}, {"cost": [[11, 137101, 390]], "level": 193, "attributes": [[1, 1667700], [2, 16677000], [3, 833850], [4, 833850]], "nextLevel": 194, "id": 76002}, {"cost": [[11, 137101, 390]], "level": 194, "attributes": [[1, 1682100], [2, 16821000], [3, 841050], [4, 841050]], "nextLevel": 195, "id": 76002}, {"cost": [[11, 137101, 390]], "level": 195, "attributes": [[1, 1696500], [2, 16965000], [3, 848250], [4, 848250]], "nextLevel": 196, "id": 76002}, {"cost": [[11, 137101, 400]], "level": 196, "attributes": [[1, 1711200], [2, 17112000], [3, 855600], [4, 855600]], "nextLevel": 197, "id": 76002}, {"cost": [[11, 137101, 400]], "level": 197, "attributes": [[1, 1725900], [2, 17259000], [3, 862950], [4, 862950]], "nextLevel": 198, "id": 76002}, {"cost": [[11, 137101, 400]], "level": 198, "attributes": [[1, 1740600], [2, 17406000], [3, 870300], [4, 870300]], "nextLevel": 199, "id": 76002}, {"cost": [[11, 137101, 400]], "level": 199, "attributes": [[1, 1755300], [2, 17553000], [3, 877650], [4, 877650]], "nextLevel": 200, "id": 76002}, {"cost": [[11, 137101, 400]], "level": 200, "attributes": [[1, 1770000], [2, 17700000], [3, 885000], [4, 885000]], "nextLevel": 201, "id": 76002}, {"cost": [[11, 137101, 410]], "level": 201, "attributes": [[1, 1785000], [2, 17850000], [3, 892500], [4, 892500]], "nextLevel": 202, "id": 76002}, {"cost": [[11, 137101, 410]], "level": 202, "attributes": [[1, 1800000], [2, 18000000], [3, 900000], [4, 900000]], "nextLevel": 203, "id": 76002}, {"cost": [[11, 137101, 410]], "level": 203, "attributes": [[1, 1815000], [2, 18150000], [3, 907500], [4, 907500]], "nextLevel": 204, "id": 76002}, {"cost": [[11, 137101, 410]], "level": 204, "attributes": [[1, 1830000], [2, 18300000], [3, 915000], [4, 915000]], "nextLevel": 205, "id": 76002}, {"cost": [[11, 137101, 410]], "level": 205, "attributes": [[1, 1845000], [2, 18450000], [3, 922500], [4, 922500]], "nextLevel": 206, "id": 76002}, {"cost": [[11, 137101, 420]], "level": 206, "attributes": [[1, 1860300], [2, 18603000], [3, 930150], [4, 930150]], "nextLevel": 207, "id": 76002}, {"cost": [[11, 137101, 420]], "level": 207, "attributes": [[1, 1875600], [2, 18756000], [3, 937800], [4, 937800]], "nextLevel": 208, "id": 76002}, {"cost": [[11, 137101, 420]], "level": 208, "attributes": [[1, 1890900], [2, 18909000], [3, 945450], [4, 945450]], "nextLevel": 209, "id": 76002}, {"cost": [[11, 137101, 420]], "level": 209, "attributes": [[1, 1906200], [2, 19062000], [3, 953100], [4, 953100]], "nextLevel": 210, "id": 76002}, {"cost": [[11, 137101, 420]], "level": 210, "attributes": [[1, 1921500], [2, 19215000], [3, 960750], [4, 960750]], "nextLevel": 211, "id": 76002}, {"cost": [[11, 137101, 430]], "level": 211, "attributes": [[1, 1937100], [2, 19371000], [3, 968550], [4, 968550]], "nextLevel": 212, "id": 76002}, {"cost": [[11, 137101, 430]], "level": 212, "attributes": [[1, 1952700], [2, 19527000], [3, 976350], [4, 976350]], "nextLevel": 213, "id": 76002}, {"cost": [[11, 137101, 430]], "level": 213, "attributes": [[1, 1968300], [2, 19683000], [3, 984150], [4, 984150]], "nextLevel": 214, "id": 76002}, {"cost": [[11, 137101, 430]], "level": 214, "attributes": [[1, 1983900], [2, 19839000], [3, 991950], [4, 991950]], "nextLevel": 215, "id": 76002}, {"cost": [[11, 137101, 430]], "level": 215, "attributes": [[1, 1999500], [2, 19995000], [3, 999750], [4, 999750]], "nextLevel": 216, "id": 76002}, {"cost": [[11, 137101, 440]], "level": 216, "attributes": [[1, 2015400], [2, 20154000], [3, 1007700], [4, 1007700]], "nextLevel": 217, "id": 76002}, {"cost": [[11, 137101, 440]], "level": 217, "attributes": [[1, 2031300], [2, 20313000], [3, 1015650], [4, 1015650]], "nextLevel": 218, "id": 76002}, {"cost": [[11, 137101, 440]], "level": 218, "attributes": [[1, 2047200], [2, 20472000], [3, 1023600], [4, 1023600]], "nextLevel": 219, "id": 76002}, {"cost": [[11, 137101, 440]], "level": 219, "attributes": [[1, 2063100], [2, 20631000], [3, 1031550], [4, 1031550]], "nextLevel": 220, "id": 76002}, {"cost": [[11, 137101, 440]], "level": 220, "attributes": [[1, 2079000], [2, 20790000], [3, 1039500], [4, 1039500]], "nextLevel": 221, "id": 76002}, {"cost": [[11, 137101, 450]], "level": 221, "attributes": [[1, 2095200], [2, 20952000], [3, 1047600], [4, 1047600]], "nextLevel": 222, "id": 76002}, {"cost": [[11, 137101, 450]], "level": 222, "attributes": [[1, 2111400], [2, 21114000], [3, 1055700], [4, 1055700]], "nextLevel": 223, "id": 76002}, {"cost": [[11, 137101, 450]], "level": 223, "attributes": [[1, 2127600], [2, 21276000], [3, 1063800], [4, 1063800]], "nextLevel": 224, "id": 76002}, {"cost": [[11, 137101, 450]], "level": 224, "attributes": [[1, 2143800], [2, 21438000], [3, 1071900], [4, 1071900]], "nextLevel": 225, "id": 76002}, {"cost": [[11, 137101, 450]], "level": 225, "attributes": [[1, 2160000], [2, 21600000], [3, 1080000], [4, 1080000]], "nextLevel": 226, "id": 76002}, {"cost": [[11, 137101, 460]], "level": 226, "attributes": [[1, 2176500], [2, 21765000], [3, 1088250], [4, 1088250]], "nextLevel": 227, "id": 76002}, {"cost": [[11, 137101, 460]], "level": 227, "attributes": [[1, 2193000], [2, 21930000], [3, 1096500], [4, 1096500]], "nextLevel": 228, "id": 76002}, {"cost": [[11, 137101, 460]], "level": 228, "attributes": [[1, 2209500], [2, 22095000], [3, 1104750], [4, 1104750]], "nextLevel": 229, "id": 76002}, {"cost": [[11, 137101, 460]], "level": 229, "attributes": [[1, 2226000], [2, 22260000], [3, 1113000], [4, 1113000]], "nextLevel": 230, "id": 76002}, {"cost": [[11, 137101, 460]], "level": 230, "attributes": [[1, 2242500], [2, 22425000], [3, 1121250], [4, 1121250]], "nextLevel": 231, "id": 76002}, {"cost": [[11, 137101, 470]], "level": 231, "attributes": [[1, 2259300], [2, 22593000], [3, 1129650], [4, 1129650]], "nextLevel": 232, "id": 76002}, {"cost": [[11, 137101, 470]], "level": 232, "attributes": [[1, 2276100], [2, 22761000], [3, 1138050], [4, 1138050]], "nextLevel": 233, "id": 76002}, {"cost": [[11, 137101, 470]], "level": 233, "attributes": [[1, 2292900], [2, 22929000], [3, 1146450], [4, 1146450]], "nextLevel": 234, "id": 76002}, {"cost": [[11, 137101, 470]], "level": 234, "attributes": [[1, 2309700], [2, 23097000], [3, 1154850], [4, 1154850]], "nextLevel": 235, "id": 76002}, {"cost": [[11, 137101, 470]], "level": 235, "attributes": [[1, 2326500], [2, 23265000], [3, 1163250], [4, 1163250]], "nextLevel": 236, "id": 76002}, {"cost": [[11, 137101, 480]], "level": 236, "attributes": [[1, 2343600], [2, 23436000], [3, 1171800], [4, 1171800]], "nextLevel": 237, "id": 76002}, {"cost": [[11, 137101, 480]], "level": 237, "attributes": [[1, 2360700], [2, 23607000], [3, 1180350], [4, 1180350]], "nextLevel": 238, "id": 76002}, {"cost": [[11, 137101, 480]], "level": 238, "attributes": [[1, 2377800], [2, 23778000], [3, 1188900], [4, 1188900]], "nextLevel": 239, "id": 76002}, {"cost": [[11, 137101, 480]], "level": 239, "attributes": [[1, 2394900], [2, 23949000], [3, 1197450], [4, 1197450]], "nextLevel": 240, "id": 76002}, {"cost": [[11, 137101, 480]], "level": 240, "attributes": [[1, 2412000], [2, 24120000], [3, 1206000], [4, 1206000]], "nextLevel": 241, "id": 76002}, {"cost": [[11, 137101, 490]], "level": 241, "attributes": [[1, 2429400], [2, 24294000], [3, 1214700], [4, 1214700]], "nextLevel": 242, "id": 76002}, {"cost": [[11, 137101, 490]], "level": 242, "attributes": [[1, 2446800], [2, 24468000], [3, 1223400], [4, 1223400]], "nextLevel": 243, "id": 76002}, {"cost": [[11, 137101, 490]], "level": 243, "attributes": [[1, 2464200], [2, 24642000], [3, 1232100], [4, 1232100]], "nextLevel": 244, "id": 76002}, {"cost": [[11, 137101, 490]], "level": 244, "attributes": [[1, 2481600], [2, 24816000], [3, 1240800], [4, 1240800]], "nextLevel": 245, "id": 76002}, {"cost": [[11, 137101, 490]], "level": 245, "attributes": [[1, 2499000], [2, 24990000], [3, 1249500], [4, 1249500]], "nextLevel": 246, "id": 76002}, {"cost": [[11, 137101, 500]], "level": 246, "attributes": [[1, 2516700], [2, 25167000], [3, 1258350], [4, 1258350]], "nextLevel": 247, "id": 76002}, {"cost": [[11, 137101, 500]], "level": 247, "attributes": [[1, 2534400], [2, 25344000], [3, 1267200], [4, 1267200]], "nextLevel": 248, "id": 76002}, {"cost": [[11, 137101, 500]], "level": 248, "attributes": [[1, 2552100], [2, 25521000], [3, 1276050], [4, 1276050]], "nextLevel": 249, "id": 76002}, {"cost": [[11, 137101, 500]], "level": 249, "attributes": [[1, 2569800], [2, 25698000], [3, 1284900], [4, 1284900]], "nextLevel": 250, "id": 76002}, {"cost": [[11, 137101, 500]], "level": 250, "attributes": [[1, 2587500], [2, 25875000], [3, 1293750], [4, 1293750]], "nextLevel": 251, "id": 76002}, {"cost": [[11, 137101, 510]], "level": 251, "attributes": [[1, 2605500], [2, 26055000], [3, 1302750], [4, 1302750]], "nextLevel": 252, "id": 76002}, {"cost": [[11, 137101, 510]], "level": 252, "attributes": [[1, 2623500], [2, 26235000], [3, 1311750], [4, 1311750]], "nextLevel": 253, "id": 76002}, {"cost": [[11, 137101, 510]], "level": 253, "attributes": [[1, 2641500], [2, 26415000], [3, 1320750], [4, 1320750]], "nextLevel": 254, "id": 76002}, {"cost": [[11, 137101, 510]], "level": 254, "attributes": [[1, 2659500], [2, 26595000], [3, 1329750], [4, 1329750]], "nextLevel": 255, "id": 76002}, {"cost": [[11, 137101, 510]], "level": 255, "attributes": [[1, 2677500], [2, 26775000], [3, 1338750], [4, 1338750]], "nextLevel": 256, "id": 76002}, {"cost": [[11, 137101, 520]], "level": 256, "attributes": [[1, 2695800], [2, 26958000], [3, 1347900], [4, 1347900]], "nextLevel": 257, "id": 76002}, {"cost": [[11, 137101, 520]], "level": 257, "attributes": [[1, 2714100], [2, 27141000], [3, 1357050], [4, 1357050]], "nextLevel": 258, "id": 76002}, {"cost": [[11, 137101, 520]], "level": 258, "attributes": [[1, 2732400], [2, 27324000], [3, 1366200], [4, 1366200]], "nextLevel": 259, "id": 76002}, {"cost": [[11, 137101, 520]], "level": 259, "attributes": [[1, 2750700], [2, 27507000], [3, 1375350], [4, 1375350]], "nextLevel": 260, "id": 76002}, {"cost": [[11, 137101, 520]], "level": 260, "attributes": [[1, 2769000], [2, 27690000], [3, 1384500], [4, 1384500]], "nextLevel": 261, "id": 76002}, {"cost": [[11, 137101, 530]], "level": 261, "attributes": [[1, 2787600], [2, 27876000], [3, 1393800], [4, 1393800]], "nextLevel": 262, "id": 76002}, {"cost": [[11, 137101, 530]], "level": 262, "attributes": [[1, 2806200], [2, 28062000], [3, 1403100], [4, 1403100]], "nextLevel": 263, "id": 76002}, {"cost": [[11, 137101, 530]], "level": 263, "attributes": [[1, 2824800], [2, 28248000], [3, 1412400], [4, 1412400]], "nextLevel": 264, "id": 76002}, {"cost": [[11, 137101, 530]], "level": 264, "attributes": [[1, 2843400], [2, 28434000], [3, 1421700], [4, 1421700]], "nextLevel": 265, "id": 76002}, {"cost": [[11, 137101, 530]], "level": 265, "attributes": [[1, 2862000], [2, 28620000], [3, 1431000], [4, 1431000]], "nextLevel": 266, "id": 76002}, {"cost": [[11, 137101, 540]], "level": 266, "attributes": [[1, 2880900], [2, 28809000], [3, 1440450], [4, 1440450]], "nextLevel": 267, "id": 76002}, {"cost": [[11, 137101, 540]], "level": 267, "attributes": [[1, 2899800], [2, 28998000], [3, 1449900], [4, 1449900]], "nextLevel": 268, "id": 76002}, {"cost": [[11, 137101, 540]], "level": 268, "attributes": [[1, 2918700], [2, 29187000], [3, 1459350], [4, 1459350]], "nextLevel": 269, "id": 76002}, {"cost": [[11, 137101, 540]], "level": 269, "attributes": [[1, 2937600], [2, 29376000], [3, 1468800], [4, 1468800]], "nextLevel": 270, "id": 76002}, {"cost": [[11, 137101, 540]], "level": 270, "attributes": [[1, 2956500], [2, 29565000], [3, 1478250], [4, 1478250]], "nextLevel": 271, "id": 76002}, {"cost": [[11, 137101, 550]], "level": 271, "attributes": [[1, 2975700], [2, 29757000], [3, 1487850], [4, 1487850]], "nextLevel": 272, "id": 76002}, {"cost": [[11, 137101, 550]], "level": 272, "attributes": [[1, 2994900], [2, 29949000], [3, 1497450], [4, 1497450]], "nextLevel": 273, "id": 76002}, {"cost": [[11, 137101, 550]], "level": 273, "attributes": [[1, 3014100], [2, 30141000], [3, 1507050], [4, 1507050]], "nextLevel": 274, "id": 76002}, {"cost": [[11, 137101, 550]], "level": 274, "attributes": [[1, 3033300], [2, 30333000], [3, 1516650], [4, 1516650]], "nextLevel": 275, "id": 76002}, {"cost": [[11, 137101, 550]], "level": 275, "attributes": [[1, 3052500], [2, 30525000], [3, 1526250], [4, 1526250]], "nextLevel": 276, "id": 76002}, {"cost": [[11, 137101, 560]], "level": 276, "attributes": [[1, 3072000], [2, 30720000], [3, 1536000], [4, 1536000]], "nextLevel": 277, "id": 76002}, {"cost": [[11, 137101, 560]], "level": 277, "attributes": [[1, 3091500], [2, 30915000], [3, 1545750], [4, 1545750]], "nextLevel": 278, "id": 76002}, {"cost": [[11, 137101, 560]], "level": 278, "attributes": [[1, 3111000], [2, 31110000], [3, 1555500], [4, 1555500]], "nextLevel": 279, "id": 76002}, {"cost": [[11, 137101, 560]], "level": 279, "attributes": [[1, 3130500], [2, 31305000], [3, 1565250], [4, 1565250]], "nextLevel": 280, "id": 76002}, {"cost": [[11, 137101, 560]], "level": 280, "attributes": [[1, 3150000], [2, 31500000], [3, 1575000], [4, 1575000]], "nextLevel": 281, "id": 76002}, {"cost": [[11, 137101, 570]], "level": 281, "attributes": [[1, 3169800], [2, 31698000], [3, 1584900], [4, 1584900]], "nextLevel": 282, "id": 76002}, {"cost": [[11, 137101, 570]], "level": 282, "attributes": [[1, 3189600], [2, 31896000], [3, 1594800], [4, 1594800]], "nextLevel": 283, "id": 76002}, {"cost": [[11, 137101, 570]], "level": 283, "attributes": [[1, 3209400], [2, 32094000], [3, 1604700], [4, 1604700]], "nextLevel": 284, "id": 76002}, {"cost": [[11, 137101, 570]], "level": 284, "attributes": [[1, 3229200], [2, 32292000], [3, 1614600], [4, 1614600]], "nextLevel": 285, "id": 76002}, {"cost": [[11, 137101, 570]], "level": 285, "attributes": [[1, 3249000], [2, 32490000], [3, 1624500], [4, 1624500]], "nextLevel": 286, "id": 76002}, {"cost": [[11, 137101, 580]], "level": 286, "attributes": [[1, 3269100], [2, 32691000], [3, 1634550], [4, 1634550]], "nextLevel": 287, "id": 76002}, {"cost": [[11, 137101, 580]], "level": 287, "attributes": [[1, 3289200], [2, 32892000], [3, 1644600], [4, 1644600]], "nextLevel": 288, "id": 76002}, {"cost": [[11, 137101, 580]], "level": 288, "attributes": [[1, 3309300], [2, 33093000], [3, 1654650], [4, 1654650]], "nextLevel": 289, "id": 76002}, {"cost": [[11, 137101, 580]], "level": 289, "attributes": [[1, 3329400], [2, 33294000], [3, 1664700], [4, 1664700]], "nextLevel": 290, "id": 76002}, {"cost": [[11, 137101, 580]], "level": 290, "attributes": [[1, 3349500], [2, 33495000], [3, 1674750], [4, 1674750]], "nextLevel": 291, "id": 76002}, {"cost": [[11, 137101, 590]], "level": 291, "attributes": [[1, 3369900], [2, 33699000], [3, 1684950], [4, 1684950]], "nextLevel": 292, "id": 76002}, {"cost": [[11, 137101, 590]], "level": 292, "attributes": [[1, 3390300], [2, 33903000], [3, 1695150], [4, 1695150]], "nextLevel": 293, "id": 76002}, {"cost": [[11, 137101, 590]], "level": 293, "attributes": [[1, 3410700], [2, 34107000], [3, 1705350], [4, 1705350]], "nextLevel": 294, "id": 76002}, {"cost": [[11, 137101, 590]], "level": 294, "attributes": [[1, 3431100], [2, 34311000], [3, 1715550], [4, 1715550]], "nextLevel": 295, "id": 76002}, {"cost": [[11, 137101, 590]], "level": 295, "attributes": [[1, 3451500], [2, 34515000], [3, 1725750], [4, 1725750]], "nextLevel": 296, "id": 76002}, {"cost": [[11, 137101, 600]], "level": 296, "attributes": [[1, 3472200], [2, 34722000], [3, 1736100], [4, 1736100]], "nextLevel": 297, "id": 76002}, {"cost": [[11, 137101, 600]], "level": 297, "attributes": [[1, 3492900], [2, 34929000], [3, 1746450], [4, 1746450]], "nextLevel": 298, "id": 76002}, {"cost": [[11, 137101, 600]], "level": 298, "attributes": [[1, 3513600], [2, 35136000], [3, 1756800], [4, 1756800]], "nextLevel": 299, "id": 76002}, {"cost": [[11, 137101, 600]], "level": 299, "attributes": [[1, 3534300], [2, 35343000], [3, 1767150], [4, 1767150]], "nextLevel": 300, "id": 76002}, {"cost": [[11, 137101, 600]], "level": 300, "attributes": [[1, 3555000], [2, 35550000], [3, 1777500], [4, 1777500]], "nextLevel": 301, "id": 76002}, {"cost": [[11, 137101, 610]], "level": 301, "attributes": [[1, 3576000], [2, 35760000], [3, 1788000], [4, 1788000]], "nextLevel": 302, "id": 76002}, {"cost": [[11, 137101, 610]], "level": 302, "attributes": [[1, 3597000], [2, 35970000], [3, 1798500], [4, 1798500]], "nextLevel": 303, "id": 76002}, {"cost": [[11, 137101, 610]], "level": 303, "attributes": [[1, 3618000], [2, 36180000], [3, 1809000], [4, 1809000]], "nextLevel": 304, "id": 76002}, {"cost": [[11, 137101, 610]], "level": 304, "attributes": [[1, 3639000], [2, 36390000], [3, 1819500], [4, 1819500]], "nextLevel": 305, "id": 76002}, {"cost": [[11, 137101, 610]], "level": 305, "attributes": [[1, 3660000], [2, 36600000], [3, 1830000], [4, 1830000]], "nextLevel": 306, "id": 76002}, {"cost": [[11, 137101, 620]], "level": 306, "attributes": [[1, 3681300], [2, 36813000], [3, 1840650], [4, 1840650]], "nextLevel": 307, "id": 76002}, {"cost": [[11, 137101, 620]], "level": 307, "attributes": [[1, 3702600], [2, 37026000], [3, 1851300], [4, 1851300]], "nextLevel": 308, "id": 76002}, {"cost": [[11, 137101, 620]], "level": 308, "attributes": [[1, 3723900], [2, 37239000], [3, 1861950], [4, 1861950]], "nextLevel": 309, "id": 76002}, {"cost": [[11, 137101, 620]], "level": 309, "attributes": [[1, 3745200], [2, 37452000], [3, 1872600], [4, 1872600]], "nextLevel": 310, "id": 76002}, {"cost": [[11, 137101, 620]], "level": 310, "attributes": [[1, 3766500], [2, 37665000], [3, 1883250], [4, 1883250]], "nextLevel": 311, "id": 76002}, {"cost": [[11, 137101, 630]], "level": 311, "attributes": [[1, 3788100], [2, 37881000], [3, 1894050], [4, 1894050]], "nextLevel": 312, "id": 76002}, {"cost": [[11, 137101, 630]], "level": 312, "attributes": [[1, 3809700], [2, 38097000], [3, 1904850], [4, 1904850]], "nextLevel": 313, "id": 76002}, {"cost": [[11, 137101, 630]], "level": 313, "attributes": [[1, 3831300], [2, 38313000], [3, 1915650], [4, 1915650]], "nextLevel": 314, "id": 76002}, {"cost": [[11, 137101, 630]], "level": 314, "attributes": [[1, 3852900], [2, 38529000], [3, 1926450], [4, 1926450]], "nextLevel": 315, "id": 76002}, {"cost": [[11, 137101, 630]], "level": 315, "attributes": [[1, 3874500], [2, 38745000], [3, 1937250], [4, 1937250]], "nextLevel": 316, "id": 76002}, {"cost": [[11, 137101, 640]], "level": 316, "attributes": [[1, 3896400], [2, 38964000], [3, 1948200], [4, 1948200]], "nextLevel": 317, "id": 76002}, {"cost": [[11, 137101, 640]], "level": 317, "attributes": [[1, 3918300], [2, 39183000], [3, 1959150], [4, 1959150]], "nextLevel": 318, "id": 76002}, {"cost": [[11, 137101, 640]], "level": 318, "attributes": [[1, 3940200], [2, 39402000], [3, 1970100], [4, 1970100]], "nextLevel": 319, "id": 76002}, {"cost": [[11, 137101, 640]], "level": 319, "attributes": [[1, 3962100], [2, 39621000], [3, 1981050], [4, 1981050]], "nextLevel": 320, "id": 76002}, {"cost": [[11, 137101, 640]], "level": 320, "attributes": [[1, 3984000], [2, 39840000], [3, 1992000], [4, 1992000]], "nextLevel": 321, "id": 76002}, {"cost": [[11, 137101, 650]], "level": 321, "attributes": [[1, 4006200], [2, 40062000], [3, 2003100], [4, 2003100]], "nextLevel": 322, "id": 76002}, {"cost": [[11, 137101, 650]], "level": 322, "attributes": [[1, 4028400], [2, 40284000], [3, 2014200], [4, 2014200]], "nextLevel": 323, "id": 76002}, {"cost": [[11, 137101, 650]], "level": 323, "attributes": [[1, 4050600], [2, 40506000], [3, 2025300], [4, 2025300]], "nextLevel": 324, "id": 76002}, {"cost": [[11, 137101, 650]], "level": 324, "attributes": [[1, 4072800], [2, 40728000], [3, 2036400], [4, 2036400]], "nextLevel": 325, "id": 76002}, {"cost": [[11, 137101, 650]], "level": 325, "attributes": [[1, 4095000], [2, 40950000], [3, 2047500], [4, 2047500]], "nextLevel": 326, "id": 76002}, {"cost": [[11, 137101, 660]], "level": 326, "attributes": [[1, 4117500], [2, 41175000], [3, 2058750], [4, 2058750]], "nextLevel": 327, "id": 76002}, {"cost": [[11, 137101, 660]], "level": 327, "attributes": [[1, 4140000], [2, 41400000], [3, 2070000], [4, 2070000]], "nextLevel": 328, "id": 76002}, {"cost": [[11, 137101, 660]], "level": 328, "attributes": [[1, 4162500], [2, 41625000], [3, 2081250], [4, 2081250]], "nextLevel": 329, "id": 76002}, {"cost": [[11, 137101, 660]], "level": 329, "attributes": [[1, 4185000], [2, 41850000], [3, 2092500], [4, 2092500]], "nextLevel": 330, "id": 76002}, {"cost": [[11, 137101, 660]], "level": 330, "attributes": [[1, 4207500], [2, 42075000], [3, 2103750], [4, 2103750]], "nextLevel": 331, "id": 76002}, {"cost": [[11, 137101, 670]], "level": 331, "attributes": [[1, 4230300], [2, 42303000], [3, 2115150], [4, 2115150]], "nextLevel": 332, "id": 76002}, {"cost": [[11, 137101, 670]], "level": 332, "attributes": [[1, 4253100], [2, 42531000], [3, 2126550], [4, 2126550]], "nextLevel": 333, "id": 76002}, {"cost": [[11, 137101, 670]], "level": 333, "attributes": [[1, 4275900], [2, 42759000], [3, 2137950], [4, 2137950]], "nextLevel": 334, "id": 76002}, {"cost": [[11, 137101, 670]], "level": 334, "attributes": [[1, 4298700], [2, 42987000], [3, 2149350], [4, 2149350]], "nextLevel": 335, "id": 76002}, {"cost": [[11, 137101, 670]], "level": 335, "attributes": [[1, 4321500], [2, 43215000], [3, 2160750], [4, 2160750]], "nextLevel": 336, "id": 76002}, {"cost": [[11, 137101, 680]], "level": 336, "attributes": [[1, 4344600], [2, 43446000], [3, 2172300], [4, 2172300]], "nextLevel": 337, "id": 76002}, {"cost": [[11, 137101, 680]], "level": 337, "attributes": [[1, 4367700], [2, 43677000], [3, 2183850], [4, 2183850]], "nextLevel": 338, "id": 76002}, {"cost": [[11, 137101, 680]], "level": 338, "attributes": [[1, 4390800], [2, 43908000], [3, 2195400], [4, 2195400]], "nextLevel": 339, "id": 76002}, {"cost": [[11, 137101, 680]], "level": 339, "attributes": [[1, 4413900], [2, 44139000], [3, 2206950], [4, 2206950]], "nextLevel": 340, "id": 76002}, {"cost": [[11, 137101, 680]], "level": 340, "attributes": [[1, 4437000], [2, 44370000], [3, 2218500], [4, 2218500]], "nextLevel": 341, "id": 76002}, {"cost": [[11, 137101, 690]], "level": 341, "attributes": [[1, 4460400], [2, 44604000], [3, 2230200], [4, 2230200]], "nextLevel": 342, "id": 76002}, {"cost": [[11, 137101, 690]], "level": 342, "attributes": [[1, 4483800], [2, 44838000], [3, 2241900], [4, 2241900]], "nextLevel": 343, "id": 76002}, {"cost": [[11, 137101, 690]], "level": 343, "attributes": [[1, 4507200], [2, 45072000], [3, 2253600], [4, 2253600]], "nextLevel": 344, "id": 76002}, {"cost": [[11, 137101, 690]], "level": 344, "attributes": [[1, 4530600], [2, 45306000], [3, 2265300], [4, 2265300]], "nextLevel": 345, "id": 76002}, {"cost": [[11, 137101, 690]], "level": 345, "attributes": [[1, 4554000], [2, 45540000], [3, 2277000], [4, 2277000]], "nextLevel": 346, "id": 76002}, {"cost": [[11, 137101, 700]], "level": 346, "attributes": [[1, 4577700], [2, 45777000], [3, 2288850], [4, 2288850]], "nextLevel": 347, "id": 76002}, {"cost": [[11, 137101, 700]], "level": 347, "attributes": [[1, 4601400], [2, 46014000], [3, 2300700], [4, 2300700]], "nextLevel": 348, "id": 76002}, {"cost": [[11, 137101, 700]], "level": 348, "attributes": [[1, 4625100], [2, 46251000], [3, 2312550], [4, 2312550]], "nextLevel": 349, "id": 76002}, {"cost": [[11, 137101, 700]], "level": 349, "attributes": [[1, 4648800], [2, 46488000], [3, 2324400], [4, 2324400]], "nextLevel": 350, "id": 76002}, {"cost": [[11, 137101, 700]], "level": 350, "attributes": [[1, 4672500], [2, 46725000], [3, 2336250], [4, 2336250]], "nextLevel": 351, "id": 76002}, {"cost": [[11, 137101, 710]], "level": 351, "attributes": [[1, 4696500], [2, 46965000], [3, 2348250], [4, 2348250]], "nextLevel": 352, "id": 76002}, {"cost": [[11, 137101, 710]], "level": 352, "attributes": [[1, 4720500], [2, 47205000], [3, 2360250], [4, 2360250]], "nextLevel": 353, "id": 76002}, {"cost": [[11, 137101, 710]], "level": 353, "attributes": [[1, 4744500], [2, 47445000], [3, 2372250], [4, 2372250]], "nextLevel": 354, "id": 76002}, {"cost": [[11, 137101, 710]], "level": 354, "attributes": [[1, 4768500], [2, 47685000], [3, 2384250], [4, 2384250]], "nextLevel": 355, "id": 76002}, {"cost": [[11, 137101, 710]], "level": 355, "attributes": [[1, 4792500], [2, 47925000], [3, 2396250], [4, 2396250]], "nextLevel": 356, "id": 76002}, {"cost": [[11, 137101, 720]], "level": 356, "attributes": [[1, 4816800], [2, 48168000], [3, 2408400], [4, 2408400]], "nextLevel": 357, "id": 76002}, {"cost": [[11, 137101, 720]], "level": 357, "attributes": [[1, 4841100], [2, 48411000], [3, 2420550], [4, 2420550]], "nextLevel": 358, "id": 76002}, {"cost": [[11, 137101, 720]], "level": 358, "attributes": [[1, 4865400], [2, 48654000], [3, 2432700], [4, 2432700]], "nextLevel": 359, "id": 76002}, {"cost": [[11, 137101, 720]], "level": 359, "attributes": [[1, 4889700], [2, 48897000], [3, 2444850], [4, 2444850]], "nextLevel": 360, "id": 76002}, {"cost": [[11, 137101, 720]], "level": 360, "attributes": [[1, 4914000], [2, 49140000], [3, 2457000], [4, 2457000]], "nextLevel": 361, "id": 76002}, {"cost": [[11, 137101, 730]], "level": 361, "attributes": [[1, 4938600], [2, 49386000], [3, 2469300], [4, 2469300]], "nextLevel": 362, "id": 76002}, {"cost": [[11, 137101, 730]], "level": 362, "attributes": [[1, 4963200], [2, 49632000], [3, 2481600], [4, 2481600]], "nextLevel": 363, "id": 76002}, {"cost": [[11, 137101, 730]], "level": 363, "attributes": [[1, 4987800], [2, 49878000], [3, 2493900], [4, 2493900]], "nextLevel": 364, "id": 76002}, {"cost": [[11, 137101, 730]], "level": 364, "attributes": [[1, 5012400], [2, 50124000], [3, 2506200], [4, 2506200]], "nextLevel": 365, "id": 76002}, {"cost": [[11, 137101, 730]], "level": 365, "attributes": [[1, 5037000], [2, 50370000], [3, 2518500], [4, 2518500]], "nextLevel": 366, "id": 76002}, {"cost": [[11, 137101, 740]], "level": 366, "attributes": [[1, 5061900], [2, 50619000], [3, 2530950], [4, 2530950]], "nextLevel": 367, "id": 76002}, {"cost": [[11, 137101, 740]], "level": 367, "attributes": [[1, 5086800], [2, 50868000], [3, 2543400], [4, 2543400]], "nextLevel": 368, "id": 76002}, {"cost": [[11, 137101, 740]], "level": 368, "attributes": [[1, 5111700], [2, 51117000], [3, 2555850], [4, 2555850]], "nextLevel": 369, "id": 76002}, {"cost": [[11, 137101, 740]], "level": 369, "attributes": [[1, 5136600], [2, 51366000], [3, 2568300], [4, 2568300]], "nextLevel": 370, "id": 76002}, {"cost": [[11, 137101, 740]], "level": 370, "attributes": [[1, 5161500], [2, 51615000], [3, 2580750], [4, 2580750]], "nextLevel": 371, "id": 76002}, {"cost": [[11, 137101, 750]], "level": 371, "attributes": [[1, 5186700], [2, 51867000], [3, 2593350], [4, 2593350]], "nextLevel": 372, "id": 76002}, {"cost": [[11, 137101, 750]], "level": 372, "attributes": [[1, 5211900], [2, 52119000], [3, 2605950], [4, 2605950]], "nextLevel": 373, "id": 76002}, {"cost": [[11, 137101, 750]], "level": 373, "attributes": [[1, 5237100], [2, 52371000], [3, 2618550], [4, 2618550]], "nextLevel": 374, "id": 76002}, {"cost": [[11, 137101, 750]], "level": 374, "attributes": [[1, 5262300], [2, 52623000], [3, 2631150], [4, 2631150]], "nextLevel": 375, "id": 76002}, {"cost": [[11, 137101, 750]], "level": 375, "attributes": [[1, 5287500], [2, 52875000], [3, 2643750], [4, 2643750]], "nextLevel": 376, "id": 76002}, {"cost": [[11, 137101, 760]], "level": 376, "attributes": [[1, 5313000], [2, 53130000], [3, 2656500], [4, 2656500]], "nextLevel": 377, "id": 76002}, {"cost": [[11, 137101, 760]], "level": 377, "attributes": [[1, 5338500], [2, 53385000], [3, 2669250], [4, 2669250]], "nextLevel": 378, "id": 76002}, {"cost": [[11, 137101, 760]], "level": 378, "attributes": [[1, 5364000], [2, 53640000], [3, 2682000], [4, 2682000]], "nextLevel": 379, "id": 76002}, {"cost": [[11, 137101, 760]], "level": 379, "attributes": [[1, 5389500], [2, 53895000], [3, 2694750], [4, 2694750]], "nextLevel": 380, "id": 76002}, {"cost": [[11, 137101, 760]], "level": 380, "attributes": [[1, 5415000], [2, 54150000], [3, 2707500], [4, 2707500]], "nextLevel": 381, "id": 76002}, {"cost": [[11, 137101, 770]], "level": 381, "attributes": [[1, 5440800], [2, 54408000], [3, 2720400], [4, 2720400]], "nextLevel": 382, "id": 76002}, {"cost": [[11, 137101, 770]], "level": 382, "attributes": [[1, 5466600], [2, 54666000], [3, 2733300], [4, 2733300]], "nextLevel": 383, "id": 76002}, {"cost": [[11, 137101, 770]], "level": 383, "attributes": [[1, 5492400], [2, 54924000], [3, 2746200], [4, 2746200]], "nextLevel": 384, "id": 76002}, {"cost": [[11, 137101, 770]], "level": 384, "attributes": [[1, 5518200], [2, 55182000], [3, 2759100], [4, 2759100]], "nextLevel": 385, "id": 76002}, {"cost": [[11, 137101, 770]], "level": 385, "attributes": [[1, 5544000], [2, 55440000], [3, 2772000], [4, 2772000]], "nextLevel": 386, "id": 76002}, {"cost": [[11, 137101, 780]], "level": 386, "attributes": [[1, 5570100], [2, 55701000], [3, 2785050], [4, 2785050]], "nextLevel": 387, "id": 76002}, {"cost": [[11, 137101, 780]], "level": 387, "attributes": [[1, 5596200], [2, 55962000], [3, 2798100], [4, 2798100]], "nextLevel": 388, "id": 76002}, {"cost": [[11, 137101, 780]], "level": 388, "attributes": [[1, 5622300], [2, 56223000], [3, 2811150], [4, 2811150]], "nextLevel": 389, "id": 76002}, {"cost": [[11, 137101, 780]], "level": 389, "attributes": [[1, 5648400], [2, 56484000], [3, 2824200], [4, 2824200]], "nextLevel": 390, "id": 76002}, {"cost": [[11, 137101, 780]], "level": 390, "attributes": [[1, 5674500], [2, 56745000], [3, 2837250], [4, 2837250]], "nextLevel": 391, "id": 76002}, {"cost": [[11, 137101, 790]], "level": 391, "attributes": [[1, 5700900], [2, 57009000], [3, 2850450], [4, 2850450]], "nextLevel": 392, "id": 76002}, {"cost": [[11, 137101, 790]], "level": 392, "attributes": [[1, 5727300], [2, 57273000], [3, 2863650], [4, 2863650]], "nextLevel": 393, "id": 76002}, {"cost": [[11, 137101, 790]], "level": 393, "attributes": [[1, 5753700], [2, 57537000], [3, 2876850], [4, 2876850]], "nextLevel": 394, "id": 76002}, {"cost": [[11, 137101, 790]], "level": 394, "attributes": [[1, 5780100], [2, 57801000], [3, 2890050], [4, 2890050]], "nextLevel": 395, "id": 76002}, {"cost": [[11, 137101, 790]], "level": 395, "attributes": [[1, 5806500], [2, 58065000], [3, 2903250], [4, 2903250]], "nextLevel": 396, "id": 76002}, {"cost": [[11, 137101, 800]], "level": 396, "attributes": [[1, 5833200], [2, 58332000], [3, 2916600], [4, 2916600]], "nextLevel": 397, "id": 76002}, {"cost": [[11, 137101, 800]], "level": 397, "attributes": [[1, 5859900], [2, 58599000], [3, 2929950], [4, 2929950]], "nextLevel": 398, "id": 76002}, {"cost": [[11, 137101, 800]], "level": 398, "attributes": [[1, 5886600], [2, 58866000], [3, 2943300], [4, 2943300]], "nextLevel": 399, "id": 76002}, {"cost": [[11, 137101, 800]], "level": 399, "attributes": [[1, 5913300], [2, 59133000], [3, 2956650], [4, 2956650]], "nextLevel": 400, "id": 76002}, {"cost": [[11, 137101, 800]], "level": 400, "attributes": [[1, 5940000], [2, 59400000], [3, 2970000], [4, 2970000]], "nextLevel": 401, "id": 76002}, {"cost": [[11, 137101, 810]], "level": 401, "attributes": [[1, 5967000], [2, 59670000], [3, 2983500], [4, 2983500]], "nextLevel": 402, "id": 76002}, {"cost": [[11, 137101, 810]], "level": 402, "attributes": [[1, 5994000], [2, 59940000], [3, 2997000], [4, 2997000]], "nextLevel": 403, "id": 76002}, {"cost": [[11, 137101, 810]], "level": 403, "attributes": [[1, 6021000], [2, 60210000], [3, 3010500], [4, 3010500]], "nextLevel": 404, "id": 76002}, {"cost": [[11, 137101, 810]], "level": 404, "attributes": [[1, 6048000], [2, 60480000], [3, 3024000], [4, 3024000]], "nextLevel": 405, "id": 76002}, {"cost": [[11, 137101, 810]], "level": 405, "attributes": [[1, 6075000], [2, 60750000], [3, 3037500], [4, 3037500]], "nextLevel": 406, "id": 76002}, {"cost": [[11, 137101, 820]], "level": 406, "attributes": [[1, 6102300], [2, 61023000], [3, 3051150], [4, 3051150]], "nextLevel": 407, "id": 76002}, {"cost": [[11, 137101, 820]], "level": 407, "attributes": [[1, 6129600], [2, 61296000], [3, 3064800], [4, 3064800]], "nextLevel": 408, "id": 76002}, {"cost": [[11, 137101, 820]], "level": 408, "attributes": [[1, 6156900], [2, 61569000], [3, 3078450], [4, 3078450]], "nextLevel": 409, "id": 76002}, {"cost": [[11, 137101, 820]], "level": 409, "attributes": [[1, 6184200], [2, 61842000], [3, 3092100], [4, 3092100]], "nextLevel": 410, "id": 76002}, {"cost": [[11, 137101, 820]], "level": 410, "attributes": [[1, 6211500], [2, 62115000], [3, 3105750], [4, 3105750]], "nextLevel": 411, "id": 76002}, {"cost": [[11, 137101, 830]], "level": 411, "attributes": [[1, 6239100], [2, 62391000], [3, 3119550], [4, 3119550]], "nextLevel": 412, "id": 76002}, {"cost": [[11, 137101, 830]], "level": 412, "attributes": [[1, 6266700], [2, 62667000], [3, 3133350], [4, 3133350]], "nextLevel": 413, "id": 76002}, {"cost": [[11, 137101, 830]], "level": 413, "attributes": [[1, 6294300], [2, 62943000], [3, 3147150], [4, 3147150]], "nextLevel": 414, "id": 76002}, {"cost": [[11, 137101, 830]], "level": 414, "attributes": [[1, 6321900], [2, 63219000], [3, 3160950], [4, 3160950]], "nextLevel": 415, "id": 76002}, {"cost": [[11, 137101, 830]], "level": 415, "attributes": [[1, 6349500], [2, 63495000], [3, 3174750], [4, 3174750]], "nextLevel": 416, "id": 76002}, {"cost": [[11, 137101, 840]], "level": 416, "attributes": [[1, 6377400], [2, 63774000], [3, 3188700], [4, 3188700]], "nextLevel": 417, "id": 76002}, {"cost": [[11, 137101, 840]], "level": 417, "attributes": [[1, 6405300], [2, 64053000], [3, 3202650], [4, 3202650]], "nextLevel": 418, "id": 76002}, {"cost": [[11, 137101, 840]], "level": 418, "attributes": [[1, 6433200], [2, 64332000], [3, 3216600], [4, 3216600]], "nextLevel": 419, "id": 76002}, {"cost": [[11, 137101, 840]], "level": 419, "attributes": [[1, 6461100], [2, 64611000], [3, 3230550], [4, 3230550]], "nextLevel": 420, "id": 76002}, {"cost": [[11, 137101, 840]], "level": 420, "attributes": [[1, 6489000], [2, 64890000], [3, 3244500], [4, 3244500]], "nextLevel": 421, "id": 76002}, {"cost": [[11, 137101, 850]], "level": 421, "attributes": [[1, 6517200], [2, 65172000], [3, 3258600], [4, 3258600]], "nextLevel": 422, "id": 76002}, {"cost": [[11, 137101, 850]], "level": 422, "attributes": [[1, 6545400], [2, 65454000], [3, 3272700], [4, 3272700]], "nextLevel": 423, "id": 76002}, {"cost": [[11, 137101, 850]], "level": 423, "attributes": [[1, 6573600], [2, 65736000], [3, 3286800], [4, 3286800]], "nextLevel": 424, "id": 76002}, {"cost": [[11, 137101, 850]], "level": 424, "attributes": [[1, 6601800], [2, 66018000], [3, 3300900], [4, 3300900]], "nextLevel": 425, "id": 76002}, {"cost": [[11, 137101, 850]], "level": 425, "attributes": [[1, 6630000], [2, 66300000], [3, 3315000], [4, 3315000]], "nextLevel": 426, "id": 76002}, {"cost": [[11, 137101, 860]], "level": 426, "attributes": [[1, 6658500], [2, 66585000], [3, 3329250], [4, 3329250]], "nextLevel": 427, "id": 76002}, {"cost": [[11, 137101, 860]], "level": 427, "attributes": [[1, 6687000], [2, 66870000], [3, 3343500], [4, 3343500]], "nextLevel": 428, "id": 76002}, {"cost": [[11, 137101, 860]], "level": 428, "attributes": [[1, 6715500], [2, 67155000], [3, 3357750], [4, 3357750]], "nextLevel": 429, "id": 76002}, {"cost": [[11, 137101, 860]], "level": 429, "attributes": [[1, 6744000], [2, 67440000], [3, 3372000], [4, 3372000]], "nextLevel": 430, "id": 76002}, {"cost": [[11, 137101, 860]], "level": 430, "attributes": [[1, 6772500], [2, 67725000], [3, 3386250], [4, 3386250]], "nextLevel": 431, "id": 76002}, {"cost": [[11, 137101, 870]], "level": 431, "attributes": [[1, 6801300], [2, 68013000], [3, 3400650], [4, 3400650]], "nextLevel": 432, "id": 76002}, {"cost": [[11, 137101, 870]], "level": 432, "attributes": [[1, 6830100], [2, 68301000], [3, 3415050], [4, 3415050]], "nextLevel": 433, "id": 76002}, {"cost": [[11, 137101, 870]], "level": 433, "attributes": [[1, 6858900], [2, 68589000], [3, 3429450], [4, 3429450]], "nextLevel": 434, "id": 76002}, {"cost": [[11, 137101, 870]], "level": 434, "attributes": [[1, 6887700], [2, 68877000], [3, 3443850], [4, 3443850]], "nextLevel": 435, "id": 76002}, {"cost": [[11, 137101, 870]], "level": 435, "attributes": [[1, 6916500], [2, 69165000], [3, 3458250], [4, 3458250]], "nextLevel": 436, "id": 76002}, {"cost": [[11, 137101, 880]], "level": 436, "attributes": [[1, 6945600], [2, 69456000], [3, 3472800], [4, 3472800]], "nextLevel": 437, "id": 76002}, {"cost": [[11, 137101, 880]], "level": 437, "attributes": [[1, 6974700], [2, 69747000], [3, 3487350], [4, 3487350]], "nextLevel": 438, "id": 76002}, {"cost": [[11, 137101, 880]], "level": 438, "attributes": [[1, 7003800], [2, 70038000], [3, 3501900], [4, 3501900]], "nextLevel": 439, "id": 76002}, {"cost": [[11, 137101, 880]], "level": 439, "attributes": [[1, 7032900], [2, 70329000], [3, 3516450], [4, 3516450]], "nextLevel": 440, "id": 76002}, {"cost": [[11, 137101, 880]], "level": 440, "attributes": [[1, 7062000], [2, 70620000], [3, 3531000], [4, 3531000]], "nextLevel": 441, "id": 76002}, {"cost": [[11, 137101, 890]], "level": 441, "attributes": [[1, 7091400], [2, 70914000], [3, 3545700], [4, 3545700]], "nextLevel": 442, "id": 76002}, {"cost": [[11, 137101, 890]], "level": 442, "attributes": [[1, 7120800], [2, 71208000], [3, 3560400], [4, 3560400]], "nextLevel": 443, "id": 76002}, {"cost": [[11, 137101, 890]], "level": 443, "attributes": [[1, 7150200], [2, 71502000], [3, 3575100], [4, 3575100]], "nextLevel": 444, "id": 76002}, {"cost": [[11, 137101, 890]], "level": 444, "attributes": [[1, 7179600], [2, 71796000], [3, 3589800], [4, 3589800]], "nextLevel": 445, "id": 76002}, {"cost": [[11, 137101, 890]], "level": 445, "attributes": [[1, 7209000], [2, 72090000], [3, 3604500], [4, 3604500]], "nextLevel": 446, "id": 76002}, {"cost": [[11, 137101, 900]], "level": 446, "attributes": [[1, 7238700], [2, 72387000], [3, 3619350], [4, 3619350]], "nextLevel": 447, "id": 76002}, {"cost": [[11, 137101, 900]], "level": 447, "attributes": [[1, 7268400], [2, 72684000], [3, 3634200], [4, 3634200]], "nextLevel": 448, "id": 76002}, {"cost": [[11, 137101, 900]], "level": 448, "attributes": [[1, 7298100], [2, 72981000], [3, 3649050], [4, 3649050]], "nextLevel": 449, "id": 76002}, {"cost": [[11, 137101, 900]], "level": 449, "attributes": [[1, 7327800], [2, 73278000], [3, 3663900], [4, 3663900]], "nextLevel": 450, "id": 76002}, {"cost": [[11, 137101, 900]], "level": 450, "attributes": [[1, 7357500], [2, 73575000], [3, 3678750], [4, 3678750]], "nextLevel": 451, "id": 76002}, {"cost": [[11, 137101, 910]], "level": 451, "attributes": [[1, 7387500], [2, 73875000], [3, 3693750], [4, 3693750]], "nextLevel": 452, "id": 76002}, {"cost": [[11, 137101, 910]], "level": 452, "attributes": [[1, 7417500], [2, 74175000], [3, 3708750], [4, 3708750]], "nextLevel": 453, "id": 76002}, {"cost": [[11, 137101, 910]], "level": 453, "attributes": [[1, 7447500], [2, 74475000], [3, 3723750], [4, 3723750]], "nextLevel": 454, "id": 76002}, {"cost": [[11, 137101, 910]], "level": 454, "attributes": [[1, 7477500], [2, 74775000], [3, 3738750], [4, 3738750]], "nextLevel": 455, "id": 76002}, {"cost": [[11, 137101, 910]], "level": 455, "attributes": [[1, 7507500], [2, 75075000], [3, 3753750], [4, 3753750]], "nextLevel": 456, "id": 76002}, {"cost": [[11, 137101, 920]], "level": 456, "attributes": [[1, 7537800], [2, 75378000], [3, 3768900], [4, 3768900]], "nextLevel": 457, "id": 76002}, {"cost": [[11, 137101, 920]], "level": 457, "attributes": [[1, 7568100], [2, 75681000], [3, 3784050], [4, 3784050]], "nextLevel": 458, "id": 76002}, {"cost": [[11, 137101, 920]], "level": 458, "attributes": [[1, 7598400], [2, 75984000], [3, 3799200], [4, 3799200]], "nextLevel": 459, "id": 76002}, {"cost": [[11, 137101, 920]], "level": 459, "attributes": [[1, 7628700], [2, 76287000], [3, 3814350], [4, 3814350]], "nextLevel": 460, "id": 76002}, {"cost": [[11, 137101, 920]], "level": 460, "attributes": [[1, 7659000], [2, 76590000], [3, 3829500], [4, 3829500]], "nextLevel": 461, "id": 76002}, {"cost": [[11, 137101, 930]], "level": 461, "attributes": [[1, 7689600], [2, 76896000], [3, 3844800], [4, 3844800]], "nextLevel": 462, "id": 76002}, {"cost": [[11, 137101, 930]], "level": 462, "attributes": [[1, 7720200], [2, 77202000], [3, 3860100], [4, 3860100]], "nextLevel": 463, "id": 76002}, {"cost": [[11, 137101, 930]], "level": 463, "attributes": [[1, 7750800], [2, 77508000], [3, 3875400], [4, 3875400]], "nextLevel": 464, "id": 76002}, {"cost": [[11, 137101, 930]], "level": 464, "attributes": [[1, 7781400], [2, 77814000], [3, 3890700], [4, 3890700]], "nextLevel": 465, "id": 76002}, {"cost": [[11, 137101, 930]], "level": 465, "attributes": [[1, 7812000], [2, 78120000], [3, 3906000], [4, 3906000]], "nextLevel": 466, "id": 76002}, {"cost": [[11, 137101, 940]], "level": 466, "attributes": [[1, 7842900], [2, 78429000], [3, 3921450], [4, 3921450]], "nextLevel": 467, "id": 76002}, {"cost": [[11, 137101, 940]], "level": 467, "attributes": [[1, 7873800], [2, 78738000], [3, 3936900], [4, 3936900]], "nextLevel": 468, "id": 76002}, {"cost": [[11, 137101, 940]], "level": 468, "attributes": [[1, 7904700], [2, 79047000], [3, 3952350], [4, 3952350]], "nextLevel": 469, "id": 76002}, {"cost": [[11, 137101, 940]], "level": 469, "attributes": [[1, 7935600], [2, 79356000], [3, 3967800], [4, 3967800]], "nextLevel": 470, "id": 76002}, {"cost": [[11, 137101, 940]], "level": 470, "attributes": [[1, 7966500], [2, 79665000], [3, 3983250], [4, 3983250]], "nextLevel": 471, "id": 76002}, {"cost": [[11, 137101, 950]], "level": 471, "attributes": [[1, 7997700], [2, 79977000], [3, 3998850], [4, 3998850]], "nextLevel": 472, "id": 76002}, {"cost": [[11, 137101, 950]], "level": 472, "attributes": [[1, 8028900], [2, 80289000], [3, 4014450], [4, 4014450]], "nextLevel": 473, "id": 76002}, {"cost": [[11, 137101, 950]], "level": 473, "attributes": [[1, 8060100], [2, 80601000], [3, 4030050], [4, 4030050]], "nextLevel": 474, "id": 76002}, {"cost": [[11, 137101, 950]], "level": 474, "attributes": [[1, 8091300], [2, 80913000], [3, 4045650], [4, 4045650]], "nextLevel": 475, "id": 76002}, {"cost": [[11, 137101, 950]], "level": 475, "attributes": [[1, 8122500], [2, 81225000], [3, 4061250], [4, 4061250]], "nextLevel": 476, "id": 76002}, {"cost": [[11, 137101, 960]], "level": 476, "attributes": [[1, 8154000], [2, 81540000], [3, 4077000], [4, 4077000]], "nextLevel": 477, "id": 76002}, {"cost": [[11, 137101, 960]], "level": 477, "attributes": [[1, 8185500], [2, 81855000], [3, 4092750], [4, 4092750]], "nextLevel": 478, "id": 76002}, {"cost": [[11, 137101, 960]], "level": 478, "attributes": [[1, 8217000], [2, 82170000], [3, 4108500], [4, 4108500]], "nextLevel": 479, "id": 76002}, {"cost": [[11, 137101, 960]], "level": 479, "attributes": [[1, 8248500], [2, 82485000], [3, 4124250], [4, 4124250]], "nextLevel": 480, "id": 76002}, {"cost": [[11, 137101, 960]], "level": 480, "attributes": [[1, 8280000], [2, 82800000], [3, 4140000], [4, 4140000]], "nextLevel": 481, "id": 76002}, {"cost": [[11, 137101, 970]], "level": 481, "attributes": [[1, 8311800], [2, 83118000], [3, 4155900], [4, 4155900]], "nextLevel": 482, "id": 76002}, {"cost": [[11, 137101, 970]], "level": 482, "attributes": [[1, 8343600], [2, 83436000], [3, 4171800], [4, 4171800]], "nextLevel": 483, "id": 76002}, {"cost": [[11, 137101, 970]], "level": 483, "attributes": [[1, 8375400], [2, 83754000], [3, 4187700], [4, 4187700]], "nextLevel": 484, "id": 76002}, {"cost": [[11, 137101, 970]], "level": 484, "attributes": [[1, 8407200], [2, 84072000], [3, 4203600], [4, 4203600]], "nextLevel": 485, "id": 76002}, {"cost": [[11, 137101, 970]], "level": 485, "attributes": [[1, 8439000], [2, 84390000], [3, 4219500], [4, 4219500]], "nextLevel": 486, "id": 76002}, {"cost": [[11, 137101, 980]], "level": 486, "attributes": [[1, 8471100], [2, 84711000], [3, 4235550], [4, 4235550]], "nextLevel": 487, "id": 76002}, {"cost": [[11, 137101, 980]], "level": 487, "attributes": [[1, 8503200], [2, 85032000], [3, 4251600], [4, 4251600]], "nextLevel": 488, "id": 76002}, {"cost": [[11, 137101, 980]], "level": 488, "attributes": [[1, 8535300], [2, 85353000], [3, 4267650], [4, 4267650]], "nextLevel": 489, "id": 76002}, {"cost": [[11, 137101, 980]], "level": 489, "attributes": [[1, 8567400], [2, 85674000], [3, 4283700], [4, 4283700]], "nextLevel": 490, "id": 76002}, {"cost": [[11, 137101, 980]], "level": 490, "attributes": [[1, 8599500], [2, 85995000], [3, 4299750], [4, 4299750]], "nextLevel": 491, "id": 76002}, {"cost": [[11, 137101, 990]], "level": 491, "attributes": [[1, 8631900], [2, 86319000], [3, 4315950], [4, 4315950]], "nextLevel": 492, "id": 76002}, {"cost": [[11, 137101, 990]], "level": 492, "attributes": [[1, 8664300], [2, 86643000], [3, 4332150], [4, 4332150]], "nextLevel": 493, "id": 76002}, {"cost": [[11, 137101, 990]], "level": 493, "attributes": [[1, 8696700], [2, 86967000], [3, 4348350], [4, 4348350]], "nextLevel": 494, "id": 76002}, {"cost": [[11, 137101, 990]], "level": 494, "attributes": [[1, 8729100], [2, 87291000], [3, 4364550], [4, 4364550]], "nextLevel": 495, "id": 76002}, {"cost": [[11, 137101, 990]], "level": 495, "attributes": [[1, 8761500], [2, 87615000], [3, 4380750], [4, 4380750]], "nextLevel": 496, "id": 76002}, {"cost": [[11, 137101, 1000]], "level": 496, "attributes": [[1, 8794200], [2, 87942000], [3, 4397100], [4, 4397100]], "nextLevel": 497, "id": 76002}, {"cost": [[11, 137101, 1000]], "level": 497, "attributes": [[1, 8826900], [2, 88269000], [3, 4413450], [4, 4413450]], "nextLevel": 498, "id": 76002}, {"cost": [[11, 137101, 1000]], "level": 498, "attributes": [[1, 8859600], [2, 88596000], [3, 4429800], [4, 4429800]], "nextLevel": 499, "id": 76002}, {"cost": [[11, 137101, 1000]], "level": 499, "attributes": [[1, 8892300], [2, 88923000], [3, 4446150], [4, 4446150]], "nextLevel": 500, "id": 76002}, {"cost": [], "level": 500, "attributes": [[1, 8925000], [2, 89250000], [3, 4462500], [4, 4462500]], "nextLevel": 0, "id": 76002}, {"cost": [[11, 137101, 10]], "level": 1, "attributes": [[1, 3000], [2, 30000], [3, 1500], [4, 1500]], "nextLevel": 2, "id": 76003}, {"cost": [[11, 137101, 10]], "level": 2, "attributes": [[1, 6000], [2, 60000], [3, 3000], [4, 3000]], "nextLevel": 3, "id": 76003}, {"cost": [[11, 137101, 10]], "level": 3, "attributes": [[1, 9000], [2, 90000], [3, 4500], [4, 4500]], "nextLevel": 4, "id": 76003}, {"cost": [[11, 137101, 10]], "level": 4, "attributes": [[1, 12000], [2, 120000], [3, 6000], [4, 6000]], "nextLevel": 5, "id": 76003}, {"cost": [[11, 137101, 10]], "level": 5, "attributes": [[1, 15000], [2, 150000], [3, 7500], [4, 7500]], "nextLevel": 6, "id": 76003}, {"cost": [[11, 137101, 20]], "level": 6, "attributes": [[1, 18300], [2, 183000], [3, 9150], [4, 9150]], "nextLevel": 7, "id": 76003}, {"cost": [[11, 137101, 20]], "level": 7, "attributes": [[1, 21600], [2, 216000], [3, 10800], [4, 10800]], "nextLevel": 8, "id": 76003}, {"cost": [[11, 137101, 20]], "level": 8, "attributes": [[1, 24900], [2, 249000], [3, 12450], [4, 12450]], "nextLevel": 9, "id": 76003}, {"cost": [[11, 137101, 20]], "level": 9, "attributes": [[1, 28200], [2, 282000], [3, 14100], [4, 14100]], "nextLevel": 10, "id": 76003}, {"cost": [[11, 137101, 20]], "level": 10, "attributes": [[1, 31500], [2, 315000], [3, 15750], [4, 15750]], "nextLevel": 11, "id": 76003}, {"cost": [[11, 137101, 30]], "level": 11, "attributes": [[1, 35100], [2, 351000], [3, 17550], [4, 17550]], "nextLevel": 12, "id": 76003}, {"cost": [[11, 137101, 30]], "level": 12, "attributes": [[1, 38700], [2, 387000], [3, 19350], [4, 19350]], "nextLevel": 13, "id": 76003}, {"cost": [[11, 137101, 30]], "level": 13, "attributes": [[1, 42300], [2, 423000], [3, 21150], [4, 21150]], "nextLevel": 14, "id": 76003}, {"cost": [[11, 137101, 30]], "level": 14, "attributes": [[1, 45900], [2, 459000], [3, 22950], [4, 22950]], "nextLevel": 15, "id": 76003}, {"cost": [[11, 137101, 30]], "level": 15, "attributes": [[1, 49500], [2, 495000], [3, 24750], [4, 24750]], "nextLevel": 16, "id": 76003}, {"cost": [[11, 137101, 40]], "level": 16, "attributes": [[1, 53400], [2, 534000], [3, 26700], [4, 26700]], "nextLevel": 17, "id": 76003}, {"cost": [[11, 137101, 40]], "level": 17, "attributes": [[1, 57300], [2, 573000], [3, 28650], [4, 28650]], "nextLevel": 18, "id": 76003}, {"cost": [[11, 137101, 40]], "level": 18, "attributes": [[1, 61200], [2, 612000], [3, 30600], [4, 30600]], "nextLevel": 19, "id": 76003}, {"cost": [[11, 137101, 40]], "level": 19, "attributes": [[1, 65100], [2, 651000], [3, 32550], [4, 32550]], "nextLevel": 20, "id": 76003}, {"cost": [[11, 137101, 40]], "level": 20, "attributes": [[1, 69000], [2, 690000], [3, 34500], [4, 34500]], "nextLevel": 21, "id": 76003}, {"cost": [[11, 137101, 50]], "level": 21, "attributes": [[1, 73200], [2, 732000], [3, 36600], [4, 36600]], "nextLevel": 22, "id": 76003}, {"cost": [[11, 137101, 50]], "level": 22, "attributes": [[1, 77400], [2, 774000], [3, 38700], [4, 38700]], "nextLevel": 23, "id": 76003}, {"cost": [[11, 137101, 50]], "level": 23, "attributes": [[1, 81600], [2, 816000], [3, 40800], [4, 40800]], "nextLevel": 24, "id": 76003}, {"cost": [[11, 137101, 50]], "level": 24, "attributes": [[1, 85800], [2, 858000], [3, 42900], [4, 42900]], "nextLevel": 25, "id": 76003}, {"cost": [[11, 137101, 50]], "level": 25, "attributes": [[1, 90000], [2, 900000], [3, 45000], [4, 45000]], "nextLevel": 26, "id": 76003}, {"cost": [[11, 137101, 60]], "level": 26, "attributes": [[1, 94500], [2, 945000], [3, 47250], [4, 47250]], "nextLevel": 27, "id": 76003}, {"cost": [[11, 137101, 60]], "level": 27, "attributes": [[1, 99000], [2, 990000], [3, 49500], [4, 49500]], "nextLevel": 28, "id": 76003}, {"cost": [[11, 137101, 60]], "level": 28, "attributes": [[1, 103500], [2, 1035000], [3, 51750], [4, 51750]], "nextLevel": 29, "id": 76003}, {"cost": [[11, 137101, 60]], "level": 29, "attributes": [[1, 108000], [2, 1080000], [3, 54000], [4, 54000]], "nextLevel": 30, "id": 76003}, {"cost": [[11, 137101, 60]], "level": 30, "attributes": [[1, 112500], [2, 1125000], [3, 56250], [4, 56250]], "nextLevel": 31, "id": 76003}, {"cost": [[11, 137101, 70]], "level": 31, "attributes": [[1, 117300], [2, 1173000], [3, 58650], [4, 58650]], "nextLevel": 32, "id": 76003}, {"cost": [[11, 137101, 70]], "level": 32, "attributes": [[1, 122100], [2, 1221000], [3, 61050], [4, 61050]], "nextLevel": 33, "id": 76003}, {"cost": [[11, 137101, 70]], "level": 33, "attributes": [[1, 126900], [2, 1269000], [3, 63450], [4, 63450]], "nextLevel": 34, "id": 76003}, {"cost": [[11, 137101, 70]], "level": 34, "attributes": [[1, 131700], [2, 1317000], [3, 65850], [4, 65850]], "nextLevel": 35, "id": 76003}, {"cost": [[11, 137101, 70]], "level": 35, "attributes": [[1, 136500], [2, 1365000], [3, 68250], [4, 68250]], "nextLevel": 36, "id": 76003}, {"cost": [[11, 137101, 80]], "level": 36, "attributes": [[1, 141600], [2, 1416000], [3, 70800], [4, 70800]], "nextLevel": 37, "id": 76003}, {"cost": [[11, 137101, 80]], "level": 37, "attributes": [[1, 146700], [2, 1467000], [3, 73350], [4, 73350]], "nextLevel": 38, "id": 76003}, {"cost": [[11, 137101, 80]], "level": 38, "attributes": [[1, 151800], [2, 1518000], [3, 75900], [4, 75900]], "nextLevel": 39, "id": 76003}, {"cost": [[11, 137101, 80]], "level": 39, "attributes": [[1, 156900], [2, 1569000], [3, 78450], [4, 78450]], "nextLevel": 40, "id": 76003}, {"cost": [[11, 137101, 80]], "level": 40, "attributes": [[1, 162000], [2, 1620000], [3, 81000], [4, 81000]], "nextLevel": 41, "id": 76003}, {"cost": [[11, 137101, 90]], "level": 41, "attributes": [[1, 167400], [2, 1674000], [3, 83700], [4, 83700]], "nextLevel": 42, "id": 76003}, {"cost": [[11, 137101, 90]], "level": 42, "attributes": [[1, 172800], [2, 1728000], [3, 86400], [4, 86400]], "nextLevel": 43, "id": 76003}, {"cost": [[11, 137101, 90]], "level": 43, "attributes": [[1, 178200], [2, 1782000], [3, 89100], [4, 89100]], "nextLevel": 44, "id": 76003}, {"cost": [[11, 137101, 90]], "level": 44, "attributes": [[1, 183600], [2, 1836000], [3, 91800], [4, 91800]], "nextLevel": 45, "id": 76003}, {"cost": [[11, 137101, 90]], "level": 45, "attributes": [[1, 189000], [2, 1890000], [3, 94500], [4, 94500]], "nextLevel": 46, "id": 76003}, {"cost": [[11, 137101, 100]], "level": 46, "attributes": [[1, 194700], [2, 1947000], [3, 97350], [4, 97350]], "nextLevel": 47, "id": 76003}, {"cost": [[11, 137101, 100]], "level": 47, "attributes": [[1, 200400], [2, 2004000], [3, 100200], [4, 100200]], "nextLevel": 48, "id": 76003}, {"cost": [[11, 137101, 100]], "level": 48, "attributes": [[1, 206100], [2, 2061000], [3, 103050], [4, 103050]], "nextLevel": 49, "id": 76003}, {"cost": [[11, 137101, 100]], "level": 49, "attributes": [[1, 211800], [2, 2118000], [3, 105900], [4, 105900]], "nextLevel": 50, "id": 76003}, {"cost": [[11, 137101, 100]], "level": 50, "attributes": [[1, 217500], [2, 2175000], [3, 108750], [4, 108750]], "nextLevel": 51, "id": 76003}, {"cost": [[11, 137101, 110]], "level": 51, "attributes": [[1, 223500], [2, 2235000], [3, 111750], [4, 111750]], "nextLevel": 52, "id": 76003}, {"cost": [[11, 137101, 110]], "level": 52, "attributes": [[1, 229500], [2, 2295000], [3, 114750], [4, 114750]], "nextLevel": 53, "id": 76003}, {"cost": [[11, 137101, 110]], "level": 53, "attributes": [[1, 235500], [2, 2355000], [3, 117750], [4, 117750]], "nextLevel": 54, "id": 76003}, {"cost": [[11, 137101, 110]], "level": 54, "attributes": [[1, 241500], [2, 2415000], [3, 120750], [4, 120750]], "nextLevel": 55, "id": 76003}, {"cost": [[11, 137101, 110]], "level": 55, "attributes": [[1, 247500], [2, 2475000], [3, 123750], [4, 123750]], "nextLevel": 56, "id": 76003}, {"cost": [[11, 137101, 120]], "level": 56, "attributes": [[1, 253800], [2, 2538000], [3, 126900], [4, 126900]], "nextLevel": 57, "id": 76003}, {"cost": [[11, 137101, 120]], "level": 57, "attributes": [[1, 260100], [2, 2601000], [3, 130050], [4, 130050]], "nextLevel": 58, "id": 76003}, {"cost": [[11, 137101, 120]], "level": 58, "attributes": [[1, 266400], [2, 2664000], [3, 133200], [4, 133200]], "nextLevel": 59, "id": 76003}, {"cost": [[11, 137101, 120]], "level": 59, "attributes": [[1, 272700], [2, 2727000], [3, 136350], [4, 136350]], "nextLevel": 60, "id": 76003}, {"cost": [[11, 137101, 120]], "level": 60, "attributes": [[1, 279000], [2, 2790000], [3, 139500], [4, 139500]], "nextLevel": 61, "id": 76003}, {"cost": [[11, 137101, 130]], "level": 61, "attributes": [[1, 285600], [2, 2856000], [3, 142800], [4, 142800]], "nextLevel": 62, "id": 76003}, {"cost": [[11, 137101, 130]], "level": 62, "attributes": [[1, 292200], [2, 2922000], [3, 146100], [4, 146100]], "nextLevel": 63, "id": 76003}, {"cost": [[11, 137101, 130]], "level": 63, "attributes": [[1, 298800], [2, 2988000], [3, 149400], [4, 149400]], "nextLevel": 64, "id": 76003}, {"cost": [[11, 137101, 130]], "level": 64, "attributes": [[1, 305400], [2, 3054000], [3, 152700], [4, 152700]], "nextLevel": 65, "id": 76003}, {"cost": [[11, 137101, 130]], "level": 65, "attributes": [[1, 312000], [2, 3120000], [3, 156000], [4, 156000]], "nextLevel": 66, "id": 76003}, {"cost": [[11, 137101, 140]], "level": 66, "attributes": [[1, 318900], [2, 3189000], [3, 159450], [4, 159450]], "nextLevel": 67, "id": 76003}, {"cost": [[11, 137101, 140]], "level": 67, "attributes": [[1, 325800], [2, 3258000], [3, 162900], [4, 162900]], "nextLevel": 68, "id": 76003}, {"cost": [[11, 137101, 140]], "level": 68, "attributes": [[1, 332700], [2, 3327000], [3, 166350], [4, 166350]], "nextLevel": 69, "id": 76003}, {"cost": [[11, 137101, 140]], "level": 69, "attributes": [[1, 339600], [2, 3396000], [3, 169800], [4, 169800]], "nextLevel": 70, "id": 76003}, {"cost": [[11, 137101, 140]], "level": 70, "attributes": [[1, 346500], [2, 3465000], [3, 173250], [4, 173250]], "nextLevel": 71, "id": 76003}, {"cost": [[11, 137101, 150]], "level": 71, "attributes": [[1, 353700], [2, 3537000], [3, 176850], [4, 176850]], "nextLevel": 72, "id": 76003}, {"cost": [[11, 137101, 150]], "level": 72, "attributes": [[1, 360900], [2, 3609000], [3, 180450], [4, 180450]], "nextLevel": 73, "id": 76003}, {"cost": [[11, 137101, 150]], "level": 73, "attributes": [[1, 368100], [2, 3681000], [3, 184050], [4, 184050]], "nextLevel": 74, "id": 76003}, {"cost": [[11, 137101, 150]], "level": 74, "attributes": [[1, 375300], [2, 3753000], [3, 187650], [4, 187650]], "nextLevel": 75, "id": 76003}, {"cost": [[11, 137101, 150]], "level": 75, "attributes": [[1, 382500], [2, 3825000], [3, 191250], [4, 191250]], "nextLevel": 76, "id": 76003}, {"cost": [[11, 137101, 160]], "level": 76, "attributes": [[1, 390000], [2, 3900000], [3, 195000], [4, 195000]], "nextLevel": 77, "id": 76003}, {"cost": [[11, 137101, 160]], "level": 77, "attributes": [[1, 397500], [2, 3975000], [3, 198750], [4, 198750]], "nextLevel": 78, "id": 76003}, {"cost": [[11, 137101, 160]], "level": 78, "attributes": [[1, 405000], [2, 4050000], [3, 202500], [4, 202500]], "nextLevel": 79, "id": 76003}, {"cost": [[11, 137101, 160]], "level": 79, "attributes": [[1, 412500], [2, 4125000], [3, 206250], [4, 206250]], "nextLevel": 80, "id": 76003}, {"cost": [[11, 137101, 160]], "level": 80, "attributes": [[1, 420000], [2, 4200000], [3, 210000], [4, 210000]], "nextLevel": 81, "id": 76003}, {"cost": [[11, 137101, 170]], "level": 81, "attributes": [[1, 427800], [2, 4278000], [3, 213900], [4, 213900]], "nextLevel": 82, "id": 76003}, {"cost": [[11, 137101, 170]], "level": 82, "attributes": [[1, 435600], [2, 4356000], [3, 217800], [4, 217800]], "nextLevel": 83, "id": 76003}, {"cost": [[11, 137101, 170]], "level": 83, "attributes": [[1, 443400], [2, 4434000], [3, 221700], [4, 221700]], "nextLevel": 84, "id": 76003}, {"cost": [[11, 137101, 170]], "level": 84, "attributes": [[1, 451200], [2, 4512000], [3, 225600], [4, 225600]], "nextLevel": 85, "id": 76003}, {"cost": [[11, 137101, 170]], "level": 85, "attributes": [[1, 459000], [2, 4590000], [3, 229500], [4, 229500]], "nextLevel": 86, "id": 76003}, {"cost": [[11, 137101, 180]], "level": 86, "attributes": [[1, 467100], [2, 4671000], [3, 233550], [4, 233550]], "nextLevel": 87, "id": 76003}, {"cost": [[11, 137101, 180]], "level": 87, "attributes": [[1, 475200], [2, 4752000], [3, 237600], [4, 237600]], "nextLevel": 88, "id": 76003}, {"cost": [[11, 137101, 180]], "level": 88, "attributes": [[1, 483300], [2, 4833000], [3, 241650], [4, 241650]], "nextLevel": 89, "id": 76003}, {"cost": [[11, 137101, 180]], "level": 89, "attributes": [[1, 491400], [2, 4914000], [3, 245700], [4, 245700]], "nextLevel": 90, "id": 76003}, {"cost": [[11, 137101, 180]], "level": 90, "attributes": [[1, 499500], [2, 4995000], [3, 249750], [4, 249750]], "nextLevel": 91, "id": 76003}, {"cost": [[11, 137101, 190]], "level": 91, "attributes": [[1, 507900], [2, 5079000], [3, 253950], [4, 253950]], "nextLevel": 92, "id": 76003}, {"cost": [[11, 137101, 190]], "level": 92, "attributes": [[1, 516300], [2, 5163000], [3, 258150], [4, 258150]], "nextLevel": 93, "id": 76003}, {"cost": [[11, 137101, 190]], "level": 93, "attributes": [[1, 524700], [2, 5247000], [3, 262350], [4, 262350]], "nextLevel": 94, "id": 76003}, {"cost": [[11, 137101, 190]], "level": 94, "attributes": [[1, 533100], [2, 5331000], [3, 266550], [4, 266550]], "nextLevel": 95, "id": 76003}, {"cost": [[11, 137101, 190]], "level": 95, "attributes": [[1, 541500], [2, 5415000], [3, 270750], [4, 270750]], "nextLevel": 96, "id": 76003}, {"cost": [[11, 137101, 200]], "level": 96, "attributes": [[1, 550200], [2, 5502000], [3, 275100], [4, 275100]], "nextLevel": 97, "id": 76003}, {"cost": [[11, 137101, 200]], "level": 97, "attributes": [[1, 558900], [2, 5589000], [3, 279450], [4, 279450]], "nextLevel": 98, "id": 76003}, {"cost": [[11, 137101, 200]], "level": 98, "attributes": [[1, 567600], [2, 5676000], [3, 283800], [4, 283800]], "nextLevel": 99, "id": 76003}, {"cost": [[11, 137101, 200]], "level": 99, "attributes": [[1, 576300], [2, 5763000], [3, 288150], [4, 288150]], "nextLevel": 100, "id": 76003}, {"cost": [[11, 137101, 200]], "level": 100, "attributes": [[1, 585000], [2, 5850000], [3, 292500], [4, 292500]], "nextLevel": 101, "id": 76003}, {"cost": [[11, 137101, 210]], "level": 101, "attributes": [[1, 594000], [2, 5940000], [3, 297000], [4, 297000]], "nextLevel": 102, "id": 76003}, {"cost": [[11, 137101, 210]], "level": 102, "attributes": [[1, 603000], [2, 6030000], [3, 301500], [4, 301500]], "nextLevel": 103, "id": 76003}, {"cost": [[11, 137101, 210]], "level": 103, "attributes": [[1, 612000], [2, 6120000], [3, 306000], [4, 306000]], "nextLevel": 104, "id": 76003}, {"cost": [[11, 137101, 210]], "level": 104, "attributes": [[1, 621000], [2, 6210000], [3, 310500], [4, 310500]], "nextLevel": 105, "id": 76003}, {"cost": [[11, 137101, 210]], "level": 105, "attributes": [[1, 630000], [2, 6300000], [3, 315000], [4, 315000]], "nextLevel": 106, "id": 76003}, {"cost": [[11, 137101, 220]], "level": 106, "attributes": [[1, 639300], [2, 6393000], [3, 319650], [4, 319650]], "nextLevel": 107, "id": 76003}, {"cost": [[11, 137101, 220]], "level": 107, "attributes": [[1, 648600], [2, 6486000], [3, 324300], [4, 324300]], "nextLevel": 108, "id": 76003}, {"cost": [[11, 137101, 220]], "level": 108, "attributes": [[1, 657900], [2, 6579000], [3, 328950], [4, 328950]], "nextLevel": 109, "id": 76003}, {"cost": [[11, 137101, 220]], "level": 109, "attributes": [[1, 667200], [2, 6672000], [3, 333600], [4, 333600]], "nextLevel": 110, "id": 76003}, {"cost": [[11, 137101, 220]], "level": 110, "attributes": [[1, 676500], [2, 6765000], [3, 338250], [4, 338250]], "nextLevel": 111, "id": 76003}, {"cost": [[11, 137101, 230]], "level": 111, "attributes": [[1, 686100], [2, 6861000], [3, 343050], [4, 343050]], "nextLevel": 112, "id": 76003}, {"cost": [[11, 137101, 230]], "level": 112, "attributes": [[1, 695700], [2, 6957000], [3, 347850], [4, 347850]], "nextLevel": 113, "id": 76003}, {"cost": [[11, 137101, 230]], "level": 113, "attributes": [[1, 705300], [2, 7053000], [3, 352650], [4, 352650]], "nextLevel": 114, "id": 76003}, {"cost": [[11, 137101, 230]], "level": 114, "attributes": [[1, 714900], [2, 7149000], [3, 357450], [4, 357450]], "nextLevel": 115, "id": 76003}, {"cost": [[11, 137101, 230]], "level": 115, "attributes": [[1, 724500], [2, 7245000], [3, 362250], [4, 362250]], "nextLevel": 116, "id": 76003}, {"cost": [[11, 137101, 240]], "level": 116, "attributes": [[1, 734400], [2, 7344000], [3, 367200], [4, 367200]], "nextLevel": 117, "id": 76003}, {"cost": [[11, 137101, 240]], "level": 117, "attributes": [[1, 744300], [2, 7443000], [3, 372150], [4, 372150]], "nextLevel": 118, "id": 76003}, {"cost": [[11, 137101, 240]], "level": 118, "attributes": [[1, 754200], [2, 7542000], [3, 377100], [4, 377100]], "nextLevel": 119, "id": 76003}, {"cost": [[11, 137101, 240]], "level": 119, "attributes": [[1, 764100], [2, 7641000], [3, 382050], [4, 382050]], "nextLevel": 120, "id": 76003}, {"cost": [[11, 137101, 240]], "level": 120, "attributes": [[1, 774000], [2, 7740000], [3, 387000], [4, 387000]], "nextLevel": 121, "id": 76003}, {"cost": [[11, 137101, 250]], "level": 121, "attributes": [[1, 784200], [2, 7842000], [3, 392100], [4, 392100]], "nextLevel": 122, "id": 76003}, {"cost": [[11, 137101, 250]], "level": 122, "attributes": [[1, 794400], [2, 7944000], [3, 397200], [4, 397200]], "nextLevel": 123, "id": 76003}, {"cost": [[11, 137101, 250]], "level": 123, "attributes": [[1, 804600], [2, 8046000], [3, 402300], [4, 402300]], "nextLevel": 124, "id": 76003}, {"cost": [[11, 137101, 250]], "level": 124, "attributes": [[1, 814800], [2, 8148000], [3, 407400], [4, 407400]], "nextLevel": 125, "id": 76003}, {"cost": [[11, 137101, 250]], "level": 125, "attributes": [[1, 825000], [2, 8250000], [3, 412500], [4, 412500]], "nextLevel": 126, "id": 76003}, {"cost": [[11, 137101, 260]], "level": 126, "attributes": [[1, 835500], [2, 8355000], [3, 417750], [4, 417750]], "nextLevel": 127, "id": 76003}, {"cost": [[11, 137101, 260]], "level": 127, "attributes": [[1, 846000], [2, 8460000], [3, 423000], [4, 423000]], "nextLevel": 128, "id": 76003}, {"cost": [[11, 137101, 260]], "level": 128, "attributes": [[1, 856500], [2, 8565000], [3, 428250], [4, 428250]], "nextLevel": 129, "id": 76003}, {"cost": [[11, 137101, 260]], "level": 129, "attributes": [[1, 867000], [2, 8670000], [3, 433500], [4, 433500]], "nextLevel": 130, "id": 76003}, {"cost": [[11, 137101, 260]], "level": 130, "attributes": [[1, 877500], [2, 8775000], [3, 438750], [4, 438750]], "nextLevel": 131, "id": 76003}, {"cost": [[11, 137101, 270]], "level": 131, "attributes": [[1, 888300], [2, 8883000], [3, 444150], [4, 444150]], "nextLevel": 132, "id": 76003}, {"cost": [[11, 137101, 270]], "level": 132, "attributes": [[1, 899100], [2, 8991000], [3, 449550], [4, 449550]], "nextLevel": 133, "id": 76003}, {"cost": [[11, 137101, 270]], "level": 133, "attributes": [[1, 909900], [2, 9099000], [3, 454950], [4, 454950]], "nextLevel": 134, "id": 76003}, {"cost": [[11, 137101, 270]], "level": 134, "attributes": [[1, 920700], [2, 9207000], [3, 460350], [4, 460350]], "nextLevel": 135, "id": 76003}, {"cost": [[11, 137101, 270]], "level": 135, "attributes": [[1, 931500], [2, 9315000], [3, 465750], [4, 465750]], "nextLevel": 136, "id": 76003}, {"cost": [[11, 137101, 280]], "level": 136, "attributes": [[1, 942600], [2, 9426000], [3, 471300], [4, 471300]], "nextLevel": 137, "id": 76003}, {"cost": [[11, 137101, 280]], "level": 137, "attributes": [[1, 953700], [2, 9537000], [3, 476850], [4, 476850]], "nextLevel": 138, "id": 76003}, {"cost": [[11, 137101, 280]], "level": 138, "attributes": [[1, 964800], [2, 9648000], [3, 482400], [4, 482400]], "nextLevel": 139, "id": 76003}, {"cost": [[11, 137101, 280]], "level": 139, "attributes": [[1, 975900], [2, 9759000], [3, 487950], [4, 487950]], "nextLevel": 140, "id": 76003}, {"cost": [[11, 137101, 280]], "level": 140, "attributes": [[1, 987000], [2, 9870000], [3, 493500], [4, 493500]], "nextLevel": 141, "id": 76003}, {"cost": [[11, 137101, 290]], "level": 141, "attributes": [[1, 998400], [2, 9984000], [3, 499200], [4, 499200]], "nextLevel": 142, "id": 76003}, {"cost": [[11, 137101, 290]], "level": 142, "attributes": [[1, 1009800], [2, 10098000], [3, 504900], [4, 504900]], "nextLevel": 143, "id": 76003}, {"cost": [[11, 137101, 290]], "level": 143, "attributes": [[1, 1021200], [2, 10212000], [3, 510600], [4, 510600]], "nextLevel": 144, "id": 76003}, {"cost": [[11, 137101, 290]], "level": 144, "attributes": [[1, 1032600], [2, 10326000], [3, 516300], [4, 516300]], "nextLevel": 145, "id": 76003}, {"cost": [[11, 137101, 290]], "level": 145, "attributes": [[1, 1044000], [2, 10440000], [3, 522000], [4, 522000]], "nextLevel": 146, "id": 76003}, {"cost": [[11, 137101, 300]], "level": 146, "attributes": [[1, 1055700], [2, 10557000], [3, 527850], [4, 527850]], "nextLevel": 147, "id": 76003}, {"cost": [[11, 137101, 300]], "level": 147, "attributes": [[1, 1067400], [2, 10674000], [3, 533700], [4, 533700]], "nextLevel": 148, "id": 76003}, {"cost": [[11, 137101, 300]], "level": 148, "attributes": [[1, 1079100], [2, 10791000], [3, 539550], [4, 539550]], "nextLevel": 149, "id": 76003}, {"cost": [[11, 137101, 300]], "level": 149, "attributes": [[1, 1090800], [2, 10908000], [3, 545400], [4, 545400]], "nextLevel": 150, "id": 76003}, {"cost": [[11, 137101, 300]], "level": 150, "attributes": [[1, 1102500], [2, 11025000], [3, 551250], [4, 551250]], "nextLevel": 151, "id": 76003}, {"cost": [[11, 137101, 310]], "level": 151, "attributes": [[1, 1114500], [2, 11145000], [3, 557250], [4, 557250]], "nextLevel": 152, "id": 76003}, {"cost": [[11, 137101, 310]], "level": 152, "attributes": [[1, 1126500], [2, 11265000], [3, 563250], [4, 563250]], "nextLevel": 153, "id": 76003}, {"cost": [[11, 137101, 310]], "level": 153, "attributes": [[1, 1138500], [2, 11385000], [3, 569250], [4, 569250]], "nextLevel": 154, "id": 76003}, {"cost": [[11, 137101, 310]], "level": 154, "attributes": [[1, 1150500], [2, 11505000], [3, 575250], [4, 575250]], "nextLevel": 155, "id": 76003}, {"cost": [[11, 137101, 310]], "level": 155, "attributes": [[1, 1162500], [2, 11625000], [3, 581250], [4, 581250]], "nextLevel": 156, "id": 76003}, {"cost": [[11, 137101, 320]], "level": 156, "attributes": [[1, 1174800], [2, 11748000], [3, 587400], [4, 587400]], "nextLevel": 157, "id": 76003}, {"cost": [[11, 137101, 320]], "level": 157, "attributes": [[1, 1187100], [2, 11871000], [3, 593550], [4, 593550]], "nextLevel": 158, "id": 76003}, {"cost": [[11, 137101, 320]], "level": 158, "attributes": [[1, 1199400], [2, 11994000], [3, 599700], [4, 599700]], "nextLevel": 159, "id": 76003}, {"cost": [[11, 137101, 320]], "level": 159, "attributes": [[1, 1211700], [2, 12117000], [3, 605850], [4, 605850]], "nextLevel": 160, "id": 76003}, {"cost": [[11, 137101, 320]], "level": 160, "attributes": [[1, 1224000], [2, 12240000], [3, 612000], [4, 612000]], "nextLevel": 161, "id": 76003}, {"cost": [[11, 137101, 330]], "level": 161, "attributes": [[1, 1236600], [2, 12366000], [3, 618300], [4, 618300]], "nextLevel": 162, "id": 76003}, {"cost": [[11, 137101, 330]], "level": 162, "attributes": [[1, 1249200], [2, 12492000], [3, 624600], [4, 624600]], "nextLevel": 163, "id": 76003}, {"cost": [[11, 137101, 330]], "level": 163, "attributes": [[1, 1261800], [2, 12618000], [3, 630900], [4, 630900]], "nextLevel": 164, "id": 76003}, {"cost": [[11, 137101, 330]], "level": 164, "attributes": [[1, 1274400], [2, 12744000], [3, 637200], [4, 637200]], "nextLevel": 165, "id": 76003}, {"cost": [[11, 137101, 330]], "level": 165, "attributes": [[1, 1287000], [2, 12870000], [3, 643500], [4, 643500]], "nextLevel": 166, "id": 76003}, {"cost": [[11, 137101, 340]], "level": 166, "attributes": [[1, 1299900], [2, 12999000], [3, 649950], [4, 649950]], "nextLevel": 167, "id": 76003}, {"cost": [[11, 137101, 340]], "level": 167, "attributes": [[1, 1312800], [2, 13128000], [3, 656400], [4, 656400]], "nextLevel": 168, "id": 76003}, {"cost": [[11, 137101, 340]], "level": 168, "attributes": [[1, 1325700], [2, 13257000], [3, 662850], [4, 662850]], "nextLevel": 169, "id": 76003}, {"cost": [[11, 137101, 340]], "level": 169, "attributes": [[1, 1338600], [2, 13386000], [3, 669300], [4, 669300]], "nextLevel": 170, "id": 76003}, {"cost": [[11, 137101, 340]], "level": 170, "attributes": [[1, 1351500], [2, 13515000], [3, 675750], [4, 675750]], "nextLevel": 171, "id": 76003}, {"cost": [[11, 137101, 350]], "level": 171, "attributes": [[1, 1364700], [2, 13647000], [3, 682350], [4, 682350]], "nextLevel": 172, "id": 76003}, {"cost": [[11, 137101, 350]], "level": 172, "attributes": [[1, 1377900], [2, 13779000], [3, 688950], [4, 688950]], "nextLevel": 173, "id": 76003}, {"cost": [[11, 137101, 350]], "level": 173, "attributes": [[1, 1391100], [2, 13911000], [3, 695550], [4, 695550]], "nextLevel": 174, "id": 76003}, {"cost": [[11, 137101, 350]], "level": 174, "attributes": [[1, 1404300], [2, 14043000], [3, 702150], [4, 702150]], "nextLevel": 175, "id": 76003}, {"cost": [[11, 137101, 350]], "level": 175, "attributes": [[1, 1417500], [2, 14175000], [3, 708750], [4, 708750]], "nextLevel": 176, "id": 76003}, {"cost": [[11, 137101, 360]], "level": 176, "attributes": [[1, 1431000], [2, 14310000], [3, 715500], [4, 715500]], "nextLevel": 177, "id": 76003}, {"cost": [[11, 137101, 360]], "level": 177, "attributes": [[1, 1444500], [2, 14445000], [3, 722250], [4, 722250]], "nextLevel": 178, "id": 76003}, {"cost": [[11, 137101, 360]], "level": 178, "attributes": [[1, 1458000], [2, 14580000], [3, 729000], [4, 729000]], "nextLevel": 179, "id": 76003}, {"cost": [[11, 137101, 360]], "level": 179, "attributes": [[1, 1471500], [2, 14715000], [3, 735750], [4, 735750]], "nextLevel": 180, "id": 76003}, {"cost": [[11, 137101, 360]], "level": 180, "attributes": [[1, 1485000], [2, 14850000], [3, 742500], [4, 742500]], "nextLevel": 181, "id": 76003}, {"cost": [[11, 137101, 370]], "level": 181, "attributes": [[1, 1498800], [2, 14988000], [3, 749400], [4, 749400]], "nextLevel": 182, "id": 76003}, {"cost": [[11, 137101, 370]], "level": 182, "attributes": [[1, 1512600], [2, 15126000], [3, 756300], [4, 756300]], "nextLevel": 183, "id": 76003}, {"cost": [[11, 137101, 370]], "level": 183, "attributes": [[1, 1526400], [2, 15264000], [3, 763200], [4, 763200]], "nextLevel": 184, "id": 76003}, {"cost": [[11, 137101, 370]], "level": 184, "attributes": [[1, 1540200], [2, 15402000], [3, 770100], [4, 770100]], "nextLevel": 185, "id": 76003}, {"cost": [[11, 137101, 370]], "level": 185, "attributes": [[1, 1554000], [2, 15540000], [3, 777000], [4, 777000]], "nextLevel": 186, "id": 76003}, {"cost": [[11, 137101, 380]], "level": 186, "attributes": [[1, 1568100], [2, 15681000], [3, 784050], [4, 784050]], "nextLevel": 187, "id": 76003}, {"cost": [[11, 137101, 380]], "level": 187, "attributes": [[1, 1582200], [2, 15822000], [3, 791100], [4, 791100]], "nextLevel": 188, "id": 76003}, {"cost": [[11, 137101, 380]], "level": 188, "attributes": [[1, 1596300], [2, 15963000], [3, 798150], [4, 798150]], "nextLevel": 189, "id": 76003}, {"cost": [[11, 137101, 380]], "level": 189, "attributes": [[1, 1610400], [2, 16104000], [3, 805200], [4, 805200]], "nextLevel": 190, "id": 76003}, {"cost": [[11, 137101, 380]], "level": 190, "attributes": [[1, 1624500], [2, 16245000], [3, 812250], [4, 812250]], "nextLevel": 191, "id": 76003}, {"cost": [[11, 137101, 390]], "level": 191, "attributes": [[1, 1638900], [2, 16389000], [3, 819450], [4, 819450]], "nextLevel": 192, "id": 76003}, {"cost": [[11, 137101, 390]], "level": 192, "attributes": [[1, 1653300], [2, 16533000], [3, 826650], [4, 826650]], "nextLevel": 193, "id": 76003}, {"cost": [[11, 137101, 390]], "level": 193, "attributes": [[1, 1667700], [2, 16677000], [3, 833850], [4, 833850]], "nextLevel": 194, "id": 76003}, {"cost": [[11, 137101, 390]], "level": 194, "attributes": [[1, 1682100], [2, 16821000], [3, 841050], [4, 841050]], "nextLevel": 195, "id": 76003}, {"cost": [[11, 137101, 390]], "level": 195, "attributes": [[1, 1696500], [2, 16965000], [3, 848250], [4, 848250]], "nextLevel": 196, "id": 76003}, {"cost": [[11, 137101, 400]], "level": 196, "attributes": [[1, 1711200], [2, 17112000], [3, 855600], [4, 855600]], "nextLevel": 197, "id": 76003}, {"cost": [[11, 137101, 400]], "level": 197, "attributes": [[1, 1725900], [2, 17259000], [3, 862950], [4, 862950]], "nextLevel": 198, "id": 76003}, {"cost": [[11, 137101, 400]], "level": 198, "attributes": [[1, 1740600], [2, 17406000], [3, 870300], [4, 870300]], "nextLevel": 199, "id": 76003}, {"cost": [[11, 137101, 400]], "level": 199, "attributes": [[1, 1755300], [2, 17553000], [3, 877650], [4, 877650]], "nextLevel": 200, "id": 76003}, {"cost": [[11, 137101, 400]], "level": 200, "attributes": [[1, 1770000], [2, 17700000], [3, 885000], [4, 885000]], "nextLevel": 201, "id": 76003}, {"cost": [[11, 137101, 410]], "level": 201, "attributes": [[1, 1785000], [2, 17850000], [3, 892500], [4, 892500]], "nextLevel": 202, "id": 76003}, {"cost": [[11, 137101, 410]], "level": 202, "attributes": [[1, 1800000], [2, 18000000], [3, 900000], [4, 900000]], "nextLevel": 203, "id": 76003}, {"cost": [[11, 137101, 410]], "level": 203, "attributes": [[1, 1815000], [2, 18150000], [3, 907500], [4, 907500]], "nextLevel": 204, "id": 76003}, {"cost": [[11, 137101, 410]], "level": 204, "attributes": [[1, 1830000], [2, 18300000], [3, 915000], [4, 915000]], "nextLevel": 205, "id": 76003}, {"cost": [[11, 137101, 410]], "level": 205, "attributes": [[1, 1845000], [2, 18450000], [3, 922500], [4, 922500]], "nextLevel": 206, "id": 76003}, {"cost": [[11, 137101, 420]], "level": 206, "attributes": [[1, 1860300], [2, 18603000], [3, 930150], [4, 930150]], "nextLevel": 207, "id": 76003}, {"cost": [[11, 137101, 420]], "level": 207, "attributes": [[1, 1875600], [2, 18756000], [3, 937800], [4, 937800]], "nextLevel": 208, "id": 76003}, {"cost": [[11, 137101, 420]], "level": 208, "attributes": [[1, 1890900], [2, 18909000], [3, 945450], [4, 945450]], "nextLevel": 209, "id": 76003}, {"cost": [[11, 137101, 420]], "level": 209, "attributes": [[1, 1906200], [2, 19062000], [3, 953100], [4, 953100]], "nextLevel": 210, "id": 76003}, {"cost": [[11, 137101, 420]], "level": 210, "attributes": [[1, 1921500], [2, 19215000], [3, 960750], [4, 960750]], "nextLevel": 211, "id": 76003}, {"cost": [[11, 137101, 430]], "level": 211, "attributes": [[1, 1937100], [2, 19371000], [3, 968550], [4, 968550]], "nextLevel": 212, "id": 76003}, {"cost": [[11, 137101, 430]], "level": 212, "attributes": [[1, 1952700], [2, 19527000], [3, 976350], [4, 976350]], "nextLevel": 213, "id": 76003}, {"cost": [[11, 137101, 430]], "level": 213, "attributes": [[1, 1968300], [2, 19683000], [3, 984150], [4, 984150]], "nextLevel": 214, "id": 76003}, {"cost": [[11, 137101, 430]], "level": 214, "attributes": [[1, 1983900], [2, 19839000], [3, 991950], [4, 991950]], "nextLevel": 215, "id": 76003}, {"cost": [[11, 137101, 430]], "level": 215, "attributes": [[1, 1999500], [2, 19995000], [3, 999750], [4, 999750]], "nextLevel": 216, "id": 76003}, {"cost": [[11, 137101, 440]], "level": 216, "attributes": [[1, 2015400], [2, 20154000], [3, 1007700], [4, 1007700]], "nextLevel": 217, "id": 76003}, {"cost": [[11, 137101, 440]], "level": 217, "attributes": [[1, 2031300], [2, 20313000], [3, 1015650], [4, 1015650]], "nextLevel": 218, "id": 76003}, {"cost": [[11, 137101, 440]], "level": 218, "attributes": [[1, 2047200], [2, 20472000], [3, 1023600], [4, 1023600]], "nextLevel": 219, "id": 76003}, {"cost": [[11, 137101, 440]], "level": 219, "attributes": [[1, 2063100], [2, 20631000], [3, 1031550], [4, 1031550]], "nextLevel": 220, "id": 76003}, {"cost": [[11, 137101, 440]], "level": 220, "attributes": [[1, 2079000], [2, 20790000], [3, 1039500], [4, 1039500]], "nextLevel": 221, "id": 76003}, {"cost": [[11, 137101, 450]], "level": 221, "attributes": [[1, 2095200], [2, 20952000], [3, 1047600], [4, 1047600]], "nextLevel": 222, "id": 76003}, {"cost": [[11, 137101, 450]], "level": 222, "attributes": [[1, 2111400], [2, 21114000], [3, 1055700], [4, 1055700]], "nextLevel": 223, "id": 76003}, {"cost": [[11, 137101, 450]], "level": 223, "attributes": [[1, 2127600], [2, 21276000], [3, 1063800], [4, 1063800]], "nextLevel": 224, "id": 76003}, {"cost": [[11, 137101, 450]], "level": 224, "attributes": [[1, 2143800], [2, 21438000], [3, 1071900], [4, 1071900]], "nextLevel": 225, "id": 76003}, {"cost": [[11, 137101, 450]], "level": 225, "attributes": [[1, 2160000], [2, 21600000], [3, 1080000], [4, 1080000]], "nextLevel": 226, "id": 76003}, {"cost": [[11, 137101, 460]], "level": 226, "attributes": [[1, 2176500], [2, 21765000], [3, 1088250], [4, 1088250]], "nextLevel": 227, "id": 76003}, {"cost": [[11, 137101, 460]], "level": 227, "attributes": [[1, 2193000], [2, 21930000], [3, 1096500], [4, 1096500]], "nextLevel": 228, "id": 76003}, {"cost": [[11, 137101, 460]], "level": 228, "attributes": [[1, 2209500], [2, 22095000], [3, 1104750], [4, 1104750]], "nextLevel": 229, "id": 76003}, {"cost": [[11, 137101, 460]], "level": 229, "attributes": [[1, 2226000], [2, 22260000], [3, 1113000], [4, 1113000]], "nextLevel": 230, "id": 76003}, {"cost": [[11, 137101, 460]], "level": 230, "attributes": [[1, 2242500], [2, 22425000], [3, 1121250], [4, 1121250]], "nextLevel": 231, "id": 76003}, {"cost": [[11, 137101, 470]], "level": 231, "attributes": [[1, 2259300], [2, 22593000], [3, 1129650], [4, 1129650]], "nextLevel": 232, "id": 76003}, {"cost": [[11, 137101, 470]], "level": 232, "attributes": [[1, 2276100], [2, 22761000], [3, 1138050], [4, 1138050]], "nextLevel": 233, "id": 76003}, {"cost": [[11, 137101, 470]], "level": 233, "attributes": [[1, 2292900], [2, 22929000], [3, 1146450], [4, 1146450]], "nextLevel": 234, "id": 76003}, {"cost": [[11, 137101, 470]], "level": 234, "attributes": [[1, 2309700], [2, 23097000], [3, 1154850], [4, 1154850]], "nextLevel": 235, "id": 76003}, {"cost": [[11, 137101, 470]], "level": 235, "attributes": [[1, 2326500], [2, 23265000], [3, 1163250], [4, 1163250]], "nextLevel": 236, "id": 76003}, {"cost": [[11, 137101, 480]], "level": 236, "attributes": [[1, 2343600], [2, 23436000], [3, 1171800], [4, 1171800]], "nextLevel": 237, "id": 76003}, {"cost": [[11, 137101, 480]], "level": 237, "attributes": [[1, 2360700], [2, 23607000], [3, 1180350], [4, 1180350]], "nextLevel": 238, "id": 76003}, {"cost": [[11, 137101, 480]], "level": 238, "attributes": [[1, 2377800], [2, 23778000], [3, 1188900], [4, 1188900]], "nextLevel": 239, "id": 76003}, {"cost": [[11, 137101, 480]], "level": 239, "attributes": [[1, 2394900], [2, 23949000], [3, 1197450], [4, 1197450]], "nextLevel": 240, "id": 76003}, {"cost": [[11, 137101, 480]], "level": 240, "attributes": [[1, 2412000], [2, 24120000], [3, 1206000], [4, 1206000]], "nextLevel": 241, "id": 76003}, {"cost": [[11, 137101, 490]], "level": 241, "attributes": [[1, 2429400], [2, 24294000], [3, 1214700], [4, 1214700]], "nextLevel": 242, "id": 76003}, {"cost": [[11, 137101, 490]], "level": 242, "attributes": [[1, 2446800], [2, 24468000], [3, 1223400], [4, 1223400]], "nextLevel": 243, "id": 76003}, {"cost": [[11, 137101, 490]], "level": 243, "attributes": [[1, 2464200], [2, 24642000], [3, 1232100], [4, 1232100]], "nextLevel": 244, "id": 76003}, {"cost": [[11, 137101, 490]], "level": 244, "attributes": [[1, 2481600], [2, 24816000], [3, 1240800], [4, 1240800]], "nextLevel": 245, "id": 76003}, {"cost": [[11, 137101, 490]], "level": 245, "attributes": [[1, 2499000], [2, 24990000], [3, 1249500], [4, 1249500]], "nextLevel": 246, "id": 76003}, {"cost": [[11, 137101, 500]], "level": 246, "attributes": [[1, 2516700], [2, 25167000], [3, 1258350], [4, 1258350]], "nextLevel": 247, "id": 76003}, {"cost": [[11, 137101, 500]], "level": 247, "attributes": [[1, 2534400], [2, 25344000], [3, 1267200], [4, 1267200]], "nextLevel": 248, "id": 76003}, {"cost": [[11, 137101, 500]], "level": 248, "attributes": [[1, 2552100], [2, 25521000], [3, 1276050], [4, 1276050]], "nextLevel": 249, "id": 76003}, {"cost": [[11, 137101, 500]], "level": 249, "attributes": [[1, 2569800], [2, 25698000], [3, 1284900], [4, 1284900]], "nextLevel": 250, "id": 76003}, {"cost": [[11, 137101, 500]], "level": 250, "attributes": [[1, 2587500], [2, 25875000], [3, 1293750], [4, 1293750]], "nextLevel": 251, "id": 76003}, {"cost": [[11, 137101, 510]], "level": 251, "attributes": [[1, 2605500], [2, 26055000], [3, 1302750], [4, 1302750]], "nextLevel": 252, "id": 76003}, {"cost": [[11, 137101, 510]], "level": 252, "attributes": [[1, 2623500], [2, 26235000], [3, 1311750], [4, 1311750]], "nextLevel": 253, "id": 76003}, {"cost": [[11, 137101, 510]], "level": 253, "attributes": [[1, 2641500], [2, 26415000], [3, 1320750], [4, 1320750]], "nextLevel": 254, "id": 76003}, {"cost": [[11, 137101, 510]], "level": 254, "attributes": [[1, 2659500], [2, 26595000], [3, 1329750], [4, 1329750]], "nextLevel": 255, "id": 76003}, {"cost": [[11, 137101, 510]], "level": 255, "attributes": [[1, 2677500], [2, 26775000], [3, 1338750], [4, 1338750]], "nextLevel": 256, "id": 76003}, {"cost": [[11, 137101, 520]], "level": 256, "attributes": [[1, 2695800], [2, 26958000], [3, 1347900], [4, 1347900]], "nextLevel": 257, "id": 76003}, {"cost": [[11, 137101, 520]], "level": 257, "attributes": [[1, 2714100], [2, 27141000], [3, 1357050], [4, 1357050]], "nextLevel": 258, "id": 76003}, {"cost": [[11, 137101, 520]], "level": 258, "attributes": [[1, 2732400], [2, 27324000], [3, 1366200], [4, 1366200]], "nextLevel": 259, "id": 76003}, {"cost": [[11, 137101, 520]], "level": 259, "attributes": [[1, 2750700], [2, 27507000], [3, 1375350], [4, 1375350]], "nextLevel": 260, "id": 76003}, {"cost": [[11, 137101, 520]], "level": 260, "attributes": [[1, 2769000], [2, 27690000], [3, 1384500], [4, 1384500]], "nextLevel": 261, "id": 76003}, {"cost": [[11, 137101, 530]], "level": 261, "attributes": [[1, 2787600], [2, 27876000], [3, 1393800], [4, 1393800]], "nextLevel": 262, "id": 76003}, {"cost": [[11, 137101, 530]], "level": 262, "attributes": [[1, 2806200], [2, 28062000], [3, 1403100], [4, 1403100]], "nextLevel": 263, "id": 76003}, {"cost": [[11, 137101, 530]], "level": 263, "attributes": [[1, 2824800], [2, 28248000], [3, 1412400], [4, 1412400]], "nextLevel": 264, "id": 76003}, {"cost": [[11, 137101, 530]], "level": 264, "attributes": [[1, 2843400], [2, 28434000], [3, 1421700], [4, 1421700]], "nextLevel": 265, "id": 76003}, {"cost": [[11, 137101, 530]], "level": 265, "attributes": [[1, 2862000], [2, 28620000], [3, 1431000], [4, 1431000]], "nextLevel": 266, "id": 76003}, {"cost": [[11, 137101, 540]], "level": 266, "attributes": [[1, 2880900], [2, 28809000], [3, 1440450], [4, 1440450]], "nextLevel": 267, "id": 76003}, {"cost": [[11, 137101, 540]], "level": 267, "attributes": [[1, 2899800], [2, 28998000], [3, 1449900], [4, 1449900]], "nextLevel": 268, "id": 76003}, {"cost": [[11, 137101, 540]], "level": 268, "attributes": [[1, 2918700], [2, 29187000], [3, 1459350], [4, 1459350]], "nextLevel": 269, "id": 76003}, {"cost": [[11, 137101, 540]], "level": 269, "attributes": [[1, 2937600], [2, 29376000], [3, 1468800], [4, 1468800]], "nextLevel": 270, "id": 76003}, {"cost": [[11, 137101, 540]], "level": 270, "attributes": [[1, 2956500], [2, 29565000], [3, 1478250], [4, 1478250]], "nextLevel": 271, "id": 76003}, {"cost": [[11, 137101, 550]], "level": 271, "attributes": [[1, 2975700], [2, 29757000], [3, 1487850], [4, 1487850]], "nextLevel": 272, "id": 76003}, {"cost": [[11, 137101, 550]], "level": 272, "attributes": [[1, 2994900], [2, 29949000], [3, 1497450], [4, 1497450]], "nextLevel": 273, "id": 76003}, {"cost": [[11, 137101, 550]], "level": 273, "attributes": [[1, 3014100], [2, 30141000], [3, 1507050], [4, 1507050]], "nextLevel": 274, "id": 76003}, {"cost": [[11, 137101, 550]], "level": 274, "attributes": [[1, 3033300], [2, 30333000], [3, 1516650], [4, 1516650]], "nextLevel": 275, "id": 76003}, {"cost": [[11, 137101, 550]], "level": 275, "attributes": [[1, 3052500], [2, 30525000], [3, 1526250], [4, 1526250]], "nextLevel": 276, "id": 76003}, {"cost": [[11, 137101, 560]], "level": 276, "attributes": [[1, 3072000], [2, 30720000], [3, 1536000], [4, 1536000]], "nextLevel": 277, "id": 76003}, {"cost": [[11, 137101, 560]], "level": 277, "attributes": [[1, 3091500], [2, 30915000], [3, 1545750], [4, 1545750]], "nextLevel": 278, "id": 76003}, {"cost": [[11, 137101, 560]], "level": 278, "attributes": [[1, 3111000], [2, 31110000], [3, 1555500], [4, 1555500]], "nextLevel": 279, "id": 76003}, {"cost": [[11, 137101, 560]], "level": 279, "attributes": [[1, 3130500], [2, 31305000], [3, 1565250], [4, 1565250]], "nextLevel": 280, "id": 76003}, {"cost": [[11, 137101, 560]], "level": 280, "attributes": [[1, 3150000], [2, 31500000], [3, 1575000], [4, 1575000]], "nextLevel": 281, "id": 76003}, {"cost": [[11, 137101, 570]], "level": 281, "attributes": [[1, 3169800], [2, 31698000], [3, 1584900], [4, 1584900]], "nextLevel": 282, "id": 76003}, {"cost": [[11, 137101, 570]], "level": 282, "attributes": [[1, 3189600], [2, 31896000], [3, 1594800], [4, 1594800]], "nextLevel": 283, "id": 76003}, {"cost": [[11, 137101, 570]], "level": 283, "attributes": [[1, 3209400], [2, 32094000], [3, 1604700], [4, 1604700]], "nextLevel": 284, "id": 76003}, {"cost": [[11, 137101, 570]], "level": 284, "attributes": [[1, 3229200], [2, 32292000], [3, 1614600], [4, 1614600]], "nextLevel": 285, "id": 76003}, {"cost": [[11, 137101, 570]], "level": 285, "attributes": [[1, 3249000], [2, 32490000], [3, 1624500], [4, 1624500]], "nextLevel": 286, "id": 76003}, {"cost": [[11, 137101, 580]], "level": 286, "attributes": [[1, 3269100], [2, 32691000], [3, 1634550], [4, 1634550]], "nextLevel": 287, "id": 76003}, {"cost": [[11, 137101, 580]], "level": 287, "attributes": [[1, 3289200], [2, 32892000], [3, 1644600], [4, 1644600]], "nextLevel": 288, "id": 76003}, {"cost": [[11, 137101, 580]], "level": 288, "attributes": [[1, 3309300], [2, 33093000], [3, 1654650], [4, 1654650]], "nextLevel": 289, "id": 76003}, {"cost": [[11, 137101, 580]], "level": 289, "attributes": [[1, 3329400], [2, 33294000], [3, 1664700], [4, 1664700]], "nextLevel": 290, "id": 76003}, {"cost": [[11, 137101, 580]], "level": 290, "attributes": [[1, 3349500], [2, 33495000], [3, 1674750], [4, 1674750]], "nextLevel": 291, "id": 76003}, {"cost": [[11, 137101, 590]], "level": 291, "attributes": [[1, 3369900], [2, 33699000], [3, 1684950], [4, 1684950]], "nextLevel": 292, "id": 76003}, {"cost": [[11, 137101, 590]], "level": 292, "attributes": [[1, 3390300], [2, 33903000], [3, 1695150], [4, 1695150]], "nextLevel": 293, "id": 76003}, {"cost": [[11, 137101, 590]], "level": 293, "attributes": [[1, 3410700], [2, 34107000], [3, 1705350], [4, 1705350]], "nextLevel": 294, "id": 76003}, {"cost": [[11, 137101, 590]], "level": 294, "attributes": [[1, 3431100], [2, 34311000], [3, 1715550], [4, 1715550]], "nextLevel": 295, "id": 76003}, {"cost": [[11, 137101, 590]], "level": 295, "attributes": [[1, 3451500], [2, 34515000], [3, 1725750], [4, 1725750]], "nextLevel": 296, "id": 76003}, {"cost": [[11, 137101, 600]], "level": 296, "attributes": [[1, 3472200], [2, 34722000], [3, 1736100], [4, 1736100]], "nextLevel": 297, "id": 76003}, {"cost": [[11, 137101, 600]], "level": 297, "attributes": [[1, 3492900], [2, 34929000], [3, 1746450], [4, 1746450]], "nextLevel": 298, "id": 76003}, {"cost": [[11, 137101, 600]], "level": 298, "attributes": [[1, 3513600], [2, 35136000], [3, 1756800], [4, 1756800]], "nextLevel": 299, "id": 76003}, {"cost": [[11, 137101, 600]], "level": 299, "attributes": [[1, 3534300], [2, 35343000], [3, 1767150], [4, 1767150]], "nextLevel": 300, "id": 76003}, {"cost": [[11, 137101, 600]], "level": 300, "attributes": [[1, 3555000], [2, 35550000], [3, 1777500], [4, 1777500]], "nextLevel": 301, "id": 76003}, {"cost": [[11, 137101, 610]], "level": 301, "attributes": [[1, 3576000], [2, 35760000], [3, 1788000], [4, 1788000]], "nextLevel": 302, "id": 76003}, {"cost": [[11, 137101, 610]], "level": 302, "attributes": [[1, 3597000], [2, 35970000], [3, 1798500], [4, 1798500]], "nextLevel": 303, "id": 76003}, {"cost": [[11, 137101, 610]], "level": 303, "attributes": [[1, 3618000], [2, 36180000], [3, 1809000], [4, 1809000]], "nextLevel": 304, "id": 76003}, {"cost": [[11, 137101, 610]], "level": 304, "attributes": [[1, 3639000], [2, 36390000], [3, 1819500], [4, 1819500]], "nextLevel": 305, "id": 76003}, {"cost": [[11, 137101, 610]], "level": 305, "attributes": [[1, 3660000], [2, 36600000], [3, 1830000], [4, 1830000]], "nextLevel": 306, "id": 76003}, {"cost": [[11, 137101, 620]], "level": 306, "attributes": [[1, 3681300], [2, 36813000], [3, 1840650], [4, 1840650]], "nextLevel": 307, "id": 76003}, {"cost": [[11, 137101, 620]], "level": 307, "attributes": [[1, 3702600], [2, 37026000], [3, 1851300], [4, 1851300]], "nextLevel": 308, "id": 76003}, {"cost": [[11, 137101, 620]], "level": 308, "attributes": [[1, 3723900], [2, 37239000], [3, 1861950], [4, 1861950]], "nextLevel": 309, "id": 76003}, {"cost": [[11, 137101, 620]], "level": 309, "attributes": [[1, 3745200], [2, 37452000], [3, 1872600], [4, 1872600]], "nextLevel": 310, "id": 76003}, {"cost": [[11, 137101, 620]], "level": 310, "attributes": [[1, 3766500], [2, 37665000], [3, 1883250], [4, 1883250]], "nextLevel": 311, "id": 76003}, {"cost": [[11, 137101, 630]], "level": 311, "attributes": [[1, 3788100], [2, 37881000], [3, 1894050], [4, 1894050]], "nextLevel": 312, "id": 76003}, {"cost": [[11, 137101, 630]], "level": 312, "attributes": [[1, 3809700], [2, 38097000], [3, 1904850], [4, 1904850]], "nextLevel": 313, "id": 76003}, {"cost": [[11, 137101, 630]], "level": 313, "attributes": [[1, 3831300], [2, 38313000], [3, 1915650], [4, 1915650]], "nextLevel": 314, "id": 76003}, {"cost": [[11, 137101, 630]], "level": 314, "attributes": [[1, 3852900], [2, 38529000], [3, 1926450], [4, 1926450]], "nextLevel": 315, "id": 76003}, {"cost": [[11, 137101, 630]], "level": 315, "attributes": [[1, 3874500], [2, 38745000], [3, 1937250], [4, 1937250]], "nextLevel": 316, "id": 76003}, {"cost": [[11, 137101, 640]], "level": 316, "attributes": [[1, 3896400], [2, 38964000], [3, 1948200], [4, 1948200]], "nextLevel": 317, "id": 76003}, {"cost": [[11, 137101, 640]], "level": 317, "attributes": [[1, 3918300], [2, 39183000], [3, 1959150], [4, 1959150]], "nextLevel": 318, "id": 76003}, {"cost": [[11, 137101, 640]], "level": 318, "attributes": [[1, 3940200], [2, 39402000], [3, 1970100], [4, 1970100]], "nextLevel": 319, "id": 76003}, {"cost": [[11, 137101, 640]], "level": 319, "attributes": [[1, 3962100], [2, 39621000], [3, 1981050], [4, 1981050]], "nextLevel": 320, "id": 76003}, {"cost": [[11, 137101, 640]], "level": 320, "attributes": [[1, 3984000], [2, 39840000], [3, 1992000], [4, 1992000]], "nextLevel": 321, "id": 76003}, {"cost": [[11, 137101, 650]], "level": 321, "attributes": [[1, 4006200], [2, 40062000], [3, 2003100], [4, 2003100]], "nextLevel": 322, "id": 76003}, {"cost": [[11, 137101, 650]], "level": 322, "attributes": [[1, 4028400], [2, 40284000], [3, 2014200], [4, 2014200]], "nextLevel": 323, "id": 76003}, {"cost": [[11, 137101, 650]], "level": 323, "attributes": [[1, 4050600], [2, 40506000], [3, 2025300], [4, 2025300]], "nextLevel": 324, "id": 76003}, {"cost": [[11, 137101, 650]], "level": 324, "attributes": [[1, 4072800], [2, 40728000], [3, 2036400], [4, 2036400]], "nextLevel": 325, "id": 76003}, {"cost": [[11, 137101, 650]], "level": 325, "attributes": [[1, 4095000], [2, 40950000], [3, 2047500], [4, 2047500]], "nextLevel": 326, "id": 76003}, {"cost": [[11, 137101, 660]], "level": 326, "attributes": [[1, 4117500], [2, 41175000], [3, 2058750], [4, 2058750]], "nextLevel": 327, "id": 76003}, {"cost": [[11, 137101, 660]], "level": 327, "attributes": [[1, 4140000], [2, 41400000], [3, 2070000], [4, 2070000]], "nextLevel": 328, "id": 76003}, {"cost": [[11, 137101, 660]], "level": 328, "attributes": [[1, 4162500], [2, 41625000], [3, 2081250], [4, 2081250]], "nextLevel": 329, "id": 76003}, {"cost": [[11, 137101, 660]], "level": 329, "attributes": [[1, 4185000], [2, 41850000], [3, 2092500], [4, 2092500]], "nextLevel": 330, "id": 76003}, {"cost": [[11, 137101, 660]], "level": 330, "attributes": [[1, 4207500], [2, 42075000], [3, 2103750], [4, 2103750]], "nextLevel": 331, "id": 76003}, {"cost": [[11, 137101, 670]], "level": 331, "attributes": [[1, 4230300], [2, 42303000], [3, 2115150], [4, 2115150]], "nextLevel": 332, "id": 76003}, {"cost": [[11, 137101, 670]], "level": 332, "attributes": [[1, 4253100], [2, 42531000], [3, 2126550], [4, 2126550]], "nextLevel": 333, "id": 76003}, {"cost": [[11, 137101, 670]], "level": 333, "attributes": [[1, 4275900], [2, 42759000], [3, 2137950], [4, 2137950]], "nextLevel": 334, "id": 76003}, {"cost": [[11, 137101, 670]], "level": 334, "attributes": [[1, 4298700], [2, 42987000], [3, 2149350], [4, 2149350]], "nextLevel": 335, "id": 76003}, {"cost": [[11, 137101, 670]], "level": 335, "attributes": [[1, 4321500], [2, 43215000], [3, 2160750], [4, 2160750]], "nextLevel": 336, "id": 76003}, {"cost": [[11, 137101, 680]], "level": 336, "attributes": [[1, 4344600], [2, 43446000], [3, 2172300], [4, 2172300]], "nextLevel": 337, "id": 76003}, {"cost": [[11, 137101, 680]], "level": 337, "attributes": [[1, 4367700], [2, 43677000], [3, 2183850], [4, 2183850]], "nextLevel": 338, "id": 76003}, {"cost": [[11, 137101, 680]], "level": 338, "attributes": [[1, 4390800], [2, 43908000], [3, 2195400], [4, 2195400]], "nextLevel": 339, "id": 76003}, {"cost": [[11, 137101, 680]], "level": 339, "attributes": [[1, 4413900], [2, 44139000], [3, 2206950], [4, 2206950]], "nextLevel": 340, "id": 76003}, {"cost": [[11, 137101, 680]], "level": 340, "attributes": [[1, 4437000], [2, 44370000], [3, 2218500], [4, 2218500]], "nextLevel": 341, "id": 76003}, {"cost": [[11, 137101, 690]], "level": 341, "attributes": [[1, 4460400], [2, 44604000], [3, 2230200], [4, 2230200]], "nextLevel": 342, "id": 76003}, {"cost": [[11, 137101, 690]], "level": 342, "attributes": [[1, 4483800], [2, 44838000], [3, 2241900], [4, 2241900]], "nextLevel": 343, "id": 76003}, {"cost": [[11, 137101, 690]], "level": 343, "attributes": [[1, 4507200], [2, 45072000], [3, 2253600], [4, 2253600]], "nextLevel": 344, "id": 76003}, {"cost": [[11, 137101, 690]], "level": 344, "attributes": [[1, 4530600], [2, 45306000], [3, 2265300], [4, 2265300]], "nextLevel": 345, "id": 76003}, {"cost": [[11, 137101, 690]], "level": 345, "attributes": [[1, 4554000], [2, 45540000], [3, 2277000], [4, 2277000]], "nextLevel": 346, "id": 76003}, {"cost": [[11, 137101, 700]], "level": 346, "attributes": [[1, 4577700], [2, 45777000], [3, 2288850], [4, 2288850]], "nextLevel": 347, "id": 76003}, {"cost": [[11, 137101, 700]], "level": 347, "attributes": [[1, 4601400], [2, 46014000], [3, 2300700], [4, 2300700]], "nextLevel": 348, "id": 76003}, {"cost": [[11, 137101, 700]], "level": 348, "attributes": [[1, 4625100], [2, 46251000], [3, 2312550], [4, 2312550]], "nextLevel": 349, "id": 76003}, {"cost": [[11, 137101, 700]], "level": 349, "attributes": [[1, 4648800], [2, 46488000], [3, 2324400], [4, 2324400]], "nextLevel": 350, "id": 76003}, {"cost": [[11, 137101, 700]], "level": 350, "attributes": [[1, 4672500], [2, 46725000], [3, 2336250], [4, 2336250]], "nextLevel": 351, "id": 76003}, {"cost": [[11, 137101, 710]], "level": 351, "attributes": [[1, 4696500], [2, 46965000], [3, 2348250], [4, 2348250]], "nextLevel": 352, "id": 76003}, {"cost": [[11, 137101, 710]], "level": 352, "attributes": [[1, 4720500], [2, 47205000], [3, 2360250], [4, 2360250]], "nextLevel": 353, "id": 76003}, {"cost": [[11, 137101, 710]], "level": 353, "attributes": [[1, 4744500], [2, 47445000], [3, 2372250], [4, 2372250]], "nextLevel": 354, "id": 76003}, {"cost": [[11, 137101, 710]], "level": 354, "attributes": [[1, 4768500], [2, 47685000], [3, 2384250], [4, 2384250]], "nextLevel": 355, "id": 76003}, {"cost": [[11, 137101, 710]], "level": 355, "attributes": [[1, 4792500], [2, 47925000], [3, 2396250], [4, 2396250]], "nextLevel": 356, "id": 76003}, {"cost": [[11, 137101, 720]], "level": 356, "attributes": [[1, 4816800], [2, 48168000], [3, 2408400], [4, 2408400]], "nextLevel": 357, "id": 76003}, {"cost": [[11, 137101, 720]], "level": 357, "attributes": [[1, 4841100], [2, 48411000], [3, 2420550], [4, 2420550]], "nextLevel": 358, "id": 76003}, {"cost": [[11, 137101, 720]], "level": 358, "attributes": [[1, 4865400], [2, 48654000], [3, 2432700], [4, 2432700]], "nextLevel": 359, "id": 76003}, {"cost": [[11, 137101, 720]], "level": 359, "attributes": [[1, 4889700], [2, 48897000], [3, 2444850], [4, 2444850]], "nextLevel": 360, "id": 76003}, {"cost": [[11, 137101, 720]], "level": 360, "attributes": [[1, 4914000], [2, 49140000], [3, 2457000], [4, 2457000]], "nextLevel": 361, "id": 76003}, {"cost": [[11, 137101, 730]], "level": 361, "attributes": [[1, 4938600], [2, 49386000], [3, 2469300], [4, 2469300]], "nextLevel": 362, "id": 76003}, {"cost": [[11, 137101, 730]], "level": 362, "attributes": [[1, 4963200], [2, 49632000], [3, 2481600], [4, 2481600]], "nextLevel": 363, "id": 76003}, {"cost": [[11, 137101, 730]], "level": 363, "attributes": [[1, 4987800], [2, 49878000], [3, 2493900], [4, 2493900]], "nextLevel": 364, "id": 76003}, {"cost": [[11, 137101, 730]], "level": 364, "attributes": [[1, 5012400], [2, 50124000], [3, 2506200], [4, 2506200]], "nextLevel": 365, "id": 76003}, {"cost": [[11, 137101, 730]], "level": 365, "attributes": [[1, 5037000], [2, 50370000], [3, 2518500], [4, 2518500]], "nextLevel": 366, "id": 76003}, {"cost": [[11, 137101, 740]], "level": 366, "attributes": [[1, 5061900], [2, 50619000], [3, 2530950], [4, 2530950]], "nextLevel": 367, "id": 76003}, {"cost": [[11, 137101, 740]], "level": 367, "attributes": [[1, 5086800], [2, 50868000], [3, 2543400], [4, 2543400]], "nextLevel": 368, "id": 76003}, {"cost": [[11, 137101, 740]], "level": 368, "attributes": [[1, 5111700], [2, 51117000], [3, 2555850], [4, 2555850]], "nextLevel": 369, "id": 76003}, {"cost": [[11, 137101, 740]], "level": 369, "attributes": [[1, 5136600], [2, 51366000], [3, 2568300], [4, 2568300]], "nextLevel": 370, "id": 76003}, {"cost": [[11, 137101, 740]], "level": 370, "attributes": [[1, 5161500], [2, 51615000], [3, 2580750], [4, 2580750]], "nextLevel": 371, "id": 76003}, {"cost": [[11, 137101, 750]], "level": 371, "attributes": [[1, 5186700], [2, 51867000], [3, 2593350], [4, 2593350]], "nextLevel": 372, "id": 76003}, {"cost": [[11, 137101, 750]], "level": 372, "attributes": [[1, 5211900], [2, 52119000], [3, 2605950], [4, 2605950]], "nextLevel": 373, "id": 76003}, {"cost": [[11, 137101, 750]], "level": 373, "attributes": [[1, 5237100], [2, 52371000], [3, 2618550], [4, 2618550]], "nextLevel": 374, "id": 76003}, {"cost": [[11, 137101, 750]], "level": 374, "attributes": [[1, 5262300], [2, 52623000], [3, 2631150], [4, 2631150]], "nextLevel": 375, "id": 76003}, {"cost": [[11, 137101, 750]], "level": 375, "attributes": [[1, 5287500], [2, 52875000], [3, 2643750], [4, 2643750]], "nextLevel": 376, "id": 76003}, {"cost": [[11, 137101, 760]], "level": 376, "attributes": [[1, 5313000], [2, 53130000], [3, 2656500], [4, 2656500]], "nextLevel": 377, "id": 76003}, {"cost": [[11, 137101, 760]], "level": 377, "attributes": [[1, 5338500], [2, 53385000], [3, 2669250], [4, 2669250]], "nextLevel": 378, "id": 76003}, {"cost": [[11, 137101, 760]], "level": 378, "attributes": [[1, 5364000], [2, 53640000], [3, 2682000], [4, 2682000]], "nextLevel": 379, "id": 76003}, {"cost": [[11, 137101, 760]], "level": 379, "attributes": [[1, 5389500], [2, 53895000], [3, 2694750], [4, 2694750]], "nextLevel": 380, "id": 76003}, {"cost": [[11, 137101, 760]], "level": 380, "attributes": [[1, 5415000], [2, 54150000], [3, 2707500], [4, 2707500]], "nextLevel": 381, "id": 76003}, {"cost": [[11, 137101, 770]], "level": 381, "attributes": [[1, 5440800], [2, 54408000], [3, 2720400], [4, 2720400]], "nextLevel": 382, "id": 76003}, {"cost": [[11, 137101, 770]], "level": 382, "attributes": [[1, 5466600], [2, 54666000], [3, 2733300], [4, 2733300]], "nextLevel": 383, "id": 76003}, {"cost": [[11, 137101, 770]], "level": 383, "attributes": [[1, 5492400], [2, 54924000], [3, 2746200], [4, 2746200]], "nextLevel": 384, "id": 76003}, {"cost": [[11, 137101, 770]], "level": 384, "attributes": [[1, 5518200], [2, 55182000], [3, 2759100], [4, 2759100]], "nextLevel": 385, "id": 76003}, {"cost": [[11, 137101, 770]], "level": 385, "attributes": [[1, 5544000], [2, 55440000], [3, 2772000], [4, 2772000]], "nextLevel": 386, "id": 76003}, {"cost": [[11, 137101, 780]], "level": 386, "attributes": [[1, 5570100], [2, 55701000], [3, 2785050], [4, 2785050]], "nextLevel": 387, "id": 76003}, {"cost": [[11, 137101, 780]], "level": 387, "attributes": [[1, 5596200], [2, 55962000], [3, 2798100], [4, 2798100]], "nextLevel": 388, "id": 76003}, {"cost": [[11, 137101, 780]], "level": 388, "attributes": [[1, 5622300], [2, 56223000], [3, 2811150], [4, 2811150]], "nextLevel": 389, "id": 76003}, {"cost": [[11, 137101, 780]], "level": 389, "attributes": [[1, 5648400], [2, 56484000], [3, 2824200], [4, 2824200]], "nextLevel": 390, "id": 76003}, {"cost": [[11, 137101, 780]], "level": 390, "attributes": [[1, 5674500], [2, 56745000], [3, 2837250], [4, 2837250]], "nextLevel": 391, "id": 76003}, {"cost": [[11, 137101, 790]], "level": 391, "attributes": [[1, 5700900], [2, 57009000], [3, 2850450], [4, 2850450]], "nextLevel": 392, "id": 76003}, {"cost": [[11, 137101, 790]], "level": 392, "attributes": [[1, 5727300], [2, 57273000], [3, 2863650], [4, 2863650]], "nextLevel": 393, "id": 76003}, {"cost": [[11, 137101, 790]], "level": 393, "attributes": [[1, 5753700], [2, 57537000], [3, 2876850], [4, 2876850]], "nextLevel": 394, "id": 76003}, {"cost": [[11, 137101, 790]], "level": 394, "attributes": [[1, 5780100], [2, 57801000], [3, 2890050], [4, 2890050]], "nextLevel": 395, "id": 76003}, {"cost": [[11, 137101, 790]], "level": 395, "attributes": [[1, 5806500], [2, 58065000], [3, 2903250], [4, 2903250]], "nextLevel": 396, "id": 76003}, {"cost": [[11, 137101, 800]], "level": 396, "attributes": [[1, 5833200], [2, 58332000], [3, 2916600], [4, 2916600]], "nextLevel": 397, "id": 76003}, {"cost": [[11, 137101, 800]], "level": 397, "attributes": [[1, 5859900], [2, 58599000], [3, 2929950], [4, 2929950]], "nextLevel": 398, "id": 76003}, {"cost": [[11, 137101, 800]], "level": 398, "attributes": [[1, 5886600], [2, 58866000], [3, 2943300], [4, 2943300]], "nextLevel": 399, "id": 76003}, {"cost": [[11, 137101, 800]], "level": 399, "attributes": [[1, 5913300], [2, 59133000], [3, 2956650], [4, 2956650]], "nextLevel": 400, "id": 76003}, {"cost": [[11, 137101, 800]], "level": 400, "attributes": [[1, 5940000], [2, 59400000], [3, 2970000], [4, 2970000]], "nextLevel": 401, "id": 76003}, {"cost": [[11, 137101, 810]], "level": 401, "attributes": [[1, 5967000], [2, 59670000], [3, 2983500], [4, 2983500]], "nextLevel": 402, "id": 76003}, {"cost": [[11, 137101, 810]], "level": 402, "attributes": [[1, 5994000], [2, 59940000], [3, 2997000], [4, 2997000]], "nextLevel": 403, "id": 76003}, {"cost": [[11, 137101, 810]], "level": 403, "attributes": [[1, 6021000], [2, 60210000], [3, 3010500], [4, 3010500]], "nextLevel": 404, "id": 76003}, {"cost": [[11, 137101, 810]], "level": 404, "attributes": [[1, 6048000], [2, 60480000], [3, 3024000], [4, 3024000]], "nextLevel": 405, "id": 76003}, {"cost": [[11, 137101, 810]], "level": 405, "attributes": [[1, 6075000], [2, 60750000], [3, 3037500], [4, 3037500]], "nextLevel": 406, "id": 76003}, {"cost": [[11, 137101, 820]], "level": 406, "attributes": [[1, 6102300], [2, 61023000], [3, 3051150], [4, 3051150]], "nextLevel": 407, "id": 76003}, {"cost": [[11, 137101, 820]], "level": 407, "attributes": [[1, 6129600], [2, 61296000], [3, 3064800], [4, 3064800]], "nextLevel": 408, "id": 76003}, {"cost": [[11, 137101, 820]], "level": 408, "attributes": [[1, 6156900], [2, 61569000], [3, 3078450], [4, 3078450]], "nextLevel": 409, "id": 76003}, {"cost": [[11, 137101, 820]], "level": 409, "attributes": [[1, 6184200], [2, 61842000], [3, 3092100], [4, 3092100]], "nextLevel": 410, "id": 76003}, {"cost": [[11, 137101, 820]], "level": 410, "attributes": [[1, 6211500], [2, 62115000], [3, 3105750], [4, 3105750]], "nextLevel": 411, "id": 76003}, {"cost": [[11, 137101, 830]], "level": 411, "attributes": [[1, 6239100], [2, 62391000], [3, 3119550], [4, 3119550]], "nextLevel": 412, "id": 76003}, {"cost": [[11, 137101, 830]], "level": 412, "attributes": [[1, 6266700], [2, 62667000], [3, 3133350], [4, 3133350]], "nextLevel": 413, "id": 76003}, {"cost": [[11, 137101, 830]], "level": 413, "attributes": [[1, 6294300], [2, 62943000], [3, 3147150], [4, 3147150]], "nextLevel": 414, "id": 76003}, {"cost": [[11, 137101, 830]], "level": 414, "attributes": [[1, 6321900], [2, 63219000], [3, 3160950], [4, 3160950]], "nextLevel": 415, "id": 76003}, {"cost": [[11, 137101, 830]], "level": 415, "attributes": [[1, 6349500], [2, 63495000], [3, 3174750], [4, 3174750]], "nextLevel": 416, "id": 76003}, {"cost": [[11, 137101, 840]], "level": 416, "attributes": [[1, 6377400], [2, 63774000], [3, 3188700], [4, 3188700]], "nextLevel": 417, "id": 76003}, {"cost": [[11, 137101, 840]], "level": 417, "attributes": [[1, 6405300], [2, 64053000], [3, 3202650], [4, 3202650]], "nextLevel": 418, "id": 76003}, {"cost": [[11, 137101, 840]], "level": 418, "attributes": [[1, 6433200], [2, 64332000], [3, 3216600], [4, 3216600]], "nextLevel": 419, "id": 76003}, {"cost": [[11, 137101, 840]], "level": 419, "attributes": [[1, 6461100], [2, 64611000], [3, 3230550], [4, 3230550]], "nextLevel": 420, "id": 76003}, {"cost": [[11, 137101, 840]], "level": 420, "attributes": [[1, 6489000], [2, 64890000], [3, 3244500], [4, 3244500]], "nextLevel": 421, "id": 76003}, {"cost": [[11, 137101, 850]], "level": 421, "attributes": [[1, 6517200], [2, 65172000], [3, 3258600], [4, 3258600]], "nextLevel": 422, "id": 76003}, {"cost": [[11, 137101, 850]], "level": 422, "attributes": [[1, 6545400], [2, 65454000], [3, 3272700], [4, 3272700]], "nextLevel": 423, "id": 76003}, {"cost": [[11, 137101, 850]], "level": 423, "attributes": [[1, 6573600], [2, 65736000], [3, 3286800], [4, 3286800]], "nextLevel": 424, "id": 76003}, {"cost": [[11, 137101, 850]], "level": 424, "attributes": [[1, 6601800], [2, 66018000], [3, 3300900], [4, 3300900]], "nextLevel": 425, "id": 76003}, {"cost": [[11, 137101, 850]], "level": 425, "attributes": [[1, 6630000], [2, 66300000], [3, 3315000], [4, 3315000]], "nextLevel": 426, "id": 76003}, {"cost": [[11, 137101, 860]], "level": 426, "attributes": [[1, 6658500], [2, 66585000], [3, 3329250], [4, 3329250]], "nextLevel": 427, "id": 76003}, {"cost": [[11, 137101, 860]], "level": 427, "attributes": [[1, 6687000], [2, 66870000], [3, 3343500], [4, 3343500]], "nextLevel": 428, "id": 76003}, {"cost": [[11, 137101, 860]], "level": 428, "attributes": [[1, 6715500], [2, 67155000], [3, 3357750], [4, 3357750]], "nextLevel": 429, "id": 76003}, {"cost": [[11, 137101, 860]], "level": 429, "attributes": [[1, 6744000], [2, 67440000], [3, 3372000], [4, 3372000]], "nextLevel": 430, "id": 76003}, {"cost": [[11, 137101, 860]], "level": 430, "attributes": [[1, 6772500], [2, 67725000], [3, 3386250], [4, 3386250]], "nextLevel": 431, "id": 76003}, {"cost": [[11, 137101, 870]], "level": 431, "attributes": [[1, 6801300], [2, 68013000], [3, 3400650], [4, 3400650]], "nextLevel": 432, "id": 76003}, {"cost": [[11, 137101, 870]], "level": 432, "attributes": [[1, 6830100], [2, 68301000], [3, 3415050], [4, 3415050]], "nextLevel": 433, "id": 76003}, {"cost": [[11, 137101, 870]], "level": 433, "attributes": [[1, 6858900], [2, 68589000], [3, 3429450], [4, 3429450]], "nextLevel": 434, "id": 76003}, {"cost": [[11, 137101, 870]], "level": 434, "attributes": [[1, 6887700], [2, 68877000], [3, 3443850], [4, 3443850]], "nextLevel": 435, "id": 76003}, {"cost": [[11, 137101, 870]], "level": 435, "attributes": [[1, 6916500], [2, 69165000], [3, 3458250], [4, 3458250]], "nextLevel": 436, "id": 76003}, {"cost": [[11, 137101, 880]], "level": 436, "attributes": [[1, 6945600], [2, 69456000], [3, 3472800], [4, 3472800]], "nextLevel": 437, "id": 76003}, {"cost": [[11, 137101, 880]], "level": 437, "attributes": [[1, 6974700], [2, 69747000], [3, 3487350], [4, 3487350]], "nextLevel": 438, "id": 76003}, {"cost": [[11, 137101, 880]], "level": 438, "attributes": [[1, 7003800], [2, 70038000], [3, 3501900], [4, 3501900]], "nextLevel": 439, "id": 76003}, {"cost": [[11, 137101, 880]], "level": 439, "attributes": [[1, 7032900], [2, 70329000], [3, 3516450], [4, 3516450]], "nextLevel": 440, "id": 76003}, {"cost": [[11, 137101, 880]], "level": 440, "attributes": [[1, 7062000], [2, 70620000], [3, 3531000], [4, 3531000]], "nextLevel": 441, "id": 76003}, {"cost": [[11, 137101, 890]], "level": 441, "attributes": [[1, 7091400], [2, 70914000], [3, 3545700], [4, 3545700]], "nextLevel": 442, "id": 76003}, {"cost": [[11, 137101, 890]], "level": 442, "attributes": [[1, 7120800], [2, 71208000], [3, 3560400], [4, 3560400]], "nextLevel": 443, "id": 76003}, {"cost": [[11, 137101, 890]], "level": 443, "attributes": [[1, 7150200], [2, 71502000], [3, 3575100], [4, 3575100]], "nextLevel": 444, "id": 76003}, {"cost": [[11, 137101, 890]], "level": 444, "attributes": [[1, 7179600], [2, 71796000], [3, 3589800], [4, 3589800]], "nextLevel": 445, "id": 76003}, {"cost": [[11, 137101, 890]], "level": 445, "attributes": [[1, 7209000], [2, 72090000], [3, 3604500], [4, 3604500]], "nextLevel": 446, "id": 76003}, {"cost": [[11, 137101, 900]], "level": 446, "attributes": [[1, 7238700], [2, 72387000], [3, 3619350], [4, 3619350]], "nextLevel": 447, "id": 76003}, {"cost": [[11, 137101, 900]], "level": 447, "attributes": [[1, 7268400], [2, 72684000], [3, 3634200], [4, 3634200]], "nextLevel": 448, "id": 76003}, {"cost": [[11, 137101, 900]], "level": 448, "attributes": [[1, 7298100], [2, 72981000], [3, 3649050], [4, 3649050]], "nextLevel": 449, "id": 76003}, {"cost": [[11, 137101, 900]], "level": 449, "attributes": [[1, 7327800], [2, 73278000], [3, 3663900], [4, 3663900]], "nextLevel": 450, "id": 76003}, {"cost": [[11, 137101, 900]], "level": 450, "attributes": [[1, 7357500], [2, 73575000], [3, 3678750], [4, 3678750]], "nextLevel": 451, "id": 76003}, {"cost": [[11, 137101, 910]], "level": 451, "attributes": [[1, 7387500], [2, 73875000], [3, 3693750], [4, 3693750]], "nextLevel": 452, "id": 76003}, {"cost": [[11, 137101, 910]], "level": 452, "attributes": [[1, 7417500], [2, 74175000], [3, 3708750], [4, 3708750]], "nextLevel": 453, "id": 76003}, {"cost": [[11, 137101, 910]], "level": 453, "attributes": [[1, 7447500], [2, 74475000], [3, 3723750], [4, 3723750]], "nextLevel": 454, "id": 76003}, {"cost": [[11, 137101, 910]], "level": 454, "attributes": [[1, 7477500], [2, 74775000], [3, 3738750], [4, 3738750]], "nextLevel": 455, "id": 76003}, {"cost": [[11, 137101, 910]], "level": 455, "attributes": [[1, 7507500], [2, 75075000], [3, 3753750], [4, 3753750]], "nextLevel": 456, "id": 76003}, {"cost": [[11, 137101, 920]], "level": 456, "attributes": [[1, 7537800], [2, 75378000], [3, 3768900], [4, 3768900]], "nextLevel": 457, "id": 76003}, {"cost": [[11, 137101, 920]], "level": 457, "attributes": [[1, 7568100], [2, 75681000], [3, 3784050], [4, 3784050]], "nextLevel": 458, "id": 76003}, {"cost": [[11, 137101, 920]], "level": 458, "attributes": [[1, 7598400], [2, 75984000], [3, 3799200], [4, 3799200]], "nextLevel": 459, "id": 76003}, {"cost": [[11, 137101, 920]], "level": 459, "attributes": [[1, 7628700], [2, 76287000], [3, 3814350], [4, 3814350]], "nextLevel": 460, "id": 76003}, {"cost": [[11, 137101, 920]], "level": 460, "attributes": [[1, 7659000], [2, 76590000], [3, 3829500], [4, 3829500]], "nextLevel": 461, "id": 76003}, {"cost": [[11, 137101, 930]], "level": 461, "attributes": [[1, 7689600], [2, 76896000], [3, 3844800], [4, 3844800]], "nextLevel": 462, "id": 76003}, {"cost": [[11, 137101, 930]], "level": 462, "attributes": [[1, 7720200], [2, 77202000], [3, 3860100], [4, 3860100]], "nextLevel": 463, "id": 76003}, {"cost": [[11, 137101, 930]], "level": 463, "attributes": [[1, 7750800], [2, 77508000], [3, 3875400], [4, 3875400]], "nextLevel": 464, "id": 76003}, {"cost": [[11, 137101, 930]], "level": 464, "attributes": [[1, 7781400], [2, 77814000], [3, 3890700], [4, 3890700]], "nextLevel": 465, "id": 76003}, {"cost": [[11, 137101, 930]], "level": 465, "attributes": [[1, 7812000], [2, 78120000], [3, 3906000], [4, 3906000]], "nextLevel": 466, "id": 76003}, {"cost": [[11, 137101, 940]], "level": 466, "attributes": [[1, 7842900], [2, 78429000], [3, 3921450], [4, 3921450]], "nextLevel": 467, "id": 76003}, {"cost": [[11, 137101, 940]], "level": 467, "attributes": [[1, 7873800], [2, 78738000], [3, 3936900], [4, 3936900]], "nextLevel": 468, "id": 76003}, {"cost": [[11, 137101, 940]], "level": 468, "attributes": [[1, 7904700], [2, 79047000], [3, 3952350], [4, 3952350]], "nextLevel": 469, "id": 76003}, {"cost": [[11, 137101, 940]], "level": 469, "attributes": [[1, 7935600], [2, 79356000], [3, 3967800], [4, 3967800]], "nextLevel": 470, "id": 76003}, {"cost": [[11, 137101, 940]], "level": 470, "attributes": [[1, 7966500], [2, 79665000], [3, 3983250], [4, 3983250]], "nextLevel": 471, "id": 76003}, {"cost": [[11, 137101, 950]], "level": 471, "attributes": [[1, 7997700], [2, 79977000], [3, 3998850], [4, 3998850]], "nextLevel": 472, "id": 76003}, {"cost": [[11, 137101, 950]], "level": 472, "attributes": [[1, 8028900], [2, 80289000], [3, 4014450], [4, 4014450]], "nextLevel": 473, "id": 76003}, {"cost": [[11, 137101, 950]], "level": 473, "attributes": [[1, 8060100], [2, 80601000], [3, 4030050], [4, 4030050]], "nextLevel": 474, "id": 76003}, {"cost": [[11, 137101, 950]], "level": 474, "attributes": [[1, 8091300], [2, 80913000], [3, 4045650], [4, 4045650]], "nextLevel": 475, "id": 76003}, {"cost": [[11, 137101, 950]], "level": 475, "attributes": [[1, 8122500], [2, 81225000], [3, 4061250], [4, 4061250]], "nextLevel": 476, "id": 76003}, {"cost": [[11, 137101, 960]], "level": 476, "attributes": [[1, 8154000], [2, 81540000], [3, 4077000], [4, 4077000]], "nextLevel": 477, "id": 76003}, {"cost": [[11, 137101, 960]], "level": 477, "attributes": [[1, 8185500], [2, 81855000], [3, 4092750], [4, 4092750]], "nextLevel": 478, "id": 76003}, {"cost": [[11, 137101, 960]], "level": 478, "attributes": [[1, 8217000], [2, 82170000], [3, 4108500], [4, 4108500]], "nextLevel": 479, "id": 76003}, {"cost": [[11, 137101, 960]], "level": 479, "attributes": [[1, 8248500], [2, 82485000], [3, 4124250], [4, 4124250]], "nextLevel": 480, "id": 76003}, {"cost": [[11, 137101, 960]], "level": 480, "attributes": [[1, 8280000], [2, 82800000], [3, 4140000], [4, 4140000]], "nextLevel": 481, "id": 76003}, {"cost": [[11, 137101, 970]], "level": 481, "attributes": [[1, 8311800], [2, 83118000], [3, 4155900], [4, 4155900]], "nextLevel": 482, "id": 76003}, {"cost": [[11, 137101, 970]], "level": 482, "attributes": [[1, 8343600], [2, 83436000], [3, 4171800], [4, 4171800]], "nextLevel": 483, "id": 76003}, {"cost": [[11, 137101, 970]], "level": 483, "attributes": [[1, 8375400], [2, 83754000], [3, 4187700], [4, 4187700]], "nextLevel": 484, "id": 76003}, {"cost": [[11, 137101, 970]], "level": 484, "attributes": [[1, 8407200], [2, 84072000], [3, 4203600], [4, 4203600]], "nextLevel": 485, "id": 76003}, {"cost": [[11, 137101, 970]], "level": 485, "attributes": [[1, 8439000], [2, 84390000], [3, 4219500], [4, 4219500]], "nextLevel": 486, "id": 76003}, {"cost": [[11, 137101, 980]], "level": 486, "attributes": [[1, 8471100], [2, 84711000], [3, 4235550], [4, 4235550]], "nextLevel": 487, "id": 76003}, {"cost": [[11, 137101, 980]], "level": 487, "attributes": [[1, 8503200], [2, 85032000], [3, 4251600], [4, 4251600]], "nextLevel": 488, "id": 76003}, {"cost": [[11, 137101, 980]], "level": 488, "attributes": [[1, 8535300], [2, 85353000], [3, 4267650], [4, 4267650]], "nextLevel": 489, "id": 76003}, {"cost": [[11, 137101, 980]], "level": 489, "attributes": [[1, 8567400], [2, 85674000], [3, 4283700], [4, 4283700]], "nextLevel": 490, "id": 76003}, {"cost": [[11, 137101, 980]], "level": 490, "attributes": [[1, 8599500], [2, 85995000], [3, 4299750], [4, 4299750]], "nextLevel": 491, "id": 76003}, {"cost": [[11, 137101, 990]], "level": 491, "attributes": [[1, 8631900], [2, 86319000], [3, 4315950], [4, 4315950]], "nextLevel": 492, "id": 76003}, {"cost": [[11, 137101, 990]], "level": 492, "attributes": [[1, 8664300], [2, 86643000], [3, 4332150], [4, 4332150]], "nextLevel": 493, "id": 76003}, {"cost": [[11, 137101, 990]], "level": 493, "attributes": [[1, 8696700], [2, 86967000], [3, 4348350], [4, 4348350]], "nextLevel": 494, "id": 76003}, {"cost": [[11, 137101, 990]], "level": 494, "attributes": [[1, 8729100], [2, 87291000], [3, 4364550], [4, 4364550]], "nextLevel": 495, "id": 76003}, {"cost": [[11, 137101, 990]], "level": 495, "attributes": [[1, 8761500], [2, 87615000], [3, 4380750], [4, 4380750]], "nextLevel": 496, "id": 76003}, {"cost": [[11, 137101, 1000]], "level": 496, "attributes": [[1, 8794200], [2, 87942000], [3, 4397100], [4, 4397100]], "nextLevel": 497, "id": 76003}, {"cost": [[11, 137101, 1000]], "level": 497, "attributes": [[1, 8826900], [2, 88269000], [3, 4413450], [4, 4413450]], "nextLevel": 498, "id": 76003}, {"cost": [[11, 137101, 1000]], "level": 498, "attributes": [[1, 8859600], [2, 88596000], [3, 4429800], [4, 4429800]], "nextLevel": 499, "id": 76003}, {"cost": [[11, 137101, 1000]], "level": 499, "attributes": [[1, 8892300], [2, 88923000], [3, 4446150], [4, 4446150]], "nextLevel": 500, "id": 76003}, {"cost": [], "level": 500, "attributes": [[1, 8925000], [2, 89250000], [3, 4462500], [4, 4462500]], "nextLevel": 0, "id": 76003}, {"cost": [[11, 137101, 10]], "level": 1, "attributes": [[1, 3000], [2, 30000], [3, 1500], [4, 1500]], "nextLevel": 2, "id": 76004}, {"cost": [[11, 137101, 10]], "level": 2, "attributes": [[1, 6000], [2, 60000], [3, 3000], [4, 3000]], "nextLevel": 3, "id": 76004}, {"cost": [[11, 137101, 10]], "level": 3, "attributes": [[1, 9000], [2, 90000], [3, 4500], [4, 4500]], "nextLevel": 4, "id": 76004}, {"cost": [[11, 137101, 10]], "level": 4, "attributes": [[1, 12000], [2, 120000], [3, 6000], [4, 6000]], "nextLevel": 5, "id": 76004}, {"cost": [[11, 137101, 10]], "level": 5, "attributes": [[1, 15000], [2, 150000], [3, 7500], [4, 7500]], "nextLevel": 6, "id": 76004}, {"cost": [[11, 137101, 20]], "level": 6, "attributes": [[1, 18300], [2, 183000], [3, 9150], [4, 9150]], "nextLevel": 7, "id": 76004}, {"cost": [[11, 137101, 20]], "level": 7, "attributes": [[1, 21600], [2, 216000], [3, 10800], [4, 10800]], "nextLevel": 8, "id": 76004}, {"cost": [[11, 137101, 20]], "level": 8, "attributes": [[1, 24900], [2, 249000], [3, 12450], [4, 12450]], "nextLevel": 9, "id": 76004}, {"cost": [[11, 137101, 20]], "level": 9, "attributes": [[1, 28200], [2, 282000], [3, 14100], [4, 14100]], "nextLevel": 10, "id": 76004}, {"cost": [[11, 137101, 20]], "level": 10, "attributes": [[1, 31500], [2, 315000], [3, 15750], [4, 15750]], "nextLevel": 11, "id": 76004}, {"cost": [[11, 137101, 30]], "level": 11, "attributes": [[1, 35100], [2, 351000], [3, 17550], [4, 17550]], "nextLevel": 12, "id": 76004}, {"cost": [[11, 137101, 30]], "level": 12, "attributes": [[1, 38700], [2, 387000], [3, 19350], [4, 19350]], "nextLevel": 13, "id": 76004}, {"cost": [[11, 137101, 30]], "level": 13, "attributes": [[1, 42300], [2, 423000], [3, 21150], [4, 21150]], "nextLevel": 14, "id": 76004}, {"cost": [[11, 137101, 30]], "level": 14, "attributes": [[1, 45900], [2, 459000], [3, 22950], [4, 22950]], "nextLevel": 15, "id": 76004}, {"cost": [[11, 137101, 30]], "level": 15, "attributes": [[1, 49500], [2, 495000], [3, 24750], [4, 24750]], "nextLevel": 16, "id": 76004}, {"cost": [[11, 137101, 40]], "level": 16, "attributes": [[1, 53400], [2, 534000], [3, 26700], [4, 26700]], "nextLevel": 17, "id": 76004}, {"cost": [[11, 137101, 40]], "level": 17, "attributes": [[1, 57300], [2, 573000], [3, 28650], [4, 28650]], "nextLevel": 18, "id": 76004}, {"cost": [[11, 137101, 40]], "level": 18, "attributes": [[1, 61200], [2, 612000], [3, 30600], [4, 30600]], "nextLevel": 19, "id": 76004}, {"cost": [[11, 137101, 40]], "level": 19, "attributes": [[1, 65100], [2, 651000], [3, 32550], [4, 32550]], "nextLevel": 20, "id": 76004}, {"cost": [[11, 137101, 40]], "level": 20, "attributes": [[1, 69000], [2, 690000], [3, 34500], [4, 34500]], "nextLevel": 21, "id": 76004}, {"cost": [[11, 137101, 50]], "level": 21, "attributes": [[1, 73200], [2, 732000], [3, 36600], [4, 36600]], "nextLevel": 22, "id": 76004}, {"cost": [[11, 137101, 50]], "level": 22, "attributes": [[1, 77400], [2, 774000], [3, 38700], [4, 38700]], "nextLevel": 23, "id": 76004}, {"cost": [[11, 137101, 50]], "level": 23, "attributes": [[1, 81600], [2, 816000], [3, 40800], [4, 40800]], "nextLevel": 24, "id": 76004}, {"cost": [[11, 137101, 50]], "level": 24, "attributes": [[1, 85800], [2, 858000], [3, 42900], [4, 42900]], "nextLevel": 25, "id": 76004}, {"cost": [[11, 137101, 50]], "level": 25, "attributes": [[1, 90000], [2, 900000], [3, 45000], [4, 45000]], "nextLevel": 26, "id": 76004}, {"cost": [[11, 137101, 60]], "level": 26, "attributes": [[1, 94500], [2, 945000], [3, 47250], [4, 47250]], "nextLevel": 27, "id": 76004}, {"cost": [[11, 137101, 60]], "level": 27, "attributes": [[1, 99000], [2, 990000], [3, 49500], [4, 49500]], "nextLevel": 28, "id": 76004}, {"cost": [[11, 137101, 60]], "level": 28, "attributes": [[1, 103500], [2, 1035000], [3, 51750], [4, 51750]], "nextLevel": 29, "id": 76004}, {"cost": [[11, 137101, 60]], "level": 29, "attributes": [[1, 108000], [2, 1080000], [3, 54000], [4, 54000]], "nextLevel": 30, "id": 76004}, {"cost": [[11, 137101, 60]], "level": 30, "attributes": [[1, 112500], [2, 1125000], [3, 56250], [4, 56250]], "nextLevel": 31, "id": 76004}, {"cost": [[11, 137101, 70]], "level": 31, "attributes": [[1, 117300], [2, 1173000], [3, 58650], [4, 58650]], "nextLevel": 32, "id": 76004}, {"cost": [[11, 137101, 70]], "level": 32, "attributes": [[1, 122100], [2, 1221000], [3, 61050], [4, 61050]], "nextLevel": 33, "id": 76004}, {"cost": [[11, 137101, 70]], "level": 33, "attributes": [[1, 126900], [2, 1269000], [3, 63450], [4, 63450]], "nextLevel": 34, "id": 76004}, {"cost": [[11, 137101, 70]], "level": 34, "attributes": [[1, 131700], [2, 1317000], [3, 65850], [4, 65850]], "nextLevel": 35, "id": 76004}, {"cost": [[11, 137101, 70]], "level": 35, "attributes": [[1, 136500], [2, 1365000], [3, 68250], [4, 68250]], "nextLevel": 36, "id": 76004}, {"cost": [[11, 137101, 80]], "level": 36, "attributes": [[1, 141600], [2, 1416000], [3, 70800], [4, 70800]], "nextLevel": 37, "id": 76004}, {"cost": [[11, 137101, 80]], "level": 37, "attributes": [[1, 146700], [2, 1467000], [3, 73350], [4, 73350]], "nextLevel": 38, "id": 76004}, {"cost": [[11, 137101, 80]], "level": 38, "attributes": [[1, 151800], [2, 1518000], [3, 75900], [4, 75900]], "nextLevel": 39, "id": 76004}, {"cost": [[11, 137101, 80]], "level": 39, "attributes": [[1, 156900], [2, 1569000], [3, 78450], [4, 78450]], "nextLevel": 40, "id": 76004}, {"cost": [[11, 137101, 80]], "level": 40, "attributes": [[1, 162000], [2, 1620000], [3, 81000], [4, 81000]], "nextLevel": 41, "id": 76004}, {"cost": [[11, 137101, 90]], "level": 41, "attributes": [[1, 167400], [2, 1674000], [3, 83700], [4, 83700]], "nextLevel": 42, "id": 76004}, {"cost": [[11, 137101, 90]], "level": 42, "attributes": [[1, 172800], [2, 1728000], [3, 86400], [4, 86400]], "nextLevel": 43, "id": 76004}, {"cost": [[11, 137101, 90]], "level": 43, "attributes": [[1, 178200], [2, 1782000], [3, 89100], [4, 89100]], "nextLevel": 44, "id": 76004}, {"cost": [[11, 137101, 90]], "level": 44, "attributes": [[1, 183600], [2, 1836000], [3, 91800], [4, 91800]], "nextLevel": 45, "id": 76004}, {"cost": [[11, 137101, 90]], "level": 45, "attributes": [[1, 189000], [2, 1890000], [3, 94500], [4, 94500]], "nextLevel": 46, "id": 76004}, {"cost": [[11, 137101, 100]], "level": 46, "attributes": [[1, 194700], [2, 1947000], [3, 97350], [4, 97350]], "nextLevel": 47, "id": 76004}, {"cost": [[11, 137101, 100]], "level": 47, "attributes": [[1, 200400], [2, 2004000], [3, 100200], [4, 100200]], "nextLevel": 48, "id": 76004}, {"cost": [[11, 137101, 100]], "level": 48, "attributes": [[1, 206100], [2, 2061000], [3, 103050], [4, 103050]], "nextLevel": 49, "id": 76004}, {"cost": [[11, 137101, 100]], "level": 49, "attributes": [[1, 211800], [2, 2118000], [3, 105900], [4, 105900]], "nextLevel": 50, "id": 76004}, {"cost": [[11, 137101, 100]], "level": 50, "attributes": [[1, 217500], [2, 2175000], [3, 108750], [4, 108750]], "nextLevel": 51, "id": 76004}, {"cost": [[11, 137101, 110]], "level": 51, "attributes": [[1, 223500], [2, 2235000], [3, 111750], [4, 111750]], "nextLevel": 52, "id": 76004}, {"cost": [[11, 137101, 110]], "level": 52, "attributes": [[1, 229500], [2, 2295000], [3, 114750], [4, 114750]], "nextLevel": 53, "id": 76004}, {"cost": [[11, 137101, 110]], "level": 53, "attributes": [[1, 235500], [2, 2355000], [3, 117750], [4, 117750]], "nextLevel": 54, "id": 76004}, {"cost": [[11, 137101, 110]], "level": 54, "attributes": [[1, 241500], [2, 2415000], [3, 120750], [4, 120750]], "nextLevel": 55, "id": 76004}, {"cost": [[11, 137101, 110]], "level": 55, "attributes": [[1, 247500], [2, 2475000], [3, 123750], [4, 123750]], "nextLevel": 56, "id": 76004}, {"cost": [[11, 137101, 120]], "level": 56, "attributes": [[1, 253800], [2, 2538000], [3, 126900], [4, 126900]], "nextLevel": 57, "id": 76004}, {"cost": [[11, 137101, 120]], "level": 57, "attributes": [[1, 260100], [2, 2601000], [3, 130050], [4, 130050]], "nextLevel": 58, "id": 76004}, {"cost": [[11, 137101, 120]], "level": 58, "attributes": [[1, 266400], [2, 2664000], [3, 133200], [4, 133200]], "nextLevel": 59, "id": 76004}, {"cost": [[11, 137101, 120]], "level": 59, "attributes": [[1, 272700], [2, 2727000], [3, 136350], [4, 136350]], "nextLevel": 60, "id": 76004}, {"cost": [[11, 137101, 120]], "level": 60, "attributes": [[1, 279000], [2, 2790000], [3, 139500], [4, 139500]], "nextLevel": 61, "id": 76004}, {"cost": [[11, 137101, 130]], "level": 61, "attributes": [[1, 285600], [2, 2856000], [3, 142800], [4, 142800]], "nextLevel": 62, "id": 76004}, {"cost": [[11, 137101, 130]], "level": 62, "attributes": [[1, 292200], [2, 2922000], [3, 146100], [4, 146100]], "nextLevel": 63, "id": 76004}, {"cost": [[11, 137101, 130]], "level": 63, "attributes": [[1, 298800], [2, 2988000], [3, 149400], [4, 149400]], "nextLevel": 64, "id": 76004}, {"cost": [[11, 137101, 130]], "level": 64, "attributes": [[1, 305400], [2, 3054000], [3, 152700], [4, 152700]], "nextLevel": 65, "id": 76004}, {"cost": [[11, 137101, 130]], "level": 65, "attributes": [[1, 312000], [2, 3120000], [3, 156000], [4, 156000]], "nextLevel": 66, "id": 76004}, {"cost": [[11, 137101, 140]], "level": 66, "attributes": [[1, 318900], [2, 3189000], [3, 159450], [4, 159450]], "nextLevel": 67, "id": 76004}, {"cost": [[11, 137101, 140]], "level": 67, "attributes": [[1, 325800], [2, 3258000], [3, 162900], [4, 162900]], "nextLevel": 68, "id": 76004}, {"cost": [[11, 137101, 140]], "level": 68, "attributes": [[1, 332700], [2, 3327000], [3, 166350], [4, 166350]], "nextLevel": 69, "id": 76004}, {"cost": [[11, 137101, 140]], "level": 69, "attributes": [[1, 339600], [2, 3396000], [3, 169800], [4, 169800]], "nextLevel": 70, "id": 76004}, {"cost": [[11, 137101, 140]], "level": 70, "attributes": [[1, 346500], [2, 3465000], [3, 173250], [4, 173250]], "nextLevel": 71, "id": 76004}, {"cost": [[11, 137101, 150]], "level": 71, "attributes": [[1, 353700], [2, 3537000], [3, 176850], [4, 176850]], "nextLevel": 72, "id": 76004}, {"cost": [[11, 137101, 150]], "level": 72, "attributes": [[1, 360900], [2, 3609000], [3, 180450], [4, 180450]], "nextLevel": 73, "id": 76004}, {"cost": [[11, 137101, 150]], "level": 73, "attributes": [[1, 368100], [2, 3681000], [3, 184050], [4, 184050]], "nextLevel": 74, "id": 76004}, {"cost": [[11, 137101, 150]], "level": 74, "attributes": [[1, 375300], [2, 3753000], [3, 187650], [4, 187650]], "nextLevel": 75, "id": 76004}, {"cost": [[11, 137101, 150]], "level": 75, "attributes": [[1, 382500], [2, 3825000], [3, 191250], [4, 191250]], "nextLevel": 76, "id": 76004}, {"cost": [[11, 137101, 160]], "level": 76, "attributes": [[1, 390000], [2, 3900000], [3, 195000], [4, 195000]], "nextLevel": 77, "id": 76004}, {"cost": [[11, 137101, 160]], "level": 77, "attributes": [[1, 397500], [2, 3975000], [3, 198750], [4, 198750]], "nextLevel": 78, "id": 76004}, {"cost": [[11, 137101, 160]], "level": 78, "attributes": [[1, 405000], [2, 4050000], [3, 202500], [4, 202500]], "nextLevel": 79, "id": 76004}, {"cost": [[11, 137101, 160]], "level": 79, "attributes": [[1, 412500], [2, 4125000], [3, 206250], [4, 206250]], "nextLevel": 80, "id": 76004}, {"cost": [[11, 137101, 160]], "level": 80, "attributes": [[1, 420000], [2, 4200000], [3, 210000], [4, 210000]], "nextLevel": 81, "id": 76004}, {"cost": [[11, 137101, 170]], "level": 81, "attributes": [[1, 427800], [2, 4278000], [3, 213900], [4, 213900]], "nextLevel": 82, "id": 76004}, {"cost": [[11, 137101, 170]], "level": 82, "attributes": [[1, 435600], [2, 4356000], [3, 217800], [4, 217800]], "nextLevel": 83, "id": 76004}, {"cost": [[11, 137101, 170]], "level": 83, "attributes": [[1, 443400], [2, 4434000], [3, 221700], [4, 221700]], "nextLevel": 84, "id": 76004}, {"cost": [[11, 137101, 170]], "level": 84, "attributes": [[1, 451200], [2, 4512000], [3, 225600], [4, 225600]], "nextLevel": 85, "id": 76004}, {"cost": [[11, 137101, 170]], "level": 85, "attributes": [[1, 459000], [2, 4590000], [3, 229500], [4, 229500]], "nextLevel": 86, "id": 76004}, {"cost": [[11, 137101, 180]], "level": 86, "attributes": [[1, 467100], [2, 4671000], [3, 233550], [4, 233550]], "nextLevel": 87, "id": 76004}, {"cost": [[11, 137101, 180]], "level": 87, "attributes": [[1, 475200], [2, 4752000], [3, 237600], [4, 237600]], "nextLevel": 88, "id": 76004}, {"cost": [[11, 137101, 180]], "level": 88, "attributes": [[1, 483300], [2, 4833000], [3, 241650], [4, 241650]], "nextLevel": 89, "id": 76004}, {"cost": [[11, 137101, 180]], "level": 89, "attributes": [[1, 491400], [2, 4914000], [3, 245700], [4, 245700]], "nextLevel": 90, "id": 76004}, {"cost": [[11, 137101, 180]], "level": 90, "attributes": [[1, 499500], [2, 4995000], [3, 249750], [4, 249750]], "nextLevel": 91, "id": 76004}, {"cost": [[11, 137101, 190]], "level": 91, "attributes": [[1, 507900], [2, 5079000], [3, 253950], [4, 253950]], "nextLevel": 92, "id": 76004}, {"cost": [[11, 137101, 190]], "level": 92, "attributes": [[1, 516300], [2, 5163000], [3, 258150], [4, 258150]], "nextLevel": 93, "id": 76004}, {"cost": [[11, 137101, 190]], "level": 93, "attributes": [[1, 524700], [2, 5247000], [3, 262350], [4, 262350]], "nextLevel": 94, "id": 76004}, {"cost": [[11, 137101, 190]], "level": 94, "attributes": [[1, 533100], [2, 5331000], [3, 266550], [4, 266550]], "nextLevel": 95, "id": 76004}, {"cost": [[11, 137101, 190]], "level": 95, "attributes": [[1, 541500], [2, 5415000], [3, 270750], [4, 270750]], "nextLevel": 96, "id": 76004}, {"cost": [[11, 137101, 200]], "level": 96, "attributes": [[1, 550200], [2, 5502000], [3, 275100], [4, 275100]], "nextLevel": 97, "id": 76004}, {"cost": [[11, 137101, 200]], "level": 97, "attributes": [[1, 558900], [2, 5589000], [3, 279450], [4, 279450]], "nextLevel": 98, "id": 76004}, {"cost": [[11, 137101, 200]], "level": 98, "attributes": [[1, 567600], [2, 5676000], [3, 283800], [4, 283800]], "nextLevel": 99, "id": 76004}, {"cost": [[11, 137101, 200]], "level": 99, "attributes": [[1, 576300], [2, 5763000], [3, 288150], [4, 288150]], "nextLevel": 100, "id": 76004}, {"cost": [[11, 137101, 200]], "level": 100, "attributes": [[1, 585000], [2, 5850000], [3, 292500], [4, 292500]], "nextLevel": 101, "id": 76004}, {"cost": [[11, 137101, 210]], "level": 101, "attributes": [[1, 594000], [2, 5940000], [3, 297000], [4, 297000]], "nextLevel": 102, "id": 76004}, {"cost": [[11, 137101, 210]], "level": 102, "attributes": [[1, 603000], [2, 6030000], [3, 301500], [4, 301500]], "nextLevel": 103, "id": 76004}, {"cost": [[11, 137101, 210]], "level": 103, "attributes": [[1, 612000], [2, 6120000], [3, 306000], [4, 306000]], "nextLevel": 104, "id": 76004}, {"cost": [[11, 137101, 210]], "level": 104, "attributes": [[1, 621000], [2, 6210000], [3, 310500], [4, 310500]], "nextLevel": 105, "id": 76004}, {"cost": [[11, 137101, 210]], "level": 105, "attributes": [[1, 630000], [2, 6300000], [3, 315000], [4, 315000]], "nextLevel": 106, "id": 76004}, {"cost": [[11, 137101, 220]], "level": 106, "attributes": [[1, 639300], [2, 6393000], [3, 319650], [4, 319650]], "nextLevel": 107, "id": 76004}, {"cost": [[11, 137101, 220]], "level": 107, "attributes": [[1, 648600], [2, 6486000], [3, 324300], [4, 324300]], "nextLevel": 108, "id": 76004}, {"cost": [[11, 137101, 220]], "level": 108, "attributes": [[1, 657900], [2, 6579000], [3, 328950], [4, 328950]], "nextLevel": 109, "id": 76004}, {"cost": [[11, 137101, 220]], "level": 109, "attributes": [[1, 667200], [2, 6672000], [3, 333600], [4, 333600]], "nextLevel": 110, "id": 76004}, {"cost": [[11, 137101, 220]], "level": 110, "attributes": [[1, 676500], [2, 6765000], [3, 338250], [4, 338250]], "nextLevel": 111, "id": 76004}, {"cost": [[11, 137101, 230]], "level": 111, "attributes": [[1, 686100], [2, 6861000], [3, 343050], [4, 343050]], "nextLevel": 112, "id": 76004}, {"cost": [[11, 137101, 230]], "level": 112, "attributes": [[1, 695700], [2, 6957000], [3, 347850], [4, 347850]], "nextLevel": 113, "id": 76004}, {"cost": [[11, 137101, 230]], "level": 113, "attributes": [[1, 705300], [2, 7053000], [3, 352650], [4, 352650]], "nextLevel": 114, "id": 76004}, {"cost": [[11, 137101, 230]], "level": 114, "attributes": [[1, 714900], [2, 7149000], [3, 357450], [4, 357450]], "nextLevel": 115, "id": 76004}, {"cost": [[11, 137101, 230]], "level": 115, "attributes": [[1, 724500], [2, 7245000], [3, 362250], [4, 362250]], "nextLevel": 116, "id": 76004}, {"cost": [[11, 137101, 240]], "level": 116, "attributes": [[1, 734400], [2, 7344000], [3, 367200], [4, 367200]], "nextLevel": 117, "id": 76004}, {"cost": [[11, 137101, 240]], "level": 117, "attributes": [[1, 744300], [2, 7443000], [3, 372150], [4, 372150]], "nextLevel": 118, "id": 76004}, {"cost": [[11, 137101, 240]], "level": 118, "attributes": [[1, 754200], [2, 7542000], [3, 377100], [4, 377100]], "nextLevel": 119, "id": 76004}, {"cost": [[11, 137101, 240]], "level": 119, "attributes": [[1, 764100], [2, 7641000], [3, 382050], [4, 382050]], "nextLevel": 120, "id": 76004}, {"cost": [[11, 137101, 240]], "level": 120, "attributes": [[1, 774000], [2, 7740000], [3, 387000], [4, 387000]], "nextLevel": 121, "id": 76004}, {"cost": [[11, 137101, 250]], "level": 121, "attributes": [[1, 784200], [2, 7842000], [3, 392100], [4, 392100]], "nextLevel": 122, "id": 76004}, {"cost": [[11, 137101, 250]], "level": 122, "attributes": [[1, 794400], [2, 7944000], [3, 397200], [4, 397200]], "nextLevel": 123, "id": 76004}, {"cost": [[11, 137101, 250]], "level": 123, "attributes": [[1, 804600], [2, 8046000], [3, 402300], [4, 402300]], "nextLevel": 124, "id": 76004}, {"cost": [[11, 137101, 250]], "level": 124, "attributes": [[1, 814800], [2, 8148000], [3, 407400], [4, 407400]], "nextLevel": 125, "id": 76004}, {"cost": [[11, 137101, 250]], "level": 125, "attributes": [[1, 825000], [2, 8250000], [3, 412500], [4, 412500]], "nextLevel": 126, "id": 76004}, {"cost": [[11, 137101, 260]], "level": 126, "attributes": [[1, 835500], [2, 8355000], [3, 417750], [4, 417750]], "nextLevel": 127, "id": 76004}, {"cost": [[11, 137101, 260]], "level": 127, "attributes": [[1, 846000], [2, 8460000], [3, 423000], [4, 423000]], "nextLevel": 128, "id": 76004}, {"cost": [[11, 137101, 260]], "level": 128, "attributes": [[1, 856500], [2, 8565000], [3, 428250], [4, 428250]], "nextLevel": 129, "id": 76004}, {"cost": [[11, 137101, 260]], "level": 129, "attributes": [[1, 867000], [2, 8670000], [3, 433500], [4, 433500]], "nextLevel": 130, "id": 76004}, {"cost": [[11, 137101, 260]], "level": 130, "attributes": [[1, 877500], [2, 8775000], [3, 438750], [4, 438750]], "nextLevel": 131, "id": 76004}, {"cost": [[11, 137101, 270]], "level": 131, "attributes": [[1, 888300], [2, 8883000], [3, 444150], [4, 444150]], "nextLevel": 132, "id": 76004}, {"cost": [[11, 137101, 270]], "level": 132, "attributes": [[1, 899100], [2, 8991000], [3, 449550], [4, 449550]], "nextLevel": 133, "id": 76004}, {"cost": [[11, 137101, 270]], "level": 133, "attributes": [[1, 909900], [2, 9099000], [3, 454950], [4, 454950]], "nextLevel": 134, "id": 76004}, {"cost": [[11, 137101, 270]], "level": 134, "attributes": [[1, 920700], [2, 9207000], [3, 460350], [4, 460350]], "nextLevel": 135, "id": 76004}, {"cost": [[11, 137101, 270]], "level": 135, "attributes": [[1, 931500], [2, 9315000], [3, 465750], [4, 465750]], "nextLevel": 136, "id": 76004}, {"cost": [[11, 137101, 280]], "level": 136, "attributes": [[1, 942600], [2, 9426000], [3, 471300], [4, 471300]], "nextLevel": 137, "id": 76004}, {"cost": [[11, 137101, 280]], "level": 137, "attributes": [[1, 953700], [2, 9537000], [3, 476850], [4, 476850]], "nextLevel": 138, "id": 76004}, {"cost": [[11, 137101, 280]], "level": 138, "attributes": [[1, 964800], [2, 9648000], [3, 482400], [4, 482400]], "nextLevel": 139, "id": 76004}, {"cost": [[11, 137101, 280]], "level": 139, "attributes": [[1, 975900], [2, 9759000], [3, 487950], [4, 487950]], "nextLevel": 140, "id": 76004}, {"cost": [[11, 137101, 280]], "level": 140, "attributes": [[1, 987000], [2, 9870000], [3, 493500], [4, 493500]], "nextLevel": 141, "id": 76004}, {"cost": [[11, 137101, 290]], "level": 141, "attributes": [[1, 998400], [2, 9984000], [3, 499200], [4, 499200]], "nextLevel": 142, "id": 76004}, {"cost": [[11, 137101, 290]], "level": 142, "attributes": [[1, 1009800], [2, 10098000], [3, 504900], [4, 504900]], "nextLevel": 143, "id": 76004}, {"cost": [[11, 137101, 290]], "level": 143, "attributes": [[1, 1021200], [2, 10212000], [3, 510600], [4, 510600]], "nextLevel": 144, "id": 76004}, {"cost": [[11, 137101, 290]], "level": 144, "attributes": [[1, 1032600], [2, 10326000], [3, 516300], [4, 516300]], "nextLevel": 145, "id": 76004}, {"cost": [[11, 137101, 290]], "level": 145, "attributes": [[1, 1044000], [2, 10440000], [3, 522000], [4, 522000]], "nextLevel": 146, "id": 76004}, {"cost": [[11, 137101, 300]], "level": 146, "attributes": [[1, 1055700], [2, 10557000], [3, 527850], [4, 527850]], "nextLevel": 147, "id": 76004}, {"cost": [[11, 137101, 300]], "level": 147, "attributes": [[1, 1067400], [2, 10674000], [3, 533700], [4, 533700]], "nextLevel": 148, "id": 76004}, {"cost": [[11, 137101, 300]], "level": 148, "attributes": [[1, 1079100], [2, 10791000], [3, 539550], [4, 539550]], "nextLevel": 149, "id": 76004}, {"cost": [[11, 137101, 300]], "level": 149, "attributes": [[1, 1090800], [2, 10908000], [3, 545400], [4, 545400]], "nextLevel": 150, "id": 76004}, {"cost": [[11, 137101, 300]], "level": 150, "attributes": [[1, 1102500], [2, 11025000], [3, 551250], [4, 551250]], "nextLevel": 151, "id": 76004}, {"cost": [[11, 137101, 310]], "level": 151, "attributes": [[1, 1114500], [2, 11145000], [3, 557250], [4, 557250]], "nextLevel": 152, "id": 76004}, {"cost": [[11, 137101, 310]], "level": 152, "attributes": [[1, 1126500], [2, 11265000], [3, 563250], [4, 563250]], "nextLevel": 153, "id": 76004}, {"cost": [[11, 137101, 310]], "level": 153, "attributes": [[1, 1138500], [2, 11385000], [3, 569250], [4, 569250]], "nextLevel": 154, "id": 76004}, {"cost": [[11, 137101, 310]], "level": 154, "attributes": [[1, 1150500], [2, 11505000], [3, 575250], [4, 575250]], "nextLevel": 155, "id": 76004}, {"cost": [[11, 137101, 310]], "level": 155, "attributes": [[1, 1162500], [2, 11625000], [3, 581250], [4, 581250]], "nextLevel": 156, "id": 76004}, {"cost": [[11, 137101, 320]], "level": 156, "attributes": [[1, 1174800], [2, 11748000], [3, 587400], [4, 587400]], "nextLevel": 157, "id": 76004}, {"cost": [[11, 137101, 320]], "level": 157, "attributes": [[1, 1187100], [2, 11871000], [3, 593550], [4, 593550]], "nextLevel": 158, "id": 76004}, {"cost": [[11, 137101, 320]], "level": 158, "attributes": [[1, 1199400], [2, 11994000], [3, 599700], [4, 599700]], "nextLevel": 159, "id": 76004}, {"cost": [[11, 137101, 320]], "level": 159, "attributes": [[1, 1211700], [2, 12117000], [3, 605850], [4, 605850]], "nextLevel": 160, "id": 76004}, {"cost": [[11, 137101, 320]], "level": 160, "attributes": [[1, 1224000], [2, 12240000], [3, 612000], [4, 612000]], "nextLevel": 161, "id": 76004}, {"cost": [[11, 137101, 330]], "level": 161, "attributes": [[1, 1236600], [2, 12366000], [3, 618300], [4, 618300]], "nextLevel": 162, "id": 76004}, {"cost": [[11, 137101, 330]], "level": 162, "attributes": [[1, 1249200], [2, 12492000], [3, 624600], [4, 624600]], "nextLevel": 163, "id": 76004}, {"cost": [[11, 137101, 330]], "level": 163, "attributes": [[1, 1261800], [2, 12618000], [3, 630900], [4, 630900]], "nextLevel": 164, "id": 76004}, {"cost": [[11, 137101, 330]], "level": 164, "attributes": [[1, 1274400], [2, 12744000], [3, 637200], [4, 637200]], "nextLevel": 165, "id": 76004}, {"cost": [[11, 137101, 330]], "level": 165, "attributes": [[1, 1287000], [2, 12870000], [3, 643500], [4, 643500]], "nextLevel": 166, "id": 76004}, {"cost": [[11, 137101, 340]], "level": 166, "attributes": [[1, 1299900], [2, 12999000], [3, 649950], [4, 649950]], "nextLevel": 167, "id": 76004}, {"cost": [[11, 137101, 340]], "level": 167, "attributes": [[1, 1312800], [2, 13128000], [3, 656400], [4, 656400]], "nextLevel": 168, "id": 76004}, {"cost": [[11, 137101, 340]], "level": 168, "attributes": [[1, 1325700], [2, 13257000], [3, 662850], [4, 662850]], "nextLevel": 169, "id": 76004}, {"cost": [[11, 137101, 340]], "level": 169, "attributes": [[1, 1338600], [2, 13386000], [3, 669300], [4, 669300]], "nextLevel": 170, "id": 76004}, {"cost": [[11, 137101, 340]], "level": 170, "attributes": [[1, 1351500], [2, 13515000], [3, 675750], [4, 675750]], "nextLevel": 171, "id": 76004}, {"cost": [[11, 137101, 350]], "level": 171, "attributes": [[1, 1364700], [2, 13647000], [3, 682350], [4, 682350]], "nextLevel": 172, "id": 76004}, {"cost": [[11, 137101, 350]], "level": 172, "attributes": [[1, 1377900], [2, 13779000], [3, 688950], [4, 688950]], "nextLevel": 173, "id": 76004}, {"cost": [[11, 137101, 350]], "level": 173, "attributes": [[1, 1391100], [2, 13911000], [3, 695550], [4, 695550]], "nextLevel": 174, "id": 76004}, {"cost": [[11, 137101, 350]], "level": 174, "attributes": [[1, 1404300], [2, 14043000], [3, 702150], [4, 702150]], "nextLevel": 175, "id": 76004}, {"cost": [[11, 137101, 350]], "level": 175, "attributes": [[1, 1417500], [2, 14175000], [3, 708750], [4, 708750]], "nextLevel": 176, "id": 76004}, {"cost": [[11, 137101, 360]], "level": 176, "attributes": [[1, 1431000], [2, 14310000], [3, 715500], [4, 715500]], "nextLevel": 177, "id": 76004}, {"cost": [[11, 137101, 360]], "level": 177, "attributes": [[1, 1444500], [2, 14445000], [3, 722250], [4, 722250]], "nextLevel": 178, "id": 76004}, {"cost": [[11, 137101, 360]], "level": 178, "attributes": [[1, 1458000], [2, 14580000], [3, 729000], [4, 729000]], "nextLevel": 179, "id": 76004}, {"cost": [[11, 137101, 360]], "level": 179, "attributes": [[1, 1471500], [2, 14715000], [3, 735750], [4, 735750]], "nextLevel": 180, "id": 76004}, {"cost": [[11, 137101, 360]], "level": 180, "attributes": [[1, 1485000], [2, 14850000], [3, 742500], [4, 742500]], "nextLevel": 181, "id": 76004}, {"cost": [[11, 137101, 370]], "level": 181, "attributes": [[1, 1498800], [2, 14988000], [3, 749400], [4, 749400]], "nextLevel": 182, "id": 76004}, {"cost": [[11, 137101, 370]], "level": 182, "attributes": [[1, 1512600], [2, 15126000], [3, 756300], [4, 756300]], "nextLevel": 183, "id": 76004}, {"cost": [[11, 137101, 370]], "level": 183, "attributes": [[1, 1526400], [2, 15264000], [3, 763200], [4, 763200]], "nextLevel": 184, "id": 76004}, {"cost": [[11, 137101, 370]], "level": 184, "attributes": [[1, 1540200], [2, 15402000], [3, 770100], [4, 770100]], "nextLevel": 185, "id": 76004}, {"cost": [[11, 137101, 370]], "level": 185, "attributes": [[1, 1554000], [2, 15540000], [3, 777000], [4, 777000]], "nextLevel": 186, "id": 76004}, {"cost": [[11, 137101, 380]], "level": 186, "attributes": [[1, 1568100], [2, 15681000], [3, 784050], [4, 784050]], "nextLevel": 187, "id": 76004}, {"cost": [[11, 137101, 380]], "level": 187, "attributes": [[1, 1582200], [2, 15822000], [3, 791100], [4, 791100]], "nextLevel": 188, "id": 76004}, {"cost": [[11, 137101, 380]], "level": 188, "attributes": [[1, 1596300], [2, 15963000], [3, 798150], [4, 798150]], "nextLevel": 189, "id": 76004}, {"cost": [[11, 137101, 380]], "level": 189, "attributes": [[1, 1610400], [2, 16104000], [3, 805200], [4, 805200]], "nextLevel": 190, "id": 76004}, {"cost": [[11, 137101, 380]], "level": 190, "attributes": [[1, 1624500], [2, 16245000], [3, 812250], [4, 812250]], "nextLevel": 191, "id": 76004}, {"cost": [[11, 137101, 390]], "level": 191, "attributes": [[1, 1638900], [2, 16389000], [3, 819450], [4, 819450]], "nextLevel": 192, "id": 76004}, {"cost": [[11, 137101, 390]], "level": 192, "attributes": [[1, 1653300], [2, 16533000], [3, 826650], [4, 826650]], "nextLevel": 193, "id": 76004}, {"cost": [[11, 137101, 390]], "level": 193, "attributes": [[1, 1667700], [2, 16677000], [3, 833850], [4, 833850]], "nextLevel": 194, "id": 76004}, {"cost": [[11, 137101, 390]], "level": 194, "attributes": [[1, 1682100], [2, 16821000], [3, 841050], [4, 841050]], "nextLevel": 195, "id": 76004}, {"cost": [[11, 137101, 390]], "level": 195, "attributes": [[1, 1696500], [2, 16965000], [3, 848250], [4, 848250]], "nextLevel": 196, "id": 76004}, {"cost": [[11, 137101, 400]], "level": 196, "attributes": [[1, 1711200], [2, 17112000], [3, 855600], [4, 855600]], "nextLevel": 197, "id": 76004}, {"cost": [[11, 137101, 400]], "level": 197, "attributes": [[1, 1725900], [2, 17259000], [3, 862950], [4, 862950]], "nextLevel": 198, "id": 76004}, {"cost": [[11, 137101, 400]], "level": 198, "attributes": [[1, 1740600], [2, 17406000], [3, 870300], [4, 870300]], "nextLevel": 199, "id": 76004}, {"cost": [[11, 137101, 400]], "level": 199, "attributes": [[1, 1755300], [2, 17553000], [3, 877650], [4, 877650]], "nextLevel": 200, "id": 76004}, {"cost": [[11, 137101, 400]], "level": 200, "attributes": [[1, 1770000], [2, 17700000], [3, 885000], [4, 885000]], "nextLevel": 201, "id": 76004}, {"cost": [[11, 137101, 410]], "level": 201, "attributes": [[1, 1785000], [2, 17850000], [3, 892500], [4, 892500]], "nextLevel": 202, "id": 76004}, {"cost": [[11, 137101, 410]], "level": 202, "attributes": [[1, 1800000], [2, 18000000], [3, 900000], [4, 900000]], "nextLevel": 203, "id": 76004}, {"cost": [[11, 137101, 410]], "level": 203, "attributes": [[1, 1815000], [2, 18150000], [3, 907500], [4, 907500]], "nextLevel": 204, "id": 76004}, {"cost": [[11, 137101, 410]], "level": 204, "attributes": [[1, 1830000], [2, 18300000], [3, 915000], [4, 915000]], "nextLevel": 205, "id": 76004}, {"cost": [[11, 137101, 410]], "level": 205, "attributes": [[1, 1845000], [2, 18450000], [3, 922500], [4, 922500]], "nextLevel": 206, "id": 76004}, {"cost": [[11, 137101, 420]], "level": 206, "attributes": [[1, 1860300], [2, 18603000], [3, 930150], [4, 930150]], "nextLevel": 207, "id": 76004}, {"cost": [[11, 137101, 420]], "level": 207, "attributes": [[1, 1875600], [2, 18756000], [3, 937800], [4, 937800]], "nextLevel": 208, "id": 76004}, {"cost": [[11, 137101, 420]], "level": 208, "attributes": [[1, 1890900], [2, 18909000], [3, 945450], [4, 945450]], "nextLevel": 209, "id": 76004}, {"cost": [[11, 137101, 420]], "level": 209, "attributes": [[1, 1906200], [2, 19062000], [3, 953100], [4, 953100]], "nextLevel": 210, "id": 76004}, {"cost": [[11, 137101, 420]], "level": 210, "attributes": [[1, 1921500], [2, 19215000], [3, 960750], [4, 960750]], "nextLevel": 211, "id": 76004}, {"cost": [[11, 137101, 430]], "level": 211, "attributes": [[1, 1937100], [2, 19371000], [3, 968550], [4, 968550]], "nextLevel": 212, "id": 76004}, {"cost": [[11, 137101, 430]], "level": 212, "attributes": [[1, 1952700], [2, 19527000], [3, 976350], [4, 976350]], "nextLevel": 213, "id": 76004}, {"cost": [[11, 137101, 430]], "level": 213, "attributes": [[1, 1968300], [2, 19683000], [3, 984150], [4, 984150]], "nextLevel": 214, "id": 76004}, {"cost": [[11, 137101, 430]], "level": 214, "attributes": [[1, 1983900], [2, 19839000], [3, 991950], [4, 991950]], "nextLevel": 215, "id": 76004}, {"cost": [[11, 137101, 430]], "level": 215, "attributes": [[1, 1999500], [2, 19995000], [3, 999750], [4, 999750]], "nextLevel": 216, "id": 76004}, {"cost": [[11, 137101, 440]], "level": 216, "attributes": [[1, 2015400], [2, 20154000], [3, 1007700], [4, 1007700]], "nextLevel": 217, "id": 76004}, {"cost": [[11, 137101, 440]], "level": 217, "attributes": [[1, 2031300], [2, 20313000], [3, 1015650], [4, 1015650]], "nextLevel": 218, "id": 76004}, {"cost": [[11, 137101, 440]], "level": 218, "attributes": [[1, 2047200], [2, 20472000], [3, 1023600], [4, 1023600]], "nextLevel": 219, "id": 76004}, {"cost": [[11, 137101, 440]], "level": 219, "attributes": [[1, 2063100], [2, 20631000], [3, 1031550], [4, 1031550]], "nextLevel": 220, "id": 76004}, {"cost": [[11, 137101, 440]], "level": 220, "attributes": [[1, 2079000], [2, 20790000], [3, 1039500], [4, 1039500]], "nextLevel": 221, "id": 76004}, {"cost": [[11, 137101, 450]], "level": 221, "attributes": [[1, 2095200], [2, 20952000], [3, 1047600], [4, 1047600]], "nextLevel": 222, "id": 76004}, {"cost": [[11, 137101, 450]], "level": 222, "attributes": [[1, 2111400], [2, 21114000], [3, 1055700], [4, 1055700]], "nextLevel": 223, "id": 76004}, {"cost": [[11, 137101, 450]], "level": 223, "attributes": [[1, 2127600], [2, 21276000], [3, 1063800], [4, 1063800]], "nextLevel": 224, "id": 76004}, {"cost": [[11, 137101, 450]], "level": 224, "attributes": [[1, 2143800], [2, 21438000], [3, 1071900], [4, 1071900]], "nextLevel": 225, "id": 76004}, {"cost": [[11, 137101, 450]], "level": 225, "attributes": [[1, 2160000], [2, 21600000], [3, 1080000], [4, 1080000]], "nextLevel": 226, "id": 76004}, {"cost": [[11, 137101, 460]], "level": 226, "attributes": [[1, 2176500], [2, 21765000], [3, 1088250], [4, 1088250]], "nextLevel": 227, "id": 76004}, {"cost": [[11, 137101, 460]], "level": 227, "attributes": [[1, 2193000], [2, 21930000], [3, 1096500], [4, 1096500]], "nextLevel": 228, "id": 76004}, {"cost": [[11, 137101, 460]], "level": 228, "attributes": [[1, 2209500], [2, 22095000], [3, 1104750], [4, 1104750]], "nextLevel": 229, "id": 76004}, {"cost": [[11, 137101, 460]], "level": 229, "attributes": [[1, 2226000], [2, 22260000], [3, 1113000], [4, 1113000]], "nextLevel": 230, "id": 76004}, {"cost": [[11, 137101, 460]], "level": 230, "attributes": [[1, 2242500], [2, 22425000], [3, 1121250], [4, 1121250]], "nextLevel": 231, "id": 76004}, {"cost": [[11, 137101, 470]], "level": 231, "attributes": [[1, 2259300], [2, 22593000], [3, 1129650], [4, 1129650]], "nextLevel": 232, "id": 76004}, {"cost": [[11, 137101, 470]], "level": 232, "attributes": [[1, 2276100], [2, 22761000], [3, 1138050], [4, 1138050]], "nextLevel": 233, "id": 76004}, {"cost": [[11, 137101, 470]], "level": 233, "attributes": [[1, 2292900], [2, 22929000], [3, 1146450], [4, 1146450]], "nextLevel": 234, "id": 76004}, {"cost": [[11, 137101, 470]], "level": 234, "attributes": [[1, 2309700], [2, 23097000], [3, 1154850], [4, 1154850]], "nextLevel": 235, "id": 76004}, {"cost": [[11, 137101, 470]], "level": 235, "attributes": [[1, 2326500], [2, 23265000], [3, 1163250], [4, 1163250]], "nextLevel": 236, "id": 76004}, {"cost": [[11, 137101, 480]], "level": 236, "attributes": [[1, 2343600], [2, 23436000], [3, 1171800], [4, 1171800]], "nextLevel": 237, "id": 76004}, {"cost": [[11, 137101, 480]], "level": 237, "attributes": [[1, 2360700], [2, 23607000], [3, 1180350], [4, 1180350]], "nextLevel": 238, "id": 76004}, {"cost": [[11, 137101, 480]], "level": 238, "attributes": [[1, 2377800], [2, 23778000], [3, 1188900], [4, 1188900]], "nextLevel": 239, "id": 76004}, {"cost": [[11, 137101, 480]], "level": 239, "attributes": [[1, 2394900], [2, 23949000], [3, 1197450], [4, 1197450]], "nextLevel": 240, "id": 76004}, {"cost": [[11, 137101, 480]], "level": 240, "attributes": [[1, 2412000], [2, 24120000], [3, 1206000], [4, 1206000]], "nextLevel": 241, "id": 76004}, {"cost": [[11, 137101, 490]], "level": 241, "attributes": [[1, 2429400], [2, 24294000], [3, 1214700], [4, 1214700]], "nextLevel": 242, "id": 76004}, {"cost": [[11, 137101, 490]], "level": 242, "attributes": [[1, 2446800], [2, 24468000], [3, 1223400], [4, 1223400]], "nextLevel": 243, "id": 76004}, {"cost": [[11, 137101, 490]], "level": 243, "attributes": [[1, 2464200], [2, 24642000], [3, 1232100], [4, 1232100]], "nextLevel": 244, "id": 76004}, {"cost": [[11, 137101, 490]], "level": 244, "attributes": [[1, 2481600], [2, 24816000], [3, 1240800], [4, 1240800]], "nextLevel": 245, "id": 76004}, {"cost": [[11, 137101, 490]], "level": 245, "attributes": [[1, 2499000], [2, 24990000], [3, 1249500], [4, 1249500]], "nextLevel": 246, "id": 76004}, {"cost": [[11, 137101, 500]], "level": 246, "attributes": [[1, 2516700], [2, 25167000], [3, 1258350], [4, 1258350]], "nextLevel": 247, "id": 76004}, {"cost": [[11, 137101, 500]], "level": 247, "attributes": [[1, 2534400], [2, 25344000], [3, 1267200], [4, 1267200]], "nextLevel": 248, "id": 76004}, {"cost": [[11, 137101, 500]], "level": 248, "attributes": [[1, 2552100], [2, 25521000], [3, 1276050], [4, 1276050]], "nextLevel": 249, "id": 76004}, {"cost": [[11, 137101, 500]], "level": 249, "attributes": [[1, 2569800], [2, 25698000], [3, 1284900], [4, 1284900]], "nextLevel": 250, "id": 76004}, {"cost": [[11, 137101, 500]], "level": 250, "attributes": [[1, 2587500], [2, 25875000], [3, 1293750], [4, 1293750]], "nextLevel": 251, "id": 76004}, {"cost": [[11, 137101, 510]], "level": 251, "attributes": [[1, 2605500], [2, 26055000], [3, 1302750], [4, 1302750]], "nextLevel": 252, "id": 76004}, {"cost": [[11, 137101, 510]], "level": 252, "attributes": [[1, 2623500], [2, 26235000], [3, 1311750], [4, 1311750]], "nextLevel": 253, "id": 76004}, {"cost": [[11, 137101, 510]], "level": 253, "attributes": [[1, 2641500], [2, 26415000], [3, 1320750], [4, 1320750]], "nextLevel": 254, "id": 76004}, {"cost": [[11, 137101, 510]], "level": 254, "attributes": [[1, 2659500], [2, 26595000], [3, 1329750], [4, 1329750]], "nextLevel": 255, "id": 76004}, {"cost": [[11, 137101, 510]], "level": 255, "attributes": [[1, 2677500], [2, 26775000], [3, 1338750], [4, 1338750]], "nextLevel": 256, "id": 76004}, {"cost": [[11, 137101, 520]], "level": 256, "attributes": [[1, 2695800], [2, 26958000], [3, 1347900], [4, 1347900]], "nextLevel": 257, "id": 76004}, {"cost": [[11, 137101, 520]], "level": 257, "attributes": [[1, 2714100], [2, 27141000], [3, 1357050], [4, 1357050]], "nextLevel": 258, "id": 76004}, {"cost": [[11, 137101, 520]], "level": 258, "attributes": [[1, 2732400], [2, 27324000], [3, 1366200], [4, 1366200]], "nextLevel": 259, "id": 76004}, {"cost": [[11, 137101, 520]], "level": 259, "attributes": [[1, 2750700], [2, 27507000], [3, 1375350], [4, 1375350]], "nextLevel": 260, "id": 76004}, {"cost": [[11, 137101, 520]], "level": 260, "attributes": [[1, 2769000], [2, 27690000], [3, 1384500], [4, 1384500]], "nextLevel": 261, "id": 76004}, {"cost": [[11, 137101, 530]], "level": 261, "attributes": [[1, 2787600], [2, 27876000], [3, 1393800], [4, 1393800]], "nextLevel": 262, "id": 76004}, {"cost": [[11, 137101, 530]], "level": 262, "attributes": [[1, 2806200], [2, 28062000], [3, 1403100], [4, 1403100]], "nextLevel": 263, "id": 76004}, {"cost": [[11, 137101, 530]], "level": 263, "attributes": [[1, 2824800], [2, 28248000], [3, 1412400], [4, 1412400]], "nextLevel": 264, "id": 76004}, {"cost": [[11, 137101, 530]], "level": 264, "attributes": [[1, 2843400], [2, 28434000], [3, 1421700], [4, 1421700]], "nextLevel": 265, "id": 76004}, {"cost": [[11, 137101, 530]], "level": 265, "attributes": [[1, 2862000], [2, 28620000], [3, 1431000], [4, 1431000]], "nextLevel": 266, "id": 76004}, {"cost": [[11, 137101, 540]], "level": 266, "attributes": [[1, 2880900], [2, 28809000], [3, 1440450], [4, 1440450]], "nextLevel": 267, "id": 76004}, {"cost": [[11, 137101, 540]], "level": 267, "attributes": [[1, 2899800], [2, 28998000], [3, 1449900], [4, 1449900]], "nextLevel": 268, "id": 76004}, {"cost": [[11, 137101, 540]], "level": 268, "attributes": [[1, 2918700], [2, 29187000], [3, 1459350], [4, 1459350]], "nextLevel": 269, "id": 76004}, {"cost": [[11, 137101, 540]], "level": 269, "attributes": [[1, 2937600], [2, 29376000], [3, 1468800], [4, 1468800]], "nextLevel": 270, "id": 76004}, {"cost": [[11, 137101, 540]], "level": 270, "attributes": [[1, 2956500], [2, 29565000], [3, 1478250], [4, 1478250]], "nextLevel": 271, "id": 76004}, {"cost": [[11, 137101, 550]], "level": 271, "attributes": [[1, 2975700], [2, 29757000], [3, 1487850], [4, 1487850]], "nextLevel": 272, "id": 76004}, {"cost": [[11, 137101, 550]], "level": 272, "attributes": [[1, 2994900], [2, 29949000], [3, 1497450], [4, 1497450]], "nextLevel": 273, "id": 76004}, {"cost": [[11, 137101, 550]], "level": 273, "attributes": [[1, 3014100], [2, 30141000], [3, 1507050], [4, 1507050]], "nextLevel": 274, "id": 76004}, {"cost": [[11, 137101, 550]], "level": 274, "attributes": [[1, 3033300], [2, 30333000], [3, 1516650], [4, 1516650]], "nextLevel": 275, "id": 76004}, {"cost": [[11, 137101, 550]], "level": 275, "attributes": [[1, 3052500], [2, 30525000], [3, 1526250], [4, 1526250]], "nextLevel": 276, "id": 76004}, {"cost": [[11, 137101, 560]], "level": 276, "attributes": [[1, 3072000], [2, 30720000], [3, 1536000], [4, 1536000]], "nextLevel": 277, "id": 76004}, {"cost": [[11, 137101, 560]], "level": 277, "attributes": [[1, 3091500], [2, 30915000], [3, 1545750], [4, 1545750]], "nextLevel": 278, "id": 76004}, {"cost": [[11, 137101, 560]], "level": 278, "attributes": [[1, 3111000], [2, 31110000], [3, 1555500], [4, 1555500]], "nextLevel": 279, "id": 76004}, {"cost": [[11, 137101, 560]], "level": 279, "attributes": [[1, 3130500], [2, 31305000], [3, 1565250], [4, 1565250]], "nextLevel": 280, "id": 76004}, {"cost": [[11, 137101, 560]], "level": 280, "attributes": [[1, 3150000], [2, 31500000], [3, 1575000], [4, 1575000]], "nextLevel": 281, "id": 76004}, {"cost": [[11, 137101, 570]], "level": 281, "attributes": [[1, 3169800], [2, 31698000], [3, 1584900], [4, 1584900]], "nextLevel": 282, "id": 76004}, {"cost": [[11, 137101, 570]], "level": 282, "attributes": [[1, 3189600], [2, 31896000], [3, 1594800], [4, 1594800]], "nextLevel": 283, "id": 76004}, {"cost": [[11, 137101, 570]], "level": 283, "attributes": [[1, 3209400], [2, 32094000], [3, 1604700], [4, 1604700]], "nextLevel": 284, "id": 76004}, {"cost": [[11, 137101, 570]], "level": 284, "attributes": [[1, 3229200], [2, 32292000], [3, 1614600], [4, 1614600]], "nextLevel": 285, "id": 76004}, {"cost": [[11, 137101, 570]], "level": 285, "attributes": [[1, 3249000], [2, 32490000], [3, 1624500], [4, 1624500]], "nextLevel": 286, "id": 76004}, {"cost": [[11, 137101, 580]], "level": 286, "attributes": [[1, 3269100], [2, 32691000], [3, 1634550], [4, 1634550]], "nextLevel": 287, "id": 76004}, {"cost": [[11, 137101, 580]], "level": 287, "attributes": [[1, 3289200], [2, 32892000], [3, 1644600], [4, 1644600]], "nextLevel": 288, "id": 76004}, {"cost": [[11, 137101, 580]], "level": 288, "attributes": [[1, 3309300], [2, 33093000], [3, 1654650], [4, 1654650]], "nextLevel": 289, "id": 76004}, {"cost": [[11, 137101, 580]], "level": 289, "attributes": [[1, 3329400], [2, 33294000], [3, 1664700], [4, 1664700]], "nextLevel": 290, "id": 76004}, {"cost": [[11, 137101, 580]], "level": 290, "attributes": [[1, 3349500], [2, 33495000], [3, 1674750], [4, 1674750]], "nextLevel": 291, "id": 76004}, {"cost": [[11, 137101, 590]], "level": 291, "attributes": [[1, 3369900], [2, 33699000], [3, 1684950], [4, 1684950]], "nextLevel": 292, "id": 76004}, {"cost": [[11, 137101, 590]], "level": 292, "attributes": [[1, 3390300], [2, 33903000], [3, 1695150], [4, 1695150]], "nextLevel": 293, "id": 76004}, {"cost": [[11, 137101, 590]], "level": 293, "attributes": [[1, 3410700], [2, 34107000], [3, 1705350], [4, 1705350]], "nextLevel": 294, "id": 76004}, {"cost": [[11, 137101, 590]], "level": 294, "attributes": [[1, 3431100], [2, 34311000], [3, 1715550], [4, 1715550]], "nextLevel": 295, "id": 76004}, {"cost": [[11, 137101, 590]], "level": 295, "attributes": [[1, 3451500], [2, 34515000], [3, 1725750], [4, 1725750]], "nextLevel": 296, "id": 76004}, {"cost": [[11, 137101, 600]], "level": 296, "attributes": [[1, 3472200], [2, 34722000], [3, 1736100], [4, 1736100]], "nextLevel": 297, "id": 76004}, {"cost": [[11, 137101, 600]], "level": 297, "attributes": [[1, 3492900], [2, 34929000], [3, 1746450], [4, 1746450]], "nextLevel": 298, "id": 76004}, {"cost": [[11, 137101, 600]], "level": 298, "attributes": [[1, 3513600], [2, 35136000], [3, 1756800], [4, 1756800]], "nextLevel": 299, "id": 76004}, {"cost": [[11, 137101, 600]], "level": 299, "attributes": [[1, 3534300], [2, 35343000], [3, 1767150], [4, 1767150]], "nextLevel": 300, "id": 76004}, {"cost": [[11, 137101, 600]], "level": 300, "attributes": [[1, 3555000], [2, 35550000], [3, 1777500], [4, 1777500]], "nextLevel": 301, "id": 76004}, {"cost": [[11, 137101, 610]], "level": 301, "attributes": [[1, 3576000], [2, 35760000], [3, 1788000], [4, 1788000]], "nextLevel": 302, "id": 76004}, {"cost": [[11, 137101, 610]], "level": 302, "attributes": [[1, 3597000], [2, 35970000], [3, 1798500], [4, 1798500]], "nextLevel": 303, "id": 76004}, {"cost": [[11, 137101, 610]], "level": 303, "attributes": [[1, 3618000], [2, 36180000], [3, 1809000], [4, 1809000]], "nextLevel": 304, "id": 76004}, {"cost": [[11, 137101, 610]], "level": 304, "attributes": [[1, 3639000], [2, 36390000], [3, 1819500], [4, 1819500]], "nextLevel": 305, "id": 76004}, {"cost": [[11, 137101, 610]], "level": 305, "attributes": [[1, 3660000], [2, 36600000], [3, 1830000], [4, 1830000]], "nextLevel": 306, "id": 76004}, {"cost": [[11, 137101, 620]], "level": 306, "attributes": [[1, 3681300], [2, 36813000], [3, 1840650], [4, 1840650]], "nextLevel": 307, "id": 76004}, {"cost": [[11, 137101, 620]], "level": 307, "attributes": [[1, 3702600], [2, 37026000], [3, 1851300], [4, 1851300]], "nextLevel": 308, "id": 76004}, {"cost": [[11, 137101, 620]], "level": 308, "attributes": [[1, 3723900], [2, 37239000], [3, 1861950], [4, 1861950]], "nextLevel": 309, "id": 76004}, {"cost": [[11, 137101, 620]], "level": 309, "attributes": [[1, 3745200], [2, 37452000], [3, 1872600], [4, 1872600]], "nextLevel": 310, "id": 76004}, {"cost": [[11, 137101, 620]], "level": 310, "attributes": [[1, 3766500], [2, 37665000], [3, 1883250], [4, 1883250]], "nextLevel": 311, "id": 76004}, {"cost": [[11, 137101, 630]], "level": 311, "attributes": [[1, 3788100], [2, 37881000], [3, 1894050], [4, 1894050]], "nextLevel": 312, "id": 76004}, {"cost": [[11, 137101, 630]], "level": 312, "attributes": [[1, 3809700], [2, 38097000], [3, 1904850], [4, 1904850]], "nextLevel": 313, "id": 76004}, {"cost": [[11, 137101, 630]], "level": 313, "attributes": [[1, 3831300], [2, 38313000], [3, 1915650], [4, 1915650]], "nextLevel": 314, "id": 76004}, {"cost": [[11, 137101, 630]], "level": 314, "attributes": [[1, 3852900], [2, 38529000], [3, 1926450], [4, 1926450]], "nextLevel": 315, "id": 76004}, {"cost": [[11, 137101, 630]], "level": 315, "attributes": [[1, 3874500], [2, 38745000], [3, 1937250], [4, 1937250]], "nextLevel": 316, "id": 76004}, {"cost": [[11, 137101, 640]], "level": 316, "attributes": [[1, 3896400], [2, 38964000], [3, 1948200], [4, 1948200]], "nextLevel": 317, "id": 76004}, {"cost": [[11, 137101, 640]], "level": 317, "attributes": [[1, 3918300], [2, 39183000], [3, 1959150], [4, 1959150]], "nextLevel": 318, "id": 76004}, {"cost": [[11, 137101, 640]], "level": 318, "attributes": [[1, 3940200], [2, 39402000], [3, 1970100], [4, 1970100]], "nextLevel": 319, "id": 76004}, {"cost": [[11, 137101, 640]], "level": 319, "attributes": [[1, 3962100], [2, 39621000], [3, 1981050], [4, 1981050]], "nextLevel": 320, "id": 76004}, {"cost": [[11, 137101, 640]], "level": 320, "attributes": [[1, 3984000], [2, 39840000], [3, 1992000], [4, 1992000]], "nextLevel": 321, "id": 76004}, {"cost": [[11, 137101, 650]], "level": 321, "attributes": [[1, 4006200], [2, 40062000], [3, 2003100], [4, 2003100]], "nextLevel": 322, "id": 76004}, {"cost": [[11, 137101, 650]], "level": 322, "attributes": [[1, 4028400], [2, 40284000], [3, 2014200], [4, 2014200]], "nextLevel": 323, "id": 76004}, {"cost": [[11, 137101, 650]], "level": 323, "attributes": [[1, 4050600], [2, 40506000], [3, 2025300], [4, 2025300]], "nextLevel": 324, "id": 76004}, {"cost": [[11, 137101, 650]], "level": 324, "attributes": [[1, 4072800], [2, 40728000], [3, 2036400], [4, 2036400]], "nextLevel": 325, "id": 76004}, {"cost": [[11, 137101, 650]], "level": 325, "attributes": [[1, 4095000], [2, 40950000], [3, 2047500], [4, 2047500]], "nextLevel": 326, "id": 76004}, {"cost": [[11, 137101, 660]], "level": 326, "attributes": [[1, 4117500], [2, 41175000], [3, 2058750], [4, 2058750]], "nextLevel": 327, "id": 76004}, {"cost": [[11, 137101, 660]], "level": 327, "attributes": [[1, 4140000], [2, 41400000], [3, 2070000], [4, 2070000]], "nextLevel": 328, "id": 76004}, {"cost": [[11, 137101, 660]], "level": 328, "attributes": [[1, 4162500], [2, 41625000], [3, 2081250], [4, 2081250]], "nextLevel": 329, "id": 76004}, {"cost": [[11, 137101, 660]], "level": 329, "attributes": [[1, 4185000], [2, 41850000], [3, 2092500], [4, 2092500]], "nextLevel": 330, "id": 76004}, {"cost": [[11, 137101, 660]], "level": 330, "attributes": [[1, 4207500], [2, 42075000], [3, 2103750], [4, 2103750]], "nextLevel": 331, "id": 76004}, {"cost": [[11, 137101, 670]], "level": 331, "attributes": [[1, 4230300], [2, 42303000], [3, 2115150], [4, 2115150]], "nextLevel": 332, "id": 76004}, {"cost": [[11, 137101, 670]], "level": 332, "attributes": [[1, 4253100], [2, 42531000], [3, 2126550], [4, 2126550]], "nextLevel": 333, "id": 76004}, {"cost": [[11, 137101, 670]], "level": 333, "attributes": [[1, 4275900], [2, 42759000], [3, 2137950], [4, 2137950]], "nextLevel": 334, "id": 76004}, {"cost": [[11, 137101, 670]], "level": 334, "attributes": [[1, 4298700], [2, 42987000], [3, 2149350], [4, 2149350]], "nextLevel": 335, "id": 76004}, {"cost": [[11, 137101, 670]], "level": 335, "attributes": [[1, 4321500], [2, 43215000], [3, 2160750], [4, 2160750]], "nextLevel": 336, "id": 76004}, {"cost": [[11, 137101, 680]], "level": 336, "attributes": [[1, 4344600], [2, 43446000], [3, 2172300], [4, 2172300]], "nextLevel": 337, "id": 76004}, {"cost": [[11, 137101, 680]], "level": 337, "attributes": [[1, 4367700], [2, 43677000], [3, 2183850], [4, 2183850]], "nextLevel": 338, "id": 76004}, {"cost": [[11, 137101, 680]], "level": 338, "attributes": [[1, 4390800], [2, 43908000], [3, 2195400], [4, 2195400]], "nextLevel": 339, "id": 76004}, {"cost": [[11, 137101, 680]], "level": 339, "attributes": [[1, 4413900], [2, 44139000], [3, 2206950], [4, 2206950]], "nextLevel": 340, "id": 76004}, {"cost": [[11, 137101, 680]], "level": 340, "attributes": [[1, 4437000], [2, 44370000], [3, 2218500], [4, 2218500]], "nextLevel": 341, "id": 76004}, {"cost": [[11, 137101, 690]], "level": 341, "attributes": [[1, 4460400], [2, 44604000], [3, 2230200], [4, 2230200]], "nextLevel": 342, "id": 76004}, {"cost": [[11, 137101, 690]], "level": 342, "attributes": [[1, 4483800], [2, 44838000], [3, 2241900], [4, 2241900]], "nextLevel": 343, "id": 76004}, {"cost": [[11, 137101, 690]], "level": 343, "attributes": [[1, 4507200], [2, 45072000], [3, 2253600], [4, 2253600]], "nextLevel": 344, "id": 76004}, {"cost": [[11, 137101, 690]], "level": 344, "attributes": [[1, 4530600], [2, 45306000], [3, 2265300], [4, 2265300]], "nextLevel": 345, "id": 76004}, {"cost": [[11, 137101, 690]], "level": 345, "attributes": [[1, 4554000], [2, 45540000], [3, 2277000], [4, 2277000]], "nextLevel": 346, "id": 76004}, {"cost": [[11, 137101, 700]], "level": 346, "attributes": [[1, 4577700], [2, 45777000], [3, 2288850], [4, 2288850]], "nextLevel": 347, "id": 76004}, {"cost": [[11, 137101, 700]], "level": 347, "attributes": [[1, 4601400], [2, 46014000], [3, 2300700], [4, 2300700]], "nextLevel": 348, "id": 76004}, {"cost": [[11, 137101, 700]], "level": 348, "attributes": [[1, 4625100], [2, 46251000], [3, 2312550], [4, 2312550]], "nextLevel": 349, "id": 76004}, {"cost": [[11, 137101, 700]], "level": 349, "attributes": [[1, 4648800], [2, 46488000], [3, 2324400], [4, 2324400]], "nextLevel": 350, "id": 76004}, {"cost": [[11, 137101, 700]], "level": 350, "attributes": [[1, 4672500], [2, 46725000], [3, 2336250], [4, 2336250]], "nextLevel": 351, "id": 76004}, {"cost": [[11, 137101, 710]], "level": 351, "attributes": [[1, 4696500], [2, 46965000], [3, 2348250], [4, 2348250]], "nextLevel": 352, "id": 76004}, {"cost": [[11, 137101, 710]], "level": 352, "attributes": [[1, 4720500], [2, 47205000], [3, 2360250], [4, 2360250]], "nextLevel": 353, "id": 76004}, {"cost": [[11, 137101, 710]], "level": 353, "attributes": [[1, 4744500], [2, 47445000], [3, 2372250], [4, 2372250]], "nextLevel": 354, "id": 76004}, {"cost": [[11, 137101, 710]], "level": 354, "attributes": [[1, 4768500], [2, 47685000], [3, 2384250], [4, 2384250]], "nextLevel": 355, "id": 76004}, {"cost": [[11, 137101, 710]], "level": 355, "attributes": [[1, 4792500], [2, 47925000], [3, 2396250], [4, 2396250]], "nextLevel": 356, "id": 76004}, {"cost": [[11, 137101, 720]], "level": 356, "attributes": [[1, 4816800], [2, 48168000], [3, 2408400], [4, 2408400]], "nextLevel": 357, "id": 76004}, {"cost": [[11, 137101, 720]], "level": 357, "attributes": [[1, 4841100], [2, 48411000], [3, 2420550], [4, 2420550]], "nextLevel": 358, "id": 76004}, {"cost": [[11, 137101, 720]], "level": 358, "attributes": [[1, 4865400], [2, 48654000], [3, 2432700], [4, 2432700]], "nextLevel": 359, "id": 76004}, {"cost": [[11, 137101, 720]], "level": 359, "attributes": [[1, 4889700], [2, 48897000], [3, 2444850], [4, 2444850]], "nextLevel": 360, "id": 76004}, {"cost": [[11, 137101, 720]], "level": 360, "attributes": [[1, 4914000], [2, 49140000], [3, 2457000], [4, 2457000]], "nextLevel": 361, "id": 76004}, {"cost": [[11, 137101, 730]], "level": 361, "attributes": [[1, 4938600], [2, 49386000], [3, 2469300], [4, 2469300]], "nextLevel": 362, "id": 76004}, {"cost": [[11, 137101, 730]], "level": 362, "attributes": [[1, 4963200], [2, 49632000], [3, 2481600], [4, 2481600]], "nextLevel": 363, "id": 76004}, {"cost": [[11, 137101, 730]], "level": 363, "attributes": [[1, 4987800], [2, 49878000], [3, 2493900], [4, 2493900]], "nextLevel": 364, "id": 76004}, {"cost": [[11, 137101, 730]], "level": 364, "attributes": [[1, 5012400], [2, 50124000], [3, 2506200], [4, 2506200]], "nextLevel": 365, "id": 76004}, {"cost": [[11, 137101, 730]], "level": 365, "attributes": [[1, 5037000], [2, 50370000], [3, 2518500], [4, 2518500]], "nextLevel": 366, "id": 76004}, {"cost": [[11, 137101, 740]], "level": 366, "attributes": [[1, 5061900], [2, 50619000], [3, 2530950], [4, 2530950]], "nextLevel": 367, "id": 76004}, {"cost": [[11, 137101, 740]], "level": 367, "attributes": [[1, 5086800], [2, 50868000], [3, 2543400], [4, 2543400]], "nextLevel": 368, "id": 76004}, {"cost": [[11, 137101, 740]], "level": 368, "attributes": [[1, 5111700], [2, 51117000], [3, 2555850], [4, 2555850]], "nextLevel": 369, "id": 76004}, {"cost": [[11, 137101, 740]], "level": 369, "attributes": [[1, 5136600], [2, 51366000], [3, 2568300], [4, 2568300]], "nextLevel": 370, "id": 76004}, {"cost": [[11, 137101, 740]], "level": 370, "attributes": [[1, 5161500], [2, 51615000], [3, 2580750], [4, 2580750]], "nextLevel": 371, "id": 76004}, {"cost": [[11, 137101, 750]], "level": 371, "attributes": [[1, 5186700], [2, 51867000], [3, 2593350], [4, 2593350]], "nextLevel": 372, "id": 76004}, {"cost": [[11, 137101, 750]], "level": 372, "attributes": [[1, 5211900], [2, 52119000], [3, 2605950], [4, 2605950]], "nextLevel": 373, "id": 76004}, {"cost": [[11, 137101, 750]], "level": 373, "attributes": [[1, 5237100], [2, 52371000], [3, 2618550], [4, 2618550]], "nextLevel": 374, "id": 76004}, {"cost": [[11, 137101, 750]], "level": 374, "attributes": [[1, 5262300], [2, 52623000], [3, 2631150], [4, 2631150]], "nextLevel": 375, "id": 76004}, {"cost": [[11, 137101, 750]], "level": 375, "attributes": [[1, 5287500], [2, 52875000], [3, 2643750], [4, 2643750]], "nextLevel": 376, "id": 76004}, {"cost": [[11, 137101, 760]], "level": 376, "attributes": [[1, 5313000], [2, 53130000], [3, 2656500], [4, 2656500]], "nextLevel": 377, "id": 76004}, {"cost": [[11, 137101, 760]], "level": 377, "attributes": [[1, 5338500], [2, 53385000], [3, 2669250], [4, 2669250]], "nextLevel": 378, "id": 76004}, {"cost": [[11, 137101, 760]], "level": 378, "attributes": [[1, 5364000], [2, 53640000], [3, 2682000], [4, 2682000]], "nextLevel": 379, "id": 76004}, {"cost": [[11, 137101, 760]], "level": 379, "attributes": [[1, 5389500], [2, 53895000], [3, 2694750], [4, 2694750]], "nextLevel": 380, "id": 76004}, {"cost": [[11, 137101, 760]], "level": 380, "attributes": [[1, 5415000], [2, 54150000], [3, 2707500], [4, 2707500]], "nextLevel": 381, "id": 76004}, {"cost": [[11, 137101, 770]], "level": 381, "attributes": [[1, 5440800], [2, 54408000], [3, 2720400], [4, 2720400]], "nextLevel": 382, "id": 76004}, {"cost": [[11, 137101, 770]], "level": 382, "attributes": [[1, 5466600], [2, 54666000], [3, 2733300], [4, 2733300]], "nextLevel": 383, "id": 76004}, {"cost": [[11, 137101, 770]], "level": 383, "attributes": [[1, 5492400], [2, 54924000], [3, 2746200], [4, 2746200]], "nextLevel": 384, "id": 76004}, {"cost": [[11, 137101, 770]], "level": 384, "attributes": [[1, 5518200], [2, 55182000], [3, 2759100], [4, 2759100]], "nextLevel": 385, "id": 76004}, {"cost": [[11, 137101, 770]], "level": 385, "attributes": [[1, 5544000], [2, 55440000], [3, 2772000], [4, 2772000]], "nextLevel": 386, "id": 76004}, {"cost": [[11, 137101, 780]], "level": 386, "attributes": [[1, 5570100], [2, 55701000], [3, 2785050], [4, 2785050]], "nextLevel": 387, "id": 76004}, {"cost": [[11, 137101, 780]], "level": 387, "attributes": [[1, 5596200], [2, 55962000], [3, 2798100], [4, 2798100]], "nextLevel": 388, "id": 76004}, {"cost": [[11, 137101, 780]], "level": 388, "attributes": [[1, 5622300], [2, 56223000], [3, 2811150], [4, 2811150]], "nextLevel": 389, "id": 76004}, {"cost": [[11, 137101, 780]], "level": 389, "attributes": [[1, 5648400], [2, 56484000], [3, 2824200], [4, 2824200]], "nextLevel": 390, "id": 76004}, {"cost": [[11, 137101, 780]], "level": 390, "attributes": [[1, 5674500], [2, 56745000], [3, 2837250], [4, 2837250]], "nextLevel": 391, "id": 76004}, {"cost": [[11, 137101, 790]], "level": 391, "attributes": [[1, 5700900], [2, 57009000], [3, 2850450], [4, 2850450]], "nextLevel": 392, "id": 76004}, {"cost": [[11, 137101, 790]], "level": 392, "attributes": [[1, 5727300], [2, 57273000], [3, 2863650], [4, 2863650]], "nextLevel": 393, "id": 76004}, {"cost": [[11, 137101, 790]], "level": 393, "attributes": [[1, 5753700], [2, 57537000], [3, 2876850], [4, 2876850]], "nextLevel": 394, "id": 76004}, {"cost": [[11, 137101, 790]], "level": 394, "attributes": [[1, 5780100], [2, 57801000], [3, 2890050], [4, 2890050]], "nextLevel": 395, "id": 76004}, {"cost": [[11, 137101, 790]], "level": 395, "attributes": [[1, 5806500], [2, 58065000], [3, 2903250], [4, 2903250]], "nextLevel": 396, "id": 76004}, {"cost": [[11, 137101, 800]], "level": 396, "attributes": [[1, 5833200], [2, 58332000], [3, 2916600], [4, 2916600]], "nextLevel": 397, "id": 76004}, {"cost": [[11, 137101, 800]], "level": 397, "attributes": [[1, 5859900], [2, 58599000], [3, 2929950], [4, 2929950]], "nextLevel": 398, "id": 76004}, {"cost": [[11, 137101, 800]], "level": 398, "attributes": [[1, 5886600], [2, 58866000], [3, 2943300], [4, 2943300]], "nextLevel": 399, "id": 76004}, {"cost": [[11, 137101, 800]], "level": 399, "attributes": [[1, 5913300], [2, 59133000], [3, 2956650], [4, 2956650]], "nextLevel": 400, "id": 76004}, {"cost": [[11, 137101, 800]], "level": 400, "attributes": [[1, 5940000], [2, 59400000], [3, 2970000], [4, 2970000]], "nextLevel": 401, "id": 76004}, {"cost": [[11, 137101, 810]], "level": 401, "attributes": [[1, 5967000], [2, 59670000], [3, 2983500], [4, 2983500]], "nextLevel": 402, "id": 76004}, {"cost": [[11, 137101, 810]], "level": 402, "attributes": [[1, 5994000], [2, 59940000], [3, 2997000], [4, 2997000]], "nextLevel": 403, "id": 76004}, {"cost": [[11, 137101, 810]], "level": 403, "attributes": [[1, 6021000], [2, 60210000], [3, 3010500], [4, 3010500]], "nextLevel": 404, "id": 76004}, {"cost": [[11, 137101, 810]], "level": 404, "attributes": [[1, 6048000], [2, 60480000], [3, 3024000], [4, 3024000]], "nextLevel": 405, "id": 76004}, {"cost": [[11, 137101, 810]], "level": 405, "attributes": [[1, 6075000], [2, 60750000], [3, 3037500], [4, 3037500]], "nextLevel": 406, "id": 76004}, {"cost": [[11, 137101, 820]], "level": 406, "attributes": [[1, 6102300], [2, 61023000], [3, 3051150], [4, 3051150]], "nextLevel": 407, "id": 76004}, {"cost": [[11, 137101, 820]], "level": 407, "attributes": [[1, 6129600], [2, 61296000], [3, 3064800], [4, 3064800]], "nextLevel": 408, "id": 76004}, {"cost": [[11, 137101, 820]], "level": 408, "attributes": [[1, 6156900], [2, 61569000], [3, 3078450], [4, 3078450]], "nextLevel": 409, "id": 76004}, {"cost": [[11, 137101, 820]], "level": 409, "attributes": [[1, 6184200], [2, 61842000], [3, 3092100], [4, 3092100]], "nextLevel": 410, "id": 76004}, {"cost": [[11, 137101, 820]], "level": 410, "attributes": [[1, 6211500], [2, 62115000], [3, 3105750], [4, 3105750]], "nextLevel": 411, "id": 76004}, {"cost": [[11, 137101, 830]], "level": 411, "attributes": [[1, 6239100], [2, 62391000], [3, 3119550], [4, 3119550]], "nextLevel": 412, "id": 76004}, {"cost": [[11, 137101, 830]], "level": 412, "attributes": [[1, 6266700], [2, 62667000], [3, 3133350], [4, 3133350]], "nextLevel": 413, "id": 76004}, {"cost": [[11, 137101, 830]], "level": 413, "attributes": [[1, 6294300], [2, 62943000], [3, 3147150], [4, 3147150]], "nextLevel": 414, "id": 76004}, {"cost": [[11, 137101, 830]], "level": 414, "attributes": [[1, 6321900], [2, 63219000], [3, 3160950], [4, 3160950]], "nextLevel": 415, "id": 76004}, {"cost": [[11, 137101, 830]], "level": 415, "attributes": [[1, 6349500], [2, 63495000], [3, 3174750], [4, 3174750]], "nextLevel": 416, "id": 76004}, {"cost": [[11, 137101, 840]], "level": 416, "attributes": [[1, 6377400], [2, 63774000], [3, 3188700], [4, 3188700]], "nextLevel": 417, "id": 76004}, {"cost": [[11, 137101, 840]], "level": 417, "attributes": [[1, 6405300], [2, 64053000], [3, 3202650], [4, 3202650]], "nextLevel": 418, "id": 76004}, {"cost": [[11, 137101, 840]], "level": 418, "attributes": [[1, 6433200], [2, 64332000], [3, 3216600], [4, 3216600]], "nextLevel": 419, "id": 76004}, {"cost": [[11, 137101, 840]], "level": 419, "attributes": [[1, 6461100], [2, 64611000], [3, 3230550], [4, 3230550]], "nextLevel": 420, "id": 76004}, {"cost": [[11, 137101, 840]], "level": 420, "attributes": [[1, 6489000], [2, 64890000], [3, 3244500], [4, 3244500]], "nextLevel": 421, "id": 76004}, {"cost": [[11, 137101, 850]], "level": 421, "attributes": [[1, 6517200], [2, 65172000], [3, 3258600], [4, 3258600]], "nextLevel": 422, "id": 76004}, {"cost": [[11, 137101, 850]], "level": 422, "attributes": [[1, 6545400], [2, 65454000], [3, 3272700], [4, 3272700]], "nextLevel": 423, "id": 76004}, {"cost": [[11, 137101, 850]], "level": 423, "attributes": [[1, 6573600], [2, 65736000], [3, 3286800], [4, 3286800]], "nextLevel": 424, "id": 76004}, {"cost": [[11, 137101, 850]], "level": 424, "attributes": [[1, 6601800], [2, 66018000], [3, 3300900], [4, 3300900]], "nextLevel": 425, "id": 76004}, {"cost": [[11, 137101, 850]], "level": 425, "attributes": [[1, 6630000], [2, 66300000], [3, 3315000], [4, 3315000]], "nextLevel": 426, "id": 76004}, {"cost": [[11, 137101, 860]], "level": 426, "attributes": [[1, 6658500], [2, 66585000], [3, 3329250], [4, 3329250]], "nextLevel": 427, "id": 76004}, {"cost": [[11, 137101, 860]], "level": 427, "attributes": [[1, 6687000], [2, 66870000], [3, 3343500], [4, 3343500]], "nextLevel": 428, "id": 76004}, {"cost": [[11, 137101, 860]], "level": 428, "attributes": [[1, 6715500], [2, 67155000], [3, 3357750], [4, 3357750]], "nextLevel": 429, "id": 76004}, {"cost": [[11, 137101, 860]], "level": 429, "attributes": [[1, 6744000], [2, 67440000], [3, 3372000], [4, 3372000]], "nextLevel": 430, "id": 76004}, {"cost": [[11, 137101, 860]], "level": 430, "attributes": [[1, 6772500], [2, 67725000], [3, 3386250], [4, 3386250]], "nextLevel": 431, "id": 76004}, {"cost": [[11, 137101, 870]], "level": 431, "attributes": [[1, 6801300], [2, 68013000], [3, 3400650], [4, 3400650]], "nextLevel": 432, "id": 76004}, {"cost": [[11, 137101, 870]], "level": 432, "attributes": [[1, 6830100], [2, 68301000], [3, 3415050], [4, 3415050]], "nextLevel": 433, "id": 76004}, {"cost": [[11, 137101, 870]], "level": 433, "attributes": [[1, 6858900], [2, 68589000], [3, 3429450], [4, 3429450]], "nextLevel": 434, "id": 76004}, {"cost": [[11, 137101, 870]], "level": 434, "attributes": [[1, 6887700], [2, 68877000], [3, 3443850], [4, 3443850]], "nextLevel": 435, "id": 76004}, {"cost": [[11, 137101, 870]], "level": 435, "attributes": [[1, 6916500], [2, 69165000], [3, 3458250], [4, 3458250]], "nextLevel": 436, "id": 76004}, {"cost": [[11, 137101, 880]], "level": 436, "attributes": [[1, 6945600], [2, 69456000], [3, 3472800], [4, 3472800]], "nextLevel": 437, "id": 76004}, {"cost": [[11, 137101, 880]], "level": 437, "attributes": [[1, 6974700], [2, 69747000], [3, 3487350], [4, 3487350]], "nextLevel": 438, "id": 76004}, {"cost": [[11, 137101, 880]], "level": 438, "attributes": [[1, 7003800], [2, 70038000], [3, 3501900], [4, 3501900]], "nextLevel": 439, "id": 76004}, {"cost": [[11, 137101, 880]], "level": 439, "attributes": [[1, 7032900], [2, 70329000], [3, 3516450], [4, 3516450]], "nextLevel": 440, "id": 76004}, {"cost": [[11, 137101, 880]], "level": 440, "attributes": [[1, 7062000], [2, 70620000], [3, 3531000], [4, 3531000]], "nextLevel": 441, "id": 76004}, {"cost": [[11, 137101, 890]], "level": 441, "attributes": [[1, 7091400], [2, 70914000], [3, 3545700], [4, 3545700]], "nextLevel": 442, "id": 76004}, {"cost": [[11, 137101, 890]], "level": 442, "attributes": [[1, 7120800], [2, 71208000], [3, 3560400], [4, 3560400]], "nextLevel": 443, "id": 76004}, {"cost": [[11, 137101, 890]], "level": 443, "attributes": [[1, 7150200], [2, 71502000], [3, 3575100], [4, 3575100]], "nextLevel": 444, "id": 76004}, {"cost": [[11, 137101, 890]], "level": 444, "attributes": [[1, 7179600], [2, 71796000], [3, 3589800], [4, 3589800]], "nextLevel": 445, "id": 76004}, {"cost": [[11, 137101, 890]], "level": 445, "attributes": [[1, 7209000], [2, 72090000], [3, 3604500], [4, 3604500]], "nextLevel": 446, "id": 76004}, {"cost": [[11, 137101, 900]], "level": 446, "attributes": [[1, 7238700], [2, 72387000], [3, 3619350], [4, 3619350]], "nextLevel": 447, "id": 76004}, {"cost": [[11, 137101, 900]], "level": 447, "attributes": [[1, 7268400], [2, 72684000], [3, 3634200], [4, 3634200]], "nextLevel": 448, "id": 76004}, {"cost": [[11, 137101, 900]], "level": 448, "attributes": [[1, 7298100], [2, 72981000], [3, 3649050], [4, 3649050]], "nextLevel": 449, "id": 76004}, {"cost": [[11, 137101, 900]], "level": 449, "attributes": [[1, 7327800], [2, 73278000], [3, 3663900], [4, 3663900]], "nextLevel": 450, "id": 76004}, {"cost": [[11, 137101, 900]], "level": 450, "attributes": [[1, 7357500], [2, 73575000], [3, 3678750], [4, 3678750]], "nextLevel": 451, "id": 76004}, {"cost": [[11, 137101, 910]], "level": 451, "attributes": [[1, 7387500], [2, 73875000], [3, 3693750], [4, 3693750]], "nextLevel": 452, "id": 76004}, {"cost": [[11, 137101, 910]], "level": 452, "attributes": [[1, 7417500], [2, 74175000], [3, 3708750], [4, 3708750]], "nextLevel": 453, "id": 76004}, {"cost": [[11, 137101, 910]], "level": 453, "attributes": [[1, 7447500], [2, 74475000], [3, 3723750], [4, 3723750]], "nextLevel": 454, "id": 76004}, {"cost": [[11, 137101, 910]], "level": 454, "attributes": [[1, 7477500], [2, 74775000], [3, 3738750], [4, 3738750]], "nextLevel": 455, "id": 76004}, {"cost": [[11, 137101, 910]], "level": 455, "attributes": [[1, 7507500], [2, 75075000], [3, 3753750], [4, 3753750]], "nextLevel": 456, "id": 76004}, {"cost": [[11, 137101, 920]], "level": 456, "attributes": [[1, 7537800], [2, 75378000], [3, 3768900], [4, 3768900]], "nextLevel": 457, "id": 76004}, {"cost": [[11, 137101, 920]], "level": 457, "attributes": [[1, 7568100], [2, 75681000], [3, 3784050], [4, 3784050]], "nextLevel": 458, "id": 76004}, {"cost": [[11, 137101, 920]], "level": 458, "attributes": [[1, 7598400], [2, 75984000], [3, 3799200], [4, 3799200]], "nextLevel": 459, "id": 76004}, {"cost": [[11, 137101, 920]], "level": 459, "attributes": [[1, 7628700], [2, 76287000], [3, 3814350], [4, 3814350]], "nextLevel": 460, "id": 76004}, {"cost": [[11, 137101, 920]], "level": 460, "attributes": [[1, 7659000], [2, 76590000], [3, 3829500], [4, 3829500]], "nextLevel": 461, "id": 76004}, {"cost": [[11, 137101, 930]], "level": 461, "attributes": [[1, 7689600], [2, 76896000], [3, 3844800], [4, 3844800]], "nextLevel": 462, "id": 76004}, {"cost": [[11, 137101, 930]], "level": 462, "attributes": [[1, 7720200], [2, 77202000], [3, 3860100], [4, 3860100]], "nextLevel": 463, "id": 76004}, {"cost": [[11, 137101, 930]], "level": 463, "attributes": [[1, 7750800], [2, 77508000], [3, 3875400], [4, 3875400]], "nextLevel": 464, "id": 76004}, {"cost": [[11, 137101, 930]], "level": 464, "attributes": [[1, 7781400], [2, 77814000], [3, 3890700], [4, 3890700]], "nextLevel": 465, "id": 76004}, {"cost": [[11, 137101, 930]], "level": 465, "attributes": [[1, 7812000], [2, 78120000], [3, 3906000], [4, 3906000]], "nextLevel": 466, "id": 76004}, {"cost": [[11, 137101, 940]], "level": 466, "attributes": [[1, 7842900], [2, 78429000], [3, 3921450], [4, 3921450]], "nextLevel": 467, "id": 76004}, {"cost": [[11, 137101, 940]], "level": 467, "attributes": [[1, 7873800], [2, 78738000], [3, 3936900], [4, 3936900]], "nextLevel": 468, "id": 76004}, {"cost": [[11, 137101, 940]], "level": 468, "attributes": [[1, 7904700], [2, 79047000], [3, 3952350], [4, 3952350]], "nextLevel": 469, "id": 76004}, {"cost": [[11, 137101, 940]], "level": 469, "attributes": [[1, 7935600], [2, 79356000], [3, 3967800], [4, 3967800]], "nextLevel": 470, "id": 76004}, {"cost": [[11, 137101, 940]], "level": 470, "attributes": [[1, 7966500], [2, 79665000], [3, 3983250], [4, 3983250]], "nextLevel": 471, "id": 76004}, {"cost": [[11, 137101, 950]], "level": 471, "attributes": [[1, 7997700], [2, 79977000], [3, 3998850], [4, 3998850]], "nextLevel": 472, "id": 76004}, {"cost": [[11, 137101, 950]], "level": 472, "attributes": [[1, 8028900], [2, 80289000], [3, 4014450], [4, 4014450]], "nextLevel": 473, "id": 76004}, {"cost": [[11, 137101, 950]], "level": 473, "attributes": [[1, 8060100], [2, 80601000], [3, 4030050], [4, 4030050]], "nextLevel": 474, "id": 76004}, {"cost": [[11, 137101, 950]], "level": 474, "attributes": [[1, 8091300], [2, 80913000], [3, 4045650], [4, 4045650]], "nextLevel": 475, "id": 76004}, {"cost": [[11, 137101, 950]], "level": 475, "attributes": [[1, 8122500], [2, 81225000], [3, 4061250], [4, 4061250]], "nextLevel": 476, "id": 76004}, {"cost": [[11, 137101, 960]], "level": 476, "attributes": [[1, 8154000], [2, 81540000], [3, 4077000], [4, 4077000]], "nextLevel": 477, "id": 76004}, {"cost": [[11, 137101, 960]], "level": 477, "attributes": [[1, 8185500], [2, 81855000], [3, 4092750], [4, 4092750]], "nextLevel": 478, "id": 76004}, {"cost": [[11, 137101, 960]], "level": 478, "attributes": [[1, 8217000], [2, 82170000], [3, 4108500], [4, 4108500]], "nextLevel": 479, "id": 76004}, {"cost": [[11, 137101, 960]], "level": 479, "attributes": [[1, 8248500], [2, 82485000], [3, 4124250], [4, 4124250]], "nextLevel": 480, "id": 76004}, {"cost": [[11, 137101, 960]], "level": 480, "attributes": [[1, 8280000], [2, 82800000], [3, 4140000], [4, 4140000]], "nextLevel": 481, "id": 76004}, {"cost": [[11, 137101, 970]], "level": 481, "attributes": [[1, 8311800], [2, 83118000], [3, 4155900], [4, 4155900]], "nextLevel": 482, "id": 76004}, {"cost": [[11, 137101, 970]], "level": 482, "attributes": [[1, 8343600], [2, 83436000], [3, 4171800], [4, 4171800]], "nextLevel": 483, "id": 76004}, {"cost": [[11, 137101, 970]], "level": 483, "attributes": [[1, 8375400], [2, 83754000], [3, 4187700], [4, 4187700]], "nextLevel": 484, "id": 76004}, {"cost": [[11, 137101, 970]], "level": 484, "attributes": [[1, 8407200], [2, 84072000], [3, 4203600], [4, 4203600]], "nextLevel": 485, "id": 76004}, {"cost": [[11, 137101, 970]], "level": 485, "attributes": [[1, 8439000], [2, 84390000], [3, 4219500], [4, 4219500]], "nextLevel": 486, "id": 76004}, {"cost": [[11, 137101, 980]], "level": 486, "attributes": [[1, 8471100], [2, 84711000], [3, 4235550], [4, 4235550]], "nextLevel": 487, "id": 76004}, {"cost": [[11, 137101, 980]], "level": 487, "attributes": [[1, 8503200], [2, 85032000], [3, 4251600], [4, 4251600]], "nextLevel": 488, "id": 76004}, {"cost": [[11, 137101, 980]], "level": 488, "attributes": [[1, 8535300], [2, 85353000], [3, 4267650], [4, 4267650]], "nextLevel": 489, "id": 76004}, {"cost": [[11, 137101, 980]], "level": 489, "attributes": [[1, 8567400], [2, 85674000], [3, 4283700], [4, 4283700]], "nextLevel": 490, "id": 76004}, {"cost": [[11, 137101, 980]], "level": 490, "attributes": [[1, 8599500], [2, 85995000], [3, 4299750], [4, 4299750]], "nextLevel": 491, "id": 76004}, {"cost": [[11, 137101, 990]], "level": 491, "attributes": [[1, 8631900], [2, 86319000], [3, 4315950], [4, 4315950]], "nextLevel": 492, "id": 76004}, {"cost": [[11, 137101, 990]], "level": 492, "attributes": [[1, 8664300], [2, 86643000], [3, 4332150], [4, 4332150]], "nextLevel": 493, "id": 76004}, {"cost": [[11, 137101, 990]], "level": 493, "attributes": [[1, 8696700], [2, 86967000], [3, 4348350], [4, 4348350]], "nextLevel": 494, "id": 76004}, {"cost": [[11, 137101, 990]], "level": 494, "attributes": [[1, 8729100], [2, 87291000], [3, 4364550], [4, 4364550]], "nextLevel": 495, "id": 76004}, {"cost": [[11, 137101, 990]], "level": 495, "attributes": [[1, 8761500], [2, 87615000], [3, 4380750], [4, 4380750]], "nextLevel": 496, "id": 76004}, {"cost": [[11, 137101, 1000]], "level": 496, "attributes": [[1, 8794200], [2, 87942000], [3, 4397100], [4, 4397100]], "nextLevel": 497, "id": 76004}, {"cost": [[11, 137101, 1000]], "level": 497, "attributes": [[1, 8826900], [2, 88269000], [3, 4413450], [4, 4413450]], "nextLevel": 498, "id": 76004}, {"cost": [[11, 137101, 1000]], "level": 498, "attributes": [[1, 8859600], [2, 88596000], [3, 4429800], [4, 4429800]], "nextLevel": 499, "id": 76004}, {"cost": [[11, 137101, 1000]], "level": 499, "attributes": [[1, 8892300], [2, 88923000], [3, 4446150], [4, 4446150]], "nextLevel": 500, "id": 76004}, {"cost": [], "level": 500, "attributes": [[1, 8925000], [2, 89250000], [3, 4462500], [4, 4462500]], "nextLevel": 0, "id": 76004}, {"cost": [[11, 137101, 10]], "level": 1, "attributes": [[1, 3000], [2, 30000], [3, 1500], [4, 1500]], "nextLevel": 2, "id": 76005}, {"cost": [[11, 137101, 10]], "level": 2, "attributes": [[1, 6000], [2, 60000], [3, 3000], [4, 3000]], "nextLevel": 3, "id": 76005}, {"cost": [[11, 137101, 10]], "level": 3, "attributes": [[1, 9000], [2, 90000], [3, 4500], [4, 4500]], "nextLevel": 4, "id": 76005}, {"cost": [[11, 137101, 10]], "level": 4, "attributes": [[1, 12000], [2, 120000], [3, 6000], [4, 6000]], "nextLevel": 5, "id": 76005}, {"cost": [[11, 137101, 10]], "level": 5, "attributes": [[1, 15000], [2, 150000], [3, 7500], [4, 7500]], "nextLevel": 6, "id": 76005}, {"cost": [[11, 137101, 20]], "level": 6, "attributes": [[1, 18300], [2, 183000], [3, 9150], [4, 9150]], "nextLevel": 7, "id": 76005}, {"cost": [[11, 137101, 20]], "level": 7, "attributes": [[1, 21600], [2, 216000], [3, 10800], [4, 10800]], "nextLevel": 8, "id": 76005}, {"cost": [[11, 137101, 20]], "level": 8, "attributes": [[1, 24900], [2, 249000], [3, 12450], [4, 12450]], "nextLevel": 9, "id": 76005}, {"cost": [[11, 137101, 20]], "level": 9, "attributes": [[1, 28200], [2, 282000], [3, 14100], [4, 14100]], "nextLevel": 10, "id": 76005}, {"cost": [[11, 137101, 20]], "level": 10, "attributes": [[1, 31500], [2, 315000], [3, 15750], [4, 15750]], "nextLevel": 11, "id": 76005}, {"cost": [[11, 137101, 30]], "level": 11, "attributes": [[1, 35100], [2, 351000], [3, 17550], [4, 17550]], "nextLevel": 12, "id": 76005}, {"cost": [[11, 137101, 30]], "level": 12, "attributes": [[1, 38700], [2, 387000], [3, 19350], [4, 19350]], "nextLevel": 13, "id": 76005}, {"cost": [[11, 137101, 30]], "level": 13, "attributes": [[1, 42300], [2, 423000], [3, 21150], [4, 21150]], "nextLevel": 14, "id": 76005}, {"cost": [[11, 137101, 30]], "level": 14, "attributes": [[1, 45900], [2, 459000], [3, 22950], [4, 22950]], "nextLevel": 15, "id": 76005}, {"cost": [[11, 137101, 30]], "level": 15, "attributes": [[1, 49500], [2, 495000], [3, 24750], [4, 24750]], "nextLevel": 16, "id": 76005}, {"cost": [[11, 137101, 40]], "level": 16, "attributes": [[1, 53400], [2, 534000], [3, 26700], [4, 26700]], "nextLevel": 17, "id": 76005}, {"cost": [[11, 137101, 40]], "level": 17, "attributes": [[1, 57300], [2, 573000], [3, 28650], [4, 28650]], "nextLevel": 18, "id": 76005}, {"cost": [[11, 137101, 40]], "level": 18, "attributes": [[1, 61200], [2, 612000], [3, 30600], [4, 30600]], "nextLevel": 19, "id": 76005}, {"cost": [[11, 137101, 40]], "level": 19, "attributes": [[1, 65100], [2, 651000], [3, 32550], [4, 32550]], "nextLevel": 20, "id": 76005}, {"cost": [[11, 137101, 40]], "level": 20, "attributes": [[1, 69000], [2, 690000], [3, 34500], [4, 34500]], "nextLevel": 21, "id": 76005}, {"cost": [[11, 137101, 50]], "level": 21, "attributes": [[1, 73200], [2, 732000], [3, 36600], [4, 36600]], "nextLevel": 22, "id": 76005}, {"cost": [[11, 137101, 50]], "level": 22, "attributes": [[1, 77400], [2, 774000], [3, 38700], [4, 38700]], "nextLevel": 23, "id": 76005}, {"cost": [[11, 137101, 50]], "level": 23, "attributes": [[1, 81600], [2, 816000], [3, 40800], [4, 40800]], "nextLevel": 24, "id": 76005}, {"cost": [[11, 137101, 50]], "level": 24, "attributes": [[1, 85800], [2, 858000], [3, 42900], [4, 42900]], "nextLevel": 25, "id": 76005}, {"cost": [[11, 137101, 50]], "level": 25, "attributes": [[1, 90000], [2, 900000], [3, 45000], [4, 45000]], "nextLevel": 26, "id": 76005}, {"cost": [[11, 137101, 60]], "level": 26, "attributes": [[1, 94500], [2, 945000], [3, 47250], [4, 47250]], "nextLevel": 27, "id": 76005}, {"cost": [[11, 137101, 60]], "level": 27, "attributes": [[1, 99000], [2, 990000], [3, 49500], [4, 49500]], "nextLevel": 28, "id": 76005}, {"cost": [[11, 137101, 60]], "level": 28, "attributes": [[1, 103500], [2, 1035000], [3, 51750], [4, 51750]], "nextLevel": 29, "id": 76005}, {"cost": [[11, 137101, 60]], "level": 29, "attributes": [[1, 108000], [2, 1080000], [3, 54000], [4, 54000]], "nextLevel": 30, "id": 76005}, {"cost": [[11, 137101, 60]], "level": 30, "attributes": [[1, 112500], [2, 1125000], [3, 56250], [4, 56250]], "nextLevel": 31, "id": 76005}, {"cost": [[11, 137101, 70]], "level": 31, "attributes": [[1, 117300], [2, 1173000], [3, 58650], [4, 58650]], "nextLevel": 32, "id": 76005}, {"cost": [[11, 137101, 70]], "level": 32, "attributes": [[1, 122100], [2, 1221000], [3, 61050], [4, 61050]], "nextLevel": 33, "id": 76005}, {"cost": [[11, 137101, 70]], "level": 33, "attributes": [[1, 126900], [2, 1269000], [3, 63450], [4, 63450]], "nextLevel": 34, "id": 76005}, {"cost": [[11, 137101, 70]], "level": 34, "attributes": [[1, 131700], [2, 1317000], [3, 65850], [4, 65850]], "nextLevel": 35, "id": 76005}, {"cost": [[11, 137101, 70]], "level": 35, "attributes": [[1, 136500], [2, 1365000], [3, 68250], [4, 68250]], "nextLevel": 36, "id": 76005}, {"cost": [[11, 137101, 80]], "level": 36, "attributes": [[1, 141600], [2, 1416000], [3, 70800], [4, 70800]], "nextLevel": 37, "id": 76005}, {"cost": [[11, 137101, 80]], "level": 37, "attributes": [[1, 146700], [2, 1467000], [3, 73350], [4, 73350]], "nextLevel": 38, "id": 76005}, {"cost": [[11, 137101, 80]], "level": 38, "attributes": [[1, 151800], [2, 1518000], [3, 75900], [4, 75900]], "nextLevel": 39, "id": 76005}, {"cost": [[11, 137101, 80]], "level": 39, "attributes": [[1, 156900], [2, 1569000], [3, 78450], [4, 78450]], "nextLevel": 40, "id": 76005}, {"cost": [[11, 137101, 80]], "level": 40, "attributes": [[1, 162000], [2, 1620000], [3, 81000], [4, 81000]], "nextLevel": 41, "id": 76005}, {"cost": [[11, 137101, 90]], "level": 41, "attributes": [[1, 167400], [2, 1674000], [3, 83700], [4, 83700]], "nextLevel": 42, "id": 76005}, {"cost": [[11, 137101, 90]], "level": 42, "attributes": [[1, 172800], [2, 1728000], [3, 86400], [4, 86400]], "nextLevel": 43, "id": 76005}, {"cost": [[11, 137101, 90]], "level": 43, "attributes": [[1, 178200], [2, 1782000], [3, 89100], [4, 89100]], "nextLevel": 44, "id": 76005}, {"cost": [[11, 137101, 90]], "level": 44, "attributes": [[1, 183600], [2, 1836000], [3, 91800], [4, 91800]], "nextLevel": 45, "id": 76005}, {"cost": [[11, 137101, 90]], "level": 45, "attributes": [[1, 189000], [2, 1890000], [3, 94500], [4, 94500]], "nextLevel": 46, "id": 76005}, {"cost": [[11, 137101, 100]], "level": 46, "attributes": [[1, 194700], [2, 1947000], [3, 97350], [4, 97350]], "nextLevel": 47, "id": 76005}, {"cost": [[11, 137101, 100]], "level": 47, "attributes": [[1, 200400], [2, 2004000], [3, 100200], [4, 100200]], "nextLevel": 48, "id": 76005}, {"cost": [[11, 137101, 100]], "level": 48, "attributes": [[1, 206100], [2, 2061000], [3, 103050], [4, 103050]], "nextLevel": 49, "id": 76005}, {"cost": [[11, 137101, 100]], "level": 49, "attributes": [[1, 211800], [2, 2118000], [3, 105900], [4, 105900]], "nextLevel": 50, "id": 76005}, {"cost": [[11, 137101, 100]], "level": 50, "attributes": [[1, 217500], [2, 2175000], [3, 108750], [4, 108750]], "nextLevel": 51, "id": 76005}, {"cost": [[11, 137101, 110]], "level": 51, "attributes": [[1, 223500], [2, 2235000], [3, 111750], [4, 111750]], "nextLevel": 52, "id": 76005}, {"cost": [[11, 137101, 110]], "level": 52, "attributes": [[1, 229500], [2, 2295000], [3, 114750], [4, 114750]], "nextLevel": 53, "id": 76005}, {"cost": [[11, 137101, 110]], "level": 53, "attributes": [[1, 235500], [2, 2355000], [3, 117750], [4, 117750]], "nextLevel": 54, "id": 76005}, {"cost": [[11, 137101, 110]], "level": 54, "attributes": [[1, 241500], [2, 2415000], [3, 120750], [4, 120750]], "nextLevel": 55, "id": 76005}, {"cost": [[11, 137101, 110]], "level": 55, "attributes": [[1, 247500], [2, 2475000], [3, 123750], [4, 123750]], "nextLevel": 56, "id": 76005}, {"cost": [[11, 137101, 120]], "level": 56, "attributes": [[1, 253800], [2, 2538000], [3, 126900], [4, 126900]], "nextLevel": 57, "id": 76005}, {"cost": [[11, 137101, 120]], "level": 57, "attributes": [[1, 260100], [2, 2601000], [3, 130050], [4, 130050]], "nextLevel": 58, "id": 76005}, {"cost": [[11, 137101, 120]], "level": 58, "attributes": [[1, 266400], [2, 2664000], [3, 133200], [4, 133200]], "nextLevel": 59, "id": 76005}, {"cost": [[11, 137101, 120]], "level": 59, "attributes": [[1, 272700], [2, 2727000], [3, 136350], [4, 136350]], "nextLevel": 60, "id": 76005}, {"cost": [[11, 137101, 120]], "level": 60, "attributes": [[1, 279000], [2, 2790000], [3, 139500], [4, 139500]], "nextLevel": 61, "id": 76005}, {"cost": [[11, 137101, 130]], "level": 61, "attributes": [[1, 285600], [2, 2856000], [3, 142800], [4, 142800]], "nextLevel": 62, "id": 76005}, {"cost": [[11, 137101, 130]], "level": 62, "attributes": [[1, 292200], [2, 2922000], [3, 146100], [4, 146100]], "nextLevel": 63, "id": 76005}, {"cost": [[11, 137101, 130]], "level": 63, "attributes": [[1, 298800], [2, 2988000], [3, 149400], [4, 149400]], "nextLevel": 64, "id": 76005}, {"cost": [[11, 137101, 130]], "level": 64, "attributes": [[1, 305400], [2, 3054000], [3, 152700], [4, 152700]], "nextLevel": 65, "id": 76005}, {"cost": [[11, 137101, 130]], "level": 65, "attributes": [[1, 312000], [2, 3120000], [3, 156000], [4, 156000]], "nextLevel": 66, "id": 76005}, {"cost": [[11, 137101, 140]], "level": 66, "attributes": [[1, 318900], [2, 3189000], [3, 159450], [4, 159450]], "nextLevel": 67, "id": 76005}, {"cost": [[11, 137101, 140]], "level": 67, "attributes": [[1, 325800], [2, 3258000], [3, 162900], [4, 162900]], "nextLevel": 68, "id": 76005}, {"cost": [[11, 137101, 140]], "level": 68, "attributes": [[1, 332700], [2, 3327000], [3, 166350], [4, 166350]], "nextLevel": 69, "id": 76005}, {"cost": [[11, 137101, 140]], "level": 69, "attributes": [[1, 339600], [2, 3396000], [3, 169800], [4, 169800]], "nextLevel": 70, "id": 76005}, {"cost": [[11, 137101, 140]], "level": 70, "attributes": [[1, 346500], [2, 3465000], [3, 173250], [4, 173250]], "nextLevel": 71, "id": 76005}, {"cost": [[11, 137101, 150]], "level": 71, "attributes": [[1, 353700], [2, 3537000], [3, 176850], [4, 176850]], "nextLevel": 72, "id": 76005}, {"cost": [[11, 137101, 150]], "level": 72, "attributes": [[1, 360900], [2, 3609000], [3, 180450], [4, 180450]], "nextLevel": 73, "id": 76005}, {"cost": [[11, 137101, 150]], "level": 73, "attributes": [[1, 368100], [2, 3681000], [3, 184050], [4, 184050]], "nextLevel": 74, "id": 76005}, {"cost": [[11, 137101, 150]], "level": 74, "attributes": [[1, 375300], [2, 3753000], [3, 187650], [4, 187650]], "nextLevel": 75, "id": 76005}, {"cost": [[11, 137101, 150]], "level": 75, "attributes": [[1, 382500], [2, 3825000], [3, 191250], [4, 191250]], "nextLevel": 76, "id": 76005}, {"cost": [[11, 137101, 160]], "level": 76, "attributes": [[1, 390000], [2, 3900000], [3, 195000], [4, 195000]], "nextLevel": 77, "id": 76005}, {"cost": [[11, 137101, 160]], "level": 77, "attributes": [[1, 397500], [2, 3975000], [3, 198750], [4, 198750]], "nextLevel": 78, "id": 76005}, {"cost": [[11, 137101, 160]], "level": 78, "attributes": [[1, 405000], [2, 4050000], [3, 202500], [4, 202500]], "nextLevel": 79, "id": 76005}, {"cost": [[11, 137101, 160]], "level": 79, "attributes": [[1, 412500], [2, 4125000], [3, 206250], [4, 206250]], "nextLevel": 80, "id": 76005}, {"cost": [[11, 137101, 160]], "level": 80, "attributes": [[1, 420000], [2, 4200000], [3, 210000], [4, 210000]], "nextLevel": 81, "id": 76005}, {"cost": [[11, 137101, 170]], "level": 81, "attributes": [[1, 427800], [2, 4278000], [3, 213900], [4, 213900]], "nextLevel": 82, "id": 76005}, {"cost": [[11, 137101, 170]], "level": 82, "attributes": [[1, 435600], [2, 4356000], [3, 217800], [4, 217800]], "nextLevel": 83, "id": 76005}, {"cost": [[11, 137101, 170]], "level": 83, "attributes": [[1, 443400], [2, 4434000], [3, 221700], [4, 221700]], "nextLevel": 84, "id": 76005}, {"cost": [[11, 137101, 170]], "level": 84, "attributes": [[1, 451200], [2, 4512000], [3, 225600], [4, 225600]], "nextLevel": 85, "id": 76005}, {"cost": [[11, 137101, 170]], "level": 85, "attributes": [[1, 459000], [2, 4590000], [3, 229500], [4, 229500]], "nextLevel": 86, "id": 76005}, {"cost": [[11, 137101, 180]], "level": 86, "attributes": [[1, 467100], [2, 4671000], [3, 233550], [4, 233550]], "nextLevel": 87, "id": 76005}, {"cost": [[11, 137101, 180]], "level": 87, "attributes": [[1, 475200], [2, 4752000], [3, 237600], [4, 237600]], "nextLevel": 88, "id": 76005}, {"cost": [[11, 137101, 180]], "level": 88, "attributes": [[1, 483300], [2, 4833000], [3, 241650], [4, 241650]], "nextLevel": 89, "id": 76005}, {"cost": [[11, 137101, 180]], "level": 89, "attributes": [[1, 491400], [2, 4914000], [3, 245700], [4, 245700]], "nextLevel": 90, "id": 76005}, {"cost": [[11, 137101, 180]], "level": 90, "attributes": [[1, 499500], [2, 4995000], [3, 249750], [4, 249750]], "nextLevel": 91, "id": 76005}, {"cost": [[11, 137101, 190]], "level": 91, "attributes": [[1, 507900], [2, 5079000], [3, 253950], [4, 253950]], "nextLevel": 92, "id": 76005}, {"cost": [[11, 137101, 190]], "level": 92, "attributes": [[1, 516300], [2, 5163000], [3, 258150], [4, 258150]], "nextLevel": 93, "id": 76005}, {"cost": [[11, 137101, 190]], "level": 93, "attributes": [[1, 524700], [2, 5247000], [3, 262350], [4, 262350]], "nextLevel": 94, "id": 76005}, {"cost": [[11, 137101, 190]], "level": 94, "attributes": [[1, 533100], [2, 5331000], [3, 266550], [4, 266550]], "nextLevel": 95, "id": 76005}, {"cost": [[11, 137101, 190]], "level": 95, "attributes": [[1, 541500], [2, 5415000], [3, 270750], [4, 270750]], "nextLevel": 96, "id": 76005}, {"cost": [[11, 137101, 200]], "level": 96, "attributes": [[1, 550200], [2, 5502000], [3, 275100], [4, 275100]], "nextLevel": 97, "id": 76005}, {"cost": [[11, 137101, 200]], "level": 97, "attributes": [[1, 558900], [2, 5589000], [3, 279450], [4, 279450]], "nextLevel": 98, "id": 76005}, {"cost": [[11, 137101, 200]], "level": 98, "attributes": [[1, 567600], [2, 5676000], [3, 283800], [4, 283800]], "nextLevel": 99, "id": 76005}, {"cost": [[11, 137101, 200]], "level": 99, "attributes": [[1, 576300], [2, 5763000], [3, 288150], [4, 288150]], "nextLevel": 100, "id": 76005}, {"cost": [[11, 137101, 200]], "level": 100, "attributes": [[1, 585000], [2, 5850000], [3, 292500], [4, 292500]], "nextLevel": 101, "id": 76005}, {"cost": [[11, 137101, 210]], "level": 101, "attributes": [[1, 594000], [2, 5940000], [3, 297000], [4, 297000]], "nextLevel": 102, "id": 76005}, {"cost": [[11, 137101, 210]], "level": 102, "attributes": [[1, 603000], [2, 6030000], [3, 301500], [4, 301500]], "nextLevel": 103, "id": 76005}, {"cost": [[11, 137101, 210]], "level": 103, "attributes": [[1, 612000], [2, 6120000], [3, 306000], [4, 306000]], "nextLevel": 104, "id": 76005}, {"cost": [[11, 137101, 210]], "level": 104, "attributes": [[1, 621000], [2, 6210000], [3, 310500], [4, 310500]], "nextLevel": 105, "id": 76005}, {"cost": [[11, 137101, 210]], "level": 105, "attributes": [[1, 630000], [2, 6300000], [3, 315000], [4, 315000]], "nextLevel": 106, "id": 76005}, {"cost": [[11, 137101, 220]], "level": 106, "attributes": [[1, 639300], [2, 6393000], [3, 319650], [4, 319650]], "nextLevel": 107, "id": 76005}, {"cost": [[11, 137101, 220]], "level": 107, "attributes": [[1, 648600], [2, 6486000], [3, 324300], [4, 324300]], "nextLevel": 108, "id": 76005}, {"cost": [[11, 137101, 220]], "level": 108, "attributes": [[1, 657900], [2, 6579000], [3, 328950], [4, 328950]], "nextLevel": 109, "id": 76005}, {"cost": [[11, 137101, 220]], "level": 109, "attributes": [[1, 667200], [2, 6672000], [3, 333600], [4, 333600]], "nextLevel": 110, "id": 76005}, {"cost": [[11, 137101, 220]], "level": 110, "attributes": [[1, 676500], [2, 6765000], [3, 338250], [4, 338250]], "nextLevel": 111, "id": 76005}, {"cost": [[11, 137101, 230]], "level": 111, "attributes": [[1, 686100], [2, 6861000], [3, 343050], [4, 343050]], "nextLevel": 112, "id": 76005}, {"cost": [[11, 137101, 230]], "level": 112, "attributes": [[1, 695700], [2, 6957000], [3, 347850], [4, 347850]], "nextLevel": 113, "id": 76005}, {"cost": [[11, 137101, 230]], "level": 113, "attributes": [[1, 705300], [2, 7053000], [3, 352650], [4, 352650]], "nextLevel": 114, "id": 76005}, {"cost": [[11, 137101, 230]], "level": 114, "attributes": [[1, 714900], [2, 7149000], [3, 357450], [4, 357450]], "nextLevel": 115, "id": 76005}, {"cost": [[11, 137101, 230]], "level": 115, "attributes": [[1, 724500], [2, 7245000], [3, 362250], [4, 362250]], "nextLevel": 116, "id": 76005}, {"cost": [[11, 137101, 240]], "level": 116, "attributes": [[1, 734400], [2, 7344000], [3, 367200], [4, 367200]], "nextLevel": 117, "id": 76005}, {"cost": [[11, 137101, 240]], "level": 117, "attributes": [[1, 744300], [2, 7443000], [3, 372150], [4, 372150]], "nextLevel": 118, "id": 76005}, {"cost": [[11, 137101, 240]], "level": 118, "attributes": [[1, 754200], [2, 7542000], [3, 377100], [4, 377100]], "nextLevel": 119, "id": 76005}, {"cost": [[11, 137101, 240]], "level": 119, "attributes": [[1, 764100], [2, 7641000], [3, 382050], [4, 382050]], "nextLevel": 120, "id": 76005}, {"cost": [[11, 137101, 240]], "level": 120, "attributes": [[1, 774000], [2, 7740000], [3, 387000], [4, 387000]], "nextLevel": 121, "id": 76005}, {"cost": [[11, 137101, 250]], "level": 121, "attributes": [[1, 784200], [2, 7842000], [3, 392100], [4, 392100]], "nextLevel": 122, "id": 76005}, {"cost": [[11, 137101, 250]], "level": 122, "attributes": [[1, 794400], [2, 7944000], [3, 397200], [4, 397200]], "nextLevel": 123, "id": 76005}, {"cost": [[11, 137101, 250]], "level": 123, "attributes": [[1, 804600], [2, 8046000], [3, 402300], [4, 402300]], "nextLevel": 124, "id": 76005}, {"cost": [[11, 137101, 250]], "level": 124, "attributes": [[1, 814800], [2, 8148000], [3, 407400], [4, 407400]], "nextLevel": 125, "id": 76005}, {"cost": [[11, 137101, 250]], "level": 125, "attributes": [[1, 825000], [2, 8250000], [3, 412500], [4, 412500]], "nextLevel": 126, "id": 76005}, {"cost": [[11, 137101, 260]], "level": 126, "attributes": [[1, 835500], [2, 8355000], [3, 417750], [4, 417750]], "nextLevel": 127, "id": 76005}, {"cost": [[11, 137101, 260]], "level": 127, "attributes": [[1, 846000], [2, 8460000], [3, 423000], [4, 423000]], "nextLevel": 128, "id": 76005}, {"cost": [[11, 137101, 260]], "level": 128, "attributes": [[1, 856500], [2, 8565000], [3, 428250], [4, 428250]], "nextLevel": 129, "id": 76005}, {"cost": [[11, 137101, 260]], "level": 129, "attributes": [[1, 867000], [2, 8670000], [3, 433500], [4, 433500]], "nextLevel": 130, "id": 76005}, {"cost": [[11, 137101, 260]], "level": 130, "attributes": [[1, 877500], [2, 8775000], [3, 438750], [4, 438750]], "nextLevel": 131, "id": 76005}, {"cost": [[11, 137101, 270]], "level": 131, "attributes": [[1, 888300], [2, 8883000], [3, 444150], [4, 444150]], "nextLevel": 132, "id": 76005}, {"cost": [[11, 137101, 270]], "level": 132, "attributes": [[1, 899100], [2, 8991000], [3, 449550], [4, 449550]], "nextLevel": 133, "id": 76005}, {"cost": [[11, 137101, 270]], "level": 133, "attributes": [[1, 909900], [2, 9099000], [3, 454950], [4, 454950]], "nextLevel": 134, "id": 76005}, {"cost": [[11, 137101, 270]], "level": 134, "attributes": [[1, 920700], [2, 9207000], [3, 460350], [4, 460350]], "nextLevel": 135, "id": 76005}, {"cost": [[11, 137101, 270]], "level": 135, "attributes": [[1, 931500], [2, 9315000], [3, 465750], [4, 465750]], "nextLevel": 136, "id": 76005}, {"cost": [[11, 137101, 280]], "level": 136, "attributes": [[1, 942600], [2, 9426000], [3, 471300], [4, 471300]], "nextLevel": 137, "id": 76005}, {"cost": [[11, 137101, 280]], "level": 137, "attributes": [[1, 953700], [2, 9537000], [3, 476850], [4, 476850]], "nextLevel": 138, "id": 76005}, {"cost": [[11, 137101, 280]], "level": 138, "attributes": [[1, 964800], [2, 9648000], [3, 482400], [4, 482400]], "nextLevel": 139, "id": 76005}, {"cost": [[11, 137101, 280]], "level": 139, "attributes": [[1, 975900], [2, 9759000], [3, 487950], [4, 487950]], "nextLevel": 140, "id": 76005}, {"cost": [[11, 137101, 280]], "level": 140, "attributes": [[1, 987000], [2, 9870000], [3, 493500], [4, 493500]], "nextLevel": 141, "id": 76005}, {"cost": [[11, 137101, 290]], "level": 141, "attributes": [[1, 998400], [2, 9984000], [3, 499200], [4, 499200]], "nextLevel": 142, "id": 76005}, {"cost": [[11, 137101, 290]], "level": 142, "attributes": [[1, 1009800], [2, 10098000], [3, 504900], [4, 504900]], "nextLevel": 143, "id": 76005}, {"cost": [[11, 137101, 290]], "level": 143, "attributes": [[1, 1021200], [2, 10212000], [3, 510600], [4, 510600]], "nextLevel": 144, "id": 76005}, {"cost": [[11, 137101, 290]], "level": 144, "attributes": [[1, 1032600], [2, 10326000], [3, 516300], [4, 516300]], "nextLevel": 145, "id": 76005}, {"cost": [[11, 137101, 290]], "level": 145, "attributes": [[1, 1044000], [2, 10440000], [3, 522000], [4, 522000]], "nextLevel": 146, "id": 76005}, {"cost": [[11, 137101, 300]], "level": 146, "attributes": [[1, 1055700], [2, 10557000], [3, 527850], [4, 527850]], "nextLevel": 147, "id": 76005}, {"cost": [[11, 137101, 300]], "level": 147, "attributes": [[1, 1067400], [2, 10674000], [3, 533700], [4, 533700]], "nextLevel": 148, "id": 76005}, {"cost": [[11, 137101, 300]], "level": 148, "attributes": [[1, 1079100], [2, 10791000], [3, 539550], [4, 539550]], "nextLevel": 149, "id": 76005}, {"cost": [[11, 137101, 300]], "level": 149, "attributes": [[1, 1090800], [2, 10908000], [3, 545400], [4, 545400]], "nextLevel": 150, "id": 76005}, {"cost": [[11, 137101, 300]], "level": 150, "attributes": [[1, 1102500], [2, 11025000], [3, 551250], [4, 551250]], "nextLevel": 151, "id": 76005}, {"cost": [[11, 137101, 310]], "level": 151, "attributes": [[1, 1114500], [2, 11145000], [3, 557250], [4, 557250]], "nextLevel": 152, "id": 76005}, {"cost": [[11, 137101, 310]], "level": 152, "attributes": [[1, 1126500], [2, 11265000], [3, 563250], [4, 563250]], "nextLevel": 153, "id": 76005}, {"cost": [[11, 137101, 310]], "level": 153, "attributes": [[1, 1138500], [2, 11385000], [3, 569250], [4, 569250]], "nextLevel": 154, "id": 76005}, {"cost": [[11, 137101, 310]], "level": 154, "attributes": [[1, 1150500], [2, 11505000], [3, 575250], [4, 575250]], "nextLevel": 155, "id": 76005}, {"cost": [[11, 137101, 310]], "level": 155, "attributes": [[1, 1162500], [2, 11625000], [3, 581250], [4, 581250]], "nextLevel": 156, "id": 76005}, {"cost": [[11, 137101, 320]], "level": 156, "attributes": [[1, 1174800], [2, 11748000], [3, 587400], [4, 587400]], "nextLevel": 157, "id": 76005}, {"cost": [[11, 137101, 320]], "level": 157, "attributes": [[1, 1187100], [2, 11871000], [3, 593550], [4, 593550]], "nextLevel": 158, "id": 76005}, {"cost": [[11, 137101, 320]], "level": 158, "attributes": [[1, 1199400], [2, 11994000], [3, 599700], [4, 599700]], "nextLevel": 159, "id": 76005}, {"cost": [[11, 137101, 320]], "level": 159, "attributes": [[1, 1211700], [2, 12117000], [3, 605850], [4, 605850]], "nextLevel": 160, "id": 76005}, {"cost": [[11, 137101, 320]], "level": 160, "attributes": [[1, 1224000], [2, 12240000], [3, 612000], [4, 612000]], "nextLevel": 161, "id": 76005}, {"cost": [[11, 137101, 330]], "level": 161, "attributes": [[1, 1236600], [2, 12366000], [3, 618300], [4, 618300]], "nextLevel": 162, "id": 76005}, {"cost": [[11, 137101, 330]], "level": 162, "attributes": [[1, 1249200], [2, 12492000], [3, 624600], [4, 624600]], "nextLevel": 163, "id": 76005}, {"cost": [[11, 137101, 330]], "level": 163, "attributes": [[1, 1261800], [2, 12618000], [3, 630900], [4, 630900]], "nextLevel": 164, "id": 76005}, {"cost": [[11, 137101, 330]], "level": 164, "attributes": [[1, 1274400], [2, 12744000], [3, 637200], [4, 637200]], "nextLevel": 165, "id": 76005}, {"cost": [[11, 137101, 330]], "level": 165, "attributes": [[1, 1287000], [2, 12870000], [3, 643500], [4, 643500]], "nextLevel": 166, "id": 76005}, {"cost": [[11, 137101, 340]], "level": 166, "attributes": [[1, 1299900], [2, 12999000], [3, 649950], [4, 649950]], "nextLevel": 167, "id": 76005}, {"cost": [[11, 137101, 340]], "level": 167, "attributes": [[1, 1312800], [2, 13128000], [3, 656400], [4, 656400]], "nextLevel": 168, "id": 76005}, {"cost": [[11, 137101, 340]], "level": 168, "attributes": [[1, 1325700], [2, 13257000], [3, 662850], [4, 662850]], "nextLevel": 169, "id": 76005}, {"cost": [[11, 137101, 340]], "level": 169, "attributes": [[1, 1338600], [2, 13386000], [3, 669300], [4, 669300]], "nextLevel": 170, "id": 76005}, {"cost": [[11, 137101, 340]], "level": 170, "attributes": [[1, 1351500], [2, 13515000], [3, 675750], [4, 675750]], "nextLevel": 171, "id": 76005}, {"cost": [[11, 137101, 350]], "level": 171, "attributes": [[1, 1364700], [2, 13647000], [3, 682350], [4, 682350]], "nextLevel": 172, "id": 76005}, {"cost": [[11, 137101, 350]], "level": 172, "attributes": [[1, 1377900], [2, 13779000], [3, 688950], [4, 688950]], "nextLevel": 173, "id": 76005}, {"cost": [[11, 137101, 350]], "level": 173, "attributes": [[1, 1391100], [2, 13911000], [3, 695550], [4, 695550]], "nextLevel": 174, "id": 76005}, {"cost": [[11, 137101, 350]], "level": 174, "attributes": [[1, 1404300], [2, 14043000], [3, 702150], [4, 702150]], "nextLevel": 175, "id": 76005}, {"cost": [[11, 137101, 350]], "level": 175, "attributes": [[1, 1417500], [2, 14175000], [3, 708750], [4, 708750]], "nextLevel": 176, "id": 76005}, {"cost": [[11, 137101, 360]], "level": 176, "attributes": [[1, 1431000], [2, 14310000], [3, 715500], [4, 715500]], "nextLevel": 177, "id": 76005}, {"cost": [[11, 137101, 360]], "level": 177, "attributes": [[1, 1444500], [2, 14445000], [3, 722250], [4, 722250]], "nextLevel": 178, "id": 76005}, {"cost": [[11, 137101, 360]], "level": 178, "attributes": [[1, 1458000], [2, 14580000], [3, 729000], [4, 729000]], "nextLevel": 179, "id": 76005}, {"cost": [[11, 137101, 360]], "level": 179, "attributes": [[1, 1471500], [2, 14715000], [3, 735750], [4, 735750]], "nextLevel": 180, "id": 76005}, {"cost": [[11, 137101, 360]], "level": 180, "attributes": [[1, 1485000], [2, 14850000], [3, 742500], [4, 742500]], "nextLevel": 181, "id": 76005}, {"cost": [[11, 137101, 370]], "level": 181, "attributes": [[1, 1498800], [2, 14988000], [3, 749400], [4, 749400]], "nextLevel": 182, "id": 76005}, {"cost": [[11, 137101, 370]], "level": 182, "attributes": [[1, 1512600], [2, 15126000], [3, 756300], [4, 756300]], "nextLevel": 183, "id": 76005}, {"cost": [[11, 137101, 370]], "level": 183, "attributes": [[1, 1526400], [2, 15264000], [3, 763200], [4, 763200]], "nextLevel": 184, "id": 76005}, {"cost": [[11, 137101, 370]], "level": 184, "attributes": [[1, 1540200], [2, 15402000], [3, 770100], [4, 770100]], "nextLevel": 185, "id": 76005}, {"cost": [[11, 137101, 370]], "level": 185, "attributes": [[1, 1554000], [2, 15540000], [3, 777000], [4, 777000]], "nextLevel": 186, "id": 76005}, {"cost": [[11, 137101, 380]], "level": 186, "attributes": [[1, 1568100], [2, 15681000], [3, 784050], [4, 784050]], "nextLevel": 187, "id": 76005}, {"cost": [[11, 137101, 380]], "level": 187, "attributes": [[1, 1582200], [2, 15822000], [3, 791100], [4, 791100]], "nextLevel": 188, "id": 76005}, {"cost": [[11, 137101, 380]], "level": 188, "attributes": [[1, 1596300], [2, 15963000], [3, 798150], [4, 798150]], "nextLevel": 189, "id": 76005}, {"cost": [[11, 137101, 380]], "level": 189, "attributes": [[1, 1610400], [2, 16104000], [3, 805200], [4, 805200]], "nextLevel": 190, "id": 76005}, {"cost": [[11, 137101, 380]], "level": 190, "attributes": [[1, 1624500], [2, 16245000], [3, 812250], [4, 812250]], "nextLevel": 191, "id": 76005}, {"cost": [[11, 137101, 390]], "level": 191, "attributes": [[1, 1638900], [2, 16389000], [3, 819450], [4, 819450]], "nextLevel": 192, "id": 76005}, {"cost": [[11, 137101, 390]], "level": 192, "attributes": [[1, 1653300], [2, 16533000], [3, 826650], [4, 826650]], "nextLevel": 193, "id": 76005}, {"cost": [[11, 137101, 390]], "level": 193, "attributes": [[1, 1667700], [2, 16677000], [3, 833850], [4, 833850]], "nextLevel": 194, "id": 76005}, {"cost": [[11, 137101, 390]], "level": 194, "attributes": [[1, 1682100], [2, 16821000], [3, 841050], [4, 841050]], "nextLevel": 195, "id": 76005}, {"cost": [[11, 137101, 390]], "level": 195, "attributes": [[1, 1696500], [2, 16965000], [3, 848250], [4, 848250]], "nextLevel": 196, "id": 76005}, {"cost": [[11, 137101, 400]], "level": 196, "attributes": [[1, 1711200], [2, 17112000], [3, 855600], [4, 855600]], "nextLevel": 197, "id": 76005}, {"cost": [[11, 137101, 400]], "level": 197, "attributes": [[1, 1725900], [2, 17259000], [3, 862950], [4, 862950]], "nextLevel": 198, "id": 76005}, {"cost": [[11, 137101, 400]], "level": 198, "attributes": [[1, 1740600], [2, 17406000], [3, 870300], [4, 870300]], "nextLevel": 199, "id": 76005}, {"cost": [[11, 137101, 400]], "level": 199, "attributes": [[1, 1755300], [2, 17553000], [3, 877650], [4, 877650]], "nextLevel": 200, "id": 76005}, {"cost": [[11, 137101, 400]], "level": 200, "attributes": [[1, 1770000], [2, 17700000], [3, 885000], [4, 885000]], "nextLevel": 201, "id": 76005}, {"cost": [[11, 137101, 410]], "level": 201, "attributes": [[1, 1785000], [2, 17850000], [3, 892500], [4, 892500]], "nextLevel": 202, "id": 76005}, {"cost": [[11, 137101, 410]], "level": 202, "attributes": [[1, 1800000], [2, 18000000], [3, 900000], [4, 900000]], "nextLevel": 203, "id": 76005}, {"cost": [[11, 137101, 410]], "level": 203, "attributes": [[1, 1815000], [2, 18150000], [3, 907500], [4, 907500]], "nextLevel": 204, "id": 76005}, {"cost": [[11, 137101, 410]], "level": 204, "attributes": [[1, 1830000], [2, 18300000], [3, 915000], [4, 915000]], "nextLevel": 205, "id": 76005}, {"cost": [[11, 137101, 410]], "level": 205, "attributes": [[1, 1845000], [2, 18450000], [3, 922500], [4, 922500]], "nextLevel": 206, "id": 76005}, {"cost": [[11, 137101, 420]], "level": 206, "attributes": [[1, 1860300], [2, 18603000], [3, 930150], [4, 930150]], "nextLevel": 207, "id": 76005}, {"cost": [[11, 137101, 420]], "level": 207, "attributes": [[1, 1875600], [2, 18756000], [3, 937800], [4, 937800]], "nextLevel": 208, "id": 76005}, {"cost": [[11, 137101, 420]], "level": 208, "attributes": [[1, 1890900], [2, 18909000], [3, 945450], [4, 945450]], "nextLevel": 209, "id": 76005}, {"cost": [[11, 137101, 420]], "level": 209, "attributes": [[1, 1906200], [2, 19062000], [3, 953100], [4, 953100]], "nextLevel": 210, "id": 76005}, {"cost": [[11, 137101, 420]], "level": 210, "attributes": [[1, 1921500], [2, 19215000], [3, 960750], [4, 960750]], "nextLevel": 211, "id": 76005}, {"cost": [[11, 137101, 430]], "level": 211, "attributes": [[1, 1937100], [2, 19371000], [3, 968550], [4, 968550]], "nextLevel": 212, "id": 76005}, {"cost": [[11, 137101, 430]], "level": 212, "attributes": [[1, 1952700], [2, 19527000], [3, 976350], [4, 976350]], "nextLevel": 213, "id": 76005}, {"cost": [[11, 137101, 430]], "level": 213, "attributes": [[1, 1968300], [2, 19683000], [3, 984150], [4, 984150]], "nextLevel": 214, "id": 76005}, {"cost": [[11, 137101, 430]], "level": 214, "attributes": [[1, 1983900], [2, 19839000], [3, 991950], [4, 991950]], "nextLevel": 215, "id": 76005}, {"cost": [[11, 137101, 430]], "level": 215, "attributes": [[1, 1999500], [2, 19995000], [3, 999750], [4, 999750]], "nextLevel": 216, "id": 76005}, {"cost": [[11, 137101, 440]], "level": 216, "attributes": [[1, 2015400], [2, 20154000], [3, 1007700], [4, 1007700]], "nextLevel": 217, "id": 76005}, {"cost": [[11, 137101, 440]], "level": 217, "attributes": [[1, 2031300], [2, 20313000], [3, 1015650], [4, 1015650]], "nextLevel": 218, "id": 76005}, {"cost": [[11, 137101, 440]], "level": 218, "attributes": [[1, 2047200], [2, 20472000], [3, 1023600], [4, 1023600]], "nextLevel": 219, "id": 76005}, {"cost": [[11, 137101, 440]], "level": 219, "attributes": [[1, 2063100], [2, 20631000], [3, 1031550], [4, 1031550]], "nextLevel": 220, "id": 76005}, {"cost": [[11, 137101, 440]], "level": 220, "attributes": [[1, 2079000], [2, 20790000], [3, 1039500], [4, 1039500]], "nextLevel": 221, "id": 76005}, {"cost": [[11, 137101, 450]], "level": 221, "attributes": [[1, 2095200], [2, 20952000], [3, 1047600], [4, 1047600]], "nextLevel": 222, "id": 76005}, {"cost": [[11, 137101, 450]], "level": 222, "attributes": [[1, 2111400], [2, 21114000], [3, 1055700], [4, 1055700]], "nextLevel": 223, "id": 76005}, {"cost": [[11, 137101, 450]], "level": 223, "attributes": [[1, 2127600], [2, 21276000], [3, 1063800], [4, 1063800]], "nextLevel": 224, "id": 76005}, {"cost": [[11, 137101, 450]], "level": 224, "attributes": [[1, 2143800], [2, 21438000], [3, 1071900], [4, 1071900]], "nextLevel": 225, "id": 76005}, {"cost": [[11, 137101, 450]], "level": 225, "attributes": [[1, 2160000], [2, 21600000], [3, 1080000], [4, 1080000]], "nextLevel": 226, "id": 76005}, {"cost": [[11, 137101, 460]], "level": 226, "attributes": [[1, 2176500], [2, 21765000], [3, 1088250], [4, 1088250]], "nextLevel": 227, "id": 76005}, {"cost": [[11, 137101, 460]], "level": 227, "attributes": [[1, 2193000], [2, 21930000], [3, 1096500], [4, 1096500]], "nextLevel": 228, "id": 76005}, {"cost": [[11, 137101, 460]], "level": 228, "attributes": [[1, 2209500], [2, 22095000], [3, 1104750], [4, 1104750]], "nextLevel": 229, "id": 76005}, {"cost": [[11, 137101, 460]], "level": 229, "attributes": [[1, 2226000], [2, 22260000], [3, 1113000], [4, 1113000]], "nextLevel": 230, "id": 76005}, {"cost": [[11, 137101, 460]], "level": 230, "attributes": [[1, 2242500], [2, 22425000], [3, 1121250], [4, 1121250]], "nextLevel": 231, "id": 76005}, {"cost": [[11, 137101, 470]], "level": 231, "attributes": [[1, 2259300], [2, 22593000], [3, 1129650], [4, 1129650]], "nextLevel": 232, "id": 76005}, {"cost": [[11, 137101, 470]], "level": 232, "attributes": [[1, 2276100], [2, 22761000], [3, 1138050], [4, 1138050]], "nextLevel": 233, "id": 76005}, {"cost": [[11, 137101, 470]], "level": 233, "attributes": [[1, 2292900], [2, 22929000], [3, 1146450], [4, 1146450]], "nextLevel": 234, "id": 76005}, {"cost": [[11, 137101, 470]], "level": 234, "attributes": [[1, 2309700], [2, 23097000], [3, 1154850], [4, 1154850]], "nextLevel": 235, "id": 76005}, {"cost": [[11, 137101, 470]], "level": 235, "attributes": [[1, 2326500], [2, 23265000], [3, 1163250], [4, 1163250]], "nextLevel": 236, "id": 76005}, {"cost": [[11, 137101, 480]], "level": 236, "attributes": [[1, 2343600], [2, 23436000], [3, 1171800], [4, 1171800]], "nextLevel": 237, "id": 76005}, {"cost": [[11, 137101, 480]], "level": 237, "attributes": [[1, 2360700], [2, 23607000], [3, 1180350], [4, 1180350]], "nextLevel": 238, "id": 76005}, {"cost": [[11, 137101, 480]], "level": 238, "attributes": [[1, 2377800], [2, 23778000], [3, 1188900], [4, 1188900]], "nextLevel": 239, "id": 76005}, {"cost": [[11, 137101, 480]], "level": 239, "attributes": [[1, 2394900], [2, 23949000], [3, 1197450], [4, 1197450]], "nextLevel": 240, "id": 76005}, {"cost": [[11, 137101, 480]], "level": 240, "attributes": [[1, 2412000], [2, 24120000], [3, 1206000], [4, 1206000]], "nextLevel": 241, "id": 76005}, {"cost": [[11, 137101, 490]], "level": 241, "attributes": [[1, 2429400], [2, 24294000], [3, 1214700], [4, 1214700]], "nextLevel": 242, "id": 76005}, {"cost": [[11, 137101, 490]], "level": 242, "attributes": [[1, 2446800], [2, 24468000], [3, 1223400], [4, 1223400]], "nextLevel": 243, "id": 76005}, {"cost": [[11, 137101, 490]], "level": 243, "attributes": [[1, 2464200], [2, 24642000], [3, 1232100], [4, 1232100]], "nextLevel": 244, "id": 76005}, {"cost": [[11, 137101, 490]], "level": 244, "attributes": [[1, 2481600], [2, 24816000], [3, 1240800], [4, 1240800]], "nextLevel": 245, "id": 76005}, {"cost": [[11, 137101, 490]], "level": 245, "attributes": [[1, 2499000], [2, 24990000], [3, 1249500], [4, 1249500]], "nextLevel": 246, "id": 76005}, {"cost": [[11, 137101, 500]], "level": 246, "attributes": [[1, 2516700], [2, 25167000], [3, 1258350], [4, 1258350]], "nextLevel": 247, "id": 76005}, {"cost": [[11, 137101, 500]], "level": 247, "attributes": [[1, 2534400], [2, 25344000], [3, 1267200], [4, 1267200]], "nextLevel": 248, "id": 76005}, {"cost": [[11, 137101, 500]], "level": 248, "attributes": [[1, 2552100], [2, 25521000], [3, 1276050], [4, 1276050]], "nextLevel": 249, "id": 76005}, {"cost": [[11, 137101, 500]], "level": 249, "attributes": [[1, 2569800], [2, 25698000], [3, 1284900], [4, 1284900]], "nextLevel": 250, "id": 76005}, {"cost": [[11, 137101, 500]], "level": 250, "attributes": [[1, 2587500], [2, 25875000], [3, 1293750], [4, 1293750]], "nextLevel": 251, "id": 76005}, {"cost": [[11, 137101, 510]], "level": 251, "attributes": [[1, 2605500], [2, 26055000], [3, 1302750], [4, 1302750]], "nextLevel": 252, "id": 76005}, {"cost": [[11, 137101, 510]], "level": 252, "attributes": [[1, 2623500], [2, 26235000], [3, 1311750], [4, 1311750]], "nextLevel": 253, "id": 76005}, {"cost": [[11, 137101, 510]], "level": 253, "attributes": [[1, 2641500], [2, 26415000], [3, 1320750], [4, 1320750]], "nextLevel": 254, "id": 76005}, {"cost": [[11, 137101, 510]], "level": 254, "attributes": [[1, 2659500], [2, 26595000], [3, 1329750], [4, 1329750]], "nextLevel": 255, "id": 76005}, {"cost": [[11, 137101, 510]], "level": 255, "attributes": [[1, 2677500], [2, 26775000], [3, 1338750], [4, 1338750]], "nextLevel": 256, "id": 76005}, {"cost": [[11, 137101, 520]], "level": 256, "attributes": [[1, 2695800], [2, 26958000], [3, 1347900], [4, 1347900]], "nextLevel": 257, "id": 76005}, {"cost": [[11, 137101, 520]], "level": 257, "attributes": [[1, 2714100], [2, 27141000], [3, 1357050], [4, 1357050]], "nextLevel": 258, "id": 76005}, {"cost": [[11, 137101, 520]], "level": 258, "attributes": [[1, 2732400], [2, 27324000], [3, 1366200], [4, 1366200]], "nextLevel": 259, "id": 76005}, {"cost": [[11, 137101, 520]], "level": 259, "attributes": [[1, 2750700], [2, 27507000], [3, 1375350], [4, 1375350]], "nextLevel": 260, "id": 76005}, {"cost": [[11, 137101, 520]], "level": 260, "attributes": [[1, 2769000], [2, 27690000], [3, 1384500], [4, 1384500]], "nextLevel": 261, "id": 76005}, {"cost": [[11, 137101, 530]], "level": 261, "attributes": [[1, 2787600], [2, 27876000], [3, 1393800], [4, 1393800]], "nextLevel": 262, "id": 76005}, {"cost": [[11, 137101, 530]], "level": 262, "attributes": [[1, 2806200], [2, 28062000], [3, 1403100], [4, 1403100]], "nextLevel": 263, "id": 76005}, {"cost": [[11, 137101, 530]], "level": 263, "attributes": [[1, 2824800], [2, 28248000], [3, 1412400], [4, 1412400]], "nextLevel": 264, "id": 76005}, {"cost": [[11, 137101, 530]], "level": 264, "attributes": [[1, 2843400], [2, 28434000], [3, 1421700], [4, 1421700]], "nextLevel": 265, "id": 76005}, {"cost": [[11, 137101, 530]], "level": 265, "attributes": [[1, 2862000], [2, 28620000], [3, 1431000], [4, 1431000]], "nextLevel": 266, "id": 76005}, {"cost": [[11, 137101, 540]], "level": 266, "attributes": [[1, 2880900], [2, 28809000], [3, 1440450], [4, 1440450]], "nextLevel": 267, "id": 76005}, {"cost": [[11, 137101, 540]], "level": 267, "attributes": [[1, 2899800], [2, 28998000], [3, 1449900], [4, 1449900]], "nextLevel": 268, "id": 76005}, {"cost": [[11, 137101, 540]], "level": 268, "attributes": [[1, 2918700], [2, 29187000], [3, 1459350], [4, 1459350]], "nextLevel": 269, "id": 76005}, {"cost": [[11, 137101, 540]], "level": 269, "attributes": [[1, 2937600], [2, 29376000], [3, 1468800], [4, 1468800]], "nextLevel": 270, "id": 76005}, {"cost": [[11, 137101, 540]], "level": 270, "attributes": [[1, 2956500], [2, 29565000], [3, 1478250], [4, 1478250]], "nextLevel": 271, "id": 76005}, {"cost": [[11, 137101, 550]], "level": 271, "attributes": [[1, 2975700], [2, 29757000], [3, 1487850], [4, 1487850]], "nextLevel": 272, "id": 76005}, {"cost": [[11, 137101, 550]], "level": 272, "attributes": [[1, 2994900], [2, 29949000], [3, 1497450], [4, 1497450]], "nextLevel": 273, "id": 76005}, {"cost": [[11, 137101, 550]], "level": 273, "attributes": [[1, 3014100], [2, 30141000], [3, 1507050], [4, 1507050]], "nextLevel": 274, "id": 76005}, {"cost": [[11, 137101, 550]], "level": 274, "attributes": [[1, 3033300], [2, 30333000], [3, 1516650], [4, 1516650]], "nextLevel": 275, "id": 76005}, {"cost": [[11, 137101, 550]], "level": 275, "attributes": [[1, 3052500], [2, 30525000], [3, 1526250], [4, 1526250]], "nextLevel": 276, "id": 76005}, {"cost": [[11, 137101, 560]], "level": 276, "attributes": [[1, 3072000], [2, 30720000], [3, 1536000], [4, 1536000]], "nextLevel": 277, "id": 76005}, {"cost": [[11, 137101, 560]], "level": 277, "attributes": [[1, 3091500], [2, 30915000], [3, 1545750], [4, 1545750]], "nextLevel": 278, "id": 76005}, {"cost": [[11, 137101, 560]], "level": 278, "attributes": [[1, 3111000], [2, 31110000], [3, 1555500], [4, 1555500]], "nextLevel": 279, "id": 76005}, {"cost": [[11, 137101, 560]], "level": 279, "attributes": [[1, 3130500], [2, 31305000], [3, 1565250], [4, 1565250]], "nextLevel": 280, "id": 76005}, {"cost": [[11, 137101, 560]], "level": 280, "attributes": [[1, 3150000], [2, 31500000], [3, 1575000], [4, 1575000]], "nextLevel": 281, "id": 76005}, {"cost": [[11, 137101, 570]], "level": 281, "attributes": [[1, 3169800], [2, 31698000], [3, 1584900], [4, 1584900]], "nextLevel": 282, "id": 76005}, {"cost": [[11, 137101, 570]], "level": 282, "attributes": [[1, 3189600], [2, 31896000], [3, 1594800], [4, 1594800]], "nextLevel": 283, "id": 76005}, {"cost": [[11, 137101, 570]], "level": 283, "attributes": [[1, 3209400], [2, 32094000], [3, 1604700], [4, 1604700]], "nextLevel": 284, "id": 76005}, {"cost": [[11, 137101, 570]], "level": 284, "attributes": [[1, 3229200], [2, 32292000], [3, 1614600], [4, 1614600]], "nextLevel": 285, "id": 76005}, {"cost": [[11, 137101, 570]], "level": 285, "attributes": [[1, 3249000], [2, 32490000], [3, 1624500], [4, 1624500]], "nextLevel": 286, "id": 76005}, {"cost": [[11, 137101, 580]], "level": 286, "attributes": [[1, 3269100], [2, 32691000], [3, 1634550], [4, 1634550]], "nextLevel": 287, "id": 76005}, {"cost": [[11, 137101, 580]], "level": 287, "attributes": [[1, 3289200], [2, 32892000], [3, 1644600], [4, 1644600]], "nextLevel": 288, "id": 76005}, {"cost": [[11, 137101, 580]], "level": 288, "attributes": [[1, 3309300], [2, 33093000], [3, 1654650], [4, 1654650]], "nextLevel": 289, "id": 76005}, {"cost": [[11, 137101, 580]], "level": 289, "attributes": [[1, 3329400], [2, 33294000], [3, 1664700], [4, 1664700]], "nextLevel": 290, "id": 76005}, {"cost": [[11, 137101, 580]], "level": 290, "attributes": [[1, 3349500], [2, 33495000], [3, 1674750], [4, 1674750]], "nextLevel": 291, "id": 76005}, {"cost": [[11, 137101, 590]], "level": 291, "attributes": [[1, 3369900], [2, 33699000], [3, 1684950], [4, 1684950]], "nextLevel": 292, "id": 76005}, {"cost": [[11, 137101, 590]], "level": 292, "attributes": [[1, 3390300], [2, 33903000], [3, 1695150], [4, 1695150]], "nextLevel": 293, "id": 76005}, {"cost": [[11, 137101, 590]], "level": 293, "attributes": [[1, 3410700], [2, 34107000], [3, 1705350], [4, 1705350]], "nextLevel": 294, "id": 76005}, {"cost": [[11, 137101, 590]], "level": 294, "attributes": [[1, 3431100], [2, 34311000], [3, 1715550], [4, 1715550]], "nextLevel": 295, "id": 76005}, {"cost": [[11, 137101, 590]], "level": 295, "attributes": [[1, 3451500], [2, 34515000], [3, 1725750], [4, 1725750]], "nextLevel": 296, "id": 76005}, {"cost": [[11, 137101, 600]], "level": 296, "attributes": [[1, 3472200], [2, 34722000], [3, 1736100], [4, 1736100]], "nextLevel": 297, "id": 76005}, {"cost": [[11, 137101, 600]], "level": 297, "attributes": [[1, 3492900], [2, 34929000], [3, 1746450], [4, 1746450]], "nextLevel": 298, "id": 76005}, {"cost": [[11, 137101, 600]], "level": 298, "attributes": [[1, 3513600], [2, 35136000], [3, 1756800], [4, 1756800]], "nextLevel": 299, "id": 76005}, {"cost": [[11, 137101, 600]], "level": 299, "attributes": [[1, 3534300], [2, 35343000], [3, 1767150], [4, 1767150]], "nextLevel": 300, "id": 76005}, {"cost": [[11, 137101, 600]], "level": 300, "attributes": [[1, 3555000], [2, 35550000], [3, 1777500], [4, 1777500]], "nextLevel": 301, "id": 76005}, {"cost": [[11, 137101, 610]], "level": 301, "attributes": [[1, 3576000], [2, 35760000], [3, 1788000], [4, 1788000]], "nextLevel": 302, "id": 76005}, {"cost": [[11, 137101, 610]], "level": 302, "attributes": [[1, 3597000], [2, 35970000], [3, 1798500], [4, 1798500]], "nextLevel": 303, "id": 76005}, {"cost": [[11, 137101, 610]], "level": 303, "attributes": [[1, 3618000], [2, 36180000], [3, 1809000], [4, 1809000]], "nextLevel": 304, "id": 76005}, {"cost": [[11, 137101, 610]], "level": 304, "attributes": [[1, 3639000], [2, 36390000], [3, 1819500], [4, 1819500]], "nextLevel": 305, "id": 76005}, {"cost": [[11, 137101, 610]], "level": 305, "attributes": [[1, 3660000], [2, 36600000], [3, 1830000], [4, 1830000]], "nextLevel": 306, "id": 76005}, {"cost": [[11, 137101, 620]], "level": 306, "attributes": [[1, 3681300], [2, 36813000], [3, 1840650], [4, 1840650]], "nextLevel": 307, "id": 76005}, {"cost": [[11, 137101, 620]], "level": 307, "attributes": [[1, 3702600], [2, 37026000], [3, 1851300], [4, 1851300]], "nextLevel": 308, "id": 76005}, {"cost": [[11, 137101, 620]], "level": 308, "attributes": [[1, 3723900], [2, 37239000], [3, 1861950], [4, 1861950]], "nextLevel": 309, "id": 76005}, {"cost": [[11, 137101, 620]], "level": 309, "attributes": [[1, 3745200], [2, 37452000], [3, 1872600], [4, 1872600]], "nextLevel": 310, "id": 76005}, {"cost": [[11, 137101, 620]], "level": 310, "attributes": [[1, 3766500], [2, 37665000], [3, 1883250], [4, 1883250]], "nextLevel": 311, "id": 76005}, {"cost": [[11, 137101, 630]], "level": 311, "attributes": [[1, 3788100], [2, 37881000], [3, 1894050], [4, 1894050]], "nextLevel": 312, "id": 76005}, {"cost": [[11, 137101, 630]], "level": 312, "attributes": [[1, 3809700], [2, 38097000], [3, 1904850], [4, 1904850]], "nextLevel": 313, "id": 76005}, {"cost": [[11, 137101, 630]], "level": 313, "attributes": [[1, 3831300], [2, 38313000], [3, 1915650], [4, 1915650]], "nextLevel": 314, "id": 76005}, {"cost": [[11, 137101, 630]], "level": 314, "attributes": [[1, 3852900], [2, 38529000], [3, 1926450], [4, 1926450]], "nextLevel": 315, "id": 76005}, {"cost": [[11, 137101, 630]], "level": 315, "attributes": [[1, 3874500], [2, 38745000], [3, 1937250], [4, 1937250]], "nextLevel": 316, "id": 76005}, {"cost": [[11, 137101, 640]], "level": 316, "attributes": [[1, 3896400], [2, 38964000], [3, 1948200], [4, 1948200]], "nextLevel": 317, "id": 76005}, {"cost": [[11, 137101, 640]], "level": 317, "attributes": [[1, 3918300], [2, 39183000], [3, 1959150], [4, 1959150]], "nextLevel": 318, "id": 76005}, {"cost": [[11, 137101, 640]], "level": 318, "attributes": [[1, 3940200], [2, 39402000], [3, 1970100], [4, 1970100]], "nextLevel": 319, "id": 76005}, {"cost": [[11, 137101, 640]], "level": 319, "attributes": [[1, 3962100], [2, 39621000], [3, 1981050], [4, 1981050]], "nextLevel": 320, "id": 76005}, {"cost": [[11, 137101, 640]], "level": 320, "attributes": [[1, 3984000], [2, 39840000], [3, 1992000], [4, 1992000]], "nextLevel": 321, "id": 76005}, {"cost": [[11, 137101, 650]], "level": 321, "attributes": [[1, 4006200], [2, 40062000], [3, 2003100], [4, 2003100]], "nextLevel": 322, "id": 76005}, {"cost": [[11, 137101, 650]], "level": 322, "attributes": [[1, 4028400], [2, 40284000], [3, 2014200], [4, 2014200]], "nextLevel": 323, "id": 76005}, {"cost": [[11, 137101, 650]], "level": 323, "attributes": [[1, 4050600], [2, 40506000], [3, 2025300], [4, 2025300]], "nextLevel": 324, "id": 76005}, {"cost": [[11, 137101, 650]], "level": 324, "attributes": [[1, 4072800], [2, 40728000], [3, 2036400], [4, 2036400]], "nextLevel": 325, "id": 76005}, {"cost": [[11, 137101, 650]], "level": 325, "attributes": [[1, 4095000], [2, 40950000], [3, 2047500], [4, 2047500]], "nextLevel": 326, "id": 76005}, {"cost": [[11, 137101, 660]], "level": 326, "attributes": [[1, 4117500], [2, 41175000], [3, 2058750], [4, 2058750]], "nextLevel": 327, "id": 76005}, {"cost": [[11, 137101, 660]], "level": 327, "attributes": [[1, 4140000], [2, 41400000], [3, 2070000], [4, 2070000]], "nextLevel": 328, "id": 76005}, {"cost": [[11, 137101, 660]], "level": 328, "attributes": [[1, 4162500], [2, 41625000], [3, 2081250], [4, 2081250]], "nextLevel": 329, "id": 76005}, {"cost": [[11, 137101, 660]], "level": 329, "attributes": [[1, 4185000], [2, 41850000], [3, 2092500], [4, 2092500]], "nextLevel": 330, "id": 76005}, {"cost": [[11, 137101, 660]], "level": 330, "attributes": [[1, 4207500], [2, 42075000], [3, 2103750], [4, 2103750]], "nextLevel": 331, "id": 76005}, {"cost": [[11, 137101, 670]], "level": 331, "attributes": [[1, 4230300], [2, 42303000], [3, 2115150], [4, 2115150]], "nextLevel": 332, "id": 76005}, {"cost": [[11, 137101, 670]], "level": 332, "attributes": [[1, 4253100], [2, 42531000], [3, 2126550], [4, 2126550]], "nextLevel": 333, "id": 76005}, {"cost": [[11, 137101, 670]], "level": 333, "attributes": [[1, 4275900], [2, 42759000], [3, 2137950], [4, 2137950]], "nextLevel": 334, "id": 76005}, {"cost": [[11, 137101, 670]], "level": 334, "attributes": [[1, 4298700], [2, 42987000], [3, 2149350], [4, 2149350]], "nextLevel": 335, "id": 76005}, {"cost": [[11, 137101, 670]], "level": 335, "attributes": [[1, 4321500], [2, 43215000], [3, 2160750], [4, 2160750]], "nextLevel": 336, "id": 76005}, {"cost": [[11, 137101, 680]], "level": 336, "attributes": [[1, 4344600], [2, 43446000], [3, 2172300], [4, 2172300]], "nextLevel": 337, "id": 76005}, {"cost": [[11, 137101, 680]], "level": 337, "attributes": [[1, 4367700], [2, 43677000], [3, 2183850], [4, 2183850]], "nextLevel": 338, "id": 76005}, {"cost": [[11, 137101, 680]], "level": 338, "attributes": [[1, 4390800], [2, 43908000], [3, 2195400], [4, 2195400]], "nextLevel": 339, "id": 76005}, {"cost": [[11, 137101, 680]], "level": 339, "attributes": [[1, 4413900], [2, 44139000], [3, 2206950], [4, 2206950]], "nextLevel": 340, "id": 76005}, {"cost": [[11, 137101, 680]], "level": 340, "attributes": [[1, 4437000], [2, 44370000], [3, 2218500], [4, 2218500]], "nextLevel": 341, "id": 76005}, {"cost": [[11, 137101, 690]], "level": 341, "attributes": [[1, 4460400], [2, 44604000], [3, 2230200], [4, 2230200]], "nextLevel": 342, "id": 76005}, {"cost": [[11, 137101, 690]], "level": 342, "attributes": [[1, 4483800], [2, 44838000], [3, 2241900], [4, 2241900]], "nextLevel": 343, "id": 76005}, {"cost": [[11, 137101, 690]], "level": 343, "attributes": [[1, 4507200], [2, 45072000], [3, 2253600], [4, 2253600]], "nextLevel": 344, "id": 76005}, {"cost": [[11, 137101, 690]], "level": 344, "attributes": [[1, 4530600], [2, 45306000], [3, 2265300], [4, 2265300]], "nextLevel": 345, "id": 76005}, {"cost": [[11, 137101, 690]], "level": 345, "attributes": [[1, 4554000], [2, 45540000], [3, 2277000], [4, 2277000]], "nextLevel": 346, "id": 76005}, {"cost": [[11, 137101, 700]], "level": 346, "attributes": [[1, 4577700], [2, 45777000], [3, 2288850], [4, 2288850]], "nextLevel": 347, "id": 76005}, {"cost": [[11, 137101, 700]], "level": 347, "attributes": [[1, 4601400], [2, 46014000], [3, 2300700], [4, 2300700]], "nextLevel": 348, "id": 76005}, {"cost": [[11, 137101, 700]], "level": 348, "attributes": [[1, 4625100], [2, 46251000], [3, 2312550], [4, 2312550]], "nextLevel": 349, "id": 76005}, {"cost": [[11, 137101, 700]], "level": 349, "attributes": [[1, 4648800], [2, 46488000], [3, 2324400], [4, 2324400]], "nextLevel": 350, "id": 76005}, {"cost": [[11, 137101, 700]], "level": 350, "attributes": [[1, 4672500], [2, 46725000], [3, 2336250], [4, 2336250]], "nextLevel": 351, "id": 76005}, {"cost": [[11, 137101, 710]], "level": 351, "attributes": [[1, 4696500], [2, 46965000], [3, 2348250], [4, 2348250]], "nextLevel": 352, "id": 76005}, {"cost": [[11, 137101, 710]], "level": 352, "attributes": [[1, 4720500], [2, 47205000], [3, 2360250], [4, 2360250]], "nextLevel": 353, "id": 76005}, {"cost": [[11, 137101, 710]], "level": 353, "attributes": [[1, 4744500], [2, 47445000], [3, 2372250], [4, 2372250]], "nextLevel": 354, "id": 76005}, {"cost": [[11, 137101, 710]], "level": 354, "attributes": [[1, 4768500], [2, 47685000], [3, 2384250], [4, 2384250]], "nextLevel": 355, "id": 76005}, {"cost": [[11, 137101, 710]], "level": 355, "attributes": [[1, 4792500], [2, 47925000], [3, 2396250], [4, 2396250]], "nextLevel": 356, "id": 76005}, {"cost": [[11, 137101, 720]], "level": 356, "attributes": [[1, 4816800], [2, 48168000], [3, 2408400], [4, 2408400]], "nextLevel": 357, "id": 76005}, {"cost": [[11, 137101, 720]], "level": 357, "attributes": [[1, 4841100], [2, 48411000], [3, 2420550], [4, 2420550]], "nextLevel": 358, "id": 76005}, {"cost": [[11, 137101, 720]], "level": 358, "attributes": [[1, 4865400], [2, 48654000], [3, 2432700], [4, 2432700]], "nextLevel": 359, "id": 76005}, {"cost": [[11, 137101, 720]], "level": 359, "attributes": [[1, 4889700], [2, 48897000], [3, 2444850], [4, 2444850]], "nextLevel": 360, "id": 76005}, {"cost": [[11, 137101, 720]], "level": 360, "attributes": [[1, 4914000], [2, 49140000], [3, 2457000], [4, 2457000]], "nextLevel": 361, "id": 76005}, {"cost": [[11, 137101, 730]], "level": 361, "attributes": [[1, 4938600], [2, 49386000], [3, 2469300], [4, 2469300]], "nextLevel": 362, "id": 76005}, {"cost": [[11, 137101, 730]], "level": 362, "attributes": [[1, 4963200], [2, 49632000], [3, 2481600], [4, 2481600]], "nextLevel": 363, "id": 76005}, {"cost": [[11, 137101, 730]], "level": 363, "attributes": [[1, 4987800], [2, 49878000], [3, 2493900], [4, 2493900]], "nextLevel": 364, "id": 76005}, {"cost": [[11, 137101, 730]], "level": 364, "attributes": [[1, 5012400], [2, 50124000], [3, 2506200], [4, 2506200]], "nextLevel": 365, "id": 76005}, {"cost": [[11, 137101, 730]], "level": 365, "attributes": [[1, 5037000], [2, 50370000], [3, 2518500], [4, 2518500]], "nextLevel": 366, "id": 76005}, {"cost": [[11, 137101, 740]], "level": 366, "attributes": [[1, 5061900], [2, 50619000], [3, 2530950], [4, 2530950]], "nextLevel": 367, "id": 76005}, {"cost": [[11, 137101, 740]], "level": 367, "attributes": [[1, 5086800], [2, 50868000], [3, 2543400], [4, 2543400]], "nextLevel": 368, "id": 76005}, {"cost": [[11, 137101, 740]], "level": 368, "attributes": [[1, 5111700], [2, 51117000], [3, 2555850], [4, 2555850]], "nextLevel": 369, "id": 76005}, {"cost": [[11, 137101, 740]], "level": 369, "attributes": [[1, 5136600], [2, 51366000], [3, 2568300], [4, 2568300]], "nextLevel": 370, "id": 76005}, {"cost": [[11, 137101, 740]], "level": 370, "attributes": [[1, 5161500], [2, 51615000], [3, 2580750], [4, 2580750]], "nextLevel": 371, "id": 76005}, {"cost": [[11, 137101, 750]], "level": 371, "attributes": [[1, 5186700], [2, 51867000], [3, 2593350], [4, 2593350]], "nextLevel": 372, "id": 76005}, {"cost": [[11, 137101, 750]], "level": 372, "attributes": [[1, 5211900], [2, 52119000], [3, 2605950], [4, 2605950]], "nextLevel": 373, "id": 76005}, {"cost": [[11, 137101, 750]], "level": 373, "attributes": [[1, 5237100], [2, 52371000], [3, 2618550], [4, 2618550]], "nextLevel": 374, "id": 76005}, {"cost": [[11, 137101, 750]], "level": 374, "attributes": [[1, 5262300], [2, 52623000], [3, 2631150], [4, 2631150]], "nextLevel": 375, "id": 76005}, {"cost": [[11, 137101, 750]], "level": 375, "attributes": [[1, 5287500], [2, 52875000], [3, 2643750], [4, 2643750]], "nextLevel": 376, "id": 76005}, {"cost": [[11, 137101, 760]], "level": 376, "attributes": [[1, 5313000], [2, 53130000], [3, 2656500], [4, 2656500]], "nextLevel": 377, "id": 76005}, {"cost": [[11, 137101, 760]], "level": 377, "attributes": [[1, 5338500], [2, 53385000], [3, 2669250], [4, 2669250]], "nextLevel": 378, "id": 76005}, {"cost": [[11, 137101, 760]], "level": 378, "attributes": [[1, 5364000], [2, 53640000], [3, 2682000], [4, 2682000]], "nextLevel": 379, "id": 76005}, {"cost": [[11, 137101, 760]], "level": 379, "attributes": [[1, 5389500], [2, 53895000], [3, 2694750], [4, 2694750]], "nextLevel": 380, "id": 76005}, {"cost": [[11, 137101, 760]], "level": 380, "attributes": [[1, 5415000], [2, 54150000], [3, 2707500], [4, 2707500]], "nextLevel": 381, "id": 76005}, {"cost": [[11, 137101, 770]], "level": 381, "attributes": [[1, 5440800], [2, 54408000], [3, 2720400], [4, 2720400]], "nextLevel": 382, "id": 76005}, {"cost": [[11, 137101, 770]], "level": 382, "attributes": [[1, 5466600], [2, 54666000], [3, 2733300], [4, 2733300]], "nextLevel": 383, "id": 76005}, {"cost": [[11, 137101, 770]], "level": 383, "attributes": [[1, 5492400], [2, 54924000], [3, 2746200], [4, 2746200]], "nextLevel": 384, "id": 76005}, {"cost": [[11, 137101, 770]], "level": 384, "attributes": [[1, 5518200], [2, 55182000], [3, 2759100], [4, 2759100]], "nextLevel": 385, "id": 76005}, {"cost": [[11, 137101, 770]], "level": 385, "attributes": [[1, 5544000], [2, 55440000], [3, 2772000], [4, 2772000]], "nextLevel": 386, "id": 76005}, {"cost": [[11, 137101, 780]], "level": 386, "attributes": [[1, 5570100], [2, 55701000], [3, 2785050], [4, 2785050]], "nextLevel": 387, "id": 76005}, {"cost": [[11, 137101, 780]], "level": 387, "attributes": [[1, 5596200], [2, 55962000], [3, 2798100], [4, 2798100]], "nextLevel": 388, "id": 76005}, {"cost": [[11, 137101, 780]], "level": 388, "attributes": [[1, 5622300], [2, 56223000], [3, 2811150], [4, 2811150]], "nextLevel": 389, "id": 76005}, {"cost": [[11, 137101, 780]], "level": 389, "attributes": [[1, 5648400], [2, 56484000], [3, 2824200], [4, 2824200]], "nextLevel": 390, "id": 76005}, {"cost": [[11, 137101, 780]], "level": 390, "attributes": [[1, 5674500], [2, 56745000], [3, 2837250], [4, 2837250]], "nextLevel": 391, "id": 76005}, {"cost": [[11, 137101, 790]], "level": 391, "attributes": [[1, 5700900], [2, 57009000], [3, 2850450], [4, 2850450]], "nextLevel": 392, "id": 76005}, {"cost": [[11, 137101, 790]], "level": 392, "attributes": [[1, 5727300], [2, 57273000], [3, 2863650], [4, 2863650]], "nextLevel": 393, "id": 76005}, {"cost": [[11, 137101, 790]], "level": 393, "attributes": [[1, 5753700], [2, 57537000], [3, 2876850], [4, 2876850]], "nextLevel": 394, "id": 76005}, {"cost": [[11, 137101, 790]], "level": 394, "attributes": [[1, 5780100], [2, 57801000], [3, 2890050], [4, 2890050]], "nextLevel": 395, "id": 76005}, {"cost": [[11, 137101, 790]], "level": 395, "attributes": [[1, 5806500], [2, 58065000], [3, 2903250], [4, 2903250]], "nextLevel": 396, "id": 76005}, {"cost": [[11, 137101, 800]], "level": 396, "attributes": [[1, 5833200], [2, 58332000], [3, 2916600], [4, 2916600]], "nextLevel": 397, "id": 76005}, {"cost": [[11, 137101, 800]], "level": 397, "attributes": [[1, 5859900], [2, 58599000], [3, 2929950], [4, 2929950]], "nextLevel": 398, "id": 76005}, {"cost": [[11, 137101, 800]], "level": 398, "attributes": [[1, 5886600], [2, 58866000], [3, 2943300], [4, 2943300]], "nextLevel": 399, "id": 76005}, {"cost": [[11, 137101, 800]], "level": 399, "attributes": [[1, 5913300], [2, 59133000], [3, 2956650], [4, 2956650]], "nextLevel": 400, "id": 76005}, {"cost": [[11, 137101, 800]], "level": 400, "attributes": [[1, 5940000], [2, 59400000], [3, 2970000], [4, 2970000]], "nextLevel": 401, "id": 76005}, {"cost": [[11, 137101, 810]], "level": 401, "attributes": [[1, 5967000], [2, 59670000], [3, 2983500], [4, 2983500]], "nextLevel": 402, "id": 76005}, {"cost": [[11, 137101, 810]], "level": 402, "attributes": [[1, 5994000], [2, 59940000], [3, 2997000], [4, 2997000]], "nextLevel": 403, "id": 76005}, {"cost": [[11, 137101, 810]], "level": 403, "attributes": [[1, 6021000], [2, 60210000], [3, 3010500], [4, 3010500]], "nextLevel": 404, "id": 76005}, {"cost": [[11, 137101, 810]], "level": 404, "attributes": [[1, 6048000], [2, 60480000], [3, 3024000], [4, 3024000]], "nextLevel": 405, "id": 76005}, {"cost": [[11, 137101, 810]], "level": 405, "attributes": [[1, 6075000], [2, 60750000], [3, 3037500], [4, 3037500]], "nextLevel": 406, "id": 76005}, {"cost": [[11, 137101, 820]], "level": 406, "attributes": [[1, 6102300], [2, 61023000], [3, 3051150], [4, 3051150]], "nextLevel": 407, "id": 76005}, {"cost": [[11, 137101, 820]], "level": 407, "attributes": [[1, 6129600], [2, 61296000], [3, 3064800], [4, 3064800]], "nextLevel": 408, "id": 76005}, {"cost": [[11, 137101, 820]], "level": 408, "attributes": [[1, 6156900], [2, 61569000], [3, 3078450], [4, 3078450]], "nextLevel": 409, "id": 76005}, {"cost": [[11, 137101, 820]], "level": 409, "attributes": [[1, 6184200], [2, 61842000], [3, 3092100], [4, 3092100]], "nextLevel": 410, "id": 76005}, {"cost": [[11, 137101, 820]], "level": 410, "attributes": [[1, 6211500], [2, 62115000], [3, 3105750], [4, 3105750]], "nextLevel": 411, "id": 76005}, {"cost": [[11, 137101, 830]], "level": 411, "attributes": [[1, 6239100], [2, 62391000], [3, 3119550], [4, 3119550]], "nextLevel": 412, "id": 76005}, {"cost": [[11, 137101, 830]], "level": 412, "attributes": [[1, 6266700], [2, 62667000], [3, 3133350], [4, 3133350]], "nextLevel": 413, "id": 76005}, {"cost": [[11, 137101, 830]], "level": 413, "attributes": [[1, 6294300], [2, 62943000], [3, 3147150], [4, 3147150]], "nextLevel": 414, "id": 76005}, {"cost": [[11, 137101, 830]], "level": 414, "attributes": [[1, 6321900], [2, 63219000], [3, 3160950], [4, 3160950]], "nextLevel": 415, "id": 76005}, {"cost": [[11, 137101, 830]], "level": 415, "attributes": [[1, 6349500], [2, 63495000], [3, 3174750], [4, 3174750]], "nextLevel": 416, "id": 76005}, {"cost": [[11, 137101, 840]], "level": 416, "attributes": [[1, 6377400], [2, 63774000], [3, 3188700], [4, 3188700]], "nextLevel": 417, "id": 76005}, {"cost": [[11, 137101, 840]], "level": 417, "attributes": [[1, 6405300], [2, 64053000], [3, 3202650], [4, 3202650]], "nextLevel": 418, "id": 76005}, {"cost": [[11, 137101, 840]], "level": 418, "attributes": [[1, 6433200], [2, 64332000], [3, 3216600], [4, 3216600]], "nextLevel": 419, "id": 76005}, {"cost": [[11, 137101, 840]], "level": 419, "attributes": [[1, 6461100], [2, 64611000], [3, 3230550], [4, 3230550]], "nextLevel": 420, "id": 76005}, {"cost": [[11, 137101, 840]], "level": 420, "attributes": [[1, 6489000], [2, 64890000], [3, 3244500], [4, 3244500]], "nextLevel": 421, "id": 76005}, {"cost": [[11, 137101, 850]], "level": 421, "attributes": [[1, 6517200], [2, 65172000], [3, 3258600], [4, 3258600]], "nextLevel": 422, "id": 76005}, {"cost": [[11, 137101, 850]], "level": 422, "attributes": [[1, 6545400], [2, 65454000], [3, 3272700], [4, 3272700]], "nextLevel": 423, "id": 76005}, {"cost": [[11, 137101, 850]], "level": 423, "attributes": [[1, 6573600], [2, 65736000], [3, 3286800], [4, 3286800]], "nextLevel": 424, "id": 76005}, {"cost": [[11, 137101, 850]], "level": 424, "attributes": [[1, 6601800], [2, 66018000], [3, 3300900], [4, 3300900]], "nextLevel": 425, "id": 76005}, {"cost": [[11, 137101, 850]], "level": 425, "attributes": [[1, 6630000], [2, 66300000], [3, 3315000], [4, 3315000]], "nextLevel": 426, "id": 76005}, {"cost": [[11, 137101, 860]], "level": 426, "attributes": [[1, 6658500], [2, 66585000], [3, 3329250], [4, 3329250]], "nextLevel": 427, "id": 76005}, {"cost": [[11, 137101, 860]], "level": 427, "attributes": [[1, 6687000], [2, 66870000], [3, 3343500], [4, 3343500]], "nextLevel": 428, "id": 76005}, {"cost": [[11, 137101, 860]], "level": 428, "attributes": [[1, 6715500], [2, 67155000], [3, 3357750], [4, 3357750]], "nextLevel": 429, "id": 76005}, {"cost": [[11, 137101, 860]], "level": 429, "attributes": [[1, 6744000], [2, 67440000], [3, 3372000], [4, 3372000]], "nextLevel": 430, "id": 76005}, {"cost": [[11, 137101, 860]], "level": 430, "attributes": [[1, 6772500], [2, 67725000], [3, 3386250], [4, 3386250]], "nextLevel": 431, "id": 76005}, {"cost": [[11, 137101, 870]], "level": 431, "attributes": [[1, 6801300], [2, 68013000], [3, 3400650], [4, 3400650]], "nextLevel": 432, "id": 76005}, {"cost": [[11, 137101, 870]], "level": 432, "attributes": [[1, 6830100], [2, 68301000], [3, 3415050], [4, 3415050]], "nextLevel": 433, "id": 76005}, {"cost": [[11, 137101, 870]], "level": 433, "attributes": [[1, 6858900], [2, 68589000], [3, 3429450], [4, 3429450]], "nextLevel": 434, "id": 76005}, {"cost": [[11, 137101, 870]], "level": 434, "attributes": [[1, 6887700], [2, 68877000], [3, 3443850], [4, 3443850]], "nextLevel": 435, "id": 76005}, {"cost": [[11, 137101, 870]], "level": 435, "attributes": [[1, 6916500], [2, 69165000], [3, 3458250], [4, 3458250]], "nextLevel": 436, "id": 76005}, {"cost": [[11, 137101, 880]], "level": 436, "attributes": [[1, 6945600], [2, 69456000], [3, 3472800], [4, 3472800]], "nextLevel": 437, "id": 76005}, {"cost": [[11, 137101, 880]], "level": 437, "attributes": [[1, 6974700], [2, 69747000], [3, 3487350], [4, 3487350]], "nextLevel": 438, "id": 76005}, {"cost": [[11, 137101, 880]], "level": 438, "attributes": [[1, 7003800], [2, 70038000], [3, 3501900], [4, 3501900]], "nextLevel": 439, "id": 76005}, {"cost": [[11, 137101, 880]], "level": 439, "attributes": [[1, 7032900], [2, 70329000], [3, 3516450], [4, 3516450]], "nextLevel": 440, "id": 76005}, {"cost": [[11, 137101, 880]], "level": 440, "attributes": [[1, 7062000], [2, 70620000], [3, 3531000], [4, 3531000]], "nextLevel": 441, "id": 76005}, {"cost": [[11, 137101, 890]], "level": 441, "attributes": [[1, 7091400], [2, 70914000], [3, 3545700], [4, 3545700]], "nextLevel": 442, "id": 76005}, {"cost": [[11, 137101, 890]], "level": 442, "attributes": [[1, 7120800], [2, 71208000], [3, 3560400], [4, 3560400]], "nextLevel": 443, "id": 76005}, {"cost": [[11, 137101, 890]], "level": 443, "attributes": [[1, 7150200], [2, 71502000], [3, 3575100], [4, 3575100]], "nextLevel": 444, "id": 76005}, {"cost": [[11, 137101, 890]], "level": 444, "attributes": [[1, 7179600], [2, 71796000], [3, 3589800], [4, 3589800]], "nextLevel": 445, "id": 76005}, {"cost": [[11, 137101, 890]], "level": 445, "attributes": [[1, 7209000], [2, 72090000], [3, 3604500], [4, 3604500]], "nextLevel": 446, "id": 76005}, {"cost": [[11, 137101, 900]], "level": 446, "attributes": [[1, 7238700], [2, 72387000], [3, 3619350], [4, 3619350]], "nextLevel": 447, "id": 76005}, {"cost": [[11, 137101, 900]], "level": 447, "attributes": [[1, 7268400], [2, 72684000], [3, 3634200], [4, 3634200]], "nextLevel": 448, "id": 76005}, {"cost": [[11, 137101, 900]], "level": 448, "attributes": [[1, 7298100], [2, 72981000], [3, 3649050], [4, 3649050]], "nextLevel": 449, "id": 76005}, {"cost": [[11, 137101, 900]], "level": 449, "attributes": [[1, 7327800], [2, 73278000], [3, 3663900], [4, 3663900]], "nextLevel": 450, "id": 76005}, {"cost": [[11, 137101, 900]], "level": 450, "attributes": [[1, 7357500], [2, 73575000], [3, 3678750], [4, 3678750]], "nextLevel": 451, "id": 76005}, {"cost": [[11, 137101, 910]], "level": 451, "attributes": [[1, 7387500], [2, 73875000], [3, 3693750], [4, 3693750]], "nextLevel": 452, "id": 76005}, {"cost": [[11, 137101, 910]], "level": 452, "attributes": [[1, 7417500], [2, 74175000], [3, 3708750], [4, 3708750]], "nextLevel": 453, "id": 76005}, {"cost": [[11, 137101, 910]], "level": 453, "attributes": [[1, 7447500], [2, 74475000], [3, 3723750], [4, 3723750]], "nextLevel": 454, "id": 76005}, {"cost": [[11, 137101, 910]], "level": 454, "attributes": [[1, 7477500], [2, 74775000], [3, 3738750], [4, 3738750]], "nextLevel": 455, "id": 76005}, {"cost": [[11, 137101, 910]], "level": 455, "attributes": [[1, 7507500], [2, 75075000], [3, 3753750], [4, 3753750]], "nextLevel": 456, "id": 76005}, {"cost": [[11, 137101, 920]], "level": 456, "attributes": [[1, 7537800], [2, 75378000], [3, 3768900], [4, 3768900]], "nextLevel": 457, "id": 76005}, {"cost": [[11, 137101, 920]], "level": 457, "attributes": [[1, 7568100], [2, 75681000], [3, 3784050], [4, 3784050]], "nextLevel": 458, "id": 76005}, {"cost": [[11, 137101, 920]], "level": 458, "attributes": [[1, 7598400], [2, 75984000], [3, 3799200], [4, 3799200]], "nextLevel": 459, "id": 76005}, {"cost": [[11, 137101, 920]], "level": 459, "attributes": [[1, 7628700], [2, 76287000], [3, 3814350], [4, 3814350]], "nextLevel": 460, "id": 76005}, {"cost": [[11, 137101, 920]], "level": 460, "attributes": [[1, 7659000], [2, 76590000], [3, 3829500], [4, 3829500]], "nextLevel": 461, "id": 76005}, {"cost": [[11, 137101, 930]], "level": 461, "attributes": [[1, 7689600], [2, 76896000], [3, 3844800], [4, 3844800]], "nextLevel": 462, "id": 76005}, {"cost": [[11, 137101, 930]], "level": 462, "attributes": [[1, 7720200], [2, 77202000], [3, 3860100], [4, 3860100]], "nextLevel": 463, "id": 76005}, {"cost": [[11, 137101, 930]], "level": 463, "attributes": [[1, 7750800], [2, 77508000], [3, 3875400], [4, 3875400]], "nextLevel": 464, "id": 76005}, {"cost": [[11, 137101, 930]], "level": 464, "attributes": [[1, 7781400], [2, 77814000], [3, 3890700], [4, 3890700]], "nextLevel": 465, "id": 76005}, {"cost": [[11, 137101, 930]], "level": 465, "attributes": [[1, 7812000], [2, 78120000], [3, 3906000], [4, 3906000]], "nextLevel": 466, "id": 76005}, {"cost": [[11, 137101, 940]], "level": 466, "attributes": [[1, 7842900], [2, 78429000], [3, 3921450], [4, 3921450]], "nextLevel": 467, "id": 76005}, {"cost": [[11, 137101, 940]], "level": 467, "attributes": [[1, 7873800], [2, 78738000], [3, 3936900], [4, 3936900]], "nextLevel": 468, "id": 76005}, {"cost": [[11, 137101, 940]], "level": 468, "attributes": [[1, 7904700], [2, 79047000], [3, 3952350], [4, 3952350]], "nextLevel": 469, "id": 76005}, {"cost": [[11, 137101, 940]], "level": 469, "attributes": [[1, 7935600], [2, 79356000], [3, 3967800], [4, 3967800]], "nextLevel": 470, "id": 76005}, {"cost": [[11, 137101, 940]], "level": 470, "attributes": [[1, 7966500], [2, 79665000], [3, 3983250], [4, 3983250]], "nextLevel": 471, "id": 76005}, {"cost": [[11, 137101, 950]], "level": 471, "attributes": [[1, 7997700], [2, 79977000], [3, 3998850], [4, 3998850]], "nextLevel": 472, "id": 76005}, {"cost": [[11, 137101, 950]], "level": 472, "attributes": [[1, 8028900], [2, 80289000], [3, 4014450], [4, 4014450]], "nextLevel": 473, "id": 76005}, {"cost": [[11, 137101, 950]], "level": 473, "attributes": [[1, 8060100], [2, 80601000], [3, 4030050], [4, 4030050]], "nextLevel": 474, "id": 76005}, {"cost": [[11, 137101, 950]], "level": 474, "attributes": [[1, 8091300], [2, 80913000], [3, 4045650], [4, 4045650]], "nextLevel": 475, "id": 76005}, {"cost": [[11, 137101, 950]], "level": 475, "attributes": [[1, 8122500], [2, 81225000], [3, 4061250], [4, 4061250]], "nextLevel": 476, "id": 76005}, {"cost": [[11, 137101, 960]], "level": 476, "attributes": [[1, 8154000], [2, 81540000], [3, 4077000], [4, 4077000]], "nextLevel": 477, "id": 76005}, {"cost": [[11, 137101, 960]], "level": 477, "attributes": [[1, 8185500], [2, 81855000], [3, 4092750], [4, 4092750]], "nextLevel": 478, "id": 76005}, {"cost": [[11, 137101, 960]], "level": 478, "attributes": [[1, 8217000], [2, 82170000], [3, 4108500], [4, 4108500]], "nextLevel": 479, "id": 76005}, {"cost": [[11, 137101, 960]], "level": 479, "attributes": [[1, 8248500], [2, 82485000], [3, 4124250], [4, 4124250]], "nextLevel": 480, "id": 76005}, {"cost": [[11, 137101, 960]], "level": 480, "attributes": [[1, 8280000], [2, 82800000], [3, 4140000], [4, 4140000]], "nextLevel": 481, "id": 76005}, {"cost": [[11, 137101, 970]], "level": 481, "attributes": [[1, 8311800], [2, 83118000], [3, 4155900], [4, 4155900]], "nextLevel": 482, "id": 76005}, {"cost": [[11, 137101, 970]], "level": 482, "attributes": [[1, 8343600], [2, 83436000], [3, 4171800], [4, 4171800]], "nextLevel": 483, "id": 76005}, {"cost": [[11, 137101, 970]], "level": 483, "attributes": [[1, 8375400], [2, 83754000], [3, 4187700], [4, 4187700]], "nextLevel": 484, "id": 76005}, {"cost": [[11, 137101, 970]], "level": 484, "attributes": [[1, 8407200], [2, 84072000], [3, 4203600], [4, 4203600]], "nextLevel": 485, "id": 76005}, {"cost": [[11, 137101, 970]], "level": 485, "attributes": [[1, 8439000], [2, 84390000], [3, 4219500], [4, 4219500]], "nextLevel": 486, "id": 76005}, {"cost": [[11, 137101, 980]], "level": 486, "attributes": [[1, 8471100], [2, 84711000], [3, 4235550], [4, 4235550]], "nextLevel": 487, "id": 76005}, {"cost": [[11, 137101, 980]], "level": 487, "attributes": [[1, 8503200], [2, 85032000], [3, 4251600], [4, 4251600]], "nextLevel": 488, "id": 76005}, {"cost": [[11, 137101, 980]], "level": 488, "attributes": [[1, 8535300], [2, 85353000], [3, 4267650], [4, 4267650]], "nextLevel": 489, "id": 76005}, {"cost": [[11, 137101, 980]], "level": 489, "attributes": [[1, 8567400], [2, 85674000], [3, 4283700], [4, 4283700]], "nextLevel": 490, "id": 76005}, {"cost": [[11, 137101, 980]], "level": 490, "attributes": [[1, 8599500], [2, 85995000], [3, 4299750], [4, 4299750]], "nextLevel": 491, "id": 76005}, {"cost": [[11, 137101, 990]], "level": 491, "attributes": [[1, 8631900], [2, 86319000], [3, 4315950], [4, 4315950]], "nextLevel": 492, "id": 76005}, {"cost": [[11, 137101, 990]], "level": 492, "attributes": [[1, 8664300], [2, 86643000], [3, 4332150], [4, 4332150]], "nextLevel": 493, "id": 76005}, {"cost": [[11, 137101, 990]], "level": 493, "attributes": [[1, 8696700], [2, 86967000], [3, 4348350], [4, 4348350]], "nextLevel": 494, "id": 76005}, {"cost": [[11, 137101, 990]], "level": 494, "attributes": [[1, 8729100], [2, 87291000], [3, 4364550], [4, 4364550]], "nextLevel": 495, "id": 76005}, {"cost": [[11, 137101, 990]], "level": 495, "attributes": [[1, 8761500], [2, 87615000], [3, 4380750], [4, 4380750]], "nextLevel": 496, "id": 76005}, {"cost": [[11, 137101, 1000]], "level": 496, "attributes": [[1, 8794200], [2, 87942000], [3, 4397100], [4, 4397100]], "nextLevel": 497, "id": 76005}, {"cost": [[11, 137101, 1000]], "level": 497, "attributes": [[1, 8826900], [2, 88269000], [3, 4413450], [4, 4413450]], "nextLevel": 498, "id": 76005}, {"cost": [[11, 137101, 1000]], "level": 498, "attributes": [[1, 8859600], [2, 88596000], [3, 4429800], [4, 4429800]], "nextLevel": 499, "id": 76005}, {"cost": [[11, 137101, 1000]], "level": 499, "attributes": [[1, 8892300], [2, 88923000], [3, 4446150], [4, 4446150]], "nextLevel": 500, "id": 76005}, {"cost": [], "level": 500, "attributes": [[1, 8925000], [2, 89250000], [3, 4462500], [4, 4462500]], "nextLevel": 0, "id": 76005}, {"cost": [[11, 137101, 10]], "level": 1, "attributes": [[1, 3000], [2, 30000], [3, 1500], [4, 1500]], "nextLevel": 2, "id": 76006}, {"cost": [[11, 137101, 10]], "level": 2, "attributes": [[1, 6000], [2, 60000], [3, 3000], [4, 3000]], "nextLevel": 3, "id": 76006}, {"cost": [[11, 137101, 10]], "level": 3, "attributes": [[1, 9000], [2, 90000], [3, 4500], [4, 4500]], "nextLevel": 4, "id": 76006}, {"cost": [[11, 137101, 10]], "level": 4, "attributes": [[1, 12000], [2, 120000], [3, 6000], [4, 6000]], "nextLevel": 5, "id": 76006}, {"cost": [[11, 137101, 10]], "level": 5, "attributes": [[1, 15000], [2, 150000], [3, 7500], [4, 7500]], "nextLevel": 6, "id": 76006}, {"cost": [[11, 137101, 20]], "level": 6, "attributes": [[1, 18300], [2, 183000], [3, 9150], [4, 9150]], "nextLevel": 7, "id": 76006}, {"cost": [[11, 137101, 20]], "level": 7, "attributes": [[1, 21600], [2, 216000], [3, 10800], [4, 10800]], "nextLevel": 8, "id": 76006}, {"cost": [[11, 137101, 20]], "level": 8, "attributes": [[1, 24900], [2, 249000], [3, 12450], [4, 12450]], "nextLevel": 9, "id": 76006}, {"cost": [[11, 137101, 20]], "level": 9, "attributes": [[1, 28200], [2, 282000], [3, 14100], [4, 14100]], "nextLevel": 10, "id": 76006}, {"cost": [[11, 137101, 20]], "level": 10, "attributes": [[1, 31500], [2, 315000], [3, 15750], [4, 15750]], "nextLevel": 11, "id": 76006}, {"cost": [[11, 137101, 30]], "level": 11, "attributes": [[1, 35100], [2, 351000], [3, 17550], [4, 17550]], "nextLevel": 12, "id": 76006}, {"cost": [[11, 137101, 30]], "level": 12, "attributes": [[1, 38700], [2, 387000], [3, 19350], [4, 19350]], "nextLevel": 13, "id": 76006}, {"cost": [[11, 137101, 30]], "level": 13, "attributes": [[1, 42300], [2, 423000], [3, 21150], [4, 21150]], "nextLevel": 14, "id": 76006}, {"cost": [[11, 137101, 30]], "level": 14, "attributes": [[1, 45900], [2, 459000], [3, 22950], [4, 22950]], "nextLevel": 15, "id": 76006}, {"cost": [[11, 137101, 30]], "level": 15, "attributes": [[1, 49500], [2, 495000], [3, 24750], [4, 24750]], "nextLevel": 16, "id": 76006}, {"cost": [[11, 137101, 40]], "level": 16, "attributes": [[1, 53400], [2, 534000], [3, 26700], [4, 26700]], "nextLevel": 17, "id": 76006}, {"cost": [[11, 137101, 40]], "level": 17, "attributes": [[1, 57300], [2, 573000], [3, 28650], [4, 28650]], "nextLevel": 18, "id": 76006}, {"cost": [[11, 137101, 40]], "level": 18, "attributes": [[1, 61200], [2, 612000], [3, 30600], [4, 30600]], "nextLevel": 19, "id": 76006}, {"cost": [[11, 137101, 40]], "level": 19, "attributes": [[1, 65100], [2, 651000], [3, 32550], [4, 32550]], "nextLevel": 20, "id": 76006}, {"cost": [[11, 137101, 40]], "level": 20, "attributes": [[1, 69000], [2, 690000], [3, 34500], [4, 34500]], "nextLevel": 21, "id": 76006}, {"cost": [[11, 137101, 50]], "level": 21, "attributes": [[1, 73200], [2, 732000], [3, 36600], [4, 36600]], "nextLevel": 22, "id": 76006}, {"cost": [[11, 137101, 50]], "level": 22, "attributes": [[1, 77400], [2, 774000], [3, 38700], [4, 38700]], "nextLevel": 23, "id": 76006}, {"cost": [[11, 137101, 50]], "level": 23, "attributes": [[1, 81600], [2, 816000], [3, 40800], [4, 40800]], "nextLevel": 24, "id": 76006}, {"cost": [[11, 137101, 50]], "level": 24, "attributes": [[1, 85800], [2, 858000], [3, 42900], [4, 42900]], "nextLevel": 25, "id": 76006}, {"cost": [[11, 137101, 50]], "level": 25, "attributes": [[1, 90000], [2, 900000], [3, 45000], [4, 45000]], "nextLevel": 26, "id": 76006}, {"cost": [[11, 137101, 60]], "level": 26, "attributes": [[1, 94500], [2, 945000], [3, 47250], [4, 47250]], "nextLevel": 27, "id": 76006}, {"cost": [[11, 137101, 60]], "level": 27, "attributes": [[1, 99000], [2, 990000], [3, 49500], [4, 49500]], "nextLevel": 28, "id": 76006}, {"cost": [[11, 137101, 60]], "level": 28, "attributes": [[1, 103500], [2, 1035000], [3, 51750], [4, 51750]], "nextLevel": 29, "id": 76006}, {"cost": [[11, 137101, 60]], "level": 29, "attributes": [[1, 108000], [2, 1080000], [3, 54000], [4, 54000]], "nextLevel": 30, "id": 76006}, {"cost": [[11, 137101, 60]], "level": 30, "attributes": [[1, 112500], [2, 1125000], [3, 56250], [4, 56250]], "nextLevel": 31, "id": 76006}, {"cost": [[11, 137101, 70]], "level": 31, "attributes": [[1, 117300], [2, 1173000], [3, 58650], [4, 58650]], "nextLevel": 32, "id": 76006}, {"cost": [[11, 137101, 70]], "level": 32, "attributes": [[1, 122100], [2, 1221000], [3, 61050], [4, 61050]], "nextLevel": 33, "id": 76006}, {"cost": [[11, 137101, 70]], "level": 33, "attributes": [[1, 126900], [2, 1269000], [3, 63450], [4, 63450]], "nextLevel": 34, "id": 76006}, {"cost": [[11, 137101, 70]], "level": 34, "attributes": [[1, 131700], [2, 1317000], [3, 65850], [4, 65850]], "nextLevel": 35, "id": 76006}, {"cost": [[11, 137101, 70]], "level": 35, "attributes": [[1, 136500], [2, 1365000], [3, 68250], [4, 68250]], "nextLevel": 36, "id": 76006}, {"cost": [[11, 137101, 80]], "level": 36, "attributes": [[1, 141600], [2, 1416000], [3, 70800], [4, 70800]], "nextLevel": 37, "id": 76006}, {"cost": [[11, 137101, 80]], "level": 37, "attributes": [[1, 146700], [2, 1467000], [3, 73350], [4, 73350]], "nextLevel": 38, "id": 76006}, {"cost": [[11, 137101, 80]], "level": 38, "attributes": [[1, 151800], [2, 1518000], [3, 75900], [4, 75900]], "nextLevel": 39, "id": 76006}, {"cost": [[11, 137101, 80]], "level": 39, "attributes": [[1, 156900], [2, 1569000], [3, 78450], [4, 78450]], "nextLevel": 40, "id": 76006}, {"cost": [[11, 137101, 80]], "level": 40, "attributes": [[1, 162000], [2, 1620000], [3, 81000], [4, 81000]], "nextLevel": 41, "id": 76006}, {"cost": [[11, 137101, 90]], "level": 41, "attributes": [[1, 167400], [2, 1674000], [3, 83700], [4, 83700]], "nextLevel": 42, "id": 76006}, {"cost": [[11, 137101, 90]], "level": 42, "attributes": [[1, 172800], [2, 1728000], [3, 86400], [4, 86400]], "nextLevel": 43, "id": 76006}, {"cost": [[11, 137101, 90]], "level": 43, "attributes": [[1, 178200], [2, 1782000], [3, 89100], [4, 89100]], "nextLevel": 44, "id": 76006}, {"cost": [[11, 137101, 90]], "level": 44, "attributes": [[1, 183600], [2, 1836000], [3, 91800], [4, 91800]], "nextLevel": 45, "id": 76006}, {"cost": [[11, 137101, 90]], "level": 45, "attributes": [[1, 189000], [2, 1890000], [3, 94500], [4, 94500]], "nextLevel": 46, "id": 76006}, {"cost": [[11, 137101, 100]], "level": 46, "attributes": [[1, 194700], [2, 1947000], [3, 97350], [4, 97350]], "nextLevel": 47, "id": 76006}, {"cost": [[11, 137101, 100]], "level": 47, "attributes": [[1, 200400], [2, 2004000], [3, 100200], [4, 100200]], "nextLevel": 48, "id": 76006}, {"cost": [[11, 137101, 100]], "level": 48, "attributes": [[1, 206100], [2, 2061000], [3, 103050], [4, 103050]], "nextLevel": 49, "id": 76006}, {"cost": [[11, 137101, 100]], "level": 49, "attributes": [[1, 211800], [2, 2118000], [3, 105900], [4, 105900]], "nextLevel": 50, "id": 76006}, {"cost": [[11, 137101, 100]], "level": 50, "attributes": [[1, 217500], [2, 2175000], [3, 108750], [4, 108750]], "nextLevel": 51, "id": 76006}, {"cost": [[11, 137101, 110]], "level": 51, "attributes": [[1, 223500], [2, 2235000], [3, 111750], [4, 111750]], "nextLevel": 52, "id": 76006}, {"cost": [[11, 137101, 110]], "level": 52, "attributes": [[1, 229500], [2, 2295000], [3, 114750], [4, 114750]], "nextLevel": 53, "id": 76006}, {"cost": [[11, 137101, 110]], "level": 53, "attributes": [[1, 235500], [2, 2355000], [3, 117750], [4, 117750]], "nextLevel": 54, "id": 76006}, {"cost": [[11, 137101, 110]], "level": 54, "attributes": [[1, 241500], [2, 2415000], [3, 120750], [4, 120750]], "nextLevel": 55, "id": 76006}, {"cost": [[11, 137101, 110]], "level": 55, "attributes": [[1, 247500], [2, 2475000], [3, 123750], [4, 123750]], "nextLevel": 56, "id": 76006}, {"cost": [[11, 137101, 120]], "level": 56, "attributes": [[1, 253800], [2, 2538000], [3, 126900], [4, 126900]], "nextLevel": 57, "id": 76006}, {"cost": [[11, 137101, 120]], "level": 57, "attributes": [[1, 260100], [2, 2601000], [3, 130050], [4, 130050]], "nextLevel": 58, "id": 76006}, {"cost": [[11, 137101, 120]], "level": 58, "attributes": [[1, 266400], [2, 2664000], [3, 133200], [4, 133200]], "nextLevel": 59, "id": 76006}, {"cost": [[11, 137101, 120]], "level": 59, "attributes": [[1, 272700], [2, 2727000], [3, 136350], [4, 136350]], "nextLevel": 60, "id": 76006}, {"cost": [[11, 137101, 120]], "level": 60, "attributes": [[1, 279000], [2, 2790000], [3, 139500], [4, 139500]], "nextLevel": 61, "id": 76006}, {"cost": [[11, 137101, 130]], "level": 61, "attributes": [[1, 285600], [2, 2856000], [3, 142800], [4, 142800]], "nextLevel": 62, "id": 76006}, {"cost": [[11, 137101, 130]], "level": 62, "attributes": [[1, 292200], [2, 2922000], [3, 146100], [4, 146100]], "nextLevel": 63, "id": 76006}, {"cost": [[11, 137101, 130]], "level": 63, "attributes": [[1, 298800], [2, 2988000], [3, 149400], [4, 149400]], "nextLevel": 64, "id": 76006}, {"cost": [[11, 137101, 130]], "level": 64, "attributes": [[1, 305400], [2, 3054000], [3, 152700], [4, 152700]], "nextLevel": 65, "id": 76006}, {"cost": [[11, 137101, 130]], "level": 65, "attributes": [[1, 312000], [2, 3120000], [3, 156000], [4, 156000]], "nextLevel": 66, "id": 76006}, {"cost": [[11, 137101, 140]], "level": 66, "attributes": [[1, 318900], [2, 3189000], [3, 159450], [4, 159450]], "nextLevel": 67, "id": 76006}, {"cost": [[11, 137101, 140]], "level": 67, "attributes": [[1, 325800], [2, 3258000], [3, 162900], [4, 162900]], "nextLevel": 68, "id": 76006}, {"cost": [[11, 137101, 140]], "level": 68, "attributes": [[1, 332700], [2, 3327000], [3, 166350], [4, 166350]], "nextLevel": 69, "id": 76006}, {"cost": [[11, 137101, 140]], "level": 69, "attributes": [[1, 339600], [2, 3396000], [3, 169800], [4, 169800]], "nextLevel": 70, "id": 76006}, {"cost": [[11, 137101, 140]], "level": 70, "attributes": [[1, 346500], [2, 3465000], [3, 173250], [4, 173250]], "nextLevel": 71, "id": 76006}, {"cost": [[11, 137101, 150]], "level": 71, "attributes": [[1, 353700], [2, 3537000], [3, 176850], [4, 176850]], "nextLevel": 72, "id": 76006}, {"cost": [[11, 137101, 150]], "level": 72, "attributes": [[1, 360900], [2, 3609000], [3, 180450], [4, 180450]], "nextLevel": 73, "id": 76006}, {"cost": [[11, 137101, 150]], "level": 73, "attributes": [[1, 368100], [2, 3681000], [3, 184050], [4, 184050]], "nextLevel": 74, "id": 76006}, {"cost": [[11, 137101, 150]], "level": 74, "attributes": [[1, 375300], [2, 3753000], [3, 187650], [4, 187650]], "nextLevel": 75, "id": 76006}, {"cost": [[11, 137101, 150]], "level": 75, "attributes": [[1, 382500], [2, 3825000], [3, 191250], [4, 191250]], "nextLevel": 76, "id": 76006}, {"cost": [[11, 137101, 160]], "level": 76, "attributes": [[1, 390000], [2, 3900000], [3, 195000], [4, 195000]], "nextLevel": 77, "id": 76006}, {"cost": [[11, 137101, 160]], "level": 77, "attributes": [[1, 397500], [2, 3975000], [3, 198750], [4, 198750]], "nextLevel": 78, "id": 76006}, {"cost": [[11, 137101, 160]], "level": 78, "attributes": [[1, 405000], [2, 4050000], [3, 202500], [4, 202500]], "nextLevel": 79, "id": 76006}, {"cost": [[11, 137101, 160]], "level": 79, "attributes": [[1, 412500], [2, 4125000], [3, 206250], [4, 206250]], "nextLevel": 80, "id": 76006}, {"cost": [[11, 137101, 160]], "level": 80, "attributes": [[1, 420000], [2, 4200000], [3, 210000], [4, 210000]], "nextLevel": 81, "id": 76006}, {"cost": [[11, 137101, 170]], "level": 81, "attributes": [[1, 427800], [2, 4278000], [3, 213900], [4, 213900]], "nextLevel": 82, "id": 76006}, {"cost": [[11, 137101, 170]], "level": 82, "attributes": [[1, 435600], [2, 4356000], [3, 217800], [4, 217800]], "nextLevel": 83, "id": 76006}, {"cost": [[11, 137101, 170]], "level": 83, "attributes": [[1, 443400], [2, 4434000], [3, 221700], [4, 221700]], "nextLevel": 84, "id": 76006}, {"cost": [[11, 137101, 170]], "level": 84, "attributes": [[1, 451200], [2, 4512000], [3, 225600], [4, 225600]], "nextLevel": 85, "id": 76006}, {"cost": [[11, 137101, 170]], "level": 85, "attributes": [[1, 459000], [2, 4590000], [3, 229500], [4, 229500]], "nextLevel": 86, "id": 76006}, {"cost": [[11, 137101, 180]], "level": 86, "attributes": [[1, 467100], [2, 4671000], [3, 233550], [4, 233550]], "nextLevel": 87, "id": 76006}, {"cost": [[11, 137101, 180]], "level": 87, "attributes": [[1, 475200], [2, 4752000], [3, 237600], [4, 237600]], "nextLevel": 88, "id": 76006}, {"cost": [[11, 137101, 180]], "level": 88, "attributes": [[1, 483300], [2, 4833000], [3, 241650], [4, 241650]], "nextLevel": 89, "id": 76006}, {"cost": [[11, 137101, 180]], "level": 89, "attributes": [[1, 491400], [2, 4914000], [3, 245700], [4, 245700]], "nextLevel": 90, "id": 76006}, {"cost": [[11, 137101, 180]], "level": 90, "attributes": [[1, 499500], [2, 4995000], [3, 249750], [4, 249750]], "nextLevel": 91, "id": 76006}, {"cost": [[11, 137101, 190]], "level": 91, "attributes": [[1, 507900], [2, 5079000], [3, 253950], [4, 253950]], "nextLevel": 92, "id": 76006}, {"cost": [[11, 137101, 190]], "level": 92, "attributes": [[1, 516300], [2, 5163000], [3, 258150], [4, 258150]], "nextLevel": 93, "id": 76006}, {"cost": [[11, 137101, 190]], "level": 93, "attributes": [[1, 524700], [2, 5247000], [3, 262350], [4, 262350]], "nextLevel": 94, "id": 76006}, {"cost": [[11, 137101, 190]], "level": 94, "attributes": [[1, 533100], [2, 5331000], [3, 266550], [4, 266550]], "nextLevel": 95, "id": 76006}, {"cost": [[11, 137101, 190]], "level": 95, "attributes": [[1, 541500], [2, 5415000], [3, 270750], [4, 270750]], "nextLevel": 96, "id": 76006}, {"cost": [[11, 137101, 200]], "level": 96, "attributes": [[1, 550200], [2, 5502000], [3, 275100], [4, 275100]], "nextLevel": 97, "id": 76006}, {"cost": [[11, 137101, 200]], "level": 97, "attributes": [[1, 558900], [2, 5589000], [3, 279450], [4, 279450]], "nextLevel": 98, "id": 76006}, {"cost": [[11, 137101, 200]], "level": 98, "attributes": [[1, 567600], [2, 5676000], [3, 283800], [4, 283800]], "nextLevel": 99, "id": 76006}, {"cost": [[11, 137101, 200]], "level": 99, "attributes": [[1, 576300], [2, 5763000], [3, 288150], [4, 288150]], "nextLevel": 100, "id": 76006}, {"cost": [[11, 137101, 200]], "level": 100, "attributes": [[1, 585000], [2, 5850000], [3, 292500], [4, 292500]], "nextLevel": 101, "id": 76006}, {"cost": [[11, 137101, 210]], "level": 101, "attributes": [[1, 594000], [2, 5940000], [3, 297000], [4, 297000]], "nextLevel": 102, "id": 76006}, {"cost": [[11, 137101, 210]], "level": 102, "attributes": [[1, 603000], [2, 6030000], [3, 301500], [4, 301500]], "nextLevel": 103, "id": 76006}, {"cost": [[11, 137101, 210]], "level": 103, "attributes": [[1, 612000], [2, 6120000], [3, 306000], [4, 306000]], "nextLevel": 104, "id": 76006}, {"cost": [[11, 137101, 210]], "level": 104, "attributes": [[1, 621000], [2, 6210000], [3, 310500], [4, 310500]], "nextLevel": 105, "id": 76006}, {"cost": [[11, 137101, 210]], "level": 105, "attributes": [[1, 630000], [2, 6300000], [3, 315000], [4, 315000]], "nextLevel": 106, "id": 76006}, {"cost": [[11, 137101, 220]], "level": 106, "attributes": [[1, 639300], [2, 6393000], [3, 319650], [4, 319650]], "nextLevel": 107, "id": 76006}, {"cost": [[11, 137101, 220]], "level": 107, "attributes": [[1, 648600], [2, 6486000], [3, 324300], [4, 324300]], "nextLevel": 108, "id": 76006}, {"cost": [[11, 137101, 220]], "level": 108, "attributes": [[1, 657900], [2, 6579000], [3, 328950], [4, 328950]], "nextLevel": 109, "id": 76006}, {"cost": [[11, 137101, 220]], "level": 109, "attributes": [[1, 667200], [2, 6672000], [3, 333600], [4, 333600]], "nextLevel": 110, "id": 76006}, {"cost": [[11, 137101, 220]], "level": 110, "attributes": [[1, 676500], [2, 6765000], [3, 338250], [4, 338250]], "nextLevel": 111, "id": 76006}, {"cost": [[11, 137101, 230]], "level": 111, "attributes": [[1, 686100], [2, 6861000], [3, 343050], [4, 343050]], "nextLevel": 112, "id": 76006}, {"cost": [[11, 137101, 230]], "level": 112, "attributes": [[1, 695700], [2, 6957000], [3, 347850], [4, 347850]], "nextLevel": 113, "id": 76006}, {"cost": [[11, 137101, 230]], "level": 113, "attributes": [[1, 705300], [2, 7053000], [3, 352650], [4, 352650]], "nextLevel": 114, "id": 76006}, {"cost": [[11, 137101, 230]], "level": 114, "attributes": [[1, 714900], [2, 7149000], [3, 357450], [4, 357450]], "nextLevel": 115, "id": 76006}, {"cost": [[11, 137101, 230]], "level": 115, "attributes": [[1, 724500], [2, 7245000], [3, 362250], [4, 362250]], "nextLevel": 116, "id": 76006}, {"cost": [[11, 137101, 240]], "level": 116, "attributes": [[1, 734400], [2, 7344000], [3, 367200], [4, 367200]], "nextLevel": 117, "id": 76006}, {"cost": [[11, 137101, 240]], "level": 117, "attributes": [[1, 744300], [2, 7443000], [3, 372150], [4, 372150]], "nextLevel": 118, "id": 76006}, {"cost": [[11, 137101, 240]], "level": 118, "attributes": [[1, 754200], [2, 7542000], [3, 377100], [4, 377100]], "nextLevel": 119, "id": 76006}, {"cost": [[11, 137101, 240]], "level": 119, "attributes": [[1, 764100], [2, 7641000], [3, 382050], [4, 382050]], "nextLevel": 120, "id": 76006}, {"cost": [[11, 137101, 240]], "level": 120, "attributes": [[1, 774000], [2, 7740000], [3, 387000], [4, 387000]], "nextLevel": 121, "id": 76006}, {"cost": [[11, 137101, 250]], "level": 121, "attributes": [[1, 784200], [2, 7842000], [3, 392100], [4, 392100]], "nextLevel": 122, "id": 76006}, {"cost": [[11, 137101, 250]], "level": 122, "attributes": [[1, 794400], [2, 7944000], [3, 397200], [4, 397200]], "nextLevel": 123, "id": 76006}, {"cost": [[11, 137101, 250]], "level": 123, "attributes": [[1, 804600], [2, 8046000], [3, 402300], [4, 402300]], "nextLevel": 124, "id": 76006}, {"cost": [[11, 137101, 250]], "level": 124, "attributes": [[1, 814800], [2, 8148000], [3, 407400], [4, 407400]], "nextLevel": 125, "id": 76006}, {"cost": [[11, 137101, 250]], "level": 125, "attributes": [[1, 825000], [2, 8250000], [3, 412500], [4, 412500]], "nextLevel": 126, "id": 76006}, {"cost": [[11, 137101, 260]], "level": 126, "attributes": [[1, 835500], [2, 8355000], [3, 417750], [4, 417750]], "nextLevel": 127, "id": 76006}, {"cost": [[11, 137101, 260]], "level": 127, "attributes": [[1, 846000], [2, 8460000], [3, 423000], [4, 423000]], "nextLevel": 128, "id": 76006}, {"cost": [[11, 137101, 260]], "level": 128, "attributes": [[1, 856500], [2, 8565000], [3, 428250], [4, 428250]], "nextLevel": 129, "id": 76006}, {"cost": [[11, 137101, 260]], "level": 129, "attributes": [[1, 867000], [2, 8670000], [3, 433500], [4, 433500]], "nextLevel": 130, "id": 76006}, {"cost": [[11, 137101, 260]], "level": 130, "attributes": [[1, 877500], [2, 8775000], [3, 438750], [4, 438750]], "nextLevel": 131, "id": 76006}, {"cost": [[11, 137101, 270]], "level": 131, "attributes": [[1, 888300], [2, 8883000], [3, 444150], [4, 444150]], "nextLevel": 132, "id": 76006}, {"cost": [[11, 137101, 270]], "level": 132, "attributes": [[1, 899100], [2, 8991000], [3, 449550], [4, 449550]], "nextLevel": 133, "id": 76006}, {"cost": [[11, 137101, 270]], "level": 133, "attributes": [[1, 909900], [2, 9099000], [3, 454950], [4, 454950]], "nextLevel": 134, "id": 76006}, {"cost": [[11, 137101, 270]], "level": 134, "attributes": [[1, 920700], [2, 9207000], [3, 460350], [4, 460350]], "nextLevel": 135, "id": 76006}, {"cost": [[11, 137101, 270]], "level": 135, "attributes": [[1, 931500], [2, 9315000], [3, 465750], [4, 465750]], "nextLevel": 136, "id": 76006}, {"cost": [[11, 137101, 280]], "level": 136, "attributes": [[1, 942600], [2, 9426000], [3, 471300], [4, 471300]], "nextLevel": 137, "id": 76006}, {"cost": [[11, 137101, 280]], "level": 137, "attributes": [[1, 953700], [2, 9537000], [3, 476850], [4, 476850]], "nextLevel": 138, "id": 76006}, {"cost": [[11, 137101, 280]], "level": 138, "attributes": [[1, 964800], [2, 9648000], [3, 482400], [4, 482400]], "nextLevel": 139, "id": 76006}, {"cost": [[11, 137101, 280]], "level": 139, "attributes": [[1, 975900], [2, 9759000], [3, 487950], [4, 487950]], "nextLevel": 140, "id": 76006}, {"cost": [[11, 137101, 280]], "level": 140, "attributes": [[1, 987000], [2, 9870000], [3, 493500], [4, 493500]], "nextLevel": 141, "id": 76006}, {"cost": [[11, 137101, 290]], "level": 141, "attributes": [[1, 998400], [2, 9984000], [3, 499200], [4, 499200]], "nextLevel": 142, "id": 76006}, {"cost": [[11, 137101, 290]], "level": 142, "attributes": [[1, 1009800], [2, 10098000], [3, 504900], [4, 504900]], "nextLevel": 143, "id": 76006}, {"cost": [[11, 137101, 290]], "level": 143, "attributes": [[1, 1021200], [2, 10212000], [3, 510600], [4, 510600]], "nextLevel": 144, "id": 76006}, {"cost": [[11, 137101, 290]], "level": 144, "attributes": [[1, 1032600], [2, 10326000], [3, 516300], [4, 516300]], "nextLevel": 145, "id": 76006}, {"cost": [[11, 137101, 290]], "level": 145, "attributes": [[1, 1044000], [2, 10440000], [3, 522000], [4, 522000]], "nextLevel": 146, "id": 76006}, {"cost": [[11, 137101, 300]], "level": 146, "attributes": [[1, 1055700], [2, 10557000], [3, 527850], [4, 527850]], "nextLevel": 147, "id": 76006}, {"cost": [[11, 137101, 300]], "level": 147, "attributes": [[1, 1067400], [2, 10674000], [3, 533700], [4, 533700]], "nextLevel": 148, "id": 76006}, {"cost": [[11, 137101, 300]], "level": 148, "attributes": [[1, 1079100], [2, 10791000], [3, 539550], [4, 539550]], "nextLevel": 149, "id": 76006}, {"cost": [[11, 137101, 300]], "level": 149, "attributes": [[1, 1090800], [2, 10908000], [3, 545400], [4, 545400]], "nextLevel": 150, "id": 76006}, {"cost": [[11, 137101, 300]], "level": 150, "attributes": [[1, 1102500], [2, 11025000], [3, 551250], [4, 551250]], "nextLevel": 151, "id": 76006}, {"cost": [[11, 137101, 310]], "level": 151, "attributes": [[1, 1114500], [2, 11145000], [3, 557250], [4, 557250]], "nextLevel": 152, "id": 76006}, {"cost": [[11, 137101, 310]], "level": 152, "attributes": [[1, 1126500], [2, 11265000], [3, 563250], [4, 563250]], "nextLevel": 153, "id": 76006}, {"cost": [[11, 137101, 310]], "level": 153, "attributes": [[1, 1138500], [2, 11385000], [3, 569250], [4, 569250]], "nextLevel": 154, "id": 76006}, {"cost": [[11, 137101, 310]], "level": 154, "attributes": [[1, 1150500], [2, 11505000], [3, 575250], [4, 575250]], "nextLevel": 155, "id": 76006}, {"cost": [[11, 137101, 310]], "level": 155, "attributes": [[1, 1162500], [2, 11625000], [3, 581250], [4, 581250]], "nextLevel": 156, "id": 76006}, {"cost": [[11, 137101, 320]], "level": 156, "attributes": [[1, 1174800], [2, 11748000], [3, 587400], [4, 587400]], "nextLevel": 157, "id": 76006}, {"cost": [[11, 137101, 320]], "level": 157, "attributes": [[1, 1187100], [2, 11871000], [3, 593550], [4, 593550]], "nextLevel": 158, "id": 76006}, {"cost": [[11, 137101, 320]], "level": 158, "attributes": [[1, 1199400], [2, 11994000], [3, 599700], [4, 599700]], "nextLevel": 159, "id": 76006}, {"cost": [[11, 137101, 320]], "level": 159, "attributes": [[1, 1211700], [2, 12117000], [3, 605850], [4, 605850]], "nextLevel": 160, "id": 76006}, {"cost": [[11, 137101, 320]], "level": 160, "attributes": [[1, 1224000], [2, 12240000], [3, 612000], [4, 612000]], "nextLevel": 161, "id": 76006}, {"cost": [[11, 137101, 330]], "level": 161, "attributes": [[1, 1236600], [2, 12366000], [3, 618300], [4, 618300]], "nextLevel": 162, "id": 76006}, {"cost": [[11, 137101, 330]], "level": 162, "attributes": [[1, 1249200], [2, 12492000], [3, 624600], [4, 624600]], "nextLevel": 163, "id": 76006}, {"cost": [[11, 137101, 330]], "level": 163, "attributes": [[1, 1261800], [2, 12618000], [3, 630900], [4, 630900]], "nextLevel": 164, "id": 76006}, {"cost": [[11, 137101, 330]], "level": 164, "attributes": [[1, 1274400], [2, 12744000], [3, 637200], [4, 637200]], "nextLevel": 165, "id": 76006}, {"cost": [[11, 137101, 330]], "level": 165, "attributes": [[1, 1287000], [2, 12870000], [3, 643500], [4, 643500]], "nextLevel": 166, "id": 76006}, {"cost": [[11, 137101, 340]], "level": 166, "attributes": [[1, 1299900], [2, 12999000], [3, 649950], [4, 649950]], "nextLevel": 167, "id": 76006}, {"cost": [[11, 137101, 340]], "level": 167, "attributes": [[1, 1312800], [2, 13128000], [3, 656400], [4, 656400]], "nextLevel": 168, "id": 76006}, {"cost": [[11, 137101, 340]], "level": 168, "attributes": [[1, 1325700], [2, 13257000], [3, 662850], [4, 662850]], "nextLevel": 169, "id": 76006}, {"cost": [[11, 137101, 340]], "level": 169, "attributes": [[1, 1338600], [2, 13386000], [3, 669300], [4, 669300]], "nextLevel": 170, "id": 76006}, {"cost": [[11, 137101, 340]], "level": 170, "attributes": [[1, 1351500], [2, 13515000], [3, 675750], [4, 675750]], "nextLevel": 171, "id": 76006}, {"cost": [[11, 137101, 350]], "level": 171, "attributes": [[1, 1364700], [2, 13647000], [3, 682350], [4, 682350]], "nextLevel": 172, "id": 76006}, {"cost": [[11, 137101, 350]], "level": 172, "attributes": [[1, 1377900], [2, 13779000], [3, 688950], [4, 688950]], "nextLevel": 173, "id": 76006}, {"cost": [[11, 137101, 350]], "level": 173, "attributes": [[1, 1391100], [2, 13911000], [3, 695550], [4, 695550]], "nextLevel": 174, "id": 76006}, {"cost": [[11, 137101, 350]], "level": 174, "attributes": [[1, 1404300], [2, 14043000], [3, 702150], [4, 702150]], "nextLevel": 175, "id": 76006}, {"cost": [[11, 137101, 350]], "level": 175, "attributes": [[1, 1417500], [2, 14175000], [3, 708750], [4, 708750]], "nextLevel": 176, "id": 76006}, {"cost": [[11, 137101, 360]], "level": 176, "attributes": [[1, 1431000], [2, 14310000], [3, 715500], [4, 715500]], "nextLevel": 177, "id": 76006}, {"cost": [[11, 137101, 360]], "level": 177, "attributes": [[1, 1444500], [2, 14445000], [3, 722250], [4, 722250]], "nextLevel": 178, "id": 76006}, {"cost": [[11, 137101, 360]], "level": 178, "attributes": [[1, 1458000], [2, 14580000], [3, 729000], [4, 729000]], "nextLevel": 179, "id": 76006}, {"cost": [[11, 137101, 360]], "level": 179, "attributes": [[1, 1471500], [2, 14715000], [3, 735750], [4, 735750]], "nextLevel": 180, "id": 76006}, {"cost": [[11, 137101, 360]], "level": 180, "attributes": [[1, 1485000], [2, 14850000], [3, 742500], [4, 742500]], "nextLevel": 181, "id": 76006}, {"cost": [[11, 137101, 370]], "level": 181, "attributes": [[1, 1498800], [2, 14988000], [3, 749400], [4, 749400]], "nextLevel": 182, "id": 76006}, {"cost": [[11, 137101, 370]], "level": 182, "attributes": [[1, 1512600], [2, 15126000], [3, 756300], [4, 756300]], "nextLevel": 183, "id": 76006}, {"cost": [[11, 137101, 370]], "level": 183, "attributes": [[1, 1526400], [2, 15264000], [3, 763200], [4, 763200]], "nextLevel": 184, "id": 76006}, {"cost": [[11, 137101, 370]], "level": 184, "attributes": [[1, 1540200], [2, 15402000], [3, 770100], [4, 770100]], "nextLevel": 185, "id": 76006}, {"cost": [[11, 137101, 370]], "level": 185, "attributes": [[1, 1554000], [2, 15540000], [3, 777000], [4, 777000]], "nextLevel": 186, "id": 76006}, {"cost": [[11, 137101, 380]], "level": 186, "attributes": [[1, 1568100], [2, 15681000], [3, 784050], [4, 784050]], "nextLevel": 187, "id": 76006}, {"cost": [[11, 137101, 380]], "level": 187, "attributes": [[1, 1582200], [2, 15822000], [3, 791100], [4, 791100]], "nextLevel": 188, "id": 76006}, {"cost": [[11, 137101, 380]], "level": 188, "attributes": [[1, 1596300], [2, 15963000], [3, 798150], [4, 798150]], "nextLevel": 189, "id": 76006}, {"cost": [[11, 137101, 380]], "level": 189, "attributes": [[1, 1610400], [2, 16104000], [3, 805200], [4, 805200]], "nextLevel": 190, "id": 76006}, {"cost": [[11, 137101, 380]], "level": 190, "attributes": [[1, 1624500], [2, 16245000], [3, 812250], [4, 812250]], "nextLevel": 191, "id": 76006}, {"cost": [[11, 137101, 390]], "level": 191, "attributes": [[1, 1638900], [2, 16389000], [3, 819450], [4, 819450]], "nextLevel": 192, "id": 76006}, {"cost": [[11, 137101, 390]], "level": 192, "attributes": [[1, 1653300], [2, 16533000], [3, 826650], [4, 826650]], "nextLevel": 193, "id": 76006}, {"cost": [[11, 137101, 390]], "level": 193, "attributes": [[1, 1667700], [2, 16677000], [3, 833850], [4, 833850]], "nextLevel": 194, "id": 76006}, {"cost": [[11, 137101, 390]], "level": 194, "attributes": [[1, 1682100], [2, 16821000], [3, 841050], [4, 841050]], "nextLevel": 195, "id": 76006}, {"cost": [[11, 137101, 390]], "level": 195, "attributes": [[1, 1696500], [2, 16965000], [3, 848250], [4, 848250]], "nextLevel": 196, "id": 76006}, {"cost": [[11, 137101, 400]], "level": 196, "attributes": [[1, 1711200], [2, 17112000], [3, 855600], [4, 855600]], "nextLevel": 197, "id": 76006}, {"cost": [[11, 137101, 400]], "level": 197, "attributes": [[1, 1725900], [2, 17259000], [3, 862950], [4, 862950]], "nextLevel": 198, "id": 76006}, {"cost": [[11, 137101, 400]], "level": 198, "attributes": [[1, 1740600], [2, 17406000], [3, 870300], [4, 870300]], "nextLevel": 199, "id": 76006}, {"cost": [[11, 137101, 400]], "level": 199, "attributes": [[1, 1755300], [2, 17553000], [3, 877650], [4, 877650]], "nextLevel": 200, "id": 76006}, {"cost": [[11, 137101, 400]], "level": 200, "attributes": [[1, 1770000], [2, 17700000], [3, 885000], [4, 885000]], "nextLevel": 201, "id": 76006}, {"cost": [[11, 137101, 410]], "level": 201, "attributes": [[1, 1785000], [2, 17850000], [3, 892500], [4, 892500]], "nextLevel": 202, "id": 76006}, {"cost": [[11, 137101, 410]], "level": 202, "attributes": [[1, 1800000], [2, 18000000], [3, 900000], [4, 900000]], "nextLevel": 203, "id": 76006}, {"cost": [[11, 137101, 410]], "level": 203, "attributes": [[1, 1815000], [2, 18150000], [3, 907500], [4, 907500]], "nextLevel": 204, "id": 76006}, {"cost": [[11, 137101, 410]], "level": 204, "attributes": [[1, 1830000], [2, 18300000], [3, 915000], [4, 915000]], "nextLevel": 205, "id": 76006}, {"cost": [[11, 137101, 410]], "level": 205, "attributes": [[1, 1845000], [2, 18450000], [3, 922500], [4, 922500]], "nextLevel": 206, "id": 76006}, {"cost": [[11, 137101, 420]], "level": 206, "attributes": [[1, 1860300], [2, 18603000], [3, 930150], [4, 930150]], "nextLevel": 207, "id": 76006}, {"cost": [[11, 137101, 420]], "level": 207, "attributes": [[1, 1875600], [2, 18756000], [3, 937800], [4, 937800]], "nextLevel": 208, "id": 76006}, {"cost": [[11, 137101, 420]], "level": 208, "attributes": [[1, 1890900], [2, 18909000], [3, 945450], [4, 945450]], "nextLevel": 209, "id": 76006}, {"cost": [[11, 137101, 420]], "level": 209, "attributes": [[1, 1906200], [2, 19062000], [3, 953100], [4, 953100]], "nextLevel": 210, "id": 76006}, {"cost": [[11, 137101, 420]], "level": 210, "attributes": [[1, 1921500], [2, 19215000], [3, 960750], [4, 960750]], "nextLevel": 211, "id": 76006}, {"cost": [[11, 137101, 430]], "level": 211, "attributes": [[1, 1937100], [2, 19371000], [3, 968550], [4, 968550]], "nextLevel": 212, "id": 76006}, {"cost": [[11, 137101, 430]], "level": 212, "attributes": [[1, 1952700], [2, 19527000], [3, 976350], [4, 976350]], "nextLevel": 213, "id": 76006}, {"cost": [[11, 137101, 430]], "level": 213, "attributes": [[1, 1968300], [2, 19683000], [3, 984150], [4, 984150]], "nextLevel": 214, "id": 76006}, {"cost": [[11, 137101, 430]], "level": 214, "attributes": [[1, 1983900], [2, 19839000], [3, 991950], [4, 991950]], "nextLevel": 215, "id": 76006}, {"cost": [[11, 137101, 430]], "level": 215, "attributes": [[1, 1999500], [2, 19995000], [3, 999750], [4, 999750]], "nextLevel": 216, "id": 76006}, {"cost": [[11, 137101, 440]], "level": 216, "attributes": [[1, 2015400], [2, 20154000], [3, 1007700], [4, 1007700]], "nextLevel": 217, "id": 76006}, {"cost": [[11, 137101, 440]], "level": 217, "attributes": [[1, 2031300], [2, 20313000], [3, 1015650], [4, 1015650]], "nextLevel": 218, "id": 76006}, {"cost": [[11, 137101, 440]], "level": 218, "attributes": [[1, 2047200], [2, 20472000], [3, 1023600], [4, 1023600]], "nextLevel": 219, "id": 76006}, {"cost": [[11, 137101, 440]], "level": 219, "attributes": [[1, 2063100], [2, 20631000], [3, 1031550], [4, 1031550]], "nextLevel": 220, "id": 76006}, {"cost": [[11, 137101, 440]], "level": 220, "attributes": [[1, 2079000], [2, 20790000], [3, 1039500], [4, 1039500]], "nextLevel": 221, "id": 76006}, {"cost": [[11, 137101, 450]], "level": 221, "attributes": [[1, 2095200], [2, 20952000], [3, 1047600], [4, 1047600]], "nextLevel": 222, "id": 76006}, {"cost": [[11, 137101, 450]], "level": 222, "attributes": [[1, 2111400], [2, 21114000], [3, 1055700], [4, 1055700]], "nextLevel": 223, "id": 76006}, {"cost": [[11, 137101, 450]], "level": 223, "attributes": [[1, 2127600], [2, 21276000], [3, 1063800], [4, 1063800]], "nextLevel": 224, "id": 76006}, {"cost": [[11, 137101, 450]], "level": 224, "attributes": [[1, 2143800], [2, 21438000], [3, 1071900], [4, 1071900]], "nextLevel": 225, "id": 76006}, {"cost": [[11, 137101, 450]], "level": 225, "attributes": [[1, 2160000], [2, 21600000], [3, 1080000], [4, 1080000]], "nextLevel": 226, "id": 76006}, {"cost": [[11, 137101, 460]], "level": 226, "attributes": [[1, 2176500], [2, 21765000], [3, 1088250], [4, 1088250]], "nextLevel": 227, "id": 76006}, {"cost": [[11, 137101, 460]], "level": 227, "attributes": [[1, 2193000], [2, 21930000], [3, 1096500], [4, 1096500]], "nextLevel": 228, "id": 76006}, {"cost": [[11, 137101, 460]], "level": 228, "attributes": [[1, 2209500], [2, 22095000], [3, 1104750], [4, 1104750]], "nextLevel": 229, "id": 76006}, {"cost": [[11, 137101, 460]], "level": 229, "attributes": [[1, 2226000], [2, 22260000], [3, 1113000], [4, 1113000]], "nextLevel": 230, "id": 76006}, {"cost": [[11, 137101, 460]], "level": 230, "attributes": [[1, 2242500], [2, 22425000], [3, 1121250], [4, 1121250]], "nextLevel": 231, "id": 76006}, {"cost": [[11, 137101, 470]], "level": 231, "attributes": [[1, 2259300], [2, 22593000], [3, 1129650], [4, 1129650]], "nextLevel": 232, "id": 76006}, {"cost": [[11, 137101, 470]], "level": 232, "attributes": [[1, 2276100], [2, 22761000], [3, 1138050], [4, 1138050]], "nextLevel": 233, "id": 76006}, {"cost": [[11, 137101, 470]], "level": 233, "attributes": [[1, 2292900], [2, 22929000], [3, 1146450], [4, 1146450]], "nextLevel": 234, "id": 76006}, {"cost": [[11, 137101, 470]], "level": 234, "attributes": [[1, 2309700], [2, 23097000], [3, 1154850], [4, 1154850]], "nextLevel": 235, "id": 76006}, {"cost": [[11, 137101, 470]], "level": 235, "attributes": [[1, 2326500], [2, 23265000], [3, 1163250], [4, 1163250]], "nextLevel": 236, "id": 76006}, {"cost": [[11, 137101, 480]], "level": 236, "attributes": [[1, 2343600], [2, 23436000], [3, 1171800], [4, 1171800]], "nextLevel": 237, "id": 76006}, {"cost": [[11, 137101, 480]], "level": 237, "attributes": [[1, 2360700], [2, 23607000], [3, 1180350], [4, 1180350]], "nextLevel": 238, "id": 76006}, {"cost": [[11, 137101, 480]], "level": 238, "attributes": [[1, 2377800], [2, 23778000], [3, 1188900], [4, 1188900]], "nextLevel": 239, "id": 76006}, {"cost": [[11, 137101, 480]], "level": 239, "attributes": [[1, 2394900], [2, 23949000], [3, 1197450], [4, 1197450]], "nextLevel": 240, "id": 76006}, {"cost": [[11, 137101, 480]], "level": 240, "attributes": [[1, 2412000], [2, 24120000], [3, 1206000], [4, 1206000]], "nextLevel": 241, "id": 76006}, {"cost": [[11, 137101, 490]], "level": 241, "attributes": [[1, 2429400], [2, 24294000], [3, 1214700], [4, 1214700]], "nextLevel": 242, "id": 76006}, {"cost": [[11, 137101, 490]], "level": 242, "attributes": [[1, 2446800], [2, 24468000], [3, 1223400], [4, 1223400]], "nextLevel": 243, "id": 76006}, {"cost": [[11, 137101, 490]], "level": 243, "attributes": [[1, 2464200], [2, 24642000], [3, 1232100], [4, 1232100]], "nextLevel": 244, "id": 76006}, {"cost": [[11, 137101, 490]], "level": 244, "attributes": [[1, 2481600], [2, 24816000], [3, 1240800], [4, 1240800]], "nextLevel": 245, "id": 76006}, {"cost": [[11, 137101, 490]], "level": 245, "attributes": [[1, 2499000], [2, 24990000], [3, 1249500], [4, 1249500]], "nextLevel": 246, "id": 76006}, {"cost": [[11, 137101, 500]], "level": 246, "attributes": [[1, 2516700], [2, 25167000], [3, 1258350], [4, 1258350]], "nextLevel": 247, "id": 76006}, {"cost": [[11, 137101, 500]], "level": 247, "attributes": [[1, 2534400], [2, 25344000], [3, 1267200], [4, 1267200]], "nextLevel": 248, "id": 76006}, {"cost": [[11, 137101, 500]], "level": 248, "attributes": [[1, 2552100], [2, 25521000], [3, 1276050], [4, 1276050]], "nextLevel": 249, "id": 76006}, {"cost": [[11, 137101, 500]], "level": 249, "attributes": [[1, 2569800], [2, 25698000], [3, 1284900], [4, 1284900]], "nextLevel": 250, "id": 76006}, {"cost": [[11, 137101, 500]], "level": 250, "attributes": [[1, 2587500], [2, 25875000], [3, 1293750], [4, 1293750]], "nextLevel": 251, "id": 76006}, {"cost": [[11, 137101, 510]], "level": 251, "attributes": [[1, 2605500], [2, 26055000], [3, 1302750], [4, 1302750]], "nextLevel": 252, "id": 76006}, {"cost": [[11, 137101, 510]], "level": 252, "attributes": [[1, 2623500], [2, 26235000], [3, 1311750], [4, 1311750]], "nextLevel": 253, "id": 76006}, {"cost": [[11, 137101, 510]], "level": 253, "attributes": [[1, 2641500], [2, 26415000], [3, 1320750], [4, 1320750]], "nextLevel": 254, "id": 76006}, {"cost": [[11, 137101, 510]], "level": 254, "attributes": [[1, 2659500], [2, 26595000], [3, 1329750], [4, 1329750]], "nextLevel": 255, "id": 76006}, {"cost": [[11, 137101, 510]], "level": 255, "attributes": [[1, 2677500], [2, 26775000], [3, 1338750], [4, 1338750]], "nextLevel": 256, "id": 76006}, {"cost": [[11, 137101, 520]], "level": 256, "attributes": [[1, 2695800], [2, 26958000], [3, 1347900], [4, 1347900]], "nextLevel": 257, "id": 76006}, {"cost": [[11, 137101, 520]], "level": 257, "attributes": [[1, 2714100], [2, 27141000], [3, 1357050], [4, 1357050]], "nextLevel": 258, "id": 76006}, {"cost": [[11, 137101, 520]], "level": 258, "attributes": [[1, 2732400], [2, 27324000], [3, 1366200], [4, 1366200]], "nextLevel": 259, "id": 76006}, {"cost": [[11, 137101, 520]], "level": 259, "attributes": [[1, 2750700], [2, 27507000], [3, 1375350], [4, 1375350]], "nextLevel": 260, "id": 76006}, {"cost": [[11, 137101, 520]], "level": 260, "attributes": [[1, 2769000], [2, 27690000], [3, 1384500], [4, 1384500]], "nextLevel": 261, "id": 76006}, {"cost": [[11, 137101, 530]], "level": 261, "attributes": [[1, 2787600], [2, 27876000], [3, 1393800], [4, 1393800]], "nextLevel": 262, "id": 76006}, {"cost": [[11, 137101, 530]], "level": 262, "attributes": [[1, 2806200], [2, 28062000], [3, 1403100], [4, 1403100]], "nextLevel": 263, "id": 76006}, {"cost": [[11, 137101, 530]], "level": 263, "attributes": [[1, 2824800], [2, 28248000], [3, 1412400], [4, 1412400]], "nextLevel": 264, "id": 76006}, {"cost": [[11, 137101, 530]], "level": 264, "attributes": [[1, 2843400], [2, 28434000], [3, 1421700], [4, 1421700]], "nextLevel": 265, "id": 76006}, {"cost": [[11, 137101, 530]], "level": 265, "attributes": [[1, 2862000], [2, 28620000], [3, 1431000], [4, 1431000]], "nextLevel": 266, "id": 76006}, {"cost": [[11, 137101, 540]], "level": 266, "attributes": [[1, 2880900], [2, 28809000], [3, 1440450], [4, 1440450]], "nextLevel": 267, "id": 76006}, {"cost": [[11, 137101, 540]], "level": 267, "attributes": [[1, 2899800], [2, 28998000], [3, 1449900], [4, 1449900]], "nextLevel": 268, "id": 76006}, {"cost": [[11, 137101, 540]], "level": 268, "attributes": [[1, 2918700], [2, 29187000], [3, 1459350], [4, 1459350]], "nextLevel": 269, "id": 76006}, {"cost": [[11, 137101, 540]], "level": 269, "attributes": [[1, 2937600], [2, 29376000], [3, 1468800], [4, 1468800]], "nextLevel": 270, "id": 76006}, {"cost": [[11, 137101, 540]], "level": 270, "attributes": [[1, 2956500], [2, 29565000], [3, 1478250], [4, 1478250]], "nextLevel": 271, "id": 76006}, {"cost": [[11, 137101, 550]], "level": 271, "attributes": [[1, 2975700], [2, 29757000], [3, 1487850], [4, 1487850]], "nextLevel": 272, "id": 76006}, {"cost": [[11, 137101, 550]], "level": 272, "attributes": [[1, 2994900], [2, 29949000], [3, 1497450], [4, 1497450]], "nextLevel": 273, "id": 76006}, {"cost": [[11, 137101, 550]], "level": 273, "attributes": [[1, 3014100], [2, 30141000], [3, 1507050], [4, 1507050]], "nextLevel": 274, "id": 76006}, {"cost": [[11, 137101, 550]], "level": 274, "attributes": [[1, 3033300], [2, 30333000], [3, 1516650], [4, 1516650]], "nextLevel": 275, "id": 76006}, {"cost": [[11, 137101, 550]], "level": 275, "attributes": [[1, 3052500], [2, 30525000], [3, 1526250], [4, 1526250]], "nextLevel": 276, "id": 76006}, {"cost": [[11, 137101, 560]], "level": 276, "attributes": [[1, 3072000], [2, 30720000], [3, 1536000], [4, 1536000]], "nextLevel": 277, "id": 76006}, {"cost": [[11, 137101, 560]], "level": 277, "attributes": [[1, 3091500], [2, 30915000], [3, 1545750], [4, 1545750]], "nextLevel": 278, "id": 76006}, {"cost": [[11, 137101, 560]], "level": 278, "attributes": [[1, 3111000], [2, 31110000], [3, 1555500], [4, 1555500]], "nextLevel": 279, "id": 76006}, {"cost": [[11, 137101, 560]], "level": 279, "attributes": [[1, 3130500], [2, 31305000], [3, 1565250], [4, 1565250]], "nextLevel": 280, "id": 76006}, {"cost": [[11, 137101, 560]], "level": 280, "attributes": [[1, 3150000], [2, 31500000], [3, 1575000], [4, 1575000]], "nextLevel": 281, "id": 76006}, {"cost": [[11, 137101, 570]], "level": 281, "attributes": [[1, 3169800], [2, 31698000], [3, 1584900], [4, 1584900]], "nextLevel": 282, "id": 76006}, {"cost": [[11, 137101, 570]], "level": 282, "attributes": [[1, 3189600], [2, 31896000], [3, 1594800], [4, 1594800]], "nextLevel": 283, "id": 76006}, {"cost": [[11, 137101, 570]], "level": 283, "attributes": [[1, 3209400], [2, 32094000], [3, 1604700], [4, 1604700]], "nextLevel": 284, "id": 76006}, {"cost": [[11, 137101, 570]], "level": 284, "attributes": [[1, 3229200], [2, 32292000], [3, 1614600], [4, 1614600]], "nextLevel": 285, "id": 76006}, {"cost": [[11, 137101, 570]], "level": 285, "attributes": [[1, 3249000], [2, 32490000], [3, 1624500], [4, 1624500]], "nextLevel": 286, "id": 76006}, {"cost": [[11, 137101, 580]], "level": 286, "attributes": [[1, 3269100], [2, 32691000], [3, 1634550], [4, 1634550]], "nextLevel": 287, "id": 76006}, {"cost": [[11, 137101, 580]], "level": 287, "attributes": [[1, 3289200], [2, 32892000], [3, 1644600], [4, 1644600]], "nextLevel": 288, "id": 76006}, {"cost": [[11, 137101, 580]], "level": 288, "attributes": [[1, 3309300], [2, 33093000], [3, 1654650], [4, 1654650]], "nextLevel": 289, "id": 76006}, {"cost": [[11, 137101, 580]], "level": 289, "attributes": [[1, 3329400], [2, 33294000], [3, 1664700], [4, 1664700]], "nextLevel": 290, "id": 76006}, {"cost": [[11, 137101, 580]], "level": 290, "attributes": [[1, 3349500], [2, 33495000], [3, 1674750], [4, 1674750]], "nextLevel": 291, "id": 76006}, {"cost": [[11, 137101, 590]], "level": 291, "attributes": [[1, 3369900], [2, 33699000], [3, 1684950], [4, 1684950]], "nextLevel": 292, "id": 76006}, {"cost": [[11, 137101, 590]], "level": 292, "attributes": [[1, 3390300], [2, 33903000], [3, 1695150], [4, 1695150]], "nextLevel": 293, "id": 76006}, {"cost": [[11, 137101, 590]], "level": 293, "attributes": [[1, 3410700], [2, 34107000], [3, 1705350], [4, 1705350]], "nextLevel": 294, "id": 76006}, {"cost": [[11, 137101, 590]], "level": 294, "attributes": [[1, 3431100], [2, 34311000], [3, 1715550], [4, 1715550]], "nextLevel": 295, "id": 76006}, {"cost": [[11, 137101, 590]], "level": 295, "attributes": [[1, 3451500], [2, 34515000], [3, 1725750], [4, 1725750]], "nextLevel": 296, "id": 76006}, {"cost": [[11, 137101, 600]], "level": 296, "attributes": [[1, 3472200], [2, 34722000], [3, 1736100], [4, 1736100]], "nextLevel": 297, "id": 76006}, {"cost": [[11, 137101, 600]], "level": 297, "attributes": [[1, 3492900], [2, 34929000], [3, 1746450], [4, 1746450]], "nextLevel": 298, "id": 76006}, {"cost": [[11, 137101, 600]], "level": 298, "attributes": [[1, 3513600], [2, 35136000], [3, 1756800], [4, 1756800]], "nextLevel": 299, "id": 76006}, {"cost": [[11, 137101, 600]], "level": 299, "attributes": [[1, 3534300], [2, 35343000], [3, 1767150], [4, 1767150]], "nextLevel": 300, "id": 76006}, {"cost": [[11, 137101, 600]], "level": 300, "attributes": [[1, 3555000], [2, 35550000], [3, 1777500], [4, 1777500]], "nextLevel": 301, "id": 76006}, {"cost": [[11, 137101, 610]], "level": 301, "attributes": [[1, 3576000], [2, 35760000], [3, 1788000], [4, 1788000]], "nextLevel": 302, "id": 76006}, {"cost": [[11, 137101, 610]], "level": 302, "attributes": [[1, 3597000], [2, 35970000], [3, 1798500], [4, 1798500]], "nextLevel": 303, "id": 76006}, {"cost": [[11, 137101, 610]], "level": 303, "attributes": [[1, 3618000], [2, 36180000], [3, 1809000], [4, 1809000]], "nextLevel": 304, "id": 76006}, {"cost": [[11, 137101, 610]], "level": 304, "attributes": [[1, 3639000], [2, 36390000], [3, 1819500], [4, 1819500]], "nextLevel": 305, "id": 76006}, {"cost": [[11, 137101, 610]], "level": 305, "attributes": [[1, 3660000], [2, 36600000], [3, 1830000], [4, 1830000]], "nextLevel": 306, "id": 76006}, {"cost": [[11, 137101, 620]], "level": 306, "attributes": [[1, 3681300], [2, 36813000], [3, 1840650], [4, 1840650]], "nextLevel": 307, "id": 76006}, {"cost": [[11, 137101, 620]], "level": 307, "attributes": [[1, 3702600], [2, 37026000], [3, 1851300], [4, 1851300]], "nextLevel": 308, "id": 76006}, {"cost": [[11, 137101, 620]], "level": 308, "attributes": [[1, 3723900], [2, 37239000], [3, 1861950], [4, 1861950]], "nextLevel": 309, "id": 76006}, {"cost": [[11, 137101, 620]], "level": 309, "attributes": [[1, 3745200], [2, 37452000], [3, 1872600], [4, 1872600]], "nextLevel": 310, "id": 76006}, {"cost": [[11, 137101, 620]], "level": 310, "attributes": [[1, 3766500], [2, 37665000], [3, 1883250], [4, 1883250]], "nextLevel": 311, "id": 76006}, {"cost": [[11, 137101, 630]], "level": 311, "attributes": [[1, 3788100], [2, 37881000], [3, 1894050], [4, 1894050]], "nextLevel": 312, "id": 76006}, {"cost": [[11, 137101, 630]], "level": 312, "attributes": [[1, 3809700], [2, 38097000], [3, 1904850], [4, 1904850]], "nextLevel": 313, "id": 76006}, {"cost": [[11, 137101, 630]], "level": 313, "attributes": [[1, 3831300], [2, 38313000], [3, 1915650], [4, 1915650]], "nextLevel": 314, "id": 76006}, {"cost": [[11, 137101, 630]], "level": 314, "attributes": [[1, 3852900], [2, 38529000], [3, 1926450], [4, 1926450]], "nextLevel": 315, "id": 76006}, {"cost": [[11, 137101, 630]], "level": 315, "attributes": [[1, 3874500], [2, 38745000], [3, 1937250], [4, 1937250]], "nextLevel": 316, "id": 76006}, {"cost": [[11, 137101, 640]], "level": 316, "attributes": [[1, 3896400], [2, 38964000], [3, 1948200], [4, 1948200]], "nextLevel": 317, "id": 76006}, {"cost": [[11, 137101, 640]], "level": 317, "attributes": [[1, 3918300], [2, 39183000], [3, 1959150], [4, 1959150]], "nextLevel": 318, "id": 76006}, {"cost": [[11, 137101, 640]], "level": 318, "attributes": [[1, 3940200], [2, 39402000], [3, 1970100], [4, 1970100]], "nextLevel": 319, "id": 76006}, {"cost": [[11, 137101, 640]], "level": 319, "attributes": [[1, 3962100], [2, 39621000], [3, 1981050], [4, 1981050]], "nextLevel": 320, "id": 76006}, {"cost": [[11, 137101, 640]], "level": 320, "attributes": [[1, 3984000], [2, 39840000], [3, 1992000], [4, 1992000]], "nextLevel": 321, "id": 76006}, {"cost": [[11, 137101, 650]], "level": 321, "attributes": [[1, 4006200], [2, 40062000], [3, 2003100], [4, 2003100]], "nextLevel": 322, "id": 76006}, {"cost": [[11, 137101, 650]], "level": 322, "attributes": [[1, 4028400], [2, 40284000], [3, 2014200], [4, 2014200]], "nextLevel": 323, "id": 76006}, {"cost": [[11, 137101, 650]], "level": 323, "attributes": [[1, 4050600], [2, 40506000], [3, 2025300], [4, 2025300]], "nextLevel": 324, "id": 76006}, {"cost": [[11, 137101, 650]], "level": 324, "attributes": [[1, 4072800], [2, 40728000], [3, 2036400], [4, 2036400]], "nextLevel": 325, "id": 76006}, {"cost": [[11, 137101, 650]], "level": 325, "attributes": [[1, 4095000], [2, 40950000], [3, 2047500], [4, 2047500]], "nextLevel": 326, "id": 76006}, {"cost": [[11, 137101, 660]], "level": 326, "attributes": [[1, 4117500], [2, 41175000], [3, 2058750], [4, 2058750]], "nextLevel": 327, "id": 76006}, {"cost": [[11, 137101, 660]], "level": 327, "attributes": [[1, 4140000], [2, 41400000], [3, 2070000], [4, 2070000]], "nextLevel": 328, "id": 76006}, {"cost": [[11, 137101, 660]], "level": 328, "attributes": [[1, 4162500], [2, 41625000], [3, 2081250], [4, 2081250]], "nextLevel": 329, "id": 76006}, {"cost": [[11, 137101, 660]], "level": 329, "attributes": [[1, 4185000], [2, 41850000], [3, 2092500], [4, 2092500]], "nextLevel": 330, "id": 76006}, {"cost": [[11, 137101, 660]], "level": 330, "attributes": [[1, 4207500], [2, 42075000], [3, 2103750], [4, 2103750]], "nextLevel": 331, "id": 76006}, {"cost": [[11, 137101, 670]], "level": 331, "attributes": [[1, 4230300], [2, 42303000], [3, 2115150], [4, 2115150]], "nextLevel": 332, "id": 76006}, {"cost": [[11, 137101, 670]], "level": 332, "attributes": [[1, 4253100], [2, 42531000], [3, 2126550], [4, 2126550]], "nextLevel": 333, "id": 76006}, {"cost": [[11, 137101, 670]], "level": 333, "attributes": [[1, 4275900], [2, 42759000], [3, 2137950], [4, 2137950]], "nextLevel": 334, "id": 76006}, {"cost": [[11, 137101, 670]], "level": 334, "attributes": [[1, 4298700], [2, 42987000], [3, 2149350], [4, 2149350]], "nextLevel": 335, "id": 76006}, {"cost": [[11, 137101, 670]], "level": 335, "attributes": [[1, 4321500], [2, 43215000], [3, 2160750], [4, 2160750]], "nextLevel": 336, "id": 76006}, {"cost": [[11, 137101, 680]], "level": 336, "attributes": [[1, 4344600], [2, 43446000], [3, 2172300], [4, 2172300]], "nextLevel": 337, "id": 76006}, {"cost": [[11, 137101, 680]], "level": 337, "attributes": [[1, 4367700], [2, 43677000], [3, 2183850], [4, 2183850]], "nextLevel": 338, "id": 76006}, {"cost": [[11, 137101, 680]], "level": 338, "attributes": [[1, 4390800], [2, 43908000], [3, 2195400], [4, 2195400]], "nextLevel": 339, "id": 76006}, {"cost": [[11, 137101, 680]], "level": 339, "attributes": [[1, 4413900], [2, 44139000], [3, 2206950], [4, 2206950]], "nextLevel": 340, "id": 76006}, {"cost": [[11, 137101, 680]], "level": 340, "attributes": [[1, 4437000], [2, 44370000], [3, 2218500], [4, 2218500]], "nextLevel": 341, "id": 76006}, {"cost": [[11, 137101, 690]], "level": 341, "attributes": [[1, 4460400], [2, 44604000], [3, 2230200], [4, 2230200]], "nextLevel": 342, "id": 76006}, {"cost": [[11, 137101, 690]], "level": 342, "attributes": [[1, 4483800], [2, 44838000], [3, 2241900], [4, 2241900]], "nextLevel": 343, "id": 76006}, {"cost": [[11, 137101, 690]], "level": 343, "attributes": [[1, 4507200], [2, 45072000], [3, 2253600], [4, 2253600]], "nextLevel": 344, "id": 76006}, {"cost": [[11, 137101, 690]], "level": 344, "attributes": [[1, 4530600], [2, 45306000], [3, 2265300], [4, 2265300]], "nextLevel": 345, "id": 76006}, {"cost": [[11, 137101, 690]], "level": 345, "attributes": [[1, 4554000], [2, 45540000], [3, 2277000], [4, 2277000]], "nextLevel": 346, "id": 76006}, {"cost": [[11, 137101, 700]], "level": 346, "attributes": [[1, 4577700], [2, 45777000], [3, 2288850], [4, 2288850]], "nextLevel": 347, "id": 76006}, {"cost": [[11, 137101, 700]], "level": 347, "attributes": [[1, 4601400], [2, 46014000], [3, 2300700], [4, 2300700]], "nextLevel": 348, "id": 76006}, {"cost": [[11, 137101, 700]], "level": 348, "attributes": [[1, 4625100], [2, 46251000], [3, 2312550], [4, 2312550]], "nextLevel": 349, "id": 76006}, {"cost": [[11, 137101, 700]], "level": 349, "attributes": [[1, 4648800], [2, 46488000], [3, 2324400], [4, 2324400]], "nextLevel": 350, "id": 76006}, {"cost": [[11, 137101, 700]], "level": 350, "attributes": [[1, 4672500], [2, 46725000], [3, 2336250], [4, 2336250]], "nextLevel": 351, "id": 76006}, {"cost": [[11, 137101, 710]], "level": 351, "attributes": [[1, 4696500], [2, 46965000], [3, 2348250], [4, 2348250]], "nextLevel": 352, "id": 76006}, {"cost": [[11, 137101, 710]], "level": 352, "attributes": [[1, 4720500], [2, 47205000], [3, 2360250], [4, 2360250]], "nextLevel": 353, "id": 76006}, {"cost": [[11, 137101, 710]], "level": 353, "attributes": [[1, 4744500], [2, 47445000], [3, 2372250], [4, 2372250]], "nextLevel": 354, "id": 76006}, {"cost": [[11, 137101, 710]], "level": 354, "attributes": [[1, 4768500], [2, 47685000], [3, 2384250], [4, 2384250]], "nextLevel": 355, "id": 76006}, {"cost": [[11, 137101, 710]], "level": 355, "attributes": [[1, 4792500], [2, 47925000], [3, 2396250], [4, 2396250]], "nextLevel": 356, "id": 76006}, {"cost": [[11, 137101, 720]], "level": 356, "attributes": [[1, 4816800], [2, 48168000], [3, 2408400], [4, 2408400]], "nextLevel": 357, "id": 76006}, {"cost": [[11, 137101, 720]], "level": 357, "attributes": [[1, 4841100], [2, 48411000], [3, 2420550], [4, 2420550]], "nextLevel": 358, "id": 76006}, {"cost": [[11, 137101, 720]], "level": 358, "attributes": [[1, 4865400], [2, 48654000], [3, 2432700], [4, 2432700]], "nextLevel": 359, "id": 76006}, {"cost": [[11, 137101, 720]], "level": 359, "attributes": [[1, 4889700], [2, 48897000], [3, 2444850], [4, 2444850]], "nextLevel": 360, "id": 76006}, {"cost": [[11, 137101, 720]], "level": 360, "attributes": [[1, 4914000], [2, 49140000], [3, 2457000], [4, 2457000]], "nextLevel": 361, "id": 76006}, {"cost": [[11, 137101, 730]], "level": 361, "attributes": [[1, 4938600], [2, 49386000], [3, 2469300], [4, 2469300]], "nextLevel": 362, "id": 76006}, {"cost": [[11, 137101, 730]], "level": 362, "attributes": [[1, 4963200], [2, 49632000], [3, 2481600], [4, 2481600]], "nextLevel": 363, "id": 76006}, {"cost": [[11, 137101, 730]], "level": 363, "attributes": [[1, 4987800], [2, 49878000], [3, 2493900], [4, 2493900]], "nextLevel": 364, "id": 76006}, {"cost": [[11, 137101, 730]], "level": 364, "attributes": [[1, 5012400], [2, 50124000], [3, 2506200], [4, 2506200]], "nextLevel": 365, "id": 76006}, {"cost": [[11, 137101, 730]], "level": 365, "attributes": [[1, 5037000], [2, 50370000], [3, 2518500], [4, 2518500]], "nextLevel": 366, "id": 76006}, {"cost": [[11, 137101, 740]], "level": 366, "attributes": [[1, 5061900], [2, 50619000], [3, 2530950], [4, 2530950]], "nextLevel": 367, "id": 76006}, {"cost": [[11, 137101, 740]], "level": 367, "attributes": [[1, 5086800], [2, 50868000], [3, 2543400], [4, 2543400]], "nextLevel": 368, "id": 76006}, {"cost": [[11, 137101, 740]], "level": 368, "attributes": [[1, 5111700], [2, 51117000], [3, 2555850], [4, 2555850]], "nextLevel": 369, "id": 76006}, {"cost": [[11, 137101, 740]], "level": 369, "attributes": [[1, 5136600], [2, 51366000], [3, 2568300], [4, 2568300]], "nextLevel": 370, "id": 76006}, {"cost": [[11, 137101, 740]], "level": 370, "attributes": [[1, 5161500], [2, 51615000], [3, 2580750], [4, 2580750]], "nextLevel": 371, "id": 76006}, {"cost": [[11, 137101, 750]], "level": 371, "attributes": [[1, 5186700], [2, 51867000], [3, 2593350], [4, 2593350]], "nextLevel": 372, "id": 76006}, {"cost": [[11, 137101, 750]], "level": 372, "attributes": [[1, 5211900], [2, 52119000], [3, 2605950], [4, 2605950]], "nextLevel": 373, "id": 76006}, {"cost": [[11, 137101, 750]], "level": 373, "attributes": [[1, 5237100], [2, 52371000], [3, 2618550], [4, 2618550]], "nextLevel": 374, "id": 76006}, {"cost": [[11, 137101, 750]], "level": 374, "attributes": [[1, 5262300], [2, 52623000], [3, 2631150], [4, 2631150]], "nextLevel": 375, "id": 76006}, {"cost": [[11, 137101, 750]], "level": 375, "attributes": [[1, 5287500], [2, 52875000], [3, 2643750], [4, 2643750]], "nextLevel": 376, "id": 76006}, {"cost": [[11, 137101, 760]], "level": 376, "attributes": [[1, 5313000], [2, 53130000], [3, 2656500], [4, 2656500]], "nextLevel": 377, "id": 76006}, {"cost": [[11, 137101, 760]], "level": 377, "attributes": [[1, 5338500], [2, 53385000], [3, 2669250], [4, 2669250]], "nextLevel": 378, "id": 76006}, {"cost": [[11, 137101, 760]], "level": 378, "attributes": [[1, 5364000], [2, 53640000], [3, 2682000], [4, 2682000]], "nextLevel": 379, "id": 76006}, {"cost": [[11, 137101, 760]], "level": 379, "attributes": [[1, 5389500], [2, 53895000], [3, 2694750], [4, 2694750]], "nextLevel": 380, "id": 76006}, {"cost": [[11, 137101, 760]], "level": 380, "attributes": [[1, 5415000], [2, 54150000], [3, 2707500], [4, 2707500]], "nextLevel": 381, "id": 76006}, {"cost": [[11, 137101, 770]], "level": 381, "attributes": [[1, 5440800], [2, 54408000], [3, 2720400], [4, 2720400]], "nextLevel": 382, "id": 76006}, {"cost": [[11, 137101, 770]], "level": 382, "attributes": [[1, 5466600], [2, 54666000], [3, 2733300], [4, 2733300]], "nextLevel": 383, "id": 76006}, {"cost": [[11, 137101, 770]], "level": 383, "attributes": [[1, 5492400], [2, 54924000], [3, 2746200], [4, 2746200]], "nextLevel": 384, "id": 76006}, {"cost": [[11, 137101, 770]], "level": 384, "attributes": [[1, 5518200], [2, 55182000], [3, 2759100], [4, 2759100]], "nextLevel": 385, "id": 76006}, {"cost": [[11, 137101, 770]], "level": 385, "attributes": [[1, 5544000], [2, 55440000], [3, 2772000], [4, 2772000]], "nextLevel": 386, "id": 76006}, {"cost": [[11, 137101, 780]], "level": 386, "attributes": [[1, 5570100], [2, 55701000], [3, 2785050], [4, 2785050]], "nextLevel": 387, "id": 76006}, {"cost": [[11, 137101, 780]], "level": 387, "attributes": [[1, 5596200], [2, 55962000], [3, 2798100], [4, 2798100]], "nextLevel": 388, "id": 76006}, {"cost": [[11, 137101, 780]], "level": 388, "attributes": [[1, 5622300], [2, 56223000], [3, 2811150], [4, 2811150]], "nextLevel": 389, "id": 76006}, {"cost": [[11, 137101, 780]], "level": 389, "attributes": [[1, 5648400], [2, 56484000], [3, 2824200], [4, 2824200]], "nextLevel": 390, "id": 76006}, {"cost": [[11, 137101, 780]], "level": 390, "attributes": [[1, 5674500], [2, 56745000], [3, 2837250], [4, 2837250]], "nextLevel": 391, "id": 76006}, {"cost": [[11, 137101, 790]], "level": 391, "attributes": [[1, 5700900], [2, 57009000], [3, 2850450], [4, 2850450]], "nextLevel": 392, "id": 76006}, {"cost": [[11, 137101, 790]], "level": 392, "attributes": [[1, 5727300], [2, 57273000], [3, 2863650], [4, 2863650]], "nextLevel": 393, "id": 76006}, {"cost": [[11, 137101, 790]], "level": 393, "attributes": [[1, 5753700], [2, 57537000], [3, 2876850], [4, 2876850]], "nextLevel": 394, "id": 76006}, {"cost": [[11, 137101, 790]], "level": 394, "attributes": [[1, 5780100], [2, 57801000], [3, 2890050], [4, 2890050]], "nextLevel": 395, "id": 76006}, {"cost": [[11, 137101, 790]], "level": 395, "attributes": [[1, 5806500], [2, 58065000], [3, 2903250], [4, 2903250]], "nextLevel": 396, "id": 76006}, {"cost": [[11, 137101, 800]], "level": 396, "attributes": [[1, 5833200], [2, 58332000], [3, 2916600], [4, 2916600]], "nextLevel": 397, "id": 76006}, {"cost": [[11, 137101, 800]], "level": 397, "attributes": [[1, 5859900], [2, 58599000], [3, 2929950], [4, 2929950]], "nextLevel": 398, "id": 76006}, {"cost": [[11, 137101, 800]], "level": 398, "attributes": [[1, 5886600], [2, 58866000], [3, 2943300], [4, 2943300]], "nextLevel": 399, "id": 76006}, {"cost": [[11, 137101, 800]], "level": 399, "attributes": [[1, 5913300], [2, 59133000], [3, 2956650], [4, 2956650]], "nextLevel": 400, "id": 76006}, {"cost": [[11, 137101, 800]], "level": 400, "attributes": [[1, 5940000], [2, 59400000], [3, 2970000], [4, 2970000]], "nextLevel": 401, "id": 76006}, {"cost": [[11, 137101, 810]], "level": 401, "attributes": [[1, 5967000], [2, 59670000], [3, 2983500], [4, 2983500]], "nextLevel": 402, "id": 76006}, {"cost": [[11, 137101, 810]], "level": 402, "attributes": [[1, 5994000], [2, 59940000], [3, 2997000], [4, 2997000]], "nextLevel": 403, "id": 76006}, {"cost": [[11, 137101, 810]], "level": 403, "attributes": [[1, 6021000], [2, 60210000], [3, 3010500], [4, 3010500]], "nextLevel": 404, "id": 76006}, {"cost": [[11, 137101, 810]], "level": 404, "attributes": [[1, 6048000], [2, 60480000], [3, 3024000], [4, 3024000]], "nextLevel": 405, "id": 76006}, {"cost": [[11, 137101, 810]], "level": 405, "attributes": [[1, 6075000], [2, 60750000], [3, 3037500], [4, 3037500]], "nextLevel": 406, "id": 76006}, {"cost": [[11, 137101, 820]], "level": 406, "attributes": [[1, 6102300], [2, 61023000], [3, 3051150], [4, 3051150]], "nextLevel": 407, "id": 76006}, {"cost": [[11, 137101, 820]], "level": 407, "attributes": [[1, 6129600], [2, 61296000], [3, 3064800], [4, 3064800]], "nextLevel": 408, "id": 76006}, {"cost": [[11, 137101, 820]], "level": 408, "attributes": [[1, 6156900], [2, 61569000], [3, 3078450], [4, 3078450]], "nextLevel": 409, "id": 76006}, {"cost": [[11, 137101, 820]], "level": 409, "attributes": [[1, 6184200], [2, 61842000], [3, 3092100], [4, 3092100]], "nextLevel": 410, "id": 76006}, {"cost": [[11, 137101, 820]], "level": 410, "attributes": [[1, 6211500], [2, 62115000], [3, 3105750], [4, 3105750]], "nextLevel": 411, "id": 76006}, {"cost": [[11, 137101, 830]], "level": 411, "attributes": [[1, 6239100], [2, 62391000], [3, 3119550], [4, 3119550]], "nextLevel": 412, "id": 76006}, {"cost": [[11, 137101, 830]], "level": 412, "attributes": [[1, 6266700], [2, 62667000], [3, 3133350], [4, 3133350]], "nextLevel": 413, "id": 76006}, {"cost": [[11, 137101, 830]], "level": 413, "attributes": [[1, 6294300], [2, 62943000], [3, 3147150], [4, 3147150]], "nextLevel": 414, "id": 76006}, {"cost": [[11, 137101, 830]], "level": 414, "attributes": [[1, 6321900], [2, 63219000], [3, 3160950], [4, 3160950]], "nextLevel": 415, "id": 76006}, {"cost": [[11, 137101, 830]], "level": 415, "attributes": [[1, 6349500], [2, 63495000], [3, 3174750], [4, 3174750]], "nextLevel": 416, "id": 76006}, {"cost": [[11, 137101, 840]], "level": 416, "attributes": [[1, 6377400], [2, 63774000], [3, 3188700], [4, 3188700]], "nextLevel": 417, "id": 76006}, {"cost": [[11, 137101, 840]], "level": 417, "attributes": [[1, 6405300], [2, 64053000], [3, 3202650], [4, 3202650]], "nextLevel": 418, "id": 76006}, {"cost": [[11, 137101, 840]], "level": 418, "attributes": [[1, 6433200], [2, 64332000], [3, 3216600], [4, 3216600]], "nextLevel": 419, "id": 76006}, {"cost": [[11, 137101, 840]], "level": 419, "attributes": [[1, 6461100], [2, 64611000], [3, 3230550], [4, 3230550]], "nextLevel": 420, "id": 76006}, {"cost": [[11, 137101, 840]], "level": 420, "attributes": [[1, 6489000], [2, 64890000], [3, 3244500], [4, 3244500]], "nextLevel": 421, "id": 76006}, {"cost": [[11, 137101, 850]], "level": 421, "attributes": [[1, 6517200], [2, 65172000], [3, 3258600], [4, 3258600]], "nextLevel": 422, "id": 76006}, {"cost": [[11, 137101, 850]], "level": 422, "attributes": [[1, 6545400], [2, 65454000], [3, 3272700], [4, 3272700]], "nextLevel": 423, "id": 76006}, {"cost": [[11, 137101, 850]], "level": 423, "attributes": [[1, 6573600], [2, 65736000], [3, 3286800], [4, 3286800]], "nextLevel": 424, "id": 76006}, {"cost": [[11, 137101, 850]], "level": 424, "attributes": [[1, 6601800], [2, 66018000], [3, 3300900], [4, 3300900]], "nextLevel": 425, "id": 76006}, {"cost": [[11, 137101, 850]], "level": 425, "attributes": [[1, 6630000], [2, 66300000], [3, 3315000], [4, 3315000]], "nextLevel": 426, "id": 76006}, {"cost": [[11, 137101, 860]], "level": 426, "attributes": [[1, 6658500], [2, 66585000], [3, 3329250], [4, 3329250]], "nextLevel": 427, "id": 76006}, {"cost": [[11, 137101, 860]], "level": 427, "attributes": [[1, 6687000], [2, 66870000], [3, 3343500], [4, 3343500]], "nextLevel": 428, "id": 76006}, {"cost": [[11, 137101, 860]], "level": 428, "attributes": [[1, 6715500], [2, 67155000], [3, 3357750], [4, 3357750]], "nextLevel": 429, "id": 76006}, {"cost": [[11, 137101, 860]], "level": 429, "attributes": [[1, 6744000], [2, 67440000], [3, 3372000], [4, 3372000]], "nextLevel": 430, "id": 76006}, {"cost": [[11, 137101, 860]], "level": 430, "attributes": [[1, 6772500], [2, 67725000], [3, 3386250], [4, 3386250]], "nextLevel": 431, "id": 76006}, {"cost": [[11, 137101, 870]], "level": 431, "attributes": [[1, 6801300], [2, 68013000], [3, 3400650], [4, 3400650]], "nextLevel": 432, "id": 76006}, {"cost": [[11, 137101, 870]], "level": 432, "attributes": [[1, 6830100], [2, 68301000], [3, 3415050], [4, 3415050]], "nextLevel": 433, "id": 76006}, {"cost": [[11, 137101, 870]], "level": 433, "attributes": [[1, 6858900], [2, 68589000], [3, 3429450], [4, 3429450]], "nextLevel": 434, "id": 76006}, {"cost": [[11, 137101, 870]], "level": 434, "attributes": [[1, 6887700], [2, 68877000], [3, 3443850], [4, 3443850]], "nextLevel": 435, "id": 76006}, {"cost": [[11, 137101, 870]], "level": 435, "attributes": [[1, 6916500], [2, 69165000], [3, 3458250], [4, 3458250]], "nextLevel": 436, "id": 76006}, {"cost": [[11, 137101, 880]], "level": 436, "attributes": [[1, 6945600], [2, 69456000], [3, 3472800], [4, 3472800]], "nextLevel": 437, "id": 76006}, {"cost": [[11, 137101, 880]], "level": 437, "attributes": [[1, 6974700], [2, 69747000], [3, 3487350], [4, 3487350]], "nextLevel": 438, "id": 76006}, {"cost": [[11, 137101, 880]], "level": 438, "attributes": [[1, 7003800], [2, 70038000], [3, 3501900], [4, 3501900]], "nextLevel": 439, "id": 76006}, {"cost": [[11, 137101, 880]], "level": 439, "attributes": [[1, 7032900], [2, 70329000], [3, 3516450], [4, 3516450]], "nextLevel": 440, "id": 76006}, {"cost": [[11, 137101, 880]], "level": 440, "attributes": [[1, 7062000], [2, 70620000], [3, 3531000], [4, 3531000]], "nextLevel": 441, "id": 76006}, {"cost": [[11, 137101, 890]], "level": 441, "attributes": [[1, 7091400], [2, 70914000], [3, 3545700], [4, 3545700]], "nextLevel": 442, "id": 76006}, {"cost": [[11, 137101, 890]], "level": 442, "attributes": [[1, 7120800], [2, 71208000], [3, 3560400], [4, 3560400]], "nextLevel": 443, "id": 76006}, {"cost": [[11, 137101, 890]], "level": 443, "attributes": [[1, 7150200], [2, 71502000], [3, 3575100], [4, 3575100]], "nextLevel": 444, "id": 76006}, {"cost": [[11, 137101, 890]], "level": 444, "attributes": [[1, 7179600], [2, 71796000], [3, 3589800], [4, 3589800]], "nextLevel": 445, "id": 76006}, {"cost": [[11, 137101, 890]], "level": 445, "attributes": [[1, 7209000], [2, 72090000], [3, 3604500], [4, 3604500]], "nextLevel": 446, "id": 76006}, {"cost": [[11, 137101, 900]], "level": 446, "attributes": [[1, 7238700], [2, 72387000], [3, 3619350], [4, 3619350]], "nextLevel": 447, "id": 76006}, {"cost": [[11, 137101, 900]], "level": 447, "attributes": [[1, 7268400], [2, 72684000], [3, 3634200], [4, 3634200]], "nextLevel": 448, "id": 76006}, {"cost": [[11, 137101, 900]], "level": 448, "attributes": [[1, 7298100], [2, 72981000], [3, 3649050], [4, 3649050]], "nextLevel": 449, "id": 76006}, {"cost": [[11, 137101, 900]], "level": 449, "attributes": [[1, 7327800], [2, 73278000], [3, 3663900], [4, 3663900]], "nextLevel": 450, "id": 76006}, {"cost": [[11, 137101, 900]], "level": 450, "attributes": [[1, 7357500], [2, 73575000], [3, 3678750], [4, 3678750]], "nextLevel": 451, "id": 76006}, {"cost": [[11, 137101, 910]], "level": 451, "attributes": [[1, 7387500], [2, 73875000], [3, 3693750], [4, 3693750]], "nextLevel": 452, "id": 76006}, {"cost": [[11, 137101, 910]], "level": 452, "attributes": [[1, 7417500], [2, 74175000], [3, 3708750], [4, 3708750]], "nextLevel": 453, "id": 76006}, {"cost": [[11, 137101, 910]], "level": 453, "attributes": [[1, 7447500], [2, 74475000], [3, 3723750], [4, 3723750]], "nextLevel": 454, "id": 76006}, {"cost": [[11, 137101, 910]], "level": 454, "attributes": [[1, 7477500], [2, 74775000], [3, 3738750], [4, 3738750]], "nextLevel": 455, "id": 76006}, {"cost": [[11, 137101, 910]], "level": 455, "attributes": [[1, 7507500], [2, 75075000], [3, 3753750], [4, 3753750]], "nextLevel": 456, "id": 76006}, {"cost": [[11, 137101, 920]], "level": 456, "attributes": [[1, 7537800], [2, 75378000], [3, 3768900], [4, 3768900]], "nextLevel": 457, "id": 76006}, {"cost": [[11, 137101, 920]], "level": 457, "attributes": [[1, 7568100], [2, 75681000], [3, 3784050], [4, 3784050]], "nextLevel": 458, "id": 76006}, {"cost": [[11, 137101, 920]], "level": 458, "attributes": [[1, 7598400], [2, 75984000], [3, 3799200], [4, 3799200]], "nextLevel": 459, "id": 76006}, {"cost": [[11, 137101, 920]], "level": 459, "attributes": [[1, 7628700], [2, 76287000], [3, 3814350], [4, 3814350]], "nextLevel": 460, "id": 76006}, {"cost": [[11, 137101, 920]], "level": 460, "attributes": [[1, 7659000], [2, 76590000], [3, 3829500], [4, 3829500]], "nextLevel": 461, "id": 76006}, {"cost": [[11, 137101, 930]], "level": 461, "attributes": [[1, 7689600], [2, 76896000], [3, 3844800], [4, 3844800]], "nextLevel": 462, "id": 76006}, {"cost": [[11, 137101, 930]], "level": 462, "attributes": [[1, 7720200], [2, 77202000], [3, 3860100], [4, 3860100]], "nextLevel": 463, "id": 76006}, {"cost": [[11, 137101, 930]], "level": 463, "attributes": [[1, 7750800], [2, 77508000], [3, 3875400], [4, 3875400]], "nextLevel": 464, "id": 76006}, {"cost": [[11, 137101, 930]], "level": 464, "attributes": [[1, 7781400], [2, 77814000], [3, 3890700], [4, 3890700]], "nextLevel": 465, "id": 76006}, {"cost": [[11, 137101, 930]], "level": 465, "attributes": [[1, 7812000], [2, 78120000], [3, 3906000], [4, 3906000]], "nextLevel": 466, "id": 76006}, {"cost": [[11, 137101, 940]], "level": 466, "attributes": [[1, 7842900], [2, 78429000], [3, 3921450], [4, 3921450]], "nextLevel": 467, "id": 76006}, {"cost": [[11, 137101, 940]], "level": 467, "attributes": [[1, 7873800], [2, 78738000], [3, 3936900], [4, 3936900]], "nextLevel": 468, "id": 76006}, {"cost": [[11, 137101, 940]], "level": 468, "attributes": [[1, 7904700], [2, 79047000], [3, 3952350], [4, 3952350]], "nextLevel": 469, "id": 76006}, {"cost": [[11, 137101, 940]], "level": 469, "attributes": [[1, 7935600], [2, 79356000], [3, 3967800], [4, 3967800]], "nextLevel": 470, "id": 76006}, {"cost": [[11, 137101, 940]], "level": 470, "attributes": [[1, 7966500], [2, 79665000], [3, 3983250], [4, 3983250]], "nextLevel": 471, "id": 76006}, {"cost": [[11, 137101, 950]], "level": 471, "attributes": [[1, 7997700], [2, 79977000], [3, 3998850], [4, 3998850]], "nextLevel": 472, "id": 76006}, {"cost": [[11, 137101, 950]], "level": 472, "attributes": [[1, 8028900], [2, 80289000], [3, 4014450], [4, 4014450]], "nextLevel": 473, "id": 76006}, {"cost": [[11, 137101, 950]], "level": 473, "attributes": [[1, 8060100], [2, 80601000], [3, 4030050], [4, 4030050]], "nextLevel": 474, "id": 76006}, {"cost": [[11, 137101, 950]], "level": 474, "attributes": [[1, 8091300], [2, 80913000], [3, 4045650], [4, 4045650]], "nextLevel": 475, "id": 76006}, {"cost": [[11, 137101, 950]], "level": 475, "attributes": [[1, 8122500], [2, 81225000], [3, 4061250], [4, 4061250]], "nextLevel": 476, "id": 76006}, {"cost": [[11, 137101, 960]], "level": 476, "attributes": [[1, 8154000], [2, 81540000], [3, 4077000], [4, 4077000]], "nextLevel": 477, "id": 76006}, {"cost": [[11, 137101, 960]], "level": 477, "attributes": [[1, 8185500], [2, 81855000], [3, 4092750], [4, 4092750]], "nextLevel": 478, "id": 76006}, {"cost": [[11, 137101, 960]], "level": 478, "attributes": [[1, 8217000], [2, 82170000], [3, 4108500], [4, 4108500]], "nextLevel": 479, "id": 76006}, {"cost": [[11, 137101, 960]], "level": 479, "attributes": [[1, 8248500], [2, 82485000], [3, 4124250], [4, 4124250]], "nextLevel": 480, "id": 76006}, {"cost": [[11, 137101, 960]], "level": 480, "attributes": [[1, 8280000], [2, 82800000], [3, 4140000], [4, 4140000]], "nextLevel": 481, "id": 76006}, {"cost": [[11, 137101, 970]], "level": 481, "attributes": [[1, 8311800], [2, 83118000], [3, 4155900], [4, 4155900]], "nextLevel": 482, "id": 76006}, {"cost": [[11, 137101, 970]], "level": 482, "attributes": [[1, 8343600], [2, 83436000], [3, 4171800], [4, 4171800]], "nextLevel": 483, "id": 76006}, {"cost": [[11, 137101, 970]], "level": 483, "attributes": [[1, 8375400], [2, 83754000], [3, 4187700], [4, 4187700]], "nextLevel": 484, "id": 76006}, {"cost": [[11, 137101, 970]], "level": 484, "attributes": [[1, 8407200], [2, 84072000], [3, 4203600], [4, 4203600]], "nextLevel": 485, "id": 76006}, {"cost": [[11, 137101, 970]], "level": 485, "attributes": [[1, 8439000], [2, 84390000], [3, 4219500], [4, 4219500]], "nextLevel": 486, "id": 76006}, {"cost": [[11, 137101, 980]], "level": 486, "attributes": [[1, 8471100], [2, 84711000], [3, 4235550], [4, 4235550]], "nextLevel": 487, "id": 76006}, {"cost": [[11, 137101, 980]], "level": 487, "attributes": [[1, 8503200], [2, 85032000], [3, 4251600], [4, 4251600]], "nextLevel": 488, "id": 76006}, {"cost": [[11, 137101, 980]], "level": 488, "attributes": [[1, 8535300], [2, 85353000], [3, 4267650], [4, 4267650]], "nextLevel": 489, "id": 76006}, {"cost": [[11, 137101, 980]], "level": 489, "attributes": [[1, 8567400], [2, 85674000], [3, 4283700], [4, 4283700]], "nextLevel": 490, "id": 76006}, {"cost": [[11, 137101, 980]], "level": 490, "attributes": [[1, 8599500], [2, 85995000], [3, 4299750], [4, 4299750]], "nextLevel": 491, "id": 76006}, {"cost": [[11, 137101, 990]], "level": 491, "attributes": [[1, 8631900], [2, 86319000], [3, 4315950], [4, 4315950]], "nextLevel": 492, "id": 76006}, {"cost": [[11, 137101, 990]], "level": 492, "attributes": [[1, 8664300], [2, 86643000], [3, 4332150], [4, 4332150]], "nextLevel": 493, "id": 76006}, {"cost": [[11, 137101, 990]], "level": 493, "attributes": [[1, 8696700], [2, 86967000], [3, 4348350], [4, 4348350]], "nextLevel": 494, "id": 76006}, {"cost": [[11, 137101, 990]], "level": 494, "attributes": [[1, 8729100], [2, 87291000], [3, 4364550], [4, 4364550]], "nextLevel": 495, "id": 76006}, {"cost": [[11, 137101, 990]], "level": 495, "attributes": [[1, 8761500], [2, 87615000], [3, 4380750], [4, 4380750]], "nextLevel": 496, "id": 76006}, {"cost": [[11, 137101, 1000]], "level": 496, "attributes": [[1, 8794200], [2, 87942000], [3, 4397100], [4, 4397100]], "nextLevel": 497, "id": 76006}, {"cost": [[11, 137101, 1000]], "level": 497, "attributes": [[1, 8826900], [2, 88269000], [3, 4413450], [4, 4413450]], "nextLevel": 498, "id": 76006}, {"cost": [[11, 137101, 1000]], "level": 498, "attributes": [[1, 8859600], [2, 88596000], [3, 4429800], [4, 4429800]], "nextLevel": 499, "id": 76006}, {"cost": [[11, 137101, 1000]], "level": 499, "attributes": [[1, 8892300], [2, 88923000], [3, 4446150], [4, 4446150]], "nextLevel": 500, "id": 76006}, {"cost": [], "level": 500, "attributes": [[1, 8925000], [2, 89250000], [3, 4462500], [4, 4462500]], "nextLevel": 0, "id": 76006}]