[{"score": 187, "stage": 1, "type": 1, "value": 5000, "quality": 1, "camp": 1}, {"score": 375, "stage": 1, "type": 1, "value": 10000, "quality": 2, "camp": 1}, {"score": 562, "stage": 1, "type": 1, "value": 15000, "quality": 3, "camp": 1}, {"score": 750, "stage": 1, "type": 1, "value": 20000, "quality": 4, "camp": 1}, {"score": 937, "stage": 1, "type": 1, "value": 25000, "quality": 5, "camp": 1}, {"score": 187, "stage": 1, "type": 2, "value": 50000, "quality": 1, "camp": 1}, {"score": 375, "stage": 1, "type": 2, "value": 100000, "quality": 2, "camp": 1}, {"score": 562, "stage": 1, "type": 2, "value": 150000, "quality": 3, "camp": 1}, {"score": 750, "stage": 1, "type": 2, "value": 200000, "quality": 4, "camp": 1}, {"score": 937, "stage": 1, "type": 2, "value": 250000, "quality": 5, "camp": 1}, {"score": 187, "stage": 1, "type": 3, "value": 2500, "quality": 1, "camp": 1}, {"score": 375, "stage": 1, "type": 3, "value": 5000, "quality": 2, "camp": 1}, {"score": 562, "stage": 1, "type": 3, "value": 7500, "quality": 3, "camp": 1}, {"score": 750, "stage": 1, "type": 3, "value": 10000, "quality": 4, "camp": 1}, {"score": 937, "stage": 1, "type": 3, "value": 12500, "quality": 5, "camp": 1}, {"score": 187, "stage": 1, "type": 4, "value": 2500, "quality": 1, "camp": 1}, {"score": 375, "stage": 1, "type": 4, "value": 5000, "quality": 2, "camp": 1}, {"score": 562, "stage": 1, "type": 4, "value": 7500, "quality": 3, "camp": 1}, {"score": 750, "stage": 1, "type": 4, "value": 10000, "quality": 4, "camp": 1}, {"score": 937, "stage": 1, "type": 4, "value": 12500, "quality": 5, "camp": 1}, {"score": 187, "stage": 1, "type": 5, "value": 5000, "quality": 1, "camp": 2}, {"score": 375, "stage": 1, "type": 5, "value": 10000, "quality": 2, "camp": 2}, {"score": 562, "stage": 1, "type": 5, "value": 15000, "quality": 3, "camp": 2}, {"score": 750, "stage": 1, "type": 5, "value": 20000, "quality": 4, "camp": 2}, {"score": 937, "stage": 1, "type": 5, "value": 25000, "quality": 5, "camp": 2}, {"score": 187, "stage": 1, "type": 6, "value": 50000, "quality": 1, "camp": 2}, {"score": 375, "stage": 1, "type": 6, "value": 100000, "quality": 2, "camp": 2}, {"score": 562, "stage": 1, "type": 6, "value": 150000, "quality": 3, "camp": 2}, {"score": 750, "stage": 1, "type": 6, "value": 200000, "quality": 4, "camp": 2}, {"score": 937, "stage": 1, "type": 6, "value": 250000, "quality": 5, "camp": 2}, {"score": 187, "stage": 1, "type": 7, "value": 2500, "quality": 1, "camp": 2}, {"score": 375, "stage": 1, "type": 7, "value": 5000, "quality": 2, "camp": 2}, {"score": 562, "stage": 1, "type": 7, "value": 7500, "quality": 3, "camp": 2}, {"score": 750, "stage": 1, "type": 7, "value": 10000, "quality": 4, "camp": 2}, {"score": 937, "stage": 1, "type": 7, "value": 12500, "quality": 5, "camp": 2}, {"score": 187, "stage": 1, "type": 8, "value": 2500, "quality": 1, "camp": 2}, {"score": 375, "stage": 1, "type": 8, "value": 5000, "quality": 2, "camp": 2}, {"score": 562, "stage": 1, "type": 8, "value": 7500, "quality": 3, "camp": 2}, {"score": 750, "stage": 1, "type": 8, "value": 10000, "quality": 4, "camp": 2}, {"score": 937, "stage": 1, "type": 8, "value": 12500, "quality": 5, "camp": 2}, {"score": 187, "stage": 1, "type": 9, "value": 5000, "quality": 1, "camp": 3}, {"score": 375, "stage": 1, "type": 9, "value": 10000, "quality": 2, "camp": 3}, {"score": 562, "stage": 1, "type": 9, "value": 15000, "quality": 3, "camp": 3}, {"score": 750, "stage": 1, "type": 9, "value": 20000, "quality": 4, "camp": 3}, {"score": 937, "stage": 1, "type": 9, "value": 25000, "quality": 5, "camp": 3}, {"score": 187, "stage": 1, "type": 10, "value": 50000, "quality": 1, "camp": 3}, {"score": 375, "stage": 1, "type": 10, "value": 100000, "quality": 2, "camp": 3}, {"score": 562, "stage": 1, "type": 10, "value": 150000, "quality": 3, "camp": 3}, {"score": 750, "stage": 1, "type": 10, "value": 200000, "quality": 4, "camp": 3}, {"score": 937, "stage": 1, "type": 10, "value": 250000, "quality": 5, "camp": 3}, {"score": 187, "stage": 1, "type": 11, "value": 2500, "quality": 1, "camp": 3}, {"score": 375, "stage": 1, "type": 11, "value": 5000, "quality": 2, "camp": 3}, {"score": 562, "stage": 1, "type": 11, "value": 7500, "quality": 3, "camp": 3}, {"score": 750, "stage": 1, "type": 11, "value": 10000, "quality": 4, "camp": 3}, {"score": 937, "stage": 1, "type": 11, "value": 12500, "quality": 5, "camp": 3}, {"score": 187, "stage": 1, "type": 12, "value": 2500, "quality": 1, "camp": 3}, {"score": 375, "stage": 1, "type": 12, "value": 5000, "quality": 2, "camp": 3}, {"score": 562, "stage": 1, "type": 12, "value": 7500, "quality": 3, "camp": 3}, {"score": 750, "stage": 1, "type": 12, "value": 10000, "quality": 4, "camp": 3}, {"score": 937, "stage": 1, "type": 12, "value": 12500, "quality": 5, "camp": 3}, {"score": 187, "stage": 1, "type": 13, "value": 5000, "quality": 1, "camp": 4}, {"score": 375, "stage": 1, "type": 13, "value": 10000, "quality": 2, "camp": 4}, {"score": 562, "stage": 1, "type": 13, "value": 15000, "quality": 3, "camp": 4}, {"score": 750, "stage": 1, "type": 13, "value": 20000, "quality": 4, "camp": 4}, {"score": 937, "stage": 1, "type": 13, "value": 25000, "quality": 5, "camp": 4}, {"score": 187, "stage": 1, "type": 14, "value": 50000, "quality": 1, "camp": 4}, {"score": 375, "stage": 1, "type": 14, "value": 100000, "quality": 2, "camp": 4}, {"score": 562, "stage": 1, "type": 14, "value": 150000, "quality": 3, "camp": 4}, {"score": 750, "stage": 1, "type": 14, "value": 200000, "quality": 4, "camp": 4}, {"score": 937, "stage": 1, "type": 14, "value": 250000, "quality": 5, "camp": 4}, {"score": 187, "stage": 1, "type": 15, "value": 2500, "quality": 1, "camp": 4}, {"score": 375, "stage": 1, "type": 15, "value": 5000, "quality": 2, "camp": 4}, {"score": 562, "stage": 1, "type": 15, "value": 7500, "quality": 3, "camp": 4}, {"score": 750, "stage": 1, "type": 15, "value": 10000, "quality": 4, "camp": 4}, {"score": 937, "stage": 1, "type": 15, "value": 12500, "quality": 5, "camp": 4}, {"score": 187, "stage": 1, "type": 16, "value": 2500, "quality": 1, "camp": 4}, {"score": 375, "stage": 1, "type": 16, "value": 5000, "quality": 2, "camp": 4}, {"score": 562, "stage": 1, "type": 16, "value": 7500, "quality": 3, "camp": 4}, {"score": 750, "stage": 1, "type": 16, "value": 10000, "quality": 4, "camp": 4}, {"score": 937, "stage": 1, "type": 16, "value": 12500, "quality": 5, "camp": 4}, {"score": 750, "stage": 1, "type": 17, "value": 5000, "quality": 1, "camp": 0}, {"score": 1500, "stage": 1, "type": 17, "value": 10000, "quality": 2, "camp": 0}, {"score": 2250, "stage": 1, "type": 17, "value": 15000, "quality": 3, "camp": 0}, {"score": 3000, "stage": 1, "type": 17, "value": 20000, "quality": 4, "camp": 0}, {"score": 3750, "stage": 1, "type": 17, "value": 25000, "quality": 5, "camp": 0}, {"score": 750, "stage": 1, "type": 18, "value": 50000, "quality": 1, "camp": 0}, {"score": 1500, "stage": 1, "type": 18, "value": 100000, "quality": 2, "camp": 0}, {"score": 2250, "stage": 1, "type": 18, "value": 150000, "quality": 3, "camp": 0}, {"score": 3000, "stage": 1, "type": 18, "value": 200000, "quality": 4, "camp": 0}, {"score": 3750, "stage": 1, "type": 18, "value": 250000, "quality": 5, "camp": 0}, {"score": 750, "stage": 1, "type": 19, "value": 2500, "quality": 1, "camp": 0}, {"score": 1500, "stage": 1, "type": 19, "value": 5000, "quality": 2, "camp": 0}, {"score": 2250, "stage": 1, "type": 19, "value": 7500, "quality": 3, "camp": 0}, {"score": 3000, "stage": 1, "type": 19, "value": 10000, "quality": 4, "camp": 0}, {"score": 3750, "stage": 1, "type": 19, "value": 12500, "quality": 5, "camp": 0}, {"score": 750, "stage": 1, "type": 20, "value": 2500, "quality": 1, "camp": 0}, {"score": 1500, "stage": 1, "type": 20, "value": 5000, "quality": 2, "camp": 0}, {"score": 2250, "stage": 1, "type": 20, "value": 7500, "quality": 3, "camp": 0}, {"score": 3000, "stage": 1, "type": 20, "value": 10000, "quality": 4, "camp": 0}, {"score": 3750, "stage": 1, "type": 20, "value": 12500, "quality": 5, "camp": 0}, {"score": 375, "stage": 2, "type": 1, "value": 10000, "quality": 1, "camp": 1}, {"score": 750, "stage": 2, "type": 1, "value": 20000, "quality": 2, "camp": 1}, {"score": 1125, "stage": 2, "type": 1, "value": 30000, "quality": 3, "camp": 1}, {"score": 1500, "stage": 2, "type": 1, "value": 40000, "quality": 4, "camp": 1}, {"score": 1875, "stage": 2, "type": 1, "value": 50000, "quality": 5, "camp": 1}, {"score": 375, "stage": 2, "type": 2, "value": 100000, "quality": 1, "camp": 1}, {"score": 750, "stage": 2, "type": 2, "value": 200000, "quality": 2, "camp": 1}, {"score": 1125, "stage": 2, "type": 2, "value": 300000, "quality": 3, "camp": 1}, {"score": 1500, "stage": 2, "type": 2, "value": 400000, "quality": 4, "camp": 1}, {"score": 1875, "stage": 2, "type": 2, "value": 500000, "quality": 5, "camp": 1}, {"score": 375, "stage": 2, "type": 3, "value": 5000, "quality": 1, "camp": 1}, {"score": 750, "stage": 2, "type": 3, "value": 10000, "quality": 2, "camp": 1}, {"score": 1125, "stage": 2, "type": 3, "value": 15000, "quality": 3, "camp": 1}, {"score": 1500, "stage": 2, "type": 3, "value": 20000, "quality": 4, "camp": 1}, {"score": 1875, "stage": 2, "type": 3, "value": 25000, "quality": 5, "camp": 1}, {"score": 375, "stage": 2, "type": 4, "value": 5000, "quality": 1, "camp": 1}, {"score": 750, "stage": 2, "type": 4, "value": 10000, "quality": 2, "camp": 1}, {"score": 1125, "stage": 2, "type": 4, "value": 15000, "quality": 3, "camp": 1}, {"score": 1500, "stage": 2, "type": 4, "value": 20000, "quality": 4, "camp": 1}, {"score": 1875, "stage": 2, "type": 4, "value": 25000, "quality": 5, "camp": 1}, {"score": 375, "stage": 2, "type": 5, "value": 10000, "quality": 1, "camp": 2}, {"score": 750, "stage": 2, "type": 5, "value": 20000, "quality": 2, "camp": 2}, {"score": 1125, "stage": 2, "type": 5, "value": 30000, "quality": 3, "camp": 2}, {"score": 1500, "stage": 2, "type": 5, "value": 40000, "quality": 4, "camp": 2}, {"score": 1875, "stage": 2, "type": 5, "value": 50000, "quality": 5, "camp": 2}, {"score": 375, "stage": 2, "type": 6, "value": 100000, "quality": 1, "camp": 2}, {"score": 750, "stage": 2, "type": 6, "value": 200000, "quality": 2, "camp": 2}, {"score": 1125, "stage": 2, "type": 6, "value": 300000, "quality": 3, "camp": 2}, {"score": 1500, "stage": 2, "type": 6, "value": 400000, "quality": 4, "camp": 2}, {"score": 1875, "stage": 2, "type": 6, "value": 500000, "quality": 5, "camp": 2}, {"score": 375, "stage": 2, "type": 7, "value": 5000, "quality": 1, "camp": 2}, {"score": 750, "stage": 2, "type": 7, "value": 10000, "quality": 2, "camp": 2}, {"score": 1125, "stage": 2, "type": 7, "value": 15000, "quality": 3, "camp": 2}, {"score": 1500, "stage": 2, "type": 7, "value": 20000, "quality": 4, "camp": 2}, {"score": 1875, "stage": 2, "type": 7, "value": 25000, "quality": 5, "camp": 2}, {"score": 375, "stage": 2, "type": 8, "value": 5000, "quality": 1, "camp": 2}, {"score": 750, "stage": 2, "type": 8, "value": 10000, "quality": 2, "camp": 2}, {"score": 1125, "stage": 2, "type": 8, "value": 15000, "quality": 3, "camp": 2}, {"score": 1500, "stage": 2, "type": 8, "value": 20000, "quality": 4, "camp": 2}, {"score": 1875, "stage": 2, "type": 8, "value": 25000, "quality": 5, "camp": 2}, {"score": 375, "stage": 2, "type": 9, "value": 10000, "quality": 1, "camp": 3}, {"score": 750, "stage": 2, "type": 9, "value": 20000, "quality": 2, "camp": 3}, {"score": 1125, "stage": 2, "type": 9, "value": 30000, "quality": 3, "camp": 3}, {"score": 1500, "stage": 2, "type": 9, "value": 40000, "quality": 4, "camp": 3}, {"score": 1875, "stage": 2, "type": 9, "value": 50000, "quality": 5, "camp": 3}, {"score": 375, "stage": 2, "type": 10, "value": 100000, "quality": 1, "camp": 3}, {"score": 750, "stage": 2, "type": 10, "value": 200000, "quality": 2, "camp": 3}, {"score": 1125, "stage": 2, "type": 10, "value": 300000, "quality": 3, "camp": 3}, {"score": 1500, "stage": 2, "type": 10, "value": 400000, "quality": 4, "camp": 3}, {"score": 1875, "stage": 2, "type": 10, "value": 500000, "quality": 5, "camp": 3}, {"score": 375, "stage": 2, "type": 11, "value": 5000, "quality": 1, "camp": 3}, {"score": 750, "stage": 2, "type": 11, "value": 10000, "quality": 2, "camp": 3}, {"score": 1125, "stage": 2, "type": 11, "value": 15000, "quality": 3, "camp": 3}, {"score": 1500, "stage": 2, "type": 11, "value": 20000, "quality": 4, "camp": 3}, {"score": 1875, "stage": 2, "type": 11, "value": 25000, "quality": 5, "camp": 3}, {"score": 375, "stage": 2, "type": 12, "value": 5000, "quality": 1, "camp": 3}, {"score": 750, "stage": 2, "type": 12, "value": 10000, "quality": 2, "camp": 3}, {"score": 1125, "stage": 2, "type": 12, "value": 15000, "quality": 3, "camp": 3}, {"score": 1500, "stage": 2, "type": 12, "value": 20000, "quality": 4, "camp": 3}, {"score": 1875, "stage": 2, "type": 12, "value": 25000, "quality": 5, "camp": 3}, {"score": 375, "stage": 2, "type": 13, "value": 10000, "quality": 1, "camp": 4}, {"score": 750, "stage": 2, "type": 13, "value": 20000, "quality": 2, "camp": 4}, {"score": 1125, "stage": 2, "type": 13, "value": 30000, "quality": 3, "camp": 4}, {"score": 1500, "stage": 2, "type": 13, "value": 40000, "quality": 4, "camp": 4}, {"score": 1875, "stage": 2, "type": 13, "value": 50000, "quality": 5, "camp": 4}, {"score": 375, "stage": 2, "type": 14, "value": 100000, "quality": 1, "camp": 4}, {"score": 750, "stage": 2, "type": 14, "value": 200000, "quality": 2, "camp": 4}, {"score": 1125, "stage": 2, "type": 14, "value": 300000, "quality": 3, "camp": 4}, {"score": 1500, "stage": 2, "type": 14, "value": 400000, "quality": 4, "camp": 4}, {"score": 1875, "stage": 2, "type": 14, "value": 500000, "quality": 5, "camp": 4}, {"score": 375, "stage": 2, "type": 15, "value": 5000, "quality": 1, "camp": 4}, {"score": 750, "stage": 2, "type": 15, "value": 10000, "quality": 2, "camp": 4}, {"score": 1125, "stage": 2, "type": 15, "value": 15000, "quality": 3, "camp": 4}, {"score": 1500, "stage": 2, "type": 15, "value": 20000, "quality": 4, "camp": 4}, {"score": 1875, "stage": 2, "type": 15, "value": 25000, "quality": 5, "camp": 4}, {"score": 375, "stage": 2, "type": 16, "value": 5000, "quality": 1, "camp": 4}, {"score": 750, "stage": 2, "type": 16, "value": 10000, "quality": 2, "camp": 4}, {"score": 1125, "stage": 2, "type": 16, "value": 15000, "quality": 3, "camp": 4}, {"score": 1500, "stage": 2, "type": 16, "value": 20000, "quality": 4, "camp": 4}, {"score": 1875, "stage": 2, "type": 16, "value": 25000, "quality": 5, "camp": 4}, {"score": 750, "stage": 2, "type": 17, "value": 5000, "quality": 1, "camp": 0}, {"score": 1500, "stage": 2, "type": 17, "value": 10000, "quality": 2, "camp": 0}, {"score": 2250, "stage": 2, "type": 17, "value": 15000, "quality": 3, "camp": 0}, {"score": 3000, "stage": 2, "type": 17, "value": 20000, "quality": 4, "camp": 0}, {"score": 3750, "stage": 2, "type": 17, "value": 25000, "quality": 5, "camp": 0}, {"score": 750, "stage": 2, "type": 18, "value": 50000, "quality": 1, "camp": 0}, {"score": 1500, "stage": 2, "type": 18, "value": 100000, "quality": 2, "camp": 0}, {"score": 2250, "stage": 2, "type": 18, "value": 150000, "quality": 3, "camp": 0}, {"score": 3000, "stage": 2, "type": 18, "value": 200000, "quality": 4, "camp": 0}, {"score": 3750, "stage": 2, "type": 18, "value": 250000, "quality": 5, "camp": 0}, {"score": 750, "stage": 2, "type": 19, "value": 2500, "quality": 1, "camp": 0}, {"score": 1500, "stage": 2, "type": 19, "value": 5000, "quality": 2, "camp": 0}, {"score": 2250, "stage": 2, "type": 19, "value": 7500, "quality": 3, "camp": 0}, {"score": 3000, "stage": 2, "type": 19, "value": 10000, "quality": 4, "camp": 0}, {"score": 3750, "stage": 2, "type": 19, "value": 12500, "quality": 5, "camp": 0}, {"score": 750, "stage": 2, "type": 20, "value": 2500, "quality": 1, "camp": 0}, {"score": 1500, "stage": 2, "type": 20, "value": 5000, "quality": 2, "camp": 0}, {"score": 2250, "stage": 2, "type": 20, "value": 7500, "quality": 3, "camp": 0}, {"score": 3000, "stage": 2, "type": 20, "value": 10000, "quality": 4, "camp": 0}, {"score": 3750, "stage": 2, "type": 20, "value": 12500, "quality": 5, "camp": 0}, {"score": 562, "stage": 3, "type": 1, "value": 15000, "quality": 1, "camp": 1}, {"score": 1125, "stage": 3, "type": 1, "value": 30000, "quality": 2, "camp": 1}, {"score": 1687, "stage": 3, "type": 1, "value": 45000, "quality": 3, "camp": 1}, {"score": 2250, "stage": 3, "type": 1, "value": 60000, "quality": 4, "camp": 1}, {"score": 2812, "stage": 3, "type": 1, "value": 75000, "quality": 5, "camp": 1}, {"score": 562, "stage": 3, "type": 2, "value": 150000, "quality": 1, "camp": 1}, {"score": 1125, "stage": 3, "type": 2, "value": 300000, "quality": 2, "camp": 1}, {"score": 1687, "stage": 3, "type": 2, "value": 450000, "quality": 3, "camp": 1}, {"score": 2250, "stage": 3, "type": 2, "value": 600000, "quality": 4, "camp": 1}, {"score": 2812, "stage": 3, "type": 2, "value": 750000, "quality": 5, "camp": 1}, {"score": 562, "stage": 3, "type": 3, "value": 7500, "quality": 1, "camp": 1}, {"score": 1125, "stage": 3, "type": 3, "value": 15000, "quality": 2, "camp": 1}, {"score": 1687, "stage": 3, "type": 3, "value": 22500, "quality": 3, "camp": 1}, {"score": 2250, "stage": 3, "type": 3, "value": 30000, "quality": 4, "camp": 1}, {"score": 2812, "stage": 3, "type": 3, "value": 37500, "quality": 5, "camp": 1}, {"score": 562, "stage": 3, "type": 4, "value": 7500, "quality": 1, "camp": 1}, {"score": 1125, "stage": 3, "type": 4, "value": 15000, "quality": 2, "camp": 1}, {"score": 1687, "stage": 3, "type": 4, "value": 22500, "quality": 3, "camp": 1}, {"score": 2250, "stage": 3, "type": 4, "value": 30000, "quality": 4, "camp": 1}, {"score": 2812, "stage": 3, "type": 4, "value": 37500, "quality": 5, "camp": 1}, {"score": 562, "stage": 3, "type": 5, "value": 15000, "quality": 1, "camp": 2}, {"score": 1125, "stage": 3, "type": 5, "value": 30000, "quality": 2, "camp": 2}, {"score": 1687, "stage": 3, "type": 5, "value": 45000, "quality": 3, "camp": 2}, {"score": 2250, "stage": 3, "type": 5, "value": 60000, "quality": 4, "camp": 2}, {"score": 2812, "stage": 3, "type": 5, "value": 75000, "quality": 5, "camp": 2}, {"score": 562, "stage": 3, "type": 6, "value": 150000, "quality": 1, "camp": 2}, {"score": 1125, "stage": 3, "type": 6, "value": 300000, "quality": 2, "camp": 2}, {"score": 1687, "stage": 3, "type": 6, "value": 450000, "quality": 3, "camp": 2}, {"score": 2250, "stage": 3, "type": 6, "value": 600000, "quality": 4, "camp": 2}, {"score": 2812, "stage": 3, "type": 6, "value": 750000, "quality": 5, "camp": 2}, {"score": 562, "stage": 3, "type": 7, "value": 7500, "quality": 1, "camp": 2}, {"score": 1125, "stage": 3, "type": 7, "value": 15000, "quality": 2, "camp": 2}, {"score": 1687, "stage": 3, "type": 7, "value": 22500, "quality": 3, "camp": 2}, {"score": 2250, "stage": 3, "type": 7, "value": 30000, "quality": 4, "camp": 2}, {"score": 2812, "stage": 3, "type": 7, "value": 37500, "quality": 5, "camp": 2}, {"score": 562, "stage": 3, "type": 8, "value": 7500, "quality": 1, "camp": 2}, {"score": 1125, "stage": 3, "type": 8, "value": 15000, "quality": 2, "camp": 2}, {"score": 1687, "stage": 3, "type": 8, "value": 22500, "quality": 3, "camp": 2}, {"score": 2250, "stage": 3, "type": 8, "value": 30000, "quality": 4, "camp": 2}, {"score": 2812, "stage": 3, "type": 8, "value": 37500, "quality": 5, "camp": 2}, {"score": 562, "stage": 3, "type": 9, "value": 15000, "quality": 1, "camp": 3}, {"score": 1125, "stage": 3, "type": 9, "value": 30000, "quality": 2, "camp": 3}, {"score": 1687, "stage": 3, "type": 9, "value": 45000, "quality": 3, "camp": 3}, {"score": 2250, "stage": 3, "type": 9, "value": 60000, "quality": 4, "camp": 3}, {"score": 2812, "stage": 3, "type": 9, "value": 75000, "quality": 5, "camp": 3}, {"score": 562, "stage": 3, "type": 10, "value": 150000, "quality": 1, "camp": 3}, {"score": 1125, "stage": 3, "type": 10, "value": 300000, "quality": 2, "camp": 3}, {"score": 1687, "stage": 3, "type": 10, "value": 450000, "quality": 3, "camp": 3}, {"score": 2250, "stage": 3, "type": 10, "value": 600000, "quality": 4, "camp": 3}, {"score": 2812, "stage": 3, "type": 10, "value": 750000, "quality": 5, "camp": 3}, {"score": 562, "stage": 3, "type": 11, "value": 7500, "quality": 1, "camp": 3}, {"score": 1125, "stage": 3, "type": 11, "value": 15000, "quality": 2, "camp": 3}, {"score": 1687, "stage": 3, "type": 11, "value": 22500, "quality": 3, "camp": 3}, {"score": 2250, "stage": 3, "type": 11, "value": 30000, "quality": 4, "camp": 3}, {"score": 2812, "stage": 3, "type": 11, "value": 37500, "quality": 5, "camp": 3}, {"score": 562, "stage": 3, "type": 12, "value": 7500, "quality": 1, "camp": 3}, {"score": 1125, "stage": 3, "type": 12, "value": 15000, "quality": 2, "camp": 3}, {"score": 1687, "stage": 3, "type": 12, "value": 22500, "quality": 3, "camp": 3}, {"score": 2250, "stage": 3, "type": 12, "value": 30000, "quality": 4, "camp": 3}, {"score": 2812, "stage": 3, "type": 12, "value": 37500, "quality": 5, "camp": 3}, {"score": 562, "stage": 3, "type": 13, "value": 15000, "quality": 1, "camp": 4}, {"score": 1125, "stage": 3, "type": 13, "value": 30000, "quality": 2, "camp": 4}, {"score": 1687, "stage": 3, "type": 13, "value": 45000, "quality": 3, "camp": 4}, {"score": 2250, "stage": 3, "type": 13, "value": 60000, "quality": 4, "camp": 4}, {"score": 2812, "stage": 3, "type": 13, "value": 75000, "quality": 5, "camp": 4}, {"score": 562, "stage": 3, "type": 14, "value": 150000, "quality": 1, "camp": 4}, {"score": 1125, "stage": 3, "type": 14, "value": 300000, "quality": 2, "camp": 4}, {"score": 1687, "stage": 3, "type": 14, "value": 450000, "quality": 3, "camp": 4}, {"score": 2250, "stage": 3, "type": 14, "value": 600000, "quality": 4, "camp": 4}, {"score": 2812, "stage": 3, "type": 14, "value": 750000, "quality": 5, "camp": 4}, {"score": 562, "stage": 3, "type": 15, "value": 7500, "quality": 1, "camp": 4}, {"score": 1125, "stage": 3, "type": 15, "value": 15000, "quality": 2, "camp": 4}, {"score": 1687, "stage": 3, "type": 15, "value": 22500, "quality": 3, "camp": 4}, {"score": 2250, "stage": 3, "type": 15, "value": 30000, "quality": 4, "camp": 4}, {"score": 2812, "stage": 3, "type": 15, "value": 37500, "quality": 5, "camp": 4}, {"score": 562, "stage": 3, "type": 16, "value": 7500, "quality": 1, "camp": 4}, {"score": 1125, "stage": 3, "type": 16, "value": 15000, "quality": 2, "camp": 4}, {"score": 1687, "stage": 3, "type": 16, "value": 22500, "quality": 3, "camp": 4}, {"score": 2250, "stage": 3, "type": 16, "value": 30000, "quality": 4, "camp": 4}, {"score": 2812, "stage": 3, "type": 16, "value": 37500, "quality": 5, "camp": 4}, {"score": 7500, "stage": 3, "type": 17, "value": 50000, "quality": 1, "camp": 0}, {"score": 15000, "stage": 3, "type": 17, "value": 100000, "quality": 2, "camp": 0}, {"score": 22500, "stage": 3, "type": 17, "value": 150000, "quality": 3, "camp": 0}, {"score": 30000, "stage": 3, "type": 17, "value": 200000, "quality": 4, "camp": 0}, {"score": 37500, "stage": 3, "type": 17, "value": 250000, "quality": 5, "camp": 0}, {"score": 7500, "stage": 3, "type": 18, "value": 500000, "quality": 1, "camp": 0}, {"score": 15000, "stage": 3, "type": 18, "value": 1000000, "quality": 2, "camp": 0}, {"score": 22500, "stage": 3, "type": 18, "value": 1500000, "quality": 3, "camp": 0}, {"score": 30000, "stage": 3, "type": 18, "value": 2000000, "quality": 4, "camp": 0}, {"score": 37500, "stage": 3, "type": 18, "value": 2500000, "quality": 5, "camp": 0}, {"score": 7500, "stage": 3, "type": 19, "value": 25000, "quality": 1, "camp": 0}, {"score": 15000, "stage": 3, "type": 19, "value": 50000, "quality": 2, "camp": 0}, {"score": 22500, "stage": 3, "type": 19, "value": 75000, "quality": 3, "camp": 0}, {"score": 30000, "stage": 3, "type": 19, "value": 100000, "quality": 4, "camp": 0}, {"score": 37500, "stage": 3, "type": 19, "value": 125000, "quality": 5, "camp": 0}, {"score": 7500, "stage": 3, "type": 20, "value": 25000, "quality": 1, "camp": 0}, {"score": 15000, "stage": 3, "type": 20, "value": 50000, "quality": 2, "camp": 0}, {"score": 22500, "stage": 3, "type": 20, "value": 75000, "quality": 3, "camp": 0}, {"score": 30000, "stage": 3, "type": 20, "value": 100000, "quality": 4, "camp": 0}, {"score": 37500, "stage": 3, "type": 20, "value": 125000, "quality": 5, "camp": 0}, {"score": 750, "stage": 4, "type": 1, "value": 20000, "quality": 1, "camp": 1}, {"score": 1500, "stage": 4, "type": 1, "value": 40000, "quality": 2, "camp": 1}, {"score": 2250, "stage": 4, "type": 1, "value": 60000, "quality": 3, "camp": 1}, {"score": 3000, "stage": 4, "type": 1, "value": 80000, "quality": 4, "camp": 1}, {"score": 3750, "stage": 4, "type": 1, "value": 100000, "quality": 5, "camp": 1}, {"score": 750, "stage": 4, "type": 2, "value": 200000, "quality": 1, "camp": 1}, {"score": 1500, "stage": 4, "type": 2, "value": 400000, "quality": 2, "camp": 1}, {"score": 2250, "stage": 4, "type": 2, "value": 600000, "quality": 3, "camp": 1}, {"score": 3000, "stage": 4, "type": 2, "value": 800000, "quality": 4, "camp": 1}, {"score": 3750, "stage": 4, "type": 2, "value": 1000000, "quality": 5, "camp": 1}, {"score": 750, "stage": 4, "type": 3, "value": 10000, "quality": 1, "camp": 1}, {"score": 1500, "stage": 4, "type": 3, "value": 20000, "quality": 2, "camp": 1}, {"score": 2250, "stage": 4, "type": 3, "value": 30000, "quality": 3, "camp": 1}, {"score": 3000, "stage": 4, "type": 3, "value": 40000, "quality": 4, "camp": 1}, {"score": 3750, "stage": 4, "type": 3, "value": 50000, "quality": 5, "camp": 1}, {"score": 750, "stage": 4, "type": 4, "value": 10000, "quality": 1, "camp": 1}, {"score": 1500, "stage": 4, "type": 4, "value": 20000, "quality": 2, "camp": 1}, {"score": 2250, "stage": 4, "type": 4, "value": 30000, "quality": 3, "camp": 1}, {"score": 3000, "stage": 4, "type": 4, "value": 40000, "quality": 4, "camp": 1}, {"score": 3750, "stage": 4, "type": 4, "value": 50000, "quality": 5, "camp": 1}, {"score": 750, "stage": 4, "type": 5, "value": 20000, "quality": 1, "camp": 2}, {"score": 1500, "stage": 4, "type": 5, "value": 40000, "quality": 2, "camp": 2}, {"score": 2250, "stage": 4, "type": 5, "value": 60000, "quality": 3, "camp": 2}, {"score": 3000, "stage": 4, "type": 5, "value": 80000, "quality": 4, "camp": 2}, {"score": 3750, "stage": 4, "type": 5, "value": 100000, "quality": 5, "camp": 2}, {"score": 750, "stage": 4, "type": 6, "value": 200000, "quality": 1, "camp": 2}, {"score": 1500, "stage": 4, "type": 6, "value": 400000, "quality": 2, "camp": 2}, {"score": 2250, "stage": 4, "type": 6, "value": 600000, "quality": 3, "camp": 2}, {"score": 3000, "stage": 4, "type": 6, "value": 800000, "quality": 4, "camp": 2}, {"score": 3750, "stage": 4, "type": 6, "value": 1000000, "quality": 5, "camp": 2}, {"score": 750, "stage": 4, "type": 7, "value": 10000, "quality": 1, "camp": 2}, {"score": 1500, "stage": 4, "type": 7, "value": 20000, "quality": 2, "camp": 2}, {"score": 2250, "stage": 4, "type": 7, "value": 30000, "quality": 3, "camp": 2}, {"score": 3000, "stage": 4, "type": 7, "value": 40000, "quality": 4, "camp": 2}, {"score": 3750, "stage": 4, "type": 7, "value": 50000, "quality": 5, "camp": 2}, {"score": 750, "stage": 4, "type": 8, "value": 10000, "quality": 1, "camp": 2}, {"score": 1500, "stage": 4, "type": 8, "value": 20000, "quality": 2, "camp": 2}, {"score": 2250, "stage": 4, "type": 8, "value": 30000, "quality": 3, "camp": 2}, {"score": 3000, "stage": 4, "type": 8, "value": 40000, "quality": 4, "camp": 2}, {"score": 3750, "stage": 4, "type": 8, "value": 50000, "quality": 5, "camp": 2}, {"score": 750, "stage": 4, "type": 9, "value": 20000, "quality": 1, "camp": 3}, {"score": 1500, "stage": 4, "type": 9, "value": 40000, "quality": 2, "camp": 3}, {"score": 2250, "stage": 4, "type": 9, "value": 60000, "quality": 3, "camp": 3}, {"score": 3000, "stage": 4, "type": 9, "value": 80000, "quality": 4, "camp": 3}, {"score": 3750, "stage": 4, "type": 9, "value": 100000, "quality": 5, "camp": 3}, {"score": 750, "stage": 4, "type": 10, "value": 200000, "quality": 1, "camp": 3}, {"score": 1500, "stage": 4, "type": 10, "value": 400000, "quality": 2, "camp": 3}, {"score": 2250, "stage": 4, "type": 10, "value": 600000, "quality": 3, "camp": 3}, {"score": 3000, "stage": 4, "type": 10, "value": 800000, "quality": 4, "camp": 3}, {"score": 3750, "stage": 4, "type": 10, "value": 1000000, "quality": 5, "camp": 3}, {"score": 750, "stage": 4, "type": 11, "value": 10000, "quality": 1, "camp": 3}, {"score": 1500, "stage": 4, "type": 11, "value": 20000, "quality": 2, "camp": 3}, {"score": 2250, "stage": 4, "type": 11, "value": 30000, "quality": 3, "camp": 3}, {"score": 3000, "stage": 4, "type": 11, "value": 40000, "quality": 4, "camp": 3}, {"score": 3750, "stage": 4, "type": 11, "value": 50000, "quality": 5, "camp": 3}, {"score": 750, "stage": 4, "type": 12, "value": 10000, "quality": 1, "camp": 3}, {"score": 1500, "stage": 4, "type": 12, "value": 20000, "quality": 2, "camp": 3}, {"score": 2250, "stage": 4, "type": 12, "value": 30000, "quality": 3, "camp": 3}, {"score": 3000, "stage": 4, "type": 12, "value": 40000, "quality": 4, "camp": 3}, {"score": 3750, "stage": 4, "type": 12, "value": 50000, "quality": 5, "camp": 3}, {"score": 750, "stage": 4, "type": 13, "value": 20000, "quality": 1, "camp": 4}, {"score": 1500, "stage": 4, "type": 13, "value": 40000, "quality": 2, "camp": 4}, {"score": 2250, "stage": 4, "type": 13, "value": 60000, "quality": 3, "camp": 4}, {"score": 3000, "stage": 4, "type": 13, "value": 80000, "quality": 4, "camp": 4}, {"score": 3750, "stage": 4, "type": 13, "value": 100000, "quality": 5, "camp": 4}, {"score": 750, "stage": 4, "type": 14, "value": 200000, "quality": 1, "camp": 4}, {"score": 1500, "stage": 4, "type": 14, "value": 400000, "quality": 2, "camp": 4}, {"score": 2250, "stage": 4, "type": 14, "value": 600000, "quality": 3, "camp": 4}, {"score": 3000, "stage": 4, "type": 14, "value": 800000, "quality": 4, "camp": 4}, {"score": 3750, "stage": 4, "type": 14, "value": 1000000, "quality": 5, "camp": 4}, {"score": 750, "stage": 4, "type": 15, "value": 10000, "quality": 1, "camp": 4}, {"score": 1500, "stage": 4, "type": 15, "value": 20000, "quality": 2, "camp": 4}, {"score": 2250, "stage": 4, "type": 15, "value": 30000, "quality": 3, "camp": 4}, {"score": 3000, "stage": 4, "type": 15, "value": 40000, "quality": 4, "camp": 4}, {"score": 3750, "stage": 4, "type": 15, "value": 50000, "quality": 5, "camp": 4}, {"score": 750, "stage": 4, "type": 16, "value": 10000, "quality": 1, "camp": 4}, {"score": 1500, "stage": 4, "type": 16, "value": 20000, "quality": 2, "camp": 4}, {"score": 2250, "stage": 4, "type": 16, "value": 30000, "quality": 3, "camp": 4}, {"score": 3000, "stage": 4, "type": 16, "value": 40000, "quality": 4, "camp": 4}, {"score": 3750, "stage": 4, "type": 16, "value": 50000, "quality": 5, "camp": 4}, {"score": 11250, "stage": 4, "type": 17, "value": 75000, "quality": 1, "camp": 0}, {"score": 22500, "stage": 4, "type": 17, "value": 150000, "quality": 2, "camp": 0}, {"score": 33750, "stage": 4, "type": 17, "value": 225000, "quality": 3, "camp": 0}, {"score": 45000, "stage": 4, "type": 17, "value": 300000, "quality": 4, "camp": 0}, {"score": 56250, "stage": 4, "type": 17, "value": 375000, "quality": 5, "camp": 0}, {"score": 11250, "stage": 4, "type": 18, "value": 750000, "quality": 1, "camp": 0}, {"score": 22500, "stage": 4, "type": 18, "value": 1500000, "quality": 2, "camp": 0}, {"score": 33750, "stage": 4, "type": 18, "value": 2250000, "quality": 3, "camp": 0}, {"score": 45000, "stage": 4, "type": 18, "value": 3000000, "quality": 4, "camp": 0}, {"score": 56250, "stage": 4, "type": 18, "value": 3750000, "quality": 5, "camp": 0}, {"score": 11250, "stage": 4, "type": 19, "value": 37500, "quality": 1, "camp": 0}, {"score": 22500, "stage": 4, "type": 19, "value": 75000, "quality": 2, "camp": 0}, {"score": 33750, "stage": 4, "type": 19, "value": 112500, "quality": 3, "camp": 0}, {"score": 45000, "stage": 4, "type": 19, "value": 150000, "quality": 4, "camp": 0}, {"score": 56250, "stage": 4, "type": 19, "value": 187500, "quality": 5, "camp": 0}, {"score": 11250, "stage": 4, "type": 20, "value": 37500, "quality": 1, "camp": 0}, {"score": 22500, "stage": 4, "type": 20, "value": 75000, "quality": 2, "camp": 0}, {"score": 33750, "stage": 4, "type": 20, "value": 112500, "quality": 3, "camp": 0}, {"score": 45000, "stage": 4, "type": 20, "value": 150000, "quality": 4, "camp": 0}, {"score": 56250, "stage": 4, "type": 20, "value": 187500, "quality": 5, "camp": 0}, {"score": 1125, "stage": 5, "type": 1, "value": 30000, "quality": 1, "camp": 1}, {"score": 2250, "stage": 5, "type": 1, "value": 60000, "quality": 2, "camp": 1}, {"score": 3375, "stage": 5, "type": 1, "value": 90000, "quality": 3, "camp": 1}, {"score": 4500, "stage": 5, "type": 1, "value": 120000, "quality": 4, "camp": 1}, {"score": 5625, "stage": 5, "type": 1, "value": 150000, "quality": 5, "camp": 1}, {"score": 1125, "stage": 5, "type": 2, "value": 300000, "quality": 1, "camp": 1}, {"score": 2250, "stage": 5, "type": 2, "value": 600000, "quality": 2, "camp": 1}, {"score": 3375, "stage": 5, "type": 2, "value": 900000, "quality": 3, "camp": 1}, {"score": 4500, "stage": 5, "type": 2, "value": 1200000, "quality": 4, "camp": 1}, {"score": 5625, "stage": 5, "type": 2, "value": 1500000, "quality": 5, "camp": 1}, {"score": 1125, "stage": 5, "type": 3, "value": 15000, "quality": 1, "camp": 1}, {"score": 2250, "stage": 5, "type": 3, "value": 30000, "quality": 2, "camp": 1}, {"score": 3375, "stage": 5, "type": 3, "value": 45000, "quality": 3, "camp": 1}, {"score": 4500, "stage": 5, "type": 3, "value": 60000, "quality": 4, "camp": 1}, {"score": 5625, "stage": 5, "type": 3, "value": 75000, "quality": 5, "camp": 1}, {"score": 1125, "stage": 5, "type": 4, "value": 15000, "quality": 1, "camp": 1}, {"score": 2250, "stage": 5, "type": 4, "value": 30000, "quality": 2, "camp": 1}, {"score": 3375, "stage": 5, "type": 4, "value": 45000, "quality": 3, "camp": 1}, {"score": 4500, "stage": 5, "type": 4, "value": 60000, "quality": 4, "camp": 1}, {"score": 5625, "stage": 5, "type": 4, "value": 75000, "quality": 5, "camp": 1}, {"score": 1125, "stage": 5, "type": 5, "value": 30000, "quality": 1, "camp": 2}, {"score": 2250, "stage": 5, "type": 5, "value": 60000, "quality": 2, "camp": 2}, {"score": 3375, "stage": 5, "type": 5, "value": 90000, "quality": 3, "camp": 2}, {"score": 4500, "stage": 5, "type": 5, "value": 120000, "quality": 4, "camp": 2}, {"score": 5625, "stage": 5, "type": 5, "value": 150000, "quality": 5, "camp": 2}, {"score": 1125, "stage": 5, "type": 6, "value": 300000, "quality": 1, "camp": 2}, {"score": 2250, "stage": 5, "type": 6, "value": 600000, "quality": 2, "camp": 2}, {"score": 3375, "stage": 5, "type": 6, "value": 900000, "quality": 3, "camp": 2}, {"score": 4500, "stage": 5, "type": 6, "value": 1200000, "quality": 4, "camp": 2}, {"score": 5625, "stage": 5, "type": 6, "value": 1500000, "quality": 5, "camp": 2}, {"score": 1125, "stage": 5, "type": 7, "value": 15000, "quality": 1, "camp": 2}, {"score": 2250, "stage": 5, "type": 7, "value": 30000, "quality": 2, "camp": 2}, {"score": 3375, "stage": 5, "type": 7, "value": 45000, "quality": 3, "camp": 2}, {"score": 4500, "stage": 5, "type": 7, "value": 60000, "quality": 4, "camp": 2}, {"score": 5625, "stage": 5, "type": 7, "value": 75000, "quality": 5, "camp": 2}, {"score": 1125, "stage": 5, "type": 8, "value": 15000, "quality": 1, "camp": 2}, {"score": 2250, "stage": 5, "type": 8, "value": 30000, "quality": 2, "camp": 2}, {"score": 3375, "stage": 5, "type": 8, "value": 45000, "quality": 3, "camp": 2}, {"score": 4500, "stage": 5, "type": 8, "value": 60000, "quality": 4, "camp": 2}, {"score": 5625, "stage": 5, "type": 8, "value": 75000, "quality": 5, "camp": 2}, {"score": 1125, "stage": 5, "type": 9, "value": 30000, "quality": 1, "camp": 3}, {"score": 2250, "stage": 5, "type": 9, "value": 60000, "quality": 2, "camp": 3}, {"score": 3375, "stage": 5, "type": 9, "value": 90000, "quality": 3, "camp": 3}, {"score": 4500, "stage": 5, "type": 9, "value": 120000, "quality": 4, "camp": 3}, {"score": 5625, "stage": 5, "type": 9, "value": 150000, "quality": 5, "camp": 3}, {"score": 1125, "stage": 5, "type": 10, "value": 300000, "quality": 1, "camp": 3}, {"score": 2250, "stage": 5, "type": 10, "value": 600000, "quality": 2, "camp": 3}, {"score": 3375, "stage": 5, "type": 10, "value": 900000, "quality": 3, "camp": 3}, {"score": 4500, "stage": 5, "type": 10, "value": 1200000, "quality": 4, "camp": 3}, {"score": 5625, "stage": 5, "type": 10, "value": 1500000, "quality": 5, "camp": 3}, {"score": 1125, "stage": 5, "type": 11, "value": 15000, "quality": 1, "camp": 3}, {"score": 2250, "stage": 5, "type": 11, "value": 30000, "quality": 2, "camp": 3}, {"score": 3375, "stage": 5, "type": 11, "value": 45000, "quality": 3, "camp": 3}, {"score": 4500, "stage": 5, "type": 11, "value": 60000, "quality": 4, "camp": 3}, {"score": 5625, "stage": 5, "type": 11, "value": 75000, "quality": 5, "camp": 3}, {"score": 1125, "stage": 5, "type": 12, "value": 15000, "quality": 1, "camp": 3}, {"score": 2250, "stage": 5, "type": 12, "value": 30000, "quality": 2, "camp": 3}, {"score": 3375, "stage": 5, "type": 12, "value": 45000, "quality": 3, "camp": 3}, {"score": 4500, "stage": 5, "type": 12, "value": 60000, "quality": 4, "camp": 3}, {"score": 5625, "stage": 5, "type": 12, "value": 75000, "quality": 5, "camp": 3}, {"score": 1125, "stage": 5, "type": 13, "value": 30000, "quality": 1, "camp": 4}, {"score": 2250, "stage": 5, "type": 13, "value": 60000, "quality": 2, "camp": 4}, {"score": 3375, "stage": 5, "type": 13, "value": 90000, "quality": 3, "camp": 4}, {"score": 4500, "stage": 5, "type": 13, "value": 120000, "quality": 4, "camp": 4}, {"score": 5625, "stage": 5, "type": 13, "value": 150000, "quality": 5, "camp": 4}, {"score": 1125, "stage": 5, "type": 14, "value": 300000, "quality": 1, "camp": 4}, {"score": 2250, "stage": 5, "type": 14, "value": 600000, "quality": 2, "camp": 4}, {"score": 3375, "stage": 5, "type": 14, "value": 900000, "quality": 3, "camp": 4}, {"score": 4500, "stage": 5, "type": 14, "value": 1200000, "quality": 4, "camp": 4}, {"score": 5625, "stage": 5, "type": 14, "value": 1500000, "quality": 5, "camp": 4}, {"score": 1125, "stage": 5, "type": 15, "value": 15000, "quality": 1, "camp": 4}, {"score": 2250, "stage": 5, "type": 15, "value": 30000, "quality": 2, "camp": 4}, {"score": 3375, "stage": 5, "type": 15, "value": 45000, "quality": 3, "camp": 4}, {"score": 4500, "stage": 5, "type": 15, "value": 60000, "quality": 4, "camp": 4}, {"score": 5625, "stage": 5, "type": 15, "value": 75000, "quality": 5, "camp": 4}, {"score": 1125, "stage": 5, "type": 16, "value": 15000, "quality": 1, "camp": 4}, {"score": 2250, "stage": 5, "type": 16, "value": 30000, "quality": 2, "camp": 4}, {"score": 3375, "stage": 5, "type": 16, "value": 45000, "quality": 3, "camp": 4}, {"score": 4500, "stage": 5, "type": 16, "value": 60000, "quality": 4, "camp": 4}, {"score": 5625, "stage": 5, "type": 16, "value": 75000, "quality": 5, "camp": 4}, {"score": 15000, "stage": 5, "type": 17, "value": 100000, "quality": 1, "camp": 0}, {"score": 30000, "stage": 5, "type": 17, "value": 200000, "quality": 2, "camp": 0}, {"score": 45000, "stage": 5, "type": 17, "value": 300000, "quality": 3, "camp": 0}, {"score": 60000, "stage": 5, "type": 17, "value": 400000, "quality": 4, "camp": 0}, {"score": 75000, "stage": 5, "type": 17, "value": 500000, "quality": 5, "camp": 0}, {"score": 15000, "stage": 5, "type": 18, "value": 1000000, "quality": 1, "camp": 0}, {"score": 30000, "stage": 5, "type": 18, "value": 2000000, "quality": 2, "camp": 0}, {"score": 45000, "stage": 5, "type": 18, "value": 3000000, "quality": 3, "camp": 0}, {"score": 60000, "stage": 5, "type": 18, "value": 4000000, "quality": 4, "camp": 0}, {"score": 75000, "stage": 5, "type": 18, "value": 5000000, "quality": 5, "camp": 0}, {"score": 15000, "stage": 5, "type": 19, "value": 50000, "quality": 1, "camp": 0}, {"score": 30000, "stage": 5, "type": 19, "value": 100000, "quality": 2, "camp": 0}, {"score": 45000, "stage": 5, "type": 19, "value": 150000, "quality": 3, "camp": 0}, {"score": 60000, "stage": 5, "type": 19, "value": 200000, "quality": 4, "camp": 0}, {"score": 75000, "stage": 5, "type": 19, "value": 250000, "quality": 5, "camp": 0}, {"score": 15000, "stage": 5, "type": 20, "value": 50000, "quality": 1, "camp": 0}, {"score": 30000, "stage": 5, "type": 20, "value": 100000, "quality": 2, "camp": 0}, {"score": 45000, "stage": 5, "type": 20, "value": 150000, "quality": 3, "camp": 0}, {"score": 60000, "stage": 5, "type": 20, "value": 200000, "quality": 4, "camp": 0}, {"score": 75000, "stage": 5, "type": 20, "value": 250000, "quality": 5, "camp": 0}, {"score": 1500, "stage": 6, "type": 1, "value": 40000, "quality": 1, "camp": 1}, {"score": 3000, "stage": 6, "type": 1, "value": 80000, "quality": 2, "camp": 1}, {"score": 4500, "stage": 6, "type": 1, "value": 120000, "quality": 3, "camp": 1}, {"score": 6000, "stage": 6, "type": 1, "value": 160000, "quality": 4, "camp": 1}, {"score": 7500, "stage": 6, "type": 1, "value": 200000, "quality": 5, "camp": 1}, {"score": 1500, "stage": 6, "type": 2, "value": 400000, "quality": 1, "camp": 1}, {"score": 3000, "stage": 6, "type": 2, "value": 800000, "quality": 2, "camp": 1}, {"score": 4500, "stage": 6, "type": 2, "value": 1200000, "quality": 3, "camp": 1}, {"score": 6000, "stage": 6, "type": 2, "value": 1600000, "quality": 4, "camp": 1}, {"score": 7500, "stage": 6, "type": 2, "value": 2000000, "quality": 5, "camp": 1}, {"score": 1500, "stage": 6, "type": 3, "value": 20000, "quality": 1, "camp": 1}, {"score": 3000, "stage": 6, "type": 3, "value": 40000, "quality": 2, "camp": 1}, {"score": 4500, "stage": 6, "type": 3, "value": 60000, "quality": 3, "camp": 1}, {"score": 6000, "stage": 6, "type": 3, "value": 80000, "quality": 4, "camp": 1}, {"score": 7500, "stage": 6, "type": 3, "value": 100000, "quality": 5, "camp": 1}, {"score": 1500, "stage": 6, "type": 4, "value": 20000, "quality": 1, "camp": 1}, {"score": 3000, "stage": 6, "type": 4, "value": 40000, "quality": 2, "camp": 1}, {"score": 4500, "stage": 6, "type": 4, "value": 60000, "quality": 3, "camp": 1}, {"score": 6000, "stage": 6, "type": 4, "value": 80000, "quality": 4, "camp": 1}, {"score": 7500, "stage": 6, "type": 4, "value": 100000, "quality": 5, "camp": 1}, {"score": 1500, "stage": 6, "type": 5, "value": 40000, "quality": 1, "camp": 2}, {"score": 3000, "stage": 6, "type": 5, "value": 80000, "quality": 2, "camp": 2}, {"score": 4500, "stage": 6, "type": 5, "value": 120000, "quality": 3, "camp": 2}, {"score": 6000, "stage": 6, "type": 5, "value": 160000, "quality": 4, "camp": 2}, {"score": 7500, "stage": 6, "type": 5, "value": 200000, "quality": 5, "camp": 2}, {"score": 1500, "stage": 6, "type": 6, "value": 400000, "quality": 1, "camp": 2}, {"score": 3000, "stage": 6, "type": 6, "value": 800000, "quality": 2, "camp": 2}, {"score": 4500, "stage": 6, "type": 6, "value": 1200000, "quality": 3, "camp": 2}, {"score": 6000, "stage": 6, "type": 6, "value": 1600000, "quality": 4, "camp": 2}, {"score": 7500, "stage": 6, "type": 6, "value": 2000000, "quality": 5, "camp": 2}, {"score": 1500, "stage": 6, "type": 7, "value": 20000, "quality": 1, "camp": 2}, {"score": 3000, "stage": 6, "type": 7, "value": 40000, "quality": 2, "camp": 2}, {"score": 4500, "stage": 6, "type": 7, "value": 60000, "quality": 3, "camp": 2}, {"score": 6000, "stage": 6, "type": 7, "value": 80000, "quality": 4, "camp": 2}, {"score": 7500, "stage": 6, "type": 7, "value": 100000, "quality": 5, "camp": 2}, {"score": 1500, "stage": 6, "type": 8, "value": 20000, "quality": 1, "camp": 2}, {"score": 3000, "stage": 6, "type": 8, "value": 40000, "quality": 2, "camp": 2}, {"score": 4500, "stage": 6, "type": 8, "value": 60000, "quality": 3, "camp": 2}, {"score": 6000, "stage": 6, "type": 8, "value": 80000, "quality": 4, "camp": 2}, {"score": 7500, "stage": 6, "type": 8, "value": 100000, "quality": 5, "camp": 2}, {"score": 1500, "stage": 6, "type": 9, "value": 40000, "quality": 1, "camp": 3}, {"score": 3000, "stage": 6, "type": 9, "value": 80000, "quality": 2, "camp": 3}, {"score": 4500, "stage": 6, "type": 9, "value": 120000, "quality": 3, "camp": 3}, {"score": 6000, "stage": 6, "type": 9, "value": 160000, "quality": 4, "camp": 3}, {"score": 7500, "stage": 6, "type": 9, "value": 200000, "quality": 5, "camp": 3}, {"score": 1500, "stage": 6, "type": 10, "value": 400000, "quality": 1, "camp": 3}, {"score": 3000, "stage": 6, "type": 10, "value": 800000, "quality": 2, "camp": 3}, {"score": 4500, "stage": 6, "type": 10, "value": 1200000, "quality": 3, "camp": 3}, {"score": 6000, "stage": 6, "type": 10, "value": 1600000, "quality": 4, "camp": 3}, {"score": 7500, "stage": 6, "type": 10, "value": 2000000, "quality": 5, "camp": 3}, {"score": 1500, "stage": 6, "type": 11, "value": 20000, "quality": 1, "camp": 3}, {"score": 3000, "stage": 6, "type": 11, "value": 40000, "quality": 2, "camp": 3}, {"score": 4500, "stage": 6, "type": 11, "value": 60000, "quality": 3, "camp": 3}, {"score": 6000, "stage": 6, "type": 11, "value": 80000, "quality": 4, "camp": 3}, {"score": 7500, "stage": 6, "type": 11, "value": 100000, "quality": 5, "camp": 3}, {"score": 1500, "stage": 6, "type": 12, "value": 20000, "quality": 1, "camp": 3}, {"score": 3000, "stage": 6, "type": 12, "value": 40000, "quality": 2, "camp": 3}, {"score": 4500, "stage": 6, "type": 12, "value": 60000, "quality": 3, "camp": 3}, {"score": 6000, "stage": 6, "type": 12, "value": 80000, "quality": 4, "camp": 3}, {"score": 7500, "stage": 6, "type": 12, "value": 100000, "quality": 5, "camp": 3}, {"score": 1500, "stage": 6, "type": 13, "value": 40000, "quality": 1, "camp": 4}, {"score": 3000, "stage": 6, "type": 13, "value": 80000, "quality": 2, "camp": 4}, {"score": 4500, "stage": 6, "type": 13, "value": 120000, "quality": 3, "camp": 4}, {"score": 6000, "stage": 6, "type": 13, "value": 160000, "quality": 4, "camp": 4}, {"score": 7500, "stage": 6, "type": 13, "value": 200000, "quality": 5, "camp": 4}, {"score": 1500, "stage": 6, "type": 14, "value": 400000, "quality": 1, "camp": 4}, {"score": 3000, "stage": 6, "type": 14, "value": 800000, "quality": 2, "camp": 4}, {"score": 4500, "stage": 6, "type": 14, "value": 1200000, "quality": 3, "camp": 4}, {"score": 6000, "stage": 6, "type": 14, "value": 1600000, "quality": 4, "camp": 4}, {"score": 7500, "stage": 6, "type": 14, "value": 2000000, "quality": 5, "camp": 4}, {"score": 1500, "stage": 6, "type": 15, "value": 20000, "quality": 1, "camp": 4}, {"score": 3000, "stage": 6, "type": 15, "value": 40000, "quality": 2, "camp": 4}, {"score": 4500, "stage": 6, "type": 15, "value": 60000, "quality": 3, "camp": 4}, {"score": 6000, "stage": 6, "type": 15, "value": 80000, "quality": 4, "camp": 4}, {"score": 7500, "stage": 6, "type": 15, "value": 100000, "quality": 5, "camp": 4}, {"score": 1500, "stage": 6, "type": 16, "value": 20000, "quality": 1, "camp": 4}, {"score": 3000, "stage": 6, "type": 16, "value": 40000, "quality": 2, "camp": 4}, {"score": 4500, "stage": 6, "type": 16, "value": 60000, "quality": 3, "camp": 4}, {"score": 6000, "stage": 6, "type": 16, "value": 80000, "quality": 4, "camp": 4}, {"score": 7500, "stage": 6, "type": 16, "value": 100000, "quality": 5, "camp": 4}, {"score": 18750, "stage": 6, "type": 17, "value": 125000, "quality": 1, "camp": 0}, {"score": 37500, "stage": 6, "type": 17, "value": 250000, "quality": 2, "camp": 0}, {"score": 56250, "stage": 6, "type": 17, "value": 375000, "quality": 3, "camp": 0}, {"score": 75000, "stage": 6, "type": 17, "value": 500000, "quality": 4, "camp": 0}, {"score": 93750, "stage": 6, "type": 17, "value": 625000, "quality": 5, "camp": 0}, {"score": 18750, "stage": 6, "type": 18, "value": 1250000, "quality": 1, "camp": 0}, {"score": 37500, "stage": 6, "type": 18, "value": 2500000, "quality": 2, "camp": 0}, {"score": 56250, "stage": 6, "type": 18, "value": 3750000, "quality": 3, "camp": 0}, {"score": 75000, "stage": 6, "type": 18, "value": 5000000, "quality": 4, "camp": 0}, {"score": 93750, "stage": 6, "type": 18, "value": 6250000, "quality": 5, "camp": 0}, {"score": 18750, "stage": 6, "type": 19, "value": 62500, "quality": 1, "camp": 0}, {"score": 37500, "stage": 6, "type": 19, "value": 125000, "quality": 2, "camp": 0}, {"score": 56250, "stage": 6, "type": 19, "value": 187500, "quality": 3, "camp": 0}, {"score": 75000, "stage": 6, "type": 19, "value": 250000, "quality": 4, "camp": 0}, {"score": 93750, "stage": 6, "type": 19, "value": 312500, "quality": 5, "camp": 0}, {"score": 18750, "stage": 6, "type": 20, "value": 62500, "quality": 1, "camp": 0}, {"score": 37500, "stage": 6, "type": 20, "value": 125000, "quality": 2, "camp": 0}, {"score": 56250, "stage": 6, "type": 20, "value": 187500, "quality": 3, "camp": 0}, {"score": 75000, "stage": 6, "type": 20, "value": 250000, "quality": 4, "camp": 0}, {"score": 93750, "stage": 6, "type": 20, "value": 312500, "quality": 5, "camp": 0}, {"score": 1875, "stage": 7, "type": 1, "value": 50000, "quality": 1, "camp": 1}, {"score": 3750, "stage": 7, "type": 1, "value": 100000, "quality": 2, "camp": 1}, {"score": 5625, "stage": 7, "type": 1, "value": 150000, "quality": 3, "camp": 1}, {"score": 7500, "stage": 7, "type": 1, "value": 200000, "quality": 4, "camp": 1}, {"score": 9375, "stage": 7, "type": 1, "value": 250000, "quality": 5, "camp": 1}, {"score": 1875, "stage": 7, "type": 2, "value": 500000, "quality": 1, "camp": 1}, {"score": 3750, "stage": 7, "type": 2, "value": 1000000, "quality": 2, "camp": 1}, {"score": 5625, "stage": 7, "type": 2, "value": 1500000, "quality": 3, "camp": 1}, {"score": 7500, "stage": 7, "type": 2, "value": 2000000, "quality": 4, "camp": 1}, {"score": 9375, "stage": 7, "type": 2, "value": 2500000, "quality": 5, "camp": 1}, {"score": 1875, "stage": 7, "type": 3, "value": 25000, "quality": 1, "camp": 1}, {"score": 3750, "stage": 7, "type": 3, "value": 50000, "quality": 2, "camp": 1}, {"score": 5625, "stage": 7, "type": 3, "value": 75000, "quality": 3, "camp": 1}, {"score": 7500, "stage": 7, "type": 3, "value": 100000, "quality": 4, "camp": 1}, {"score": 9375, "stage": 7, "type": 3, "value": 125000, "quality": 5, "camp": 1}, {"score": 1875, "stage": 7, "type": 4, "value": 25000, "quality": 1, "camp": 1}, {"score": 3750, "stage": 7, "type": 4, "value": 50000, "quality": 2, "camp": 1}, {"score": 5625, "stage": 7, "type": 4, "value": 75000, "quality": 3, "camp": 1}, {"score": 7500, "stage": 7, "type": 4, "value": 100000, "quality": 4, "camp": 1}, {"score": 9375, "stage": 7, "type": 4, "value": 125000, "quality": 5, "camp": 1}, {"score": 1875, "stage": 7, "type": 5, "value": 50000, "quality": 1, "camp": 2}, {"score": 3750, "stage": 7, "type": 5, "value": 100000, "quality": 2, "camp": 2}, {"score": 5625, "stage": 7, "type": 5, "value": 150000, "quality": 3, "camp": 2}, {"score": 7500, "stage": 7, "type": 5, "value": 200000, "quality": 4, "camp": 2}, {"score": 9375, "stage": 7, "type": 5, "value": 250000, "quality": 5, "camp": 2}, {"score": 1875, "stage": 7, "type": 6, "value": 500000, "quality": 1, "camp": 2}, {"score": 3750, "stage": 7, "type": 6, "value": 1000000, "quality": 2, "camp": 2}, {"score": 5625, "stage": 7, "type": 6, "value": 1500000, "quality": 3, "camp": 2}, {"score": 7500, "stage": 7, "type": 6, "value": 2000000, "quality": 4, "camp": 2}, {"score": 9375, "stage": 7, "type": 6, "value": 2500000, "quality": 5, "camp": 2}, {"score": 1875, "stage": 7, "type": 7, "value": 25000, "quality": 1, "camp": 2}, {"score": 3750, "stage": 7, "type": 7, "value": 50000, "quality": 2, "camp": 2}, {"score": 5625, "stage": 7, "type": 7, "value": 75000, "quality": 3, "camp": 2}, {"score": 7500, "stage": 7, "type": 7, "value": 100000, "quality": 4, "camp": 2}, {"score": 9375, "stage": 7, "type": 7, "value": 125000, "quality": 5, "camp": 2}, {"score": 1875, "stage": 7, "type": 8, "value": 25000, "quality": 1, "camp": 2}, {"score": 3750, "stage": 7, "type": 8, "value": 50000, "quality": 2, "camp": 2}, {"score": 5625, "stage": 7, "type": 8, "value": 75000, "quality": 3, "camp": 2}, {"score": 7500, "stage": 7, "type": 8, "value": 100000, "quality": 4, "camp": 2}, {"score": 9375, "stage": 7, "type": 8, "value": 125000, "quality": 5, "camp": 2}, {"score": 1875, "stage": 7, "type": 9, "value": 50000, "quality": 1, "camp": 3}, {"score": 3750, "stage": 7, "type": 9, "value": 100000, "quality": 2, "camp": 3}, {"score": 5625, "stage": 7, "type": 9, "value": 150000, "quality": 3, "camp": 3}, {"score": 7500, "stage": 7, "type": 9, "value": 200000, "quality": 4, "camp": 3}, {"score": 9375, "stage": 7, "type": 9, "value": 250000, "quality": 5, "camp": 3}, {"score": 1875, "stage": 7, "type": 10, "value": 500000, "quality": 1, "camp": 3}, {"score": 3750, "stage": 7, "type": 10, "value": 1000000, "quality": 2, "camp": 3}, {"score": 5625, "stage": 7, "type": 10, "value": 1500000, "quality": 3, "camp": 3}, {"score": 7500, "stage": 7, "type": 10, "value": 2000000, "quality": 4, "camp": 3}, {"score": 9375, "stage": 7, "type": 10, "value": 2500000, "quality": 5, "camp": 3}, {"score": 1875, "stage": 7, "type": 11, "value": 25000, "quality": 1, "camp": 3}, {"score": 3750, "stage": 7, "type": 11, "value": 50000, "quality": 2, "camp": 3}, {"score": 5625, "stage": 7, "type": 11, "value": 75000, "quality": 3, "camp": 3}, {"score": 7500, "stage": 7, "type": 11, "value": 100000, "quality": 4, "camp": 3}, {"score": 9375, "stage": 7, "type": 11, "value": 125000, "quality": 5, "camp": 3}, {"score": 1875, "stage": 7, "type": 12, "value": 25000, "quality": 1, "camp": 3}, {"score": 3750, "stage": 7, "type": 12, "value": 50000, "quality": 2, "camp": 3}, {"score": 5625, "stage": 7, "type": 12, "value": 75000, "quality": 3, "camp": 3}, {"score": 7500, "stage": 7, "type": 12, "value": 100000, "quality": 4, "camp": 3}, {"score": 9375, "stage": 7, "type": 12, "value": 125000, "quality": 5, "camp": 3}, {"score": 1875, "stage": 7, "type": 13, "value": 50000, "quality": 1, "camp": 4}, {"score": 3750, "stage": 7, "type": 13, "value": 100000, "quality": 2, "camp": 4}, {"score": 5625, "stage": 7, "type": 13, "value": 150000, "quality": 3, "camp": 4}, {"score": 7500, "stage": 7, "type": 13, "value": 200000, "quality": 4, "camp": 4}, {"score": 9375, "stage": 7, "type": 13, "value": 250000, "quality": 5, "camp": 4}, {"score": 1875, "stage": 7, "type": 14, "value": 500000, "quality": 1, "camp": 4}, {"score": 3750, "stage": 7, "type": 14, "value": 1000000, "quality": 2, "camp": 4}, {"score": 5625, "stage": 7, "type": 14, "value": 1500000, "quality": 3, "camp": 4}, {"score": 7500, "stage": 7, "type": 14, "value": 2000000, "quality": 4, "camp": 4}, {"score": 9375, "stage": 7, "type": 14, "value": 2500000, "quality": 5, "camp": 4}, {"score": 1875, "stage": 7, "type": 15, "value": 25000, "quality": 1, "camp": 4}, {"score": 3750, "stage": 7, "type": 15, "value": 50000, "quality": 2, "camp": 4}, {"score": 5625, "stage": 7, "type": 15, "value": 75000, "quality": 3, "camp": 4}, {"score": 7500, "stage": 7, "type": 15, "value": 100000, "quality": 4, "camp": 4}, {"score": 9375, "stage": 7, "type": 15, "value": 125000, "quality": 5, "camp": 4}, {"score": 1875, "stage": 7, "type": 16, "value": 25000, "quality": 1, "camp": 4}, {"score": 3750, "stage": 7, "type": 16, "value": 50000, "quality": 2, "camp": 4}, {"score": 5625, "stage": 7, "type": 16, "value": 75000, "quality": 3, "camp": 4}, {"score": 7500, "stage": 7, "type": 16, "value": 100000, "quality": 4, "camp": 4}, {"score": 9375, "stage": 7, "type": 16, "value": 125000, "quality": 5, "camp": 4}, {"score": 22500, "stage": 7, "type": 17, "value": 150000, "quality": 1, "camp": 0}, {"score": 45000, "stage": 7, "type": 17, "value": 300000, "quality": 2, "camp": 0}, {"score": 67500, "stage": 7, "type": 17, "value": 450000, "quality": 3, "camp": 0}, {"score": 90000, "stage": 7, "type": 17, "value": 600000, "quality": 4, "camp": 0}, {"score": 112500, "stage": 7, "type": 17, "value": 750000, "quality": 5, "camp": 0}, {"score": 22500, "stage": 7, "type": 18, "value": 1500000, "quality": 1, "camp": 0}, {"score": 45000, "stage": 7, "type": 18, "value": 3000000, "quality": 2, "camp": 0}, {"score": 67500, "stage": 7, "type": 18, "value": 4500000, "quality": 3, "camp": 0}, {"score": 90000, "stage": 7, "type": 18, "value": 6000000, "quality": 4, "camp": 0}, {"score": 112500, "stage": 7, "type": 18, "value": 7500000, "quality": 5, "camp": 0}, {"score": 22500, "stage": 7, "type": 19, "value": 75000, "quality": 1, "camp": 0}, {"score": 45000, "stage": 7, "type": 19, "value": 150000, "quality": 2, "camp": 0}, {"score": 67500, "stage": 7, "type": 19, "value": 225000, "quality": 3, "camp": 0}, {"score": 90000, "stage": 7, "type": 19, "value": 300000, "quality": 4, "camp": 0}, {"score": 112500, "stage": 7, "type": 19, "value": 375000, "quality": 5, "camp": 0}, {"score": 22500, "stage": 7, "type": 20, "value": 75000, "quality": 1, "camp": 0}, {"score": 45000, "stage": 7, "type": 20, "value": 150000, "quality": 2, "camp": 0}, {"score": 67500, "stage": 7, "type": 20, "value": 225000, "quality": 3, "camp": 0}, {"score": 90000, "stage": 7, "type": 20, "value": 300000, "quality": 4, "camp": 0}, {"score": 112500, "stage": 7, "type": 20, "value": 375000, "quality": 5, "camp": 0}, {"score": 2250, "stage": 8, "type": 1, "value": 60000, "quality": 1, "camp": 1}, {"score": 4500, "stage": 8, "type": 1, "value": 120000, "quality": 2, "camp": 1}, {"score": 6750, "stage": 8, "type": 1, "value": 180000, "quality": 3, "camp": 1}, {"score": 9000, "stage": 8, "type": 1, "value": 240000, "quality": 4, "camp": 1}, {"score": 11250, "stage": 8, "type": 1, "value": 300000, "quality": 5, "camp": 1}, {"score": 2250, "stage": 8, "type": 2, "value": 600000, "quality": 1, "camp": 1}, {"score": 4500, "stage": 8, "type": 2, "value": 1200000, "quality": 2, "camp": 1}, {"score": 6750, "stage": 8, "type": 2, "value": 1800000, "quality": 3, "camp": 1}, {"score": 9000, "stage": 8, "type": 2, "value": 2400000, "quality": 4, "camp": 1}, {"score": 11250, "stage": 8, "type": 2, "value": 3000000, "quality": 5, "camp": 1}, {"score": 2250, "stage": 8, "type": 3, "value": 30000, "quality": 1, "camp": 1}, {"score": 4500, "stage": 8, "type": 3, "value": 60000, "quality": 2, "camp": 1}, {"score": 6750, "stage": 8, "type": 3, "value": 90000, "quality": 3, "camp": 1}, {"score": 9000, "stage": 8, "type": 3, "value": 120000, "quality": 4, "camp": 1}, {"score": 11250, "stage": 8, "type": 3, "value": 150000, "quality": 5, "camp": 1}, {"score": 2250, "stage": 8, "type": 4, "value": 30000, "quality": 1, "camp": 1}, {"score": 4500, "stage": 8, "type": 4, "value": 60000, "quality": 2, "camp": 1}, {"score": 6750, "stage": 8, "type": 4, "value": 90000, "quality": 3, "camp": 1}, {"score": 9000, "stage": 8, "type": 4, "value": 120000, "quality": 4, "camp": 1}, {"score": 11250, "stage": 8, "type": 4, "value": 150000, "quality": 5, "camp": 1}, {"score": 2250, "stage": 8, "type": 5, "value": 60000, "quality": 1, "camp": 2}, {"score": 4500, "stage": 8, "type": 5, "value": 120000, "quality": 2, "camp": 2}, {"score": 6750, "stage": 8, "type": 5, "value": 180000, "quality": 3, "camp": 2}, {"score": 9000, "stage": 8, "type": 5, "value": 240000, "quality": 4, "camp": 2}, {"score": 11250, "stage": 8, "type": 5, "value": 300000, "quality": 5, "camp": 2}, {"score": 2250, "stage": 8, "type": 6, "value": 600000, "quality": 1, "camp": 2}, {"score": 4500, "stage": 8, "type": 6, "value": 1200000, "quality": 2, "camp": 2}, {"score": 6750, "stage": 8, "type": 6, "value": 1800000, "quality": 3, "camp": 2}, {"score": 9000, "stage": 8, "type": 6, "value": 2400000, "quality": 4, "camp": 2}, {"score": 11250, "stage": 8, "type": 6, "value": 3000000, "quality": 5, "camp": 2}, {"score": 2250, "stage": 8, "type": 7, "value": 30000, "quality": 1, "camp": 2}, {"score": 4500, "stage": 8, "type": 7, "value": 60000, "quality": 2, "camp": 2}, {"score": 6750, "stage": 8, "type": 7, "value": 90000, "quality": 3, "camp": 2}, {"score": 9000, "stage": 8, "type": 7, "value": 120000, "quality": 4, "camp": 2}, {"score": 11250, "stage": 8, "type": 7, "value": 150000, "quality": 5, "camp": 2}, {"score": 2250, "stage": 8, "type": 8, "value": 30000, "quality": 1, "camp": 2}, {"score": 4500, "stage": 8, "type": 8, "value": 60000, "quality": 2, "camp": 2}, {"score": 6750, "stage": 8, "type": 8, "value": 90000, "quality": 3, "camp": 2}, {"score": 9000, "stage": 8, "type": 8, "value": 120000, "quality": 4, "camp": 2}, {"score": 11250, "stage": 8, "type": 8, "value": 150000, "quality": 5, "camp": 2}, {"score": 2250, "stage": 8, "type": 9, "value": 60000, "quality": 1, "camp": 3}, {"score": 4500, "stage": 8, "type": 9, "value": 120000, "quality": 2, "camp": 3}, {"score": 6750, "stage": 8, "type": 9, "value": 180000, "quality": 3, "camp": 3}, {"score": 9000, "stage": 8, "type": 9, "value": 240000, "quality": 4, "camp": 3}, {"score": 11250, "stage": 8, "type": 9, "value": 300000, "quality": 5, "camp": 3}, {"score": 2250, "stage": 8, "type": 10, "value": 600000, "quality": 1, "camp": 3}, {"score": 4500, "stage": 8, "type": 10, "value": 1200000, "quality": 2, "camp": 3}, {"score": 6750, "stage": 8, "type": 10, "value": 1800000, "quality": 3, "camp": 3}, {"score": 9000, "stage": 8, "type": 10, "value": 2400000, "quality": 4, "camp": 3}, {"score": 11250, "stage": 8, "type": 10, "value": 3000000, "quality": 5, "camp": 3}, {"score": 2250, "stage": 8, "type": 11, "value": 30000, "quality": 1, "camp": 3}, {"score": 4500, "stage": 8, "type": 11, "value": 60000, "quality": 2, "camp": 3}, {"score": 6750, "stage": 8, "type": 11, "value": 90000, "quality": 3, "camp": 3}, {"score": 9000, "stage": 8, "type": 11, "value": 120000, "quality": 4, "camp": 3}, {"score": 11250, "stage": 8, "type": 11, "value": 150000, "quality": 5, "camp": 3}, {"score": 2250, "stage": 8, "type": 12, "value": 30000, "quality": 1, "camp": 3}, {"score": 4500, "stage": 8, "type": 12, "value": 60000, "quality": 2, "camp": 3}, {"score": 6750, "stage": 8, "type": 12, "value": 90000, "quality": 3, "camp": 3}, {"score": 9000, "stage": 8, "type": 12, "value": 120000, "quality": 4, "camp": 3}, {"score": 11250, "stage": 8, "type": 12, "value": 150000, "quality": 5, "camp": 3}, {"score": 2250, "stage": 8, "type": 13, "value": 60000, "quality": 1, "camp": 4}, {"score": 4500, "stage": 8, "type": 13, "value": 120000, "quality": 2, "camp": 4}, {"score": 6750, "stage": 8, "type": 13, "value": 180000, "quality": 3, "camp": 4}, {"score": 9000, "stage": 8, "type": 13, "value": 240000, "quality": 4, "camp": 4}, {"score": 11250, "stage": 8, "type": 13, "value": 300000, "quality": 5, "camp": 4}, {"score": 2250, "stage": 8, "type": 14, "value": 600000, "quality": 1, "camp": 4}, {"score": 4500, "stage": 8, "type": 14, "value": 1200000, "quality": 2, "camp": 4}, {"score": 6750, "stage": 8, "type": 14, "value": 1800000, "quality": 3, "camp": 4}, {"score": 9000, "stage": 8, "type": 14, "value": 2400000, "quality": 4, "camp": 4}, {"score": 11250, "stage": 8, "type": 14, "value": 3000000, "quality": 5, "camp": 4}, {"score": 2250, "stage": 8, "type": 15, "value": 30000, "quality": 1, "camp": 4}, {"score": 4500, "stage": 8, "type": 15, "value": 60000, "quality": 2, "camp": 4}, {"score": 6750, "stage": 8, "type": 15, "value": 90000, "quality": 3, "camp": 4}, {"score": 9000, "stage": 8, "type": 15, "value": 120000, "quality": 4, "camp": 4}, {"score": 11250, "stage": 8, "type": 15, "value": 150000, "quality": 5, "camp": 4}, {"score": 2250, "stage": 8, "type": 16, "value": 30000, "quality": 1, "camp": 4}, {"score": 4500, "stage": 8, "type": 16, "value": 60000, "quality": 2, "camp": 4}, {"score": 6750, "stage": 8, "type": 16, "value": 90000, "quality": 3, "camp": 4}, {"score": 9000, "stage": 8, "type": 16, "value": 120000, "quality": 4, "camp": 4}, {"score": 11250, "stage": 8, "type": 16, "value": 150000, "quality": 5, "camp": 4}, {"score": 26250, "stage": 8, "type": 17, "value": 175000, "quality": 1, "camp": 0}, {"score": 52500, "stage": 8, "type": 17, "value": 350000, "quality": 2, "camp": 0}, {"score": 78750, "stage": 8, "type": 17, "value": 525000, "quality": 3, "camp": 0}, {"score": 105000, "stage": 8, "type": 17, "value": 700000, "quality": 4, "camp": 0}, {"score": 131250, "stage": 8, "type": 17, "value": 875000, "quality": 5, "camp": 0}, {"score": 26250, "stage": 8, "type": 18, "value": 1750000, "quality": 1, "camp": 0}, {"score": 52500, "stage": 8, "type": 18, "value": 3500000, "quality": 2, "camp": 0}, {"score": 78750, "stage": 8, "type": 18, "value": 5250000, "quality": 3, "camp": 0}, {"score": 105000, "stage": 8, "type": 18, "value": 7000000, "quality": 4, "camp": 0}, {"score": 131250, "stage": 8, "type": 18, "value": 8750000, "quality": 5, "camp": 0}, {"score": 26250, "stage": 8, "type": 19, "value": 87500, "quality": 1, "camp": 0}, {"score": 52500, "stage": 8, "type": 19, "value": 175000, "quality": 2, "camp": 0}, {"score": 78750, "stage": 8, "type": 19, "value": 262500, "quality": 3, "camp": 0}, {"score": 105000, "stage": 8, "type": 19, "value": 350000, "quality": 4, "camp": 0}, {"score": 131250, "stage": 8, "type": 19, "value": 437500, "quality": 5, "camp": 0}, {"score": 26250, "stage": 8, "type": 20, "value": 87500, "quality": 1, "camp": 0}, {"score": 52500, "stage": 8, "type": 20, "value": 175000, "quality": 2, "camp": 0}, {"score": 78750, "stage": 8, "type": 20, "value": 262500, "quality": 3, "camp": 0}, {"score": 105000, "stage": 8, "type": 20, "value": 350000, "quality": 4, "camp": 0}, {"score": 131250, "stage": 8, "type": 20, "value": 437500, "quality": 5, "camp": 0}, {"score": 2812, "stage": 9, "type": 1, "value": 75000, "quality": 1, "camp": 1}, {"score": 5625, "stage": 9, "type": 1, "value": 150000, "quality": 2, "camp": 1}, {"score": 8437, "stage": 9, "type": 1, "value": 225000, "quality": 3, "camp": 1}, {"score": 11250, "stage": 9, "type": 1, "value": 300000, "quality": 4, "camp": 1}, {"score": 14062, "stage": 9, "type": 1, "value": 375000, "quality": 5, "camp": 1}, {"score": 2812, "stage": 9, "type": 2, "value": 750000, "quality": 1, "camp": 1}, {"score": 5625, "stage": 9, "type": 2, "value": 1500000, "quality": 2, "camp": 1}, {"score": 8437, "stage": 9, "type": 2, "value": 2250000, "quality": 3, "camp": 1}, {"score": 11250, "stage": 9, "type": 2, "value": 3000000, "quality": 4, "camp": 1}, {"score": 14062, "stage": 9, "type": 2, "value": 3750000, "quality": 5, "camp": 1}, {"score": 2812, "stage": 9, "type": 3, "value": 37500, "quality": 1, "camp": 1}, {"score": 5625, "stage": 9, "type": 3, "value": 75000, "quality": 2, "camp": 1}, {"score": 8437, "stage": 9, "type": 3, "value": 112500, "quality": 3, "camp": 1}, {"score": 11250, "stage": 9, "type": 3, "value": 150000, "quality": 4, "camp": 1}, {"score": 14062, "stage": 9, "type": 3, "value": 187500, "quality": 5, "camp": 1}, {"score": 2812, "stage": 9, "type": 4, "value": 37500, "quality": 1, "camp": 1}, {"score": 5625, "stage": 9, "type": 4, "value": 75000, "quality": 2, "camp": 1}, {"score": 8437, "stage": 9, "type": 4, "value": 112500, "quality": 3, "camp": 1}, {"score": 11250, "stage": 9, "type": 4, "value": 150000, "quality": 4, "camp": 1}, {"score": 14062, "stage": 9, "type": 4, "value": 187500, "quality": 5, "camp": 1}, {"score": 2812, "stage": 9, "type": 5, "value": 75000, "quality": 1, "camp": 2}, {"score": 5625, "stage": 9, "type": 5, "value": 150000, "quality": 2, "camp": 2}, {"score": 8437, "stage": 9, "type": 5, "value": 225000, "quality": 3, "camp": 2}, {"score": 11250, "stage": 9, "type": 5, "value": 300000, "quality": 4, "camp": 2}, {"score": 14062, "stage": 9, "type": 5, "value": 375000, "quality": 5, "camp": 2}, {"score": 2812, "stage": 9, "type": 6, "value": 750000, "quality": 1, "camp": 2}, {"score": 5625, "stage": 9, "type": 6, "value": 1500000, "quality": 2, "camp": 2}, {"score": 8437, "stage": 9, "type": 6, "value": 2250000, "quality": 3, "camp": 2}, {"score": 11250, "stage": 9, "type": 6, "value": 3000000, "quality": 4, "camp": 2}, {"score": 14062, "stage": 9, "type": 6, "value": 3750000, "quality": 5, "camp": 2}, {"score": 2812, "stage": 9, "type": 7, "value": 37500, "quality": 1, "camp": 2}, {"score": 5625, "stage": 9, "type": 7, "value": 75000, "quality": 2, "camp": 2}, {"score": 8437, "stage": 9, "type": 7, "value": 112500, "quality": 3, "camp": 2}, {"score": 11250, "stage": 9, "type": 7, "value": 150000, "quality": 4, "camp": 2}, {"score": 14062, "stage": 9, "type": 7, "value": 187500, "quality": 5, "camp": 2}, {"score": 2812, "stage": 9, "type": 8, "value": 37500, "quality": 1, "camp": 2}, {"score": 5625, "stage": 9, "type": 8, "value": 75000, "quality": 2, "camp": 2}, {"score": 8437, "stage": 9, "type": 8, "value": 112500, "quality": 3, "camp": 2}, {"score": 11250, "stage": 9, "type": 8, "value": 150000, "quality": 4, "camp": 2}, {"score": 14062, "stage": 9, "type": 8, "value": 187500, "quality": 5, "camp": 2}, {"score": 2812, "stage": 9, "type": 9, "value": 75000, "quality": 1, "camp": 3}, {"score": 5625, "stage": 9, "type": 9, "value": 150000, "quality": 2, "camp": 3}, {"score": 8437, "stage": 9, "type": 9, "value": 225000, "quality": 3, "camp": 3}, {"score": 11250, "stage": 9, "type": 9, "value": 300000, "quality": 4, "camp": 3}, {"score": 14062, "stage": 9, "type": 9, "value": 375000, "quality": 5, "camp": 3}, {"score": 2812, "stage": 9, "type": 10, "value": 750000, "quality": 1, "camp": 3}, {"score": 5625, "stage": 9, "type": 10, "value": 1500000, "quality": 2, "camp": 3}, {"score": 8437, "stage": 9, "type": 10, "value": 2250000, "quality": 3, "camp": 3}, {"score": 11250, "stage": 9, "type": 10, "value": 3000000, "quality": 4, "camp": 3}, {"score": 14062, "stage": 9, "type": 10, "value": 3750000, "quality": 5, "camp": 3}, {"score": 2812, "stage": 9, "type": 11, "value": 37500, "quality": 1, "camp": 3}, {"score": 5625, "stage": 9, "type": 11, "value": 75000, "quality": 2, "camp": 3}, {"score": 8437, "stage": 9, "type": 11, "value": 112500, "quality": 3, "camp": 3}, {"score": 11250, "stage": 9, "type": 11, "value": 150000, "quality": 4, "camp": 3}, {"score": 14062, "stage": 9, "type": 11, "value": 187500, "quality": 5, "camp": 3}, {"score": 2812, "stage": 9, "type": 12, "value": 37500, "quality": 1, "camp": 3}, {"score": 5625, "stage": 9, "type": 12, "value": 75000, "quality": 2, "camp": 3}, {"score": 8437, "stage": 9, "type": 12, "value": 112500, "quality": 3, "camp": 3}, {"score": 11250, "stage": 9, "type": 12, "value": 150000, "quality": 4, "camp": 3}, {"score": 14062, "stage": 9, "type": 12, "value": 187500, "quality": 5, "camp": 3}, {"score": 2812, "stage": 9, "type": 13, "value": 75000, "quality": 1, "camp": 4}, {"score": 5625, "stage": 9, "type": 13, "value": 150000, "quality": 2, "camp": 4}, {"score": 8437, "stage": 9, "type": 13, "value": 225000, "quality": 3, "camp": 4}, {"score": 11250, "stage": 9, "type": 13, "value": 300000, "quality": 4, "camp": 4}, {"score": 14062, "stage": 9, "type": 13, "value": 375000, "quality": 5, "camp": 4}, {"score": 2812, "stage": 9, "type": 14, "value": 750000, "quality": 1, "camp": 4}, {"score": 5625, "stage": 9, "type": 14, "value": 1500000, "quality": 2, "camp": 4}, {"score": 8437, "stage": 9, "type": 14, "value": 2250000, "quality": 3, "camp": 4}, {"score": 11250, "stage": 9, "type": 14, "value": 3000000, "quality": 4, "camp": 4}, {"score": 14062, "stage": 9, "type": 14, "value": 3750000, "quality": 5, "camp": 4}, {"score": 2812, "stage": 9, "type": 15, "value": 37500, "quality": 1, "camp": 4}, {"score": 5625, "stage": 9, "type": 15, "value": 75000, "quality": 2, "camp": 4}, {"score": 8437, "stage": 9, "type": 15, "value": 112500, "quality": 3, "camp": 4}, {"score": 11250, "stage": 9, "type": 15, "value": 150000, "quality": 4, "camp": 4}, {"score": 14062, "stage": 9, "type": 15, "value": 187500, "quality": 5, "camp": 4}, {"score": 2812, "stage": 9, "type": 16, "value": 37500, "quality": 1, "camp": 4}, {"score": 5625, "stage": 9, "type": 16, "value": 75000, "quality": 2, "camp": 4}, {"score": 8437, "stage": 9, "type": 16, "value": 112500, "quality": 3, "camp": 4}, {"score": 11250, "stage": 9, "type": 16, "value": 150000, "quality": 4, "camp": 4}, {"score": 14062, "stage": 9, "type": 16, "value": 187500, "quality": 5, "camp": 4}, {"score": 30000, "stage": 9, "type": 17, "value": 200000, "quality": 1, "camp": 0}, {"score": 60000, "stage": 9, "type": 17, "value": 400000, "quality": 2, "camp": 0}, {"score": 90000, "stage": 9, "type": 17, "value": 600000, "quality": 3, "camp": 0}, {"score": 120000, "stage": 9, "type": 17, "value": 800000, "quality": 4, "camp": 0}, {"score": 150000, "stage": 9, "type": 17, "value": 1000000, "quality": 5, "camp": 0}, {"score": 30000, "stage": 9, "type": 18, "value": 2000000, "quality": 1, "camp": 0}, {"score": 60000, "stage": 9, "type": 18, "value": 4000000, "quality": 2, "camp": 0}, {"score": 90000, "stage": 9, "type": 18, "value": 6000000, "quality": 3, "camp": 0}, {"score": 120000, "stage": 9, "type": 18, "value": 8000000, "quality": 4, "camp": 0}, {"score": 150000, "stage": 9, "type": 18, "value": 10000000, "quality": 5, "camp": 0}, {"score": 30000, "stage": 9, "type": 19, "value": 100000, "quality": 1, "camp": 0}, {"score": 60000, "stage": 9, "type": 19, "value": 200000, "quality": 2, "camp": 0}, {"score": 90000, "stage": 9, "type": 19, "value": 300000, "quality": 3, "camp": 0}, {"score": 120000, "stage": 9, "type": 19, "value": 400000, "quality": 4, "camp": 0}, {"score": 150000, "stage": 9, "type": 19, "value": 500000, "quality": 5, "camp": 0}, {"score": 30000, "stage": 9, "type": 20, "value": 100000, "quality": 1, "camp": 0}, {"score": 60000, "stage": 9, "type": 20, "value": 200000, "quality": 2, "camp": 0}, {"score": 90000, "stage": 9, "type": 20, "value": 300000, "quality": 3, "camp": 0}, {"score": 120000, "stage": 9, "type": 20, "value": 400000, "quality": 4, "camp": 0}, {"score": 150000, "stage": 9, "type": 20, "value": 500000, "quality": 5, "camp": 0}]