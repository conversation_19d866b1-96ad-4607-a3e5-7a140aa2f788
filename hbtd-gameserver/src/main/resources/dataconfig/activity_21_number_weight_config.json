[{"cost": [[11, 223307, 1]], "data": 2101, "numberWeight": [[1, 500], [2, 500], [3, 500], [4, 500], [5, 500], [6, 500], [7, 500], [8, 500], [9, 500]], "position": 1}, {"cost": "", "data": 2101, "numberWeight": [[0, 500], [1, 500], [2, 500], [3, 500], [4, 500], [5, 500], [6, 500], [7, 500], [8, 500], [9, 500]], "position": 2}, {"cost": "", "data": 2101, "numberWeight": [[0, 500], [1, 500], [2, 500], [3, 500], [4, 500], [5, 500], [6, 500], [7, 500], [8, 500], [9, 500]], "position": 3}, {"cost": "", "data": 2101, "numberWeight": [[0, 500], [1, 500], [2, 500], [3, 500], [4, 500], [5, 500], [6, 500], [7, 500], [8, 500], [9, 500]], "position": 4}, {"cost": "", "data": 2101, "numberWeight": [[0, 500], [1, 500], [2, 500], [3, 500], [4, 500], [5, 500], [6, 500], [7, 500], [8, 500], [9, 500]], "position": 5}, {"cost": [[11, 223307, 1]], "data": 2102, "numberWeight": [[1, 500], [2, 500], [3, 500], [4, 500], [5, 500], [6, 500], [7, 500], [8, 500], [9, 500]], "position": 1}, {"cost": "", "data": 2102, "numberWeight": [[0, 500], [1, 500], [2, 500], [3, 500], [4, 500], [5, 500], [6, 500], [7, 500], [8, 500], [9, 500]], "position": 2}, {"cost": "", "data": 2102, "numberWeight": [[0, 500], [1, 500], [2, 500], [3, 500], [4, 500], [5, 500], [6, 500], [7, 500], [8, 500], [9, 500]], "position": 3}, {"cost": "", "data": 2102, "numberWeight": [[0, 500], [1, 500], [2, 500], [3, 500], [4, 500], [5, 500], [6, 500], [7, 500], [8, 500], [9, 500]], "position": 4}, {"cost": "", "data": 2102, "numberWeight": [[0, 500], [1, 500], [2, 500], [3, 500], [4, 500], [5, 500], [6, 500], [7, 500], [8, 500], [9, 500]], "position": 5}, {"cost": [[11, 223307, 1]], "data": 2103, "numberWeight": [[1, 500], [2, 500], [3, 500], [4, 500], [5, 500], [6, 500], [7, 500], [8, 500], [9, 500]], "position": 1}, {"cost": "", "data": 2103, "numberWeight": [[0, 500], [1, 500], [2, 500], [3, 500], [4, 500], [5, 500], [6, 500], [7, 500], [8, 500], [9, 500]], "position": 2}, {"cost": "", "data": 2103, "numberWeight": [[0, 500], [1, 500], [2, 500], [3, 500], [4, 500], [5, 500], [6, 500], [7, 500], [8, 500], [9, 500]], "position": 3}, {"cost": "", "data": 2103, "numberWeight": [[0, 500], [1, 500], [2, 500], [3, 500], [4, 500], [5, 500], [6, 500], [7, 500], [8, 500], [9, 500]], "position": 4}, {"cost": "", "data": 2103, "numberWeight": [[0, 500], [1, 500], [2, 500], [3, 500], [4, 500], [5, 500], [6, 500], [7, 500], [8, 500], [9, 500]], "position": 5}, {"cost": [[11, 223307, 1]], "data": 2104, "numberWeight": [[1, 500], [2, 500], [3, 500], [4, 500], [5, 500], [6, 500], [7, 500], [8, 500], [9, 500]], "position": 1}, {"cost": "", "data": 2104, "numberWeight": [[0, 500], [1, 500], [2, 500], [3, 500], [4, 500], [5, 500], [6, 500], [7, 500], [8, 500], [9, 500]], "position": 2}, {"cost": "", "data": 2104, "numberWeight": [[0, 500], [1, 500], [2, 500], [3, 500], [4, 500], [5, 500], [6, 500], [7, 500], [8, 500], [9, 500]], "position": 3}, {"cost": "", "data": 2104, "numberWeight": [[0, 500], [1, 500], [2, 500], [3, 500], [4, 500], [5, 500], [6, 500], [7, 500], [8, 500], [9, 500]], "position": 4}, {"cost": "", "data": 2104, "numberWeight": [[0, 500], [1, 500], [2, 500], [3, 500], [4, 500], [5, 500], [6, 500], [7, 500], [8, 500], [9, 500]], "position": 5}, {"cost": [[11, 223307, 1]], "data": 2105, "numberWeight": [[1, 500], [2, 500], [3, 500], [4, 500], [5, 500], [6, 500], [7, 500], [8, 500], [9, 500]], "position": 1}, {"cost": "", "data": 2105, "numberWeight": [[0, 500], [1, 500], [2, 500], [3, 500], [4, 500], [5, 500], [6, 500], [7, 500], [8, 500], [9, 500]], "position": 2}, {"cost": "", "data": 2105, "numberWeight": [[0, 500], [1, 500], [2, 500], [3, 500], [4, 500], [5, 500], [6, 500], [7, 500], [8, 500], [9, 500]], "position": 3}, {"cost": "", "data": 2105, "numberWeight": [[0, 500], [1, 500], [2, 500], [3, 500], [4, 500], [5, 500], [6, 500], [7, 500], [8, 500], [9, 500]], "position": 4}, {"cost": "", "data": 2105, "numberWeight": [[0, 500], [1, 500], [2, 500], [3, 500], [4, 500], [5, 500], [6, 500], [7, 500], [8, 500], [9, 500]], "position": 5}, {"cost": [[11, 223307, 1]], "data": 2106, "numberWeight": [[1, 500], [2, 500], [3, 500], [4, 500], [5, 500], [6, 500], [7, 500], [8, 500], [9, 500]], "position": 1}, {"cost": "", "data": 2106, "numberWeight": [[0, 500], [1, 500], [2, 500], [3, 500], [4, 500], [5, 500], [6, 500], [7, 500], [8, 500], [9, 500]], "position": 2}, {"cost": "", "data": 2106, "numberWeight": [[0, 500], [1, 500], [2, 500], [3, 500], [4, 500], [5, 500], [6, 500], [7, 500], [8, 500], [9, 500]], "position": 3}, {"cost": "", "data": 2106, "numberWeight": [[0, 500], [1, 500], [2, 500], [3, 500], [4, 500], [5, 500], [6, 500], [7, 500], [8, 500], [9, 500]], "position": 4}, {"cost": "", "data": 2106, "numberWeight": [[0, 500], [1, 500], [2, 500], [3, 500], [4, 500], [5, 500], [6, 500], [7, 500], [8, 500], [9, 500]], "position": 5}, {"cost": [[11, 223307, 1]], "data": 2107, "numberWeight": [[1, 500], [2, 500], [3, 500], [4, 500], [5, 500], [6, 500], [7, 500], [8, 500], [9, 500]], "position": 1}, {"cost": "", "data": 2107, "numberWeight": [[0, 500], [1, 500], [2, 500], [3, 500], [4, 500], [5, 500], [6, 500], [7, 500], [8, 500], [9, 500]], "position": 2}, {"cost": "", "data": 2107, "numberWeight": [[0, 500], [1, 500], [2, 500], [3, 500], [4, 500], [5, 500], [6, 500], [7, 500], [8, 500], [9, 500]], "position": 3}, {"cost": "", "data": 2107, "numberWeight": [[0, 500], [1, 500], [2, 500], [3, 500], [4, 500], [5, 500], [6, 500], [7, 500], [8, 500], [9, 500]], "position": 4}, {"cost": "", "data": 2107, "numberWeight": [[0, 500], [1, 500], [2, 500], [3, 500], [4, 500], [5, 500], [6, 500], [7, 500], [8, 500], [9, 500]], "position": 5}, {"cost": [[11, 223307, 1]], "data": 2108, "numberWeight": [[1, 500], [2, 500], [3, 500], [4, 500], [5, 500], [6, 500], [7, 500], [8, 500], [9, 500]], "position": 1}, {"cost": "", "data": 2108, "numberWeight": [[0, 500], [1, 500], [2, 500], [3, 500], [4, 500], [5, 500], [6, 500], [7, 500], [8, 500], [9, 500]], "position": 2}, {"cost": "", "data": 2108, "numberWeight": [[0, 500], [1, 500], [2, 500], [3, 500], [4, 500], [5, 500], [6, 500], [7, 500], [8, 500], [9, 500]], "position": 3}, {"cost": "", "data": 2108, "numberWeight": [[0, 500], [1, 500], [2, 500], [3, 500], [4, 500], [5, 500], [6, 500], [7, 500], [8, 500], [9, 500]], "position": 4}, {"cost": "", "data": 2108, "numberWeight": [[0, 500], [1, 500], [2, 500], [3, 500], [4, 500], [5, 500], [6, 500], [7, 500], [8, 500], [9, 500]], "position": 5}]