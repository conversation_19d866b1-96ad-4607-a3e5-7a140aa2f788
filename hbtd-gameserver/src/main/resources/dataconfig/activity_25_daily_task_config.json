[{"data": 2501, "type": 1, "taskGroup": [120101, 120102, 120103, 120104]}, {"data": 2501, "type": 2, "taskGroup": [120201, 120202, 120203, 120301, 120302, 120303, 120304, 120305, 120306, 120307, 120308, 120309, 120310, 120401, 120402, 120403, 120501, 120502, 120503]}, {"data": 2502, "type": 1, "taskGroup": [121101, 121102, 121103, 121104]}, {"data": 2502, "type": 2, "taskGroup": [121201, 121202, 121203, 121301, 121302, 121303, 121304, 121305, 121306, 121307, 121308, 121309, 121310, 121401, 121402, 121403, 121501, 121502, 121503]}, {"data": 2503, "type": 1, "taskGroup": [122101, 122102, 122103, 122104]}, {"data": 2503, "type": 2, "taskGroup": [122201, 122202, 122203, 122301, 122302, 122303, 122304, 122305, 122306, 122307, 122308, 122309, 122310, 122401, 122402, 122403, 122404, 122405, 122406, 122501, 122502, 122503, 122504, 122505, 122506]}, {"data": 2504, "type": 1, "taskGroup": [122101, 122102, 122103, 122104]}, {"data": 2504, "type": 2, "taskGroup": [122201, 122202, 122203, 122301, 122302, 122303, 122304, 122305, 122306, 122307, 122308, 122309, 122310, 122401, 122402, 122403, 122404, 122405, 122406, 122501, 122502, 122503, 122504, 122505, 122506]}, {"data": 2505, "type": 1, "taskGroup": [122101, 122102, 122103, 122104]}, {"data": 2505, "type": 2, "taskGroup": [122201, 122202, 122203, 122301, 122302, 122303, 122304, 122305, 122306, 122307, 122308, 122309, 122310, 122401, 122402, 122403, 122404, 122405, 122406, 122501, 122502, 122503, 122504, 122505, 122506]}, {"data": 2506, "type": 1, "taskGroup": [122101, 122102, 122103, 122104]}, {"data": 2506, "type": 2, "taskGroup": [122201, 122202, 122203, 122301, 122302, 122303, 122304, 122305, 122306, 122307, 122308, 122309, 122310, 122401, 122402, 122403, 122404, 122405, 122406, 122501, 122502, 122503, 122504, 122505, 122506]}, {"data": 2507, "type": 1, "taskGroup": [122101, 122102, 122103, 122104]}, {"data": 2507, "type": 2, "taskGroup": [122201, 122202, 122203, 122301, 122302, 122303, 122304, 122305, 122306, 122307, 122308, 122309, 122310, 122401, 122402, 122403, 122404, 122405, 122406, 122501, 122502, 122503, 122504, 122505, 122506]}, {"data": 2509, "type": 1, "taskGroup": [123101, 123102, 123103, 123104]}, {"data": 2509, "type": 2, "taskGroup": [123201, 123202, 123203, 123301, 123302, 123303, 123304, 123305, 123306, 123307, 123308, 123309, 123310, 123401, 123402, 123403, 123404, 123405, 123406, 123501, 123502, 123503, 123504, 123505, 123506]}, {"data": 2510, "type": 1, "taskGroup": [124101, 124102, 124103, 124104]}, {"data": 2510, "type": 2, "taskGroup": [124201, 124202, 124203, 124301, 124302, 124303, 124304, 124305, 124306, 124307, 124308, 124309, 124310, 124401, 124402, 124403, 124404, 124405, 124406, 124501, 124502, 124503, 124504, 124505, 124506]}]