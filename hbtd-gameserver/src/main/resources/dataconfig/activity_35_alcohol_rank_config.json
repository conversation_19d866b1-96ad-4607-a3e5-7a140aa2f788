[{"data": 3501, "rank": 1, "type": 1, "rankRewards": [[12, 14010, 1], [11, 115402, 100]]}, {"data": 3501, "rank": 2, "type": 1, "rankRewards": [[12, 14010, 1], [11, 115402, 60]]}, {"data": 3501, "rank": 3, "type": 1, "rankRewards": [[12, 14010, 1], [11, 115402, 40]]}, {"data": 3501, "rank": 5, "type": 1, "rankRewards": [[11, 311410, 80], [11, 115402, 50]]}, {"data": 3501, "rank": 10, "type": 1, "rankRewards": [[11, 311410, 60], [11, 115302, 60]]}, {"data": 3501, "rank": 20, "type": 1, "rankRewards": [[11, 311410, 40], [11, 115302, 40]]}, {"data": 3501, "rank": 50, "type": 1, "rankRewards": [[11, 311410, 20], [11, 115302, 20]]}, {"data": 3501, "rank": 0, "type": 1, "rankRewards": [[11, 311410, 20], [11, 115302, 20]]}, {"data": 3501, "rank": 1, "type": 2, "rankRewards": [[11, 115402, 100], [11, 121301, 120]]}, {"data": 3501, "rank": 2, "type": 2, "rankRewards": [[11, 115402, 80], [11, 121301, 100]]}, {"data": 3501, "rank": 3, "type": 2, "rankRewards": [[11, 115402, 60], [11, 121301, 80]]}, {"data": 3501, "rank": 5, "type": 2, "rankRewards": [[11, 115402, 50], [11, 121301, 60]]}, {"data": 3501, "rank": 10, "type": 2, "rankRewards": [[11, 115402, 40], [11, 121301, 50]]}, {"data": 3501, "rank": 20, "type": 2, "rankRewards": [[11, 115402, 30], [11, 121301, 30]]}, {"data": 3501, "rank": 50, "type": 2, "rankRewards": [[11, 115402, 20], [11, 121301, 10]]}, {"data": 3501, "rank": 0, "type": 2, "rankRewards": [[11, 311410, 20], [11, 115302, 20]]}, {"data": 3504, "rank": 1, "type": 1, "rankRewards": [[11, 550115, 200], [11, 550116, 200]]}, {"data": 3504, "rank": 2, "type": 1, "rankRewards": [[11, 550115, 160], [11, 550116, 160]]}, {"data": 3504, "rank": 3, "type": 1, "rankRewards": [[11, 550115, 120], [11, 550116, 120]]}, {"data": 3504, "rank": 5, "type": 1, "rankRewards": [[11, 550115, 100], [11, 550116, 100]]}, {"data": 3504, "rank": 10, "type": 1, "rankRewards": [[11, 550115, 80], [11, 550116, 80]]}, {"data": 3504, "rank": 20, "type": 1, "rankRewards": [[11, 550115, 60], [11, 550116, 60]]}, {"data": 3504, "rank": 50, "type": 1, "rankRewards": [[11, 550115, 40], [11, 550116, 40]]}, {"data": 3504, "rank": 0, "type": 1, "rankRewards": [[11, 550115, 30], [11, 550116, 30]]}, {"data": 3504, "rank": 1, "type": 2, "rankRewards": [[11, 115402, 200], [11, 114101, 8000]]}, {"data": 3504, "rank": 2, "type": 2, "rankRewards": [[11, 115402, 150], [11, 114101, 6000]]}, {"data": 3504, "rank": 3, "type": 2, "rankRewards": [[11, 115402, 125], [11, 114101, 5000]]}, {"data": 3504, "rank": 5, "type": 2, "rankRewards": [[11, 115402, 100], [11, 114101, 4000]]}, {"data": 3504, "rank": 10, "type": 2, "rankRewards": [[11, 115402, 80], [11, 114101, 3200]]}, {"data": 3504, "rank": 20, "type": 2, "rankRewards": [[11, 115402, 70], [11, 114101, 2800]]}, {"data": 3504, "rank": 50, "type": 2, "rankRewards": [[11, 115402, 60], [11, 114101, 2400]]}, {"data": 3504, "rank": 0, "type": 2, "rankRewards": [[11, 115402, 50], [11, 114101, 2000]]}, {"data": 3505, "rank": 1, "type": 1, "rankRewards": [[11, 550120, 200], [11, 550116, 200]]}, {"data": 3505, "rank": 2, "type": 1, "rankRewards": [[11, 550120, 160], [11, 550116, 160]]}, {"data": 3505, "rank": 3, "type": 1, "rankRewards": [[11, 550120, 120], [11, 550116, 120]]}, {"data": 3505, "rank": 5, "type": 1, "rankRewards": [[11, 550120, 100], [11, 550116, 100]]}, {"data": 3505, "rank": 10, "type": 1, "rankRewards": [[11, 550120, 80], [11, 550116, 80]]}, {"data": 3505, "rank": 20, "type": 1, "rankRewards": [[11, 550120, 60], [11, 550116, 60]]}, {"data": 3505, "rank": 50, "type": 1, "rankRewards": [[11, 550120, 40], [11, 550116, 40]]}, {"data": 3505, "rank": 0, "type": 1, "rankRewards": [[11, 550120, 30], [11, 550116, 30]]}, {"data": 3505, "rank": 1, "type": 2, "rankRewards": [[11, 115402, 200], [11, 114101, 8000]]}, {"data": 3505, "rank": 2, "type": 2, "rankRewards": [[11, 115402, 150], [11, 114101, 6000]]}, {"data": 3505, "rank": 3, "type": 2, "rankRewards": [[11, 115402, 125], [11, 114101, 5000]]}, {"data": 3505, "rank": 5, "type": 2, "rankRewards": [[11, 115402, 100], [11, 114101, 4000]]}, {"data": 3505, "rank": 10, "type": 2, "rankRewards": [[11, 115402, 80], [11, 114101, 3200]]}, {"data": 3505, "rank": 20, "type": 2, "rankRewards": [[11, 115402, 70], [11, 114101, 2800]]}, {"data": 3505, "rank": 50, "type": 2, "rankRewards": [[11, 115402, 60], [11, 114101, 2400]]}, {"data": 3505, "rank": 0, "type": 2, "rankRewards": [[11, 115402, 50], [11, 114101, 2000]]}, {"data": 3506, "rank": 1, "type": 1, "rankRewards": [[11, 311408, 180], [11, 550116, 200], [11, 25107, 1]]}, {"data": 3506, "rank": 2, "type": 1, "rankRewards": [[11, 311408, 150], [11, 550116, 160], [11, 25107, 1]]}, {"data": 3506, "rank": 3, "type": 1, "rankRewards": [[11, 311408, 120], [11, 550116, 120], [11, 25107, 1]]}, {"data": 3506, "rank": 5, "type": 1, "rankRewards": [[11, 311408, 80], [11, 550116, 100]]}, {"data": 3506, "rank": 10, "type": 1, "rankRewards": [[11, 311408, 60], [11, 550116, 80]]}, {"data": 3506, "rank": 50, "type": 1, "rankRewards": [[11, 311408, 40], [11, 550116, 60]]}, {"data": 3506, "rank": 100, "type": 1, "rankRewards": [[11, 311408, 30], [11, 550116, 40]]}, {"data": 3506, "rank": 0, "type": 1, "rankRewards": [[11, 311408, 20], [11, 550116, 30]]}, {"data": 3506, "rank": 1, "type": 2, "rankRewards": [[11, 211401, 100], [11, 114101, 4000], [11, 25103, 1]]}, {"data": 3506, "rank": 2, "type": 2, "rankRewards": [[11, 211401, 90], [11, 114101, 3600], [11, 25103, 1]]}, {"data": 3506, "rank": 3, "type": 2, "rankRewards": [[11, 211401, 80], [11, 114101, 3200], [11, 25103, 1]]}, {"data": 3506, "rank": 5, "type": 2, "rankRewards": [[11, 211401, 60], [11, 114101, 2400]]}, {"data": 3506, "rank": 10, "type": 2, "rankRewards": [[11, 211401, 50], [11, 114101, 2000]]}, {"data": 3506, "rank": 50, "type": 2, "rankRewards": [[11, 211401, 40], [11, 114101, 1600]]}, {"data": 3506, "rank": 100, "type": 2, "rankRewards": [[11, 211401, 30], [11, 114101, 1200]]}, {"data": 3506, "rank": 0, "type": 2, "rankRewards": [[11, 211401, 20], [11, 114101, 800]]}, {"data": 3511, "rank": 1, "type": 1, "rankRewards": []}, {"data": 3511, "rank": 2, "type": 1, "rankRewards": []}, {"data": 3511, "rank": 3, "type": 1, "rankRewards": []}, {"data": 3511, "rank": 5, "type": 1, "rankRewards": []}, {"data": 3511, "rank": 10, "type": 1, "rankRewards": []}, {"data": 3511, "rank": 20, "type": 1, "rankRewards": []}, {"data": 3511, "rank": 50, "type": 1, "rankRewards": []}, {"data": 3511, "rank": 0, "type": 1, "rankRewards": []}, {"data": 3511, "rank": 1, "type": 2, "rankRewards": []}, {"data": 3511, "rank": 2, "type": 2, "rankRewards": []}, {"data": 3511, "rank": 3, "type": 2, "rankRewards": []}, {"data": 3511, "rank": 5, "type": 2, "rankRewards": []}, {"data": 3511, "rank": 10, "type": 2, "rankRewards": []}, {"data": 3511, "rank": 20, "type": 2, "rankRewards": []}, {"data": 3511, "rank": 50, "type": 2, "rankRewards": []}, {"data": 3511, "rank": 0, "type": 2, "rankRewards": []}, {"data": 3512, "rank": 1, "type": 1, "rankRewards": []}, {"data": 3512, "rank": 2, "type": 1, "rankRewards": []}, {"data": 3512, "rank": 3, "type": 1, "rankRewards": []}, {"data": 3512, "rank": 5, "type": 1, "rankRewards": []}, {"data": 3512, "rank": 10, "type": 1, "rankRewards": []}, {"data": 3512, "rank": 50, "type": 1, "rankRewards": []}, {"data": 3512, "rank": 100, "type": 1, "rankRewards": []}, {"data": 3512, "rank": 0, "type": 1, "rankRewards": []}, {"data": 3512, "rank": 1, "type": 2, "rankRewards": []}, {"data": 3512, "rank": 2, "type": 2, "rankRewards": []}, {"data": 3512, "rank": 3, "type": 2, "rankRewards": []}, {"data": 3512, "rank": 5, "type": 2, "rankRewards": []}, {"data": 3512, "rank": 10, "type": 2, "rankRewards": []}, {"data": 3512, "rank": 50, "type": 2, "rankRewards": []}, {"data": 3512, "rank": 100, "type": 2, "rankRewards": []}, {"data": 3512, "rank": 0, "type": 2, "rankRewards": []}, {"data": 3513, "rank": 1, "type": 1, "rankRewards": []}, {"data": 3513, "rank": 2, "type": 1, "rankRewards": []}, {"data": 3513, "rank": 3, "type": 1, "rankRewards": []}, {"data": 3513, "rank": 5, "type": 1, "rankRewards": []}, {"data": 3513, "rank": 10, "type": 1, "rankRewards": []}, {"data": 3513, "rank": 20, "type": 1, "rankRewards": []}, {"data": 3513, "rank": 50, "type": 1, "rankRewards": []}, {"data": 3513, "rank": 0, "type": 1, "rankRewards": []}, {"data": 3513, "rank": 1, "type": 2, "rankRewards": []}, {"data": 3513, "rank": 2, "type": 2, "rankRewards": []}, {"data": 3513, "rank": 3, "type": 2, "rankRewards": []}, {"data": 3513, "rank": 5, "type": 2, "rankRewards": []}, {"data": 3513, "rank": 10, "type": 2, "rankRewards": []}, {"data": 3513, "rank": 20, "type": 2, "rankRewards": []}, {"data": 3513, "rank": 50, "type": 2, "rankRewards": []}, {"data": 3513, "rank": 0, "type": 2, "rankRewards": []}]