[{"limitAttribute": [[1, 450], [2, 900], [3, 1800], [4, 450]], "beautyId": 64001, "typicalCoefficient": [[1, 300], [2, 3000], [3, 180], [4, 150]], "feature": 3, "name": "#name1001", "scoreExpr": "(600+3000*Math.pow(x3,2)+3000*x3+x1/4*6+x2*10000)*(100+x2)/100", "dimensionAttribute": [[1, 5], [2, 10], [3, 20], [4, 5]], "exclusiveProps": [661304, 661305, 661306], "fragmentCount": [[11, 371101, 30]], "quality": 4}, {"limitAttribute": [[1, 450], [2, 900], [3, 450], [4, 1800]], "beautyId": 64002, "typicalCoefficient": [[1, 300], [2, 3000], [3, 150], [4, 180]], "feature": 4, "name": "#name1002", "scoreExpr": "(1200+3000*Math.pow(x3,2)+3000*x3+x1/4*6+x2*10000)*(100+x2)/100", "dimensionAttribute": [[1, 10], [2, 20], [3, 10], [4, 40]], "exclusiveProps": [661307, 661308, 661309], "fragmentCount": [[11, 371102, 30]], "quality": 4}, {"limitAttribute": [[1, 1800], [2, 900], [3, 450], [4, 450]], "beautyId": 64003, "typicalCoefficient": [[1, 360], [2, 3000], [3, 150], [4, 150]], "feature": 1, "name": "#name1003", "scoreExpr": "(2400+3000*Math.pow(x3,2)+3000*x3+x1/4*6+x2*10000)*(100+x2)/100", "dimensionAttribute": [[1, 80], [2, 40], [3, 20], [4, 20]], "exclusiveProps": [661310, 661311, 661312], "fragmentCount": [[11, 371103, 30]], "quality": 4}, {"limitAttribute": [[1, 900], [2, 1800], [3, 450], [4, 450]], "beautyId": 64004, "typicalCoefficient": [[1, 300], [2, 3600], [3, 150], [4, 150]], "feature": 2, "name": "#name1004", "scoreExpr": "(2400+3000*Math.pow(x3,2)+3000*x3+x1/4*6+x2*10000)*(100+x2)/100", "dimensionAttribute": [[1, 20], [2, 80], [3, 20], [4, 40]], "exclusiveProps": [661313, 661314, 661315], "fragmentCount": [[11, 371104, 30]], "quality": 4}, {"limitAttribute": [[1, 450], [2, 900], [3, 1800], [4, 450]], "beautyId": 64005, "typicalCoefficient": [[1, 300], [2, 3000], [3, 180], [4, 150]], "feature": 3, "name": "#name1005", "scoreExpr": "(2400+3000*Math.pow(x3,2)+3000*x3+x1/4*6+x2*10000)*(100+x2)/100", "dimensionAttribute": [[1, 20], [2, 40], [3, 80], [4, 20]], "exclusiveProps": [661316, 661317, 661318], "fragmentCount": [[11, 371105, 30]], "quality": 4}]