[{"attributes": [[1, 20]], "type": 1, "quality": 1, "goldExpr": "200+(50+x1)*x1"}, {"attributes": [[1, 30]], "type": 1, "quality": 2, "goldExpr": "200+(50+x1)*x1"}, {"attributes": [[1, 100]], "type": 1, "quality": 3, "goldExpr": "300+(100+3*x1)*x1"}, {"attributes": [[1, 300]], "type": 1, "quality": 4, "goldExpr": "450+(200+6*x1)*x1"}, {"attributes": [[1, 500]], "type": 1, "quality": 5, "goldExpr": "750+(400+10*x1)*x1"}, {"attributes": [[1, 600]], "type": 1, "quality": 6, "goldExpr": "750+(400+10*x1)*x1"}, {"attributes": [[2, 200]], "type": 2, "quality": 1, "goldExpr": "200+(50+x1)*x1"}, {"attributes": [[2, 300]], "type": 2, "quality": 2, "goldExpr": "200+(50+x1)*x1"}, {"attributes": [[2, 1000]], "type": 2, "quality": 3, "goldExpr": "300+(100+3*x1)*x1"}, {"attributes": [[2, 3000]], "type": 2, "quality": 4, "goldExpr": "450+(200+6*x1)*x1"}, {"attributes": [[2, 5000]], "type": 2, "quality": 5, "goldExpr": "750+(400+10*x1)*x1"}, {"attributes": [[2, 6000]], "type": 2, "quality": 6, "goldExpr": "750+(400+10*x1)*x1"}, {"attributes": [[3, 10]], "type": 3, "quality": 1, "goldExpr": "200+(50+x1)*x1"}, {"attributes": [[3, 15]], "type": 3, "quality": 2, "goldExpr": "200+(50+x1)*x1"}, {"attributes": [[3, 50]], "type": 3, "quality": 3, "goldExpr": "300+(100+3*x1)*x1"}, {"attributes": [[3, 150]], "type": 3, "quality": 4, "goldExpr": "450+(200+6*x1)*x1"}, {"attributes": [[3, 250]], "type": 3, "quality": 5, "goldExpr": "750+(400+10*x1)*x1"}, {"attributes": [[3, 300]], "type": 3, "quality": 6, "goldExpr": "750+(400+10*x1)*x1"}, {"attributes": [[4, 10]], "type": 4, "quality": 1, "goldExpr": "200+(50+x1)*x1"}, {"attributes": [[4, 15]], "type": 4, "quality": 2, "goldExpr": "200+(50+x1)*x1"}, {"attributes": [[4, 50]], "type": 4, "quality": 3, "goldExpr": "300+(100+3*x1)*x1"}, {"attributes": [[4, 150]], "type": 4, "quality": 4, "goldExpr": "450+(200+6*x1)*x1"}, {"attributes": [[4, 250]], "type": 4, "quality": 5, "goldExpr": "750+(400+10*x1)*x1"}, {"attributes": [[4, 300]], "type": 4, "quality": 6, "goldExpr": "750+(400+10*x1)*x1"}]