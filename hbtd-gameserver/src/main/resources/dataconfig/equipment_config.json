[{"equipId": 20011, "baseEffect": [[1, 200]], "decomposeRewards": "", "groupId": 1, "fragmentId": 320011, "name": "<PERSON><PERSON>", "scoreExpr": "(0+20+1*x1+60*x2+(11.054*Math.pow(x3,3)+104.71*Math.pow(x3,2)+190.35*x3)/4*0.9+0+0+(0.0341*Math.pow(Math.floor(x1/10),3)-Math.pow(Math.floor(x1/10),2)+17*Math.floor(x1/10))*6+(0.1885*Math.pow(Math.floor(x2/5),3)-4.6262*Math.pow(Math.floor(x2/5),2)+109.98*Math.floor(x2/5))*6+(23.866*Math.pow(x3,3)-60.435*Math.pow(x3,2)+564.34*x3)/24+0)*(100+0.25*x2+0+(x3+((x3>3)?(x3-3):0)+((x3>6)?(x3-6):0))*6)/100*(100+0)/100", "aptitude": 6, "type": 1, "fragmentCount": 10, "quality": 1, "initScoreExpr": 21}, {"equipId": 20021, "baseEffect": [[2, 2000]], "decomposeRewards": "", "groupId": 1, "fragmentId": 320021, "name": "<PERSON><PERSON><PERSON><PERSON>", "scoreExpr": "(0+20+1*x1+60*x2+(11.054*Math.pow(x3,3)+104.71*Math.pow(x3,2)+190.35*x3)/4*0.9+0+0+(0.0341*Math.pow(Math.floor(x1/10),3)-Math.pow(Math.floor(x1/10),2)+17*Math.floor(x1/10))*6+(0.1885*Math.pow(Math.floor(x2/5),3)-4.6262*Math.pow(Math.floor(x2/5),2)+109.98*Math.floor(x2/5))*6+(23.866*Math.pow(x3,3)-60.435*Math.pow(x3,2)+564.34*x3)/24+0)*(100+0.25*x2+0+(x3+((x3>3)?(x3-3):0)+((x3>6)?(x3-6):0))*6)/100*(100+0)/100", "aptitude": 6, "type": 2, "fragmentCount": 10, "quality": 1, "initScoreExpr": 21}, {"equipId": 20031, "baseEffect": [[3, 100]], "decomposeRewards": "", "groupId": 1, "fragmentId": 320031, "name": "<PERSON><PERSON>", "scoreExpr": "(0+20+1*x1+60*x2+(11.054*Math.pow(x3,3)+104.71*Math.pow(x3,2)+190.35*x3)/4*0.9+0+0+(0.0341*Math.pow(Math.floor(x1/10),3)-Math.pow(Math.floor(x1/10),2)+17*Math.floor(x1/10))*6+(0.1885*Math.pow(Math.floor(x2/5),3)-4.6262*Math.pow(Math.floor(x2/5),2)+109.98*Math.floor(x2/5))*6+(23.866*Math.pow(x3,3)-60.435*Math.pow(x3,2)+564.34*x3)/24+0)*(100+0.25*x2+0+(x3+((x3>3)?(x3-3):0)+((x3>6)?(x3-6):0))*6)/100*(100+0)/100", "aptitude": 6, "type": 3, "fragmentCount": 10, "quality": 1, "initScoreExpr": 21}, {"equipId": 20041, "baseEffect": [[4, 100]], "decomposeRewards": "", "groupId": 1, "fragmentId": 320041, "name": "<PERSON><PERSON><PERSON><PERSON>", "scoreExpr": "(0+20+1*x1+60*x2+(11.054*Math.pow(x3,3)+104.71*Math.pow(x3,2)+190.35*x3)/4*0.9+0+0+(0.0341*Math.pow(Math.floor(x1/10),3)-Math.pow(Math.floor(x1/10),2)+17*Math.floor(x1/10))*6+(0.1885*Math.pow(Math.floor(x2/5),3)-4.6262*Math.pow(Math.floor(x2/5),2)+109.98*Math.floor(x2/5))*6+(23.866*Math.pow(x3,3)-60.435*Math.pow(x3,2)+564.34*x3)/24+0)*(100+0.25*x2+0+(x3+((x3>3)?(x3-3):0)+((x3>6)?(x3-6):0))*6)/100*(100+0)/100", "aptitude": 6, "type": 4, "fragmentCount": 10, "quality": 1, "initScoreExpr": 21}, {"equipId": 21111, "baseEffect": [[1, 600]], "decomposeRewards": "", "groupId": 11, "fragmentId": 321111, "name": "<PERSON><PERSON><PERSON>", "scoreExpr": "(500+60+3*x1+60*x2+(11.054*Math.pow(x3,3)+104.71*Math.pow(x3,2)+190.35*x3)/4*0.95+0+15+(0.0341*Math.pow(Math.floor(x1/10),3)-Math.pow(Math.floor(x1/10),2)+17*Math.floor(x1/10))*6+(0.1885*Math.pow(Math.floor(x2/5),3)-4.6262*Math.pow(Math.floor(x2/5),2)+109.98*Math.floor(x2/5))*6+(23.866*Math.pow(x3,3)-60.435*Math.pow(x3,2)+564.34*x3)/24+0)*(100+0.25*x2+1+(x3+((x3>3)?(x3-3):0)+((x3>6)?(x3-6):0))*6)/100*(100+2)/100", "aptitude": 8, "type": 1, "fragmentCount": 20, "quality": 2, "initScoreExpr": 596}, {"equipId": 21121, "baseEffect": [[2, 6000]], "decomposeRewards": "", "groupId": 11, "fragmentId": 321121, "name": "<PERSON><PERSON><PERSON><PERSON>", "scoreExpr": "(500+60+3*x1+60*x2+(11.054*Math.pow(x3,3)+104.71*Math.pow(x3,2)+190.35*x3)/4*0.95+0+15+(0.0341*Math.pow(Math.floor(x1/10),3)-Math.pow(Math.floor(x1/10),2)+17*Math.floor(x1/10))*6+(0.1885*Math.pow(Math.floor(x2/5),3)-4.6262*Math.pow(Math.floor(x2/5),2)+109.98*Math.floor(x2/5))*6+(23.866*Math.pow(x3,3)-60.435*Math.pow(x3,2)+564.34*x3)/24+0)*(100+0.25*x2+1+(x3+((x3>3)?(x3-3):0)+((x3>6)?(x3-6):0))*6)/100*(100+2)/100", "aptitude": 8, "type": 2, "fragmentCount": 20, "quality": 2, "initScoreExpr": 596}, {"equipId": 21131, "baseEffect": [[3, 300]], "decomposeRewards": "", "groupId": 11, "fragmentId": 321131, "name": "<PERSON><PERSON>", "scoreExpr": "(500+60+3*x1+60*x2+(11.054*Math.pow(x3,3)+104.71*Math.pow(x3,2)+190.35*x3)/4*0.95+0+15+(0.0341*Math.pow(Math.floor(x1/10),3)-Math.pow(Math.floor(x1/10),2)+17*Math.floor(x1/10))*6+(0.1885*Math.pow(Math.floor(x2/5),3)-4.6262*Math.pow(Math.floor(x2/5),2)+109.98*Math.floor(x2/5))*6+(23.866*Math.pow(x3,3)-60.435*Math.pow(x3,2)+564.34*x3)/24+0)*(100+0.25*x2+1+(x3+((x3>3)?(x3-3):0)+((x3>6)?(x3-6):0))*6)/100*(100+2)/100", "aptitude": 8, "type": 3, "fragmentCount": 20, "quality": 2, "initScoreExpr": 596}, {"equipId": 21141, "baseEffect": [[4, 300]], "decomposeRewards": "", "groupId": 11, "fragmentId": 321141, "name": "<PERSON><PERSON><PERSON><PERSON>", "scoreExpr": "(500+60+3*x1+60*x2+(11.054*Math.pow(x3,3)+104.71*Math.pow(x3,2)+190.35*x3)/4*0.95+0+15+(0.0341*Math.pow(Math.floor(x1/10),3)-Math.pow(Math.floor(x1/10),2)+17*Math.floor(x1/10))*6+(0.1885*Math.pow(Math.floor(x2/5),3)-4.6262*Math.pow(Math.floor(x2/5),2)+109.98*Math.floor(x2/5))*6+(23.866*Math.pow(x3,3)-60.435*Math.pow(x3,2)+564.34*x3)/24+0)*(100+0.25*x2+1+(x3+((x3>3)?(x3-3):0)+((x3>6)?(x3-6):0))*6)/100*(100+2)/100", "aptitude": 8, "type": 4, "fragmentCount": 20, "quality": 2, "initScoreExpr": 596}, {"equipId": 22111, "baseEffect": [[1, 600]], "decomposeRewards": "", "groupId": 21, "fragmentId": 322111, "name": "<PERSON><PERSON><PERSON>", "scoreExpr": "(500+60+3*x1+60*x2+(11.054*Math.pow(x3,3)+104.71*Math.pow(x3,2)+190.35*x3)/4*0.95+0+15+(0.0341*Math.pow(Math.floor(x1/10),3)-Math.pow(Math.floor(x1/10),2)+17*Math.floor(x1/10))*6+(0.1885*Math.pow(Math.floor(x2/5),3)-4.6262*Math.pow(Math.floor(x2/5),2)+109.98*Math.floor(x2/5))*6+(23.866*Math.pow(x3,3)-60.435*Math.pow(x3,2)+564.34*x3)/24+0)*(100+0.25*x2+1+(x3+((x3>3)?(x3-3):0)+((x3>6)?(x3-6):0))*6)/100*(100+2)/100", "aptitude": 8, "type": 1, "fragmentCount": 20, "quality": 2, "initScoreExpr": 596}, {"equipId": 22121, "baseEffect": [[2, 6000]], "decomposeRewards": "", "groupId": 21, "fragmentId": 322121, "name": "<PERSON><PERSON><PERSON><PERSON>", "scoreExpr": "(500+60+3*x1+60*x2+(11.054*Math.pow(x3,3)+104.71*Math.pow(x3,2)+190.35*x3)/4*0.95+0+15+(0.0341*Math.pow(Math.floor(x1/10),3)-Math.pow(Math.floor(x1/10),2)+17*Math.floor(x1/10))*6+(0.1885*Math.pow(Math.floor(x2/5),3)-4.6262*Math.pow(Math.floor(x2/5),2)+109.98*Math.floor(x2/5))*6+(23.866*Math.pow(x3,3)-60.435*Math.pow(x3,2)+564.34*x3)/24+0)*(100+0.25*x2+1+(x3+((x3>3)?(x3-3):0)+((x3>6)?(x3-6):0))*6)/100*(100+2)/100", "aptitude": 8, "type": 2, "fragmentCount": 20, "quality": 2, "initScoreExpr": 596}, {"equipId": 22131, "baseEffect": [[3, 300]], "decomposeRewards": "", "groupId": 21, "fragmentId": 322131, "name": "<PERSON><PERSON>", "scoreExpr": "(500+60+3*x1+60*x2+(11.054*Math.pow(x3,3)+104.71*Math.pow(x3,2)+190.35*x3)/4*0.95+0+15+(0.0341*Math.pow(Math.floor(x1/10),3)-Math.pow(Math.floor(x1/10),2)+17*Math.floor(x1/10))*6+(0.1885*Math.pow(Math.floor(x2/5),3)-4.6262*Math.pow(Math.floor(x2/5),2)+109.98*Math.floor(x2/5))*6+(23.866*Math.pow(x3,3)-60.435*Math.pow(x3,2)+564.34*x3)/24+0)*(100+0.25*x2+1+(x3+((x3>3)?(x3-3):0)+((x3>6)?(x3-6):0))*6)/100*(100+2)/100", "aptitude": 8, "type": 3, "fragmentCount": 20, "quality": 2, "initScoreExpr": 596}, {"equipId": 22141, "baseEffect": [[4, 300]], "decomposeRewards": "", "groupId": 21, "fragmentId": 322141, "name": "<PERSON><PERSON><PERSON><PERSON>", "scoreExpr": "(500+60+3*x1+60*x2+(11.054*Math.pow(x3,3)+104.71*Math.pow(x3,2)+190.35*x3)/4*0.95+0+15+(0.0341*Math.pow(Math.floor(x1/10),3)-Math.pow(Math.floor(x1/10),2)+17*Math.floor(x1/10))*6+(0.1885*Math.pow(Math.floor(x2/5),3)-4.6262*Math.pow(Math.floor(x2/5),2)+109.98*Math.floor(x2/5))*6+(23.866*Math.pow(x3,3)-60.435*Math.pow(x3,2)+564.34*x3)/24+0)*(100+0.25*x2+1+(x3+((x3>3)?(x3-3):0)+((x3>6)?(x3-6):0))*6)/100*(100+2)/100", "aptitude": 8, "type": 4, "fragmentCount": 20, "quality": 2, "initScoreExpr": 596}, {"equipId": 21211, "baseEffect": [[1, 2000]], "decomposeRewards": "", "groupId": 12, "fragmentId": 321211, "name": "Thương B<PERSON>", "scoreExpr": "(1000+200+10*x1+200*x2+(11.054*Math.pow(x3,3)+104.71*Math.pow(x3,2)+190.35*x3)/4*1+0+20+(0.0341*Math.pow(Math.floor(x1/10),3)-Math.pow(Math.floor(x1/10),2)+17*Math.floor(x1/10))*6+(0.1885*Math.pow(Math.floor(x2/5),3)-4.6262*Math.pow(Math.floor(x2/5),2)+109.98*Math.floor(x2/5))*6+(23.866*Math.pow(x3,3)-60.435*Math.pow(x3,2)+564.34*x3)/24+0)*(100+0.25*x2+1.25+(x3+((x3>3)?(x3-3):0)+((x3>6)?(x3-6):0))*6)/100*(100+5)/100", "aptitude": 10, "type": 1, "fragmentCount": 30, "quality": 3, "initScoreExpr": 1308}, {"equipId": 21221, "baseEffect": [[2, 20000]], "decomposeRewards": "", "groupId": 12, "fragmentId": 321221, "name": "Giáp <PERSON>", "scoreExpr": "(1000+200+10*x1+200*x2+(11.054*Math.pow(x3,3)+104.71*Math.pow(x3,2)+190.35*x3)/4*1+0+20+(0.0341*Math.pow(Math.floor(x1/10),3)-Math.pow(Math.floor(x1/10),2)+17*Math.floor(x1/10))*6+(0.1885*Math.pow(Math.floor(x2/5),3)-4.6262*Math.pow(Math.floor(x2/5),2)+109.98*Math.floor(x2/5))*6+(23.866*Math.pow(x3,3)-60.435*Math.pow(x3,2)+564.34*x3)/24+0)*(100+0.25*x2+1.25+(x3+((x3>3)?(x3-3):0)+((x3>6)?(x3-6):0))*6)/100*(100+5)/100", "aptitude": 10, "type": 2, "fragmentCount": 30, "quality": 3, "initScoreExpr": 1308}, {"equipId": 21231, "baseEffect": [[3, 1000]], "decomposeRewards": "", "groupId": 12, "fragmentId": 321231, "name": "<PERSON>ũ <PERSON>", "scoreExpr": "(1000+200+10*x1+200*x2+(11.054*Math.pow(x3,3)+104.71*Math.pow(x3,2)+190.35*x3)/4*1+0+20+(0.0341*Math.pow(Math.floor(x1/10),3)-Math.pow(Math.floor(x1/10),2)+17*Math.floor(x1/10))*6+(0.1885*Math.pow(Math.floor(x2/5),3)-4.6262*Math.pow(Math.floor(x2/5),2)+109.98*Math.floor(x2/5))*6+(23.866*Math.pow(x3,3)-60.435*Math.pow(x3,2)+564.34*x3)/24+0)*(100+0.25*x2+1.25+(x3+((x3>3)?(x3-3):0)+((x3>6)?(x3-6):0))*6)/100*(100+5)/100", "aptitude": 10, "type": 3, "fragmentCount": 30, "quality": 3, "initScoreExpr": 1308}, {"equipId": 21241, "baseEffect": [[4, 1000]], "decomposeRewards": "", "groupId": 12, "fragmentId": 321241, "name": "Giày <PERSON>", "scoreExpr": "(1000+200+10*x1+200*x2+(11.054*Math.pow(x3,3)+104.71*Math.pow(x3,2)+190.35*x3)/4*1+0+20+(0.0341*Math.pow(Math.floor(x1/10),3)-Math.pow(Math.floor(x1/10),2)+17*Math.floor(x1/10))*6+(0.1885*Math.pow(Math.floor(x2/5),3)-4.6262*Math.pow(Math.floor(x2/5),2)+109.98*Math.floor(x2/5))*6+(23.866*Math.pow(x3,3)-60.435*Math.pow(x3,2)+564.34*x3)/24+0)*(100+0.25*x2+1.25+(x3+((x3>3)?(x3-3):0)+((x3>6)?(x3-6):0))*6)/100*(100+5)/100", "aptitude": 10, "type": 4, "fragmentCount": 30, "quality": 3, "initScoreExpr": 1308}, {"equipId": 22211, "baseEffect": [[1, 2000]], "decomposeRewards": "", "groupId": 22, "fragmentId": 322211, "name": "<PERSON><PERSON><PERSON>", "scoreExpr": "(1000+200+10*x1+200*x2+(11.054*Math.pow(x3,3)+104.71*Math.pow(x3,2)+190.35*x3)/4*1+0+20+(0.0341*Math.pow(Math.floor(x1/10),3)-Math.pow(Math.floor(x1/10),2)+17*Math.floor(x1/10))*6+(0.1885*Math.pow(Math.floor(x2/5),3)-4.6262*Math.pow(Math.floor(x2/5),2)+109.98*Math.floor(x2/5))*6+(23.866*Math.pow(x3,3)-60.435*Math.pow(x3,2)+564.34*x3)/24+0)*(100+0.25*x2+1.25+(x3+((x3>3)?(x3-3):0)+((x3>6)?(x3-6):0))*6)/100*(100+5)/100", "aptitude": 10, "type": 1, "fragmentCount": 30, "quality": 3, "initScoreExpr": 1308}, {"equipId": 22221, "baseEffect": [[2, 20000]], "decomposeRewards": "", "groupId": 22, "fragmentId": 322221, "name": "<PERSON><PERSON><PERSON><PERSON>", "scoreExpr": "(1000+200+10*x1+200*x2+(11.054*Math.pow(x3,3)+104.71*Math.pow(x3,2)+190.35*x3)/4*1+0+20+(0.0341*Math.pow(Math.floor(x1/10),3)-Math.pow(Math.floor(x1/10),2)+17*Math.floor(x1/10))*6+(0.1885*Math.pow(Math.floor(x2/5),3)-4.6262*Math.pow(Math.floor(x2/5),2)+109.98*Math.floor(x2/5))*6+(23.866*Math.pow(x3,3)-60.435*Math.pow(x3,2)+564.34*x3)/24+0)*(100+0.25*x2+1.25+(x3+((x3>3)?(x3-3):0)+((x3>6)?(x3-6):0))*6)/100*(100+5)/100", "aptitude": 10, "type": 2, "fragmentCount": 30, "quality": 3, "initScoreExpr": 1308}, {"equipId": 22231, "baseEffect": [[3, 1000]], "decomposeRewards": "", "groupId": 22, "fragmentId": 322231, "name": "<PERSON><PERSON>", "scoreExpr": "(1000+200+10*x1+200*x2+(11.054*Math.pow(x3,3)+104.71*Math.pow(x3,2)+190.35*x3)/4*1+0+20+(0.0341*Math.pow(Math.floor(x1/10),3)-Math.pow(Math.floor(x1/10),2)+17*Math.floor(x1/10))*6+(0.1885*Math.pow(Math.floor(x2/5),3)-4.6262*Math.pow(Math.floor(x2/5),2)+109.98*Math.floor(x2/5))*6+(23.866*Math.pow(x3,3)-60.435*Math.pow(x3,2)+564.34*x3)/24+0)*(100+0.25*x2+1.25+(x3+((x3>3)?(x3-3):0)+((x3>6)?(x3-6):0))*6)/100*(100+5)/100", "aptitude": 10, "type": 3, "fragmentCount": 30, "quality": 3, "initScoreExpr": 1308}, {"equipId": 22241, "baseEffect": [[4, 1000]], "decomposeRewards": "", "groupId": 22, "fragmentId": 322241, "name": "<PERSON><PERSON><PERSON><PERSON>", "scoreExpr": "(1000+200+10*x1+200*x2+(11.054*Math.pow(x3,3)+104.71*Math.pow(x3,2)+190.35*x3)/4*1+0+20+(0.0341*Math.pow(Math.floor(x1/10),3)-Math.pow(Math.floor(x1/10),2)+17*Math.floor(x1/10))*6+(0.1885*Math.pow(Math.floor(x2/5),3)-4.6262*Math.pow(Math.floor(x2/5),2)+109.98*Math.floor(x2/5))*6+(23.866*Math.pow(x3,3)-60.435*Math.pow(x3,2)+564.34*x3)/24+0)*(100+0.25*x2+1.25+(x3+((x3>3)?(x3-3):0)+((x3>6)?(x3-6):0))*6)/100*(100+5)/100", "aptitude": 10, "type": 4, "fragmentCount": 30, "quality": 3, "initScoreExpr": 1308}, {"equipId": 21311, "baseEffect": [[1, 6000]], "decomposeRewards": "", "groupId": 13, "fragmentId": 321311, "name": "<PERSON><PERSON><PERSON>", "scoreExpr": "(2000+600+30*x1+600*x2+(11.054*Math.pow(x3,3)+104.71*Math.pow(x3,2)+190.35*x3)/4*1.05+75*x4+40+(0.0341*Math.pow(Math.floor(x1/10),3)-Math.pow(Math.floor(x1/10),2)+17*Math.floor(x1/10))*6+(0.1885*Math.pow(Math.floor(x2/5),3)-4.6262*Math.pow(Math.floor(x2/5),2)+109.98*Math.floor(x2/5))*6+(23.866*Math.pow(x3,3)-60.435*Math.pow(x3,2)+564.34*x3)/24+7.9291*Math.pow(x4,2)+64.568*x4+(Math.floor(x4/4)*10+20)*x5)*(100+0.25*x2+1.75+(x3+((x3>3)?(x3-3):0)+((x3>6)?(x3-6):0))*6+Math.floor(x4/8)*0.3)/100*(100+8)/100", "aptitude": 12, "type": 1, "fragmentCount": 50, "quality": 4, "initScoreExpr": 2935}, {"equipId": 21321, "baseEffect": [[2, 60000]], "decomposeRewards": "", "groupId": 13, "fragmentId": 321321, "name": "<PERSON><PERSON><PERSON><PERSON>", "scoreExpr": "(2000+600+30*x1+600*x2+(11.054*Math.pow(x3,3)+104.71*Math.pow(x3,2)+190.35*x3)/4*1.05+75*x4+40+(0.0341*Math.pow(Math.floor(x1/10),3)-Math.pow(Math.floor(x1/10),2)+17*Math.floor(x1/10))*6+(0.1885*Math.pow(Math.floor(x2/5),3)-4.6262*Math.pow(Math.floor(x2/5),2)+109.98*Math.floor(x2/5))*6+(23.866*Math.pow(x3,3)-60.435*Math.pow(x3,2)+564.34*x3)/24+7.9291*Math.pow(x4,2)+64.568*x4+(Math.floor(x4/4)*10+20)*x5)*(100+0.25*x2+1.75+(x3+((x3>3)?(x3-3):0)+((x3>6)?(x3-6):0))*6+Math.floor(x4/8)*0.3)/100*(100+8)/100", "aptitude": 12, "type": 2, "fragmentCount": 50, "quality": 4, "initScoreExpr": 2935}, {"equipId": 21331, "baseEffect": [[3, 3000]], "decomposeRewards": "", "groupId": 13, "fragmentId": 321331, "name": "<PERSON><PERSON>", "scoreExpr": "(2000+600+30*x1+600*x2+(11.054*Math.pow(x3,3)+104.71*Math.pow(x3,2)+190.35*x3)/4*1.05+75*x4+40+(0.0341*Math.pow(Math.floor(x1/10),3)-Math.pow(Math.floor(x1/10),2)+17*Math.floor(x1/10))*6+(0.1885*Math.pow(Math.floor(x2/5),3)-4.6262*Math.pow(Math.floor(x2/5),2)+109.98*Math.floor(x2/5))*6+(23.866*Math.pow(x3,3)-60.435*Math.pow(x3,2)+564.34*x3)/24+7.9291*Math.pow(x4,2)+64.568*x4+(Math.floor(x4/4)*10+20)*x5)*(100+0.25*x2+1.75+(x3+((x3>3)?(x3-3):0)+((x3>6)?(x3-6):0))*6+Math.floor(x4/8)*0.3)/100*(100+8)/100", "aptitude": 12, "type": 3, "fragmentCount": 50, "quality": 4, "initScoreExpr": 2935}, {"equipId": 21341, "baseEffect": [[4, 3000]], "decomposeRewards": "", "groupId": 13, "fragmentId": 321341, "name": "<PERSON><PERSON><PERSON><PERSON>", "scoreExpr": "(2000+600+30*x1+600*x2+(11.054*Math.pow(x3,3)+104.71*Math.pow(x3,2)+190.35*x3)/4*1.05+75*x4+40+(0.0341*Math.pow(Math.floor(x1/10),3)-Math.pow(Math.floor(x1/10),2)+17*Math.floor(x1/10))*6+(0.1885*Math.pow(Math.floor(x2/5),3)-4.6262*Math.pow(Math.floor(x2/5),2)+109.98*Math.floor(x2/5))*6+(23.866*Math.pow(x3,3)-60.435*Math.pow(x3,2)+564.34*x3)/24+7.9291*Math.pow(x4,2)+64.568*x4+(Math.floor(x4/4)*10+20)*x5)*(100+0.25*x2+1.75+(x3+((x3>3)?(x3-3):0)+((x3>6)?(x3-6):0))*6+Math.floor(x4/8)*0.3)/100*(100+8)/100", "aptitude": 12, "type": 4, "fragmentCount": 50, "quality": 4, "initScoreExpr": 2935}, {"equipId": 22311, "baseEffect": [[1, 6000]], "decomposeRewards": "", "groupId": 23, "fragmentId": 322311, "name": "<PERSON><PERSON><PERSON><PERSON>", "scoreExpr": "(2000+600+30*x1+600*x2+(11.054*Math.pow(x3,3)+104.71*Math.pow(x3,2)+190.35*x3)/4*1.05+75*x4+40+(0.0341*Math.pow(Math.floor(x1/10),3)-Math.pow(Math.floor(x1/10),2)+17*Math.floor(x1/10))*6+(0.1885*Math.pow(Math.floor(x2/5),3)-4.6262*Math.pow(Math.floor(x2/5),2)+109.98*Math.floor(x2/5))*6+(23.866*Math.pow(x3,3)-60.435*Math.pow(x3,2)+564.34*x3)/24+7.9291*Math.pow(x4,2)+64.568*x4+(Math.floor(x4/4)*10+20)*x5)*(100+0.25*x2+1.75+(x3+((x3>3)?(x3-3):0)+((x3>6)?(x3-6):0))*6+Math.floor(x4/8)*0.3)/100*(100+8)/100", "aptitude": 12, "type": 1, "fragmentCount": 50, "quality": 4, "initScoreExpr": 2935}, {"equipId": 22321, "baseEffect": [[2, 60000]], "decomposeRewards": "", "groupId": 23, "fragmentId": 322321, "name": "<PERSON><PERSON><PERSON><PERSON>", "scoreExpr": "(2000+600+30*x1+600*x2+(11.054*Math.pow(x3,3)+104.71*Math.pow(x3,2)+190.35*x3)/4*1.05+75*x4+40+(0.0341*Math.pow(Math.floor(x1/10),3)-Math.pow(Math.floor(x1/10),2)+17*Math.floor(x1/10))*6+(0.1885*Math.pow(Math.floor(x2/5),3)-4.6262*Math.pow(Math.floor(x2/5),2)+109.98*Math.floor(x2/5))*6+(23.866*Math.pow(x3,3)-60.435*Math.pow(x3,2)+564.34*x3)/24+7.9291*Math.pow(x4,2)+64.568*x4+(Math.floor(x4/4)*10+20)*x5)*(100+0.25*x2+1.75+(x3+((x3>3)?(x3-3):0)+((x3>6)?(x3-6):0))*6+Math.floor(x4/8)*0.3)/100*(100+8)/100", "aptitude": 12, "type": 2, "fragmentCount": 50, "quality": 4, "initScoreExpr": 2935}, {"equipId": 22331, "baseEffect": [[3, 3000]], "decomposeRewards": "", "groupId": 23, "fragmentId": 322331, "name": "<PERSON><PERSON>", "scoreExpr": "(2000+600+30*x1+600*x2+(11.054*Math.pow(x3,3)+104.71*Math.pow(x3,2)+190.35*x3)/4*1.05+75*x4+40+(0.0341*Math.pow(Math.floor(x1/10),3)-Math.pow(Math.floor(x1/10),2)+17*Math.floor(x1/10))*6+(0.1885*Math.pow(Math.floor(x2/5),3)-4.6262*Math.pow(Math.floor(x2/5),2)+109.98*Math.floor(x2/5))*6+(23.866*Math.pow(x3,3)-60.435*Math.pow(x3,2)+564.34*x3)/24+7.9291*Math.pow(x4,2)+64.568*x4+(Math.floor(x4/4)*10+20)*x5)*(100+0.25*x2+1.75+(x3+((x3>3)?(x3-3):0)+((x3>6)?(x3-6):0))*6+Math.floor(x4/8)*0.3)/100*(100+8)/100", "aptitude": 12, "type": 3, "fragmentCount": 50, "quality": 4, "initScoreExpr": 2935}, {"equipId": 22341, "baseEffect": [[4, 3000]], "decomposeRewards": "", "groupId": 23, "fragmentId": 322341, "name": "<PERSON><PERSON><PERSON><PERSON>", "scoreExpr": "(2000+600+30*x1+600*x2+(11.054*Math.pow(x3,3)+104.71*Math.pow(x3,2)+190.35*x3)/4*1.05+75*x4+40+(0.0341*Math.pow(Math.floor(x1/10),3)-Math.pow(Math.floor(x1/10),2)+17*Math.floor(x1/10))*6+(0.1885*Math.pow(Math.floor(x2/5),3)-4.6262*Math.pow(Math.floor(x2/5),2)+109.98*Math.floor(x2/5))*6+(23.866*Math.pow(x3,3)-60.435*Math.pow(x3,2)+564.34*x3)/24+7.9291*Math.pow(x4,2)+64.568*x4+(Math.floor(x4/4)*10+20)*x5)*(100+0.25*x2+1.75+(x3+((x3>3)?(x3-3):0)+((x3>6)?(x3-6):0))*6+Math.floor(x4/8)*0.3)/100*(100+8)/100", "aptitude": 12, "type": 4, "fragmentCount": 50, "quality": 4, "initScoreExpr": 2935}, {"equipId": 23411, "baseEffect": [[1, 30000]], "decomposeRewards": "", "groupId": 34, "fragmentId": 323411, "name": "<PERSON><PERSON><PERSON>", "scoreExpr": "(6000+3000+50*x1+2000*x2+(11.054*Math.pow(x3,3)+104.71*Math.pow(x3,2)+190.35*x3)/4*1.1+150*x4+800+(0.0341*Math.pow(Math.floor(x1/10),3)-Math.pow(Math.floor(x1/10),2)+17*Math.floor(x1/10))*6+(0.1885*Math.pow(Math.floor(x2/5),3)-4.6262*Math.pow(Math.floor(x2/5),2)+109.98*Math.floor(x2/5))*6+(23.866*Math.pow(x3,3)-60.435*Math.pow(x3,2)+564.34*x3)/24+15.473*Math.pow(x4,2)+82.116*x4+(Math.floor(x4/4)*10+20)*x5)*(100+0.25*x2+3+(x3+((x3>3)?(x3-3):0)+((x3>6)?(x3-6):0))*6+Math.floor(x4/8)*0.5)/100*(100+40)/100", "aptitude": 16, "type": 1, "fragmentCount": 100, "quality": 5, "initScoreExpr": 14204}, {"equipId": 23421, "baseEffect": [[2, 300000]], "decomposeRewards": "", "groupId": 34, "fragmentId": 323421, "name": "<PERSON><PERSON><PERSON><PERSON>", "scoreExpr": "(6000+3000+50*x1+2000*x2+(11.054*Math.pow(x3,3)+104.71*Math.pow(x3,2)+190.35*x3)/4*1.1+150*x4+800+(0.0341*Math.pow(Math.floor(x1/10),3)-Math.pow(Math.floor(x1/10),2)+17*Math.floor(x1/10))*6+(0.1885*Math.pow(Math.floor(x2/5),3)-4.6262*Math.pow(Math.floor(x2/5),2)+109.98*Math.floor(x2/5))*6+(23.866*Math.pow(x3,3)-60.435*Math.pow(x3,2)+564.34*x3)/24+15.473*Math.pow(x4,2)+82.116*x4+(Math.floor(x4/4)*10+20)*x5)*(100+0.25*x2+3+(x3+((x3>3)?(x3-3):0)+((x3>6)?(x3-6):0))*6+Math.floor(x4/8)*0.5)/100*(100+40)/100", "aptitude": 16, "type": 2, "fragmentCount": 100, "quality": 5, "initScoreExpr": 14204}, {"equipId": 23431, "baseEffect": [[3, 15000]], "decomposeRewards": "", "groupId": 34, "fragmentId": 323431, "name": "<PERSON><PERSON>", "scoreExpr": "(6000+3000+50*x1+2000*x2+(11.054*Math.pow(x3,3)+104.71*Math.pow(x3,2)+190.35*x3)/4*1.1+150*x4+800+(0.0341*Math.pow(Math.floor(x1/10),3)-Math.pow(Math.floor(x1/10),2)+17*Math.floor(x1/10))*6+(0.1885*Math.pow(Math.floor(x2/5),3)-4.6262*Math.pow(Math.floor(x2/5),2)+109.98*Math.floor(x2/5))*6+(23.866*Math.pow(x3,3)-60.435*Math.pow(x3,2)+564.34*x3)/24+15.473*Math.pow(x4,2)+82.116*x4+(Math.floor(x4/4)*10+20)*x5)*(100+0.25*x2+3+(x3+((x3>3)?(x3-3):0)+((x3>6)?(x3-6):0))*6+Math.floor(x4/8)*0.5)/100*(100+40)/100", "aptitude": 16, "type": 3, "fragmentCount": 100, "quality": 5, "initScoreExpr": 14204}, {"equipId": 23441, "baseEffect": [[4, 15000]], "decomposeRewards": "", "groupId": 34, "fragmentId": 323441, "name": "<PERSON><PERSON><PERSON><PERSON>", "scoreExpr": "(6000+3000+50*x1+2000*x2+(11.054*Math.pow(x3,3)+104.71*Math.pow(x3,2)+190.35*x3)/4*1.1+150*x4+800+(0.0341*Math.pow(Math.floor(x1/10),3)-Math.pow(Math.floor(x1/10),2)+17*Math.floor(x1/10))*6+(0.1885*Math.pow(Math.floor(x2/5),3)-4.6262*Math.pow(Math.floor(x2/5),2)+109.98*Math.floor(x2/5))*6+(23.866*Math.pow(x3,3)-60.435*Math.pow(x3,2)+564.34*x3)/24+15.473*Math.pow(x4,2)+82.116*x4+(Math.floor(x4/4)*10+20)*x5)*(100+0.25*x2+3+(x3+((x3>3)?(x3-3):0)+((x3>6)?(x3-6):0))*6+Math.floor(x4/8)*0.5)/100*(100+40)/100", "aptitude": 16, "type": 4, "fragmentCount": 100, "quality": 5, "initScoreExpr": 14204}, {"equipId": 24511, "baseEffect": [[1, 300000]], "decomposeRewards": "", "groupId": 34, "fragmentId": 323511, "name": "<PERSON><PERSON><PERSON>", "scoreExpr": "(60000+30000+60*x1+(x6<1?0:x6<2?4500:x6<3?13500:27000)+2400*x2+(11.054*Math.pow(x3,3)+104.71*Math.pow(x3,2)+190.35*x3)/4*1.2*1.1+150*x4+800+(0.0341*Math.pow(Math.floor(x1/10),3)-Math.pow(Math.floor(x1/10),2)+17*Math.floor(x1/10))*6+(0.1885*Math.pow(Math.floor(x2/5),3)-4.6262*Math.pow(Math.floor(x2/5),2)+109.98*Math.floor(x2/5))*6+(23.866*Math.pow(x3,3)-60.435*Math.pow(x3,2)+564.34*x3)/24+142*Math.pow(Math.floor(x4/5),3)-55*Math.pow(Math.floor(x4/5),2)+1271*Math.floor(x4/5))*(100+0.25*x2+3+(x3+((x3>3)?(x3-3):0)+((x3>6)?(x3-6):0))*6+(x6<1?0:x6<2?3:x6<3?7:12))/100*(100+40)/100", "aptitude": 18, "type": 1, "fragmentCount": 100, "quality": 6, "initScoreExpr": 142040}, {"equipId": 24521, "baseEffect": [[2, 3000000]], "decomposeRewards": "", "groupId": 34, "fragmentId": 323521, "name": "Giáp <PERSON>ương T<PERSON>", "scoreExpr": "(60000+30000+60*x1+(x6<1?0:x6<2?4500:x6<3?13500:27000)+2400*x2+(11.054*Math.pow(x3,3)+104.71*Math.pow(x3,2)+190.35*x3)/4*1.2*1.1+150*x4+800+(0.0341*Math.pow(Math.floor(x1/10),3)-Math.pow(Math.floor(x1/10),2)+17*Math.floor(x1/10))*6+(0.1885*Math.pow(Math.floor(x2/5),3)-4.6262*Math.pow(Math.floor(x2/5),2)+109.98*Math.floor(x2/5))*6+(23.866*Math.pow(x3,3)-60.435*Math.pow(x3,2)+564.34*x3)/24+142*Math.pow(Math.floor(x4/5),3)-55*Math.pow(Math.floor(x4/5),2)+1271*Math.floor(x4/5))*(100+0.25*x2+3+(x3+((x3>3)?(x3-3):0)+((x3>6)?(x3-6):0))*6+(x6<1?0:x6<2?3:x6<3?7:12))/100*(100+40)/100", "aptitude": 18, "type": 2, "fragmentCount": 100, "quality": 6, "initScoreExpr": 142040}, {"equipId": 24531, "baseEffect": [[3, 150000]], "decomposeRewards": "", "groupId": 34, "fragmentId": 323531, "name": "<PERSON><PERSON>ương T<PERSON>", "scoreExpr": "(60000+30000+60*x1+(x6<1?0:x6<2?4500:x6<3?13500:27000)+2400*x2+(11.054*Math.pow(x3,3)+104.71*Math.pow(x3,2)+190.35*x3)/4*1.2*1.1+150*x4+800+(0.0341*Math.pow(Math.floor(x1/10),3)-Math.pow(Math.floor(x1/10),2)+17*Math.floor(x1/10))*6+(0.1885*Math.pow(Math.floor(x2/5),3)-4.6262*Math.pow(Math.floor(x2/5),2)+109.98*Math.floor(x2/5))*6+(23.866*Math.pow(x3,3)-60.435*Math.pow(x3,2)+564.34*x3)/24+142*Math.pow(Math.floor(x4/5),3)-55*Math.pow(Math.floor(x4/5),2)+1271*Math.floor(x4/5))*(100+0.25*x2+3+(x3+((x3>3)?(x3-3):0)+((x3>6)?(x3-6):0))*6+(x6<1?0:x6<2?3:x6<3?7:12))/100*(100+40)/100", "aptitude": 18, "type": 3, "fragmentCount": 100, "quality": 6, "initScoreExpr": 142040}, {"equipId": 24541, "baseEffect": [[4, 150000]], "decomposeRewards": "", "groupId": 34, "fragmentId": 323541, "name": "G<PERSON>ày B<PERSON> Vương Thi<PERSON>n", "scoreExpr": "(60000+30000+60*x1+(x6<1?0:x6<2?4500:x6<3?13500:27000)+2400*x2+(11.054*Math.pow(x3,3)+104.71*Math.pow(x3,2)+190.35*x3)/4*1.2*1.1+150*x4+800+(0.0341*Math.pow(Math.floor(x1/10),3)-Math.pow(Math.floor(x1/10),2)+17*Math.floor(x1/10))*6+(0.1885*Math.pow(Math.floor(x2/5),3)-4.6262*Math.pow(Math.floor(x2/5),2)+109.98*Math.floor(x2/5))*6+(23.866*Math.pow(x3,3)-60.435*Math.pow(x3,2)+564.34*x3)/24+142*Math.pow(Math.floor(x4/5),3)-55*Math.pow(Math.floor(x4/5),2)+1271*Math.floor(x4/5))*(100+0.25*x2+3+(x3+((x3>3)?(x3-3):0)+((x3>6)?(x3-6):0))*6+(x6<1?0:x6<2?3:x6<3?7:12))/100*(100+40)/100", "aptitude": 18, "type": 4, "fragmentCount": 100, "quality": 6, "initScoreExpr": 142040}, {"equipId": -20019, "baseEffect": [[1, 900000000000]], "decomposeRewards": "", "groupId": 99, "fragmentId": 320019, "name": "<PERSON><PERSON><PERSON>", "scoreExpr": 20000000, "aptitude": 16, "type": 1, "fragmentCount": 100000, "quality": 5, "initScoreExpr": 20000000}, {"equipId": -20029, "baseEffect": [[2, 9000000000000]], "decomposeRewards": "", "groupId": 99, "fragmentId": 320029, "name": "<PERSON><PERSON><PERSON><PERSON>", "scoreExpr": 20000000, "aptitude": 16, "type": 2, "fragmentCount": 100000, "quality": 5, "initScoreExpr": 20000000}, {"equipId": -20039, "baseEffect": [[3, 900000000000]], "decomposeRewards": "", "groupId": 99, "fragmentId": 320039, "name": "Mũ Test", "scoreExpr": 20000000, "aptitude": 16, "type": 3, "fragmentCount": 100000, "quality": 5, "initScoreExpr": 20000000}, {"equipId": -20049, "baseEffect": [[4, 900000000000]], "decomposeRewards": "", "groupId": 99, "fragmentId": 320049, "name": "Giày Test", "scoreExpr": 20000000, "aptitude": 16, "type": 4, "fragmentCount": 100000, "quality": 5, "initScoreExpr": 20000000}]